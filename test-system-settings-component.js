/**
 * This script tests if the SystemSettings component file exists at the correct path.
 * It doesn't test functionality or imports, just that the file is present.
 */

const fs = require('fs');
const path = require('path');

async function testSystemSettingsComponent() {
  try {
    const systemSettingsPath = path.join(__dirname, 'client/src/components/AccessControl/SystemSettings.js');
    
    console.log('Checking if SystemSettings component exists...');
    if (fs.existsSync(systemSettingsPath)) {
      console.log('✅ SystemSettings component exists at the correct path');
    } else {
      throw new Error(`SystemSettings component not found at ${systemSettingsPath}`);
    }
    
    console.log('Component file exists at the correct path!');
    return true;
  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

// Run the test
testSystemSettingsComponent()
  .then(success => {
    if (success) {
      console.log('Test completed successfully');
      process.exit(0);
    } else {
      console.error('Test failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });