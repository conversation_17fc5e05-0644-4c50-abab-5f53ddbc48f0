/**
 * Test script for adding Google Forms to shortcuts
 * 
 * This script tests the functionality of adding Google Forms to shortcuts
 * by creating a shortcut for a Google Form and verifying it was added correctly.
 */

const axios = require('axios');
const mongoose = require('mongoose');
const Shortcut = require('./models/Shortcut');
require('dotenv').config();

// Get MongoDB URI from environment variables
const mongoURI = process.env.MONGO_URI;

if (!mongoURI) {
  console.error('MONGO_URI environment variable is not set');
  process.exit(1);
}

// Connect to MongoDB
mongoose.connect(mongoURI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});

// Test data for a Google Form shortcut
const testFormShortcut = {
  title: 'Test Google Form Shortcut',
  description: 'Google Form: Test Form',
  url: 'https://docs.google.com/forms/d/e/1FAIpQLSdFQxUQBZzLsQzL9mGCvKV-9UmJxGPe8_x16WgD0X_VSwvVmQ/viewform',
  icon: 'description',
  categories: ['Google Forms', 'Test'],
  instructions: 'Click to open this Google Form',
  isPublic: false,
  isGlobal: false,
  createdBy: null // Will be set to the test user ID
};

// Create a test user ID (for testing purposes)
const testUserId = new mongoose.Types.ObjectId();
testFormShortcut.createdBy = testUserId;

// Function to add a Google Form shortcut
async function addGoogleFormShortcut() {
  try {
    console.log('=== Google Forms Shortcuts Test ===');
    console.log('Date/Time:', new Date().toISOString());
    console.log('=======================================');
    
    console.log('Creating a shortcut for a Google Form...');
    
    // Create the shortcut directly in the database
    const shortcut = new Shortcut(testFormShortcut);
    await shortcut.save();
    
    console.log('Shortcut created with ID:', shortcut._id);
    
    // Verify the shortcut was created correctly
    const savedShortcut = await Shortcut.findById(shortcut._id);
    
    if (!savedShortcut) {
      console.error('❌ TEST FAILED: Shortcut not found in database');
      process.exit(1);
    }
    
    console.log('Shortcut details:');
    console.log('- Title:', savedShortcut.title);
    console.log('- URL:', savedShortcut.url);
    console.log('- Categories:', savedShortcut.categories.join(', '));
    
    // Verify the shortcut has the correct properties
    if (
      savedShortcut.title === testFormShortcut.title &&
      savedShortcut.url === testFormShortcut.url &&
      savedShortcut.icon === testFormShortcut.icon &&
      savedShortcut.categories.includes('Google Forms')
    ) {
      console.log('✅ TEST PASSED: Shortcut created successfully with correct properties');
    } else {
      console.error('❌ TEST FAILED: Shortcut properties do not match');
      process.exit(1);
    }
    
    // Clean up - delete the test shortcut
    console.log('Cleaning up - deleting test shortcut...');
    await Shortcut.findByIdAndDelete(shortcut._id);
    console.log('Test shortcut deleted');
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB Disconnected');
    
  } catch (error) {
    console.error('Error in test:', error);
    process.exit(1);
  }
}

// Run the test
addGoogleFormShortcut();