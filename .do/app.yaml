name: csfportal
services:
  - name: web
    dockerfile_path: Dockerfile
    github:
      branch: main
      deploy_on_push: true
    health_check:
      http_path: /
    http_port: 8080
    instance_count: 1
    instance_size_slug: basic-xs
    routes:
      - path: /
    envs:
      - key: NODE_ENV
        value: production
        scope: RUN_TIME
      - key: PORT
        value: "8080"
        scope: RUN_TIME
      - key: SESSION_SECRET
        scope: RUN_TIME
        value: ${SESSION_SECRET}
        type: SECRET
      - key: MONGO_URI
        scope: RUN_TIME
        value: ${MONGO_URI}
        type: SECRET
      - key: GOOGLE_CLIENT_ID
        scope: RUN_TIME
        value: ${GOOGLE_CLIENT_ID}
        type: SECRET
      - key: GOOGLE_CLIENT_SECRET
        scope: RUN_TIME
        value: ${GOOGLE_CLIENT_SECRET}
        type: SECRET
      - key: GOOGLE_CALLBACK_URL
        scope: RUN_TIME
        value: ${GOOGLE_CALLBACK_URL}
        type: SECRET
      - key: GOOGLE_API_KEY
        scope: RUN_TIME
        value: ${GOOGLE_API_KEY}
        type: SECRET
      - key: ALLOWED_DOMAINS
        scope: RUN_TIME
        value: ${ALLOWED_DOMAINS}
        type: SECRET