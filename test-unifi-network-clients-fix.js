/**
 * Test script to verify the UniFi Network clients endpoint fix
 * 
 * This script tests the updated UniFi Network API wrapper to ensure
 * it can successfully fetch clients from the UniFi Network Controller
 * by trying multiple endpoint formats.
 * 
 * Usage: node test-unifi-network-clients-fix.js
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API wrapper
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get configuration from environment variables
const host = process.env.UNIFI_NETWORK_HOST || '';
const apiKey = process.env.UNIFI_NETWORK_API_KEY || '';
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Check if required configuration is available
if (!host || !apiKey) {
  console.error('Error: UniFi Network host and API key are required.');
  console.error('Please set the UNIFI_NETWORK_HOST and UNIFI_NETWORK_API_KEY environment variables.');
  process.exit(1);
}

// Create an instance of the UniFi Network API wrapper
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Test function to verify the API wrapper can fetch clients
async function testGetClients() {
  try {
    console.log('Testing UniFi Network API getClients method with fallback endpoints...');
    
    // Initialize the API
    await unifiNetworkAPI.initialize();
    console.log('UniFi Network API initialized successfully');
    
    // Test getClients method
    console.log('Fetching clients...');
    const clients = await unifiNetworkAPI.getClients();
    
    if (clients && Array.isArray(clients)) {
      console.log(`Success! Found ${clients.length} clients.`);
      if (clients.length > 0) {
        console.log('First client:', JSON.stringify(clients[0], null, 2));
      } else {
        console.log('No clients found, but the API call was successful.');
      }
      return true;
    } else {
      console.error('Error: Unexpected response format. Expected an array of clients.');
      console.error('Response:', clients);
      return false;
    }
  } catch (error) {
    console.error('Error testing getClients method:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Run the test
async function runTest() {
  console.log('Starting UniFi Network clients endpoint fix test...');
  console.log(`Using host: ${host}, API key: [HIDDEN], port: ${port}, site: ${site}`);
  
  try {
    const result = await testGetClients();
    
    if (result) {
      console.log('Test passed! The UniFi Network clients endpoint fix is working correctly.');
    } else {
      console.error('Test failed. Please check the error messages above.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running test:', error.message);
    process.exit(1);
  }
}

// Run the test
runTest();