/**
 * Test script to verify the UniFi Network API integration with the new endpoint format
 * 
 * This script tests the updated UniFi Network API wrapper to ensure
 * it correctly uses the specified format:
 * https://YOUR_CONSOLE_IP/proxy/network/integration/v1/sites
 * with X-API-KEY header for authentication
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API wrapper
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get configuration from environment variables
const host = process.env.UNIFI_NETWORK_HOST;
const apiKey = process.env.UNIFI_NETWORK_API_KEY;
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Create an instance of the UniFi Network API wrapper
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Main test function
async function runTests() {
  console.log('Starting UniFi Network API tests with new endpoint format...');
  console.log(`Host: ${host}`);
  console.log(`Port: ${port}`);
  console.log(`Site: ${site}`);
  console.log('API Key: [REDACTED]');
  console.log('-------------------------------------------');

  try {
    // Test initialization
    console.log('\nTesting initialization...');
    await unifiNetworkAPI.initialize();
    console.log('✅ Initialization successful');

    // Test getting sites
    console.log('\nTesting connection to sites endpoint...');
    await unifiNetworkAPI.testConnection();
    console.log('✅ Connection to sites endpoint successful');

    // Test getting devices
    console.log('\nTesting getDevices method...');
    const devices = await unifiNetworkAPI.getDevices();
    console.log(`✅ Successfully retrieved ${devices.length} devices`);
    
    if (devices.length > 0) {
      const deviceId = devices[0].mac;
      
      // Test getting device details
      console.log(`\nTesting getDeviceDetails method for device ${deviceId}...`);
      const deviceDetails = await unifiNetworkAPI.getDeviceDetails(deviceId);
      console.log('✅ Successfully retrieved device details');
      
      // Test getting device statistics
      console.log(`\nTesting getDeviceStatistics method for device ${deviceId}...`);
      const deviceStats = await unifiNetworkAPI.getDeviceStatistics(deviceId);
      console.log('✅ Successfully retrieved device statistics');
    }

    // Test getting clients
    console.log('\nTesting getClients method...');
    const clients = await unifiNetworkAPI.getClients();
    console.log(`✅ Successfully retrieved ${clients.length} clients`);
    
    if (clients.length > 0) {
      const clientId = clients[0].mac;
      
      // Test getting client details
      console.log(`\nTesting getClientDetails method for client ${clientId}...`);
      const clientDetails = await unifiNetworkAPI.getClientDetails(clientId);
      console.log('✅ Successfully retrieved client details');
      
      // Note: We're not testing blockClient and unblockClient to avoid affecting production clients
    }


    console.log('\n-------------------------------------------');
    console.log('All tests passed! The UniFi Network API integration is working correctly with the new endpoint format.');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();