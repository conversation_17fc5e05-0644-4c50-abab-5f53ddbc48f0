/**
 * Test script to verify the Lenel S2 NetBox API rate limiting fix
 * 
 * This script simulates the door status monitoring process and checks if the rate limiting fix works as expected.
 * It will make sequential requests with a delay between each request, similar to the fix implemented in realtimeService.js.
 * 
 * Usage: node test-lenel-rate-limiting-fix.js
 */

// Load environment variables
require('dotenv').config();

// Import the Lenel S2 NetBox API
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Environment variables for Lenel S2 NetBox API
const host = process.env.LENEL_S2_NETBOX_HOST || '';
const username = process.env.LENEL_S2_NETBOX_USERNAME || '';
const password = process.env.LENEL_S2_NETBOX_PASSWORD || '';
const port = process.env.LENEL_S2_NETBOX_PORT || 443;

// Check if environment variables are set
if (!host || !username || !password) {
  console.error('Lenel S2 NetBox environment variables are not set. Please check your .env file.');
  process.exit(1);
}

// Create a new instance of the Lenel S2 NetBox API
const lenelS2NetBoxAPI = new LenelS2NetBoxAPI(host, username, password, port);

/**
 * Test the door status monitoring process with rate limiting
 */
async function testDoorStatusMonitoring() {
  try {
    console.log('Initializing Lenel S2 NetBox API...');
    await lenelS2NetBoxAPI.initialize();
    
    console.log('Getting all doors...');
    const doors = await lenelS2NetBoxAPI.getDoors();
    
    if (!doors || doors.length === 0) {
      console.log('No doors found. Exiting...');
      return;
    }
    
    console.log(`Found ${doors.length} doors.`);
    
    // Get status for each door with rate limiting
    console.log('Getting status for each door with rate limiting...');
    const doorStatuses = [];
    let successCount = 0;
    let failureCount = 0;
    
    for (const door of doors) {
      try {
        // Add a small delay between requests to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
        
        console.log(`Getting status for door ${door.id} (${door.name})...`);
        const status = await lenelS2NetBoxAPI.getDoorStatus(door.id);
        
        doorStatuses.push({
          doorId: door.id,
          doorName: door.name,
          status: status
        });
        
        console.log(`Successfully got status for door ${door.id}: ${status.status}`);
        successCount++;
      } catch (error) {
        console.error(`Error getting status for door ${door.id}:`, error.message);
        failureCount++;
      }
    }
    
    console.log('\nTest Results:');
    console.log(`Total doors: ${doors.length}`);
    console.log(`Successful requests: ${successCount}`);
    console.log(`Failed requests: ${failureCount}`);
    console.log(`Success rate: ${(successCount / doors.length * 100).toFixed(2)}%`);
    
    if (failureCount === 0) {
      console.log('\nTest PASSED: All door status requests were successful with rate limiting.');
    } else {
      console.log('\nTest FAILED: Some door status requests failed even with rate limiting.');
      console.log('Consider increasing the delay between requests or implementing a more sophisticated rate limiting mechanism.');
    }
  } catch (error) {
    console.error('Error testing door status monitoring:', error);
  }
}

// Run the test
testDoorStatusMonitoring().then(() => {
  console.log('Test completed.');
  process.exit(0);
}).catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});