#!/usr/bin/env node

/**
 * Simple test for Google Service Account credentials (without impersonation)
 */

require('dotenv').config();
const { google } = require('googleapis');

async function testServiceAccount() {
  console.log('🧪 Testing Google Service Account Credentials (No Impersonation)...\n');

  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;

  console.log('📋 Configuration:');
  console.log(`   Service Account Email: ${serviceAccountEmail || '❌ Missing'}`);
  console.log(`   Service Account Key: ${serviceAccountPrivateKey ? '✅ Set' : '❌ Missing'}`);

  // Check private key format
  if (serviceAccountPrivateKey) {
    console.log(`   Private Key Format: ${serviceAccountPrivateKey.includes('-----BEGIN PRIVATE KEY-----') ? '✅ Valid' : '❌ Invalid'}`);
    console.log(`   Private Key Length: ${serviceAccountPrivateKey.length} characters`);
  }
  console.log();

  if (!serviceAccountEmail || !serviceAccountPrivateKey) {
    console.error('❌ Error: Missing service account credentials');
    process.exit(1);
  }

  try {
    // Test without impersonation first
    console.log('🔑 Test 1: Creating JWT client (no impersonation)...');
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });
    console.log('   ✅ JWT client created successfully\n');

    // Test getting access token
    console.log('🎫 Test 2: Getting access token...');
    try {
      const accessToken = await auth.getAccessToken();
      console.log('   ✅ Access token obtained successfully');
      console.log(`   📝 Token starts with: ${accessToken.token ? accessToken.token.substring(0, 20) + '...' : 'No token'}\n`);
    } catch (tokenError) {
      console.error('   ❌ Failed to get access token:', tokenError.message);
      
      if (tokenError.message.includes('private_key_id')) {
        console.log('   🔧 This might be a private key format issue');
      } else if (tokenError.message.includes('invalid_grant')) {
        console.log('   🔧 This might be a service account configuration issue');
      }
      
      throw tokenError;
    }

    console.log('✅ Service account credentials are valid (without impersonation)!\n');
    
    // Now test with impersonation
    console.log('👤 Test 3: Testing with impersonation...');
    const impersonationAuth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/drive.readonly'],
      subject: '<EMAIL>'
    });

    try {
      const impersonationToken = await impersonationAuth.getAccessToken();
      console.log('   ✅ Impersonation token obtained successfully');
      console.log('   🎉 Domain-wide delegation is working!\n');
    } catch (impersonationError) {
      console.error('   ❌ Impersonation failed:', impersonationError.message);
      console.log('   🔧 Domain-wide delegation needs to be configured:');
      console.log('      1. Go to Google Admin Console → Security → API controls');
      console.log('      2. Click "Manage Domain Wide Delegation"');
      console.log('      3. Add the service account client ID with the required scopes');
      console.log('      4. Required scopes for this app:');
      console.log('         - https://www.googleapis.com/auth/drive');
      console.log('         - https://www.googleapis.com/auth/admin.directory.user');
      console.log('         - https://www.googleapis.com/auth/admin.directory.group');
      console.log('         - https://www.googleapis.com/auth/calendar');
      console.log('         - https://www.googleapis.com/auth/forms.body');
      console.log('         - https://www.googleapis.com/auth/forms.responses.readonly\n');
    }

  } catch (error) {
    console.error('\n💥 Service account test failed:', error.message);
    process.exit(1);
  }
}

testServiceAccount();