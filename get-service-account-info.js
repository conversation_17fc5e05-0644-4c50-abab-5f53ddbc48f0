#!/usr/bin/env node

/**
 * Extract Google Service Account information for domain-wide delegation setup
 */

require('dotenv').config();
const { google } = require('googleapis');

async function getServiceAccountInfo() {
  console.log('🔍 Google Service Account Information for Domain-Wide Delegation\n');

  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;

  if (!serviceAccountEmail || !serviceAccountPrivateKey) {
    console.error('❌ Missing service account credentials');
    process.exit(1);
  }

  try {
    // Create JWT client to extract information
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });

    // Get access token to verify credentials
    const accessToken = await auth.getAccessToken();
    
    console.log('📋 Service Account Details:');
    console.log(`   Email: ${serviceAccountEmail}`);
    
    // Extract key information
    const keyLines = serviceAccountPrivateKey.split('\n');
    const keyHeader = keyLines[0];
    const keyFooter = keyLines[keyLines.length - 2] || keyLines[keyLines.length - 1];
    
    console.log(`   Private Key: ${keyHeader}...${keyFooter}`);
    console.log(`   Status: ✅ Valid (token obtained)`);
    
    // The client ID is embedded in the JWT, but we need to decode it
    // For now, provide instructions on how to get it
    console.log('\n🔑 To get the Client ID for domain-wide delegation:');
    console.log('   1. Go to https://console.cloud.google.com/');
    console.log('   2. Navigate to IAM & Admin → Service Accounts');
    console.log('   3. Find: <EMAIL>');
    console.log('   4. Copy the "Unique ID" (numeric value) - this is your Client ID');
    console.log('   5. Or look for "Client ID" in the service account details');

    console.log('\n📝 Domain-Wide Delegation Setup:');
    console.log('   1. Go to https://admin.google.com/');
    console.log('   2. Security → Access and data control → API controls');
    console.log('   3. Manage Domain Wide Delegation → Add new');
    console.log('   4. Enter the Client ID from step 4 above');
    console.log('   5. Add these OAuth scopes (comma-separated):');
    
    const requiredScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file', 
      'https://www.googleapis.com/auth/drive.metadata',
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.user.security',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar.settings.readonly',
      'https://www.googleapis.com/auth/forms.body',
      'https://www.googleapis.com/auth/forms.responses.readonly'
    ];

    console.log(`\n   OAuth Scopes to add:`);
    console.log(`   ${requiredScopes.join(',')}`);

    console.log('\n🧪 After configuring delegation, test with:');
    console.log('   node test-google-service-auth-simple.js');

    // Try to extract project info
    try {
      const projectMatch = serviceAccountEmail.match(/@(.+)\.iam\.gserviceaccount\.com$/);
      if (projectMatch) {
        const projectId = projectMatch[1];
        console.log(`\n🏗️  Project ID: ${projectId}`);
        console.log(`   Service Account Full Name: projects/${projectId}/serviceAccounts/${serviceAccountEmail}`);
      }
    } catch (e) {
      // Ignore errors in project extraction
    }

  } catch (error) {
    console.error('\n❌ Error verifying service account:', error.message);
    process.exit(1);
  }
}

getServiceAccountInfo();