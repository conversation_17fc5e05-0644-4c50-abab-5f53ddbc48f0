// Test script to verify Dreo environment variables are being read correctly
require('dotenv').config();
const DreoAPI = require('./server/integrations/dreo/dreoAPI');
const dreoController = require('./server/controllers/dreoController');

console.log('Testing Dreo environment variables...');

// Check if environment variables are set
console.log('DREO_USERNAME environment variable is set:', !!process.env.DREO_USERNAME);
console.log('DREO_PASSWORD environment variable is set:', !!process.env.DREO_PASSWORD);

// Create a mock request object
const mockReq = {
  user: { id: 'test-user-id' }
};

// Test the getLatestConfig function (exported for testing)
async function testGetLatestConfig() {
  try {
    // Access the getLatestConfig function for testing
    // Note: This is a bit of a hack since the function is not exported directly
    const getLatestConfig = dreoController.getLatestConfig || 
                           (dreoController.exports && dreoController.exports.getLatestConfig);
    
    if (typeof getLatestConfig !== 'function') {
      console.log('getLatestConfig function is not directly accessible for testing.');
      console.log('Testing alternative approach using getConfig controller method...');
      
      // Mock response object
      const mockRes = {
        json: (data) => {
          console.log('Config returned by controller:', data);
          console.log('Config is using environment variables:', !!data.fromEnv);
          return data;
        },
        status: (code) => {
          console.log('Response status code:', code);
          return {
            json: (data) => {
              console.log('Error response:', data);
              return data;
            }
          };
        }
      };
      
      // Call the getConfig controller method
      await dreoController.getConfig(mockReq, mockRes);
    } else {
      // If getLatestConfig is accessible, call it directly
      const config = await getLatestConfig(mockReq);
      console.log('Config returned by getLatestConfig:', config);
      console.log('Config is using environment variables:', !!config.fromEnv);
    }
  } catch (error) {
    console.error('Error testing getLatestConfig:', error);
  }
}

// Run the test
testGetLatestConfig()
  .then(() => {
    console.log('Dreo environment variables test completed.');
  })
  .catch((error) => {
    console.error('Error running Dreo environment variables test:', error);
  });