/**
 * Test script to verify that the Spotify Connect play links work correctly
 * 
 * This script tests the following functionality:
 * 1. Playing a Spotify playlist using Spotify Connect
 * 2. Playing a Spotify track using Spotify Connect
 * 3. Playing a Spotify album using Spotify Connect
 * 4. Playing a Spotify artist using Spotify Connect
 */

const axios = require('axios');

// Sample IDs for testing
// Replace these with actual IDs if needed
const PLAYLIST_ID = '37i9dQZF1DXcBWIGoYBM5M'; // Today's Top Hits
const TRACK_ID = '4cOdK2wGLETKBW3PvgPWqT';    // Random example
const ALBUM_ID = '0JGOiO34nwfUdDrD612dOp';    // Random example
const ARTIST_ID = '06HL4z0CvFAxyc27GXpf02';   // Taylor Swift

// Base URL for API calls
const BASE_URL = 'http://localhost:3000/api/wiim';

// Test playing a Spotify playlist using Spotify Connect
async function testPlaySpotifyPlaylistConnect() {
  try {
    console.log(`Testing play Spotify playlist using Connect: ${PLAYLIST_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/playlists/${PLAYLIST_ID}/play-connect`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify playlist using Connect:', error.response?.data || error.message);
    return false;
  }
}

// Test playing a Spotify track using Spotify Connect
async function testPlaySpotifyTrackConnect() {
  try {
    console.log(`Testing play Spotify track using Connect: ${TRACK_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/tracks/${TRACK_ID}/play-connect`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify track using Connect:', error.response?.data || error.message);
    return false;
  }
}

// Test playing a Spotify album using Spotify Connect
async function testPlaySpotifyAlbumConnect() {
  try {
    console.log(`Testing play Spotify album using Connect: ${ALBUM_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/albums/${ALBUM_ID}/play-connect`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify album using Connect:', error.response?.data || error.message);
    return false;
  }
}

// Test playing a Spotify artist using Spotify Connect
async function testPlaySpotifyArtistConnect() {
  try {
    console.log(`Testing play Spotify artist using Connect: ${ARTIST_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/artists/${ARTIST_ID}/play-connect`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify artist using Connect:', error.response?.data || error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting Spotify Connect play links tests...');
  
  // Test playing a Spotify playlist using Connect
  const playlistResult = await testPlaySpotifyPlaylistConnect();
  
  // Test playing a Spotify track using Connect
  const trackResult = await testPlaySpotifyTrackConnect();
  
  // Test playing a Spotify album using Connect
  const albumResult = await testPlaySpotifyAlbumConnect();
  
  // Test playing a Spotify artist using Connect
  const artistResult = await testPlaySpotifyArtistConnect();
  
  // Summary
  console.log('\nTest Results:');
  console.log(`Play Spotify Playlist using Connect: ${playlistResult ? 'PASS' : 'FAIL'}`);
  console.log(`Play Spotify Track using Connect: ${trackResult ? 'PASS' : 'FAIL'}`);
  console.log(`Play Spotify Album using Connect: ${albumResult ? 'PASS' : 'FAIL'}`);
  console.log(`Play Spotify Artist using Connect: ${artistResult ? 'PASS' : 'FAIL'}`);
  
  const allPassed = playlistResult && trackResult && albumResult && artistResult;
  console.log(`\nOverall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});