#!/usr/bin/env node

/**
 * Test script for LG ThinQ integration
 * This script tests the corrected authentication and API calls
 */

require('dotenv').config();
const LGThinqAPI = require('./server/integrations/lgThinq/lgThinqAPI');

async function testLGIntegration() {
  console.log('🧪 Testing LG ThinQ Integration...\n');

  // Check environment variables
  const patToken = process.env.LG_THINQ_PAT_TOKEN;
  const region = process.env.LG_THINQ_REGION || 'america';
  const country = process.env.LG_THINQ_COUNTRY || 'US';
  const clientId = process.env.LG_THINQ_CLIENT_ID;

  console.log('📋 Configuration:');
  console.log(`   Region: ${region}`);
  console.log(`   Country: ${country}`);
  console.log(`   PAT Token: ${patToken ? '✅ Set' : '❌ Missing'}`);
  console.log(`   Client ID: ${clientId ? '✅ Set' : '🔄 Auto-generated'}\n`);

  if (!patToken) {
    console.error('❌ Error: LG_THINQ_PAT_TOKEN environment variable is required.');
    console.log('\n📝 Setup Instructions:');
    console.log('1. Visit https://connect-pat.lgthinq.com');
    console.log('2. Log in with your ThinQ account');
    console.log('3. Create a new PAT token');
    console.log('4. Set the following environment variables:');
    console.log('   export LG_THINQ_PAT_TOKEN="your_pat_token_here"');
    console.log('   export LG_THINQ_REGION="america"  # or asia, europe');
    console.log('   export LG_THINQ_COUNTRY="US"');
    console.log('   export LG_THINQ_CLIENT_ID="your_client_id"  # optional');
    process.exit(1);
  }

  try {
    // Initialize API client
    const lgAPI = new LGThinqAPI(patToken, region, country, clientId);
    
    // Test 1: Route information (basic connectivity)
    console.log('🔌 Test 1: Getting route information...');
    try {
      const routeInfo = await lgAPI.getRouteInfo();
      console.log('   ✅ Route info retrieved successfully');
      console.log(`   📍 Backend URL: ${JSON.stringify(routeInfo, null, 2)}\n`);
    } catch (error) {
      console.error('   ❌ Route info failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 2: Check if API is configured properly
    console.log('⚙️  Test 2: Checking API configuration...');
    const isConfigured = await lgAPI.isConfigured();
    console.log(`   ${isConfigured ? '✅' : '❌'} API configuration: ${isConfigured ? 'Valid' : 'Invalid'}\n`);

    if (!isConfigured) {
      throw new Error('API configuration validation failed');
    }

    // Test 3: Get devices (requires proper authentication)
    console.log('📱 Test 3: Getting device list...');
    try {
      const devices = await lgAPI.getDevices();
      console.log('   ✅ Device list retrieved successfully');
      console.log(`   📊 Found ${devices.length || 0} device(s)`);
      
      if (devices && devices.length > 0) {
        console.log('   📋 Devices:');
        devices.forEach((device, index) => {
          console.log(`      ${index + 1}. ${device.alias || device.deviceId} (${device.deviceType})`);
        });
      } else {
        console.log('   ℹ️  No devices found in your LG ThinQ account');
      }
      console.log();
    } catch (error) {
      console.error('   ❌ Device list failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    console.log('🎉 All tests passed! LG ThinQ integration is working correctly.\n');

  } catch (error) {
    console.error('\n💥 Integration test failed:', error.message);
    
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      console.log('\n🔐 Authentication Issue:');
      console.log('   - Check that your PAT token is valid and not expired');
      console.log('   - Ensure you have the correct permissions on your ThinQ account');
    } else if (error.message.includes('400') || error.message.includes('Bad Request')) {
      console.log('\n⚠️  Request Issue:');
      console.log('   - Check that region and country are set correctly');
      console.log('   - Verify the API endpoint is accessible from your location');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testLGIntegration();
}

module.exports = testLGIntegration;