#!/usr/bin/env node

/**
 * Test script for ZeeVee HDbridge 2920-NA integration
 * This script tests the API connectivity and functionality
 */

require('dotenv').config();
const ZeeVeeAPI = require('./server/integrations/zeeVee/zeeVeeAPI');

async function testZeeVeeIntegration() {
  console.log('🧪 Testing ZeeVee HDbridge 2920-NA Integration...\n');

  // Check environment variables
  const host = process.env.ZEEVEE_HOST;
  const port = process.env.ZEEVEE_PORT || 80;
  const username = process.env.ZEEVEE_USERNAME || '';
  const password = process.env.ZEEVEE_PASSWORD || '';

  console.log('📋 Configuration:');
  console.log(`   Host: ${host || '❌ Missing'}`);
  console.log(`   Port: ${port}`);
  console.log(`   Username: ${username ? '✅ Set' : '❌ Not set'}`);
  console.log(`   Password: ${password ? '✅ Set' : '❌ Not set'}\n`);

  if (!host) {
    console.error('❌ Error: ZEEVEE_HOST environment variable is required.');
    console.log('\n📝 Setup Instructions:');
    console.log('1. Find the IP address or hostname of your ZeeVee HDbridge 2920-NA device');
    console.log('2. Set the following environment variables:');
    console.log('   export ZEEVEE_HOST="your_zeevee_host_or_ip"');
    console.log('   export ZEEVEE_PORT=80  # optional, defaults to 80');
    console.log('   export ZEEVEE_USERNAME="your_username"  # if authentication is required');
    console.log('   export ZEEVEE_PASSWORD="your_password"  # if authentication is required');
    process.exit(1);
  }

  try {
    // Initialize API client
    const zeeVeeAPI = new ZeeVeeAPI(host, port, username, password);
    
    // Test 1: Check if API is configured properly
    console.log('⚙️  Test 1: Checking API configuration...');
    const isConfigured = await zeeVeeAPI.isConfigured();
    console.log(`   ${isConfigured ? '✅' : '❌'} API configuration: ${isConfigured ? 'Valid' : 'Invalid'}\n`);

    if (!isConfigured) {
      throw new Error('API configuration validation failed');
    }

    // Test 2: Get device information
    console.log('📱 Test 2: Getting device information...');
    try {
      const deviceInfo = await zeeVeeAPI.getDeviceInfo();
      console.log('   ✅ Device information retrieved successfully');
      console.log(`   📊 Device Info: ${JSON.stringify(deviceInfo, null, 2)}\n`);
    } catch (error) {
      console.error('   ❌ Device information failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 3: Get system status
    console.log('🔄 Test 3: Getting system status...');
    try {
      const systemStatus = await zeeVeeAPI.getSystemStatus();
      console.log('   ✅ System status retrieved successfully');
      console.log(`   📊 System Status: ${JSON.stringify(systemStatus, null, 2)}\n`);
    } catch (error) {
      console.error('   ❌ System status failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 4: Get encoders
    console.log('🎬 Test 4: Getting encoders...');
    try {
      const encoders = await zeeVeeAPI.getEncoders();
      console.log('   ✅ Encoders retrieved successfully');
      console.log(`   📊 Found ${encoders.length || 0} encoder(s)`);
      
      if (encoders && encoders.length > 0) {
        console.log('   📋 Encoders:');
        encoders.forEach((encoder, index) => {
          console.log(`      ${index + 1}. ${encoder.name || encoder.id}`);
        });
      } else {
        console.log('   ℹ️  No encoders found');
      }
      console.log();
    } catch (error) {
      console.error('   ❌ Encoders failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 5: Get decoders
    console.log('📺 Test 5: Getting decoders...');
    try {
      const decoders = await zeeVeeAPI.getDecoders();
      console.log('   ✅ Decoders retrieved successfully');
      console.log(`   📊 Found ${decoders.length || 0} decoder(s)`);
      
      if (decoders && decoders.length > 0) {
        console.log('   📋 Decoders:');
        decoders.forEach((decoder, index) => {
          console.log(`      ${index + 1}. ${decoder.name || decoder.id}`);
        });
      } else {
        console.log('   ℹ️  No decoders found');
      }
      console.log();
    } catch (error) {
      console.error('   ❌ Decoders failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 6: Get channels
    console.log('📡 Test 6: Getting channels...');
    try {
      const channels = await zeeVeeAPI.getChannels();
      console.log('   ✅ Channels retrieved successfully');
      console.log(`   📊 Found ${channels.length || 0} channel(s)`);
      
      if (channels && channels.length > 0) {
        console.log('   📋 Channels:');
        channels.forEach((channel, index) => {
          console.log(`      ${index + 1}. ${channel.name || channel.id}`);
        });
      } else {
        console.log('   ℹ️  No channels found');
      }
      console.log();
    } catch (error) {
      console.error('   ❌ Channels failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 7: Get network settings
    console.log('🌐 Test 7: Getting network settings...');
    try {
      const networkSettings = await zeeVeeAPI.getNetworkSettings();
      console.log('   ✅ Network settings retrieved successfully');
      console.log(`   📊 Network Settings: ${JSON.stringify(networkSettings, null, 2)}\n`);
    } catch (error) {
      console.error('   ❌ Network settings failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    // Test 8: Get health status
    console.log('❤️  Test 8: Getting health status...');
    try {
      const healthStatus = await zeeVeeAPI.getHealthStatus();
      console.log('   ✅ Health status retrieved successfully');
      console.log(`   📊 Health Status: ${JSON.stringify(healthStatus, null, 2)}\n`);
    } catch (error) {
      console.error('   ❌ Health status failed:', error.message);
      if (error.response) {
        console.error('   📝 Response:', error.response.status, error.response.statusText);
        console.error('   📝 Data:', error.response.data);
      }
      throw error;
    }

    console.log('🎉 All tests passed! ZeeVee HDbridge 2920-NA integration is working correctly.\n');

  } catch (error) {
    console.error('\n💥 Integration test failed:', error.message);
    
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      console.log('\n🔐 Authentication Issue:');
      console.log('   - Check that your username and password are correct');
      console.log('   - Ensure you have the correct permissions on your ZeeVee device');
    } else if (error.message.includes('400') || error.message.includes('Bad Request')) {
      console.log('\n⚠️  Request Issue:');
      console.log('   - Check that host and port are set correctly');
      console.log('   - Verify the API endpoint is accessible from your location');
    } else if (error.message.includes('ECONNREFUSED') || error.message.includes('ETIMEDOUT')) {
      console.log('\n🔌 Connection Issue:');
      console.log('   - Check that the ZeeVee device is powered on and connected to the network');
      console.log('   - Verify that the host and port are correct');
      console.log('   - Ensure there are no firewalls blocking the connection');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testZeeVeeIntegration();
}

module.exports = testZeeVeeIntegration;