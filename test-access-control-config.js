/**
 * Test script to verify the access control configuration endpoint
 * 
 * This script tests the /api/access-control/config endpoint to ensure
 * it correctly detects the Unifi Access and Lenel S2 NetBox integrations.
 * 
 * Note: This script directly tests the controller function rather than making
 * an HTTP request, which would require authentication and a running server.
 */

const dotenv = require('dotenv');
const accessControlController = require('./server/controllers/accessControlController');

// Load environment variables
dotenv.config();

// Function to test the access control configuration endpoint
async function testAccessControlConfig() {
  try {
    console.log('Testing access control configuration directly...');
    
    // Create mock request and response objects
    const req = {};
    const res = {
      json: (data) => {
        // Store the response data
        res.data = data;
        return res;
      },
      status: (code) => {
        res.statusCode = code;
        return res;
      },
      data: null,
      statusCode: 200
    };
    
    // Call the controller function directly
    await accessControlController.getConfigStatus(req, res);
    
    // Log the response data
    console.log('Response status:', res.statusCode);
    console.log('Response data:', JSON.stringify(res.data, null, 2));
    
    // Check if Unifi Access is configured
    if (res.data.unifiAccess) {
      console.log('✅ Unifi Access is correctly detected as configured');
      console.log('Unifi Access host:', res.data.unifiAccess.host);
    } else {
      console.log('❌ Unifi Access is not detected as configured');
    }
    
    // Check if Lenel S2 NetBox is configured
    if (res.data.lenelS2NetBox) {
      console.log('✅ Lenel S2 NetBox is correctly detected as configured');
      console.log('Lenel S2 NetBox host:', res.data.lenelS2NetBox.host);
    } else {
      console.log('❌ Lenel S2 NetBox is not detected as configured');
    }
    
    // Check environment variables
    console.log('\nEnvironment Variables Check:');
    console.log('UNIFI_ACCESS_HOST:', process.env.UNIFI_ACCESS_HOST ? '✅ Set' : '❌ Not set');
    console.log('UNIFI_ACCESS_API_KEY:', process.env.UNIFI_ACCESS_API_KEY ? '✅ Set' : '❌ Not set');
    console.log('LENEL_S2_NETBOX_HOST:', process.env.LENEL_S2_NETBOX_HOST ? '✅ Set' : '❌ Not set');
    console.log('LENEL_S2_NETBOX_USERNAME:', process.env.LENEL_S2_NETBOX_USERNAME ? '✅ Set' : '❌ Not set');
    console.log('LENEL_S2_NETBOX_PASSWORD:', process.env.LENEL_S2_NETBOX_PASSWORD ? '✅ Set' : '❌ Not set');
    
    return true;
  } catch (error) {
    console.error('Error testing access control configuration:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test
testAccessControlConfig()
  .then(success => {
    if (success) {
      console.log('\nTest completed successfully');
    } else {
      console.log('\nTest failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });