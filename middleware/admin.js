const User = require('../models/User');

/**
 * Middleware to check if user has admin role
 */
module.exports = async (req, res, next) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ msg: 'Not authenticated. Please log in.' });
  }

  try {
    // Get user with fresh data from database
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(403).json({ msg: 'User account is inactive' });
    }

    // Check if user has admin role
    if (!user.roles.includes('admin')) {
      return res.status(403).json({ msg: 'Access denied. Admin privileges required.' });
    }

    next();
  } catch (err) {
    console.error('Error in admin authorization:', err);
    res.status(500).json({ msg: 'Server error during authorization' });
  }
};