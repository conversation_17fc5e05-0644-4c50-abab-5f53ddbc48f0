const User = require('../models/User');
const adminMiddleware = require('./admin');

/**
 * Middleware to check if user is authenticated
 */
exports.isAuthenticated = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ msg: 'Not authenticated. Please log in.' });
};

/**
 * Middleware to check if user has admin role
 */
exports.isAdmin = adminMiddleware;

/**
 * Middleware to check if user has access to a specific integration
 * All integrations are now assumed to be active for all users
 * Access is controlled by roles and permissions
 * @param {String} integration - Integration ID to check
 */
exports.hasActivatedIntegration = (integration) => {
  return async (req, res, next) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ msg: 'Not authenticated. Please log in.' });
    }

    try {
      // Get user with fresh data from database
      const user = await User.findById(req.user.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({ msg: 'User account is inactive' });
      }

      // All users now have access to all integrations
      // Access control is handled by role-based permissions
      
      // Store integration preferences in the request for controllers to use
      const integrationPrefs = user.integrationPreferences?.get 
        ? user.integrationPreferences.get(integration) 
        : user.integrationPreferences?.[integration];
      
      req.useGlobalConfig = integrationPrefs ? integrationPrefs.useGlobalConfig : true;

      next();
    } catch (err) {
      console.error('Error checking integration access:', err);
      res.status(500).json({ msg: 'Server error during integration access check' });
    }
  };
};

/**
 * Middleware to check if user has required roles
 * @param {Array} roles - Array of required roles
 */
exports.hasRoles = (roles) => {
  return async (req, res, next) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ msg: 'Not authenticated. Please log in.' });
    }

    try {
      // Get user with fresh data from database
      const user = await User.findById(req.user.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({ msg: 'User account is inactive' });
      }

      // Check if user has admin role (admins can access everything)
      if (user.roles.includes('admin')) {
        return next();
      }

      // Check if user has at least one of the required roles
      const hasRequiredRole = user.roles.some(role => roles.includes(role));

      if (!hasRequiredRole) {
        return res.status(403).json({ msg: 'Access denied. Insufficient permissions.' });
      }

      // Update user's last login time
      user.lastLogin = Date.now();
      await user.save();

      next();
    } catch (err) {
      console.error('Error in role authorization:', err);
      res.status(500).json({ msg: 'Server error during authorization' });
    }
  };
};

/**
 * Middleware to check if user has specific permission
 * @param {String} permission - Required permission
 */
exports.hasPermission = (permission) => {
  return async (req, res, next) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ msg: 'Not authenticated. Please log in.' });
    }

    try {
      // Get user with fresh data from database
      const user = await User.findById(req.user.id).populate('roles');

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({ msg: 'User account is inactive' });
      }

      // Check if user has admin role (admins have all permissions)
      if (user.roles.includes('admin')) {
        return next();
      }

      // Get all roles for the user
      const Role = require('../models/Role');
      const userRoles = await Role.find({ name: { $in: user.roles } });

      // Check if any of the user's roles have the required permission
      // or have the wildcard permission '*'
      const hasPermission = userRoles.some(role => {
        // Check for global wildcard permission
        if (role.permissions.includes('*')) {
          return true;
        }
        
        // Check for exact permission match
        if (role.permissions.includes(permission)) {
          return true;
        }
        
        // Handle hierarchical permissions (entity:level)
        if (permission.includes(':')) {
          const [entity, level] = permission.split(':');
          
          // Check for entity wildcard (entity:*)
          if (role.permissions.includes(`${entity}:*`)) {
            return true;
          }
          
          // Check for higher level permissions
          if (level === 'read') {
            // write, delete, and admin permissions include read
            return role.permissions.includes(`${entity}:write`) || 
                   role.permissions.includes(`${entity}:delete`) ||
                   role.permissions.includes(`${entity}:admin`);
          } else if (level === 'write') {
            // delete and admin permissions include write
            return role.permissions.includes(`${entity}:delete`) ||
                   role.permissions.includes(`${entity}:admin`);
          } else if (level === 'delete') {
            // admin permission includes delete
            return role.permissions.includes(`${entity}:admin`);
          }
        }
        
        return false;
      });

      if (!hasPermission) {
        return res.status(403).json({ msg: 'Access denied. Insufficient permissions.' });
      }

      next();
    } catch (err) {
      console.error('Error in permission authorization:', err);
      res.status(500).json({ msg: 'Server error during authorization' });
    }
  };
};
