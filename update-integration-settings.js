/**
 * <PERSON><PERSON>t to update all integration settings to make them required by default
 * This ensures all integrations are enabled and active for all users
 */

const mongoose = require('mongoose');
const config = require('./config');
const IntegrationSettings = require('./models/IntegrationSettings');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect(config.mongoURI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(async () => {
  console.log('MongoDB Connected');
  
  try {
    // Get all available integrations from the file system
    const fs = require('fs');
    const path = require('path');
    const integrationsDir = path.join(process.cwd(), 'server', 'integrations');
    
    const availableIntegrations = fs.readdirSync(integrationsDir)
      .filter(item => {
        // Filter out any non-directory items or hidden files
        const itemPath = path.join(integrationsDir, item);
        return fs.statSync(itemPath).isDirectory() && !item.startsWith('.');
      });
    
    console.log(`Found ${availableIntegrations.length} integrations: ${availableIntegrations.join(', ')}`);
    
    // Update or create settings for each integration
    for (const integrationId of availableIntegrations) {
      let settings = await IntegrationSettings.findOne({ integrationId });
      
      if (!settings) {
        // Create new settings if they don't exist
        settings = new IntegrationSettings({
          integrationId,
          isRequired: true,
          isReadOnly: true,
          useGlobalConfig: true
        });
      } else {
        // Update existing settings
        settings.isRequired = true;
        settings.isReadOnly = true;
        settings.useGlobalConfig = true;
      }
      
      await settings.save();
      console.log(`Updated settings for ${integrationId}`);
    }
    
    // Update all users to enable all integrations
    const users = await User.find();
    console.log(`Updating ${users.length} users...`);
    
    for (const user of users) {
      // Enable all integrations for each user
      for (const integrationId of availableIntegrations) {
        // Initialize preferences for this integration if they don't exist
        if (!user.integrationPreferences.has(integrationId)) {
          user.integrationPreferences.set(integrationId, {
            enabled: true,
            useGlobalConfig: true
          });
        } else {
          // Update existing preferences
          const prefs = user.integrationPreferences.get(integrationId);
          prefs.enabled = true;
          prefs.useGlobalConfig = true;
          user.integrationPreferences.set(integrationId, prefs);
        }
        
        // Add to activated integrations if not already there
        if (!user.activatedIntegrations.includes(integrationId)) {
          user.activatedIntegrations.push(integrationId);
        }
      }
      
      await user.save();
    }
    
    console.log('All integration settings updated successfully');
    console.log('All users updated to enable all integrations');
    
    process.exit(0);
  } catch (error) {
    console.error('Error updating integration settings:', error);
    process.exit(1);
  }
})
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});