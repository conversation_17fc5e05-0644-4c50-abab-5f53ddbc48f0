// Test script to verify that the auth middleware can be imported correctly
const path = require('path');
console.log('Current directory:', process.cwd());

try {
  // Try to require the forms.js file
  const formsRouter = require('./server/routes/api/forms');
  console.log('Successfully imported forms.js');
} catch (error) {
  console.error('Error importing forms.js:', error.message);
  console.error('Error stack:', error.stack);
}