#!/bin/bash

# CSF Portal Deployment Script
# This script automates the deployment process by:
# 1. Pulling the latest code from git
# 2. Building the Docker containers
# 3. Stopping the current containers
# 4. Starting the new containers in detached mode
# The script will stop if any step fails and display error messages.

# Set error handling
set -e  # Exit immediately if a command exits with a non-zero status

# Function to display error messages
function error_exit {
    echo -e "\033[0;31mERROR: $1\033[0m" >&2
    exit 1
}

# Function to display success messages
function success_message {
    echo -e "\033[0;32m$1\033[0m"
}

# Function to display info messages
function info_message {
    echo -e "\033[0;34m$1\033[0m"
}

# Display start message
info_message "Starting deployment process for CSF Portal..."

# Step 1: Pull latest code from git
info_message "Step 1: Pulling latest code from git repository..."
if ! git pull; then
    error_exit "Failed to pull latest code from git repository."
fi
success_message "Successfully pulled latest code."

# Step 2: Build Docker containers
info_message "Step 2: Building Docker containers..."
if ! docker-compose build; then
    error_exit "Failed to build Docker containers. Please check the error messages above."
fi
success_message "Successfully built Docker containers."

# Step 3: Stop current containers
info_message "Step 3: Stopping current containers..."
if ! docker-compose down; then
    error_exit "Failed to stop current containers."
fi
success_message "Successfully stopped current containers."

# Step 4: Start new containers in detached mode
info_message "Step 4: Starting new containers in detached mode..."
if ! docker-compose up -d; then
    error_exit "Failed to start new containers."
fi
success_message "Successfully started new containers."

# Display completion message
success_message "Deployment completed successfully!"
info_message "The CSF Portal is now running with the latest code."