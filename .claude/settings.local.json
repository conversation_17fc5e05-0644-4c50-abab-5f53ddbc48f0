{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["WebFetch(domain:www.wiimhome.com)", "WebFetch(domain:github.com)", "Bash(npm run dev:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm test)", "<PERSON><PERSON>(chmod:*)", "Bash(npm install:*)", "Bash(NODE_ENV=development ENABLE_DEBUG_LOGGING=true npm run server)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(NODE_ENV=development npm run server)", "Bash(DEBUG=api-debug NODE_ENV=development ENABLE_DEBUG_LOGGING=true node -e \"\nconst axios = require(''axios'');\n\nconsole.log(''Testing axios interception...'');\n\nsetTimeout(async () => {\n  try {\n    console.log(''Making test axios request...'');\n    const response = await axios.get(''https://jsonplaceholder.typicode.com/posts/1'');\n    console.log(''Request completed successfully'');\n  } catch (error) {\n    console.log(''Request failed:'', error.message);\n  }\n}, 1000);\n\")", "Bash(DEBUG=api-debug node test_axios_debug.js)", "Bash(npm run test:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm run:*)", "Bash(ls:*)", "Bash(npm test:*)", "Bash(echo $PORT)", "WebFetch(domain:developer.apple.com)", "Bash(PORT=3001 npm start)", "Bash(PORT=3001 npm run client)"], "deny": []}}