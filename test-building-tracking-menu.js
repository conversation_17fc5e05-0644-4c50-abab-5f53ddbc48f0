/**
 * Test script to reinitialize menu items and verify that the Building Tracking menu item is included
 */

const axios = require('axios');

async function main() {
  try {
    console.log('Reinitializing default menu items...');
    
    // Call the API endpoint to reinitialize menu items
    const response = await axios.post('http://localhost:6000/api/menu-items/init');
    
    console.log(`Initialized ${response.data.length} menu items`);
    
    // Check if the Building Tracking menu item is included
    const buildingTrackingItem = response.data.find(item => 
      item.title === 'Building Tracking' && 
      item.path === '/building-management/tracking'
    );
    
    if (buildingTrackingItem) {
      console.log('Success! Building Tracking menu item was added:');
      console.log(JSON.stringify(buildingTrackingItem, null, 2));
    } else {
      console.error('Error: Building Tracking menu item was not found in the initialized items');
    }
    
    // List all Building Management category items
    console.log('\nAll Building Management category items:');
    const buildingManagementItems = response.data.filter(item => 
      item.categories.includes('Building Management')
    );
    
    buildingManagementItems.forEach(item => {
      console.log(`- ${item.title} (${item.path})`);
    });
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

main();