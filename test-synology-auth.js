// Test script to debug Synology authentication issues with 2FA support
require('dotenv').config();
const axios = require('axios');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify the question method
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

async function testSynologyAuth() {
  try {
    console.log('Testing Synology authentication with environment variables:');
    console.log(`SYNOLOGY_HOST: ${process.env.SYNOLOGY_HOST}`);
    console.log(`SYNOLOGY_PORT: ${process.env.SYNOLOGY_PORT}`);
    console.log(`SYNOLOGY_USERNAME: ${process.env.SYNOLOGY_USERNAME}`);
    console.log(`SYNOLOGY_SECURE: ${process.env.SYNOLOGY_SECURE}`);
    
    // Extract host without protocol
    let host = process.env.SYNOLOGY_HOST;
    if (host.startsWith('http://') || host.startsWith('https://')) {
      host = host.replace(/^https?:\/\//, '');
    }
    
    const secure = process.env.SYNOLOGY_SECURE !== 'false';
    const baseURL = `${secure ? 'https' : 'http'}://${host}:${process.env.SYNOLOGY_PORT}/webapi`;
    
    console.log(`Generated baseURL: ${baseURL}`);
    
    // Step 1: Get API Info
    console.log('\n1. Getting API info...');
    const apiInfoUrl = `${baseURL}/entry.cgi?api=SYNO.API.Info&version=1&method=query&query=SYNO.API.Auth,SYNO.API.OTP`;
    const apiInfoResponse = await axios.get(apiInfoUrl);
    console.log('API info response:', JSON.stringify(apiInfoResponse.data, null, 2));
    
    if (!apiInfoResponse.data.success) {
      throw new Error(`Failed to get API info: ${JSON.stringify(apiInfoResponse.data.error)}`);
    }
    
    // Extract auth API info
    const authApiInfo = apiInfoResponse.data.data['SYNO.API.Auth'];
    const otpApiInfo = apiInfoResponse.data.data['SYNO.API.OTP'] || { path: 'entry.cgi', maxVersion: 1, minVersion: 1 };
    console.log('Auth API info:', authApiInfo);
    console.log('OTP API info:', otpApiInfo);
    
    // Step 2: Try authentication with version 6 (which supports 2FA)
    console.log('\n2. Trying authentication with version 6...');
    
    // Build auth URL
    const authParams = {
      api: 'SYNO.API.Auth',
      version: '6',
      method: 'login',
      account: process.env.SYNOLOGY_USERNAME,
      passwd: process.env.SYNOLOGY_PASSWORD,
      session: 'FileStation',
      enable_syno_token: 'yes'
    };
    
    // Convert params to query string
    const queryString = Object.entries(authParams)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
    
    const authUrl = `${baseURL}/${authApiInfo.path}?${queryString}`;
    console.log(`Auth URL: ${authUrl.replace(process.env.SYNOLOGY_PASSWORD, '********')}`);
    
    try {
      const response = await axios.get(authUrl);
      console.log('Auth response:', JSON.stringify(response.data, null, 2));
      
      // Check if authentication was successful
      if (response.data.success) {
        console.log('✅ Authentication successful without 2FA!');
        
        // If we got a successful response, try listing files
        if (response.data.data && response.data.data.sid) {
          await testListFiles(baseURL, response.data.data.sid);
        }
      } else {
        // Check if this is a 2FA error (code 406)
        if (response.data.error && response.data.error.code === 406) {
          console.log('🔐 2FA is required. Processing second authentication step...');
          
          // Extract the token from the error response
          const token = response.data.error.errors && response.data.error.errors.token;
          
          if (token) {
            // Ask user for the verification code
            const code = await question('Enter the 2FA verification code: ');
            
            // Step 3: Complete 2FA authentication
            console.log('\n3. Completing 2FA authentication...');
            
            // Build 2FA auth URL
            const otpParams = {
              api: 'SYNO.API.Auth',
              version: '6',
              method: 'login',
              otp_code: code,
              account: process.env.SYNOLOGY_USERNAME,
              session: 'FileStation',
              syno_token: token
            };
            
            // Convert params to query string
            const otpQueryString = Object.entries(otpParams)
              .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
              .join('&');
            
            const otpUrl = `${baseURL}/${authApiInfo.path}?${otpQueryString}`;
            console.log(`OTP URL: ${otpUrl}`);
            
            try {
              const otpResponse = await axios.get(otpUrl);
              console.log('OTP response:', JSON.stringify(otpResponse.data, null, 2));
              
              if (otpResponse.data.success) {
                console.log('✅ 2FA authentication successful!');
                
                // If we got a successful response, try listing files
                if (otpResponse.data.data && otpResponse.data.data.sid) {
                  await testListFiles(baseURL, otpResponse.data.data.sid);
                }
              } else {
                console.error('❌ 2FA authentication failed:', otpResponse.data.error);
              }
            } catch (otpError) {
              console.error('❌ Error during 2FA authentication:', otpError.message);
              if (otpError.response) {
                console.error('Response status:', otpError.response.status);
                console.error('Response data:', JSON.stringify(otpError.response.data, null, 2));
              }
            }
          } else {
            console.error('❌ No token found in the 2FA error response');
          }
        } else {
          console.error('❌ Authentication failed:', response.data.error);
        }
      }
    } catch (authError) {
      console.error('❌ Error during authentication:', authError.message);
      if (authError.response) {
        console.error('Response status:', authError.response.status);
        console.error('Response data:', JSON.stringify(authError.response.data, null, 2));
      }
    }
    
  } catch (error) {
    console.error('Error in test script:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  } finally {
    // Close the readline interface
    rl.close();
  }
}

// Helper function to test listing files
async function testListFiles(baseURL, sid) {
  console.log(`\nTrying to list files with SID: ${sid}`);
  
  // Try listing files in root directory
  const listFilesParams = {
    api: 'SYNO.FileStation.List',
    version: '2',
    method: 'list',
    folder_path: '/volume1',
    _sid: sid
  };
  
  const listFilesQueryString = Object.entries(listFilesParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  
  const listFilesUrl = `${baseURL}/entry.cgi?${listFilesQueryString}`;
  console.log(`List files URL: ${listFilesUrl}`);
  
  try {
    const listFilesResponse = await axios.get(listFilesUrl);
    console.log('List files response:', JSON.stringify(listFilesResponse.data, null, 2));
    
    if (listFilesResponse.data.success) {
      console.log('✅ Successfully listed files!');
    }
  } catch (listFilesError) {
    console.error('❌ Error listing files:', listFilesError.message);
    if (listFilesError.response) {
      console.error('Response status:', listFilesError.response.status);
      console.error('Response data:', JSON.stringify(listFilesError.response.data, null, 2));
    }
  }
}

// Run the test
testSynologyAuth();