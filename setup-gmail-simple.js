#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');
const { execSync } = require('child_process');

/**
 * Simplified Gmail Ticketing Setup Script
 * Uses service account authentication instead of OAuth2
 */
class SimpleGmailSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.config = {
      projectId: null,
      serviceAccountEmail: null,
      serviceAccountKeyPath: null,
      topicName: 'gmail-tickets',
      subscriptionName: 'gmail-tickets-sub',
      monitoredEmail: null,
      webhookSecret: crypto.randomBytes(32).toString('hex')
    };
  }

  /**
   * Main setup process
   */
  async run() {
    try {
      console.log('\n🎫 Simplified Gmail Ticketing Setup');
      console.log('====================================\n');
      
      await this.checkPrerequisites();
      await this.setupProject();
      await this.enableAPIs();
      await this.createServiceAccount();
      await this.setupPubSub();
      await this.configureGmailSettings();
      await this.updateEnvironmentFile();
      await this.showCompletionInstructions();
      
      console.log('\n✅ Setup completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      console.log('\nTroubleshooting:');
      console.log('1. Ensure Google Cloud CLI is installed and authenticated');
      console.log('2. Make sure you have the necessary permissions in Google Cloud');
      console.log('3. Check that the project ID is correct');
      console.log('4. Try running: gcloud auth login');
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  /**
   * Check prerequisites
   */
  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...\n');
    
    // Check if gcloud CLI is installed
    try {
      execSync('gcloud --version', { stdio: 'pipe' });
      console.log('✅ Google Cloud CLI found');
    } catch (error) {
      console.log('❌ Google Cloud CLI not found');
      console.log('\nInstall Google Cloud CLI:');
      console.log('macOS: brew install google-cloud-sdk');
      console.log('Windows: https://cloud.google.com/sdk/docs/install');
      console.log('Linux: https://cloud.google.com/sdk/docs/install\n');
      throw new Error('Google Cloud CLI required');
    }

    // Check authentication
    try {
      const account = execSync('gcloud auth list --filter=status:ACTIVE --format="value(account)"', { 
        encoding: 'utf8' 
      }).trim();
      
      if (!account) {
        console.log('❌ Not authenticated with Google Cloud');
        console.log('\nRun: gcloud auth login');
        throw new Error('Authentication required');
      }
      
      console.log(`✅ Authenticated as: ${account}`);
    } catch (error) {
      console.log('❌ Authentication check failed');
      console.log('\nRun: gcloud auth login');
      throw new Error('Authentication required');
    }

    console.log('✅ All prerequisites met\n');
  }

  /**
   * Set up Google Cloud project
   */
  async setupProject() {
    console.log('🏗️  Setting up Google Cloud project...\n');
    
    // Get current project
    try {
      const currentProject = execSync('gcloud config get-value project', { 
        encoding: 'utf8' 
      }).trim();
      
      if (currentProject && currentProject !== '(unset)') {
        const useExisting = await this.question(
          `Use existing project "${currentProject}"? (y/n): `
        );
        
        if (useExisting.toLowerCase() === 'y') {
          this.config.projectId = currentProject;
        }
      }
    } catch (error) {
      // No current project
    }

    if (!this.config.projectId) {
      console.log('\n📋 Create a new project or use existing:');
      console.log('1. Go to: https://console.cloud.google.com/projectcreate');
      console.log('2. Create a new project or note an existing project ID');
      
      this.config.projectId = await this.question('\nEnter your Google Cloud Project ID: ');
      
      // Set the project
      try {
        execSync(`gcloud config set project ${this.config.projectId}`, { stdio: 'pipe' });
        console.log(`✅ Project set to: ${this.config.projectId}`);
      } catch (error) {
        throw new Error(`Failed to set project: ${error.message}`);
      }
    }
    
    console.log(`✅ Using project: ${this.config.projectId}\n`);
  }

  /**
   * Enable required APIs
   */
  async enableAPIs() {
    console.log('🔌 Enabling required Google APIs...\n');
    
    const apis = [
      { name: 'gmail.googleapis.com', description: 'Gmail API' },
      { name: 'pubsub.googleapis.com', description: 'Cloud Pub/Sub API' },
      { name: 'iam.googleapis.com', description: 'Identity and Access Management API' }
    ];

    for (const api of apis) {
      try {
        console.log(`Enabling ${api.description}...`);
        execSync(`gcloud services enable ${api.name} --project=${this.config.projectId}`, { stdio: 'pipe' });
        console.log(`✅ ${api.description} enabled`);
      } catch (error) {
        console.log(`⚠️  ${api.description} may already be enabled or failed to enable`);
      }
    }
    
    // Wait a moment for APIs to propagate
    console.log('⏳ Waiting for APIs to initialize...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log('✅ All APIs enabled\n');
  }

  /**
   * Create service account
   */
  async createServiceAccount() {
    console.log('👤 Creating service account...\n');
    
    const serviceAccountName = 'gmail-ticketing-sa';
    this.config.serviceAccountEmail = `${serviceAccountName}@${this.config.projectId}.iam.gserviceaccount.com`;
    
    try {
      // Create service account
      console.log('Creating service account...');
      execSync(`gcloud iam service-accounts create ${serviceAccountName} \
        --display-name="Gmail Ticketing Service Account" \
        --description="Service account for Gmail ticketing system" \
        --project=${this.config.projectId}`, { stdio: 'pipe' });
      console.log('✅ Service account created');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('✅ Service account already exists');
      } else {
        console.log('⚠️  Service account creation failed, continuing...');
      }
    }

    // Grant necessary roles
    const roles = [
      { role: 'roles/pubsub.admin', description: 'Pub/Sub Admin' },
      { role: 'roles/logging.logWriter', description: 'Logs Writer' }
    ];

    for (const roleInfo of roles) {
      try {
        console.log(`Granting ${roleInfo.description} role...`);
        execSync(`gcloud projects add-iam-policy-binding ${this.config.projectId} \
          --member="serviceAccount:${this.config.serviceAccountEmail}" \
          --role="${roleInfo.role}"`, { stdio: 'pipe' });
        console.log(`✅ ${roleInfo.description} role granted`);
      } catch (error) {
        console.log(`⚠️  ${roleInfo.description} role may already be granted`);
      }
    }

    // Create service account key
    const keyFileName = 'gmail-ticketing-service-account-key.json';
    this.config.serviceAccountKeyPath = path.join(__dirname, keyFileName);
    
    try {
      console.log('Creating service account key...');
      execSync(`gcloud iam service-accounts keys create "${this.config.serviceAccountKeyPath}" \
        --iam-account="${this.config.serviceAccountEmail}" \
        --project=${this.config.projectId}`, { stdio: 'pipe' });
      console.log(`✅ Service account key saved: ${keyFileName}`);
    } catch (error) {
      console.log('⚠️  Service account key creation may have failed');
    }
    
    console.log('✅ Service account setup completed\n');
  }

  /**
   * Set up Pub/Sub
   */
  async setupPubSub() {
    console.log('📡 Setting up Pub/Sub for Gmail notifications...\n');
    
    try {
      // Create topic
      console.log(`Creating Pub/Sub topic: ${this.config.topicName}`);
      execSync(`gcloud pubsub topics create ${this.config.topicName} --project=${this.config.projectId}`, { stdio: 'pipe' });
      console.log(`✅ Topic created: ${this.config.topicName}`);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log(`✅ Topic already exists: ${this.config.topicName}`);
      } else {
        console.log(`⚠️  Topic creation failed: ${this.config.topicName}`);
      }
    }

    try {
      // Create subscription
      console.log(`Creating Pub/Sub subscription: ${this.config.subscriptionName}`);
      execSync(`gcloud pubsub subscriptions create ${this.config.subscriptionName} \
        --topic=${this.config.topicName} \
        --project=${this.config.projectId}`, { stdio: 'pipe' });
      console.log(`✅ Subscription created: ${this.config.subscriptionName}`);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log(`✅ Subscription already exists: ${this.config.subscriptionName}`);
      } else {
        console.log(`⚠️  Subscription creation failed: ${this.config.subscriptionName}`);
      }
    }

    // Grant Gmail API permission to publish to topic
    try {
      console.log('Granting Gmail API permissions to publish to topic...');
      execSync(`gcloud pubsub topics add-iam-policy-binding ${this.config.topicName} \
        --member="serviceAccount:<EMAIL>" \
        --role="roles/pubsub.publisher" \
        --project=${this.config.projectId}`, { stdio: 'pipe' });
      console.log('✅ Gmail API permissions granted');
    } catch (error) {
      console.log('⚠️  Gmail API permissions may already be granted');
    }
    
    console.log('✅ Pub/Sub setup completed\n');
  }

  /**
   * Configure Gmail settings
   */
  async configureGmailSettings() {
    console.log('📧 Configuring Gmail settings...\n');
    
    this.config.monitoredEmail = await this.question(
      'Enter the email address to monitor for tickets (e.g., <EMAIL>): '
    );

    console.log('\n📋 Gmail Configuration Notes:');
    console.log('• The monitored email must have Gmail API access');
    console.log('• For Google Workspace, domain-wide delegation may be required');
    console.log('• Gmail watch notifications will be set up automatically when your app starts');
    
    console.log('✅ Gmail settings configured\n');
  }

  /**
   * Update environment file
   */
  async updateEnvironmentFile() {
    console.log('📝 Updating environment configuration...\n');
    
    const envPath = path.join(__dirname, '.env');
    
    // Read existing .env file
    let existingEnv = '';
    try {
      existingEnv = await fs.readFile(envPath, 'utf8');
    } catch (error) {
      console.log('Creating new .env file...');
    }

    // Gmail ticketing configuration
    const gmailConfig = `
# Gmail Ticketing System Configuration (Generated by setup script)
GOOGLE_GMAIL_PROJECT_ID=${this.config.projectId}
GOOGLE_GMAIL_TOPIC_NAME=${this.config.topicName}
GOOGLE_GMAIL_SUBSCRIPTION_NAME=${this.config.subscriptionName}
GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY=${this.config.serviceAccountKeyPath}
GMAIL_MONITORED_EMAIL=${this.config.monitoredEmail}

# SMTP Configuration for outgoing emails (Update SMTP_PASS with app password)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=${this.config.monitoredEmail}
SMTP_PASS=YOUR_APP_PASSWORD_HERE

# Ticket system configuration
TICKET_EMAIL_FROM=${this.config.monitoredEmail}
PORTAL_URL=https://yourportal.com
WEBHOOK_SECRET=${this.config.webhookSecret}
`;

    // Remove existing Gmail configuration
    const cleanedEnv = existingEnv.replace(
      /# Gmail Ticketing System Configuration[\s\S]*?(?=\n#|\n[A-Z]|\n$|$)/g, 
      ''
    ).trim();

    // Combine configurations
    const newEnvContent = (cleanedEnv ? cleanedEnv + '\n' : '') + gmailConfig;
    
    // Write updated .env file
    await fs.writeFile(envPath, newEnvContent);
    console.log('✅ Environment file updated (.env)');

    // Create example file
    const exampleContent = newEnvContent.replace(/=.+$/gm, '=YOUR_VALUE_HERE');
    await fs.writeFile(path.join(__dirname, '.env.example'), exampleContent);
    console.log('✅ Example environment file created (.env.example)');
    
    console.log('✅ Environment configuration completed\n');
  }

  /**
   * Show completion instructions
   */
  async showCompletionInstructions() {
    console.log('📋 IMPORTANT: Complete these final steps:\n');
    
    console.log('1. 🔑 Set up App Password for SMTP:');
    console.log(`   • Go to: https://myaccount.google.com/security`);
    console.log(`   • Enable 2-Step Verification if not already enabled`);
    console.log(`   • Click "App passwords"`);
    console.log(`   • Generate password for "Mail"`);
    console.log(`   • Update SMTP_PASS in your .env file with the generated password\n`);
    
    console.log('2. 🌐 Update Portal URL:');
    console.log(`   • Replace "https://yourportal.com" in .env with your actual portal URL\n`);
    
    console.log('3. 🔐 Domain-wide Delegation (Google Workspace only):');
    console.log(`   • Go to: https://admin.google.com/ac/owl/domainwidedelegation`);
    console.log(`   • Add client ID from service account key file`);
    console.log(`   • Scopes: https://www.googleapis.com/auth/gmail.readonly,https://www.googleapis.com/auth/pubsub\n`);
    
    console.log('4. 🧪 Test Your Setup:');
    console.log(`   • Start your application: npm run dev`);
    console.log(`   • Send test email to: ${this.config.monitoredEmail}`);
    console.log(`   • Check server logs for processing confirmation\n`);
    
    console.log('5. 📁 Generated Files:');
    console.log(`   • Service account key: ${path.basename(this.config.serviceAccountKeyPath)}`);
    console.log(`   • Environment config: .env`);
    console.log(`   • Example config: .env.example\n`);
    
    console.log('🔒 Security Note: Keep the service account key file secure and never commit it to git!');
  }

  /**
   * Utility function for user input
   */
  question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  const setup = new SimpleGmailSetup();
  setup.run().catch(console.error);
}

module.exports = SimpleGmailSetup;