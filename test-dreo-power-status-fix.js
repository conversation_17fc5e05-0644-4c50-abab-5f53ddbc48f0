// Test script to verify the fix for Dreo power status detection
require('dotenv').config();
const DreoAPI = require('./server/integrations/dreo/dreoAPI');

// Get credentials from environment variables
const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
const password = process.env.DREO_PASSWORD;

if (!email || !password) {
  console.error('Error: DREO_EMAIL/DREO_USERNAME and DREO_PASSWORD environment variables must be set');
  process.exit(1);
}

async function testDreoPowerStatusFix() {
  try {
    console.log('Initializing Dreo API with credentials...');
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    console.log('Authenticating with Dreo API...');
    await dreoAPI.authenticate();
    
    // Get devices
    console.log('Fetching devices...');
    const devices = await dreoAPI.getDevices();
    console.log(`Found ${devices.length} devices`);
    
    if (devices.length === 0) {
      console.log('No devices found. Please make sure your Dreo account has devices associated with it.');
      return;
    }
    
    // Test power status detection for each device
    for (let i = 0; i < devices.length; i++) {
      const device = devices[i];
      const deviceId = device.id || device.sn;
      console.log(`\n--- Device ${i + 1}: ${device.name || device.deviceName} (ID: ${deviceId}) ---`);
      
      // Get device details
      console.log('Fetching device details...');
      const deviceDetails = await dreoAPI.getDeviceDetails(deviceId);
      
      // Check power status
      console.log('\nPower status detection:');
      
      // Check if power status is available at the top level
      if (deviceDetails.power !== undefined) {
        console.log(`- Power status: ${deviceDetails.power} (${typeof deviceDetails.power})`);
        console.log(`- Interpreted as: ${deviceDetails.power ? 'ON' : 'OFF'}`);
        
        // Verify that power status is a boolean
        if (typeof deviceDetails.power === 'boolean') {
          console.log('✅ Power status is correctly represented as a boolean value');
        } else {
          console.log('❌ Power status is not a boolean value');
        }
      } else {
        console.log('❌ Power status not found at the top level');
      }
      
      // Check if power status is available in the state object
      if (deviceDetails.state && deviceDetails.state.power !== undefined) {
        console.log(`- State.power: ${deviceDetails.state.power} (${typeof deviceDetails.state.power})`);
        console.log(`- Interpreted as: ${deviceDetails.state.power ? 'ON' : 'OFF'}`);
        
        // Verify that power status in state is a boolean
        if (typeof deviceDetails.state.power === 'boolean') {
          console.log('✅ Power status in state is correctly represented as a boolean value');
        } else {
          console.log('❌ Power status in state is not a boolean value');
        }
      } else {
        console.log('- State.power: Not found');
      }
      
      // Check if original power properties are still available
      console.log('\nOriginal power properties:');
      
      if (deviceDetails.state && deviceDetails.state.poweron !== undefined) {
        console.log(`- State.poweron: ${deviceDetails.state.poweron} (${typeof deviceDetails.state.poweron})`);
      } else {
        console.log('- State.poweron: Not found');
      }
      
      if (deviceDetails.state && deviceDetails.state.on !== undefined) {
        console.log(`- State.on: ${deviceDetails.state.on} (${typeof deviceDetails.state.on})`);
      } else {
        console.log('- State.on: Not found');
      }
      
      // Test power toggle
      console.log('\nTesting power toggle:');
      
      // Get current power state
      const currentPower = deviceDetails.power;
      console.log(`- Current power state: ${currentPower ? 'ON' : 'OFF'}`);
      
      // Toggle power
      console.log(`- Toggling power to: ${!currentPower ? 'ON' : 'OFF'}...`);
      await dreoAPI.setPower(deviceId, !currentPower);
      
      // Wait for the change to take effect
      console.log('- Waiting for 2 seconds...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Get updated status
      console.log('- Fetching updated status...');
      const updatedDetails = await dreoAPI.getDeviceDetails(deviceId);
      
      // Check updated power status
      if (updatedDetails.power !== undefined) {
        console.log(`- Updated power status: ${updatedDetails.power} (${typeof updatedDetails.power})`);
        console.log(`- Interpreted as: ${updatedDetails.power ? 'ON' : 'OFF'}`);
        
        // Verify that power status changed
        if (updatedDetails.power === !currentPower) {
          console.log('✅ Power status changed successfully');
        } else {
          console.log('❌ Power status did not change as expected');
        }
        
        // Verify that power status is a boolean
        if (typeof updatedDetails.power === 'boolean') {
          console.log('✅ Updated power status is correctly represented as a boolean value');
        } else {
          console.log('❌ Updated power status is not a boolean value');
        }
      } else {
        console.log('❌ Updated power status not found');
      }
      
      // Restore original power state
      console.log(`- Restoring original power state: ${currentPower ? 'ON' : 'OFF'}...`);
      await dreoAPI.setPower(deviceId, currentPower);
      
      // Wait for the change to take effect
      console.log('- Waiting for 2 seconds...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Get final status
      console.log('- Fetching final status...');
      const finalDetails = await dreoAPI.getDeviceDetails(deviceId);
      
      // Check final power status
      if (finalDetails.power !== undefined) {
        console.log(`- Final power status: ${finalDetails.power} (${typeof finalDetails.power})`);
        console.log(`- Interpreted as: ${finalDetails.power ? 'ON' : 'OFF'}`);
        
        // Verify that power status was restored
        if (finalDetails.power === currentPower) {
          console.log('✅ Power status restored successfully');
        } else {
          console.log('❌ Power status was not restored as expected');
        }
      } else {
        console.log('❌ Final power status not found');
      }
    }
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing Dreo power status fix:', error);
  }
}

// Run the test
testDreoPowerStatusFix();