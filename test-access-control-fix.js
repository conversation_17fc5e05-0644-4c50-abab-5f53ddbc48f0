/**
 * Test script to verify the fix for access control integration detection
 * 
 * This script tests:
 * 1. The initialization of Lenel S2 NetBox API
 * 2. The initialization of UniFi Access API
 * 3. The integration status endpoint
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const axios = require('axios');
const integrationTracker = require('./server/utils/integrationTracker');
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');
const UnifiAccessAPI = require('./server/integrations/unifiAccess/unifiAccessAPI');

// Test Lenel S2 NetBox API initialization
async function testLenelS2NetBoxInitialization() {
  console.log('\n--- Testing Lenel S2 NetBox API Initialization ---');
  
  try {
    // Check if environment variables are set
    const lenelHost = process.env.LENEL_S2_NETBOX_HOST;
    const lenelUsername = process.env.LENEL_S2_NETBOX_USERNAME;
    const lenelPassword = process.env.LENEL_S2_NETBOX_PASSWORD;
    const lenelPort = process.env.LENEL_S2_NETBOX_PORT || 443;
    
    console.log('Lenel S2 NetBox environment variables:');
    console.log(`  Host: ${lenelHost ? 'Set' : 'Not set'}`);
    console.log(`  Username: ${lenelUsername ? 'Set' : 'Not set'}`);
    console.log(`  Password: ${lenelPassword ? 'Set' : 'Not set'}`);
    console.log(`  Port: ${lenelPort}`);
    
    if (lenelHost && lenelUsername && lenelPassword) {
      console.log('Creating and initializing Lenel S2 NetBox API...');
      
      // Create and initialize the API
      const lenelS2NetBoxAPI = new LenelS2NetBoxAPI(lenelHost, lenelUsername, lenelPassword, lenelPort);
      await lenelS2NetBoxAPI.initialize();
      
      // Check integration status
      const status = integrationTracker.getStatus('Lenel S2 NetBox');
      console.log('Lenel S2 NetBox integration status:', status);
      
      if (status && status.status === 'active') {
        console.log('✅ Lenel S2 NetBox API initialized successfully');
        return true;
      } else {
        console.log('❌ Lenel S2 NetBox API initialization failed');
        return false;
      }
    } else {
      console.log('❌ Lenel S2 NetBox environment variables are not set');
      return false;
    }
  } catch (error) {
    console.error('Error testing Lenel S2 NetBox API initialization:', error);
    return false;
  }
}

// Test UniFi Access API initialization
async function testUnifiAccessInitialization() {
  console.log('\n--- Testing UniFi Access API Initialization ---');
  
  try {
    // Check if environment variables are set
    const unifiAccessHost = process.env.UNIFI_ACCESS_HOST;
    const unifiAccessApiKey = process.env.UNIFI_ACCESS_API_KEY;
    const unifiAccessPort = process.env.UNIFI_ACCESS_PORT || 443;
    
    console.log('UniFi Access environment variables:');
    console.log(`  Host: ${unifiAccessHost ? 'Set' : 'Not set'}`);
    console.log(`  API Key: ${unifiAccessApiKey ? 'Set' : 'Not set'}`);
    console.log(`  Port: ${unifiAccessPort}`);
    
    if (unifiAccessHost && unifiAccessApiKey) {
      console.log('Creating and initializing UniFi Access API...');
      
      // Create and initialize the API
      const unifiAccessAPI = new UnifiAccessAPI();
      await unifiAccessAPI.initialize();
      
      // Check integration status
      const status = integrationTracker.getStatus('UniFi Access');
      console.log('UniFi Access integration status:', status);
      
      if (status && status.status === 'active') {
        console.log('✅ UniFi Access API initialized successfully');
        return true;
      } else {
        console.log('❌ UniFi Access API initialization failed');
        return false;
      }
    } else {
      console.log('❌ UniFi Access environment variables are not set');
      return false;
    }
  } catch (error) {
    console.error('Error testing UniFi Access API initialization:', error);
    return false;
  }
}

// Test integration status endpoint
async function testIntegrationStatusEndpoint() {
  console.log('\n--- Testing Integration Status Endpoint ---');
  
  try {
    // Make a request to the integration status endpoint
    console.log('Making request to /api/integration-status endpoint...');
    console.log('Note: This test requires the server to be running.');
    
    try {
      const response = await axios.get('http://localhost:6000/api/integration-status');
      console.log('Integration status endpoint response:', response.data);
      
      // Check if Lenel S2 NetBox and UniFi Access are included in the response
      const hasLenelS2NetBox = response.data.hasOwnProperty('Lenel S2 NetBox');
      const hasUnifiAccess = response.data.hasOwnProperty('UniFi Access');
      
      console.log(`Lenel S2 NetBox included: ${hasLenelS2NetBox ? '✅' : '❌'}`);
      console.log(`UniFi Access included: ${hasUnifiAccess ? '✅' : '❌'}`);
      
      // Check if they are active
      const lenelS2NetBoxActive = hasLenelS2NetBox && response.data['Lenel S2 NetBox'].status === 'active';
      const unifiAccessActive = hasUnifiAccess && response.data['UniFi Access'].status === 'active';
      
      console.log(`Lenel S2 NetBox active: ${lenelS2NetBoxActive ? '✅' : '❌'}`);
      console.log(`UniFi Access active: ${unifiAccessActive ? '✅' : '❌'}`);
      
      return hasLenelS2NetBox && hasUnifiAccess && lenelS2NetBoxActive && unifiAccessActive;
    } catch (error) {
      console.error('Error making request to integration status endpoint:', error.message);
      console.log('Note: This test requires the server to be running and a valid session cookie.');
      return false;
    }
  } catch (error) {
    console.error('Error testing integration status endpoint:', error);
    return false;
  }
}

// Main function
async function main() {
  console.log('=== Access Control Integration Fix Test ===');
  
  try {
    // Run tests
    const lenelResult = await testLenelS2NetBoxInitialization();
    const unifiResult = await testUnifiAccessInitialization();
    const endpointResult = await testIntegrationStatusEndpoint();
    
    // Print summary
    console.log('\n=== Test Summary ===');
    console.log(`Lenel S2 NetBox API Initialization: ${lenelResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`UniFi Access API Initialization: ${unifiResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Integration Status Endpoint: ${endpointResult ? '✅ PASS' : '❌ FAIL'}`);
    
    // Overall result
    const overallResult = lenelResult && unifiResult && endpointResult;
    console.log(`\nOverall Result: ${overallResult ? '✅ PASS' : '❌ FAIL'}`);
    
    process.exit(overallResult ? 0 : 1);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the main function
main();