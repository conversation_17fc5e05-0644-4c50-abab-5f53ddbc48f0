# CSF Portal – Development Guidelines (Project‑specific)

These notes capture project‑specific details to accelerate work on this codebase. They assume you are an experienced Node/React developer and focus on the particulars of this repository.

## Assistant Runtime Preferences

- Default assistant mode: Coding
- Effective date: 2025-08-20
- Rationale: Per repository issue, <PERSON><PERSON> should default to Coding mode for interactions tied to this repo.


## 1) Build and Configuration

### Runtime, tooling, and OS notes
- Node version: engines require Node >= 20.18.1. Use the exact 20.18.x line where possible.
- If building outside Docker on Linux, node‑canvas requires native libs (same set installed in the Dockerfile):
  - Debian/Ubuntu: `apt-get install -y libcairo2 libpango-1.0-0 libpangocairo-1.0-0 libjpeg62-turbo libgif7 librsvg2-2`
  - macOS (Homebrew): `brew install pkg-config cairo pango libpng jpeg giflib librsvg`

### Monorepo layout
- Root is an Express/Mongoose server (CommonJS). Client is CRA under `client/` (React 18 + MUI), launched via react-app-rewired with a custom `config-overrides.js` that:
  - Removes CRA’s ModuleScopePlugin.
  - <PERSON>ases `react`, `react-dom`, and JSX runtimes to the client’s node_modules to avoid multiple React instances.

### Dev workflow (local, without Docker)
1. Install root dependencies: `npm install`
2. Install client dependencies: `npm run client-install`
3. Environment:
   - Server listens on `PORT` or defaults to `6000` in dev.
   - Client dev server proxies API to `http://localhost:6000` (see client/package.json `proxy`).
   - Minimal `.env` for local dev:
     - `SESSION_SECRET` (any strong string)
     - Optional `MONGO_URI` (defaults to `mongodb://localhost:27017/csfportal`)
     - Consider setting `ENABLE_DEBUG_LOGGING=true` to activate HTTP debug interceptors via WebSocket.
   - Start a local MongoDB if you don’t set a remote `MONGO_URI`.
4. Run in watch mode: `npm run dev`
   - This runs `nodemon server.js` and `react-app-rewired start` concurrently.

### Production build
- Root script `npm run build` will install client deps (with dev deps) and produce a CRA build under `client/build/`.
- Server serves static assets in production (`NODE_ENV=production`): requests fallback to `client/build/index.html`.

### Docker
- The Dockerfile pins `node:20.18.1-bookworm-slim` and installs node-canvas runtime deps, then builds the client.
- Default runtime env inside the image: `PORT=8080` (mapped by compose to host 2001).
- Quick start:
  - Build: `docker build -t csfportal .`
  - Compose: `docker compose up -d` (compose uses `docker-compose.yml` which references an `.env.production` file — not the root `.env`).
  - Exposed: HTTP 8080 (mapped to host 2001), RADIUS UDP 1812/1813.

### Services initialized by server.js (order & behavior)
- Mongo connects first. After connect:
  - Default roles are created via `models/Role.createDefaultRoles()`.
  - Several integrations initialize in `try/catch` blocks (RADIUS server, Gmail webhook, schedulers, etc.). Failures are logged but non-fatal for local/dev.
- WebSockets: initialized on the same HTTP server; WS URL is `ws://<host>:<PORT>/ws`. Realtime service starts automatically.
- Security headers via `helmet`, with CSP adjustments for Google and allowed image sources.


## 2) Testing

### Framework and configuration
- Jest 29 is configured at root; see `jest.config.js`:
  - `testEnvironment: 'node'`
  - `roots: ['<rootDir>/tests']`
  - `testMatch: ['**/__tests__/**/*.js?(x)', '**/?(*.)+(spec|test).js?(x)']`
  - Coverage dir: `coverage/`, provider: `v8`
- Only tests under `tests/` are discovered by default. Many legacy ad‑hoc scripts named `test-*.js` at repo root are not part of Jest; run those directly with `node` if needed.

### Running tests
- All tests: `npm test`
- Watch: `npm run test:watch`
- Single file: `npm test -- tests/path/to/file.test.js`
- Coverage: `npm test -- --coverage`
- If you experience resource pressure or binary downloads (mongodb-memory-server), consider serial execution: `jest --runInBand`.

### Adding tests (project patterns)
- Place new Jest tests under `tests/` with `*.test.js` names. Organize by area, e.g. `tests/routes/...` or `tests/websocket/...`.
- API route tests should NOT boot the entire server. Instead, attach the route to a fresh `express()` instance and use `supertest` (see `tests/routes/api/users.test.js`). Example:

```js
const request = require('supertest');
const express = require('express');

// Optional: mock auth middleware and models to decouple from DB and session
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => next(),
  hasRoles: () => (req, res, next) => next()
}));

const app = express();
app.use(express.json());
app.use('/api/users', require('../../../routes/api/users'));

test('GET /api/users returns 200', async () => {
  const res = await request(app).get('/api/users');
  expect(res.statusCode).toBe(200);
});
```

- Database-heavy tests: prefer `mongodb-memory-server`. Typical pattern:

```js
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');

let mongo;

beforeAll(async () => {
  mongo = await MongoMemoryServer.create();
  await mongoose.connect(mongo.getUri());
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongo.stop();
});
```

- WebSocket tests: see `tests/websocket/*` for patterns. Avoid importing `server.js` (which starts subsystems). Isolate services and mock timers/sockets where practical.

### Demo test (validated during preparation)
We verified a trivial test works under the current Jest setup. To reproduce locally, create `tests/demo/junie-demo.test.js`:

```js
/** Self-contained demo test */
describe('junie demo', () => {
  it('adds numbers correctly', () => {
    const add = (a, b) => a + b;
    expect(add(2, 3)).toBe(5);
  });

  it('handles async/await without additional setup', async () => {
    const value = await Promise.resolve('ok');
    expect(value).toBe('ok');
  });
});
```

Run it:

```
npm test -- tests/demo/junie-demo.test.js
```

This test does not require any environment configuration and runs fast. After experimenting, you can remove the demo file.

### Notes on mongodb-memory-server
- It downloads a MongoDB binary on first use. Behind proxies/air‑gapped CI this can fail. Options:
  - Pre-cache binaries on CI workers.
  - Use `--runInBand` to reduce parallel processes.
  - If you need to customize download URL/version, see mongodb-memory-server docs (env vars like `MONGOMS_DOWNLOAD_URL`, etc.).


## 3) Additional Development Information

### Code style and module system
- Server is CommonJS; prefer `module.exports`/`require` rather than ESM.
- Prefer async/await; keep route handlers thin and push logic into services.
- Follow existing patterns for mocking in tests (see users route test) and for feature organization under `server/` and `routes/`.

### Debugging and observability
- Set `ENABLE_DEBUG_LOGGING=true` in dev to enable Axios interceptors that emit logs over WebSocket.
- WebSocket server runs on `ws://localhost:<PORT>/ws`. The realtime service is started by default; use SIGINT/SIGTERM for graceful shutdown.

### Ports and security headers
- Dev default server port: `6000`. Client dev runs on CRA default (usually `3000`) and proxies to `6000`.
- Production/Docker default: `8080`.
- `helmet` CSP is customized to allow Google Drive/Forms embeds and certain image hosts.

### Data initialization
- On successful Mongo connect, default roles are auto-created. Use a local MongoDB for experimentation to avoid polluting shared databases.

### Docker and Windows/WSL
- Prefer Docker for consistent builds (node-canvas deps handled for you). See `DOCKER_WINDOWS_DEPLOYMENT.md` and `WSL_DEPLOYMENT_GUIDE.md` for platform-specific notes.

### Pitfalls
- Don’t import `server.js` from tests; spin up isolated Express apps or mock layers.
- Many legacy `test-*.js` scripts at the repo root are not part of Jest. Run them via `node <script>` if needed or migrate them into `tests/`.
- Do not commit `.env` or secrets. Compose uses `.env.production`; the local dev `.env` is separate.


---
Maintainer notes
- Node 20.18.x is strongly preferred to avoid ABI/runtime edge cases.
- If you update CRA or react-app-rewired, revisit `client/config-overrides.js` to ensure React aliasing and ModuleScope removal still work.

### Development notes
- When adding new features, implement the actual changes instead of just mocking them or planning them endlessly. Unless specified to do further planning, you should work towards building the actual feature with code.