/**
 * Test script to verify that <PERSON><PERSON><PERSON> and Constant Contact menu items are properly initialized
 * 
 * This script:
 * 1. Fetches all menu items
 * 2. Checks if Go<PERSON>e and Constant Contact menu items exist
 * 3. Reinitializes menu items if needed
 * 4. Verifies the menu items after reinitialization
 */

require('dotenv').config();
const axios = require('axios');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:6000';
const EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
const PASSWORD = process.env.TEST_USER_PASSWORD || 'password';

// Main test function
async function testNewIntegrationMenuItems() {
  try {
    console.log('Testing Govee and Constant Contact menu items...');
    
    // Step 1: Login to get authentication token
    console.log('\n1. Logging in...');
    const loginResponse = await axios.post(`${API_URL}/api/auth/login`, {
      email: EMAIL,
      password: PASSWORD
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    
    // Configure axios with authentication header
    const authAxios = axios.create({
      headers: {
        'x-auth-token': token
      }
    });
    
    // Step 2: Fetch all menu items
    console.log('\n2. Fetching all menu items...');
    const menuItemsResponse = await authAxios.get(`${API_URL}/api/menu-items`);
    const menuItems = menuItemsResponse.data;
    console.log(`✅ Retrieved ${menuItems.length} menu items`);
    
    // Step 3: Check if Govee and Constant Contact menu items exist
    console.log('\n3. Checking for Govee and Constant Contact menu items...');
    
    const goveeMenuItem = menuItems.find(item => item.path === '/govee');
    const constantContactMenuItem = menuItems.find(item => item.path === '/constant-contact');
    
    if (goveeMenuItem) {
      console.log('✅ Govee menu item exists:');
      console.log(`   - Title: ${goveeMenuItem.title}`);
      console.log(`   - Path: ${goveeMenuItem.path}`);
      console.log(`   - Icon: ${goveeMenuItem.icon}`);
      console.log(`   - Required Permission: ${goveeMenuItem.requiredPermission}`);
    } else {
      console.log('❌ Govee menu item not found');
    }
    
    if (constantContactMenuItem) {
      console.log('\n✅ Constant Contact menu item exists:');
      console.log(`   - Title: ${constantContactMenuItem.title}`);
      console.log(`   - Path: ${constantContactMenuItem.path}`);
      console.log(`   - Icon: ${constantContactMenuItem.icon}`);
      console.log(`   - Required Permission: ${constantContactMenuItem.requiredPermission}`);
    } else {
      console.log('\n❌ Constant Contact menu item not found');
    }
    
    // Step 4: If menu items don't exist, reinitialize them
    if (!goveeMenuItem || !constantContactMenuItem) {
      console.log('\n4. Reinitializing menu items...');
      
      const initResponse = await authAxios.post(`${API_URL}/api/menu-items/init`);
      console.log(`✅ Reinitialized ${initResponse.data.length} menu items`);
      
      // Step 5: Verify menu items after reinitialization
      console.log('\n5. Verifying menu items after reinitialization...');
      
      const updatedMenuItemsResponse = await authAxios.get(`${API_URL}/api/menu-items`);
      const updatedMenuItems = updatedMenuItemsResponse.data;
      
      const updatedGoveeMenuItem = updatedMenuItems.find(item => item.path === '/govee');
      const updatedConstantContactMenuItem = updatedMenuItems.find(item => item.path === '/constant-contact');
      
      if (updatedGoveeMenuItem) {
        console.log('✅ Govee menu item now exists:');
        console.log(`   - Title: ${updatedGoveeMenuItem.title}`);
        console.log(`   - Path: ${updatedGoveeMenuItem.path}`);
        console.log(`   - Icon: ${updatedGoveeMenuItem.icon}`);
        console.log(`   - Required Permission: ${updatedGoveeMenuItem.requiredPermission}`);
      } else {
        console.log('❌ Govee menu item still not found after reinitialization');
      }
      
      if (updatedConstantContactMenuItem) {
        console.log('\n✅ Constant Contact menu item now exists:');
        console.log(`   - Title: ${updatedConstantContactMenuItem.title}`);
        console.log(`   - Path: ${updatedConstantContactMenuItem.path}`);
        console.log(`   - Icon: ${updatedConstantContactMenuItem.icon}`);
        console.log(`   - Required Permission: ${updatedConstantContactMenuItem.requiredPermission}`);
      } else {
        console.log('\n❌ Constant Contact menu item still not found after reinitialization');
      }
    }
    
    // Step 6: List all integration menu items
    console.log('\n6. Listing all integration menu items:');
    const integrationMenuItems = menuItems.filter(item => item.type === 'integration');
    
    console.log(`Found ${integrationMenuItems.length} integration menu items:`);
    integrationMenuItems.forEach((item, index) => {
      console.log(`${index + 1}. ${item.title} (${item.path})`);
    });
    
    console.log('\n✅ Test completed successfully');
    
  } catch (error) {
    console.error('\n❌ Error during test:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

// Run the test
testNewIntegrationMenuItems();