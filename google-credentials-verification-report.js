#!/usr/bin/env node

/**
 * Comprehensive Google Service Account Credentials Verification Report
 */

require('dotenv').config();
const { google } = require('googleapis');
const jwt = require('jsonwebtoken');

async function generateVerificationReport() {
  console.log('📊 Google Service Account Credentials Verification Report');
  console.log('=' * 60);
  console.log();

  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
  const domain = process.env.ALLOWED_DOMAINS;

  // Basic configuration check
  console.log('🔧 CONFIGURATION STATUS');
  console.log('-' * 30);
  console.log(`Service Account Email: ${serviceAccountEmail ? '✅ Set' : '❌ Missing'}`);
  console.log(`Private Key: ${serviceAccountPrivateKey ? '✅ Set' : '❌ Missing'}`);
  console.log(`Domain: ${domain ? '✅ Set (' + domain + ')' : '❌ Missing'}`);
  console.log(`Key Format: ${serviceAccountPrivateKey && serviceAccountPrivateKey.includes('-----BEGIN PRIVATE KEY-----') ? '✅ Valid' : '❌ Invalid'}`);
  console.log(`Key Length: ${serviceAccountPrivateKey ? serviceAccountPrivateKey.length + ' chars' : 'N/A'}`);
  console.log();

  if (!serviceAccountEmail || !serviceAccountPrivateKey) {
    console.error('❌ CRITICAL: Missing service account credentials');
    process.exit(1);
  }

  try {
    // Extract information from the private key
    console.log('🔍 PRIVATE KEY ANALYSIS');
    console.log('-' * 30);
    
    // Try to decode the key to get more info
    try {
      // The private key might contain embedded information
      const keyContent = serviceAccountPrivateKey
        .replace('-----BEGIN PRIVATE KEY-----', '')
        .replace('-----END PRIVATE KEY-----', '')
        .replace(/\s/g, '');
      
      console.log(`Key Content Length: ${keyContent.length} characters`);
      console.log(`Key Type: RSA Private Key (PKCS#8 format)`);
    } catch (keyError) {
      console.log('Could not analyze private key details');
    }
    console.log();

    // Test service account authentication
    console.log('🔐 AUTHENTICATION TESTS');
    console.log('-' * 30);

    // Test 1: Basic JWT creation
    console.log('Test 1: JWT Client Creation...');
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });
    console.log('   ✅ JWT client created successfully');

    // Test 2: Token generation
    console.log('Test 2: Access Token Generation...');
    try {
      const accessToken = await auth.getAccessToken();
      console.log('   ✅ Access token generated successfully');
      console.log(`   Token Preview: ${accessToken.token.substring(0, 30)}...`);
      console.log(`   Token Type: ${accessToken.token.startsWith('ya29.') ? 'OAuth 2.0' : 'Unknown'}`);
    } catch (tokenError) {
      console.log(`   ❌ Token generation failed: ${tokenError.message}`);
      throw tokenError;
    }

    // Test 3: Impersonation test
    console.log('Test 3: User Impersonation Test...');
    const impersonationAuth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/drive.readonly'],
      subject: `bpritchett@${domain}`
    });

    try {
      const impersonationToken = await impersonationAuth.getAccessToken();
      console.log('   ✅ User impersonation successful');
      console.log('   🎉 Domain-wide delegation is properly configured!');
    } catch (impersonationError) {
      console.log(`   ❌ User impersonation failed: ${impersonationError.message}`);
      console.log('   ⚠️  Domain-wide delegation needs to be configured');
    }
    console.log();

    // Project information
    console.log('🏗️  PROJECT INFORMATION');
    console.log('-' * 30);
    const projectMatch = serviceAccountEmail.match(/@(.+)\.iam\.gserviceaccount\.com$/);
    if (projectMatch) {
      const projectId = projectMatch[1];
      console.log(`Project ID: ${projectId}`);
      console.log(`Service Account: ${serviceAccountEmail}`);
      console.log(`Full Resource Name: projects/${projectId}/serviceAccounts/${serviceAccountEmail}`);
    }
    console.log();

    // Required scopes
    console.log('📋 REQUIRED OAUTH SCOPES FOR DOMAIN-WIDE DELEGATION');
    console.log('-' * 50);
    const requiredScopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.metadata',
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.user.security',
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar.settings.readonly',
      'https://www.googleapis.com/auth/forms.body',
      'https://www.googleapis.com/auth/forms.responses.readonly'
    ];

    requiredScopes.forEach((scope, index) => {
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${scope}`);
    });
    console.log();
    console.log('Copy this for domain-wide delegation setup:');
    console.log(requiredScopes.join(','));
    console.log();

    // Setup instructions
    console.log('⚙️  DOMAIN-WIDE DELEGATION SETUP INSTRUCTIONS');
    console.log('-' * 45);
    console.log('1. Go to Google Cloud Console (https://console.cloud.google.com/)');
    console.log('2. Navigate to IAM & Admin → Service Accounts');
    console.log(`3. Find service account: ${serviceAccountEmail}`);
    console.log('4. Copy the "Unique ID" or "Client ID" (numeric value)');
    console.log('5. Go to Google Admin Console (https://admin.google.com/)');
    console.log('6. Navigate to Security → Access and data control → API controls');
    console.log('7. Click "Manage Domain Wide Delegation"');
    console.log('8. Click "Add new" and enter:');
    console.log('   - Client ID: [from step 4]');
    console.log('   - OAuth scopes: [from the list above]');
    console.log('9. Save the configuration');
    console.log('10. Test again with: node test-google-service-auth-simple.js');
    console.log();

    console.log('📈 SUMMARY');
    console.log('-' * 30);
    console.log('✅ Service account credentials are valid');
    console.log('✅ Private key format is correct');
    console.log('✅ Basic authentication works');
    console.log('❌ Domain-wide delegation needs configuration');
    console.log();
    console.log('Once domain-wide delegation is configured, all Google integrations will work!');

  } catch (error) {
    console.error(`\n💥 Verification failed: ${error.message}`);
    
    if (error.message.includes('invalid_grant')) {
      console.log('\n🔧 This is expected - it means domain-wide delegation is not configured yet.');
    }
    
    process.exit(1);
  }
}

generateVerificationReport();