/**
 * Test script to verify Google authentication impersonation changes
 * 
 * This script tests:
 * 1. Using a Google-authenticated user's email for impersonation
 * 2. Error handling for local user accounts
 */

require('dotenv').config();
const GoogleDriveAPI = require('./server/integrations/googleDrive/googleDriveAPI');
const GoogleAdminAPI = require('./server/integrations/googleAdmin/googleAdminAPI');
const GoogleFormsAPI = require('./server/integrations/googleForms/googleFormsAPI');

// Mock user objects
const googleUser = {
  id: 'google-user-id',
  name: 'Google User',
  email: '<EMAIL>',
  authType: 'google'
};

const localUser = {
  id: 'local-user-id',
  name: 'Local User',
  email: '<EMAIL>',
  authType: 'local'
};

// Mock request objects
const googleUserRequest = {
  user: googleUser
};

const localUserRequest = {
  user: localUser
};

// Test Google Drive API with Google user
async function testGoogleDriveWithGoogleUser() {
  console.log('\n--- Testing Google Drive API with Google-authenticated user ---');
  
  try {
    // Create a new API instance with the Google user's email
    const api = new GoogleDriveAPI(
      process.env.GOOGLE_DRIVE_CLIENT_ID || '',
      process.env.GOOGLE_DRIVE_CLIENT_SECRET || '',
      process.env.GOOGLE_DRIVE_REDIRECT_URI || '',
      './google-drive-token.json',
      null, // No user tokens
      googleUser.id,
      googleUser.email // Use Google user's email for impersonation
    );
    
    await api.initialize();
    console.log('✅ Successfully initialized Google Drive API with Google user email:', googleUser.email);
    
    // Test a simple API call
    try {
      const files = await api.listFiles({ pageSize: 5 });
      console.log(`✅ Successfully listed ${files.length} files using Google user's email for impersonation`);
    } catch (error) {
      console.error('❌ Error listing files:', error.message);
    }
  } catch (error) {
    console.error('❌ Error initializing Google Drive API with Google user:', error.message);
  }
}

// Test Google Drive API with local user
async function testGoogleDriveWithLocalUser() {
  console.log('\n--- Testing Google Drive API with local user ---');
  
  try {
    // Simulate the getApiInstance function's behavior
    if (localUser.authType !== 'google') {
      throw new Error('You are not logged in using Google authentication. Google Drive integration will not work. Please log in with your Google account.');
    }
    
    // This code should not execute due to the error above
    const api = new GoogleDriveAPI(
      process.env.GOOGLE_DRIVE_CLIENT_ID || '',
      process.env.GOOGLE_DRIVE_CLIENT_SECRET || '',
      process.env.GOOGLE_DRIVE_REDIRECT_URI || '',
      './google-drive-token.json',
      null, // No user tokens
      localUser.id,
      localUser.email // Use local user's email for impersonation
    );
    
    await api.initialize();
    console.log('✅ Successfully initialized Google Drive API with local user email:', localUser.email);
  } catch (error) {
    console.log('✅ Correctly caught error for local user:', error.message);
  }
}

// Test Google Admin API with Google user
async function testGoogleAdminWithGoogleUser() {
  console.log('\n--- Testing Google Admin API with Google-authenticated user ---');
  
  try {
    // Create a new API instance with the Google user's email
    const api = new GoogleAdminAPI(
      process.env.GOOGLE_ADMIN_CLIENT_ID || '',
      process.env.GOOGLE_ADMIN_CLIENT_SECRET || '',
      process.env.GOOGLE_ADMIN_REDIRECT_URI || '',
      './google-admin-token.json',
      null, // No user tokens
      googleUser.id,
      googleUser.email // Use Google user's email for impersonation
    );
    
    await api.initialize();
    console.log('✅ Successfully initialized Google Admin API with Google user email:', googleUser.email);
    
    // Test a simple API call
    try {
      const users = await api.listUsers({ maxResults: 5 });
      console.log(`✅ Successfully listed users using Google user's email for impersonation`);
    } catch (error) {
      console.error('❌ Error listing users:', error.message);
    }
  } catch (error) {
    console.error('❌ Error initializing Google Admin API with Google user:', error.message);
  }
}

// Test Google Admin API with local user
async function testGoogleAdminWithLocalUser() {
  console.log('\n--- Testing Google Admin API with local user ---');
  
  try {
    // Simulate the getApiInstance function's behavior
    if (localUser.authType !== 'google') {
      throw new Error('You are not logged in using Google authentication. Google Admin integration will not work. Please log in with your Google account.');
    }
    
    // This code should not execute due to the error above
    const api = new GoogleAdminAPI(
      process.env.GOOGLE_ADMIN_CLIENT_ID || '',
      process.env.GOOGLE_ADMIN_CLIENT_SECRET || '',
      process.env.GOOGLE_ADMIN_REDIRECT_URI || '',
      './google-admin-token.json',
      null, // No user tokens
      localUser.id,
      localUser.email // Use local user's email for impersonation
    );
    
    await api.initialize();
    console.log('✅ Successfully initialized Google Admin API with local user email:', localUser.email);
  } catch (error) {
    console.log('✅ Correctly caught error for local user:', error.message);
  }
}

// Test Google Forms API with Google user
async function testGoogleFormsWithGoogleUser() {
  console.log('\n--- Testing Google Forms API with Google-authenticated user ---');
  
  try {
    // Create a new API instance with the Google user's email
    const api = new GoogleFormsAPI(
      process.env.GOOGLE_FORMS_CLIENT_ID || '',
      process.env.GOOGLE_FORMS_CLIENT_SECRET || '',
      process.env.GOOGLE_FORMS_REDIRECT_URI || '',
      './google-forms-token.json',
      null, // No user tokens
      googleUser.id,
      googleUser.email // Use Google user's email for impersonation
    );
    
    await api.initialize();
    console.log('✅ Successfully initialized Google Forms API with Google user email:', googleUser.email);
  } catch (error) {
    console.error('❌ Error initializing Google Forms API with Google user:', error.message);
  }
}

// Test Google Forms API with local user
async function testGoogleFormsWithLocalUser() {
  console.log('\n--- Testing Google Forms API with local user ---');
  
  try {
    // Simulate the getApiInstance function's behavior
    if (localUser.authType !== 'google') {
      throw new Error('You are not logged in using Google authentication. Google Forms integration will not work. Please log in with your Google account.');
    }
    
    // This code should not execute due to the error above
    const api = new GoogleFormsAPI(
      process.env.GOOGLE_FORMS_CLIENT_ID || '',
      process.env.GOOGLE_FORMS_CLIENT_SECRET || '',
      process.env.GOOGLE_FORMS_REDIRECT_URI || '',
      './google-forms-token.json',
      null, // No user tokens
      localUser.id,
      localUser.email // Use local user's email for impersonation
    );
    
    await api.initialize();
    console.log('✅ Successfully initialized Google Forms API with local user email:', localUser.email);
  } catch (error) {
    console.log('✅ Correctly caught error for local user:', error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('=== TESTING GOOGLE AUTHENTICATION IMPERSONATION ===');
  
  // Test Google Drive API
  await testGoogleDriveWithGoogleUser();
  await testGoogleDriveWithLocalUser();
  
  // Test Google Admin API
  await testGoogleAdminWithGoogleUser();
  await testGoogleAdminWithLocalUser();
  
  // Test Google Forms API
  await testGoogleFormsWithGoogleUser();
  await testGoogleFormsWithLocalUser();
  
  console.log('\n=== TESTS COMPLETED ===');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});