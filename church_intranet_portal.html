<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grace Community Church - Staff Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .logo {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo-image {
            width: 72px;
            height: 72px;
            margin-bottom: 1rem;
            object-fit: contain;
            display: block;
        }

        .logo h1 {
            color: #4a5568;
            font-size: 1.1rem;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 0.5rem;
        }

        .logo p {
            color: #718096;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }

        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.5rem 1rem;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
            fill: currentColor;
        }

        .dropdown {
            margin-left: 2rem;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .dropdown.active {
            max-height: 200px;
        }

        .dropdown .nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            opacity: 0.8;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            flex: 1;
            padding: 2rem;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h2 {
            color: #2d3748;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            color: #718096;
            font-size: 1.1rem;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .widget {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2563eb, #1e40af);
        }

        .widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .widget-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }

        .widget-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
            display: flex;
            align-items: center;
        }

        .widget-title svg {
            width: 24px;
            height: 24px;
            margin-right: 0.75rem;
            fill: #2563eb;
        }

        .widget-menu {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            color: #718096;
            transition: all 0.3s ease;
        }

        .widget-menu:hover {
            background: #f7fafc;
            color: #2563eb;
        }

        /* Calendar Widget */
        .calendar-widget {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .calendar-day:hover {
            background: #e2e8f0;
        }

        .calendar-day.today {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
        }

        .calendar-day.event {
            background: #dbeafe;
            color: #1e40af;
        }

        /* Files Widget */
        .file-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-item:hover {
            background: #f7fafc;
            transform: translateX(5px);
        }

        .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-weight: 600;
        }

        .file-icon.doc { background: linear-gradient(135deg, #2563eb, #1e40af); }
        .file-icon.pdf { background: linear-gradient(135deg, #dc2626, #b91c1c); }
        .file-icon.sheet { background: linear-gradient(135deg, #16a34a, #15803d); }

        .file-info h4 {
            color: #2d3748;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .file-info p {
            color: #718096;
            font-size: 0.825rem;
        }

        /* Music Player Widget */
        .music-player {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .music-info {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .music-info h4 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .music-info p {
            opacity: 0.8;
        }

        .music-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .music-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .music-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .music-btn.play {
            width: 56px;
            height: 56px;
            background: white;
            color: #2563eb;
        }

        /* Tasks Widget */
        .task-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #cbd5e0;
            border-radius: 4px;
            margin-right: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .task-checkbox.completed {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            border-color: #2563eb;
        }

        .task-text {
            flex: 1;
            color: #2d3748;
        }

        .task-text.completed {
            text-decoration: line-through;
            opacity: 0.6;
        }

        .task-priority {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 1rem;
        }

        .task-priority.high { background: #dc2626; }
        .task-priority.medium { background: #1e40af; }
        .task-priority.low { background: #16a34a; }

        /* News Widget */
        .news-item {
            padding: 1rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .news-item:last-child {
            border-bottom: none;
        }

        .news-item h4 {
            color: #2d3748;
            font-size: 0.95rem;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .news-meta {
            color: #718096;
            font-size: 0.8rem;
            display: flex;
            gap: 1rem;
        }

        /* Communications Widget */
        .comm-item {
            display: flex;
            align-items: flex-start;
            padding: 1rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .comm-item:last-child {
            border-bottom: none;
        }

        .comm-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2563eb, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .comm-content {
            flex: 1;
        }

        .comm-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .comm-name {
            font-weight: 600;
            color: #2d3748;
        }

        .comm-time {
            color: #718096;
            font-size: 0.8rem;
        }

        .comm-message {
            color: #4a5568;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Notes Widget */
        .notes-area {
            width: 100%;
            min-height: 120px;
            border: none;
            background: #f7fafc;
            border-radius: 12px;
            padding: 1rem;
            font-family: inherit;
            resize: vertical;
            transition: all 0.3s ease;
        }

        .notes-area:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 0 2px #2563eb;
        }

        /* Shortcuts Widget */
        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1.5rem 1rem;
            border-radius: 16px;
            background: #f7fafc;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: #4a5568;
        }

        .shortcut-item:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            transform: translateY(-3px);
        }

        .shortcut-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .shortcut-item:hover .shortcut-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .shortcut-item:hover .shortcut-icon svg {
            fill: white;
        }

        .shortcut-icon svg {
            width: 24px;
            height: 24px;
            fill: #2563eb;
        }

        .shortcut-name {
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
        }

        /* User Profile Area */
        .user-profile {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem;
            margin-top: auto;
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 1rem;
            position: relative;
        }

        .user-info:hover {
            background: #f7fafc;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2563eb, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 0.75rem;
            position: relative;
        }

        .user-status {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .user-status.online { background: #10b981; }
        .user-status.away { background: #f59e0b; }
        .user-status.busy { background: #ef4444; }
        .user-status.offline { background: #6b7280; }

        .user-details h4 {
            color: #2d3748;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-details p {
            color: #718096;
            font-size: 0.75rem;
        }

        .user-menu {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            padding: 0.5rem 0;
            margin-bottom: 0.5rem;
            z-index: 1000;
        }

        .user-menu.active {
            display: block;
        }

        .user-menu-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .user-menu-item:hover {
            background: #f7fafc;
        }

        .user-menu-item svg {
            width: 16px;
            height: 16px;
            margin-right: 0.75rem;
            fill: currentColor;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .quick-action-btn {
            background: #f7fafc;
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #4a5568;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .quick-action-btn:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            transform: translateY(-2px);
        }

        .quick-action-btn svg {
            width: 20px;
            height: 20px;
            margin-bottom: 0.5rem;
            fill: currentColor;
        }

        /* Quick Links */
        .quick-links {
            margin-bottom: 1rem;
        }

        .quick-links h5 {
            color: #4a5568;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
            padding: 0 0.5rem;
        }

        .quick-link {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 8px;
            color: #4a5568;
            text-decoration: none;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            margin-bottom: 0.25rem;
        }

        .quick-link:hover {
            background: #f7fafc;
            color: #2563eb;
            transform: translateX(3px);
        }

        .quick-link svg {
            width: 16px;
            height: 16px;
            margin-right: 0.75rem;
            fill: currentColor;
        }

        /* Staff Messages */
        .staff-messages {
            margin-bottom: 1rem;
        }

        .staff-messages h5 {
            color: #4a5568;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .new-message-btn {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
        }

        .message-thread {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.25rem;
            position: relative;
        }

        .message-thread:hover {
            background: #f7fafc;
        }

        .message-thread.unread {
            background: #dbeafe;
        }

        .message-thread.unread::before {
            content: '';
            position: absolute;
            left: 2px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #2563eb;
        }

        .message-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2563eb, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.7rem;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-name {
            font-size: 0.75rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }

        .message-preview {
            font-size: 0.7rem;
            color: #718096;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message-time {
            font-size: 0.65rem;
            color: #a0aec0;
            flex-shrink: 0;
            margin-left: 0.5rem;
        }

        /* Status Dropdown */
        .status-dropdown {
            display: none;
            position: absolute;
            bottom: 100%;
            left: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            padding: 0.5rem 0;
            margin-bottom: 0.5rem;
            z-index: 1000;
            min-width: 120px;
        }

        .status-dropdown.active {
            display: block;
        }

        .status-option {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .status-option:hover {
            background: #f7fafc;
        }

        .status-option .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        /* Floating Widgets */
        .floating-widgets {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .floating-widget {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            min-width: 250px;
            max-width: 300px;
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .floating-widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        .floating-widget h5 {
            color: #2d3748;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .floating-widget h5 svg {
            width: 20px;
            height: 20px;
            fill: #2563eb;
        }

        /* Widget Toggle Buttons */
        .widget-toggle {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.3);
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .widget-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(37, 99, 235, 0.4);
        }

        .widget-toggle svg {
            width: 24px;
            height: 24px;
            fill: white;
        }

        .widgets-container {
            display: none;
            position: fixed;
            bottom: 6rem;
            right: 2rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            z-index: 1000;
        }

        .widgets-container.active {
            display: flex;
        }

        /* Quick Actions Widget */
        .quick-actions-floating {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
        }

        .quick-action-floating {
            background: #f7fafc;
            border: none;
            border-radius: 12px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #4a5568;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .quick-action-floating:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            color: white;
            transform: translateY(-2px);
        }

        .quick-action-floating svg {
            width: 24px;
            height: 24px;
            margin-bottom: 0.5rem;
            fill: currentColor;
        }

        /* Messages Widget */
        .message-floating {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
            position: relative;
        }

        .message-floating:hover {
            background: #f7fafc;
        }

        .message-floating.unread {
            background: #dbeafe;
        }

        .message-floating.unread::before {
            content: '';
            position: absolute;
            left: 4px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #2563eb;
        }

        .message-avatar-floating {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2563eb, #1e40af);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        /* Quick Links Widget */
        .quick-link-floating {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border-radius: 12px;
            color: #4a5568;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin-bottom: 0.5rem;
        }

        .quick-link-floating:hover {
            background: #f7fafc;
            color: #2563eb;
            transform: translateX(5px);
        }

        .quick-link-floating svg {
            width: 18px;
            height: 18px;
            margin-right: 0.75rem;
            fill: currentColor;
        }

        /* User Profile Fixed */
        .user-profile-fixed {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 280px;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem;
            z-index: 999;
        }
        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6rem;
            font-weight: 600;
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="logo">
                <div class="logo-container">
                    <img src="https://www.ukcsf.org/wp-content/uploads/2017/07/Header3.png" alt="Christian Student Fellowship" class="logo-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="logo-fallback" style="display: none; width: 72px; height: 72px; background: linear-gradient(135deg, #2563eb, #1e40af); border-radius: 12px; align-items: center; justify-content: center; margin-bottom: 1rem; color: white; font-weight: 700; font-size: 24px;">CSF</div>
                </div>
                <h1>Christian Student Fellowship</h1>
                <p>Staff Portal</p>
            </div>
            
            <div class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showDashboardDropdown()">
                        <svg class="nav-icon" viewBox="0 0 24 24">
                            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                        </svg>
                        Dashboard
                    </a>
                    <div class="dropdown" id="dashboard-dropdown">
                        <a href="#" class="nav-link active" onclick="showMyDashboard()">My Dashboard</a>
                        <a href="#" class="nav-link" onclick="showAnalytics()">Analytics</a>
                        <a href="#" class="nav-link" onclick="showReports()">Reports</a>
                    </div>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showUserManagement()">
                        <svg class="nav-icon" viewBox="0 0 24 24">
                            <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        User Management
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="showBuildingManagement()">
                        <svg class="nav-icon" viewBox="0 0 24 24">
                            <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                        Building Management
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24">
                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Documents
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24">
                            <path d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m3 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m3 0h6l-1 9H4l-1-9h6"/>
                        </svg>
                        Events & Ministry
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="#" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24">
                            <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        Settings
                    </a>
                </div>
            </div>
            
            <!-- User Profile Fixed at Bottom -->
            <div class="user-profile-fixed">
                <div onclick="toggleUserMenu()" style="display: flex; align-items: center; padding: 0.75rem; border-radius: 12px; cursor: pointer; position: relative;" onmouseover="this.style.background='#f7fafc'" onmouseout="this.style.background='transparent'">
                    <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #2563eb, #1e40af); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-right: 0.75rem; position: relative;">
                        JD
                        <div style="position: absolute; bottom: 0; right: 0; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; background: #10b981;"></div>
                        <div style="position: absolute; top: -2px; right: -2px; background: #ef4444; color: white; border-radius: 50%; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center; font-size: 0.6rem; font-weight: 600;">3</div>
                    </div>
                    <div>
                        <h4 style="color: #2d3748; font-size: 0.9rem; font-weight: 600; margin: 0 0 0.25rem 0;">John Doe</h4>
                        <p style="color: #718096; font-size: 0.75rem; margin: 0;">Online • Staff Member</p>
                    </div>
                </div>

                <!-- User Menu -->
                <div id="user-menu" style="display: none; position: absolute; bottom: 100%; left: 0; right: 0; background: white; border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15); padding: 0.5rem 0; margin-bottom: 0.5rem; z-index: 1000;">
                    <div onclick="alert('Change Status')" style="display: flex; align-items: center; padding: 0.75rem 1rem; color: #4a5568; cursor: pointer;" onmouseover="this.style.background='#f7fafc'" onmouseout="this.style.background='transparent'">
                        <svg style="width: 16px; height: 16px; margin-right: 0.75rem; fill: currentColor;" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>
                        Change Status
                    </div>
                    <div onclick="alert('Edit Profile')" style="display: flex; align-items: center; padding: 0.75rem 1rem; color: #4a5568; cursor: pointer;" onmouseover="this.style.background='#f7fafc'" onmouseout="this.style.background='transparent'">
                        <svg style="width: 16px; height: 16px; margin-right: 0.75rem; fill: currentColor;" viewBox="0 0 24 24"><path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/></svg>
                        Edit Profile
                    </div>
                    <div onclick="alert('View Alerts')" style="display: flex; align-items: center; padding: 0.75rem 1rem; color: #4a5568; cursor: pointer;" onmouseover="this.style.background='#f7fafc'" onmouseout="this.style.background='transparent'">
                        <svg style="width: 16px; height: 16px; margin-right: 0.75rem; fill: currentColor;" viewBox="0 0 24 24"><path d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/></svg>
                        View Alerts
                    </div>
                    <div onclick="if(confirm('Logout?')) alert('Logging out')" style="display: flex; align-items: center; padding: 0.75rem 1rem; color: #4a5568; cursor: pointer;" onmouseover="this.style.background='#f7fafc'" onmouseout="this.style.background='transparent'">
                        <svg style="width: 16px; height: 16px; margin-right: 0.75rem; fill: currentColor;" viewBox="0 0 24 24"><path d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/></svg>
                        Logout
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="header">
                <h2 id="page-title">My Dashboard</h2>
                <p id="page-subtitle">Welcome back! Here's your personalized overview.</p>
            </div>

            <!-- Dashboard Content -->
            <div id="dashboard-content">
                <div class="dashboard-grid">
                    <!-- Calendar Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m3 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m3 0h6l-1 9H4l-1-9h6"/>
                                </svg>
                                Calendar Events
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="calendar-widget">
                            <div class="calendar-day">S</div>
                            <div class="calendar-day">M</div>
                            <div class="calendar-day">T</div>
                            <div class="calendar-day">W</div>
                            <div class="calendar-day">T</div>
                            <div class="calendar-day">F</div>
                            <div class="calendar-day">S</div>
                            <div class="calendar-day">18</div>
                            <div class="calendar-day">19</div>
                            <div class="calendar-day today">20</div>
                            <div class="calendar-day event">21</div>
                            <div class="calendar-day">22</div>
                            <div class="calendar-day">23</div>
                            <div class="calendar-day">24</div>
                        </div>
                        <div style="margin-top: 1rem; font-size: 0.9rem; color: #718096;">
                            <strong>Today:</strong> Student Leaders Meeting<br>
                            <strong>Tomorrow:</strong> Synergy Worship at 8 PM
                        </div>
                    </div>

                    <!-- Google Files Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                Recent Files
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="file-item">
                            <div class="file-icon doc">W</div>
                            <div class="file-info">
                                <h4>Synergy Worship Outline</h4>
                                <p>Modified 2 hours ago</p>
                            </div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon sheet">S</div>
                            <div class="file-info">
                                <h4>Student Leader Schedule</h4>
                                <p>Modified yesterday</p>
                            </div>
                        </div>
                        <div class="file-item">
                            <div class="file-icon pdf">P</div>
                            <div class="file-info">
                                <h4>Fall Event Budget</h4>
                                <p>Modified 3 days ago</p>
                            </div>
                        </div>
                    </div>

                    <!-- Building Music Player Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z"/>
                                </svg>
                                Building Audio
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="music-player">
                            <div class="music-info">
                                <h4>CSF Building - Main Area</h4>
                                <p>Campus Worship Mix</p>
                            </div>
                            <div class="music-controls">
                                <button class="music-btn">⏮</button>
                                <button class="music-btn play">▶</button>
                                <button class="music-btn">⏭</button>
                            </div>
                        </div>
                    </div>

                    <!-- Tasks Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                </svg>
                                My Tasks
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="task-item">
                            <div class="task-checkbox completed"></div>
                            <div class="task-text completed">Review new student applications</div>
                            <div class="task-priority low"></div>
                        </div>
                        <div class="task-item">
                            <div class="task-checkbox"></div>
                            <div class="task-text">Prepare Shift lesson plan</div>
                            <div class="task-priority high"></div>
                        </div>
                        <div class="task-item">
                            <div class="task-checkbox"></div>
                            <div class="task-text">Update CSF website</div>
                            <div class="task-priority medium"></div>
                        </div>
                        <div class="task-item">
                            <div class="task-checkbox"></div>
                            <div class="task-text">Order supplies for Fall Retreat</div>
                            <div class="task-priority medium"></div>
                        </div>
                    </div>

                    <!-- News Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                                </svg>
                                Church News
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="news-item">
                            <h4>New Sound System Installed in Main Building</h4>
                            <div class="news-meta">
                                <span>Posted by Admin</span>
                                <span>2 days ago</span>
                            </div>
                        </div>
                        <div class="news-item">
                            <h4>Fall Retreat Registration Opens Next Week</h4>
                            <div class="news-meta">
                                <span>Posted by Sarah M.</span>
                                <span>5 days ago</span>
                            </div>
                        </div>
                        <div class="news-item">
                            <h4>Weekly Staff Meeting Moved to Friday</h4>
                            <div class="news-meta">
                                <span>Posted by Brian Marshall</span>
                                <span>1 week ago</span>
                            </div>
                        </div>
                    </div>

                    <!-- Communications Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                </svg>
                                Recent Messages
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="comm-item">
                            <div class="comm-avatar">SM</div>
                            <div class="comm-content">
                                <div class="comm-header">
                                    <span class="comm-name">Sarah Martinez</span>
                                    <span class="comm-time">10 min ago</span>
                                </div>
                                <div class="comm-message">Can we discuss the student leadership budget for next quarter?</div>
                            </div>
                        </div>
                        <div class="comm-item">
                            <div class="comm-avatar">BM</div>
                            <div class="comm-content">
                                <div class="comm-header">
                                    <span class="comm-name">Brian Marshall</span>
                                    <span class="comm-time">1 hour ago</span>
                                </div>
                                <div class="comm-message">Great job on last night's Synergy setup!</div>
                            </div>
                        </div>
                        <div class="comm-item">
                            <div class="comm-avatar">MR</div>
                            <div class="comm-content">
                                <div class="comm-header">
                                    <span class="comm-name">Mike Roberts</span>
                                    <span class="comm-time">3 hours ago</span>
                                </div>
                                <div class="comm-message">HVAC system in building B needs attention</div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                                Quick Notes
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <textarea class="notes-area" placeholder="Add your notes here...">Meeting notes from staff discussion:
- New student orientation coordinator needed
- Budget approval for building renovation
- Fall retreat planning begins next month</textarea>
                    </div>

                    <!-- Shortcuts Widget -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                </svg>
                                Quick Access
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div class="shortcuts-grid">
                            <a href="#" class="shortcut-item">
                                <div class="shortcut-icon">
                                    <svg viewBox="0 0 24 24">
                                        <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                <span class="shortcut-name">Students</span>
                            </a>
                            <a href="#" class="shortcut-item">
                                <div class="shortcut-icon">
                                    <svg viewBox="0 0 24 24">
                                        <path d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m3 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m3 0h6l-1 9H4l-1-9h6"/>
                                    </svg>
                                </div>
                                <span class="shortcut-name">Events</span>
                            </a>
                            <a href="#" class="shortcut-item">
                                <div class="shortcut-icon">
                                    <svg viewBox="0 0 24 24">
                                        <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                    </svg>
                                </div>
                                <span class="shortcut-name">Finance</span>
                            </a>
                            <a href="#" class="shortcut-item">
                                <div class="shortcut-icon">
                                    <svg viewBox="0 0 24 24">
                                        <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                </div>
                                <span class="shortcut-name">Facility</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management Content (Hidden by default) -->
            <div id="user-management-content" style="display: none;">
                <div class="widget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <svg viewBox="0 0 24 24">
                                <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            Staff Directory
                        </div>
                        <button style="background: linear-gradient(135deg, #2563eb, #1e40af); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 12px; cursor: pointer;">Add New Staff</button>
                    </div>
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
                            <thead>
                                <tr style="border-bottom: 2px solid #e2e8f0;">
                                    <th style="text-align: left; padding: 1rem; color: #4a5568; font-weight: 600;">Name</th>
                                    <th style="text-align: left; padding: 1rem; color: #4a5568; font-weight: 600;">Role</th>
                                    <th style="text-align: left; padding: 1rem; color: #4a5568; font-weight: 600;">Department</th>
                                    <th style="text-align: left; padding: 1rem; color: #4a5568; font-weight: 600;">Status</th>
                                    <th style="text-align: left; padding: 1rem; color: #4a5568; font-weight: 600;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #2563eb, #1e40af); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-right: 1rem;">BM</div>
                                            <div>
                                                <div style="font-weight: 600; color: #2d3748;">Brian Marshall</div>
                                                <div style="color: #718096; font-size: 0.875rem;"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem; color: #4a5568;">Campus Minister</td>
                                    <td style="padding: 1rem; color: #4a5568;">Leadership</td>
                                    <td style="padding: 1rem;">
                                        <span style="background: #c6f6d5; color: #22543d; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem; font-weight: 500;">Active</span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <button style="background: none; border: none; color: #2563eb; cursor: pointer; margin-right: 1rem;">Edit</button>
                                        <button style="background: none; border: none; color: #dc2626; cursor: pointer;">Remove</button>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #2563eb, #1e40af); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-right: 1rem;">SM</div>
                                            <div>
                                                <div style="font-weight: 600; color: #2d3748;">Sarah Martinez</div>
                                                <div style="color: #718096; font-size: 0.875rem;"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem; color: #4a5568;">Student Leadership Director</td>
                                    <td style="padding: 1rem; color: #4a5568;">Student Ministry</td>
                                    <td style="padding: 1rem;">
                                        <span style="background: #c6f6d5; color: #22543d; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem; font-weight: 500;">Active</span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <button style="background: none; border: none; color: #2563eb; cursor: pointer; margin-right: 1rem;">Edit</button>
                                        <button style="background: none; border: none; color: #dc2626; cursor: pointer;">Remove</button>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #e2e8f0;">
                                    <td style="padding: 1rem;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #2563eb, #1e40af); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-right: 1rem;">MR</div>
                                            <div>
                                                <div style="font-weight: 600; color: #2d3748;">Mike Roberts</div>
                                                <div style="color: #718096; font-size: 0.875rem;"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td style="padding: 1rem; color: #4a5568;">Facilities Manager</td>
                                    <td style="padding: 1rem; color: #4a5568;">Operations</td>
                                    <td style="padding: 1rem;">
                                        <span style="background: #c6f6d5; color: #22543d; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem; font-weight: 500;">Active</span>
                                    </td>
                                    <td style="padding: 1rem;">
                                        <button style="background: none; border: none; color: #2563eb; cursor: pointer; margin-right: 1rem;">Edit</button>
                                        <button style="background: none; border: none; color: #dc2626; cursor: pointer;">Remove</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Building Management Content (Hidden by default) -->
            <div id="building-management-content" style="display: none;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <!-- HVAC Control -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                                </svg>
                                HVAC System
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-top: 1rem;">
                            <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px; text-align: center;">
                                <div style="font-size: 2rem; font-weight: 700; color: #667eea; margin-bottom: 0.5rem;">72°F</div>
                                <div style="color: #718096;">Sanctuary</div>
                            </div>
                            <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px; text-align: center;">
                                <div style="font-size: 2rem; font-weight: 700; color: #667eea; margin-bottom: 0.5rem;">68°F</div>
                                <div style="color: #718096;">Fellowship Hall</div>
                            </div>
                        </div>
                        <button style="width: 100%; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 1rem; border-radius: 12px; margin-top: 1rem; cursor: pointer; font-weight: 600;">Adjust Temperature</button>
                    </div>

                    <!-- Lighting Control -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 1-4 4-4s4 2 4 4c2-1 2.657-2.657 2.657-2.657a8 8 0 01-2 11.314z"/>
                                </svg>
                                Lighting Control
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div style="space-y: 1rem;">
                            <div style="display: flex; justify-content: between; align-items: center; padding: 1rem 0; border-bottom: 1px solid #e2e8f0;">
                                <span style="color: #4a5568; font-weight: 500;">Main Building</span>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <span style="color: #718096; font-size: 0.875rem;">85%</span>
                                    <label style="position: relative; display: inline-block; width: 60px; height: 34px;">
                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                        <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, #2563eb, #1e40af); transition: .4s; border-radius: 34px; before: position: absolute; content: ''; height: 26px; width: 26px; left: 30px; bottom: 4px; background: white; transition: .4s; border-radius: 50%;"></span>
                                    </label>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: between; align-items: center; padding: 1rem 0; border-bottom: 1px solid #e2e8f0;">
                                <span style="color: #4a5568; font-weight: 500;">Meeting Rooms</span>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <span style="color: #718096; font-size: 0.875rem;">60%</span>
                                    <label style="position: relative; display: inline-block; width: 60px; height: 34px;">
                                        <input type="checkbox" checked style="opacity: 0; width: 0; height: 0;">
                                        <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, #2563eb, #1e40af); transition: .4s; border-radius: 34px;"></span>
                                    </label>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: between; align-items: center; padding: 1rem 0;">
                                <span style="color: #4a5568; font-weight: 500;">Study Rooms</span>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <span style="color: #718096; font-size: 0.875rem;">OFF</span>
                                    <label style="position: relative; display: inline-block; width: 60px; height: 34px;">
                                        <input type="checkbox" style="opacity: 0; width: 0; height: 0;">
                                        <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background: #ccc; transition: .4s; border-radius: 34px;"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security System -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                </svg>
                                Security System
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div style="text-align: center; margin: 2rem 0;">
                            <div style="display: inline-flex; align-items: center; justify-content: center; width: 100px; height: 100px; border-radius: 50%; background: linear-gradient(135deg, #16a34a, #15803d); color: white; font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem;">
                                <svg style="width: 48px; height: 48px; fill: white;" viewBox="0 0 24 24">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                </svg>
                            </div>
                            <div style="font-size: 1.25rem; font-weight: 600; color: #2d3748; margin-bottom: 0.5rem;">System Armed</div>
                            <div style="color: #718096;">All zones secure</div>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                            <button style="background: #fef2f2; color: #dc2626; border: none; padding: 1rem; border-radius: 12px; cursor: pointer; font-weight: 600;">Disarm</button>
                            <button style="background: #f7fafc; color: #4a5568; border: none; padding: 1rem; border-radius: 12px; cursor: pointer; font-weight: 600;">View Logs</button>
                        </div>
                    </div>

                    <!-- Audio/Visual Systems -->
                    <div class="widget">
                        <div class="widget-header">
                            <div class="widget-title">
                                <svg viewBox="0 0 24 24">
                                    <path d="M15.536 4.1a.8.8 0 00-1.072 0l-6.4 5.6a.8.8 0 000 1.2l6.4 5.6a.8.8 0 001.072 0l.8-.7a.8.8 0 000-1.2L11.472 12l4.864-2.6a.8.8 0 000-1.2l-.8-.7z"/>
                                </svg>
                                Audio/Visual
                            </div>
                            <button class="widget-menu">⋯</button>
                        </div>
                        <div style="space-y: 1rem;">
                            <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px; margin-bottom: 1rem;">
                                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 1rem;">
                                    <span style="font-weight: 600; color: #2d3748;">Main Building Audio</span>
                                    <span style="background: #dcfdf4; color: #047857; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem;">Online</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <span style="color: #718096; font-size: 0.875rem;">Volume:</span>
                                    <div style="flex: 1; height: 8px; background: #e2e8f0; border-radius: 4px; position: relative;">
                                        <div style="width: 75%; height: 100%; background: linear-gradient(90deg, #2563eb, #1e40af); border-radius: 4px;"></div>
                                    </div>
                                    <span style="color: #4a5568; font-size: 0.875rem; font-weight: 600;">75%</span>
                                </div>
                            </div>
                            <div style="background: #f7fafc; padding: 1.5rem; border-radius: 12px;">
                                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 1rem;">
                                    <span style="font-weight: 600; color: #2d3748;">Live Stream</span>
                                    <span style="background: #fef2f2; color: #dc2626; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem;">Offline</span>
                                </div>
                                <button style="width: 100%; background: linear-gradient(135deg, #2563eb, #1e40af); color: white; border: none; padding: 0.75rem; border-radius: 8px; cursor: pointer; font-weight: 600;">Start Stream</button>135deg, #667eea, #764ba2); color: white; border: none; padding: 0.75rem; border-radius: 8px; cursor: pointer; font-weight: 600;">Start Stream</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Floating Widget Toggle Button -->
        <button class="widget-toggle" onclick="toggleWidgets()" id="widget-toggle">
            <svg viewBox="0 0 24 24">
                <path d="M7 14l5-5 5 5z"/>
            </svg>
        </button>

        <!-- Floating Widgets Container -->
        <div class="widgets-container" id="widgets-container">
            <div class="widgets-header">
                <h3>Staff Tools</h3>
            </div>
            <div class="widgets-grid">
                <!-- Quick Actions Widget -->
                <div class="floating-widget">
                    <h5>
                        <div style="display: flex; align-items: center;">
                            <svg viewBox="0 0 24 24">
                                <path d="M12 4v16m8-8H4"/>
                            </svg>
                            Quick Actions
                        </div>
                    </h5>
                    <div class="quick-actions-floating">
                        <button class="quick-action-floating" onclick="alert('Add Task')">
                            <svg viewBox="0 0 24 24">
                                <path d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                            </svg>
                            Task
                        </button>
                        <button class="quick-action-floating" onclick="alert('Add Note')">
                            <svg viewBox="0 0 24 24">
                                <path d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            Note
                        </button>
                        <button class="quick-action-floating" onclick="alert('Add Event')">
                            <svg viewBox="0 0 24 24">
                                <path d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m3 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m3 0h6l-1 9H4l-1-9h6"/>
                            </svg>
                            Event
                        </button>
                    </div>
                </div>

                <!-- Quick Links Widget -->
                <div class="floating-widget">
                    <h5>
                        <div style="display: flex; align-items: center;">
                            <svg viewBox="0 0 24 24">
                                <path d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                            </svg>
                            Quick Links
                        </div>
                    </h5>
                    <a href="#" class="quick-link-floating" onclick="alert('Student Directory')">
                        <svg viewBox="0 0 24 24">
                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Student Directory
                    </a>
                    <a href="#" class="quick-link-floating" onclick="alert('Event Calendar')">
                        <svg viewBox="0 0 24 24">
                            <path d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m3 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m3 0h6l-1 9H4l-1-9h6"/>
                        </svg>
                        Event Calendar
                    </a>
                    <a href="#" class="quick-link-floating" onclick="alert('Budget Reports')">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                        Budget Reports
                    </a>
                    <a href="#" class="quick-link-floating" onclick="alert('Facilities')">
                        <svg viewBox="0 0 24 24">
                            <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                        </svg>
                        Facilities
                    </a>
                </div>

                <!-- Messages Widget -->
                <div class="floating-widget">
                    <h5>
                        <div style="display: flex; align-items: center;">
                            <svg viewBox="0 0 24 24">
                                <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            Messages
                        </div>
                        <button onclick="alert('New Message')" style="background: linear-gradient(135deg, #2563eb, #1e40af); color: white; border: none; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; cursor: pointer; font-size: 14px; font-weight: 600;">+</button>
                    </h5>
                    <div class="message-floating unread" onclick="alert('Message from Sarah: Can we discuss the Fall Retreat budget?')">
                        <div class="message-avatar-floating">SM</div>
                        <div style="flex: 1; min-width: 0;">
                            <div style="font-size: 0.8rem; font-weight: 600; color: #2d3748; margin-bottom: 0.25rem;">Sarah Martinez</div>
                            <div style="font-size: 0.75rem; color: #718096; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Can we discuss the Fall Retreat...</div>
                        </div>
                        <div style="font-size: 0.7rem; color: #a0aec0; flex-shrink: 0; margin-left: 0.5rem;">2m</div>
                    </div>
                    <div class="message-floating" onclick="alert('Message from Mike: HVAC system update complete')">
                        <div class="message-avatar-floating">MR</div>
                        <div style="flex: 1; min-width: 0;">
                            <div style="font-size: 0.8rem; font-weight: 600; color: #2d3748; margin-bottom: 0.25rem;">Mike Roberts</div>
                            <div style="font-size: 0.75rem; color: #718096; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">HVAC system update complete</div>
                        </div>
                        <div style="font-size: 0.7rem; color: #a0aec0; flex-shrink: 0; margin-left: 0.5rem;">1h</div>
                    </div>
                    <div class="message-floating unread" onclick="alert('Message from Brian: Great job on last nights setup!')">
                        <div class="message-avatar-floating">BM</div>
                        <div style="flex: 1; min-width: 0;">
                            <div style="font-size: 0.8rem; font-weight: 600; color: #2d3748; margin-bottom: 0.25rem;">Brian Marshall</div>
                            <div style="font-size: 0.75rem; color: #718096; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">Great job on last night's setup!</div>
                        </div>
                        <div style="font-size: 0.7rem; color: #a0aec0; flex-shrink: 0; margin-left: 0.5rem;">3h</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle floating widgets
        function toggleWidgets() {
            const widgetsContainer = document.getElementById('widgets-container');
            const toggleButton = document.getElementById('widget-toggle');
            
            widgetsContainer.classList.toggle('active');
            toggleButton.classList.toggle('active');
        }

        // User menu toggle function
        function toggleUserMenu() {
            const userMenu = document.getElementById('user-menu');
            if (userMenu.style.display === 'none' || userMenu.style.display === '') {
                userMenu.style.display = 'block';
            } else {
                userMenu.style.display = 'none';
            }
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(e) {
            const userMenu = document.getElementById('user-menu');
            const userProfile = e.target.closest('[onclick="toggleUserMenu()"]');
            if (!userProfile && !userMenu.contains(e.target)) {
                userMenu.style.display = 'none';
            }
        });

        // Navigation functionality
        function showDashboardDropdown() {
            const dropdown = document.getElementById('dashboard-dropdown');
            dropdown.classList.toggle('active');
        }

        function showMyDashboard() {
            document.getElementById('page-title').textContent = 'My Dashboard';
            document.getElementById('page-subtitle').textContent = 'Welcome back! Here\'s your personalized overview.';
            document.getElementById('dashboard-content').style.display = 'block';
            document.getElementById('user-management-content').style.display = 'none';
            document.getElementById('building-management-content').style.display = 'none';
            
            // Update active states
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showAnalytics() {
            document.getElementById('page-title').textContent = 'Analytics Dashboard';
            document.getElementById('page-subtitle').textContent = 'Track attendance, engagement, and growth metrics.';
            document.getElementById('dashboard-content').style.display = 'none';
            document.getElementById('user-management-content').style.display = 'none';
            document.getElementById('building-management-content').style.display = 'none';
            
            // Update active states
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showReports() {
            document.getElementById('page-title').textContent = 'Reports';
            document.getElementById('page-subtitle').textContent = 'Generate and view detailed reports.';
            document.getElementById('dashboard-content').style.display = 'none';
            document.getElementById('user-management-content').style.display = 'none';
            document.getElementById('building-management-content').style.display = 'none';
            
            // Update active states
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showUserManagement() {
            document.getElementById('page-title').textContent = 'User Management';
            document.getElementById('page-subtitle').textContent = 'Manage staff accounts, roles, and permissions.';
            document.getElementById('dashboard-content').style.display = 'none';
            document.getElementById('user-management-content').style.display = 'block';
            document.getElementById('building-management-content').style.display = 'none';
            
            // Update active states
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showBuildingManagement() {
            document.getElementById('page-title').textContent = 'Building Management';
            document.getElementById('page-subtitle').textContent = 'Control HVAC, lighting, security, and audio/visual systems.';
            document.getElementById('dashboard-content').style.display = 'none';
            document.getElementById('user-management-content').style.display = 'none';
            document.getElementById('building-management-content').style.display = 'block';
            
            // Update active states
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        // Interactive widget functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Task checkboxes
            document.querySelectorAll('.task-checkbox').forEach(checkbox => {
                checkbox.addEventListener('click', function() {
                    this.classList.toggle('completed');
                    const taskText = this.nextElementSibling;
                    taskText.classList.toggle('completed');
                });
            });

            // Music player controls
            document.querySelectorAll('.music-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.classList.contains('play')) {
                        this.innerHTML = this.innerHTML === '▶' ? '⏸' : '▶';
                    }
                });
            });

            // Calendar day interactions
            document.querySelectorAll('.calendar-day').forEach(day => {
                day.addEventListener('click', function() {
                    if (this.textContent && !isNaN(this.textContent)) {
                        document.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('today'));
                        this.classList.add('today');
                    }
                });
            });

            // File item interactions
            document.querySelectorAll('.file-item').forEach(item => {
                item.addEventListener('click', function() {
                    const fileName = this.querySelector('h4').textContent;
                    alert(`Opening: ${fileName}`);
                });
            });

            // Shortcut interactions
            document.querySelectorAll('.shortcut-item').forEach(shortcut => {
                shortcut.addEventListener('click', function(e) {
                    e.preventDefault();
                    const shortcutName = this.querySelector('.shortcut-name').textContent;
                    alert(`Navigating to ${shortcutName} section...`);
                });
            });

            // Widget menu interactions
            document.querySelectorAll('.widget-menu').forEach(menu => {
                menu.addEventListener('click', function(e) {
                    e.stopPropagation();
                    // Create and show context menu
                    const contextMenu = document.createElement('div');
                    contextMenu.style.cssText = `
                        position: absolute;
                        background: white;
                        border: 1px solid #e2e8f0;
                        border-radius: 8px;
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                        padding: 0.5rem 0;
                        z-index: 1000;
                        min-width: 150px;
                    `;
                    
                    const menuItems = [
                        'Customize Widget',
                        'Refresh Data',
                        'Export Data',
                        'Remove Widget'
                    ];
                    
                    menuItems.forEach(item => {
                        const menuItem = document.createElement('div');
                        menuItem.style.cssText = `
                            padding: 0.75rem 1rem;
                            cursor: pointer;
                            transition: background 0.2s;
                            color: #4a5568;
                            font-size: 0.875rem;
                        `;
                        menuItem.textContent = item;
                        menuItem.addEventListener('mouseenter', () => {
                            menuItem.style.background = '#f7fafc';
                        });
                        menuItem.addEventListener('mouseleave', () => {
                            menuItem.style.background = 'transparent';
                        });
                        menuItem.addEventListener('click', () => {
                            alert(`${item} clicked`);
                            document.body.removeChild(contextMenu);
                        });
                        contextMenu.appendChild(menuItem);
                    });
                    
                    // Position the menu
                    const rect = this.getBoundingClientRect();
                    contextMenu.style.left = (rect.right - 150) + 'px';
                    contextMenu.style.top = (rect.bottom + 5) + 'px';
                    
                    document.body.appendChild(contextMenu);
                    
                    // Remove menu when clicking elsewhere
                    const removeMenu = (e) => {
                        if (!contextMenu.contains(e.target)) {
                            document.body.removeChild(contextMenu);
                            document.removeEventListener('click', removeMenu);
                        }
                    };
                    setTimeout(() => document.addEventListener('click', removeMenu), 100);
                });
            });

            // Building management controls
            const lightingSwitches = document.querySelectorAll('input[type="checkbox"]');
            lightingSwitches.forEach(switchEl => {
                switchEl.addEventListener('change', function() {
                    const container = this.closest('[style*="display: flex"]');
                    const statusSpan = container.querySelector('span[style*="color: #718096"]');
                    if (this.checked) {
                        statusSpan.textContent = Math.floor(Math.random() * 40 + 60) + '%';
                        this.nextElementSibling.style.background = 'linear-gradient(135deg, #2563eb, #1e40af)';
                    } else {
                        statusSpan.textContent = 'OFF';
                        this.nextElementSibling.style.background = '#ccc';
                    }
                });
            });

            // HVAC temperature adjustment
            const hvacButton = document.querySelector('button[style*="Adjust Temperature"]');
            if (hvacButton) {
                hvacButton.addEventListener('click', function() {
                    const sanctuaryTemp = document.querySelector('[style*="72°F"]');
                    const fellowshipTemp = document.querySelector('[style*="68°F"]');
                    
                    // Simulate temperature adjustment
                    const newSanctuaryTemp = Math.floor(Math.random() * 6 + 70);
                    const newFellowshipTemp = Math.floor(Math.random() * 6 + 66);
                    
                    sanctuaryTemp.textContent = newSanctuaryTemp + '°F';
                    fellowshipTemp.textContent = newFellowshipTemp + '°F';
                    
                    // Show feedback
                    this.textContent = 'Adjusting...';
                    this.style.background = '#1e40af';
                    setTimeout(() => {
                        this.textContent = 'Temperature Adjusted';
                        this.style.background = 'linear-gradient(135deg, #16a34a, #15803d)';
                        setTimeout(() => {
                            this.textContent = 'Adjust Temperature';
                            this.style.background = 'linear-gradient(135deg, #2563eb, #1e40af)';
                        }, 2000);
                    }, 1500);
                });
            }

            // Security system controls
            const disarmButton = document.querySelector('button[style*="background: #fef2f2"]');
            const viewLogsButton = document.querySelector('button[style*="View Logs"]');
            
            if (disarmButton) {
                disarmButton.addEventListener('click', function() {
                    const statusDiv = document.querySelector('[style*="System Armed"]');
                    const statusDesc = document.querySelector('[style*="All zones secure"]');
                    const securityIcon = document.querySelector('[style*="width: 100px; height: 100px"]');
                    
                    if (statusDiv.textContent === 'System Armed') {
                        statusDiv.textContent = 'System Disarmed';
                        statusDesc.textContent = 'Security offline';
                        securityIcon.style.background = 'linear-gradient(135deg, #dc2626, #b91c1c)';
                        this.textContent = 'Arm';
                        this.style.background = '#dcfdf4';
                        this.style.color = '#047857';
                    } else {
                        statusDiv.textContent = 'System Armed';
                        statusDesc.textContent = 'All zones secure';
                        securityIcon.style.background = 'linear-gradient(135deg, #16a34a, #15803d)';
                        this.textContent = 'Disarm';
                        this.style.background = '#fef2f2';
                        this.style.color = '#dc2626';
                    }
                });
            }

            if (viewLogsButton) {
                viewLogsButton.addEventListener('click', function() {
                    alert('Security Logs:\n\n[08:15] System Armed\n[07:45] Motion detected - Youth Room\n[07:30] Door opened - Main entrance\n[06:00] System Disarmed');
                });
            }

            // Live stream control
            const streamButton = document.querySelector('button[style*="Start Stream"]');
            if (streamButton) {
                streamButton.addEventListener('click', function() {
                    const streamStatus = this.previousElementSibling.querySelector('span[style*="background: #fed7d7"]');
                    
                    if (this.textContent === 'Start Stream') {
                        this.textContent = 'Stop Stream';
                        this.style.background = 'linear-gradient(135deg, #dc2626, #b91c1c)';
                        streamStatus.textContent = 'Live';
                        streamStatus.style.background = '#dcfdf4';
                        streamStatus.style.color = '#047857';
                    } else {
                        this.textContent = 'Start Stream';
                        this.style.background = 'linear-gradient(135deg, #2563eb, #1e40af)';
                        streamStatus.textContent = 'Offline';
                        streamStatus.style.background = '#fef2f2';
                        streamStatus.style.color = '#dc2626';
                    }
                });
            }

            // Notes auto-save simulation
            const notesArea = document.querySelector('.notes-area');
            if (notesArea) {
                let saveTimeout;
                notesArea.addEventListener('input', function() {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        // Simulate auto-save
                        const originalBorder = this.style.border;
                        this.style.border = '2px solid #16a34a';
                        setTimeout(() => {
                            this.style.border = originalBorder;
                        }, 1000);
                    }, 2000);
                });
            }

            // Communication item interactions
            document.querySelectorAll('.comm-item').forEach(item => {
                item.addEventListener('click', function() {
                    const name = this.querySelector('.comm-name').textContent;
                    alert(`Opening conversation with ${name}`);
                });
            });

            // Add widget drag and drop functionality
            let draggedWidget = null;
            
            document.querySelectorAll('.widget').forEach(widget => {
                widget.draggable = true;
                
                widget.addEventListener('dragstart', function(e) {
                    draggedWidget = this;
                    this.style.opacity = '0.5';
                });
                
                widget.addEventListener('dragend', function(e) {
                    this.style.opacity = '1';
                    draggedWidget = null;
                });
                
                widget.addEventListener('dragover', function(e) {
                    e.preventDefault();
                });
                
                widget.addEventListener('drop', function(e) {
                    e.preventDefault();
                    if (draggedWidget && draggedWidget !== this) {
                        const grid = this.parentNode;
                        const draggedIndex = Array.from(grid.children).indexOf(draggedWidget);
                        const targetIndex = Array.from(grid.children).indexOf(this);
                        
                        if (draggedIndex < targetIndex) {
                            grid.insertBefore(draggedWidget, this.nextSibling);
                        } else {
                            grid.insertBefore(draggedWidget, this);
                        }
                    }
                });
            });
        });

        // Mobile menu toggle
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
        }

        // User profile functions
        function toggleUserMenu() {
            const userMenu = document.getElementById('user-menu');
            const statusDropdown = document.getElementById('status-dropdown');
            userMenu.classList.toggle('active');
            statusDropdown.classList.remove('active');
        }

        function changeStatus() {
            const userMenu = document.getElementById('user-menu');
            const statusDropdown = document.getElementById('status-dropdown');
            userMenu.classList.remove('active');
            statusDropdown.classList.toggle('active');
        }

        function setStatus(status) {
            const statusElement = document.querySelector('.user-status');
            const statusText = document.querySelector('.user-details p');
            const statusDropdown = document.getElementById('status-dropdown');
            
            statusElement.className = `user-status ${status}`;
            
            const statusTexts = {
                online: 'Online • Staff Member',
                away: 'Away • Staff Member',
                busy: 'Busy • Staff Member',
                offline: 'Offline • Staff Member'
            };
            
            statusText.textContent = statusTexts[status];
            statusDropdown.classList.remove('active');
        }

        function editProfile() {
            const userMenu = document.getElementById('user-menu');
            userMenu.classList.remove('active');
            alert('Edit Profile feature - This would open a profile editing modal');
        }

        function viewAlerts() {
            const userMenu = document.getElementById('user-menu');
            userMenu.classList.remove('active');
            alert('Alerts:\n• New student registration\n• Facility maintenance reminder\n• Upcoming event deadline');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                alert('Logging out...');
                // In a real application, this would redirect to login page
            }
        }

        // Quick action functions
        function quickAddTask() {
            const taskTitle = prompt('Enter task title:');
            if (taskTitle) {
                alert(`Task "${taskTitle}" added successfully!`);
                // In a real application, this would add the task to the system
            }
        }

        function quickAddNote() {
            const noteContent = prompt('Enter note content:');
            if (noteContent) {
                alert(`Note saved successfully!`);
                // In a real application, this would save the note
            }
        }

        function quickAddEvent() {
            const eventTitle = prompt('Enter event title:');
            if (eventTitle) {
                alert(`Event "${eventTitle}" created successfully!`);
                // In a real application, this would create the event
            }
        }

        // Message functions
        function newMessage() {
            const recipient = prompt('Send message to:');
            if (recipient) {
                const message = prompt(`Message to ${recipient}:`);
                if (message) {
                    alert(`Message sent to ${recipient}!`);
                    // In a real application, this would send the message
                }
            }
        }

        function openMessage(userId) {
            const messages = {
                sarah: {
                    name: 'Sarah Martinez',
                    messages: [
                        { sender: 'Sarah Martinez', text: 'Can we discuss the Fall Retreat budget?', time: '2:15 PM' },
                        { sender: 'You', text: 'Sure! What specific areas need review?', time: '2:16 PM' },
                        { sender: 'Sarah Martinez', text: 'Transportation and accommodations mainly', time: '2:18 PM' }
                    ]
                },
                mike: {
                    name: 'Mike Roberts',
                    messages: [
                        { sender: 'Mike Roberts', text: 'HVAC system update complete', time: '1:30 PM' },
                        { sender: 'You', text: 'Great! Any issues found?', time: '1:32 PM' },
                        { sender: 'Mike Roberts', text: 'All systems running smoothly now', time: '1:35 PM' }
                    ]
                },
                brian: {
                    name: 'Brian Marshall',
                    messages: [
                        { sender: 'Brian Marshall', text: 'Great job on last night\'s setup!', time: '11:45 AM' },
                        { sender: 'You', text: 'Thanks! The team worked really well together', time: '11:47 AM' }
                    ]
                }
            };

            const conversation = messages[userId];
            if (conversation) {
                let messageDisplay = `Conversation with ${conversation.name}:\n\n`;
                conversation.messages.forEach(msg => {
                    messageDisplay += `${msg.sender} (${msg.time}):\n${msg.text}\n\n`;
                });
                
                const reply = prompt(messageDisplay + 'Your reply:');
                if (reply) {
                    alert('Message sent!');
                    // Mark as read
                    const messageThread = event.currentTarget;
                    messageThread.classList.remove('unread');
                }
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.user-profile')) {
                document.getElementById('user-menu').classList.remove('active');
                document.getElementById('status-dropdown').classList.remove('active');
            }
        });

        // Add mobile menu button for small screens
        if (window.innerWidth <= 768) {
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '☰';
            mobileMenuBtn.style.cssText = `
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 1001;
                background: rgba(255, 255, 255, 0.9);
                border: none;
                border-radius: 8px;
                padding: 0.75rem;
                font-size: 1.25rem;
                cursor: pointer;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            `;
            mobileMenuBtn.addEventListener('click', toggleMobileMenu);
            document.body.appendChild(mobileMenuBtn);
        }
    </script>
</body>
</html>