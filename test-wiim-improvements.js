#!/usr/bin/env node

/**
 * Test script for WiiM Socket Hang Up improvements
 * This script tests the enhanced error handling, retry logic, and circuit breaker functionality
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api/wiim`;

// Test configuration
const TESTS = {
  healthCheck: true,
  basicConnectivity: true,
  playlistFetching: true,
  errorHandling: true,
  circuitBreaker: false // Set to true to test circuit breaker (will cause failures)
};

/**
 * Test runner class
 */
class WiimTestRunner {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  /**
   * Log test result
   */
  logResult(testName, status, details = {}) {
    const result = {
      test: testName,
      status,
      timestamp: new Date().toISOString(),
      duration: details.duration || 0,
      ...details
    };
    
    this.results.push(result);
    
    const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${statusIcon} ${testName}: ${status}`);
    
    if (details.error) {
      console.log(`   Error: ${details.error}`);
    }
    if (details.responseTime) {
      console.log(`   Response Time: ${details.responseTime}ms`);
    }
    if (details.retries) {
      console.log(`   Retries: ${details.retries}`);
    }
  }

  /**
   * Test health check endpoint
   */
  async testHealthCheck() {
    console.log('\n🔍 Testing Health Check...');
    
    try {
      const startTime = Date.now();
      const response = await axios.get(`${API_BASE}/health`, {
        timeout: 10000
      });
      const duration = Date.now() - startTime;
      
      const health = response.data;
      
      this.logResult('Health Check', 'PASS', {
        duration,
        responseTime: duration,
        healthStatus: health.status,
        tests: health.tests
      });
      
      // Log detailed health information
      console.log(`   Overall Status: ${health.status}`);
      console.log(`   Host: ${health.host}:${health.port}`);
      
      if (health.tests) {
        Object.entries(health.tests).forEach(([testName, testResult]) => {
          console.log(`   ${testName}: ${testResult.status} (${testResult.responseTime || 'N/A'})`);
        });
      }
      
      if (health.circuitBreakers) {
        Object.entries(health.circuitBreakers).forEach(([endpoint, state]) => {
          if (state.failures > 0) {
            console.log(`   Circuit Breaker ${endpoint}: ${state.failures} failures, open: ${state.isOpen}`);
          }
        });
      }
      
      return true;
    } catch (error) {
      this.logResult('Health Check', 'FAIL', {
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  /**
   * Test basic connectivity
   */
  async testBasicConnectivity() {
    console.log('\n🔗 Testing Basic Connectivity...');
    
    try {
      const startTime = Date.now();
      const response = await axios.get(`${API_BASE}/device`, {
        timeout: 15000
      });
      const duration = Date.now() - startTime;
      
      this.logResult('Basic Connectivity', 'PASS', {
        duration,
        responseTime: duration,
        deviceInfo: response.data
      });
      
      console.log(`   Device: ${response.data.name || 'Unknown'}`);
      console.log(`   Model: ${response.data.model || 'Unknown'}`);
      
      return true;
    } catch (error) {
      this.logResult('Basic Connectivity', 'FAIL', {
        error: error.message,
        status: error.response?.status
      });
      return false;
    }
  }

  /**
   * Test playlist fetching with retry logic
   */
  async testPlaylistFetching() {
    console.log('\n📋 Testing Playlist Fetching...');
    
    try {
      const startTime = Date.now();
      const response = await axios.get(`${API_BASE}/playlists`, {
        timeout: 25000
      });
      const duration = Date.now() - startTime;
      
      this.logResult('Playlist Fetching', 'PASS', {
        duration,
        responseTime: duration,
        playlistCount: response.data?.playlists?.length || 0
      });
      
      console.log(`   Playlists found: ${response.data?.playlists?.length || 0}`);
      
      return true;
    } catch (error) {
      // Check if this is a circuit breaker error
      const isCircuitBreakerError = error.response?.data?.error?.includes('circuit breaker');
      const status = isCircuitBreakerError ? 'WARN' : 'FAIL';
      
      this.logResult('Playlist Fetching', status, {
        error: error.message,
        status: error.response?.status,
        isCircuitBreakerError
      });
      
      if (isCircuitBreakerError) {
        console.log('   ⚠️ Circuit breaker is open - this is expected behavior after repeated failures');
      }
      
      return false;
    }
  }

  /**
   * Test error handling improvements
   */
  async testErrorHandling() {
    console.log('\n🛠️ Testing Error Handling...');
    
    try {
      // Test with an invalid endpoint to trigger error handling
      const startTime = Date.now();
      await axios.get(`${API_BASE}/invalid-endpoint`, {
        timeout: 5000
      });
      
      this.logResult('Error Handling', 'FAIL', {
        error: 'Expected 404 error but request succeeded'
      });
      
      return false;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // We expect a 404 error here
      if (error.response?.status === 404) {
        this.logResult('Error Handling', 'PASS', {
          duration,
          responseTime: duration,
          expectedError: '404 Not Found'
        });
        
        console.log('   ✅ Correctly handled 404 error');
        return true;
      } else {
        this.logResult('Error Handling', 'WARN', {
          duration,
          error: error.message,
          status: error.response?.status
        });
        
        console.log('   ⚠️ Unexpected error type (this may be normal)');
        return false;
      }
    }
  }

  /**
   * Test circuit breaker functionality (optional)
   */
  async testCircuitBreaker() {
    console.log('\n⚡ Testing Circuit Breaker...');
    console.log('   This test will intentionally cause failures to test circuit breaker');
    
    // Make multiple requests to a potentially failing endpoint
    let failures = 0;
    const maxAttempts = 5;
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        console.log(`   Attempt ${i + 1}/${maxAttempts}...`);
        await axios.get(`${API_BASE}/playlists`, {
          timeout: 1000 // Very short timeout to force failures
        });
      } catch (error) {
        failures++;
        console.log(`   Failure ${failures}: ${error.message}`);
        
        // Check if circuit breaker is now open
        if (failures >= 3) {
          try {
            const healthResponse = await axios.get(`${API_BASE}/health`);
            const circuitBreakerState = healthResponse.data.circuitBreakers?.getPlaylists;
            
            if (circuitBreakerState?.isOpen) {
              this.logResult('Circuit Breaker', 'PASS', {
                failures,
                circuitBreakerOpen: true
              });
              
              console.log('   ✅ Circuit breaker opened after 3 failures');
              return true;
            }
          } catch (healthError) {
            console.log('   Could not check circuit breaker state');
          }
        }
      }
    }
    
    this.logResult('Circuit Breaker', 'WARN', {
      failures,
      note: 'Circuit breaker may not have opened (this could be normal)'
    });
    
    return false;
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting WiiM Socket Hang Up Improvements Test Suite');
    console.log(`📍 Testing against: ${API_BASE}`);
    console.log('=' * 60);
    
    let passCount = 0;
    let totalTests = 0;
    
    // Run tests based on configuration
    if (TESTS.healthCheck) {
      totalTests++;
      if (await this.testHealthCheck()) passCount++;
    }
    
    if (TESTS.basicConnectivity) {
      totalTests++;
      if (await this.testBasicConnectivity()) passCount++;
    }
    
    if (TESTS.playlistFetching) {
      totalTests++;
      if (await this.testPlaylistFetching()) passCount++;
    }
    
    if (TESTS.errorHandling) {
      totalTests++;
      if (await this.testErrorHandling()) passCount++;
    }
    
    if (TESTS.circuitBreaker) {
      totalTests++;
      if (await this.testCircuitBreaker()) passCount++;
    }
    
    // Print summary
    const totalDuration = Date.now() - this.startTime;
    console.log('\n' + '=' * 60);
    console.log('📊 Test Summary');
    console.log('=' * 60);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passCount}`);
    console.log(`Failed: ${totalTests - passCount}`);
    console.log(`Success Rate: ${((passCount / totalTests) * 100).toFixed(1)}%`);
    console.log(`Total Duration: ${totalDuration}ms`);
    
    // Print detailed results
    console.log('\n📋 Detailed Results:');
    this.results.forEach(result => {
      console.log(`  ${result.test}: ${result.status} (${result.duration}ms)`);
    });
    
    return passCount === totalTests;
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const runner = new WiimTestRunner();
  
  runner.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = WiimTestRunner;
