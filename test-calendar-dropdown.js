console.log('Testing Google Calendar API...');
const axios = require('axios');

async function testGoogleCalendarAPI() {
  try {
    console.log('Fetching calendars from /api/google-calendar/calendars...');
    const response = await axios.get('/api/google-calendar/calendars');
    console.log('Response:', response.data);
    
    if (Array.isArray(response.data) && response.data.length > 0) {
      console.log('Success! Found', response.data.length, 'calendars');
      console.log('First calendar:', response.data[0]);
    } else {
      console.log('No calendars found or response is not an array');
    }
  } catch (error) {
    console.error('Error fetching calendars:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testGoogleCalendarAPI();
