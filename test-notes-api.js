const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Set up axios with credentials
axios.defaults.withCredentials = true;
axios.defaults.baseURL = 'http://localhost:6000';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'password123'
};

// Test note data
const testNote = {
  title: 'Test Note',
  content: 'This is a test note',
  type: 'note',
  color: '#ffffff',
  pinned: false
};

const testTodo = {
  title: 'Test To-Do',
  content: 'This is a test to-do',
  type: 'todo',
  color: '#fff8e1',
  pinned: true
};

// Test functions
const login = async () => {
  try {
    const response = await axios.post('/api/auth/login', testUser);
    console.log('Login successful:', response.data.user.name);
    return response.data.user;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
};

const createNote = async () => {
  try {
    const response = await axios.post('/api/notes', testNote);
    console.log('Note created:', response.data);
    return response.data;
  } catch (error) {
    console.error('Create note failed:', error.response?.data || error.message);
    throw error;
  }
};

const createTodo = async () => {
  try {
    const response = await axios.post('/api/notes', testTodo);
    console.log('To-Do created:', response.data);
    return response.data;
  } catch (error) {
    console.error('Create to-do failed:', error.response?.data || error.message);
    throw error;
  }
};

const getAllNotes = async () => {
  try {
    const response = await axios.get('/api/notes');
    console.log('All notes:', response.data);
    return response.data;
  } catch (error) {
    console.error('Get all notes failed:', error.response?.data || error.message);
    throw error;
  }
};

const getNoteById = async (id) => {
  try {
    const response = await axios.get(`/api/notes/${id}`);
    console.log('Note by ID:', response.data);
    return response.data;
  } catch (error) {
    console.error('Get note by ID failed:', error.response?.data || error.message);
    throw error;
  }
};

const updateNote = async (id, data) => {
  try {
    const response = await axios.put(`/api/notes/${id}`, data);
    console.log('Note updated:', response.data);
    return response.data;
  } catch (error) {
    console.error('Update note failed:', error.response?.data || error.message);
    throw error;
  }
};

const toggleNoteCompletion = async (id) => {
  try {
    const response = await axios.put(`/api/notes/${id}/toggle-completion`);
    console.log('Note completion toggled:', response.data);
    return response.data;
  } catch (error) {
    console.error('Toggle note completion failed:', error.response?.data || error.message);
    throw error;
  }
};

const toggleNotePinned = async (id) => {
  try {
    const response = await axios.put(`/api/notes/${id}/toggle-pinned`);
    console.log('Note pinned status toggled:', response.data);
    return response.data;
  } catch (error) {
    console.error('Toggle note pinned status failed:', error.response?.data || error.message);
    throw error;
  }
};

const deleteNote = async (id) => {
  try {
    const response = await axios.delete(`/api/notes/${id}`);
    console.log('Note deleted:', response.data);
    return response.data;
  } catch (error) {
    console.error('Delete note failed:', error.response?.data || error.message);
    throw error;
  }
};

// Run tests
const runTests = async () => {
  try {
    // Login
    await login();
    
    // Create notes
    const note = await createNote();
    const todo = await createTodo();
    
    // Get all notes
    const allNotes = await getAllNotes();
    
    // Get note by ID
    await getNoteById(note._id);
    
    // Update note
    await updateNote(note._id, { title: 'Updated Test Note', content: 'This note has been updated' });
    
    // Toggle to-do completion
    await toggleNoteCompletion(todo._id);
    
    // Toggle note pinned status
    await toggleNotePinned(note._id);
    
    // Delete notes
    await deleteNote(note._id);
    await deleteNote(todo._id);
    
    // Verify deletion
    await getAllNotes();
    
    console.log('All tests completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the tests
runTests();