const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Session Schema
 * 
 * This schema represents a user session in the application.
 * It's designed to work with express-session and connect-mongo.
 * 
 * Note: This schema matches the format used by connect-mongo for storing sessions.
 * Do not modify the field names or types unless you also update the connect-mongo configuration.
 */
const SessionSchema = new Schema({
  _id: String,
  expires: {
    type: Date,
    required: true
  },
  session: {
    type: Object,
    required: true
  }
}, {
  collection: 'sessions'
});

// Add indexes for better performance
SessionSchema.index({ expires: 1 }, { expireAfterSeconds: 0 });

module.exports = mongoose.model('Session', SessionSchema);
