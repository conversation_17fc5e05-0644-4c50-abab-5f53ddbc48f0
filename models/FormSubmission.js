const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Form Submission Schema
 * Stores submissions for forms
 */
const FormSubmissionSchema = new Schema({
  // Reference to the form
  form: {
    type: Schema.Types.ObjectId,
    ref: 'Form',
    required: true
  },
  
  // Form version at time of submission (for historical reference)
  formVersion: {
    title: String,
    fieldGroups: [{
      title: String,
      fields: [{
        fieldId: String,
        type: String,
        label: String
      }]
    }]
  },
  
  // Submission data - stores all field values
  data: {
    type: Map,
    of: Schema.Types.Mixed,
    default: new Map()
  },
  
  // File uploads associated with this submission
  files: [{
    fieldId: {
      type: String,
      required: true
    },
    filename: {
      type: String,
      required: true
    },
    originalFilename: {
      type: String,
      required: true
    },
    mimetype: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    },
    path: {
      type: String,
      required: true
    },
    url: {
      type: String
    }
  }],
  
  // Submission status
  status: {
    type: String,
    enum: ['draft', 'submitted', 'in_review', 'approved', 'rejected'],
    default: 'submitted'
  },
  
  // Submission metadata
  submittedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // For anonymous submissions
  submitterEmail: {
    type: String,
    trim: true
  },
  
  submitterName: {
    type: String,
    trim: true
  },
  
  // IP address of submitter
  ipAddress: {
    type: String
  },
  
  // User agent of submitter
  userAgent: {
    type: String
  },
  
  // Time taken to complete the form (in seconds)
  completionTime: {
    type: Number
  },
  
  // For multistep forms - track progress
  progress: {
    currentStep: {
      type: Number,
      default: 0
    },
    totalSteps: {
      type: Number,
      default: 1
    },
    completedSteps: {
      type: [Number],
      default: []
    }
  },
  
  // Review information
  review: {
    reviewedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    reviewedAt: {
      type: Date
    },
    comments: {
      type: String
    },
    rating: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  
  // Processing information
  processing: {
    // If this submission created a ticket
    ticketCreated: {
      type: Boolean,
      default: false
    },
    ticketId: {
      type: Schema.Types.ObjectId,
      ref: 'Ticket'
    },
    
    // If this submission created a task
    taskCreated: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: Schema.Types.ObjectId,
      ref: 'Task'
    },
    
    // Notification status
    notificationsSent: {
      type: Boolean,
      default: false
    },
    notificationErrors: {
      type: String
    },
    
    // Custom processing results
    customProcessing: {
      type: Map,
      of: Schema.Types.Mixed,
      default: new Map()
    }
  },
  
  // Submission notes (internal)
  notes: [{
    text: {
      type: String,
      required: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes for performance
FormSubmissionSchema.index({ form: 1 });
FormSubmissionSchema.index({ submittedBy: 1 });
FormSubmissionSchema.index({ status: 1 });
FormSubmissionSchema.index({ createdAt: 1 });
FormSubmissionSchema.index({ 'processing.ticketId': 1 });
FormSubmissionSchema.index({ 'processing.taskId': 1 });

// Method to get a sanitized version of the submission (for public viewing)
FormSubmissionSchema.methods.getSanitized = function() {
  const sanitized = {
    _id: this._id,
    form: this.form,
    status: this.status,
    data: {},
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  };
  
  // Convert Map to plain object
  this.data.forEach((value, key) => {
    sanitized.data[key] = value;
  });
  
  // Add file URLs but not file paths
  if (this.files && this.files.length > 0) {
    sanitized.files = this.files.map(file => ({
      fieldId: file.fieldId,
      filename: file.filename,
      originalFilename: file.originalFilename,
      mimetype: file.mimetype,
      size: file.size,
      url: file.url
    }));
  }
  
  return sanitized;
};

// Method to check if a user can view this submission
FormSubmissionSchema.methods.canView = async function(user) {
  // If no user, no access
  if (!user) {
    return false;
  }
  
  // Admin users can view all submissions
  if (user.roles.includes('admin')) {
    return true;
  }
  
  // Submitter can view their own submission
  if (this.submittedBy && this.submittedBy.equals(user._id)) {
    return true;
  }
  
  // Check if user has permission to view submissions for this form
  try {
    const Form = mongoose.model('Form');
    const form = await Form.findById(this.form);
    
    if (!form) {
      return false;
    }
    
    return form.hasViewSubmissionsPermission(user);
  } catch (err) {
    console.error('Error checking submission view permission:', err);
    return false;
  }
};

// Method to check if a user can edit this submission
FormSubmissionSchema.methods.canEdit = async function(user) {
  // If no user, no access
  if (!user) {
    return false;
  }
  
  // Admin users can edit all submissions
  if (user.roles.includes('admin')) {
    return true;
  }
  
  // Submitter can edit their own submission if it's in draft status
  if (this.status === 'draft' && this.submittedBy && this.submittedBy.equals(user._id)) {
    return true;
  }
  
  // Check if user has permission to manage the form
  try {
    const Form = mongoose.model('Form');
    const form = await Form.findById(this.form);
    
    if (!form) {
      return false;
    }
    
    return form.hasManagePermission(user);
  } catch (err) {
    console.error('Error checking submission edit permission:', err);
    return false;
  }
};

module.exports = mongoose.model('FormSubmission', FormSubmissionSchema);