const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Request Schema
 * Represents a request for an asset in the system
 */
const AssetRequestSchema = new Schema({
  assetId: {
    type: String,
    required: true
  },
  assetType: {
    type: String,
    required: true
  },
  assetName: {
    type: String,
    required: true
  },
  requestedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  requestReason: {
    type: String,
    required: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'denied', 'completed', 'cancelled'],
    default: 'pending'
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: {
    type: Date
  },
  approvalNotes: {
    type: String
  },
  returnDate: {
    type: Date
  },
  returnCondition: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'damaged'],
  },
  returnNotes: {
    type: String
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('AssetRequest', AssetRequestSchema);