const mongoose = require('mongoose');

/**
 * AVEquipment model for Phase 8 - AV Equipment Management & Technical Support
 * Manages sanctuary tech, projectors, microphones, and audio/visual equipment
 */
const avEquipmentSchema = new mongoose.Schema({
  // Basic equipment information
  equipmentId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 100
  },
  
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Equipment categorization
  category: {
    type: String,
    required: true,
    enum: [
      'audio_mixer',
      'microphone_wireless',
      'microphone_wired', 
      'microphone_headset',
      'speakers',
      'amplifier',
      'projector',
      'screen',
      'camera_ptz',
      'camera_fixed',
      'lighting_control',
      'lighting_fixture',
      'recording_device',
      'streaming_encoder',
      'monitor',
      'cable_snake',
      'di_box',
      'signal_processor',
      'power_conditioner',
      'rack_equipment',
      'other'
    ]
  },
  
  subcategory: {
    type: String,
    trim: true,
    maxlength: 100
  },
  
  // Location and positioning
  location: {
    buildingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Building',
      required: true
    },
    floorId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Floor'
    },
    roomId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Room'
    },
    roomName: String,
    position: {
      zone: String, // 'sanctuary', 'lobby', 'booth', 'stage', 'balcony'
      x: Number,
      y: Number,
      z: Number, // Height for ceiling-mounted equipment
      mounting: {
        type: String,
        enum: ['ceiling', 'wall', 'floor', 'rack', 'portable', 'handheld']
      }
    },
    iconId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'FloorplanIcon'
    }
  },
  
  // Technical specifications
  specifications: {
    manufacturer: String,
    model: String,
    serialNumber: String,
    manufacturerPartNumber: String,
    firmware: {
      version: String,
      lastUpdated: Date,
      updateAvailable: Boolean,
      releaseNotes: String
    },
    power: {
      voltage: String, // '120V', '240V', '48V phantom'
      consumption: Number, // Watts
      powerRequirement: String, // 'AC', 'DC', 'Phantom', 'PoE'
    },
    connectivity: {
      inputs: [String], // ['XLR', 'TRS', 'RCA', 'HDMI', 'SDI']
      outputs: [String],
      wireless: {
        frequency: String, // '2.4GHz', '5GHz', 'UHF 470-698 MHz'
        channel: String,
        range: String
      },
      network: {
        ipAddress: String,
        macAddress: String,
        protocol: String // 'Dante', 'AES67', 'NDI', 'RTSP'
      }
    },
    performance: {
      frequencyResponse: String, // '20Hz-20kHz ±1dB'
      sensitivity: String,
      maxSPL: String,
      resolution: String, // For cameras/displays
      frameRate: String,
      latency: String
    }
  },
  
  // Operational status
  status: {
    operational: {
      type: String,
      enum: ['active', 'standby', 'maintenance', 'failed', 'retired'],
      default: 'active'
    },
    health: {
      type: String,
      enum: ['excellent', 'good', 'warning', 'critical', 'unknown'],
      default: 'unknown'
    },
    lastChecked: Date,
    checkedBy: String,
    issues: [String],
    notes: String
  },
  
  // Usage and scheduling
  usage: {
    primaryFunction: String, // 'worship_service', 'special_events', 'recording', 'streaming'
    schedule: [{
      dayOfWeek: {
        type: String,
        enum: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      },
      startTime: String, // '09:00'
      endTime: String,   // '12:00'
      service: String,   // 'morning_worship', 'evening_service'
      operator: String
    }],
    currentSession: {
      active: Boolean,
      startedAt: Date,
      operator: String,
      service: String
    },
    statistics: {
      hoursOfOperation: Number,
      sessionsCount: Number,
      lastUsed: Date,
      utilizationRate: Number // Percentage
    }
  },
  
  // Maintenance and service
  maintenance: {
    preventiveMaintenance: {
      interval: Number, // Days between maintenance
      lastService: Date,
      nextService: Date,
      technician: String,
      checklist: [String]
    },
    warranty: {
      startDate: Date,
      endDate: Date,
      provider: String,
      terms: String,
      isActive: Boolean
    },
    serviceHistory: [{
      date: Date,
      type: {
        type: String,
        enum: ['installation', 'maintenance', 'repair', 'calibration', 'firmware_update', 'replacement']
      },
      technician: String,
      description: String,
      partsUsed: [String],
      cost: Number,
      duration: Number, // Minutes
      nextServiceDate: Date,
      attachments: [{
        url: String,
        name: String,
        type: String
      }]
    }],
    alerts: [{
      type: {
        type: String,
        enum: ['maintenance_due', 'service_overdue', 'health_warning', 'firmware_available', 'warranty_expiring']
      },
      severity: {
        type: String,
        enum: ['info', 'warning', 'critical'],
        default: 'info'
      },
      message: String,
      createdAt: {
        type: Date,
        default: Date.now
      },
      acknowledged: Boolean,
      acknowledgedBy: String,
      acknowledgedAt: Date
    }]
  },
  
  // Audio/Video settings and presets
  settings: {
    audioSettings: {
      inputGain: Number,
      outputLevel: Number,
      equalizer: [{
        frequency: String,
        gain: Number
      }],
      effects: {
        reverb: Number,
        compression: {
          threshold: Number,
          ratio: Number,
          attack: Number,
          release: Number
        },
        gate: {
          threshold: Number,
          release: Number
        }
      }
    },
    videoSettings: {
      resolution: String,
      frameRate: Number,
      brightness: Number,
      contrast: Number,
      saturation: Number,
      presets: [{
        name: String,
        settings: mongoose.Schema.Types.Mixed
      }]
    },
    presets: [{
      name: String, // 'Sunday Morning Service', 'Evening Worship', 'Conference Mode'
      description: String,
      settings: mongoose.Schema.Types.Mixed,
      createdBy: String,
      createdAt: Date,
      isDefault: Boolean
    }]
  },
  
  // Integration and automation
  integration: {
    controlSystem: {
      platform: String, // 'Crestron', 'AMX', 'Q-SYS', 'Dante Controller'
      address: String,
      commands: [{
        name: String,
        command: String,
        description: String
      }]
    },
    automation: {
      powerOnSequence: [String],
      powerOffSequence: [String],
      servicePresets: [{
        service: String,
        autoActivate: Boolean,
        activationTime: String // '08:45' - 15 minutes before service
      }]
    },
    monitoring: {
      snmpEnabled: Boolean,
      snmpCommunity: String,
      alertsEnabled: Boolean,
      healthCheckInterval: Number // Minutes
    }
  },
  
  // Financial and procurement
  financial: {
    purchaseInfo: {
      purchaseDate: Date,
      vendor: String,
      purchasePrice: Number,
      poNumber: String,
      invoice: String
    },
    depreciation: {
      method: String, // 'straight_line', 'declining_balance'
      usefulLife: Number, // Years
      currentValue: Number,
      depreciationRate: Number
    },
    insurance: {
      provider: String,
      policyNumber: String,
      coverage: Number,
      deductible: Number,
      expirationDate: Date
    },
    replacement: {
      recommendedReplacement: Date,
      budget: Number,
      notes: String
    }
  },
  
  // Documentation and training
  documentation: {
    manuals: [{
      type: String, // 'user_manual', 'service_manual', 'quick_reference'
      url: String,
      version: String
    }],
    diagrams: [{
      type: String, // 'wiring', 'block', 'installation'
      url: String,
      description: String
    }],
    training: [{
      title: String,
      url: String,
      duration: Number, // Minutes
      level: String // 'basic', 'intermediate', 'advanced'
    }],
    troubleshooting: [{
      problem: String,
      solution: String,
      difficulty: String,
      estimatedTime: Number // Minutes
    }]
  },
  
  // Safety and compliance
  safety: {
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low'
    },
    safetyRequirements: [String],
    certifications: [{
      type: String, // 'UL', 'CE', 'FCC', 'RoHS'
      number: String,
      expirationDate: Date
    }],
    trainingRequired: Boolean,
    emergencyProcedures: [String]
  },
  
  // Administrative fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  tags: [String], // For categorization and searching
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  isPortable: {
    type: Boolean,
    default: false
  },
  
  requiresOperator: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Indexes for performance
avEquipmentSchema.index({ equipmentId: 1 }, { unique: true });
avEquipmentSchema.index({ 'location.buildingId': 1, 'location.floorId': 1 });
avEquipmentSchema.index({ category: 1, subcategory: 1 });
avEquipmentSchema.index({ 'status.operational': 1, 'status.health': 1 });
avEquipmentSchema.index({ 'maintenance.preventiveMaintenance.nextService': 1 });
avEquipmentSchema.index({ 
  name: 'text', 
  description: 'text', 
  'specifications.manufacturer': 'text',
  'specifications.model': 'text'
});

// Virtual for equipment display name
avEquipmentSchema.virtual('displayName').get(function() {
  if (this.specifications.manufacturer && this.specifications.model) {
    return `${this.specifications.manufacturer} ${this.specifications.model}`;
  }
  return this.name;
});

// Virtual for maintenance status
avEquipmentSchema.virtual('maintenanceStatus').get(function() {
  if (!this.maintenance.preventiveMaintenance.nextService) return 'no_schedule';
  
  const now = new Date();
  const nextService = new Date(this.maintenance.preventiveMaintenance.nextService);
  const daysDiff = Math.ceil((nextService - now) / (1000 * 60 * 60 * 24));
  
  if (daysDiff < 0) return 'overdue';
  if (daysDiff <= 7) return 'due_soon';
  if (daysDiff <= 30) return 'upcoming';
  return 'current';
});

// Virtual for alert summary
avEquipmentSchema.virtual('alertSummary').get(function() {
  if (!this.maintenance.alerts) return { total: 0, critical: 0, warning: 0 };
  
  const unacknowledged = this.maintenance.alerts.filter(alert => !alert.acknowledged);
  const critical = unacknowledged.filter(alert => alert.severity === 'critical').length;
  const warning = unacknowledged.filter(alert => alert.severity === 'warning').length;
  
  return {
    total: unacknowledged.length,
    critical,
    warning,
    info: unacknowledged.length - critical - warning
  };
});

// Pre-save middleware to update maintenance schedule
avEquipmentSchema.pre('save', function(next) {
  // Auto-calculate next service date if interval is set
  if (this.isModified('maintenance.preventiveMaintenance.lastService') && 
      this.maintenance.preventiveMaintenance.interval) {
    const lastService = new Date(this.maintenance.preventiveMaintenance.lastService);
    const nextService = new Date(lastService);
    nextService.setDate(lastService.getDate() + this.maintenance.preventiveMaintenance.interval);
    this.maintenance.preventiveMaintenance.nextService = nextService;
  }
  
  // Update warranty status
  if (this.maintenance.warranty.endDate) {
    this.maintenance.warranty.isActive = new Date() <= new Date(this.maintenance.warranty.endDate);
  }
  
  next();
});

// Static methods
avEquipmentSchema.statics.findByLocation = function(buildingId, floorId, options = {}) {
  const query = {
    'location.buildingId': buildingId,
    isActive: true
  };
  
  if (floorId) {
    query['location.floorId'] = floorId;
  }
  
  if (options.category) {
    query.category = options.category;
  }
  
  if (options.status) {
    query['status.operational'] = options.status;
  }
  
  return this.find(query).sort({ name: 1 });
};

avEquipmentSchema.statics.findByCategory = function(category, options = {}) {
  const query = { category, isActive: true };
  
  if (options.buildingId) {
    query['location.buildingId'] = options.buildingId;
  }
  
  return this.find(query).sort({ name: 1 });
};

avEquipmentSchema.statics.findRequiringMaintenance = function(daysAhead = 30) {
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + daysAhead);
  
  return this.find({
    'maintenance.preventiveMaintenance.nextService': { $lte: targetDate },
    'status.operational': { $ne: 'retired' },
    isActive: true
  }).sort({ 'maintenance.preventiveMaintenance.nextService': 1 });
};

avEquipmentSchema.statics.findWithAlerts = function(severity = null) {
  const query = {
    'maintenance.alerts': { $exists: true, $ne: [] },
    'maintenance.alerts.acknowledged': false,
    isActive: true
  };
  
  if (severity) {
    query['maintenance.alerts.severity'] = severity;
  }
  
  return this.find(query).sort({ 'maintenance.alerts.createdAt': -1 });
};

// Instance methods
avEquipmentSchema.methods.addServiceRecord = function(serviceData) {
  if (!this.maintenance.serviceHistory) {
    this.maintenance.serviceHistory = [];
  }
  
  this.maintenance.serviceHistory.push({
    ...serviceData,
    date: serviceData.date || new Date()
  });
  
  // Update last service date for preventive maintenance
  if (serviceData.type === 'maintenance') {
    this.maintenance.preventiveMaintenance.lastService = serviceData.date || new Date();
  }
  
  return this.save();
};

avEquipmentSchema.methods.addAlert = function(alertData) {
  if (!this.maintenance.alerts) {
    this.maintenance.alerts = [];
  }
  
  this.maintenance.alerts.push({
    ...alertData,
    createdAt: new Date(),
    acknowledged: false
  });
  
  return this.save();
};

avEquipmentSchema.methods.acknowledgeAlert = function(alertId, acknowledgedBy) {
  const alert = this.maintenance.alerts.id(alertId);
  if (alert) {
    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = new Date();
  }
  
  return this.save();
};

avEquipmentSchema.methods.updateStatus = function(status, notes = '') {
  this.status.operational = status;
  this.status.lastChecked = new Date();
  if (notes) this.status.notes = notes;
  
  return this.save();
};

avEquipmentSchema.methods.startSession = function(operator, service) {
  this.usage.currentSession = {
    active: true,
    startedAt: new Date(),
    operator,
    service
  };
  
  return this.save();
};

avEquipmentSchema.methods.endSession = function() {
  if (this.usage.currentSession.active) {
    const sessionDuration = Date.now() - new Date(this.usage.currentSession.startedAt);
    const hours = sessionDuration / (1000 * 60 * 60);
    
    this.usage.statistics.hoursOfOperation += hours;
    this.usage.statistics.sessionsCount += 1;
    this.usage.statistics.lastUsed = new Date();
    
    this.usage.currentSession.active = false;
  }
  
  return this.save();
};

const AVEquipment = mongoose.model('AVEquipment', avEquipmentSchema);

module.exports = AVEquipment;