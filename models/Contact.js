const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Contact Schema
 * Represents a contact in the building management system phone book
 */
const ContactSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['business', 'maintenance', 'support', 'emergency', 'other'],
    default: 'business'
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    trim: true
  },
  address: {
    street: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    zipCode: { type: String, trim: true },
    country: { type: String, trim: true, default: 'USA' }
  },
  company: {
    type: String,
    trim: true
  },
  groups: [{
    type: String,
    trim: true
  }],
  notes: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
ContactSchema.index({ name: 1 });
ContactSchema.index({ category: 1 });
ContactSchema.index({ company: 1 });
ContactSchema.index({ groups: 1 });

module.exports = mongoose.model('Contact', ContactSchema);