const mongoose = require('mongoose');

/**
 * WiFiAccessPoint Schema
 * Enhanced access point tracking for floor plan integration
 * Builds on existing UniFi Network integration with location-specific data
 * Part of Phase 5 - Wi-Fi coverage & APs
 */
const wifiAccessPointSchema = new mongoose.Schema({
  // UniFi integration
  deviceId: {
    type: String, // MAC address from UniFi
    required: true,
    unique: true,
    index: true,
    uppercase: true,
    match: /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/
  },
  unifiSiteId: {
    type: String,
    required: true
  },
  
  // Location information
  buildingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Building',
    required: true,
    index: true
  },
  floorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Floor',
    required: true,
    index: true
  },
  room: {
    type: String,
    trim: true,
    maxlength: 100
  },
  
  // Floor plan positioning
  position: {
    x: { type: Number, required: true }, // X coordinate on floor plan
    y: { type: Number, required: true }, // Y coordinate on floor plan
    z: { type: Number, default: 0 },     // Height/floor level
    rotation: { type: Number, default: 0, min: 0, max: 360 } // Rotation angle
  },
  
  // Physical mounting information
  mounting: {
    type: {
      type: String,
      enum: ['ceiling', 'wall', 'pole', 'desk', 'other'],
      default: 'ceiling'
    },
    height: { type: Number, min: 0, max: 50 }, // Height in meters
    orientation: {
      type: String,
      enum: ['horizontal', 'vertical', 'angled'],
      default: 'horizontal'
    },
    notes: String
  },
  
  // Device information (cached from UniFi)
  deviceInfo: {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    model: String,
    firmwareVersion: String,
    serialNumber: String,
    adoptedAt: Date,
    lastSeen: Date,
    
    // Hardware capabilities
    capabilities: {
      supports5GHz: { type: Boolean, default: true },
      supports6GHz: { type: Boolean, default: false },
      maxSpatialStreams: { type: Number, min: 1, max: 8, default: 2 },
      wifiStandard: {
        type: String,
        enum: ['802.11n', '802.11ac', '802.11ax', '802.11be'],
        default: '802.11ac'
      },
      maxThroughput: Number, // Mbps
      antennaGain: Number    // dBi
    }
  },
  
  // Current operational status (cached from UniFi)
  status: {
    state: {
      type: String,
      enum: ['connected', 'disconnected', 'adopting', 'upgrading', 'error'],
      default: 'disconnected'
    },
    uptime: Number, // Seconds
    cpuUsage: { type: Number, min: 0, max: 100 },
    memoryUsage: { type: Number, min: 0, max: 100 },
    temperature: Number, // Celsius
    
    // Network connectivity
    ipAddress: String,
    gateway: String,
    dns: [String],
    
    // Power and health
    powerSource: {
      type: String,
      enum: ['poe', 'poe_plus', 'poe_plus_plus', 'ac_adapter'],
      default: 'poe'
    },
    powerConsumption: Number, // Watts
    voltageReading: Number,   // Volts
    ledOverride: {
      type: String,
      enum: ['default', 'off', 'on', 'locating'],
      default: 'default'
    }
  },
  
  // Wireless configuration and performance
  wireless: {
    // Radio configurations
    radios: [{
      band: {
        type: String,
        enum: ['2.4GHz', '5GHz', '6GHz'],
        required: true
      },
      channel: { type: Number, min: 1, max: 196 },
      channelWidth: {
        type: Number,
        enum: [20, 40, 80, 160],
        default: 80
      },
      txPower: { type: Number, min: 1, max: 30 }, // dBm
      enabled: { type: Boolean, default: true },
      
      // Performance metrics
      utilization: { type: Number, min: 0, max: 100 },
      noise: Number, // dBm
      interference: { type: Number, min: 0, max: 100 }
    }],
    
    // SSIDs broadcast by this AP
    ssids: [{
      name: { type: String, required: true },
      enabled: { type: Boolean, default: true },
      hidden: { type: Boolean, default: false },
      bandSteering: { type: Boolean, default: true },
      clientLimit: { type: Number, min: 0, max: 1000, default: 100 },
      vlan: Number
    }],
    
    // Client information
    clientStats: {
      total: { type: Number, default: 0 },
      connected2_4GHz: { type: Number, default: 0 },
      connected5GHz: { type: Number, default: 0 },
      connected6GHz: { type: Number, default: 0 },
      peakConnections: { type: Number, default: 0 },
      peakConnectionsTime: Date
    },
    
    // Traffic statistics
    traffic: {
      txBytes: { type: Number, default: 0 },
      rxBytes: { type: Number, default: 0 },
      txPackets: { type: Number, default: 0 },
      rxPackets: { type: Number, default: 0 },
      txErrors: { type: Number, default: 0 },
      rxErrors: { type: Number, default: 0 },
      lastReset: Date
    }
  },
  
  // Coverage prediction and analysis
  coverage: {
    estimatedRange: {
      radius2_4GHz: { type: Number, min: 0, max: 500 }, // meters
      radius5GHz: { type: Number, min: 0, max: 300 },   // meters
      radius6GHz: { type: Number, min: 0, max: 100 }    // meters
    },
    
    // Coverage areas this AP contributes to
    coverageAreas: [{
      coverageId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'WiFiCoverage'
      },
      contribution: { type: Number, min: 0, max: 100 } // Percentage
    }],
    
    // Interference and optimization
    nearbyAPs: [{
      deviceId: String,
      distance: Number, // meters
      signalStrength: Number, // dBm
      channel: Number,
      band: String,
      interferenceLevel: {
        type: String,
        enum: ['none', 'low', 'medium', 'high'],
        default: 'none'
      }
    }],
    
    // Performance analysis
    analysis: {
      channelOptimization: {
        type: String,
        enum: ['optimal', 'suboptimal', 'poor'],
        default: 'optimal'
      },
      powerOptimization: {
        type: String,
        enum: ['optimal', 'too_high', 'too_low'],
        default: 'optimal'
      },
      placementScore: { type: Number, min: 0, max: 100, default: 75 },
      recommendations: [String]
    }
  },
  
  // Event and scheduling information
  eventOptimization: {
    recommendedFor: [{
      type: String,
      enum: ['conference', 'worship', 'youth_event', 'wedding', 'general_use', 'live_stream']
    }],
    
    // Load balancing settings
    loadBalancing: {
      enabled: { type: Boolean, default: true },
      rssiThreshold: { type: Number, min: -90, max: -30, default: -70 },
      bandPreference: {
        type: String,
        enum: ['auto', 'prefer_5ghz', 'prefer_2_4ghz'],
        default: 'prefer_5ghz'
      }
    },
    
    // Guest access configuration
    guestAccess: {
      enabled: { type: Boolean, default: false },
      bandwidthLimitDown: Number, // Mbps
      bandwidthLimitUp: Number,   // Mbps
      sessionTimeout: Number,     // Minutes
      portalRedirect: String      // URL
    }
  },
  
  // Maintenance and alerts
  maintenance: {
    lastMaintenanceDate: Date,
    nextMaintenanceDate: Date,
    maintenanceNotes: String,
    
    // Health monitoring
    alerts: [{
      type: {
        type: String,
        enum: ['high_temperature', 'low_memory', 'high_cpu', 'interference', 'poor_signal', 'offline'],
        required: true
      },
      severity: {
        type: String,
        enum: ['info', 'warning', 'critical'],
        default: 'warning'
      },
      message: String,
      firstSeen: { type: Date, default: Date.now },
      lastSeen: { type: Date, default: Date.now },
      acknowledged: { type: Boolean, default: false },
      acknowledgedBy: String,
      acknowledgedAt: Date
    }],
    
    // Performance thresholds
    thresholds: {
      maxTemperature: { type: Number, default: 70 }, // Celsius
      maxCpuUsage: { type: Number, default: 80 },     // Percentage
      maxMemoryUsage: { type: Number, default: 90 },  // Percentage
      minSignalStrength: { type: Number, default: -70 } // dBm
    }
  },
  
  // Icon and visualization on floor plan
  floorplanIcon: {
    iconId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'FloorplanIcon'
    },
    customIcon: String, // Custom icon name/path
    showCoverage: { type: Boolean, default: true },
    showClients: { type: Boolean, default: true },
    showStatus: { type: Boolean, default: true }
  },
  
  // Data synchronization
  sync: {
    lastUnifiSync: Date,
    syncEnabled: { type: Boolean, default: true },
    syncErrors: [String],
    autoUpdate: { type: Boolean, default: true }
  },
  
  // Administrative
  isManaged: { type: Boolean, default: true },
  isEnabled: { type: Boolean, default: true },
  notes: String,
  tags: [String]
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
wifiAccessPointSchema.index({ buildingId: 1, floorId: 1 });
wifiAccessPointSchema.index({ buildingId: 1, floorId: 1, 'status.state': 1 });
wifiAccessPointSchema.index({ deviceId: 1, unifiSiteId: 1 });
wifiAccessPointSchema.index({ 'wireless.ssids.name': 1 });
wifiAccessPointSchema.index({ 'position.x': 1, 'position.y': 1 });
wifiAccessPointSchema.index({ isEnabled: 1, isManaged: 1 });

// Geospatial index for location queries
wifiAccessPointSchema.index({ 
  buildingId: 1, 
  floorId: 1, 
  'position.x': 1, 
  'position.y': 1 
}, { name: 'location_2d' });

// Virtual for overall health score
wifiAccessPointSchema.virtual('healthScore').get(function() {
  if (this.status.state !== 'connected') return 0;
  
  let score = 100;
  
  // Deduct for high resource usage
  if (this.status.cpuUsage > this.maintenance.thresholds.maxCpuUsage) score -= 20;
  if (this.status.memoryUsage > this.maintenance.thresholds.maxMemoryUsage) score -= 20;
  if (this.status.temperature > this.maintenance.thresholds.maxTemperature) score -= 30;
  
  // Deduct for active alerts
  const criticalAlerts = this.maintenance.alerts.filter(a => 
    a.severity === 'critical' && !a.acknowledged).length;
  const warningAlerts = this.maintenance.alerts.filter(a => 
    a.severity === 'warning' && !a.acknowledged).length;
  
  score -= (criticalAlerts * 15) + (warningAlerts * 5);
  
  return Math.max(0, score);
});

// Virtual for client density
wifiAccessPointSchema.virtual('clientDensity').get(function() {
  const totalClients = this.wireless.clientStats.total;
  const estimatedCoverage = this.coverage.estimatedRange.radius5GHz || 50;
  const coverageArea = Math.PI * Math.pow(estimatedCoverage, 2); // m²
  
  return totalClients / (coverageArea / 100); // clients per 100m²
});

// Virtual for event readiness
wifiAccessPointSchema.virtual('eventReadiness').get(function() {
  const health = this.healthScore;
  const placement = this.coverage.analysis.placementScore;
  const load = Math.max(0, 100 - (this.wireless.clientStats.total / 100 * 100));
  
  return Math.round((health + placement + load) / 3);
});

// Static method to find APs by location
wifiAccessPointSchema.statics.findByLocation = function(buildingId, floorId, options = {}) {
  const query = { buildingId, floorId, isEnabled: true };
  
  if (options.includeOffline !== true) {
    query['status.state'] = 'connected';
  }
  
  if (options.room) {
    query.room = options.room;
  }
  
  if (options.ssid) {
    query['wireless.ssids.name'] = options.ssid;
  }
  
  return this.find(query).populate('floorplanIcon.iconId');
};

// Static method to find APs near a point
wifiAccessPointSchema.statics.findNearPoint = function(buildingId, floorId, x, y, radius = 50) {
  return this.find({
    buildingId,
    floorId,
    isEnabled: true,
    'status.state': 'connected',
    'position.x': { $gte: x - radius, $lte: x + radius },
    'position.y': { $gte: y - radius, $lte: y + radius }
  }).sort({ 
    // Sort by distance (approximate)
    $expr: {
      $add: [
        { $pow: [{ $subtract: ['$position.x', x] }, 2] },
        { $pow: [{ $subtract: ['$position.y', y] }, 2] }
      ]
    }
  });
};

// Static method to get floor AP summary
wifiAccessPointSchema.statics.getFloorSummary = async function(buildingId, floorId) {
  const aps = await this.find({ buildingId, floorId, isEnabled: true });
  
  if (aps.length === 0) {
    return {
      totalAPs: 0,
      connectedAPs: 0,
      averageHealth: 0,
      totalClients: 0,
      eventReadiness: 'No APs'
    };
  }
  
  const connectedAPs = aps.filter(ap => ap.status.state === 'connected');
  const totalClients = aps.reduce((sum, ap) => sum + ap.wireless.clientStats.total, 0);
  const avgHealth = connectedAPs.reduce((sum, ap) => sum + ap.healthScore, 0) / (connectedAPs.length || 1);
  const avgEventReadiness = connectedAPs.reduce((sum, ap) => sum + ap.eventReadiness, 0) / (connectedAPs.length || 1);
  
  return {
    totalAPs: aps.length,
    connectedAPs: connectedAPs.length,
    averageHealth: Math.round(avgHealth),
    totalClients,
    eventReadiness: avgEventReadiness >= 80 ? 'Excellent' :
                   avgEventReadiness >= 60 ? 'Good' :
                   avgEventReadiness >= 40 ? 'Fair' : 'Poor',
    detailedStats: {
      bands: {
        '2.4GHz': aps.filter(ap => ap.wireless.radios.some(r => r.band === '2.4GHz' && r.enabled)).length,
        '5GHz': aps.filter(ap => ap.wireless.radios.some(r => r.band === '5GHz' && r.enabled)).length,
        '6GHz': aps.filter(ap => ap.wireless.radios.some(r => r.band === '6GHz' && r.enabled)).length
      },
      alerts: {
        critical: aps.reduce((sum, ap) => sum + ap.maintenance.alerts.filter(a => 
          a.severity === 'critical' && !a.acknowledged).length, 0),
        warning: aps.reduce((sum, ap) => sum + ap.maintenance.alerts.filter(a => 
          a.severity === 'warning' && !a.acknowledged).length, 0)
      }
    }
  };
};

// Instance method to update from UniFi data
wifiAccessPointSchema.methods.updateFromUnifiData = function(unifiData) {
  // Update device info
  this.deviceInfo.name = unifiData.name || this.deviceInfo.name;
  this.deviceInfo.model = unifiData.model || this.deviceInfo.model;
  this.deviceInfo.firmwareVersion = unifiData.version || this.deviceInfo.firmwareVersion;
  this.deviceInfo.serialNumber = unifiData.serial || this.deviceInfo.serialNumber;
  this.deviceInfo.lastSeen = unifiData.last_seen ? new Date(unifiData.last_seen * 1000) : this.deviceInfo.lastSeen;
  
  // Update status
  this.status.state = unifiData.state === 1 ? 'connected' : 'disconnected';
  this.status.uptime = unifiData.uptime || 0;
  this.status.ipAddress = unifiData.ip || this.status.ipAddress;
  
  // Update wireless stats if available
  if (unifiData.stat) {
    this.wireless.clientStats.total = unifiData.stat.user_num_sta || 0;
    this.wireless.traffic.txBytes = unifiData.stat.tx_bytes || 0;
    this.wireless.traffic.rxBytes = unifiData.stat.rx_bytes || 0;
  }
  
  // Update sync timestamp
  this.sync.lastUnifiSync = new Date();
  
  return this;
};

// Instance method to calculate estimated coverage
wifiAccessPointSchema.methods.calculateCoverage = function() {
  const baseRange = {
    '2.4GHz': 150, // meters
    '5GHz': 100,   // meters  
    '6GHz': 50     // meters
  };
  
  // Adjust based on mounting height and power
  const heightFactor = Math.max(0.5, Math.min(2, (this.mounting.height || 3) / 3));
  
  this.wireless.radios.forEach(radio => {
    if (radio.enabled && baseRange[radio.band]) {
      const powerFactor = (radio.txPower || 20) / 20; // Normalize to typical 20dBm
      const adjustedRange = baseRange[radio.band] * heightFactor * powerFactor;
      
      if (radio.band === '2.4GHz') this.coverage.estimatedRange.radius2_4GHz = Math.round(adjustedRange);
      if (radio.band === '5GHz') this.coverage.estimatedRange.radius5GHz = Math.round(adjustedRange);
      if (radio.band === '6GHz') this.coverage.estimatedRange.radius6GHz = Math.round(adjustedRange);
    }
  });
  
  return this.coverage.estimatedRange;
};

// Pre-save middleware
wifiAccessPointSchema.pre('save', function(next) {
  // Calculate coverage if radios are configured
  if (this.wireless.radios.length > 0) {
    this.calculateCoverage();
  }
  
  // Update device capabilities based on model
  if (this.deviceInfo.model && !this.deviceInfo.capabilities.wifiStandard) {
    if (this.deviceInfo.model.includes('U6') || this.deviceInfo.model.includes('U7')) {
      this.deviceInfo.capabilities.wifiStandard = '802.11ax';
      this.deviceInfo.capabilities.supports6GHz = this.deviceInfo.model.includes('U6E') || this.deviceInfo.model.includes('U7');
    } else if (this.deviceInfo.model.includes('AC')) {
      this.deviceInfo.capabilities.wifiStandard = '802.11ac';
    }
  }
  
  next();
});

module.exports = mongoose.model('WiFiAccessPoint', wifiAccessPointSchema);