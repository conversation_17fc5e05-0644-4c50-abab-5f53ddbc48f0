const mongoose = require('mongoose');

/**
 * UtilityShutoff model for Phase 6 - Water & Gas Shutoffs
 * Tracks water mains, zone valves, gas shutoffs, and emergency procedures
 */
const utilityShutoffSchema = new mongoose.Schema({
  // Basic identification
  shutoffId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 50
  },
  
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  // Location and building association
  buildingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Building',
    required: true
  },
  
  floorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Floor',
    required: true
  },
  
  room: {
    type: String,
    trim: true,
    maxlength: 100
  },
  
  // Position on floor plan
  position: {
    x: { type: Number, min: 0, max: 100 }, // Percentage from left
    y: { type: Number, min: 0, max: 100 }, // Percentage from top
    z: { type: Number, default: 0 }, // Floor level offset
    rotation: { type: Number, default: 0, min: 0, max: 360 }
  },
  
  // Shutoff type and specifications
  shutoffType: {
    type: String,
    required: true,
    enum: [
      'water_main',
      'water_zone', 
      'water_emergency',
      'gas_main',
      'gas_zone',
      'gas_emergency',
      'steam_main',
      'steam_zone',
      'compressed_air',
      'other'
    ]
  },
  
  // Technical specifications
  specifications: {
    valveType: {
      type: String,
      enum: ['ball_valve', 'gate_valve', 'globe_valve', 'butterfly_valve', 'plug_valve', 'other']
    },
    size: String, // "3/4 inch", "2 inch", etc.
    material: {
      type: String,
      enum: ['brass', 'copper', 'steel', 'pvc', 'cast_iron', 'stainless_steel', 'other']
    },
    pressure: String, // "50 PSI", "High Pressure", etc.
    manufacturer: String,
    model: String,
    serialNumber: String,
    installDate: Date
  },
  
  // Coverage and affected areas
  coverage: {
    affectedAreas: [String], // Rooms, zones, or areas controlled by this shutoff
    affectedBuildings: [String], // If shutoff affects multiple buildings
    description: String, // "Controls water to east wing bathrooms"
    priority: {
      type: String,
      enum: ['critical', 'high', 'medium', 'low'],
      default: 'medium'
    }
  },
  
  // Emergency procedures and documentation
  procedures: {
    shutoffInstructions: String, // Step-by-step shutdown procedure
    turnOnInstructions: String, // Step-by-step startup procedure
    emergencyContact: String, // Who to call for this utility
    specialTools: [String], // Required tools: ["water meter key", "pipe wrench"]
    warnings: [String], // Safety warnings and precautions
    photos: [{
      url: String,
      caption: String,
      type: { type: String, enum: ['location', 'valve', 'instructions', 'diagram'] }
    }]
  },
  
  // Status and operational information
  status: {
    currentState: {
      type: String,
      enum: ['open', 'closed', 'partially_open', 'unknown', 'maintenance'],
      default: 'unknown'
    },
    lastChecked: Date,
    checkedBy: String,
    isAccessible: {
      type: Boolean,
      default: true
    },
    accessNotes: String, // "Requires key", "Behind locked panel", etc.
    condition: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor', 'needs_replacement'],
      default: 'good'
    }
  },
  
  // Maintenance and inspection tracking
  maintenance: {
    lastInspection: Date,
    nextInspection: Date,
    inspectionFrequency: {
      type: Number,
      default: 365 // Days between inspections
    },
    inspector: String,
    lastMaintenance: Date,
    nextMaintenance: Date,
    maintenanceNotes: String
  },
  
  // Service history
  serviceHistory: [{
    date: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['inspection', 'maintenance', 'repair', 'replacement', 'emergency_use', 'testing'],
      required: true
    },
    technician: String,
    description: String,
    beforeState: String,
    afterState: String,
    partsReplaced: [String],
    cost: Number,
    nextServiceDate: Date,
    attachments: [{
      url: String,
      name: String,
      type: String
    }]
  }],
  
  // Emergency and alert information
  alerts: [{
    type: {
      type: String,
      enum: ['inspection_due', 'maintenance_due', 'needs_repair', 'inaccessible', 'emergency_used']
    },
    severity: {
      type: String,
      enum: ['info', 'warning', 'critical'],
      default: 'info'
    },
    message: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    acknowledged: {
      type: Boolean,
      default: false
    },
    acknowledgedBy: String,
    acknowledgedAt: Date
  }],
  
  // Integration with other systems
  integration: {
    iconId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'FloorplanIcon'
    },
    qrCode: String, // For mobile access to shutoff information
    rfidTag: String, // Physical tag for identification
    externalSystemId: String, // Integration with building automation
    monitoringEnabled: {
      type: Boolean,
      default: false
    }
  },
  
  // Administrative fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  tags: [String], // For categorization and searching
  
  notes: String,
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for performance
utilityShutoffSchema.index({ buildingId: 1, floorId: 1 });
utilityShutoffSchema.index({ shutoffType: 1 });
utilityShutoffSchema.index({ 'coverage.priority': 1 });
utilityShutoffSchema.index({ 'status.currentState': 1 });
utilityShutoffSchema.index({ 'maintenance.nextInspection': 1 });
utilityShutoffSchema.index({ shutoffId: 1 }, { unique: true });
utilityShutoffSchema.index({ name: 'text', 'coverage.description': 'text' });

// Virtual for overdue inspections
utilityShutoffSchema.virtual('isInspectionOverdue').get(function() {
  if (!this.maintenance.nextInspection) return false;
  return new Date() > this.maintenance.nextInspection;
});

// Virtual for emergency priority display
utilityShutoffSchema.virtual('emergencyPriority').get(function() {
  const priorityMap = {
    'water_main': 'critical',
    'gas_main': 'critical', 
    'water_emergency': 'critical',
    'gas_emergency': 'critical',
    'water_zone': 'high',
    'gas_zone': 'high',
    'steam_main': 'high'
  };
  return priorityMap[this.shutoffType] || this.coverage.priority;
});

// Virtual for utility type display
utilityShutoffSchema.virtual('utilityType').get(function() {
  if (this.shutoffType.startsWith('water')) return 'water';
  if (this.shutoffType.startsWith('gas')) return 'gas';
  if (this.shutoffType.startsWith('steam')) return 'steam';
  return 'other';
});

// Pre-save middleware to calculate next inspection date
utilityShutoffSchema.pre('save', function(next) {
  if (this.maintenance.lastInspection && this.maintenance.inspectionFrequency) {
    const nextDate = new Date(this.maintenance.lastInspection);
    nextDate.setDate(nextDate.getDate() + this.maintenance.inspectionFrequency);
    this.maintenance.nextInspection = nextDate;
  }
  next();
});

// Static methods
utilityShutoffSchema.statics.findByLocation = function(buildingId, floorId) {
  return this.find({ buildingId, floorId, isActive: true })
    .populate('integration.iconId')
    .sort({ 'coverage.priority': -1, shutoffType: 1 });
};

utilityShutoffSchema.statics.findByType = function(shutoffType) {
  return this.find({ shutoffType, isActive: true })
    .populate('buildingId floorId')
    .sort({ 'coverage.priority': -1 });
};

utilityShutoffSchema.statics.findOverdueInspections = function() {
  const today = new Date();
  return this.find({
    'maintenance.nextInspection': { $lt: today },
    isActive: true
  }).populate('buildingId floorId');
};

utilityShutoffSchema.statics.findCriticalShutoffs = function() {
  return this.find({
    $or: [
      { 'coverage.priority': 'critical' },
      { shutoffType: { $in: ['water_main', 'gas_main', 'water_emergency', 'gas_emergency'] } }
    ],
    isActive: true
  }).populate('buildingId floorId');
};

// Instance methods
utilityShutoffSchema.methods.recordInspection = function(inspector, notes = '') {
  this.maintenance.lastInspection = new Date();
  this.maintenance.inspector = inspector;
  
  // Calculate next inspection date
  if (this.maintenance.inspectionFrequency) {
    const nextDate = new Date();
    nextDate.setDate(nextDate.getDate() + this.maintenance.inspectionFrequency);
    this.maintenance.nextInspection = nextDate;
  }
  
  // Add to service history
  this.serviceHistory.push({
    type: 'inspection',
    technician: inspector,
    description: notes || 'Routine inspection completed'
  });
  
  return this.save();
};

utilityShutoffSchema.methods.recordEmergencyUse = function(usedBy, reason) {
  // Add alert for emergency use
  this.alerts.push({
    type: 'emergency_used',
    severity: 'critical',
    message: `Emergency shutoff activated: ${reason}`
  });
  
  // Add to service history
  this.serviceHistory.push({
    type: 'emergency_use',
    technician: usedBy,
    description: `Emergency shutoff used: ${reason}`,
    beforeState: this.status.currentState,
    afterState: 'closed'
  });
  
  this.status.currentState = 'closed';
  this.status.lastChecked = new Date();
  this.status.checkedBy = usedBy;
  
  return this.save();
};

const UtilityShutoff = mongoose.model('UtilityShutoff', utilityShutoffSchema);

module.exports = UtilityShutoff;