const mongoose = require('mongoose');

const WidgetSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  position: {
    x: { type: Number, required: true },
    y: { type: Number, required: true },
    w: { type: Number, required: true },
    h: { type: Number, required: true }
  },
  settings: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  isVisible: {
    type: Boolean,
    default: true
  }
});

const DashboardPreferenceSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  widgets: {
    type: [WidgetSchema],
    default: []
  },
  layout: {
    type: String,
    enum: ['grid', 'list'],
    default: 'grid'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to update the updatedAt field
DashboardPreferenceSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to get or create dashboard preferences for a user
DashboardPreferenceSchema.statics.getOrCreateForUser = async function(userId) {
  let preferences = await this.findOne({ user: userId });
  
  if (!preferences) {
    // Create default preferences with default widgets
    preferences = new this({
      user: userId,
      widgets: [
        {
          type: 'shortcuts',
          title: 'Popular Shortcuts',
          position: { x: 0, y: 0, w: 6, h: 2 },
          settings: {}
        },
        {
          type: 'recentFiles',
          title: 'Recent Files',
          position: { x: 6, y: 0, w: 6, h: 2 },
          settings: {}
        },
        {
          type: 'wiim',
          title: 'WiiM Media Player',
          position: { x: 0, y: 2, w: 12, h: 3 },
          settings: {
            refreshInterval: 10
          }
        },
        {
          type: 'quickActions',
          title: 'Quick Actions',
          position: { x: 0, y: 5, w: 12, h: 4 },
          settings: {}
        }
      ]
    });
    await preferences.save();
  }
  
  return preferences;
};

module.exports = mongoose.model('DashboardPreference', DashboardPreferenceSchema);