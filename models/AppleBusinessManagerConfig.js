const mongoose = require('mongoose');

const AppleBusinessManagerConfigSchema = new mongoose.Schema({
  clientId: {
    type: String,
    required: true
  },
  clientSecret: {
    type: String,
    required: true
  },
  organizationId: {
    type: String,
    required: true
  },
  keyId: {
    type: String,
    required: true,
    description: 'The Key ID from Apple Business Manager API account'
  },
  privateKeyPath: {
    type: String,
    required: true,
    description: 'Path to the private key file for JWT authentication'
  },
  issuerId: {
    type: String,
    required: true,
    description: 'The Issuer ID from Apple Business Manager API account'
  },
  tokenExpiry: {
    type: Number,
    default: 1200, // 20 minutes in seconds
    description: 'Token expiration time in seconds'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
AppleBusinessManagerConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('appleBusinessManagerConfig', AppleBusinessManagerConfigSchema);
