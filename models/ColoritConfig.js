const mongoose = require('mongoose');

/**
 * Colorlit Configuration Schema
 * Stores configuration for the Colorlit Z4 Pro LED Controller
 */
const ColoritConfigSchema = new mongoose.Schema({
  host: {
    type: String,
    required: true,
    trim: true
  },
  port: {
    type: Number,
    default: 80
  },
  apiKey: {
    type: String,
    default: ''
  },
  username: {
    type: String,
    default: ''
  },
  password: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Add index on updatedAt for sorting
ColoritConfigSchema.index({ updatedAt: -1 });

module.exports = mongoose.model('ColoritConfig', ColoritConfigSchema);