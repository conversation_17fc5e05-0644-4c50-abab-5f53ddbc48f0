const mongoose = require('mongoose');

const LenelS2NetBoxConfigSchema = new mongoose.Schema({
  host: {
    type: String,
    required: true
  },
  username: {
    type: String,
    required: true
  },
  password: {
    type: String,
    required: true
  },
  port: {
    type: Number,
    default: 443
  },
  localNetwork: {
    type: Boolean,
    default: true,
    description: 'Indicates if the Lenel S2 NetBox is on the local network'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
LenelS2NetBoxConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('lenelS2NetBoxConfig', LenelS2NetBoxConfigSchema);