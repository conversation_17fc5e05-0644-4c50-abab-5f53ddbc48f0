const mongoose = require('mongoose');

/**
 * Schema for storing default role settings
 * This replaces the environment variables:
 * - DEFAULT_ROLE_LOCAL_USERS
 * - DEFAULT_ROLE_GOOGLE_USERS
 * - GOOGLE_GROUPS_ROLE_MAPPING
 */
const RoleSettingsSchema = new mongoose.Schema({
  // Default role for new local users
  defaultRoleLocalUsers: {
    type: String,
    default: 'user',
    required: true
  },
  
  // Default role for new Google users
  defaultRoleGoogleUsers: {
    type: String,
    default: 'user',
    required: true
  },
  
  // Mapping of Google Groups to roles
  // Stored as an array of objects with groupEmail and roleName
  googleGroupsRoleMapping: [{
    groupEmail: {
      type: String,
      required: true
    },
    roleName: {
      type: String,
      required: true
    }
  }],
  
  // Timestamps
  updatedAt: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
});

// Pre-save middleware to update the updatedAt field
RoleSettingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to get the current settings
// Creates default settings if none exist
RoleSettingsSchema.statics.getCurrentSettings = async function() {
  let settings = await this.findOne();
  
  if (!settings) {
    // Create default settings
    settings = await this.create({
      defaultRoleLocalUsers: 'user',
      defaultRoleGoogleUsers: 'user',
      googleGroupsRoleMapping: []
    });
  }
  
  return settings;
};

// Static method to update settings
RoleSettingsSchema.statics.updateSettings = async function(newSettings, userId) {
  let settings = await this.findOne();
  
  if (!settings) {
    // Create new settings
    settings = new this({
      defaultRoleLocalUsers: newSettings.defaultRoleLocalUsers || 'user',
      defaultRoleGoogleUsers: newSettings.defaultRoleGoogleUsers || 'user',
      googleGroupsRoleMapping: newSettings.googleGroupsRoleMapping || [],
      updatedBy: userId
    });
  } else {
    // Update existing settings
    settings.defaultRoleLocalUsers = newSettings.defaultRoleLocalUsers || settings.defaultRoleLocalUsers;
    settings.defaultRoleGoogleUsers = newSettings.defaultRoleGoogleUsers || settings.defaultRoleGoogleUsers;
    settings.googleGroupsRoleMapping = newSettings.googleGroupsRoleMapping || settings.googleGroupsRoleMapping;
    settings.updatedBy = userId;
  }
  
  await settings.save();
  return settings;
};

module.exports = mongoose.model('RoleSettings', RoleSettingsSchema);