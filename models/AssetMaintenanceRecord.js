const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Maintenance Record Schema
 * Tracks maintenance activities performed on assets
 */
const AssetMaintenanceRecordSchema = new Schema({
  asset: {
    type: Schema.Types.ObjectId,
    ref: 'Asset',
    required: true
  },
  workOrder: {
    type: Schema.Types.ObjectId,
    ref: 'AssetWorkOrder'
  },

  // Maintenance details
  maintenanceType: {
    type: String,
    enum: [
      'preventive',
      'corrective',
      'emergency',
      'inspection',
      'calibration',
      'upgrade',
      'replacement',
      'cleaning',
      'other'
    ],
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  
  // Scheduling
  scheduledDate: {
    type: Date,
    required: true
  },
  startDate: {
    type: Date
  },
  completedDate: {
    type: Date
  },
  estimatedDuration: {
    type: Number, // Duration in minutes
    min: 0
  },
  actualDuration: {
    type: Number, // Duration in minutes
    min: 0
  },

  // Status and priority
  status: {
    type: String,
    enum: ['scheduled', 'in_progress', 'completed', 'cancelled', 'deferred'],
    default: 'scheduled'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },

  // Personnel
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  performedBy: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['technician', 'supervisor', 'contractor', 'vendor']
    }
  }],

  // Parts and materials
  partsUsed: [{
    partNumber: String,
    partName: String,
    quantity: {
      type: Number,
      min: 0
    },
    cost: {
      type: Number,
      min: 0
    },
    vendor: String
  }],

  // Costs
  laborCost: {
    type: Number,
    min: 0
  },
  partsCost: {
    type: Number,
    min: 0
  },
  otherCosts: {
    type: Number,
    min: 0
  },
  totalCost: {
    type: Number,
    min: 0
  },

  // Documentation
  workPerformed: {
    type: String
  },
  findings: {
    type: String
  },
  recommendations: {
    type: String
  },
  followUpRequired: {
    type: Boolean,
    default: false
  },
  followUpDate: {
    type: Date
  },
  followUpNotes: {
    type: String
  },

  // Attachments
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Asset condition after maintenance
  conditionBefore: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'needs_repair']
  },
  conditionAfter: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'needs_repair']
  },

  // Vendor information
  vendor: {
    name: String,
    contact: String,
    phone: String,
    email: String
  },

  // Quality and safety
  qualityCheck: {
    passed: Boolean,
    checkedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    checkedDate: Date,
    notes: String
  },
  safetyIncidents: [{
    type: String,
    severity: {
      type: String,
      enum: ['minor', 'moderate', 'serious', 'critical']
    },
    description: String,
    reportedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],

  // Tracking
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
AssetMaintenanceRecordSchema.index({ asset: 1 });
AssetMaintenanceRecordSchema.index({ status: 1 });
AssetMaintenanceRecordSchema.index({ maintenanceType: 1 });
AssetMaintenanceRecordSchema.index({ scheduledDate: 1 });
AssetMaintenanceRecordSchema.index({ assignedTo: 1 });
AssetMaintenanceRecordSchema.index({ priority: 1 });

// Virtual for overdue status
AssetMaintenanceRecordSchema.virtual('isOverdue').get(function() {
  return this.status === 'scheduled' && this.scheduledDate < new Date();
});

// Virtual for cost efficiency
AssetMaintenanceRecordSchema.virtual('costEfficiency').get(function() {
  if (!this.estimatedDuration || !this.actualDuration || !this.totalCost) return null;
  const timeEfficiency = this.estimatedDuration / this.actualDuration;
  return {
    timeEfficiency,
    costPerMinute: this.totalCost / this.actualDuration,
    overUnderTime: this.actualDuration - this.estimatedDuration
  };
});

// Pre-save middleware to calculate total cost
AssetMaintenanceRecordSchema.pre('save', function(next) {
  if (this.isModified(['laborCost', 'partsCost', 'otherCosts'])) {
    this.totalCost = (this.laborCost || 0) + (this.partsCost || 0) + (this.otherCosts || 0);
  }
  
  if (this.partsUsed && this.partsUsed.length > 0) {
    this.partsCost = this.partsUsed.reduce((total, part) => {
      return total + ((part.cost || 0) * (part.quantity || 0));
    }, 0);
    this.totalCost = (this.laborCost || 0) + this.partsCost + (this.otherCosts || 0);
  }

  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }
  
  next();
});

// Static methods
AssetMaintenanceRecordSchema.statics.findByAsset = function(assetId, options = {}) {
  const query = { asset: assetId };
  if (options.status) query.status = options.status;
  if (options.maintenanceType) query.maintenanceType = options.maintenanceType;
  
  return this.find(query)
    .populate('assignedTo', 'name email')
    .populate('performedBy.user', 'name email')
    .sort({ scheduledDate: -1 });
};

AssetMaintenanceRecordSchema.statics.findOverdue = function() {
  return this.find({
    status: 'scheduled',
    scheduledDate: { $lt: new Date() }
  })
    .populate('asset', 'name assetTag')
    .populate('assignedTo', 'name email')
    .sort({ scheduledDate: 1 });
};

AssetMaintenanceRecordSchema.statics.findUpcoming = function(days = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    status: 'scheduled',
    scheduledDate: { 
      $gte: new Date(),
      $lte: futureDate 
    }
  })
    .populate('asset', 'name assetTag')
    .populate('assignedTo', 'name email')
    .sort({ scheduledDate: 1 });
};

AssetMaintenanceRecordSchema.statics.getMaintenanceStats = function(assetId, startDate, endDate) {
  const matchStage = { asset: mongoose.Types.ObjectId(assetId) };
  if (startDate && endDate) {
    matchStage.completedDate = { $gte: startDate, $lte: endDate };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalRecords: { $sum: 1 },
        totalCost: { $sum: '$totalCost' },
        averageCost: { $avg: '$totalCost' },
        totalDowntime: { $sum: '$actualDuration' },
        averageDowntime: { $avg: '$actualDuration' },
        maintenanceTypes: { $push: '$maintenanceType' }
      }
    }
  ]);
};

// Instance methods
AssetMaintenanceRecordSchema.methods.markComplete = function(completedBy, workPerformed, conditionAfter) {
  this.status = 'completed';
  this.completedDate = new Date();
  this.workPerformed = workPerformed;
  this.conditionAfter = conditionAfter;
  this.lastModifiedBy = completedBy;
  return this.save();
};

AssetMaintenanceRecordSchema.methods.calculateActualDuration = function() {
  if (this.startDate && this.completedDate) {
    this.actualDuration = Math.round((this.completedDate - this.startDate) / (1000 * 60));
  }
};

module.exports = mongoose.model('AssetMaintenanceRecord', AssetMaintenanceRecordSchema);