const mongoose = require('mongoose');

const PlanningCenterConfigSchema = new mongoose.Schema({
  personalAccessToken: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
PlanningCenterConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('planningCenterConfig', PlanningCenterConfigSchema);
