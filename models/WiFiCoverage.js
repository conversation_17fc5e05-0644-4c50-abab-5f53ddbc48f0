const mongoose = require('mongoose');

/**
 * WiFiCoverage Schema
 * Stores Wi-Fi coverage zone data for floor plan visualization
 * Part of Phase 5 - Wi-Fi coverage & APs
 */
const wifiCoverageSchema = new mongoose.Schema({
  // Location information
  buildingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Building',
    required: true,
    index: true
  },
  floorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Floor',
    required: true,
    index: true
  },
  
  // Coverage zone definition
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // Coverage area definition (polygon coordinates)
  coverageArea: {
    type: {
      type: String,
      enum: ['Polygon'],
      required: true
    },
    coordinates: {
      type: [[[Number]]], // Array of polygon coordinates [[[x,y], [x,y], ...]]
      required: true
    }
  },
  
  // Signal strength and quality metrics
  signalStrength: {
    average: { type: Number, min: -100, max: 0 }, // dBm
    minimum: { type: Number, min: -100, max: 0 }, // dBm
    maximum: { type: Number, min: -100, max: 0 }, // dBm
    standardDeviation: { type: Number, min: 0 }
  },
  
  // Coverage quality metrics
  quality: {
    excellent: { type: Number, min: 0, max: 100, default: 0 }, // % coverage above -50 dBm
    good: { type: Number, min: 0, max: 100, default: 0 },      // % coverage -50 to -70 dBm
    fair: { type: Number, min: 0, max: 100, default: 0 },      // % coverage -70 to -85 dBm
    poor: { type: Number, min: 0, max: 100, default: 0 },      // % coverage below -85 dBm
    overallScore: { type: Number, min: 0, max: 100, default: 0 } // Weighted overall score
  },
  
  // Network information
  ssid: {
    type: String,
    trim: true,
    maxlength: 32
  },
  frequency: {
    type: String,
    enum: ['2.4GHz', '5GHz', '6GHz', 'Mixed'],
    default: 'Mixed'
  },
  channel: {
    type: Number,
    min: 1,
    max: 196
  },
  
  // Associated access points
  accessPoints: [{
    deviceId: {
      type: String, // MAC address from UniFi
      required: true
    },
    name: String,
    contribution: {
      type: Number, // Percentage contribution to this coverage area
      min: 0,
      max: 100,
      default: 0
    },
    distance: Number, // Distance from center of coverage area (meters)
    signalStrength: Number // Signal strength from this AP in this area
  }],
  
  // Survey data source
  surveyData: {
    source: {
      type: String,
      enum: ['manual', 'ekahau', 'unifi_analytics', 'predicted', 'measured'],
      default: 'predicted'
    },
    surveyDate: Date,
    surveyTool: String, // Tool used for survey (Ekahau, WiFi Analyzer, etc.)
    surveyedBy: String, // Person who conducted survey
    dataPoints: Number, // Number of measurement points
    methodology: String, // Survey methodology description
    notes: String
  },
  
  // Event suitability
  eventSuitability: {
    capacity: {
      light: { type: Number, default: 0 },    // < 50 devices
      moderate: { type: Number, default: 0 }, // 50-150 devices
      heavy: { type: Number, default: 0 }     // > 150 devices
    },
    recommendedMaxDevices: {
      type: Number,
      min: 0,
      default: 50
    },
    suitableFor: [{
      type: String,
      enum: ['conference', 'worship', 'youth_event', 'wedding', 'general_use', 'live_stream']
    }]
  },
  
  // Visualization settings
  visualization: {
    colorScheme: {
      type: String,
      enum: ['signal_strength', 'quality_zones', 'capacity', 'custom'],
      default: 'signal_strength'
    },
    opacity: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.6
    },
    showBoundary: {
      type: Boolean,
      default: true
    },
    boundaryColor: {
      type: String,
      default: '#2196f3'
    },
    fillColor: {
      type: String,
      default: '#2196f3'
    }
  },
  
  // Status and metadata
  status: {
    type: String,
    enum: ['active', 'inactive', 'needs_survey', 'deprecated'],
    default: 'active'
  },
  isEnabled: {
    type: Boolean,
    default: true
  },
  
  // Import/export metadata
  importSource: String, // Source file for imported data
  exportData: {
    lastExported: Date,
    exportFormat: String,
    exportedBy: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
wifiCoverageSchema.index({ buildingId: 1, floorId: 1 });
wifiCoverageSchema.index({ buildingId: 1, floorId: 1, status: 1 });
wifiCoverageSchema.index({ 'coverageArea': '2dsphere' }); // Geospatial index for coverage queries
wifiCoverageSchema.index({ ssid: 1, frequency: 1 });
wifiCoverageSchema.index({ 'accessPoints.deviceId': 1 });

// Virtual for coverage quality summary
wifiCoverageSchema.virtual('qualitySummary').get(function() {
  const { excellent, good, fair, poor } = this.quality;
  if (excellent >= 80) return 'Excellent';
  if (excellent + good >= 70) return 'Good';
  if (excellent + good + fair >= 50) return 'Fair';
  return 'Poor';
});

// Virtual for event capacity recommendation
wifiCoverageSchema.virtual('eventCapacityRecommendation').get(function() {
  const maxDevices = this.eventSuitability.recommendedMaxDevices;
  if (maxDevices >= 150) return 'Heavy Load Suitable';
  if (maxDevices >= 50) return 'Moderate Load Suitable';
  return 'Light Load Only';
});

// Static method to find coverage areas by point
wifiCoverageSchema.statics.findByPoint = function(buildingId, floorId, x, y) {
  return this.find({
    buildingId,
    floorId,
    status: 'active',
    isEnabled: true,
    coverageArea: {
      $geoIntersects: {
        $geometry: {
          type: 'Point',
          coordinates: [x, y]
        }
      }
    }
  });
};

// Static method to get coverage summary for floor
wifiCoverageSchema.statics.getFloorCoverageSummary = async function(buildingId, floorId) {
  const coverage = await this.find({ buildingId, floorId, status: 'active', isEnabled: true });
  
  if (coverage.length === 0) {
    return {
      totalCoverageAreas: 0,
      overallQuality: 'No Coverage Data',
      averageSignalStrength: null,
      recommendedCapacity: 0,
      accessPointCount: 0
    };
  }
  
  const totalAreas = coverage.length;
  const avgSignalStrength = coverage.reduce((sum, area) => 
    sum + (area.signalStrength.average || -70), 0) / totalAreas;
  
  const overallQuality = coverage.reduce((sum, area) => sum + area.quality.overallScore, 0) / totalAreas;
  const recommendedCapacity = coverage.reduce((sum, area) => 
    sum + area.eventSuitability.recommendedMaxDevices, 0);
  
  const uniqueAPs = new Set();
  coverage.forEach(area => {
    area.accessPoints.forEach(ap => uniqueAPs.add(ap.deviceId));
  });
  
  return {
    totalCoverageAreas: totalAreas,
    overallQuality: overallQuality >= 80 ? 'Excellent' : 
                   overallQuality >= 60 ? 'Good' : 
                   overallQuality >= 40 ? 'Fair' : 'Poor',
    averageSignalStrength: Math.round(avgSignalStrength),
    recommendedCapacity,
    accessPointCount: uniqueAPs.size,
    detailedMetrics: {
      excellentCoverage: coverage.reduce((sum, area) => sum + area.quality.excellent, 0) / totalAreas,
      goodCoverage: coverage.reduce((sum, area) => sum + area.quality.good, 0) / totalAreas,
      fairCoverage: coverage.reduce((sum, area) => sum + area.quality.fair, 0) / totalAreas,
      poorCoverage: coverage.reduce((sum, area) => sum + area.quality.poor, 0) / totalAreas
    }
  };
};

// Instance method to calculate coverage area size
wifiCoverageSchema.methods.calculateAreaSize = function() {
  // Simple polygon area calculation (assuming rectangular coordinates)
  const coords = this.coverageArea.coordinates[0];
  if (coords.length < 3) return 0;
  
  let area = 0;
  for (let i = 0; i < coords.length; i++) {
    const j = (i + 1) % coords.length;
    area += coords[i][0] * coords[j][1];
    area -= coords[j][0] * coords[i][1];
  }
  return Math.abs(area) / 2;
};

// Instance method to get color for visualization
wifiCoverageSchema.methods.getVisualizationColor = function() {
  const scheme = this.visualization.colorScheme;
  
  switch (scheme) {
    case 'signal_strength':
      const strength = this.signalStrength.average || -70;
      if (strength > -50) return '#4caf50'; // Green - Excellent
      if (strength > -65) return '#8bc34a'; // Light Green - Good
      if (strength > -75) return '#ffeb3b'; // Yellow - Fair
      if (strength > -85) return '#ff9800'; // Orange - Poor
      return '#f44336'; // Red - Very Poor
      
    case 'quality_zones':
      const quality = this.quality.overallScore;
      if (quality >= 80) return '#4caf50'; // Green
      if (quality >= 60) return '#8bc34a'; // Light Green
      if (quality >= 40) return '#ffeb3b'; // Yellow
      if (quality >= 20) return '#ff9800'; // Orange
      return '#f44336'; // Red
      
    case 'capacity':
      const capacity = this.eventSuitability.recommendedMaxDevices;
      if (capacity >= 150) return '#4caf50'; // Green - High Capacity
      if (capacity >= 50) return '#ffeb3b';  // Yellow - Medium Capacity
      return '#ff9800'; // Orange - Low Capacity
      
    default:
      return this.visualization.fillColor || '#2196f3';
  }
};

// Pre-save middleware to calculate quality metrics
wifiCoverageSchema.pre('save', function(next) {
  // Calculate overall quality score if individual metrics are provided
  if (this.quality.excellent !== undefined && this.quality.good !== undefined && 
      this.quality.fair !== undefined && this.quality.poor !== undefined) {
    
    // Weighted scoring: Excellent=4, Good=3, Fair=2, Poor=1
    const weightedScore = (
      this.quality.excellent * 4 + 
      this.quality.good * 3 + 
      this.quality.fair * 2 + 
      this.quality.poor * 1
    ) / 4; // Divide by max possible weight
    
    this.quality.overallScore = Math.round(weightedScore);
  }
  
  // Set survey date if not provided and source is manual
  if (this.surveyData.source === 'manual' && !this.surveyData.surveyDate) {
    this.surveyData.surveyDate = new Date();
  }
  
  next();
});

module.exports = mongoose.model('WiFiCoverage', wifiCoverageSchema);