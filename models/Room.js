const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Room Schema
 * Represents a room within a floor in the building management system
 */
const RoomSchema = new Schema({
  floorId: {
    type: Schema.Types.ObjectId,
    ref: 'Floor',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  roomNumber: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  capacity: {
    type: Number,
    required: true,
    min: 1
  },
  features: [{
    type: String,
    enum: ['tv', 'projector', 'whiteboard', 'videoconference', 'phone', 'computer', 'other'],
  }],
  otherFeatures: [{
    name: { type: String, trim: true },
    description: { type: String, trim: true }
  }],
  dimensions: {
    width: { type: Number }, // in feet or meters
    length: { type: Number }, // in feet or meters
    squareFootage: { type: Number }
  },
  status: {
    type: String,
    enum: ['available', 'unavailable', 'maintenance', 'reserved'],
    default: 'available'
  },
  calendarId: {
    type: String,
    trim: true
  }, // Google Calendar ID for this room
  metadata: {
    publicAccess: { type: Boolean, default: true },
    requiresApproval: { type: Boolean, default: false },
    approvers: [{ type: Schema.Types.ObjectId, ref: 'User' }], // Users who can approve reservations
    notes: { type: String }
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
RoomSchema.index({ floorId: 1 });
RoomSchema.index({ status: 1 });
RoomSchema.index({ capacity: 1 });
RoomSchema.index({ features: 1 });

module.exports = mongoose.model('Room', RoomSchema);