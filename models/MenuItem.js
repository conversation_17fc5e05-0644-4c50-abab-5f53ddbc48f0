const mongoose = require('mongoose');

const MenuItemSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true
  },
  originalTitle: {
    type: String,
    required: true
  },
  friendlyName: {
    type: String
  },
  description: {
    type: String
  },
  path: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: 'link' // Default Material-UI icon
  },
  customIcon: {
    type: String // URL to custom icon (company logo)
  },
  categories: {
    type: [String],
    default: ['Core Features']
  },
  requiredRoles: {
    type: [String],
    default: ['user'] // By default, all users can see this menu item
  },
  requiredPermission: {
    type: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  type: {
    type: String,
    enum: ['regular', 'integration', 'admin'],
    default: 'regular'
  },
  // Optional parent menu item for submenu support
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'MenuItem'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to update the updatedAt field
MenuItemSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Index for text search
MenuItemSchema.index({ 
  title: 'text', 
  friendlyName: 'text',
  description: 'text', 
  categories: 'text'
});

// Create default menu items if they don't exist
MenuItemSchema.statics.createDefaultMenuItems = async function() {
  // Get the current menu items from Layout.js
  const navItems = [
    { title: 'Dashboard', originalTitle: 'Dashboard', path: '/dashboard', icon: 'dashboard', categories: ['Core Features'], requiredPermission: 'dashboard:read', type: 'regular' },
    { title: 'Shortcuts', originalTitle: 'Shortcuts', path: '/shortcuts', icon: 'link', categories: ['Core Features'], requiredPermission: 'shortcuts:read', type: 'regular' },
    { title: 'News', originalTitle: 'News', path: '/news', icon: 'newspaper', categories: ['Core Features'], requiredPermission: 'news:read', type: 'regular' },
    { title: 'Help Tickets', originalTitle: 'Help Tickets', path: '/tickets', icon: 'confirmation_number', categories: ['Core Features'], requiredPermission: 'tickets:read', type: 'regular' },
    { title: 'Forms', originalTitle: 'Forms', path: '/forms', icon: 'list_alt', categories: ['Core Features'], requiredPermission: 'forms:read', type: 'regular' },
    { title: 'Drive Files', originalTitle: 'Drive Files', path: '/drive', icon: 'folder', categories: ['Google Services'], requiredPermission: 'googleDrive:read', type: 'regular' },
    { title: 'Google Calendar', originalTitle: 'Google Calendar', path: '/google-calendar', icon: 'calendar_month', categories: ['Google Services'], requiredPermission: 'googleCalendar:read', type: 'regular' },
    { title: 'Google Forms', originalTitle: 'Google Forms', path: '/google-forms', icon: 'description', categories: ['Google Services'], requiredPermission: 'googleForms:read', type: 'regular' },
    { title: 'Staff Directory', originalTitle: 'Staff Directory', path: '/staff-directory', icon: 'contact_page', categories: ['Core Features'], requiredPermission: 'staffDirectory:read', type: 'regular' },
    { title: 'People', originalTitle: 'People', path: '/people', icon: 'people', categories: ['Core Features'], requiredPermission: 'people:read', type: 'regular' },
    { title: 'Tasks', originalTitle: 'Tasks', path: '/tasks', icon: 'assignment', categories: ['Core Features'], requiredPermission: 'tasks:read', type: 'regular' },
    // New Asset Management links
    { title: 'Assets', originalTitle: 'Assets', path: '/assets', icon: 'inventory2', categories: ['Core Features'], requiredPermission: 'assets:read', type: 'regular' },
    { title: 'Asset List', originalTitle: 'Asset List', path: '/assets/list', icon: 'list', categories: ['Core Features'], requiredPermission: 'assets:read', type: 'regular' },
    { title: 'Asset Reports', originalTitle: 'Asset Reports', path: '/assets/reports', icon: 'assessment', categories: ['Core Features'], requiredPermission: 'assets:read', type: 'regular' },
    { title: 'Asset Import/Export', originalTitle: 'Asset Import/Export', path: '/assets/import-export', icon: 'import_export', categories: ['Core Features'], requiredPermission: 'assets:admin', type: 'regular' },
    { title: 'Notes', originalTitle: 'Notes', path: '/notes', icon: 'note', categories: ['Core Features'], requiredPermission: 'notes:read', type: 'regular' },
    { title: 'Phone Book', originalTitle: 'Phone Book', path: '/phone-book', icon: 'contact_phone', categories: ['Building Management'], requiredPermission: 'contacts:read', type: 'regular' },
    { title: 'Room Booking', originalTitle: 'Room Booking', path: '/room-booking', icon: 'meeting_room', categories: ['Building Management'], requiredPermission: 'roomBooking:read', type: 'regular' },
    { title: 'Building Management', originalTitle: 'Building Management', path: '/building-management', icon: 'home_work', categories: ['Building Management'], requiredPermission: 'buildingManagement:read', type: 'regular' },
    { title: 'BMS (Building Management System)', originalTitle: 'BMS (Building Management System)', path: '/bms', icon: 'precision_manufacturing', categories: ['Building Management'], requiredPermission: 'bms:read', type: 'regular' },
    { title: 'Facility Services', originalTitle: 'Facility Services', path: '/building-management/tracking', icon: 'assignment', categories: ['Building Management'], requiredPermission: 'buildingManagement:read', type: 'regular' },
    { title: 'Help Center', originalTitle: 'Help Center', path: '/help', icon: 'help_outline', categories: ['Core Features'], requiredPermission: 'help:read', type: 'regular' },
    { title: 'FAQs', originalTitle: 'FAQs', path: '/help/faq', icon: 'question_answer', categories: ['Core Features'], requiredPermission: 'help:read', type: 'regular' }
  ];

  // Integration items
  const integrationItems = [
    { title: 'Canva', originalTitle: 'Canva', path: '/canva', icon: 'image', categories: ['Integrations'], requiredPermission: 'canva:read', type: 'integration' },
    { title: 'Planning Center', originalTitle: 'Planning Center', path: '/planning-center', icon: 'event', categories: ['Integrations'], requiredPermission: 'planningCenter:read', type: 'integration' },
    { title: 'Synology', originalTitle: 'Synology', path: '/synology', icon: 'storage', categories: ['Integrations'], requiredPermission: 'synology:read', type: 'integration' },
    { title: 'Dreo', originalTitle: 'Dreo', path: '/dreo', icon: 'ac_unit', categories: ['Integrations'], requiredPermission: 'dreo:read', type: 'integration' },
    { title: 'LG ThinQ AC', originalTitle: 'LG ThinQ AC', path: '/lg-thinq', icon: 'ac_unit', categories: ['Integrations'], requiredPermission: 'lgThinq:read', type: 'integration' },
    { title: 'Lenel S2 NetBox', originalTitle: 'Lenel S2 NetBox', path: '/lenel-s2-netbox', icon: 'security', categories: ['Security'], requiredPermission: 'lenelS2NetBox:read', type: 'integration' },
    { title: 'Mosyle Business', originalTitle: 'Mosyle Business', path: '/mosyle-business', icon: 'phone_iphone', categories: ['Integrations'], requiredPermission: 'mosyleBusiness:read', type: 'integration' },
    { title: 'UniFi Access', originalTitle: 'UniFi Access', path: '/unifi-access', icon: 'lock', categories: ['Security'], requiredPermission: 'unifiAccess:read', type: 'integration' },
    { title: 'UniFi Network', originalTitle: 'UniFi Network', path: '/unifi-network', icon: 'router', categories: ['Integrations'], requiredPermission: 'unifiNetwork:read', type: 'integration' },
    { title: 'UniFi Protect', originalTitle: 'UniFi Protect', path: '/unifi-protect', icon: 'videocam', categories: ['Security'], requiredPermission: 'unifiProtect:read', type: 'integration' },
    { title: 'Google Admin', originalTitle: 'Google Admin', path: '/google-admin', icon: 'admin_panel_settings', categories: ['Google Services', 'Administration'], requiredPermission: 'googleAdmin:read', type: 'integration' },
    { title: 'RADIUS', originalTitle: 'RADIUS', path: '/radius', icon: 'network_check', categories: ['Security'], requiredPermission: 'radius:read', type: 'integration' },
    { title: 'Apple Business Manager', originalTitle: 'Apple Business Manager', path: '/apple-business-manager', icon: 'apple', categories: ['Integrations'], requiredPermission: 'appleBusinessManager:read', type: 'integration' },
    { title: 'Rain Bird', originalTitle: 'Rain Bird', path: '/rain-bird', icon: 'thermostat', categories: ['Building Management'], requiredPermission: 'rainBird:read', type: 'integration' },
    { title: 'WiiM', originalTitle: 'WiiM', path: '/wiim', icon: 'music_note', categories: ['Integrations'], requiredPermission: 'wiim:read', type: 'integration' },
    { title: 'SkyportCloud HVAC', originalTitle: 'SkyportCloud HVAC', path: '/skyportcloud', icon: 'thermostat', categories: ['Building Management'], requiredPermission: 'skyportcloud:read', type: 'integration' },
    { title: 'Q-sys Core Manager', originalTitle: 'Q-sys Core Manager', path: '/qsys', icon: 'speaker', categories: ['Integrations'], requiredPermission: 'qsys:read', type: 'integration' },
    { title: 'Colorlit LED Controller', originalTitle: 'Colorlit LED Controller', path: '/colorlit', icon: 'lightbulb', categories: ['Integrations'], requiredPermission: 'colorlit:read', type: 'integration' },
    { title: 'ZeeVee HDbridge', originalTitle: 'ZeeVee HDbridge', path: '/zeevee', icon: 'tv', categories: ['Integrations'], requiredPermission: 'zeevee:read', type: 'integration' },
    { title: 'Panasonic Pro AV Camera', originalTitle: 'Panasonic Pro AV Camera', path: '/panasonic', icon: 'videocam', categories: ['Integrations'], requiredPermission: 'panasonic:read', type: 'integration' },
    { title: 'Govee Smart Devices', originalTitle: 'Govee Smart Devices', path: '/govee', icon: 'lightbulb', categories: ['Integrations'], requiredPermission: 'govee:read', type: 'integration' },
    { title: 'Constant Contact', originalTitle: 'Constant Contact', path: '/constant-contact', icon: 'email', categories: ['Integrations'], requiredPermission: 'constant-contact:read', type: 'integration' }
  ];

  // Admin items
  const adminItems = [
    { title: 'System Status', originalTitle: 'System Status', path: '/admin/status', icon: 'admin_panel_settings', categories: ['Administration'], requiredRoles: ['admin'], type: 'admin' },
    { title: 'Manage Users', originalTitle: 'Manage Users', path: '/admin/users', icon: 'people', categories: ['Administration'], requiredPermission: 'users:admin', type: 'admin' },
    { title: 'Manage Roles', originalTitle: 'Manage Roles', path: '/admin/roles', icon: 'vpn_key', categories: ['Administration'], requiredPermission: 'roles:admin', type: 'admin' },
    { title: 'Manage Shortcuts', originalTitle: 'Manage Shortcuts', path: '/admin/shortcuts', icon: 'settings', categories: ['Administration'], requiredPermission: 'shortcuts:admin', type: 'admin' },
    { title: 'Manage Menu', originalTitle: 'Manage Menu', path: '/admin/menu', icon: 'menu', categories: ['Administration'], requiredPermission: 'menu:admin', type: 'admin' },
    { title: 'Manage RADIUS', originalTitle: 'Manage RADIUS', path: '/admin/radius', icon: 'network_check', categories: ['Administration'], requiredPermission: 'radius:admin', type: 'admin' },
    { title: 'Manage Building Management', originalTitle: 'Manage Building Management', path: '/admin/building-management', icon: 'home_work', categories: ['Administration'], requiredPermission: 'buildingManagement:admin', type: 'admin' },
    { title: 'Manage Ticket Categories & Tags', originalTitle: 'Manage Ticket Categories & Tags', path: '/admin/ticket-categories-and-tags', icon: 'category', categories: ['Administration'], requiredPermission: 'tickets:admin', type: 'admin' },
    { title: 'Access Control', originalTitle: 'Access Control', path: '/access-control', icon: 'security', categories: ['Administration'], requiredPermission: 'accessControl:admin', type: 'admin' }
  ];

  // Combine all items
  const allItems = [...navItems, ...integrationItems, ...adminItems];

  // Create or update each menu item
  for (const item of allItems) {
    try {
      await this.findOneAndUpdate(
        { path: item.path },
        item,
        { upsert: true, new: true, setDefaultsOnInsert: true }
      );
    } catch (err) {
      console.error(`Error creating default menu item ${item.title}:`, err);
    }
  }

  // Deactivate GLPI menu item if it exists
  try {
    await this.updateOne({ path: '/glpi' }, { isActive: false });
  } catch (err) {
    console.error('Error deactivating GLPI menu item:', err);
  }
};

// Sync only missing default menu items (non-destructive)
MenuItemSchema.statics.syncMissingDefaultMenuItems = async function() {
  // Default items definition (same as createDefaultMenuItems)
  const navItems = [
    { title: 'Dashboard', originalTitle: 'Dashboard', path: '/dashboard', icon: 'dashboard', categories: ['Core Features'], requiredPermission: 'dashboard:read', type: 'regular' },
    { title: 'Shortcuts', originalTitle: 'Shortcuts', path: '/shortcuts', icon: 'link', categories: ['Core Features'], requiredPermission: 'shortcuts:read', type: 'regular' },
    { title: 'News', originalTitle: 'News', path: '/news', icon: 'newspaper', categories: ['Core Features'], requiredPermission: 'news:read', type: 'regular' },
    { title: 'Help Tickets', originalTitle: 'Help Tickets', path: '/tickets', icon: 'confirmation_number', categories: ['Core Features'], requiredPermission: 'tickets:read', type: 'regular' },
    { title: 'Forms', originalTitle: 'Forms', path: '/forms', icon: 'list_alt', categories: ['Core Features'], requiredPermission: 'forms:read', type: 'regular' },
    { title: 'Drive Files', originalTitle: 'Drive Files', path: '/drive', icon: 'folder', categories: ['Google Services'], requiredPermission: 'googleDrive:read', type: 'regular' },
    { title: 'Google Calendar', originalTitle: 'Google Calendar', path: '/google-calendar', icon: 'calendar_month', categories: ['Google Services'], requiredPermission: 'googleCalendar:read', type: 'regular' },
    { title: 'Google Forms', originalTitle: 'Google Forms', path: '/google-forms', icon: 'description', categories: ['Google Services'], requiredPermission: 'googleForms:read', type: 'regular' },
    { title: 'Staff Directory', originalTitle: 'Staff Directory', path: '/staff-directory', icon: 'contact_page', categories: ['Core Features'], requiredPermission: 'staffDirectory:read', type: 'regular' },
    { title: 'People Directory', originalTitle: 'People Directory', path: '/people', icon: 'people', categories: ['Core Features'], requiredPermission: 'people:read', type: 'regular' },
    { title: 'Team Hubs', originalTitle: 'Team Hubs', path: '/team-hubs', icon: 'groups', categories: ['Core Features'], type: 'regular' },
    { title: 'Tasks', originalTitle: 'Tasks', path: '/tasks', icon: 'assignment', categories: ['Core Features'], requiredPermission: 'tasks:read', type: 'regular' },
    { title: 'Assets', originalTitle: 'Assets', path: '/assets', icon: 'inventory2', categories: ['Core Features'], requiredPermission: 'assets:read', type: 'regular' },
    { title: 'Asset List', originalTitle: 'Asset List', path: '/assets/list', icon: 'list', categories: ['Core Features'], requiredPermission: 'assets:read', type: 'regular' },
    { title: 'Asset Reports', originalTitle: 'Asset Reports', path: '/assets/reports', icon: 'assessment', categories: ['Core Features'], requiredPermission: 'assets:read', type: 'regular' },
    { title: 'Asset Import/Export', originalTitle: 'Asset Import/Export', path: '/assets/import-export', icon: 'import_export', categories: ['Core Features'], requiredPermission: 'assets:admin', type: 'regular' },
    { title: 'Notes', originalTitle: 'Notes', path: '/notes', icon: 'note', categories: ['Core Features'], requiredPermission: 'notes:read', type: 'regular' },
    { title: 'Phone Book', originalTitle: 'Phone Book', path: '/phone-book', icon: 'contact_phone', categories: ['Building Management'], requiredPermission: 'contacts:read', type: 'regular' },
    { title: 'Room Booking', originalTitle: 'Room Booking', path: '/room-booking', icon: 'meeting_room', categories: ['Building Management'], requiredPermission: 'roomBooking:read', type: 'regular' },
    { title: 'Building Management', originalTitle: 'Building Management', path: '/building-management', icon: 'home_work', categories: ['Building Management'], requiredPermission: 'buildingManagement:read', type: 'regular' },
    { title: 'BMS (Building Management System)', originalTitle: 'BMS (Building Management System)', path: '/bms', icon: 'precision_manufacturing', categories: ['Building Management'], requiredPermission: 'bms:read', type: 'regular' },
    { title: 'Facility Services', originalTitle: 'Facility Services', path: '/building-management/tracking', icon: 'assignment', categories: ['Building Management'], requiredPermission: 'buildingManagement:read', type: 'regular' },
    { title: 'Help Center', originalTitle: 'Help Center', path: '/help', icon: 'help_outline', categories: ['Core Features'], requiredPermission: 'help:read', type: 'regular' },
    { title: 'FAQs', originalTitle: 'FAQs', path: '/help/faq', icon: 'question_answer', categories: ['Core Features'], requiredPermission: 'help:read', type: 'regular' }
  ];

  const integrationItems = [
    { title: 'Canva', originalTitle: 'Canva', path: '/canva', icon: 'image', categories: ['Integrations'], requiredPermission: 'canva:read', type: 'integration' },
    { title: 'Planning Center', originalTitle: 'Planning Center', path: '/planning-center', icon: 'event', categories: ['Integrations'], requiredPermission: 'planningCenter:read', type: 'integration' },
    { title: 'Synology', originalTitle: 'Synology', path: '/synology', icon: 'storage', categories: ['Integrations'], requiredPermission: 'synology:read', type: 'integration' },
    { title: 'Dreo', originalTitle: 'Dreo', path: '/dreo', icon: 'ac_unit', categories: ['Integrations'], requiredPermission: 'dreo:read', type: 'integration' },
    { title: 'LG ThinQ AC', originalTitle: 'LG ThinQ AC', path: '/lg-thinq', icon: 'ac_unit', categories: ['Integrations'], requiredPermission: 'lgThinq:read', type: 'integration' },
    { title: 'Lenel S2 NetBox', originalTitle: 'Lenel S2 NetBox', path: '/lenel-s2-netbox', icon: 'security', categories: ['Security'], requiredPermission: 'lenelS2NetBox:read', type: 'integration' },
    { title: 'Mosyle Business', originalTitle: 'Mosyle Business', path: '/mosyle-business', icon: 'phone_iphone', categories: ['Integrations'], requiredPermission: 'mosyleBusiness:read', type: 'integration' },
    { title: 'UniFi Access', originalTitle: 'UniFi Access', path: '/unifi-access', icon: 'lock', categories: ['Security'], requiredPermission: 'unifiAccess:read', type: 'integration' },
    { title: 'UniFi Network', originalTitle: 'UniFi Network', path: '/unifi-network', icon: 'router', categories: ['Integrations'], requiredPermission: 'unifiNetwork:read', type: 'integration' },
    { title: 'UniFi Protect', originalTitle: 'UniFi Protect', path: '/unifi-protect', icon: 'videocam', categories: ['Security'], requiredPermission: 'unifiProtect:read', type: 'integration' },
    { title: 'Google Admin', originalTitle: 'Google Admin', path: '/google-admin', icon: 'admin_panel_settings', categories: ['Google Services', 'Administration'], requiredPermission: 'googleAdmin:read', type: 'integration' },
    { title: 'RADIUS', originalTitle: 'RADIUS', path: '/radius', icon: 'network_check', categories: ['Security'], requiredPermission: 'radius:read', type: 'integration' },
    { title: 'Apple Business Manager', originalTitle: 'Apple Business Manager', path: '/apple-business-manager', icon: 'apple', categories: ['Integrations'], requiredPermission: 'appleBusinessManager:read', type: 'integration' },
    { title: 'Rain Bird', originalTitle: 'Rain Bird', path: '/rain-bird', icon: 'thermostat', categories: ['Building Management'], requiredPermission: 'rainBird:read', type: 'integration' },
    { title: 'WiiM', originalTitle: 'WiiM', path: '/wiim', icon: 'music_note', categories: ['Integrations'], requiredPermission: 'wiim:read', type: 'integration' },
    { title: 'SkyportCloud HVAC', originalTitle: 'SkyportCloud HVAC', path: '/skyportcloud', icon: 'thermostat', categories: ['Building Management'], requiredPermission: 'skyportcloud:read', type: 'integration' },
    { title: 'Q-sys Core Manager', originalTitle: 'Q-sys Core Manager', path: '/qsys', icon: 'speaker', categories: ['Integrations'], requiredPermission: 'qsys:read', type: 'integration' },
    { title: 'Colorlit LED Controller', originalTitle: 'Colorlit LED Controller', path: '/colorlit', icon: 'lightbulb', categories: ['Integrations'], requiredPermission: 'colorlit:read', type: 'integration' },
    { title: 'ZeeVee HDbridge', originalTitle: 'ZeeVee HDbridge', path: '/zeevee', icon: 'tv', categories: ['Integrations'], requiredPermission: 'zeevee:read', type: 'integration' },
    { title: 'Panasonic Pro AV Camera', originalTitle: 'Panasonic Pro AV Camera', path: '/panasonic', icon: 'videocam', categories: ['Integrations'], requiredPermission: 'panasonic:read', type: 'integration' },
    { title: 'Govee Smart Devices', originalTitle: 'Govee Smart Devices', path: '/govee', icon: 'lightbulb', categories: ['Integrations'], requiredPermission: 'govee:read', type: 'integration' },
    { title: 'Constant Contact', originalTitle: 'Constant Contact', path: '/constant-contact', icon: 'email', categories: ['Integrations'], requiredPermission: 'constant-contact:read', type: 'integration' }
  ];

  const adminItems = [
    { title: 'System Status', originalTitle: 'System Status', path: '/admin/status', icon: 'admin_panel_settings', categories: ['Administration'], requiredRoles: ['admin'], type: 'admin' },
    { title: 'Manage Users', originalTitle: 'Manage Users', path: '/admin/users', icon: 'people', categories: ['Administration'], requiredPermission: 'users:admin', type: 'admin' },
    { title: 'Manage Roles', originalTitle: 'Manage Roles', path: '/admin/roles', icon: 'vpn_key', categories: ['Administration'], requiredPermission: 'roles:admin', type: 'admin' },
    { title: 'Manage Shortcuts', originalTitle: 'Manage Shortcuts', path: '/admin/shortcuts', icon: 'settings', categories: ['Administration'], requiredPermission: 'shortcuts:admin', type: 'admin' },
    { title: 'Manage Menu', originalTitle: 'Manage Menu', path: '/admin/menu', icon: 'menu', categories: ['Administration'], requiredPermission: 'menu:admin', type: 'admin' },
    { title: 'Manage RADIUS', originalTitle: 'Manage RADIUS', path: '/admin/radius', icon: 'network_check', categories: ['Administration'], requiredPermission: 'radius:admin', type: 'admin' },
    { title: 'Manage Building Management', originalTitle: 'Manage Building Management', path: '/admin/building-management', icon: 'home_work', categories: ['Administration'], requiredPermission: 'buildingManagement:admin', type: 'admin' },
    { title: 'Manage Ticket Categories & Tags', originalTitle: 'Manage Ticket Categories & Tags', path: '/admin/ticket-categories-and-tags', icon: 'category', categories: ['Administration'], requiredPermission: 'tickets:admin', type: 'admin' },
    { title: 'Access Control', originalTitle: 'Access Control', path: '/access-control', icon: 'security', categories: ['Administration'], requiredPermission: 'accessControl:admin', type: 'admin' }
  ];

  const allItems = [...navItems, ...integrationItems, ...adminItems];
  const created = [];

  for (const item of allItems) {
    try {
      const exists = await this.findOne({ path: item.path });
      if (!exists) {
        const createdItem = await this.create(item);
        created.push(createdItem);
      }
    } catch (err) {
      console.error(`Error syncing default menu item ${item.title}:`, err);
    }
  }

  return created;
};

module.exports = mongoose.model('MenuItem', MenuItemSchema);