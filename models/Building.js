const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Building Schema
 * Represents a physical building in the building management system
 */
const BuildingSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    street: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    zipCode: { type: String, trim: true },
    country: { type: String, trim: true, default: 'USA' }
  },
  description: {
    type: String,
    trim: true
  },
  buildingType: {
    type: String,
    enum: ['office', 'residential', 'mixed-use', 'educational', 'industrial', 'other'],
    default: 'office'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'under_construction', 'maintenance'],
    default: 'active'
  },
  contactInfo: {
    primaryContact: { type: String, trim: true },
    phone: { type: String, trim: true },
    email: { type: String, trim: true }
  },
  metadata: {
    yearBuilt: Number,
    squareFootage: Number,
    numberOfFloors: Number
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
BuildingSchema.index({ name: 1 });
BuildingSchema.index({ 'address.city': 1, 'address.state': 1 });
BuildingSchema.index({ status: 1 });

module.exports = mongoose.model('Building', BuildingSchema);