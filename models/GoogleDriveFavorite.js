const mongoose = require('mongoose');

const GoogleDriveFavoriteSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  fileId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  mimeType: {
    type: String,
    required: true
  },
  iconLink: {
    type: String
  },
  webViewLink: {
    type: String
  },
  size: {
    type: Number
  },
  modifiedTime: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create a compound index on user and fileId to ensure uniqueness
GoogleDriveFavoriteSchema.index({ user: 1, fileId: 1 }, { unique: true });

module.exports = mongoose.model('GoogleDriveFavorite', GoogleDriveFavoriteSchema);