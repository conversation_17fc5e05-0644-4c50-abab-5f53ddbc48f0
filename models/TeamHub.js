const mongoose = require('mongoose');

const LinkSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  url: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  pinned: { type: Boolean, default: false },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, { _id: true });

const HelpPageSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  content: { type: String, required: true },
  pinned: { type: Boolean, default: false },
  createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, { _id: true });

const TeamHubSchema = new mongoose.Schema({
  team: { type: mongoose.Schema.Types.ObjectId, ref: 'Team', required: true, unique: true },
  links: { type: [LinkSchema], default: [] },
  helpPages: { type: [HelpPageSchema], default: [] }
}, { timestamps: true });

// Keep subdocs updatedAt fresh
TeamHubSchema.pre('save', function(next) {
  const now = Date.now();
  if (Array.isArray(this.links)) {
    this.links.forEach(l => { if (l.isModified && l.isModified()) { l.updatedAt = now; } });
  }
  if (Array.isArray(this.helpPages)) {
    this.helpPages.forEach(p => { if (p.isModified && p.isModified()) { p.updatedAt = now; } });
  }
  next();
});

module.exports = mongoose.model('TeamHub', TeamHubSchema);
