const mongoose = require('mongoose');

const WayfindingRouteSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  routeType: {
    type: String,
    required: true,
    enum: [
      'evacuation',          // Emergency evacuation routes
      'accessibility',       // ADA-compliant routes  
      'volunteer_guide',     // Volunteer navigation
      'visitor_directions',  // Guest wayfinding
      'utility_access',      // Maintenance/utility access
      'event_setup',         // Event preparation routes
      'safety_patrol',       // Security patrol routes
      'tour_guide',          // Building tours
      'custom'
    ],
    default: 'custom'
  },
  priority: {
    type: String,
    enum: ['emergency', 'high', 'medium', 'low'],
    default: 'medium'
  },
  
  // Route definition
  startPoint: {
    buildingId: { type: mongoose.Schema.Types.ObjectId, ref: 'Building', required: true },
    floorId: { type: mongoose.Schema.Types.ObjectId, ref: 'Floor', required: true },
    room: { type: String },
    description: { type: String, required: true },
    coordinates: {
      x: { type: Number },
      y: { type: Number }
    },
    landmark: { type: String } // "Main entrance", "Sanctuary doors", etc.
  },
  
  endPoint: {
    buildingId: { type: mongoose.Schema.Types.ObjectId, ref: 'Building', required: true },
    floorId: { type: mongoose.Schema.Types.ObjectId, ref: 'Floor', required: true },
    room: { type: String },
    description: { type: String, required: true },
    coordinates: {
      x: { type: Number },
      y: { type: Number }
    },
    landmark: { type: String }
  },
  
  // Step-by-step directions
  steps: [{
    stepNumber: { type: Number, required: true },
    instruction: { type: String, required: true, maxlength: 300 },
    direction: { 
      type: String, 
      enum: ['straight', 'left', 'right', 'up', 'down', 'enter', 'exit', 'stop'],
      default: 'straight'
    },
    distance: { 
      value: { type: Number }, // in feet
      description: { type: String } // "50 feet", "end of hallway", etc.
    },
    landmark: { type: String }, // Reference point for navigation
    warning: { type: String }, // Safety warnings or important notes
    alternativeAction: { type: String }, // If primary route blocked
    
    // Location context
    buildingId: { type: mongoose.Schema.Types.ObjectId, ref: 'Building' },
    floorId: { type: mongoose.Schema.Types.ObjectId, ref: 'Floor' },
    coordinates: {
      x: { type: Number },
      y: { type: Number }
    },
    
    // Visual aids
    photos: [{
      url: { type: String },
      caption: { type: String },
      viewDirection: { type: String } // "facing north", "looking left", etc.
    }],
    iconId: { type: mongoose.Schema.Types.ObjectId, ref: 'FloorplanIcon' }
  }],
  
  // Route metadata
  estimatedTime: {
    walking: { type: Number }, // minutes
    wheelchair: { type: Number }, // minutes  
    carrying: { type: Number } // minutes (when carrying equipment)
  },
  difficulty: {
    type: String,
    enum: ['easy', 'moderate', 'difficult'],
    default: 'easy'
  },
  accessibility: {
    wheelchairAccessible: { type: Boolean, default: true },
    elevatorRequired: { type: Boolean, default: false },
    stairsRequired: { type: Boolean, default: false },
    heavyDoorsWarning: { type: Boolean, default: false },
    narrowPassagesWarning: { type: Boolean, default: false },
    notes: { type: String }
  },
  
  // Safety and emergency information
  safetyInfo: {
    emergencyExits: [{
      description: { type: String, required: true },
      coordinates: {
        x: { type: Number },
        y: { type: Number }
      },
      distanceFromRoute: { type: Number } // feet
    }],
    firstAidStations: [{
      description: { type: String, required: true },
      coordinates: {
        x: { type: Number },
        y: { type: Number }
      }
    }],
    aedLocations: [{
      description: { type: String, required: true },
      coordinates: {
        x: { type: Number },
        y: { type: Number }
      }
    }],
    utilityShutoffs: [{
      type: { type: String, enum: ['water', 'gas', 'electric'] },
      description: { type: String, required: true },
      coordinates: {
        x: { type: Number },
        y: { type: Number }
      }
    }],
    hazards: [{
      type: { type: String, enum: ['slip', 'trip', 'electrical', 'chemical', 'structural'] },
      description: { type: String, required: true },
      severity: { type: String, enum: ['low', 'medium', 'high', 'critical'] },
      mitigation: { type: String }
    }]
  },
  
  // Associated resources and equipment
  associatedResources: [{
    resourceType: { 
      type: String, 
      enum: ['safety_equipment', 'volunteer_tools', 'signage', 'keys', 'communication'] 
    },
    description: { type: String, required: true },
    location: { type: String },
    required: { type: Boolean, default: false }
  }],
  
  // Conditions and restrictions
  conditions: {
    timeRestrictions: [{
      dayOfWeek: { type: Number, min: 0, max: 6 }, // 0 = Sunday
      startTime: { type: String }, // "09:00"
      endTime: { type: String },   // "17:00"
      reason: { type: String }
    }],
    eventRestrictions: [{
      eventType: { type: String },
      reason: { type: String },
      alternativeRoute: { type: mongoose.Schema.Types.ObjectId, ref: 'WayfindingRoute' }
    }],
    seasonalConsiderations: [{
      season: { type: String, enum: ['spring', 'summer', 'fall', 'winter'] },
      consideration: { type: String },
      modification: { type: String }
    }],
    maintenanceNotes: { type: String }
  },
  
  // PDF generation settings
  pdfSettings: {
    includeFloorplans: { type: Boolean, default: true },
    includePhotos: { type: Boolean, default: true },
    includeSafetyInfo: { type: Boolean, default: true },
    paperSize: { type: String, enum: ['letter', 'a4', 'legal'], default: 'letter' },
    orientation: { type: String, enum: ['portrait', 'landscape'], default: 'portrait' },
    colorMode: { type: String, enum: ['color', 'grayscale', 'black_and_white'], default: 'color' },
    fontSize: { type: String, enum: ['small', 'medium', 'large'], default: 'medium' },
    showGrid: { type: Boolean, default: false },
    includeQRCode: { type: Boolean, default: true }
  },
  
  // Usage tracking
  usage: {
    timesGenerated: { type: Number, default: 0 },
    lastGenerated: { type: Date },
    downloadCount: { type: Number, default: 0 },
    printCount: { type: Number, default: 0 },
    feedback: [{
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      rating: { type: Number, min: 1, max: 5 },
      comment: { type: String },
      improvements: [{ type: String }],
      createdAt: { type: Date, default: Date.now }
    }]
  },
  
  // Versioning and updates
  version: { type: String, default: '1.0' },
  lastVerified: { type: Date },
  verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  needsUpdate: { type: Boolean, default: false },
  updateReasons: [{ type: String }],
  
  // Access control
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPublic: { type: Boolean, default: true },
  allowedRoles: [{
    type: String,
    enum: ['admin', 'facilities', 'volunteer', 'staff', 'security']
  }],
  
  // Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'under_review', 'needs_verification'],
    default: 'active'
  }
}, {
  timestamps: true
});

// Indexes
WayfindingRouteSchema.index({ routeType: 1, priority: 1 });
WayfindingRouteSchema.index({ 'startPoint.buildingId': 1, 'startPoint.floorId': 1 });
WayfindingRouteSchema.index({ 'endPoint.buildingId': 1, 'endPoint.floorId': 1 });
WayfindingRouteSchema.index({ status: 1, isPublic: 1 });
WayfindingRouteSchema.index({ 'usage.timesGenerated': -1 });

// Text search index
WayfindingRouteSchema.index({
  name: 'text',
  description: 'text',
  'startPoint.description': 'text',
  'endPoint.description': 'text',
  'steps.instruction': 'text'
});

// Virtual for total distance estimate
WayfindingRouteSchema.virtual('totalDistance').get(function() {
  return this.steps.reduce((total, step) => {
    return total + (step.distance?.value || 0);
  }, 0);
});

// Virtual for route complexity
WayfindingRouteSchema.virtual('complexity').get(function() {
  let score = 0;
  
  // Base complexity from step count
  score += this.steps.length;
  
  // Add complexity for floor changes
  const floors = new Set();
  this.steps.forEach(step => {
    if (step.floorId) floors.add(step.floorId.toString());
  });
  score += (floors.size - 1) * 3; // Floor changes add complexity
  
  // Add complexity for warnings and hazards
  score += this.safetyInfo.hazards.length * 2;
  
  // Normalize to simple scale
  if (score <= 5) return 'simple';
  if (score <= 10) return 'moderate';
  return 'complex';
});

// Virtual for accessibility score
WayfindingRouteSchema.virtual('accessibilityScore').get(function() {
  let score = 100;
  
  if (!this.accessibility.wheelchairAccessible) score -= 50;
  if (this.accessibility.stairsRequired) score -= 30;
  if (this.accessibility.elevatorRequired) score -= 10;
  if (this.accessibility.heavyDoorsWarning) score -= 10;
  if (this.accessibility.narrowPassagesWarning) score -= 15;
  
  return Math.max(score, 0);
});

// Method to record usage
WayfindingRouteSchema.methods.recordUsage = function(action = 'generated') {
  if (action === 'generated') {
    this.usage.timesGenerated += 1;
    this.usage.lastGenerated = new Date();
  } else if (action === 'downloaded') {
    this.usage.downloadCount += 1;
  } else if (action === 'printed') {
    this.usage.printCount += 1;
  }
};

// Method to add feedback
WayfindingRouteSchema.methods.addFeedback = function(userId, rating, comment, improvements = []) {
  this.usage.feedback.push({
    userId,
    rating,
    comment,
    improvements
  });
  
  // Calculate average rating
  const totalRating = this.usage.feedback.reduce((sum, fb) => sum + fb.rating, 0);
  this.averageRating = totalRating / this.usage.feedback.length;
};

// Method to validate route
WayfindingRouteSchema.methods.validateRoute = function() {
  const issues = [];
  
  // Check for missing required fields
  if (!this.startPoint.description) {
    issues.push('Start point description is required');
  }
  
  if (!this.endPoint.description) {
    issues.push('End point description is required');
  }
  
  if (this.steps.length === 0) {
    issues.push('Route must have at least one step');
  }
  
  // Check step sequence
  for (let i = 0; i < this.steps.length; i++) {
    const step = this.steps[i];
    if (step.stepNumber !== i + 1) {
      issues.push(`Step ${i + 1} has incorrect step number: ${step.stepNumber}`);
    }
    
    if (!step.instruction) {
      issues.push(`Step ${i + 1} is missing instruction`);
    }
  }
  
  // Check for accessibility conflicts
  if (this.accessibility.wheelchairAccessible && this.accessibility.stairsRequired) {
    issues.push('Route marked as wheelchair accessible but requires stairs');
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};

// Static method to find routes by type
WayfindingRouteSchema.statics.findByType = function(routeType, buildingId = null) {
  const filter = { routeType, status: 'active' };
  if (buildingId) {
    filter.$or = [
      { 'startPoint.buildingId': buildingId },
      { 'endPoint.buildingId': buildingId }
    ];
  }
  
  return this.find(filter)
    .populate('startPoint.buildingId', 'name')
    .populate('endPoint.buildingId', 'name')
    .sort({ priority: -1, name: 1 });
};

// Static method to find emergency routes
WayfindingRouteSchema.statics.findEmergencyRoutes = function(buildingId, floorId = null) {
  const filter = {
    routeType: 'evacuation',
    status: 'active',
    'startPoint.buildingId': buildingId
  };
  
  if (floorId) {
    filter['startPoint.floorId'] = floorId;
  }
  
  return this.find(filter)
    .populate('startPoint.floorId', 'name level')
    .populate('endPoint.floorId', 'name level')
    .sort({ priority: -1, 'estimatedTime.walking': 1 });
};

// Static method to search routes
WayfindingRouteSchema.statics.searchRoutes = function(query, filters = {}) {
  const searchFilter = {
    $text: { $search: query },
    status: 'active',
    ...filters
  };
  
  return this.find(searchFilter, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } })
    .populate('startPoint.buildingId', 'name')
    .populate('endPoint.buildingId', 'name');
};

// Pre-save middleware to auto-generate step numbers
WayfindingRouteSchema.pre('save', function(next) {
  // Sort steps by stepNumber and re-number them
  this.steps.sort((a, b) => a.stepNumber - b.stepNumber);
  this.steps.forEach((step, index) => {
    step.stepNumber = index + 1;
  });
  
  next();
});

module.exports = mongoose.model('WayfindingRoute', WayfindingRouteSchema);