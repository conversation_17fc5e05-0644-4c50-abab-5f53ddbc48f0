const mongoose = require('mongoose');
const { Schema } = mongoose;

/**
 * Electrical Circuit Schema
 * Represents a breaker/circuit within a panel
 */
const ElectricalCircuitSchema = new Schema({
  panelId: { type: Schema.Types.ObjectId, ref: 'ElectricalPanel', required: true },
  number: { type: Number, required: true },
  label: { type: String, trim: true },
  breakerType: { type: String, trim: true }, // e.g., single, double, GFCI, AFCI
  amperage: { type: Number },
  notes: { type: String, trim: true }
}, { timestamps: true });

ElectricalCircuitSchema.index({ panelId: 1, number: 1 }, { unique: true });

module.exports = mongoose.model('ElectricalCircuit', ElectricalCircuitSchema);
