const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Reservation Schema
 * Represents a room reservation in the scheduling system
 */
const ReservationSchema = new Schema({
  roomId: {
    type: Schema.Types.ObjectId,
    ref: 'Room',
    required: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    required: true
  },
  // For recurring reservations
  recurrence: {
    isRecurring: { type: Boolean, default: false },
    pattern: { 
      type: String, 
      enum: ['daily', 'weekly', 'biweekly', 'monthly', 'custom'],
      required: function() { return this.recurrence.isRecurring; }
    },
    daysOfWeek: [{ 
      type: Number, 
      min: 0, 
      max: 6 
    }], // 0 = Sunday, 1 = Monday, etc.
    interval: { 
      type: Number, 
      min: 1, 
      default: 1 
    }, // e.g., every 2 weeks
    endDate: { type: Date },
    count: { type: Number, min: 1 }, // number of occurrences
    exceptions: [{ type: Date }] // dates when the recurring event doesn't occur
  },
  attendees: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    name: { type: String, trim: true },
    email: { type: String, trim: true },
    status: { 
      type: String, 
      enum: ['pending', 'accepted', 'declined', 'tentative'],
      default: 'pending'
    }
  }],
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'cancelled', 'completed'],
    default: 'pending'
  },
  approvalStatus: {
    isApprovalRequired: { type: Boolean, default: false },
    approvedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    approvedAt: { type: Date },
    rejectedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    rejectedAt: { type: Date },
    rejectionReason: { type: String, trim: true }
  },
  confirmationStatus: {
    isConfirmed: { type: Boolean, default: false },
    confirmedAt: { type: Date },
    reminderSent: { type: Boolean, default: false },
    reminderSentAt: { type: Date }
  },
  googleCalendarEventId: {
    type: String,
    trim: true
  },
  metadata: {
    purpose: { type: String, trim: true },
    numberOfAttendees: { type: Number, min: 1 },
    requiredFeatures: [{ type: String }],
    notes: { type: String, trim: true },
    tags: [{ type: String, trim: true }]
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
ReservationSchema.index({ roomId: 1, startTime: 1, endTime: 1 });
ReservationSchema.index({ userId: 1 });
ReservationSchema.index({ status: 1 });
ReservationSchema.index({ 'approvalStatus.isApprovalRequired': 1, status: 1 });
ReservationSchema.index({ startTime: 1, endTime: 1 });

// Validate that endTime is after startTime
ReservationSchema.pre('validate', function(next) {
  if (this.startTime >= this.endTime) {
    this.invalidate('endTime', 'End time must be after start time');
  }
  next();
});

module.exports = mongoose.model('Reservation', ReservationSchema);