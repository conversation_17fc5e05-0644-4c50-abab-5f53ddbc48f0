const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Category Schema
 * Hierarchical categorization system for assets
 */
const AssetCategorySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  parentCategory: {
    type: Schema.Types.ObjectId,
    ref: 'AssetCategory',
    default: null
  },
  icon: {
    type: String,
    trim: true
  },
  color: {
    type: String,
    trim: true,
    default: '#1976d2'
  },
  
  // Default settings for assets in this category
  defaultSettings: {
    depreciationRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 20
    },
    maintenanceSchedule: {
      type: String,
      enum: ['none', 'monthly', 'quarterly', 'semi_annual', 'annual', 'custom'],
      default: 'annual'
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low'
    }
  },

  // Custom fields template for this category
  customFieldsTemplate: [{
    name: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['text', 'number', 'date', 'boolean', 'url', 'select'],
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    options: [String], // For select type fields
    validation: {
      min: Number,
      max: Number,
      pattern: String
    }
  }],

  // Category management
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AssetCategorySchema.index({ name: 1 });
AssetCategorySchema.index({ parentCategory: 1 });
AssetCategorySchema.index({ isActive: 1 });

// Virtual for full path
AssetCategorySchema.virtual('fullPath').get(function() {
  // This would need to be populated to work properly
  return this.parentCategory ? `${this.parentCategory.name} > ${this.name}` : this.name;
});

// Virtual for child categories
AssetCategorySchema.virtual('children', {
  ref: 'AssetCategory',
  localField: '_id',
  foreignField: 'parentCategory'
});

// Virtual for asset count
AssetCategorySchema.virtual('assetCount', {
  ref: 'Asset',
  localField: '_id',
  foreignField: 'category',
  count: true
});

// Static methods
AssetCategorySchema.statics.findRootCategories = function() {
  return this.find({ parentCategory: null, isActive: true }).populate('children');
};

AssetCategorySchema.statics.findByParent = function(parentId) {
  return this.find({ parentCategory: parentId, isActive: true });
};

// Instance methods
AssetCategorySchema.methods.getFullPath = async function() {
  let path = [this.name];
  let current = this;
  
  while (current.parentCategory) {
    current = await this.model('AssetCategory').findById(current.parentCategory);
    if (current) {
      path.unshift(current.name);
    } else {
      break;
    }
  }
  
  return path.join(' > ');
};

AssetCategorySchema.methods.getAllChildren = async function() {
  const children = await this.model('AssetCategory').find({ parentCategory: this._id });
  let allChildren = [...children];
  
  for (const child of children) {
    const grandchildren = await child.getAllChildren();
    allChildren = allChildren.concat(grandchildren);
  }
  
  return allChildren;
};

// Pre-save middleware
AssetCategorySchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }
  next();
});

// Prevent circular references
AssetCategorySchema.pre('save', async function(next) {
  if (this.parentCategory) {
    const parent = await this.model('AssetCategory').findById(this.parentCategory);
    if (parent) {
      const parentPath = await parent.getAllChildren();
      const circularRef = parentPath.some(child => child._id.equals(this._id));
      if (circularRef) {
        const error = new Error('Circular reference detected in category hierarchy');
        return next(error);
      }
    }
  }
  next();
});

module.exports = mongoose.model('AssetCategory', AssetCategorySchema);