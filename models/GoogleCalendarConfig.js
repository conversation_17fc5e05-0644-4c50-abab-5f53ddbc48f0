const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Google Calendar Configuration Schema
 * Stores the configuration for the Google Calendar integration
 */
const GoogleCalendarConfigSchema = new Schema({
  clientId: {
    type: String,
    required: true
  },
  clientSecret: {
    type: String,
    required: true
  },
  redirectUri: {
    type: String,
    required: true
  },
  tokenPath: {
    type: String,
    default: './google-calendar-token.json'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
GoogleCalendarConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('GoogleCalendarConfig', GoogleCalendarConfigSchema);