const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Help Entry Schema
 * Represents a help/FAQ entry in the system
 */
const HelpEntrySchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['General', 'Account', 'Planning Center', 'Synology', 'Canva', 'Google Drive', 
           'GLPI', 'Mosyle Business', 'Dreo', 'UniFi Protect', 'UniFi Access', 'UniFi Network', 
           'Lenel S2 NetBox', 'Other'],
    default: 'General'
  },
  tags: [{
    type: String,
    trim: true
  }],
  externalLinks: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    url: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['website', 'document', 'video', 'other'],
      default: 'website'
    }
  }],
  googleDocId: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  viewCount: {
    type: Number,
    default: 0
  },
  helpfulCount: {
    type: Number,
    default: 0
  },
  unhelpfulCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Create text index for search functionality
HelpEntrySchema.index({ 
  title: 'text', 
  content: 'text', 
  tags: 'text',
  'externalLinks.title': 'text'
});

module.exports = mongoose.model('HelpEntry', HelpEntrySchema);