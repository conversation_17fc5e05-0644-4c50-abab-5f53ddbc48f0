const mongoose = require('mongoose');

const MosyleBusinessConfigSchema = new mongoose.Schema({
  apiKey: {
    type: String,
    required: true
  },
  domain: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
MosyleBusinessConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('mosyleBusinessConfig', MosyleBusinessConfigSchema);