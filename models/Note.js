const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Note Schema
 * Schema for user notes and to-dos
 */
const NoteSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['note', 'todo'],
    default: 'note'
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  completed: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: '#ffffff'
  },
  pinned: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true
  }],
  categories: [{
    type: String,
    trim: true
  }]
}, {
  timestamps: true
});

// Create indexes for faster queries
NoteSchema.index({ userId: 1 });
NoteSchema.index({ type: 1 });
NoteSchema.index({ completed: 1 });
NoteSchema.index({ pinned: 1 });
NoteSchema.index({ 'tags': 1 });
NoteSchema.index({ 'categories': 1 });

module.exports = mongoose.model('Note', NoteSchema);