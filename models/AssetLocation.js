const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Location Schema
 * Hierarchical location system for asset tracking
 */
const AssetLocationSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    unique: true,
    trim: true,
    sparse: true
  },
  description: {
    type: String,
    trim: true
  },
  locationType: {
    type: String,
    enum: ['building', 'floor', 'room', 'area', 'rack', 'desk', 'storage', 'vehicle', 'other'],
    required: true
  },
  parentLocation: {
    type: Schema.Types.ObjectId,
    ref: 'AssetLocation',
    default: null
  },

  // Physical attributes
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  coordinates: {
    latitude: {
      type: Number,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      min: -180,
      max: 180
    }
  },
  floorPlan: {
    imageUrl: String,
    coordinates: {
      x: Number,
      y: Number
    }
  },

  // Capacity and limits
  capacity: {
    type: Number,
    min: 0
  },
  maxAssets: {
    type: Number,
    min: 0
  },

  // Environmental conditions
  environmentalConditions: {
    temperature: {
      min: Number,
      max: Number,
      unit: {
        type: String,
        enum: ['celsius', 'fahrenheit'],
        default: 'fahrenheit'
      }
    },
    humidity: {
      min: Number,
      max: Number
    },
    notes: String
  },

  // Access and security
  accessRestricted: {
    type: Boolean,
    default: false
  },
  accessRequirements: [{
    type: String,
    enum: ['key_card', 'biometric', 'pin', 'physical_key', 'escort_required'],
  }],
  securityLevel: {
    type: String,
    enum: ['low', 'medium', 'high', 'maximum'],
    default: 'low'
  },

  // Responsible parties
  manager: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  contacts: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['primary', 'secondary', 'emergency']
    },
    notes: String
  }],

  // Integration with existing room system
  room: {
    type: Schema.Types.ObjectId,
    ref: 'Room'
  },
  building: {
    type: Schema.Types.ObjectId,
    ref: 'Building'
  },

  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Tracking
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AssetLocationSchema.index({ name: 1 });
AssetLocationSchema.index({ code: 1 });
AssetLocationSchema.index({ locationType: 1 });
AssetLocationSchema.index({ parentLocation: 1 });
AssetLocationSchema.index({ isActive: 1 });
AssetLocationSchema.index({ 'coordinates.latitude': 1, 'coordinates.longitude': 1 });

// Virtual for full path
AssetLocationSchema.virtual('fullPath').get(function() {
  return this.parentLocation ? `${this.parentLocation.name} > ${this.name}` : this.name;
});

// Virtual for child locations
AssetLocationSchema.virtual('children', {
  ref: 'AssetLocation',
  localField: '_id',
  foreignField: 'parentLocation'
});

// Virtual for assets at this location
AssetLocationSchema.virtual('assets', {
  ref: 'Asset',
  localField: '_id',
  foreignField: 'location'
});

// Virtual for asset count
AssetLocationSchema.virtual('assetCount', {
  ref: 'Asset',
  localField: '_id',
  foreignField: 'location',
  count: true
});

// Virtual for current occupancy percentage
AssetLocationSchema.virtual('occupancyPercentage').get(function() {
  if (!this.maxAssets || !this.assetCount) return 0;
  return Math.round((this.assetCount / this.maxAssets) * 100);
});

// Static methods
AssetLocationSchema.statics.findRootLocations = function() {
  return this.find({ parentLocation: null, isActive: true }).populate('children');
};

AssetLocationSchema.statics.findByType = function(locationType) {
  return this.find({ locationType, isActive: true });
};

AssetLocationSchema.statics.findByParent = function(parentId) {
  return this.find({ parentLocation: parentId, isActive: true });
};

AssetLocationSchema.statics.searchByName = function(searchTerm) {
  return this.find({ 
    name: { $regex: searchTerm, $options: 'i' }, 
    isActive: true 
  });
};

// Instance methods
AssetLocationSchema.methods.getFullPath = async function() {
  let path = [this.name];
  let current = this;
  
  while (current.parentLocation) {
    current = await this.model('AssetLocation').findById(current.parentLocation);
    if (current) {
      path.unshift(current.name);
    } else {
      break;
    }
  }
  
  return path.join(' > ');
};

AssetLocationSchema.methods.getAllChildren = async function() {
  const children = await this.model('AssetLocation').find({ parentLocation: this._id });
  let allChildren = [...children];
  
  for (const child of children) {
    const grandchildren = await child.getAllChildren();
    allChildren = allChildren.concat(grandchildren);
  }
  
  return allChildren;
};

AssetLocationSchema.methods.canAccommodateAssets = function(count = 1) {
  if (!this.maxAssets) return true;
  return (this.assetCount || 0) + count <= this.maxAssets;
};

// Pre-save middleware
AssetLocationSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }
  next();
});

// Prevent circular references
AssetLocationSchema.pre('save', async function(next) {
  if (this.parentLocation) {
    const parent = await this.model('AssetLocation').findById(this.parentLocation);
    if (parent) {
      const parentPath = await parent.getAllChildren();
      const circularRef = parentPath.some(child => child._id.equals(this._id));
      if (circularRef) {
        const error = new Error('Circular reference detected in location hierarchy');
        return next(error);
      }
    }
  }
  next();
});

module.exports = mongoose.model('AssetLocation', AssetLocationSchema);