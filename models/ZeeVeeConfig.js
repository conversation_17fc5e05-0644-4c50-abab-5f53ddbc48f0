const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Create Schema
const ZeeVeeConfigSchema = new Schema({
  host: {
    type: String,
    required: true
  },
  port: {
    type: Number,
    default: 80
  },
  username: {
    type: String,
    default: ''
  },
  password: {
    type: String,
    default: ''
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
ZeeVeeConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('zeeveeconfig', ZeeVeeConfigSchema);