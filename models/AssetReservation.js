const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Reservation Schema
 * Manages checkout/checkin system for portable or shared assets
 */
const AssetReservationSchema = new Schema({
  asset: {
    type: Schema.Types.ObjectId,
    ref: 'Asset',
    required: true
  },
  
  // Reservation details
  reservationNumber: {
    type: String,
    required: true,
    unique: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  purpose: {
    type: String,
    required: true,
    trim: true
  },

  // Who and when
  reservedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  onBehalfOf: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  department: {
    type: String,
    trim: true
  },

  // Timing
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  expectedReturnDate: {
    type: Date
  },
  actualCheckoutDate: {
    type: Date
  },
  actualReturnDate: {
    type: Date
  },
  
  // Status management
  status: {
    type: String,
    enum: [
      'pending',      // Waiting for approval
      'approved',     // Approved, waiting for pickup
      'active',       // Currently checked out
      'returned',     // Returned successfully
      'overdue',      // Past due date
      'cancelled',    // Cancelled before pickup
      'lost',         // Asset reported lost
      'damaged'       // Asset returned damaged
    ],
    default: 'pending'
  },

  // Approval workflow
  requiresApproval: {
    type: Boolean,
    default: true
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: {
    type: Date
  },
  approvalNotes: {
    type: String
  },

  // Checkout process
  checkedOutBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  checkoutNotes: {
    type: String
  },
  conditionAtCheckout: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor'],
    default: 'good'
  },
  
  // Return process
  checkedInBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  checkinNotes: {
    type: String
  },
  conditionAtReturn: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'damaged']
  },
  
  // Damage and issues
  damageReported: {
    type: Boolean,
    default: false
  },
  damageDescription: {
    type: String
  },
  damagePhotos: [{
    url: String,
    caption: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  repairCost: {
    type: Number,
    min: 0
  },

  // Location tracking
  expectedLocation: {
    type: Schema.Types.ObjectId,
    ref: 'AssetLocation'
  },
  currentLocation: {
    type: Schema.Types.ObjectId,
    ref: 'AssetLocation'
  },

  // Extensions and modifications
  extensions: [{
    requestedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    requestDate: {
      type: Date,
      default: Date.now
    },
    originalEndDate: Date,
    newEndDate: Date,
    reason: String,
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    approved: Boolean,
    approvalDate: Date
  }],

  // Terms and conditions
  termsAccepted: {
    type: Boolean,
    default: false
  },
  termsAcceptedDate: {
    type: Date
  },
  specialTerms: {
    type: String
  },

  // Notifications and reminders
  remindersSent: [{
    type: {
      type: String,
      enum: ['pickup_reminder', 'return_reminder', 'overdue_notice']
    },
    sentDate: {
      type: Date,
      default: Date.now
    },
    method: {
      type: String,
      enum: ['email', 'sms', 'push']
    }
  }],

  // Attachments
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Comments and communication
  comments: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    text: String,
    date: {
      type: Date,
      default: Date.now
    },
    internal: {
      type: Boolean,
      default: false
    }
  }],

  // Tracking
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AssetReservationSchema.index({ reservationNumber: 1 });
AssetReservationSchema.index({ asset: 1 });
AssetReservationSchema.index({ status: 1 });
AssetReservationSchema.index({ reservedBy: 1 });
AssetReservationSchema.index({ startDate: 1, endDate: 1 });
AssetReservationSchema.index({ expectedReturnDate: 1 });

// Virtual for duration
AssetReservationSchema.virtual('duration').get(function() {
  if (!this.startDate || !this.endDate) return null;
  return Math.ceil((this.endDate - this.startDate) / (1000 * 60 * 60 * 24));
});

// Virtual for days overdue
AssetReservationSchema.virtual('daysOverdue').get(function() {
  if (this.status !== 'overdue' || !this.expectedReturnDate) return 0;
  const now = new Date();
  return Math.ceil((now - this.expectedReturnDate) / (1000 * 60 * 60 * 24));
});

// Virtual for actual duration
AssetReservationSchema.virtual('actualDuration').get(function() {
  if (!this.actualCheckoutDate || !this.actualReturnDate) return null;
  return Math.ceil((this.actualReturnDate - this.actualCheckoutDate) / (1000 * 60 * 60 * 24));
});

// Pre-save middleware
AssetReservationSchema.pre('save', function(next) {
  // Update expected return date if not set
  if (!this.expectedReturnDate) {
    this.expectedReturnDate = this.endDate;
  }

  // Auto-detect overdue status
  if (this.status === 'active' && this.expectedReturnDate < new Date()) {
    this.status = 'overdue';
  }

  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }

  next();
});

// Static method to generate reservation number
AssetReservationSchema.statics.generateReservationNumber = async function() {
  const currentYear = new Date().getFullYear();
  const prefix = `RSV${currentYear}`;
  
  const lastReservation = await this.findOne(
    { reservationNumber: { $regex: `^${prefix}` } },
    {},
    { sort: { reservationNumber: -1 } }
  );

  let nextNumber = 1;
  if (lastReservation) {
    const lastNumber = parseInt(lastReservation.reservationNumber.replace(prefix, ''));
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
};

// Static methods
AssetReservationSchema.statics.findByAsset = function(assetId, options = {}) {
  const query = { asset: assetId };
  if (options.status) query.status = options.status;
  if (options.active) {
    query.status = { $in: ['approved', 'active', 'overdue'] };
  }
  
  return this.find(query)
    .populate('reservedBy', 'name email')
    .populate('asset', 'name assetTag')
    .sort({ startDate: -1 });
};

AssetReservationSchema.statics.findOverdue = function() {
  return this.find({
    status: 'overdue'
  })
    .populate('asset', 'name assetTag')
    .populate('reservedBy', 'name email')
    .sort({ expectedReturnDate: 1 });
};

AssetReservationSchema.statics.findByUser = function(userId, options = {}) {
  const query = { reservedBy: userId };
  if (options.status) query.status = options.status;
  
  return this.find(query)
    .populate('asset', 'name assetTag category')
    .sort({ startDate: -1 });
};

AssetReservationSchema.statics.findConflicts = function(assetId, startDate, endDate, excludeId = null) {
  const query = {
    asset: assetId,
    status: { $in: ['approved', 'active'] },
    $or: [
      {
        startDate: { $lte: endDate },
        endDate: { $gte: startDate }
      }
    ]
  };
  
  if (excludeId) {
    query._id = { $ne: excludeId };
  }
  
  return this.find(query);
};

// Instance methods
AssetReservationSchema.methods.approve = function(approvedBy, notes = '') {
  this.status = 'approved';
  this.approvedBy = approvedBy;
  this.approvalDate = new Date();
  this.approvalNotes = notes;
  this.lastModifiedBy = approvedBy;
  return this.save();
};

AssetReservationSchema.methods.checkout = function(checkedOutBy, condition, notes = '') {
  this.status = 'active';
  this.checkedOutBy = checkedOutBy;
  this.actualCheckoutDate = new Date();
  this.conditionAtCheckout = condition;
  this.checkoutNotes = notes;
  this.lastModifiedBy = checkedOutBy;
  return this.save();
};

AssetReservationSchema.methods.return = function(checkedInBy, condition, notes = '', damageReported = false, damageDescription = '') {
  this.status = damageReported ? 'damaged' : 'returned';
  this.checkedInBy = checkedInBy;
  this.actualReturnDate = new Date();
  this.conditionAtReturn = condition;
  this.checkinNotes = notes;
  this.damageReported = damageReported;
  this.damageDescription = damageDescription;
  this.lastModifiedBy = checkedInBy;
  return this.save();
};

AssetReservationSchema.methods.extend = function(newEndDate, reason, requestedBy) {
  const extension = {
    requestedBy: requestedBy,
    originalEndDate: this.endDate,
    newEndDate: newEndDate,
    reason: reason,
    approved: false
  };
  
  this.extensions.push(extension);
  this.lastModifiedBy = requestedBy;
  return this.save();
};

AssetReservationSchema.methods.approveExtension = function(extensionIndex, approvedBy) {
  if (this.extensions[extensionIndex]) {
    this.extensions[extensionIndex].approved = true;
    this.extensions[extensionIndex].approvedBy = approvedBy;
    this.extensions[extensionIndex].approvalDate = new Date();
    
    // Update the reservation end date
    this.endDate = this.extensions[extensionIndex].newEndDate;
    this.expectedReturnDate = this.extensions[extensionIndex].newEndDate;
    
    this.lastModifiedBy = approvedBy;
  }
  return this.save();
};

module.exports = mongoose.model('AssetReservation', AssetReservationSchema);