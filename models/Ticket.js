const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Ticket Schema
 * Represents a support ticket in the system
 */
const TicketSchema = new Schema({
  // Basic ticket information
  ticketNumber: {
    type: String,
    unique: true,
    required: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['open', 'pending', 'on_hold', 'resolved', 'closed'],
    default: 'open',
    required: true
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal',
    required: true
  },
  type: {
    type: String,
    enum: ['incident', 'request', 'problem', 'change'],
    default: 'incident',
    required: true
  },
  
  // Assignment and ownership
  requester: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: function() {
      return !this.requesterEmail;
    }
  },
  requesterEmail: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        // Only validate if provided
        if (!v) return true;
        // Basic email validation
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
      },
      message: props => `${props.value} is not a valid email address!`
    },
    required: function() {
      return !this.requester;
    }
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedGroup: {
    type: Schema.Types.ObjectId,
    ref: 'Group'
  },
  
  // Categorization
  category: {
    type: String,
    trim: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  
  // Time tracking
  dueDate: {
    type: Date
  },
  resolvedAt: {
    type: Date
  },
  closedAt: {
    type: Date
  },
  firstResponseAt: {
    type: Date
  },
  
  // Email integration
  emailThreadId: {
    type: String,
    trim: true
  },
  incomingEmailAddress: {
    type: String,
    trim: true
  },
  lastEmailMessageId: {
    type: String,
    trim: true
  },
  
  // Followers - users who receive notifications
  followers: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],
  
  // Custom fields for flexibility
  customFields: {
    type: Map,
    of: Schema.Types.Mixed,
    default: new Map()
  },
  
  // Source tracking
  source: {
    type: String,
    enum: ['email', 'form', 'portal', 'api', 'webhook'],
    default: 'portal'
  },
  sourceMetadata: {
    type: Map,
    of: Schema.Types.Mixed,
    default: new Map()
  },
  
  // SLA tracking
  slaTarget: {
    firstResponseDue: Date,
    resolutionDue: Date
  },
  slaBreach: {
    firstResponse: {
      type: Boolean,
      default: false
    },
    resolution: {
      type: Boolean,
      default: false
    }
  },
  
  // Satisfaction tracking
  satisfaction: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    submittedAt: Date,
    submittedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true
});

// Indexes for performance
TicketSchema.index({ ticketNumber: 1 });
TicketSchema.index({ status: 1 });
TicketSchema.index({ priority: 1 });
TicketSchema.index({ assignedTo: 1 });
TicketSchema.index({ requester: 1 });
TicketSchema.index({ category: 1 });
TicketSchema.index({ tags: 1 });
TicketSchema.index({ createdAt: -1 });
TicketSchema.index({ dueDate: 1 });
TicketSchema.index({ emailThreadId: 1 });

// Text search index
TicketSchema.index({ 
  subject: 'text', 
  description: 'text',
  tags: 'text'
});

// Pre-save middleware to generate ticket number
TicketSchema.pre('save', async function(next) {
  if (this.isNew && !this.ticketNumber) {
    try {
      const count = await this.constructor.countDocuments();
      this.ticketNumber = `CSF-${String(count + 1).padStart(6, '0')}`;
    } catch (error) {
      return next(error);
    }
  }
  next();
});

// Virtual for ticket age in hours
TicketSchema.virtual('ageInHours').get(function() {
  return Math.floor((Date.now() - this.createdAt) / (1000 * 60 * 60));
});

// Virtual for time to first response
TicketSchema.virtual('timeToFirstResponseHours').get(function() {
  if (!this.firstResponseAt) return null;
  return Math.floor((this.firstResponseAt - this.createdAt) / (1000 * 60 * 60));
});

// Virtual for resolution time
TicketSchema.virtual('resolutionTimeHours').get(function() {
  if (!this.resolvedAt) return null;
  return Math.floor((this.resolvedAt - this.createdAt) / (1000 * 60 * 60));
});

module.exports = mongoose.model('Ticket', TicketSchema);