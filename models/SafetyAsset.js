const mongoose = require('mongoose');

const SafetyAssetSchema = new mongoose.Schema({
  // Basic identification
  assetType: {
    type: String,
    required: true,
    enum: ['fire_extinguisher', 'aed', 'emergency_kit', 'eyewash_station', 'first_aid_kit', 'emergency_shower'],
    index: true
  },
  assetId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: true
  },
  
  // Location references
  buildingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Building',
    required: true,
    index: true
  },
  floorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Floor',
    required: true,
    index: true
  },
  room: {
    type: String,
    required: false
  },
  iconId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FloorplanIcon',
    required: false,
    index: true
  },
  
  // Specifications
  specifications: {
    manufacturer: String,
    model: String,
    capacity: String, // e.g., "5lbs", "Adult/Pediatric", "10 gallon"
    agent: String, // Fire extinguisher agent type (ABC, CO2, etc.)
    serialNumber: String,
    purchaseDate: Date,
    warrantyExpiry: Date
  },
  
  // Inspection tracking
  inspection: {
    frequency: {
      type: Number,
      required: true,
      default: 30 // Days between inspections
    },
    lastInspection: {
      type: Date,
      required: false
    },
    nextInspection: {
      type: Date,
      required: false
    },
    inspector: String,
    status: {
      type: String,
      enum: ['current', 'due', 'overdue', 'requires_service'],
      default: 'due'
    },
    notes: String
  },
  
  // Service history
  serviceHistory: [{
    date: {
      type: Date,
      required: true
    },
    type: {
      type: String,
      enum: ['inspection', 'maintenance', 'replacement', 'repair'],
      required: true
    },
    technician: String,
    notes: String,
    nextServiceDate: Date,
    cost: Number,
    attachments: [{
      filename: String,
      url: String,
      uploadedBy: String,
      uploadedAt: Date
    }]
  }],
  
  // Status and alerts
  status: {
    type: String,
    enum: ['active', 'out_of_service', 'needs_replacement', 'decommissioned'],
    default: 'active',
    index: true
  },
  alerts: [{
    type: {
      type: String,
      enum: ['inspection_due', 'inspection_overdue', 'needs_service', 'warranty_expiring', 'replacement_due']
    },
    severity: {
      type: String,
      enum: ['info', 'warning', 'critical'],
      default: 'info'
    },
    message: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    acknowledged: {
      type: Boolean,
      default: false
    },
    acknowledgedBy: String,
    acknowledgedAt: Date
  }],
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for performance
SafetyAssetSchema.index({ buildingId: 1, floorId: 1 });
SafetyAssetSchema.index({ assetType: 1, status: 1 });
SafetyAssetSchema.index({ 'inspection.status': 1, 'inspection.nextInspection': 1 });
SafetyAssetSchema.index({ 'alerts.severity': 1, 'alerts.acknowledged': 1 });

// Pre-save middleware to calculate next inspection date
SafetyAssetSchema.pre('save', function(next) {
  if (this.inspection.lastInspection && this.inspection.frequency) {
    const nextDate = new Date(this.inspection.lastInspection);
    nextDate.setDate(nextDate.getDate() + this.inspection.frequency);
    this.inspection.nextInspection = nextDate;
    
    // Update inspection status based on dates
    const now = new Date();
    const daysUntilDue = Math.ceil((nextDate - now) / (1000 * 60 * 60 * 24));
    
    if (daysUntilDue < -30) {
      this.inspection.status = 'overdue';
    } else if (daysUntilDue <= 0) {
      this.inspection.status = 'due';
    } else if (daysUntilDue <= 7) {
      this.inspection.status = 'due';
    } else {
      this.inspection.status = 'current';
    }
  }
  next();
});

// Static methods for common queries
SafetyAssetSchema.statics.findByType = function(assetType, options = {}) {
  return this.find({ assetType, ...options });
};

SafetyAssetSchema.statics.findDueForInspection = function() {
  return this.find({
    'inspection.status': { $in: ['due', 'overdue'] }
  }).populate('buildingId floorId');
};

SafetyAssetSchema.statics.findOverdue = function() {
  return this.find({
    'inspection.status': 'overdue'
  }).populate('buildingId floorId');
};

SafetyAssetSchema.statics.findByLocation = function(buildingId, floorId) {
  const filter = { buildingId };
  if (floorId) {
    filter.floorId = floorId;
  }
  return this.find(filter).populate('buildingId floorId iconId');
};

// Instance methods
SafetyAssetSchema.methods.recordInspection = function(inspector, notes = '', nextServiceDate = null) {
  const inspection = {
    date: new Date(),
    type: 'inspection',
    technician: inspector,
    notes: notes,
    nextServiceDate: nextServiceDate
  };
  
  this.serviceHistory.push(inspection);
  this.inspection.lastInspection = new Date();
  this.inspection.inspector = inspector;
  this.inspection.notes = notes;
  
  // Clear inspection alerts
  this.alerts = this.alerts.filter(alert => 
    !['inspection_due', 'inspection_overdue'].includes(alert.type)
  );
  
  return this.save();
};

SafetyAssetSchema.methods.addAlert = function(type, severity, message) {
  this.alerts.push({
    type,
    severity,
    message,
    createdAt: new Date()
  });
  return this.save();
};

SafetyAssetSchema.methods.acknowledgeAlert = function(alertId, userId) {
  const alert = this.alerts.id(alertId);
  if (alert) {
    alert.acknowledged = true;
    alert.acknowledgedBy = userId;
    alert.acknowledgedAt = new Date();
  }
  return this.save();
};

// Virtual for days until next inspection
SafetyAssetSchema.virtual('daysUntilInspection').get(function() {
  if (!this.inspection.nextInspection) return null;
  
  const now = new Date();
  const diffTime = this.inspection.nextInspection - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Ensure virtual fields are serialized
SafetyAssetSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('SafetyAsset', SafetyAssetSchema);