const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Contact Sync Log Schema
 * Tracks history and results of contact synchronization operations
 */
const ContactSyncLogSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  provider: {
    type: String,
    enum: ['google', 'apple'],
    required: true
  },
  startTime: {
    type: Date,
    default: Date.now
  },
  endTime: {
    type: Date
  },
  status: {
    type: String,
    enum: ['in_progress', 'success', 'error'],
    default: 'in_progress'
  },
  contactsAdded: {
    type: Number,
    default: 0
  },
  contactsUpdated: {
    type: Number,
    default: 0
  },
  contactsRemoved: {
    type: Number,
    default: 0
  },
  error: {
    type: String
  },
  details: {
    type: String
  }
}, { timestamps: true });

// Create indexes for faster queries
ContactSyncLogSchema.index({ user: 1, provider: 1 });
ContactSyncLogSchema.index({ startTime: -1 });
ContactSyncLogSchema.index({ status: 1 });

module.exports = mongoose.model('ContactSyncLog', ContactSyncLogSchema);