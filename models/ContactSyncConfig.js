const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Contact Sync Configuration Schema
 * Stores user preferences and authentication tokens for contact synchronization
 */
const ContactSyncConfigSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  provider: {
    type: String,
    enum: ['google', 'apple'],
    required: true
  },
  enabled: {
    type: Boolean,
    default: true
  },
  syncFrequency: {
    type: Number, // in minutes
    default: 60
  },
  lastSyncTime: {
    type: Date
  },
  syncStatus: {
    type: String,
    enum: ['idle', 'in_progress', 'success', 'error'],
    default: 'idle'
  },
  syncError: {
    type: String
  },
  // For Google integration
  googleRefreshToken: {
    type: String
  },
  googleResourceName: {
    type: String // The resource name of the contact group in Google
  },
  // For Apple integration (if needed)
  appleUserIdentifier: {
    type: String
  },
  // Sync filters
  syncCategories: {
    type: [String],
    enum: ['business', 'maintenance', 'support', 'emergency', 'other'],
    default: ['business', 'maintenance', 'support', 'emergency', 'other']
  },
  // Mapping of local contact IDs to provider resource IDs
  contactMappings: {
    type: Map,
    of: String,
    default: new Map()
  }
}, { timestamps: true });

// Create index for faster queries
ContactSyncConfigSchema.index({ user: 1, provider: 1 }, { unique: true });

module.exports = mongoose.model('ContactSyncConfig', ContactSyncConfigSchema);