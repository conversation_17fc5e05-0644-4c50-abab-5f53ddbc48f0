const mongoose = require('mongoose');

const VolunteerResourceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  category: {
    type: String,
    required: true,
    enum: [
      'cleaning_supplies',    // Brooms, mops, vacuum cleaners
      'event_supplies',       // Tables, chairs, linens
      'av_equipment',         // Microphones, extension cords, batteries
      'signage',             // Signs, banners, easels
      'kitchen_supplies',     // Serving items, utensils
      'office_supplies',      // Paper, pens, markers
      'safety_equipment',     // First aid, flashlights
      'maintenance_tools',    // Basic tools, ladder access
      'setup_equipment',      // Dollies, carts, rope
      'storage_area',         // General storage rooms
      'other'
    ],
    default: 'other'
  },
  subcategory: {
    type: String,
    trim: true,
    maxlength: 50
  },
  
  // Location information
  buildingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Building',
    required: true
  },
  floorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Floor',
    required: true
  },
  room: {
    type: String,
    required: true,
    trim: true
  },
  location: {
    specific: { type: String }, // "Top shelf", "Behind door", etc.
    iconId: { type: mongoose.Schema.Types.ObjectId, ref: 'FloorplanIcon' },
    coordinates: {
      x: { type: Number },
      y: { type: Number }
    }
  },
  
  // Resource details
  resourceType: {
    type: String,
    enum: ['consumable', 'equipment', 'space', 'tool'],
    required: true
  },
  quantity: {
    available: { type: Number, default: 1 },
    reserved: { type: Number, default: 0 },
    outOfOrder: { type: Number, default: 0 },
    unit: { type: String, default: 'items' } // 'items', 'boxes', 'rolls', etc.
  },
  
  // Access and availability
  accessibility: {
    requiresKey: { type: Boolean, default: false },
    keyLocation: { type: String },
    accessInstructions: { type: String },
    restrictedAccess: { type: Boolean, default: false },
    allowedRoles: [{ type: String }] // 'volunteer', 'staff', 'admin'
  },
  availability: {
    alwaysAvailable: { type: Boolean, default: true },
    schedule: [{
      dayOfWeek: { type: Number, min: 0, max: 6 }, // 0 = Sunday
      startTime: { type: String }, // "09:00"
      endTime: { type: String }    // "17:00"
    }],
    specialNotes: { type: String }
  },
  
  // Resource specifications
  specifications: {
    brand: { type: String },
    model: { type: String },
    size: { type: String },
    color: { type: String },
    condition: { 
      type: String, 
      enum: ['excellent', 'good', 'fair', 'poor', 'needs_replacement'],
      default: 'good'
    },
    notes: { type: String }
  },
  
  // Maintenance and tracking
  maintenance: {
    lastChecked: { type: Date },
    nextCheck: { type: Date },
    checkFrequency: { type: Number }, // days
    checkedBy: { type: String },
    maintenanceNotes: { type: String },
    replacementCost: { type: Number },
    vendor: { type: String }
  },
  
  // Usage tracking
  usage: {
    timesUsed: { type: Number, default: 0 },
    lastUsed: { type: Date },
    averageUsagePerMonth: { type: Number, default: 0 },
    popularityScore: { type: Number, default: 0 },
    usageHistory: [{
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      eventId: { type: mongoose.Schema.Types.ObjectId, ref: 'ChurchEvent' },
      usedAt: { type: Date, default: Date.now },
      quantity: { type: Number, default: 1 },
      notes: { type: String }
    }]
  },
  
  // Search and tagging
  tags: [{ 
    type: String, 
    trim: true,
    lowercase: true 
  }],
  searchKeywords: [{ 
    type: String, 
    trim: true,
    lowercase: true 
  }],
  
  // Images and documentation
  photos: [{
    url: { type: String, required: true },
    caption: { type: String },
    isPrimary: { type: Boolean, default: false },
    uploadedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    uploadedAt: { type: Date, default: Date.now }
  }],
  documentation: [{
    title: { type: String, required: true },
    type: { type: String, enum: ['manual', 'instructions', 'video', 'link'] },
    url: { type: String },
    description: { type: String }
  }],
  
  // Status and alerts
  status: {
    type: String,
    enum: ['available', 'in_use', 'maintenance', 'missing', 'broken', 'reserved'],
    default: 'available'
  },
  alerts: [{
    type: { 
      type: String, 
      enum: ['low_stock', 'maintenance_due', 'missing', 'broken', 'needs_replacement'] 
    },
    severity: { type: String, enum: ['info', 'warning', 'critical'], default: 'info' },
    message: { type: String, required: true },
    acknowledged: { type: Boolean, default: false },
    acknowledgedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    acknowledgedAt: { type: Date },
    createdAt: { type: Date, default: Date.now }
  }],
  
  // Created/updated tracking
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
VolunteerResourceSchema.index({ category: 1, status: 1 });
VolunteerResourceSchema.index({ buildingId: 1, floorId: 1, room: 1 });
VolunteerResourceSchema.index({ tags: 1 });
VolunteerResourceSchema.index({ searchKeywords: 1 });
VolunteerResourceSchema.index({ 'usage.popularityScore': -1 });
VolunteerResourceSchema.index({ 'usage.timesUsed': -1 });
VolunteerResourceSchema.index({ status: 1, category: 1 });

// Text search index
VolunteerResourceSchema.index({
  name: 'text',
  description: 'text',
  tags: 'text',
  searchKeywords: 'text',
  'specifications.notes': 'text'
});

// Virtual for total quantity
VolunteerResourceSchema.virtual('totalQuantity').get(function() {
  return this.quantity.available + this.quantity.reserved + this.quantity.outOfOrder;
});

// Virtual for availability status
VolunteerResourceSchema.virtual('isAvailableNow').get(function() {
  if (this.status !== 'available' || this.quantity.available === 0) {
    return false;
  }
  
  if (this.availability.alwaysAvailable) {
    return true;
  }
  
  // Check current time against schedule
  const now = new Date();
  const currentDay = now.getDay();
  const currentTime = now.toTimeString().substring(0, 5); // "HH:MM"
  
  return this.availability.schedule.some(slot => 
    slot.dayOfWeek === currentDay &&
    currentTime >= slot.startTime &&
    currentTime <= slot.endTime
  );
});

// Virtual for maintenance status
VolunteerResourceSchema.virtual('maintenanceStatus').get(function() {
  if (!this.maintenance.nextCheck) return 'unknown';
  
  const now = new Date();
  const daysUntilCheck = Math.ceil((this.maintenance.nextCheck - now) / (1000 * 60 * 60 * 24));
  
  if (daysUntilCheck < 0) return 'overdue';
  if (daysUntilCheck <= 7) return 'due_soon';
  return 'current';
});

// Method to record usage
VolunteerResourceSchema.methods.recordUsage = function(userId, quantity = 1, eventId = null, notes = '') {
  this.usage.usageHistory.push({
    userId,
    eventId,
    quantity,
    notes
  });
  
  this.usage.timesUsed += 1;
  this.usage.lastUsed = new Date();
  
  // Update availability if it's a consumable
  if (this.resourceType === 'consumable') {
    this.quantity.available = Math.max(0, this.quantity.available - quantity);
    
    // Create low stock alert if needed
    if (this.quantity.available <= 5 && this.quantity.available > 0) {
      this.addAlert('low_stock', 'warning', `Only ${this.quantity.available} ${this.quantity.unit} remaining`);
    } else if (this.quantity.available === 0) {
      this.status = 'missing';
      this.addAlert('missing', 'critical', 'Resource is out of stock');
    }
  }
  
  // Calculate popularity score
  this.updatePopularityScore();
};

// Method to add alerts
VolunteerResourceSchema.methods.addAlert = function(type, severity, message) {
  // Check if similar alert already exists
  const existingAlert = this.alerts.find(alert => 
    alert.type === type && !alert.acknowledged
  );
  
  if (!existingAlert) {
    this.alerts.push({ type, severity, message });
  }
};

// Method to update popularity score
VolunteerResourceSchema.methods.updatePopularityScore = function() {
  const recencyWeight = 0.4;
  const frequencyWeight = 0.6;
  
  // Calculate recency score (0-1, where 1 is very recent)
  const daysSinceLastUse = this.usage.lastUsed ? 
    (Date.now() - this.usage.lastUsed.getTime()) / (1000 * 60 * 60 * 24) : 365;
  const recencyScore = Math.max(0, 1 - (daysSinceLastUse / 90)); // 90 day decay
  
  // Calculate frequency score (0-1, normalized)
  const maxUsage = 50; // Normalize against this maximum
  const frequencyScore = Math.min(this.usage.timesUsed / maxUsage, 1);
  
  this.usage.popularityScore = (recencyScore * recencyWeight + frequencyScore * frequencyWeight) * 100;
};

// Static method to find nearest resources
VolunteerResourceSchema.statics.findNearest = async function(floorId, category = null, limit = 5) {
  const filter = { 
    floorId,
    status: 'available',
    'quantity.available': { $gt: 0 }
  };
  
  if (category) {
    filter.category = category;
  }
  
  return this.find(filter)
    .sort({ 'usage.popularityScore': -1 })
    .limit(limit)
    .populate('buildingId', 'name')
    .populate('floorId', 'name level');
};

// Static method to search resources
VolunteerResourceSchema.statics.searchResources = async function(query, filters = {}) {
  const searchFilter = {
    $text: { $search: query },
    ...filters
  };
  
  return this.find(searchFilter, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' }, 'usage.popularityScore': -1 })
    .populate('buildingId', 'name')
    .populate('floorId', 'name level');
};

// Static method to get resources by category
VolunteerResourceSchema.statics.getByCategory = async function(category, buildingId = null) {
  const filter = { category, status: { $ne: 'missing' } };
  
  if (buildingId) {
    filter.buildingId = buildingId;
  }
  
  return this.find(filter)
    .sort({ 'usage.popularityScore': -1, name: 1 })
    .populate('buildingId', 'name')
    .populate('floorId', 'name level');
};

// Static method to get maintenance alerts
VolunteerResourceSchema.statics.getMaintenanceAlerts = async function() {
  const now = new Date();
  const sevenDaysFromNow = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
  
  return this.find({
    $or: [
      { 'maintenance.nextCheck': { $lt: now } }, // Overdue
      { 'maintenance.nextCheck': { $lt: sevenDaysFromNow } }, // Due soon
      { 'alerts.type': 'maintenance_due', 'alerts.acknowledged': false }
    ]
  })
  .populate('buildingId', 'name')
  .populate('floorId', 'name level');
};

// Pre-save middleware to update search keywords
VolunteerResourceSchema.pre('save', function(next) {
  // Auto-generate search keywords
  const keywords = new Set();
  
  // Add name words
  if (this.name) {
    this.name.toLowerCase().split(/\s+/).forEach(word => keywords.add(word));
  }
  
  // Add category and subcategory
  keywords.add(this.category);
  if (this.subcategory) {
    keywords.add(this.subcategory.toLowerCase());
  }
  
  // Add specifications
  if (this.specifications.brand) keywords.add(this.specifications.brand.toLowerCase());
  if (this.specifications.model) keywords.add(this.specifications.model.toLowerCase());
  if (this.specifications.color) keywords.add(this.specifications.color.toLowerCase());
  
  // Add room
  if (this.room) keywords.add(this.room.toLowerCase());
  
  this.searchKeywords = Array.from(keywords);
  next();
});

module.exports = mongoose.model('VolunteerResource', VolunteerResourceSchema);