const mongoose = require('mongoose');

const AVChecklistSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  checklistType: {
    type: String,
    required: true,
    enum: ['pre_service', 'post_service', 'weekly_maintenance', 'monthly_maintenance', 'event_setup', 'emergency_check', 'custom'],
    default: 'pre_service'
  },
  serviceType: {
    type: String,
    enum: ['sunday_service', 'youth_service', 'special_event', 'conference', 'wedding', 'funeral', 'general'],
    default: 'general'
  },
  
  // Schedule and timing
  schedule: {
    frequency: {
      type: String,
      enum: ['one_time', 'weekly', 'monthly', 'event_based', 'as_needed'],
      default: 'event_based'
    },
    dayOfWeek: { type: Number, min: 0, max: 6 }, // 0 = Sunday
    timeOfDay: { type: String }, // "09:00"
    minutesBeforeEvent: { type: Number }, // For event-based checklists
    autoSchedule: { type: Boolean, default: false }
  },
  
  // Checklist items
  items: [{
    id: { type: String, required: true },
    title: { type: String, required: true, maxlength: 200 },
    description: { type: String, maxlength: 500 },
    category: {
      type: String,
      enum: ['audio', 'video', 'lighting', 'recording', 'streaming', 'presentation', 'microphones', 'monitors', 'general'],
      required: true
    },
    priority: {
      type: String,
      enum: ['critical', 'high', 'medium', 'low'],
      default: 'medium'
    },
    estimatedTime: { type: Number }, // minutes
    order: { type: Number, required: true },
    
    // Equipment references
    equipmentIds: [{ type: mongoose.Schema.Types.ObjectId, ref: 'AVEquipment' }],
    equipmentNames: [{ type: String }], // Fallback for non-tracked equipment
    
    // Instructions and guidance
    instructions: { type: String },
    troubleshootingTips: [{ type: String }],
    warningNotes: [{ type: String }],
    
    // Verification requirements
    verification: {
      required: { type: Boolean, default: false },
      type: { type: String, enum: ['visual', 'audio_test', 'functional_test', 'measurement', 'photo'], default: 'visual' },
      expectedResult: { type: String },
      acceptableLevels: {
        min: { type: Number },
        max: { type: Number },
        unit: { type: String }
      }
    },
    
    // Dependencies
    dependencies: [{ type: String }], // Other item IDs that must be completed first
    blockers: [{ type: String }], // Items that cannot be done simultaneously
    
    // Automation
    canBeAutomated: { type: Boolean, default: false },
    automationScript: { type: String },
    
    // Status tracking
    isActive: { type: Boolean, default: true },
    isOptional: { type: Boolean, default: false },
    skipConditions: [{
      condition: { type: String }, // 'no_video_service', 'external_tech_team', etc.
      description: { type: String }
    }]
  }],
  
  // Completion tracking
  completions: [{
    completedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    eventId: { type: mongoose.Schema.Types.ObjectId, ref: 'ChurchEvent' },
    startedAt: { type: Date, required: true },
    completedAt: { type: Date },
    status: {
      type: String,
      enum: ['in_progress', 'completed', 'failed', 'skipped', 'cancelled'],
      default: 'in_progress'
    },
    
    // Item-level completion
    itemResults: [{
      itemId: { type: String, required: true },
      status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'skipped', 'blocked'],
        default: 'pending'
      },
      completedAt: { type: Date },
      notes: { type: String },
      issues: [{ 
        description: { type: String },
        severity: { type: String, enum: ['info', 'warning', 'error', 'critical'] },
        resolved: { type: Boolean, default: false },
        resolution: { type: String }
      }],
      verificationData: {
        result: { type: String },
        measurement: { type: Number },
        photoUrl: { type: String },
        passed: { type: Boolean }
      },
      timeSpent: { type: Number } // minutes
    }],
    
    // Overall results
    totalTimeSpent: { type: Number }, // minutes
    issuesFound: { type: Number, default: 0 },
    criticalIssues: { type: Number, default: 0 },
    overallNotes: { type: String },
    recommendations: [{ type: String }],
    
    // Follow-up actions
    followUpRequired: { type: Boolean, default: false },
    followUpActions: [{
      action: { type: String, required: true },
      assignedTo: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      dueDate: { type: Date },
      priority: { type: String, enum: ['low', 'medium', 'high', 'critical'], default: 'medium' },
      completed: { type: Boolean, default: false }
    }]
  }],
  
  // Analytics and optimization
  analytics: {
    averageCompletionTime: { type: Number }, // minutes
    successRate: { type: Number }, // percentage
    commonIssues: [{
      itemId: { type: String },
      issueDescription: { type: String },
      frequency: { type: Number },
      lastOccurred: { type: Date }
    }],
    optimizationSuggestions: [{ type: String }],
    lastAnalyzed: { type: Date }
  },
  
  // Access control
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTeams: [{
    teamName: { type: String },
    role: { type: String, enum: ['primary', 'backup', 'reviewer'] },
    members: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }]
  }],
  requiredRoles: [{
    type: String,
    enum: ['admin', 'av_tech', 'facilities', 'volunteer', 'staff']
  }],
  
  // Template and versioning
  isTemplate: { type: Boolean, default: false },
  templateVersion: { type: String },
  parentTemplate: { type: mongoose.Schema.Types.ObjectId, ref: 'AVChecklist' },
  customizations: [{
    field: { type: String },
    originalValue: { type: mongoose.Schema.Types.Mixed },
    customValue: { type: mongoose.Schema.Types.Mixed },
    reason: { type: String }
  }],
  
  // Integration settings
  integrations: {
    calendar: {
      googleCalendarId: { type: String },
      createEvents: { type: Boolean, default: false },
      reminderMinutes: [{ type: Number }] // [15, 60, 1440] for 15min, 1hr, 1day
    },
    notifications: {
      emailNotifications: { type: Boolean, default: true },
      slackWebhook: { type: String },
      teamsWebhook: { type: String }
    },
    avEquipment: {
      autoCheckStatus: { type: Boolean, default: false },
      includeMaintenanceAlerts: { type: Boolean, default: true }
    }
  }
}, {
  timestamps: true
});

// Indexes
AVChecklistSchema.index({ checklistType: 1, serviceType: 1 });
AVChecklistSchema.index({ 'schedule.frequency': 1, 'schedule.autoSchedule': 1 });
AVChecklistSchema.index({ 'assignedTeams.members': 1 });
AVChecklistSchema.index({ isTemplate: 1 });
AVChecklistSchema.index({ 'completions.completedBy': 1, 'completions.completedAt': -1 });

// Virtual for active items
AVChecklistSchema.virtual('activeItems').get(function() {
  return this.items.filter(item => item.isActive);
});

// Virtual for critical items
AVChecklistSchema.virtual('criticalItems').get(function() {
  return this.items.filter(item => item.priority === 'critical' && item.isActive);
});

// Virtual for estimated total time
AVChecklistSchema.virtual('estimatedTotalTime').get(function() {
  return this.items
    .filter(item => item.isActive)
    .reduce((total, item) => total + (item.estimatedTime || 0), 0);
});

// Virtual for completion rate
AVChecklistSchema.virtual('completionRate').get(function() {
  if (this.completions.length === 0) return 0;
  
  const successful = this.completions.filter(c => c.status === 'completed').length;
  return Math.round((successful / this.completions.length) * 100);
});

// Method to start a new checklist execution
AVChecklistSchema.methods.startExecution = function(userId, eventId = null) {
  const execution = {
    completedBy: userId,
    eventId,
    startedAt: new Date(),
    status: 'in_progress',
    itemResults: this.activeItems.map(item => ({
      itemId: item.id,
      status: 'pending'
    }))
  };
  
  this.completions.push(execution);
  return execution;
};

// Method to complete an item
AVChecklistSchema.methods.completeItem = function(executionId, itemId, result) {
  const execution = this.completions.id(executionId);
  if (!execution) throw new Error('Execution not found');
  
  const itemResult = execution.itemResults.find(ir => ir.itemId === itemId);
  if (!itemResult) throw new Error('Item not found in execution');
  
  Object.assign(itemResult, {
    ...result,
    completedAt: new Date(),
    status: result.status || 'completed'
  });
  
  // Check if all items are complete
  const pendingItems = execution.itemResults.filter(ir => ir.status === 'pending');
  if (pendingItems.length === 0) {
    this.completeExecution(executionId);
  }
  
  return itemResult;
};

// Method to complete entire execution
AVChecklistSchema.methods.completeExecution = function(executionId, overallNotes = '', recommendations = []) {
  const execution = this.completions.id(executionId);
  if (!execution) throw new Error('Execution not found');
  
  const completedItems = execution.itemResults.filter(ir => ir.status === 'completed');
  const failedItems = execution.itemResults.filter(ir => ir.status === 'failed');
  
  execution.completedAt = new Date();
  execution.status = failedItems.length > 0 ? 'failed' : 'completed';
  execution.overallNotes = overallNotes;
  execution.recommendations = recommendations;
  
  // Calculate total time
  execution.totalTimeSpent = execution.itemResults.reduce((total, ir) => 
    total + (ir.timeSpent || 0), 0);
  
  // Count issues
  execution.issuesFound = execution.itemResults.reduce((total, ir) => 
    total + (ir.issues ? ir.issues.length : 0), 0);
  
  execution.criticalIssues = execution.itemResults.reduce((total, ir) => 
    total + (ir.issues ? ir.issues.filter(issue => issue.severity === 'critical').length : 0), 0);
  
  // Update analytics
  this.updateAnalytics();
  
  return execution;
};

// Method to update analytics
AVChecklistSchema.methods.updateAnalytics = function() {
  const completedExecutions = this.completions.filter(c => c.status === 'completed');
  
  if (completedExecutions.length === 0) return;
  
  // Calculate average completion time
  const totalTime = completedExecutions.reduce((sum, exec) => sum + (exec.totalTimeSpent || 0), 0);
  this.analytics.averageCompletionTime = Math.round(totalTime / completedExecutions.length);
  
  // Calculate success rate
  this.analytics.successRate = Math.round((completedExecutions.length / this.completions.length) * 100);
  
  // Analyze common issues
  const issueMap = new Map();
  completedExecutions.forEach(exec => {
    exec.itemResults.forEach(ir => {
      if (ir.issues && ir.issues.length > 0) {
        ir.issues.forEach(issue => {
          const key = `${ir.itemId}:${issue.description}`;
          if (issueMap.has(key)) {
            issueMap.get(key).frequency++;
            issueMap.get(key).lastOccurred = exec.completedAt;
          } else {
            issueMap.set(key, {
              itemId: ir.itemId,
              issueDescription: issue.description,
              frequency: 1,
              lastOccurred: exec.completedAt
            });
          }
        });
      }
    });
  });
  
  this.analytics.commonIssues = Array.from(issueMap.values())
    .sort((a, b) => b.frequency - a.frequency)
    .slice(0, 10); // Top 10 issues
  
  this.analytics.lastAnalyzed = new Date();
};

// Static method to get template checklists
AVChecklistSchema.statics.getTemplates = function() {
  return this.find({ isTemplate: true }).sort({ name: 1 });
};

// Static method to create from template
AVChecklistSchema.statics.createFromTemplate = async function(templateId, customizations = {}) {
  const template = await this.findById(templateId);
  if (!template) throw new Error('Template not found');
  
  const newChecklist = new this({
    ...template.toObject(),
    _id: undefined,
    name: customizations.name || `${template.name} - Copy`,
    isTemplate: false,
    parentTemplate: templateId,
    customizations: Object.entries(customizations).map(([field, value]) => ({
      field,
      originalValue: template[field],
      customValue: value,
      reason: 'Custom configuration'
    })),
    completions: [],
    analytics: {
      averageCompletionTime: 0,
      successRate: 0,
      commonIssues: [],
      optimizationSuggestions: []
    },
    createdBy: customizations.createdBy || template.createdBy
  });
  
  // Apply customizations
  Object.assign(newChecklist, customizations);
  
  return newChecklist;
};

// Static method to get due checklists
AVChecklistSchema.statics.getDueChecklists = function(userId = null) {
  const now = new Date();
  const filter = {
    'schedule.autoSchedule': true,
    isTemplate: false,
    $or: [
      { 'schedule.frequency': 'weekly' },
      { 'schedule.frequency': 'monthly' },
      { 'schedule.frequency': 'event_based' }
    ]
  };
  
  if (userId) {
    filter['assignedTeams.members'] = userId;
  }
  
  return this.find(filter)
    .populate('assignedTeams.members', 'name email')
    .sort({ 'schedule.dayOfWeek': 1, 'schedule.timeOfDay': 1 });
};

// Pre-save middleware to generate item IDs
AVChecklistSchema.pre('save', function(next) {
  this.items.forEach((item, index) => {
    if (!item.id) {
      item.id = `item_${Date.now()}_${index}`;
    }
  });
  next();
});

module.exports = mongoose.model('AVChecklist', AVChecklistSchema);