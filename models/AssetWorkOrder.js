const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Work Order Schema
 * Manages maintenance work orders and service requests
 */
const AssetWorkOrderSchema = new Schema({
  workOrderNumber: {
    type: String,
    required: true,
    unique: true
  },
  
  // Asset and request information
  asset: {
    type: Schema.Types.ObjectId,
    ref: 'Asset',
    required: true
  },
  relatedIssue: {
    type: Schema.Types.ObjectId,
    ref: 'AssetIssue'
  },

  // Work order details
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  workType: {
    type: String,
    enum: [
      'preventive_maintenance',
      'corrective_maintenance',
      'emergency_repair',
      'installation',
      'inspection',
      'calibration',
      'upgrade',
      'relocation',
      'decommission',
      'other'
    ],
    required: true
  },

  // Scheduling and timing
  requestedDate: {
    type: Date,
    required: true
  },
  scheduledStartDate: {
    type: Date
  },
  scheduledEndDate: {
    type: Date
  },
  actualStartDate: {
    type: Date
  },
  actualEndDate: {
    type: Date
  },
  estimatedHours: {
    type: Number,
    min: 0
  },
  actualHours: {
    type: Number,
    min: 0
  },

  // Status and priority
  status: {
    type: String,
    enum: [
      'draft',
      'submitted',
      'approved',
      'assigned',
      'in_progress',
      'on_hold',
      'completed',
      'closed',
      'cancelled'
    ],
    default: 'draft'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  urgency: {
    type: String,
    enum: ['routine', 'urgent', 'emergency'],
    default: 'routine'
  },

  // Personnel and assignment
  requestedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedTeam: {
    type: Schema.Types.ObjectId,
    ref: 'Team'
  },
  approvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  approvalDate: {
    type: Date
  },

  // External resources
  contractor: {
    name: String,
    company: String,
    contact: String,
    phone: String,
    email: String,
    cost: Number
  },
  requiresShutdown: {
    type: Boolean,
    default: false
  },
  shutdownDuration: {
    type: Number, // Hours
    min: 0
  },

  // Parts and materials
  requiredParts: [{
    partNumber: String,
    partName: String,
    description: String,
    quantity: {
      type: Number,
      min: 0,
      required: true
    },
    estimatedCost: {
      type: Number,
      min: 0
    },
    vendor: String,
    inStock: {
      type: Boolean,
      default: false
    },
    orderDate: Date,
    receivedDate: Date
  }],

  // Skills and requirements
  requiredSkills: [{
    type: String,
    trim: true
  }],
  requiredCertifications: [{
    type: String,
    trim: true
  }],
  safetyRequirements: [{
    type: String,
    trim: true
  }],

  // Costs and budgeting
  estimatedCost: {
    labor: {
      type: Number,
      min: 0
    },
    parts: {
      type: Number,
      min: 0
    },
    contractor: {
      type: Number,
      min: 0
    },
    other: {
      type: Number,
      min: 0
    },
    total: {
      type: Number,
      min: 0
    }
  },
  actualCost: {
    labor: {
      type: Number,
      min: 0
    },
    parts: {
      type: Number,
      min: 0
    },
    contractor: {
      type: Number,
      min: 0
    },
    other: {
      type: Number,
      min: 0
    },
    total: {
      type: Number,
      min: 0
    }
  },
  budgetCode: {
    type: String,
    trim: true
  },

  // Work progress and completion
  workProgress: [{
    date: {
      type: Date,
      default: Date.now
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['started', 'progress_update', 'on_hold', 'resumed', 'completed', 'issue']
    },
    notes: String,
    hoursWorked: Number,
    percentComplete: {
      type: Number,
      min: 0,
      max: 100
    }
  }],

  // Completion details
  workCompleted: {
    type: String
  },
  issuesEncountered: {
    type: String
  },
  followUpRequired: {
    type: Boolean,
    default: false
  },
  followUpNotes: {
    type: String
  },
  nextScheduledMaintenance: {
    type: Date
  },

  // Quality and approval
  qualityCheck: {
    required: {
      type: Boolean,
      default: false
    },
    completed: {
      type: Boolean,
      default: false
    },
    checkedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    checkedDate: Date,
    passed: Boolean,
    notes: String
  },
  customerApproval: {
    required: {
      type: Boolean,
      default: false
    },
    approved: {
      type: Boolean,
      default: false
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    approvalDate: Date,
    notes: String
  },

  // Attachments and documentation
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Comments and communication
  comments: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    text: String,
    date: {
      type: Date,
      default: Date.now
    },
    internal: {
      type: Boolean,
      default: true
    }
  }],

  // Tracking
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AssetWorkOrderSchema.index({ workOrderNumber: 1 });
AssetWorkOrderSchema.index({ asset: 1 });
AssetWorkOrderSchema.index({ status: 1 });
AssetWorkOrderSchema.index({ priority: 1 });
AssetWorkOrderSchema.index({ assignedTo: 1 });
AssetWorkOrderSchema.index({ requestedBy: 1 });
AssetWorkOrderSchema.index({ scheduledStartDate: 1 });
AssetWorkOrderSchema.index({ workType: 1 });

// Virtual for overall progress percentage
AssetWorkOrderSchema.virtual('overallProgress').get(function() {
  if (!this.workProgress || this.workProgress.length === 0) return 0;
  const latestProgress = this.workProgress[this.workProgress.length - 1];
  return latestProgress.percentComplete || 0;
});

// Virtual for cost variance
AssetWorkOrderSchema.virtual('costVariance').get(function() {
  if (!this.estimatedCost.total || !this.actualCost.total) return null;
  return {
    absolute: this.actualCost.total - this.estimatedCost.total,
    percentage: ((this.actualCost.total - this.estimatedCost.total) / this.estimatedCost.total) * 100
  };
});

// Virtual for duration variance
AssetWorkOrderSchema.virtual('durationVariance').get(function() {
  if (!this.estimatedHours || !this.actualHours) return null;
  return {
    absolute: this.actualHours - this.estimatedHours,
    percentage: ((this.actualHours - this.estimatedHours) / this.estimatedHours) * 100
  };
});

// Pre-save middleware
AssetWorkOrderSchema.pre('save', function(next) {
  // Calculate total estimated cost
  if (this.isModified(['estimatedCost.labor', 'estimatedCost.parts', 'estimatedCost.contractor', 'estimatedCost.other'])) {
    this.estimatedCost.total = (this.estimatedCost.labor || 0) + 
                               (this.estimatedCost.parts || 0) + 
                               (this.estimatedCost.contractor || 0) + 
                               (this.estimatedCost.other || 0);
  }

  // Calculate total actual cost
  if (this.isModified(['actualCost.labor', 'actualCost.parts', 'actualCost.contractor', 'actualCost.other'])) {
    this.actualCost.total = (this.actualCost.labor || 0) + 
                            (this.actualCost.parts || 0) + 
                            (this.actualCost.contractor || 0) + 
                            (this.actualCost.other || 0);
  }

  // Calculate actual hours from work progress
  if (this.workProgress && this.workProgress.length > 0) {
    this.actualHours = this.workProgress.reduce((total, progress) => {
      return total + (progress.hoursWorked || 0);
    }, 0);
  }

  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }

  next();
});

// Static method to generate work order number
AssetWorkOrderSchema.statics.generateWorkOrderNumber = async function() {
  const currentYear = new Date().getFullYear();
  const prefix = `WO${currentYear}`;
  
  const lastWorkOrder = await this.findOne(
    { workOrderNumber: { $regex: `^${prefix}` } },
    {},
    { sort: { workOrderNumber: -1 } }
  );

  let nextNumber = 1;
  if (lastWorkOrder) {
    const lastNumber = parseInt(lastWorkOrder.workOrderNumber.replace(prefix, ''));
    nextNumber = lastNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
};

// Static methods
AssetWorkOrderSchema.statics.findByAsset = function(assetId, options = {}) {
  const query = { asset: assetId };
  if (options.status) query.status = options.status;
  if (options.workType) query.workType = options.workType;
  
  return this.find(query)
    .populate('requestedBy', 'name email')
    .populate('assignedTo', 'name email')
    .sort({ createdAt: -1 });
};

AssetWorkOrderSchema.statics.findOverdue = function() {
  return this.find({
    status: { $in: ['assigned', 'in_progress'] },
    scheduledEndDate: { $lt: new Date() }
  })
    .populate('asset', 'name assetTag')
    .populate('assignedTo', 'name email')
    .sort({ scheduledEndDate: 1 });
};

AssetWorkOrderSchema.statics.findByTechnician = function(technicianId, options = {}) {
  const query = { assignedTo: technicianId };
  if (options.status) query.status = options.status;
  
  return this.find(query)
    .populate('asset', 'name assetTag location')
    .sort({ priority: -1, scheduledStartDate: 1 });
};

// Instance methods
AssetWorkOrderSchema.methods.addProgress = function(userId, status, notes, hoursWorked, percentComplete) {
  this.workProgress.push({
    user: userId,
    status: status,
    notes: notes,
    hoursWorked: hoursWorked,
    percentComplete: percentComplete
  });
  
  // Update status if completing
  if (percentComplete === 100 && status === 'completed') {
    this.status = 'completed';
    this.actualEndDate = new Date();
  }
  
  this.lastModifiedBy = userId;
  return this.save();
};

AssetWorkOrderSchema.methods.approve = function(approvedBy) {
  this.status = 'approved';
  this.approvedBy = approvedBy;
  this.approvalDate = new Date();
  this.lastModifiedBy = approvedBy;
  return this.save();
};

AssetWorkOrderSchema.methods.assign = function(assignedTo, assignedBy) {
  this.assignedTo = assignedTo;
  this.status = 'assigned';
  this.lastModifiedBy = assignedBy;
  return this.save();
};

module.exports = mongoose.model('AssetWorkOrder', AssetWorkOrderSchema);