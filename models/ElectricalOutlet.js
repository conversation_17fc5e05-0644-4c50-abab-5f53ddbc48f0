const mongoose = require('mongoose');
const { Schema } = mongoose;

/**
 * Electrical Outlet Schema
 * Represents a power outlet point and its associations
 */
const ElectricalOutletSchema = new Schema({
  buildingId: { type: Schema.Types.ObjectId, ref: 'Building' },
  floorId: { type: Schema.Types.ObjectId, ref: 'Floor' },
  room: { type: String, trim: true },
  label: { type: String, required: true, trim: true },
  panelId: { type: Schema.Types.ObjectId, ref: 'ElectricalPanel' },
  circuitId: { type: Schema.Types.ObjectId, ref: 'ElectricalCircuit' },
  breakerNumber: { type: Number },
  iconId: { type: Schema.Types.ObjectId, ref: 'FloorplanIcon' },
  qrCode: { type: String, trim: true },
  photos: [{ url: String, caption: String }],
  notes: { type: String, trim: true },
  createdBy: { type: Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true });

ElectricalOutletSchema.index({ label: 1 });
ElectricalOutletSchema.index({ panelId: 1, circuitId: 1 });
ElectricalOutletSchema.index({ buildingId: 1, floorId: 1 });

module.exports = mongoose.model('ElectricalOutlet', ElectricalOutletSchema);
