const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Issue Schema
 * Represents an issue reported for an asset in the system
 */
const AssetIssueSchema = new Schema({
  assetId: {
    type: String,
    required: true
  },
  assetType: {
    type: String,
    required: true
  },
  assetName: {
    type: String,
    required: true
  },
  reportedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  issueType: {
    type: String,
    enum: ['hardware', 'software', 'connectivity', 'other'],
    required: true
  },
  issueDescription: {
    type: String,
    required: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['open', 'in_progress', 'resolved', 'closed', 'reopened'],
    default: 'open'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolution: {
    type: String
  },
  resolutionDate: {
    type: Date
  },
  attachments: [{
    name: {
      type: String
    },
    url: {
      type: String
    },
    type: {
      type: String
    }
  }],
  comments: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    text: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

module.exports = mongoose.model('AssetIssue', AssetIssueSchema);