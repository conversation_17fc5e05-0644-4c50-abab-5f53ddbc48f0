const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Google Forms Webhook Schema
 * Stores configuration for creating tasks from Google Forms responses
 */
const GoogleFormsWebhookSchema = new Schema({
  // The form this webhook is for
  formId: {
    type: String,
    required: true,
    trim: true
  },
  formName: {
    type: String,
    required: true,
    trim: true
  },
  // User who created this webhook
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Whether this webhook is active
  active: {
    type: Boolean,
    default: true
  },
  // Field mappings from form to task
  fieldMappings: {
    // Task title field
    titleField: {
      type: String,
      required: true,
      trim: true
    },
    // Task description field
    descriptionField: {
      type: String,
      trim: true
    },
    // Task priority field
    priorityField: {
      type: String,
      trim: true
    },
    // Task due date field
    dueDateField: {
      type: String,
      trim: true
    },
    // Task tags field
    tagsField: {
      type: String,
      trim: true
    },
    // Custom field mappings
    customFields: [{
      formField: {
        type: String,
        required: true,
        trim: true
      },
      taskField: {
        type: String,
        required: true,
        trim: true
      }
    }]
  },
  // Assignment rules
  assignmentRules: [{
    // Field to check for the rule
    field: {
      type: String,
      required: true,
      trim: true
    },
    // Value to match
    value: {
      type: String,
      required: true,
      trim: true
    },
    // User to assign to if match
    assignTo: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    // Priority of this rule (lower number = higher priority)
    priority: {
      type: Number,
      default: 0
    }
  }],
  // Default assignment if no rules match
  defaultAssignee: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  // Task type to create
  taskType: {
    type: String,
    enum: ['general', 'maintenance'],
    default: 'general'
  },
  // Processed response IDs to avoid duplicates
  processedResponses: [{
    responseId: {
      type: String,
      required: true,
      trim: true
    },
    processedAt: {
      type: Date,
      default: Date.now
    },
    taskId: {
      type: Schema.Types.ObjectId,
      ref: 'Task'
    }
  }],
  // Last time responses were checked
  lastChecked: {
    type: Date
  }
}, {
  timestamps: true
});

// Create indexes for faster queries
GoogleFormsWebhookSchema.index({ formId: 1 });
GoogleFormsWebhookSchema.index({ createdBy: 1 });
GoogleFormsWebhookSchema.index({ 'processedResponses.responseId': 1 });

module.exports = mongoose.model('GoogleFormsWebhook', GoogleFormsWebhookSchema);