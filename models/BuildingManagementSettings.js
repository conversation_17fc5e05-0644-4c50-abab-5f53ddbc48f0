const mongoose = require('mongoose');

/**
 * Building Management Settings Schema
 * Stores settings for the building management system
 */
const BuildingManagementSettingsSchema = new mongoose.Schema({
  dashboardRefreshInterval: {
    type: Number,
    default: 60,
    min: 10,
    max: 3600
  },
  defaultView: {
    type: String,
    enum: ['dashboard', 'doors', 'climate', 'cameras', 'network'],
    default: 'dashboard'
  },
  notifications: {
    email: {
      type: Boolean,
      default: true
    },
    push: {
      type: Boolean,
      default: true
    },
    slack: {
      type: Boolean,
      default: false
    },
    recipients: [{
      type: String,
      trim: true
    }]
  },
  automation: {
    enabled: {
      type: <PERSON>olean,
      default: true
    },
    scheduleCheckInterval: {
      type: Number,
      default: 300, // 5 minutes
      min: 60,
      max: 3600
    }
  },
  integrations: {
    lenelS2NetBox: {
      enabled: {
        type: Boolean,
        default: false
      },
      status: {
        type: String,
        enum: ['unknown', 'configured', 'active', 'error', 'disabled'],
        default: 'unknown'
      },
      lastChecked: {
        type: Date
      },
      errorMessage: {
        type: String
      },
      errorType: {
        type: String,
        enum: ['none', 'configuration', 'connection', 'authentication', 'unknown'],
        default: 'none'
      },
      configRequirements: {
        type: String,
        default: 'Requires host, username, password, and port'
      },
      initializationAttempts: {
        type: Number,
        default: 0
      }
    },
    unifiAccess: {
      enabled: {
        type: Boolean,
        default: false
      },
      status: {
        type: String,
        enum: ['unknown', 'configured', 'active', 'error', 'disabled'],
        default: 'unknown'
      },
      lastChecked: {
        type: Date
      },
      errorMessage: {
        type: String
      },
      errorType: {
        type: String,
        enum: ['none', 'configuration', 'connection', 'authentication', 'unknown'],
        default: 'none'
      },
      configRequirements: {
        type: String,
        default: 'Requires host, username, password, and port'
      },
      initializationAttempts: {
        type: Number,
        default: 0
      }
    },
    skyportcloud: {
      enabled: {
        type: Boolean,
        default: false
      },
      status: {
        type: String,
        enum: ['unknown', 'configured', 'active', 'error', 'disabled'],
        default: 'unknown'
      },
      lastChecked: {
        type: Date
      },
      errorMessage: {
        type: String
      },
      errorType: {
        type: String,
        enum: ['none', 'configuration', 'connection', 'authentication', 'unknown'],
        default: 'none'
      },
      configRequirements: {
        type: String,
        default: 'Requires apiKey, username, password, and baseUrl'
      },
      initializationAttempts: {
        type: Number,
        default: 0
      }
    },
    unifiProtect: {
      enabled: {
        type: Boolean,
        default: false
      },
      status: {
        type: String,
        enum: ['unknown', 'configured', 'active', 'error', 'disabled'],
        default: 'unknown'
      },
      lastChecked: {
        type: Date
      },
      errorMessage: {
        type: String
      },
      errorType: {
        type: String,
        enum: ['none', 'configuration', 'connection', 'authentication', 'unknown'],
        default: 'none'
      },
      configRequirements: {
        type: String,
        default: 'Requires host, username, password, and port'
      },
      initializationAttempts: {
        type: Number,
        default: 0
      }
    },
    unifiNetwork: {
      enabled: {
        type: Boolean,
        default: false
      },
      status: {
        type: String,
        enum: ['unknown', 'configured', 'active', 'error', 'disabled'],
        default: 'unknown'
      },
      lastChecked: {
        type: Date
      },
      errorMessage: {
        type: String
      },
      errorType: {
        type: String,
        enum: ['none', 'configuration', 'connection', 'authentication', 'unknown'],
        default: 'none'
      },
      configRequirements: {
        type: String,
        default: 'Requires host, apiKey, port, and site'
      },
      initializationAttempts: {
        type: Number,
        default: 0
      }
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
BuildingManagementSettingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

/**
 * Get the current settings or create default settings if none exist
 * @returns {Promise<Object>} The settings object
 */
BuildingManagementSettingsSchema.statics.getSettings = async function() {
  const settings = await this.findOne();
  if (settings) {
    return settings;
  }
  
  // Create default settings if none exist
  return await this.create({});
};

/**
 * Update settings
 * @param {Object} newSettings - The new settings
 * @returns {Promise<Object>} The updated settings
 */
BuildingManagementSettingsSchema.statics.updateSettings = async function(newSettings) {
  const settings = await this.getSettings();
  
  // Update only the fields that are provided
  if (newSettings.dashboardRefreshInterval !== undefined) {
    settings.dashboardRefreshInterval = newSettings.dashboardRefreshInterval;
  }
  
  if (newSettings.defaultView !== undefined) {
    settings.defaultView = newSettings.defaultView;
  }
  
  if (newSettings.notifications !== undefined) {
    if (newSettings.notifications.email !== undefined) {
      settings.notifications.email = newSettings.notifications.email;
    }
    
    if (newSettings.notifications.push !== undefined) {
      settings.notifications.push = newSettings.notifications.push;
    }
    
    if (newSettings.notifications.slack !== undefined) {
      settings.notifications.slack = newSettings.notifications.slack;
    }
    
    if (newSettings.notifications.recipients !== undefined) {
      settings.notifications.recipients = newSettings.notifications.recipients;
    }
  }
  
  if (newSettings.automation !== undefined) {
    if (newSettings.automation.enabled !== undefined) {
      settings.automation.enabled = newSettings.automation.enabled;
    }
    
    if (newSettings.automation.scheduleCheckInterval !== undefined) {
      settings.automation.scheduleCheckInterval = newSettings.automation.scheduleCheckInterval;
    }
  }
  
  if (newSettings.integrations !== undefined) {
    if (newSettings.integrations.lenelS2NetBox !== undefined) {
      if (newSettings.integrations.lenelS2NetBox.enabled !== undefined) {
        settings.integrations.lenelS2NetBox.enabled = newSettings.integrations.lenelS2NetBox.enabled;
      }
    }
    
    if (newSettings.integrations.unifiAccess !== undefined) {
      if (newSettings.integrations.unifiAccess.enabled !== undefined) {
        settings.integrations.unifiAccess.enabled = newSettings.integrations.unifiAccess.enabled;
      }
    }
    
    if (newSettings.integrations.skyportcloud !== undefined) {
      if (newSettings.integrations.skyportcloud.enabled !== undefined) {
        settings.integrations.skyportcloud.enabled = newSettings.integrations.skyportcloud.enabled;
      }
    }
    
    if (newSettings.integrations.unifiProtect !== undefined) {
      if (newSettings.integrations.unifiProtect.enabled !== undefined) {
        settings.integrations.unifiProtect.enabled = newSettings.integrations.unifiProtect.enabled;
      }
    }
    
    if (newSettings.integrations.unifiNetwork !== undefined) {
      if (newSettings.integrations.unifiNetwork.enabled !== undefined) {
        settings.integrations.unifiNetwork.enabled = newSettings.integrations.unifiNetwork.enabled;
      }
    }
  }
  
  await settings.save();
  return settings;
};

/**
 * Update integration status
 * @param {string} integration - The integration name
 * @param {Object} statusData - The status data to update
 * @returns {Promise<Object>} The updated settings
 */
BuildingManagementSettingsSchema.statics.updateIntegrationStatus = async function(integration, statusData) {
  const settings = await this.getSettings();
  
  // Check if the integration exists
  if (!settings.integrations[integration]) {
    throw new Error(`Integration ${integration} not found`);
  }
  
  // Update status fields
  if (statusData.status !== undefined) {
    settings.integrations[integration].status = statusData.status;
  }
  
  if (statusData.errorMessage !== undefined) {
    settings.integrations[integration].errorMessage = statusData.errorMessage;
  }
  
  if (statusData.errorType !== undefined) {
    settings.integrations[integration].errorType = statusData.errorType;
  }
  
  // Always update lastChecked when status is updated
  settings.integrations[integration].lastChecked = new Date();
  
  // Increment initialization attempts if specified
  if (statusData.incrementAttempts) {
    settings.integrations[integration].initializationAttempts += 1;
  }
  
  // Reset initialization attempts if specified
  if (statusData.resetAttempts) {
    settings.integrations[integration].initializationAttempts = 0;
  }
  
  await settings.save();
  return settings;
};

/**
 * Check if an integration is properly configured
 * @param {string} integration - The integration name
 * @param {Object} config - The integration configuration
 * @returns {Object} Result with isConfigured flag and any missing requirements
 */
BuildingManagementSettingsSchema.statics.checkIntegrationConfig = async function(integration, config) {
  const settings = await this.getSettings();
  
  // Check if the integration exists
  if (!settings.integrations[integration]) {
    throw new Error(`Integration ${integration} not found`);
  }
  
  // Define required fields for each integration
  const requiredFields = {
    lenelS2NetBox: ['host', 'username', 'password', 'port'],
    unifiAccess: ['host', 'username', 'password', 'port'],
    unifiProtect: ['host', 'username', 'password', 'port'],
    unifiNetwork: ['host', 'apiKey', 'port', 'site']
  };

  let missingFields = [];
  let isConfigured = false;

  if (integration === 'skyportcloud') {
    // Special logic: baseUrl is required AND either apiKey OR (username AND password)
    const baseUrlOk = !!(config && config.baseUrl);
    const hasApiKey = !!(config && config.apiKey);
    const hasUserPass = !!(config && config.username && config.password);

    if (!baseUrlOk) missingFields.push('baseUrl');

    if (!(hasApiKey || hasUserPass)) {
      // Helpfully indicate what is missing
      if (!hasApiKey) missingFields.push('apiKey');
      if (!hasUserPass) {
        if (!config || !config.username) missingFields.push('username');
        if (!config || !config.password) missingFields.push('password');
      }
    }

    isConfigured = baseUrlOk && (hasApiKey || hasUserPass);
  } else {
    // Generic check for other integrations
    for (const field of requiredFields[integration] || []) {
      if (!config || !config[field]) {
        missingFields.push(field);
      }
    }
    isConfigured = missingFields.length === 0;
  }
  
  // Update status if needed (auto-enabled behavior)
  if (isConfigured && settings.integrations[integration].status === 'unknown') {
    settings.integrations[integration].status = 'configured';
    await settings.save();
  } else if (!isConfigured && settings.integrations[integration].status !== 'error') {
    settings.integrations[integration].status = 'error';
    settings.integrations[integration].errorType = 'configuration';
    settings.integrations[integration].errorMessage = `Missing required fields: ${missingFields.join(', ')}`;
    settings.integrations[integration].lastChecked = new Date();
    await settings.save();
  }
  
  return {
    isConfigured,
    missingFields,
    configRequirements: settings.integrations[integration].configRequirements
  };
};

/**
 * Reset all integration statuses to unknown
 * @returns {Promise<Object>} The updated settings
 */
BuildingManagementSettingsSchema.statics.resetIntegrationStatuses = async function() {
  const settings = await this.getSettings();
  
  // Reset status for all integrations
  for (const integration in settings.integrations) {
    settings.integrations[integration].status = 'unknown';
    settings.integrations[integration].errorMessage = null;
    settings.integrations[integration].errorType = 'none';
    settings.integrations[integration].initializationAttempts = 0;
  }
  
  await settings.save();
  return settings;
};

module.exports = mongoose.model('BuildingManagementSettings', BuildingManagementSettingsSchema);