const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Template Schema
 * Pre-configured templates for creating assets with consistent defaults
 */
const AssetTemplateSchema = new Schema({
  // Template identification
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  templateCode: {
    type: String,
    unique: true,
    trim: true,
    sparse: true
  },

  // Category and type
  category: {
    type: Schema.Types.ObjectId,
    ref: 'AssetCategory',
    required: true
  },
  assetType: {
    type: String,
    enum: [
      'hardware',
      'software',
      'equipment',
      'furniture',
      'vehicle',
      'property',
      'consumable',
      'service',
      'other'
    ],
    required: true
  },

  // Default values for new assets
  defaults: {
    // Basic information
    manufacturer: {
      type: String,
      trim: true
    },
    model: {
      type: String,
      trim: true
    },
    
    // Financial defaults
    depreciationRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 20
    },
    warrantyPeriod: {
      type: Number, // Months
      min: 0
    },
    expectedLifespan: {
      type: Number, // Years
      min: 0
    },

    // Status and condition
    initialStatus: {
      type: String,
      enum: [
        'active',
        'inactive',
        'in_maintenance',
        'retired',
        'disposed',
        'lost',
        'stolen',
        'on_loan',
        'reserved'
      ],
      default: 'active'
    },
    initialCondition: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor', 'needs_repair'],
      default: 'excellent'
    },

    // Location and assignment
    defaultLocation: {
      type: Schema.Types.ObjectId,
      ref: 'AssetLocation'
    },
    defaultDepartment: {
      type: String,
      trim: true
    },

    // Maintenance settings
    maintenanceSchedule: {
      type: String,
      enum: ['none', 'monthly', 'quarterly', 'semi_annual', 'annual', 'custom'],
      default: 'annual'
    },
    maintenanceIntervalDays: {
      type: Number,
      min: 0
    },

    // Risk and compliance
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low'
    },
    requiresCompliance: {
      type: Boolean,
      default: false
    },
    complianceStandards: [{
      type: String,
      trim: true
    }]
  },

  // Custom fields template
  customFields: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    label: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['text', 'number', 'date', 'boolean', 'url', 'select', 'multiselect'],
      required: true
    },
    required: {
      type: Boolean,
      default: false
    },
    defaultValue: Schema.Types.Mixed,
    options: [String], // For select and multiselect types
    validation: {
      min: Number,
      max: Number,
      pattern: String,
      minLength: Number,
      maxLength: Number
    },
    placeholder: String,
    helpText: String,
    order: {
      type: Number,
      default: 0
    }
  }],

  // Specification templates
  specificationFields: [{
    category: {
      type: String,
      required: true,
      enum: ['hardware', 'software', 'network', 'physical', 'performance', 'other']
    },
    name: {
      type: String,
      required: true
    },
    label: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['text', 'number', 'select', 'boolean'],
      required: true
    },
    unit: String, // e.g., "GB", "MHz", "inches"
    options: [String],
    required: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    }
  }],

  // Asset tag generation rules
  assetTagFormat: {
    prefix: {
      type: String,
      trim: true
    },
    useCategory: {
      type: Boolean,
      default: false
    },
    useYear: {
      type: Boolean,
      default: true
    },
    numberLength: {
      type: Number,
      default: 4,
      min: 1,
      max: 10
    },
    separator: {
      type: String,
      default: '-',
      maxLength: 2
    },
    // Examples: LAPTOP-2024-0001, IT-PRINTER-001, DESK-2024-A001
    example: {
      type: String,
      trim: true
    }
  },

  // Workflow and approval settings
  workflowSettings: {
    requiresApproval: {
      type: Boolean,
      default: false
    },
    approvalRequired: [{
      type: String,
      enum: ['creation', 'assignment', 'disposal', 'high_value', 'location_change']
    }],
    approverRoles: [{
      type: String,
      trim: true
    }],
    notificationSettings: {
      onCreate: {
        type: Boolean,
        default: true
      },
      onAssign: {
        type: Boolean,
        default: true
      },
      onMaintenance: {
        type: Boolean,
        default: true
      }
    }
  },

  // Integration settings
  integrationSettings: {
    syncWithGLPI: {
      type: Boolean,
      default: false
    },
    glpiItemType: {
      type: String,
      default: 'Computer'
    },
    autoCreateBarcode: {
      type: Boolean,
      default: true
    },
    requirePhotos: {
      type: Boolean,
      default: false
    },
    maxPhotos: {
      type: Number,
      default: 5,
      min: 0
    }
  },

  // Usage and statistics
  usage: {
    timesUsed: {
      type: Number,
      default: 0
    },
    assetsCreated: {
      type: Number,
      default: 0
    },
    lastUsed: {
      type: Date
    }
  },

  // Template management
  isActive: {
    type: Boolean,
    default: true
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  version: {
    type: Number,
    default: 1
  },
  parentTemplate: {
    type: Schema.Types.ObjectId,
    ref: 'AssetTemplate'
  },

  // Access control
  visibility: {
    type: String,
    enum: ['public', 'restricted', 'private'],
    default: 'public'
  },
  allowedRoles: [{
    type: String,
    trim: true
  }],
  allowedUsers: [{
    type: Schema.Types.ObjectId,
    ref: 'User'
  }],

  // Tracking
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AssetTemplateSchema.index({ name: 1 });
AssetTemplateSchema.index({ templateCode: 1 });
AssetTemplateSchema.index({ category: 1 });
AssetTemplateSchema.index({ assetType: 1 });
AssetTemplateSchema.index({ isActive: 1 });
AssetTemplateSchema.index({ visibility: 1 });
AssetTemplateSchema.index({ 'usage.timesUsed': -1 });

// Virtual for popularity score
AssetTemplateSchema.virtual('popularityScore').get(function() {
  const recencyWeight = Math.max(0, 1 - (Date.now() - this.usage.lastUsed) / (1000 * 60 * 60 * 24 * 365));
  const usageWeight = Math.log(this.usage.timesUsed + 1) / 10;
  return recencyWeight * 0.3 + usageWeight * 0.7;
});

// Virtual for example asset tag
AssetTemplateSchema.virtual('exampleAssetTag').get(function() {
  if (this.assetTagFormat.example) return this.assetTagFormat.example;
  
  let tag = '';
  if (this.assetTagFormat.prefix) {
    tag += this.assetTagFormat.prefix;
  }
  
  if (this.assetTagFormat.useCategory && this.category && this.category.name) {
    if (tag) tag += this.assetTagFormat.separator;
    tag += this.category.name.toUpperCase().substring(0, 3);
  }
  
  if (this.assetTagFormat.useYear) {
    if (tag) tag += this.assetTagFormat.separator;
    tag += new Date().getFullYear();
  }
  
  if (tag) tag += this.assetTagFormat.separator;
  tag += '0'.repeat(this.assetTagFormat.numberLength - 1) + '1';
  
  return tag;
});

// Pre-save middleware
AssetTemplateSchema.pre('save', function(next) {
  // Update example asset tag
  if (this.isModified(['assetTagFormat', 'category'])) {
    // The example will be generated by the virtual
  }

  // Sort custom fields by order
  if (this.customFields && this.customFields.length > 0) {
    this.customFields.sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  // Sort specification fields by order
  if (this.specificationFields && this.specificationFields.length > 0) {
    this.specificationFields.sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }

  next();
});

// Static methods
AssetTemplateSchema.statics.findByCategory = function(categoryId) {
  return this.find({ 
    category: categoryId, 
    isActive: true 
  }).sort({ 'usage.timesUsed': -1, name: 1 });
};

AssetTemplateSchema.statics.findByType = function(assetType) {
  return this.find({ 
    assetType: assetType, 
    isActive: true 
  }).sort({ 'usage.timesUsed': -1, name: 1 });
};

AssetTemplateSchema.statics.findPopular = function(limit = 10) {
  return this.find({ isActive: true })
    .sort({ 'usage.timesUsed': -1, 'usage.lastUsed': -1 })
    .limit(limit);
};

AssetTemplateSchema.statics.findDefault = function() {
  return this.findOne({ isDefault: true, isActive: true });
};

AssetTemplateSchema.statics.generateAssetTag = async function(templateId) {
  const template = await this.findById(templateId);
  if (!template) throw new Error('Template not found');

  let tag = '';
  const format = template.assetTagFormat;
  
  if (format.prefix) {
    tag += format.prefix.toUpperCase();
  }
  
  if (format.useCategory && template.category) {
    await template.populate('category');
    if (tag) tag += format.separator;
    tag += template.category.name.toUpperCase().substring(0, 3);
  }
  
  if (format.useYear) {
    if (tag) tag += format.separator;
    tag += new Date().getFullYear();
  }

  // Find the next sequential number
  const Asset = mongoose.model('Asset');
  const regex = new RegExp(`^${tag.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}${format.separator}(\\d+)$`);
  
  const lastAsset = await Asset.findOne(
    { assetTag: { $regex: regex } },
    {},
    { sort: { assetTag: -1 } }
  );

  let nextNumber = 1;
  if (lastAsset) {
    const match = lastAsset.assetTag.match(regex);
    if (match && match[1]) {
      nextNumber = parseInt(match[1]) + 1;
    }
  }

  if (tag) tag += format.separator;
  tag += nextNumber.toString().padStart(format.numberLength, '0');

  return tag;
};

// Instance methods
AssetTemplateSchema.methods.incrementUsage = function() {
  this.usage.timesUsed += 1;
  this.usage.assetsCreated += 1;
  this.usage.lastUsed = new Date();
  return this.save();
};

AssetTemplateSchema.methods.createAsset = async function(assetData, user) {
  const Asset = mongoose.model('Asset');
  
  // Merge template defaults with provided data
  const mergedData = {
    ...this.defaults,
    ...assetData,
    category: this.category,
    createdBy: user._id
  };

  // Generate asset tag if not provided
  if (!mergedData.assetTag) {
    mergedData.assetTag = await this.constructor.generateAssetTag(this._id);
  }

  // Apply custom fields defaults
  const customFields = [];
  for (const field of this.customFields) {
    if (field.defaultValue !== undefined) {
      customFields.push({
        name: field.name,
        value: field.defaultValue,
        type: field.type
      });
    }
  }
  
  if (customFields.length > 0) {
    mergedData.specifications = mergedData.specifications || {};
    mergedData.specifications.customFields = customFields;
  }

  // Create the asset
  const asset = new Asset(mergedData);
  await asset.save();

  // Update template usage
  await this.incrementUsage();

  return asset;
};

AssetTemplateSchema.methods.clone = function(newName, user) {
  const clonedTemplate = new this.constructor({
    name: newName,
    description: `Cloned from ${this.name}`,
    category: this.category,
    assetType: this.assetType,
    defaults: this.defaults,
    customFields: this.customFields,
    specificationFields: this.specificationFields,
    assetTagFormat: this.assetTagFormat,
    workflowSettings: this.workflowSettings,
    integrationSettings: this.integrationSettings,
    visibility: this.visibility,
    allowedRoles: this.allowedRoles,
    allowedUsers: this.allowedUsers,
    parentTemplate: this._id,
    createdBy: user._id
  });

  return clonedTemplate;
};

module.exports = mongoose.model('AssetTemplate', AssetTemplateSchema);