const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Building Tracking Item Schema
 * Represents a contract, service, inspection, or other item with expiration/renewal dates
 * associated with a building in the building management system
 */
const BuildingTrackingItemSchema = new Schema({
  buildingId: {
    type: Schema.Types.ObjectId,
    ref: 'Building',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['contract', 'service', 'inspection', 'license', 'certification', 'warranty', 'other'],
    required: true
  },
  description: {
    type: String,
    trim: true
  },
  provider: {
    name: { type: String, trim: true },
    contactPerson: { type: String, trim: true },
    phone: { type: String, trim: true },
    email: { type: String, trim: true },
    website: { type: String, trim: true }
  },
  // Optional link to a Phone Book contact to prevent duplication and enable reuse
  providerContactId: {
    type: Schema.Types.ObjectId,
    ref: 'Contact'
  },
  startDate: {
    type: Date,
    required: true
  },
  expirationDate: {
    type: Date,
    required: true
  },
  renewalDate: {
    type: Date
  },
  notificationDays: {
    type: Number,
    default: 30,
    description: 'Days before expiration to send notification'
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'pending_renewal', 'renewed', 'terminated', 'draft'],
    default: 'active'
  },
  cost: {
    amount: { type: Number },
    currency: { type: String, default: 'USD' },
    billingCycle: { type: String, enum: ['one-time', 'monthly', 'quarterly', 'annually', 'other'], default: 'annually' }
  },
  documents: [{
    name: { type: String, trim: true },
    fileUrl: { type: String, trim: true },
    uploadDate: { type: Date, default: Date.now }
  }],
  notes: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create indexes for faster queries
BuildingTrackingItemSchema.index({ buildingId: 1 });
BuildingTrackingItemSchema.index({ type: 1 });
BuildingTrackingItemSchema.index({ status: 1 });
BuildingTrackingItemSchema.index({ expirationDate: 1 });
BuildingTrackingItemSchema.index({ renewalDate: 1 });
BuildingTrackingItemSchema.index({ providerContactId: 1 });

module.exports = mongoose.model('BuildingTrackingItem', BuildingTrackingItemSchema);