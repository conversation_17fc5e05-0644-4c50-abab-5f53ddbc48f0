const mongoose = require('mongoose');
const Task = require('./Task');

/**
 * Maintenance Task Schema
 * Extends the base Task schema with maintenance-specific fields
 */
const MaintenanceTaskSchema = new mongoose.Schema({
  // Location information
  location: {
    building: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Building'
    },
    floor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Floor'
    },
    room: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Room'
    },
    description: {
      type: String,
      trim: true
    }
  },
  // Maintenance category
  category: {
    type: String,
    enum: ['electrical', 'plumbing', 'hvac', 'structural', 'cleaning', 'equipment', 'other'],
    required: true
  },
  // Equipment information (if applicable)
  equipment: {
    name: {
      type: String,
      trim: true
    },
    model: {
      type: String,
      trim: true
    },
    serialNumber: {
      type: String,
      trim: true
    },
    assetTag: {
      type: String,
      trim: true
    }
  },
  // Maintenance type
  maintenanceType: {
    type: String,
    enum: ['preventive', 'corrective', 'emergency', 'inspection'],
    default: 'corrective'
  },
  // Estimated time to complete (in minutes)
  estimatedDuration: {
    type: Number,
    min: 0
  },
  // Actual time spent (in minutes)
  actualDuration: {
    type: Number,
    min: 0
  },
  // Materials used
  materials: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unit: {
      type: String,
      trim: true
    },
    cost: {
      type: Number,
      min: 0
    }
  }],
  // Safety requirements
  safetyRequirements: {
    type: String,
    trim: true
  },
  // Special instructions
  specialInstructions: {
    type: String,
    trim: true
  },
  // Approval information
  approval: {
    isRequired: {
      type: Boolean,
      default: false
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: {
      type: Date
    },
    notes: {
      type: String,
      trim: true
    }
  },
  // Completion checklist
  checklist: [{
    item: {
      type: String,
      required: true,
      trim: true
    },
    completed: {
      type: Boolean,
      default: false
    },
    completedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    completedAt: {
      type: Date
    },
    notes: {
      type: String,
      trim: true
    }
  }]
});

// Create the MaintenanceTask model as a discriminator of the Task model
const MaintenanceTask = Task.discriminator('maintenance', MaintenanceTaskSchema);

module.exports = MaintenanceTask;