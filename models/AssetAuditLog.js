const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Asset Audit Log Schema
 * Tracks all changes and activities related to assets for compliance and history
 */
const AssetAuditLogSchema = new Schema({
  // Asset reference
  asset: {
    type: Schema.Types.ObjectId,
    ref: 'Asset',
    required: true
  },
  assetTag: {
    type: String,
    required: true // Store for historical reference even if asset is deleted
  },

  // Action details
  action: {
    type: String,
    enum: [
      'created',
      'updated',
      'deleted',
      'restored',
      'assigned',
      'unassigned',
      'relocated',
      'status_changed',
      'maintenance_performed',
      'reservation_created',
      'checked_out',
      'checked_in',
      'damaged',
      'disposed',
      'barcode_generated',
      'bulk_updated',
      'imported',
      'exported',
      'integration_sync',
      'other'
    ],
    required: true
  },
  actionDescription: {
    type: String,
    required: true
  },

  // Change tracking
  changedFields: [{
    field: {
      type: String,
      required: true
    },
    oldValue: Schema.Types.Mixed,
    newValue: Schema.Types.Mixed,
    dataType: {
      type: String,
      enum: ['string', 'number', 'boolean', 'date', 'objectId', 'array', 'object']
    }
  }],

  // Context and metadata
  category: {
    type: String,
    enum: [
      'asset_management',
      'maintenance',
      'reservation',
      'location',
      'assignment',
      'financial',
      'compliance',
      'integration',
      'system'
    ],
    required: true
  },
  severity: {
    type: String,
    enum: ['info', 'warning', 'error', 'critical'],
    default: 'info'
  },

  // User and system info
  performedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  performedByName: {
    type: String,
    required: true // Store for historical reference
  },
  performedByEmail: {
    type: String
  },
  systemAction: {
    type: Boolean,
    default: false // True for automated system actions
  },
  
  // Session and request info
  sessionId: {
    type: String
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  requestId: {
    type: String
  },

  // Related records
  relatedRecords: [{
    recordType: {
      type: String,
      enum: ['AssetWorkOrder', 'AssetReservation', 'AssetMaintenanceRecord', 'AssetIssue', 'User', 'AssetLocation']
    },
    recordId: Schema.Types.ObjectId,
    recordDescription: String
  }],

  // Integration tracking
  integrationSource: {
    type: String,
    enum: ['glpi', 'assetpanda', 'manual', 'bulk_import', 'api', 'webhook']
  },
  integrationId: {
    type: String // External system ID
  },

  // Compliance and retention
  retentionPeriod: {
    type: Number, // Days to retain this log entry
    default: 2555 // 7 years default
  },
  complianceFlags: [{
    type: String,
    enum: ['gdpr', 'sox', 'hipaa', 'pci_dss', 'iso27001']
  }],

  // Additional metadata
  metadata: {
    location: {
      building: String,
      room: String,
      coordinates: {
        lat: Number,
        lng: Number
      }
    },
    device: {
      type: String, // mobile, desktop, tablet
      os: String,
      browser: String
    },
    workflow: {
      step: String,
      process: String
    }
  },

  // Status and lifecycle
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  },
  archivedDate: {
    type: Date
  },
  
  // Timestamps are handled by timestamps: true
}, {
  timestamps: true,
  // Don't allow updates to audit logs - they should be immutable
  strict: false
});

// Indexes for performance
AssetAuditLogSchema.index({ asset: 1, createdAt: -1 });
AssetAuditLogSchema.index({ assetTag: 1, createdAt: -1 });
AssetAuditLogSchema.index({ action: 1, createdAt: -1 });
AssetAuditLogSchema.index({ performedBy: 1, createdAt: -1 });
AssetAuditLogSchema.index({ category: 1, createdAt: -1 });
AssetAuditLogSchema.index({ createdAt: -1 }); // For general chronological queries
AssetAuditLogSchema.index({ sessionId: 1 });
AssetAuditLogSchema.index({ integrationSource: 1 });
AssetAuditLogSchema.index({ status: 1 });

// TTL index for automatic cleanup based on retention period
AssetAuditLogSchema.index({ 
  createdAt: 1 
}, { 
  expireAfterSeconds: 220752000 // 7 years in seconds, can be overridden per document
});

// Virtual for age of log entry
AssetAuditLogSchema.virtual('ageInDays').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
});

// Virtual for time until expiration
AssetAuditLogSchema.virtual('daysUntilExpiration').get(function() {
  if (!this.retentionPeriod) return null;
  const expirationDate = new Date(this.createdAt.getTime() + (this.retentionPeriod * 24 * 60 * 60 * 1000));
  return Math.max(0, Math.floor((expirationDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24)));
});

// Prevent updates to audit logs (immutable)
AssetAuditLogSchema.pre(['updateOne', 'findOneAndUpdate', 'replaceOne'], function() {
  throw new Error('Audit logs are immutable and cannot be updated');
});

// Static methods for creating audit logs
AssetAuditLogSchema.statics.logAction = async function(params) {
  const {
    asset,
    assetTag,
    action,
    actionDescription,
    performedBy,
    performedByName,
    performedByEmail,
    changedFields = [],
    category,
    severity = 'info',
    systemAction = false,
    sessionInfo = {},
    relatedRecords = [],
    integrationSource = 'manual',
    integrationId = null,
    metadata = {}
  } = params;

  const auditEntry = new this({
    asset,
    assetTag,
    action,
    actionDescription,
    changedFields,
    category,
    severity,
    performedBy,
    performedByName,
    performedByEmail,
    systemAction,
    sessionId: sessionInfo.sessionId,
    ipAddress: sessionInfo.ipAddress,
    userAgent: sessionInfo.userAgent,
    requestId: sessionInfo.requestId,
    relatedRecords,
    integrationSource,
    integrationId,
    metadata
  });

  try {
    await auditEntry.save();
    return auditEntry;
  } catch (error) {
    console.error('Failed to create audit log entry:', error);
    // Don't throw error to prevent disrupting main operations
    return null;
  }
};

// Static method for asset creation audit
AssetAuditLogSchema.statics.logAssetCreated = function(asset, user, sessionInfo = {}) {
  return this.logAction({
    asset: asset._id,
    assetTag: asset.assetTag,
    action: 'created',
    actionDescription: `Asset "${asset.name}" (${asset.assetTag}) created`,
    performedBy: user._id,
    performedByName: user.name,
    performedByEmail: user.email,
    category: 'asset_management',
    sessionInfo
  });
};

// Static method for asset updates audit
AssetAuditLogSchema.statics.logAssetUpdated = function(asset, changedFields, user, sessionInfo = {}) {
  const fieldNames = changedFields.map(f => f.field).join(', ');
  return this.logAction({
    asset: asset._id,
    assetTag: asset.assetTag,
    action: 'updated',
    actionDescription: `Asset "${asset.name}" (${asset.assetTag}) updated. Changed fields: ${fieldNames}`,
    performedBy: user._id,
    performedByName: user.name,
    performedByEmail: user.email,
    changedFields,
    category: 'asset_management',
    sessionInfo
  });
};

// Static method for maintenance audit
AssetAuditLogSchema.statics.logMaintenancePerformed = function(asset, maintenanceRecord, user, sessionInfo = {}) {
  return this.logAction({
    asset: asset._id,
    assetTag: asset.assetTag,
    action: 'maintenance_performed',
    actionDescription: `Maintenance "${maintenanceRecord.title}" completed on asset "${asset.name}" (${asset.assetTag})`,
    performedBy: user._id,
    performedByName: user.name,
    performedByEmail: user.email,
    category: 'maintenance',
    relatedRecords: [{
      recordType: 'AssetMaintenanceRecord',
      recordId: maintenanceRecord._id,
      recordDescription: maintenanceRecord.title
    }],
    sessionInfo
  });
};

// Query helpers
AssetAuditLogSchema.statics.findByAsset = function(assetId, options = {}) {
  const query = { asset: assetId, status: 'active' };
  if (options.action) query.action = options.action;
  if (options.category) query.category = options.category;
  if (options.startDate || options.endDate) {
    query.createdAt = {};
    if (options.startDate) query.createdAt.$gte = options.startDate;
    if (options.endDate) query.createdAt.$lte = options.endDate;
  }
  
  return this.find(query)
    .populate('performedBy', 'name email')
    .sort({ createdAt: -1 })
    .limit(options.limit || 100);
};

AssetAuditLogSchema.statics.findByUser = function(userId, options = {}) {
  const query = { performedBy: userId, status: 'active' };
  if (options.action) query.action = options.action;
  
  return this.find(query)
    .populate('asset', 'name assetTag')
    .sort({ createdAt: -1 })
    .limit(options.limit || 100);
};

AssetAuditLogSchema.statics.getAuditSummary = function(assetId, startDate, endDate) {
  const matchStage = { 
    asset: mongoose.Types.ObjectId(assetId),
    status: 'active'
  };
  
  if (startDate && endDate) {
    matchStage.createdAt = { $gte: startDate, $lte: endDate };
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$action',
        count: { $sum: 1 },
        lastOccurrence: { $max: '$createdAt' },
        users: { $addToSet: '$performedByName' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

// Archive old logs
AssetAuditLogSchema.statics.archiveOldLogs = async function(olderThanDays = 365) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
  
  return this.updateMany(
    { 
      createdAt: { $lt: cutoffDate },
      status: 'active'
    },
    { 
      status: 'archived',
      archivedDate: new Date()
    }
  );
};

module.exports = mongoose.model('AssetAuditLog', AssetAuditLogSchema);