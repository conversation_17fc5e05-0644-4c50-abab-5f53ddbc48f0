const mongoose = require('mongoose');

/**
 * Building Management Automation Rule Schema
 * Stores automation rules for the building management system
 */
const BuildingManagementAutomationRuleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  enabled: {
    type: Boolean,
    default: true
  },
  conditions: {
    time: {
      days: [{
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
      }],
      startTime: {
        type: String,
        validate: {
          validator: function(v) {
            return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v);
          },
          message: props => `${props.value} is not a valid time format (HH:MM)`
        }
      },
      endTime: {
        type: String,
        validate: {
          validator: function(v) {
            return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v);
          },
          message: props => `${props.value} is not a valid time format (HH:MM)`
        }
      },
      allDay: {
        type: Boolean,
        default: false
      }
    },
    temperature: {
      min: {
        type: Number
      },
      max: {
        type: Number
      }
    },
    occupancy: {
      min: {
        type: Number
      },
      max: {
        type: Number
      }
    },
    custom: {
      type: mongoose.Schema.Types.Mixed
    }
  },
  actions: {
    climate: {
      targetTemperature: {
        type: Number
      },
      mode: {
        type: String,
        enum: ['heat', 'cool', 'auto', 'off', 'eco']
      },
      zones: [{
        type: String
      }]
    },
    access: {
      lockDoors: [{
        type: String
      }],
      unlockDoors: [{
        type: String
      }]
    },
    security: {
      enableCameras: [{
        type: String
      }],
      disableCameras: [{
        type: String
      }]
    },
    network: {
      enableDevices: [{
        type: String
      }],
      disableDevices: [{
        type: String
      }]
    },
    notifications: {
      email: {
        type: Boolean,
        default: false
      },
      push: {
        type: Boolean,
        default: false
      },
      recipients: [{
        type: String
      }],
      message: {
        type: String
      }
    },
    custom: {
      type: mongoose.Schema.Types.Mixed
    }
  },
  schedule: {
    type: String,
    default: 'realtime' // 'realtime', 'hourly', 'daily'
  },
  lastRun: {
    type: Date
  },
  lastResult: {
    success: {
      type: Boolean
    },
    message: {
      type: String
    },
    timestamp: {
      type: Date
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
BuildingManagementAutomationRuleSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

/**
 * Get all automation rules
 * @returns {Promise<Array>} Array of automation rules
 */
BuildingManagementAutomationRuleSchema.statics.getAllRules = async function() {
  return await this.find().sort({ createdAt: -1 });
};

/**
 * Get all enabled automation rules
 * @returns {Promise<Array>} Array of enabled automation rules
 */
BuildingManagementAutomationRuleSchema.statics.getEnabledRules = async function() {
  return await this.find({ enabled: true }).sort({ createdAt: -1 });
};

/**
 * Get automation rule by ID
 * @param {string} id - Rule ID
 * @returns {Promise<Object>} Automation rule
 */
BuildingManagementAutomationRuleSchema.statics.getRuleById = async function(id) {
  return await this.findById(id);
};

/**
 * Create a new automation rule
 * @param {Object} ruleData - Rule data
 * @returns {Promise<Object>} Created rule
 */
BuildingManagementAutomationRuleSchema.statics.createRule = async function(ruleData) {
  return await this.create(ruleData);
};

/**
 * Update an automation rule
 * @param {string} id - Rule ID
 * @param {Object} ruleData - Updated rule data
 * @returns {Promise<Object>} Updated rule
 */
BuildingManagementAutomationRuleSchema.statics.updateRule = async function(id, ruleData) {
  return await this.findByIdAndUpdate(id, ruleData, { new: true });
};

/**
 * Delete an automation rule
 * @param {string} id - Rule ID
 * @returns {Promise<Object>} Deleted rule
 */
BuildingManagementAutomationRuleSchema.statics.deleteRule = async function(id) {
  return await this.findByIdAndDelete(id);
};

/**
 * Update rule execution result
 * @param {string} id - Rule ID
 * @param {boolean} success - Whether the execution was successful
 * @param {string} message - Result message
 * @returns {Promise<Object>} Updated rule
 */
BuildingManagementAutomationRuleSchema.statics.updateRuleResult = async function(id, success, message) {
  return await this.findByIdAndUpdate(id, {
    lastRun: new Date(),
    lastResult: {
      success,
      message,
      timestamp: new Date()
    }
  }, { new: true });
};

module.exports = mongoose.model('BuildingManagementAutomationRule', BuildingManagementAutomationRuleSchema);