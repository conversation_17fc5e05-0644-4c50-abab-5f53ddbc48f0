const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Form Schema
 * Represents a form that can be created, edited, and filled out
 */
const FormSchema = new Schema({
  // Basic form information
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  
  // Form URL slug for custom URLs
  slug: {
    type: String,
    trim: true,
    unique: true,
    sparse: true
  },
  
  // Form status
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  
  // Form type
  formType: {
    type: String,
    enum: ['standard', 'multistep', 'survey'],
    default: 'standard'
  },
  
  // Form styling and appearance
  styling: {
    theme: {
      type: String,
      enum: ['default', 'light', 'dark', 'colorful', 'minimal'],
      default: 'default'
    },
    primaryColor: {
      type: String,
      default: '#3f51b5'
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    fontFamily: {
      type: String,
      default: 'Roboto, sans-serif'
    },
    customCSS: {
      type: String
    }
  },
  
  // Form fields organized in groups
  fieldGroups: [{
    title: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String,
      trim: true
    },
    order: {
      type: Number,
      default: 0
    },
    // Conditional display logic for the entire group
    conditionalDisplay: {
      enabled: {
        type: Boolean,
        default: false
      },
      conditions: [{
        fieldId: {
          type: String
        },
        operator: {
          type: String,
          enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'starts_with', 'ends_with', 'is_empty', 'is_not_empty'],
          default: 'equals'
        },
        value: {
          type: Schema.Types.Mixed
        }
      }],
      logicType: {
        type: String,
        enum: ['and', 'or'],
        default: 'and'
      }
    },
    // Fields in this group
    fields: [{
      type: {
        type: String,
        enum: [
          'text', 'textarea', 'number', 'email', 'phone', 'date', 'time', 'datetime',
          'select', 'multiselect', 'radio', 'checkbox', 'file', 'image', 'signature',
          'name', 'address', 'rating', 'slider', 'hidden', 'html', 'divider', 'page_break'
        ],
        required: true
      },
      fieldId: {
        type: String,
        required: true
      },
      label: {
        type: String,
        required: true,
        trim: true
      },
      description: {
        type: String,
        trim: true
      },
      placeholder: {
        type: String,
        trim: true
      },
      defaultValue: {
        type: Schema.Types.Mixed
      },
      required: {
        type: Boolean,
        default: false
      },
      order: {
        type: Number,
        default: 0
      },
      // Field-specific options
      options: {
        // For select, multiselect, radio, checkbox
        choices: [{
          label: {
            type: String,
            trim: true
          },
          value: {
            type: String,
            trim: true
          }
        }],
        // For number, slider
        min: {
          type: Number
        },
        max: {
          type: Number
        },
        step: {
          type: Number,
          default: 1
        },
        // For file upload
        maxFileSize: {
          type: Number, // in bytes
          default: 5242880 // 5MB
        },
        allowedFileTypes: {
          type: [String],
          default: ['image/*', 'application/pdf']
        },
        maxFiles: {
          type: Number,
          default: 1
        },
        // For date/time
        dateFormat: {
          type: String,
          default: 'YYYY-MM-DD'
        },
        timeFormat: {
          type: String,
          default: 'HH:mm'
        },
        // For HTML content
        htmlContent: {
          type: String
        }
      },
      // Validation rules
      validation: {
        pattern: {
          type: String
        },
        patternDescription: {
          type: String
        },
        minLength: {
          type: Number
        },
        maxLength: {
          type: Number
        },
        customValidation: {
          type: String // JavaScript function as string
        }
      },
      // Conditional display logic
      conditionalDisplay: {
        enabled: {
          type: Boolean,
          default: false
        },
        conditions: [{
          fieldId: {
            type: String
          },
          operator: {
            type: String,
            enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'starts_with', 'ends_with', 'is_empty', 'is_not_empty'],
            default: 'equals'
          },
          value: {
            type: Schema.Types.Mixed
          }
        }],
        logicType: {
          type: String,
          enum: ['and', 'or'],
          default: 'and'
        }
      },
      // Styling for this specific field
      styling: {
        width: {
          type: String,
          enum: ['full', 'half', 'third', 'quarter'],
          default: 'full'
        },
        cssClass: {
          type: String
        }
      }
    }]
  }],
  
  // Form submission settings
  submission: {
    // Success message shown after form submission
    successMessage: {
      type: String,
      default: 'Thank you for your submission!'
    },
    // Redirect URL after submission
    redirectUrl: {
      type: String
    },
    // Email notifications
    notifications: [{
      type: {
        type: String,
        enum: ['admin', 'user', 'custom'],
        required: true
      },
      recipients: {
        type: [String] // Array of email addresses or user IDs
      },
      subject: {
        type: String
      },
      template: {
        type: String // Email template content with variables
      },
      // When to send this notification
      trigger: {
        type: String,
        enum: ['on_submit', 'on_approval', 'on_rejection', 'custom'],
        default: 'on_submit'
      },
      // For custom triggers
      triggerConditions: [{
        fieldId: {
          type: String
        },
        operator: {
          type: String,
          enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than'],
          default: 'equals'
        },
        value: {
          type: Schema.Types.Mixed
        }
      }]
    }],
    // Integration with ticketing system
    createTicket: {
      enabled: {
        type: Boolean,
        default: false
      },
      // Mapping from form fields to ticket fields
      fieldMappings: {
        subject: {
          type: String // Field ID to use for ticket subject
        },
        description: {
          type: String // Field ID to use for ticket description
        },
        priority: {
          type: String // Field ID to use for ticket priority
        },
        category: {
          type: String // Field ID to use for ticket category
        },
        // Additional custom field mappings
        customFields: [{
          formField: {
            type: String // Field ID from form
          },
          ticketField: {
            type: String // Field name in ticket
          }
        }]
      },
      // Default values for ticket
      defaults: {
        assignee: {
          type: Schema.Types.ObjectId,
          ref: 'User'
        },
        group: {
          type: Schema.Types.ObjectId,
          ref: 'Group'
        },
        priority: {
          type: String,
          enum: ['low', 'normal', 'high', 'urgent'],
          default: 'normal'
        },
        tags: [{
          type: String
        }]
      }
    },
    // Integration with task system
    createTask: {
      enabled: {
        type: Boolean,
        default: false
      },
      // Mapping from form fields to task fields
      fieldMappings: {
        title: {
          type: String // Field ID to use for task title
        },
        description: {
          type: String // Field ID to use for task description
        },
        dueDate: {
          type: String // Field ID to use for task due date
        },
        // Additional custom field mappings
        customFields: [{
          formField: {
            type: String // Field ID from form
          },
          taskField: {
            type: String // Field name in task
          }
        }]
      },
      // Default values for task
      defaults: {
        assignee: {
          type: Schema.Types.ObjectId,
          ref: 'User'
        },
        priority: {
          type: String,
          enum: ['low', 'normal', 'high', 'urgent'],
          default: 'normal'
        },
        tags: [{
          type: String
        }]
      }
    }
  },
  
  // Access control and permissions
  permissions: {
    // Who can view/fill this form
    access: {
      type: String,
      enum: ['public', 'authenticated', 'specific_roles', 'specific_users', 'specific_groups'],
      default: 'authenticated'
    },
    // Required for specific_roles, specific_users, specific_groups
    accessRoles: [{
      type: String
    }],
    accessUsers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    accessGroups: [{
      type: Schema.Types.ObjectId,
      ref: 'Group'
    }],
    // Who can manage this form
    manageRoles: [{
      type: String
    }],
    manageUsers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    manageGroups: [{
      type: Schema.Types.ObjectId,
      ref: 'Group'
    }],
    // Who can view submissions
    viewSubmissionsRoles: [{
      type: String
    }],
    viewSubmissionsUsers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    viewSubmissionsGroups: [{
      type: Schema.Types.ObjectId,
      ref: 'Group'
    }]
  },
  
  // Metadata
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  // Statistics
  stats: {
    views: {
      type: Number,
      default: 0
    },
    submissions: {
      type: Number,
      default: 0
    },
    completionRate: {
      type: Number,
      default: 0 // Percentage
    },
    averageCompletionTime: {
      type: Number,
      default: 0 // In seconds
    }
  }
}, {
  timestamps: true
});

// Indexes for performance
FormSchema.index({ slug: 1 });
FormSchema.index({ status: 1 });
FormSchema.index({ createdBy: 1 });
FormSchema.index({ 'permissions.access': 1 });
FormSchema.index({ 'permissions.accessRoles': 1 });
FormSchema.index({ 'permissions.accessUsers': 1 });
FormSchema.index({ 'permissions.accessGroups': 1 });

// Pre-save middleware to ensure slug uniqueness
FormSchema.pre('save', async function(next) {
  if (this.slug) {
    this.slug = this.slug.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    
    // Check if slug exists and is not this document
    const existingForm = await this.constructor.findOne({ 
      slug: this.slug,
      _id: { $ne: this._id }
    });
    
    if (existingForm) {
      // Append a random string to make it unique
      const randomString = Math.random().toString(36).substring(2, 8);
      this.slug = `${this.slug}-${randomString}`;
    }
  }
  
  next();
});

// Method to check if a user has access to view/fill this form
FormSchema.methods.hasAccessPermission = function(user) {
  // Public forms are accessible to everyone
  if (this.permissions.access === 'public') {
    return true;
  }
  
  // Authenticated forms require a logged-in user
  if (this.permissions.access === 'authenticated') {
    return !!user;
  }
  
  // If no user, no access for restricted forms
  if (!user) {
    return false;
  }
  
  // Admin users have access to everything
  if (user.roles.includes('admin')) {
    return true;
  }
  
  // Check specific roles
  if (this.permissions.access === 'specific_roles') {
    return this.permissions.accessRoles.some(role => user.roles.includes(role));
  }
  
  // Check specific users
  if (this.permissions.access === 'specific_users') {
    return this.permissions.accessUsers.some(userId => userId.equals(user._id));
  }
  
  // Check specific groups
  if (this.permissions.access === 'specific_groups') {
    return this.permissions.accessGroups.some(groupId => 
      user.groups && user.groups.some(userGroupId => userGroupId.equals(groupId))
    );
  }
  
  return false;
};

// Method to check if a user has permission to manage this form
FormSchema.methods.hasManagePermission = function(user) {
  // If no user, no management permission
  if (!user) {
    return false;
  }
  
  // Form creator always has management permission
  if (this.createdBy.equals(user._id)) {
    return true;
  }
  
  // Admin users have management permission
  if (user.roles.includes('admin')) {
    return true;
  }
  
  // Check specific roles
  if (this.permissions.manageRoles.some(role => user.roles.includes(role))) {
    return true;
  }
  
  // Check specific users
  if (this.permissions.manageUsers.some(userId => userId.equals(user._id))) {
    return true;
  }
  
  // Check specific groups
  if (this.permissions.manageGroups.some(groupId => 
    user.groups && user.groups.some(userGroupId => userGroupId.equals(groupId))
  )) {
    return true;
  }
  
  return false;
};

// Method to check if a user has permission to view submissions
FormSchema.methods.hasViewSubmissionsPermission = function(user) {
  // If no user, no view submissions permission
  if (!user) {
    return false;
  }
  
  // If user has manage permission, they can view submissions
  if (this.hasManagePermission(user)) {
    return true;
  }
  
  // Check specific roles
  if (this.permissions.viewSubmissionsRoles.some(role => user.roles.includes(role))) {
    return true;
  }
  
  // Check specific users
  if (this.permissions.viewSubmissionsUsers.some(userId => userId.equals(user._id))) {
    return true;
  }
  
  // Check specific groups
  if (this.permissions.viewSubmissionsGroups.some(groupId => 
    user.groups && user.groups.some(userGroupId => userGroupId.equals(groupId))
  )) {
    return true;
  }
  
  return false;
};

module.exports = mongoose.model('Form', FormSchema);