const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Ticket Comment Schema
 * Represents comments/replies on tickets
 */
const TicketCommentSchema = new Schema({
  // Associated ticket
  ticket: {
    type: Schema.Types.ObjectId,
    ref: 'Ticket',
    required: true
  },
  
  // Comment content
  content: {
    type: String,
    required: true
  },
  contentType: {
    type: String,
    enum: ['text', 'html'],
    default: 'text'
  },
  
  // Author information
  author: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  authorEmail: {
    type: String,
    trim: true
  },
  authorName: {
    type: String,
    trim: true
  },
  
  // Comment type and visibility
  type: {
    type: String,
    enum: ['comment', 'internal_note', 'system', 'email_inbound', 'email_outbound'],
    default: 'comment',
    required: true
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  
  // Email integration
  emailMessageId: {
    type: String,
    trim: true
  },
  emailThreadId: {
    type: String,
    trim: true
  },
  emailInReplyTo: {
    type: String,
    trim: true
  },
  emailHeaders: {
    type: Map,
    of: String,
    default: new Map()
  },
  
  // Attachments
  attachments: [{
    filename: {
      type: String,
      required: true
    },
    originalName: {
      type: String,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // System tracking
  isFirstResponse: {
    type: Boolean,
    default: false
  },
  triggerNotifications: {
    type: Boolean,
    default: true
  },
  
  // Edit tracking
  editedAt: {
    type: Date
  },
  editedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  originalContent: {
    type: String
  }
}, {
  timestamps: true
});

// Indexes for performance
TicketCommentSchema.index({ ticket: 1, createdAt: 1 });
TicketCommentSchema.index({ author: 1 });
TicketCommentSchema.index({ type: 1 });
TicketCommentSchema.index({ emailMessageId: 1 });
TicketCommentSchema.index({ emailThreadId: 1 });

// Text search index
TicketCommentSchema.index({ 
  content: 'text'
});

module.exports = mongoose.model('TicketComment', TicketCommentSchema);