const mongoose = require('mongoose');

const GoogleDriveConfigSchema = new mongoose.Schema({
  clientId: {
    type: String,
    required: true
  },
  clientSecret: {
    type: String,
    required: true
  },
  redirectUri: {
    type: String,
    required: true
  },
  tokenPath: {
    type: String,
    default: './google-drive-token.json'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
GoogleDriveConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('googleDriveConfig', GoogleDriveConfigSchema);