const mongoose = require('mongoose');
const { Schema } = mongoose;

/**
 * Electrical Panel Schema
 * Represents an electrical breaker panel in the building
 */
const ElectricalPanelSchema = new Schema({
  buildingId: { type: Schema.Types.ObjectId, ref: 'Building' },
  floorId: { type: Schema.Types.ObjectId, ref: 'Floor' },
  name: { type: String, required: true, trim: true },
  code: { type: String, trim: true }, // e.g., Panel A, Panel B
  room: { type: String, trim: true },
  locationIconId: { type: Schema.Types.ObjectId, ref: 'FloorplanIcon' },
  notes: { type: String, trim: true },
  createdBy: { type: Schema.Types.ObjectId, ref: 'User' }
}, { timestamps: true });

ElectricalPanelSchema.index({ name: 1 });
ElectricalPanelSchema.index({ code: 1 });
ElectricalPanelSchema.index({ buildingId: 1, floorId: 1 });

module.exports = mongoose.model('ElectricalPanel', ElectricalPanelSchema);
