const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * FloorplanIcon Schema
 * Represents an icon placed on a floor's floorplan in the building management system
 */
const FloorplanIconSchema = new Schema({
  floorId: {
    type: Schema.Types.ObjectId,
    ref: 'Floor',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['temperature', 'door', 'camera', 'motion', 'light', 'hvac', 'fire', 'water', 'power', 'network', 'custom'],
    required: true
  },
  customType: {
    type: String,
    trim: true
  },
  position: {
    x: { type: Number, required: true }, // x coordinate on the floorplan
    y: { type: Number, required: true }, // y coordinate on the floorplan
    rotation: { type: Number, default: 0 } // rotation in degrees
  },
  size: {
    width: { type: Number, default: 32 }, // width in pixels
    height: { type: Number, default: 32 } // height in pixels
  },
  color: {
    type: String,
    default: '#1976d2' // default blue color
  },
  status: {
    type: String,
    enum: ['normal', 'warning', 'alert', 'inactive'],
    default: 'normal'
  },
  data: {
    value: { type: Schema.Types.Mixed }, // current value (temperature, status, etc.)
    unit: { type: String }, // unit of measurement (°F, °C, etc.)
    min: { type: Number }, // minimum value
    max: { type: Number }, // maximum value
    lastUpdated: { type: Date } // last time the data was updated
  },
  deviceId: {
    type: String, // ID of the physical device this icon represents
    trim: true
  },
  integrationSource: {
    type: String, // Source of the data (dreo, unifiAccess, etc.)
    trim: true
  },
  metadata: {
    description: { type: String },
    room: { type: String },
    notes: { type: String }
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
FloorplanIconSchema.index({ floorId: 1 });
FloorplanIconSchema.index({ type: 1 });
FloorplanIconSchema.index({ status: 1 });
FloorplanIconSchema.index({ deviceId: 1 }, { sparse: true });

module.exports = mongoose.model('FloorplanIcon', FloorplanIconSchema);