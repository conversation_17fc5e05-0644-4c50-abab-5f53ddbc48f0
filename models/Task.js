const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Task Schema
 * Base schema for all types of tasks in the system
 */
const TaskSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['open', 'in_progress', 'on_hold', 'completed', 'cancelled'],
    default: 'open'
  },
  dueDate: {
    type: Date
  },
  // For recurring tasks
  recurrence: {
    isRecurring: { type: Boolean, default: false },
    pattern: { 
      type: String, 
      enum: ['daily', 'weekly', 'biweekly', 'monthly', 'custom'],
      required: function() { return this.recurrence.isRecurring; }
    },
    daysOfWeek: [{ 
      type: Number, 
      min: 0, 
      max: 6 
    }], // 0 = Sunday, 1 = Monday, etc.
    interval: { 
      type: Number, 
      min: 1, 
      default: 1 
    }, // e.g., every 2 weeks
    endDate: { type: Date },
    count: { type: Number, min: 1 }, // number of occurrences
    exceptions: [{ type: Date }] // dates when the recurring task doesn't occur
  },
  // For task dependencies
  dependencies: [{
    task: {
      type: Schema.Types.ObjectId,
      ref: 'Task'
    },
    type: {
      type: String,
      enum: ['blocks', 'blocked_by', 'related_to'],
      default: 'related_to'
    }
  }],
  // For task comments/conversation
  comments: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    text: {
      type: String,
      required: true
    },
    date: {
      type: Date,
      default: Date.now
    },
    isInternal: {
      type: Boolean,
      default: false
    }
  }],
  // For file attachments
  attachments: [{
    name: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // For task completion
  completedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  completedAt: {
    type: Date
  },
  // For task categorization
  tags: [{
    type: String,
    trim: true
  }],
  // Discriminator key for task type
  taskType: {
    type: String,
    required: true,
    default: 'general'
  }
}, {
  timestamps: true,
  discriminatorKey: 'taskType'
});

// Create indexes for faster queries
TaskSchema.index({ createdBy: 1 });
TaskSchema.index({ assignedTo: 1 });
TaskSchema.index({ status: 1 });
TaskSchema.index({ dueDate: 1 });
TaskSchema.index({ taskType: 1 });
TaskSchema.index({ 'tags': 1 });

module.exports = mongoose.model('Task', TaskSchema);