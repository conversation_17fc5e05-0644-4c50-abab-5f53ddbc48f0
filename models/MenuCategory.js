const mongoose = require('mongoose');

const MenuCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  icon: {
    type: String,
    default: 'folder' // Default Material-UI icon
  },
  color: {
    type: String,
    default: '#1976d2' // Default primary color
  },
  order: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to update the updatedAt field
MenuCategorySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create default categories if they don't exist
MenuCategorySchema.statics.createDefaultCategories = async function() {
  const categories = [
    {
      name: 'Core Features',
      description: 'Essential portal features',
      icon: 'dashboard',
      color: '#1976d2'
    },
    {
      name: 'Google Services',
      description: 'Google related services',
      icon: 'cloud',
      color: '#4285F4'
    },
    {
      name: 'Building Management',
      description: 'Building and facility management',
      icon: 'business',
      color: '#0B8043'
    },
    {
      name: 'Security',
      description: 'Security and access control',
      icon: 'security',
      color: '#D50000'
    },
    {
      name: 'Administration',
      description: 'Administrative tools',
      icon: 'admin_panel_settings',
      color: '#6200EA'
    },
    {
      name: 'Integrations',
      description: 'Third-party integrations',
      icon: 'extension',
      color: '#FF6D00'
    }
  ];

  for (const category of categories) {
    try {
      await this.findOneAndUpdate(
        { name: category.name },
        category,
        { upsert: true, new: true }
      );
    } catch (err) {
      console.error(`Error creating default category ${category.name}:`, err);
    }
  }
};

module.exports = mongoose.model('MenuCategory', MenuCategorySchema);