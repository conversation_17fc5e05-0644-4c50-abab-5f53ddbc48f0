const mongoose = require('mongoose');

/**
 * HVAC Unit Schema
 * Represents HVAC systems with filter tracking, service history, and maintenance scheduling
 * Part of Phase 4 - Climate heatmap + HVAC service tracking
 */
const hvacUnitSchema = new mongoose.Schema({
  // Basic Unit Information
  unitId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  name: {
    type: String,
    required: true
  },
  unitType: {
    type: String,
    required: true,
    enum: ['air_handler', 'heat_pump', 'furnace', 'ac_unit', 'vrf_system', 'mini_split', 'packaged_unit', 'chiller', 'boiler'],
    index: true
  },
  
  // Location Information
  buildingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Building',
    required: true,
    index: true
  },
  floorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Floor',
    index: true
  },
  room: {
    type: String,
    required: true
  },
  zone: {
    type: String,
    index: true
  },
  
  // Physical Position (for FloorPlan mapping)
  iconId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FloorplanIcon'
  },
  x: {
    type: Number,
    min: 0,
    max: 100
  },
  y: {
    type: Number,
    min: 0,
    max: 100
  },
  
  // Unit Specifications
  specifications: {
    manufacturer: String,
    model: String,
    serialNumber: String,
    capacity: {
      btu: Number,
      tonnage: Number,
      cfm: Number // Cubic feet per minute
    },
    efficiency: {
      seer: Number, // Seasonal Energy Efficiency Ratio
      hspf: Number, // Heating Seasonal Performance Factor
      eer: Number   // Energy Efficiency Ratio
    },
    installDate: Date,
    warrantyExpiration: Date
  },
  
  // Current Status
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'needs_repair', 'out_of_service'],
    default: 'active',
    index: true
  },
  operationalStatus: {
    isRunning: {
      type: Boolean,
      default: false
    },
    currentMode: {
      type: String,
      enum: ['heating', 'cooling', 'auto', 'fan_only', 'off'],
      default: 'off'
    },
    setTemperature: Number,
    currentTemperature: Number,
    humidity: Number,
    lastStatusUpdate: {
      type: Date,
      default: Date.now
    }
  },
  
  // Filter Information
  filters: [{
    filterType: {
      type: String,
      required: true,
      enum: ['pleated', 'fiberglass', 'electrostatic', 'hepa', 'carbon', 'washable']
    },
    size: {
      type: String,
      required: true // e.g., "16x25x1", "20x20x4"
    },
    merv: {
      type: Number,
      min: 1,
      max: 20 // MERV rating (Minimum Efficiency Reporting Value)
    },
    position: {
      type: String,
      required: true // e.g., "return_air", "supply_air", "intake_1"
    },
    installDate: {
      type: Date,
      required: true
    },
    lastChanged: Date,
    nextChangeDate: {
      type: Date,
      required: true,
      index: true
    },
    changeFrequency: {
      type: Number,
      required: true,
      default: 90 // Days between filter changes
    },
    brand: String,
    partNumber: String,
    cost: Number,
    notes: String,
    
    // Filter Status
    condition: {
      type: String,
      enum: ['new', 'good', 'fair', 'needs_replacement', 'overdue'],
      default: 'good'
    },
    
    // Alert Configuration
    alerts: {
      daysBeforeWarning: {
        type: Number,
        default: 14
      },
      daysBeforeCritical: {
        type: Number,
        default: 7
      }
    }
  }],
  
  // Service History
  serviceHistory: [{
    serviceDate: {
      type: Date,
      required: true
    },
    serviceType: {
      type: String,
      required: true,
      enum: ['routine_maintenance', 'filter_change', 'repair', 'inspection', 'cleaning', 'calibration', 'warranty_service', 'emergency_repair']
    },
    description: {
      type: String,
      required: true
    },
    technician: {
      name: String,
      company: String,
      licenseNumber: String
    },
    cost: Number,
    partsReplaced: [String],
    workPerformed: String,
    recommendations: String,
    nextServiceDate: Date,
    serviceNotes: String,
    attachments: [{
      filename: String,
      url: String,
      type: String
    }],
    
    // Integration with existing systems
    ticketId: String, // Link to GLPI or other ticketing system
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Maintenance Schedule
  maintenanceSchedule: {
    routineInspectionFrequency: {
      type: Number,
      default: 180 // Days between routine inspections
    },
    lastInspection: Date,
    nextInspection: {
      type: Date,
      index: true
    },
    
    // Seasonal maintenance
    springMaintenance: {
      completed: Boolean,
      completedDate: Date,
      notes: String
    },
    fallMaintenance: {
      completed: Boolean,
      completedDate: Date,
      notes: String
    }
  },
  
  // Integration Information
  integrationSource: {
    type: String,
    enum: ['dreo', 'skyportcloud', 'lg_thinq', 'manual'],
    index: true
  },
  deviceId: {
    type: String,
    index: true // External device ID for integration
  },
  
  // Audit Trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for performance
hvacUnitSchema.index({ buildingId: 1, floorId: 1 });
hvacUnitSchema.index({ unitType: 1, status: 1 });
hvacUnitSchema.index({ 'filters.nextChangeDate': 1 });
hvacUnitSchema.index({ 'maintenanceSchedule.nextInspection': 1 });
hvacUnitSchema.index({ zone: 1, status: 1 });

// Virtual for filter status summary
hvacUnitSchema.virtual('filterStatus').get(function() {
  const now = new Date();
  const overdue = this.filters.filter(f => f.nextChangeDate < now);
  const due = this.filters.filter(f => {
    const daysUntilChange = (f.nextChangeDate - now) / (1000 * 60 * 60 * 24);
    return daysUntilChange <= (f.alerts?.daysBeforeWarning || 14) && daysUntilChange > 0;
  });
  
  return {
    total: this.filters.length,
    overdue: overdue.length,
    due: due.length,
    good: this.filters.length - overdue.length - due.length
  };
});

// Virtual for next maintenance requirement
hvacUnitSchema.virtual('nextMaintenance').get(function() {
  const filterDates = this.filters.map(f => f.nextChangeDate).filter(Boolean);
  const inspectionDate = this.maintenanceSchedule?.nextInspection;
  
  const allDates = [...filterDates];
  if (inspectionDate) allDates.push(inspectionDate);
  
  if (allDates.length === 0) return null;
  
  const nextDate = new Date(Math.min(...allDates));
  const now = new Date();
  const daysUntil = Math.ceil((nextDate - now) / (1000 * 60 * 60 * 24));
  
  let type = 'inspection';
  if (filterDates.includes(nextDate.getTime())) {
    type = 'filter_change';
  }
  
  return {
    date: nextDate,
    type,
    daysUntil,
    isOverdue: daysUntil < 0,
    isDue: daysUntil <= 7,
    isWarning: daysUntil <= 14
  };
});

// Static method to find units by location
hvacUnitSchema.statics.findByLocation = function(buildingId, floorId = null) {
  const query = { buildingId };
  if (floorId) query.floorId = floorId;
  
  return this.find(query)
    .populate('buildingId', 'name code')
    .populate('floorId', 'name code')
    .populate('iconId')
    .sort({ room: 1, name: 1 });
};

// Static method to find units needing maintenance
hvacUnitSchema.statics.findMaintenanceDue = function(daysAhead = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + daysAhead);
  
  return this.find({
    $or: [
      { 'filters.nextChangeDate': { $lte: futureDate } },
      { 'maintenanceSchedule.nextInspection': { $lte: futureDate } }
    ],
    status: { $in: ['active', 'maintenance'] }
  })
  .populate('buildingId', 'name code')
  .populate('floorId', 'name code')
  .sort({ 'filters.nextChangeDate': 1, 'maintenanceSchedule.nextInspection': 1 });
};

// Instance method to record filter change
hvacUnitSchema.methods.recordFilterChange = function(filterPosition, changeDetails, performedBy) {
  const filter = this.filters.find(f => f.position === filterPosition);
  if (!filter) {
    throw new Error('Filter position not found');
  }
  
  const changeDate = changeDetails.changeDate || new Date();
  
  // Update filter information
  filter.lastChanged = changeDate;
  filter.installDate = changeDate;
  filter.nextChangeDate = new Date(changeDate.getTime() + (filter.changeFrequency * 24 * 60 * 60 * 1000));
  filter.condition = 'new';
  
  if (changeDetails.newFilterBrand) filter.brand = changeDetails.newFilterBrand;
  if (changeDetails.newFilterPartNumber) filter.partNumber = changeDetails.newFilterPartNumber;
  if (changeDetails.cost) filter.cost = changeDetails.cost;
  
  // Add to service history
  this.serviceHistory.push({
    serviceDate: changeDate,
    serviceType: 'filter_change',
    description: `Filter changed for position: ${filterPosition}`,
    cost: changeDetails.cost || 0,
    partsReplaced: [filter.brand && filter.partNumber ? `${filter.brand} ${filter.partNumber}` : 'HVAC Filter'],
    workPerformed: changeDetails.notes || 'Routine filter replacement',
    performedBy: performedBy,
    serviceNotes: changeDetails.notes || ''
  });
  
  this.updatedBy = performedBy;
  return this.save();
};

// Instance method to schedule maintenance
hvacUnitSchema.methods.scheduleInspection = function(inspectionDate, performedBy) {
  this.maintenanceSchedule.nextInspection = inspectionDate;
  this.updatedBy = performedBy;
  return this.save();
};

// Pre-save middleware to update timestamps and calculate next maintenance
hvacUnitSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Auto-calculate filter conditions based on dates
  const now = new Date();
  this.filters.forEach(filter => {
    if (filter.nextChangeDate) {
      const daysUntilChange = (filter.nextChangeDate - now) / (1000 * 60 * 60 * 24);
      
      if (daysUntilChange < 0) {
        filter.condition = 'overdue';
      } else if (daysUntilChange <= (filter.alerts?.daysBeforeCritical || 7)) {
        filter.condition = 'needs_replacement';
      } else if (daysUntilChange <= (filter.alerts?.daysBeforeWarning || 14)) {
        filter.condition = 'fair';
      } else {
        filter.condition = 'good';
      }
    }
  });
  
  next();
});

module.exports = mongoose.model('HVACUnit', hvacUnitSchema);