const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Core Asset Schema
 * Comprehensive asset management model inspired by AssetPanda and GLPI
 */
const AssetSchema = new Schema({
  // Basic Asset Information
  name: {
    type: String,
    required: true,
    trim: true
  },
  assetTag: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  serialNumber: {
    type: String,
    trim: true,
    sparse: true
  },
  model: {
    type: String,
    trim: true
  },
  manufacturer: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },

  // Categorization
  category: {
    type: Schema.Types.ObjectId,
    ref: 'AssetCategory',
    required: true
  },
  subCategory: {
    type: Schema.Types.ObjectId,
    ref: 'AssetSubCategory'
  },
  tags: [{
    type: String,
    trim: true
  }],

  // Status and Lifecycle
  status: {
    type: String,
    enum: [
      'active',
      'inactive',
      'in_maintenance',
      'retired',
      'disposed',
      'lost',
      'stolen',
      'on_loan',
      'reserved'
    ],
    default: 'active'
  },
  condition: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor', 'needs_repair'],
    default: 'good'
  },

  // Location and Assignment
  location: {
    type: Schema.Types.ObjectId,
    ref: 'AssetLocation'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  department: {
    type: String,
    trim: true
  },
  room: {
    type: Schema.Types.ObjectId,
    ref: 'Room'
  },

  // Financial Information
  purchasePrice: {
    type: Number,
    min: 0
  },
  currentValue: {
    type: Number,
    min: 0
  },
  depreciationRate: {
    type: Number,
    min: 0,
    max: 100
  },
  purchaseDate: {
    type: Date
  },
  warrantyExpiration: {
    type: Date
  },
  vendor: {
    type: String,
    trim: true
  },
  poNumber: {
    type: String,
    trim: true
  },

  // Technical Specifications
  specifications: {
    cpu: String,
    ram: String,
    storage: String,
    os: String,
    ipAddress: String,
    macAddress: String,
    hostname: String,
    customFields: [{
      name: String,
      value: String,
      type: {
        type: String,
        enum: ['text', 'number', 'date', 'boolean', 'url']
      }
    }]
  },

  // Maintenance Information
  lastMaintenanceDate: {
    type: Date
  },
  nextMaintenanceDate: {
    type: Date
  },
  maintenanceSchedule: {
    type: String,
    enum: ['none', 'monthly', 'quarterly', 'semi_annual', 'annual', 'custom']
  },
  maintenanceNotes: {
    type: String
  },

  // Barcode and QR Code
  barcode: {
    type: String,
    unique: true,
    sparse: true
  },
  qrCode: {
    type: String,
    unique: true,
    sparse: true
  },

  // Attachments and Documentation
  attachments: [{
    name: String,
    url: String,
    type: String,
    size: Number,
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Insurance and Compliance
  insuranceValue: {
    type: Number,
    min: 0
  },
  complianceNotes: {
    type: String
  },
  riskLevel: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'low'
  },

  // Tracking and History
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // GLPI Integration
  glpiId: {
    type: String,
    sparse: true
  },
  glpiItemType: {
    type: String,
    default: 'Computer'
  },

  // Soft Delete
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date
  },
  deletedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
AssetSchema.index({ assetTag: 1 });
AssetSchema.index({ serialNumber: 1 });
AssetSchema.index({ status: 1 });
AssetSchema.index({ category: 1 });
AssetSchema.index({ assignedTo: 1 });
AssetSchema.index({ location: 1 });
AssetSchema.index({ tags: 1 });
AssetSchema.index({ isDeleted: 1 });
AssetSchema.index({ glpiId: 1 });

// Virtual for calculated depreciation
AssetSchema.virtual('deprecatedValue').get(function() {
  if (!this.purchasePrice || !this.purchaseDate || !this.depreciationRate) {
    return this.currentValue || this.purchasePrice || 0;
  }
  
  const yearsOwned = (Date.now() - this.purchaseDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
  const depreciation = this.purchasePrice * (this.depreciationRate / 100) * yearsOwned;
  return Math.max(0, this.purchasePrice - depreciation);
});

// Virtual for age in days
AssetSchema.virtual('ageInDays').get(function() {
  if (!this.purchaseDate) return null;
  return Math.floor((Date.now() - this.purchaseDate.getTime()) / (1000 * 60 * 60 * 24));
});

// Pre-save middleware
AssetSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.lastModifiedBy = this._modifiedBy;
  }
  next();
});

// Static methods
AssetSchema.statics.findByAssetTag = function(assetTag) {
  return this.findOne({ assetTag, isDeleted: false });
};

AssetSchema.statics.findBySerialNumber = function(serialNumber) {
  return this.findOne({ serialNumber, isDeleted: false });
};

AssetSchema.statics.findByStatus = function(status) {
  return this.find({ status, isDeleted: false });
};

AssetSchema.statics.findByCategory = function(categoryId) {
  return this.find({ category: categoryId, isDeleted: false });
};

AssetSchema.statics.findAssignedTo = function(userId) {
  return this.find({ assignedTo: userId, isDeleted: false });
};

// Instance methods
AssetSchema.methods.softDelete = function(deletedBy) {
  this.isDeleted = true;
  this.deletedAt = new Date();
  this.deletedBy = deletedBy;
  return this.save();
};

AssetSchema.methods.restore = function() {
  this.isDeleted = false;
  this.deletedAt = null;
  this.deletedBy = null;
  return this.save();
};

AssetSchema.methods.updateValue = function(newValue) {
  this.currentValue = newValue;
  this.lastModifiedBy = this._modifiedBy;
  return this.save();
};

module.exports = mongoose.model('Asset', AssetSchema);