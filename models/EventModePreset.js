const mongoose = require('mongoose');

const EventModePresetSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500
  },
  eventType: {
    type: String,
    required: true,
    enum: ['sunday_service', 'youth_night', 'conference', 'wedding', 'funeral', 'community_event', 'maintenance', 'emergency', 'custom'],
    default: 'custom'
  },
  icon: {
    type: String,
    default: 'event'
  },
  color: {
    type: String,
    default: '#2196F3',
    validate: {
      validator: function(v) {
        return /^#[0-9A-F]{6}$/i.test(v);
      },
      message: 'Color must be a valid hex color code'
    }
  },
  
  // Layer visibility configuration
  layerConfig: {
    electrical: {
      enabled: { type: Boolean, default: false },
      opacity: { type: Number, default: 1.0, min: 0, max: 1 }
    },
    safety: {
      enabled: { type: Boolean, default: true },
      opacity: { type: Number, default: 1.0, min: 0, max: 1 }
    },
    doors: {
      enabled: { type: Boolean, default: true },
      opacity: { type: Number, default: 1.0, min: 0, max: 1 }
    },
    cameras: {
      enabled: { type: Boolean, default: true },
      opacity: { type: Number, default: 0.7, min: 0, max: 1 }
    },
    hvac: {
      enabled: { type: Boolean, default: false },
      opacity: { type: Number, default: 0.5, min: 0, max: 1 }
    },
    wifi: {
      enabled: { type: Boolean, default: false },
      opacity: { type: Number, default: 0.5, min: 0, max: 1 }
    },
    utilities: {
      enabled: { type: Boolean, default: false },
      opacity: { type: Number, default: 0.3, min: 0, max: 1 }
    },
    churchEvents: {
      enabled: { type: Boolean, default: true },
      opacity: { type: Number, default: 1.0, min: 0, max: 1 }
    },
    avEquipment: {
      enabled: { type: Boolean, default: false },
      opacity: { type: Number, default: 1.0, min: 0, max: 1 }
    },
    temperature: {
      enabled: { type: Boolean, default: false },
      opacity: { type: Number, default: 0.6, min: 0, max: 1 }
    }
  },
  
  // Quick action shortcuts
  quickActions: [{
    name: { type: String, required: true },
    action: { type: String, required: true }, // 'unlock_doors', 'start_cameras', 'check_av', 'emergency_lights'
    icon: { type: String, default: 'play_arrow' },
    color: { type: String, default: '#4CAF50' },
    enabled: { type: Boolean, default: true },
    requiresConfirmation: { type: Boolean, default: false },
    permissionLevel: { type: String, enum: ['viewer', 'admin'], default: 'admin' }
  }],
  
  // Specific room/area focus
  focusAreas: [{
    name: { type: String, required: true },
    buildingId: { type: mongoose.Schema.Types.ObjectId, ref: 'Building' },
    floorId: { type: mongoose.Schema.Types.ObjectId, ref: 'Floor' },
    roomName: { type: String },
    coordinates: {
      x: { type: Number },
      y: { type: Number },
      zoom: { type: Number, default: 1.0 }
    },
    priority: { type: Number, default: 1 } // 1 = primary focus, 2 = secondary, etc.
  }],
  
  // Automated systems configuration
  systemSettings: {
    doors: {
      autoUnlock: [{
        doorId: { type: String },
        unlockTime: { type: Number }, // minutes before event
        relockTime: { type: Number }  // minutes after event
      }],
      accessLevels: [{ type: String }] // Which access groups to enable
    },
    cameras: {
      autoRecord: { type: Boolean, default: false },
      recordingProfile: { type: String }, // 'high_quality', 'standard', 'motion_only'
      focusCameras: [{ type: String }] // Camera IDs to highlight
    },
    hvac: {
      targetTemperature: { type: Number },
      preConditionTime: { type: Number }, // minutes before event
      energySaveMode: { type: Boolean, default: true }
    },
    lighting: {
      presetName: { type: String },
      dimLevel: { type: Number, min: 0, max: 100 },
      autoSchedule: { type: Boolean, default: false }
    }
  },
  
  // Usage tracking
  usage: {
    timesUsed: { type: Number, default: 0 },
    lastUsed: { type: Date },
    averageRating: { type: Number, min: 1, max: 5 },
    feedback: [{
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      rating: { type: Number, min: 1, max: 5 },
      comment: { type: String, maxlength: 500 },
      createdAt: { type: Date, default: Date.now }
    }]
  },
  
  // Access control
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  allowedRoles: [{
    type: String,
    enum: ['admin', 'facilities', 'volunteer', 'staff']
  }],
  
  // Schedule integration
  scheduleIntegration: {
    googleCalendarId: { type: String },
    autoActivate: { type: Boolean, default: false },
    activationBuffer: { type: Number, default: 15 }, // minutes before event
    deactivationBuffer: { type: Number, default: 30 } // minutes after event
  }
}, {
  timestamps: true
});

// Indexes
EventModePresetSchema.index({ eventType: 1, isPublic: 1 });
EventModePresetSchema.index({ createdBy: 1 });
EventModePresetSchema.index({ 'usage.timesUsed': -1 });
EventModePresetSchema.index({ 'usage.averageRating': -1 });

// Virtual for popularity score
EventModePresetSchema.virtual('popularityScore').get(function() {
  const usageWeight = 0.6;
  const ratingWeight = 0.4;
  const normalizedUsage = Math.min(this.usage.timesUsed / 10, 1); // Normalize to 0-1
  const normalizedRating = (this.usage.averageRating || 3) / 5; // Normalize to 0-1
  return (normalizedUsage * usageWeight + normalizedRating * ratingWeight);
});

// Method to apply preset to current session
EventModePresetSchema.methods.apply = function(currentConfig = {}) {
  const appliedConfig = {
    ...currentConfig,
    layers: { ...currentConfig.layers, ...this.layerConfig },
    quickActions: this.quickActions.filter(action => action.enabled),
    focusAreas: this.focusAreas,
    systemSettings: this.systemSettings,
    presetInfo: {
      id: this._id,
      name: this.name,
      eventType: this.eventType,
      appliedAt: new Date()
    }
  };
  
  // Update usage statistics
  this.usage.timesUsed += 1;
  this.usage.lastUsed = new Date();
  
  return appliedConfig;
};

// Method to get default presets
EventModePresetSchema.statics.getDefaults = function() {
  return [
    {
      name: 'Sunday Service',
      description: 'Standard Sunday worship service configuration with safety, doors, and AV equipment visible',
      eventType: 'sunday_service',
      icon: 'church',
      color: '#8BC34A',
      layerConfig: {
        electrical: { enabled: false, opacity: 0.3 },
        safety: { enabled: true, opacity: 1.0 },
        doors: { enabled: true, opacity: 1.0 },
        cameras: { enabled: true, opacity: 0.8 },
        hvac: { enabled: false, opacity: 0.5 },
        wifi: { enabled: false, opacity: 0.5 },
        utilities: { enabled: false, opacity: 0.3 },
        churchEvents: { enabled: true, opacity: 1.0 },
        avEquipment: { enabled: true, opacity: 1.0 },
        temperature: { enabled: false, opacity: 0.6 }
      },
      quickActions: [
        { name: 'Check AV Systems', action: 'check_av', icon: 'mic', color: '#FF9800' },
        { name: 'Unlock Main Doors', action: 'unlock_main_doors', icon: 'lock_open', color: '#4CAF50', requiresConfirmation: true },
        { name: 'Start Recording', action: 'start_recording', icon: 'videocam', color: '#F44336', requiresConfirmation: true }
      ],
      isPublic: true,
      allowedRoles: ['admin', 'facilities', 'volunteer', 'staff']
    },
    {
      name: 'Youth Night',
      description: 'Youth ministry events with enhanced security and AV focus',
      eventType: 'youth_night',
      icon: 'groups',
      color: '#E91E63',
      layerConfig: {
        electrical: { enabled: false, opacity: 0.3 },
        safety: { enabled: true, opacity: 1.0 },
        doors: { enabled: true, opacity: 1.0 },
        cameras: { enabled: true, opacity: 1.0 },
        hvac: { enabled: true, opacity: 0.7 },
        wifi: { enabled: true, opacity: 0.8 },
        utilities: { enabled: false, opacity: 0.3 },
        churchEvents: { enabled: true, opacity: 1.0 },
        avEquipment: { enabled: true, opacity: 1.0 },
        temperature: { enabled: true, opacity: 0.8 }
      },
      quickActions: [
        { name: 'Youth Area Setup', action: 'youth_setup', icon: 'celebration', color: '#E91E63' },
        { name: 'Check WiFi Coverage', action: 'check_wifi', icon: 'wifi', color: '#2196F3' },
        { name: 'Temperature Control', action: 'hvac_youth_mode', icon: 'thermostat', color: '#FF5722' }
      ],
      isPublic: true,
      allowedRoles: ['admin', 'facilities', 'volunteer', 'staff']
    },
    {
      name: 'Conference Mode',
      description: 'Large event configuration with all systems monitoring enabled',
      eventType: 'conference',
      icon: 'event',
      color: '#673AB7',
      layerConfig: {
        electrical: { enabled: true, opacity: 0.5 },
        safety: { enabled: true, opacity: 1.0 },
        doors: { enabled: true, opacity: 1.0 },
        cameras: { enabled: true, opacity: 1.0 },
        hvac: { enabled: true, opacity: 1.0 },
        wifi: { enabled: true, opacity: 1.0 },
        utilities: { enabled: false, opacity: 0.3 },
        churchEvents: { enabled: true, opacity: 1.0 },
        avEquipment: { enabled: true, opacity: 1.0 },
        temperature: { enabled: true, opacity: 1.0 }
      },
      quickActions: [
        { name: 'Full System Check', action: 'full_system_check', icon: 'checklist', color: '#673AB7' },
        { name: 'Conference AV Setup', action: 'conference_av', icon: 'settings_input_hdmi', color: '#FF9800' },
        { name: 'Climate Optimization', action: 'climate_optimize', icon: 'eco', color: '#4CAF50' }
      ],
      isPublic: true,
      allowedRoles: ['admin', 'facilities']
    },
    {
      name: 'Emergency Mode',
      description: 'Emergency response configuration highlighting safety systems and evacuation routes',
      eventType: 'emergency',
      icon: 'emergency',
      color: '#F44336',
      layerConfig: {
        electrical: { enabled: true, opacity: 0.8 },
        safety: { enabled: true, opacity: 1.0 },
        doors: { enabled: true, opacity: 1.0 },
        cameras: { enabled: true, opacity: 1.0 },
        hvac: { enabled: false, opacity: 0.3 },
        wifi: { enabled: false, opacity: 0.3 },
        utilities: { enabled: true, opacity: 1.0 },
        churchEvents: { enabled: false, opacity: 0.3 },
        avEquipment: { enabled: false, opacity: 0.3 },
        temperature: { enabled: false, opacity: 0.3 }
      },
      quickActions: [
        { name: 'Emergency Unlock All', action: 'emergency_unlock_all', icon: 'lock_open', color: '#F44336', requiresConfirmation: true, permissionLevel: 'admin' },
        { name: 'Find Nearest AED', action: 'locate_aed', icon: 'local_hospital', color: '#F44336' },
        { name: 'Utility Shutoffs', action: 'emergency_shutoffs', icon: 'power_off', color: '#FF5722', requiresConfirmation: true, permissionLevel: 'admin' }
      ],
      isPublic: true,
      allowedRoles: ['admin', 'facilities']
    }
  ];
};

// Create default presets on first run
EventModePresetSchema.statics.initializeDefaults = async function() {
  const count = await this.countDocuments();
  if (count === 0) {
    const defaults = this.getDefaults();
    // Need a system user ID - using admin for now
    const systemUser = await mongoose.model('User').findOne({ role: 'admin' });
    if (systemUser) {
      const presetsWithUser = defaults.map(preset => ({
        ...preset,
        createdBy: systemUser._id
      }));
      await this.insertMany(presetsWithUser);
      console.log('Initialized default event mode presets');
    }
  }
};

module.exports = mongoose.model('EventModePreset', EventModePresetSchema);