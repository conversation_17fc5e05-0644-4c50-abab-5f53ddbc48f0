const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Ticket Forms Webhook Schema
 * Extends Google Forms webhook functionality to create tickets
 */
const TicketFormsWebhookSchema = new Schema({
  // Form identification
  formId: {
    type: String,
    required: true,
    trim: true
  },
  formName: {
    type: String,
    required: true,
    trim: true
  },
  
  // User who created this webhook
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Whether this webhook is active
  active: {
    type: Boolean,
    default: true
  },
  
  // Field mappings from form to ticket using token system
  fieldMappings: {
    // Core ticket fields
    subjectField: {
      type: String,
      required: true,
      trim: true
    },
    descriptionField: {
      type: String,
      trim: true
    },
    priorityField: {
      type: String,
      trim: true
    },
    typeField: {
      type: String,
      trim: true
    },
    dueDateField: {
      type: String,
      trim: true
    },
    categoryField: {
      type: String,
      trim: true
    },
    
    // Requester information
    requesterEmailField: {
      type: String,
      trim: true
    },
    requesterNameField: {
      type: String,
      trim: true
    },
    
    // Tags field mapping
    tagsField: {
      type: String,
      trim: true
    },
    
    // Custom field mappings with token support
    customFields: [{
      formField: {
        type: String,
        required: true,
        trim: true
      },
      ticketField: {
        type: String,
        required: true,
        trim: true
      },
      transform: {
        type: String,
        enum: ['none', 'email_to_user', 'text_to_tags', 'date_parse', 'priority_map'],
        default: 'none'
      }
    }]
  },
  
  // Token mappings for dynamic field assignment
  tokenMappings: [{
    token: {
      type: String,
      required: true,
      trim: true
    },
    value: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['static', 'form_field', 'user_lookup', 'date_calc'],
      required: true
    }
  }],
  
  // Assignment rules based on form responses
  assignmentRules: [{
    field: {
      type: String,
      required: true,
      trim: true
    },
    operator: {
      type: String,
      enum: ['equals', 'contains', 'starts_with', 'ends_with', 'regex'],
      required: true
    },
    value: {
      type: String,
      required: true,
      trim: true
    },
    assignTo: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    assignToGroup: {
      type: Schema.Types.ObjectId,
      ref: 'Group'
    },
    category: {
      type: String,
      trim: true
    },
    tags: [{
      type: String,
      trim: true
    }],
    priority: {
      type: Number,
      default: 0
    }
  }],
  
  // Default values
  defaults: {
    assignee: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    group: {
      type: Schema.Types.ObjectId,
      ref: 'Group'
    },
    category: {
      type: String,
      trim: true
    },
    priority: {
      type: String,
      enum: ['low', 'normal', 'high', 'urgent'],
      default: 'normal'
    },
    type: {
      type: String,
      enum: ['incident', 'request', 'problem', 'change'],
      default: 'request'
    },
    tags: [{
      type: String,
      trim: true
    }]
  },
  
  // Notification settings
  notifications: {
    notifyAssignee: {
      type: Boolean,
      default: true
    },
    notifyGroup: {
      type: Boolean,
      default: true
    },
    notifyFollowers: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  
  // Processing tracking
  processedResponses: [{
    responseId: {
      type: String,
      required: true,
      trim: true
    },
    processedAt: {
      type: Date,
      default: Date.now
    },
    ticketId: {
      type: Schema.Types.ObjectId,
      ref: 'Ticket'
    },
    success: {
      type: Boolean,
      default: true
    },
    error: {
      type: String,
      trim: true
    }
  }],
  
  // Last processing time
  lastChecked: {
    type: Date
  },
  
  // Statistics
  stats: {
    totalProcessed: {
      type: Number,
      default: 0
    },
    successCount: {
      type: Number,
      default: 0
    },
    errorCount: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true
});

// Indexes for performance
TicketFormsWebhookSchema.index({ formId: 1 });
TicketFormsWebhookSchema.index({ createdBy: 1 });
TicketFormsWebhookSchema.index({ active: 1 });
TicketFormsWebhookSchema.index({ 'processedResponses.responseId': 1 });

module.exports = mongoose.model('TicketFormsWebhook', TicketFormsWebhookSchema);