const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Ticket Tag Schema
 * Represents tags for flexible ticket organization
 */
const TicketTagSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true,
    lowercase: true
  },
  displayName: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  
  // Visual properties
  color: {
    type: String,
    trim: true,
    default: '#757575'
  },
  backgroundColor: {
    type: String,
    trim: true,
    default: '#f5f5f5'
  },
  
  // Usage statistics
  ticketCount: {
    type: Number,
    default: 0
  },
  
  // Auto-tagging rules
  autoRules: [{
    field: {
      type: String,
      enum: ['subject', 'description', 'requesterEmail', 'senderDomain', 'category'],
      required: true
    },
    operator: {
      type: String,
      enum: ['contains', 'starts_with', 'ends_with', 'equals', 'regex'],
      required: true
    },
    value: {
      type: String,
      required: true
    },
    priority: {
      type: Number,
      default: 0
    }
  }],
  
  // Tag relationships
  relatedTags: [{
    tag: {
      type: Schema.Types.ObjectId,
      ref: 'TicketTag'
    },
    relationship: {
      type: String,
      enum: ['similar', 'opposite', 'parent', 'child'],
      default: 'similar'
    }
  }],
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  isSystemTag: {
    type: Boolean,
    default: false
  },
  
  // Creation/modification tracking  
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
TicketTagSchema.index({ name: 1 });
TicketTagSchema.index({ isActive: 1 });
TicketTagSchema.index({ ticketCount: -1 });

// Text search index
TicketTagSchema.index({ 
  name: 'text', 
  displayName: 'text',
  description: 'text'
});

module.exports = mongoose.model('TicketTag', TicketTagSchema);