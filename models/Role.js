const mongoose = require('mongoose');

const RoleSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true
  },
  description: {
    type: String
  },
  permissions: {
    type: [String],
    default: []
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to update the updatedAt field
RoleSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create default roles if they don't exist
RoleSchema.statics.createDefaultRoles = async function() {
  const roles = [
    {
      name: 'admin',
      description: 'Administrator with full access to all features',
      permissions: ['*'],
      isDefault: true
    },
    {
      name: 'user',
      description: 'Regular user with basic access',
      permissions: ['view_shortcuts', 'view_drive'],
      isDefault: true
    }
  ];

  for (const role of roles) {
    try {
      await this.findOneAndUpdate(
        { name: role.name },
        role,
        { upsert: true, new: true }
      );
    } catch (err) {
      console.error(`Error creating default role ${role.name}:`, err);
    }
  }
};

module.exports = mongoose.model('Role', RoleSchema);