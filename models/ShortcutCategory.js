const mongoose = require('mongoose');

const ShortcutCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  icon: {
    type: String,
    default: 'folder' // Default Material-UI icon
  },
  color: {
    type: String,
    default: '#1976d2' // Default primary color
  },
  order: {
    type: Number,
    default: 0
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save middleware to update the updatedAt field
ShortcutCategorySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create default categories if they don't exist
ShortcutCategorySchema.statics.createDefaultCategories = async function() {
  const categories = [
    {
      name: 'General',
      description: 'General shortcuts',
      icon: 'link',
      color: '#1976d2'
    },
    {
      name: 'Google',
      description: 'Google related shortcuts',
      icon: 'cloud',
      color: '#4285F4'
    },
    {
      name: 'Files',
      description: 'File storage and management',
      icon: 'folder',
      color: '#FFA000'
    },
    {
      name: 'Calendar',
      description: 'Calendar and scheduling',
      icon: 'event',
      color: '#0B8043'
    }
  ];

  for (const category of categories) {
    try {
      await this.findOneAndUpdate(
        { name: category.name },
        category,
        { upsert: true, new: true }
      );
    } catch (err) {
      console.error(`Error creating default category ${category.name}:`, err);
    }
  }
};

module.exports = mongoose.model('ShortcutCategory', ShortcutCategorySchema);