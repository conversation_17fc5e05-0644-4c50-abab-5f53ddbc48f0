const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Floor Schema
 * Represents a floor within a building in the building management system
 */
const FloorSchema = new Schema({
  buildingId: {
    type: Schema.Types.ObjectId,
    ref: 'Building',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  level: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    trim: true
  },
  floorplan: {
    filename: { type: String },
    originalFilename: { type: String },
    path: { type: String },
    mimetype: { type: String },
    size: { type: Number },
    uploadDate: { type: Date, default: Date.now }
  },
  dimensions: {
    width: { type: Number }, // in feet or meters
    length: { type: Number }, // in feet or meters
    squareFootage: { type: Number }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'under_construction', 'maintenance'],
    default: 'active'
  },
  metadata: {
    occupancy: { type: Number }, // maximum occupancy
    publicAccess: { type: Boolean, default: true },
    notes: { type: String }
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
FloorSchema.index({ buildingId: 1, level: 1 }, { unique: true });
FloorSchema.index({ status: 1 });

module.exports = mongoose.model('Floor', FloorSchema);