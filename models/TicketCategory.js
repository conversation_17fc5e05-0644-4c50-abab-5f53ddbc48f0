const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Ticket Category Schema
 * Represents categories for organizing tickets
 */
const TicketCategorySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    unique: true
  },
  description: {
    type: String,
    trim: true
  },
  
  // Hierarchy support
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'TicketCategory'
  },
  level: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  path: {
    type: String,
    trim: true
  },
  
  // Visual properties
  color: {
    type: String,
    trim: true,
    default: '#1976d2'
  },
  icon: {
    type: String,
    trim: true
  },
  
  // Assignment rules
  defaultAssignee: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  defaultGroup: {
    type: Schema.Types.ObjectId,
    ref: 'Group'
  },
  
  // SLA settings
  slaSettings: {
    firstResponseTime: {
      type: Number, // in minutes
      default: 240 // 4 hours
    },
    resolutionTime: {
      type: Number, // in minutes
      default: 1440 // 24 hours
    }
  },
  
  // Auto-categorization rules
  autoRules: [{
    field: {
      type: String,
      enum: ['subject', 'description', 'requesterEmail', 'senderDomain'],
      required: true
    },
    operator: {
      type: String,
      enum: ['contains', 'starts_with', 'ends_with', 'equals', 'regex'],
      required: true
    },
    value: {
      type: String,
      required: true
    },
    priority: {
      type: Number,
      default: 0
    }
  }],
  
  // Usage statistics
  ticketCount: {
    type: Number,
    default: 0
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Order for display
  sortOrder: {
    type: Number,
    default: 0
  },
  
  // Creation/modification tracking
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
TicketCategorySchema.index({ name: 1 });
TicketCategorySchema.index({ parent: 1 });
TicketCategorySchema.index({ level: 1 });
TicketCategorySchema.index({ isActive: 1 });
TicketCategorySchema.index({ sortOrder: 1 });

// Pre-save middleware to set path and level
TicketCategorySchema.pre('save', async function(next) {
  if (this.parent) {
    try {
      const parentCategory = await this.constructor.findById(this.parent);
      if (parentCategory) {
        this.level = parentCategory.level + 1;
        this.path = parentCategory.path ? `${parentCategory.path}/${this.name}` : this.name;
      }
    } catch (error) {
      return next(error);
    }
  } else {
    this.level = 0;
    this.path = this.name;
  }
  next();
});

// Virtual for full path display
TicketCategorySchema.virtual('fullPath').get(function() {
  return this.path || this.name;
});

// Method to get all descendants
TicketCategorySchema.methods.getDescendants = async function() {
  const regex = new RegExp(`^${this.path}/`);
  return await this.constructor.find({ path: regex });
};

module.exports = mongoose.model('TicketCategory', TicketCategorySchema);