const mongoose = require('mongoose');

/**
 * ChurchEvent model for Phase 7 - Church Event Management & Room Usage
 * Integrates with Google Calendar and provides church-specific event management
 */
const churchEventSchema = new mongoose.Schema({
  // Basic event information
  eventId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 100
  },
  
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 2000
  },
  
  // Event timing
  startTime: {
    type: Date,
    required: true
  },
  
  endTime: {
    type: Date,
    required: true
  },
  
  allDay: {
    type: Boolean,
    default: false
  },
  
  // Recurrence information
  recurrence: {
    isRecurring: {
      type: Boolean,
      default: false
    },
    pattern: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly', 'custom']
    },
    frequency: Number, // Every N days/weeks/months
    daysOfWeek: [String], // For weekly recurrence: ['sunday', 'wednesday']
    endDate: Date,
    occurrenceCount: Number
  },
  
  // Location and room management
  location: {
    buildingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Building'
    },
    floorId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Floor'
    },
    roomId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Room'
    },
    roomName: String, // Human-readable room name
    customLocation: String, // For off-site or custom locations
    capacity: Number,
    expectedAttendance: Number
  },
  
  // Church-specific categorization
  eventType: {
    type: String,
    required: true,
    enum: [
      'worship_service',
      'bible_study',
      'prayer_meeting',
      'youth_group',
      'children_ministry',
      'music_practice',
      'committee_meeting',
      'wedding',
      'funeral',
      'baptism',
      'community_event',
      'fellowship',
      'outreach',
      'conference',
      'training',
      'maintenance',
      'setup',
      'cleanup',
      'other'
    ]
  },
  
  ministry: {
    type: String,
    enum: [
      'worship',
      'youth',
      'children',
      'music',
      'outreach',
      'fellowship',
      'administration',
      'facilities',
      'education',
      'care',
      'missions',
      'other'
    ]
  },
  
  // Event priority and visibility
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  
  visibility: {
    type: String,
    enum: ['public', 'private', 'staff_only', 'ministry_only'],
    default: 'public'
  },
  
  // Staff and coordination
  organizer: {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    name: String,
    email: String,
    phone: String
  },
  
  staff: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    name: String,
    role: String, // 'coordinator', 'tech', 'security', 'setup', 'cleanup'
    responsibilities: [String],
    contactInfo: {
      email: String,
      phone: String
    }
  }],
  
  // Resource and equipment requirements
  resources: {
    audioVisual: {
      required: {
        type: Boolean,
        default: false
      },
      equipment: [{
        type: String,
        enum: [
          'microphones',
          'projector',
          'screens',
          'lighting',
          'cameras',
          'live_stream',
          'recording',
          'sound_system',
          'piano',
          'organ',
          'drums',
          'guitars',
          'other'
        ]
      }],
      technicianRequired: Boolean,
      setupTime: Number, // Minutes before event
      notes: String
    },
    
    facilities: {
      heating: Boolean,
      cooling: Boolean,
      lighting: [{
        zone: String,
        level: String // 'low', 'medium', 'high', 'custom'
      }],
      security: {
        required: Boolean,
        staffRequired: Boolean,
        lockdown: Boolean, // For sensitive events
        cameras: Boolean
      },
      cleaning: {
        beforeEvent: Boolean,
        afterEvent: Boolean,
        specialRequirements: String
      }
    },
    
    catering: {
      required: Boolean,
      mealType: String, // 'breakfast', 'lunch', 'dinner', 'snacks', 'coffee'
      expectedCount: Number,
      dietaryRestrictions: [String],
      kitchen: Boolean,
      servingArea: String
    },
    
    parking: {
      expectedVehicles: Number,
      specialNeeds: String, // 'handicap', 'bus', 'oversized'
      valetRequired: Boolean
    }
  },
  
  // Preparation and setup checklists
  preparation: {
    checklist: [{
      task: String,
      responsible: String, // Role or person name
      dueTime: Date, // When this should be completed
      estimatedDuration: Number, // Minutes
      completed: Boolean,
      completedBy: String,
      completedAt: Date,
      notes: String,
      systems: [String] // Related building systems: ['hvac', 'lighting', 'audio', 'cameras']
    }],
    
    systemsChecks: [{
      system: {
        type: String,
        enum: ['hvac', 'electrical', 'lighting', 'audio', 'cameras', 'doors', 'wifi', 'safety']
      },
      status: {
        type: String,
        enum: ['not_checked', 'checking', 'ok', 'warning', 'error'],
        default: 'not_checked'
      },
      lastChecked: Date,
      checkedBy: String,
      notes: String,
      issues: [String]
    }],
    
    setupTime: {
      start: Date,
      duration: Number, // Minutes
      teamSize: Number,
      leadContact: String
    }
  },
  
  // Google Calendar integration
  googleCalendar: {
    calendarId: String,
    googleEventId: String,
    lastSynced: Date,
    syncStatus: {
      type: String,
      enum: ['synced', 'pending', 'error', 'manual'],
      default: 'manual'
    },
    syncErrors: [String]
  },
  
  // Building system impact and requirements
  buildingSystems: {
    hvac: {
      preHeatCool: Boolean,
      targetTemperature: Number,
      advanceTime: Number, // Minutes before event
      zones: [String]
    },
    
    lighting: {
      presets: [{
        zone: String,
        preset: String,
        activateTime: Date
      }],
      dimmingSchedule: [{
        time: Date,
        zone: String,
        level: Number // 0-100
      }]
    },
    
    doors: {
      unlockSchedule: [{
        doorId: String,
        unlockTime: Date,
        lockTime: Date,
        accessLevel: String
      }],
      securityMode: String // 'normal', 'enhanced', 'lockdown'
    },
    
    cameras: {
      recording: Boolean,
      liveStream: Boolean,
      presets: [String],
      privacy: Boolean // Disable certain cameras
    }
  },
  
  // Status and workflow
  status: {
    type: String,
    enum: [
      'draft',
      'planning',
      'approved',
      'preparing',
      'setup',
      'in_progress',
      'concluded',
      'cleanup',
      'completed',
      'cancelled'
    ],
    default: 'draft'
  },
  
  workflow: {
    approvalRequired: Boolean,
    approvedBy: {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      name: String,
      approvedAt: Date
    },
    
    statusHistory: [{
      status: String,
      changedBy: String,
      changedAt: {
        type: Date,
        default: Date.now
      },
      notes: String
    }]
  },
  
  // Attendance and follow-up
  attendance: {
    expected: Number,
    actual: Number,
    checkedIn: Number,
    demographics: {
      adults: Number,
      children: Number,
      youth: Number,
      visitors: Number,
      members: Number
    }
  },
  
  feedback: {
    overall: Number, // 1-5 rating
    comments: [String],
    improvements: [String],
    followUpRequired: Boolean,
    nextSteps: [String]
  },
  
  // Financial tracking
  budget: {
    allocated: Number,
    spent: Number,
    expenses: [{
      category: String,
      amount: Number,
      description: String,
      date: Date,
      approvedBy: String
    }]
  },
  
  // Administrative fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  tags: [String], // For categorization and searching
  
  notes: String,
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes for performance
churchEventSchema.index({ startTime: 1, endTime: 1 });
churchEventSchema.index({ 'location.buildingId': 1, 'location.floorId': 1 });
churchEventSchema.index({ eventType: 1, ministry: 1 });
churchEventSchema.index({ status: 1 });
churchEventSchema.index({ 'googleCalendar.calendarId': 1, 'googleCalendar.googleEventId': 1 });
churchEventSchema.index({ eventId: 1 }, { unique: true });
churchEventSchema.index({ 
  title: 'text', 
  description: 'text', 
  'location.roomName': 'text' 
});

// Virtual for event duration
churchEventSchema.virtual('duration').get(function() {
  if (!this.startTime || !this.endTime) return 0;
  return Math.round((this.endTime - this.startTime) / (1000 * 60)); // Duration in minutes
});

// Virtual for event status display
churchEventSchema.virtual('displayStatus').get(function() {
  const now = new Date();
  
  if (this.status === 'cancelled') return 'Cancelled';
  if (this.status === 'completed') return 'Completed';
  
  if (now < this.startTime) {
    if (this.status === 'setup') return 'Setting Up';
    if (this.status === 'preparing') return 'Preparing';
    return 'Upcoming';
  } else if (now >= this.startTime && now <= this.endTime) {
    return 'In Progress';
  } else {
    if (this.status === 'cleanup') return 'Cleanup';
    return 'Concluded';
  }
});

// Virtual for preparation status
churchEventSchema.virtual('preparationStatus').get(function() {
  if (!this.preparation.checklist || this.preparation.checklist.length === 0) {
    return { total: 0, completed: 0, percentage: 0 };
  }
  
  const total = this.preparation.checklist.length;
  const completed = this.preparation.checklist.filter(item => item.completed).length;
  const percentage = Math.round((completed / total) * 100);
  
  return { total, completed, percentage };
});

// Pre-save middleware to update status history
churchEventSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    if (!this.workflow.statusHistory) {
      this.workflow.statusHistory = [];
    }
    
    this.workflow.statusHistory.push({
      status: this.status,
      changedBy: 'System', // This should be set by the application
      changedAt: new Date(),
      notes: `Status changed to ${this.status}`
    });
  }
  next();
});

// Static methods
churchEventSchema.statics.findByDateRange = function(startDate, endDate, filters = {}) {
  const query = {
    $or: [
      {
        startTime: { $gte: startDate, $lte: endDate }
      },
      {
        endTime: { $gte: startDate, $lte: endDate }
      },
      {
        startTime: { $lte: startDate },
        endTime: { $gte: endDate }
      }
    ],
    isActive: true,
    ...filters
  };
  
  return this.find(query).sort({ startTime: 1 });
};

churchEventSchema.statics.findByLocation = function(buildingId, floorId, dateRange = {}) {
  const query = {
    'location.buildingId': buildingId,
    isActive: true
  };
  
  if (floorId) {
    query['location.floorId'] = floorId;
  }
  
  if (dateRange.start || dateRange.end) {
    const start = dateRange.start || new Date();
    const end = dateRange.end || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
    
    query.$or = [
      { startTime: { $gte: start, $lte: end } },
      { endTime: { $gte: start, $lte: end } },
      { startTime: { $lte: start }, endTime: { $gte: end } }
    ];
  }
  
  return this.find(query).sort({ startTime: 1 });
};

churchEventSchema.statics.findUpcoming = function(limit = 10, eventTypes = []) {
  const now = new Date();
  const query = {
    startTime: { $gte: now },
    status: { $nin: ['cancelled', 'completed'] },
    isActive: true
  };
  
  if (eventTypes.length > 0) {
    query.eventType = { $in: eventTypes };
  }
  
  return this.find(query)
    .sort({ startTime: 1 })
    .limit(limit)
    .populate('location.buildingId location.floorId organizer.userId');
};

churchEventSchema.statics.findRequiringPreparation = function() {
  const now = new Date();
  const oneHour = new Date(now.getTime() + 60 * 60 * 1000);
  
  return this.find({
    startTime: { $gte: now, $lte: oneHour },
    status: { $in: ['approved', 'planning', 'preparing'] },
    'preparation.checklist': { $exists: true, $ne: [] },
    isActive: true
  }).sort({ startTime: 1 });
};

// Instance methods
churchEventSchema.methods.updatePreparationStatus = function() {
  if (!this.preparation.systemsChecks) {
    this.preparation.systemsChecks = [];
  }
  
  // Auto-determine status based on checklist completion
  const prep = this.preparationStatus;
  
  if (prep.percentage === 100) {
    this.status = 'setup';
  } else if (prep.percentage > 50) {
    this.status = 'preparing';
  }
  
  return this.save();
};

churchEventSchema.methods.addChecklistItem = function(task, responsible, dueTime, systems = []) {
  if (!this.preparation.checklist) {
    this.preparation.checklist = [];
  }
  
  this.preparation.checklist.push({
    task,
    responsible,
    dueTime,
    systems,
    completed: false
  });
  
  return this.save();
};

churchEventSchema.methods.completeChecklistItem = function(taskId, completedBy, notes = '') {
  const item = this.preparation.checklist.id(taskId);
  if (item) {
    item.completed = true;
    item.completedBy = completedBy;
    item.completedAt = new Date();
    if (notes) item.notes = notes;
  }
  
  return this.save();
};

const ChurchEvent = mongoose.model('ChurchEvent', churchEventSchema);

module.exports = ChurchEvent;