const mongoose = require('mongoose');

const UserSchema = new mongoose.Schema({
  // Authentication fields
  googleId: {
    type: String,
    unique: true,
    sparse: true
  },
  authType: {
    type: String,
    enum: ['google', 'local'],
    required: true,
    default: 'google'
  },
  name: {
    type: String,
    required: true
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  avatar: {
    type: String
  },
  // Google auth fields
  googleAccessToken: {
    type: String
  },
  googleRefreshToken: {
    type: String
  },
  // Local auth fields
  password: {
    type: String
  },
  passwordResetToken: {
    type: String
  },
  passwordResetExpires: {
    type: Date
  },
  // 2FA fields
  twoFactorSecret: {
    type: String
  },
  twoFactorEnabled: {
    type: Boolean,
    default: false
  },
  twoFactorSetupComplete: {
    type: Boolean,
    default: false
  },
  // User status fields
  roles: {
    type: [String],
    default: ['user']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  // Presence and status fields
  presence: {
    status: {
      type: String,
      enum: ['available', 'away', 'lunch', 'busy', 'custom'],
      default: 'available'
    },
    statusMessage: {
      type: String,
      default: ''
    },
    lastActiveAt: {
      type: Date,
      default: Date.now
    },
    statusUpdatedAt: {
      type: Date,
      default: Date.now
    }
  },
  // Floating shortcut widget preferences
  widgetPreferences: {
    floatingShortcut: {
      favoriteShortcuts: {
        type: [String],
        default: []
      },
      favoritePortalPages: {
        type: [String],
        default: []
      },
      enabled: {
        type: Boolean,
        default: true
      },
      alwaysShowLabels: {
        type: Boolean,
        default: false
      }
    }
  },
  // Additional profile fields for staff directory
  jobTitle: {
    type: String
  },
  type: {
    type: String,
    enum: ['student', 'staff', 'contractor', 'resident', 'intern', 'other'],
    default: 'other'
  },
  department: {
    type: String
  },
  phoneNumber: {
    type: String
  },
  location: {
    type: String
  },
  bio: {
    type: String
  },
  skills: {
    type: [String],
    default: []
  },
  tags: {
    type: [String],
    default: []
  },
  teams: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Team'
  }],
  groups: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group'
  }],
  // Store information about user's provisioned accounts in integrations
  provisionedAccounts: {
    type: Map,
    of: {
      accountId: String,
      provisionedAt: Date,
      deprovisionedAt: Date,
      status: {
        type: String,
        enum: ['active', 'deprovisioned', 'error'],
        default: 'active'
      },
      error: String
    },
    default: new Map()
  },
  // Store user-specific integration configurations
  dreoConfig: {
    username: String,
    password: String,
    updatedAt: {
      type: Date,
      default: Date.now
    }
  },
  // Canva OAuth credentials
  canvaAccessToken: {
    type: String
  },
  canvaRefreshToken: {
    type: String
  },
  canvaTokenExpiry: {
    type: Date
  },
  // Notification preferences
  notificationPreferences: {
    emailEnabled: {
      type: Boolean,
      default: true
    },
    systemEnabled: {
      type: Boolean,
      default: true
    }
  }
});

module.exports = mongoose.model('User', UserSchema);
