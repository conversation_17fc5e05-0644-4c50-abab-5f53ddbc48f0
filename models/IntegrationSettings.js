const mongoose = require('mongoose');

const IntegrationSettingsSchema = new mongoose.Schema({
  integrationId: {
    type: String,
    required: true,
    unique: true
  },
  isRequired: {
    type: Boolean,
    default: false
  },
  isReadOnly: {
    type: Boolean,
    default: false
  },
  useGlobalConfig: {
    type: Boolean,
    default: false
  },
  // Provisioning settings
  supportsProvisioning: {
    type: Boolean,
    default: false
  },
  provisioningEnabled: {
    type: Boolean,
    default: false
  },
  provisioningConfig: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
IntegrationSettingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('IntegrationSettings', IntegrationSettingsSchema);