const mongoose = require('mongoose');

const LGThinqConfigSchema = new mongoose.Schema({
  patToken: {
    type: String,
    required: true
  },
  apiKey: {
    type: String,
    required: false // Deprecated, keeping for backward compatibility
  },
  clientId: {
    type: String,
    required: false
  },
  region: {
    type: String,
    enum: ['asia', 'pacific', 'america', 'europe', 'middle_east', 'africa'],
    default: 'america'
  },
  country: {
    type: String,
    default: 'US'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
LGThinqConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('lgThinqConfig', LGThinqConfigSchema);