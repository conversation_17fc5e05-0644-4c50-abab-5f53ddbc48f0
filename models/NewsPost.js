const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Comment subdocument schema for news posts
 */
const CommentSchema = new Schema({
  author: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  content: { type: String, required: true, trim: true },
  createdAt: { type: Date, default: Date.now }
}, { _id: true });

/**
 * News Post Schema
 * Schema for news posts with category, author, optional team, and comments
 */
const NewsPostSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  content: {
    type: String,
    required: true
  },
  summary: {
    type: String,
    trim: true
  },
  imageUrl: {
    type: String,
    trim: true
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'NewsCategory',
    required: true
  },
  team: {
    type: Schema.Types.ObjectId,
    ref: 'Team',
    default: null // null means global/all teams
  },
  author: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  published: {
    type: Boolean,
    default: false
  },
  publishedAt: {
    type: Date
  },
  featured: {
    type: Boolean,
    default: false
  },
  allowComments: {
    type: Boolean,
    default: false
  },
  comments: [CommentSchema],
  tags: [{
    type: String,
    trim: true
  }],
  viewCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Pre-save middleware to set publishedAt date when published is set to true
NewsPostSchema.pre('save', function(next) {
  if (this.published && !this.publishedAt) {
    this.publishedAt = Date.now();
  }
  next();
});

// Create indexes for faster queries
NewsPostSchema.index({ category: 1 });
NewsPostSchema.index({ team: 1 });
NewsPostSchema.index({ author: 1 });
NewsPostSchema.index({ published: 1 });
NewsPostSchema.index({ publishedAt: -1 });
NewsPostSchema.index({ featured: 1 });
NewsPostSchema.index({ 'tags': 1 });
NewsPostSchema.index({ createdAt: -1 });

module.exports = mongoose.model('NewsPost', NewsPostSchema);