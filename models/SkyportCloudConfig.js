const mongoose = require('mongoose');

/**
 * SkyportCloud Configuration Schema
 * Stores the authentication credentials and other settings for the SkyportCloud integration
 */
const SkyportCloudConfigSchema = new mongoose.Schema({
  // API key authentication
  apiKey: {
    type: String,
    trim: true
  },
  // Username/password authentication
  username: {
    type: String,
    trim: true
  },
  password: {
    type: String,
    trim: true
  },
  // Other settings
  baseUrl: {
    type: String,
    default: 'https://api.skyportcloud.com',
    trim: true
  },
  defaultDeviceId: {
    type: String,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
SkyportCloudConfigSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('SkyportCloudConfig', SkyportCloudConfigSchema);