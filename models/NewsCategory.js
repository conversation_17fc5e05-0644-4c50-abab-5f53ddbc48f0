const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * News Category Schema
 * Schema for news categories with permission settings
 */
const NewsCategorySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Roles that have access to this category
  // This is used for quick access checks without having to query the Role collection
  accessRoles: {
    type: [String],
    default: []
  }
}, {
  timestamps: true
});

// Create indexes for faster queries
NewsCategorySchema.index({ name: 1 });
NewsCategorySchema.index({ accessRoles: 1 });

module.exports = mongoose.model('NewsCategory', NewsCategorySchema);