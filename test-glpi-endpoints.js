// Test script for GLPI endpoints
const axios = require('axios');

// Set up axios instance with base URL
const api = axios.create({
  baseURL: 'http://localhost:6000/api',
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true
});

// Function to authenticate
async function authenticate() {
  try {
    // Replace with valid credentials for your test environment
    const response = await api.post('/auth/login', {
      email: '<EMAIL>',
      password: 'password'
    });
    
    console.log('Authentication successful');
    return response.data;
  } catch (error) {
    console.error('Authentication failed:', error.message);
    throw error;
  }
}

// Function to test the asset-types endpoint (known to be working)
async function testAssetTypes() {
  try {
    const response = await api.get('/glpi/asset-types');
    console.log('Asset types endpoint successful');
    console.log(`Retrieved ${response.data.length} asset types`);
    return response.data;
  } catch (error) {
    console.error('Asset types endpoint failed:', error.message);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

// Function to test the assets endpoint (previously failing)
async function testAssets() {
  try {
    const response = await api.get('/glpi/assets');
    console.log('Assets endpoint successful');
    console.log(`Retrieved ${response.data.length} assets`);
    return response.data;
  } catch (error) {
    console.error('Assets endpoint failed:', error.message);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

// Function to test the search endpoint (previously failing)
async function testSearch() {
  try {
    const response = await api.get('/glpi/search', {
      params: {
        is_deleted: 0,
        sort: 'name',
        order: 'ASC'
      }
    });
    console.log('Search endpoint successful');
    console.log(`Retrieved search results`);
    return response.data;
  } catch (error) {
    console.error('Search endpoint failed:', error.message);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

// Main function to run all tests
async function runTests() {
  try {
    // Authenticate first
    await authenticate();
    
    // Test the endpoints
    console.log('\n--- Testing asset-types endpoint ---');
    await testAssetTypes();
    
    console.log('\n--- Testing assets endpoint ---');
    await testAssets();
    
    console.log('\n--- Testing search endpoint ---');
    await testSearch();
    
    console.log('\n--- All tests completed successfully ---');
  } catch (error) {
    console.error('\n--- Test failed ---');
  }
}

// Run the tests
runTests();