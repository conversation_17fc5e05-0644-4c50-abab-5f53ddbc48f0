/**
 * Test script for UniFi Access controller with API key authentication
 * 
 * This script tests the UniFi Access controller by:
 * 1. Setting environment variables for UniFi Access including API key
 * 2. Mocking Express request and response objects
 * 3. Testing the getConfig method to ensure it properly checks for API key
 * 
 * Usage:
 * node test-unifi-access-controller.js
 */

// Import the controller
const unifiAccessController = require('./server/controllers/unifiAccessController');

// Create a test function
async function testUnifiAccessController() {
  console.log('Testing UniFi Access controller with API key authentication...');
  
  // Test 1: getConfig with all environment variables set
  console.log('\nTest 1: getConfig with all environment variables set');
  
  // Set environment variables
  process.env.UNIFI_ACCESS_HOST = 'test-host';
  process.env.UNIFI_ACCESS_API_KEY = 'test-api-key';
  process.env.UNIFI_ACCESS_PORT = '443';
  
  // Mock Express request and response objects
  const req = {};
  const res = {
    status: function(statusCode) {
      this.statusCode = statusCode;
      return this;
    },
    json: function(data) {
      this.data = data;
      return this;
    }
  };
  
  // Call the getConfig method
  await unifiAccessController.getConfig(req, res);
  
  // Check the response
  if (res.statusCode) {
    console.log(`❌ getConfig returned status code ${res.statusCode}`);
    console.log('Response data:', res.data);
  } else {
    console.log('✅ getConfig returned success response');
    console.log('Response data:', res.data);
    
    // Verify the response contains the expected data
    if (
      res.data.host === process.env.UNIFI_ACCESS_HOST &&
      res.data.port.toString() === process.env.UNIFI_ACCESS_PORT &&
      res.data.message === 'UniFi Access is configured using environment variables'
    ) {
      console.log('✅ Response data is correct');
    } else {
      console.log('❌ Response data is incorrect');
      console.log('Expected:');
      console.log(`  host: ${process.env.UNIFI_ACCESS_HOST}`);
      console.log(`  port: ${process.env.UNIFI_ACCESS_PORT}`);
      console.log(`  message: 'UniFi Access is configured using environment variables'`);
    }
  }
  
  // Test 2: getConfig with missing API key
  console.log('\nTest 2: getConfig with missing API key');
  
  // Set environment variables (missing API key)
  process.env.UNIFI_ACCESS_HOST = 'test-host';
  delete process.env.UNIFI_ACCESS_API_KEY;
  process.env.UNIFI_ACCESS_PORT = '443';
  
  // Reset mock response
  res.statusCode = null;
  res.data = null;
  
  // Call the getConfig method
  await unifiAccessController.getConfig(req, res);
  
  // Check the response
  if (res.statusCode === 404) {
    console.log('✅ getConfig returned 404 status code as expected');
    console.log('Response data:', res.data);
    
    // Verify the error message mentions API key
    if (res.data.message.includes('UNIFI_ACCESS_API_KEY')) {
      console.log('✅ Error message correctly mentions UNIFI_ACCESS_API_KEY');
    } else {
      console.log('❌ Error message does not mention UNIFI_ACCESS_API_KEY');
      console.log('Error message:', res.data.message);
    }
  } else {
    console.log(`❌ getConfig returned unexpected status code: ${res.statusCode || 'none'}`);
    console.log('Response data:', res.data);
  }
  
  // Test 3: getConfig with missing host
  console.log('\nTest 3: getConfig with missing host');
  
  // Set environment variables (missing host)
  delete process.env.UNIFI_ACCESS_HOST;
  process.env.UNIFI_ACCESS_API_KEY = 'test-api-key';
  process.env.UNIFI_ACCESS_PORT = '443';
  
  // Reset mock response
  res.statusCode = null;
  res.data = null;
  
  // Call the getConfig method
  await unifiAccessController.getConfig(req, res);
  
  // Check the response
  if (res.statusCode === 404) {
    console.log('✅ getConfig returned 404 status code as expected');
    console.log('Response data:', res.data);
    
    // Verify the error message mentions host
    if (res.data.message.includes('UNIFI_ACCESS_HOST')) {
      console.log('✅ Error message correctly mentions UNIFI_ACCESS_HOST');
    } else {
      console.log('❌ Error message does not mention UNIFI_ACCESS_HOST');
      console.log('Error message:', res.data.message);
    }
  } else {
    console.log(`❌ getConfig returned unexpected status code: ${res.statusCode || 'none'}`);
    console.log('Response data:', res.data);
  }
  
  console.log('\nTest completed.');
}

// Run the test
testUnifiAccessController();