// Test script for LG ThinQ power status extraction
console.log('Testing LG ThinQ power status extraction...');

// Sample response from the status endpoint as described in the issue
const sampleResponse = {
    "messageId": "ZjJlNjNmZGZlNzAwNDNiYj",
    "timestamp": "2025-08-05T02:36:12.057070",
    "response": {
        "runState": {
            "currentState": "NORMAL"
        },
        "airConJobMode": {
            "currentJobMode": "COOL"
        },
        "powerSave": {
            "powerSaveEnabled": false
        },
        "temperature": {
            "currentTemperature": 18.5,
            "targetTemperature": 18,
            "unit": "C"
        },
        "temperatureInUnits": [
            {
                "currentTemperature": 18.5,
                "targetTemperature": 18,
                "unit": "C"
            },
            {
                "currentTemperature": 65,
                "targetTemperature": 64,
                "unit": "F"
            }
        ],
        "filterInfo": {
            "filterLifetime": 284,
            "usedTime": 76
        },
        "airFlow": {
            "windStrength": "AUTO",
            "windStrengthDetail": "NATURE"
        },
        "windDirection": {
            "rotateLeftRight": false,
            "rotateUpDown": false
        },
        "operation": {
            "airConOperationMode": "POWER_ON"
        },
        "timer": {
            "absoluteStartTimer": "UNSET",
            "absoluteStopTimer": "UNSET"
        },
        "sleepTimer": {
            "relativeStopTimer": "UNSET"
        }
    }
};

// Function to simulate the processing logic in LGThinqPage.js
function processDeviceStatus(response) {
    // Extract the actual response data if it's nested
    const data = response.response || response;
    
    // Extract power status from operation.airConOperationMode field
    // Default to false if the operation field is missing or not "POWER_ON"
    const isPowerOn = data.operation && 
                    data.operation.airConOperationMode === "POWER_ON" ? true : false;
    
    // Extract temperature from the response
    let currentTemp = 22;
    let targetTemp = 22;
    if (data.temperature) {
        if (typeof data.temperature.currentTemperature === 'number') {
            currentTemp = data.temperature.currentTemperature;
        }
        if (typeof data.temperature.targetTemperature === 'number') {
            targetTemp = data.temperature.targetTemperature;
        }
    }
    
    // Extract other status information
    const processedStatus = {
        power: isPowerOn,
        currentTemperature: currentTemp,
        temperature: targetTemp,
        mode: data.airConJobMode?.currentJobMode?.toLowerCase() || 'cool',
        // Keep the raw response for reference
        _raw: data
    };
    
    return processedStatus;
}

// Test with POWER_ON
console.log('\nTesting with POWER_ON:');
const resultPowerOn = processDeviceStatus(sampleResponse);
console.log('Power status:', resultPowerOn.power);
console.log('Current temperature:', resultPowerOn.currentTemperature);
console.log('Target temperature:', resultPowerOn.temperature);
console.log('Mode:', resultPowerOn.mode);

// Test with POWER_OFF
console.log('\nTesting with POWER_OFF:');
const sampleResponseOff = JSON.parse(JSON.stringify(sampleResponse));
sampleResponseOff.response.operation.airConOperationMode = "POWER_OFF";
const resultPowerOff = processDeviceStatus(sampleResponseOff);
console.log('Power status:', resultPowerOff.power);

// Test with missing operation field
console.log('\nTesting with missing operation field:');
const sampleResponseNoOperation = JSON.parse(JSON.stringify(sampleResponse));
delete sampleResponseNoOperation.response.operation;
const resultNoOperation = processDeviceStatus(sampleResponseNoOperation);
console.log('Power status:', resultNoOperation.power);

console.log('\nAll tests completed!');