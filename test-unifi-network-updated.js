/**
 * Test script to verify the updated UniFi Network API implementation
 * 
 * This script tests the updated UniFi Network API wrapper to ensure
 * it correctly handles filtering and the new Hotspot Vouchers endpoints.
 * 
 * Usage: node test-unifi-network-updated.js
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API wrapper
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get environment variables
const host = process.env.UNIFI_NETWORK_HOST;
const apiKey = process.env.UNIFI_NETWORK_API_KEY;
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Check if required environment variables are set
if (!host || !apiKey) {
  console.error('Error: UniFi Network host and API key are required.');
  console.error('Please set UNIFI_NETWORK_HOST and UNIFI_NETWORK_API_KEY environment variables.');
  process.exit(1);
}

// Create an instance of the UniFi Network API wrapper
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

/**
 * Test the getDevices method with filtering
 */
async function testGetDevicesWithFilter() {
  try {
    console.log('Testing UniFi Network API getDevices method with filtering...');
    
    // Test without filter
    console.log('Fetching all devices...');
    const allDevices = await unifiNetworkAPI.getDevices();
    console.log(`Successfully fetched ${allDevices.length} devices`);
    
    // Test with filter (example: filter for online devices)
    console.log('Fetching online devices...');
    const onlineDevices = await unifiNetworkAPI.getDevices({
      filter: 'state.eq("CONNECTED")'
    });
    console.log(`Successfully fetched ${onlineDevices.length} online devices`);
    
    return true;
  } catch (error) {
    console.error('Error testing getDevices with filter:', error);
    return false;
  }
}

/**
 * Test the getClients method with filtering
 */
async function testGetClientsWithFilter() {
  try {
    console.log('Testing UniFi Network API getClients method with filtering...');
    
    // Test without filter
    console.log('Fetching all clients...');
    const allClients = await unifiNetworkAPI.getClients();
    console.log(`Successfully fetched ${allClients.length} clients`);
    
    // Test with filter (example: filter for wired clients)
    console.log('Fetching wired clients...');
    const wiredClients = await unifiNetworkAPI.getClients({
      filter: 'type.eq("wired")'
    });
    console.log(`Successfully fetched ${wiredClients.length} wired clients`);
    
    return true;
  } catch (error) {
    console.error('Error testing getClients with filter:', error);
    return false;
  }
}


/**
 * Run all tests
 */
async function runTests() {
  console.log('Starting UniFi Network API tests...');
  
  try {
    // Initialize the API
    console.log('Initializing UniFi Network API...');
    await unifiNetworkAPI.initialize();
    console.log('UniFi Network API initialized successfully');
    
    // Run tests
    const devicesTestResult = await testGetDevicesWithFilter();
    const clientsTestResult = await testGetClientsWithFilter();
    
    // Check results
    if (devicesTestResult && clientsTestResult) {
      console.log('All tests passed! The UniFi Network API implementation is working correctly.');
    } else {
      console.error('Some tests failed. Please check the error messages above.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the tests
runTests();