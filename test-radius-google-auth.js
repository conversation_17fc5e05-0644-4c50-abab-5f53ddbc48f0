/**
 * Test script to verify that the RADIUS integration is properly using the service account environment variables
 */
require('dotenv').config();
const GoogleWorkspaceAuth = require('./server/integrations/radius/googleWorkspaceAuth');

async function testRadiusGoogleAuth() {
  console.log('Testing RADIUS Google Workspace Authentication');
  console.log('---------------------------------------------');
  
  // Check if environment variables are set
  console.log('Environment Variables:');
  console.log('- GOOGLE_SERVICE_ACCOUNT_EMAIL:', process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL ? 'Set' : 'Not set');
  console.log('- GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY:', process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY ? 'Set (length: ' + process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY.length + ')' : 'Not set');
  console.log('- GOOGLE_ADMIN_IMPERSONATION_EMAIL:', process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL ? 'Set' : 'Not set');
  console.log('---------------------------------------------');
  
  try {
    // Create a new instance of GoogleWorkspaceAuth
    const googleWorkspaceAuth = new GoogleWorkspaceAuth();
    
    // Check if it's configured
    const isConfigured = googleWorkspaceAuth.isConfigured();
    console.log('Is configured:', isConfigured);
    
    if (!isConfigured) {
      console.error('Google Workspace authentication is not configured. Please check your environment variables.');
      return;
    }
    
    // Initialize the authentication
    console.log('Initializing Google Workspace authentication...');
    await googleWorkspaceAuth.initialize();
    
    // Check if it's authenticated
    const isAuthenticated = await googleWorkspaceAuth.isAuthenticated();
    console.log('Is authenticated:', isAuthenticated);
    
    if (!isAuthenticated) {
      console.error('Google Workspace authentication failed. Please check your environment variables and credentials.');
      return;
    }
    
    // Get configuration status
    const configStatus = googleWorkspaceAuth.getConfigStatus();
    console.log('Configuration status:', configStatus);
    
    // Try to get a user to verify that the authentication is working
    console.log('Fetching a test user...');
    const testUser = await googleWorkspaceAuth.getUser(process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL);
    
    if (testUser) {
      console.log('Successfully fetched user:', testUser.primaryEmail);
      console.log('Authentication is working correctly!');
    } else {
      console.log('Failed to fetch user. Authentication might not be working correctly.');
    }
    
  } catch (error) {
    console.error('Error testing RADIUS Google Workspace authentication:', error);
  }
}

// Run the test
testRadiusGoogleAuth().catch(console.error);