/**
 * <PERSON><PERSON><PERSON> to add BMS (Building Management System) permissions to existing roles
 * Run this script to update roles with the new BMS permissions
 */

const mongoose = require('mongoose');
const Role = require('../models/Role');
require('dotenv').config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost/csfportal', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB Connected');
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    process.exit(1);
  }
};

const addBMSPermissions = async () => {
  try {
    // Connect to database
    await connectDB();

    // Define BMS permissions
    const bmsPermissions = [
      'bms:read',
      'bms:write',
      'bms:admin',
      'electrical:read',
      'electrical:write',
      'electrical:admin',
      'safety:read',
      'safety:write',
      'safety:admin',
      'hvac:read',
      'hvac:write',
      'hvac:admin',
      'wifi:read',
      'wifi:write',
      'wifi:admin',
      'utilities:read',
      'utilities:write',
      'utilities:admin',
      'events:read',
      'events:write',
      'events:admin',
      'av:read',
      'av:write',
      'av:admin',
      'checklists:read',
      'checklists:write',
      'checklists:admin',
      'wayfinding:read',
      'wayfinding:write',
      'wayfinding:admin'
    ];

    // Update admin role (if it has * permission, it already has all permissions)
    const adminRole = await Role.findOne({ name: 'admin' });
    if (adminRole && !adminRole.permissions.includes('*')) {
      adminRole.permissions.push(...bmsPermissions);
      await adminRole.save();
      console.log('Updated admin role with BMS permissions');
    } else if (adminRole) {
      console.log('Admin role already has all permissions (*)');
    }

    // Update user role with basic BMS permissions
    const userRole = await Role.findOne({ name: 'user' });
    if (userRole) {
      // Add basic BMS permissions for regular users
      const basicBMSPermissions = [
        'bms:read',
        'electrical:read',
        'safety:read',
        'hvac:read',
        'wifi:read',
        'utilities:read',
        'events:read',
        'av:read',
        'checklists:read',
        'wayfinding:read'
      ];
      
      // Check if permissions already exist
      const needsUpdate = basicBMSPermissions.some(perm => !userRole.permissions.includes(perm));
      
      if (needsUpdate) {
        userRole.permissions.push(...basicBMSPermissions.filter(perm => !userRole.permissions.includes(perm)));
        await userRole.save();
        console.log('Updated user role with basic BMS permissions');
      } else {
        console.log('User role already has basic BMS permissions');
      }
    }

    // Update facility manager or similar role with enhanced permissions
    const facilityRole = await Role.findOne({ name: 'facility_manager' });
    if (facilityRole) {
      const facilityBMSPermissions = [
        'bms:read',
        'bms:write',
        'electrical:read',
        'electrical:write',
        'safety:read',
        'safety:write',
        'hvac:read',
        'hvac:write',
        'wifi:read',
        'utilities:read',
        'utilities:write',
        'events:read',
        'events:write',
        'av:read',
        'av:write',
        'checklists:read',
        'checklists:write',
        'wayfinding:read'
      ];
      
      const needsUpdate = facilityBMSPermissions.some(perm => !facilityRole.permissions.includes(perm));
      
      if (needsUpdate) {
        facilityRole.permissions.push(...facilityBMSPermissions.filter(perm => !facilityRole.permissions.includes(perm)));
        await facilityRole.save();
        console.log('Updated facility_manager role with enhanced BMS permissions');
      }
    } else {
      // Create facility_manager role if it doesn't exist
      const newFacilityRole = new Role({
        name: 'facility_manager',
        description: 'Manages building facilities and systems',
        permissions: [
          'bms:read',
          'bms:write',
          'electrical:read',
          'electrical:write',
          'safety:read',
          'safety:write',
          'hvac:read',
          'hvac:write',
          'wifi:read',
          'utilities:read',
          'utilities:write',
          'events:read',
          'events:write',
          'av:read',
          'av:write',
          'checklists:read',
          'checklists:write',
          'wayfinding:read',
          'buildingManagement:read',
          'buildingManagement:write'
        ]
      });
      
      await newFacilityRole.save();
      console.log('Created facility_manager role with BMS permissions');
    }

    // Update any other custom roles to include basic BMS permissions
    const allRoles = await Role.find({ name: { $nin: ['admin', 'user', 'facility_manager'] } });
    
    for (const role of allRoles) {
      // Skip if role has * permission
      if (role.permissions.includes('*')) continue;
      
      // Add basic BMS read permissions
      const basicPermissions = ['bms:read', 'electrical:read', 'safety:read', 'hvac:read'];
      const needsUpdate = basicPermissions.some(perm => !role.permissions.includes(perm));
      
      if (needsUpdate) {
        role.permissions.push(...basicPermissions.filter(perm => !role.permissions.includes(perm)));
        await role.save();
        console.log(`Updated ${role.name} role with basic BMS permissions`);
      }
    }

    console.log('BMS permissions have been added to all roles');
    
    // Create default BMS permissions documentation
    console.log('\nBMS Permissions:');
    console.log('- bms:read - View BMS dashboard and system status');
    console.log('- bms:write - Operate BMS controls and systems');
    console.log('- bms:admin - Manage BMS configuration and settings');
    console.log('- electrical:read - View electrical system information');
    console.log('- electrical:write - Operate electrical controls');
    console.log('- safety:read - View safety assets and information');
    console.log('- safety:write - Manage safety assets and equipment');
    console.log('- hvac:read - View HVAC system status');
    console.log('- hvac:write - Control HVAC systems');
    console.log('- wifi:read - View WiFi network status');
    console.log('- wifi:write - Manage WiFi networks');
    console.log('- utilities:read - View utility shutoff status');
    console.log('- utilities:write - Control utility shutoffs');
    console.log('- events:read - View church events management');
    console.log('- events:write - Manage church events');
    console.log('- av:read - View AV equipment status');
    console.log('- av:write - Control AV equipment');
    console.log('- checklists:read - View maintenance checklists');
    console.log('- checklists:write - Manage maintenance checklists');
    console.log('- wayfinding:read - View digital wayfinding');
    console.log('- wayfinding:write - Manage wayfinding system');
    
    process.exit(0);
  } catch (error) {
    console.error('Error adding BMS permissions:', error);
    process.exit(1);
  }
};

// Run the script
addBMSPermissions();