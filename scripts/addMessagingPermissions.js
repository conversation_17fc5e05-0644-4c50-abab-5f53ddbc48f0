/**
 * <PERSON><PERSON><PERSON> to add messaging permissions to existing roles
 * Run this script to update roles with the new messaging permissions
 */

const mongoose = require('mongoose');
const Role = require('../models/Role');
require('dotenv').config();

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost/csfportal', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB Connected');
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    process.exit(1);
  }
};

const addMessagingPermissions = async () => {
  try {
    // Connect to database
    await connectDB();

    // Define messaging permissions
    const messagingPermissions = [
      'messages:read',
      'messages:create',
      'messages:delete'
    ];

    // Update admin role (if it has * permission, it already has all permissions)
    const adminRole = await Role.findOne({ name: 'admin' });
    if (adminRole && !adminRole.permissions.includes('*')) {
      adminRole.permissions.push(...messagingPermissions);
      await adminRole.save();
      console.log('Updated admin role with messaging permissions');
    } else if (adminRole) {
      console.log('Admin role already has all permissions (*)');
    }

    // Update user role
    const userRole = await Role.findOne({ name: 'user' });
    if (userRole) {
      // Add basic messaging permissions for regular users
      const userMessagingPermissions = ['messages:read', 'messages:create'];
      
      // Check if permissions already exist
      const needsUpdate = userMessagingPermissions.some(perm => !userRole.permissions.includes(perm));
      
      if (needsUpdate) {
        userRole.permissions.push(...userMessagingPermissions.filter(perm => !userRole.permissions.includes(perm)));
        await userRole.save();
        console.log('Updated user role with messaging permissions');
      } else {
        console.log('User role already has messaging permissions');
      }
    }

    // Update any other custom roles to include messaging permissions
    const allRoles = await Role.find({ name: { $nin: ['admin', 'user'] } });
    
    for (const role of allRoles) {
      // Skip if role has * permission
      if (role.permissions.includes('*')) continue;
      
      // Add basic messaging permissions
      const basicPermissions = ['messages:read', 'messages:create'];
      const needsUpdate = basicPermissions.some(perm => !role.permissions.includes(perm));
      
      if (needsUpdate) {
        role.permissions.push(...basicPermissions.filter(perm => !role.permissions.includes(perm)));
        await role.save();
        console.log(`Updated ${role.name} role with messaging permissions`);
      }
    }

    console.log('Messaging permissions have been added to all roles');
    
    // Create default messaging permissions documentation
    console.log('\nMessaging Permissions:');
    console.log('- messages:read - View conversations and messages');
    console.log('- messages:create - Send messages and create conversations');
    console.log('- messages:delete - Delete own messages');
    
    process.exit(0);
  } catch (error) {
    console.error('Error adding messaging permissions:', error);
    process.exit(1);
  }
};

// Run the script
addMessagingPermissions();