#!/usr/bin/env node

/**
 * Manual test script for WebSocket implementation
 * This script tests the WebSocket server and real-time functionality
 * 
 * Usage: node test-websocket-implementation.js [port]
 * Example: node test-websocket-implementation.js 6000
 */

const WebSocket = require('ws');
const axios = require('axios');

// Configuration
const PORT = process.argv[2] || 6000;
const BASE_URL = `http://localhost:${PORT}`;
const WS_URL = `ws://localhost:${PORT}/ws`;

class WebSocketTester {
  constructor() {
    this.results = [];
    this.clients = [];
  }

  /**
   * Log test result
   */
  logResult(testName, status, details = {}) {
    const result = {
      test: testName,
      status,
      timestamp: new Date().toISOString(),
      details
    };
    
    this.results.push(result);
    
    const statusIcon = status === 'PASS' ? '✅' : '❌';
    console.log(`${statusIcon} ${testName}: ${status}`);
    
    if (details.error) {
      console.log(`   Error: ${details.error}`);
    }
    if (details.duration) {
      console.log(`   Duration: ${details.duration}ms`);
    }
  }

  /**
   * Test basic WebSocket connection
   */
  async testBasicConnection() {
    console.log('\n🔗 Testing Basic WebSocket Connection...');
    
    return new Promise((resolve) => {
      const startTime = Date.now();
      const client = new WebSocket(WS_URL);
      
      const timeout = setTimeout(() => {
        this.logResult('Basic Connection', 'FAIL', {
          error: 'Connection timeout after 5 seconds'
        });
        client.close();
        resolve(false);
      }, 5000);
      
      client.on('open', () => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        this.logResult('Basic Connection', 'PASS', { duration });
        client.close();
        resolve(true);
      });
      
      client.on('error', (error) => {
        clearTimeout(timeout);
        this.logResult('Basic Connection', 'FAIL', {
          error: error.message
        });
        resolve(false);
      });
    });
  }

  /**
   * Test welcome message
   */
  async testWelcomeMessage() {
    console.log('\n👋 Testing Welcome Message...');
    
    return new Promise((resolve) => {
      const client = new WebSocket(WS_URL);
      
      const timeout = setTimeout(() => {
        this.logResult('Welcome Message', 'FAIL', {
          error: 'No welcome message received within 3 seconds'
        });
        client.close();
        resolve(false);
      }, 3000);
      
      client.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          
          if (message.type === 'welcome') {
            clearTimeout(timeout);
            this.logResult('Welcome Message', 'PASS', {
              clientId: message.clientId,
              timestamp: message.timestamp
            });
            client.close();
            resolve(true);
          }
        } catch (error) {
          clearTimeout(timeout);
          this.logResult('Welcome Message', 'FAIL', {
            error: 'Invalid JSON in welcome message'
          });
          client.close();
          resolve(false);
        }
      });
      
      client.on('error', (error) => {
        clearTimeout(timeout);
        this.logResult('Welcome Message', 'FAIL', {
          error: error.message
        });
        resolve(false);
      });
    });
  }

  /**
   * Test subscription functionality
   */
  async testSubscription() {
    console.log('\n📡 Testing Subscription Functionality...');
    
    return new Promise((resolve) => {
      const client = new WebSocket(WS_URL);
      let subscriptionConfirmed = false;
      
      const timeout = setTimeout(() => {
        this.logResult('Subscription', 'FAIL', {
          error: 'Subscription not confirmed within 3 seconds'
        });
        client.close();
        resolve(false);
      }, 3000);
      
      client.on('open', () => {
        // Subscribe to activity-log events
        client.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
      });
      
      client.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          
          if (message.type === 'subscribed' && message.eventType === 'activity-log') {
            clearTimeout(timeout);
            subscriptionConfirmed = true;
            this.logResult('Subscription', 'PASS', {
              eventType: message.eventType
            });
            client.close();
            resolve(true);
          }
        } catch (error) {
          clearTimeout(timeout);
          this.logResult('Subscription', 'FAIL', {
            error: 'Invalid JSON in subscription response'
          });
          client.close();
          resolve(false);
        }
      });
      
      client.on('error', (error) => {
        clearTimeout(timeout);
        this.logResult('Subscription', 'FAIL', {
          error: error.message
        });
        resolve(false);
      });
    });
  }

  /**
   * Test ping/pong functionality
   */
  async testPingPong() {
    console.log('\n🏓 Testing Ping/Pong Functionality...');
    
    return new Promise((resolve) => {
      const client = new WebSocket(WS_URL);
      
      const timeout = setTimeout(() => {
        this.logResult('Ping/Pong', 'FAIL', {
          error: 'No pong response within 3 seconds'
        });
        client.close();
        resolve(false);
      }, 3000);
      
      client.on('open', () => {
        // Send ping
        client.send(JSON.stringify({ type: 'ping' }));
      });
      
      client.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          
          if (message.type === 'pong') {
            clearTimeout(timeout);
            this.logResult('Ping/Pong', 'PASS');
            client.close();
            resolve(true);
          }
        } catch (error) {
          // Ignore non-pong messages
        }
      });
      
      client.on('error', (error) => {
        clearTimeout(timeout);
        this.logResult('Ping/Pong', 'FAIL', {
          error: error.message
        });
        resolve(false);
      });
    });
  }

  /**
   * Test multiple client connections
   */
  async testMultipleClients() {
    console.log('\n👥 Testing Multiple Client Connections...');
    
    return new Promise((resolve) => {
      const clients = [];
      let connectedClients = 0;
      const targetClients = 3;
      
      const timeout = setTimeout(() => {
        this.logResult('Multiple Clients', 'FAIL', {
          error: `Only ${connectedClients}/${targetClients} clients connected`
        });
        clients.forEach(client => client.close());
        resolve(false);
      }, 5000);
      
      for (let i = 0; i < targetClients; i++) {
        const client = new WebSocket(WS_URL);
        clients.push(client);
        
        client.on('open', () => {
          connectedClients++;
          
          if (connectedClients === targetClients) {
            clearTimeout(timeout);
            this.logResult('Multiple Clients', 'PASS', {
              connectedClients: targetClients
            });
            clients.forEach(client => client.close());
            resolve(true);
          }
        });
        
        client.on('error', (error) => {
          clearTimeout(timeout);
          this.logResult('Multiple Clients', 'FAIL', {
            error: error.message
          });
          clients.forEach(client => client.close());
          resolve(false);
        });
      }
    });
  }

  /**
   * Test server health endpoint
   */
  async testServerHealth() {
    console.log('\n🏥 Testing Server Health...');
    
    try {
      const startTime = Date.now();
      const response = await axios.get(`${BASE_URL}/health`, {
        timeout: 5000
      });
      const duration = Date.now() - startTime;
      
      if (response.status === 200) {
        this.logResult('Server Health', 'PASS', {
          duration,
          status: response.status
        });
        return true;
      } else {
        this.logResult('Server Health', 'FAIL', {
          status: response.status
        });
        return false;
      }
    } catch (error) {
      this.logResult('Server Health', 'FAIL', {
        error: error.message
      });
      return false;
    }
  }

  /**
   * Test error handling
   */
  async testErrorHandling() {
    console.log('\n🚨 Testing Error Handling...');
    
    return new Promise((resolve) => {
      const client = new WebSocket(WS_URL);
      
      const timeout = setTimeout(() => {
        this.logResult('Error Handling', 'FAIL', {
          error: 'No error response received within 3 seconds'
        });
        client.close();
        resolve(false);
      }, 3000);
      
      client.on('open', () => {
        // Send invalid JSON
        client.send('invalid json message');
      });
      
      client.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          
          if (message.type === 'error' && message.message === 'Invalid JSON message') {
            clearTimeout(timeout);
            this.logResult('Error Handling', 'PASS');
            client.close();
            resolve(true);
          }
        } catch (error) {
          // Ignore invalid messages
        }
      });
      
      client.on('error', (error) => {
        clearTimeout(timeout);
        this.logResult('Error Handling', 'FAIL', {
          error: error.message
        });
        resolve(false);
      });
    });
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🚀 Starting WebSocket Implementation Test Suite');
    console.log(`📍 Testing against: ${WS_URL}`);
    console.log('=' * 60);
    
    let passCount = 0;
    let totalTests = 0;
    
    const tests = [
      { name: 'Basic Connection', fn: () => this.testBasicConnection() },
      { name: 'Welcome Message', fn: () => this.testWelcomeMessage() },
      { name: 'Subscription', fn: () => this.testSubscription() },
      { name: 'Ping/Pong', fn: () => this.testPingPong() },
      { name: 'Multiple Clients', fn: () => this.testMultipleClients() },
      { name: 'Server Health', fn: () => this.testServerHealth() },
      { name: 'Error Handling', fn: () => this.testErrorHandling() }
    ];
    
    for (const test of tests) {
      totalTests++;
      try {
        const result = await test.fn();
        if (result) passCount++;
      } catch (error) {
        this.logResult(test.name, 'FAIL', { error: error.message });
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Print summary
    console.log('\n' + '=' * 60);
    console.log('📊 Test Summary');
    console.log('=' * 60);
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passCount}`);
    console.log(`Failed: ${totalTests - passCount}`);
    console.log(`Success Rate: ${((passCount / totalTests) * 100).toFixed(1)}%`);
    
    if (passCount === totalTests) {
      console.log('\n🎉 All tests passed! WebSocket implementation is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check the implementation.');
    }
    
    return passCount === totalTests;
  }
}

// Run the tests
if (require.main === module) {
  const tester = new WebSocketTester();
  tester.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = WebSocketTester;
