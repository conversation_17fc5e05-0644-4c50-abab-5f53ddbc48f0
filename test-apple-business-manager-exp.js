// Test script to verify the "exp" claim in the JWT token for Apple Business Manager API
const AppleBusinessManagerAPI = require('./server/integrations/appleBusinessManager/appleBusinessManagerAPI');

// Override the _generateJWT method to inspect the payload
const originalPrototype = AppleBusinessManagerAPI.prototype;
const originalGenerateJWT = originalPrototype._generateJWT;

// Create a wrapper function to inspect the payload
originalPrototype._generateJWT = function() {
  // Get the current time in seconds
  const now = Math.floor(Date.now() / 1000);
  
  // Log the tokenExpiry value and its type
  console.log('tokenExpiry value:', this.tokenExpiry);
  console.log('tokenExpiry type:', typeof this.tokenExpiry);
  
  // Calculate what the expiration time should be
  const expectedExp = now + this.tokenExpiry;
  console.log('Expected exp value:', expectedExp);
  
  // Call the original method (which will use this.tokenExpiry)
  try {
    const token = originalGenerateJWT.call(this);
    console.log('JWT token generated successfully');
    return token;
  } catch (error) {
    console.error('Error in original _generateJWT:', error.message);
    throw error;
  }
};

// Test with string token expiry (simulating environment variable)
console.log('=== Testing with string token expiry ===');
const stringTokenExpiry = '1200'; // This is how it would come from process.env
const api = new AppleBusinessManagerAPI(
  'test-client-id',
  'test-client-secret',
  'test-org-id',
  'test-key-id',
*********************************************************************************************************************************************************************************************************************************************************
  'test-issuer-id',
  stringTokenExpiry
);

// Generate JWT token
try {
  api._generateJWT();
  console.log('✅ Success: JWT token generated with string tokenExpiry');
} catch (error) {
  console.error('Error generating JWT token:', error);
  console.log('❌ Failure: Could not generate JWT token with string tokenExpiry');
}

// Test with default token expiry
console.log('\n=== Testing with default token expiry ===');
const apiWithDefault = new AppleBusinessManagerAPI(
  'test-client-id',
  'test-client-secret',
  'test-org-id',
  'test-key-id',
*********************************************************************************************************************************************************************************************************************************************************
  'test-issuer-id'
);

try {
  apiWithDefault._generateJWT();
  console.log('✅ Success: JWT token generated with default tokenExpiry');
} catch (error) {
  console.error('Error generating JWT token:', error);
  console.log('❌ Failure: Could not generate JWT token with default tokenExpiry');
}

console.log('\nTest completed.');