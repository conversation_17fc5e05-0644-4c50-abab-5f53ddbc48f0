/**
 * Test script to verify the WiiM playlist fetching functionality
 * 
 * This script tests the getPlaylists and getSpotifyPlaylists methods
 * to ensure they work correctly with the timeout and retry logic.
 * 
 * Usage: node test-wiim-playlists.js [host] [port]
 * Example: node test-wiim-playlists.js ************* 80
 */

// Import required modules
const WiimAPI = require('./server/integrations/wiim/wiimAPI');
require('dotenv').config(); // Load environment variables from .env file

// Get host and port from command line arguments or use defaults
const host = process.argv[2] || process.env.WIIM_HOST || '*************';
const port = parseInt(process.argv[3] || process.env.WIIM_PORT || 80, 10);

console.log(`Testing WiiM API with host: ${host}, port: ${port}`);

// Create WiiM API instance
const wiimAPI = new WiimAPI(host, port);

// Test getPlaylists method
async function testGetPlaylists() {
  console.log('\n=== Testing getPlaylists method ===');
  try {
    console.log('Fetching playlists...');
    const playlists = await wiimAPI.getPlaylists();
    console.log('Successfully fetched playlists:');
    console.log(JSON.stringify(playlists, null, 2));
    return true;
  } catch (error) {
    console.error('Error fetching playlists:');
    console.error(`- Message: ${error.message}`);
    console.error(`- Is WiiM Error: ${error.isWiimError || false}`);
    
    if (error.originalError) {
      console.error(`- Original Error: ${error.originalError.message}`);
      console.error(`- Code: ${error.originalError.code || 'N/A'}`);
    }
    
    return false;
  }
}

// Test getSpotifyPlaylists method
async function testGetSpotifyPlaylists() {
  console.log('\n=== Testing getSpotifyPlaylists method ===');
  
  // Check if Spotify credentials are available
  if (!process.env.SPOTIFY_CLIENT_ID || !process.env.SPOTIFY_CLIENT_SECRET || !process.env.SPOTIFY_REFRESH_TOKEN) {
    console.warn('Spotify credentials are missing. Skipping getSpotifyPlaylists test.');
    console.warn('Set SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET, and SPOTIFY_REFRESH_TOKEN in your .env file to test this method.');
    return false;
  }
  
  try {
    console.log('Fetching Spotify playlists...');
    const playlists = await wiimAPI.getSpotifyPlaylists();
    console.log('Successfully fetched Spotify playlists:');
    console.log(`Total playlists: ${playlists.playlists ? playlists.playlists.length : 0}`);
    
    // Print first 3 playlists for brevity
    if (playlists.playlists && playlists.playlists.length > 0) {
      console.log('First 3 playlists:');
      for (let i = 0; i < Math.min(3, playlists.playlists.length); i++) {
        const playlist = playlists.playlists[i];
        console.log(`${i+1}. ${playlist.name} (${playlist.id}) - ${playlist.trackCount} tracks`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error fetching Spotify playlists:');
    console.error(`- Message: ${error.message}`);
    console.error(`- Is WiiM Error: ${error.isWiimError || false}`);
    console.error(`- Is Spotify Error: ${error.isSpotifyError || false}`);
    
    if (error.originalError) {
      console.error(`- Original Error: ${error.originalError.message}`);
      console.error(`- Code: ${error.originalError.code || 'N/A'}`);
    }
    
    return false;
  }
}

// Test device info to verify connection
async function testDeviceInfo() {
  console.log('\n=== Testing getDeviceInfo method ===');
  try {
    console.log('Fetching device info...');
    const deviceInfo = await wiimAPI.getDeviceInfo();
    console.log('Successfully fetched device info:');
    console.log(JSON.stringify(deviceInfo, null, 2));
    return true;
  } catch (error) {
    console.error('Error fetching device info:');
    console.error(`- Message: ${error.message}`);
    
    if (error.originalError) {
      console.error(`- Original Error: ${error.originalError.message}`);
      console.error(`- Code: ${error.originalError.code || 'N/A'}`);
    }
    
    console.error('\nThis suggests the WiiM device is not reachable at the specified host and port.');
    console.error('Please check your network connection and the WiiM device status.');
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting WiiM API tests...');
  
  // First test device info to verify connection
  const deviceInfoSuccess = await testDeviceInfo();
  
  if (!deviceInfoSuccess) {
    console.error('\nDevice connection test failed. Skipping playlist tests.');
    process.exit(1);
  }
  
  // Test getPlaylists
  const playlistsSuccess = await testGetPlaylists();
  
  // Test getSpotifyPlaylists
  const spotifyPlaylistsSuccess = await testGetSpotifyPlaylists();
  
  // Print summary
  console.log('\n=== Test Summary ===');
  console.log(`Device Info Test: ${deviceInfoSuccess ? 'PASSED' : 'FAILED'}`);
  console.log(`Playlists Test: ${playlistsSuccess ? 'PASSED' : 'FAILED'}`);
  console.log(`Spotify Playlists Test: ${spotifyPlaylistsSuccess === false ? 'FAILED' : (spotifyPlaylistsSuccess === true ? 'PASSED' : 'SKIPPED')}`);
  
  // Exit with appropriate code
  if (deviceInfoSuccess && (playlistsSuccess || spotifyPlaylistsSuccess)) {
    console.log('\nTests completed successfully!');
    process.exit(0);
  } else {
    console.error('\nSome tests failed. Please check the logs above for details.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unexpected error running tests:', error);
  process.exit(1);
});