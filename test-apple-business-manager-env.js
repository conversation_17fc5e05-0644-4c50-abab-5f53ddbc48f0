/**
 * Test script to verify that Apple Business Manager is properly using environment variables
 * 
 * This script tests:
 * 1. The controller's ability to read environment variables
 * 2. The API's initialization with environment variables
 * 3. The error handling when environment variables are not set
 */

const AppleBusinessManagerAPI = require('./server/integrations/appleBusinessManager/appleBusinessManagerAPI');
const appleBusinessManagerController = require('./server/controllers/appleBusinessManagerController');

// Mock Express request and response objects
const mockReq = {};
const mockRes = {
  status: function(code) {
    console.log(`Response status: ${code}`);
    return this;
  },
  json: function(data) {
    console.log('Response data:', data);
    return this;
  }
};

// Test function to verify environment variables
async function testEnvironmentVariables() {
  console.log('\n=== Testing Apple Business Manager Environment Variables ===\n');
  
  // Save original environment variables
  const originalEnv = {
    APPLE_BUSINESS_MANAGER_CLIENT_ID: process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID,
    APPLE_BUSINESS_MANAGER_CLIENT_SECRET: process.env.APPLE_BUSINESS_MANAGER_CLIENT_SECRET,
    APPLE_BUSINESS_MANAGER_ORGANIZATION_ID: process.env.APPLE_BUSINESS_MANAGER_ORGANIZATION_ID,
    APPLE_BUSINESS_MANAGER_KEY_ID: process.env.APPLE_BUSINESS_MANAGER_KEY_ID,
    APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH: process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH,
    APPLE_BUSINESS_MANAGER_ISSUER_ID: process.env.APPLE_BUSINESS_MANAGER_ISSUER_ID,
    APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY: process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY
  };
  
  // Test 1: When environment variables are not set
  console.log('Test 1: When environment variables are not set');
  
  // Clear environment variables
  process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID = '';
  process.env.APPLE_BUSINESS_MANAGER_CLIENT_SECRET = '';
  process.env.APPLE_BUSINESS_MANAGER_ORGANIZATION_ID = '';
  process.env.APPLE_BUSINESS_MANAGER_KEY_ID = '';
  process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH = '';
  process.env.APPLE_BUSINESS_MANAGER_ISSUER_ID = '';
  
  try {
    // Call the getConfig method
    await appleBusinessManagerController.getConfig(mockReq, mockRes);
    console.log('✓ Controller correctly handles missing environment variables\n');
  } catch (error) {
    console.error('✗ Error in controller when environment variables are not set:', error);
  }
  
  // Test 2: When environment variables are set
  console.log('Test 2: When environment variables are set');
  
  // Set test environment variables
  process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID = 'test-client-id';
  process.env.APPLE_BUSINESS_MANAGER_CLIENT_SECRET = 'test-client-secret';
  process.env.APPLE_BUSINESS_MANAGER_ORGANIZATION_ID = 'test-org-id';
  process.env.APPLE_BUSINESS_MANAGER_KEY_ID = 'test-key-id';
  process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH = '/path/to/test/key.p8';
  process.env.APPLE_BUSINESS_MANAGER_ISSUER_ID = 'test-issuer-id';
  process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY = '1800';
  
  try {
    // Call the getConfig method
    await appleBusinessManagerController.getConfig(mockReq, mockRes);
    console.log('✓ Controller correctly uses environment variables\n');
  } catch (error) {
    console.error('✗ Error in controller when environment variables are set:', error);
  }
  
  // Restore original environment variables
  process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID = originalEnv.APPLE_BUSINESS_MANAGER_CLIENT_ID;
  process.env.APPLE_BUSINESS_MANAGER_CLIENT_SECRET = originalEnv.APPLE_BUSINESS_MANAGER_CLIENT_SECRET;
  process.env.APPLE_BUSINESS_MANAGER_ORGANIZATION_ID = originalEnv.APPLE_BUSINESS_MANAGER_ORGANIZATION_ID;
  process.env.APPLE_BUSINESS_MANAGER_KEY_ID = originalEnv.APPLE_BUSINESS_MANAGER_KEY_ID;
  process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH = originalEnv.APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH;
  process.env.APPLE_BUSINESS_MANAGER_ISSUER_ID = originalEnv.APPLE_BUSINESS_MANAGER_ISSUER_ID;
  process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY = originalEnv.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY;
  
  console.log('Environment variables restored to original values');
  console.log('\n=== Test completed ===\n');
}

// Run the test
testEnvironmentVariables().catch(error => {
  console.error('Test failed with error:', error);
});