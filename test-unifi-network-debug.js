/**
 * Test script for UniFi Network debug output
 * 
 * This script tests the enhanced debug output for the UniFi Network integration
 * by making requests that will trigger various error conditions, including 404 errors.
 */

const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Test with invalid host to trigger connection errors
async function testInvalidHost() {
  console.log('\n=== Testing with invalid host ===');
  
  const invalidApi = new UnifiNetworkAPI('invalid-host.example.com', 'fake-api-key', 443, 'default');
  
  try {
    await invalidApi.initialize();
    console.log('Should not reach here');
  } catch (error) {
    console.log('Expected error caught from initialize with invalid host');
  }
  
  try {
    await invalidApi.getDevices();
    console.log('Should not reach here');
  } catch (error) {
    console.log('Expected error caught from getDevices with invalid host');
  }
}

// Test with valid host but invalid API key to trigger authentication errors
async function testInvalidApiKey() {
  console.log('\n=== Testing with invalid API key ===');
  
  // Use a potentially valid host but invalid API key
  // Replace with your actual UniFi controller host if available
  const host = process.env.UNIFI_NETWORK_HOST || 'unifi.example.com';
  const invalidApi = new UnifiNetworkAPI(host, 'invalid-api-key', 443, 'default');
  
  try {
    await invalidApi.initialize();
    console.log('Should not reach here');
  } catch (error) {
    console.log('Expected error caught from initialize with invalid API key');
  }
}

// Test with valid credentials but invalid device ID to trigger 404 errors
async function testInvalidDeviceId() {
  console.log('\n=== Testing with invalid device ID ===');
  
  // Use environment variables if available, otherwise use placeholders
  const host = process.env.UNIFI_NETWORK_HOST || 'unifi.example.com';
  const apiKey = process.env.UNIFI_NETWORK_API_KEY || 'fake-api-key';
  const api = new UnifiNetworkAPI(host, apiKey, 443, 'default');
  
  try {
    // Use a definitely invalid device ID
    await api.getDeviceDetails('00:00:00:00:00:00');
    console.log('Should not reach here');
  } catch (error) {
    console.log('Expected error caught from getDeviceDetails with invalid device ID');
  }
}

// Test with valid credentials but invalid client ID to trigger 404 errors
async function testInvalidClientId() {
  console.log('\n=== Testing with invalid client ID ===');
  
  // Use environment variables if available, otherwise use placeholders
  const host = process.env.UNIFI_NETWORK_HOST || 'unifi.example.com';
  const apiKey = process.env.UNIFI_NETWORK_API_KEY || 'fake-api-key';
  const api = new UnifiNetworkAPI(host, apiKey, 443, 'default');
  
  try {
    // Use a definitely invalid client ID
    await api.getClientDetails('00:00:00:00:00:00');
    console.log('Should not reach here');
  } catch (error) {
    console.log('Expected error caught from getClientDetails with invalid client ID');
  }
}

// Run all tests
async function runTests() {
  try {
    console.log('Starting UniFi Network debug output tests');
    
    await testInvalidHost();
    await testInvalidApiKey();
    await testInvalidDeviceId();
    await testInvalidClientId();
    
    console.log('\nAll tests completed. Check the debug output above for detailed error information.');
  } catch (error) {
    console.error('Unexpected error during tests:', error);
  }
}

// Run the tests
runTests();