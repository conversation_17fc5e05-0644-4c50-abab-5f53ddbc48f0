/**
 * Test script to verify the UniFi Network API fix
 * 
 * This script tests the updated UniFi Network API wrapper to ensure
 * it can successfully fetch devices, clients, and device statistics
 * from the UniFi Network Controller.
 * 
 * Usage: node test-unifi-network-fix.js
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API wrapper
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get configuration from environment variables
const host = process.env.UNIFI_NETWORK_HOST || '';
const apiKey = process.env.UNIFI_NETWORK_API_KEY || '';
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Check if required configuration is available
if (!host || !apiKey) {
  console.error('Error: UniFi Network host and API key are required.');
  console.error('Please set the UNIFI_NETWORK_HOST and UNIFI_NETWORK_API_KEY environment variables.');
  process.exit(1);
}

// Create an instance of the UniFi Network API wrapper
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Test function to verify the API wrapper can fetch devices
async function testGetDevices() {
  try {
    console.log('Testing UniFi Network API getDevices method...');
    const devices = await unifiNetworkAPI.getDevices();
    
    if (devices && Array.isArray(devices)) {
      console.log(`Success! Found ${devices.length} devices.`);
      console.log('First device:', devices[0] ? JSON.stringify(devices[0], null, 2) : 'No devices found');
      return true;
    } else {
      console.error('Error: Unexpected response format. Expected an array of devices.');
      console.error('Response:', devices);
      return false;
    }
  } catch (error) {
    console.error('Error testing getDevices method:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Test function to verify the API wrapper can fetch clients
async function testGetClients() {
  try {
    console.log('Testing UniFi Network API getClients method...');
    const clients = await unifiNetworkAPI.getClients();
    
    if (clients && Array.isArray(clients)) {
      console.log(`Success! Found ${clients.length} clients.`);
      console.log('First client:', clients[0] ? JSON.stringify(clients[0], null, 2) : 'No clients found');
      return true;
    } else {
      console.error('Error: Unexpected response format. Expected an array of clients.');
      console.error('Response:', clients);
      return false;
    }
  } catch (error) {
    console.error('Error testing getClients method:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Test function to verify the API wrapper can fetch device statistics
async function testGetDeviceStatistics() {
  try {
    console.log('Testing UniFi Network API getDeviceStatistics method...');
    
    // First, get a list of devices
    const devices = await unifiNetworkAPI.getDevices();
    
    if (!devices || !Array.isArray(devices) || devices.length === 0) {
      console.error('Error: No devices found to test statistics.');
      return false;
    }
    
    // Use the first device's ID to test statistics
    const deviceId = devices[0].mac;
    console.log(`Using device ID: ${deviceId} for statistics test`);
    
    const stats = await unifiNetworkAPI.getDeviceStatistics(deviceId);
    
    if (stats && typeof stats === 'object') {
      console.log('Success! Device statistics retrieved.');
      console.log('Device statistics:', JSON.stringify(stats, null, 2));
      return true;
    } else {
      console.error('Error: Unexpected response format. Expected an object with device statistics.');
      console.error('Response:', stats);
      return false;
    }
  } catch (error) {
    console.error('Error testing getDeviceStatistics method:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Run the tests
async function runTests() {
  console.log('Starting UniFi Network API tests...');
  console.log(`Using host: ${host}, API key: [HIDDEN], port: ${port}, site: ${site}`);
  
  try {
    // Initialize the API
    await unifiNetworkAPI.initialize();
    console.log('API initialized successfully.');
    
    // Test getDevices method
    const devicesResult = await testGetDevices();
    
    // Test getClients method
    const clientsResult = await testGetClients();
    
    // Test getDeviceStatistics method
    const statisticsResult = await testGetDeviceStatistics();
    
    // Report overall results
    if (devicesResult && clientsResult && statisticsResult) {
      console.log('All tests passed! The UniFi Network API fix is working correctly.');
    } else {
      console.log('Some tests failed. Please check the error messages above.');
      
      // Report which tests failed
      if (!devicesResult) console.log('- getDevices test failed');
      if (!clientsResult) console.log('- getClients test failed');
      if (!statisticsResult) console.log('- getDeviceStatistics test failed');
    }
  } catch (error) {
    console.error('Error running tests:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the tests
runTests();