/**
 * Test script for menu items management functionality
 * 
 * This script tests the menu items API endpoints to ensure they work correctly.
 * It performs the following operations:
 * 1. Get all menu items
 * 2. Create a new menu item
 * 3. Get the created menu item by ID
 * 4. Update the menu item
 * 5. Delete the menu item
 * 6. Initialize default menu items
 */

const axios = require('axios');
const dotenv = require('dotenv');

dotenv.config();

// Set base URL for API requests
const baseURL = process.env.API_URL || 'http://localhost:5000';
axios.defaults.baseURL = baseURL;

// Set auth token if available (required for admin operations)
const token = process.env.TEST_AUTH_TOKEN;
if (token) {
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
}

// Test menu item data
const testMenuItem = {
  title: 'Test Menu Item',
  originalTitle: 'Test Menu Item',
  friendlyName: 'Test Item',
  description: 'This is a test menu item created by the test script',
  path: '/test-menu-item',
  icon: 'science',
  categories: ['Test Category'],
  requiredRoles: ['user'],
  requiredPermission: 'test:read',
  isActive: true,
  order: 999,
  type: 'regular'
};

// Updated menu item data
const updatedMenuItem = {
  ...testMenuItem,
  title: 'Updated Test Menu Item',
  description: 'This menu item has been updated by the test script',
  icon: 'update'
};

// Store created menu item ID
let createdMenuItemId = null;

/**
 * Run all tests
 */
async function runTests() {
  try {
    console.log('Starting menu items management tests...');
    
    // Test 1: Get all menu items
    console.log('\n1. Testing getMenuItems endpoint...');
    const allMenuItems = await getMenuItems();
    console.log(`Retrieved ${allMenuItems.length} menu items`);
    
    // Test 2: Create a new menu item
    console.log('\n2. Testing createMenuItem endpoint...');
    const createdMenuItem = await createMenuItem(testMenuItem);
    createdMenuItemId = createdMenuItem._id;
    console.log(`Created menu item with ID: ${createdMenuItemId}`);
    
    // Test 3: Get the created menu item by ID
    console.log('\n3. Testing getMenuItemById endpoint...');
    const retrievedMenuItem = await getMenuItemById(createdMenuItemId);
    console.log(`Retrieved menu item: ${retrievedMenuItem.title}`);
    
    // Test 4: Update the menu item
    console.log('\n4. Testing updateMenuItem endpoint...');
    const updatedItem = await updateMenuItem(createdMenuItemId, updatedMenuItem);
    console.log(`Updated menu item: ${updatedItem.title}`);
    
    // Test 5: Delete the menu item
    console.log('\n5. Testing deleteMenuItem endpoint...');
    const deleteResult = await deleteMenuItem(createdMenuItemId);
    console.log(`Delete result: ${JSON.stringify(deleteResult)}`);
    
    // Test 6: Initialize default menu items
    console.log('\n6. Testing initializeDefaultMenuItems endpoint...');
    const defaultMenuItems = await initializeDefaultMenuItems();
    console.log(`Initialized ${defaultMenuItems.length} default menu items`);
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error during tests:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

/**
 * Get all menu items
 */
async function getMenuItems() {
  try {
    const response = await axios.get('/api/menu-items');
    return response.data;
  } catch (error) {
    console.error('Error getting menu items:', error.message);
    throw error;
  }
}

/**
 * Create a new menu item
 */
async function createMenuItem(menuItemData) {
  try {
    const response = await axios.post('/api/menu-items', menuItemData);
    return response.data;
  } catch (error) {
    console.error('Error creating menu item:', error.message);
    throw error;
  }
}

/**
 * Get a menu item by ID
 */
async function getMenuItemById(id) {
  try {
    const response = await axios.get(`/api/menu-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error getting menu item ${id}:`, error.message);
    throw error;
  }
}

/**
 * Update a menu item
 */
async function updateMenuItem(id, menuItemData) {
  try {
    const response = await axios.put(`/api/menu-items/${id}`, menuItemData);
    return response.data;
  } catch (error) {
    console.error(`Error updating menu item ${id}:`, error.message);
    throw error;
  }
}

/**
 * Delete a menu item
 */
async function deleteMenuItem(id) {
  try {
    const response = await axios.delete(`/api/menu-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting menu item ${id}:`, error.message);
    throw error;
  }
}

/**
 * Initialize default menu items
 */
async function initializeDefaultMenuItems() {
  try {
    const response = await axios.post('/api/menu-items/init');
    return response.data;
  } catch (error) {
    console.error('Error initializing default menu items:', error.message);
    throw error;
  }
}

// Run the tests
runTests();