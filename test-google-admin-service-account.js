/**
 * Test script to verify Google Admin API with service account credentials
 * 
 * This script tests if the Google Admin API is working correctly with the
 * service account credentials configured in the .env file.
 * 
 * Usage:
 * node test-google-admin-service-account.js
 */

// Load environment variables
require('dotenv').config();

// Import the GoogleAdminAPI class
const GoogleAdminAPI = require('./server/integrations/googleAdmin/googleAdminAPI');

// Log the environment variables being used (without showing the private key)
console.log('Environment variables:');
console.log('- GOOGLE_SERVICE_ACCOUNT_EMAIL:', process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL ? 'Set' : 'Not set');
console.log('- GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY:', process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY ? 'Set' : 'Not set');
console.log('- GOOGLE_ADMIN_IMPERSONATION_EMAIL:', process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL ? 'Set' : 'Not set');

// Create a new instance of the GoogleAdminAPI
const googleAdminAPI = new GoogleAdminAPI(
  '', // clientId (not needed for service account)
  '', // clientSecret (not needed for service account)
  '', // redirectUri (not needed for service account)
  '', // tokenPath (not needed for service account)
  null, // userTokens (not needed for service account)
  null, // userId (not needed for service account)
  process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL // impersonation email
);

// Main test function
async function testGoogleAdminAPI() {
  try {
    console.log('\nInitializing Google Admin API...');
    await googleAdminAPI.initialize();
    
    console.log('Checking if authenticated...');
    const isAuthenticated = googleAdminAPI.isAuthenticated();
    console.log('Is authenticated:', isAuthenticated);
    
    if (!isAuthenticated) {
      console.error('Error: Not authenticated. Please check your service account credentials.');
      return;
    }
    
    console.log('Using service account:', googleAdminAPI.usingServiceAccount);
    
    console.log('\nTesting API by listing users...');
    const users = await googleAdminAPI.listUsers({ maxResults: 10 });
    
    console.log(`Successfully retrieved ${users.length} users:`);
    users.forEach(user => {
      console.log(`- ${user.primaryEmail} (${user.name.fullName})`);
    });
    
    console.log('\nTest completed successfully! The Google Admin API is working correctly with service account credentials.');
  } catch (error) {
    console.error('Error testing Google Admin API:', error);
  }
}

// Run the test
testGoogleAdminAPI();