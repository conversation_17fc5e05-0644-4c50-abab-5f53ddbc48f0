// Test script to verify that the RainBirdAPI works correctly without a username
const RainBirdAPI = require('./server/integrations/rainBird/rainBirdAPI');

async function testRainBirdWithoutUsername() {
  try {
    console.log('Testing RainBirdAPI without a username...');
    
    // Create a RainBirdAPI instance without a username
    const rainBirdAPI = new RainBirdAPI(
      'test-host',  // host
      '',           // empty username
      'test-password',
      443,          // port
      true,         // localNetwork
      'rdz-rbcloud.rainbird.com', // cloudHost
      'test-mac'    // controllerMac
    );
    
    console.log('RainBirdAPI instance created successfully without a username.');
    console.log('RainBirdAPI instance:', {
      host: rainBirdAPI.host,
      username: rainBirdAPI.username,
      password: rainBirdAPI.password ? '********' : null,
      port: rainBirdAPI.port,
      localNetwork: rainBirdAPI.localNetwork,
      cloudHost: rainBirdAPI.cloudHost,
      controllerMac: rainBirdAPI.controllerMac
    });
    
    // We can't actually test API calls without real hardware,
    // but we can verify that the instance was created correctly
    
    console.log('Test completed successfully.');
  } catch (error) {
    console.error('Error testing RainBirdAPI without username:', error);
  }
}

// Run the test
testRainBirdWithoutUsername();