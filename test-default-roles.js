/**
 * Test script to demonstrate the default role assignment functionality
 * 
 * This script shows how the system assigns roles to new users based on:
 * 1. Default roles for local users
 * 2. Default roles for Google users
 * 3. Google Groups membership
 * 
 * To run this test:
 * 1. Set the environment variables in .env:
 *    - DEFAULT_ROLE_LOCAL_USERS=member (or any other role)
 *    - DEFAULT_ROLE_GOOGLE_USERS=member (or any other role)
 *    - GOOGLE_GROUPS_ROLE_MAPPING={"<EMAIL>":"admin","<EMAIL>":"finance"}
 * 2. Run: node test-default-roles.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

// Mock GoogleAdminAPI for testing
class MockGoogleAdminAPI {
  constructor() {
    this.initialized = false;
    this.groups = [
      { id: '1', email: '<EMAIL>', name: 'Ad<PERSON>' },
      { id: '2', email: '<EMAIL>', name: 'Finance' },
      { id: '3', email: '<EMAIL>', name: 'Staff' }
    ];
    
    // Mock user group memberships
    this.memberships = {
      '<EMAIL>': ['<EMAIL>'],
      '<EMAIL>': ['<EMAIL>'],
      '<EMAIL>': ['<EMAIL>'],
      '<EMAIL>': ['<EMAIL>', '<EMAIL>'],
      '<EMAIL>': []
    };
  }
  
  async initialize() {
    this.initialized = true;
    return true;
  }
  
  async listGroups() {
    return this.groups;
  }
  
  async isUserMemberOfGroup(userEmail, groupEmail) {
    const userGroups = this.memberships[userEmail] || [];
    return userGroups.includes(groupEmail);
  }
}

// Mock functions to simulate user creation
async function createLocalUser(name, email, providedRoles = null) {
  console.log(`Creating local user: ${name} (${email})`);
  
  // Get default role for local users from environment variable or use 'user' as fallback
  const defaultRole = process.env.DEFAULT_ROLE_LOCAL_USERS || 'user';
  
  const roles = providedRoles || [defaultRole];
  
  console.log(`Assigned roles: ${roles.join(', ')}`);
  return { name, email, roles };
}

async function createGoogleUser(profile) {
  console.log(`Creating Google user: ${profile.displayName} (${profile.email})`);
  
  // Get default role for Google users from environment variable or use 'user' as fallback
  const defaultRole = process.env.DEFAULT_ROLE_GOOGLE_USERS || 'user';
  let roles = [defaultRole];
  
  // Check if Google Groups role mapping is configured
  const googleGroupsRoleMapping = process.env.GOOGLE_GROUPS_ROLE_MAPPING ? 
    JSON.parse(process.env.GOOGLE_GROUPS_ROLE_MAPPING) : null;
  
  // If Google Groups role mapping is configured, check user's groups and assign roles
  if (googleGroupsRoleMapping) {
    try {
      // Create a mock API instance for checking groups
      const googleAdminAPI = new MockGoogleAdminAPI();
      
      // Initialize the API
      await googleAdminAPI.initialize();
      
      // Get user's email
      const userEmail = profile.email;
      
      // Get all groups
      const groups = await googleAdminAPI.listGroups();
      
      // For each group in the mapping
      for (const [groupEmail, role] of Object.entries(googleGroupsRoleMapping)) {
        // Find the group
        const group = groups.find(g => g.email.toLowerCase() === groupEmail.toLowerCase());
        
        if (group) {
          try {
            // Check if user is a member of the group
            const isMember = await googleAdminAPI.isUserMemberOfGroup(userEmail, group.email);
            
            if (isMember) {
              // Add the role if not already in the roles array
              if (!roles.includes(role)) {
                roles.push(role);
              }
            }
          } catch (groupCheckError) {
            console.error(`Error checking if user is member of group ${group.email}:`, groupCheckError);
          }
        }
      }
    } catch (error) {
      console.error('Error checking Google Groups for role assignment:', error);
      // Continue with default role if there's an error
    }
  }
  
  console.log(`Assigned roles: ${roles.join(', ')}`);
  return { name: profile.displayName, email: profile.email, roles };
}

async function runTests() {
  console.log('=== Testing Default Role Assignment ===\n');
  
  // Display current configuration
  console.log('Current configuration:');
  console.log(`- DEFAULT_ROLE_LOCAL_USERS: ${process.env.DEFAULT_ROLE_LOCAL_USERS || 'user (default)'}`);
  console.log(`- DEFAULT_ROLE_GOOGLE_USERS: ${process.env.DEFAULT_ROLE_GOOGLE_USERS || 'user (default)'}`);
  console.log(`- GOOGLE_GROUPS_ROLE_MAPPING: ${process.env.GOOGLE_GROUPS_ROLE_MAPPING || 'Not configured'}`);
  console.log();
  
  // Test local user creation
  console.log('=== Local User Tests ===');
  await createLocalUser('Local User', '<EMAIL>');
  await createLocalUser('Local User with Roles', '<EMAIL>', ['editor', 'viewer']);
  console.log();
  
  // Test Google user creation
  console.log('=== Google User Tests ===');
  await createGoogleUser({ 
    displayName: 'Regular Google User', 
    email: '<EMAIL>' 
  });
  
  await createGoogleUser({ 
    displayName: 'Admin Google User', 
    email: '<EMAIL>' 
  });
  
  await createGoogleUser({ 
    displayName: 'Finance Google User', 
    email: '<EMAIL>' 
  });
  
  await createGoogleUser({ 
    displayName: 'Admin and Finance Google User', 
    email: '<EMAIL>' 
  });
  
  console.log('\n=== Tests Completed ===');
}

// Run the tests
runTests()
  .then(() => {
    console.log('All tests completed successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error running tests:', err);
    process.exit(1);
  });