// Test script for Planning Center API
const PlanningCenterAPI = require('./server/integrations/planningCenter/planningCenterAPI');
require('dotenv').config();

// Initialize Planning Center API
const planningCenterAPI = new PlanningCenterAPI();

// Test function to fetch people
async function testGetPeople() {
  try {
    console.log('Testing getPeople() method...');
    const people = await planningCenterAPI.getPeople({ per_page: 5 });
    console.log(`Successfully fetched ${people.data.length} people`);
    console.log('First person:', JSON.stringify(people.data[0], null, 2));
    return true;
  } catch (error) {
    console.error('Error fetching people:', error.message);
    return false;
  }
}

// Test function to fetch a person by ID
async function testGetPersonById(personId) {
  try {
    console.log(`Testing getPersonById() method with ID ${personId}...`);
    const person = await planningCenterAPI.getPersonById(personId);
    console.log('Successfully fetched person:', JSON.stringify(person.data, null, 2));
    return true;
  } catch (error) {
    console.error(`Error fetching person with ID ${personId}:`, error.message);
    return false;
  }
}

// Main test function
async function runTests() {
  console.log('Initializing Planning Center API...');
  await planningCenterAPI.initialize();
  
  // Test getPeople()
  const peopleSuccess = await testGetPeople();
  
  // Test getPersonById() if getPeople() was successful
  if (peopleSuccess) {
    try {
      const people = await planningCenterAPI.getPeople({ per_page: 1 });
      if (people.data && people.data.length > 0) {
        const personId = people.data[0].id;
        await testGetPersonById(personId);
      } else {
        console.log('No people found to test getPersonById()');
      }
    } catch (error) {
      console.error('Error getting person ID for testing:', error.message);
    }
  }
  
  console.log('Tests completed');
}

// Run the tests
runTests().catch(error => {
  console.error('Test error:', error);
});