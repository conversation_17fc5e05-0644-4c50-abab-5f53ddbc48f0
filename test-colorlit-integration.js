/**
 * Test script for the Colorlit Z4 Pro LED Controller integration
 * 
 * This script tests the API endpoints for the Colorlit integration
 * 
 * Usage:
 * node test-colorlit-integration.js
 */

require('dotenv').config();
const axios = require('axios');

// Configuration
const API_BASE_URL = 'http://localhost:' + (process.env.PORT || 8080) + '/api/colorlit';
const TEST_HOST = process.env.COLORLIT_HOST || '*************'; // Replace with your device IP
const TEST_PORT = process.env.COLORLIT_PORT || 80;
const TEST_API_KEY = process.env.COLORLIT_API_KEY || '';
const TEST_USERNAME = process.env.COLORLIT_USERNAME || '';
const TEST_PASSWORD = process.env.COLORLIT_PASSWORD || '';

// Test configuration
const config = {
  host: TEST_HOST,
  port: TEST_PORT,
  apiKey: TEST_API_KEY,
  username: TEST_USERNAME,
  password: TEST_PASSWORD
};

// Colors to test
const colors = [
  { r: 255, g: 0, b: 0, w: 0 },   // Red
  { r: 0, g: 255, b: 0, w: 0 },   // Green
  { r: 0, g: 0, b: 255, w: 0 },   // Blue
  { r: 255, g: 255, b: 0, w: 0 }, // Yellow
  { r: 255, g: 0, b: 255, w: 0 }, // Magenta
  { r: 0, g: 255, b: 255, w: 0 }, // Cyan
  { r: 255, g: 255, b: 255, w: 0 } // White
];

// Brightness levels to test
const brightnessLevels = [10, 50, 100];

// Speed levels to test
const speedLevels = [10, 50, 100];

// Helper function to make API requests
async function makeRequest(method, endpoint, data = null) {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const options = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    if (data) {
      options.data = data;
    }
    
    console.log(`Making ${method} request to ${url}`);
    const response = await axios(options);
    return response.data;
  } catch (error) {
    console.error('API request failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    throw error;
  }
}

// Test saving configuration
async function testSaveConfig() {
  console.log('\n=== Testing Save Configuration ===');
  try {
    const result = await makeRequest('POST', '/config', config);
    console.log('Configuration saved successfully:', result.message);
    return true;
  } catch (error) {
    console.error('Failed to save configuration');
    return false;
  }
}

// Test getting configuration
async function testGetConfig() {
  console.log('\n=== Testing Get Configuration ===');
  try {
    const result = await makeRequest('GET', '/config');
    console.log('Configuration retrieved successfully:');
    console.log(`Host: ${result.host}`);
    console.log(`Port: ${result.port}`);
    console.log(`Configured with: ${result.configuredWith}`);
    return true;
  } catch (error) {
    console.error('Failed to get configuration');
    return false;
  }
}

// Test getting device information
async function testGetDeviceInfo() {
  console.log('\n=== Testing Get Device Information ===');
  try {
    const result = await makeRequest('GET', '/device');
    console.log('Device information retrieved successfully:');
    console.log(result);
    return true;
  } catch (error) {
    console.error('Failed to get device information');
    return false;
  }
}

// Test getting status
async function testGetStatus() {
  console.log('\n=== Testing Get Status ===');
  try {
    const result = await makeRequest('GET', '/status');
    console.log('Status retrieved successfully:');
    console.log(result);
    return true;
  } catch (error) {
    console.error('Failed to get status');
    return false;
  }
}

// Test power control
async function testPowerControl() {
  console.log('\n=== Testing Power Control ===');
  try {
    // Turn on
    console.log('Turning on...');
    await makeRequest('POST', '/power', { power: true });
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds
    
    // Turn off
    console.log('Turning off...');
    await makeRequest('POST', '/power', { power: false });
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds
    
    // Turn back on
    console.log('Turning back on...');
    await makeRequest('POST', '/power', { power: true });
    
    return true;
  } catch (error) {
    console.error('Failed to test power control');
    return false;
  }
}

// Test color control
async function testColorControl() {
  console.log('\n=== Testing Color Control ===');
  try {
    for (const color of colors) {
      console.log(`Setting color to R:${color.r}, G:${color.g}, B:${color.b}, W:${color.w}...`);
      await makeRequest('POST', '/color', color);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds
    }
    return true;
  } catch (error) {
    console.error('Failed to test color control');
    return false;
  }
}

// Test brightness control
async function testBrightnessControl() {
  console.log('\n=== Testing Brightness Control ===');
  try {
    for (const brightness of brightnessLevels) {
      console.log(`Setting brightness to ${brightness}%...`);
      await makeRequest('POST', '/brightness', { brightness });
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds
    }
    return true;
  } catch (error) {
    console.error('Failed to test brightness control');
    return false;
  }
}

// Test speed control
async function testSpeedControl() {
  console.log('\n=== Testing Speed Control ===');
  try {
    for (const speed of speedLevels) {
      console.log(`Setting speed to ${speed}%...`);
      await makeRequest('POST', '/speed', { speed });
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for 2 seconds
    }
    return true;
  } catch (error) {
    console.error('Failed to test speed control');
    return false;
  }
}

// Test getting modes
async function testGetModes() {
  console.log('\n=== Testing Get Modes ===');
  try {
    const result = await makeRequest('GET', '/modes');
    console.log('Modes retrieved successfully:');
    console.log(result);
    
    if (result && result.length > 0) {
      // Test setting a mode
      const mode = result[0].name || result[0];
      console.log(`Setting mode to ${mode}...`);
      await makeRequest('POST', '/mode', { mode });
    }
    
    return true;
  } catch (error) {
    console.error('Failed to get modes');
    return false;
  }
}

// Test getting zones
async function testGetZones() {
  console.log('\n=== Testing Get Zones ===');
  try {
    const result = await makeRequest('GET', '/zones');
    console.log('Zones retrieved successfully:');
    console.log(result);
    
    if (result && result.length > 0) {
      // Test selecting a zone
      const zoneId = result[0].id || result[0];
      console.log(`Selecting zone ${zoneId}...`);
      await makeRequest('POST', '/zone', { zoneId });
    }
    
    return true;
  } catch (error) {
    console.error('Failed to get zones');
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting Colorlit Z4 Pro LED Controller integration tests...');
  
  // Track test results
  const results = {
    saveConfig: false,
    getConfig: false,
    getDeviceInfo: false,
    getStatus: false,
    powerControl: false,
    colorControl: false,
    brightnessControl: false,
    speedControl: false,
    getModes: false,
    getZones: false
  };
  
  try {
    // First, save configuration
    results.saveConfig = await testSaveConfig();
    
    // Only proceed with other tests if configuration was saved successfully
    if (results.saveConfig) {
      results.getConfig = await testGetConfig();
      results.getDeviceInfo = await testGetDeviceInfo();
      results.getStatus = await testGetStatus();
      results.powerControl = await testPowerControl();
      results.colorControl = await testColorControl();
      results.brightnessControl = await testBrightnessControl();
      results.speedControl = await testSpeedControl();
      results.getModes = await testGetModes();
      results.getZones = await testGetZones();
    }
  } catch (error) {
    console.error('An error occurred during testing:', error);
  }
  
  // Print test summary
  console.log('\n=== Test Summary ===');
  for (const [test, passed] of Object.entries(results)) {
    console.log(`${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  }
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  console.log(`\nPassed ${passedCount} out of ${totalCount} tests`);
  
  if (passedCount === totalCount) {
    console.log('\nAll tests passed! The Colorlit Z4 Pro LED Controller integration is working correctly.');
  } else {
    console.log('\nSome tests failed. Please check the logs for details.');
  }
}

// Run the tests
runTests();