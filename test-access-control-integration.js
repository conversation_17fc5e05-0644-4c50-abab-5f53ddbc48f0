/**
 * Integration tests for Access Control System
 * 
 * This file contains integration tests for the unified access control system
 * that verify interactions between different components.
 */

const chai = require('chai');
const expect = chai.expect;
const sinon = require('sinon');
const proxyquire = require('proxyquire');

// Import the services and controllers
const accessControlService = require('./server/services/accessControlService');
const unifiAccessController = require('./server/controllers/unifiAccessController');
const lenelS2NetBoxController = require('./server/controllers/lenelS2NetBoxController');
const accessControlController = require('./server/controllers/accessControlController');
const realtimeService = require('./server/services/realtimeService');
const websocketServer = require('./server/websocket/websocketServer');

describe('Access Control System Integration Tests', () => {
  // Setup and teardown
  beforeEach(() => {
    // Reset all stubs and spies
    sinon.restore();
  });

  describe('User Creation Flow', () => {
    it('should create a user in multiple systems and broadcast the event', async () => {
      // Mock the request and response objects
      const req = {
        body: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          department: 'IT',
          title: 'Developer',
          systems: ['unifi-access', 'lenel-s2-netbox'],
          accessLevels: {
            'unifi-access': ['level-1'],
            'lenel-s2-netbox': ['level-2']
          },
          cards: [
            { cardNumber: '12345', format: 'HID' }
          ]
        }
      };
      
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the individual system controllers
      const unifiCreateUserStub = sinon.stub(unifiAccessController, 'createUser').resolves({
        id: 'unifi-user-1',
        name: 'John Doe',
        email: '<EMAIL>'
      });
      
      const lenelCreateUserStub = sinon.stub(lenelS2NetBoxController, 'createUser').resolves({
        id: 'lenel-user-1',
        name: 'John Doe',
        email: '<EMAIL>'
      });

      // Mock the realtime service broadcast method
      const broadcastUserStatusChangeStub = sinon.stub(realtimeService, 'broadcastUserStatusChange');

      // Call the controller method
      await accessControlController.createUser(req, res);

      // Verify the individual system controllers were called with correct data
      expect(unifiCreateUserStub.calledOnce).to.be.true;
      expect(lenelCreateUserStub.calledOnce).to.be.true;
      
      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('success', true);
      
      // Verify the realtime service was called to broadcast the event
      expect(broadcastUserStatusChangeStub.calledOnce).to.be.true;
      expect(broadcastUserStatusChangeStub.firstCall.args[0]).to.include({
        action: 'created',
        userName: 'John Doe'
      });
    });

    it('should handle partial failures when creating a user in multiple systems', async () => {
      // Mock the request and response objects
      const req = {
        body: {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          department: 'HR',
          title: 'Manager',
          systems: ['unifi-access', 'lenel-s2-netbox'],
          accessLevels: {
            'unifi-access': ['level-1'],
            'lenel-s2-netbox': ['level-2']
          },
          cards: [
            { cardNumber: '67890', format: 'HID' }
          ]
        }
      };
      
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the individual system controllers - one succeeds, one fails
      const unifiCreateUserStub = sinon.stub(unifiAccessController, 'createUser').resolves({
        id: 'unifi-user-2',
        name: 'Jane Smith',
        email: '<EMAIL>'
      });
      
      const lenelCreateUserStub = sinon.stub(lenelS2NetBoxController, 'createUser').rejects(
        new Error('Failed to create user in Lenel S2 NetBox')
      );

      // Call the controller method
      await accessControlController.createUser(req, res);

      // Verify the individual system controllers were called
      expect(unifiCreateUserStub.calledOnce).to.be.true;
      expect(lenelCreateUserStub.calledOnce).to.be.true;
      
      // Verify the response indicates partial success
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('partialSuccess', true);
      expect(res.json.firstCall.args[0].results).to.have.property('unifi-access').that.includes({ success: true });
      expect(res.json.firstCall.args[0].results).to.have.property('lenel-s2-netbox').that.includes({ success: false });
    });
  });

  describe('Door Control Flow', () => {
    it('should control a door and broadcast the status change', async () => {
      // Mock the request and response objects
      const req = {
        body: {
          doorId: 'door-1',
          system: 'unifi-access',
          action: 'unlock'
        }
      };
      
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the unifi access controller
      const unifiControlDoorStub = sinon.stub(unifiAccessController, 'controlDoor').resolves({
        success: true,
        message: 'Door unlocked successfully'
      });

      // Mock the door status retrieval
      const unifiGetDoorStub = sinon.stub(unifiAccessController, 'getDoor').resolves({
        id: 'door-1',
        name: 'Front Door',
        status: 'unlocked'
      });

      // Mock the realtime service broadcast method
      const broadcastDoorStatusStub = sinon.stub(realtimeService, 'broadcastDoorStatus');

      // Call the controller method
      await accessControlController.controlDoor(req, res);

      // Verify the unifi controller was called
      expect(unifiControlDoorStub.calledOnce).to.be.true;
      expect(unifiControlDoorStub.firstCall.args[0]).to.equal('door-1');
      expect(unifiControlDoorStub.firstCall.args[1]).to.equal('unlock');
      
      // Verify the door status was retrieved
      expect(unifiGetDoorStub.calledOnce).to.be.true;
      
      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('success', true);
      
      // Verify the realtime service was called to broadcast the door status
      expect(broadcastDoorStatusStub.calledOnce).to.be.true;
      expect(broadcastDoorStatusStub.firstCall.args[0]).to.be.an('array');
      expect(broadcastDoorStatusStub.firstCall.args[0][0]).to.include({
        doorId: 'door-1',
        doorName: 'Front Door',
        status: 'unlocked'
      });
    });
  });

  describe('Access Level Assignment Flow', () => {
    it('should assign access levels to a user and broadcast the change', async () => {
      // Mock the request and response objects
      const req = {
        params: { userId: 'user-1' },
        body: {
          accessLevels: {
            'unifi-access': ['level-1', 'level-3'],
            'lenel-s2-netbox': ['level-2']
          }
        }
      };
      
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the individual system controllers
      const unifiAssignAccessLevelsStub = sinon.stub(unifiAccessController, 'assignAccessLevels').resolves({
        success: true,
        userId: 'user-1',
        accessLevels: ['level-1', 'level-3']
      });
      
      const lenelAssignAccessLevelsStub = sinon.stub(lenelS2NetBoxController, 'assignAccessLevels').resolves({
        success: true,
        userId: 'user-1',
        accessLevels: ['level-2']
      });

      // Mock the user retrieval
      const getUserStub = sinon.stub(accessControlController, 'getUser').resolves({
        id: 'user-1',
        name: 'John Doe',
        email: '<EMAIL>',
        systems: ['unifi-access', 'lenel-s2-netbox']
      });

      // Mock the realtime service broadcast method
      const broadcastAccessLevelChangeStub = sinon.stub(realtimeService, 'broadcastAccessLevelChange');

      // Call the controller method
      await accessControlController.assignAccessLevels(req, res);

      // Verify the individual system controllers were called
      expect(unifiAssignAccessLevelsStub.calledOnce).to.be.true;
      expect(lenelAssignAccessLevelsStub.calledOnce).to.be.true;
      
      // Verify the user was retrieved
      expect(getUserStub.calledOnce).to.be.true;
      
      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('success', true);
      
      // Verify the realtime service was called to broadcast the access level change
      expect(broadcastAccessLevelChangeStub.calledOnce).to.be.true;
      expect(broadcastAccessLevelChangeStub.firstCall.args[0]).to.include({
        userId: 'user-1',
        userName: 'John Doe',
        action: 'updated'
      });
    });
  });

  describe('Schedule Creation Flow', () => {
    it('should create a schedule in multiple systems and broadcast the event', async () => {
      // Mock the request and response objects
      const req = {
        body: {
          name: 'Business Hours',
          description: 'Monday to Friday, 9 AM to 5 PM',
          timeZone: 'America/New_York',
          handleDaylightSaving: true,
          systems: ['unifi-access', 'lenel-s2-netbox'],
          timeWindows: [
            {
              days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
              startTime: '09:00',
              endTime: '17:00'
            }
          ],
          doors: {
            'unifi-access': ['door-1', 'door-2'],
            'lenel-s2-netbox': ['door-3', 'door-4']
          }
        }
      };
      
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the individual system controllers
      const unifiCreateScheduleStub = sinon.stub(unifiAccessController, 'createSchedule').resolves({
        id: 'unifi-schedule-1',
        name: 'Business Hours'
      });
      
      const lenelCreateScheduleStub = sinon.stub(lenelS2NetBoxController, 'createSchedule').resolves({
        id: 'lenel-schedule-1',
        name: 'Business Hours'
      });

      // Mock the realtime service broadcast method
      const broadcastSystemStatusChangeStub = sinon.stub(realtimeService, 'broadcastSystemStatusChange');

      // Call the controller method
      await accessControlController.createSchedule(req, res);

      // Verify the individual system controllers were called
      expect(unifiCreateScheduleStub.calledOnce).to.be.true;
      expect(lenelCreateScheduleStub.calledOnce).to.be.true;
      
      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('success', true);
      
      // Verify the realtime service was called to broadcast the system status change
      expect(broadcastSystemStatusChangeStub.calledOnce).to.be.true;
    });
  });

  describe('WebSocket Integration', () => {
    it('should broadcast events through the WebSocket server', () => {
      // Mock the WebSocket server broadcast method
      const broadcastStub = sinon.stub(websocketServer, 'broadcast');

      // Create a test event
      const testEvent = {
        doorId: 'door-1',
        doorName: 'Front Door',
        status: 'unlocked',
        timestamp: new Date().toISOString()
      };

      // Call the realtime service broadcast method
      realtimeService.broadcastDoorStatus([testEvent]);

      // Verify the WebSocket server broadcast method was called
      expect(broadcastStub.calledOnce).to.be.true;
      expect(broadcastStub.firstCall.args[0]).to.equal('door-status');
      expect(broadcastStub.firstCall.args[1]).to.deep.equal([testEvent]);
    });

    it('should broadcast access attempt events', () => {
      // Mock the WebSocket server broadcast method
      const broadcastStub = sinon.stub(websocketServer, 'broadcast');

      // Create a test access attempt event
      const accessAttempt = {
        userId: 'user-1',
        userName: 'John Doe',
        doorId: 'door-1',
        doorName: 'Front Door',
        timestamp: new Date().toISOString(),
        granted: true,
        system: 'unifi-access'
      };

      // Call the realtime service broadcast method
      realtimeService.broadcastAccessAttempt(accessAttempt);

      // Verify the WebSocket server broadcast method was called
      expect(broadcastStub.calledOnce).to.be.true;
      expect(broadcastStub.firstCall.args[0]).to.equal('access-attempt');
      expect(broadcastStub.firstCall.args[1]).to.deep.equal(accessAttempt);
    });
  });

  describe('Time Zone Management', () => {
    it('should create schedules with different time zones', async () => {
      // Create schedules with different time zones
      const schedules = [
        {
          name: 'Eastern Time Schedule',
          timeZone: 'America/New_York',
          handleDaylightSaving: true,
          timeWindows: [{ days: ['monday'], startTime: '09:00', endTime: '17:00' }]
        },
        {
          name: 'Pacific Time Schedule',
          timeZone: 'America/Los_Angeles',
          handleDaylightSaving: true,
          timeWindows: [{ days: ['monday'], startTime: '09:00', endTime: '17:00' }]
        },
        {
          name: 'UTC Schedule',
          timeZone: 'UTC',
          handleDaylightSaving: false,
          timeWindows: [{ days: ['monday'], startTime: '09:00', endTime: '17:00' }]
        }
      ];

      // Mock the controller's dependencies
      const createScheduleStub = sinon.stub(accessControlController, 'createSchedule');
      
      // Set up the stubs to resolve with success
      schedules.forEach((schedule, index) => {
        createScheduleStub.onCall(index).resolves({
          success: true,
          id: `schedule-${index + 1}`,
          name: schedule.name,
          timeZone: schedule.timeZone
        });
      });

      // Create each schedule
      for (const schedule of schedules) {
        const req = { body: { ...schedule, systems: ['unifi-access'] } };
        const res = {
          json: sinon.spy(),
          status: sinon.stub().returnsThis()
        };

        await accessControlController.createSchedule(req, res);
        
        // Verify the response
        expect(res.json.calledOnce).to.be.true;
        expect(res.json.firstCall.args[0]).to.have.property('success', true);
        expect(res.json.firstCall.args[0]).to.have.property('timeZone', schedule.timeZone);
      }

      // Verify the controller was called for each schedule
      expect(createScheduleStub.callCount).to.equal(schedules.length);
    });
  });
});

// Run the tests
if (require.main === module) {
  describe('Running Access Control Integration Tests', () => {
    // Add any setup code here
    
    // Run the tests
    require('mocha').run();
  });
}