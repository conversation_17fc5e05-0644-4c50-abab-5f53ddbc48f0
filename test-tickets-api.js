/**
 * Test script to verify the tickets API response format
 * 
 * This script makes a request to the tickets API endpoint and logs the response
 * to verify that the client-side code can correctly handle the response format.
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set up axios with authentication if needed
const api = axios.create({
  baseURL: 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Function to get an authentication token (if needed)
async function getAuthToken() {
  try {
    // This is a simplified example - adjust based on your actual auth system
    const response = await api.post('/api/auth/login', {
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      password: process.env.TEST_USER_PASSWORD || 'password'
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Error getting auth token:', error.message);
    return null;
  }
}

// Function to test the tickets API
async function testTicketsAPI() {
  try {
    // Get auth token if needed
    const token = await getAuthToken();
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }
    
    // Make request to tickets API
    console.log('Making request to tickets API...');
    const response = await api.get('/api/tickets', {
      params: {
        page: 1,
        limit: 10
      }
    });
    
    // Log the response structure
    console.log('Response received:');
    console.log('Status:', response.status);
    console.log('Response structure:', Object.keys(response.data));
    
    // Check if the response has the expected format
    if (response.data.tickets && response.data.pagination) {
      console.log('✅ Response has the expected format with tickets and pagination properties');
      console.log('Tickets count:', response.data.tickets.length);
      console.log('Pagination:', response.data.pagination);
    } else {
      console.log('❌ Response does not have the expected format');
      console.log('Full response:', response.data);
    }
    
    return response.data;
  } catch (error) {
    console.error('Error testing tickets API:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testTicketsAPI()
  .then(() => {
    console.log('Test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });