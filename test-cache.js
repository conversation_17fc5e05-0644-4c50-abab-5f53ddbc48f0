/**
 * Test script to verify the caching mechanism for external API calls
 * 
 * This script makes multiple requests to cached endpoints and checks
 * if subsequent requests are served from the cache.
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Base URL for API requests
const baseUrl = process.env.NODE_ENV === 'production' 
  ? 'https://your-production-url.com' 
  : 'http://localhost:5000';

// Authentication token (if needed)
let authToken = '';

/**
 * Login to get authentication token
 */
async function login() {
  try {
    // Replace with your actual login endpoint and credentials
    const response = await axios.post(`${baseUrl}/api/auth/login`, {
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      password: process.env.TEST_USER_PASSWORD || 'password'
    });
    
    authToken = response.data.token;
    console.log('Successfully logged in and got auth token');
  } catch (error) {
    console.error('Error logging in:', error.message);
    process.exit(1);
  }
}

/**
 * Make a request to a cached endpoint
 * @param {string} endpoint - The API endpoint to test
 * @param {number} count - Number of times to call the endpoint
 */
async function testCachedEndpoint(endpoint, count = 3) {
  console.log(`\n--- Testing cached endpoint: ${endpoint} ---`);
  
  const headers = {
    'x-auth-token': authToken
  };
  
  try {
    // Make multiple requests to the same endpoint
    for (let i = 1; i <= count; i++) {
      console.log(`\nRequest #${i}:`);
      const startTime = Date.now();
      
      const response = await axios.get(`${baseUrl}${endpoint}`, { headers });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`Status: ${response.status}`);
      console.log(`Duration: ${duration}ms`);
      
      // Check for cache-related headers
      const cacheControl = response.headers['cache-control'] || 'not set';
      console.log(`Cache-Control: ${cacheControl}`);
      
      // If this is the first request, it should be a cache miss
      // Subsequent requests should be faster if caching is working
      if (i === 1) {
        console.log('First request - should be a cache miss');
        console.log('Response data:', JSON.stringify(response.data).substring(0, 100) + '...');
      } else {
        console.log(`Subsequent request - should be served from cache and faster than the first request`);
      }
      
      // Wait a short time between requests
      if (i < count) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  } catch (error) {
    console.error(`Error testing endpoint ${endpoint}:`, error.message);
  }
}

/**
 * Main test function
 */
async function runTests() {
  try {
    // Login to get auth token
    await login();
    
    // Test WiiM endpoints
    await testCachedEndpoint('/api/wiim/device');
    await testCachedEndpoint('/api/wiim/config');
    await testCachedEndpoint('/api/wiim/inputs');
    await testCachedEndpoint('/api/wiim/equalizer');
    
    // Test Panasonic endpoints
    await testCachedEndpoint('/api/panasonic/info');
    await testCachedEndpoint('/api/panasonic/presets');
    await testCachedEndpoint('/api/panasonic/config');
    
    console.log('\n--- All tests completed ---');
  } catch (error) {
    console.error('Error running tests:', error.message);
  }
}

// Run the tests
runTests();