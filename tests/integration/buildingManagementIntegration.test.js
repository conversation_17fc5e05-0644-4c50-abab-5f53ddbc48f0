const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Import models
const ElectricalPanel = require('../../models/ElectricalPanel');
const ElectricalCircuit = require('../../models/ElectricalCircuit');
const ElectricalOutlet = require('../../models/ElectricalOutlet');
const SafetyAsset = require('../../models/SafetyAsset');
const Building = require('../../models/Building');
const Floor = require('../../models/Floor');
const FloorplanIcon = require('../../models/FloorplanIcon');

// Test app setup
const app = require('../../server');

let mongoServer;
let testBuilding, testFloor, testUser;

describe('Building Management System Integration Tests', () => {
  beforeAll(async () => {
    // Setup in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    // Create test building and floor
    testBuilding = new Building({
      name: 'Test Church Building',
      code: 'TCB',
      address: '123 Test Street',
      status: 'active'
    });
    await testBuilding.save();

    testFloor = new Floor({
      buildingId: testBuilding._id,
      name: 'Ground Floor',
      code: 'GF',
      level: 1,
      status: 'active'
    });
    await testFloor.save();

    // Mock user for authenticated requests
    testUser = {
      _id: new mongoose.Types.ObjectId(),
      name: 'Test User',
      email: '<EMAIL>',
      role: 'admin'
    };
  });

  afterAll(async () => {
    await mongoose.connection.close();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clean up collections before each test
    await ElectricalPanel.deleteMany({});
    await ElectricalCircuit.deleteMany({});
    await ElectricalOutlet.deleteMany({});
    await SafetyAsset.deleteMany({});
    await FloorplanIcon.deleteMany({});
  });

  describe('Complete Electrical System Integration', () => {
    it('should create complete electrical infrastructure and perform outlet trace', async () => {
      // Step 1: Create electrical panel
      const panelResponse = await request(app)
        .post('/api/electrical/panels')
        .send({
          buildingId: testBuilding._id,
          floorId: testFloor._id,
          name: 'Main Distribution Panel',
          code: 'MDP-A',
          room: 'Electrical Room'
        });

      expect(panelResponse.status).toBe(201);
      const panel = panelResponse.body;

      // Step 2: Create circuit in panel
      const circuitResponse = await request(app)
        .post(`/api/electrical/panels/${panel._id}/circuits`)
        .send({
          number: 1,
          label: 'Lobby Outlets',
          breakerType: 'standard',
          amperage: 20
        });

      expect(circuitResponse.status).toBe(201);
      const circuit = circuitResponse.body;

      // Step 3: Create outlet linked to circuit
      const outletResponse = await request(app)
        .post('/api/electrical/outlets')
        .send({
          buildingId: testBuilding._id,
          floorId: testFloor._id,
          room: 'Main Lobby',
          label: 'LO-01',
          panelId: panel._id,
          circuitId: circuit._id,
          breakerNumber: 1
        });

      expect(outletResponse.status).toBe(201);
      const outlet = outletResponse.body;

      // Step 4: Perform outlet trace
      const traceResponse = await request(app)
        .get('/api/electrical/trace')
        .query({ outletId: outlet._id });

      expect(traceResponse.status).toBe(200);
      expect(traceResponse.body.outlet).toBeDefined();
      expect(traceResponse.body.circuit).toBeDefined();
      expect(traceResponse.body.panel).toBeDefined();
      expect(traceResponse.body.outlet._id).toBe(outlet._id);
      expect(traceResponse.body.circuit._id).toBe(circuit._id);
      expect(traceResponse.body.panel._id).toBe(panel._id);

      // Step 5: Verify data integrity
      expect(traceResponse.body.outlet.panelId).toBe(panel._id);
      expect(traceResponse.body.outlet.circuitId).toBe(circuit._id);
      expect(traceResponse.body.circuit.panelId).toBe(panel._id);
    });

    it('should handle electrical system data export', async () => {
      // Create test data
      const panel = new ElectricalPanel({
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        name: 'Export Test Panel',
        code: 'ETP',
        room: 'Test Room'
      });
      await panel.save();

      const outlet = new ElectricalOutlet({
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        label: 'TEST-01',
        room: 'Test Room',
        panelId: panel._id
      });
      await outlet.save();

      // Test panels export
      const panelsExportResponse = await request(app)
        .get('/api/electrical/export/panels');

      expect(panelsExportResponse.status).toBe(200);
      expect(panelsExportResponse.headers['content-type']).toContain('text/csv');

      // Test outlets export
      const outletsExportResponse = await request(app)
        .get('/api/electrical/export/outlets');

      expect(outletsExportResponse.status).toBe(200);
      expect(outletsExportResponse.headers['content-type']).toContain('text/csv');
    });
  });

  describe('Complete Safety System Integration', () => {
    it('should create safety assets and perform emergency equipment location', async () => {
      // Step 1: Create fire extinguisher
      const fireExtinguisherResponse = await request(app)
        .post('/api/safety/assets')
        .send({
          assetType: 'fire_extinguisher',
          assetId: 'FE-001',
          name: 'Lobby Fire Extinguisher',
          buildingId: testBuilding._id,
          floorId: testFloor._id,
          room: 'Main Lobby',
          specifications: {
            manufacturer: 'Amerex',
            model: 'B500',
            capacity: '5lbs',
            agent: 'ABC Dry Chemical'
          },
          inspection: {
            frequency: 30,
            lastInspection: new Date(),
            inspector: 'Test Inspector'
          }
        });

      expect(fireExtinguisherResponse.status).toBe(201);
      const fireExtinguisher = fireExtinguisherResponse.body;

      // Step 2: Create AED
      const aedResponse = await request(app)
        .post('/api/safety/assets')
        .send({
          assetType: 'aed',
          assetId: 'AED-001',
          name: 'Sanctuary AED',
          buildingId: testBuilding._id,
          floorId: testFloor._id,
          room: 'Sanctuary',
          specifications: {
            manufacturer: 'Philips',
            model: 'HeartStart FRx',
            capacity: 'Adult/Pediatric'
          },
          inspection: {
            frequency: 90,
            lastInspection: new Date(),
            inspector: 'Test Inspector'
          }
        });

      expect(aedResponse.status).toBe(201);
      const aed = aedResponse.body;

      // Step 3: Find nearest fire extinguisher
      const nearestFireResponse = await request(app)
        .get('/api/safety/nearest')
        .query({ 
          assetType: 'fire_extinguisher',
          buildingId: testBuilding._id,
          floorId: testFloor._id
        });

      expect(nearestFireResponse.status).toBe(200);
      expect(nearestFireResponse.body).toHaveLength(1);
      expect(nearestFireResponse.body[0]._id).toBe(fireExtinguisher._id);

      // Step 4: Find nearest AED
      const nearestAEDResponse = await request(app)
        .get('/api/safety/nearest')
        .query({ 
          assetType: 'aed',
          buildingId: testBuilding._id
        });

      expect(nearestAEDResponse.status).toBe(200);
      expect(nearestAEDResponse.body).toHaveLength(1);
      expect(nearestAEDResponse.body[0]._id).toBe(aed._id);

      // Step 5: Record inspection
      const inspectionResponse = await request(app)
        .post(`/api/safety/inspections/${fireExtinguisher._id}`)
        .send({
          notes: 'Integration test inspection',
          status: 'current'
        });

      expect(inspectionResponse.status).toBe(200);
      expect(inspectionResponse.body.serviceHistory).toHaveLength(1);
    });

    it('should track inspection compliance and generate alerts', async () => {
      // Create overdue asset
      const overdueAsset = new SafetyAsset({
        assetType: 'first_aid_kit',
        assetId: 'FAK-OVERDUE',
        name: 'Overdue First Aid Kit',
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        room: 'Test Room',
        inspection: {
          frequency: 30,
          lastInspection: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
          status: 'overdue'
        }
      });
      await overdueAsset.save();

      // Add alert to asset
      overdueAsset.alerts.push({
        type: 'inspection_overdue',
        severity: 'critical',
        message: 'Inspection overdue by 15 days'
      });
      await overdueAsset.save();

      // Get due inspections
      const dueInspectionsResponse = await request(app)
        .get('/api/safety/inspections/due');

      expect(dueInspectionsResponse.status).toBe(200);
      expect(dueInspectionsResponse.body).toHaveLength(1);
      expect(dueInspectionsResponse.body[0].inspection.status).toBe('overdue');

      // Get alerts
      const alertsResponse = await request(app)
        .get('/api/safety/alerts')
        .query({ severity: 'critical', acknowledged: 'false' });

      expect(alertsResponse.status).toBe(200);
      expect(alertsResponse.body).toHaveLength(1);
      expect(alertsResponse.body[0].alert.type).toBe('inspection_overdue');
    });
  });

  describe('Cross-System Integration', () => {
    it('should demonstrate unified building management across electrical and safety systems', async () => {
      // Create complete building infrastructure
      
      // Electrical infrastructure
      const panel = new ElectricalPanel({
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        name: 'Integration Test Panel',
        code: 'ITP',
        room: 'Main Room'
      });
      await panel.save();

      const circuit = new ElectricalCircuit({
        panelId: panel._id,
        number: 1,
        label: 'Main Room Outlets',
        breakerType: 'standard',
        amperage: 20
      });
      await circuit.save();

      const outlet = new ElectricalOutlet({
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        room: 'Main Room',
        label: 'MR-01',
        panelId: panel._id,
        circuitId: circuit._id,
        breakerNumber: 1
      });
      await outlet.save();

      // Safety infrastructure
      const fireExtinguisher = new SafetyAsset({
        assetType: 'fire_extinguisher',
        assetId: 'FE-MAIN',
        name: 'Main Room Fire Extinguisher',
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        room: 'Main Room',
        specifications: {
          manufacturer: 'Amerex',
          capacity: '5lbs'
        },
        inspection: {
          frequency: 30,
          lastInspection: new Date(),
          status: 'current'
        }
      });
      await fireExtinguisher.save();

      const aed = new SafetyAsset({
        assetType: 'aed',
        assetId: 'AED-MAIN',
        name: 'Main Room AED',
        buildingId: testBuilding._id,
        floorId: testFloor._id,
        room: 'Main Room',
        inspection: {
          frequency: 90,
          lastInspection: new Date(),
          status: 'current'
        }
      });
      await aed.save();

      // Test unified location queries
      
      // 1. Get all electrical assets for floor
      const electricalAssetsResponse = await request(app)
        .get('/api/electrical/outlets')
        .query({ floorId: testFloor._id });

      expect(electricalAssetsResponse.status).toBe(200);
      expect(electricalAssetsResponse.body).toHaveLength(1);
      expect(electricalAssetsResponse.body[0].room).toBe('Main Room');

      // 2. Get all safety assets for same floor
      const safetyAssetsResponse = await request(app)
        .get(`/api/safety/assets/building/${testBuilding._id}/floor/${testFloor._id}`);

      expect(safetyAssetsResponse.status).toBe(200);
      expect(safetyAssetsResponse.body).toHaveLength(2);

      // 3. Verify room-based co-location
      const mainRoomElectrical = electricalAssetsResponse.body.filter(item => item.room === 'Main Room');
      const mainRoomSafety = safetyAssetsResponse.body.filter(item => item.room === 'Main Room');

      expect(mainRoomElectrical).toHaveLength(1);
      expect(mainRoomSafety).toHaveLength(2);

      // 4. Test emergency scenario: power failure in Main Room
      // Find electrical panel to shut off power
      const traceResponse = await request(app)
        .get('/api/electrical/trace')
        .query({ label: 'MR-01' });

      expect(traceResponse.status).toBe(200);
      expect(traceResponse.body.panel.room).toBe('Main Room');

      // Find nearest fire safety equipment in case of electrical fire
      const nearestFireEquipmentResponse = await request(app)
        .get('/api/safety/nearest')
        .query({ 
          assetType: 'fire_extinguisher',
          buildingId: testBuilding._id,
          floorId: testFloor._id,
          room: 'Main Room'
        });

      expect(nearestFireEquipmentResponse.status).toBe(200);
      expect(nearestFireEquipmentResponse.body).toHaveLength(1);
      expect(nearestFireEquipmentResponse.body[0].room).toBe('Main Room');

      // This demonstrates unified building management:
      // - Electrical system provides power isolation capabilities
      // - Safety system provides emergency response equipment
      // - Both systems work together for comprehensive building management
    });

    it('should handle demo data seeding for both systems', async () => {
      // Seed electrical demo data
      const electricalSeedResponse = await request(app)
        .post('/api/electrical/seed-demo-data');

      expect(electricalSeedResponse.status).toBe(200);
      expect(electricalSeedResponse.body.created.panels).toBeGreaterThan(0);
      expect(electricalSeedResponse.body.created.circuits).toBeGreaterThan(0);
      expect(electricalSeedResponse.body.created.outlets).toBeGreaterThan(0);

      // Seed safety demo data
      const safetySeedResponse = await request(app)
        .post('/api/safety/seed-demo-data');

      expect(safetySeedResponse.status).toBe(200);
      expect(safetySeedResponse.body.created).toBeGreaterThan(0);

      // Verify both systems have data
      const electricalCount = await ElectricalOutlet.countDocuments();
      const safetyCount = await SafetyAsset.countDocuments();

      expect(electricalCount).toBeGreaterThan(0);
      expect(safetyCount).toBeGreaterThan(0);

      // Test cross-system functionality with demo data
      const allOutletsResponse = await request(app)
        .get('/api/electrical/outlets');
      
      const allSafetyResponse = await request(app)
        .get('/api/safety/assets');

      expect(allOutletsResponse.status).toBe(200);
      expect(allSafetyResponse.status).toBe(200);
      expect(allOutletsResponse.body.length).toBeGreaterThan(0);
      expect(allSafetyResponse.body.assets.length).toBeGreaterThan(0);
    });
  });

  describe('System Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      // Create multiple assets to test performance
      const promises = [];

      // Create 50 outlets
      for (let i = 1; i <= 50; i++) {
        promises.push(
          new ElectricalOutlet({
            buildingId: testBuilding._id,
            floorId: testFloor._id,
            label: `PERF-${i.toString().padStart(2, '0')}`,
            room: `Room ${i}`
          }).save()
        );
      }

      // Create 25 safety assets
      for (let i = 1; i <= 25; i++) {
        promises.push(
          new SafetyAsset({
            assetType: i % 2 === 0 ? 'fire_extinguisher' : 'aed',
            assetId: `SAFETY-${i.toString().padStart(2, '0')}`,
            name: `Safety Asset ${i}`,
            buildingId: testBuilding._id,
            floorId: testFloor._id,
            room: `Room ${i}`,
            inspection: {
              frequency: 30,
              status: 'current'
            }
          }).save()
        );
      }

      await Promise.all(promises);

      // Test search performance
      const startTime = Date.now();
      
      const searchResponse = await request(app)
        .get('/api/electrical/outlets')
        .query({ search: 'PERF', limit: 20 });

      const searchTime = Date.now() - startTime;

      expect(searchResponse.status).toBe(200);
      expect(searchResponse.body.length).toBeLessThanOrEqual(20);
      expect(searchTime).toBeLessThan(1000); // Should complete within 1 second

      // Test pagination
      const paginatedResponse = await request(app)
        .get('/api/safety/assets')
        .query({ limit: 10, offset: 10 });

      expect(paginatedResponse.status).toBe(200);
      expect(paginatedResponse.body.assets).toHaveLength(10);
      expect(paginatedResponse.body.total).toBe(25);
    });
  });
});

module.exports = {
  testBuilding,
  testFloor,
  testUser
};