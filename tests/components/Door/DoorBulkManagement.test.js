import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DoorBulkManagement from '../../../client/src/components/Door/DoorBulkManagement';

// Mock buildingManagementService
jest.mock('../../../client/src/services/buildingManagementService', () => ({
  getDoors: jest.fn(),
  unlockDoor: jest.fn(),
  lockDoor: jest.fn(),
  setDoorPassageMode: jest.fn(),
  refreshDoorStatus: jest.fn()
}));

import buildingManagementService from '../../../client/src/services/buildingManagementService';

describe('DoorBulkManagement', () => {
  const mockDoors = [
    {
      id: 'door-1',
      name: 'Main Entrance',
      location: 'Lobby',
      status: 'locked',
      source: 'lenelS2NetBox',
      lastActivity: '2025-08-20T10:00:00Z'
    },
    {
      id: 'door-2',
      name: 'Office Door',
      location: 'Office Area',
      status: 'unlocked',
      source: 'unifiAccess',
      lastActivity: '2025-08-20T09:30:00Z'
    },
    {
      id: 'door-3',
      name: 'Emergency Exit',
      location: 'Hallway',
      status: 'closed',
      source: 'lenelS2NetBox',
      lastActivity: '2025-08-20T08:15:00Z'
    }
  ];

  beforeEach(() => {
    buildingManagementService.getDoors.mockResolvedValue(mockDoors);
    buildingManagementService.unlockDoor.mockResolvedValue({ success: true });
    buildingManagementService.lockDoor.mockResolvedValue({ success: true });
    buildingManagementService.setDoorPassageMode.mockResolvedValue({ success: true });
    buildingManagementService.refreshDoorStatus.mockResolvedValue({ success: true });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders door bulk management dialog when open', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        buildingId="building-1"
        floorId="floor-1"
      />
    );

    expect(screen.getByText('Door Bulk Management')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Main Entrance')).toBeInTheDocument();
    });
  });

  it('does not render when closed', () => {
    render(
      <DoorBulkManagement
        open={false}
        onClose={jest.fn()}
      />
    );

    expect(screen.queryByText('Door Bulk Management')).not.toBeInTheDocument();
  });

  it('loads doors on open', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
      />
    );

    await waitFor(() => {
      expect(buildingManagementService.getDoors).toHaveBeenCalledTimes(1);
    });

    expect(screen.getByText('Main Entrance')).toBeInTheDocument();
    expect(screen.getByText('Office Door')).toBeInTheDocument();
    expect(screen.getByText('Emergency Exit')).toBeInTheDocument();
  });

  it('uses initial doors when provided', () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Should not call getDoors if initial doors provided
    expect(buildingManagementService.getDoors).not.toHaveBeenCalled();
    expect(screen.getByText('Main Entrance')).toBeInTheDocument();
  });

  it('filters doors by status', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Filter by locked status
    const statusFilter = screen.getByLabelText('Filter by Status');
    fireEvent.mouseDown(statusFilter);
    fireEvent.click(screen.getByText('Locked'));

    // Should only show locked doors
    expect(screen.getByText('Main Entrance')).toBeInTheDocument();
    expect(screen.queryByText('Office Door')).not.toBeInTheDocument();
    expect(screen.queryByText('Emergency Exit')).not.toBeInTheDocument();
  });

  it('filters doors by source', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Filter by UniFi Access source
    const sourceFilter = screen.getByLabelText('Filter by Source');
    fireEvent.mouseDown(sourceFilter);
    fireEvent.click(screen.getByText('UniFi Access'));

    // Should only show UniFi Access doors
    expect(screen.queryByText('Main Entrance')).not.toBeInTheDocument();
    expect(screen.getByText('Office Door')).toBeInTheDocument();
    expect(screen.queryByText('Emergency Exit')).not.toBeInTheDocument();
  });

  it('selects and deselects doors', () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Find checkboxes
    const checkboxes = screen.getAllByRole('checkbox');
    const selectAllCheckbox = checkboxes[0]; // First checkbox is select all
    const firstDoorCheckbox = checkboxes[1]; // First door checkbox

    // Select first door
    fireEvent.click(firstDoorCheckbox);
    expect(firstDoorCheckbox).toBeChecked();

    // Status should show 1 selected
    expect(screen.getByText('1 of 3 doors selected')).toBeInTheDocument();

    // Select all should work
    fireEvent.click(selectAllCheckbox);
    expect(screen.getByText('3 of 3 doors selected')).toBeInTheDocument();
  });

  it('executes bulk unlock operation', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Select all doors
    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(selectAllCheckbox);

    // Select unlock operation
    const operationSelect = screen.getByLabelText('Bulk Operation');
    fireEvent.mouseDown(operationSelect);
    fireEvent.click(screen.getByText('Unlock Doors'));

    // Execute operation
    const executeButton = screen.getByRole('button', { name: /execute/i });
    fireEvent.click(executeButton);

    await waitFor(() => {
      expect(buildingManagementService.unlockDoor).toHaveBeenCalledTimes(3);
    });

    expect(buildingManagementService.unlockDoor).toHaveBeenCalledWith('door-1');
    expect(buildingManagementService.unlockDoor).toHaveBeenCalledWith('door-2');
    expect(buildingManagementService.unlockDoor).toHaveBeenCalledWith('door-3');
  });

  it('executes bulk lock operation', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Select first door
    const firstDoorCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(firstDoorCheckbox);

    // Select lock operation
    const operationSelect = screen.getByLabelText('Bulk Operation');
    fireEvent.mouseDown(operationSelect);
    fireEvent.click(screen.getByText('Lock Doors'));

    // Execute operation
    const executeButton = screen.getByRole('button', { name: /execute/i });
    fireEvent.click(executeButton);

    await waitFor(() => {
      expect(buildingManagementService.lockDoor).toHaveBeenCalledWith('door-1');
    });
  });

  it('shows operation results', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Select first door
    const firstDoorCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(firstDoorCheckbox);

    // Select unlock operation
    const operationSelect = screen.getByLabelText('Bulk Operation');
    fireEvent.mouseDown(operationSelect);
    fireEvent.click(screen.getByText('Unlock Doors'));

    // Execute operation
    const executeButton = screen.getByRole('button', { name: /execute/i });
    fireEvent.click(executeButton);

    await waitFor(() => {
      expect(screen.getByText('Operation Results')).toBeInTheDocument();
    });

    expect(screen.getByText('Successfully performed unlock on 1 doors')).toBeInTheDocument();
  });

  it('handles operation errors gracefully', async () => {
    buildingManagementService.unlockDoor.mockRejectedValue(new Error('Access denied'));

    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    // Select first door and execute unlock
    const firstDoorCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(firstDoorCheckbox);

    const operationSelect = screen.getByLabelText('Bulk Operation');
    fireEvent.mouseDown(operationSelect);
    fireEvent.click(screen.getByText('Unlock Doors'));

    const executeButton = screen.getByRole('button', { name: /execute/i });
    fireEvent.click(executeButton);

    await waitFor(() => {
      expect(screen.getByText(/operation completed with.*failures/i)).toBeInTheDocument();
    });
  });

  it('calls onClose when close button is clicked', () => {
    const mockOnClose = jest.fn();
    
    render(
      <DoorBulkManagement
        open={true}
        onClose={mockOnClose}
        initialDoors={mockDoors}
      />
    );

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('refreshes door list when refresh button is clicked', async () => {
    render(
      <DoorBulkManagement
        open={true}
        onClose={jest.fn()}
        initialDoors={mockDoors}
      />
    );

    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(buildingManagementService.getDoors).toHaveBeenCalledTimes(1);
    });
  });
});