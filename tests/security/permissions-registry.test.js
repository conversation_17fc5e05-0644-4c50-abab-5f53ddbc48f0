const fs = require('fs');
const path = require('path');

// Import the registry
const permissions = require('../../config/permissions');

function isJsFile(filePath) {
  return filePath.endsWith('.js') || filePath.endsWith('.jsx');
}

function walk(dir, out = []) {
  if (!fs.existsSync(dir)) return out;
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    if (entry.name === 'node_modules' || entry.name === '.git' || entry.name === 'build' || entry.name === 'coverage') {
      continue;
    }
    const full = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      walk(full, out);
    } else if (entry.isFile() && isJsFile(full)) {
      out.push(full);
    }
  }
  return out;
}

function extractPermissionsFromSource(src) {
  const perms = [];
  const regexes = [
    /hasPermission\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
    /checkPermission\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
  ];
  for (const rx of regexes) {
    let m;
    while ((m = rx.exec(src)) !== null) {
      perms.push(m[1]);
    }
  }
  return perms;
}

function scanPermissions(dirs) {
  const files = dirs.flatMap((d) => walk(d));
  const set = new Set();
  for (const f of files) {
    try {
      const src = fs.readFileSync(f, 'utf8');
      const found = extractPermissionsFromSource(src);
      for (const p of found) set.add(p);
    } catch (e) {
      // ignore
    }
  }
  return Array.from(set).sort();
}

// Define dirs to scan similarly to the registry
const SCAN_DIRS = [
  path.join(__dirname, '..', '..', 'routes'),
  path.join(__dirname, '..', '..', 'server', 'routes'),
  path.join(__dirname, '..', '..', 'server'),
];

describe('permissions registry coverage', () => {
  it('includes every permission referenced in code', () => {
    const discovered = scanPermissions(SCAN_DIRS);
    const missing = discovered.filter((p) => !permissions.list.includes(p));

    if (missing.length > 0) {
      // helpful output if failing
      console.error('Discovered permissions not in registry:', missing);
    }

    expect(missing).toEqual([]);
  });

  it('exports grouped map covering all permissions', () => {
    // sanity: ensure grouped has keys for each entity that appears
    const entities = new Set(permissions.list.map((p) => p.split(':')[0]));
    for (const ent of entities) {
      expect(Object.prototype.hasOwnProperty.call(permissions.grouped, ent)).toBe(true);
      expect(permissions.grouped[ent].every((p) => permissions.list.includes(p))).toBe(true);
    }
  });
});
