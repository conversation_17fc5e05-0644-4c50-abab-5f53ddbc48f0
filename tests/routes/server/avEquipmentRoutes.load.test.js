/** Smoke test: ensure avEquipmentRoutes module loads without throwing (regression for Route.get() requires a callback) */
describe('avEquipmentRoutes module', () => {
  it('loads without throwing', () => {
    expect(() => {
      // Require the router module; if middleware import is incorrect, this will throw at definition time
      require('../../../server/routes/avEquipmentRoutes');
    }).not.toThrow();
  });
});
