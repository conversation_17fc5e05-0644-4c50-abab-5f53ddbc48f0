const request = require('supertest');
const express = require('express');

// Mock User model to simulate an authenticated admin user
jest.mock('../../models/User', () => ({
  findById: jest.fn(() => ({
    populate: () => Promise.resolve({
      id: 'u1',
      roles: ['admin'],
      isActive: true,
      save: jest.fn()
    })
  }))
}));

const checkPermission = require('../../middleware/checkPermission');

describe('chat permissions - admin access', () => {
  it('allows admin user through checkPermission(messages:read)', async () => {
    const app = express();
    app.use(express.json());

    // Simulate passport authentication state
    app.use((req, res, next) => {
      req.isAuthenticated = () => true;
      req.user = { id: 'u1' };
      next();
    });

    app.get('/protected', checkPermission('messages:read'), (req, res) => {
      res.status(200).json({ success: true });
    });

    const res = await request(app).get('/protected');
    expect(res.statusCode).toBe(200);
    expect(res.body).toEqual({ success: true });
  });
});
