const request = require('supertest');
const express = require('express');

// Mock auth middleware
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => {
    req.user = { _id: 'mockUserId', name: 'Test User', email: '<EMAIL>' };
    next();
  },
  isAdmin: (req, res, next) => next()
}));

// Mock models
jest.mock('../../../models/SafetyAsset');
jest.mock('../../../models/Building');
jest.mock('../../../models/Floor');
jest.mock('../../../models/FloorplanIcon');

const SafetyAsset = require('../../../models/SafetyAsset');
const Building = require('../../../models/Building');
const Floor = require('../../../models/Floor');

// Create Express app for testing
const app = express();
app.use(express.json());
app.use('/api/safety', require('../../../server/routes/safetyRoutes'));

describe('Safety API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/safety/assets', () => {
    it('should return safety assets with pagination', async () => {
      const mockAssets = [
        {
          _id: 'asset1',
          assetType: 'fire_extinguisher',
          assetId: 'FE-001',
          name: 'Lobby Fire Extinguisher',
          buildingId: { name: 'Main Building', code: 'MAIN' },
          floorId: { name: 'First Floor', code: 'F1' },
          room: 'Lobby',
          status: 'active',
          inspection: { status: 'current' }
        },
        {
          _id: 'asset2',
          assetType: 'aed',
          assetId: 'AED-001',
          name: 'Sanctuary AED',
          buildingId: { name: 'Main Building', code: 'MAIN' },
          floorId: { name: 'First Floor', code: 'F1' },
          room: 'Sanctuary',
          status: 'active',
          inspection: { status: 'due' }
        }
      ];

      SafetyAsset.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        skip: jest.fn().mockResolvedValue(mockAssets)
      });

      SafetyAsset.countDocuments.mockResolvedValue(2);

      const res = await request(app)
        .get('/api/safety/assets')
        .expect(200);

      expect(res.body).toHaveProperty('assets');
      expect(res.body).toHaveProperty('total', 2);
      expect(res.body.assets).toHaveLength(2);
      expect(res.body.assets[0].assetType).toBe('fire_extinguisher');
    });

    it('should filter assets by type', async () => {
      SafetyAsset.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        skip: jest.fn().mockResolvedValue([])
      });

      SafetyAsset.countDocuments.mockResolvedValue(0);

      await request(app)
        .get('/api/safety/assets?assetType=aed')
        .expect(200);

      expect(SafetyAsset.find).toHaveBeenCalledWith({ assetType: 'aed' });
    });

    it('should handle search queries', async () => {
      SafetyAsset.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        skip: jest.fn().mockResolvedValue([])
      });

      SafetyAsset.countDocuments.mockResolvedValue(0);

      await request(app)
        .get('/api/safety/assets?search=fire')
        .expect(200);

      expect(SafetyAsset.find).toHaveBeenCalledWith({
        $or: [
          { name: { $regex: 'fire', $options: 'i' } },
          { assetId: { $regex: 'fire', $options: 'i' } },
          { room: { $regex: 'fire', $options: 'i' } },
          { 'specifications.manufacturer': { $regex: 'fire', $options: 'i' } },
          { 'specifications.model': { $regex: 'fire', $options: 'i' } }
        ]
      });
    });
  });

  describe('GET /api/safety/assets/:id', () => {
    it('should return a specific safety asset', async () => {
      const mockAsset = {
        _id: 'asset1',
        assetType: 'fire_extinguisher',
        name: 'Test Extinguisher'
      };

      SafetyAsset.findById.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockAsset)
      });

      const res = await request(app)
        .get('/api/safety/assets/asset1')
        .expect(200);

      expect(res.body).toMatchObject(mockAsset);
      expect(SafetyAsset.findById).toHaveBeenCalledWith('asset1');
    });

    it('should return 404 for non-existent asset', async () => {
      SafetyAsset.findById.mockReturnValue({
        populate: jest.fn().mockResolvedValue(null)
      });

      await request(app)
        .get('/api/safety/assets/nonexistent')
        .expect(404);
    });
  });

  describe('POST /api/safety/assets', () => {
    it('should create a new safety asset', async () => {
      const newAssetData = {
        assetType: 'fire_extinguisher',
        assetId: 'FE-001',
        name: 'New Fire Extinguisher',
        buildingId: 'building1',
        floorId: 'floor1',
        room: 'Test Room',
        specifications: {
          manufacturer: 'Test Manufacturer',
          capacity: '5lbs'
        },
        inspection: {
          frequency: 30
        }
      };

      const mockSavedAsset = { _id: 'newAsset', ...newAssetData };

      Building.findById.mockResolvedValue({ _id: 'building1', name: 'Test Building' });
      Floor.findById.mockResolvedValue({ _id: 'floor1', name: 'Test Floor' });
      SafetyAsset.findOne.mockResolvedValue(null); // No existing asset
      
      // Mock the SafetyAsset constructor
      SafetyAsset.mockImplementation(function(data) {
        this._id = 'newAsset';
        this.save = jest.fn().mockResolvedValue(this);
        this.populate = jest.fn().mockResolvedValue(mockSavedAsset);
        Object.assign(this, data);
        Object.assign(this, mockSavedAsset);
        return this;
      });

      const res = await request(app)
        .post('/api/safety/assets')
        .send(newAssetData)
        .expect(201);

      expect(res.body).toMatchObject(mockSavedAsset);
    });

    it('should return 400 for duplicate asset ID', async () => {
      const newAssetData = {
        assetType: 'fire_extinguisher',
        assetId: 'FE-001',
        name: 'Duplicate Asset'
      };

      Building.findById.mockResolvedValue({ _id: 'building1' });
      Floor.findById.mockResolvedValue({ _id: 'floor1' });
      SafetyAsset.findOne.mockResolvedValue({ _id: 'existing', assetId: 'FE-001' });

      await request(app)
        .post('/api/safety/assets')
        .send(newAssetData)
        .expect(400);
    });

    it('should return 400 for invalid building ID', async () => {
      const newAssetData = {
        assetType: 'fire_extinguisher',
        assetId: 'FE-001',
        buildingId: 'invalid'
      };

      Building.findById.mockResolvedValue(null);

      await request(app)
        .post('/api/safety/assets')
        .send(newAssetData)
        .expect(400);
    });
  });

  describe('PUT /api/safety/assets/:id', () => {
    it('should update an existing safety asset', async () => {
      const updateData = {
        name: 'Updated Fire Extinguisher',
        status: 'out_of_service'
      };

      const mockUpdatedAsset = {
        _id: 'asset1',
        ...updateData
      };

      SafetyAsset.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockUpdatedAsset)
      });

      const res = await request(app)
        .put('/api/safety/assets/asset1')
        .send(updateData)
        .expect(200);

      expect(res.body).toMatchObject(mockUpdatedAsset);
      expect(SafetyAsset.findByIdAndUpdate).toHaveBeenCalledWith(
        'asset1',
        expect.objectContaining({
          ...updateData,
          updatedBy: 'mockUserId'
        }),
        { new: true, runValidators: true }
      );
    });

    it('should return 404 for non-existent asset', async () => {
      SafetyAsset.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockResolvedValue(null)
      });

      await request(app)
        .put('/api/safety/assets/nonexistent')
        .send({ name: 'Updated Name' })
        .expect(404);
    });
  });

  describe('DELETE /api/safety/assets/:id', () => {
    it('should delete a safety asset', async () => {
      const mockAsset = { _id: 'asset1', name: 'Test Asset' };

      SafetyAsset.findByIdAndDelete.mockResolvedValue(mockAsset);

      await request(app)
        .delete('/api/safety/assets/asset1')
        .expect(200);

      expect(SafetyAsset.findByIdAndDelete).toHaveBeenCalledWith('asset1');
    });

    it('should return 404 for non-existent asset', async () => {
      SafetyAsset.findByIdAndDelete.mockResolvedValue(null);

      await request(app)
        .delete('/api/safety/assets/nonexistent')
        .expect(404);
    });
  });

  describe('GET /api/safety/assets/type/:type', () => {
    it('should return assets by type', async () => {
      const mockAssets = [
        { _id: 'asset1', assetType: 'aed', name: 'AED 1' },
        { _id: 'asset2', assetType: 'aed', name: 'AED 2' }
      ];

      SafetyAsset.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockAssets)
      });

      const res = await request(app)
        .get('/api/safety/assets/type/aed')
        .expect(200);

      expect(res.body).toHaveLength(2);
      expect(SafetyAsset.find).toHaveBeenCalledWith({
        assetType: 'aed',
        status: 'active'
      });
    });
  });

  describe('GET /api/safety/assets/building/:buildingId/floor/:floorId', () => {
    it('should return assets by location', async () => {
      const mockAssets = [
        { _id: 'asset1', buildingId: 'building1', floorId: 'floor1' }
      ];

      SafetyAsset.findByLocation = jest.fn().mockResolvedValue(mockAssets);

      const res = await request(app)
        .get('/api/safety/assets/building/building1/floor/floor1')
        .expect(200);

      expect(res.body).toEqual(mockAssets);
      expect(SafetyAsset.findByLocation).toHaveBeenCalledWith('building1', 'floor1');
    });
  });

  describe('GET /api/safety/inspections/due', () => {
    it('should return assets with due inspections', async () => {
      const mockAssets = [
        {
          _id: 'asset1',
          name: 'Overdue Asset',
          inspection: { status: 'overdue' }
        }
      ];

      SafetyAsset.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockAssets)
      });

      const res = await request(app)
        .get('/api/safety/inspections/due')
        .expect(200);

      expect(res.body).toEqual(mockAssets);
    });
  });

  describe('POST /api/safety/inspections/:assetId', () => {
    it('should record an inspection', async () => {
      const mockAsset = {
        _id: 'asset1',
        name: 'Test Asset',
        inspection: {
          status: 'current'
        },
        serviceHistory: [],
        recordInspection: jest.fn().mockResolvedValue(true),
        save: jest.fn().mockResolvedValue(true),
        populate: jest.fn().mockResolvedValue({
          _id: 'asset1',
          name: 'Test Asset',
          inspection: { status: 'current' }
        })
      };

      SafetyAsset.findById.mockResolvedValue(mockAsset);

      const inspectionData = {
        notes: 'Inspection completed successfully',
        status: 'current'
      };

      const res = await request(app)
        .post('/api/safety/inspections/asset1')
        .send(inspectionData)
        .expect(200);

      expect(mockAsset.recordInspection).toHaveBeenCalledWith('Test User', 'Inspection completed successfully');
    });

    it('should return 404 for non-existent asset', async () => {
      SafetyAsset.findById.mockResolvedValue(null);

      await request(app)
        .post('/api/safety/inspections/nonexistent')
        .send({ notes: 'Test' })
        .expect(404);
    });
  });

  describe('GET /api/safety/alerts', () => {
    it('should return system alerts', async () => {
      const mockAlerts = [
        {
          _id: 'asset1',
          assetId: 'FE-001',
          name: 'Test Asset',
          assetType: 'fire_extinguisher',
          alert: {
            type: 'inspection_overdue',
            severity: 'critical',
            message: 'Inspection overdue by 30 days'
          }
        }
      ];

      SafetyAsset.aggregate.mockResolvedValue(mockAlerts);

      const res = await request(app)
        .get('/api/safety/alerts')
        .expect(200);

      expect(res.body).toEqual(mockAlerts);
    });
  });

  describe('GET /api/safety/nearest', () => {
    it('should find nearest assets', async () => {
      const mockAssets = [
        {
          _id: 'asset1',
          name: 'Nearby Fire Extinguisher',
          room: 'Test Room'
        }
      ];

      SafetyAsset.find.mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockResolvedValue(mockAssets)
      });

      const res = await request(app)
        .get('/api/safety/nearest?assetType=fire_extinguisher&room=Test Room')
        .expect(200);

      expect(res.body).toEqual(mockAssets);
    });
  });

  describe('POST /api/safety/seed-demo-data', () => {
    it('should create demo safety assets', async () => {
      SafetyAsset.countDocuments.mockResolvedValue(0);
      Building.findOne.mockResolvedValue({ _id: 'building1', name: 'Test Building' });
      Floor.findOne.mockResolvedValue({ _id: 'floor1', name: 'Test Floor' });

      SafetyAsset.prototype.save.mockResolvedValue({
        _id: 'demo1',
        assetId: 'FE-001',
        name: 'Demo Fire Extinguisher',
        assetType: 'fire_extinguisher'
      });

      const res = await request(app)
        .post('/api/safety/seed-demo-data')
        .expect(200);

      expect(res.body).toHaveProperty('message');
      expect(res.body).toHaveProperty('created');
      expect(res.body.created).toBeGreaterThan(0);
    });

    it('should return 400 if demo data already exists', async () => {
      SafetyAsset.countDocuments.mockResolvedValue(5);

      await request(app)
        .post('/api/safety/seed-demo-data')
        .expect(400);
    });
  });
});