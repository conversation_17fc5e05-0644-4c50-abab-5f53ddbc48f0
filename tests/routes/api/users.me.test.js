const request = require('supertest');
const express = require('express');

// Mock auth to inject a user id for /me
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => {
    req.user = { id: '5f7d7e1c9d3e2a1b3c4d5e6f' };
    next();
  },
  hasRoles: () => (req, res, next) => next()
}));

// Mock User model for /me lookup
jest.mock('../../../models/User', () => {
  const mockUser = {
    _id: '5f7d7e1c9d3e2a1b3c4d5e6f',
    name: 'Me User',
    email: '<EMAIL>',
    roles: ['user'],
    isActive: true
  };

  return {
    findById: jest.fn().mockImplementation((id) => {
      if (id === '5f7d7e1c9d3e2a1b3c4d5e6f') {
        return { select: jest.fn().mockResolvedValue(mockUser) };
      }
      return { select: jest.fn().mockResolvedValue(null) };
    })
  };
});

const app = express();
app.use(express.json());
app.use('/api/users', require('../../../routes/api/users'));

describe('GET /api/users/me', () => {
  it('returns the current user when authenticated', async () => {
    const res = await request(app).get('/api/users/me');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('email', '<EMAIL>');
    expect(res.body).toHaveProperty('name', 'Me User');
  });
});
