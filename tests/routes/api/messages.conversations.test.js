const request = require('supertest');
const express = require('express');

// Mock auth middleware to allow requests through as authenticated
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => {
    req.user = { _id: '507f1f77bcf86cd799439011' }; // sample ObjectId-like string
    req.isAuthenticated = () => true;
    return next();
  },
  hasRoles: () => (req, res, next) => next(),
  hasPermission: () => (req, res, next) => next()
}));

// Mock the message controller so we don't hit the database
jest.mock('../../../server/controllers/messageController', () => ({
  getConversations: (req, res) => res.json({ success: true, conversations: [], page: 1, limit: 20 }),
  getOrCreateConversation: (req, res) => res.json({ success: true }),
  getConversation: (req, res) => res.json({ success: true }),
  getMessages: (req, res) => res.json({ success: true, messages: [] }),
  sendMessage: (req, res) => res.json({ success: true }),
  markAsRead: (req, res) => res.json({ success: true }),
  deleteMessage: (req, res) => res.json({ success: true }),
  addReaction: (req, res) => res.json({ success: true }),
  setTypingStatus: (req, res) => res.json({ success: true }),
  searchUsers: (req, res) => res.json({ success: true, users: [] })
}));

// Build an express app with the messages router mounted
const app = express();
app.use(express.json());
app.use('/api/messages', require('../../../server/routes/messageRoutes'));

describe('GET /api/messages/conversations', () => {
  it('returns 200 for authenticated users (no permission middleware blocking)', async () => {
    const res = await request(app).get('/api/messages/conversations?page=1&limit=20');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('success', true);
    expect(res.body).toHaveProperty('conversations');
    expect(Array.isArray(res.body.conversations)).toBe(true);
  });
});
