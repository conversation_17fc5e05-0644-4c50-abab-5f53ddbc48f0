const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Mock middleware
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => next(),
  hasRoles: () => (req, res, next) => next()
}));

// Mock User model
jest.mock('../../../models/User', () => {
  const mockUser = {
    _id: '5f7d7e1c9d3e2a1b3c4d5e6f',
    name: 'Test User',
    email: '<EMAIL>',
    roles: ['user'],
    isActive: true
  };
  
  return {
    find: jest.fn().mockReturnThis(),
    findById: jest.fn().mockImplementation((id) => {
      if (id === '5f7d7e1c9d3e2a1b3c4d5e6f') {
        return {
          select: jest.fn().mockResolvedValue(mockUser)
        };
      } else {
        return {
          select: jest.fn().mockResolvedValue(null)
        };
      }
    }),
    select: jest.fn().mockResolvedValue([mockUser])
  };
});

// Create express app with users route
const app = express();
app.use(express.json());
app.use('/api/users', require('../../../routes/api/users'));

describe('Users API Routes', () => {
  describe('GET /api/users', () => {
    it('should return all users', async () => {
      const res = await request(app).get('/api/users');
      
      expect(res.statusCode).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
      expect(res.body.length).toBeGreaterThan(0);
      expect(res.body[0]).toHaveProperty('name', 'Test User');
    });
  });

  describe('GET /api/users/:id', () => {
    it('should return a user by ID', async () => {
      const res = await request(app).get('/api/users/5f7d7e1c9d3e2a1b3c4d5e6f');
      
      expect(res.statusCode).toBe(200);
      expect(res.body).toHaveProperty('name', 'Test User');
      expect(res.body).toHaveProperty('email', '<EMAIL>');
    });

    it('should return 404 if user not found', async () => {
      const res = await request(app).get('/api/users/invalidid');
      
      expect(res.statusCode).toBe(404);
      expect(res.body).toHaveProperty('msg', 'User not found');
    });
  });
});