const request = require('supertest');
const express = require('express');

// Mock auth to inject a user
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => {
    req.isAuthenticated = () => true;
    req.user = { id: 'user1', roles: [] };
    next();
  }
}));

// Mock Team model: user is a manager of the team so canManage=true
const mockTeamDoc = {
  _id: 't1',
  name: 'Test Team',
  description: 'A team',
  leader: null,
  managers: ['user1'],
  members: []
};
jest.mock('../../../models/Team', () => ({
  findById: jest.fn(() => ({
    select: jest.fn().mockResolvedValue(mockTeamDoc)
  }))
}));

// Mock TeamHub model
const mockHub = { team: 't1', links: [], helpPages: [], save: jest.fn().mockResolvedValue(true) };
jest.mock('../../../models/TeamHub', () => ({
  findOne: jest.fn().mockResolvedValue({ ...mockHub, links: [], helpPages: [] }),
  create: jest.fn().mockResolvedValue({ ...mockHub, links: [], helpPages: [] })
}));

// Import router after mocks
const teamHubRouter = require('../../../routes/api/teamHub');

describe('Team Hub API', () => {
  let app;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    app.use('/api/team-hub', teamHubRouter);
  });

  test('GET /api/team-hub/:teamId returns hub with canManage when user is manager', async () => {
    const res = await request(app).get('/api/team-hub/t1');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('team');
    expect(res.body).toHaveProperty('hub');
    expect(res.body).toHaveProperty('canManage', true);
  });

  test('POST /api/team-hub/:teamId/links adds a link for managers', async () => {
    const body = { title: 'Docs', url: 'https://example.com/docs', description: 'Docs', pinned: true };
    const res = await request(app).post('/api/team-hub/t1/links').send(body);
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('links');
    // New link should exist
    expect(Array.isArray(res.body.links)).toBe(true);
  });
});
