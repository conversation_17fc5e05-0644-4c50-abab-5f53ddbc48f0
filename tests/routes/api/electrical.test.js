const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Router under test
const electricalRouter = require('../../../server/routes/electricalRoutes');

let mongo;
let app;

beforeAll(async () => {
  mongo = await MongoMemoryServer.create();
  const uri = mongo.getUri();
  await mongoose.connect(uri);

  app = express();
  app.use(express.json());
  app.use('/api/electrical', electricalRouter);
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongo.stop();
});

describe('Electrical API', () => {
  let panelId;
  let circuitId;
  let outletId;

  test('POST /api/electrical/panels creates a panel', async () => {
    const res = await request(app)
      .post('/api/electrical/panels')
      .send({
        buildingId: new mongoose.Types.ObjectId().toString(),
        floorId: new mongoose.Types.ObjectId().toString(),
        name: 'Panel A',
        code: 'A',
        room: 'Electrical Room'
      });
    expect(res.status).toBe(201);
    expect(res.body._id).toBeDefined();
    panelId = res.body._id;
  });

  test('POST /api/electrical/panels/:id/circuits creates a circuit', async () => {
    const res = await request(app)
      .post(`/api/electrical/panels/${panelId}/circuits`)
      .send({ number: 1, label: 'Lobby Outlets', amperage: 20 });
    expect(res.status).toBe(201);
    expect(res.body.panelId).toBe(panelId);
    circuitId = res.body._id;
  });

  test('POST /api/electrical/outlets creates an outlet linked to panel/circuit', async () => {
    const res = await request(app)
      .post('/api/electrical/outlets')
      .send({
        buildingId: new mongoose.Types.ObjectId().toString(),
        floorId: new mongoose.Types.ObjectId().toString(),
        label: 'LO-01',
        room: 'Lobby',
        panelId,
        circuitId,
        breakerNumber: 1
      });
    expect(res.status).toBe(201);
    expect(res.body.label).toBe('LO-01');
    outletId = res.body._id;
  });

  test('GET /api/electrical/trace resolves outlet to panel/circuit', async () => {
    const res = await request(app)
      .get('/api/electrical/trace')
      .query({ outletId });
    expect(res.status).toBe(200);
    expect(res.body.outlet._id).toBe(outletId);
    expect(res.body.panel._id).toBe(panelId);
    expect(res.body.circuit._id).toBe(circuitId);
    expect(Array.isArray(res.body.path)).toBe(true);
  });

  test('GET /api/electrical/outlets filters by label', async () => {
    const res = await request(app)
      .get('/api/electrical/outlets')
      .query({ label: 'LO-01' });
    expect(res.status).toBe(200);
    expect(res.body.length).toBeGreaterThanOrEqual(1);
    expect(res.body[0].label).toBe('LO-01');
  });

  test('POST /api/electrical/outlets rejects inconsistent circuit/panel', async () => {
    const otherPanelRes = await request(app)
      .post('/api/electrical/panels')
      .send({ buildingId: new mongoose.Types.ObjectId().toString(), floorId: new mongoose.Types.ObjectId().toString(), name: 'Panel B' });
    const otherPanelId = otherPanelRes.body._id;

    const res = await request(app)
      .post('/api/electrical/outlets')
      .send({ label: 'BAD-01', panelId: otherPanelId, circuitId });
    expect([422, 400]).toContain(res.status);
  });
});
