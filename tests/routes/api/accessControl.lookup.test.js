const request = require('supertest');
const express = require('express');

// Mock auth to bypass permissions for tests
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => next(),
  hasPermission: () => (req, res, next) => next()
}));

// Mock sub-controllers used by accessControlController.lookupUserByContact
jest.mock('../../../server/controllers/unifiAccessController', () => ({
  getUsers: jest.fn()
}));

jest.mock('../../../server/controllers/lenelS2NetBoxController', () => ({
  getCardholders: jest.fn()
}));

const unifiAccessController = require('../../../server/controllers/unifiAccessController');
const lenelS2NetBoxController = require('../../../server/controllers/lenelS2NetBoxController');

// Use the real routes (wire up to controller under test)
const accessControlRoutes = require('../../../routes/api/accessControl');

const makeApp = () => {
  const app = express();
  app.use(express.json());
  app.use('/api/access-control', accessControlRoutes);
  return app;
};

describe('GET /api/access-control/users/lookup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('matches user by email across UniFi and Lenel and returns combined data', async () => {
    unifiAccessController.getUsers.mockResolvedValue([
      { id: 'u1', email: '<EMAIL>', phone: '(*************', firstName: 'Alice', lastName: 'U' }
    ]);
    lenelS2NetBoxController.getCardholders.mockResolvedValue([
      { id: 'l1', email: '<EMAIL>', phone: '******-111-2222', firstName: 'Alice', lastName: 'L', cards: [{ id: 'c1', number: '1234' }] }
    ]);

    const app = makeApp();
    const res = await request(app)
      .get('/api/access-control/users/lookup')
      .query({ email: '<EMAIL>' });

    expect(res.statusCode).toBe(200);
    expect(res.body).toBeDefined();
    expect(res.body.systems).toEqual(expect.arrayContaining(['unifi-access', 'lenel-s2-netbox']));
    expect(res.body.unifiAccessId).toBe('u1');
    expect(res.body.lenelS2NetBoxId).toBe('l1');
    expect(res.body.id).toBe('u1|l1');
    expect(res.body.email.toLowerCase()).toBe('<EMAIL>');
    expect(res.body.cards).toBeDefined();
  });

  it('matches user by phone across UniFi and Lenel when email missing', async () => {
    unifiAccessController.getUsers.mockResolvedValue([
      { id: 'u2', email: '', phone: '5551112222', firstName: 'Bob', lastName: 'U' }
    ]);
    lenelS2NetBoxController.getCardholders.mockResolvedValue([
      { id: 'l2', email: '', phone: '+1 (*************', firstName: 'Bob', lastName: 'L' }
    ]);

    const app = makeApp();
    const res = await request(app)
      .get('/api/access-control/users/lookup')
      .query({ phone: '******-111-2222' });

    expect(res.statusCode).toBe(200);
    expect(res.body.systems).toEqual(expect.arrayContaining(['unifi-access', 'lenel-s2-netbox']));
    expect(res.body.unifiAccessId).toBe('u2');
    expect(res.body.lenelS2NetBoxId).toBe('l2');
  });

  it('returns 404 when no match is found', async () => {
    unifiAccessController.getUsers.mockResolvedValue([
      { id: 'u3', email: '<EMAIL>', phone: '5550000000', firstName: 'Carol' }
    ]);
    lenelS2NetBoxController.getCardholders.mockResolvedValue([
      { id: 'l3', email: '<EMAIL>', phone: '5559999999', firstName: 'Other' }
    ]);

    const app = makeApp();
    const res = await request(app)
      .get('/api/access-control/users/lookup')
      .query({ email: '<EMAIL>' });

    expect(res.statusCode).toBe(404);
    expect(res.body.error).toBeDefined();
  });
});
