const request = require('supertest');
const express = require('express');

// Mock auth middleware to bypass authentication/authorization
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => next(),
  hasRoles: () => (req, res, next) => next()
}));

// Mock the MenuItem model to control sync behavior
const mockCreated = [{ path: '/new-item-1' }, { path: '/new-item-2' }];
jest.mock('../../../models/MenuItem', () => ({
  syncMissingDefaultMenuItems: jest.fn().mockResolvedValue(mockCreated)
}));

// Import router after mocks so it uses the mocked modules
const menuItemsRouter = require('../../../routes/api/menuItems');

describe('Menu Items Sync API', () => {
  let app;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    app.use('/api/menu-items', menuItemsRouter);
  });

  test('POST /api/menu-items/sync returns createdCount and created array', async () => {
    const res = await request(app).post('/api/menu-items/sync');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('createdCount', mockCreated.length);
    expect(Array.isArray(res.body.created)).toBe(true);
    expect(res.body.created).toHaveLength(mockCreated.length);
  });
});
