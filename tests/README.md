# CSF Portal Testing Framework

This directory contains the testing framework and tests for the CSF Portal application.

## Overview

The testing framework uses:
- **Jest**: JavaScript testing framework
- **Supertest**: HTTP assertions library for testing API endpoints
- **mongodb-memory-server**: In-memory MongoDB server for integration tests

## Directory Structure

```
tests/
├── README.md                 # This file
├── setup.test.js             # Basic tests to verify the testing framework
├── routes/                   # Tests for API routes
│   └── api/
│       └── users.test.js     # Tests for user API endpoints
└── ... (other test files)
```

## Writing Tests

### Unit Tests

For simple unit tests, use Jest's standard testing functions:

```javascript
describe('My Function', () => {
  it('should do something', () => {
    expect(myFunction()).toBe(expectedResult);
  });
});
```

### API Tests

For testing API endpoints, use Supertest:

```javascript
const request = require('supertest');
const app = require('../path/to/app');

describe('API Endpoint', () => {
  it('should return expected data', async () => {
    const res = await request(app).get('/api/endpoint');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('expectedProperty');
  });
});
```

### Database Tests

For tests that require database interaction, use mongodb-memory-server:

```javascript
const { MongoMemoryServer } = require('mongodb-memory-server');
const mongoose = require('mongoose');

let mongoServer;

beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  await mongoose.connect(mongoServer.getUri());
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

// Your tests here
```

## Running Tests

To run all tests:

```
npm test
```

To run tests in watch mode (automatically re-run when files change):

```
npm run test:watch
```

To run a specific test file:

```
npm test -- path/to/test/file.js
```

## Best Practices

1. **Isolation**: Each test should be independent and not rely on the state from other tests.
2. **Mocking**: Use Jest's mocking capabilities to isolate the code being tested.
3. **Coverage**: Aim for high test coverage, especially for critical functionality.
4. **Descriptive Names**: Use descriptive test names that explain what is being tested.
5. **Setup and Teardown**: Use `beforeEach`, `afterEach`, `beforeAll`, and `afterAll` for setup and teardown.