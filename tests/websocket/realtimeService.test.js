const realtimeService = require('../../server/services/realtimeService');

// Mock the websocket server
const mockWebsocketServer = {
  broadcast: jest.fn(),
  getStats: jest.fn().mockReturnValue({
    connectedClients: 0,
    subscriptions: {},
    totalSubscriptions: 0
  })
};

// Mock the Lenel S2 NetBox API
const mockLenelS2NetBoxAPI = {
  getLiveActivityLog: jest.fn(),
  getEvacuationStatus: jest.fn(),
  getElevators: jest.fn(),
  getElevatorStatus: jest.fn(),
  initialize: jest.fn().mockResolvedValue(undefined)
};

// Mock the modules
jest.mock('../../server/websocket/websocketServer', () => mockWebsocketServer);
jest.mock('../../server/integrations/lenelS2NetBox/lenelS2NetBoxAPI', () => mockLenelS2NetBoxAPI);

describe('Realtime Service', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Reset service state
    realtimeService.stop();
    realtimeService.lastActivityCheck = null;
    realtimeService.lastEvacuationCheck = null;
    realtimeService.lastElevatorCheck = null;
  });

  afterEach(() => {
    realtimeService.stop();
  });

  describe('Service Lifecycle', () => {
    it('should start the service', async () => {
      expect(realtimeService.isRunning).toBe(false);
      
      await realtimeService.start();
      
      expect(realtimeService.isRunning).toBe(true);
    });

    it('should not start if already running', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      await realtimeService.start();
      await realtimeService.start(); // Try to start again
      
      expect(consoleSpy).toHaveBeenCalledWith('Realtime service is already running');
      consoleSpy.mockRestore();
    });

    it('should stop the service', () => {
      realtimeService.start();
      expect(realtimeService.isRunning).toBe(true);
      
      realtimeService.stop();
      
      expect(realtimeService.isRunning).toBe(false);
    });

    it('should provide service status', () => {
      const status = realtimeService.getStatus();
      
      expect(status).toHaveProperty('isRunning');
      expect(status).toHaveProperty('activeMonitors');
      expect(status).toHaveProperty('lastChecks');
      expect(status).toHaveProperty('websocketStats');
      
      expect(Array.isArray(status.activeMonitors)).toBe(true);
      expect(typeof status.lastChecks).toBe('object');
    });
  });

  describe('Activity Log Monitoring', () => {
    beforeEach(async () => {
      await realtimeService.start();
    });

    it('should fetch and broadcast new activity log entries', async () => {
      const mockActivity = [
        { id: 1, message: 'Test activity 1', timestamp: new Date().toISOString() },
        { id: 2, message: 'Test activity 2', timestamp: new Date().toISOString() }
      ];
      
      mockLenelS2NetBoxAPI.getLiveActivityLog.mockResolvedValue(mockActivity);
      
      // Manually trigger activity log check
      await realtimeService.checkActivityLog();
      
      expect(mockLenelS2NetBoxAPI.getLiveActivityLog).toHaveBeenCalled();
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('activity-log', mockActivity);
    });

    it('should handle activity log API errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      mockLenelS2NetBoxAPI.getLiveActivityLog.mockRejectedValue(new Error('API Error'));
      
      await realtimeService.checkActivityLog();
      
      expect(consoleSpy).toHaveBeenCalledWith('Error checking activity log:', expect.any(Error));
      consoleSpy.mockRestore();
    });

    it('should filter out already seen activities', async () => {
      const oldActivity = { id: 1, message: 'Old activity', timestamp: '2023-01-01T00:00:00Z' };
      const newActivity = { id: 2, message: 'New activity', timestamp: new Date().toISOString() };
      
      // Set last check time
      realtimeService.lastActivityCheck = '2023-01-01T12:00:00Z';
      
      mockLenelS2NetBoxAPI.getLiveActivityLog.mockResolvedValue([oldActivity, newActivity]);
      
      await realtimeService.checkActivityLog();
      
      // Should only broadcast the new activity
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('activity-log', [newActivity]);
    });

    it('should manually broadcast activity log', () => {
      const activities = [{ id: 1, message: 'Manual activity' }];
      
      realtimeService.broadcastActivityLog(activities);
      
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('activity-log', activities);
    });
  });

  describe('Evacuation Status Monitoring', () => {
    beforeEach(async () => {
      await realtimeService.start();
    });

    it('should fetch and broadcast evacuation status changes', async () => {
      const mockEvacuationStatus = {
        isEvacuating: true,
        zones: ['Zone A', 'Zone B'],
        timestamp: new Date().toISOString()
      };
      
      mockLenelS2NetBoxAPI.getEvacuationStatus.mockResolvedValue(mockEvacuationStatus);
      
      // First check - should not broadcast (no previous state)
      await realtimeService.checkEvacuationStatus();
      expect(mockWebsocketServer.broadcast).not.toHaveBeenCalled();
      
      // Second check with different status - should broadcast
      const updatedStatus = { ...mockEvacuationStatus, isEvacuating: false };
      mockLenelS2NetBoxAPI.getEvacuationStatus.mockResolvedValue(updatedStatus);
      
      await realtimeService.checkEvacuationStatus();
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('evacuation-status', updatedStatus);
    });

    it('should handle evacuation status API errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      mockLenelS2NetBoxAPI.getEvacuationStatus.mockRejectedValue(new Error('API Error'));
      
      await realtimeService.checkEvacuationStatus();
      
      expect(consoleSpy).toHaveBeenCalledWith('Error checking evacuation status:', expect.any(Error));
      consoleSpy.mockRestore();
    });

    it('should manually broadcast evacuation status', () => {
      const evacuationStatus = { isEvacuating: true, zones: ['Zone A'] };
      
      realtimeService.broadcastEvacuationStatus(evacuationStatus);
      
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('evacuation-status', evacuationStatus);
    });
  });

  describe('Elevator Status Monitoring', () => {
    beforeEach(async () => {
      await realtimeService.start();
    });

    it('should fetch and broadcast elevator status changes', async () => {
      const mockElevators = [
        { id: 1, name: 'Elevator 1' },
        { id: 2, name: 'Elevator 2' }
      ];
      
      const mockElevatorStatuses = [
        { elevatorId: 1, status: 'operational', floor: 3 },
        { elevatorId: 2, status: 'maintenance', floor: 1 }
      ];
      
      mockLenelS2NetBoxAPI.getElevators.mockResolvedValue(mockElevators);
      mockLenelS2NetBoxAPI.getElevatorStatus
        .mockResolvedValueOnce(mockElevatorStatuses[0])
        .mockResolvedValueOnce(mockElevatorStatuses[1]);
      
      // First check - should not broadcast (no previous state)
      await realtimeService.checkElevatorStatus();
      expect(mockWebsocketServer.broadcast).not.toHaveBeenCalled();
      
      // Second check with different status - should broadcast
      const updatedStatuses = [
        { elevatorId: 1, status: 'out-of-service', floor: 3 },
        { elevatorId: 2, status: 'maintenance', floor: 1 }
      ];
      
      mockLenelS2NetBoxAPI.getElevatorStatus
        .mockResolvedValueOnce(updatedStatuses[0])
        .mockResolvedValueOnce(updatedStatuses[1]);
      
      await realtimeService.checkElevatorStatus();
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('elevator-status', updatedStatuses);
    });

    it('should handle elevator API errors', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      mockLenelS2NetBoxAPI.getElevators.mockRejectedValue(new Error('API Error'));
      
      await realtimeService.checkElevatorStatus();
      
      expect(consoleSpy).toHaveBeenCalledWith('Error checking elevator status:', expect.any(Error));
      consoleSpy.mockRestore();
    });

    it('should filter out null elevator statuses', async () => {
      const mockElevators = [
        { id: 1, name: 'Elevator 1' },
        { id: 2, name: 'Elevator 2' }
      ];
      
      mockLenelS2NetBoxAPI.getElevators.mockResolvedValue(mockElevators);
      mockLenelS2NetBoxAPI.getElevatorStatus
        .mockResolvedValueOnce({ elevatorId: 1, status: 'operational' })
        .mockResolvedValueOnce(null); // This should be filtered out
      
      await realtimeService.checkElevatorStatus();
      
      // Should store only the valid status
      expect(realtimeService.lastElevatorCheck).toEqual([
        { elevatorId: 1, status: 'operational' }
      ]);
    });

    it('should manually broadcast elevator status', () => {
      const elevatorStatuses = [{ elevatorId: 1, status: 'operational' }];
      
      realtimeService.broadcastElevatorStatus(elevatorStatuses);
      
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('elevator-status', elevatorStatuses);
    });
  });

  describe('Card Status Broadcasting', () => {
    it('should broadcast card status changes', () => {
      const cardStatusChange = {
        cardId: '12345',
        status: 'activated',
        timestamp: new Date().toISOString()
      };
      
      realtimeService.broadcastCardStatusChange(cardStatusChange);
      
      expect(mockWebsocketServer.broadcast).toHaveBeenCalledWith('card-status-change', cardStatusChange);
    });
  });
});
