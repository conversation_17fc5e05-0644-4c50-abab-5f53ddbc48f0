/**
 * End-to-end integration tests for WebSocket functionality
 * Tests the complete flow from server to client
 */

const WebSocket = require('ws');
const http = require('http');
const websocketServer = require('../../server/websocket/websocketServer');
const realtimeService = require('../../server/services/realtimeService');

describe('WebSocket Integration Tests', () => {
  let server;
  let httpServer;
  let testClient1;
  let testClient2;
  let serverPort;

  beforeAll(async () => {
    // Create HTTP server for testing
    httpServer = http.createServer();
    websocketServer.initialize(httpServer);
    
    // Start server on random port
    await new Promise((resolve) => {
      httpServer.listen(0, () => {
        server = httpServer;
        serverPort = server.address().port;
        resolve();
      });
    });
  });

  afterAll(async () => {
    // Clean up
    if (testClient1 && testClient1.readyState === WebSocket.OPEN) {
      testClient1.close();
    }
    if (testClient2 && testClient2.readyState === WebSocket.OPEN) {
      testClient2.close();
    }

    // Wait a bit for connections to close
    await new Promise(resolve => setTimeout(resolve, 100));

    realtimeService.stop();
    websocketServer.shutdown();

    if (server) {
      await new Promise((resolve) => {
        server.close(resolve);
      });
    }
  }, 10000); // Increase timeout to 10 seconds

  beforeEach(() => {
    // Clear any existing clients and subscriptions
    websocketServer.clients.clear();
    websocketServer.subscriptions.clear();
  });

  describe('Multi-Client Communication', () => {
    it('should handle multiple clients subscribing to same event', (done) => {
      let client1Connected = false;
      let client2Connected = false;
      let client1Subscribed = false;
      let client2Subscribed = false;
      let client1ReceivedEvent = false;
      let client2ReceivedEvent = false;

      // Connect first client
      testClient1 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient1.on('open', () => {
        client1Connected = true;
        testClient1.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
      });

      testClient1.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribed' && message.eventType === 'activity-log') {
          client1Subscribed = true;
          checkAndProceed();
        } else if (message.type === 'event' && message.eventType === 'activity-log') {
          client1ReceivedEvent = true;
          checkCompletion();
        }
      });

      // Connect second client
      testClient2 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient2.on('open', () => {
        client2Connected = true;
        testClient2.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
      });

      testClient2.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribed' && message.eventType === 'activity-log') {
          client2Subscribed = true;
          checkAndProceed();
        } else if (message.type === 'event' && message.eventType === 'activity-log') {
          client2ReceivedEvent = true;
          checkCompletion();
        }
      });

      function checkAndProceed() {
        if (client1Connected && client2Connected && client1Subscribed && client2Subscribed) {
          // Both clients are subscribed, now broadcast an event
          setTimeout(() => {
            websocketServer.broadcast('activity-log', {
              message: 'Test activity for multiple clients',
              timestamp: new Date().toISOString()
            });
          }, 100);
        }
      }

      function checkCompletion() {
        if (client1ReceivedEvent && client2ReceivedEvent) {
          done();
        }
      }

      testClient1.on('error', done);
      testClient2.on('error', done);
    });

    it('should handle selective broadcasting based on subscriptions', (done) => {
      let client1Ready = false;
      let client2Ready = false;
      let client1ReceivedActivity = false;
      let client2ReceivedEvacuation = false;
      let client1ReceivedEvacuation = false; // Should not happen
      let client2ReceivedActivity = false; // Should not happen

      // Client 1 subscribes to activity-log
      testClient1 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient1.on('open', () => {
        testClient1.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
      });

      testClient1.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribed') {
          client1Ready = true;
          checkAndProceed();
        } else if (message.type === 'event') {
          if (message.eventType === 'activity-log') {
            client1ReceivedActivity = true;
          } else if (message.eventType === 'evacuation-status') {
            client1ReceivedEvacuation = true;
          }
          checkCompletion();
        }
      });

      // Client 2 subscribes to evacuation-status
      testClient2 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient2.on('open', () => {
        testClient2.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'evacuation-status'
        }));
      });

      testClient2.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribed') {
          client2Ready = true;
          checkAndProceed();
        } else if (message.type === 'event') {
          if (message.eventType === 'evacuation-status') {
            client2ReceivedEvacuation = true;
          } else if (message.eventType === 'activity-log') {
            client2ReceivedActivity = true;
          }
          checkCompletion();
        }
      });

      function checkAndProceed() {
        if (client1Ready && client2Ready) {
          // Broadcast both events
          setTimeout(() => {
            websocketServer.broadcast('activity-log', { message: 'Activity event' });
            websocketServer.broadcast('evacuation-status', { status: 'Evacuation event' });
          }, 100);
        }
      }

      function checkCompletion() {
        if (client1ReceivedActivity && client2ReceivedEvacuation) {
          // Verify selective delivery
          expect(client1ReceivedEvacuation).toBe(false);
          expect(client2ReceivedActivity).toBe(false);
          done();
        }
      }

      testClient1.on('error', done);
      testClient2.on('error', done);
    });
  });

  describe('Real-time Service Integration', () => {
    it('should integrate with realtime service broadcasting', (done) => {
      testClient1 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient1.on('open', () => {
        testClient1.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
      });

      testClient1.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribed') {
          // Use realtime service to broadcast
          setTimeout(() => {
            realtimeService.broadcastActivityLog([{
              id: 1,
              message: 'Test activity from realtime service',
              timestamp: new Date().toISOString()
            }]);
          }, 100);
        } else if (message.type === 'event' && message.eventType === 'activity-log') {
          expect(message.payload).toEqual([{
            id: 1,
            message: 'Test activity from realtime service',
            timestamp: expect.any(String)
          }]);
          done();
        }
      });

      testClient1.on('error', done);
    });
  });

  describe('Connection Health and Recovery', () => {
    it('should handle client disconnection gracefully', (done) => {
      testClient1 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient1.on('open', () => {
        // Subscribe to an event
        testClient1.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
      });

      testClient1.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribed') {
          expect(websocketServer.clients.size).toBe(1);
          expect(websocketServer.subscriptions.get('activity-log').size).toBe(1);
          
          // Close the connection
          testClient1.close();
        }
      });

      testClient1.on('close', () => {
        // Give some time for cleanup
        setTimeout(() => {
          expect(websocketServer.clients.size).toBe(0);
          expect(websocketServer.subscriptions.has('activity-log')).toBe(false);
          done();
        }, 100);
      });

      testClient1.on('error', done);
    });

    it('should handle ping/pong for connection health', (done) => {
      testClient1 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient1.on('open', () => {
        testClient1.send(JSON.stringify({ type: 'ping' }));
      });

      testClient1.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'pong') {
          done();
        }
      });

      testClient1.on('error', done);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed messages gracefully', (done) => {
      testClient1 = new WebSocket(`ws://localhost:${serverPort}/ws`);
      
      testClient1.on('open', () => {
        testClient1.send('invalid json message');
      });

      testClient1.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'error' && message.message === 'Invalid JSON message') {
          done();
        }
      });

      testClient1.on('error', done);
    });

    it('should provide server statistics', () => {
      const stats = websocketServer.getStats();
      
      expect(stats).toHaveProperty('connectedClients');
      expect(stats).toHaveProperty('subscriptions');
      expect(stats).toHaveProperty('totalSubscriptions');
      expect(typeof stats.connectedClients).toBe('number');
      expect(typeof stats.subscriptions).toBe('object');
      expect(typeof stats.totalSubscriptions).toBe('number');
    });
  });
});
