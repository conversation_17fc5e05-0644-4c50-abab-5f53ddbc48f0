/**
 * Tests for client-side WebSocket service
 * Note: These tests use jsdom to simulate browser environment
 */

// Mock WebSocket for Node.js environment
global.WebSocket = require('ws');

// Mock window object for browser-specific code
global.window = {
  location: {
    protocol: 'http:',
    host: 'localhost:3000'
  }
};

// Mock the ES6 module since we can't directly require it in Node.js
class MockWebSocketService {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
    this.listeners = new Map();
    this.isConnecting = false;
  }

  connect(url = null) {
    return new Promise((resolve, reject) => {
      if (this.socket && this.socket.readyState === 1) { // OPEN
        resolve();
        return;
      }

      if (this.isConnecting) {
        return;
      }

      this.isConnecting = true;

      const wsUrl = url || `${global.window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${global.window.location.host}/ws`;

      try {
        this.socket = new global.WebSocket(wsUrl);

        this.socket.onopen = () => {
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          resolve();
        };

        this.socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.socket.onclose = () => {
          this.isConnecting = false;
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            setTimeout(() => {
              this.reconnectAttempts++;
              this.connect(url);
            }, this.reconnectInterval);
          }
        };

        this.socket.onerror = (error) => {
          this.isConnecting = false;
          reject(error);
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  handleMessage(data) {
    const { type, eventType, payload } = data;

    if (type === 'event' && eventType) {
      if (this.listeners.has(eventType)) {
        this.listeners.get(eventType).forEach(callback => {
          try {
            callback(payload);
          } catch (error) {
            console.error('Error in WebSocket event listener:', error);
          }
        });
      }
    }
  }

  subscribe(eventType) {
    if (this.socket && this.socket.readyState === 1) {
      this.socket.send(JSON.stringify({
        type: 'subscribe',
        eventType: eventType
      }));
    }
  }

  unsubscribe(eventType) {
    if (this.socket && this.socket.readyState === 1) {
      this.socket.send(JSON.stringify({
        type: 'unsubscribe',
        eventType: eventType
      }));
    }
  }

  on(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);
  }

  off(eventType, callback) {
    if (callback) {
      if (this.listeners.has(eventType)) {
        this.listeners.get(eventType).delete(callback);
      }
    } else {
      this.listeners.delete(eventType);
    }
  }

  send(message) {
    if (this.socket && this.socket.readyState === 1) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  isConnected() {
    return this.socket && this.socket.readyState === 1;
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
}

const WebSocketService = MockWebSocketService;

describe('WebSocket Service (Client)', () => {
  let websocketService;
  let mockWebSocket;

  beforeEach(() => {
    websocketService = new WebSocketService();
    
    // Mock WebSocket constructor
    const originalWebSocket = global.WebSocket;
    global.WebSocket = jest.fn().mockImplementation((url) => {
      mockWebSocket = {
        url,
        readyState: 0, // CONNECTING
        onopen: null,
        onmessage: null,
        onclose: null,
        onerror: null,
        send: jest.fn(),
        close: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
      };
      
      // Simulate connection opening after a short delay
      setTimeout(() => {
        mockWebSocket.readyState = 1; // OPEN
        if (mockWebSocket.onopen) {
          mockWebSocket.onopen();
        }
      }, 10);
      
      return mockWebSocket;
    });
    
    // Store original for cleanup
    global.WebSocket.CONNECTING = 0;
    global.WebSocket.OPEN = 1;
    global.WebSocket.CLOSING = 2;
    global.WebSocket.CLOSED = 3;
  });

  afterEach(() => {
    if (websocketService) {
      websocketService.disconnect();
    }
  });

  describe('Connection Management', () => {
    it('should connect to WebSocket server', async () => {
      const connectPromise = websocketService.connect('ws://localhost:3000/ws');
      
      await expect(connectPromise).resolves.toBeUndefined();
      expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:3000/ws');
    });

    it('should construct URL from window location if no URL provided', async () => {
      await websocketService.connect();
      
      expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:3000/ws');
    });

    it('should handle connection errors', (done) => {
      websocketService.connect('ws://localhost:3000/ws');
      
      // Simulate connection error
      setTimeout(() => {
        if (mockWebSocket.onerror) {
          mockWebSocket.onerror(new Error('Connection failed'));
        }
      }, 20);
      
      // Check that reconnection is attempted
      setTimeout(() => {
        expect(websocketService.reconnectAttempts).toBeGreaterThan(0);
        done();
      }, 100);
    });

    it('should not connect if already connecting', async () => {
      websocketService.isConnecting = true;
      
      const result = await websocketService.connect();
      expect(result).toBeUndefined();
      expect(global.WebSocket).not.toHaveBeenCalled();
    });

    it('should not connect if already connected', async () => {
      // Mock already connected socket
      websocketService.socket = { readyState: 1 }; // OPEN
      
      await websocketService.connect();
      expect(global.WebSocket).not.toHaveBeenCalled();
    });
  });

  describe('Message Handling', () => {
    beforeEach(async () => {
      await websocketService.connect('ws://localhost:3000/ws');
    });

    it('should handle incoming messages', () => {
      const testPayload = { data: 'test' };
      const testMessage = {
        type: 'event',
        eventType: 'activity-log',
        payload: testPayload
      };
      
      const listener = jest.fn();
      websocketService.on('activity-log', listener);
      
      // Simulate incoming message
      if (mockWebSocket.onmessage) {
        mockWebSocket.onmessage({
          data: JSON.stringify(testMessage)
        });
      }
      
      expect(listener).toHaveBeenCalledWith(testPayload);
    });

    it('should handle invalid JSON messages gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Simulate invalid JSON message
      if (mockWebSocket.onmessage) {
        mockWebSocket.onmessage({
          data: 'invalid json'
        });
      }
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error parsing WebSocket message:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });

    it('should handle messages without event type', () => {
      const testMessage = {
        type: 'welcome',
        clientId: 'test-client-id'
      };
      
      const listener = jest.fn();
      websocketService.on('activity-log', listener);
      
      // Simulate incoming message
      if (mockWebSocket.onmessage) {
        mockWebSocket.onmessage({
          data: JSON.stringify(testMessage)
        });
      }
      
      // Listener should not be called for non-event messages
      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('Event Subscription', () => {
    beforeEach(async () => {
      await websocketService.connect('ws://localhost:3000/ws');
    });

    it('should subscribe to events', () => {
      websocketService.subscribe('activity-log');
      
      expect(mockWebSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        })
      );
    });

    it('should unsubscribe from events', () => {
      websocketService.unsubscribe('activity-log');
      
      expect(mockWebSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'unsubscribe',
          eventType: 'activity-log'
        })
      );
    });

    it('should add event listeners', () => {
      const listener = jest.fn();
      websocketService.on('activity-log', listener);
      
      expect(websocketService.listeners.has('activity-log')).toBe(true);
      expect(websocketService.listeners.get('activity-log').has(listener)).toBe(true);
    });

    it('should remove event listeners', () => {
      const listener = jest.fn();
      websocketService.on('activity-log', listener);
      websocketService.off('activity-log', listener);
      
      expect(websocketService.listeners.get('activity-log').has(listener)).toBe(false);
    });

    it('should remove all listeners for an event type', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      
      websocketService.on('activity-log', listener1);
      websocketService.on('activity-log', listener2);
      websocketService.off('activity-log');
      
      expect(websocketService.listeners.has('activity-log')).toBe(false);
    });
  });

  describe('Connection State', () => {
    it('should report correct connection state', () => {
      expect(websocketService.isConnected()).toBe(false);
      
      websocketService.socket = { readyState: 1 }; // OPEN
      expect(websocketService.isConnected()).toBe(true);
      
      websocketService.socket = { readyState: 3 }; // CLOSED
      expect(websocketService.isConnected()).toBe(false);
    });

    it('should handle disconnection', () => {
      websocketService.socket = { 
        readyState: 1,
        close: jest.fn()
      };
      
      websocketService.disconnect();
      
      expect(websocketService.socket.close).toHaveBeenCalled();
      expect(websocketService.socket).toBe(null);
    });
  });

  describe('Error Handling', () => {
    it('should handle listener errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const faultyListener = jest.fn().mockImplementation(() => {
        throw new Error('Listener error');
      });
      
      websocketService.on('activity-log', faultyListener);
      
      // Simulate incoming message
      const testMessage = {
        type: 'event',
        eventType: 'activity-log',
        payload: { data: 'test' }
      };
      
      websocketService.handleMessage(testMessage);
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error in WebSocket event listener:',
        expect.any(Error)
      );
      
      consoleSpy.mockRestore();
    });

    it('should warn when sending message without connection', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      websocketService.send({ type: 'test' });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        'WebSocket not connected, cannot send message'
      );
      
      consoleSpy.mockRestore();
    });
  });
});
