const WebSocket = require('ws');
const http = require('http');
const websocketServer = require('../../server/websocket/websocketServer');

describe('WebSocket Server', () => {
  let server;
  let httpServer;
  let testClient;
  let testClient2;

  beforeAll(async () => {
    // Create HTTP server for testing
    httpServer = http.createServer();
    websocketServer.initialize(httpServer);
    
    // Start server on random port
    await new Promise((resolve) => {
      httpServer.listen(0, () => {
        server = httpServer;
        resolve();
      });
    });
  });

  afterAll(async () => {
    // Clean up
    if (testClient && testClient.readyState === WebSocket.OPEN) {
      testClient.close();
    }
    if (testClient2 && testClient2.readyState === WebSocket.OPEN) {
      testClient2.close();
    }

    // Wait a bit for connections to close
    await new Promise(resolve => setTimeout(resolve, 100));

    websocketServer.shutdown();

    if (server) {
      await new Promise((resolve) => {
        server.close(resolve);
      });
    }
  }, 10000); // Increase timeout to 10 seconds

  beforeEach(() => {
    // Clear any existing clients
    websocketServer.clients.clear();
    websocketServer.subscriptions.clear();
  });

  describe('Connection Management', () => {
    it('should accept WebSocket connections', (done) => {
      const port = server.address().port;
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      
      testClient.on('open', () => {
        expect(websocketServer.clients.size).toBe(1);
        done();
      });
      
      testClient.on('error', done);
    });

    it('should send welcome message on connection', (done) => {
      const port = server.address().port;
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      
      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        expect(message.type).toBe('welcome');
        expect(message.clientId).toBeDefined();
        expect(message.timestamp).toBeDefined();
        done();
      });
      
      testClient.on('error', done);
    });

    it('should handle client disconnection', (done) => {
      const port = server.address().port;
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      
      testClient.on('open', () => {
        expect(websocketServer.clients.size).toBe(1);
        testClient.close();
      });
      
      testClient.on('close', () => {
        // Give some time for cleanup
        setTimeout(() => {
          expect(websocketServer.clients.size).toBe(0);
          done();
        }, 100);
      });
      
      testClient.on('error', done);
    });

    it('should handle multiple concurrent connections', (done) => {
      const port = server.address().port;
      let connectionsOpened = 0;
      
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      testClient2 = new WebSocket(`ws://localhost:${port}/ws`);
      
      const checkConnections = () => {
        connectionsOpened++;
        if (connectionsOpened === 2) {
          expect(websocketServer.clients.size).toBe(2);
          done();
        }
      };
      
      testClient.on('open', checkConnections);
      testClient2.on('open', checkConnections);
      
      testClient.on('error', done);
      testClient2.on('error', done);
    });
  });

  describe('Message Handling', () => {
    beforeEach((done) => {
      const port = server.address().port;
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      testClient.on('open', () => done());
      testClient.on('error', done);
    });

    it('should handle ping messages', (done) => {
      testClient.send(JSON.stringify({ type: 'ping' }));
      
      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'pong') {
          done();
        }
      });
    });

    it('should handle invalid JSON messages', (done) => {
      testClient.send('invalid json');
      
      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'error' && message.message === 'Invalid JSON message') {
          done();
        }
      });
    });

    it('should handle unknown message types', (done) => {
      testClient.send(JSON.stringify({ type: 'unknown' }));

      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'error' && message.message.includes('Unknown message type')) {
          done();
        }
      });
    });
  });

  describe('Subscription Management', () => {
    beforeEach((done) => {
      const port = server.address().port;
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      testClient.on('open', () => done());
      testClient.on('error', done);
    });

    it('should handle subscription requests', (done) => {
      testClient.send(JSON.stringify({
        type: 'subscribe',
        eventType: 'activity-log'
      }));

      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'subscribed' && message.eventType === 'activity-log') {
          expect(websocketServer.subscriptions.has('activity-log')).toBe(true);
          done();
        }
      });
    });

    it('should handle unsubscription requests', (done) => {
      // First subscribe
      testClient.send(JSON.stringify({
        type: 'subscribe',
        eventType: 'activity-log'
      }));

      let subscribed = false;
      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());

        if (message.type === 'subscribed' && !subscribed) {
          subscribed = true;
          // Now unsubscribe
          testClient.send(JSON.stringify({
            type: 'unsubscribe',
            eventType: 'activity-log'
          }));
        } else if (message.type === 'unsubscribed' && message.eventType === 'activity-log') {
          expect(websocketServer.subscriptions.has('activity-log')).toBe(false);
          done();
        }
      });
    });

    it('should handle multiple subscriptions from same client', (done) => {
      let subscriptionCount = 0;
      const expectedSubscriptions = ['activity-log', 'evacuation-status'];

      testClient.send(JSON.stringify({
        type: 'subscribe',
        eventType: 'activity-log'
      }));

      testClient.send(JSON.stringify({
        type: 'subscribe',
        eventType: 'evacuation-status'
      }));

      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'subscribed') {
          subscriptionCount++;
          expect(expectedSubscriptions).toContain(message.eventType);

          if (subscriptionCount === 2) {
            expect(websocketServer.subscriptions.size).toBe(2);
            done();
          }
        }
      });
    });
  });

  describe('Broadcasting', () => {
    beforeEach((done) => {
      const port = server.address().port;
      testClient = new WebSocket(`ws://localhost:${port}/ws`);
      testClient.on('open', () => {
        // Subscribe to activity-log events
        testClient.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'activity-log'
        }));
        done();
      });
      testClient.on('error', done);
    });

    it('should broadcast events to subscribed clients', (done) => {
      const testPayload = { message: 'test activity', timestamp: new Date().toISOString() };

      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'event' && message.eventType === 'activity-log') {
          expect(message.payload).toEqual(testPayload);
          expect(message.timestamp).toBeDefined();
          done();
        }
      });

      // Wait a bit for subscription to be processed, then broadcast
      setTimeout(() => {
        websocketServer.broadcast('activity-log', testPayload);
      }, 100);
    });

    it('should not broadcast to unsubscribed clients', (done) => {
      const testPayload = { message: 'test evacuation', timestamp: new Date().toISOString() };
      let receivedUnexpectedMessage = false;

      testClient.on('message', (data) => {
        const message = JSON.parse(data.toString());
        if (message.type === 'event' && message.eventType === 'evacuation-status') {
          receivedUnexpectedMessage = true;
        }
      });

      // Broadcast to evacuation-status (client is not subscribed to this)
      websocketServer.broadcast('evacuation-status', testPayload);

      // Wait and check that no unexpected message was received
      setTimeout(() => {
        expect(receivedUnexpectedMessage).toBe(false);
        done();
      }, 200);
    });
  });

  describe('Statistics and Health', () => {
    it('should provide accurate statistics', () => {
      const stats = websocketServer.getStats();
      expect(stats).toHaveProperty('connectedClients');
      expect(stats).toHaveProperty('subscriptions');
      expect(stats).toHaveProperty('totalSubscriptions');
      expect(typeof stats.connectedClients).toBe('number');
      expect(typeof stats.subscriptions).toBe('object');
      expect(typeof stats.totalSubscriptions).toBe('number');
    });
  });
});
