version: '3.8'

services:
  csfportal:
    build: .
    container_name: csfportal
    ports:
      - "2001:8080"
      - "1812:1812/udp"
      - "1813:1813/udp"
    env_file:
      - .env.production
    volumes:
      - csfportal-data:/app/data
      # Persist uploaded files across container recreations
      - csfportal-uploads:/app/uploads
    restart: unless-stopped
    networks:
      - csfportal-network

volumes:
  csfportal-data:
  csfportal-uploads:

networks:
  csfportal-network:
    driver: bridge
