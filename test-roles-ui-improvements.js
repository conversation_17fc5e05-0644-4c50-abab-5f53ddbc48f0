const axios = require('axios');
const dotenv = require('dotenv');
const { Builder, By, until } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');

// Load environment variables
dotenv.config();

// Set the base URL for API requests
const baseURL = process.env.NODE_ENV === 'production' 
  ? 'https://portal.ukcsf.org' 
  : `http://localhost:${process.env.PORT || 8080}`;

/**
 * Test script for verifying the roles UI improvements:
 * 1. Google groups dropdown in role settings
 * 2. URL-based tab navigation
 * 
 * Note: This is a template script and requires:
 * - A running instance of the application
 * - Valid login credentials
 * - Selenium WebDriver installed
 * - Chrome browser installed
 */
async function testRolesUIImprovements() {
  console.log('Testing Roles UI Improvements...');
  console.log('--------------------------------');
  
  // Initialize the WebDriver
  const driver = await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(new chrome.Options().headless())
    .build();
  
  try {
    // 1. Test URL-based tab navigation
    console.log('\n1. Testing URL-based tab navigation:');
    
    // Navigate to the roles page with tab=1 (Default Role Settings)
    await driver.get(`${baseURL}/admin/roles?tab=1`);
    
    // Wait for the page to load
    await driver.sleep(2000);
    
    // Check if the Default Role Settings tab is active
    const defaultRoleSettingsTab = await driver.findElement(By.xpath("//div[contains(@class, 'MuiTabs-root')]//button[contains(text(), 'Default Role Settings')]"));
    const isTabActive = await defaultRoleSettingsTab.getAttribute('aria-selected');
    
    if (isTabActive === 'true') {
      console.log('✅ URL-based tab navigation works! Default Role Settings tab is active when loaded with ?tab=1');
    } else {
      console.log('❌ URL-based tab navigation failed. Default Role Settings tab is not active.');
    }
    
    // 2. Test Google groups dropdown
    console.log('\n2. Testing Google groups dropdown:');
    
    // Wait for the "Add Mapping" button and click it
    const addMappingButton = await driver.findElement(By.xpath("//button[contains(text(), 'Add Mapping')]"));
    await addMappingButton.click();
    
    // Wait for the dialog to appear
    await driver.wait(until.elementLocated(By.xpath("//h2[contains(text(), 'Add Google Group Mapping')]")), 5000);
    
    // Check if the Google Group dropdown exists
    try {
      const groupDropdown = await driver.findElement(By.id('group-select'));
      console.log('✅ Google Group dropdown exists in the dialog');
      
      // Click the dropdown to open it
      await groupDropdown.click();
      
      // Wait for dropdown options to appear
      await driver.sleep(1000);
      
      // Check if dropdown options are loaded
      const dropdownOptions = await driver.findElements(By.xpath("//ul[contains(@class, 'MuiMenu-list')]/li"));
      
      if (dropdownOptions.length > 0) {
        console.log(`✅ Google Group dropdown has ${dropdownOptions.length} options`);
      } else {
        console.log('⚠️ Google Group dropdown has no options. This might be normal if no groups exist or if API access is restricted in test environment.');
      }
      
    } catch (error) {
      console.log('❌ Google Group dropdown not found in the dialog');
      console.error(error.message);
    }
    
    // Close the dialog
    const cancelButton = await driver.findElement(By.xpath("//button[contains(text(), 'Cancel')]"));
    await cancelButton.click();
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    // Close the browser
    await driver.quit();
    console.log('\nTest completed.');
  }
}

// Run the test
console.log('Note: This test requires a running application instance and valid login credentials.');
console.log('It also requires Selenium WebDriver and Chrome browser to be installed.');
console.log('This is a template script that demonstrates how to test the UI improvements.');
console.log('You may need to modify it to work in your specific environment.\n');

// Uncomment the line below to run the test
// testRolesUIImprovements();