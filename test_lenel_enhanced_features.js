#!/usr/bin/env node

/**
 * Enhanced Test Script for Lenel S2 NetBox API
 * Tests the new enhanced features including search, filtering, detailed views, and CRUD operations
 */

const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Configuration - replace with your actual NetBox details
const config = {
  host: 'your-netbox-host.local',  // Replace with your NetBox IP/hostname
  username: 'your-username',       // Replace with your NetBox username
  password: 'your-password',       // Replace with your NetBox password
  port: 443                        // Default HTTPS port
};

async function testEnhancedFeatures() {
  console.log('🚀 Testing Lenel S2 NetBox Enhanced Features...\n');

  try {
    // Initialize the API client
    const api = new LenelS2NetBoxAPI(config.host, config.username, config.password, config.port);
    
    console.log('📡 Initializing API client...');
    await api.initialize();
    console.log('✅ API client initialized successfully\n');

    // ===================================================================
    // TEST ENHANCED SEARCH AND FILTERING
    // ===================================================================
    
    console.log('🔍 Testing Enhanced Search and Filtering Features...\n');

    // Test 1: Advanced Cardholder Search
    console.log('👥 Testing searchCardholders() with filters...');
    try {
      const cardholderSearch = await api.searchCardholders({
        status: 'Active',
        limit: 10,
        offset: 0
      });
      console.log(`✅ Found ${cardholderSearch.results.length} active cardholders`);
      console.log(`   Total: ${cardholderSearch.pagination.total}, Has More: ${cardholderSearch.pagination.hasMore}`);
      if (cardholderSearch.results.length > 0) {
        console.log(`   First result: ${cardholderSearch.results[0].fullName}`);
      }
    } catch (error) {
      console.log(`❌ searchCardholders() failed: ${error.message}`);
    }
    console.log('');

    // Test 2: Advanced Event Search
    console.log('📋 Testing searchEvents() with date filters...');
    try {
      const eventSearch = await api.searchEvents({
        startDate: '2024-01-01 00:00:00',
        endDate: '2024-12-31 23:59:59',
        limit: 20
      });
      console.log(`✅ Found ${eventSearch.results.length} events`);
      console.log(`   Total: ${eventSearch.pagination.total}, Has More: ${eventSearch.pagination.hasMore}`);
    } catch (error) {
      console.log(`❌ searchEvents() failed: ${error.message}`);
    }
    console.log('');

    // Test 3: Portal Search
    console.log('🚪 Testing searchPortals() with filters...');
    try {
      const portalSearch = await api.searchPortals({
        type: 'Portal',
        limit: 10
      });
      console.log(`✅ Found ${portalSearch.results.length} portals`);
      console.log(`   Total: ${portalSearch.pagination.total}`);
    } catch (error) {
      console.log(`❌ searchPortals() failed: ${error.message}`);
    }
    console.log('');

    // Test 4: Alarm Search
    console.log('🚨 Testing searchAlarms() with filters...');
    try {
      const alarmSearch = await api.searchAlarms({
        active: true,
        limit: 10
      });
      console.log(`✅ Found ${alarmSearch.results.length} active alarms`);
      console.log(`   Total: ${alarmSearch.pagination.total}`);
    } catch (error) {
      console.log(`❌ searchAlarms() failed: ${error.message}`);
    }
    console.log('');

    // ===================================================================
    // TEST ENHANCED DETAILED VIEWS
    // ===================================================================
    
    console.log('🔍 Testing Enhanced Detailed Views...\n');

    // Test 5: Enhanced Cardholder Details
    console.log('👤 Testing getCardholderDetailsEnhanced()...');
    try {
      const cardholders = await api.getCardholders();
      if (cardholders.length > 0) {
        const enhancedDetails = await api.getCardholderDetailsEnhanced(cardholders[0].id);
        console.log(`✅ Retrieved enhanced details for ${enhancedDetails.fullName}`);
        console.log(`   Access Levels: ${enhancedDetails.statistics.totalAccessLevels}`);
        console.log(`   Credentials: ${enhancedDetails.statistics.totalCredentials}`);
        console.log(`   Recent Activities: ${enhancedDetails.statistics.recentActivityCount}`);
      } else {
        console.log('⚠️  No cardholders available to test enhanced details');
      }
    } catch (error) {
      console.log(`❌ getCardholderDetailsEnhanced() failed: ${error.message}`);
    }
    console.log('');

    // Test 6: Enhanced Portal Details
    console.log('🚪 Testing getPortalDetailsEnhanced()...');
    try {
      const portals = await api.getPortals();
      if (portals.length > 0) {
        const enhancedPortal = await api.getPortalDetailsEnhanced(portals[0].id);
        console.log(`✅ Retrieved enhanced details for ${enhancedPortal.name}`);
        console.log(`   Current Status: ${enhancedPortal.currentStatus?.status || 'Unknown'}`);
        console.log(`   Recent Events: ${enhancedPortal.statistics.recentEventCount}`);
      } else {
        console.log('⚠️  No portals available to test enhanced details');
      }
    } catch (error) {
      console.log(`❌ getPortalDetailsEnhanced() failed: ${error.message}`);
    }
    console.log('');

    // Test 7: Enhanced Access Level Details
    console.log('🔐 Testing getAccessLevelDetailsEnhanced()...');
    try {
      const accessLevels = await api.getAccessLevels();
      if (accessLevels.length > 0) {
        const enhancedAccessLevel = await api.getAccessLevelDetailsEnhanced(accessLevels[0].id);
        console.log(`✅ Retrieved enhanced details for ${enhancedAccessLevel.name}`);
        console.log(`   Associated Cardholders: ${enhancedAccessLevel.statistics.totalCardholders}`);
        console.log(`   Active Cardholders: ${enhancedAccessLevel.statistics.activeCardholders}`);
      } else {
        console.log('⚠️  No access levels available to test enhanced details');
      }
    } catch (error) {
      console.log(`❌ getAccessLevelDetailsEnhanced() failed: ${error.message}`);
    }
    console.log('');

    // ===================================================================
    // TEST CRUD OPERATIONS
    // ===================================================================
    
    console.log('🛠️  Testing CRUD Operations...\n');

    // Test 8: Create Time Specification
    console.log('📅 Testing createTimeSpec()...');
    try {
      const newTimeSpec = await api.createTimeSpec({
        name: 'Test Business Hours',
        description: 'Test schedule for business hours',
        startTime: '09:00',
        endTime: '17:00',
        daysOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
      });
      console.log(`✅ Created time spec: ${newTimeSpec.timeSpecData.name}`);
      console.log(`   Key: ${newTimeSpec.timeSpecKey}`);
      
      // Test 9: Update the created time spec
      console.log('📅 Testing updateTimeSpec()...');
      const updatedTimeSpec = await api.updateTimeSpec(newTimeSpec.timeSpecKey, {
        description: 'Updated test schedule for business hours',
        endTime: '18:00'
      });
      console.log(`✅ Updated time spec successfully`);
      
      // Test 10: Delete the created time spec
      console.log('📅 Testing deleteTimeSpec()...');
      const deletedTimeSpec = await api.deleteTimeSpec(newTimeSpec.timeSpecKey);
      console.log(`✅ Deleted time spec successfully`);
      
    } catch (error) {
      console.log(`❌ Time spec CRUD operations failed: ${error.message}`);
    }
    console.log('');

    // ===================================================================
    // TEST UTILITY FEATURES
    // ===================================================================
    
    console.log('🔧 Testing Utility Features...\n');

    // Test 11: System Statistics
    console.log('📊 Testing getSystemStatistics()...');
    try {
      const stats = await api.getSystemStatistics();
      console.log(`✅ Retrieved system statistics:`);
      console.log(`   Total Portals: ${stats.overview.totalPortals}`);
      console.log(`   Total Cardholders: ${stats.overview.totalCardholders} (${stats.cardholders.active} active)`);
      console.log(`   Total Access Levels: ${stats.overview.totalAccessLevels}`);
      console.log(`   Total Alarms: ${stats.overview.totalAlarms} (${stats.alarms.active} active)`);
    } catch (error) {
      console.log(`❌ getSystemStatistics() failed: ${error.message}`);
    }
    console.log('');

    console.log('🎉 All enhanced feature tests completed!\n');
    console.log('📝 Enhanced Features Summary:');
    console.log('   ✅ Advanced search with filtering and pagination');
    console.log('   ✅ Enhanced detailed views with related information');
    console.log('   ✅ Comprehensive CRUD operations for access levels and time specs');
    console.log('   ✅ Bulk operations for access level management');
    console.log('   ✅ System statistics and utility functions');
    console.log('   ✅ Credential status checking and validation');

  } catch (error) {
    console.error('❌ Enhanced features test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testEnhancedFeatures().catch(console.error);
}

module.exports = { testEnhancedFeatures };
