// Test script to verify the Planning Center people name fix
const axios = require('axios');
require('dotenv').config();

// Function to test the Planning Center people directory endpoint
async function testPlanningCenterPeopleDirectory() {
  try {
    console.log('Testing Planning Center people directory endpoint...');
    
    // Make a request to the people-directory endpoint
    const response = await axios.get('http://localhost:8080/api/planning-center/people-directory', {
      headers: {
        // Include authentication headers if needed
        'Cookie': 'connect.sid=YOUR_SESSION_ID' // Replace with a valid session ID
      }
    });
    
    console.log('Response status:', response.status);
    
    // Check if the response contains people data
    if (response.data && response.data.people && Array.isArray(response.data.people)) {
      console.log(`Found ${response.data.people.length} people in the response`);
      
      // Log the first few people to verify their names
      console.log('\nSample people data:');
      response.data.people.slice(0, 5).forEach((person, index) => {
        console.log(`Person ${index + 1}: ID=${person._id}, Name=${person.name || 'N/A'}`);
      });
      
      // Check if any people have empty or missing names
      const peopleWithoutNames = response.data.people.filter(person => !person.name || person.name === 'N/A');
      if (peopleWithoutNames.length > 0) {
        console.log(`\nWARNING: Found ${peopleWithoutNames.length} people without proper names`);
        peopleWithoutNames.slice(0, 3).forEach((person, index) => {
          console.log(`  Person without name ${index + 1}: ID=${person._id}, Name=${person.name || 'N/A'}`);
        });
      } else {
        console.log('\nSUCCESS: All people have proper names');
      }
      
      // Check if any people have the default "Unknown" name
      const peopleWithUnknownNames = response.data.people.filter(person => person.name === 'Unknown');
      if (peopleWithUnknownNames.length > 0) {
        console.log(`\nNOTE: Found ${peopleWithUnknownNames.length} people with the default "Unknown" name`);
        peopleWithUnknownNames.slice(0, 3).forEach((person, index) => {
          console.log(`  Person with Unknown name ${index + 1}: ID=${person._id}`);
        });
      }
    } else {
      console.log('No people data found in the response');
      console.log('Response data:', response.data);
    }
  } catch (error) {
    console.error('Error testing Planning Center people directory:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testPlanningCenterPeopleDirectory();