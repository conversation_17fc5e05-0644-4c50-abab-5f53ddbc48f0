/**
 * Test script for Govee integration
 * 
 * This script tests the Govee integration by:
 * 1. Initializing the Govee API
 * 2. Getting all devices
 * 3. Getting device state for the first device
 * 4. Controlling a device (if available)
 * 
 * Usage:
 * node test-govee-integration.js
 * 
 * Requirements:
 * - GOVEE_API_KEY must be set in the .env file
 */

require('dotenv').config();
const GoveeAPI = require('./server/integrations/govee/goveeAPI');

// Create a new instance of the Govee API
const goveeAPI = new GoveeAPI();

// Test the Govee integration
async function testGoveeIntegration() {
  console.log('Testing Govee integration...');
  
  try {
    // Step 1: Initialize the Govee API
    console.log('\n1. Initializing Govee API...');
    await goveeAPI.initialize();
    console.log('✅ Govee API initialized successfully');
    
    // Step 2: Get all devices
    console.log('\n2. Getting all devices...');
    const devices = await goveeAPI.getDevices();
    
    if (!devices || devices.length === 0) {
      console.log('⚠️ No devices found. Make sure your Govee API key is correct and you have devices linked to your account.');
      return;
    }
    
    console.log(`✅ Found ${devices.length} devices:`);
    devices.forEach((device, index) => {
      console.log(`   Device ${index + 1}:`);
      console.log(`   - Name: ${device.deviceName || 'Unknown'}`);
      console.log(`   - Model: ${device.model}`);
      console.log(`   - ID: ${device.device}`);
      console.log(`   - Controllable: ${device.controllable ? 'Yes' : 'No'}`);
      console.log(`   - Retrievable: ${device.retrievable ? 'Yes' : 'No'}`);
      console.log('');
    });
    
    // Step 3: Get device state for the first device
    const firstDevice = devices[0];
    console.log(`\n3. Getting state for device "${firstDevice.deviceName || firstDevice.device}"...`);
    
    if (!firstDevice.retrievable) {
      console.log('⚠️ This device does not support state retrieval. Skipping state check.');
    } else {
      const deviceState = await goveeAPI.getDeviceState(firstDevice.device, firstDevice.model);
      
      if (!deviceState) {
        console.log('❌ Failed to get device state');
      } else {
        console.log('✅ Device state retrieved successfully:');
        console.log(`   - Power: ${deviceState.powerState || 'Unknown'}`);
        console.log(`   - Brightness: ${deviceState.brightness || 'N/A'}`);
        
        if (deviceState.color) {
          console.log(`   - Color: RGB(${deviceState.color.r}, ${deviceState.color.g}, ${deviceState.color.b})`);
        }
        
        if (deviceState.colorTemperature) {
          console.log(`   - Color Temperature: ${deviceState.colorTemperature}K`);
        }
      }
    }
    
    // Step 4: Control a device (commented out to prevent accidental changes)
    console.log('\n4. Device control test (SKIPPED)');
    console.log('   To test device control, uncomment the code in this section');
    
    /*
    if (!firstDevice.controllable) {
      console.log('⚠️ This device does not support control. Skipping control test.');
    } else {
      // Example: Toggle power state
      const currentPowerState = deviceState?.powerState === 'on';
      console.log(`   Turning device ${currentPowerState ? 'OFF' : 'ON'}...`);
      
      const success = await goveeAPI.turnDevice(firstDevice.device, firstDevice.model, !currentPowerState);
      
      if (success) {
        console.log(`✅ Successfully turned device ${currentPowerState ? 'OFF' : 'ON'}`);
      } else {
        console.log('❌ Failed to control device');
      }
    }
    */
    
    console.log('\n✅ Govee integration test completed successfully');
    
  } catch (error) {
    console.error('\n❌ Error testing Govee integration:', error);
  }
}

// Run the test
testGoveeIntegration().catch(console.error);