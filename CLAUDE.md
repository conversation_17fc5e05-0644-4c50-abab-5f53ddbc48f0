# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Church Staff Portal (CSF Portal) - a secure intranet platform built with Node.js/Express backend and React frontend. The application provides role-based access control, Google service integrations, and numerous third-party integrations for church staff management.

## Development Commands

### Project Setup
```bash
# Install backend dependencies
npm install

# Install frontend dependencies
npm run client-install

# Development mode (runs both backend and frontend concurrently)
npm run dev

# Run backend only (with nodemon for auto-reload)
npm run server

# Run frontend only
npm run client
```

### Testing
```bash
# Run backend tests with Jest
npm test

# Run tests in watch mode
npm run test:watch
```

### Build & Deployment
```bash
# Build production bundle
npm run build

# Start production server
npm start
```

## Architecture

### Monorepo Structure
- **Root**: Node.js/Express backend (port 6000)
- **client/**: React frontend (port 3000 in dev, proxied to backend)
- **server/**: Backend modules organized by feature

### Backend Architecture
- **MVC Pattern**: Controllers handle business logic, routes define API endpoints
- **Models**: Mongoose schemas for MongoDB
- **Integrations**: Third-party API wrappers organized by service
- **Authentication**: Passport.js with Google OAuth 2.0, session-based auth
- **Authorization**: Role-based middleware (admin.js, auth.js)

### Frontend Architecture  
- **React**: Functional components with hooks
- **Material-UI**: Component library and theming
- **Context API**: AuthContext for authentication, DashboardContext for app state
- **React Router**: Client-side routing
- **Services**: API communication layer organized by feature
- **Widget System**: Modular dashboard components

### Key Integrations
The app integrates with 20+ external services including:
- **Core Google Services**: Drive, Calendar, Admin SDK, Forms
- **Access Control**: Lenel S2 NetBox, UniFi Access/Network/Protect
- **Smart Building**: WIIM, Dreo, LG ThinQ
- **Business Tools**: Canva, Apple Business Manager, Mosyle
- **Asset Management**: GLPI, Planning Center, Synology

Each integration follows a consistent pattern:
- API wrapper in `server/integrations/[service]/[service]API.js`
- Controller in `server/controllers/[service]Controller.js`
- React service in `client/src/services/[service]Service.js`
- Configuration model in `models/[Service]Config.js`

### Database Design
- **MongoDB** with Mongoose ODM
- **User Management**: Users, Roles, Groups, Teams with hierarchical permissions
- **Integration Configs**: Service-specific configuration models
- **Content Management**: Shortcuts, Help entries, Dashboard preferences
- **Building Management**: Buildings, Floors, Rooms with floorplan support

## Development Guidelines

### Code Organization
- Follow existing file naming conventions (camelCase for JS, PascalCase for React components)
- Place new integrations in appropriate server/integrations and client/services directories
- Use consistent error handling patterns found in existing API wrappers
- Follow the established controller → service → API wrapper pattern

### Environment Configuration
- Backend server runs on port 6000 (configured in client proxy)
- All Google API credentials and service configurations use environment variables
- Integration configs are stored in database models, not environment files

### Authentication & Security
- All API routes require authentication except login endpoints
- Admin routes use additional role-based middleware
- Google domain restriction enforced via ALLOWED_DOMAINS env var
- CSP headers configured for Google services integration

### Testing Patterns
- Jest configuration includes MongoDB Memory Server for integration tests
- Test files follow *.test.js naming convention
- Setup/teardown patterns established in tests/setup.test.js