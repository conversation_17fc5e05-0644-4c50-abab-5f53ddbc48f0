/**
 * Test script to verify that the Lenel API is only initialized when needed
 * 
 * This script makes a request to a non-Lenel endpoint and checks if the Lenel API is initialized.
 * It then makes a request to a Lenel endpoint and verifies that the Lenel API is initialized.
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set up axios with base URL
const api = axios.create({
  baseURL: `http://localhost:${process.env.PORT || 8080}`,
  timeout: 10000
});

// Function to check if Lenel API is initialized
async function checkLenelInitialization() {
  try {
    console.log('Testing Lenel lazy initialization...');
    
    // Step 1: Make a request to a non-Lenel endpoint
    console.log('\nStep 1: Making request to a non-Lenel endpoint (Google Drive files)...');
    const nonLenelResponse = await api.get('/api/google-drive/files');
    console.log('Non-Lenel endpoint response status:', nonLenelResponse.status);
    
    // Step 2: Check server logs to see if Lenel API was initialized
    console.log('\nCheck server logs to see if "Initializing Lenel S2 NetBox API on demand" message appears.');
    console.log('If the message does not appear, then the Lenel API was not initialized, which is good.');
    
    // Step 3: Make a request to a Lenel endpoint
    console.log('\nStep 3: Making request to a Lenel endpoint (portals)...');
    try {
      const lenelResponse = await api.get('/api/lenel-s2-netbox/portals');
      console.log('Lenel endpoint response status:', lenelResponse.status);
      console.log('Lenel API should now be initialized. Check server logs for "Initializing Lenel S2 NetBox API on demand" message.');
    } catch (error) {
      console.log('Lenel endpoint request failed, but this is expected if Lenel is not configured.');
      console.log('Error:', error.message);
    }
    
    console.log('\nTest completed. Check server logs to verify lazy initialization is working correctly.');
    
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
checkLenelInitialization();