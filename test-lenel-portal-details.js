// Test script for Lenel S2 NetBox portal details
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');
require('dotenv').config();

// Get environment variables
const host = process.env.LENEL_S2_NETBOX_HOST || '';
const username = process.env.LENEL_S2_NETBOX_USERNAME || '';
const password = process.env.LENEL_S2_NETBOX_PASSWORD || '';
const port = process.env.LENEL_S2_NETBOX_PORT || 443;

// Create an instance of the API
const lenelS2NetBoxAPI = new LenelS2NetBoxAPI(host, username, password, port);

// Test function to get portal details
async function testGetPortalDetails() {
  console.log('Testing Lenel S2 NetBox getPortalDetails method...');
  console.log(`Using host: ${host}, port: ${port}`);

  try {
    // First, get all portals to find a valid portal ID
    console.log('Getting all portals...');
    const portals = await lenelS2NetBoxAPI.getPortals();
    
    if (portals && portals.length > 0) {
      const portalId = portals[0].id;
      console.log(`Found portal ID: ${portalId}`);
      
      // Now get the details for this portal
      console.log(`Getting details for portal ID: ${portalId}...`);
      const portalDetails = await lenelS2NetBoxAPI.getPortalDetails(portalId);
      
      console.log('Portal details:', portalDetails);
      console.log('Test successful!');
    } else {
      console.log('No portals found to test with.');
    }
  } catch (error) {
    console.error('Error testing getPortalDetails:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testGetPortalDetails().catch(error => {
  console.error('Test error:', error);
});