/**
 * Test script to verify the UniFi Network API connection fix
 * 
 * This script tests the updated UniFi Network API wrapper to ensure
 * it can successfully connect to the UniFi Network Controller using
 * the corrected endpoint that includes the site parameter.
 * 
 * Usage: node test-unifi-network-connection-fix.js
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API wrapper
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get configuration from environment variables
const host = process.env.UNIFI_NETWORK_HOST || '';
const apiKey = process.env.UNIFI_NETWORK_API_KEY || '';
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Check if required configuration is available
if (!host || !apiKey) {
  console.error('Error: UniFi Network host and API key are required.');
  console.error('Please set the UNIFI_NETWORK_HOST and UNIFI_NETWORK_API_KEY environment variables.');
  process.exit(1);
}

// Create an instance of the UniFi Network API wrapper
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Test function to verify the API connection
async function testConnection() {
  try {
    console.log('Testing UniFi Network API connection...');
    console.log(`Using host: ${host}, API key: [HIDDEN], port: ${port}, site: ${site}`);
    
    // Test the connection directly
    const result = await unifiNetworkAPI.testConnection();
    
    console.log('Connection test successful!');
    console.log('The fix for the UniFi Network API endpoint is working correctly.');
    return true;
  } catch (error) {
    console.error('Error testing connection:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    console.error('The fix for the UniFi Network API endpoint is NOT working.');
    return false;
  }
}

// Run the test
testConnection();