const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});

// Import User model
const User = require('./models/User');

// Test function to verify widget preferences are saved correctly
async function testWidgetPreferences() {
  try {
    console.log('Testing floating shortcut widget preferences for alwaysShowLabels...');
    
    // Find any user in the database
    const user = await User.findOne({});
    
    if (!user) {
      console.error('No users found in the database.');
      process.exit(1);
    }
    
    console.log('Found user:', user.name, user.email);
    
    // Current widget preferences
    console.log('Current widget preferences:', JSON.stringify(user.widgetPreferences, null, 2));
    
    // Test data
    const testShortcuts = ['shortcut1', 'shortcut2', 'shortcut3'];
    const testPortalPages = ['dashboard', 'shortcuts', 'drive'];
    const testAlwaysShowLabels = true; // Test with labels always shown
    
    // Update widget preferences
    if (!user.widgetPreferences) {
      user.widgetPreferences = {};
    }
    
    if (!user.widgetPreferences.floatingShortcut) {
      user.widgetPreferences.floatingShortcut = {};
    }
    
    user.widgetPreferences.floatingShortcut.favoriteShortcuts = testShortcuts;
    user.widgetPreferences.floatingShortcut.favoritePortalPages = testPortalPages;
    user.widgetPreferences.floatingShortcut.enabled = true;
    user.widgetPreferences.floatingShortcut.alwaysShowLabels = testAlwaysShowLabels;
    
    // Save the user
    await user.save();
    console.log('Updated widget preferences saved.');
    
    // Verify the changes were saved
    const updatedUser = await User.findById(user._id);
    console.log('Updated widget preferences:', JSON.stringify(updatedUser.widgetPreferences, null, 2));
    
    // Verify all fields are saved correctly
    const savedShortcuts = updatedUser.widgetPreferences?.floatingShortcut?.favoriteShortcuts || [];
    const savedPortalPages = updatedUser.widgetPreferences?.floatingShortcut?.favoritePortalPages || [];
    const savedAlwaysShowLabels = updatedUser.widgetPreferences?.floatingShortcut?.alwaysShowLabels;
    
    console.log('Saved shortcuts:', savedShortcuts);
    console.log('Saved portal pages:', savedPortalPages);
    console.log('Saved alwaysShowLabels:', savedAlwaysShowLabels);
    
    // Check if all fields were saved correctly
    const shortcutsMatch = JSON.stringify(savedShortcuts) === JSON.stringify(testShortcuts);
    const portalPagesMatch = JSON.stringify(savedPortalPages) === JSON.stringify(testPortalPages);
    const labelsMatch = savedAlwaysShowLabels === testAlwaysShowLabels;
    
    if (shortcutsMatch && portalPagesMatch && labelsMatch) {
      console.log('TEST PASSED: All preferences including alwaysShowLabels were saved correctly.');
    } else {
      console.log('TEST FAILED:');
      if (!shortcutsMatch) console.log('- favoriteShortcuts not saved correctly');
      if (!portalPagesMatch) console.log('- favoritePortalPages not saved correctly');
      if (!labelsMatch) console.log('- alwaysShowLabels not saved correctly');
    }
    
    // Now test with alwaysShowLabels set to false
    console.log('\nTesting with alwaysShowLabels set to false...');
    user.widgetPreferences.floatingShortcut.alwaysShowLabels = false;
    
    // Save the user
    await user.save();
    console.log('Updated widget preferences saved.');
    
    // Verify the changes were saved
    const updatedUser2 = await User.findById(user._id);
    console.log('Updated widget preferences:', JSON.stringify(updatedUser2.widgetPreferences, null, 2));
    
    // Verify alwaysShowLabels is saved correctly
    const savedAlwaysShowLabels2 = updatedUser2.widgetPreferences?.floatingShortcut?.alwaysShowLabels;
    console.log('Saved alwaysShowLabels:', savedAlwaysShowLabels2);
    
    // Check if alwaysShowLabels was saved correctly
    const labelsMatch2 = savedAlwaysShowLabels2 === false;
    
    if (labelsMatch2) {
      console.log('TEST PASSED: alwaysShowLabels=false was saved correctly.');
    } else {
      console.log('TEST FAILED: alwaysShowLabels not saved correctly');
    }
    
  } catch (err) {
    console.error('Error testing widget preferences:', err);
  } finally {
    // Close the MongoDB connection
    mongoose.connection.close();
  }
}

// Run the test
testWidgetPreferences();