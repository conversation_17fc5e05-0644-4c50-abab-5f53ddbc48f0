const axios = require('axios');

// Set up axios with base URL
const api = axios.create({
  baseURL: 'http://localhost:6000',
  withCredentials: true
});

// Test function to verify the service provisioner
async function testServiceProvisioner() {
  try {
    console.log('Testing service provisioner...');
    
    // Step 1: Get all integrations
    console.log('\n1. Getting all integrations...');
    const integrationsRes = await api.get('/api/integrations');
    console.log(`Found ${integrationsRes.data.length} integrations`);
    console.log('Integrations with provisioning support:');
    const provisioningIntegrations = integrationsRes.data.filter(i => i.supportsProvisioning);
    console.log(provisioningIntegrations.map(i => `- ${i.name} (${i.id})`).join('\n'));
    
    // Step 2: Get a user to test with
    console.log('\n2. Getting a user to test with...');
    const usersRes = await api.get('/api/staff-directory/users');
    if (!usersRes.data || usersRes.data.length === 0) {
      throw new Error('No users found');
    }
    const testUser = usersRes.data[0];
    console.log(`Using test user: ${testUser.name} (${testUser._id})`);
    
    // Step 3: Get user account status
    console.log('\n3. Getting user account status...');
    const statusRes = await api.get(`/api/user-provisioning/${testUser._id}/status`);
    console.log('User account status:');
    Object.entries(statusRes.data).forEach(([integrationId, status]) => {
      console.log(`- ${integrationId}: ${status.exists ? 'Exists' : 'Does not exist'}`);
    });
    
    // Step 4: Test provisioning a user account (if not already provisioned)
    // Note: This is commented out to avoid actually provisioning accounts during testing
    /*
    console.log('\n4. Testing provisioning a user account...');
    if (provisioningIntegrations.length > 0) {
      const testIntegration = provisioningIntegrations[0];
      if (!statusRes.data[testIntegration.id]?.exists) {
        console.log(`Provisioning user in ${testIntegration.name}...`);
        const provisionRes = await api.post(`/api/user-provisioning/${testUser._id}/${testIntegration.id}`, {
          firstName: testUser.name.split(' ')[0],
          lastName: testUser.name.split(' ').slice(1).join(' '),
          email: testUser.email
        });
        console.log('Provisioning result:', provisionRes.data);
      } else {
        console.log(`User already has an account in ${testIntegration.name}`);
      }
    }
    */
    
    console.log('\nService provisioner test completed successfully!');
  } catch (error) {
    console.error('Error testing service provisioner:', error.response?.data || error.message);
  }
}

// Run the test
testServiceProvisioner();