const axios = require('axios');

async function testElevatorFields() {
  try {
    console.log('Testing elevator fields in access control API...');
    
    // Make a request to get elevators
    const response = await axios.get('http://localhost:3000/api/access-control/elevators', {
      headers: {
        // Add any required authentication headers here
        // 'Authorization': 'Bearer your-token-here'
      }
    });
    
    console.log('Response status:', response.status);
    
    // Check if we have elevators in the response
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      console.log(`Found ${response.data.length} elevators`);
      
      // Check each elevator for required fields
      let missingFields = false;
      response.data.forEach((elevator, index) => {
        console.log(`\nElevator ${index + 1}:`);
        console.log(`  ID: ${elevator.id || 'MISSING'}`);
        console.log(`  Key: ${elevator.key || 'MISSING'}`);
        console.log(`  Name: ${elevator.name || 'MISSING'}`);
        
        if (!elevator.key || !elevator.name) {
          console.error('  ERROR: Missing required fields!');
          missingFields = true;
        } else {
          console.log('  All required fields present ✓');
        }
      });
      
      if (!missingFields) {
        console.log('\nSUCCESS: All elevators have the required fields (KEY and NAME)');
      } else {
        console.error('\nFAILURE: Some elevators are missing required fields');
      }
    } else {
      console.log('No elevators found in the response');
    }
  } catch (error) {
    console.error('Error testing elevator fields:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testElevatorFields();