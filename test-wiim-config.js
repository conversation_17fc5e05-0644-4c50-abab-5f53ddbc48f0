/**
 * Test script to verify that the WiiM config endpoint is working correctly
 * and not returning a 304 error.
 * 
 * Note: This script requires the server to be running and a valid session to be established.
 * You may need to log in to the application in a browser first to establish a session.
 */
const axios = require('axios');

// Function to test the WiiM config endpoint
async function testWiimConfigEndpoint() {
  try {
    console.log('Testing WiiM config endpoint...');
    console.log('Note: This test requires the server to be running and a valid session to be established.');
    console.log('If you encounter authentication errors, please log in to the application in a browser first.');
    
    // Make a request to the WiiM config endpoint
    console.log('\nMaking first request...');
    const response = await axios.get('http://localhost:6000/api/wiim/config', {
      withCredentials: true, // Include cookies for authentication
      headers: {
        'Accept': 'application/json',
      }
    });
    
    // Log the response status and headers
    console.log(`Response status: ${response.status}`);
    console.log('Response headers:', JSON.stringify(response.headers, null, 2));
    
    // Check if the response includes the cache-control headers we set
    const cacheControl = response.headers['cache-control'];
    const pragma = response.headers['pragma'];
    const expires = response.headers['expires'];
    
    console.log('\nCache headers:');
    console.log('Cache-Control:', cacheControl);
    console.log('Pragma:', pragma);
    console.log('Expires:', expires);
    
    // Verify that the response is not a 304
    if (response.status === 304) {
      console.error('\nERROR: Received a 304 response. The fix did not work.');
    } else {
      console.log('\nSUCCESS: Received a non-304 response. The fix worked!');
    }
    
    // Make a second request to verify that it also doesn't return a 304
    console.log('\nMaking a second request to verify...');
    const secondResponse = await axios.get('http://localhost:6000/api/wiim/config', {
      withCredentials: true,
      headers: {
        'Accept': 'application/json',
      }
    });
    
    console.log(`Second response status: ${secondResponse.status}`);
    console.log('Second response headers:', JSON.stringify(secondResponse.headers, null, 2));
    
    // Verify that the second response is also not a 304
    if (secondResponse.status === 304) {
      console.error('\nERROR: Received a 304 response on the second request. The fix did not work.');
    } else {
      console.log('\nSUCCESS: Received a non-304 response on the second request. The fix worked!');
    }
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('\nError testing WiiM config endpoint:');
    
    if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused. Make sure the server is running on port 6000.');
    } else {
      console.error('Error message:', error.message);
      
      // If the error is a response error, log the response details
      if (error.response) {
        console.error(`Response status: ${error.response.status}`);
        
        if (error.response.status === 401) {
          console.error('Authentication error: You need to be logged in to access this endpoint.');
          console.error('Please log in to the application in a browser first to establish a session.');
        }
        
        console.error('Response headers:', JSON.stringify(error.response.headers, null, 2));
        console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      } else if (error.request) {
        console.error('No response received from the server.');
      }
    }
    
    console.error('\nTest failed.');
  }
}

// Run the test
testWiimConfigEndpoint();