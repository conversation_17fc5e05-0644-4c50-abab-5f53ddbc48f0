require('dotenv').config();
const axios = require('axios');
const mongoose = require('mongoose');
const User = require('./models/User');
const Group = require('./models/Group');
const db = process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal';

// Connect to MongoDB
mongoose.connect(db, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});

// Test function to verify group members lookup
const testGroupMembersLookup = async () => {
  try {
    console.log('Testing group members lookup...');
    
    // 1. Create a test user if it doesn't exist
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      console.log('Creating test user...');
      testUser = new User({
        name: 'Test User',
        email: '<EMAIL>',
        authType: 'local',
        isActive: true,
        googleId: 'test-user-' + Date.now() // Add a unique googleId
      });
      await testUser.save();
      console.log('Test user created:', testUser._id);
    } else {
      console.log('Using existing test user:', testUser._id);
    }
    
    // 2. Create a test group if it doesn't exist
    let testGroup = await Group.findOne({ name: 'Test Group' });
    
    if (!testGroup) {
      console.log('Creating test group...');
      testGroup = new Group({
        name: 'Test Group',
        description: 'A test group for verifying member lookup',
        owner: testUser._id,
        members: [testUser._id], // Add test user as a member in the group's members array
        isPublic: true
      });
      await testGroup.save();
      console.log('Test group created:', testGroup._id);
    } else {
      console.log('Using existing test group:', testGroup._id);
      
      // Make sure test user is in the group's members array
      if (!testGroup.members.includes(testUser._id)) {
        testGroup.members.push(testUser._id);
        await testGroup.save();
        console.log('Added test user to test group members array');
      }
    }
    
    // 3. Make sure test user has the test group in their groups array
    if (!testUser.groups.includes(testGroup._id)) {
      testUser.groups.push(testGroup._id);
      await testUser.save();
      console.log('Added test group to test user groups array');
    }
    
    // 4. Create a second test user that is NOT in the group
    let testUser2 = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser2) {
      console.log('Creating second test user...');
      testUser2 = new User({
        name: 'Test User 2',
        email: '<EMAIL>',
        authType: 'local',
        isActive: true,
        googleId: 'test-user2-' + Date.now() // Add a unique googleId
      });
      await testUser2.save();
      console.log('Second test user created:', testUser2._id);
    } else {
      console.log('Using existing second test user:', testUser2._id);
    }
    
    // 5. Create a test case where:
    // - User is in group.members but NOT in user.groups
    // - This should NOT show up in the API response if we're using user.groups for lookup
    
    // First, make sure testUser2 is NOT in testUser.groups
    testUser2.groups = testUser2.groups.filter(g => g.toString() !== testGroup._id.toString());
    await testUser2.save();
    
    // Then, add testUser2 to testGroup.members
    if (!testGroup.members.includes(testUser2._id)) {
      testGroup.members.push(testUser2._id);
      await testGroup.save();
      console.log('Added second test user to test group members array (but NOT to user.groups)');
    }
    
    // 6. Fetch the group via the API
    console.log('\nFetching group via API...');
    const response = await axios.get(`http://localhost:5000/api/staff-directory/groups/${testGroup._id}`);
    const group = response.data;
    
    console.log('Group members from API:', group.members.map(m => m.email));
    
    // 7. Verify that only users with the group in their groups array are returned
    const memberEmails = group.members.map(m => m.email);
    
    if (memberEmails.includes(testUser.email) && !memberEmails.includes(testUser2.email)) {
      console.log('\n✅ SUCCESS: API is correctly using user.groups for lookup');
      console.log('- First test user (in user.groups) is included in the response');
      console.log('- Second test user (only in group.members) is NOT included in the response');
    } else {
      console.log('\n❌ FAILURE: API is NOT correctly using user.groups for lookup');
      console.log('- First test user in response:', memberEmails.includes(testUser.email));
      console.log('- Second test user in response:', memberEmails.includes(testUser2.email));
    }
    
    // 8. Clean up (optional)
    // Uncomment these lines if you want to clean up the test data
    /*
    await User.findByIdAndDelete(testUser._id);
    await User.findByIdAndDelete(testUser2._id);
    await Group.findByIdAndDelete(testGroup._id);
    console.log('Test data cleaned up');
    */
    
  } catch (err) {
    console.error('Error testing group members lookup:', err);
  } finally {
    // Disconnect from MongoDB
    mongoose.disconnect();
    console.log('MongoDB Disconnected');
  }
};

// Run the test
testGroupMembersLookup();