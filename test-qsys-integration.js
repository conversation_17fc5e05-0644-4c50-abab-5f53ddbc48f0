// Test script for Q-sys Core Manager integration
require('dotenv').config();
const QsysAPI = require('./server/integrations/qsys/qsysAPI');

// Function to test the Q-sys integration
async function testQsysIntegration() {
  console.log('Testing Q-sys Core Manager integration...');
  
  // Get configuration from environment variables
  const host = process.env.QSYS_HOST || '';
  const port = process.env.QSYS_PORT || 1710;
  const username = process.env.QSYS_USERNAME || '';
  const password = process.env.QSYS_PASSWORD || '';
  const protocol = process.env.QSYS_PROTOCOL || 'tcp';
  
  // Check if required environment variables are set
  if (!host) {
    console.error('Error: QSYS_HOST environment variable is not set.');
    console.log('Please set the following environment variables:');
    console.log('  QSYS_HOST: The hostname or IP address of the Q-sys Core Manager');
    console.log('  QSYS_PORT: The port number (optional, defaults to 1710 for TCP, 443 for HTTPS)');
    console.log('  QSYS_USERNAME: The username for authentication (optional)');
    console.log('  QSYS_PASSWORD: The password for authentication (optional)');
    console.log('  QSYS_PROTOCOL: The protocol to use (tcp or https, defaults to tcp)');
    return;
  }
  
  console.log(`Using configuration: ${host}:${port} (${protocol})`);
  
  try {
    // Create a new instance of the Q-sys API
    const qsysAPI = new QsysAPI(host, port, username, password, protocol);
    
    // Initialize the API
    console.log('Initializing Q-sys API...');
    await qsysAPI.initialize();
    console.log('Q-sys API initialized successfully.');
    
    // Get status
    console.log('Getting Q-sys status...');
    const status = await qsysAPI.getStatus();
    console.log('Q-sys status:', JSON.stringify(status, null, 2));
    
    // Get components
    console.log('Getting Q-sys components...');
    const components = await qsysAPI.getComponents();
    console.log('Q-sys components:', JSON.stringify(components, null, 2));
    
    // If components are available, get controls for the first component
    if (components && components.length > 0) {
      const componentName = components[0].Name;
      console.log(`Getting controls for component: ${componentName}...`);
      const controls = await qsysAPI.getControls(componentName);
      console.log('Q-sys controls:', JSON.stringify(controls, null, 2));
      
      // If controls are available, get the value of the first control
      if (controls && controls.length > 0) {
        const controlName = controls[0].Name;
        console.log(`Getting value for control: ${controlName}...`);
        const value = await qsysAPI.getControlValue(componentName, controlName);
        console.log('Q-sys control value:', JSON.stringify(value, null, 2));
      }
    }
    
    // Get health status
    console.log('Getting Q-sys health status...');
    const healthStatus = await qsysAPI.getHealthStatus();
    console.log('Q-sys health status:', JSON.stringify(healthStatus, null, 2));
    
    // Close the connection
    console.log('Closing Q-sys connection...');
    await qsysAPI.close();
    console.log('Q-sys connection closed.');
    
    console.log('Q-sys integration test completed successfully.');
  } catch (error) {
    console.error('Error testing Q-sys integration:', error);
  }
}

// Run the test
testQsysIntegration()
  .then(() => {
    console.log('Test script completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test script failed:', error);
    process.exit(1);
  });