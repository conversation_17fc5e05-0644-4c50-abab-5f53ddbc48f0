name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x]
        mongodb-version: [4.4]
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v2
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Start MongoDB
      uses: supercharge/mongodb-github-action@1.8.0
      with:
        mongodb-version: ${{ matrix.mongodb-version }}
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install client dependencies
      run: npm run client-install
    
    - name: Run server tests
      run: npm test
      env:
        CI: true
        MONGO_URI: mongodb://localhost:27017/csfportal_test
        SESSION_SECRET: ${{ secrets.SESSION_SECRET }}
    
    - name: Run client tests
      run: npm test --prefix client
      env:
        CI: true

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Use Node.js 16.x
      uses: actions/setup-node@v2
      with:
        node-version: 16.x
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Install client dependencies
      run: npm run client-install
    
    - name: Build client
      run: npm run build --prefix client
      env:
        CI: false
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v2
      with:
        name: build-artifacts
        path: client/build

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Download build artifacts
      uses: actions/download-artifact@v2
      with:
        name: build-artifacts
        path: client/build
    
    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
        heroku_app_name: ${{ secrets.HEROKU_APP_NAME }}
        heroku_email: ${{ secrets.HEROKU_EMAIL }}