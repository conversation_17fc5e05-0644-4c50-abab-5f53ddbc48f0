/**
 * Test script to verify the Recent Files Widget integration with Google Drive
 * 
 * This script simulates the API calls made by the RecentFilesWidget component
 * to ensure that the Google Drive integration is working properly.
 */

const axios = require('axios');
require('dotenv').config();

// Set up authentication headers (you would need to be logged in to the application)
const headers = {
  'Content-Type': 'application/json',
  // Add any authentication headers needed for your application
};

// Test the Google Drive configuration endpoint
const testConfig = async () => {
  try {
    console.log('Testing Google Drive configuration...');
    const response = await axios.get('/api/google-drive/config', { headers });
    console.log('Configuration status:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching Google Drive configuration:', error.response?.data || error.message);
    return null;
  }
};

// Test the Google Drive files endpoint with the same options as the widget
const testListFiles = async () => {
  try {
    console.log('Testing Google Drive files listing...');
    
    // Use the same options as in the RecentFilesWidget
    const options = { 
      q: "'root' in parents",
      orderBy: "modifiedTime desc"
    };
    
    const response = await axios.get('/api/google-drive/files', { 
      params: options,
      headers 
    });
    
    console.log(`Retrieved ${response.data.length} files`);
    
    // Display the first few files
    if (response.data.length > 0) {
      console.log('Recent files:');
      response.data.slice(0, 5).forEach((file, index) => {
        console.log(`${index + 1}. ${file.name} (${file.mimeType}) - Modified: ${new Date(file.modifiedTime).toLocaleString()}`);
      });
    } else {
      console.log('No files found');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error listing Google Drive files:', error.response?.data || error.message);
    return null;
  }
};

// Main test function
const runTest = async () => {
  console.log('=== Testing Recent Files Widget Google Drive Integration ===');
  
  // Test configuration
  const config = await testConfig();
  
  if (!config || !config.isAuthenticated) {
    console.log('Google Drive is not authenticated. Please configure and authenticate first.');
    return;
  }
  
  // Test file listing
  await testListFiles();
  
  console.log('=== Test completed ===');
};

// Run the test
runTest().catch(error => {
  console.error('Unhandled error during test:', error);
});