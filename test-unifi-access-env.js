/**
 * Test script for UniFi Access integration with API key authentication
 * 
 * This script tests the UniFi Access integration by:
 * 1. Setting environment variables for UniFi Access including API key
 * 2. Initializing the UniFi Access API
 * 3. Verifying the API is using the API key for authentication
 * 
 * Usage:
 * node test-unifi-access-env.js
 */

// Set environment variables for testing
process.env.UNIFI_ACCESS_HOST = 'test-host';
process.env.UNIFI_ACCESS_API_KEY = 'test-api-key';
process.env.UNIFI_ACCESS_PORT = '443';

// Import the UniFi Access API
const UnifiAccessAPI = require('./server/integrations/unifiAccess/unifiAccessAPI');

// Create a test function
async function testUnifiAccessEnv() {
  console.log('Testing UniFi Access integration with API key authentication...');
  console.log('Environment variables:');
  console.log(`  UNIFI_ACCESS_HOST: ${process.env.UNIFI_ACCESS_HOST}`);
  console.log(`  UNIFI_ACCESS_PORT: ${process.env.UNIFI_ACCESS_PORT}`);
  console.log(`  UNIFI_ACCESS_API_KEY: [hidden]`);
  
  try {
    // Initialize the API
    const unifiAccessAPI = new UnifiAccessAPI();
    
    // Check if the API was initialized with the correct values
    console.log('\nAPI initialized with:');
    console.log(`  Host: ${unifiAccessAPI.host}`);
    console.log(`  Port: ${unifiAccessAPI.port}`);
    console.log(`  API Key: [hidden]`);
    
    // Verify that the API is using the environment variables
    if (
      unifiAccessAPI.host === process.env.UNIFI_ACCESS_HOST &&
      unifiAccessAPI.apiKey === process.env.UNIFI_ACCESS_API_KEY &&
      unifiAccessAPI.port.toString() === process.env.UNIFI_ACCESS_PORT
    ) {
      console.log('\n✅ API is correctly using environment variables');
    } else {
      console.log('\n❌ API is NOT correctly using environment variables');
      console.log('Expected:');
      console.log(`  Host: ${process.env.UNIFI_ACCESS_HOST}`);
      console.log(`  API Key: [hidden]`);
      console.log(`  Port: ${process.env.UNIFI_ACCESS_PORT}`);
      console.log('Got:');
      console.log(`  Host: ${unifiAccessAPI.host}`);
      console.log(`  API Key: ${unifiAccessAPI.apiKey ? '[present]' : '[missing]'}`);
      console.log(`  Port: ${unifiAccessAPI.port}`);
    }
    
    // Test the ensureApiKeySet method
    console.log('\nTesting ensureApiKeySet method...');
    try {
      unifiAccessAPI.ensureApiKeySet();
      console.log('✅ ensureApiKeySet method executed successfully');
      
      // Check if Authorization header is set correctly
      const authHeader = unifiAccessAPI.axios.defaults.headers.common['Authorization'];
      if (authHeader === `Bearer ${process.env.UNIFI_ACCESS_API_KEY}`) {
        console.log('✅ Authorization header is set correctly');
      } else {
        console.log('❌ Authorization header is NOT set correctly');
        console.log(`Expected: Bearer ${process.env.UNIFI_ACCESS_API_KEY}`);
        console.log(`Got: ${authHeader}`);
      }
    } catch (error) {
      console.error('❌ ensureApiKeySet method failed:', error);
    }
    
    console.log('\nTest completed successfully.');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }
}

// Run the test
testUnifiAccessEnv();