/**
 * Test script for the Floating Shortcut Widget database synchronization
 * 
 * This script tests:
 * 1. Retrieving widget preferences from the database
 * 2. Saving widget preferences to the database
 * 3. Verifying that preferences are correctly stored
 * 
 * Run with: node test-floating-shortcut-widget.js
 */

const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB Connected...');
  } catch (err) {
    console.error('MongoDB connection error:', err.message);
    process.exit(1);
  }
};

// Load User model
const User = require('./models/User');

// Test functions
const testWidgetPreferences = async () => {
  try {
    console.log('\n--- Testing Floating Shortcut Widget Preferences ---\n');
    
    // 1. Find a test user
    const testUser = await User.findOne({ email: process.env.TEST_USER_EMAIL || '<EMAIL>' });
    
    if (!testUser) {
      console.error('Test user not found. Please set TEST_USER_EMAIL in .env file.');
      process.exit(1);
    }
    
    console.log(`Found test user: ${testUser.name} (${testUser.email})`);
    
    // 2. Check current widget preferences
    console.log('\nCurrent widget preferences:');
    if (testUser.widgetPreferences && testUser.widgetPreferences.floatingShortcut) {
      console.log(JSON.stringify(testUser.widgetPreferences.floatingShortcut, null, 2));
    } else {
      console.log('No widget preferences found for this user.');
    }
    
    // 3. Create test preferences
    const testPreferences = {
      favoriteShortcuts: [
        // Add some test shortcut IDs here - these should be valid IDs from your shortcuts collection
        '60f1a5c5e6b3f32b4c8d9a1b',
        '60f1a5c5e6b3f32b4c8d9a1c',
        '60f1a5c5e6b3f32b4c8d9a1d'
      ],
      enabled: true
    };
    
    // 4. Save test preferences to the database
    console.log('\nSaving test preferences to the database...');
    
    // Initialize widgetPreferences if it doesn't exist
    if (!testUser.widgetPreferences) {
      testUser.widgetPreferences = {};
    }
    
    // Update the preferences for the floating shortcut widget
    testUser.widgetPreferences.floatingShortcut = testPreferences;
    
    await testUser.save();
    console.log('Test preferences saved successfully.');
    
    // 5. Verify the preferences were saved correctly
    const updatedUser = await User.findById(testUser._id);
    
    console.log('\nUpdated widget preferences:');
    if (updatedUser.widgetPreferences && updatedUser.widgetPreferences.floatingShortcut) {
      console.log(JSON.stringify(updatedUser.widgetPreferences.floatingShortcut, null, 2));
      
      // Verify the preferences match what we saved
      const savedPrefs = updatedUser.widgetPreferences.floatingShortcut;
      const prefsMatch = 
        JSON.stringify(savedPrefs.favoriteShortcuts) === JSON.stringify(testPreferences.favoriteShortcuts) &&
        savedPrefs.enabled === testPreferences.enabled;
      
      if (prefsMatch) {
        console.log('\n✅ Test passed: Preferences were saved correctly to the database.');
      } else {
        console.log('\n❌ Test failed: Saved preferences do not match test preferences.');
      }
    } else {
      console.log('\n❌ Test failed: No widget preferences found after saving.');
    }
    
    console.log('\n--- Test Complete ---');
    
  } catch (err) {
    console.error('Error testing widget preferences:', err);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await testWidgetPreferences();
  mongoose.disconnect();
};

// Run the tests
main();