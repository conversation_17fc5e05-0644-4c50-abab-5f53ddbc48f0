# CSF Portal Deployment Script for Windows
# This script automates the deployment process using Windows Command Prompt by:
# 1. Pulling the latest code from git
# 2. Building the Docker containers
# 3. Stopping the current containers
# 4. Starting the new containers in detached mode

Write-Host "Starting CSF Portal deployment process..." -ForegroundColor Cyan

# Get the current directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Create a log file to capture all output
$logFileName = "deploy_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$logFilePath = Join-Path -Path $scriptDir -ChildPath $logFileName

# Start transcript to capture all output to the log file
Start-Transcript -Path $logFilePath -Append

# Function to display error messages
function Write-ErrorMessage {
    param (
        [string]$Message
    )
    
    Write-Host "ERROR: $Message" -ForegroundColor Red
    Write-Host "Deployment failed. See $logFileName for details." -ForegroundColor Red
    
    # Add Docker troubleshooting information
    Write-Host "`nTROUBLESHOOTING TIPS:" -ForegroundColor Yellow
    Write-Host "- Ensure Docker Desktop is installed and running" -ForegroundColor Yellow
    Write-Host "- Check if Docker is properly configured" -ForegroundColor Yellow
    Write-Host "- If issues persist, try restarting Docker Desktop" -ForegroundColor Yellow
    
    Stop-Transcript
    Write-Host "`nPress Enter to continue..."
    $null = Read-Host
    exit 1
}

# Function to display success messages
function Write-SuccessMessage {
    param (
        [string]$Message
    )
    
    Write-Host "SUCCESS: $Message" -ForegroundColor Green
}

# Function to display info messages
function Write-InfoMessage {
    param (
        [string]$Message
    )
    
    Write-Host "INFO: $Message" -ForegroundColor Cyan
}

# Check if Docker is available
try {
    $dockerVersion = docker --version
    Write-InfoMessage "Docker is installed: $dockerVersion"
} catch {
    Write-ErrorMessage "Docker is not installed or not in PATH. Please install Docker Desktop and ensure it's in your PATH before running this script."
}

# Check if Docker is running
try {
    $dockerInfo = docker info
    Write-InfoMessage "Docker is running"
} catch {
    Write-ErrorMessage "Docker daemon is not running. Please start Docker Desktop before running this script."
}

# Check if docker-compose is available
try {
    $dockerComposeVersion = docker-compose --version
    Write-InfoMessage "Docker Compose is installed: $dockerComposeVersion"
} catch {
    Write-ErrorMessage "docker-compose is not installed or not in PATH. Please install Docker Desktop which includes docker-compose and ensure it's in your PATH."
}

# Display start message
Write-InfoMessage "Starting deployment process for CSF Portal..."

# Step 1: Pull latest code from git
Write-InfoMessage "Step 1: Pulling latest code from git repository..."
try {
    git pull
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMessage "Failed to pull latest code from git repository."
    }
    Write-SuccessMessage "Successfully pulled latest code."
} catch {
    Write-ErrorMessage "Failed to pull latest code from git repository: $_"
}

# Step 2: Build Docker containers
Write-InfoMessage "Step 2: Building Docker containers..."
try {
    docker-compose build
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMessage "Failed to build Docker containers. Please check the error messages above."
    }
    Write-SuccessMessage "Successfully built Docker containers."
} catch {
    Write-ErrorMessage "Failed to build Docker containers: $_"
}

# Step 3: Stop current containers
Write-InfoMessage "Step 3: Stopping current containers..."
try {
    docker-compose down
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMessage "Failed to stop current containers."
    }
    Write-SuccessMessage "Successfully stopped current containers."
} catch {
    Write-ErrorMessage "Failed to stop current containers: $_"
}

# Step 4: Start new containers in detached mode
Write-InfoMessage "Step 4: Starting new containers in detached mode..."
try {
    docker-compose up -d
    if ($LASTEXITCODE -ne 0) {
        Write-ErrorMessage "Failed to start new containers."
    }
    Write-SuccessMessage "Successfully started new containers."
} catch {
    Write-ErrorMessage "Failed to start new containers: $_"
}

# Display completion message
Write-SuccessMessage "Deployment completed successfully!"
Write-InfoMessage "The CSF Portal is now running with the latest code."

# Stop transcript
Stop-Transcript

Write-Host "`nDeployment log has been saved to: $logFileName" -ForegroundColor Green
Write-Host "`nPress Enter to continue..."
$null = Read-Host