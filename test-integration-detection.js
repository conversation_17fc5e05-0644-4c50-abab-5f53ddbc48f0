/**
 * Test script to verify the integration detection mechanism
 * This script tests the enhanced integration detection functionality
 */

const axios = require('axios');

// Base URL for API requests
const baseUrl = 'http://localhost:5000'; // Adjust if your server runs on a different port

// Test function to check if the integration detection mechanism is working correctly
async function testIntegrationDetection() {
  console.log('Testing integration detection mechanism...');
  
  try {
    // Test 1: Get detailed integration status
    console.log('\n1. Testing GET /api/building-management/integrations/status');
    try {
      const statusResponse = await axios.get(`${baseUrl}/api/building-management/integrations/status`);
      console.log('Status code:', statusResponse.status);
      console.log('Response data:', JSON.stringify(statusResponse.data, null, 2));
      
      // Check if the response contains the expected fields
      const hasExpectedFields = statusResponse.data && 
        statusResponse.data.timestamp &&
        statusResponse.data.integrations &&
        statusResponse.data.summary;
      
      console.log('Has expected fields:', hasExpectedFields ? '✅' : '❌');
      
      // Check if the response contains integration status information
      if (statusResponse.data && statusResponse.data.integrations) {
        const integrations = statusResponse.data.integrations;
        console.log('\nIntegration status summary:');
        
        for (const integration in integrations) {
          const status = integrations[integration];
          console.log(`- ${status.name} (${integration}): ${status.status} (${status.active ? 'active' : 'inactive'})`);
          
          if (status.status === 'error') {
            console.log(`  Error type: ${status.errorType}`);
            console.log(`  Error message: ${status.errorMessage}`);
          }
        }
        
        console.log('\nSummary:');
        console.log(`- Total integrations: ${statusResponse.data.summary.total}`);
        console.log(`- Active integrations: ${statusResponse.data.summary.active}`);
        console.log(`- Error integrations: ${statusResponse.data.summary.error}`);
        console.log(`- Disabled integrations: ${statusResponse.data.summary.disabled}`);
      }
    } catch (error) {
      console.error('Error testing integration status endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }
    
    // Test 2: Force reinitialization of integrations
    console.log('\n2. Testing GET /api/building-management/integrations/status?force=true');
    try {
      const reinitResponse = await axios.get(`${baseUrl}/api/building-management/integrations/status?force=true`);
      console.log('Status code:', reinitResponse.status);
      console.log('Response data:', JSON.stringify(reinitResponse.data, null, 2));
      
      // Check if the response contains the expected fields
      const hasExpectedFields = reinitResponse.data && 
        reinitResponse.data.timestamp &&
        reinitResponse.data.integrations &&
        reinitResponse.data.summary;
      
      console.log('Has expected fields:', hasExpectedFields ? '✅' : '❌');
      
      // Check if the response contains integration status information
      if (reinitResponse.data && reinitResponse.data.integrations) {
        const integrations = reinitResponse.data.integrations;
        console.log('\nIntegration status after reinitialization:');
        
        for (const integration in integrations) {
          const status = integrations[integration];
          console.log(`- ${status.name} (${integration}): ${status.status} (${status.active ? 'active' : 'inactive'})`);
          
          if (status.status === 'error') {
            console.log(`  Error type: ${status.errorType}`);
            console.log(`  Error message: ${status.errorMessage}`);
          }
        }
      }
    } catch (error) {
      console.error('Error testing integration reinitialization:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }
    
    // Test 3: Get building management settings
    console.log('\n3. Testing GET /api/building-management/settings');
    try {
      const settingsResponse = await axios.get(`${baseUrl}/api/building-management/settings`);
      console.log('Status code:', settingsResponse.status);
      console.log('Response data (partial):', JSON.stringify({
        ...settingsResponse.data,
        integrationStatus: settingsResponse.data.integrationStatus
      }, null, 2));
      
      // Check if the response contains integration status information
      const hasIntegrationStatus = settingsResponse.data && 
        settingsResponse.data.integrationStatus;
      
      console.log('Has integration status:', hasIntegrationStatus ? '✅' : '❌');
      
      // Check if the integration status contains detailed information
      if (hasIntegrationStatus) {
        const integrationStatus = settingsResponse.data.integrationStatus;
        let hasDetailedStatus = true;
        
        for (const integration in integrationStatus) {
          const status = integrationStatus[integration];
          if (!status.hasOwnProperty('status') || !status.hasOwnProperty('errorType') || !status.hasOwnProperty('errorMessage')) {
            hasDetailedStatus = false;
            break;
          }
        }
        
        console.log('Has detailed status information:', hasDetailedStatus ? '✅' : '❌');
      }
    } catch (error) {
      console.error('Error testing settings endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }
    
    // Test 4: Test system status endpoint
    console.log('\n4. Testing GET /api/building-management/status');
    try {
      const systemStatusResponse = await axios.get(`${baseUrl}/api/building-management/status`);
      console.log('Status code:', systemStatusResponse.status);
      console.log('Response data:', JSON.stringify(systemStatusResponse.data, null, 2));
      
      // Check if the response contains system status information
      const hasSystemStatus = systemStatusResponse.data && 
        systemStatusResponse.data.accessControl &&
        systemStatusResponse.data.climate &&
        systemStatusResponse.data.security &&
        systemStatusResponse.data.network;
      
      console.log('Has system status information:', hasSystemStatus ? '✅' : '❌');
      
      // Check if the system status is based on real data
      if (hasSystemStatus) {
        console.log('\nSystem status:');
        console.log(`- Access Control: ${systemStatusResponse.data.accessControl.status} (${systemStatusResponse.data.accessControl.message})`);
        console.log(`- Climate: ${systemStatusResponse.data.climate.status} (${systemStatusResponse.data.climate.message})`);
        console.log(`- Security: ${systemStatusResponse.data.security.status} (${systemStatusResponse.data.security.message})`);
        console.log(`- Network: ${systemStatusResponse.data.network.status} (${systemStatusResponse.data.network.message})`);
      }
    } catch (error) {
      console.error('Error testing system status endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }
    
    console.log('\nIntegration detection testing completed.');
    console.log('Note: 401 Unauthorized errors are expected if you are not authenticated.');
    console.log('To test with authentication, you need to include a valid session cookie.');
    
  } catch (error) {
    console.error('Error testing integration detection:', error);
  }
}

// Run the test
testIntegrationDetection();