/**
 * Very simple script to check if SkyportCloud environment variables are set
 * 
 * Usage:
 * node check-skyportcloud-env.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Check if environment variables are set
const envApiKey = process.env.SKYPORTCLOUD_API_KEY || '';
const envUsername = process.env.SKYPORTCLOUD_USERNAME || '';
const envPassword = process.env.SKYPORTCLOUD_PASSWORD || '';
const envBaseUrl = process.env.SKYPORTCLOUD_BASE_URL || 'https://api.skyportcloud.com';

console.log('SkyportCloud environment variables:');
console.log(`SKYPORTCLOUD_API_KEY: ${envApiKey ? '✓ Set' : '✗ Not set'}`);
console.log(`SKYPORTCLOUD_USERNAME: ${envUsername ? '✓ Set' : '✗ Not set'}`);
console.log(`SKYPORTCLOUD_PASSWORD: ${envPassword ? '✓ Set' : '✗ Not set'}`);
console.log(`SKYPORTCLOUD_BASE_URL: ${envBaseUrl}`);

// Log the actual values for debugging (length only for sensitive data)
console.log('\nEnvironment variable details:');
console.log(`SKYPORTCLOUD_API_KEY length: ${envApiKey.length}`);
console.log(`SKYPORTCLOUD_USERNAME length: ${envUsername.length}`);
console.log(`SKYPORTCLOUD_PASSWORD length: ${envPassword.length}`);

// Check if we have enough credentials to proceed
if (envApiKey || (envUsername && envPassword)) {
  console.log('\n✅ SkyportCloud environment variables are properly configured.');
  
  if (envUsername && envPassword) {
    console.log('Using username/password authentication:');
    console.log(`Username: ${envUsername}`);
    console.log(`Password length: ${envPassword.length}`);
  } else if (envApiKey) {
    console.log('Using API key authentication:');
    console.log(`API key length: ${envApiKey.length}`);
  }
} else {
  console.error('\n❌ Error: No authentication credentials found in environment variables.');
  console.log('Please set either SKYPORTCLOUD_API_KEY or both SKYPORTCLOUD_USERNAME and SKYPORTCLOUD_PASSWORD in your .env file.');
}

// Create a mock configuration object like the one returned by getConfig
if (envUsername && envPassword) {
  const configData = {
    baseUrl: envBaseUrl,
    configuredAt: new Date(),
    hasApiKey: false,
    hasUsernamePassword: true,
    authMethod: 'usernamePassword',
    fromEnv: true
  };
  
  console.log('\nMock configuration data:');
  console.log(JSON.stringify(configData, null, 2));
} else if (envApiKey) {
  const configData = {
    baseUrl: envBaseUrl,
    configuredAt: new Date(),
    hasApiKey: true,
    hasUsernamePassword: false,
    authMethod: 'apiKey',
    fromEnv: true
  };
  
  console.log('\nMock configuration data:');
  console.log(JSON.stringify(configData, null, 2));
}