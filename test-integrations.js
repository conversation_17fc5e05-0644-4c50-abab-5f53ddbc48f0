// Test script for integration tracking
const UnifiAccessAPI = require('./server/integrations/unifiAccess/unifiAccessAPI');
const DreoAPI = require('./server/integrations/dreo/dreoAPI');
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');
const MosyleBusinessAPI = require('./server/integrations/mosyleBusiness/mosyleBusinessAPI');
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');
const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');
const PlanningCenterAPI = require('./server/integrations/planningCenter/planningCenterAPI');
const SynologyAPI = require('./server/integrations/synology/synologyAPI');
const CanvaAPI = require('./server/integrations/canva/canvaAPI');
const GLPIAPI = require('./server/integrations/glpi/glpiAPI');
const GoogleAdminAPI = require('./server/integrations/googleAdmin/googleAdminAPI');
const GoogleCalendarAPI = require('./server/integrations/googleCalendar/googleCalendarAPI');
const GoogleDriveAPI = require('./server/integrations/googleDrive/googleDriveAPI');
const GoogleFormsAPI = require('./server/integrations/googleForms/googleFormsAPI');
const integrationTracker = require('./server/utils/integrationTracker');

// Test function to initialize and check integration status
async function testIntegration(name, api) {
  console.log(`Testing ${name} integration...`);

  try {
    // Initialize the integration
    await api.initialize();

    // Get the integration status
    const status = integrationTracker.getStatus(name);
    console.log(`${name} status:`, status);
  } catch (error) {
    console.error(`Error testing ${name} integration:`, error.message);
  }
}

// Main test function
async function runTests() {
  console.log('Starting integration tests...');

  // Create instances of each API with dummy credentials
  const unifiAccess = new UnifiAccessAPI('localhost', 'test', 'test');
  const dreo = new DreoAPI('test', 'test');
  const lenelS2NetBox = new LenelS2NetBoxAPI('localhost', 'test', 'test');
  const mosyleBusiness = new MosyleBusinessAPI('test', 'test');
  const unifiNetwork = new UnifiNetworkAPI('localhost', 'test', 'test');
  const unifiProtect = new UnifiProtectAPI('localhost', 'test', 'test');
  const planningCenter = new PlanningCenterAPI('test', 'test');
  const synology = new SynologyAPI('localhost', 'test', 'test');
  const canva = new CanvaAPI('test', 'test');
  const glpi = new GLPIAPI('localhost', 'test', 'test');
  const googleAdmin = new GoogleAdminAPI('test');
  const googleCalendar = new GoogleCalendarAPI('test');
  const googleDrive = new GoogleDriveAPI('test');
  const googleForms = new GoogleFormsAPI('test');

  // Test each integration
  await testIntegration('UniFi Access', unifiAccess);
  await testIntegration('Dreo', dreo);
  await testIntegration('Lenel S2 NetBox', lenelS2NetBox);
  await testIntegration('Mosyle Business', mosyleBusiness);
  await testIntegration('UniFi Network', unifiNetwork);
  await testIntegration('UniFi Protect', unifiProtect);
  await testIntegration('Planning Center', planningCenter);
  await testIntegration('Synology', synology);
  await testIntegration('Canva', canva);
  await testIntegration('GLPI', glpi);
  await testIntegration('Google Admin', googleAdmin);
  await testIntegration('Google Calendar', googleCalendar);
  await testIntegration('Google Drive', googleDrive);
  await testIntegration('Google Forms', googleForms);

  // Print the final integration status
  console.log('\nFinal integration status:');
  console.log(integrationTracker.getAllStatuses());
}

// Run the tests
runTests().catch(error => {
  console.error('Test error:', error);
});
