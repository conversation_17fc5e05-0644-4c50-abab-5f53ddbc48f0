const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});

// Import User model
const User = require('./models/User');

// Test function to verify widget preferences are saved correctly
async function testWidgetPreferences() {
  try {
    console.log('Testing floating shortcut widget preferences...');
    
    // Find any user in the database
    const user = await User.findOne({});
    
    if (!user) {
      console.error('No users found in the database.');
      process.exit(1);
    }
    
    console.log('Found user:', user.name, user.email);
    
    // Current widget preferences
    console.log('Current widget preferences:', JSON.stringify(user.widgetPreferences, null, 2));
    
    // Test data
    const testShortcuts = ['shortcut1', 'shortcut2', 'shortcut3'];
    const testPortalPages = ['dashboard', 'shortcuts', 'drive'];
    
    // Update widget preferences
    if (!user.widgetPreferences) {
      user.widgetPreferences = {};
    }
    
    if (!user.widgetPreferences.floatingShortcut) {
      user.widgetPreferences.floatingShortcut = {};
    }
    
    user.widgetPreferences.floatingShortcut.favoriteShortcuts = testShortcuts;
    user.widgetPreferences.floatingShortcut.favoritePortalPages = testPortalPages;
    user.widgetPreferences.floatingShortcut.enabled = true;
    
    // Save the user
    await user.save();
    console.log('Updated widget preferences saved.');
    
    // Verify the changes were saved
    const updatedUser = await User.findById(user._id);
    console.log('Updated widget preferences:', JSON.stringify(updatedUser.widgetPreferences, null, 2));
    
    // Verify both fields are saved correctly
    const savedShortcuts = updatedUser.widgetPreferences?.floatingShortcut?.favoriteShortcuts || [];
    const savedPortalPages = updatedUser.widgetPreferences?.floatingShortcut?.favoritePortalPages || [];
    
    console.log('Saved shortcuts:', savedShortcuts);
    console.log('Saved portal pages:', savedPortalPages);
    
    // Check if both fields were saved correctly
    const shortcutsMatch = JSON.stringify(savedShortcuts) === JSON.stringify(testShortcuts);
    const portalPagesMatch = JSON.stringify(savedPortalPages) === JSON.stringify(testPortalPages);
    
    if (shortcutsMatch && portalPagesMatch) {
      console.log('TEST PASSED: Both favoriteShortcuts and favoritePortalPages were saved correctly.');
    } else {
      console.log('TEST FAILED:');
      if (!shortcutsMatch) console.log('- favoriteShortcuts not saved correctly');
      if (!portalPagesMatch) console.log('- favoritePortalPages not saved correctly');
    }
    
  } catch (err) {
    console.error('Error testing widget preferences:', err);
  } finally {
    // Close the MongoDB connection
    mongoose.connection.close();
  }
}

// Run the test
testWidgetPreferences();