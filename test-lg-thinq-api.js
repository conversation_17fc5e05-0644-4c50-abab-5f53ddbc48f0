// Test script for LG ThinQ API integration
require('dotenv').config();
const LGThinqAPI = require('./server/integrations/lgThinq/lgThinqAPI');

// Get PAT token, region, and country from environment variables
const patToken = process.env.LG_THINQ_PAT_TOKEN;
const region = process.env.LG_THINQ_REGION || 'america';
const country = process.env.LG_THINQ_COUNTRY || 'US';

// Create a new instance of the LG ThinQ API
const lgThinqAPI = new LGThinqAPI(patToken, region, country);

// Test the API
async function testLGThinqAPI() {
  try {
    console.log('Testing LG ThinQ API integration...');
    console.log(`PAT Token: ${patToken ? '********' : 'Not set'}`);
    console.log(`Region: ${region}`);
    console.log(`Country: ${country}`);
    
    // Initialize the API
    console.log('\nInitializing API...');
    await lgThinqAPI.initialize();
    console.log('API initialized successfully.');
    
    // Get route information
    console.log('\nGetting route information...');
    const routeInfo = await lgThinqAPI.getRouteInfo();
    console.log('Route information:', JSON.stringify(routeInfo, null, 2));
    
    // Get devices
    console.log('\nGetting devices...');
    const devicesResponse = await lgThinqAPI.getDevices();
    
    // Handle the response structure which might have a 'response' property
    const devices = devicesResponse.response || devicesResponse;
    const deviceList = Array.isArray(devices) ? devices : [];
    
    console.log(`Found ${deviceList.length || 0} devices.`);
    
    if (deviceList.length > 0) {
      // Get the first device's ID
      const device = deviceList[0];
      const deviceId = device.deviceId || device.id;
      
      console.log(`\nGetting profile for device ${deviceId}...`);
      const deviceProfile = await lgThinqAPI.getDeviceDetails(deviceId);
      console.log('Device profile:', JSON.stringify(deviceProfile, null, 2));
      
      console.log(`\nGetting state for device ${deviceId}...`);
      const deviceState = await lgThinqAPI.getDeviceStatus(deviceId);
      console.log('Device state:', JSON.stringify(deviceState, null, 2));
    }
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error testing LG ThinQ API:', error);
  }
}

// Run the test
testLGThinqAPI();