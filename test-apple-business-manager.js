// Test script for Apple Business Manager API
const AppleBusinessManagerAPI = require('./server/integrations/appleBusinessManager/appleBusinessManagerAPI');
const fs = require('fs');
const path = require('path');

// Sample private key for testing (this is not a real private key, just a format example)
const SAMPLE_PRIVATE_KEY = `-----BEGIN PRIVATE KEY-----
MIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQgSampleKeyContent
ThisIsNotARealKeyJustAFormatExampleForTestingPurposesOnly1234567890
ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/
-----END PRIVATE KEY-----`;

// Test the API class directly with undefined/empty privateKeyPath
async function testAPIWithUndefinedPath() {
  console.log('\n=== Testing API with undefined privateKeyPath ===');
  try {
    // Create API instance with undefined privateKeyPath
    const api = new AppleBusinessManagerAPI(
      'test-client-id',
      'test-client-secret',
      'test-org-id',
      'test-key-id',
      undefined, // privateKeyPath is undefined
      'test-issuer-id'
    );
    
    // Try to generate JWT (should throw a proper error)
    await api._generateJWT();
    
    console.log('ERROR: _generateJWT did not throw an error with undefined privateKeyPath');
  } catch (error) {
    console.log('Success: Caught expected error:', error.message);
    if (error.message.includes('undefined, empty, or not a string')) {
      console.log('✅ Test passed: Error message indicates proper handling of undefined path');
    } else {
      console.log('❌ Test failed: Error message does not indicate proper handling');
    }
  }
}

async function testAPIWithEmptyPath() {
  console.log('\n=== Testing API with empty privateKeyPath ===');
  try {
    // Create API instance with empty privateKeyPath
    const api = new AppleBusinessManagerAPI(
      'test-client-id',
      'test-client-secret',
      'test-org-id',
      'test-key-id',
      '', // privateKeyPath is empty
      'test-issuer-id'
    );
    
    // Try to generate JWT (should throw a proper error)
    await api._generateJWT();
    
    console.log('ERROR: _generateJWT did not throw an error with empty privateKeyPath');
  } catch (error) {
    console.log('Success: Caught expected error:', error.message);
    if (error.message.includes('undefined, empty, or not a string')) {
      console.log('✅ Test passed: Error message indicates proper handling of empty path');
    } else {
      console.log('❌ Test failed: Error message does not indicate proper handling');
    }
  }
}

async function testAPIInitializeWithUndefinedPath() {
  console.log('\n=== Testing API initialize with undefined privateKeyPath ===');
  try {
    // Create API instance with undefined privateKeyPath
    const api = new AppleBusinessManagerAPI(
      'test-client-id',
      'test-client-secret',
      'test-org-id',
      'test-key-id',
      undefined, // privateKeyPath is undefined
      'test-issuer-id'
    );
    
    // Try to initialize (should throw a proper error or handle gracefully)
    await api.initialize();
    
    console.log('Initialize completed without error (expected behavior if it handles undefined path properly)');
  } catch (error) {
    console.log('Caught error during initialize:', error.message);
    if (error.message.includes('undefined, empty, or not a string')) {
      console.log('✅ Test passed: Error message indicates proper handling of undefined path');
    } else {
      console.log('❌ Test failed: Unexpected error message');
    }
  }
}

async function testAPIWithPrivateKeyContent() {
  console.log('\n=== Testing API with private key content ===');
  try {
    // Create API instance with private key content directly
    const api = new AppleBusinessManagerAPI(
      'test-client-id',
      'test-client-secret',
      'test-org-id',
      'test-key-id',
      SAMPLE_PRIVATE_KEY, // Use the private key content directly
      'test-issuer-id'
    );
    
    // Check if the API correctly identified this as private key content
    if (api.isPrivateKeyContent) {
      console.log('✅ Test passed: API correctly identified the input as private key content');
    } else {
      console.log('❌ Test failed: API did not identify the input as private key content');
      return;
    }
    
    // Mock the jwt.sign function to avoid actual signing
    const originalSign = require('jsonwebtoken').sign;
    require('jsonwebtoken').sign = (payload, key, options) => {
      // Check that the key is the same as our sample key
      if (key === SAMPLE_PRIVATE_KEY) {
        console.log('✅ Test passed: Private key content was passed directly to JWT signing function');
        return 'mock-jwt-token';
      } else {
        console.log('❌ Test failed: Private key content was not passed correctly to JWT signing function');
        return 'mock-jwt-token';
      }
    };
    
    // Try to generate JWT (should use the content directly)
    const token = api._generateJWT();
    
    // Restore the original jwt.sign function
    require('jsonwebtoken').sign = originalSign;
    
    console.log('Successfully generated JWT token with private key content');
  } catch (error) {
    console.log('Error during test:', error.message);
    console.log('❌ Test failed: Unexpected error when using private key content');
  }
}

async function testAPIWithBothPathAndContent() {
  console.log('\n=== Testing API with both path and content (content should take precedence) ===');
  
  // Create a temporary file with different content
  const tempDir = path.join(__dirname, 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir);
  }
  const tempFilePath = path.join(tempDir, 'temp_key.p8');
  const fileContent = '-----BEGIN PRIVATE KEY-----\nDIFFERENT_CONTENT\n-----END PRIVATE KEY-----';
  fs.writeFileSync(tempFilePath, fileContent);
  
  try {
    // First verify the file exists and has different content
    if (fs.existsSync(tempFilePath) && fs.readFileSync(tempFilePath, 'utf8') !== SAMPLE_PRIVATE_KEY) {
      console.log('Temporary file created with different content');
    } else {
      console.log('❌ Test failed: Could not create temporary file with different content');
      return;
    }
    
    // Create API instance with both a valid path and content
    // We'll set up the API to use the content, but provide a valid path
    // The API should prioritize the content over the path
    const api = new AppleBusinessManagerAPI(
      'test-client-id',
      'test-client-secret',
      'test-org-id',
      'test-key-id',
      SAMPLE_PRIVATE_KEY, // Use the private key content directly
      'test-issuer-id'
    );
    
    // Mock the jwt.sign function to avoid actual signing
    const originalSign = require('jsonwebtoken').sign;
    require('jsonwebtoken').sign = (payload, key, options) => {
      // Check that the key is the same as our sample key (not the file content)
      if (key === SAMPLE_PRIVATE_KEY) {
        console.log('✅ Test passed: Private key content was used instead of file content');
        return 'mock-jwt-token';
      } else if (key === fileContent) {
        console.log('❌ Test failed: File content was used instead of private key content');
        return 'mock-jwt-token';
      } else {
        console.log('❌ Test failed: Unexpected key content was used');
        return 'mock-jwt-token';
      }
    };
    
    // Try to generate JWT (should use the content directly)
    const token = api._generateJWT();
    
    // Restore the original jwt.sign function
    require('jsonwebtoken').sign = originalSign;
    
    console.log('Successfully generated JWT token with private key content taking precedence');
  } catch (error) {
    console.log('Error during test:', error.message);
    console.log('❌ Test failed: Unexpected error when testing precedence');
  } finally {
    // Clean up the temporary file
    if (fs.existsSync(tempFilePath)) {
      fs.unlinkSync(tempFilePath);
    }
    if (fs.existsSync(tempDir)) {
      fs.rmdirSync(tempDir);
    }
  }
}

async function runTests() {
  console.log('Starting Apple Business Manager API direct tests...');
  
  // Test with undefined and empty privateKeyPath
  await testAPIWithUndefinedPath();
  await testAPIWithEmptyPath();
  await testAPIInitializeWithUndefinedPath();
  
  // Test with private key content
  await testAPIWithPrivateKeyContent();
  await testAPIWithBothPathAndContent();
  
  console.log('\nAll tests completed.');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});