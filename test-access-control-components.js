/**
 * This script tests if the AccessControl component files exist at the correct paths.
 * It doesn't test functionality or imports, just that the files are present.
 */

const fs = require('fs');
const path = require('path');

async function testAccessControlComponents() {
  try {
    const policyManagementPath = path.join(__dirname, 'client/src/components/AccessControl/PolicyManagement.js');
    const accessLevelManagementPath = path.join(__dirname, 'client/src/components/AccessControl/AccessLevelManagement.js');
    
    console.log('Checking if PolicyManagement component exists...');
    if (fs.existsSync(policyManagementPath)) {
      console.log('✅ PolicyManagement component exists at the correct path');
    } else {
      throw new Error(`PolicyManagement component not found at ${policyManagementPath}`);
    }
    
    console.log('Checking if AccessLevelManagement component exists...');
    if (fs.existsSync(accessLevelManagementPath)) {
      console.log('✅ AccessLevelManagement component exists at the correct path');
    } else {
      throw new Error(`AccessLevelManagement component not found at ${accessLevelManagementPath}`);
    }
    
    console.log('All component files exist at the correct paths!');
    return true;
  } catch (error) {
    console.error('❌ Error:', error.message);
    return false;
  }
}

// Run the test
testAccessControlComponents()
  .then(success => {
    if (success) {
      console.log('Test completed successfully');
      process.exit(0);
    } else {
      console.error('Test failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });