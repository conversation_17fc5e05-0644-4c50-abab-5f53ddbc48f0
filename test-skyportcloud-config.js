/**
 * Simplified test script to verify that the SkyportCloud getConfig method works correctly
 * 
 * This script tests:
 * 1. Loading configuration from environment variables
 * 2. Testing the controller's getConfig method
 * 
 * Usage:
 * node test-skyportcloud-config.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Import the SkyportCloud controller
const skyportCloudController = require('./server/controllers/skyportCloudController');

// Test function
async function testSkyportCloudConfig() {
  console.log('Testing SkyportCloud getConfig method...');
  
  // Check if environment variables are set
  const envApiKey = process.env.SKYPORTCLOUD_API_KEY || '';
  const envUsername = process.env.SKYPORTCLOUD_USERNAME || '';
  const envPassword = process.env.SKYPORTCLOUD_PASSWORD || '';
  const envBaseUrl = process.env.SKYPORTCLOUD_BASE_URL || 'https://api.skyportcloud.com';
  
  console.log('\nEnvironment variables:');
  console.log(`SKYPORTCLOUD_API_KEY: ${envApiKey ? '✓ Set' : '✗ Not set'}`);
  console.log(`SKYPORTCLOUD_USERNAME: ${envUsername ? '✓ Set' : '✗ Not set'}`);
  console.log(`SKYPORTCLOUD_PASSWORD: ${envPassword ? '✓ Set' : '✗ Not set'}`);
  console.log(`SKYPORTCLOUD_BASE_URL: ${envBaseUrl}`);
  
  try {
    // Test the controller's getConfig method
    console.log('\nTesting controller getConfig method...');
    
    // Mock Express request and response objects
    const req = {};
    const res = {
      json: (data) => {
        console.log('✅ getConfig returned:');
        console.log(JSON.stringify(data, null, 2));
      },
      status: (code) => ({
        json: (data) => {
          console.error(`❌ getConfig returned status ${code}:`, data);
        }
      })
    };
    
    // Call the getConfig method
    await skyportCloudController.getConfig(req, res);
  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

// Run the test
testSkyportCloudConfig().catch(console.error);