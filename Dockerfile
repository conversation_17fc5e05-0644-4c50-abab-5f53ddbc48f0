# Use Node.js 20.18.1 (Debian slim) as the base image to satisfy engines and provide glibc for prebuilt binaries
FROM node:20.18.1-bookworm-slim

# Set working directory
WORKDIR /app

# Install runtime libraries required by node-canvas (prebuilt binary dependencies)
RUN apt-get update && apt-get install -y --no-install-recommends \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libjpeg62-turbo \
    libgif7 \
    librsvg2-2 \
 && rm -rf /var/lib/apt/lists/*

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci --omit=dev

# Copy client package.json and package-lock.json
COPY client/package*.json ./client/

# Copy the rest of the application code
COPY . .

# Install client dependencies and build (ensuring dev dependencies are included)
# Using a more Windows-compatible approach
WORKDIR /app/client
RUN NPM_CONFIG_PRODUCTION=false npm install
# Fix for Windows path resolution issue with react-app-rewired
RUN npx react-app-rewired build
WORKDIR /app

# Expose the ports the app runs on
EXPOSE 8080
# Expose RADIUS server ports (UDP)
EXPOSE 1812/udp 1813/udp

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
# RADIUS server environment variables
# Note: For security, RADIUS_SECRET should be provided at runtime
# TLS can be enabled by setting RADIUS_TLS_ENABLED=true and providing key/cert paths
ENV RADIUS_PORT=1812
ENV RADIUS_AUTHORIZATION_PORT=1813
ENV RADIUS_ADDRESS=0.0.0.0
ENV RADIUS_AUTHENTICATION=StaticAuth

# Start the application
CMD ["node", "server.js"]
