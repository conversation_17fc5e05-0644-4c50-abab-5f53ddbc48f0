/**
 * Test script for Planning Center people pagination and search
 * 
 * This script tests the Planning Center people directory API endpoint
 * to verify that pagination and search functionality work correctly.
 */

const axios = require('axios');
require('dotenv').config();

// Set up authentication
const username = process.env.USER_EMAIL || '<EMAIL>';
const password = process.env.USER_PASSWORD || 'password';

// Test parameters
const baseUrl = 'http://localhost:5000'; // Change if your server runs on a different port
const endpoint = '/api/planning-center/people-directory';

// Test cases
const testCases = [
  { 
    name: 'Default pagination (page 1)',
    params: {}
  },
  { 
    name: 'Page 2 with 10 items per page',
    params: { page: 2, per_page: 10 }
  },
  { 
    name: 'Search for "smith"',
    params: { search: 'smith' }
  },
  { 
    name: 'Search with pagination',
    params: { search: 'john', page: 1, per_page: 5 }
  }
];

// Login function
async function login() {
  try {
    const response = await axios.post(`${baseUrl}/api/auth/login`, {
      email: username,
      password: password
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// Test function
async function runTests() {
  try {
    console.log('Starting Planning Center pagination and search tests...');
    
    // Login to get token
    const token = await login();
    console.log('Login successful');
    
    // Set up axios with authentication
    const authAxios = axios.create({
      baseURL: baseUrl,
      headers: {
        'x-auth-token': token
      }
    });
    
    // Run each test case
    for (const testCase of testCases) {
      console.log(`\nRunning test: ${testCase.name}`);
      console.log(`Parameters: ${JSON.stringify(testCase.params)}`);
      
      try {
        const response = await authAxios.get(endpoint, { params: testCase.params });
        const { people, pagination, meta } = response.data;
        
        console.log('Test results:');
        console.log(`- People count: ${people?.length || 0}`);
        console.log(`- Total count: ${pagination?.totalCount || meta?.total_count || 'N/A'}`);
        console.log(`- Current page: ${pagination?.currentPage || meta?.current_page || 'N/A'}`);
        console.log(`- Total pages: ${pagination?.totalPages || meta?.total_pages || 'N/A'}`);
        
        if (people && people.length > 0) {
          console.log('- First person:', people[0].name);
          console.log('- Last person:', people[people.length - 1].name);
        }
        
        console.log('Test passed ✓');
      } catch (error) {
        console.error('Test failed:', error.response?.data || error.message);
      }
    }
    
    console.log('\nAll tests completed');
  } catch (error) {
    console.error('Test runner failed:', error);
  }
}

// Run the tests
runTests();