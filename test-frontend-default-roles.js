/**
 * Test script to verify the frontend configuration for default roles
 * 
 * This script demonstrates how to:
 * 1. Retrieve the current default role settings
 * 2. Update the default role settings
 * 3. Verify that the settings are saved correctly
 * 
 * To run this test:
 * 1. Make sure the server is running
 * 2. Run: node test-frontend-default-roles.js
 */

require('dotenv').config();
const axios = require('axios');
const mongoose = require('mongoose');
const RoleSettings = require('./models/RoleSettings');
const Role = require('./models/Role');

// Set up axios with credentials
axios.defaults.withCredentials = true;
axios.defaults.baseURL = 'http://localhost:8080';

// Admin credentials for login
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'password123';

// Test settings
const TEST_SETTINGS = {
  defaultRoleLocalUsers: 'editor',
  defaultRoleGoogleUsers: 'viewer',
  googleGroupsRoleMapping: [
    { groupEmail: '<EMAIL>', roleName: 'admin' },
    { groupEmail: '<EMAIL>', roleName: 'editor' }
  ]
};

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (err) {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  }
}

// Login as admin
async function login() {
  try {
    const response = await axios.post('/api/auth/login', {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });
    
    console.log('Logged in successfully');
    return response.data;
  } catch (err) {
    console.error('Login failed:', err.response?.data || err.message);
    process.exit(1);
  }
}

// Get current role settings
async function getCurrentSettings() {
  try {
    const response = await axios.get('/api/roles/settings');
    console.log('Current settings:', response.data);
    return response.data;
  } catch (err) {
    console.error('Error getting settings:', err.response?.data || err.message);
    process.exit(1);
  }
}

// Update role settings
async function updateSettings(settings) {
  try {
    const response = await axios.put('/api/roles/settings', settings);
    console.log('Settings updated successfully:', response.data);
    return response.data;
  } catch (err) {
    console.error('Error updating settings:', err.response?.data || err.message);
    process.exit(1);
  }
}

// Verify settings in database
async function verifySettingsInDatabase(expectedSettings) {
  try {
    const settings = await RoleSettings.findOne();
    
    if (!settings) {
      console.error('No settings found in database');
      return false;
    }
    
    const isValid = 
      settings.defaultRoleLocalUsers === expectedSettings.defaultRoleLocalUsers &&
      settings.defaultRoleGoogleUsers === expectedSettings.defaultRoleGoogleUsers &&
      settings.googleGroupsRoleMapping.length === expectedSettings.googleGroupsRoleMapping.length;
    
    if (isValid) {
      console.log('✅ Settings verified in database');
      return true;
    } else {
      console.error('❌ Settings in database do not match expected settings');
      console.log('Expected:', expectedSettings);
      console.log('Actual:', settings);
      return false;
    }
  } catch (err) {
    console.error('Error verifying settings in database:', err);
    return false;
  }
}

// Create test roles if they don't exist
async function createTestRoles() {
  try {
    // Check if roles exist
    const roles = ['admin', 'editor', 'viewer'];
    
    for (const roleName of roles) {
      const role = await Role.findOne({ name: roleName });
      
      if (!role) {
        console.log(`Creating role: ${roleName}`);
        const newRole = new Role({
          name: roleName,
          description: `Test role: ${roleName}`,
          permissions: roleName === 'admin' ? ['*'] : [`${roleName}:read`],
          isDefault: roleName === 'admin'
        });
        
        await newRole.save();
      }
    }
    
    console.log('Test roles created or verified');
  } catch (err) {
    console.error('Error creating test roles:', err);
  }
}

// Main test function
async function runTest() {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Create test roles
    await createTestRoles();
    
    // Login as admin
    await login();
    
    // Get current settings
    const originalSettings = await getCurrentSettings();
    
    // Update settings
    await updateSettings(TEST_SETTINGS);
    
    // Verify settings
    const updatedSettings = await getCurrentSettings();
    
    // Verify settings in database
    const verified = await verifySettingsInDatabase(TEST_SETTINGS);
    
    if (verified) {
      console.log('✅ Test passed: Settings were updated and verified successfully');
    } else {
      console.error('❌ Test failed: Settings were not updated correctly');
    }
    
    // Restore original settings
    await updateSettings(originalSettings);
    console.log('Original settings restored');
    
  } catch (err) {
    console.error('Test failed:', err);
  } finally {
    // Disconnect from database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
runTest()
  .then(() => {
    console.log('Test completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error running test:', err);
    process.exit(1);
  });