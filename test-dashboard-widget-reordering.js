/**
 * Test script to verify that widget reordering persists after saving
 * 
 * This script simulates the process of:
 * 1. Entering edit mode
 * 2. Reordering widgets
 * 3. Saving the changes
 * 4. Verifying that the changes persist
 */

const axios = require('axios');
const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

// MongoDB connection string
const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal';

// Test user ID - replace with a valid user ID from your database
const testUserId = '60a1b2c3d4e5f6g7h8i9j0k1';

// Function to get current dashboard preferences
async function getDashboardPreferences() {
  try {
    const client = new MongoClient(mongoURI);
    await client.connect();
    
    const db = client.db();
    const preferences = await db.collection('dashboardpreferences').findOne({ user: new ObjectId(testUserId) });
    
    await client.close();
    return preferences;
  } catch (err) {
    console.error('Error getting dashboard preferences:', err);
    throw err;
  }
}

// Function to update dashboard preferences with reordered widgets
async function updateDashboardPreferences(preferences) {
  try {
    // Create a copy of the preferences
    const updatedPreferences = JSON.parse(JSON.stringify(preferences));
    
    // Reorder widgets by swapping the positions of the first two widgets
    if (updatedPreferences.widgets.length >= 2) {
      const widget1 = updatedPreferences.widgets[0];
      const widget2 = updatedPreferences.widgets[1];
      
      // Swap positions
      const tempPosition = widget1.position;
      widget1.position = widget2.position;
      widget2.position = tempPosition;
      
      console.log('Reordered widgets:');
      console.log('Widget 1:', widget1.title, widget1.position);
      console.log('Widget 2:', widget2.title, widget2.position);
    } else {
      console.log('Not enough widgets to reorder');
      return null;
    }
    
    // Update preferences in database
    const client = new MongoClient(mongoURI);
    await client.connect();
    
    const db = client.db();
    await db.collection('dashboardpreferences').updateOne(
      { user: new ObjectId(testUserId) },
      { $set: { widgets: updatedPreferences.widgets } }
    );
    
    await client.close();
    return updatedPreferences;
  } catch (err) {
    console.error('Error updating dashboard preferences:', err);
    throw err;
  }
}

// Function to verify that widget reordering persists
async function verifyWidgetReordering(originalPreferences, updatedPreferences) {
  try {
    // Get current preferences from database
    const currentPreferences = await getDashboardPreferences();
    
    // Check if the widget positions match the updated positions
    const widget1Original = originalPreferences.widgets[0];
    const widget2Original = originalPreferences.widgets[1];
    
    const widget1Updated = updatedPreferences.widgets[0];
    const widget2Updated = updatedPreferences.widgets[1];
    
    const widget1Current = currentPreferences.widgets.find(w => w._id.toString() === widget1Original._id.toString());
    const widget2Current = currentPreferences.widgets.find(w => w._id.toString() === widget2Original._id.toString());
    
    console.log('\nVerifying widget positions:');
    console.log('Widget 1 original position:', widget1Original.position);
    console.log('Widget 1 updated position:', widget1Updated.position);
    console.log('Widget 1 current position:', widget1Current.position);
    
    console.log('\nWidget 2 original position:', widget2Original.position);
    console.log('Widget 2 updated position:', widget2Updated.position);
    console.log('Widget 2 current position:', widget2Current.position);
    
    // Check if the current positions match the updated positions
    const widget1Matches = 
      widget1Current.position.x === widget1Updated.position.x &&
      widget1Current.position.y === widget1Updated.position.y;
    
    const widget2Matches = 
      widget2Current.position.x === widget2Updated.position.x &&
      widget2Current.position.y === widget2Updated.position.y;
    
    if (widget1Matches && widget2Matches) {
      console.log('\nSUCCESS: Widget reordering persisted after saving');
      return true;
    } else {
      console.log('\nFAILURE: Widget reordering did not persist after saving');
      return false;
    }
  } catch (err) {
    console.error('Error verifying widget reordering:', err);
    throw err;
  }
}

// Main test function
async function testWidgetReordering() {
  try {
    console.log('Starting widget reordering test...');
    
    // Get original preferences
    const originalPreferences = await getDashboardPreferences();
    console.log('Original preferences retrieved');
    
    // Update preferences with reordered widgets
    const updatedPreferences = await updateDashboardPreferences(originalPreferences);
    if (!updatedPreferences) {
      console.log('Test skipped: Not enough widgets to reorder');
      return;
    }
    console.log('Preferences updated with reordered widgets');
    
    // Verify that widget reordering persists
    const success = await verifyWidgetReordering(originalPreferences, updatedPreferences);
    
    if (success) {
      console.log('Test passed: Widget reordering persists after saving');
    } else {
      console.log('Test failed: Widget reordering does not persist after saving');
    }
  } catch (err) {
    console.error('Test error:', err);
  }
}

// Run the test
testWidgetReordering();