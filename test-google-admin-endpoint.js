/**
 * Test script for the Google Admin users endpoint
 * 
 * This script tests the /api/google-admin/users endpoint to verify that it works correctly.
 * Note: This requires authentication, so it may need to be run in a browser where you're already logged in.
 * 
 * Usage:
 * node test-google-admin-endpoint.js
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Base URL for API requests
const baseUrl = process.env.FRONTEND_URL || 'http://localhost:8080';

// Function to test the Google Admin users endpoint
async function testGoogleAdminEndpoint() {
  try {
    console.log(`Testing ${baseUrl}/api/google-admin/users endpoint...`);
    
    // Make a GET request to the endpoint
    const response = await axios.get(`${baseUrl}/api/google-admin/users`, {
      // Add authentication if needed
      // headers: { Authorization: `Bearer ${token}` }
    });
    
    // Log the response status
    console.log(`Response status: ${response.status}`);
    
    // Check if the response contains users
    if (response.data && Array.isArray(response.data)) {
      console.log(`Successfully retrieved ${response.data.length} users`);
      
      // Log the first user (if available)
      if (response.data.length > 0) {
        const firstUser = response.data[0];
        console.log('First user:', {
          id: firstUser.id,
          primaryEmail: firstUser.primaryEmail,
          name: firstUser.name ? firstUser.name.fullName : 'N/A'
        });
      }
    } else {
      console.log('Response data is not an array of users:', response.data);
    }
    
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error testing Google Admin users endpoint:');
    
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from server');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
  }
}

// Run the test
testGoogleAdminEndpoint();