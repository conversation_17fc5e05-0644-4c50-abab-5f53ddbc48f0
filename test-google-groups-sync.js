/**
 * Test script to verify that teams and groups are being pulled from Google
 * 
 * This script:
 * 1. Authenticates a user
 * 2. Calls the sync-profile endpoint
 * 3. Logs the user's teams and groups
 * 
 * Usage: node test-google-groups-sync.js
 */

const axios = require('axios');
const dotenv = require('dotenv');
const mongoose = require('mongoose');
const User = require('./models/User');
const { getAuthenticatedClient } = require('./server/utils/googleServiceAuth');
const { google } = require('googleapis');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

/**
 * Test the sync-profile endpoint
 */
async function testSyncProfile() {
  try {
    // Get the first user from the database
    const user = await User.findOne({ isActive: true });
    
    if (!user) {
      console.error('No active users found in the database');
      process.exit(1);
    }
    
    console.log(`Testing with user: ${user.name} (${user.email})`);
    
    // Get user's groups directly from Google Admin API for comparison
    console.log('\nFetching groups directly from Google Admin API...');
    const adminScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.user.readonly',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.group.readonly'
    ];
    
    const auth = await getAuthenticatedClient('Admin', adminScopes, user.email);
    const admin = google.admin({ version: 'directory_v1', auth });
    
    const googleGroupsResponse = await admin.groups.list({
      userKey: user.email
    });
    
    const googleGroups = googleGroupsResponse.data.groups || [];
    console.log('Google groups:', googleGroups.map(g => g.name));
    
    // Get user's current teams and groups from database
    const userBefore = await User.findById(user._id)
      .populate('teams', 'name description')
      .populate('groups', 'name description');
    
    console.log('\nUser teams before sync:', userBefore.teams.map(t => t.name));
    console.log('User groups before sync:', userBefore.groups.map(g => g.name));
    
    // Call the sync-profile endpoint
    console.log('\nCalling sync-profile endpoint...');
    // Create a server instance and make a request to the endpoint
    // This is a simplified approach - in a real test, you would use a test framework
    const express = require('express');
    const app = express();
    const staffDirectoryRoutes = require('./routes/api/staffDirectory');
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.user = {
        id: user._id,
        email: user.email
      };
      next();
    });
    
    app.use('/api/staff-directory', staffDirectoryRoutes);
    
    // Start the server
    const server = app.listen(0, async () => {
      const port = server.address().port;
      console.log(`Test server running on port ${port}`);
      
      try {
        // Make a request to the sync-profile endpoint
        const response = await axios.post(`http://localhost:${port}/api/staff-directory/google/sync-profile`);
        
        console.log('Sync profile response:', response.data);
        
        // Get user's updated teams and groups from database
        const userAfter = await User.findById(user._id)
          .populate('teams', 'name description')
          .populate('groups', 'name description');
        
        console.log('\nUser teams after sync:', userAfter.teams.map(t => t.name));
        console.log('User groups after sync:', userAfter.groups.map(g => g.name));
        
        // Verify that the groups match what we got directly from Google
        const googleGroupNames = googleGroups.map(g => g.name);
        const userGroupNames = userAfter.groups.map(g => g.name);
        
        const missingGroups = googleGroupNames.filter(name => !userGroupNames.includes(name));
        const extraGroups = userGroupNames.filter(name => !googleGroupNames.includes(name));
        
        if (missingGroups.length > 0) {
          console.warn('Warning: Some Google groups are missing from the user\'s groups:', missingGroups);
        }
        
        if (extraGroups.length > 0) {
          console.warn('Warning: User has groups that are not in Google:', extraGroups);
        }
        
        if (missingGroups.length === 0 && extraGroups.length === 0) {
          console.log('\nSuccess! All Google groups are correctly synced to the user\'s profile.');
        }
        
        // Check if any teams were derived from Google groups
        const teamGroups = googleGroups.filter(g => g.name.startsWith('Team-'));
        if (teamGroups.length > 0) {
          console.log('\nTeams derived from Google groups:');
          for (const group of teamGroups) {
            const teamName = group.name.substring(5); // Remove "Team-" prefix
            const teamExists = userAfter.teams.some(t => t.name === teamName);
            console.log(`- ${teamName}: ${teamExists ? 'Successfully added to user\'s teams' : 'Not found in user\'s teams'}`);
          }
        } else {
          console.log('\nNo Google groups with "Team-" prefix found.');
        }
      } catch (error) {
        console.error('Error calling sync-profile endpoint:', error.response?.data || error.message);
      } finally {
        // Close the server and database connection
        server.close();
        mongoose.disconnect();
        console.log('Test completed');
      }
    });
  } catch (error) {
    console.error('Test error:', error);
    mongoose.disconnect();
  }
}

// Run the test
testSyncProfile();