/**
 * Test script for the UniFi Network API client implementation
 * 
 * This script tests the basic functionality of the UniFi Network API client
 * using the @panoptic-it-solutions/unifi-api-client package.
 * 
 * Usage: node test-unifi-network-api-client.js
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API client
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get configuration from environment variables
const host = process.env.UNIFI_NETWORK_HOST || '';
const apiKey = process.env.UNIFI_NETWORK_API_KEY || '';
const username = process.env.UNIFI_NETWORK_USERNAME || 'admin';
const password = process.env.UNIFI_NETWORK_PASSWORD || apiKey;
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Check if required configuration is provided
if (!host) {
  console.error('Error: UNIFI_NETWORK_HOST environment variable is required');
  process.exit(1);
}

if (!apiKey && !password) {
  console.error('Error: Either UNIFI_NETWORK_API_KEY or UNIFI_NETWORK_PASSWORD environment variable is required');
  process.exit(1);
}

// Create a new instance of the UniFi Network API client
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Test the UniFi Network API client
async function testUnifiNetworkAPI() {
  try {
    console.log('Testing UniFi Network API client...');
    
    // Initialize the API client
    console.log('Initializing API client...');
    await unifiNetworkAPI.initialize();
    console.log('API client initialized successfully');
    
    // Test getting devices
    console.log('\nTesting getDevices()...');
    const devices = await unifiNetworkAPI.getDevices();
    console.log(`Successfully retrieved ${devices.data.length} devices`);
    console.log('First device:', JSON.stringify(devices.data[0], null, 2));
    
    // Test getting clients
    console.log('\nTesting getClients()...');
    const clients = await unifiNetworkAPI.getClients();
    console.log(`Successfully retrieved ${clients.data.length} clients`);
    if (clients.data.length > 0) {
      console.log('First client:', JSON.stringify(clients.data[0], null, 2));
    }
    
    // Test getting networks
    console.log('\nTesting getNetworks()...');
    const networks = await unifiNetworkAPI.getNetworks();
    console.log(`Successfully retrieved ${networks.length} networks`);
    if (networks.length > 0) {
      console.log('First network:', JSON.stringify(networks[0], null, 2));
    }
    
    // Test getting wireless networks
    console.log('\nTesting getWirelessNetworks()...');
    const wlans = await unifiNetworkAPI.getWirelessNetworks();
    console.log(`Successfully retrieved ${wlans.length} wireless networks`);
    if (wlans.length > 0) {
      console.log('First wireless network:', JSON.stringify(wlans[0], null, 2));
    }
    
    // Test getting system status
    console.log('\nTesting getSystemStatus()...');
    const status = await unifiNetworkAPI.getSystemStatus();
    console.log('System status:', JSON.stringify(status, null, 2));
    
    // Test getting site statistics
    console.log('\nTesting getSiteStatistics()...');
    const siteStats = await unifiNetworkAPI.getSiteStatistics();
    console.log('Site statistics:', JSON.stringify(siteStats, null, 2));
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error testing UniFi Network API client:', error);
  }
}

// Run the test
testUnifiNetworkAPI();