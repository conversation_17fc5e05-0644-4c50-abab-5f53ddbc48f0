require('dotenv').config();
const gmailWebhookController = require('./server/controllers/gmailWebhookController');

/**
 * Test script to verify the Gmail webhook controller can process emails correctly
 * This script simulates receiving an <NAME_EMAIL> and processes it through the webhook controller
 */
async function testGmailWebhook() {
  try {
    console.log('Testing Gmail webhook controller...');
    
    // Create a sample email data object
    const emailData = {
      messageId: `test-${Date.now()}@ukcsf.org`,
      from: '<EMAIL>',
      to: process.env.GMAIL_MONITORED_EMAIL || '<EMAIL>',
      subject: 'Test Email for Gmail Webhook',
      text: 'This is a test email to verify the Gmail webhook controller is working correctly.',
      html: '<p>This is a test email to verify the Gmail webhook controller is working correctly.</p>',
      inReplyTo: null,
      references: null,
      headers: {
        'from': 'Test User <<EMAIL>>',
        'to': process.env.GMAIL_MONITORED_EMAIL || '<EMAIL>',
        'subject': 'Test Email for Gmail Webhook',
        'message-id': `test-${Date.now()}@ukcsf.org`
      },
      attachments: []
    };
    
    // Simulate a webhook request with the email data
    await gmailWebhookController.simulateWebhookRequest(emailData);
    
    console.log('Test completed. Check the logs above to see if the email was processed correctly.');
    console.log('If successful, you should see logs indicating a ticket was created or updated.');
    
  } catch (error) {
    console.error('Error testing Gmail webhook:', error);
  }
}

// Run the test
testGmailWebhook();