/**
 * Test script for Constant Contact integration
 * 
 * This script tests the Constant Contact integration by:
 * 1. Initializing the Constant Contact API
 * 2. Getting account information
 * 3. Getting contact lists
 * 4. Getting contacts
 * 5. Getting campaigns
 * 
 * Usage:
 * node test-constant-contact-integration.js
 * 
 * Requirements:
 * - CONSTANT_CONTACT_CLIENT_ID must be set in the .env file
 * - CONSTANT_CONTACT_CLIENT_SECRET must be set in the .env file
 * - CONSTANT_CONTACT_ACCESS_TOKEN must be set in the .env file
 * - CONSTANT_CONTACT_REFRESH_TOKEN should be set in the .env file (optional)
 */

require('dotenv').config();
const ConstantContactAPI = require('./server/integrations/constantContact/constantContactAPI');

// Create a new instance of the Constant Contact API
const constantContactAPI = new ConstantContactAPI();

// Test the Constant Contact integration
async function testConstantContactIntegration() {
  console.log('Testing Constant Contact integration...');
  
  try {
    // Step 1: Initialize the Constant Contact API
    console.log('\n1. Initializing Constant Contact API...');
    await constantContactAPI.initialize();
    console.log('✅ Constant Contact API initialized successfully');
    
    // Step 2: Get account information
    console.log('\n2. Getting account information...');
    const accountInfo = await constantContactAPI.getAccountInfo();
    
    if (!accountInfo) {
      console.log('❌ Failed to get account information. Make sure your Constant Contact credentials are correct.');
      return;
    }
    
    console.log('✅ Account information retrieved successfully:');
    console.log(`   - Organization Name: ${accountInfo.organization_name || 'N/A'}`);
    console.log(`   - Email: ${accountInfo.email || 'N/A'}`);
    console.log(`   - Phone: ${accountInfo.phone || 'N/A'}`);
    console.log(`   - Website: ${accountInfo.website || 'N/A'}`);
    console.log(`   - Address: ${accountInfo.address ? `${accountInfo.address.line1}, ${accountInfo.address.city}, ${accountInfo.address.state} ${accountInfo.address.postal_code}` : 'N/A'}`);
    
    // Step 3: Get contact lists
    console.log('\n3. Getting contact lists...');
    const contactLists = await constantContactAPI.getContactLists();
    
    if (!contactLists || contactLists.length === 0) {
      console.log('⚠️ No contact lists found.');
    } else {
      console.log(`✅ Found ${contactLists.length} contact lists:`);
      contactLists.forEach((list, index) => {
        console.log(`   List ${index + 1}:`);
        console.log(`   - Name: ${list.name}`);
        console.log(`   - ID: ${list.list_id}`);
        console.log(`   - Contact Count: ${list.contact_count}`);
        console.log(`   - Status: ${list.status}`);
        console.log(`   - Created: ${new Date(list.created_at).toLocaleString()}`);
        console.log('');
      });
    }
    
    // Step 4: Get contacts
    console.log('\n4. Getting contacts...');
    const contactsResponse = await constantContactAPI.getContacts({ limit: 10 });
    
    if (!contactsResponse || !contactsResponse.contacts || contactsResponse.contacts.length === 0) {
      console.log('⚠️ No contacts found.');
    } else {
      console.log(`✅ Found ${contactsResponse.contacts.length} contacts (showing first 10):`);
      contactsResponse.contacts.forEach((contact, index) => {
        console.log(`   Contact ${index + 1}:`);
        console.log(`   - Email: ${contact.email_address}`);
        console.log(`   - Name: ${contact.first_name || ''} ${contact.last_name || ''}`);
        console.log(`   - Status: ${contact.status}`);
        console.log(`   - Created: ${new Date(contact.created_at).toLocaleString()}`);
        console.log('');
      });
    }
    
    // Step 5: Get campaigns
    console.log('\n5. Getting campaigns...');
    const campaignsResponse = await constantContactAPI.getCampaigns({ limit: 10 });
    
    if (!campaignsResponse || !campaignsResponse.campaigns || campaignsResponse.campaigns.length === 0) {
      console.log('⚠️ No campaigns found.');
    } else {
      console.log(`✅ Found ${campaignsResponse.campaigns.length} campaigns (showing first 10):`);
      campaignsResponse.campaigns.forEach((campaign, index) => {
        console.log(`   Campaign ${index + 1}:`);
        console.log(`   - Name: ${campaign.name}`);
        console.log(`   - ID: ${campaign.campaign_id}`);
        console.log(`   - Status: ${campaign.status}`);
        console.log(`   - Created: ${new Date(campaign.created_at).toLocaleString()}`);
        console.log(`   - Last Updated: ${new Date(campaign.updated_at).toLocaleString()}`);
        console.log('');
      });
      
      // Optional: Get tracking data for the first campaign
      if (campaignsResponse.campaigns.length > 0) {
        const firstCampaign = campaignsResponse.campaigns[0];
        console.log(`\n6. Getting tracking data for campaign "${firstCampaign.name}"...`);
        
        const trackingData = await constantContactAPI.getCampaignTracking(firstCampaign.campaign_id);
        
        if (!trackingData) {
          console.log('⚠️ No tracking data available for this campaign.');
        } else {
          console.log('✅ Campaign tracking data retrieved successfully:');
          console.log(`   - Sends: ${trackingData.sends || 0}`);
          console.log(`   - Opens: ${trackingData.opens || 0} (${trackingData.opens_rate || '0%'})`);
          console.log(`   - Clicks: ${trackingData.clicks || 0} (${trackingData.clicks_rate || '0%'})`);
          console.log(`   - Bounces: ${trackingData.bounces || 0} (${trackingData.bounces_rate || '0%'})`);
          console.log(`   - Unsubscribes: ${trackingData.unsubscribes || 0} (${trackingData.unsubscribes_rate || '0%'})`);
        }
      }
    }
    
    console.log('\n✅ Constant Contact integration test completed successfully');
    
  } catch (error) {
    console.error('\n❌ Error testing Constant Contact integration:', error);
  }
}

// Run the test
testConstantContactIntegration().catch(console.error);