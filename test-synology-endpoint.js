// Test script to directly test the /api/synology/files endpoint
require('dotenv').config();
const axios = require('axios');

async function testSynologyEndpoint() {
  try {
    console.log('Testing /api/synology/files endpoint with root path...');
    
    // You'll need to be authenticated to access this endpoint
    // For testing purposes, you can use a valid session cookie or token
    // This is just a basic example - you might need to adjust it based on your authentication setup
    
    // For testing purposes, we'll bypass authentication by directly calling the controller
    // This is not recommended for production, but it's useful for testing
    const synologyController = require('./server/controllers/synologyController');
    
    // Create mock request and response objects
    // Try a specific subdirectory that might have fewer files
    const req = {
      query: {
        path: '/volume1/homes',
        limit: 5  // Set a very small limit to avoid timeouts
      }
    };
    
    const res = {
      status: function(statusCode) {
        console.log('Response status:', statusCode);
        return this;
      },
      json: function(data) {
        console.log('Response data:', JSON.stringify(data, null, 2));
        return this;
      },
      send: function(data) {
        console.log('Response data (binary):', data ? 'Binary data received' : 'No data');
        return this;
      }
    };
    
    // Call the controller directly
    await synologyController.listFiles(req, res);
    
  } catch (error) {
    console.error('Error testing Synology endpoint:', error.message);
    
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
      
      // If we got a 500 error, let's examine the error more closely
      if (error.response.status === 500) {
        console.error('500 Internal Server Error detected. This matches the reported issue.');
        console.error('Error details:', error.response.data);
      }
    }
  }
}

// Run the test
testSynologyEndpoint();