/**
 * Test script for the Google Admin API integration
 * 
 * This script directly tests the GoogleAdminAPI class to verify that the fix works correctly.
 * It bypasses the Express route and controller to test the core functionality.
 * 
 * Usage:
 * node test-google-admin-users-endpoint.js
 */

const dotenv = require('dotenv');
const GoogleAdminAPI = require('./server/integrations/googleAdmin/googleAdminAPI');
const path = require('path');

// Load environment variables
dotenv.config();

// Function to test the Google Admin API directly
async function testGoogleAdminAPI() {
  try {
    console.log('Testing Google Admin API directly...');
    
    // Get environment variables for Google Admin API
    const clientId = process.env.GOOGLE_ADMIN_CLIENT_ID || '';
    const clientSecret = process.env.GOOGLE_ADMIN_CLIENT_SECRET || '';
    const redirectUri = process.env.GOOGLE_ADMIN_REDIRECT_URI || '';
    const tokenPath = path.resolve(process.cwd(), process.env.GOOGLE_ADMIN_TOKEN_PATH || './google-admin-token.json');
    const impersonationEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL || '';
    
    console.log('Using service account email:', process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL);
    console.log('Using impersonation email:', impersonationEmail);
    console.log('Token path:', tokenPath);
    
    // Create a new instance of GoogleAdminAPI
    const googleAdminAPI = new GoogleAdminAPI(
      clientId,
      clientSecret,
      redirectUri,
      tokenPath,
      null, // No user tokens
      null, // No user ID
      impersonationEmail // Pass the impersonation email for service account authentication
    );
    
    // Initialize the API
    console.log('Initializing Google Admin API...');
    await googleAdminAPI.initialize();
    
    // Check if the API is authenticated
    const isAuthenticated = googleAdminAPI.isAuthenticated();
    console.log('Is authenticated:', isAuthenticated);
    
    if (!isAuthenticated) {
      console.error('API is not authenticated. Cannot proceed with the test.');
      return;
    }
    
    // List users
    console.log('Listing users...');
    const users = await googleAdminAPI.listUsers();
    
    // Check if the response contains users
    if (users && Array.isArray(users)) {
      console.log(`Successfully retrieved ${users.length} users`);
      
      // Log the first user (if available)
      if (users.length > 0) {
        const firstUser = users[0];
        console.log('First user:', {
          id: firstUser.id,
          primaryEmail: firstUser.primaryEmail,
          name: firstUser.name ? firstUser.name.fullName : 'N/A'
        });
      }
    } else {
      console.log('Response is not an array of users:', users);
    }
    
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error testing Google Admin API:');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
  }
}

// Run the test
testGoogleAdminAPI();