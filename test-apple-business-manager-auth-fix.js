// Test script to verify the authentication fix for Apple Business Manager API
const AppleBusinessManagerAPI = require('./server/integrations/appleBusinessManager/appleBusinessManagerAPI');
require('dotenv').config();

// Get configuration from environment variables
const clientId = process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID;
const keyId = process.env.APPLE_BUSINESS_MANAGER_KEY_ID;
const privateKey = process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY;
const tokenExpiry = process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY;

// Determine which private key source to use (content takes precedence over path)
const privateKeySource = privateKey;

// Test the authentication with the updated implementation
async function testAuthentication() {
  console.log('\n=== Testing Apple Business Manager Authentication ===\n');
  
  // Check if required environment variables are set
  if (!clientId || !keyId || !privateKeySource) {
    console.error('Error: Required environment variables are not set.');
    console.log('Required variables: APPLE_BUSINESS_MANAGER_CLIENT_ID, APPLE_BUSINESS_MANAGER_ORGANIZATION_ID, APPLE_BUSINESS_MANAGER_KEY_ID, APPLE_BUSINESS_MANAGER_PRIVATE_KEY or APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH, APPLE_BUSINESS_MANAGER_ISSUER_ID');
    return;
  }
  
  try {
    // Create API instance
    const api = new AppleBusinessManagerAPI(
      clientId,
      keyId,
      privateKeySource,
      tokenExpiry
    );
    
    console.log('API instance created successfully.');
    
    // Test authentication
    console.log('Testing authentication...');
    const accessToken = await api.authenticate();
    console.log('Authentication successful! Access token received.');
    
    // Test a simple API call
    console.log('Testing API call...');
    const devices = await api.getDevices({ limit: 1 });
    console.log('API call successful!');
    console.log(`Retrieved ${devices.length} devices.`);
    
    console.log('\n=== Authentication Test Completed Successfully ===\n');
  } catch (error) {
    console.error('Error during authentication test:', error.message);
    
    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    }
    
    console.log('\n=== Authentication Test Failed ===\n');
  }
}

// Run the test
testAuthentication().catch(error => {
  console.error('Test failed with error:', error);
});