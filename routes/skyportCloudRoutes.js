const express = require('express');
const router = express.Router();
const skyportCloudController = require('../server/controllers/skyportCloudController');
const { isAuthenticated } = require('../middleware/auth');

// Configuration status route - only for checking if environment variables are set
router.get('/config', isAuthenticated, skyportCloudController.getConfig);

// User routes
router.get('/user', isAuthenticated, skyportCloudController.getUserInfo);

// Device routes
router.get('/devices', isAuthenticated, skyportCloudController.getDevices);
router.get('/devices/:deviceId', isAuthenticated, skyportCloudController.getDeviceInfo);
router.get('/devices/:deviceId/status', isAuthenticated, skyportCloudController.getDeviceStatus);

// Temperature control
router.post('/devices/:deviceId/temperature', isAuthenticated, skyportCloudController.setTemperature);

// Mode control
router.post('/devices/:deviceId/mode', isAuthenticated, skyportCloudController.setMode);

// Fan control
router.post('/devices/:deviceId/fan', isAuthenticated, skyportCloudController.setFanMode);

// Schedule management
router.get('/devices/:deviceId/schedule', isAuthenticated, skyportCloudController.getSchedule);
router.post('/devices/:deviceId/schedule', isAuthenticated, skyportCloudController.setSchedule);

// Energy usage
router.get('/devices/:deviceId/energy', isAuthenticated, skyportCloudController.getEnergyUsage);

// Hold and away mode
router.post('/devices/:deviceId/hold', isAuthenticated, skyportCloudController.setHold);
router.post('/devices/:deviceId/away', isAuthenticated, skyportCloudController.setAwayMode);

// Zone management
router.get('/devices/:deviceId/zones', isAuthenticated, skyportCloudController.getZones);
router.post('/devices/:deviceId/zones/:zoneId', isAuthenticated, skyportCloudController.setZoneSettings);

module.exports = router;