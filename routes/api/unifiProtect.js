const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const unifiProtectController = require('../../server/controllers/unifiProtectController');

// @route   GET api/unifi-protect/cameras
// @desc    Get all UniFi Protect cameras
// @access  Private
router.get('/cameras', isAuthenticated, unifiProtectController.getCameras);

// @route   GET api/unifi-protect/cameras/:id
// @desc    Get UniFi Protect camera details
// @access  Private
router.get('/cameras/:id', isAuthenticated, unifiProtectController.getCameraDetails);

// @route   GET api/unifi-protect/cameras/:id/snapshot
// @desc    Get UniFi Protect camera snapshot
// @access  Private
router.get('/cameras/:id/snapshot', isAuthenticated, unifiProtectController.getCameraSnapshot);

// @route   GET api/unifi-protect/events
// @desc    Get UniFi Protect events
// @access  Private
router.get('/events', isAuthenticated, unifiProtectController.getEvents);

// @route   GET api/unifi-protect/system
// @desc    Get UniFi Protect system status
// @access  Private
router.get('/system', isAuthenticated, unifiProtectController.getSystemStatus);

// @route   GET api/unifi-protect/config
// @desc    Get UniFi Protect configuration status
// @access  Private
router.get('/config', isAuthenticated, unifiProtectController.getConfig);

// @route   POST api/unifi-protect/cameras/:id/ptz
// @desc    Control PTZ camera
// @access  Private
router.post('/cameras/:id/ptz', isAuthenticated, unifiProtectController.controlPTZ);

// @route   GET api/unifi-protect/viewers
// @desc    Get all UniFi Protect viewers
// @access  Private
router.get('/viewers', isAuthenticated, unifiProtectController.getViewers);

// @route   PATCH api/unifi-protect/viewers/:id
// @desc    Update UniFi Protect viewer settings
// @access  Private
router.patch('/viewers/:id', isAuthenticated, unifiProtectController.updateViewer);

module.exports = router;