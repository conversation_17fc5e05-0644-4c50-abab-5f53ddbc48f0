const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const rainBirdController = require('../../server/controllers/rainBirdController');

// @route   GET api/rain-bird/zones
// @desc    Get all Rain Bird zones
// @access  Private
router.get('/zones', isAuthenticated, rainBirdController.getZones);

// @route   GET api/rain-bird/zones/:id
// @desc    Get Rain Bird zone details
// @access  Private
router.get('/zones/:id', isAuthenticated, rainBirdController.getZoneDetails);

// @route   POST api/rain-bird/zones/:id/start
// @desc    Start watering a Rain Bird zone
// @access  Private
router.post('/zones/:id/start', isAuthenticated, rainBirdController.startZone);

// @route   POST api/rain-bird/zones/:id/stop
// @desc    Stop watering a Rain Bird zone
// @access  Private
router.post('/zones/:id/stop', isAuthenticated, rainBirdController.stopZone);

// @route   GET api/rain-bird/programs
// @desc    Get all Rain Bird programs (schedules)
// @access  Private
router.get('/programs', isAuthenticated, rainBirdController.getPrograms);

// @route   POST api/rain-bird/programs/:id/start
// @desc    Start a Rain Bird program
// @access  Private
router.post('/programs/:id/start', isAuthenticated, rainBirdController.startProgram);

// @route   POST api/rain-bird/programs/:id/stop
// @desc    Stop a Rain Bird program
// @access  Private
router.post('/programs/:id/stop', isAuthenticated, rainBirdController.stopProgram);

// @route   GET api/rain-bird/system
// @desc    Get Rain Bird system status
// @access  Private
router.get('/system', isAuthenticated, rainBirdController.getSystemStatus);

// @route   POST api/rain-bird/config
// @desc    Save Rain Bird configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, rainBirdController.saveConfig);

// @route   GET api/rain-bird/config
// @desc    Get Rain Bird configuration status
// @access  Private
router.get('/config', isAuthenticated, rainBirdController.getConfig);

// @route   POST api/rain-bird/one-click-setup
// @desc    Set up Rain Bird with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, rainBirdController.oneClickSetup);

// @route   POST api/rain-bird/zones/stop-all
// @desc    Stop all Rain Bird zones
// @access  Private
router.post('/zones/stop-all', isAuthenticated, rainBirdController.stopAllZones);

// @route   GET api/rain-bird/rain-delay
// @desc    Get Rain Bird rain delay
// @access  Private
router.get('/rain-delay', isAuthenticated, rainBirdController.getRainDelay);

// @route   POST api/rain-bird/rain-delay
// @desc    Set Rain Bird rain delay
// @access  Private
router.post('/rain-delay', isAuthenticated, rainBirdController.setRainDelay);

// @route   GET api/rain-bird/rain-sensor
// @desc    Get Rain Bird rain sensor state
// @access  Private
router.get('/rain-sensor', isAuthenticated, rainBirdController.getRainSensorState);

// @route   GET api/rain-bird/irrigation-state
// @desc    Get Rain Bird irrigation state
// @access  Private
router.get('/irrigation-state', isAuthenticated, rainBirdController.getIrrigationState);

// @route   GET api/rain-bird/controller-state
// @desc    Get Rain Bird controller state
// @access  Private
router.get('/controller-state', isAuthenticated, rainBirdController.getControllerState);

module.exports = router;