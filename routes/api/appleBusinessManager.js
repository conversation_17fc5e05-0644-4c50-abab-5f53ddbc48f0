const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const appleBusinessManagerController = require('../../server/controllers/appleBusinessManagerController');

// @desc    Get Apple Business Manager configuration
// @route   GET /api/apple-business-manager/config
// @access  Private
router.get('/config', isAuthenticated, appleBusinessManagerController.getConfig);

// @desc    Save Apple Business Manager configuration
// @route   POST /api/apple-business-manager/config
// @access  Private/Admin
router.post('/config', isAuthenticated, isAdmin, appleBusinessManagerController.saveConfig);

// @desc    Get Apple Business Manager organization details
// @route   GET /api/apple-business-manager/organization
// @access  Private/Admin
router.get('/organization', isAuthenticated, isAdmin, appleBusinessManagerController.getOrganizationDetails);

// @desc    Get all MDM servers
// @route   GET /api/apple-business-manager/mdm-servers
// @access  Private
router.get('/mdm-servers', isAuthenticated, appleBusinessManagerController.getMdmServers);

// @desc    Get all Apple Business Manager devices
// @route   GET /api/apple-business-manager/devices
// @access  Private
router.get('/devices', isAuthenticated, appleBusinessManagerController.getDevices);

// @desc    Get Apple Business Manager device details
// @route   GET /api/apple-business-manager/devices/:id
// @access  Private
router.get('/devices/:id', isAuthenticated, appleBusinessManagerController.getDeviceDetails);

// @desc    Get device MDM assignment information
// @route   GET /api/apple-business-manager/devices/:id/mdm
// @access  Private
router.get('/devices/:id/mdm', isAuthenticated, appleBusinessManagerController.getDeviceMdmAssignment);

// @desc    Assign device to MDM service
// @route   POST /api/apple-business-manager/devices/:id/mdm
// @access  Private/Admin
router.post('/devices/:id/mdm', isAuthenticated, isAdmin, appleBusinessManagerController.assignDeviceToMdm);

// @desc    Reassign device to a different MDM service
// @route   PUT /api/apple-business-manager/devices/:id/mdm
// @access  Private/Admin
router.put('/devices/:id/mdm', isAuthenticated, isAdmin, appleBusinessManagerController.reassignDeviceToMdm);

// @desc    Unassign device from MDM service
// @route   DELETE /api/apple-business-manager/devices/:id/mdm
// @access  Private/Admin
router.delete('/devices/:id/mdm', isAuthenticated, isAdmin, appleBusinessManagerController.unassignDeviceFromMdm);

// @desc    Get devices associated with a specific MDM server
// @route   GET /api/apple-business-manager/mdm-servers/:id/devices
// @access  Private
router.get('/mdm-servers/:id/devices', isAuthenticated, appleBusinessManagerController.getMdmServerDevices);

// @desc    Create a bulk device activity (assign/unassign devices)
// @route   POST /api/apple-business-manager/device-activities
// @access  Private/Admin
router.post('/device-activities', isAuthenticated, isAdmin, appleBusinessManagerController.createDeviceActivity);

// @desc    Get the status of a device activity
// @route   GET /api/apple-business-manager/device-activities/:id
// @access  Private
router.get('/device-activities/:id', isAuthenticated, appleBusinessManagerController.getDeviceActivityStatus);

// @desc    Bulk assign devices to MDM server
// @route   POST /api/apple-business-manager/devices/bulk-assign
// @access  Private/Admin
router.post('/devices/bulk-assign', isAuthenticated, isAdmin, appleBusinessManagerController.bulkAssignDevicesToMdm);

// @desc    Bulk unassign devices from MDM server
// @route   POST /api/apple-business-manager/devices/bulk-unassign
// @access  Private/Admin
router.post('/devices/bulk-unassign', isAuthenticated, isAdmin, appleBusinessManagerController.bulkUnassignDevicesFromMdm);

module.exports = router;