const express = require('express');
const router = express.Router();
const googleAdminController = require('../../server/controllers/googleAdminController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');

/**
 * @route   POST /api/google-admin/config
 * @desc    Save Google Admin configuration
 */
router.post('/config', isAuthenticated, hasR<PERSON>s(['admin']), googleAdminController.saveConfig);

/**
 * @route   GET /api/google-admin/config
 * @desc    Get Google Admin configuration
 */
router.get('/config', isAuthenticated, hasRoles(['admin']), googleAdminController.getConfig);

/**
 * @route   GET /api/google-admin/auth-url
 * @desc    Get Google Admin authentication URL
 */
router.get('/auth-url', isAuthenticated, hasRoles(['admin']), googleAdminController.getAuthUrl);

/**
 * @route   GET /api/google-admin/callback
 * @desc    Handle Google Admin OAuth2 callback
 */
router.get('/callback', googleAdminController.handleCallback);

/**
 * @route   GET /api/google-admin/users
 * @desc    List users in Google Admin
 */
router.get('/users', isAuthenticated, hasRoles(['admin']), googleAdminController.listUsers);

/**
 * @route   GET /api/google-admin/users/:userKey
 * @desc    Get user information
 */
router.get('/users/:userKey', isAuthenticated, hasRoles(['admin']), googleAdminController.getUser);

/**
 * @route   POST /api/google-admin/users
 * @desc    Create a new user
 */
router.post('/users', isAuthenticated, hasRoles(['admin']), googleAdminController.createUser);

/**
 * @route   PUT /api/google-admin/users/:userKey
 * @desc    Update user information
 */
router.put('/users/:userKey', isAuthenticated, hasRoles(['admin']), googleAdminController.updateUser);

/**
 * @route   DELETE /api/google-admin/users/:userKey
 * @desc    Delete a user
 */
router.delete('/users/:userKey', isAuthenticated, hasRoles(['admin']), googleAdminController.deleteUser);

/**
 * @route   GET /api/google-admin/groups
 * @desc    List groups in Google Admin
 */
router.get('/groups', isAuthenticated, hasRoles(['admin']), googleAdminController.listGroups);

/**
 * @route   POST /api/google-admin/groups/:groupKey/members
 * @desc    Add user to a group
 */
router.post('/groups/:groupKey/members', isAuthenticated, hasRoles(['admin']), googleAdminController.addUserToGroup);

/**
 * @route   DELETE /api/google-admin/groups/:groupKey/members/:memberKey
 * @desc    Remove user from a group
 */
router.delete('/groups/:groupKey/members/:memberKey', isAuthenticated, hasRoles(['admin']), googleAdminController.removeUserFromGroup);

/**
 * @route   POST /api/google-admin/sync-users
 * @desc    Sync all users from Google Workspace to the system
 */
router.post('/sync-users', isAuthenticated, hasRoles(['admin']), googleAdminController.syncAllUsers);

/**
 * @route   POST /api/google-admin/sync-all-users
 * @desc    Alias for sync-users endpoint (backward compatibility)
 */
router.post('/sync-all-users', isAuthenticated, hasRoles(['admin']), googleAdminController.syncAllUsers);

module.exports = router;
