const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const User = require('../../models/User');
const Team = require('../../models/Team');
const Group = require('../../models/Group');
const Tag = require('../../models/Tag');
const { check, validationResult } = require('express-validator');
const { google } = require('googleapis');
const { getAuthenticatedClient } = require('../../server/utils/googleServiceAuth');

/**
 * Helper function to create Google API client with service account authentication
 * @param {string} userId - User ID to get email for impersonation
 * @param {string} serviceName - Name of the Google service (e.g., 'Drive', 'Admin')
 * @param {string[]} scopes - OAuth scopes required for the API
 * @returns {Promise<Object>} - Authenticated Google API client
 */
const createGoogleClient = async (userId, serviceName, scopes) => {
  try {
    // Get user email for impersonation
    const user = await User.findById(userId);

    if (!user || !user.email) {
      throw new Error('User not found or missing email');
    }

    // Use service account authentication
    const auth = await getAuthenticatedClient(serviceName, scopes, user.email);
    return auth;
  } catch (err) {
    console.error(`Error creating Google ${serviceName} client:`, err);
    throw err;
  }
};

/**
 * Execute a Google API request
 * @param {Function} apiCall - Function that makes the API call
 * @returns {Promise<any>} API response
 */
const executeGoogleApiCall = async (apiCall) => {
  try {
    // Execute the API call
    return await apiCall();
  } catch (error) {
    console.error('Error executing Google API call:', error);
    throw error;
  }
};

/**
 * @route   GET /api/staff-directory/users
 * @desc    Get users with profile information (paginated, with optional filtering)
 */
router.get('/users', isAuthenticated, async (req, res) => {
  try {
    // Parse pagination parameters
    let page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    
    // Ensure page is at least 1
    if (page < 1) {
      page = 1;
    }
    
    const skip = (page - 1) * limit;
    
    // Parse filter parameters
    const searchTerm = req.query.search || '';
    const department = req.query.department || '';
    const type = req.query.type || '';
    
    // Build filter query
    let filterQuery = { isActive: true };
    
    // Add department filter if provided
    if (department) {
      filterQuery.department = department;
    }
    
    // Add type filter if provided
    if (type) {
      filterQuery.type = type;
    }
    
    // Add search filter if provided
    if (searchTerm) {
      // Search in multiple fields
      filterQuery.$or = [
        { name: { $regex: searchTerm, $options: 'i' } },
        { email: { $regex: searchTerm, $options: 'i' } },
        { jobTitle: { $regex: searchTerm, $options: 'i' } },
        { department: { $regex: searchTerm, $options: 'i' } }
      ];
      
      // Also search in arrays (skills and tags)
      // This is a simplified approach - for production, consider using text indexes
      const usersWithMatchingSkills = await User.find({
        skills: { $elemMatch: { $regex: searchTerm, $options: 'i' } }
      }).select('_id');
      
      const usersWithMatchingTags = await User.find({
        tags: { $elemMatch: { $regex: searchTerm, $options: 'i' } }
      }).select('_id');
      
      // Add users with matching skills or tags to the $or query
      if (usersWithMatchingSkills.length > 0 || usersWithMatchingTags.length > 0) {
        const skillsAndTagsIds = [
          ...usersWithMatchingSkills.map(u => u._id),
          ...usersWithMatchingTags.map(u => u._id)
        ];
        
        filterQuery.$or.push({ _id: { $in: skillsAndTagsIds } });
      }
    }

    // Get total count for pagination with filters applied
    const totalUsers = await User.countDocuments(filterQuery);
    
    // Get paginated users with filters
    const users = await User.find(filterQuery)
      .select('-googleAccessToken -googleRefreshToken')
      .populate('teams', 'name description')
      .populate('groups', 'name description')
      .skip(skip)
      .limit(limit);
    
    // Return paginated results with metadata
    res.json({
      users,
      pagination: {
        total: totalUsers,
        page,
        limit,
        pages: Math.ceil(totalUsers / limit)
      }
    });
  } catch (err) {
    console.error('Error fetching users:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/users/:id
 * @desc    Get user profile by ID
 */
router.get('/users/:id', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-googleAccessToken -googleRefreshToken')
      .populate('teams', 'name description leader members')
      .populate('groups', 'name description owner members');
    
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    res.json(user);
  } catch (err) {
    console.error('Error fetching user:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   PUT /api/staff-directory/users/:id/profile
 * @desc    Update user profile
 */
router.put('/users/:id/profile', [
  isAuthenticated,
  check('jobTitle', 'Job title is required').optional().notEmpty(),
  check('type', 'Type must be one of: student, staff, contractor, resident, intern, other')
    .optional()
    .isIn(['student', 'staff', 'contractor', 'resident', 'intern', 'other']),
  check('department', 'Department is required').optional().notEmpty(),
  check('phoneNumber', 'Phone number is required').optional().notEmpty(),
  check('location', 'Location is required').optional().notEmpty(),
  check('bio', 'Bio is required').optional().notEmpty(),
  check('skills', 'Skills must be an array').optional().isArray(),
  check('tags', 'Tags must be an array').optional().isArray()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  // Only allow users to update their own profile unless they're an admin
  if (req.params.id !== req.user.id && !req.user.roles.includes('admin')) {
    return res.status(403).json({ msg: 'Not authorized to update this profile' });
  }
  
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    // Update fields if they are provided in the request
    if (req.body.jobTitle !== undefined) user.jobTitle = req.body.jobTitle;
    if (req.body.type !== undefined) user.type = req.body.type;
    if (req.body.department !== undefined) user.department = req.body.department;
    if (req.body.phoneNumber !== undefined) user.phoneNumber = req.body.phoneNumber;
    if (req.body.location !== undefined) user.location = req.body.location;
    if (req.body.bio !== undefined) user.bio = req.body.bio;
    if (req.body.skills !== undefined) user.skills = req.body.skills;
    if (req.body.tags !== undefined) user.tags = req.body.tags;
    
    await user.save();
    
    // Return the updated user without sensitive information
    const updatedUser = await User.findById(req.params.id)
      .select('-googleAccessToken -googleRefreshToken')
      .populate('teams', 'name description')
      .populate('groups', 'name description');
    
    res.json(updatedUser);
  } catch (err) {
    console.error('Error updating user profile:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/teams
 * @desc    Get all teams (paginated)
 */
router.get('/teams', isAuthenticated, async (req, res) => {
  try {
    // Parse pagination parameters
    let page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    
    // Ensure page is at least 1
    if (page < 1) {
      page = 1;
    }
    
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalTeams = await Team.countDocuments();
    
    // Get paginated teams
    const teams = await Team.find()
      .populate('leader', 'name email avatar')
      .populate('members', 'name email avatar')
      .skip(skip)
      .limit(limit);
    
    // Return paginated results with metadata
    res.json({
      teams,
      pagination: {
        total: totalTeams,
        page,
        limit,
        pages: Math.ceil(totalTeams / limit)
      }
    });
  } catch (err) {
    console.error('Error fetching teams:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/teams/:id
 * @desc    Get team by ID
 */
router.get('/teams/:id', isAuthenticated, async (req, res) => {
  try {
    const team = await Team.findById(req.params.id)
      .populate('leader', 'name email avatar jobTitle department')
      .populate('members', 'name email avatar jobTitle department');
    
    if (!team) {
      return res.status(404).json({ msg: 'Team not found' });
    }
    
    res.json(team);
  } catch (err) {
    console.error('Error fetching team:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Team not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   POST /api/staff-directory/teams
 * @desc    Create a new team
 */
router.post('/teams', [
  isAuthenticated,
  hasRoles(['admin']),
  check('name', 'Name is required').notEmpty(),
  check('description', 'Description is required').optional(),
  check('leader', 'Leader ID is required').optional().isMongoId(),
  check('members', 'Members must be an array of user IDs').optional().isArray(),
  check('tags', 'Tags must be an array').optional().isArray()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    // Check if team with the same name already exists
    const existingTeam = await Team.findOne({ name: req.body.name });
    if (existingTeam) {
      return res.status(400).json({ msg: 'Team with this name already exists' });
    }
    
    const newTeam = new Team({
      name: req.body.name,
      description: req.body.description,
      leader: req.body.leader,
      members: req.body.members || [],
      tags: req.body.tags || []
    });
    
    const team = await newTeam.save();
    
    // Add team to each member's teams array
    if (team.members.length > 0) {
      await User.updateMany(
        { _id: { $in: team.members } },
        { $addToSet: { teams: team._id } }
      );
    }
    
    // Add team to leader's teams array if leader is set
    if (team.leader) {
      await User.findByIdAndUpdate(
        team.leader,
        { $addToSet: { teams: team._id } }
      );
    }
    
    res.json(team);
  } catch (err) {
    console.error('Error creating team:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   PUT /api/staff-directory/teams/:id
 * @desc    Update a team
 */
router.put('/teams/:id', [
  isAuthenticated,
  hasRoles(['admin']),
  check('name', 'Name is required').optional().notEmpty(),
  check('description', 'Description is required').optional(),
  check('leader', 'Leader ID is required').optional().isMongoId(),
  check('members', 'Members must be an array of user IDs').optional().isArray(),
  check('tags', 'Tags must be an array').optional().isArray()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    const team = await Team.findById(req.params.id);
    
    if (!team) {
      return res.status(404).json({ msg: 'Team not found' });
    }
    
    // If name is being changed, check if the new name is already taken
    if (req.body.name && req.body.name !== team.name) {
      const existingTeam = await Team.findOne({ name: req.body.name });
      if (existingTeam) {
        return res.status(400).json({ msg: 'Team with this name already exists' });
      }
      team.name = req.body.name;
    }
    
    // Update fields if they are provided in the request
    if (req.body.description !== undefined) team.description = req.body.description;
    if (req.body.tags !== undefined) team.tags = req.body.tags;
    
    // Handle leader change
    if (req.body.leader !== undefined && req.body.leader !== team.leader) {
      // Remove team from old leader's teams array if there was a leader
      if (team.leader) {
        await User.findByIdAndUpdate(
          team.leader,
          { $pull: { teams: team._id } }
        );
      }
      
      // Add team to new leader's teams array
      if (req.body.leader) {
        await User.findByIdAndUpdate(
          req.body.leader,
          { $addToSet: { teams: team._id } }
        );
      }
      
      team.leader = req.body.leader;
    }
    
    // Handle members change
    if (req.body.members !== undefined) {
      // Get current members
      const currentMembers = team.members.map(id => id.toString());
      const newMembers = req.body.members.map(id => id.toString());
      
      // Find members to remove (in current but not in new)
      const membersToRemove = currentMembers.filter(id => !newMembers.includes(id));
      
      // Find members to add (in new but not in current)
      const membersToAdd = newMembers.filter(id => !currentMembers.includes(id));
      
      // Remove team from removed members' teams array
      if (membersToRemove.length > 0) {
        await User.updateMany(
          { _id: { $in: membersToRemove } },
          { $pull: { teams: team._id } }
        );
      }
      
      // Add team to added members' teams array
      if (membersToAdd.length > 0) {
        await User.updateMany(
          { _id: { $in: membersToAdd } },
          { $addToSet: { teams: team._id } }
        );
      }
      
      team.members = req.body.members;
    }
    
    await team.save();
    
    // Return the updated team with populated fields
    const updatedTeam = await Team.findById(req.params.id)
      .populate('leader', 'name email avatar jobTitle department')
      .populate('members', 'name email avatar jobTitle department');
    
    res.json(updatedTeam);
  } catch (err) {
    console.error('Error updating team:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Team not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   DELETE /api/staff-directory/teams/:id
 * @desc    Delete a team
 */
router.delete('/teams/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const team = await Team.findById(req.params.id);
    
    if (!team) {
      return res.status(404).json({ msg: 'Team not found' });
    }
    
    // Remove team from all members' teams array
    await User.updateMany(
      { teams: team._id },
      { $pull: { teams: team._id } }
    );
    
    await team.remove();
    
    res.json({ msg: 'Team removed' });
  } catch (err) {
    console.error('Error deleting team:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Team not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/groups
 * @desc    Get all groups (paginated)
 */
router.get('/groups', isAuthenticated, async (req, res) => {
  try {
    // Parse pagination parameters
    let page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    
    // Ensure page is at least 1
    if (page < 1) {
      page = 1;
    }
    
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalGroups = await Group.countDocuments();
    
    // Get paginated groups
    const groups = await Group.find()
      .populate('owner', 'name email avatar')
      .populate({
        path: 'members',
        match: { isActive: true }, // Only populate active members
        select: 'name email avatar jobTitle department'
      })
      .skip(skip)
      .limit(limit);
    
    // Return paginated results with metadata
    res.json({
      groups,
      pagination: {
        total: totalGroups,
        page,
        limit,
        pages: Math.ceil(totalGroups / limit)
      }
    });
  } catch (err) {
    console.error('Error fetching groups:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/groups/:id
 * @desc    Get group by ID
 */
router.get('/groups/:id', isAuthenticated, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id)
      .populate('owner', 'name email avatar jobTitle department')
      .populate({
        path: 'members',
        match: { isActive: true }, // Only populate active members
        select: 'name email avatar jobTitle department'
      });
    
    if (!group) {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    res.json(group);
  } catch (err) {
    console.error('Error fetching group:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   POST /api/staff-directory/groups
 * @desc    Create a new group
 */
router.post('/groups', [
  isAuthenticated,
  check('name', 'Name is required').notEmpty(),
  check('description', 'Description is required').optional(),
  check('isPublic', 'isPublic must be a boolean').optional().isBoolean(),
  check('tags', 'Tags must be an array').optional().isArray()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    // Check if group with the same name already exists
    const existingGroup = await Group.findOne({ name: req.body.name });
    if (existingGroup) {
      return res.status(400).json({ msg: 'Group with this name already exists' });
    }
    
    const newGroup = new Group({
      name: req.body.name,
      description: req.body.description,
      owner: req.user.id,
      members: [req.user.id], // Add creator as the first member
      tags: req.body.tags || [],
      isPublic: req.body.isPublic !== undefined ? req.body.isPublic : true
    });
    
    const group = await newGroup.save();
    
    // Add group to creator's groups array
    await User.findByIdAndUpdate(
      req.user.id,
      { $addToSet: { groups: group._id } }
    );
    
    res.json(group);
  } catch (err) {
    console.error('Error creating group:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   PUT /api/staff-directory/groups/:id
 * @desc    Update a group
 */
router.put('/groups/:id', [
  isAuthenticated,
  check('name', 'Name is required').optional().notEmpty(),
  check('description', 'Description is required').optional(),
  check('isPublic', 'isPublic must be a boolean').optional().isBoolean(),
  check('tags', 'Tags must be an array').optional().isArray()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    // Check if user is authorized to update this group
    if (group.owner.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ msg: 'Not authorized to update this group' });
    }
    
    // If name is being changed, check if the new name is already taken
    if (req.body.name && req.body.name !== group.name) {
      const existingGroup = await Group.findOne({ name: req.body.name });
      if (existingGroup) {
        return res.status(400).json({ msg: 'Group with this name already exists' });
      }
      group.name = req.body.name;
    }
    
    // Update fields if they are provided in the request
    if (req.body.description !== undefined) group.description = req.body.description;
    if (req.body.isPublic !== undefined) group.isPublic = req.body.isPublic;
    if (req.body.tags !== undefined) group.tags = req.body.tags;
    
    await group.save();
    
    // Return the updated group with populated fields
    const updatedGroup = await Group.findById(req.params.id)
      .populate('owner', 'name email avatar jobTitle department')
      .populate('members', 'name email avatar jobTitle department');
    
    res.json(updatedGroup);
  } catch (err) {
    console.error('Error updating group:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   POST /api/staff-directory/groups/:id/members
 * @desc    Add a member to a group
 */
router.post('/groups/:id/members', [
  isAuthenticated,
  check('userId', 'User ID is required').isMongoId()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    // Check if user is already a member
    if (group.members.includes(req.body.userId)) {
      return res.status(400).json({ msg: 'User is already a member of this group' });
    }
    
    // Add user to group members
    group.members.push(req.body.userId);
    await group.save();
    
    // Add group to user's groups array
    await User.findByIdAndUpdate(
      req.body.userId,
      { $addToSet: { groups: group._id } }
    );
    
    // Return the updated group with populated fields
    const updatedGroup = await Group.findById(req.params.id)
      .populate('owner', 'name email avatar jobTitle department')
      .populate({
        path: 'members',
        match: { isActive: true }, // Only populate active members
        select: 'name email avatar jobTitle department'
      });
    
    res.json(updatedGroup);
  } catch (err) {
    console.error('Error adding member to group:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Group or user not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   DELETE /api/staff-directory/groups/:id/members/:userId
 * @desc    Remove a member from a group
 */
router.delete('/groups/:id/members/:userId', isAuthenticated, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    // Check if user is authorized to remove members
    const isOwner = group.owner.toString() === req.user.id;
    const isAdmin = req.user.roles.includes('admin');
    const isSelf = req.params.userId === req.user.id;
    
    if (!isOwner && !isAdmin && !isSelf) {
      return res.status(403).json({ msg: 'Not authorized to remove members from this group' });
    }
    
    // Check if user is a member
    if (!group.members.includes(req.params.userId)) {
      return res.status(400).json({ msg: 'User is not a member of this group' });
    }
    
    // Don't allow removing the owner
    if (group.owner.toString() === req.params.userId && !isAdmin) {
      return res.status(400).json({ msg: 'Cannot remove the owner from the group' });
    }
    
    // Remove user from group members
    group.members = group.members.filter(
      member => member.toString() !== req.params.userId
    );
    await group.save();
    
    // Remove group from user's groups array
    await User.findByIdAndUpdate(
      req.params.userId,
      { $pull: { groups: group._id } }
    );
    
    // Return the updated group with populated fields
    const updatedGroup = await Group.findById(req.params.id)
      .populate('owner', 'name email avatar jobTitle department')
      .populate({
        path: 'members',
        match: { isActive: true }, // Only populate active members
        select: 'name email avatar jobTitle department'
      });
    
    res.json(updatedGroup);
  } catch (err) {
    console.error('Error removing member from group:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Group or user not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   DELETE /api/staff-directory/groups/:id
 * @desc    Delete a group
 */
router.delete('/groups/:id', isAuthenticated, async (req, res) => {
  try {
    const group = await Group.findById(req.params.id);
    
    if (!group) {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    // Check if user is authorized to delete this group
    if (group.owner.toString() !== req.user.id && !req.user.roles.includes('admin')) {
      return res.status(403).json({ msg: 'Not authorized to delete this group' });
    }
    
    // Remove group from all members' groups array
    await User.updateMany(
      { groups: group._id },
      { $pull: { groups: group._id } }
    );
    
    await group.remove();
    
    res.json({ msg: 'Group removed' });
  } catch (err) {
    console.error('Error deleting group:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Group not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/tags
 * @desc    Get all tags
 */
router.get('/tags', isAuthenticated, async (req, res) => {
  try {
    const tags = await Tag.find().populate('createdBy', 'name email');
    res.json(tags);
  } catch (err) {
    console.error('Error fetching tags:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   POST /api/staff-directory/tags
 * @desc    Create a new tag
 */
router.post('/tags', [
  isAuthenticated,
  hasRoles(['admin']),
  check('name', 'Name is required').notEmpty(),
  check('description', 'Description is required').optional(),
  check('color', 'Color must be a valid hex color').optional().isHexColor()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    // Check if tag with the same name already exists
    const existingTag = await Tag.findOne({ name: req.body.name });
    if (existingTag) {
      return res.status(400).json({ msg: 'Tag with this name already exists' });
    }
    
    const newTag = new Tag({
      name: req.body.name,
      description: req.body.description,
      color: req.body.color,
      createdBy: req.user.id
    });
    
    const tag = await newTag.save();
    res.json(tag);
  } catch (err) {
    console.error('Error creating tag:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   PUT /api/staff-directory/tags/:id
 * @desc    Update a tag
 */
router.put('/tags/:id', [
  isAuthenticated,
  hasRoles(['admin']),
  check('name', 'Name is required').optional().notEmpty(),
  check('description', 'Description is required').optional(),
  check('color', 'Color must be a valid hex color').optional().isHexColor()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  
  try {
    const tag = await Tag.findById(req.params.id);
    
    if (!tag) {
      return res.status(404).json({ msg: 'Tag not found' });
    }
    
    // If name is being changed, check if the new name is already taken
    if (req.body.name && req.body.name !== tag.name) {
      const existingTag = await Tag.findOne({ name: req.body.name });
      if (existingTag) {
        return res.status(400).json({ msg: 'Tag with this name already exists' });
      }
      tag.name = req.body.name;
    }
    
    // Update fields if they are provided in the request
    if (req.body.description !== undefined) tag.description = req.body.description;
    if (req.body.color !== undefined) tag.color = req.body.color;
    
    await tag.save();
    res.json(tag);
  } catch (err) {
    console.error('Error updating tag:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Tag not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   DELETE /api/staff-directory/tags/:id
 * @desc    Delete a tag
 */
router.delete('/tags/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const tag = await Tag.findById(req.params.id);
    
    if (!tag) {
      return res.status(404).json({ msg: 'Tag not found' });
    }
    
    await tag.remove();
    
    res.json({ msg: 'Tag removed' });
  } catch (err) {
    console.error('Error deleting tag:', err);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Tag not found' });
    }
    
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/staff-directory/google/profile
 * @desc    Get additional profile information from Google
 */
router.get('/google/profile', isAuthenticated, async (req, res) => {
  try {
    const adminScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.user.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Admin', adminScopes);
    const admin = google.admin({ version: 'directory_v1', auth });
    
    // Get user profile from Google Admin
    const googleProfile = await executeGoogleApiCall(async () => {
      const response = await admin.users.get({
        userKey: req.user.email
      });
      return response.data;
    });
    
    // Extract relevant fields from Google profile
    const profileData = {
      name: googleProfile.name?.fullName,
      email: googleProfile.primaryEmail,
      jobTitle: googleProfile.organizations?.[0]?.title,
      department: googleProfile.organizations?.[0]?.department,
      phoneNumber: googleProfile.phones?.[0]?.value,
      location: googleProfile.locations?.[0]?.buildingId
    };
    
    res.json(profileData);
  } catch (err) {
    console.error('Error fetching Google profile:', err);
    
    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }
    
    res.status(500).json({ msg: 'Error fetching Google profile' });
  }
});

/**
 * @route   POST /api/staff-directory/google/sync-profile
 * @desc    Sync user profile with Google
 */
router.post('/google/sync-profile', isAuthenticated, async (req, res) => {
  try {
    const adminScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.user.readonly',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.group.readonly'
    ];
    
    const auth = await createGoogleClient(req.user.id, 'Admin', adminScopes);
    const admin = google.admin({ version: 'directory_v1', auth });
    
    // Get user profile from Google Admin
    const googleProfile = await executeGoogleApiCall(async () => {
      const response = await admin.users.get({
        userKey: req.user.email
      });
      return response.data;
    });
    
    // Get user's groups from Google Admin
    const googleGroups = await executeGoogleApiCall(async () => {
      const response = await admin.groups.list({
        userKey: req.user.email
      });
      return response.data.groups || [];
    });
    
    // Update user profile with Google data
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    // Update fields from Google profile
    user.name = googleProfile.name?.fullName || user.name;
    user.jobTitle = googleProfile.organizations?.[0]?.title || user.jobTitle;
    user.department = googleProfile.organizations?.[0]?.department || user.department;
    user.phoneNumber = googleProfile.phones?.[0]?.value || user.phoneNumber;
    user.location = googleProfile.locations?.[0]?.buildingId || user.location;
    
    // Process Google groups
    if (googleGroups && googleGroups.length > 0) {
      // Clear existing groups
      user.groups = [];
      
      // Process each Google group
      for (const googleGroup of googleGroups) {
        // Find or create the group in our database
        let group = await Group.findOne({ name: googleGroup.name });
        
        if (!group) {
          // Create a new group if it doesn't exist
          group = new Group({
            name: googleGroup.name,
            description: googleGroup.description || `Google group: ${googleGroup.name}`,
            // No owner is set as this is from Google
          });
          await group.save();
        }
        
        // Add the group to the user's groups
        if (!user.groups.includes(group._id)) {
          user.groups.push(group._id);
        }
        
        // Check if this group should be treated as a team based on naming convention
        // For example, if the group name starts with "Team-"
        if (googleGroup.name.startsWith('Team-')) {
          // Find or create the team
          const teamName = googleGroup.name.substring(5); // Remove "Team-" prefix
          let team = await Team.findOne({ name: teamName });
          
          if (!team) {
            // Create a new team if it doesn't exist
            team = new Team({
              name: teamName,
              description: googleGroup.description || `Team derived from Google group: ${googleGroup.name}`,
              // No leader is set as this is from Google
            });
            await team.save();
          }
          
          // Add the team to the user's teams if not already there
          if (!user.teams.includes(team._id)) {
            user.teams.push(team._id);
          }
        }
      }
    }
    
    await user.save();
    
    // Return the updated user without sensitive information
    const updatedUser = await User.findById(req.user.id)
      .select('-googleAccessToken -googleRefreshToken')
      .populate('teams', 'name description')
      .populate('groups', 'name description');
    
    res.json(updatedUser);
  } catch (err) {
    console.error('Error syncing with Google profile:', err);
    
    if (err.message === 'User not found or missing email') {
      return res.status(401).json({ msg: 'Authentication required for Google Admin' });
    }
    
    res.status(500).json({ msg: 'Error syncing with Google profile' });
  }
});

module.exports = router;