const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const integrationStatusController = require('../../server/controllers/integrationStatusController');

// @route   GET api/integration-status
// @desc    Get all integration statuses
// @access  Private
router.get('/', isAuthenticated, integrationStatusController.getAllIntegrationStatuses);

// @route   GET api/integration-status/:integration
// @desc    Get status of a specific integration
// @access  Private
router.get('/:integration', isAuthenticated, integrationStatusController.getIntegrationStatus);

// @route   POST api/integration-status/refresh
// @desc    Force refresh of the integration status file
// @access  Admin
router.post('/refresh', isAuthenticated, hasRoles(['admin']), integrationStatusController.refreshStatusFile);

// @route   GET api/integration-status/markdown/raw
// @desc    Get the raw markdown content of the integration status file
// @access  Private
router.get('/markdown/raw', isAuthenticated, integrationStatusController.getStatusMarkdown);

module.exports = router;
