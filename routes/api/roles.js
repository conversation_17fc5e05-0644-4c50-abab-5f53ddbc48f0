const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const Role = require('../../models/Role');
const RoleSettings = require('../../models/RoleSettings');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/roles
 * @desc    Get all roles
 * @access  private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const roles = await Role.find();
    res.json(roles);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/roles/details
 * @desc    Get all roles with their permissions
 * @access  private
 */
router.get('/details', isAuthenticated, async (req, res) => {
  try {
    const roles = await Role.find();
    res.json(roles);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/roles/:id
 * @desc    Get role by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const role = await Role.findById(req.params.id);
    
    if (!role) {
      return res.status(404).json({ msg: 'Role not found' });
    }
    
    res.json(role);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Role not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/roles
 * @desc    Create a role
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
    check('permissions', 'Permissions must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, description, permissions } = req.body;

    try {
      // Check if role already exists
      let role = await Role.findOne({ name });
      
      if (role) {
        return res.status(400).json({ msg: 'Role already exists' });
      }
      
      // Create new role
      role = new Role({
        name,
        description,
        permissions,
        createdBy: req.user.id
      });
      
      await role.save();
      
      res.json(role);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/roles/:id
 * @desc    Update a role
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
    check('permissions', 'Permissions must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const role = await Role.findById(req.params.id);
      
      if (!role) {
        return res.status(404).json({ msg: 'Role not found' });
      }
      
      // Don't allow modification of default roles
      if (role.isDefault) {
        return res.status(403).json({ msg: 'Default roles cannot be modified' });
      }
      
      // Update role fields
      role.name = req.body.name;
      role.description = req.body.description;
      role.permissions = req.body.permissions;
      
      await role.save();
      
      res.json(role);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Role not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/roles/:id
 * @desc    Delete a role
 * @access  private
 */
router.delete('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const role = await Role.findById(req.params.id);
    
    if (!role) {
      return res.status(404).json({ msg: 'Role not found' });
    }
    
    // Don't allow deletion of default roles
    if (role.isDefault) {
      return res.status(403).json({ msg: 'Default roles cannot be deleted' });
    }
    
    await role.remove();
    
    res.json({ msg: 'Role removed' });
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Role not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/roles/init
 * @desc    Initialize default roles
 * @access  private
 */
router.post('/init', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    await Role.createDefaultRoles();
    const roles = await Role.find();
    res.json(roles);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/roles/settings
 * @desc    Get default role settings
 * @access  Private
 */
router.get('/settings', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const settings = await RoleSettings.getCurrentSettings();
    res.json(settings);
  } catch (err) {
    console.error('Error fetching role settings:', err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/roles/settings
 * @desc    Update default role settings
 * @access  Private
 */
router.put(
  '/settings',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('defaultRoleLocalUsers', 'Default role for local users is required').not().isEmpty(),
    check('defaultRoleGoogleUsers', 'Default role for Google users is required').not().isEmpty(),
    check('googleGroupsRoleMapping', 'Google Groups role mapping must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { defaultRoleLocalUsers, defaultRoleGoogleUsers, googleGroupsRoleMapping } = req.body;
      
      // Validate that the specified roles exist
      const localRole = await Role.findOne({ name: defaultRoleLocalUsers });
      if (!localRole) {
        return res.status(400).json({ msg: `Role '${defaultRoleLocalUsers}' does not exist` });
      }
      
      const googleRole = await Role.findOne({ name: defaultRoleGoogleUsers });
      if (!googleRole) {
        return res.status(400).json({ msg: `Role '${defaultRoleGoogleUsers}' does not exist` });
      }
      
      // Validate that all roles in the Google Groups mapping exist
      for (const mapping of googleGroupsRoleMapping) {
        const role = await Role.findOne({ name: mapping.roleName });
        if (!role) {
          return res.status(400).json({ msg: `Role '${mapping.roleName}' does not exist` });
        }
      }
      
      // Update settings
      const settings = await RoleSettings.updateSettings({
        defaultRoleLocalUsers,
        defaultRoleGoogleUsers,
        googleGroupsRoleMapping
      }, req.user.id);
      
      res.json(settings);
    } catch (err) {
      console.error('Error updating role settings:', err.message);
      res.status(500).send('Server Error');
    }
  }
);

module.exports = router;