const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const { isAuthenticated } = require('../../middleware/auth');
const planningCenterController = require('../../server/controllers/planningCenterController');

// @route   GET api/planning-center/applications
// @desc    Get all Planning Center applications
// @access  Private
router.get('/applications', isAuthenticated, planningCenterController.getApplications);

// @route   GET api/planning-center/events
// @desc    Get Planning Center events
// @access  Private
router.get('/events', isAuthenticated, planningCenterController.getEvents);

// @route   GET api/planning-center/people
// @desc    Get Planning Center people
// @access  Private
router.get('/people', isAuthenticated, planningCenterController.getPeople);

// @route   GET api/planning-center/people/:id
// @desc    Get a single Planning Center person by ID
// @access  Private
router.get('/people/:id', isAuthenticated, planningCenterController.getPersonById);

// @route   GET api/planning-center/resources
// @desc    Get Planning Center resources
// @access  Private
router.get('/resources', isAuthenticated, planningCenterController.getResources);

// @route   GET api/planning-center/resources/:id
// @desc    Get a single Planning Center resource by ID
// @access  Private
router.get('/resources/:id', isAuthenticated, planningCenterController.getResourceById);

// @route   POST api/planning-center/config
// @desc    Save Planning Center configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, planningCenterController.saveConfig);

// @route   GET api/planning-center/config
// @desc    Get Planning Center configuration status
// @access  Private
router.get('/config', isAuthenticated, planningCenterController.getConfig);

// @route   GET api/planning-center/people-directory
// @desc    Get Planning Center people formatted for the People Directory
// @access  Private
router.get('/people-directory', isAuthenticated, planningCenterController.getPeopleForDirectory);

// @route   GET api/planning-center/verify-token
// @desc    Verify Personal Access Token with Planning Center
// @access  Private
router.get('/verify-token', isAuthenticated, planningCenterController.verifyToken);

// @route   POST api/planning-center/one-click-setup
// @desc    Set up Planning Center with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, planningCenterController.oneClickSetup);

module.exports = router;
