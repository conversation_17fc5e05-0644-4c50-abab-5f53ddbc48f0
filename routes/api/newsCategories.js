const express = require('express');
const router = express.Router();
const { isAuthenticated, hasPermission } = require('../../middleware/auth');
const newsCategoryController = require('../../server/controllers/newsCategoryController');
const { check } = require('express-validator');

// GET /api/news-categories
// Get all news categories
// Access: private (admin)
router.get('/', isAuthenticated, hasPermission('news:admin'), newsCategoryController.getAllCategories);

// GET /api/news-categories/accessible
// Get news categories accessible by the current user
// Access: private
router.get('/accessible', isAuthenticated, newsCategoryController.getAccessibleCategories);

// GET /api/news-categories/:id
// Get category by ID
// Access: private
router.get('/:id', isAuthenticated, newsCategoryController.getCategoryById);

// POST /api/news-categories
// Create a new news category
// Access: private (admin)
router.post(
  '/',
  [
    isAuthenticated,
    hasPermission('news:admin'),
    check('name', 'Name is required').not().isEmpty()
  ],
  newsCategoryController.createCategory
);

// PUT /api/news-categories/:id
// Update a news category
// Access: private (admin)
router.put(
  '/:id',
  [
    isAuthenticated,
    hasPermission('news:admin'),
    check('name', 'Name is required').not().isEmpty()
  ],
  newsCategoryController.updateCategory
);

// DELETE /api/news-categories/:id
// Delete a news category
// Access: private (admin)
router.delete('/:id', isAuthenticated, hasPermission('news:admin'), newsCategoryController.deleteCategory);

module.exports = router;