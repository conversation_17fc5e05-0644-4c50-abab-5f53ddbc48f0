const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const MenuCategory = require('../../models/MenuCategory');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/menu-categories
 * @desc    Get all menu categories
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    const categories = await MenuCategory.find().sort({ order: 1, name: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/menu-categories/:id
 * @desc    Get menu category by ID
 * @access  Public
 */
router.get('/:id', async (req, res) => {
  try {
    const category = await MenuCategory.findById(req.params.id);

    if (!category) {
      return res.status(404).json({ msg: 'Category not found' });
    }

    res.json(category);
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Category not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/menu-categories
 * @desc    Create a menu category
 * @access  Private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, description, icon, color, order } = req.body;

    try {
      // Check if category with this name already exists
      let category = await MenuCategory.findOne({ name });
      if (category) {
        return res.status(400).json({ msg: 'Category with this name already exists' });
      }

      // Create new category
      category = new MenuCategory({
        name,
        description,
        icon: icon || 'folder',
        color: color || '#1976d2',
        order: order || 0,
        createdBy: req.user.id
      });

      await category.save();

      res.json(category);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/menu-categories/:id
 * @desc    Update a menu category
 * @access  Private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const category = await MenuCategory.findById(req.params.id);

      if (!category) {
        return res.status(404).json({ msg: 'Category not found' });
      }

      const { name, description, icon, color, order } = req.body;

      // Check if another category with this name already exists
      const existingCategory = await MenuCategory.findOne({ 
        name, 
        _id: { $ne: req.params.id } 
      });
      
      if (existingCategory) {
        return res.status(400).json({ msg: 'Category with this name already exists' });
      }

      // Update category fields
      category.name = name;
      category.description = description;
      category.icon = icon || 'folder';
      category.color = color || '#1976d2';
      if (order !== undefined) {
        category.order = order;
      }

      await category.save();

      res.json(category);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Category not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/menu-categories/:id
 * @desc    Delete a menu category
 * @access  Private
 */
router.delete('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const category = await MenuCategory.findById(req.params.id);

    if (!category) {
      return res.status(404).json({ msg: 'Category not found' });
    }

    await category.remove();

    res.json({ msg: 'Category removed' });
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Category not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/menu-categories/init
 * @desc    Initialize default categories
 * @access  Private
 */
router.post('/init', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    await MenuCategory.createDefaultCategories();
    const categories = await MenuCategory.find();
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;