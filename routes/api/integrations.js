const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const fs = require('fs');
const path = require('path');
const IntegrationSettings = require('../../models/IntegrationSettings');
const User = require('../../models/User');

// @route   GET api/integrations
// @desc    Get all integrations with their provisioning status
// @access  Authenticated
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Get all available integrations from the filesystem
    const integrationsDir = path.join(process.cwd(), 'server', 'integrations');
    
    // Read the integrations directory to get all available integrations
    const availableIntegrations = fs.readdirSync(integrationsDir)
      .filter(item => {
        // Filter out any non-directory items or hidden files
        const itemPath = path.join(integrationsDir, item);
        return fs.statSync(itemPath).isDirectory() && !item.startsWith('.');
      })
      .map(dir => {
        // Try to read the integration's package.json for metadata
        try {
          const packagePath = path.join(integrationsDir, dir, 'package.json');
          if (fs.existsSync(packagePath)) {
            const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            return {
              id: dir,
              name: packageData.name || dir,
              description: packageData.description || '',
              version: packageData.version || '1.0.0'
            };
          }
        } catch (err) {
          console.warn(`Could not read package.json for ${dir}:`, err);
        }

        // Fallback if no package.json or error reading it
        return {
          id: dir,
          name: dir.charAt(0).toUpperCase() + dir.slice(1).replace(/-/g, ' '),
          description: '',
          version: '1.0.0'
        };
      });
    
    // Exclude deprecated/removed integrations
    const filteredAvailableIntegrations = availableIntegrations.filter(integration => integration.id !== 'glpi');
    
    // Get all integration settings from the database
    const allSettings = await IntegrationSettings.find();
    const settingsMap = new Map();
    allSettings.forEach(setting => {
      settingsMap.set(setting.integrationId, setting);
    });
    
    // Combine the information
    const integrations = filteredAvailableIntegrations.map(integration => {
      const settings = settingsMap.get(integration.id);
      return {
        ...integration,
        supportsProvisioning: settings?.supportsProvisioning || false,
        provisioningEnabled: settings?.provisioningEnabled || false
      };
    });
    
    res.json(integrations);
  } catch (error) {
    console.error('Error fetching integrations:', error);
    res.status(500).json({ message: 'Error fetching integrations', error: error.message });
  }
});

// @route   GET api/integrations/available
// @desc    Get all available integrations
// @access  Authenticated
router.get('/available', isAuthenticated, async (req, res) => {
  try {
    const integrationsDir = path.join(process.cwd(), 'server', 'integrations');

    // Read the integrations directory to get all available integrations
    const availableIntegrations = fs.readdirSync(integrationsDir)
      .filter(item => {
        // Filter out any non-directory items or hidden files
        const itemPath = path.join(integrationsDir, item);
        return fs.statSync(itemPath).isDirectory() && !item.startsWith('.');
      })
      .map(dir => {
        // Try to read the integration's package.json for metadata
        try {
          const packagePath = path.join(integrationsDir, dir, 'package.json');
          if (fs.existsSync(packagePath)) {
            const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            return {
              id: dir,
              name: packageData.name || dir,
              description: packageData.description || '',
              version: packageData.version || '1.0.0'
            };
          }
        } catch (err) {
          console.warn(`Could not read package.json for ${dir}:`, err);
        }

        // Fallback if no package.json or error reading it
        return {
          id: dir,
          name: dir.charAt(0).toUpperCase() + dir.slice(1).replace(/-/g, ' '),
          description: '',
          version: '1.0.0'
        };
      });

    const filteredAvailableIntegrations = availableIntegrations.filter(integration => integration.id !== 'glpi');

    res.json(filteredAvailableIntegrations);
  } catch (error) {
    console.error('Error fetching available integrations:', error);
    res.status(500).json({ message: 'Error fetching available integrations', error: error.message });
  }
});

// @route   GET api/integrations/:id/config
// @desc    Get configuration for a specific integration
// @access  Authenticated
router.get('/:id/config', isAuthenticated, async (req, res) => {
  try {
    const integrationId = req.params.id;
    const configDir = path.join(process.cwd(), 'server', 'integrations', integrationId, 'config');

    // Check if the integration exists
    if (!fs.existsSync(path.join(process.cwd(), 'server', 'integrations', integrationId))) {
      return res.status(404).json({ message: `Integration ${integrationId} not found` });
    }

    // Check if the integration has a config directory
    if (!fs.existsSync(configDir)) {
      return res.json({ configOptions: [] });
    }

    // Read the config directory to get all configuration files
    const configFiles = fs.readdirSync(configDir)
      .filter(file => file.endsWith('.json') && !file.startsWith('.'));

    // Read each config file and combine them
    const configOptions = [];
    for (const file of configFiles) {
      try {
        const configData = JSON.parse(fs.readFileSync(path.join(configDir, file), 'utf8'));
        configOptions.push({
          id: file.replace('.json', ''),
          ...configData
        });
      } catch (err) {
        console.warn(`Could not read config file ${file} for ${integrationId}:`, err);
      }
    }

    res.json({ configOptions });
  } catch (error) {
    console.error(`Error fetching configuration for integration ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching configuration for integration ${req.params.id}`, error: error.message });
  }
});

// @route   GET api/integrations/settings
// @desc    Get all integration settings
// @access  Admin
router.get('/settings', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const settings = await IntegrationSettings.find();
    res.json(settings);
  } catch (error) {
    console.error('Error fetching integration settings:', error);
    res.status(500).json({ message: 'Error fetching integration settings', error: error.message });
  }
});

// @route   GET api/integrations/settings/:id
// @desc    Get settings for a specific integration
// @access  Authenticated
router.get('/settings/:id', isAuthenticated, async (req, res) => {
  try {
    const integrationId = req.params.id;
    let settings = await IntegrationSettings.findOne({ integrationId });

    // If settings don't exist, return default settings
    if (!settings) {
      settings = {
        integrationId,
        isRequired: false,
        isReadOnly: false,
        useGlobalConfig: false
      };
    }

    res.json(settings);
  } catch (error) {
    console.error(`Error fetching settings for integration ${req.params.id}:`, error);
    res.status(500).json({ message: `Error fetching settings for integration ${req.params.id}`, error: error.message });
  }
});

// @route   PUT api/integrations/settings/:id
// @desc    Update settings for a specific integration
// @access  Admin
router.put('/settings/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const integrationId = req.params.id;
    const { 
      isRequired, 
      isReadOnly, 
      useGlobalConfig, 
      supportsProvisioning, 
      provisioningEnabled, 
      provisioningConfig 
    } = req.body;

    let settings = await IntegrationSettings.findOne({ integrationId });

    // If settings don't exist, create them
    if (!settings) {
      settings = new IntegrationSettings({
        integrationId,
        isRequired: isRequired !== undefined ? isRequired : true, // Default to true - all integrations are required
        isReadOnly: isReadOnly !== undefined ? isReadOnly : true, // Default to true - all integrations are read-only
        useGlobalConfig: useGlobalConfig !== undefined ? useGlobalConfig : true, // Default to true - all integrations use global config
        supportsProvisioning: supportsProvisioning !== undefined ? supportsProvisioning : false, // Default to false
        provisioningEnabled: provisioningEnabled !== undefined ? provisioningEnabled : false, // Default to false
        provisioningConfig: provisioningConfig || {} // Default to empty object
      });
    } else {
      // Update existing settings
      if (isRequired !== undefined) settings.isRequired = isRequired;
      if (isReadOnly !== undefined) settings.isReadOnly = isReadOnly;
      if (useGlobalConfig !== undefined) settings.useGlobalConfig = useGlobalConfig;
      if (supportsProvisioning !== undefined) settings.supportsProvisioning = supportsProvisioning;
      if (provisioningEnabled !== undefined) settings.provisioningEnabled = provisioningEnabled;
      if (provisioningConfig !== undefined) settings.provisioningConfig = provisioningConfig;
    }

    await settings.save();

    // If the integration is required, update all users' preferences to enable it
    if (isRequired) {
      const users = await User.find();
      for (const user of users) {
        // Initialize preferences for this integration if they don't exist
        if (!user.integrationPreferences.has(integrationId)) {
          user.integrationPreferences.set(integrationId, {
            enabled: true,
            useGlobalConfig: useGlobalConfig
          });
        } else {
          // Update existing preferences
          const prefs = user.integrationPreferences.get(integrationId);
          prefs.enabled = true;
          if (useGlobalConfig !== undefined) prefs.useGlobalConfig = useGlobalConfig;
          user.integrationPreferences.set(integrationId, prefs);
        }

        // Activated integrations are no longer used - access is controlled by roles and permissions

        await user.save();
      }
    }

    res.json(settings);
  } catch (error) {
    console.error(`Error updating settings for integration ${req.params.id}:`, error);
    res.status(500).json({ message: `Error updating settings for integration ${req.params.id}`, error: error.message });
  }
});

// @route   GET api/integrations/preferences
// @desc    Get current user's integration preferences
// @access  Authenticated
router.get('/preferences', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Convert Map to object for response
    const preferences = {};
    for (const [key, value] of user.integrationPreferences.entries()) {
      preferences[key] = value;
    }

    res.json(preferences);
  } catch (error) {
    console.error('Error fetching user integration preferences:', error);
    res.status(500).json({ message: 'Error fetching user integration preferences', error: error.message });
  }
});

// @route   PUT api/integrations/preferences
// @desc    Update current user's integration preferences
// @access  Authenticated
router.put('/preferences', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all integration settings
    const allSettings = await IntegrationSettings.find();
    const settingsMap = new Map();
    allSettings.forEach(setting => {
      settingsMap.set(setting.integrationId, setting);
    });

    // Update preferences while respecting admin settings
    for (const [integrationId, preferences] of Object.entries(req.body)) {
      const settings = settingsMap.get(integrationId);

      // If this integration is required, ensure it stays enabled
      if (settings && settings.isRequired) {
        preferences.enabled = true;
      }

      // If this integration is set to use global config, ensure that setting is respected
      if (settings && settings.useGlobalConfig) {
        preferences.useGlobalConfig = true;
      }

      // If this integration is read-only and the user already has preferences for it,
      // don't update the preferences
      if (settings && settings.isReadOnly && user.integrationPreferences.has(integrationId)) {
        continue;
      }

      // Update the preferences
      user.integrationPreferences.set(integrationId, preferences);

      // Activated integrations are no longer used - access is controlled by roles and permissions
    }

    await user.save();

    // Convert Map to object for response
    const updatedPreferences = {};
    for (const [key, value] of user.integrationPreferences.entries()) {
      updatedPreferences[key] = value;
    }

    res.json(updatedPreferences);
  } catch (error) {
    console.error('Error updating user integration preferences:', error);
    res.status(500).json({ message: 'Error updating user integration preferences', error: error.message });
  }
});

module.exports = router;
