const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const User = require('../../models/User');
const RoleSettings = require('../../models/RoleSettings');
const { check, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const nodemailer = require('nodemailer');
const crypto = require('crypto');
const emailUtils = require('../../server/utils/emailTemplates/emailUtils');
const websocketServer = require('../../server/websocket/websocketServer');

/**
 * @route   GET /api/users
 * @desc    Get all users
 * @access  private
 */
router.get('/', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const users = await User.find().select('-googleAccessToken -googleRefreshToken');
    res.json(users);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/users/me
 * @desc    Get current user's information
 * @access  private
 */
router.get('/me', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-googleAccessToken -googleRefreshToken');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-googleAccessToken -googleRefreshToken');

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.json(user);
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/users/:id/roles
 * @desc    Update user roles
 * @access  private
 */
router.put(
  '/:id/roles',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('roles', 'Roles are required').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      user.roles = req.body.roles;
      await user.save();

      res.json(user);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'User not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/users/:id/status
 * @desc    Activate or deactivate user
 * @access  private
 */
router.put(
  '/:id/status',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('isActive', 'isActive field is required').isBoolean()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      user.isActive = req.body.isActive;
      await user.save();

      res.json(user);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'User not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user information
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').optional().notEmpty(),
    check('email', 'Please include a valid email').optional().isEmail(),
    check('avatar', 'Avatar URL is not valid').optional().isURL(),
    check('roles', 'Roles must be an array').optional().isArray(),
    check('isActive', 'isActive must be a boolean').optional().isBoolean()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.params.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Update fields if they are provided in the request
      if (req.body.name) user.name = req.body.name;
      if (req.body.email) user.email = req.body.email;
      if (req.body.avatar) user.avatar = req.body.avatar;
      if (req.body.roles) user.roles = req.body.roles;
      if (typeof req.body.isActive === 'boolean') user.isActive = req.body.isActive;

      // Update integration preferences if provided
      if (req.body.integrationPreferences) {
        // If it's already a Map, use it directly
        if (req.body.integrationPreferences instanceof Map) {
          user.integrationPreferences = req.body.integrationPreferences;
        } 
        // Otherwise convert from object to Map
        else {
          // Clear existing preferences
          user.integrationPreferences.clear();

          // Add new preferences
          Object.entries(req.body.integrationPreferences).forEach(([key, value]) => {
            user.integrationPreferences.set(key, value);
          });
        }
      }

      await user.save();

      // Return the updated user without sensitive information
      const updatedUser = await User.findById(req.params.id).select('-googleAccessToken -googleRefreshToken');
      res.json(updatedUser);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'User not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   POST /api/users/create-local
 * @desc    Create a new local user (admin only)
 * @access  private
 */
router.post(
  '/create-local',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('roles', 'Roles must be an array').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, email, roles, jobTitle, department, phoneNumber, location } = req.body;

    try {
      // Check if user already exists
      let user = await User.findOne({ email });
      if (user) {
        return res.status(400).json({ msg: 'User already exists' });
      }

      // Generate a default avatar using initials
      const initials = name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase();
      const avatarColor = Math.floor(Math.random() * 16777215).toString(16);
      const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(
        initials
      )}&background=${avatarColor}&color=fff`;

      // Create the user
      // Get default role from database settings or use 'user' as fallback
      let defaultRole = 'user';
      
      try {
        // Get settings from database
        const settings = await RoleSettings.getCurrentSettings();
        defaultRole = settings.defaultRoleLocalUsers;
      } catch (error) {
        console.error('Error getting role settings, using defaults:', error);
        // Continue with default role if there's an error
      }
      
      user = new User({
        authType: 'local',
        name,
        email,
        avatar: avatarUrl,
        roles: roles || [defaultRole], // Use provided roles or default role
        jobTitle,
        department,
        phoneNumber,
        location,
        isActive: true,
        twoFactorEnabled: false,
        twoFactorSetupComplete: false
      });

      await user.save();

      // Generate a login token for the welcome email
      const loginToken = crypto.randomBytes(20).toString('hex');
      user.passwordResetToken = loginToken; // Repurposing passwordResetToken for login
      user.passwordResetExpires = Date.now() + 24 * 3600000; // 24 hours
      await user.save();

      // Send welcome email with setup instructions
      try {
        // Use the email templating system to send the welcome email
        await emailUtils.sendTemplatedEmail({
          to: email,
          subject: 'Welcome to CSF Portal - Account Setup',
          templateName: 'welcome',
          data: {
            name: name,
            loginLink: `${process.env.FRONTEND_URL}/initial-login/${loginToken}`
          }
        });
        
        console.log(`Welcome email sent to ${email}`);
      } catch (emailErr) {
        console.error('Error sending welcome email:', emailErr);
        // Continue even if email fails, just log the error
      }

      res.json({
        msg: 'User created successfully',
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          roles: user.roles
        }
      });
    } catch (err) {
      console.error('Error creating local user:', err);
      res.status(500).json({ msg: 'Server error during user creation' });
    }
  }
);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete a user
 * @access  private
 */
router.delete('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    await User.findByIdAndRemove(req.params.id);

    res.json({ msg: 'User deleted' });
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/users/me/notification-preferences
 * @desc    Get current user's notification preferences
 * @access  private
 */
router.get('/me/notification-preferences', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('notificationPreferences');
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    const prefs = user.notificationPreferences || { emailEnabled: true, systemEnabled: true };
    res.json(prefs);
  } catch (err) {
    console.error('Error fetching notification preferences:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/users/me/notification-preferences
 * @desc    Update current user's notification preferences
 * @access  private
 */
router.put(
  '/me/notification-preferences',
  [
    isAuthenticated,
    check('emailEnabled').optional().isBoolean().withMessage('emailEnabled must be a boolean'),
    check('systemEnabled').optional().isBoolean().withMessage('systemEnabled must be a boolean')
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.user.id);
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      if (!user.notificationPreferences) user.notificationPreferences = {};

      const { emailEnabled, systemEnabled } = req.body;
      if (emailEnabled !== undefined) user.notificationPreferences.emailEnabled = emailEnabled;
      if (systemEnabled !== undefined) user.notificationPreferences.systemEnabled = systemEnabled;

      await user.save();

      res.json(user.notificationPreferences);
    } catch (err) {
      console.error('Error updating notification preferences:', err);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/users/me
 * @desc    Update current user's profile
 * @access  private
 */
router.put(
  '/me',
  [
    isAuthenticated,
    check('name', 'Name is required').optional().notEmpty(),
    check('avatar', 'Avatar URL is not valid').optional().isURL(),
    check('jobTitle', 'Job title is required').optional().notEmpty(),
    check('department', 'Department is required').optional().notEmpty(),
    check('phoneNumber', 'Phone number is required').optional().notEmpty(),
    check('location', 'Location is required').optional().notEmpty(),
    check('bio', 'Bio is required').optional().notEmpty(),
    check('skills', 'Skills must be an array').optional().isArray(),
    check('tags', 'Tags must be an array').optional().isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.user.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      // Update basic profile fields if provided
      if (req.body.name) user.name = req.body.name;
      if (req.body.avatar) user.avatar = req.body.avatar;

      // Update additional profile fields if provided
      if (req.body.jobTitle !== undefined) user.jobTitle = req.body.jobTitle;
      if (req.body.department !== undefined) user.department = req.body.department;
      if (req.body.phoneNumber !== undefined) user.phoneNumber = req.body.phoneNumber;
      if (req.body.location !== undefined) user.location = req.body.location;
      if (req.body.bio !== undefined) user.bio = req.body.bio;
      if (req.body.skills !== undefined) user.skills = req.body.skills;
      if (req.body.tags !== undefined) user.tags = req.body.tags;

      await user.save();

      // Return the updated user without sensitive information
      const updatedUser = await User.findById(req.user.id)
        .select('-googleAccessToken -googleRefreshToken');

      res.json(updatedUser);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * Note: Integration activation endpoints have been removed.
 * All integrations are now assumed to be active for all users.
 * Access is controlled by roles and permissions instead.
 */

/**
 * @route   GET /api/users/me/widget-preferences
 * @desc    Get current user's widget preferences
 * @access  private
 */
router.get('/me/widget-preferences', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    res.json(user.widgetPreferences || {});
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/users/me/widget-preferences
 * @desc    Update current user's widget preferences
 * @access  private
 */
router.put(
  '/me/widget-preferences',
  [
    isAuthenticated,
    check('widgetType', 'Widget type is required').notEmpty(),
    check('preferences', 'Preferences object is required').isObject()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.user.id);

      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      const { widgetType, preferences } = req.body;

      // Initialize widgetPreferences if it doesn't exist
      if (!user.widgetPreferences) {
        user.widgetPreferences = {};
      }

      // Update the preferences for the specified widget type
      user.widgetPreferences[widgetType] = preferences;

      await user.save();

      res.json(user.widgetPreferences);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/users/me/activity
 * @desc    Update last active timestamp for current user
 * @access  private
 */
router.put('/me/activity', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    const now = new Date();

    // Initialize presence if needed
    if (!user.presence) {
      user.presence = {};
    }

    const last = user.presence.lastActiveAt ? new Date(user.presence.lastActiveAt) : null;

    // Throttle updates to reduce writes (only update if older than 30s)
    if (!last || (now - last) > 30000) {
      user.presence.lastActiveAt = now;
      await user.save();
    }

    const payload = {
      userId: user._id.toString(),
      presence: {
        status: user.presence.status || 'available',
        statusMessage: user.presence.statusMessage || '',
        lastActiveAt: user.presence.lastActiveAt
      },
      timestamp: now.toISOString()
    };

    try {
      websocketServer.broadcast('presence', payload);
    } catch (err) {
      console.error('Error broadcasting presence update:', err);
    }

    res.json(payload);
  } catch (err) {
    console.error('Error updating activity:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/users/me/status
 * @desc    Update presence status for current user
 * @access  private
 */
router.put(
  '/me/status',
  [
    isAuthenticated,
    check('status', 'Invalid status')
      .isIn(['available', 'away', 'lunch', 'busy', 'custom']),
    check('statusMessage').optional().isString()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const user = await User.findById(req.user.id);
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }

      if (!user.presence) {
        user.presence = {};
      }

      const { status, statusMessage = '' } = req.body;
      user.presence.status = status;
      user.presence.statusMessage = status === 'custom' ? statusMessage : (status === 'available' ? '' : statusMessage);
      user.presence.statusUpdatedAt = new Date();

      await user.save();

      const payload = {
        userId: user._id.toString(),
        presence: {
          status: user.presence.status,
          statusMessage: user.presence.statusMessage,
          lastActiveAt: user.presence.lastActiveAt || new Date()
        },
        timestamp: new Date().toISOString()
      };

      try {
        websocketServer.broadcast('presence', payload);
      } catch (err) {
        console.error('Error broadcasting presence status change:', err);
      }

      res.json(payload);
    } catch (err) {
      console.error('Error updating presence status:', err);
      res.status(500).send('Server Error');
    }
  }
);

module.exports = router;
