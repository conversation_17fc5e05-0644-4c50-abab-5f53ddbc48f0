const express = require('express');
const router = express.Router();
const { isAuthenticated, hasPermission } = require('../../middleware/auth');
const unifiAccessController = require('../../server/controllers/unifiAccessController');

// @route   GET api/unifi-access/doors
// @desc    Get all UniFi Access doors
// @access  Private (requires unifi-access:access permission)
router.get('/doors', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getDoors);

// @route   GET api/unifi-access/doors/:id
// @desc    Get UniFi Access door details
// @access  Private (requires unifi-access:access permission)
router.get('/doors/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getDoorDetails);

// @route   POST api/unifi-access/doors/:id/unlock
// @desc    Unlock a door
// @access  Private (requires unifi-access:access permission)
router.post('/doors/:id/unlock', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.unlockDoor);

// @route   POST api/unifi-access/doors/:id/lock
// @desc    Lock a door (Note: This functionality is not supported by the UniFi Access API and will return an error)
// @access  Private (requires unifi-access:access permission)
router.post('/doors/:id/lock', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.lockDoor);

// @route   GET api/unifi-access/access-points
// @desc    Get all UniFi Access access points
// @access  Private (requires unifi-access:access permission)
router.get('/access-points', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getAccessPoints);

// @route   GET api/unifi-access/users
// @desc    Get all UniFi Access users
// @access  Private (requires unifi-access:access permission)
router.get('/users', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getUsers);

// @route   GET api/unifi-access/events
// @desc    Get UniFi Access events
// @access  Private (requires unifi-access:access permission)
router.get('/events', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getEvents);

// @route   GET api/unifi-access/system
// @desc    Get UniFi Access system status
// @access  Private (requires unifi-access:access permission)
router.get('/system', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getSystemStatus);

// @route   GET api/unifi-access/config
// @desc    Get UniFi Access configuration status
// @access  Private
router.get('/config', isAuthenticated, unifiAccessController.getConfig);

// @route   GET api/unifi-access/configuration
// @desc    Alternative route for getting UniFi Access configuration status
// @access  Private
router.get('/configuration', isAuthenticated, unifiAccessController.getConfig);

// @route   GET api/unifi-access/users/:id
// @desc    Get UniFi Access user by ID
// @access  Private (requires unifi-access:access permission)
router.get('/users/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getUserById);

// @route   GET api/unifi-access/doors/:id/status
// @desc    Get status for a specific door
// @access  Private (requires unifi-access:access permission)
router.get('/doors/:id/status', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getDoorStatusHandler);

// Groups endpoints
// @route   GET api/unifi-access/groups
// @desc    Get all UniFi Access groups
// @access  Private (requires unifi-access:access permission)
router.get('/groups', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getGroups);

// @route   GET api/unifi-access/groups/:id
// @desc    Get UniFi Access group details
// @access  Private (requires unifi-access:access permission)
router.get('/groups/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getGroupDetails);

// @route   POST api/unifi-access/groups
// @desc    Create a UniFi Access group
// @access  Private (requires unifi-access:access permission)
router.post('/groups', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.createGroup);

// @route   PUT api/unifi-access/groups/:id
// @desc    Update a UniFi Access group
// @access  Private (requires unifi-access:access permission)
router.put('/groups/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.updateGroup);

// @route   DELETE api/unifi-access/groups/:id
// @desc    Delete a UniFi Access group
// @access  Private (requires unifi-access:access permission)
router.delete('/groups/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.deleteGroup);

// Policies endpoints
// @route   GET api/unifi-access/policies
// @desc    Get all UniFi Access policies
// @access  Private (requires unifi-access:access permission)
router.get('/policies', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getPolicies);

// @route   GET api/unifi-access/policies/:id
// @desc    Get UniFi Access policy details
// @access  Private (requires unifi-access:access permission)
router.get('/policies/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getPolicyDetails);

// @route   POST api/unifi-access/policies
// @desc    Create a UniFi Access policy
// @access  Private (requires unifi-access:access permission)
router.post('/policies', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.createPolicy);

// @route   PUT api/unifi-access/policies/:id
// @desc    Update a UniFi Access policy
// @access  Private (requires unifi-access:access permission)
router.put('/policies/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.updatePolicy);

// @route   DELETE api/unifi-access/policies/:id
// @desc    Delete a UniFi Access policy
// @access  Private (requires unifi-access:access permission)
router.delete('/policies/:id', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.deletePolicy);

// @route   POST api/unifi-access/policies/:id/doors/:doorId
// @desc    Assign a policy to a door
// @access  Private (requires unifi-access:access permission)
router.post('/policies/:id/doors/:doorId', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.assignPolicyToDoor);

// @route   DELETE api/unifi-access/policies/:id/doors/:doorId
// @desc    Remove a policy from a door
// @access  Private (requires unifi-access:access permission)
router.delete('/policies/:id/doors/:doorId', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.removePolicyFromDoor);

// Schedules (stub)
// @route   GET api/unifi-access/schedules
// @desc    Get UniFi Access schedules (not implemented)
// @access  Private (requires unifi-access:access permission)
router.get('/schedules', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getSchedules);

// Access levels (stub)
// @route   GET api/unifi-access/access-levels
// @desc    Get UniFi Access access levels (not implemented)
// @access  Private (requires unifi-access:access permission)
router.get('/access-levels', isAuthenticated, hasPermission('unifi-access:access'), unifiAccessController.getAccessLevels);

module.exports = router;
