const express = require('express');
const router = express.Router();
const googleDriveController = require('../../server/controllers/googleDriveController');
const googleDriveFavoriteController = require('../../server/controllers/googleDriveFavoriteController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');

/**
 * @route   POST /api/google-drive/config
 * @desc    Save Google Drive configuration
 */
router.post('/config', isAuthenticated, hasRoles(['admin']), googleDriveController.saveConfig);

/**
 * @route   GET /api/google-drive/config
 * @desc    Get Google Drive configuration
 */
router.get('/config', isAuthenticated, hasRoles(['admin']), googleDriveController.getConfig);

/**
 * @route   GET /api/google-drive/auth-url
 * @desc    Get Google Drive authentication URL
 */
router.get('/auth-url', isAuthenticated, hasRoles(['admin']), googleDriveController.getAuthUrl);

/**
 * @route   GET /api/google-drive/callback
 * @desc    Handle Google Drive OAuth2 callback
 */
router.get('/callback', googleDriveController.handleCallback);

/**
 * @route   GET /api/google-drive/files
 * @desc    List files in Google Drive
 */
router.get('/files', isAuthenticated, googleDriveController.listFiles);

/**
 * @route   GET /api/google-drive/search
 * @desc    Search files in Google Drive
 */
router.get('/search', isAuthenticated, googleDriveController.searchFiles);

/**
 * @route   GET /api/google-drive/files/:fileId
 * @desc    Get file metadata
 */
router.get('/files/:fileId', isAuthenticated, googleDriveController.getFile);

/**
 * @route   GET /api/google-drive/viewer/:fileId
 * @desc    Get embedded viewer URL
 */
router.get('/viewer/:fileId', isAuthenticated, googleDriveController.getEmbeddedViewerUrl);

/**
 * @route   GET /api/google-drive/editor/:fileId
 * @desc    Get embedded editor URL
 */
router.get('/editor/:fileId', isAuthenticated, googleDriveController.getEmbeddedEditorUrl);

/**
 * @route   POST /api/google-drive/favorites
 * @desc    Add a file to favorites
 */
router.post('/favorites', isAuthenticated, googleDriveFavoriteController.addFavorite);

/**
 * @route   DELETE /api/google-drive/favorites/:fileId
 * @desc    Remove a file from favorites
 */
router.delete('/favorites/:fileId', isAuthenticated, googleDriveFavoriteController.removeFavorite);

/**
 * @route   GET /api/google-drive/favorites
 * @desc    Get all favorites for the current user
 */
router.get('/favorites', isAuthenticated, googleDriveFavoriteController.getFavorites);

/**
 * @route   GET /api/google-drive/favorites/:fileId
 * @desc    Check if a file is favorited by the current user
 */
router.get('/favorites/:fileId', isAuthenticated, googleDriveFavoriteController.checkFavorite);

module.exports = router;
