const express = require('express');
const router = express.Router();
const expressWs = require('express-ws');
const { isAuthenticated, hasActivatedIntegration } = require('../../middleware/auth');
const dreoController = require('../../server/controllers/dreoController');

// Add WebSocket support to the router
expressWs(router);

// @route   GET api/dreo/devices
// @desc    Get all Dreo devices
// @access  Private (requires dreo integration)
router.get('/devices', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.getDevices);

// @route   GET api/dreo/devices/:id
// @desc    Get Dreo device details
// @access  Private (requires dreo integration)
router.get('/devices/:id', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.getDeviceDetails);

// @route   POST api/dreo/devices/:id/power
// @desc    Set device power state
// @access  Private (requires dreo integration)
router.post('/devices/:id/power', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.setPower);

// @route   POST api/dreo/devices/:id/temperature
// @desc    Set device temperature
// @access  Private (requires dreo integration)
router.post('/devices/:id/temperature', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.setTemperature);

// @route   POST api/dreo/devices/:id/fan-speed
// @desc    Set device fan speed
// @access  Private (requires dreo integration)
router.post('/devices/:id/fan-speed', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.setFanSpeed);

// @route   POST api/dreo/devices/:id/mode
// @desc    Set device mode
// @access  Private (requires dreo integration)
router.post('/devices/:id/mode', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.setMode);

// @route   POST api/dreo/devices/:id/control
// @desc    Control device with custom commands
// @access  Private (requires dreo integration)
router.post('/devices/:id/control', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.controlDevice);

// @route   POST api/dreo/config
// @desc    Save Dreo configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, dreoController.saveConfig);

// @route   GET api/dreo/config
// @desc    Get Dreo configuration status
// @access  Private
router.get('/config', isAuthenticated, dreoController.getConfig);

// @route   POST api/dreo/one-click-setup
// @desc    Set up Dreo with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, dreoController.oneClickSetup);

// @route   POST api/dreo/devices/:id/register-listener
// @desc    Register a client for real-time updates for a specific device
// @access  Private (requires dreo integration)
router.post('/devices/:id/register-listener', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.registerDeviceListener);

// @route   POST api/dreo/devices/:id/unregister-listener
// @desc    Unregister a client from real-time updates for a specific device
// @access  Private (requires dreo integration)
router.post('/devices/:id/unregister-listener', isAuthenticated, hasActivatedIntegration('dreo'), dreoController.unregisterDeviceListener);

// @route   WS api/dreo/ws
// @desc    WebSocket endpoint for real-time device updates
// @access  Private (requires authentication token)
router.ws('/ws', dreoController.handleWebSocket);

module.exports = router;
