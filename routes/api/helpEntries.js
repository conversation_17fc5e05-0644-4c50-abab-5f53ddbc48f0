const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const HelpEntry = require('../../models/HelpEntry');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/help/entries
 * @desc    Get all help entries
 * @access  public
 */
router.get('/', async (req, res) => {
  try {
    const entries = await HelpEntry.find({ isPublished: true })
      .sort({ category: 1, title: 1 });
    res.json(entries);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/help/entries/search
 * @desc    Search help entries
 * @access  public
 */
router.get('/search', async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({ msg: 'Search query is required' });
    }
    
    const entries = await HelpEntry.find(
      { 
        $text: { $search: query },
        isPublished: true 
      },
      { 
        score: { $meta: 'textScore' } 
      }
    )
    .sort({ score: { $meta: 'textScore' } })
    .limit(20);
    
    res.json(entries);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/help/entries/category/:categoryId
 * @desc    Get help entries by category
 * @access  public
 */
router.get('/category/:categoryId', async (req, res) => {
  try {
    const entries = await HelpEntry.find({ 
      category: req.params.categoryId,
      isPublished: true 
    }).sort({ title: 1 });
    
    res.json(entries);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Category not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/help/entries/:id
 * @desc    Get help entry by ID
 * @access  public
 */
router.get('/:id', async (req, res) => {
  try {
    const entry = await HelpEntry.findById(req.params.id);
    
    if (!entry) {
      return res.status(404).json({ msg: 'Help entry not found' });
    }
    
    // Increment view count
    entry.viewCount += 1;
    await entry.save();
    
    res.json(entry);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Help entry not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/help/entries
 * @desc    Create a help entry
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin', 'content_manager']),
    check('title', 'Title is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        category, 
        tags, 
        externalLinks, 
        googleDocId,
        isPublished 
      } = req.body;

      const newEntry = new HelpEntry({
        title,
        content,
        category,
        tags: tags || [],
        externalLinks: externalLinks || [],
        googleDocId,
        isPublished: isPublished !== undefined ? isPublished : true,
        createdBy: req.user.id,
        updatedBy: req.user.id
      });

      const entry = await newEntry.save();
      res.json(entry);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/help/entries/:id
 * @desc    Update a help entry
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin', 'content_manager']),
    check('title', 'Title is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        category, 
        tags, 
        externalLinks, 
        googleDocId,
        isPublished 
      } = req.body;

      const entry = await HelpEntry.findById(req.params.id);
      
      if (!entry) {
        return res.status(404).json({ msg: 'Help entry not found' });
      }
      
      entry.title = title;
      entry.content = content;
      entry.category = category;
      entry.tags = tags || [];
      entry.externalLinks = externalLinks || [];
      entry.googleDocId = googleDocId;
      entry.isPublished = isPublished !== undefined ? isPublished : entry.isPublished;
      entry.updatedBy = req.user.id;

      await entry.save();
      res.json(entry);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Help entry not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/help/entries/:id
 * @desc    Delete a help entry
 * @access  private
 */
router.delete(
  '/:id',
  [isAuthenticated, hasRoles(['admin', 'content_manager'])],
  async (req, res) => {
    try {
      const entry = await HelpEntry.findById(req.params.id);
      
      if (!entry) {
        return res.status(404).json({ msg: 'Help entry not found' });
      }
      
      await entry.remove();
      res.json({ msg: 'Help entry removed' });
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Help entry not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/help/entries/:id/helpful
 * @desc    Mark a help entry as helpful
 * @access  public
 */
router.put('/:id/helpful', async (req, res) => {
  try {
    const entry = await HelpEntry.findById(req.params.id);
    
    if (!entry) {
      return res.status(404).json({ msg: 'Help entry not found' });
    }
    
    entry.helpfulCount += 1;
    await entry.save();
    
    res.json({ helpfulCount: entry.helpfulCount });
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Help entry not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/help/entries/:id/unhelpful
 * @desc    Mark a help entry as unhelpful
 * @access  public
 */
router.put('/:id/unhelpful', async (req, res) => {
  try {
    const entry = await HelpEntry.findById(req.params.id);
    
    if (!entry) {
      return res.status(404).json({ msg: 'Help entry not found' });
    }
    
    entry.unhelpfulCount += 1;
    await entry.save();
    
    res.json({ unhelpfulCount: entry.unhelpfulCount });
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Help entry not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

module.exports = router;