const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const ShortcutCategory = require('../../models/ShortcutCategory');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/shortcut-categories
 * @desc    Get all shortcut categories
 * @access  Public
 */
router.get('/', async (req, res) => {
  try {
    const categories = await ShortcutCategory.find().sort({ order: 1, name: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/shortcut-categories/:id
 * @desc    Get shortcut category by ID
 * @access  Public
 */
router.get('/:id', async (req, res) => {
  try {
    const category = await ShortcutCategory.findById(req.params.id);

    if (!category) {
      return res.status(404).json({ msg: 'Category not found' });
    }

    res.json(category);
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Category not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/shortcut-categories
 * @desc    Create a shortcut category
 * @access  Private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, description, icon, color, order } = req.body;

    try {
      // Check if category with this name already exists
      let category = await ShortcutCategory.findOne({ name });
      if (category) {
        return res.status(400).json({ msg: 'Category with this name already exists' });
      }

      // Create new category
      category = new ShortcutCategory({
        name,
        description,
        icon: icon || 'folder',
        color: color || '#1976d2',
        order: order || 0,
        createdBy: req.user.id
      });

      await category.save();

      res.json(category);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/shortcut-categories/:id
 * @desc    Update a shortcut category
 * @access  Private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin']),
    check('name', 'Name is required').not().isEmpty(),
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const category = await ShortcutCategory.findById(req.params.id);

      if (!category) {
        return res.status(404).json({ msg: 'Category not found' });
      }

      const { name, description, icon, color, order } = req.body;

      // Check if another category with this name already exists
      const existingCategory = await ShortcutCategory.findOne({ 
        name, 
        _id: { $ne: req.params.id } 
      });
      
      if (existingCategory) {
        return res.status(400).json({ msg: 'Category with this name already exists' });
      }

      // Update category fields
      category.name = name;
      category.description = description;
      category.icon = icon || 'folder';
      category.color = color || '#1976d2';
      if (order !== undefined) {
        category.order = order;
      }

      await category.save();

      res.json(category);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Category not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/shortcut-categories/:id
 * @desc    Delete a shortcut category
 * @access  Private
 */
router.delete('/:id', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    const category = await ShortcutCategory.findById(req.params.id);

    if (!category) {
      return res.status(404).json({ msg: 'Category not found' });
    }

    await category.remove();

    res.json({ msg: 'Category removed' });
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Category not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/shortcut-categories/init
 * @desc    Initialize default categories
 * @access  Private
 */
router.post('/init', isAuthenticated, hasRoles(['admin']), async (req, res) => {
  try {
    await ShortcutCategory.createDefaultCategories();
    const categories = await ShortcutCategory.find();
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;