const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const noteController = require('../../server/controllers/noteController');
const { check } = require('express-validator');

/**
 * @route   GET /api/notes
 * @desc    Get all notes for the current user
 * @access  private
 */
router.get('/', isAuthenticated, noteController.getUserNotes);

/**
 * @route   GET /api/notes/:id
 * @desc    Get note by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, noteController.getNoteById);

/**
 * @route   POST /api/notes
 * @desc    Create a new note
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    check('title', 'Title is required').not().isEmpty()
  ],
  noteController.createNote
);

/**
 * @route   PUT /api/notes/:id
 * @desc    Update a note
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    check('title', 'Title is required').not().isEmpty()
  ],
  noteController.updateNote
);

/**
 * @route   DELETE /api/notes/:id
 * @desc    Delete a note
 * @access  private
 */
router.delete('/:id', isAuthenticated, noteController.deleteNote);

/**
 * @route   PUT /api/notes/:id/toggle-completion
 * @desc    Toggle note completion status (for to-dos)
 * @access  private
 */
router.put('/:id/toggle-completion', isAuthenticated, noteController.toggleNoteCompletion);

/**
 * @route   PUT /api/notes/:id/toggle-pinned
 * @desc    Toggle note pinned status
 * @access  private
 */
router.put('/:id/toggle-pinned', isAuthenticated, noteController.toggleNotePinned);

module.exports = router;