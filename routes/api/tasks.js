const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const taskController = require('../../server/controllers/taskController');
const { check } = require('express-validator');

/**
 * @route   GET /api/tasks
 * @desc    Get all tasks
 * @access  private
 */
router.get('/', isAuthenticated, taskController.getAllTasks);

/**
 * @route   GET /api/tasks/filter
 * @desc    Get tasks by filter
 * @access  private
 */
router.get('/filter', isAuthenticated, taskController.getTasksByFilter);

/**
 * @route   GET /api/tasks/:id
 * @desc    Get task by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, taskController.getTaskById);

/**
 * @route   POST /api/tasks
 * @desc    Create a new task
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    check('title', 'Title is required').not().isEmpty(),
    check('taskType', 'Task type is required').not().isEmpty()
  ],
  taskController.createTask
);

/**
 * @route   PUT /api/tasks/:id
 * @desc    Update a task
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    check('title', 'Title is required').not().isEmpty()
  ],
  taskController.updateTask
);

/**
 * @route   DELETE /api/tasks/:id
 * @desc    Delete a task
 * @access  private
 */
router.delete('/:id', isAuthenticated, taskController.deleteTask);

/**
 * @route   PUT /api/tasks/:id/assign
 * @desc    Assign a task to a user
 * @access  private
 */
router.put(
  '/:id/assign',
  [
    isAuthenticated,
    check('assignedTo', 'User ID to assign to is required').not().isEmpty()
  ],
  taskController.assignTask
);

/**
 * @route   PUT /api/tasks/:id/status
 * @desc    Update the status of a task
 * @access  private
 */
router.put(
  '/:id/status',
  [
    isAuthenticated,
    check('status', 'Status is required').isIn(['open', 'in_progress', 'on_hold', 'completed', 'cancelled']),
    check('comment', 'Comment is required').not().isEmpty()
  ],
  taskController.updateStatus
);

/**
 * @route   POST /api/tasks/:id/comment
 * @desc    Add a comment to a task
 * @access  private
 */
router.post(
  '/:id/comment',
  [
    isAuthenticated,
    check('text', 'Comment text is required').not().isEmpty()
  ],
  taskController.addComment
);

/**
 * @route   POST /api/tasks/:id/attachment
 * @desc    Add an attachment to a task
 * @access  private
 */
router.post(
  '/:id/attachment',
  [
    isAuthenticated,
    check('name', 'Attachment name is required').not().isEmpty(),
    check('url', 'Attachment URL is required').not().isEmpty(),
    check('type', 'Attachment type is required').not().isEmpty()
  ],
  taskController.addAttachment
);

module.exports = router;