const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const wiimController = require('../../server/controllers/wiimController');

// @route   GET api/wiim/device
// @desc    Get WiiM device information
// @access  Private
router.get('/device', isAuthenticated, wiimController.getDeviceInfo);

// @route   GET api/wiim/playback
// @desc    Get current playback status
// @access  Private
router.get('/playback', isAuthenticated, wiimController.getPlaybackStatus);

// @route   POST api/wiim/playback/play
// @desc    Play
// @access  Private
router.post('/playback/play', isAuthenticated, wiimController.play);

// @route   POST api/wiim/playback/pause
// @desc    Pause
// @access  Private
router.post('/playback/pause', isAuthenticated, wiimController.pause);

// @route   POST api/wiim/playback/next
// @desc    Next track
// @access  Private
router.post('/playback/next', isAuthenticated, wiimController.next);

// @route   POST api/wiim/playback/previous
// @desc    Previous track
// @access  Private
router.post('/playback/previous', isAuthenticated, wiimController.previous);

// @route   POST api/wiim/playback/volume
// @desc    Set volume
// @access  Private
router.post('/playback/volume', isAuthenticated, wiimController.setVolume);

// @route   GET api/wiim/playlist
// @desc    Get current playlist
// @access  Private
router.get('/playlist', isAuthenticated, wiimController.getPlaylist);

// @route   POST api/wiim/playlist/track/:index
// @desc    Play specific track from playlist
// @access  Private
router.post('/playlist/track/:index', isAuthenticated, wiimController.playTrack);

// @route   POST api/wiim/playback/repeat
// @desc    Set repeat mode
// @access  Private
router.post('/playback/repeat', isAuthenticated, wiimController.setRepeat);

// @route   POST api/wiim/playback/shuffle
// @desc    Set shuffle mode
// @access  Private
router.post('/playback/shuffle', isAuthenticated, wiimController.setShuffle);

// @route   GET api/wiim/playback/check-and-advance
// @desc    Check if the current playlist has ended and advance to the next playlist if needed
// @access  Private
router.get('/playback/check-and-advance', isAuthenticated, wiimController.checkAndAdvancePlaylist);

// @route   GET api/wiim/playlists
// @desc    Get list of available playlists
// @access  Private
router.get('/playlists', isAuthenticated, wiimController.getPlaylists);

// @route   POST api/wiim/playlists/:playlistId/play
// @desc    Play a specific playlist
// @access  Private
router.post('/playlists/:playlistId/play', isAuthenticated, wiimController.playPlaylist);

// @route   GET api/wiim/playlists/:playlistId/songs
// @desc    Get songs in a specific playlist without playing it
// @access  Private
router.get('/playlists/:playlistId/songs', isAuthenticated, wiimController.getPlaylistSongs);

// @route   GET api/wiim/search
// @desc    Search for music
// @access  Private
router.get('/search', isAuthenticated, wiimController.search);

// @route   GET api/wiim/spotify/playlists
// @desc    Get Spotify playlists
// @access  Private
router.get('/spotify/playlists', isAuthenticated, wiimController.getSpotifyPlaylists);

// @route   POST api/wiim/spotify/playlists/:playlistId/play
// @desc    Play Spotify playlist
// @access  Private
router.post('/spotify/playlists/:playlistId/play', isAuthenticated, wiimController.playSpotifyPlaylist);

// @route   GET api/wiim/spotify/search
// @desc    Search Spotify for tracks, albums, artists, or playlists
// @access  Private
router.get('/spotify/search', isAuthenticated, wiimController.searchSpotify);

// @route   POST api/wiim/spotify/tracks/:trackId/play
// @desc    Play a Spotify track
// @access  Private
router.post('/spotify/tracks/:trackId/play', isAuthenticated, wiimController.playSpotifyTrack);

// @route   POST api/wiim/spotify/albums/:albumId/play
// @desc    Play a Spotify album
// @access  Private
router.post('/spotify/albums/:albumId/play', isAuthenticated, wiimController.playSpotifyAlbum);

// @route   POST api/wiim/spotify/artists/:artistId/play
// @desc    Play a Spotify artist's top tracks
// @access  Private
router.post('/spotify/artists/:artistId/play', isAuthenticated, wiimController.playSpotifyArtist);

// @route   GET api/wiim/spotify/devices
// @desc    Get available Spotify devices for playback
// @access  Private
router.get('/spotify/devices', isAuthenticated, wiimController.getSpotifyDevices);

// @route   GET api/wiim/spotify/playback
// @desc    Get current Spotify playback state
// @access  Private
router.get('/spotify/playback', isAuthenticated, wiimController.getSpotifyPlaybackState);

// @route   POST api/wiim/spotify/tracks/:trackId/play-connect
// @desc    Play a Spotify track using Spotify Connect API
// @access  Private
router.post('/spotify/tracks/:trackId/play-connect', isAuthenticated, wiimController.playSpotifyTrackConnect);

// @route   POST api/wiim/spotify/albums/:albumId/play-connect
// @desc    Play a Spotify album using Spotify Connect API
// @access  Private
router.post('/spotify/albums/:albumId/play-connect', isAuthenticated, wiimController.playSpotifyAlbumConnect);

// @route   POST api/wiim/spotify/artists/:artistId/play-connect
// @desc    Play a Spotify artist using Spotify Connect API
// @access  Private
router.post('/spotify/artists/:artistId/play-connect', isAuthenticated, wiimController.playSpotifyArtistConnect);

// @route   POST api/wiim/spotify/playlists/:playlistId/play-connect
// @desc    Play a Spotify playlist using Spotify Connect API
// @access  Private
router.post('/spotify/playlists/:playlistId/play-connect', isAuthenticated, wiimController.playSpotifyPlaylistConnect);

// @route   POST api/wiim/spotify/transfer-playback
// @desc    Transfer playback to the WiiM device
// @access  Private
router.post('/spotify/transfer-playback', isAuthenticated, wiimController.transferPlaybackToWiim);

// @route   GET api/wiim/spotify/queue
// @desc    Get the current Spotify queue
// @access  Private
router.get('/spotify/queue', isAuthenticated, wiimController.getSpotifyQueue);

// @route   POST api/wiim/spotify/queue
// @desc    Add an item to the Spotify queue
// @access  Private
router.post('/spotify/queue', isAuthenticated, wiimController.addToSpotifyQueue);

// @route   POST api/wiim/spotify/next
// @desc    Skip to the next track in the Spotify queue
// @access  Private
router.post('/spotify/next', isAuthenticated, wiimController.skipToNextTrack);

// @route   POST api/wiim/spotify/previous
// @desc    Skip to the previous track in the Spotify queue
// @access  Private
router.post('/spotify/previous', isAuthenticated, wiimController.skipToPreviousTrack);

// @route   GET api/wiim/inputs
// @desc    Get list of available inputs
// @access  Private
router.get('/inputs', isAuthenticated, wiimController.getInputs);

// @route   POST api/wiim/inputs/:inputId/switch
// @desc    Switch to a specific input
// @access  Private
router.post('/inputs/:inputId/switch', isAuthenticated, wiimController.switchInput);

// @route   GET api/wiim/health
// @desc    Get WiiM health status
// @access  Private
router.get('/health', isAuthenticated, wiimController.getHealthStatus);

// @route   GET api/wiim/config
// @desc    Get WiiM configuration
// @access  Private
router.get('/config', isAuthenticated, wiimController.getConfig);

// @route   POST api/wiim/config
// @desc    Save WiiM configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, wiimController.saveConfig);

// @route   POST api/wiim/one-click-setup
// @desc    Set up WiiM with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, wiimController.oneClickSetup);

// @route   POST api/wiim/playback/seek
// @desc    Seek to a specific position in the current track
// @access  Private
router.post('/playback/seek', isAuthenticated, wiimController.seek);

// @route   GET api/wiim/track
// @desc    Get detailed information about the current track
// @access  Private
router.get('/track', isAuthenticated, wiimController.getTrackInfo);

// @route   GET api/wiim/equalizer
// @desc    Get equalizer settings
// @access  Private
router.get('/equalizer', isAuthenticated, wiimController.getEqualizer);

// @route   POST api/wiim/equalizer
// @desc    Set equalizer settings
// @access  Private
router.post('/equalizer', isAuthenticated, wiimController.setEqualizer);

module.exports = router;