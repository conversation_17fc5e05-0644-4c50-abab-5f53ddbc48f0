const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const goveeController = require('../../server/controllers/goveeController');

// @route   GET api/govee/initialize
// @desc    Initialize the Govee API
// @access  Private
router.get('/initialize', isAuthenticated, goveeController.initialize);

// @route   GET api/govee/devices
// @desc    Get all Govee devices
// @access  Private
router.get('/devices', isAuthenticated, goveeController.getDevices);

// @route   GET api/govee/device/state
// @desc    Get Govee device state
// @access  Private
router.get('/device/state', isAuthenticated, goveeController.getDeviceState);

// @route   POST api/govee/device/control
// @desc    Control Govee device
// @access  Private
router.post('/device/control', isAuthenticated, goveeController.controlDevice);

// @route   POST api/govee/device/turn
// @desc    Turn Govee device on or off
// @access  Private
router.post('/device/turn', isAuthenticated, goveeController.turnDevice);

// @route   POST api/govee/device/brightness
// @desc    Set Govee device brightness
// @access  Private
router.post('/device/brightness', isAuthenticated, goveeController.setBrightness);

// @route   POST api/govee/device/color
// @desc    Set Govee device color
// @access  Private
router.post('/device/color', isAuthenticated, goveeController.setColor);

// @route   POST api/govee/device/color-temperature
// @desc    Set Govee device color temperature
// @access  Private
router.post('/device/color-temperature', isAuthenticated, goveeController.setColorTemperature);

// @route   GET api/govee/config
// @desc    Get Govee configuration status
// @access  Private
router.get('/config', isAuthenticated, goveeController.getConfig);

module.exports = router;