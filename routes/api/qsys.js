const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const qsysController = require('../../server/controllers/qsysController');

// @route   GET api/qsys/status
// @desc    Get Q-sys Core Manager status
// @access  Private
router.get('/status', isAuthenticated, qsysController.getStatus);

// @route   GET api/qsys/health
// @desc    Get Q-sys Core Manager health status
// @access  Private
router.get('/health', isAuthenticated, qsysController.getHealthStatus);

// @route   GET api/qsys/components
// @desc    Get Q-sys Core Manager components
// @access  Private
router.get('/components', isAuthenticated, qsysController.getComponents);

// @route   GET api/qsys/components/:componentName/controls
// @desc    Get controls for a component
// @access  Private
router.get('/components/:componentName/controls', isAuthenticated, qsysController.getControls);

// @route   GET api/qsys/components/:componentName/controls/:controlName
// @desc    Get the value of a control
// @access  Private
router.get('/components/:componentName/controls/:controlName', isAuthenticated, qsysController.getControlValue);

// @route   POST api/qsys/components/:componentName/controls/:controlName
// @desc    Set the value of a control
// @access  Private
router.post('/components/:componentName/controls/:controlName', isAuthenticated, qsysController.setControlValue);

// @route   GET api/qsys/config
// @desc    Get Q-sys configuration
// @access  Private
router.get('/config', isAuthenticated, qsysController.getConfig);

// @route   POST api/qsys/config
// @desc    Save Q-sys configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, qsysController.saveConfig);

// @route   POST api/qsys/one-click-setup
// @desc    Set up Q-sys with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, qsysController.oneClickSetup);

module.exports = router;