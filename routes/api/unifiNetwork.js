const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const unifiNetworkController = require('../../server/controllers/unifiNetworkController');

// ===== Device Endpoints =====

// @route   GET api/unifi-network/devices
// @desc    Get all UniFi Network devices
// @access  Private
router.get('/devices', isAuthenticated, unifiNetworkController.getDevices);

// @route   GET api/unifi-network/devices/:id
// @desc    Get UniFi Network device details
// @access  Private
router.get('/devices/:id', isAuthenticated, unifiNetworkController.getDeviceDetails);

// @route   GET api/unifi-network/devices/:id/statistics
// @desc    Get UniFi Network device statistics
// @access  Private
router.get('/devices/:id/statistics', isAuthenticated, unifiNetworkController.getDeviceStatistics);

// @route   POST api/unifi-network/devices/:id/reboot
// @desc    Reboot a UniFi Network device
// @access  Private
router.post('/devices/:id/reboot', isAuthenticated, unifiNetworkController.rebootDevice);

// @route   POST api/unifi-network/devices/:id/adopt
// @desc    Adopt a UniFi Network device
// @access  Private
router.post('/devices/:id/adopt', isAuthenticated, unifiNetworkController.adoptDevice);

// @route   PUT api/unifi-network/devices/:id/name
// @desc    Set name for a UniFi Network device
// @access  Private
router.put('/devices/:id/name', isAuthenticated, unifiNetworkController.setDeviceName);

// @route   POST api/unifi-network/devices/:id/upgrade
// @desc    Upgrade firmware for a UniFi Network device
// @access  Private
router.post('/devices/:id/upgrade', isAuthenticated, unifiNetworkController.upgradeDeviceFirmware);

// ===== Client Endpoints =====

// @route   GET api/unifi-network/clients
// @desc    Get all UniFi Network clients
// @access  Private
router.get('/clients', isAuthenticated, unifiNetworkController.getClients);

// @route   GET api/unifi-network/clients/:id
// @desc    Get UniFi Network client details
// @access  Private
router.get('/clients/:id', isAuthenticated, unifiNetworkController.getClientDetails);

// @route   GET api/unifi-network/clients/:id/statistics
// @desc    Get UniFi Network client statistics
// @access  Private
router.get('/clients/:id/statistics', isAuthenticated, unifiNetworkController.getClientStatistics);

// @route   POST api/unifi-network/clients/:id/block
// @desc    Block a UniFi Network client
// @access  Private
router.post('/clients/:id/block', isAuthenticated, unifiNetworkController.blockClient);

// @route   POST api/unifi-network/clients/:id/unblock
// @desc    Unblock a UniFi Network client
// @access  Private
router.post('/clients/:id/unblock', isAuthenticated, unifiNetworkController.unblockClient);

// @route   POST api/unifi-network/clients/:id/reconnect
// @desc    Reconnect (kick) a UniFi Network client
// @access  Private
router.post('/clients/:id/reconnect', isAuthenticated, unifiNetworkController.reconnectClient);

// @route   POST api/unifi-network/clients/:id/authorize
// @desc    Authorize a guest UniFi Network client
// @access  Private
router.post('/clients/:id/authorize', isAuthenticated, unifiNetworkController.authorizeGuest);

// @route   POST api/unifi-network/clients/:id/unauthorize
// @desc    Unauthorize a guest UniFi Network client
// @access  Private
router.post('/clients/:id/unauthorize', isAuthenticated, unifiNetworkController.unauthorizeGuest);

// ===== Network Endpoints =====

// @route   GET api/unifi-network/networks
// @desc    Get all UniFi Network networks
// @access  Private
router.get('/networks', isAuthenticated, unifiNetworkController.getNetworks);

// @route   POST api/unifi-network/networks
// @desc    Create a new UniFi Network network
// @access  Private
router.post('/networks', isAuthenticated, unifiNetworkController.createNetwork);

// @route   PUT api/unifi-network/networks/:id
// @desc    Update an existing UniFi Network network
// @access  Private
router.put('/networks/:id', isAuthenticated, unifiNetworkController.updateNetwork);

// @route   DELETE api/unifi-network/networks/:id
// @desc    Delete a UniFi Network network
// @access  Private
router.delete('/networks/:id', isAuthenticated, unifiNetworkController.deleteNetwork);

// ===== Wireless Endpoints =====

// @route   GET api/unifi-network/wireless
// @desc    Get all UniFi Network wireless networks (WLANs)
// @access  Private
router.get('/wireless', isAuthenticated, unifiNetworkController.getWirelessNetworks);

// @route   POST api/unifi-network/wireless
// @desc    Create a new UniFi Network wireless network (WLAN)
// @access  Private
router.post('/wireless', isAuthenticated, unifiNetworkController.createWirelessNetwork);

// @route   PUT api/unifi-network/wireless/:id
// @desc    Update an existing UniFi Network wireless network (WLAN)
// @access  Private
router.put('/wireless/:id', isAuthenticated, unifiNetworkController.updateWirelessNetwork);

// @route   DELETE api/unifi-network/wireless/:id
// @desc    Delete a UniFi Network wireless network (WLAN)
// @access  Private
router.delete('/wireless/:id', isAuthenticated, unifiNetworkController.deleteWirelessNetwork);

// ===== System Endpoints =====

// @route   GET api/unifi-network/system/status
// @desc    Get UniFi Network system status
// @access  Private
router.get('/system/status', isAuthenticated, unifiNetworkController.getSystemStatus);

// @route   GET api/unifi-network/system/settings
// @desc    Get UniFi Network system settings
// @access  Private
router.get('/system/settings', isAuthenticated, unifiNetworkController.getSystemSettings);

// @route   PUT api/unifi-network/system/settings
// @desc    Update UniFi Network system settings
// @access  Private
router.put('/system/settings', isAuthenticated, unifiNetworkController.updateSystemSettings);

// @route   GET api/unifi-network/system/logs
// @desc    Get UniFi Network system logs
// @access  Private
router.get('/system/logs', isAuthenticated, unifiNetworkController.getSystemLogs);

// @route   GET api/unifi-network/system/alerts
// @desc    Get UniFi Network system alerts
// @access  Private
router.get('/system/alerts', isAuthenticated, unifiNetworkController.getSystemAlerts);

// ===== Statistics Endpoints =====

// @route   GET api/unifi-network/statistics/site
// @desc    Get UniFi Network site statistics
// @access  Private
router.get('/statistics/site', isAuthenticated, unifiNetworkController.getSiteStatistics);

// @route   GET api/unifi-network/statistics/traffic
// @desc    Get UniFi Network traffic statistics
// @access  Private
router.get('/statistics/traffic', isAuthenticated, unifiNetworkController.getTrafficStatistics);

// ===== Configuration Endpoints =====

// @route   GET api/unifi-network/config
// @desc    Get UniFi Network configuration status
// @access  Private
router.get('/config', isAuthenticated, unifiNetworkController.getConfig);


module.exports = router;
