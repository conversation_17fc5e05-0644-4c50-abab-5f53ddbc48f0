const express = require('express');
const router = express.Router();
const glpiController = require('../../server/controllers/glpiController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');

/**
 * @route   POST /api/glpi/config
 * @desc    Save GLPI configuration
 */
router.post('/config', isAuthenticated, hasRoles(['admin']), glpiController.saveConfig);

/**
 * @route   GET /api/glpi/config
 * @desc    Get GLPI configuration
 */
router.get('/config', isAuthenticated, hasRoles(['admin']), glpiController.getConfig);

/**
 * @route   GET /api/glpi/test-connection
 * @desc    Test GLPI connection
 */
router.get('/test-connection', isAuthenticated, hasRoles(['admin']), glpiController.testConnection);

/**
 * @route   GET /api/glpi/assets
 * @desc    Get all assets
 */
router.get('/assets', isAuthenticated, glpiController.getAssets);

/**
 * @route   GET /api/glpi/assets/:id
 * @desc    Get asset by ID
 */
router.get('/assets/:id', isAuthenticated, glpiController.getAsset);

/**
 * @route   GET /api/glpi/search
 * @desc    Search assets
 */
router.get('/search', isAuthenticated, glpiController.searchAssets);

/**
 * @route   GET /api/glpi/asset-types
 * @desc    Get all asset types
 */
router.get('/asset-types', isAuthenticated, glpiController.getAssetTypes);

/**
 * @route   GET /api/glpi/asset-models
 * @desc    Get all asset models
 */
router.get('/asset-models', isAuthenticated, glpiController.getAssetModels);

/**
 * @route   GET /api/glpi/asset-manufacturers
 * @desc    Get all asset manufacturers
 */
router.get('/asset-manufacturers', isAuthenticated, glpiController.getAssetManufacturers);

/**
 * @route   GET /api/glpi/asset-categories
 * @desc    Get all asset categories
 */
router.get('/asset-categories', isAuthenticated, glpiController.getAssetCategories);

/**
 * @route   POST /api/glpi/assets
 * @desc    Create a new asset
 */
router.post('/assets', isAuthenticated, hasRoles(['admin']), glpiController.createAsset);

/**
 * @route   PUT /api/glpi/assets/:id
 * @desc    Update an asset
 */
router.put('/assets/:id', isAuthenticated, hasRoles(['admin']), glpiController.updateAsset);

/**
 * @route   DELETE /api/glpi/assets/:id
 * @desc    Delete an asset
 */
router.delete('/assets/:id', isAuthenticated, hasRoles(['admin']), glpiController.deleteAsset);

module.exports = router;