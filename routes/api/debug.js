const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const { debugStore } = require('../../server/middleware/debugLogging');

router.get('/requests', isAuthenticated, isAdmin, (req, res) => {
  try {
    const { limit = 100, offset = 0, type } = req.query;
    
    let requests = debugStore.getRequests();
    
    if (type) {
      requests = requests.filter(request => request.type === type);
    }
    
    const totalCount = requests.length;
    const paginatedRequests = requests.slice(offset, offset + parseInt(limit));
    
    res.json({
      requests: paginatedRequests,
      totalCount,
      offset: parseInt(offset),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('Error getting debug requests:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

router.delete('/requests', isAuthenticated, isAdmin, (req, res) => {
  try {
    debugStore.clear();
    res.json({ msg: 'Debug request history cleared' });
  } catch (error) {
    console.error('Error clearing debug requests:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

router.get('/stats', isAuthenticated, isAdmin, (req, res) => {
  try {
    const websocketServer = require('../../server/websocket/websocketServer');
    const wsStats = websocketServer.getStats();
    
    res.json({
      debugRequests: {
        total: debugStore.getRequests().length,
        apiRequests: debugStore.getRequests().filter(r => r.type !== 'external_api').length,
        externalApiRequests: debugStore.getRequests().filter(r => r.type === 'external_api').length
      },
      websocket: wsStats
    });
  } catch (error) {
    console.error('Error getting debug stats:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

module.exports = router;