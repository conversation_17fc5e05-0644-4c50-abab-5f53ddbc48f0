const express = require('express');
const router = express.Router();
const googleFormsController = require('../../server/controllers/googleFormsController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');

/**
 * @route   POST /api/google-forms/config
 * @desc    Save Google Forms configuration
 */
router.post('/config', isAuthenticated, hasR<PERSON>s(['admin']), googleFormsController.saveConfig);

/**
 * @route   GET /api/google-forms/config
 * @desc    Get Google Forms configuration
 */
router.get('/config', isAuthenticated, hasRoles(['admin']), googleFormsController.getConfig);

/**
 * @route   GET /api/google-forms/auth-url
 * @desc    Get Google Forms authentication URL
 */
router.get('/auth-url', isAuthenticated, hasRoles(['admin']), googleFormsController.getAuthUrl);

/**
 * @route   GET /api/google-forms/callback
 * @desc    Handle Google Forms OAuth2 callback
 */
router.get('/callback', googleFormsController.handleCallback);

/**
 * @route   GET /api/google-forms/forms
 * @desc    List forms in Google Forms
 */
router.get('/forms', isAuthenticated, googleFormsController.listForms);

/**
 * @route   GET /api/google-forms/forms/:formId
 * @desc    Get form details
 */
router.get('/forms/:formId', isAuthenticated, googleFormsController.getForm);

/**
 * @route   GET /api/google-forms/forms/:formId/responses
 * @desc    Get form responses
 */
router.get('/forms/:formId/responses', isAuthenticated, googleFormsController.getFormResponses);

/**
 * @route   POST /api/google-forms/forms
 * @desc    Create a new form
 */
router.post('/forms', isAuthenticated, googleFormsController.createForm);

/**
 * @route   PUT /api/google-forms/forms/:formId
 * @desc    Update an existing form
 */
router.put('/forms/:formId', isAuthenticated, googleFormsController.updateForm);

module.exports = router;