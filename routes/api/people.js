const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const Person = require('../../models/Person');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/people
 * @desc    Get all people
 * @access  private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const people = await Person.find();
    res.json(people);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/people/search
 * @desc    Search people by name, email, or category
 * @access  private
 */
router.get('/search', isAuthenticated, async (req, res) => {
  try {
    const { query, category } = req.query;
    let searchQuery = {};

    if (query) {
      searchQuery = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { email: { $regex: query, $options: 'i' } },
          { notes: { $regex: query, $options: 'i' } }
        ]
      };
    }

    if (category) {
      searchQuery.categories = category;
    }

    const people = await Person.find(searchQuery);
    res.json(people);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/people/:id
 * @desc    Get person by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const person = await Person.findById(req.params.id);

    if (!person) {
      return res.status(404).json({ msg: 'Person not found' });
    }

    res.json(person);
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Person not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/people
 * @desc    Create a new person
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    check('name', 'Name is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const newPerson = new Person({
        ...req.body,
        createdBy: req.user.id
      });

      const person = await newPerson.save();
      res.json(person);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/people/:id
 * @desc    Update a person
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    check('name', 'Name is required').optional().not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      let person = await Person.findById(req.params.id);

      if (!person) {
        return res.status(404).json({ msg: 'Person not found' });
      }

      // Update fields
      const updateFields = { ...req.body };
      delete updateFields.createdBy; // Prevent changing the creator
      delete updateFields.createdAt; // Prevent changing creation date

      person = await Person.findByIdAndUpdate(
        req.params.id,
        { $set: updateFields },
        { new: true }
      );

      res.json(person);
    } catch (err) {
      console.error(err.message);

      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Person not found' });
      }

      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/people/:id
 * @desc    Delete a person
 * @access  private
 */
router.delete('/:id', isAuthenticated, async (req, res) => {
  try {
    const person = await Person.findById(req.params.id);

    if (!person) {
      return res.status(404).json({ msg: 'Person not found' });
    }

    await Person.findByIdAndRemove(req.params.id);

    res.json({ msg: 'Person removed' });
  } catch (err) {
    console.error(err.message);

    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Person not found' });
    }

    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/people/categories
 * @desc    Get all unique categories
 * @access  private
 */
router.get('/categories/all', isAuthenticated, async (req, res) => {
  try {
    const categories = await Person.distinct('categories');
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;