const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const AssetRequest = require('../../models/AssetRequest');
const AssetIssue = require('../../models/AssetIssue');

/**
 * @route   GET /api/asset-reports/summary
 * @desc    Get summary statistics for assets
 * @access  private
 */
router.get('/summary', isAuthenticated, hasRoles(['admin', 'asset_manager']), async (req, res) => {
  try {
    // Get request statistics
    const totalRequests = await AssetRequest.countDocuments();
    const pendingRequests = await AssetRequest.countDocuments({ status: 'pending' });
    const approvedRequests = await AssetRequest.countDocuments({ status: 'approved' });
    const deniedRequests = await AssetRequest.countDocuments({ status: 'denied' });
    const completedRequests = await AssetRequest.countDocuments({ status: 'completed' });
    const cancelledRequests = await AssetRequest.countDocuments({ status: 'cancelled' });
    
    // Get issue statistics
    const totalIssues = await AssetIssue.countDocuments();
    const openIssues = await AssetIssue.countDocuments({ status: 'open' });
    const inProgressIssues = await AssetIssue.countDocuments({ status: 'in_progress' });
    const resolvedIssues = await AssetIssue.countDocuments({ status: 'resolved' });
    const closedIssues = await AssetIssue.countDocuments({ status: 'closed' });
    const reopenedIssues = await AssetIssue.countDocuments({ status: 'reopened' });
    
    // Get issue statistics by type
    const hardwareIssues = await AssetIssue.countDocuments({ issueType: 'hardware' });
    const softwareIssues = await AssetIssue.countDocuments({ issueType: 'software' });
    const connectivityIssues = await AssetIssue.countDocuments({ issueType: 'connectivity' });
    const otherIssues = await AssetIssue.countDocuments({ issueType: 'other' });
    
    // Get issue statistics by priority
    const lowPriorityIssues = await AssetIssue.countDocuments({ priority: 'low' });
    const mediumPriorityIssues = await AssetIssue.countDocuments({ priority: 'medium' });
    const highPriorityIssues = await AssetIssue.countDocuments({ priority: 'high' });
    const criticalPriorityIssues = await AssetIssue.countDocuments({ priority: 'critical' });
    
    res.json({
      requests: {
        total: totalRequests,
        pending: pendingRequests,
        approved: approvedRequests,
        denied: deniedRequests,
        completed: completedRequests,
        cancelled: cancelledRequests
      },
      issues: {
        total: totalIssues,
        byStatus: {
          open: openIssues,
          inProgress: inProgressIssues,
          resolved: resolvedIssues,
          closed: closedIssues,
          reopened: reopenedIssues
        },
        byType: {
          hardware: hardwareIssues,
          software: softwareIssues,
          connectivity: connectivityIssues,
          other: otherIssues
        },
        byPriority: {
          low: lowPriorityIssues,
          medium: mediumPriorityIssues,
          high: highPriorityIssues,
          critical: criticalPriorityIssues
        }
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/asset-reports/requests/monthly
 * @desc    Get monthly statistics for asset requests
 * @access  private
 */
router.get('/requests/monthly', isAuthenticated, hasRoles(['admin', 'asset_manager']), async (req, res) => {
  try {
    // Get the current date
    const now = new Date();
    
    // Create an array to hold the last 12 months
    const months = [];
    for (let i = 0; i < 12; i++) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      // Get request counts for this month
      const totalRequests = await AssetRequest.countDocuments({
        createdAt: { $gte: month, $lt: nextMonth }
      });
      
      const approvedRequests = await AssetRequest.countDocuments({
        status: 'approved',
        approvalDate: { $gte: month, $lt: nextMonth }
      });
      
      const completedRequests = await AssetRequest.countDocuments({
        status: 'completed',
        returnDate: { $gte: month, $lt: nextMonth }
      });
      
      months.push({
        month: month.toLocaleString('default', { month: 'long' }),
        year: month.getFullYear(),
        total: totalRequests,
        approved: approvedRequests,
        completed: completedRequests
      });
    }
    
    res.json(months.reverse());
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/asset-reports/issues/monthly
 * @desc    Get monthly statistics for asset issues
 * @access  private
 */
router.get('/issues/monthly', isAuthenticated, hasRoles(['admin', 'asset_manager']), async (req, res) => {
  try {
    // Get the current date
    const now = new Date();
    
    // Create an array to hold the last 12 months
    const months = [];
    for (let i = 0; i < 12; i++) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      // Get issue counts for this month
      const totalIssues = await AssetIssue.countDocuments({
        createdAt: { $gte: month, $lt: nextMonth }
      });
      
      const resolvedIssues = await AssetIssue.countDocuments({
        status: 'resolved',
        resolutionDate: { $gte: month, $lt: nextMonth }
      });
      
      months.push({
        month: month.toLocaleString('default', { month: 'long' }),
        year: month.getFullYear(),
        total: totalIssues,
        resolved: resolvedIssues
      });
    }
    
    res.json(months.reverse());
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/asset-reports/top-requested-assets
 * @desc    Get the top requested assets
 * @access  private
 */
router.get('/top-requested-assets', isAuthenticated, hasRoles(['admin', 'asset_manager']), async (req, res) => {
  try {
    const topAssets = await AssetRequest.aggregate([
      {
        $group: {
          _id: { assetId: '$assetId', assetName: '$assetName', assetType: '$assetType' },
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    
    res.json(topAssets.map(asset => ({
      assetId: asset._id.assetId,
      assetName: asset._id.assetName,
      assetType: asset._id.assetType,
      requestCount: asset.count
    })));
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/asset-reports/top-issue-assets
 * @desc    Get the assets with the most issues
 * @access  private
 */
router.get('/top-issue-assets', isAuthenticated, hasRoles(['admin', 'asset_manager']), async (req, res) => {
  try {
    const topIssueAssets = await AssetIssue.aggregate([
      {
        $group: {
          _id: { assetId: '$assetId', assetName: '$assetName', assetType: '$assetType' },
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    
    res.json(topIssueAssets.map(asset => ({
      assetId: asset._id.assetId,
      assetName: asset._id.assetName,
      assetType: asset._id.assetType,
      issueCount: asset.count
    })));
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;