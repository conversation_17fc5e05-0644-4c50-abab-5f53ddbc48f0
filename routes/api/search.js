const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const User = require('../../models/User');
const HelpEntry = require('../../models/HelpEntry');
const Shortcut = require('../../models/Shortcut');

/**
 * @route   GET /api/search
 * @desc    Search across all content types
 * @access  Private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q || q.trim().length < 3) {
      return res.status(400).json({ msg: 'Search query must be at least 3 characters' });
    }

    const searchQuery = q.trim();
    const results = [];

    // Search users
    const users = await User.find({
      $or: [
        { name: { $regex: searchQuery, $options: 'i' } },
        { email: { $regex: searchQuery, $options: 'i' } }
      ]
    }).select('name email avatar');

    users.forEach(user => {
      results.push({
        id: user._id,
        title: user.name,
        description: user.email,
        type: 'person',
        url: `/staff-directory/users/${user._id}`,
        icon: null
      });
    });

    // Search help entries
    const helpEntries = await HelpEntry.find({
      $or: [
        { title: { $regex: searchQuery, $options: 'i' } },
        { content: { $regex: searchQuery, $options: 'i' } }
      ]
    }).select('title content category');

    helpEntries.forEach(entry => {
      results.push({
        id: entry._id,
        title: entry.title,
        description: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
        type: 'help',
        url: `/help/entries/${entry._id}`,
        icon: null
      });
    });

    // Search shortcuts
    const shortcuts = await Shortcut.find({
      $or: [
        { name: { $regex: searchQuery, $options: 'i' } },
        { description: { $regex: searchQuery, $options: 'i' } }
      ]
    }).select('name description url');

    shortcuts.forEach(shortcut => {
      results.push({
        id: shortcut._id,
        title: shortcut.name,
        description: shortcut.description,
        type: 'link',
        url: `/shortcuts?id=${shortcut._id}`,
        icon: null
      });
    });

    // Sort results by relevance (simple implementation - title matches first)
    results.sort((a, b) => {
      const aTitle = a.title.toLowerCase();
      const bTitle = b.title.toLowerCase();
      const query = searchQuery.toLowerCase();
      
      // If title starts with query, prioritize
      if (aTitle.startsWith(query) && !bTitle.startsWith(query)) return -1;
      if (!aTitle.startsWith(query) && bTitle.startsWith(query)) return 1;
      
      // If title contains query, prioritize
      if (aTitle.includes(query) && !bTitle.includes(query)) return -1;
      if (!aTitle.includes(query) && bTitle.includes(query)) return 1;
      
      // Alphabetical order as fallback
      return aTitle.localeCompare(bTitle);
    });

    res.json(results);
  } catch (err) {
    console.error('Error searching:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/search/suggestions
 * @desc    Get search suggestions based on partial query
 * @access  Private
 */
router.get('/suggestions', isAuthenticated, async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q || q.trim().length < 3) {
      return res.status(400).json({ msg: 'Search query must be at least 3 characters' });
    }

    const searchQuery = q.trim();
    const results = [];
    const limit = 5; // Limit results per category

    // Search users (limit to top 5)
    const users = await User.find({
      $or: [
        { name: { $regex: searchQuery, $options: 'i' } },
        { email: { $regex: searchQuery, $options: 'i' } }
      ]
    })
    .select('name email avatar')
    .limit(limit);

    users.forEach(user => {
      results.push({
        id: user._id,
        title: user.name,
        description: user.email,
        type: 'person',
        url: `/staff-directory/users/${user._id}`,
        icon: null
      });
    });

    // Search help entries (limit to top 5)
    const helpEntries = await HelpEntry.find({
      $or: [
        { title: { $regex: searchQuery, $options: 'i' } },
        { content: { $regex: searchQuery, $options: 'i' } }
      ]
    })
    .select('title content category')
    .limit(limit);

    helpEntries.forEach(entry => {
      results.push({
        id: entry._id,
        title: entry.title,
        description: entry.content.substring(0, 100) + (entry.content.length > 100 ? '...' : ''),
        type: 'help',
        url: `/help/entries/${entry._id}`,
        icon: null
      });
    });

    // Search shortcuts (limit to top 5)
    const shortcuts = await Shortcut.find({
      $or: [
        { name: { $regex: searchQuery, $options: 'i' } },
        { description: { $regex: searchQuery, $options: 'i' } }
      ]
    })
    .select('name description url')
    .limit(limit);

    shortcuts.forEach(shortcut => {
      results.push({
        id: shortcut._id,
        title: shortcut.name,
        description: shortcut.description,
        type: 'link',
        url: `/shortcuts?id=${shortcut._id}`,
        icon: null
      });
    });

    // Sort results by relevance
    results.sort((a, b) => {
      const aTitle = a.title.toLowerCase();
      const bTitle = b.title.toLowerCase();
      const query = searchQuery.toLowerCase();
      
      // If title starts with query, prioritize
      if (aTitle.startsWith(query) && !bTitle.startsWith(query)) return -1;
      if (!aTitle.startsWith(query) && bTitle.startsWith(query)) return 1;
      
      // Alphabetical order as fallback
      return aTitle.localeCompare(bTitle);
    });

    // Limit total results
    const limitedResults = results.slice(0, 10);
    
    res.json(limitedResults);
  } catch (err) {
    console.error('Error getting search suggestions:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

module.exports = router;