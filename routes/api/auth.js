const express = require('express');
const passport = require('passport');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const axios = require('axios');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const qrcode = require('qrcode');
const crypto = require('crypto');
const User = require('../../models/User');
const { check, validationResult } = require('express-validator');
const emailUtils = require('../../server/utils/emailTemplates/emailUtils');

/**
 * @route   GET /api/auth/google
 * @desc    Authenticate with Google
 * @access  Public
 */
router.get('/google', 
  passport.authenticate('google', { 
    scope: [
      'profile', 
      'email'
    ]
  })
);

/**
 * @route   GET /api/auth/google/callback
 * @desc    Google auth callback
 * @access  Public
 */
router.get(
  '/google/callback',
  passport.authenticate('google', { 
    failureRedirect: '/login',
    session: true
  }),
  (req, res) => {
    // Ensure the user is logged in by explicitly establishing the session
    req.login(req.user, (err) => {
      if (err) {
        console.error('Error during login:', err);
        return res.redirect('/login');
      }

      // Explicitly save the session to ensure cookie is set
      req.session.save((err) => {
        if (err) {
          console.error('Error saving session:', err);
          return res.redirect('/login');
        }
        // Successful authentication, redirect to dashboard
        res.redirect('/dashboard');
      });
    });
  }
);

/**
 * @route   GET /api/auth/current
 * @desc    Get current user
 * @access  Private
 */
router.get('/current', isAuthenticated, (req, res) => {
  // Return both id and _id for compatibility with components expecting Mongoose-style _id
  res.json({
    _id: req.user.id,
    id: req.user.id,
    name: req.user.name,
    email: req.user.email,
    avatar: req.user.avatar,
    roles: req.user.roles
  });
});

/**
 * @route   GET /api/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.get('/logout', (req, res) => {
  req.logout(function(err) {
    if (err) { return next(err); }
    res.redirect('/');
  });
});

/**
 * @route   POST /api/auth/login
 * @desc    Authenticate user with local credentials
 * @access  Public
 */
router.post(
  '/login',
  [
    check('email', 'Please include a valid email').isEmail(),
    check('password', 'Password is required').exists()
  ],
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    passport.authenticate('local', (err, user, info) => {
      if (err) {
        console.error('Error during local authentication:', err);
        return res.status(500).json({ msg: 'Server error during authentication' });
      }
      
      if (!user) {
        return res.status(400).json({ msg: info.message || 'Invalid credentials' });
      }
      
      // Check if 2FA verification is required
      if (info.requires2FA) {
        // Return a response indicating 2FA is required, but don't log the user in yet
        return res.json({ 
          requires2FA: true, 
          userId: user.id,
          msg: 'Two-factor authentication required' 
        });
      }
      
      // Check if 2FA setup is required
      if (info.requiresSetup2FA) {
        // Return a response indicating 2FA setup is required
        return res.json({ 
          requiresSetup2FA: true, 
          userId: user.id,
          msg: 'Two-factor authentication setup required' 
        });
      }
      
      // Log in the user
      req.login(user, (err) => {
        if (err) {
          console.error('Error during login:', err);
          return res.status(500).json({ msg: 'Server error during login' });
        }
        
        // Update last login time
        User.findByIdAndUpdate(user.id, { lastLogin: Date.now() }).exec();
        
        // Return user info
        return res.json({
          id: user.id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          roles: user.roles
        });
      });
    })(req, res, next);
  }
);

/**
 * @route   POST /api/auth/verify-2fa
 * @desc    Verify 2FA token and complete login
 * @access  Public
 */
router.post(
  '/verify-2fa',
  [
    check('userId', 'User ID is required').not().isEmpty(),
    check('token', 'Token is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { userId, token } = req.body;
      
      // Find the user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }
      
      // Verify the token
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: token
      });
      
      if (!verified) {
        return res.status(400).json({ msg: 'Invalid token' });
      }
      
      // Log in the user
      req.login(user, (err) => {
        if (err) {
          console.error('Error during login after 2FA:', err);
          return res.status(500).json({ msg: 'Server error during login' });
        }
        
        // Update last login time
        User.findByIdAndUpdate(user.id, { lastLogin: Date.now() }).exec();
        
        // Return user info
        return res.json({
          id: user.id,
          name: user.name,
          email: user.email,
          avatar: user.avatar,
          roles: user.roles
        });
      });
    } catch (err) {
      console.error('Error during 2FA verification:', err);
      res.status(500).json({ msg: 'Server error during 2FA verification' });
    }
  }
);

/**
 * @route   POST /api/auth/setup-2fa
 * @desc    Generate 2FA secret and QR code
 * @access  Private
 */
router.post('/setup-2fa', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }
    
    // Generate a new secret
    const secret = speakeasy.generateSecret({
      name: `CSF Portal (${user.email})`
    });
    
    // Save the secret to the user
    user.twoFactorSecret = secret.base32;
    await user.save();
    
    // Generate QR code
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url);
    
    res.json({
      secret: secret.base32,
      qrCode: qrCodeUrl
    });
  } catch (err) {
    console.error('Error setting up 2FA:', err);
    res.status(500).json({ msg: 'Server error during 2FA setup' });
  }
});

/**
 * @route   POST /api/auth/verify-setup-2fa
 * @desc    Verify and enable 2FA
 * @access  Private
 */
router.post(
  '/verify-setup-2fa',
  [
    isAuthenticated,
    check('token', 'Token is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { token } = req.body;
      const user = await User.findById(req.user.id);
      
      if (!user) {
        return res.status(404).json({ msg: 'User not found' });
      }
      
      // Verify the token
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: token
      });
      
      if (!verified) {
        return res.status(400).json({ msg: 'Invalid token' });
      }
      
      // Enable 2FA
      user.twoFactorEnabled = true;
      user.twoFactorSetupComplete = true;
      
      // Clear any password reset token if it exists
      // This ensures the login link expires after 2FA setup is complete
      if (user.passwordResetToken) {
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
      }
      
      await user.save();
      
      res.json({ msg: 'Two-factor authentication enabled successfully' });
    } catch (err) {
      console.error('Error verifying 2FA setup:', err);
      res.status(500).json({ msg: 'Server error during 2FA verification' });
    }
  }
);

/**
 * @route   POST /api/auth/forgot-password
 * @desc    Request password reset
 * @access  Public
 */
router.post(
  '/forgot-password',
  [
    check('email', 'Please include a valid email').isEmail()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { email } = req.body;
      
      // Find the user
      const user = await User.findOne({ email, authType: 'local' });
      if (!user) {
        // Don't reveal that the user doesn't exist
        return res.json({ msg: 'If an account with that email exists, a password reset link has been sent.' });
      }
      
      // Generate a reset token
      const token = crypto.randomBytes(20).toString('hex');
      
      // Set token and expiry
      user.passwordResetToken = token;
      user.passwordResetExpires = Date.now() + 3600000; // 1 hour
      await user.save();
      
      // Send password reset email using the email templating system
      try {
        await emailUtils.sendTemplatedEmail({
          to: email,
          subject: 'CSF Portal - Password Reset Request',
          templateName: 'passwordReset',
          data: {
            resetLink: `${process.env.FRONTEND_URL}/reset-password/${token}`
          }
        });
        console.log(`Password reset email sent to ${email}`);
      } catch (emailErr) {
        console.error('Error sending password reset email:', emailErr);
        // Continue even if email fails, just log the error
      }
      
      res.json({ msg: 'If an account with that email exists, a password reset link has been sent.' });
    } catch (err) {
      console.error('Error requesting password reset:', err);
      res.status(500).json({ msg: 'Server error during password reset request' });
    }
  }
);

/**
 * @route   POST /api/auth/request-login-link
 * @desc    Request a new login link for users who haven't completed setup
 * @access  Public
 */
router.post(
  '/request-login-link',
  [
    check('email', 'Please include a valid email').isEmail()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { email } = req.body;
      
      // Find the user
      const user = await User.findOne({ 
        email, 
        authType: 'local',
        $or: [
          { password: { $exists: false } },
          { password: null },
          { twoFactorSetupComplete: false }
        ]
      });
      
      if (!user) {
        // Don't reveal that the user doesn't exist or has already completed setup
        return res.json({ 
          msg: 'If an eligible account with that email exists, a login link has been sent.' 
        });
      }
      
      // Generate a login token
      const loginToken = crypto.randomBytes(20).toString('hex');
      
      // Set token and expiry
      user.passwordResetToken = loginToken;
      user.passwordResetExpires = Date.now() + 24 * 3600000; // 24 hours
      await user.save();
      
      // Send login link email
      try {
        await emailUtils.sendTemplatedEmail({
          to: email,
          subject: 'CSF Portal - New Login Link',
          templateName: 'loginLink',
          data: {
            name: user.name,
            loginLink: `${process.env.FRONTEND_URL}/initial-login/${loginToken}`
          }
        });
        console.log(`Login link email sent to ${email}`);
      } catch (emailErr) {
        console.error('Error sending login link email:', emailErr);
        // Continue even if email fails, just log the error
      }
      
      res.json({ 
        msg: 'If an eligible account with that email exists, a login link has been sent.' 
      });
    } catch (err) {
      console.error('Error requesting login link:', err);
      res.status(500).json({ msg: 'Server error during login link request' });
    }
  }
);

/**
 * @route   POST /api/auth/reset-password/:token
 * @desc    Reset password with token
 * @access  Public
 */
router.post(
  '/reset-password/:token',
  [
    check('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 })
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { token } = req.params;
      const { password } = req.body;
      
      // Find the user with the token
      const user = await User.findOne({
        passwordResetToken: token,
        passwordResetExpires: { $gt: Date.now() }
      });
      
      if (!user) {
        return res.status(400).json({ msg: 'Password reset token is invalid or has expired' });
      }
      
      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(password, salt);
      
      // Clear reset token and expiry
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      
      await user.save();
      
      res.json({ msg: 'Password has been reset successfully' });
    } catch (err) {
      console.error('Error resetting password:', err);
      res.status(500).json({ msg: 'Server error during password reset' });
    }
  }
);

/**
 * @route   GET /api/auth/verify-login-token/:token
 * @desc    Verify login token validity
 * @access  Public
 */
router.get('/verify-login-token/:token', async (req, res) => {
  try {
    const { token } = req.params;
    
    // Find the user with the token
    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: Date.now() }
    });
    
    if (!user) {
      return res.status(400).json({ 
        valid: false,
        msg: 'Login link is invalid or has expired. Please contact your administrator for a new link.' 
      });
    }
    
    res.json({ 
      valid: true,
      email: user.email,
      name: user.name
    });
  } catch (err) {
    console.error('Error verifying login token:', err);
    res.status(500).json({ msg: 'Server error during token verification' });
  }
});

/**
 * @route   POST /api/auth/initial-login/:token
 * @desc    Set password and begin 2FA setup for new users
 * @access  Public
 */
router.post(
  '/initial-login/:token',
  [
    check('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 })
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { token } = req.params;
      const { password } = req.body;
      
      // Find the user with the token
      const user = await User.findOne({
        passwordResetToken: token,
        passwordResetExpires: { $gt: Date.now() }
      });
      
      if (!user) {
        return res.status(400).json({ 
          msg: 'Login link is invalid or has expired. Please contact your administrator for a new link.' 
        });
      }
      
      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(password, salt);
      
      // Don't clear the token yet - we'll do that after 2FA setup is complete
      
      await user.save();
      
      // Generate a 2FA secret for the user
      const secret = speakeasy.generateSecret({
        name: `CSF Portal (${user.email})`
      });
      
      // Save the secret to the user
      user.twoFactorSecret = secret.base32;
      await user.save();
      
      // Generate QR code
      const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url);
      
      // Return success with 2FA setup info
      res.json({
        msg: 'Password set successfully. Please complete 2FA setup.',
        userId: user.id,
        requiresSetup2FA: true,
        secret: secret.base32,
        qrCode: qrCodeUrl
      });
    } catch (err) {
      console.error('Error during initial login:', err);
      res.status(500).json({ msg: 'Server error during initial login' });
    }
  }
);

/**
 * @route   GET /api/auth/avatar-proxy
 * @desc    Proxy for user avatar images to avoid CORP issues
 * @access  Public
 */
router.get('/avatar-proxy', async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({ msg: 'URL parameter is required' });
    }

    // Validate that the URL is from a trusted domain (Google in this case)
    if (!url.startsWith('https://lh3.googleusercontent.com/')) {
      return res.status(400).json({ msg: 'Invalid URL domain' });
    }

    const response = await axios.get(url, {
      responseType: 'arraybuffer'
    });

    // Set the appropriate content type
    const contentType = response.headers['content-type'];
    res.setHeader('Content-Type', contentType);

    // Send the image data
    res.send(response.data);
  } catch (err) {
    console.error('Error proxying avatar:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
