const express = require('express');
const router = express.Router();
const { isAuthenticated, hasPermission } = require('../../middleware/auth');
const newsPostController = require('../../server/controllers/newsPostController');
const { check } = require('express-validator');

// GET /api/news-posts
// Get all news posts with pagination and filtering
// Access: private
router.get('/', isAuthenticated, newsPostController.getAllPosts);

// GET /api/news-posts/latest
// Get latest news posts for dashboard widget
// Access: private
router.get('/latest', isAuthenticated, newsPostController.getLatestPosts);

// GET /api/news-posts/:id
// Get post by ID
// Access: private
router.get('/:id', isAuthenticated, newsPostController.getPostById);

// POST /api/news-posts
// Create a new news post
// Access: private (requires news:write permission)
router.post(
  '/',
  [
    isAuthenticated,
    hasPermission('news:write'),
    check('title', 'Title is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty()
  ],
  newsPostController.createPost
);

// PUT /api/news-posts/:id
// Update a news post
// Access: private (requires news:write permission)
router.put(
  '/:id',
  [
    isAuthenticated,
    hasPermission('news:write'),
    check('title', 'Title is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty()
  ],
  newsPostController.updatePost
);

// DELETE /api/news-posts/:id
// Delete a news post
// Access: private (requires news:delete permission)
router.delete('/:id', isAuthenticated, hasPermission('news:delete'), newsPostController.deletePost);

// PUT /api/news-posts/:id/toggle-featured
// Toggle post featured status
// Access: private (requires news:write permission)
router.put(
  '/:id/toggle-featured',
  isAuthenticated,
  hasPermission('news:write'),
  newsPostController.toggleFeatured
);

// PUT /api/news-posts/:id/toggle-published
// Toggle post published status
// Access: private (requires news:write permission)
router.put(
  '/:id/toggle-published',
  isAuthenticated,
  hasPermission('news:write'),
  newsPostController.togglePublished
);

module.exports = router;