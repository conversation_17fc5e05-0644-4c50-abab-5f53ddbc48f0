const express = require('express');
const router = express.Router();
const { isAuthenticated, hasActivatedIntegration } = require('../../middleware/auth');
const zeeVeeController = require('../../server/controllers/zeeVeeController');

// @route   GET api/zeevee/device/info
// @desc    Get ZeeVee device information
// @access  Private (requires zeevee integration)
router.get('/device/info', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getDeviceInfo);

// @route   GET api/zeevee/system/status
// @desc    Get ZeeVee system status
// @access  Private (requires zeevee integration)
router.get('/system/status', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getSystemStatus);

// @route   GET api/zeevee/encoders
// @desc    Get all ZeeVee encoders
// @access  Private (requires zeevee integration)
router.get('/encoders', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getEncoders);

// @route   GET api/zeevee/encoders/:id
// @desc    Get ZeeVee encoder details
// @access  Private (requires zeevee integration)
router.get('/encoders/:id', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getEncoderDetails);

// @route   GET api/zeevee/decoders
// @desc    Get all ZeeVee decoders
// @access  Private (requires zeevee integration)
router.get('/decoders', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getDecoders);

// @route   GET api/zeevee/decoders/:id
// @desc    Get ZeeVee decoder details
// @access  Private (requires zeevee integration)
router.get('/decoders/:id', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getDecoderDetails);

// @route   GET api/zeevee/channels
// @desc    Get all ZeeVee video channels
// @access  Private (requires zeevee integration)
router.get('/channels', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getChannels);

// @route   GET api/zeevee/channels/:id
// @desc    Get ZeeVee channel details
// @access  Private (requires zeevee integration)
router.get('/channels/:id', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getChannelDetails);

// @route   PUT api/zeevee/encoders/:id/input
// @desc    Set ZeeVee encoder input
// @access  Private (requires zeevee integration)
router.put('/encoders/:id/input', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.setEncoderInput);

// @route   PUT api/zeevee/decoders/:id/output
// @desc    Set ZeeVee decoder output
// @access  Private (requires zeevee integration)
router.put('/decoders/:id/output', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.setDecoderOutput);

// @route   POST api/zeevee/system/reboot
// @desc    Reboot ZeeVee device
// @access  Private (requires zeevee integration)
router.post('/system/reboot', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.rebootDevice);

// @route   GET api/zeevee/network/settings
// @desc    Get ZeeVee network settings
// @access  Private (requires zeevee integration)
router.get('/network/settings', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getNetworkSettings);

// @route   GET api/zeevee/health
// @desc    Get ZeeVee health status
// @access  Private (requires zeevee integration)
router.get('/health', isAuthenticated, hasActivatedIntegration('zeeVee'), zeeVeeController.getHealthStatus);

// @route   GET api/zeevee/config
// @desc    Get ZeeVee configuration status
// @access  Private
router.get('/config', isAuthenticated, zeeVeeController.getConfig);

// @route   POST api/zeevee/config
// @desc    Save ZeeVee configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, zeeVeeController.saveConfig);

// @route   POST api/zeevee/one-click-setup
// @desc    Set up ZeeVee with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, zeeVeeController.oneClickSetup);

module.exports = router;