const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const { check, validationResult } = require('express-validator');
const Team = require('../../models/Team');
const TeamHub = require('../../models/TeamHub');

// Helpers
const isUserAdmin = (req) => req.user && Array.isArray(req.user.roles) && req.user.roles.includes('admin');

const getTeamById = async (teamId) => {
  return Team.findById(teamId).select('name description leader managers members');
};

const isTeamMember = (team, userId) => {
  if (!team || !userId) return false;
  const uid = userId.toString();
  return (
    (team.leader && team.leader.toString() === uid) ||
    (Array.isArray(team.managers) && team.managers.some(m => m && m.toString() === uid)) ||
    (Array.isArray(team.members) && team.members.some(m => m && m.toString() === uid))
  );
};

const canManageTeamHub = (team, req) => {
  if (isUserAdmin(req)) return true;
  if (!team || !req.user) return false;
  const uid = req.user.id?.toString();
  if (!uid) return false;
  if (team.leader && team.leader.toString() === uid) return true;
  if (Array.isArray(team.managers) && team.managers.some(m => m && m.toString() === uid)) return true;
  return false;
};

const getOrCreateHub = async (teamId) => {
  let hub = await TeamHub.findOne({ team: teamId });
  if (!hub) {
    hub = await TeamHub.create({ team: teamId, links: [], helpPages: [] });
  }
  return hub;
};

// GET /api/team-hub/:teamId - fetch hub content for a team
router.get('/:teamId', isAuthenticated, async (req, res) => {
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });

    // Access control: admin or team member can view
    if (!isUserAdmin(req) && !isTeamMember(team, req.user.id)) {
      return res.status(403).json({ msg: 'Access denied' });
    }

    const hub = await TeamHub.findOne({ team: team._id });
    const canManage = canManageTeamHub(team, req);

    res.json({
      team: { id: team._id, name: team.name, description: team.description },
      hub: hub ? hub : { team: team._id, links: [], helpPages: [] },
      canManage
    });
  } catch (err) {
    console.error('Error fetching team hub:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// Validation for link
const linkValidators = [
  check('title').trim().notEmpty().withMessage('Title is required'),
  check('url').trim().isURL().withMessage('Valid URL is required'),
  check('description').optional().isString(),
  check('pinned').optional().isBoolean()
];

// Validation for help page
const helpPageValidators = [
  check('title').trim().notEmpty().withMessage('Title is required'),
  check('content').notEmpty().withMessage('Content is required'),
  check('pinned').optional().isBoolean()
];

// Create link
router.post('/:teamId/links', [isAuthenticated, ...linkValidators], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });
    if (!canManageTeamHub(team, req)) return res.status(403).json({ msg: 'Not authorized' });

    const hub = await getOrCreateHub(team._id);
    hub.links.push({
      title: req.body.title,
      url: req.body.url,
      description: req.body.description,
      pinned: !!req.body.pinned,
      createdBy: req.user.id
    });
    await hub.save();
    res.json(hub);
  } catch (err) {
    console.error('Error creating team hub link:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// Update link
router.put('/:teamId/links/:linkId', [isAuthenticated, ...linkValidators], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });
    if (!canManageTeamHub(team, req)) return res.status(403).json({ msg: 'Not authorized' });

    const hub = await getOrCreateHub(team._id);
    const link = hub.links.id(req.params.linkId);
    if (!link) return res.status(404).json({ msg: 'Link not found' });

    link.title = req.body.title;
    link.url = req.body.url;
    link.description = req.body.description;
    if (req.body.pinned !== undefined) link.pinned = !!req.body.pinned;
    link.updatedAt = Date.now();

    await hub.save();
    res.json(hub);
  } catch (err) {
    console.error('Error updating team hub link:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// Delete link
router.delete('/:teamId/links/:linkId', isAuthenticated, async (req, res) => {
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });
    if (!canManageTeamHub(team, req)) return res.status(403).json({ msg: 'Not authorized' });

    const hub = await getOrCreateHub(team._id);
    const link = hub.links.id(req.params.linkId);
    if (!link) return res.status(404).json({ msg: 'Link not found' });
    link.deleteOne();
    await hub.save();
    res.json(hub);
  } catch (err) {
    console.error('Error deleting team hub link:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// Create help page
router.post('/:teamId/pages', [isAuthenticated, ...helpPageValidators], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });
    if (!canManageTeamHub(team, req)) return res.status(403).json({ msg: 'Not authorized' });

    const hub = await getOrCreateHub(team._id);
    hub.helpPages.push({
      title: req.body.title,
      content: req.body.content,
      pinned: !!req.body.pinned,
      createdBy: req.user.id
    });
    await hub.save();
    res.json(hub);
  } catch (err) {
    console.error('Error creating team hub help page:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// Update help page
router.put('/:teamId/pages/:pageId', [isAuthenticated, ...helpPageValidators], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });
    if (!canManageTeamHub(team, req)) return res.status(403).json({ msg: 'Not authorized' });

    const hub = await getOrCreateHub(team._id);
    const page = hub.helpPages.id(req.params.pageId);
    if (!page) return res.status(404).json({ msg: 'Help page not found' });

    page.title = req.body.title;
    page.content = req.body.content;
    if (req.body.pinned !== undefined) page.pinned = !!req.body.pinned;
    page.updatedAt = Date.now();

    await hub.save();
    res.json(hub);
  } catch (err) {
    console.error('Error updating team hub help page:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// Delete help page
router.delete('/:teamId/pages/:pageId', isAuthenticated, async (req, res) => {
  try {
    const team = await getTeamById(req.params.teamId);
    if (!team) return res.status(404).json({ msg: 'Team not found' });
    if (!canManageTeamHub(team, req)) return res.status(403).json({ msg: 'Not authorized' });

    const hub = await getOrCreateHub(team._id);
    const page = hub.helpPages.id(req.params.pageId);
    if (!page) return res.status(404).json({ msg: 'Help page not found' });
    page.deleteOne();
    await hub.save();
    res.json(hub);
  } catch (err) {
    console.error('Error deleting team hub help page:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

module.exports = router;
