const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const DashboardPreference = require('../../models/DashboardPreference');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/dashboard-preferences
 * @desc    Get current user's dashboard preferences
 * @access  private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const preferences = await DashboardPreference.getOrCreateForUser(req.user.id);
    res.json(preferences);
  } catch (err) {
    console.error('Error fetching dashboard preferences:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/dashboard-preferences
 * @desc    Update current user's dashboard preferences
 * @access  private
 */
router.put('/', [
  isAuthenticated,
  check('widgets', 'Widgets array is required').isArray(),
  check('layout', 'Layout must be either grid or list').optional().isIn(['grid', 'list'])
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    let preferences = await DashboardPreference.findOne({ user: req.user.id });
    
    if (!preferences) {
      preferences = new DashboardPreference({
        user: req.user.id,
        widgets: req.body.widgets
      });
    } else {
      preferences.widgets = req.body.widgets;
    }
    
    if (req.body.layout) {
      preferences.layout = req.body.layout;
    }
    
    await preferences.save();
    res.json(preferences);
  } catch (err) {
    console.error('Error updating dashboard preferences:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/dashboard-preferences/widgets
 * @desc    Add a new widget to dashboard
 * @access  private
 */
router.post('/widgets', [
  isAuthenticated,
  check('type', 'Widget type is required').notEmpty(),
  check('title', 'Widget title is required').notEmpty(),
  check('position', 'Widget position is required').isObject(),
  check('position.x', 'Position X is required').isNumeric(),
  check('position.y', 'Position Y is required').isNumeric(),
  check('position.w', 'Position width is required').isNumeric(),
  check('position.h', 'Position height is required').isNumeric()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const preferences = await DashboardPreference.getOrCreateForUser(req.user.id);
    
    preferences.widgets.push({
      type: req.body.type,
      title: req.body.title,
      position: req.body.position,
      settings: req.body.settings || {},
      isVisible: req.body.isVisible !== undefined ? req.body.isVisible : true
    });
    
    await preferences.save();
    res.json(preferences);
  } catch (err) {
    console.error('Error adding widget:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   PUT /api/dashboard-preferences/widgets/:widgetId
 * @desc    Update a widget
 * @access  private
 */
router.put('/widgets/:widgetId', [
  isAuthenticated,
  check('title', 'Widget title is required').optional().notEmpty(),
  check('position', 'Widget position must be an object').optional().isObject(),
  check('settings', 'Widget settings must be an object').optional().isObject(),
  check('isVisible', 'isVisible must be a boolean').optional().isBoolean()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const preferences = await DashboardPreference.findOne({ user: req.user.id });
    
    if (!preferences) {
      return res.status(404).json({ msg: 'Dashboard preferences not found' });
    }
    
    const widgetIndex = preferences.widgets.findIndex(w => w._id.toString() === req.params.widgetId);
    
    if (widgetIndex === -1) {
      return res.status(404).json({ msg: 'Widget not found' });
    }
    
    if (req.body.title) preferences.widgets[widgetIndex].title = req.body.title;
    if (req.body.position) preferences.widgets[widgetIndex].position = req.body.position;
    if (req.body.settings) preferences.widgets[widgetIndex].settings = req.body.settings;
    if (req.body.isVisible !== undefined) preferences.widgets[widgetIndex].isVisible = req.body.isVisible;
    
    await preferences.save();
    res.json(preferences);
  } catch (err) {
    console.error('Error updating widget:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   DELETE /api/dashboard-preferences/widgets/:widgetId
 * @desc    Remove a widget
 * @access  private
 */
router.delete('/widgets/:widgetId', isAuthenticated, async (req, res) => {
  try {
    const preferences = await DashboardPreference.findOne({ user: req.user.id });
    
    if (!preferences) {
      return res.status(404).json({ msg: 'Dashboard preferences not found' });
    }
    
    const widgetIndex = preferences.widgets.findIndex(w => w._id.toString() === req.params.widgetId);
    
    if (widgetIndex === -1) {
      return res.status(404).json({ msg: 'Widget not found' });
    }
    
    preferences.widgets.splice(widgetIndex, 1);
    await preferences.save();
    
    res.json(preferences);
  } catch (err) {
    console.error('Error removing widget:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/dashboard-preferences/default
 * @desc    Get default dashboard preferences (doesn't save to user)
 * @access  private
 */
router.get('/default', isAuthenticated, async (req, res) => {
  try {
    // Create a temporary instance with default widgets without saving
    const defaultPreferences = {
      user: req.user.id,
      widgets: [
        {
          type: 'shortcuts',
          title: 'Popular Shortcuts',
          position: { x: 0, y: 0, w: 6, h: 2 },
          settings: {}
        },
        {
          type: 'recentFiles',
          title: 'Recent Files',
          position: { x: 6, y: 0, w: 6, h: 2 },
          settings: {}
        },
        {
          type: 'wiim',
          title: 'WiiM Media Player',
          position: { x: 0, y: 2, w: 12, h: 3 },
          settings: {
            refreshInterval: 10
          }
        },
        {
          type: 'quickActions',
          title: 'Quick Actions',
          position: { x: 0, y: 5, w: 12, h: 4 },
          settings: {}
        }
      ],
      layout: 'grid'
    };
    
    res.json(defaultPreferences);
  } catch (err) {
    console.error('Error getting default dashboard:', err);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/dashboard-preferences/reset
 * @desc    Reset dashboard to default
 * @access  private
 */
router.post('/reset', isAuthenticated, async (req, res) => {
  try {
    await DashboardPreference.findOneAndRemove({ user: req.user.id });
    const preferences = await DashboardPreference.getOrCreateForUser(req.user.id);
    res.json(preferences);
  } catch (err) {
    console.error('Error resetting dashboard:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;