const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const { isAuthenticated } = require('../../middleware/auth');
const canvaController = require('../../server/controllers/canvaController');

// OAuth Routes

// @route   GET api/canva/auth
// @desc    Get Canva OAuth authorization URL
// @access  Private
router.get('/auth', isAuthenticated, canvaController.getAuthUrl);

// @route   GET api/canva/callback
// @desc    Handle Canva OAuth callback
// @access  Private
router.get('/callback', isAuthenticated, canvaController.handleCallback);

// @route   GET api/canva/check-auth
// @desc    Check if user is authenticated with Canva
// @access  Private
router.get('/check-auth', isAuthenticated, canvaController.checkAuth);

// API Routes

// @route   GET api/canva/designs
// @desc    Get all Canva designs
// @access  Private
router.get('/designs', isAuthenticated, canvaController.getDesigns);

// @route   GET api/canva/designs/:id
// @desc    Get Canva design details
// @access  Private
router.get('/designs/:id', isAuthenticated, canvaController.getDesign);

// @route   GET api/canva/templates
// @desc    Get Canva templates
// @access  Private
router.get('/templates', isAuthenticated, canvaController.getTemplates);

// @route   GET api/canva/designs/:id/assets
// @desc    Get Canva design assets
// @access  Private
router.get('/designs/:id/assets', isAuthenticated, canvaController.getDesignAssets);

// @route   GET api/canva/designs/:id/files
// @desc    Get Canva design files
// @access  Private
router.get('/designs/:id/files', isAuthenticated, canvaController.getDesignFiles);

// @route   GET api/canva/files/:id
// @desc    Get Canva file details
// @access  Private
router.get('/files/:id', isAuthenticated, canvaController.getFile);

// @route   GET api/canva/users
// @desc    Get Canva users
// @access  Private
router.get('/users', isAuthenticated, canvaController.getUsers);

// @route   GET api/canva/users/:id
// @desc    Get Canva user details
// @access  Private
router.get('/users/:id', isAuthenticated, canvaController.getUser);

// Configuration Routes

// @route   POST api/canva/config
// @desc    Save Canva configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, canvaController.saveConfig);

// @route   GET api/canva/config
// @desc    Get Canva configuration status
// @access  Private
router.get('/config', isAuthenticated, canvaController.getConfig);

module.exports = router;