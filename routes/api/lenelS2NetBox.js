const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const lenelS2NetBoxController = require('../../server/controllers/lenelS2NetBoxController');

// @route   GET api/lenel-s2-netbox/portals
// @desc    Get all Lenel S2 NetBox portals
// @access  Private
router.get('/portals', isAuthenticated, lenelS2NetBoxController.getPortals);

// @route   GET api/lenel-s2-netbox/portals/:id
// @desc    Get Lenel S2 NetBox portal details
// @access  Private
router.get('/portals/:id', isAuthenticated, lenelS2NetBoxController.getPortalDetails);

// @route   GET api/lenel-s2-netbox/cardholders
// @desc    Get all Lenel S2 NetBox cardholders
// @access  Private
router.get('/cardholders', isAuthenticated, lenelS2NetBoxController.getCardholders);

// @route   GET api/lenel-s2-netbox/cardholders/:id
// @desc    Get Lenel S2 NetBox cardholder details
// @access  Private
router.get('/cardholders/:id', isAuthenticated, lenelS2NetBoxController.getCardholderDetails);

// @route   GET api/lenel-s2-netbox/alarms
// @desc    Get all Lenel S2 NetBox alarms
// @access  Private
router.get('/alarms', isAuthenticated, lenelS2NetBoxController.getAlarms);

// @route   POST api/lenel-s2-netbox/alarms/:id/acknowledge
// @desc    Acknowledge a Lenel S2 NetBox alarm
// @access  Private
router.post('/alarms/:id/acknowledge', isAuthenticated, lenelS2NetBoxController.acknowledgeAlarm);

// @route   GET api/lenel-s2-netbox/events
// @desc    Get all Lenel S2 NetBox events
// @access  Private
router.get('/events', isAuthenticated, lenelS2NetBoxController.getEvents);

// @route   GET api/lenel-s2-netbox/system/status
// @desc    Get Lenel S2 NetBox system status
// @access  Private
router.get('/system/status', isAuthenticated, lenelS2NetBoxController.getSystemStatus);

// @route   POST api/lenel-s2-netbox/doors/:id/unlock
// @desc    Unlock a Lenel S2 NetBox door
// @access  Private
router.post('/doors/:id/unlock', isAuthenticated, lenelS2NetBoxController.unlockDoor);

// @route   POST api/lenel-s2-netbox/doors/:id/lock
// @desc    Lock a Lenel S2 NetBox door
// @access  Private
router.post('/doors/:id/lock', isAuthenticated, lenelS2NetBoxController.lockDoor);

// @route   POST api/lenel-s2-netbox/portals/:id/control
// @desc    Control a Lenel S2 NetBox portal
// @access  Private
router.post('/portals/:id/control', isAuthenticated, lenelS2NetBoxController.controlPortal);

// @route   POST api/lenel-s2-netbox/config
// @desc    Save Lenel S2 NetBox configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, lenelS2NetBoxController.saveConfig);

// @route   GET api/lenel-s2-netbox/config
// @desc    Get Lenel S2 NetBox configuration status
// @access  Private
router.get('/config', isAuthenticated, lenelS2NetBoxController.getConfig);

// @route   POST api/lenel-s2-netbox/one-click-setup
// @desc    Set up Lenel S2 NetBox with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, lenelS2NetBoxController.oneClickSetup);

// @route   GET api/lenel-s2-netbox/access-levels
// @desc    Get all Lenel S2 NetBox access levels
// @access  Private
router.get('/access-levels', isAuthenticated, lenelS2NetBoxController.getAccessLevels);

// @route   GET api/lenel-s2-netbox/access-levels/:id
// @desc    Get Lenel S2 NetBox access level details
// @access  Private
router.get('/access-levels/:id', isAuthenticated, lenelS2NetBoxController.getAccessLevelDetails);

// @route   GET api/lenel-s2-netbox/access-groups
// @desc    Get all Lenel S2 NetBox access groups
// @access  Private
router.get('/access-groups', isAuthenticated, lenelS2NetBoxController.getAccessGroups);

// @route   GET api/lenel-s2-netbox/access-groups/:id
// @desc    Get Lenel S2 NetBox access group details
// @access  Private
router.get('/access-groups/:id', isAuthenticated, lenelS2NetBoxController.getAccessGroupDetails);

// @route   GET api/lenel-s2-netbox/badges
// @desc    Get all Lenel S2 NetBox badges
// @access  Private
router.get('/badges', isAuthenticated, lenelS2NetBoxController.getBadges);

// @route   GET api/lenel-s2-netbox/badges/:id
// @desc    Get Lenel S2 NetBox badge details
// @access  Private
router.get('/badges/:id', isAuthenticated, lenelS2NetBoxController.getBadgeDetails);

// @route   GET api/lenel-s2-netbox/door-schedules
// @desc    Get all Lenel S2 NetBox door schedules
// @access  Private
router.get('/door-schedules', isAuthenticated, lenelS2NetBoxController.getDoorSchedules);

// @route   GET api/lenel-s2-netbox/door-schedules/:id
// @desc    Get Lenel S2 NetBox door schedule details
// @access  Private
router.get('/door-schedules/:id', isAuthenticated, lenelS2NetBoxController.getDoorScheduleDetails);

// @route   GET api/lenel-s2-netbox/doors
// @desc    Get all Lenel S2 NetBox doors
// @access  Private
router.get('/doors', isAuthenticated, lenelS2NetBoxController.getDoors);

// @route   GET api/lenel-s2-netbox/doors/:id/status
// @desc    Get Lenel S2 NetBox door status
// @access  Private
router.get('/doors/:id/status', isAuthenticated, lenelS2NetBoxController.getDoorStatus);

// @route   POST api/lenel-s2-netbox/cardholders/:cardholderId/access-levels/:accessLevelId
// @desc    Assign access level to cardholder
// @access  Private
router.post('/cardholders/:cardholderId/access-levels/:accessLevelId', isAuthenticated, lenelS2NetBoxController.assignAccessLevelToCardholder);

// @route   DELETE api/lenel-s2-netbox/cardholders/:cardholderId/access-levels/:accessLevelId
// @desc    Remove access level from cardholder
// @access  Private
router.delete('/cardholders/:cardholderId/access-levels/:accessLevelId', isAuthenticated, lenelS2NetBoxController.removeAccessLevelFromCardholder);

// User Management Routes (using 'users' endpoint for new frontend)

// @route   POST api/lenel-s2-netbox/users
// @desc    Create a new user
// @access  Private
router.post('/users', isAuthenticated, lenelS2NetBoxController.createUser);

// @route   PUT api/lenel-s2-netbox/users/:id
// @desc    Update an existing user
// @access  Private
router.put('/users/:id', isAuthenticated, lenelS2NetBoxController.updateUser);

// @route   DELETE api/lenel-s2-netbox/users/:id
// @desc    Delete a user
// @access  Private
router.delete('/users/:id', isAuthenticated, lenelS2NetBoxController.deleteUser);

// @route   GET api/lenel-s2-netbox/users
// @desc    Get all users
// @access  Private
router.get('/users', isAuthenticated, lenelS2NetBoxController.getCardholders);

// @route   GET api/lenel-s2-netbox/users/:id
// @desc    Get user details
// @access  Private
router.get('/users/:id', isAuthenticated, lenelS2NetBoxController.getCardholderDetails);

// @route   POST api/lenel-s2-netbox/users/:id/credentials
// @desc    Create a new user credential
// @access  Private
router.post('/users/:id/credentials', isAuthenticated, lenelS2NetBoxController.createUserCredential);

// @route   PUT api/lenel-s2-netbox/users/:id/credentials/:credentialId
// @desc    Update a user credential
// @access  Private
router.put('/users/:id/credentials/:credentialId', isAuthenticated, lenelS2NetBoxController.updateUserCredential);

// @route   DELETE api/lenel-s2-netbox/users/:id/credentials/:credentialId
// @desc    Delete a user credential
// @access  Private
router.delete('/users/:id/credentials/:credentialId', isAuthenticated, lenelS2NetBoxController.deleteUserCredential);

// @route   GET api/lenel-s2-netbox/users/:id/credentials
// @desc    Get user credentials
// @access  Private
router.get('/users/:id/credentials', isAuthenticated, lenelS2NetBoxController.getUserCredentials);

// @route   GET api/lenel-s2-netbox/users/:id/logs
// @desc    Get user activity logs
// @access  Private
router.get('/users/:id/logs', isAuthenticated, lenelS2NetBoxController.getUserLogs);

// Same routes for cardholders (for backward compatibility)

// @route   POST api/lenel-s2-netbox/cardholders
// @desc    Create a new cardholder
// @access  Private
router.post('/cardholders', isAuthenticated, lenelS2NetBoxController.createUser);

// @route   PUT api/lenel-s2-netbox/cardholders/:id
// @desc    Update an existing cardholder
// @access  Private
router.put('/cardholders/:id', isAuthenticated, lenelS2NetBoxController.updateUser);

// @route   DELETE api/lenel-s2-netbox/cardholders/:id
// @desc    Delete a cardholder
// @access  Private
router.delete('/cardholders/:id', isAuthenticated, lenelS2NetBoxController.deleteUser);

// @route   POST api/lenel-s2-netbox/cardholders/:id/credentials
// @desc    Create a new cardholder credential
// @access  Private
router.post('/cardholders/:id/credentials', isAuthenticated, lenelS2NetBoxController.createUserCredential);

// @route   PUT api/lenel-s2-netbox/cardholders/:id/credentials/:credentialId
// @desc    Update a cardholder credential
// @access  Private
router.put('/cardholders/:id/credentials/:credentialId', isAuthenticated, lenelS2NetBoxController.updateUserCredential);

// @route   DELETE api/lenel-s2-netbox/cardholders/:id/credentials/:credentialId
// @desc    Delete a cardholder credential
// @access  Private
router.delete('/cardholders/:id/credentials/:credentialId', isAuthenticated, lenelS2NetBoxController.deleteUserCredential);

// @route   GET api/lenel-s2-netbox/cardholders/:id/credentials
// @desc    Get cardholder credentials
// @access  Private
router.get('/cardholders/:id/credentials', isAuthenticated, lenelS2NetBoxController.getUserCredentials);

// @route   GET api/lenel-s2-netbox/cardholders/:id/logs
// @desc    Get cardholder activity logs
// @access  Private
router.get('/cardholders/:id/logs', isAuthenticated, lenelS2NetBoxController.getUserLogs);

// ===================================================================
// LIVE ACTIVITY LOG AND CARD MANAGEMENT ROUTES
// ===================================================================

// @route   GET api/lenel-s2-netbox/activity-log/live
// @desc    Get live activity log with real-time updates
// @access  Private
router.get('/activity-log/live', isAuthenticated, lenelS2NetBoxController.getLiveActivityLog);

// @route   POST api/lenel-s2-netbox/credentials/:credentialId/mark-lost
// @desc    Mark a card as lost
// @access  Private
router.post('/credentials/:credentialId/mark-lost', isAuthenticated, lenelS2NetBoxController.markCardAsLost);

// @route   POST api/lenel-s2-netbox/credentials/:credentialId/restore
// @desc    Restore a lost card
// @access  Private
router.post('/credentials/:credentialId/restore', isAuthenticated, lenelS2NetBoxController.restoreCard);

// ===================================================================
// EVACUATION MANAGEMENT ROUTES
// ===================================================================

// @route   POST api/lenel-s2-netbox/evacuations
// @desc    Initiate an evacuation
// @access  Private
router.post('/evacuations', isAuthenticated, lenelS2NetBoxController.initiateEvacuation);

// @route   POST api/lenel-s2-netbox/evacuations/:id/end
// @desc    End an evacuation
// @access  Private
router.post('/evacuations/:id/end', isAuthenticated, lenelS2NetBoxController.endEvacuation);

// @route   GET api/lenel-s2-netbox/evacuations/status
// @desc    Get evacuation status
// @access  Private
router.get('/evacuations/status', isAuthenticated, lenelS2NetBoxController.getEvacuationStatus);

// @route   GET api/lenel-s2-netbox/evacuations/:id/status
// @desc    Get specific evacuation status
// @access  Private
router.get('/evacuations/:id/status', isAuthenticated, lenelS2NetBoxController.getEvacuationStatus);

// @route   GET api/lenel-s2-netbox/occupancy/report
// @desc    Get occupancy report
// @access  Private
router.get('/occupancy/report', isAuthenticated, lenelS2NetBoxController.getOccupancyReport);

// ===================================================================
// READER MANAGEMENT ROUTES
// ===================================================================

// @route   GET api/lenel-s2-netbox/readers
// @desc    Get all Lenel S2 NetBox readers
// @access  Private
router.get('/readers', isAuthenticated, lenelS2NetBoxController.getReaders);

// @route   GET api/lenel-s2-netbox/readers/:id
// @desc    Get Lenel S2 NetBox reader details
// @access  Private
router.get('/readers/:id', isAuthenticated, lenelS2NetBoxController.getReaderDetails);

// @route   GET api/lenel-s2-netbox/reader-groups
// @desc    Get all Lenel S2 NetBox reader groups
// @access  Private
router.get('/reader-groups', isAuthenticated, lenelS2NetBoxController.getReaderGroups);

// @route   GET api/lenel-s2-netbox/reader-groups/:id
// @desc    Get Lenel S2 NetBox reader group details
// @access  Private
router.get('/reader-groups/:id', isAuthenticated, lenelS2NetBoxController.getReaderGroupDetails);

// ===================================================================
// PORTAL GROUP MANAGEMENT ROUTES
// ===================================================================

// @route   GET api/lenel-s2-netbox/portal-groups
// @desc    Get all Lenel S2 NetBox portal groups
// @access  Private
router.get('/portal-groups', isAuthenticated, lenelS2NetBoxController.getPortalGroups);

// @route   GET api/lenel-s2-netbox/portal-groups/:id
// @desc    Get Lenel S2 NetBox portal group details
// @access  Private
router.get('/portal-groups/:id', isAuthenticated, lenelS2NetBoxController.getPortalGroupDetails);

// ===================================================================
// ELEVATOR CONTROL ROUTES
// ===================================================================

// @route   GET api/lenel-s2-netbox/elevators
// @desc    Get all Lenel S2 NetBox elevators
// @access  Private
router.get('/elevators', isAuthenticated, lenelS2NetBoxController.getElevators);

// @route   GET api/lenel-s2-netbox/elevators/:id
// @desc    Get Lenel S2 NetBox elevator details
// @access  Private
router.get('/elevators/:id', isAuthenticated, lenelS2NetBoxController.getElevatorDetails);

// @route   POST api/lenel-s2-netbox/elevators/:id/control
// @desc    Control Lenel S2 NetBox elevator
// @access  Private
router.post('/elevators/:id/control', isAuthenticated, lenelS2NetBoxController.controlElevator);

// @route   GET api/lenel-s2-netbox/elevators/:id/status
// @desc    Get Lenel S2 NetBox elevator status
// @access  Private
router.get('/elevators/:id/status', isAuthenticated, lenelS2NetBoxController.getElevatorStatus);

module.exports = router;
