const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const panasonicController = require('../../server/controllers/panasonicController');

// @route   GET api/panasonic/initialize
// @desc    Initialize the Panasonic camera API
// @access  Private
router.get('/initialize', isAuthenticated, panasonicController.initialize);

// @route   GET api/panasonic/info
// @desc    Get Panasonic camera information
// @access  Private
router.get('/info', isAuthenticated, panasonicController.getCameraInfo);

// @route   GET api/panasonic/status
// @desc    Get Panasonic camera status
// @access  Private
router.get('/status', isAuthenticated, panasonicController.getCameraStatus);

// @route   GET api/panasonic/presets
// @desc    Get Panasonic camera presets
// @access  Private
router.get('/presets', isAuthenticated, panasonicController.getCameraPresets);

// @route   GET api/panasonic/presets/:presetId
// @desc    Move Panasonic camera to preset position
// @access  Private
router.get('/presets/:presetId', isAuthenticated, panasonicController.moveToPreset);

// @route   POST api/panasonic/ptz
// @desc    Control Panasonic camera PTZ (Pan, Tilt, Zoom)
// @access  Private
router.post('/ptz', isAuthenticated, panasonicController.controlPTZ);

// @route   POST api/panasonic/zoom
// @desc    Control Panasonic camera zoom
// @access  Private
router.post('/zoom', isAuthenticated, panasonicController.controlZoom);

// @route   GET api/panasonic/snapshot
// @desc    Get Panasonic camera snapshot
// @access  Private
router.get('/snapshot', isAuthenticated, panasonicController.getSnapshot);

// @route   GET api/panasonic/config
// @desc    Get Panasonic camera configuration status
// @access  Private
router.get('/config', isAuthenticated, panasonicController.getConfig);

module.exports = router;