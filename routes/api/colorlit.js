const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const coloritController = require('../../server/controllers/coloritController');

// @route   GET api/colorlit/device
// @desc    Get Colorlit device information
// @access  Private
router.get('/device', isAuthenticated, coloritController.getDeviceInfo);

// @route   GET api/colorlit/status
// @desc    Get current status
// @access  Private
router.get('/status', isAuthenticated, coloritController.getStatus);

// @route   POST api/colorlit/power
// @desc    Set power state
// @access  Private
router.post('/power', isAuthenticated, coloritController.setPower);

// @route   POST api/colorlit/color
// @desc    Set color
// @access  Private
router.post('/color', isAuthenticated, coloritController.setColor);

// @route   POST api/colorlit/brightness
// @desc    Set brightness
// @access  Private
router.post('/brightness', isAuthenticated, coloritController.setBrightness);

// @route   GET api/colorlit/modes
// @desc    Get available modes
// @access  Private
router.get('/modes', isAuthenticated, coloritController.getModes);

// @route   POST api/colorlit/mode
// @desc    Set mode
// @access  Private
router.post('/mode', isAuthenticated, coloritController.setMode);

// @route   POST api/colorlit/speed
// @desc    Set speed
// @access  Private
router.post('/speed', isAuthenticated, coloritController.setSpeed);

// @route   GET api/colorlit/zones
// @desc    Get zones
// @access  Private
router.get('/zones', isAuthenticated, coloritController.getZones);

// @route   POST api/colorlit/zone
// @desc    Select zone
// @access  Private
router.post('/zone', isAuthenticated, coloritController.selectZone);

// @route   GET api/colorlit/config
// @desc    Get configuration
// @access  Private
router.get('/config', isAuthenticated, coloritController.getConfig);

// @route   POST api/colorlit/config
// @desc    Save configuration
// @access  Private
router.post('/config', isAuthenticated, coloritController.saveConfig);

module.exports = router;