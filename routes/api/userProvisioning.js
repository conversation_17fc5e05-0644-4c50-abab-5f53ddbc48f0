const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const userProvisioningController = require('../../server/controllers/userProvisioningController');

// GET /api/user-provisioning/:userId/status
// Get user account status across all integrations
// Private (admin only)
router.get('/:userId/status', isAuthenticated, hasRoles(['admin']), userProvisioningController.getUserAccountStatus);

// POST /api/user-provisioning/:userId/:integrationId
// Provision user account in an integration
// Private (admin only)
router.post('/:userId/:integrationId', isAuthenticated, hasRoles(['admin']), userProvisioningController.provisionUserAccount);

// DELETE /api/user-provisioning/:userId/:integrationId
// Deprovision user account from an integration
// Private (admin only)
router.delete('/:userId/:integrationId', isAuthenticated, hasRoles(['admin']), userProvisioningController.deprovisionUserAccount);

module.exports = router;