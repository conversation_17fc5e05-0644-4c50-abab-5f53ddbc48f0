const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const AssetRequest = require('../../models/AssetRequest');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/asset-requests
 * @desc    Get all asset requests
 * @access  private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // If user is admin or has asset_manager role, return all requests
    // Otherwise, return only the user's requests
    let requests;
    if (req.user.roles.includes('admin') || req.user.roles.includes('asset_manager')) {
      requests = await AssetRequest.find()
        .sort({ createdAt: -1 })
        .populate('requestedBy', 'name email')
        .populate('approvedBy', 'name email');
    } else {
      requests = await AssetRequest.find({ requestedBy: req.user.id })
        .sort({ createdAt: -1 })
        .populate('approvedBy', 'name email');
    }
    
    res.json(requests);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/asset-requests/:id
 * @desc    Get asset request by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const request = await AssetRequest.findById(req.params.id)
      .populate('requestedBy', 'name email')
      .populate('approvedBy', 'name email');
    
    if (!request) {
      return res.status(404).json({ msg: 'Asset request not found' });
    }
    
    // Check if user is authorized to view this request
    if (!req.user.roles.includes('admin') && 
        !req.user.roles.includes('asset_manager') && 
        request.requestedBy._id.toString() !== req.user.id) {
      return res.status(403).json({ msg: 'Not authorized to view this request' });
    }
    
    res.json(request);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Asset request not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/asset-requests
 * @desc    Create an asset request
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    check('assetId', 'Asset ID is required').not().isEmpty(),
    check('assetType', 'Asset type is required').not().isEmpty(),
    check('assetName', 'Asset name is required').not().isEmpty(),
    check('requestReason', 'Request reason is required').not().isEmpty(),
    check('startDate', 'Start date is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        assetId, 
        assetType, 
        assetName, 
        requestReason, 
        startDate, 
        endDate 
      } = req.body;

      const newRequest = new AssetRequest({
        assetId,
        assetType,
        assetName,
        requestReason,
        startDate,
        endDate,
        requestedBy: req.user.id,
        status: 'pending'
      });

      const request = await newRequest.save();
      res.json(request);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/asset-requests/:id/approve
 * @desc    Approve an asset request
 * @access  private
 */
router.put(
  '/:id/approve',
  [
    isAuthenticated,
    hasRoles(['admin', 'asset_manager']),
    check('approvalNotes', 'Approval notes are required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const request = await AssetRequest.findById(req.params.id);
      
      if (!request) {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      if (request.status !== 'pending') {
        return res.status(400).json({ msg: 'Request has already been processed' });
      }
      
      request.status = 'approved';
      request.approvedBy = req.user.id;
      request.approvalDate = Date.now();
      request.approvalNotes = req.body.approvalNotes;

      await request.save();
      
      res.json(request);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/asset-requests/:id/deny
 * @desc    Deny an asset request
 * @access  private
 */
router.put(
  '/:id/deny',
  [
    isAuthenticated,
    hasRoles(['admin', 'asset_manager']),
    check('approvalNotes', 'Denial reason is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const request = await AssetRequest.findById(req.params.id);
      
      if (!request) {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      if (request.status !== 'pending') {
        return res.status(400).json({ msg: 'Request has already been processed' });
      }
      
      request.status = 'denied';
      request.approvedBy = req.user.id;
      request.approvalDate = Date.now();
      request.approvalNotes = req.body.approvalNotes;

      await request.save();
      
      res.json(request);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/asset-requests/:id/return
 * @desc    Mark an asset as returned
 * @access  private
 */
router.put(
  '/:id/return',
  [
    isAuthenticated,
    hasRoles(['admin', 'asset_manager']),
    check('returnCondition', 'Return condition is required').not().isEmpty(),
    check('returnNotes', 'Return notes are required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const request = await AssetRequest.findById(req.params.id);
      
      if (!request) {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      if (request.status !== 'approved') {
        return res.status(400).json({ msg: 'Only approved requests can be marked as returned' });
      }
      
      request.status = 'completed';
      request.returnDate = Date.now();
      request.returnCondition = req.body.returnCondition;
      request.returnNotes = req.body.returnNotes;

      await request.save();
      
      res.json(request);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/asset-requests/:id/cancel
 * @desc    Cancel an asset request
 * @access  private
 */
router.put(
  '/:id/cancel',
  isAuthenticated,
  async (req, res) => {
    try {
      const request = await AssetRequest.findById(req.params.id);
      
      if (!request) {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      // Only the requester or an admin/asset manager can cancel a request
      if (request.requestedBy.toString() !== req.user.id && 
          !req.user.roles.includes('admin') && 
          !req.user.roles.includes('asset_manager')) {
        return res.status(403).json({ msg: 'Not authorized to cancel this request' });
      }
      
      if (request.status !== 'pending' && request.status !== 'approved') {
        return res.status(400).json({ msg: 'Only pending or approved requests can be cancelled' });
      }
      
      request.status = 'cancelled';

      await request.save();
      
      res.json(request);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset request not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

module.exports = router;