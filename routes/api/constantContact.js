const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const constantContactController = require('../../server/controllers/constantContactController');

// @route   GET api/constant-contact/auth-url
// @desc    Get Constant Contact OAuth authorization URL
// @access  Private (admin or authorized users)
router.get('/auth-url', isAuthenticated, constantContactController.getAuthUrl);

// @route   GET api/constant-contact/oauth/callback
// @desc    OAuth callback to exchange code for tokens
// @access  Public (called by Constant Contact)
router.get('/oauth/callback', constantContactController.oauthCallback);

// @route   GET api/constant-contact/initialize
// @desc    Initialize the Constant Contact API
// @access  Private
router.get('/initialize', isAuthenticated, constantContactController.initialize);

// @route   GET api/constant-contact/account
// @desc    Get Constant Contact account information
// @access  Private
router.get('/account', isAuthenticated, constantContactController.getAccountInfo);

// @route   GET api/constant-contact/lists
// @desc    Get Constant Contact lists
// @access  Private
router.get('/lists', isAuthenticated, constantContactController.getContactLists);

// @route   GET api/constant-contact/contacts
// @desc    Get Constant Contact contacts
// @access  Private
router.get('/contacts', isAuthenticated, constantContactController.getContacts);

// @route   GET api/constant-contact/contacts/email
// @desc    Get Constant Contact contact by email
// @access  Private
router.get('/contacts/email', isAuthenticated, constantContactController.getContactByEmail);

// @route   POST api/constant-contact/contacts
// @desc    Create a new Constant Contact contact
// @access  Private
router.post('/contacts', isAuthenticated, constantContactController.createContact);

// @route   PUT api/constant-contact/contacts/:contactId
// @desc    Update an existing Constant Contact contact
// @access  Private
router.put('/contacts/:contactId', isAuthenticated, constantContactController.updateContact);

// @route   DELETE api/constant-contact/contacts/:contactId
// @desc    Delete a Constant Contact contact
// @access  Private
router.delete('/contacts/:contactId', isAuthenticated, constantContactController.deleteContact);

// @route   GET api/constant-contact/campaigns
// @desc    Get Constant Contact campaigns
// @access  Private
router.get('/campaigns', isAuthenticated, constantContactController.getCampaigns);

// @route   GET api/constant-contact/campaigns/:campaignId
// @desc    Get Constant Contact campaign by ID
// @access  Private
router.get('/campaigns/:campaignId', isAuthenticated, constantContactController.getCampaign);

// @route   GET api/constant-contact/campaigns/:campaignId/tracking
// @desc    Get Constant Contact campaign tracking data
// @access  Private
router.get('/campaigns/:campaignId/tracking', isAuthenticated, constantContactController.getCampaignTracking);

// @route   GET api/constant-contact/config
// @desc    Get Constant Contact configuration status
// @access  Private
router.get('/config', isAuthenticated, constantContactController.getConfig);

module.exports = router;