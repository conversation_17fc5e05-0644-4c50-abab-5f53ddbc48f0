const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const AssetIssue = require('../../models/AssetIssue');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/asset-issues
 * @desc    Get all asset issues
 * @access  private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // If user is admin or has asset_manager role, return all issues
    // Otherwise, return only the user's issues or issues assigned to them
    let issues;
    if (req.user.roles.includes('admin') || req.user.roles.includes('asset_manager')) {
      issues = await AssetIssue.find()
        .sort({ createdAt: -1 })
        .populate('reportedBy', 'name email')
        .populate('assignedTo', 'name email');
    } else {
      issues = await AssetIssue.find({ 
        $or: [
          { reportedBy: req.user.id },
          { assignedTo: req.user.id }
        ]
      })
        .sort({ createdAt: -1 })
        .populate('reportedBy', 'name email')
        .populate('assignedTo', 'name email');
    }
    
    res.json(issues);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/asset-issues/:id
 * @desc    Get asset issue by ID
 * @access  private
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const issue = await AssetIssue.findById(req.params.id)
      .populate('reportedBy', 'name email')
      .populate('assignedTo', 'name email')
      .populate('comments.user', 'name email');
    
    if (!issue) {
      return res.status(404).json({ msg: 'Asset issue not found' });
    }
    
    // Check if user is authorized to view this issue
    if (!req.user.roles.includes('admin') && 
        !req.user.roles.includes('asset_manager') && 
        issue.reportedBy._id.toString() !== req.user.id &&
        (issue.assignedTo && issue.assignedTo._id.toString() !== req.user.id)) {
      return res.status(403).json({ msg: 'Not authorized to view this issue' });
    }
    
    res.json(issue);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Asset issue not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/asset-issues
 * @desc    Create an asset issue
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    check('assetId', 'Asset ID is required').not().isEmpty(),
    check('assetType', 'Asset type is required').not().isEmpty(),
    check('assetName', 'Asset name is required').not().isEmpty(),
    check('issueType', 'Issue type is required').not().isEmpty(),
    check('issueDescription', 'Issue description is required').not().isEmpty(),
    check('priority', 'Priority is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        assetId, 
        assetType, 
        assetName, 
        issueType, 
        issueDescription, 
        priority,
        attachments
      } = req.body;

      const newIssue = new AssetIssue({
        assetId,
        assetType,
        assetName,
        issueType,
        issueDescription,
        priority,
        attachments: attachments || [],
        reportedBy: req.user.id,
        status: 'open'
      });

      const issue = await newIssue.save();
      res.json(issue);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/asset-issues/:id/assign
 * @desc    Assign an asset issue to a user
 * @access  private
 */
router.put(
  '/:id/assign',
  [
    isAuthenticated,
    hasRoles(['admin', 'asset_manager']),
    check('assignedTo', 'User ID to assign to is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const issue = await AssetIssue.findById(req.params.id);
      
      if (!issue) {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      issue.assignedTo = req.body.assignedTo;
      if (issue.status === 'open') {
        issue.status = 'in_progress';
      }

      // Add a comment about the assignment
      issue.comments.push({
        user: req.user.id,
        text: `Issue assigned to user ID: ${req.body.assignedTo}`,
        date: Date.now()
      });

      await issue.save();
      
      res.json(issue);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/asset-issues/:id/status
 * @desc    Update the status of an asset issue
 * @access  private
 */
router.put(
  '/:id/status',
  [
    isAuthenticated,
    check('status', 'Status is required').isIn(['open', 'in_progress', 'resolved', 'closed', 'reopened']),
    check('comment', 'Comment is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const issue = await AssetIssue.findById(req.params.id);
      
      if (!issue) {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      // Check if user is authorized to update this issue
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('asset_manager') && 
          issue.reportedBy.toString() !== req.user.id &&
          (issue.assignedTo && issue.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to update this issue' });
      }
      
      const { status, comment, resolution } = req.body;
      
      issue.status = status;
      
      // If resolving the issue, add resolution details
      if (status === 'resolved' && resolution) {
        issue.resolution = resolution;
        issue.resolutionDate = Date.now();
      }
      
      // Add the comment
      issue.comments.push({
        user: req.user.id,
        text: comment,
        date: Date.now()
      });

      await issue.save();
      
      res.json(issue);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   POST /api/asset-issues/:id/comment
 * @desc    Add a comment to an asset issue
 * @access  private
 */
router.post(
  '/:id/comment',
  [
    isAuthenticated,
    check('text', 'Comment text is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const issue = await AssetIssue.findById(req.params.id);
      
      if (!issue) {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      // Check if user is authorized to comment on this issue
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('asset_manager') && 
          issue.reportedBy.toString() !== req.user.id &&
          (issue.assignedTo && issue.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to comment on this issue' });
      }
      
      // Add the comment
      issue.comments.push({
        user: req.user.id,
        text: req.body.text,
        date: Date.now()
      });

      await issue.save();
      
      res.json(issue);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   POST /api/asset-issues/:id/attachment
 * @desc    Add an attachment to an asset issue
 * @access  private
 */
router.post(
  '/:id/attachment',
  [
    isAuthenticated,
    check('name', 'Attachment name is required').not().isEmpty(),
    check('url', 'Attachment URL is required').not().isEmpty(),
    check('type', 'Attachment type is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const issue = await AssetIssue.findById(req.params.id);
      
      if (!issue) {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      // Check if user is authorized to add attachments to this issue
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('asset_manager') && 
          issue.reportedBy.toString() !== req.user.id &&
          (issue.assignedTo && issue.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to add attachments to this issue' });
      }
      
      const { name, url, type } = req.body;
      
      // Add the attachment
      issue.attachments.push({
        name,
        url,
        type
      });

      // Add a comment about the attachment
      issue.comments.push({
        user: req.user.id,
        text: `Added attachment: ${name}`,
        date: Date.now()
      });

      await issue.save();
      
      res.json(issue);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Asset issue not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

module.exports = router;