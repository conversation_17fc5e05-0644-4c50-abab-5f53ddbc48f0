const express = require('express');
const router = express.Router();
const { isAuthenticated, hasActivatedIntegration } = require('../../middleware/auth');
const lgThinqController = require('../../server/controllers/lgThinqController');

// @route   GET api/lg-thinq/devices
// @desc    Get all LG ThinQ devices
// @access  Private (requires lg-thinq integration)
router.get('/devices', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.getDevices);

// @route   GET api/lg-thinq/devices/:id
// @desc    Get LG ThinQ device profile (detailed information)
// @access  Private (requires lg-thinq integration)
router.get('/devices/:id', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.getDeviceDetails);

// @route   GET api/lg-thinq/devices/:id/status
// @desc    Get LG ThinQ device state (current status)
// @access  Private (requires lg-thinq integration)
router.get('/devices/:id/status', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.getDeviceStatus);

// @route   POST api/lg-thinq/devices/:id/power
// @desc    Set device power state
// @access  Private (requires lg-thinq integration)
router.post('/devices/:id/power', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.setPower);

// @route   POST api/lg-thinq/devices/:id/temperature
// @desc    Set device temperature
// @access  Private (requires lg-thinq integration)
router.post('/devices/:id/temperature', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.setTemperature);

// @route   POST api/lg-thinq/devices/:id/fan-speed
// @desc    Set device fan speed
// @access  Private (requires lg-thinq integration)
router.post('/devices/:id/fan-speed', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.setFanSpeed);

// @route   POST api/lg-thinq/devices/:id/mode
// @desc    Set device mode
// @access  Private (requires lg-thinq integration)
router.post('/devices/:id/mode', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.setMode);

// @route   POST api/lg-thinq/devices/:id/swing-mode
// @desc    Set device swing mode
// @access  Private (requires lg-thinq integration)
router.post('/devices/:id/swing-mode', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.setSwingMode);

// @route   POST api/lg-thinq/devices/:id/control
// @desc    Control device with custom commands
// @access  Private (requires lg-thinq integration)
router.post('/devices/:id/control', isAuthenticated, hasActivatedIntegration('lgThinq'), lgThinqController.controlDevice);

// @route   POST api/lg-thinq/config
// @desc    Save LG ThinQ configuration
// @access  Private (admin only)
router.post('/config', isAuthenticated, lgThinqController.saveConfig);

// @route   GET api/lg-thinq/config
// @desc    Get LG ThinQ configuration status
// @access  Private
router.get('/config', isAuthenticated, lgThinqController.getConfig);

// @route   POST api/lg-thinq/one-click-setup
// @desc    Set up LG ThinQ with one click
// @access  Private
router.post('/one-click-setup', isAuthenticated, lgThinqController.oneClickSetup);

module.exports = router;