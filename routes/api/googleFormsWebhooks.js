const express = require('express');
const router = express.Router();
const googleFormsWebhookController = require('../../server/controllers/googleFormsWebhookController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const { check } = require('express-validator');

/**
 * @route   POST /api/google-forms-webhooks
 * @desc    Create a new webhook
 */
router.post(
  '/',
  isAuthenticated,
  [
    check('formId', 'Form ID is required').not().isEmpty(),
    check('formName', 'Form name is required').not().isEmpty(),
    check('fieldMappings', 'Field mappings are required').isObject(),
    check('fieldMappings.titleField', 'Title field mapping is required').not().isEmpty()
  ],
  googleFormsWebhookController.createWebhook
);

/**
 * @route   GET /api/google-forms-webhooks
 * @desc    Get all webhooks
 */
router.get(
  '/',
  isAuthenticated,
  googleFormsWebhookController.getAllWebhooks
);

/**
 * @route   GET /api/google-forms-webhooks/:id
 * @desc    Get webhook by ID
 */
router.get(
  '/:id',
  isAuthenticated,
  googleFormsWebhookController.getWebhookById
);

/**
 * @route   PUT /api/google-forms-webhooks/:id
 * @desc    Update a webhook
 */
router.put(
  '/:id',
  isAuthenticated,
  googleFormsWebhookController.updateWebhook
);

/**
 * @route   DELETE /api/google-forms-webhooks/:id
 * @desc    Delete a webhook
 */
router.delete(
  '/:id',
  isAuthenticated,
  googleFormsWebhookController.deleteWebhook
);

/**
 * @route   POST /api/google-forms-webhooks/:id/process
 * @desc    Process a webhook
 */
router.post(
  '/:id/process',
  isAuthenticated,
  googleFormsWebhookController.processWebhook
);

/**
 * @route   POST /api/google-forms-webhooks/process-all
 * @desc    Process all active webhooks (Admin only)
 */
router.post(
  '/process-all',
  isAuthenticated,
  hasRoles(['admin']),
  googleFormsWebhookController.processAllWebhooks
);

module.exports = router;