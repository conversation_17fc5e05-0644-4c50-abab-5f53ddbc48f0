const express = require('express');
const router = express.Router();
const assetController = require('../../server/controllers/assetController');
const assetCategoryController = require('../../server/controllers/assetCategoryController');
const assetLocationController = require('../../server/controllers/assetLocationController');
const assetMaintenanceController = require('../../server/controllers/assetMaintenanceController');
const assetReportController = require('../../server/controllers/assetReportController');
const assetBulkController = require('../../server/controllers/assetBulkController');
const assetAttachmentController = require('../../server/controllers/assetAttachmentController');
const { isAuthenticated } = require('../../middleware/auth');
const admin = require('../../middleware/admin');

/**
 * Asset Management Routes
 */


// Asset Barcode and QR Code
router.get('/:id/barcode', isAuthenticated, assetController.generateBarcode);
router.get('/:id/qrcode', isAuthenticated, assetController.generateQRCode);

// Asset History and Audit
router.get('/:id/history', isAuthenticated, assetController.getAssetHistory);

// Bulk Operations (Admin only)
router.post('/bulk-update', isAuthenticated, admin, assetController.bulkUpdate);

// Asset Categories
router.get('/categories/tree', isAuthenticated, assetCategoryController.getCategoryTree);
router.get('/categories/stats', isAuthenticated, assetCategoryController.getCategoryStats);
router.get('/categories/:id/assets', isAuthenticated, assetCategoryController.getCategoryAssets);
router.get('/categories', isAuthenticated, assetCategoryController.getCategories);
router.get('/categories/:id', isAuthenticated, assetCategoryController.getCategory);
router.post('/categories', isAuthenticated, assetCategoryController.createCategory);
router.put('/categories/:id', isAuthenticated, assetCategoryController.updateCategory);
router.delete('/categories/:id', isAuthenticated, admin, assetCategoryController.deleteCategory);

// Asset Locations
router.get('/locations/tree', isAuthenticated, assetLocationController.getLocationTree);
router.get('/locations/search', isAuthenticated, assetLocationController.searchLocations);
router.get('/locations/stats', isAuthenticated, assetLocationController.getLocationStats);
router.get('/locations/:id/assets', isAuthenticated, assetLocationController.getLocationAssets);
router.get('/locations', isAuthenticated, assetLocationController.getLocations);
router.get('/locations/:id', isAuthenticated, assetLocationController.getLocation);
router.post('/locations', isAuthenticated, assetLocationController.createLocation);
router.put('/locations/:id', isAuthenticated, assetLocationController.updateLocation);
router.delete('/locations/:id', isAuthenticated, admin, assetLocationController.deleteLocation);

// Asset Maintenance
router.get('/maintenance/overdue', isAuthenticated, assetMaintenanceController.getOverdueMaintenanceRecords);
router.get('/maintenance/upcoming', isAuthenticated, assetMaintenanceController.getUpcomingMaintenanceRecords);
router.get('/:assetId/maintenance/stats', isAuthenticated, assetMaintenanceController.getAssetMaintenanceStats);
router.post('/:assetId/maintenance/schedule-recurring', isAuthenticated, assetMaintenanceController.scheduleRecurringMaintenance);
router.get('/maintenance', isAuthenticated, assetMaintenanceController.getMaintenanceRecords);
router.get('/maintenance/:id', isAuthenticated, assetMaintenanceController.getMaintenanceRecord);
router.post('/maintenance', isAuthenticated, assetMaintenanceController.createMaintenanceRecord);
router.put('/maintenance/:id', isAuthenticated, assetMaintenanceController.updateMaintenanceRecord);
router.post('/maintenance/:id/complete', isAuthenticated, assetMaintenanceController.completeMaintenanceRecord);
router.delete('/maintenance/:id', isAuthenticated, assetMaintenanceController.deleteMaintenanceRecord);

// Asset Reports
router.get('/reports/overview', isAuthenticated, assetReportController.getAssetOverview);
router.get('/reports/financial', isAuthenticated, assetReportController.getFinancialReport);
router.get('/reports/maintenance', isAuthenticated, assetReportController.getMaintenanceReport);
router.get('/reports/utilization', isAuthenticated, assetReportController.getUtilizationReport);
router.get('/reports/audit', isAuthenticated, assetReportController.getAuditReport);
router.get('/reports/export', isAuthenticated, assetReportController.exportReport);
router.post('/reports/custom', isAuthenticated, assetReportController.getCustomReport);

// Bulk Operations
router.get('/export', isAuthenticated, assetBulkController.exportAssets);
router.post('/import', isAuthenticated, admin, assetBulkController.importAssets);
router.get('/import/template', isAuthenticated, assetBulkController.getImportTemplate);
router.post('/import/validate', isAuthenticated, assetBulkController.validateImportFile);

// Asset Attachments
router.get('/:id/attachments', isAuthenticated, assetAttachmentController.getAttachments);
router.post('/:id/attachments', isAuthenticated, assetAttachmentController.uploadAttachment);
router.post('/:id/attachments/bulk', isAuthenticated, assetAttachmentController.bulkUpload);
router.get('/:id/attachments/stats', isAuthenticated, assetAttachmentController.getAttachmentStats);
router.get('/:id/attachments/:attachmentId/download', isAuthenticated, assetAttachmentController.downloadAttachment);
router.put('/:id/attachments/:attachmentId', isAuthenticated, assetAttachmentController.updateAttachment);
router.delete('/:id/attachments/:attachmentId', isAuthenticated, assetAttachmentController.deleteAttachment);

// Asset CRUD Routes (placed after specific subroutes to prevent route conflicts)
router.get('/', isAuthenticated, assetController.getAssets);
router.get('/:id', isAuthenticated, assetController.getAsset);
router.post('/', isAuthenticated, assetController.createAsset);
router.put('/:id', isAuthenticated, assetController.updateAsset);
router.delete('/:id', isAuthenticated, assetController.deleteAsset);
router.post('/:id/restore', isAuthenticated, assetController.restoreAsset);

module.exports = router;
