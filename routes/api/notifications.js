const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../../middleware/auth');
const notificationController = require('../../server/controllers/notificationController');

// Get notifications for current user
router.get('/', isAuthenticated, notificationController.getUserNotifications);

// Get unread count for current user
router.get('/unread-count', isAuthenticated, notificationController.getUnreadCount);

// Mark a notification as read
router.patch('/:id/read', isAuthenticated, notificationController.markAsRead);

// Mark all notifications as read
router.patch('/mark-all-read', isAuthenticated, notificationController.markAllAsRead);

// Optional: create notification for testing/self
router.post('/', isAuthenticated, notificationController.createNotification);

module.exports = router;
