const express = require('express');
const router = express.Router();
const googleCalendarController = require('../../server/controllers/googleCalendarController');
const { isAuthenticated, hasRoles } = require('../../middleware/auth');

/**
 * @route   POST /api/google-calendar/config
 * @desc    Save Google Calendar configuration
 */
router.post('/config', isAuthenticated, hasR<PERSON>s(['admin']), googleCalendarController.saveConfig);

/**
 * @route   GET /api/google-calendar/config
 * @desc    Get Google Calendar configuration
 */
router.get('/config', isAuthenticated, hasRoles(['admin']), googleCalendarController.getConfig);

/**
 * @route   GET /api/google-calendar/auth-url
 * @desc    Get Google Calendar authentication URL
 */
router.get('/auth-url', isAuthenticated, hasRoles(['admin']), googleCalendarController.getAuthUrl);

/**
 * @route   GET /api/google-calendar/callback
 * @desc    Handle Google Calendar OAuth2 callback
 */
router.get('/callback', googleCalendarController.handleCallback);

/**
 * @route   GET /api/google-calendar/calendars
 * @desc    List calendars in Google Calendar
 */
router.get('/calendars', isAuthenticated, googleCalendarController.listCalendars);

/**
 * @route   GET /api/google-calendar/calendars/:calendarId
 * @desc    Get calendar details
 */
router.get('/calendars/:calendarId', isAuthenticated, googleCalendarController.getCalendar);

/**
 * @route   GET /api/google-calendar/calendars/:calendarId/events
 * @desc    List events for a calendar
 */
router.get('/calendars/:calendarId/events', isAuthenticated, googleCalendarController.listEvents);

/**
 * @route   GET /api/google-calendar/calendars/:calendarId/events/:eventId
 * @desc    Get event details
 */
router.get('/calendars/:calendarId/events/:eventId', isAuthenticated, googleCalendarController.getEvent);

/**
 * @route   POST /api/google-calendar/calendars/:calendarId/events
 * @desc    Create a new event
 */
router.post('/calendars/:calendarId/events', isAuthenticated, googleCalendarController.createEvent);

/**
 * @route   PUT /api/google-calendar/calendars/:calendarId/events/:eventId
 * @desc    Update an existing event
 */
router.put('/calendars/:calendarId/events/:eventId', isAuthenticated, googleCalendarController.updateEvent);

/**
 * @route   DELETE /api/google-calendar/calendars/:calendarId/events/:eventId
 * @desc    Delete an event
 */
router.delete('/calendars/:calendarId/events/:eventId', isAuthenticated, googleCalendarController.deleteEvent);

/**
 * @route   POST /api/google-calendar/one-click-setup
 * @desc    Set up Google Calendar with one click
 */
router.post('/one-click-setup', isAuthenticated, hasRoles(['admin']), googleCalendarController.oneClickSetup);

module.exports = router;
