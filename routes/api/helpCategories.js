const express = require('express');
const router = express.Router();
const { isAuthenticated, hasRoles } = require('../../middleware/auth');
const HelpCategory = require('../../models/HelpCategory');
const { check, validationResult } = require('express-validator');

/**
 * @route   GET /api/help/categories
 * @desc    Get all help categories
 * @access  public
 */
router.get('/', async (req, res) => {
  try {
    const categories = await HelpCategory.find({ isActive: true })
      .sort({ order: 1, name: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/help/categories/all
 * @desc    Get all help categories including inactive ones (admin only)
 * @access  private
 */
router.get('/all', isAuthenticated, hasRoles(['admin', 'content_manager']), async (req, res) => {
  try {
    const categories = await HelpCategory.find()
      .sort({ order: 1, name: 1 });
    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/help/categories/tree
 * @desc    Get help categories in a hierarchical tree structure
 * @access  public
 */
router.get('/tree', async (req, res) => {
  try {
    // Get all root categories (those without a parent)
    const rootCategories = await HelpCategory.find({ 
      parentCategory: null,
      isActive: true 
    }).sort({ order: 1, name: 1 });
    
    // Function to recursively get subcategories
    const getSubcategories = async (category) => {
      const subcategories = await HelpCategory.find({ 
        parentCategory: category._id,
        isActive: true 
      }).sort({ order: 1, name: 1 });
      
      const result = {
        ...category.toObject(),
        subcategories: []
      };
      
      if (subcategories.length > 0) {
        for (const subcat of subcategories) {
          result.subcategories.push(await getSubcategories(subcat));
        }
      }
      
      return result;
    };
    
    // Build the tree
    const categoryTree = [];
    for (const rootCat of rootCategories) {
      categoryTree.push(await getSubcategories(rootCat));
    }
    
    res.json(categoryTree);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET /api/help/categories/:id
 * @desc    Get help category by ID
 * @access  public
 */
router.get('/:id', async (req, res) => {
  try {
    const category = await HelpCategory.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({ msg: 'Help category not found' });
    }
    
    res.json(category);
  } catch (err) {
    console.error(err.message);
    
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'Help category not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST /api/help/categories
 * @desc    Create a help category
 * @access  private
 */
router.post(
  '/',
  [
    isAuthenticated,
    hasRoles(['admin', 'content_manager']),
    check('name', 'Name is required').not().isEmpty(),
    check('slug', 'Slug is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        name, 
        description, 
        slug, 
        icon, 
        color, 
        order,
        parentCategory,
        isActive
      } = req.body;

      // Check if category with same slug already exists
      const existingCategory = await HelpCategory.findOne({ slug });
      if (existingCategory) {
        return res.status(400).json({ msg: 'Category with this slug already exists' });
      }

      const newCategory = new HelpCategory({
        name,
        description,
        slug,
        icon: icon || 'help',
        color: color || '#2563eb',
        order: order || 0,
        parentCategory: parentCategory || null,
        isActive: isActive !== undefined ? isActive : true,
        createdBy: req.user.id,
        updatedBy: req.user.id
      });

      const category = await newCategory.save();
      res.json(category);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   PUT /api/help/categories/:id
 * @desc    Update a help category
 * @access  private
 */
router.put(
  '/:id',
  [
    isAuthenticated,
    hasRoles(['admin', 'content_manager']),
    check('name', 'Name is required').not().isEmpty(),
    check('slug', 'Slug is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        name, 
        description, 
        slug, 
        icon, 
        color, 
        order,
        parentCategory,
        isActive
      } = req.body;

      const category = await HelpCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ msg: 'Help category not found' });
      }
      
      // Check if another category with same slug already exists
      if (slug !== category.slug) {
        const existingCategory = await HelpCategory.findOne({ slug });
        if (existingCategory) {
          return res.status(400).json({ msg: 'Category with this slug already exists' });
        }
      }
      
      category.name = name;
      category.description = description;
      category.slug = slug;
      category.icon = icon || category.icon;
      category.color = color || category.color;
      category.order = order !== undefined ? order : category.order;
      category.parentCategory = parentCategory || category.parentCategory;
      category.isActive = isActive !== undefined ? isActive : category.isActive;
      category.updatedBy = req.user.id;

      await category.save();
      res.json(category);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Help category not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

/**
 * @route   DELETE /api/help/categories/:id
 * @desc    Delete a help category
 * @access  private
 */
router.delete(
  '/:id',
  [isAuthenticated, hasRoles(['admin', 'content_manager'])],
  async (req, res) => {
    try {
      const category = await HelpCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ msg: 'Help category not found' });
      }
      
      // Check if category has subcategories
      const subcategories = await HelpCategory.find({ parentCategory: req.params.id });
      if (subcategories.length > 0) {
        return res.status(400).json({ 
          msg: 'Cannot delete category with subcategories. Please delete or reassign subcategories first.' 
        });
      }
      
      // Check if category has entries
      const HelpEntry = require('../../models/HelpEntry');
      const entries = await HelpEntry.find({ category: req.params.id });
      if (entries.length > 0) {
        return res.status(400).json({ 
          msg: 'Cannot delete category with entries. Please delete or reassign entries first.' 
        });
      }
      
      await category.remove();
      res.json({ msg: 'Help category removed' });
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Help category not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
);

module.exports = router;