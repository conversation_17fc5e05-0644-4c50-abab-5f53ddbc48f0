/**
 * Simple test script to verify the import of the UnifiProtectAPI class
 * 
 * This script just imports the UnifiProtectAPI class and creates an instance
 * without trying to initialize or use any actual UniFi Protect instances.
 * 
 * Usage:
 * node test-unifi-protect-import.js
 */

// Load environment variables from .env file
require('dotenv').config();

console.log('Testing UnifiProtectAPI import...');

// Import the UnifiProtectAPI class
try {
  console.log('Importing UnifiProtectAPI class...');
  const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');
  console.log('✓ Successfully imported UnifiProtectAPI class');
  
  // Create a new instance of the API
  console.log('Creating UnifiProtectAPI instance...');
  const unifiProtectAPI = new UnifiProtectAPI();
  console.log('✓ Successfully created UnifiProtectAPI instance');
  
  console.log('Test completed successfully!');
} catch (error) {
  console.error('Error during test:', error);
}