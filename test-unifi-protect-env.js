/**
 * Test script for UniFi Protect integration with environment variables
 *
 * This script tests the UniFi Protect integration by:
 * 1. Setting environment variables for multiple UniFi Protect instances
 * 2. Initializing the UniFi Protect API
 * 3. Verifying that the API is using the environment variables correctly
 *
 * Usage:
 * node test-unifi-protect-env.js
 */

// Set environment variables for testing multiple instances
process.env.UNIFI_PROTECT_HOST_A = 'test-host-a';
process.env.UNIFI_PROTECT_HOST_B = 'test-host-b';
process.env.UNIFI_PROTECT_PORT = '443';
process.env.UNIFI_NETWORK_USERNAME = 'network-user';
process.env.UNIFI_NETWORK_PASSWORD = 'network-pass';

// For backward compatibility testing
process.env.UNIFI_PROTECT_HOST = 'test-host-legacy';
process.env.UNIFI_PROTECT_USERNAME = 'test-username-legacy';
process.env.UNIFI_PROTECT_PASSWORD = 'test-password-legacy';

// Import the UniFi Protect API
const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');

// Create a test function
async function testUnifiProtectEnv() {
  console.log('Testing UniFi Protect integration with environment variables...');
  console.log('Environment variables:');
  console.log(`  UNIFI_PROTECT_HOST_A: ${process.env.UNIFI_PROTECT_HOST_A}`);
  console.log(`  UNIFI_PROTECT_HOST_B: ${process.env.UNIFI_PROTECT_HOST_B}`);
  console.log(`  UNIFI_PROTECT_PORT: ${process.env.UNIFI_PROTECT_PORT}`);
  console.log(`  UNIFI_NETWORK_USERNAME: ${process.env.UNIFI_NETWORK_USERNAME}`);
  console.log(`  UNIFI_NETWORK_PASSWORD: [hidden]`);
  console.log(`  UNIFI_PROTECT_HOST: ${process.env.UNIFI_PROTECT_HOST} (legacy)`);
  console.log(`  UNIFI_PROTECT_USERNAME: ${process.env.UNIFI_PROTECT_USERNAME} (legacy)`);
  console.log(`  UNIFI_PROTECT_PASSWORD: [hidden] (legacy)`);

  try {
    // Initialize the API
    const unifiProtectAPI = new UnifiProtectAPI();

    // Give time for async initializeInstances (in constructor) to complete
    await new Promise(r => setTimeout(r, 50));

    // Check if the API was initialized with the correct instances
    console.log('\nAPI initialized with instances:');

    // Check instance A
    if (unifiProtectAPI.instances.A) {
      console.log('Instance A:');
      console.log(`  Host: ${unifiProtectAPI.instances.A.host}`);
      console.log(`  Username: ${unifiProtectAPI.instances.A.username}`);
      console.log(`  Port: ${unifiProtectAPI.instances.A.port}`);

      // Verify that instance A is using the correct environment variables
      if (
        unifiProtectAPI.instances.A.host === process.env.UNIFI_PROTECT_HOST_A &&
        unifiProtectAPI.instances.A.username === process.env.UNIFI_NETWORK_USERNAME &&
        unifiProtectAPI.instances.A.password === process.env.UNIFI_NETWORK_PASSWORD &&
        unifiProtectAPI.instances.A.port.toString() === process.env.UNIFI_PROTECT_PORT
      ) {
        console.log('  ✅ Instance A is correctly configured');
      } else {
        console.log('  ❌ Instance A is NOT correctly configured');
      }
    } else {
      console.log('❌ Instance A not found');
    }

    // Check instance B
    if (unifiProtectAPI.instances.B) {
      console.log('\nInstance B:');
      console.log(`  Host: ${unifiProtectAPI.instances.B.host}`);
      console.log(`  Username: ${unifiProtectAPI.instances.B.username}`);
      console.log(`  Port: ${unifiProtectAPI.instances.B.port}`);

      // Verify that instance B is using the correct environment variables
      if (
        unifiProtectAPI.instances.B.host === process.env.UNIFI_PROTECT_HOST_B &&
        unifiProtectAPI.instances.B.username === process.env.UNIFI_NETWORK_USERNAME &&
        unifiProtectAPI.instances.B.password === process.env.UNIFI_NETWORK_PASSWORD &&
        unifiProtectAPI.instances.B.port.toString() === process.env.UNIFI_PROTECT_PORT
      ) {
        console.log('  ✅ Instance B is correctly configured');
      } else {
        console.log('  ❌ Instance B is NOT correctly configured');
      }
    } else {
      console.log('❌ Instance B not found');
    }

    // Test the _getApiInstance method
    console.log('\nTesting _getApiInstance method:');

    try {
      await unifiProtectAPI._getApiInstance('A');
      console.log('  ✅ Successfully got API instance for Instance A');
    } catch (error) {
      console.log(`  ❌ Failed to get API instance for Instance A: ${error.message}`);
    }

    try {
      await unifiProtectAPI._getApiInstance('B');
      console.log('  ✅ Successfully got API instance for Instance B');
    } catch (error) {
      console.log(`  ❌ Failed to get API instance for Instance B: ${error.message}`);
    }

    console.log('\nTest completed.');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }
}

// Run the test
testUnifiProtectEnv();