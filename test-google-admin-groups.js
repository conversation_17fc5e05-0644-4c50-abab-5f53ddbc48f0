// Test script for the Google Admin groups endpoint
const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set up axios with base URL
const api = axios.create({
  baseURL: 'http://localhost:5000',
  timeout: 10000,
  withCredentials: true
});

// Function to test the Google Admin groups endpoint
async function testGoogleAdminGroups() {
  try {
    console.log('Testing /api/google-admin/groups endpoint...');
    
    // First, we need to authenticate (this assumes you have a test user with admin role)
    // You may need to adjust this based on your authentication setup
    const loginResponse = await api.post('/api/auth/login', {
      email: process.env.TEST_ADMIN_EMAIL,
      password: process.env.TEST_ADMIN_PASSWORD
    });
    
    console.log('Authentication successful');
    
    // Now test the groups endpoint
    const groupsResponse = await api.get('/api/google-admin/groups');
    
    console.log('Groups endpoint response status:', groupsResponse.status);
    console.log('Groups data:', JSON.stringify(groupsResponse.data, null, 2));
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing Google Admin groups endpoint:');
    
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
  }
}

// Run the test
testGoogleAdminGroups();