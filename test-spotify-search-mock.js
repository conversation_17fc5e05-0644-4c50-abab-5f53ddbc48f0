// Mock test script for Spotify search
const WiimAPI = require('./server/integrations/wiim/wiimAPI');

// Create a WiimAPI instance with dummy values
const wiimAPI = new WiimAPI('dummy-host', 80);

// Mock the ensureSpotifyAuthenticated method to avoid authentication errors
wiimAPI.ensureSpotifyAuthenticated = async function() {
  console.log('Mock: Spotify authentication successful');
  return true;
};

// Mock the spotifyAxios.get method to return mock data with null objects
wiimAPI.spotifyAxios = {
  get: async function() {
    console.log('Mock: Spotify API call successful');
    
    // Create mock data with null objects to test our fix
    return {
      data: {
        tracks: {
          items: [
            null, // This null object would cause the error before our fix
            {
              name: 'Test Track',
              album: null // This null album would cause the error before our fix
            },
            {
              name: 'Test Track 2',
              album: {
                name: 'Test Album',
                images: null // This null images array would cause the error before our fix
              }
            },
            {
              name: 'Test Track 3',
              album: {
                name: 'Test Album 2',
                images: [{ url: 'https://example.com/image.jpg' }]
              }
            }
          ]
        },
        albums: {
          items: [
            null, // This null object would cause the error before our fix
            {
              name: 'Test Album',
              images: null // This null images array would cause the error before our fix
            },
            {
              name: 'Test Album 2',
              images: [{ url: 'https://example.com/image.jpg' }]
            }
          ]
        },
        artists: {
          items: [
            null, // This null object would cause the error before our fix
            {
              name: 'Test Artist',
              images: null // This null images array would cause the error before our fix
            },
            {
              name: 'Test Artist 2',
              images: [{ url: 'https://example.com/image.jpg' }]
            }
          ]
        },
        playlists: {
          items: [
            null, // This null object would cause the error before our fix
            {
              name: 'Test Playlist',
              images: null // This null images array would cause the error before our fix
            },
            {
              name: 'Test Playlist 2',
              images: [{ url: 'https://example.com/image.jpg' }]
            }
          ]
        }
      }
    };
  }
};

// Test function to search Spotify with mock data
async function testSpotifySearchWithMockData() {
  try {
    console.log('Testing Spotify search with mock data...');
    
    // Call the searchSpotify method
    const results = await wiimAPI.searchSpotify('test query');
    console.log('Search successful!');
    
    // Check if the results have the expected structure
    if (results.tracks && results.tracks.items) {
      console.log(`Found ${results.tracks.items.length} tracks`);
      
      // Check each track to make sure we can access the images property without errors
      results.tracks.items.forEach((track, index) => {
        console.log(`Track ${index}: album.images is accessible:`, !!track.album.images);
      });
    }
    
    if (results.albums && results.albums.items) {
      console.log(`Found ${results.albums.items.length} albums`);
      
      // Check each album to make sure we can access the images property without errors
      results.albums.items.forEach((album, index) => {
        console.log(`Album ${index}: images is accessible:`, !!album.images);
      });
    }
    
    if (results.artists && results.artists.items) {
      console.log(`Found ${results.artists.items.length} artists`);
      
      // Check each artist to make sure we can access the images property without errors
      results.artists.items.forEach((artist, index) => {
        console.log(`Artist ${index}: images is accessible:`, !!artist.images);
      });
    }
    
    if (results.playlists && results.playlists.items) {
      console.log(`Found ${results.playlists.items.length} playlists`);
      
      // Check each playlist to make sure we can access the images property without errors
      results.playlists.items.forEach((playlist, index) => {
        console.log(`Playlist ${index}: images is accessible:`, !!playlist.images);
      });
    }
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testSpotifySearchWithMockData();