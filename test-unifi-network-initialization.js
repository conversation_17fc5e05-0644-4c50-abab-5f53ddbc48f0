// Test script to verify UniFi Network initialization
require('dotenv').config(); // Load environment variables from .env file

const unifiNetworkController = require('./server/controllers/unifiNetworkController');
const realtimeService = require('./server/services/realtimeService');

async function testUnifiNetworkInitialization() {
  console.log('Testing UniFi Network initialization...');
  
  try {
    // Test direct initialization of the UniFi Network API
    console.log('Testing direct initialization of UniFi Network API...');
    const initResult = await unifiNetworkController.initializeAPI();
    console.log('Direct initialization result:', initResult);
    
    // Test initialization through the realtime service
    console.log('\nTesting initialization through realtime service...');
    await realtimeService.initializeUnifiIntegrations();
    console.log('Realtime service initialization completed successfully');
    
    console.log('\nAll tests passed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testUnifiNetworkInitialization();