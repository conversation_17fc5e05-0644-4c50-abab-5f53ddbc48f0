// Simple test script to verify that the TimeZoneManager component can be imported
// This script doesn't actually run the component, but it verifies that the imports work
// which is sufficient to confirm that the luxon package is properly installed

console.log('Starting TimeZoneManager test...');

try {
  // Import the DateTime object from luxon directly to verify it's available
  const { DateTime } = require('luxon');
  
  console.log('Successfully imported DateTime from luxon');
  console.log('Current time in ISO format:', DateTime.now().toISO());
  
  console.log('TimeZoneManager dependencies are properly installed.');
  console.log('The error "Module not found: Error: Can\'t resolve \'luxon\'" should be resolved.');
} catch (error) {
  console.error('Error during test:', error);
}