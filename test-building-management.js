/**
 * Test script to verify the fix for the building management page
 * This script directly checks the mock data in buildingManagementService.js
 * to ensure that both Lenel S2 NetBox and UniFi Access are shown as active
 */

// Import the necessary modules
const fs = require('fs');
const path = require('path');

// Test function to check if both Lenel S2 NetBox and UniFi Access are active in the mock data
function testBuildingManagementIntegrations() {
  console.log('Testing building management integrations...');
  
  try {
    // Read the buildingManagementService.js file
    const filePath = path.join(__dirname, 'client', 'src', 'services', 'buildingManagementService.js');
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // Check if Lenel S2 NetBox is active in the mock data
    const lenelS2Active = fileContent.includes('lenelS2NetBox: { \n        active: true');
    console.log(`Lenel S2 NetBox active in mock data: ${lenelS2Active}`);
    
    // Check if UniFi Access is active in the mock data
    const unifiAccessActive = fileContent.includes('unifiAccess: { \n        active: true');
    console.log(`UniFi Access active in mock data: ${unifiAccessActive}`);
    
    // Check if at least one access control system is active
    const accessControlActive = lenelS2Active || unifiAccessActive;
    console.log(`At least one access control system active: ${accessControlActive}`);
    
    // Simulate the condition in the BuildingManagementPage.js file
    const showNoAccessControlMessage = !lenelS2Active && !unifiAccessActive;
    console.log(`Show "No access control systems" message: ${showNoAccessControlMessage}`);
    
    if (showNoAccessControlMessage) {
      console.error('ERROR: The "No access control systems" message would still be displayed!');
      process.exit(1);
    } else {
      console.log('SUCCESS: The "No access control systems" message would not be displayed!');
      console.log('The fix has been successfully implemented.');
    }
  } catch (error) {
    console.error('Error testing building management integrations:', error);
    process.exit(1);
  }
}

// Run the test
testBuildingManagementIntegrations();