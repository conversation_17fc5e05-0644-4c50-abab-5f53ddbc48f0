// Test script to verify the expanded Dreo device details
const DreoAPI = require('./server/integrations/dreo/dreoAPI');
require('dotenv').config();

// Get credentials from environment variables
const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
const password = process.env.DREO_PASSWORD;

if (!email || !password) {
  console.error('Error: DREO_EMAIL/DREO_USERNAME and DREO_PASSWORD environment variables must be set');
  process.exit(1);
}

async function testExpandedDeviceDetails() {
  try {
    console.log('Initializing Dreo API with credentials...');
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    console.log('Authenticating with Dreo API...');
    await dreoAPI.authenticate();
    
    // Get devices
    console.log('Fetching devices...');
    const devices = await dreoAPI.getDevices();
    console.log(`Found ${devices.length} devices`);
    
    if (devices.length === 0) {
      console.log('No devices found. Please make sure your Dreo account has devices associated with it.');
      return;
    }
    
    // Get details for the first device
    const deviceId = devices[0].id || devices[0].sn;
    console.log(`Fetching expanded details for device ${deviceId}...`);
    
    const deviceDetails = await dreoAPI.getDeviceDetails(deviceId);
    
    // Check if the expanded details are present
    console.log('\nVerifying expanded device details:');
    
    // Check basic device information
    console.log('\n--- Basic Device Information ---');
    console.log(`Device ID: ${deviceDetails.deviceId || deviceDetails.sn || 'Not found'}`);
    console.log(`Brand: ${deviceDetails.brand || 'Not found'}`);
    console.log(`Model: ${deviceDetails.model || 'Not found'}`);
    console.log(`Product Name: ${deviceDetails.productName || 'Not found'}`);
    console.log(`Device Name: ${deviceDetails.deviceName || 'Not found'}`);
    
    // Check controlsConf
    console.log('\n--- Controls Configuration ---');
    if (deviceDetails.controlsConf) {
      console.log(`Template: ${deviceDetails.controlsConf.template || 'Not found'}`);
      console.log(`Category: ${deviceDetails.controlsConf.category || 'Not found'}`);
      console.log('Controls Configuration is present');
    } else {
      console.log('Controls Configuration not found');
    }
    
    // Check mainConf
    console.log('\n--- Main Configuration ---');
    if (deviceDetails.mainConf) {
      console.log(`WiFi: ${deviceDetails.mainConf.isWifi ? 'Yes' : 'No'}`);
      console.log(`Bluetooth: ${deviceDetails.mainConf.isBluetooth ? 'Yes' : 'No'}`);
      console.log(`Smart Features: ${deviceDetails.mainConf.isSmart ? 'Yes' : 'No'}`);
      console.log(`Voice Control: ${deviceDetails.mainConf.isVoiceControl ? 'Yes' : 'No'}`);
    } else {
      console.log('Main Configuration not found');
    }
    
    // Check resourcesConf
    console.log('\n--- Resources Configuration ---');
    if (deviceDetails.resourcesConf) {
      console.log(`Image Small Source: ${deviceDetails.resourcesConf.imageSmallSrc || 'Not found'}`);
      console.log(`Image Full Source: ${deviceDetails.resourcesConf.imageFullSrc || 'Not found'}`);
    } else {
      console.log('Resources Configuration not found');
    }
    
    // Check servicesConf
    console.log('\n--- Services Configuration ---');
    if (deviceDetails.servicesConf && deviceDetails.servicesConf.length > 0) {
      deviceDetails.servicesConf.forEach((service, index) => {
        console.log(`Service ${index + 1}: ${service.key} = ${service.value}`);
      });
    } else {
      console.log('Services Configuration not found or empty');
    }
    
    // Check userManuals
    console.log('\n--- User Manuals ---');
    if (deviceDetails.userManuals && deviceDetails.userManuals.length > 0) {
      deviceDetails.userManuals.forEach((manual, index) => {
        console.log(`Manual ${index + 1}: ${manual.desc || 'No description'} (${manual.lang || 'en'})`);
        console.log(`URL: ${manual.url || 'No URL'}`);
      });
    } else {
      console.log('User Manuals not found or empty');
    }
    
    // Check device state
    console.log('\n--- Device State ---');
    if (deviceDetails.state) {
      console.log('Device state is present');
      // Print a few key state properties
      const stateKeys = Object.keys(deviceDetails.state);
      if (stateKeys.length > 0) {
        console.log('Sample state properties:');
        stateKeys.slice(0, 5).forEach(key => {
          console.log(`${key}: ${deviceDetails.state[key]}`);
        });
        if (stateKeys.length > 5) {
          console.log(`...and ${stateKeys.length - 5} more properties`);
        }
      } else {
        console.log('Device state is empty');
      }
    } else {
      console.log('Device state not found');
    }
    
    // Check capabilities
    console.log('\n--- Device Capabilities ---');
    if (deviceDetails.capabilities) {
      console.log('Device capabilities are present');
      console.log(deviceDetails.capabilities);
    } else if (deviceDetails.deviceInfo && deviceDetails.deviceInfo.capabilities) {
      console.log('Device capabilities are present in deviceInfo');
      console.log(deviceDetails.deviceInfo.capabilities);
    } else {
      console.log('Device capabilities not found');
    }
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing expanded device details:', error);
  }
}

// Run the test
testExpandedDeviceDetails();