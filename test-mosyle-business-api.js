// Test script for Mosyle Business API
require('dotenv').config();
const MosyleBusinessAPI = require('./server/integrations/mosyleBusiness/mosyleBusinessAPI');

async function testMosyleBusinessAPI() {
  console.log('Testing Mosyle Business API...');
  
  // Get API key and domain from environment variables
  const apiKey = process.env.MOSYLE_BUSINESS_API_KEY;
  const domain = process.env.MOSYLE_BUSINESS_DOMAIN;
  
  console.log(`Using domain: ${domain}`);
  console.log(`API key is ${apiKey ? 'set' : 'not set'}`);
  
  const usingPlaceholderApiKey = !apiKey || apiKey === 'your_mosyle_api_key_here';
  const usingPlaceholderDomain = !domain || domain === 'your_mosyle_subdomain';
  
  if (usingPlaceholderApiKey) {
    console.warn('Warning: Using placeholder API key. Please replace with your actual Mosyle Business API key in .env file.');
    console.warn('You can find your API key in your Mosyle Business account settings.');
  }
  
  if (usingPlaceholderDomain) {
    console.warn('Warning: Using placeholder domain. Please replace with your actual Mosyle Business subdomain in .env file.');
    console.warn('If your Mosyle URL is https://example.mosyle.com, your subdomain is "example".');
  }
  
  // If using placeholder values, don't attempt to make API calls
  if (usingPlaceholderApiKey || usingPlaceholderDomain) {
    console.log('\nSkipping API calls because placeholder values are being used.');
    console.log('Please update the .env file with your actual Mosyle Business credentials and run this test again.');
    return;
  }
  
  try {
    // Initialize the API
    const mosyleBusinessAPI = new MosyleBusinessAPI(apiKey, domain);
    
    // Test the connection by initializing
    await mosyleBusinessAPI.initialize();
    console.log('Mosyle Business API initialized successfully.');
    
    // Try to get devices
    console.log('Attempting to fetch devices...');
    try {
      const devices = await mosyleBusinessAPI.getDevices({ limit: 5 });
      console.log(`Successfully fetched ${devices.length} devices.`);
    } catch (error) {
      console.error('Error fetching devices:', error.message);
    }
    
  } catch (error) {
    console.error('Error testing Mosyle Business API:', error.message);
  }
}

// Run the test
testMosyleBusinessAPI().catch(error => {
  console.error('Unhandled error in test script:', error);
});