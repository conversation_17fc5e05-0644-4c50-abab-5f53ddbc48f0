/**
 * Test script to verify the UniFi Network API fallback endpoints fix
 * 
 * This script tests the updated UniFi Network API wrapper to ensure
 * it can successfully fetch devices using fallback endpoints if the
 * primary endpoint returns a 404 error.
 * 
 * Usage: node test-unifi-network-fallback-endpoints.js
 */

// Load environment variables
require('dotenv').config();

// Import the UniFi Network API wrapper
const UnifiNetworkAPI = require('./server/integrations/unifiNetwork/unifiNetworkAPI');

// Get configuration from environment variables
const host = process.env.UNIFI_NETWORK_HOST || '';
const apiKey = process.env.UNIFI_NETWORK_API_KEY || '';
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITE || 'default';

// Check if required configuration is available
if (!host || !apiKey) {
  console.error('Error: UniFi Network host and API key are required.');
  console.error('Please set the UNIFI_NETWORK_HOST and UNIFI_NETWORK_API_KEY environment variables.');
  process.exit(1);
}

// Create an instance of the UniFi Network API wrapper
const unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Test function to verify the API wrapper can fetch devices using fallback endpoints
async function testGetDevicesWithFallback() {
  try {
    console.log('Testing UniFi Network API getDevices method with fallback endpoints...');
    console.log(`Using host: ${host}, API key: [HIDDEN], port: ${port}, site: ${site}`);
    
    // Initialize the API
    await unifiNetworkAPI.initialize();
    console.log('API initialized successfully.');
    
    // Try to fetch devices
    console.log('Fetching devices...');
    const devices = await unifiNetworkAPI.getDevices();
    
    if (devices && Array.isArray(devices)) {
      console.log(`Success! Found ${devices.length} devices.`);
      console.log('First device:', devices[0] ? JSON.stringify(devices[0], null, 2) : 'No devices found');
      return true;
    } else {
      console.error('Error: Unexpected response format. Expected an array of devices.');
      console.error('Response:', devices);
      return false;
    }
  } catch (error) {
    console.error('Error testing getDevices method with fallback endpoints:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Test function to verify the API endpoint directly
async function testApiEndpoint() {
  try {
    console.log('Testing API endpoint directly...');
    
    // Create a simple HTTP client
    const axios = require('axios');
    const http = axios.create({
      baseURL: `http://localhost:${process.env.PORT || 8080}`,
      timeout: 10000
    });
    
    // Make a request to the API endpoint
    console.log('Making request to /api/unifi-network/devices...');
    const response = await http.get('/api/unifi-network/devices');
    
    console.log('Response status:', response.status);
    console.log('Response data:', response.data.length ? `Found ${response.data.length} devices` : 'No devices found');
    
    return true;
  } catch (error) {
    console.error('Error testing API endpoint directly:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// Run the tests
async function runTests() {
  console.log('Starting UniFi Network API fallback endpoints tests...');
  
  try {
    // Test getDevices method with fallback endpoints
    const devicesResult = await testGetDevicesWithFallback();
    
    // Test API endpoint directly
    const endpointResult = await testApiEndpoint();
    
    // Report overall results
    if (devicesResult && endpointResult) {
      console.log('All tests passed! The UniFi Network API fallback endpoints fix is working correctly.');
    } else {
      console.log('Some tests failed. Please check the error messages above.');
      
      // Report which tests failed
      if (!devicesResult) console.log('- getDevices test failed');
      if (!endpointResult) console.log('- API endpoint test failed');
    }
  } catch (error) {
    console.error('Error running tests:', error.message);
  }
}

// Run the tests
runTests();