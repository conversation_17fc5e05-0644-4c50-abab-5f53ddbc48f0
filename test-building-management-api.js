/**
 * Test script to verify the building management API endpoints
 * This script makes requests to the API endpoints and logs the responses
 */

const axios = require('axios');

// Base URL for API requests
const baseUrl = 'http://localhost:5000'; // Adjust if your server runs on a different port

// Test function to check if the API endpoints are working
async function testBuildingManagementAPI() {
  console.log('Testing building management API endpoints...');
  
  try {
    // Test the system status endpoint
    console.log('\nTesting GET /api/building-management/status');
    try {
      const statusResponse = await axios.get(`${baseUrl}/api/building-management/status`);
      console.log('Status code:', statusResponse.status);
      console.log('Response data:', JSON.stringify(statusResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing status endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the doors endpoint
    console.log('\nTesting GET /api/building-management/doors');
    try {
      const doorsResponse = await axios.get(`${baseUrl}/api/building-management/doors`);
      console.log('Status code:', doorsResponse.status);
      console.log('Response data:', JSON.stringify(doorsResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing doors endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the climate devices endpoint
    console.log('\nTesting GET /api/building-management/climate-devices');
    try {
      const climateResponse = await axios.get(`${baseUrl}/api/building-management/climate-devices`);
      console.log('Status code:', climateResponse.status);
      console.log('Response data:', JSON.stringify(climateResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing climate devices endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the cameras endpoint
    console.log('\nTesting GET /api/building-management/cameras');
    try {
      const camerasResponse = await axios.get(`${baseUrl}/api/building-management/cameras`);
      console.log('Status code:', camerasResponse.status);
      console.log('Response data:', JSON.stringify(camerasResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing cameras endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the network devices endpoint
    console.log('\nTesting GET /api/building-management/network-devices');
    try {
      const networkResponse = await axios.get(`${baseUrl}/api/building-management/network-devices`);
      console.log('Status code:', networkResponse.status);
      console.log('Response data:', JSON.stringify(networkResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing network devices endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the settings endpoint
    console.log('\nTesting GET /api/building-management/settings');
    try {
      const settingsResponse = await axios.get(`${baseUrl}/api/building-management/settings`);
      console.log('Status code:', settingsResponse.status);
      console.log('Response data:', JSON.stringify(settingsResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing settings endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the automation rules endpoint
    console.log('\nTesting GET /api/building-management/automation/rules');
    try {
      const rulesResponse = await axios.get(`${baseUrl}/api/building-management/automation/rules`);
      console.log('Status code:', rulesResponse.status);
      console.log('Response data:', JSON.stringify(rulesResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing automation rules endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the historical data endpoint
    console.log('\nTesting GET /api/building-management/historical-data');
    try {
      const historicalResponse = await axios.get(`${baseUrl}/api/building-management/historical-data`);
      console.log('Status code:', historicalResponse.status);
      console.log('Response data:', JSON.stringify(historicalResponse.data, null, 2));
    } catch (error) {
      console.error('Error testing historical data endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    console.log('\nAPI testing completed.');
    console.log('Note: 401 Unauthorized errors are expected if you are not authenticated.');
    console.log('To test with authentication, you need to include a valid session cookie.');

  } catch (error) {
    console.error('Error testing building management API:', error);
  }
}

// Run the test
testBuildingManagementAPI();