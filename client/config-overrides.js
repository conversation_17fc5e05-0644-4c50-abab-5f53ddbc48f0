const path = require('path');

module.exports = function override(config, env) {
  // Force a single React instance across the app to avoid hook dispatcher mismatches
  // This prevents issues like: TypeError: null is not an object (evaluating 'O.current.useRef')
  config.resolve = config.resolve || {};

  // Remove CRA's ModuleScopePlugin so aliases to node_modules outside src are allowed
  if (config.resolve.plugins) {
    config.resolve.plugins = config.resolve.plugins.filter(
      (plugin) => !(plugin && plugin.constructor && plugin.constructor.name === 'ModuleScopePlugin')
    );
  }

  config.resolve.alias = {
    ...(config.resolve.alias || {}),
    react: path.resolve(__dirname, 'node_modules/react'),
    'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
    'react/jsx-runtime': path.resolve(__dirname, 'node_modules/react/jsx-runtime'),
    'react/jsx-dev-runtime': path.resolve(__dirname, 'node_modules/react/jsx-dev-runtime')
  };

  // Keep devServer override to avoid deprecation warnings while using CRA
  config.devServer = {
    ...(config.devServer || {}),
    setupMiddlewares: (middlewares, devServer) => {
      return middlewares;
    }
  };

  return config;
};
