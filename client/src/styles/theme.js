/**
 * CSF Portal Theme Configuration
 * This file contains the theme configuration for the CSF Portal
 * Based on the branding of www.ukcsf.org
 */

import { createTheme } from '@mui/material/styles';

// CSF brand colors - Light theme (New Modern Design)
const lightColors = {
  primary: {
    main: '#2563eb', // Modern Blue
    light: '#3b82f6',
    dark: '#1e40af',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#1e3a8a', // Deep Blue
    light: '#1e40af',
    dark: '#1e3a8a',
    contrastText: '#FFFFFF',
  },
  tertiary: {
    main: '#4CAF50', // CSF Green
    light: '#6FBF73',
    dark: '#357A38',
    contrastText: '#FFFFFF',
  },
  error: {
    main: '#D32F2F',
    light: '#EF5350',
    dark: '#C62828',
    contrastText: '#FFFFFF',
  },
  warning: {
    main: '#FF9800',
    light: '#FFB74D',
    dark: '#F57C00',
    contrastText: '#000000',
  },
  info: {
    main: '#2196F3',
    light: '#64B5F6',
    dark: '#1976D2',
    contrastText: '#FFFFFF',
  },
  success: {
    main: '#4CAF50',
    light: '#81C784',
    dark: '#388E3C',
    contrastText: '#FFFFFF',
  },
  grey: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#9E9E9E',
  },
  background: {
    default: 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%)',
    paper: 'rgba(255, 255, 255, 0.95)',
    light: '#FAFAFA',
    dark: '#EEEEEE',
  },
  divider: 'rgba(0, 0, 0, 0.12)',
};

// CSF brand colors - Dark theme
const darkColors = {
  primary: {
    main: '#2196F3', // Lighter blue for dark theme
    light: '#64B5F6',
    dark: '#1976D2',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#FFB74D', // Lighter gold for dark theme
    light: '#FFCC80',
    dark: '#FFA726',
    contrastText: '#000000',
  },
  tertiary: {
    main: '#66BB6A', // Lighter green for dark theme
    light: '#81C784',
    dark: '#4CAF50',
    contrastText: '#000000',
  },
  error: {
    main: '#F44336',
    light: '#E57373',
    dark: '#D32F2F',
    contrastText: '#FFFFFF',
  },
  warning: {
    main: '#FFA726',
    light: '#FFB74D',
    dark: '#F57C00',
    contrastText: '#000000',
  },
  info: {
    main: '#29B6F6',
    light: '#4FC3F7',
    dark: '#0288D1',
    contrastText: '#000000',
  },
  success: {
    main: '#66BB6A',
    light: '#81C784',
    dark: '#388E3C',
    contrastText: '#000000',
  },
  grey: {
    50: '#2C2C2C',
    100: '#333333',
    200: '#424242',
    300: '#616161',
    400: '#757575',
    500: '#9E9E9E',
    600: '#BDBDBD',
    700: '#E0E0E0',
    800: '#EEEEEE',
    900: '#F5F5F5',
  },
  text: {
    primary: '#FFFFFF',
    secondary: '#E0E0E0',
    disabled: '#9E9E9E',
  },
  background: {
    default: '#121212',
    paper: '#1E1E1E',
    light: '#2C2C2C',
    dark: '#0A0A0A',
  },
  divider: 'rgba(255, 255, 255, 0.12)',
};

// Use light colors as default
const colors = lightColors;

// CSF typography
const typography = {
  fontFamily: [
    '"Segoe UI"',
    'Tahoma',
    'Geneva',
    'Verdana',
    'sans-serif',
  ].join(','),
  h1: {
    fontWeight: 700,
    fontSize: '2.5rem',
    lineHeight: 1.2,
    letterSpacing: '-0.01562em',
    color: colors.text.primary,
  },
  h2: {
    fontWeight: 700,
    fontSize: '2rem',
    lineHeight: 1.2,
    letterSpacing: '-0.00833em',
    color: colors.text.primary,
  },
  h3: {
    fontWeight: 600,
    fontSize: '1.75rem',
    lineHeight: 1.2,
    letterSpacing: '0em',
    color: colors.text.primary,
  },
  h4: {
    fontWeight: 600,
    fontSize: '1.5rem',
    lineHeight: 1.2,
    letterSpacing: '0.00735em',
    color: colors.text.primary,
  },
  h5: {
    fontWeight: 600,
    fontSize: '1.25rem',
    lineHeight: 1.2,
    letterSpacing: '0em',
    color: colors.text.primary,
  },
  h6: {
    fontWeight: 600,
    fontSize: '1rem',
    lineHeight: 1.2,
    letterSpacing: '0.0075em',
    color: colors.text.primary,
  },
  subtitle1: {
    fontWeight: 400,
    fontSize: '1rem',
    lineHeight: 1.5,
    letterSpacing: '0.00938em',
    color: colors.text.secondary,
  },
  subtitle2: {
    fontWeight: 500,
    fontSize: '0.875rem',
    lineHeight: 1.57,
    letterSpacing: '0.00714em',
    color: colors.text.secondary,
  },
  body1: {
    fontWeight: 400,
    fontSize: '1rem',
    lineHeight: 1.5,
    letterSpacing: '0.00938em',
    color: colors.text.primary,
  },
  body2: {
    fontWeight: 400,
    fontSize: '0.875rem',
    lineHeight: 1.43,
    letterSpacing: '0.01071em',
    color: colors.text.primary,
  },
  button: {
    fontWeight: 500,
    fontSize: '0.875rem',
    lineHeight: 1.75,
    letterSpacing: '0.02857em',
    textTransform: 'uppercase',
  },
  caption: {
    fontWeight: 400,
    fontSize: '0.75rem',
    lineHeight: 1.66,
    letterSpacing: '0.03333em',
    color: colors.text.secondary,
  },
  overline: {
    fontWeight: 400,
    fontSize: '0.75rem',
    lineHeight: 2.66,
    letterSpacing: '0.08333em',
    textTransform: 'uppercase',
    color: colors.text.secondary,
  },
};

// CSF shape
const shape = {
  borderRadius: 12,
};

// CSF shadows
const shadows = [
  'none',
  '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.07),0px 1px 3px 0px rgba(0,0,0,0.06)',
  '0px 3px 1px -2px rgba(0,0,0,0.1),0px 2px 2px 0px rgba(0,0,0,0.07),0px 1px 5px 0px rgba(0,0,0,0.06)',
  '0px 3px 3px -2px rgba(0,0,0,0.1),0px 3px 4px 0px rgba(0,0,0,0.07),0px 1px 8px 0px rgba(0,0,0,0.06)',
  '0px 2px 4px -1px rgba(0,0,0,0.1),0px 4px 5px 0px rgba(0,0,0,0.07),0px 1px 10px 0px rgba(0,0,0,0.06)',
  '0px 3px 5px -1px rgba(0,0,0,0.1),0px 5px 8px 0px rgba(0,0,0,0.07),0px 1px 14px 0px rgba(0,0,0,0.06)',
  '0px 3px 5px -1px rgba(0,0,0,0.1),0px 6px 10px 0px rgba(0,0,0,0.07),0px 1px 18px 0px rgba(0,0,0,0.06)',
  '0px 4px 5px -2px rgba(0,0,0,0.1),0px 7px 10px 1px rgba(0,0,0,0.07),0px 2px 16px 1px rgba(0,0,0,0.06)',
  '0px 5px 5px -3px rgba(0,0,0,0.1),0px 8px 10px 1px rgba(0,0,0,0.07),0px 3px 14px 2px rgba(0,0,0,0.06)',
  '0px 5px 6px -3px rgba(0,0,0,0.1),0px 9px 12px 1px rgba(0,0,0,0.07),0px 3px 16px 2px rgba(0,0,0,0.06)',
  '0px 6px 6px -3px rgba(0,0,0,0.1),0px 10px 14px 1px rgba(0,0,0,0.07),0px 4px 18px 3px rgba(0,0,0,0.06)',
  '0px 6px 7px -4px rgba(0,0,0,0.1),0px 11px 15px 1px rgba(0,0,0,0.07),0px 4px 20px 3px rgba(0,0,0,0.06)',
  '0px 7px 8px -4px rgba(0,0,0,0.1),0px 12px 17px 2px rgba(0,0,0,0.07),0px 5px 22px 4px rgba(0,0,0,0.06)',
  '0px 7px 8px -4px rgba(0,0,0,0.1),0px 13px 19px 2px rgba(0,0,0,0.07),0px 5px 24px 4px rgba(0,0,0,0.06)',
  '0px 7px 9px -4px rgba(0,0,0,0.1),0px 14px 21px 2px rgba(0,0,0,0.07),0px 5px 26px 4px rgba(0,0,0,0.06)',
  '0px 8px 9px -5px rgba(0,0,0,0.1),0px 15px 22px 2px rgba(0,0,0,0.07),0px 6px 28px 5px rgba(0,0,0,0.06)',
  '0px 8px 10px -5px rgba(0,0,0,0.1),0px 16px 24px 2px rgba(0,0,0,0.07),0px 6px 30px 5px rgba(0,0,0,0.06)',
  '0px 8px 11px -5px rgba(0,0,0,0.1),0px 17px 26px 2px rgba(0,0,0,0.07),0px 6px 32px 5px rgba(0,0,0,0.06)',
  '0px 9px 11px -5px rgba(0,0,0,0.1),0px 18px 28px 2px rgba(0,0,0,0.07),0px 7px 34px 6px rgba(0,0,0,0.06)',
  '0px 9px 12px -6px rgba(0,0,0,0.1),0px 19px 29px 2px rgba(0,0,0,0.07),0px 7px 36px 6px rgba(0,0,0,0.06)',
  '0px 10px 13px -6px rgba(0,0,0,0.1),0px 20px 31px 3px rgba(0,0,0,0.07),0px 8px 38px 7px rgba(0,0,0,0.06)',
  '0px 10px 13px -6px rgba(0,0,0,0.1),0px 21px 33px 3px rgba(0,0,0,0.07),0px 8px 40px 7px rgba(0,0,0,0.06)',
  '0px 10px 14px -6px rgba(0,0,0,0.1),0px 22px 35px 3px rgba(0,0,0,0.07),0px 8px 42px 7px rgba(0,0,0,0.06)',
  '0px 11px 14px -7px rgba(0,0,0,0.1),0px 23px 36px 3px rgba(0,0,0,0.07),0px 9px 44px 8px rgba(0,0,0,0.06)',
  '0px 11px 15px -7px rgba(0,0,0,0.1),0px 24px 38px 3px rgba(0,0,0,0.07),0px 9px 46px 8px rgba(0,0,0,0.06)',
];

// Create theme with specified mode and colors
const createAppTheme = (mode = 'light') => {
  const themeColors = mode === 'dark' ? darkColors : lightColors;
  
  return createTheme({
    palette: {
      mode,
      primary: themeColors.primary,
      secondary: themeColors.secondary,
      error: themeColors.error,
      warning: themeColors.warning,
      info: themeColors.info,
      success: themeColors.success,
      grey: themeColors.grey,
      text: themeColors.text,
      background: themeColors.background,
      divider: themeColors.divider,
    },
    typography,
    shape,
    shadows,
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: shape.borderRadius,
            textTransform: 'none',
            fontWeight: 600,
            transition: 'all 0.3s ease',
          },
          contained: {
            background: 'linear-gradient(135deg, #2563eb, #1e40af)',
            boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',
            '&:hover': {
              background: 'linear-gradient(135deg, #1e40af, #1e3a8a)',
              boxShadow: '0 6px 20px rgba(37, 99, 235, 0.4)',
              transform: 'translateY(-2px)',
            },
          },
          containedPrimary: {
            background: 'linear-gradient(135deg, #2563eb, #1e40af)',
            '&:hover': {
              background: 'linear-gradient(135deg, #1e40af, #1e3a8a)',
            },
          },
          containedSecondary: {
            background: 'linear-gradient(135deg, #1e3a8a, #1e40af)',
            '&:hover': {
              background: 'linear-gradient(135deg, #1e40af, #2563eb)',
            },
          },
          outlined: {
            borderWidth: 2,
            '&:hover': {
              borderWidth: 2,
              transform: 'translateY(-1px)',
            },
          },
          outlinedPrimary: {
            borderColor: themeColors.primary.main,
            '&:hover': {
              borderColor: themeColors.primary.dark,
              background: 'rgba(37, 99, 235, 0.1)',
            },
          },
          outlinedSecondary: {
            borderColor: themeColors.secondary.main,
            '&:hover': {
              borderColor: themeColors.secondary.dark,
              background: 'rgba(30, 58, 138, 0.1)',
            },
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            boxShadow: shadows[4],
          },
          colorPrimary: {
            background: 'linear-gradient(rgb(98, 161, 255), rgb(23, 101, 217))',
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: shape.borderRadius,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(10px)',
            background: 'rgba(255, 255, 255, 0.95)',
            '&:hover': {
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
              transform: 'translateY(-2px)',
              transition: 'all 0.3s ease',
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          rounded: {
            borderRadius: shape.borderRadius * 2,
          },
          elevation1: {
            boxShadow: shadows[1],
          },
          elevation2: {
            boxShadow: shadows[2],
          },
          elevation3: {
            boxShadow: shadows[3],
          },
          elevation4: {
            boxShadow: shadows[4],
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            padding: '16px',
          },
          head: {
            fontWeight: 600,
            backgroundColor: themeColors.background.light,
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: shape.borderRadius,
          },
        },
      },
    },
  });
};

// Create light and dark themes
const lightTheme = createAppTheme('light');
const darkTheme = createAppTheme('dark');

// Default theme is light
const theme = lightTheme;

export default theme;

// Export themes and theme elements for use in custom components
export { lightTheme, darkTheme, lightColors, darkColors, colors, typography, shape, shadows, createAppTheme };