/* CSF Portal Responsive Styles
 * This file contains mobile-first responsive styles for the CSF Portal
 * Based on the wireframes document
 */

/* Base styles (mobile first) */
:root {
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* Ensure all interactive elements are touch-friendly on mobile */
button, 
.MuiButtonBase-root,
.MuiIconButton-root,
a.clickable,
input[type="checkbox"],
input[type="radio"],
.interactive {
  min-height: 44px;
  min-width: 44px;
}

/* Layout containers */
.container {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

/* Grid system - mobile first (1 column) */
.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

/* Card layouts */
.card-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.card {
  padding: var(--spacing-md);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Navigation - mobile */
.mobile-nav {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.mobile-nav-item {
  flex: 1;
  text-align: center;
  padding: var(--spacing-sm) 0;
}

.sidebar-nav {
  display: none; /* Hidden on mobile */
}

.hamburger-menu {
  display: block; /* Shown on mobile */
}

/* Tables - mobile friendly */
.responsive-table {
  width: 100%;
  overflow-x: auto;
}

.card-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Progressive disclosure */
.collapsible-section {
  margin-bottom: var(--spacing-md);
}

.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: #f5f5f5;
  cursor: pointer;
}

.collapsible-content {
  padding: var(--spacing-md);
  border: 1px solid #f5f5f5;
}

/* Form elements - mobile friendly */
input, select, textarea {
  width: 100%;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* Tablet styles (≥768px) */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
  
  /* Grid system - 2 columns for tablet */
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Navigation - tablet */
  .mobile-nav {
    display: none; /* Hidden on tablet */
  }
  
  .sidebar-nav {
    display: block; /* Shown on tablet */
    width: 64px; /* Collapsed with icons only */
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    background-color: #f5f5f5;
    overflow-x: hidden;
    transition: width 0.3s;
    z-index: 1000;
  }
  
  .sidebar-nav:hover {
    width: 240px; /* Expanded on hover */
  }
  
  .sidebar-nav-text {
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .sidebar-nav:hover .sidebar-nav-text {
    opacity: 1;
  }
  
  .hamburger-menu {
    display: none; /* Hidden on tablet */
  }
  
  /* Tables - more columns for tablet */
  .responsive-table {
    overflow-x: auto;
  }
  
  /* Card list to grid for tablet */
  .card-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }
}

/* Desktop styles (≥1024px) */
@media (min-width: 1024px) {
  .container {
    max-width: 960px;
  }
  
  /* Grid system - 3 columns for desktop */
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .card-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* Navigation - desktop */
  .sidebar-nav {
    width: 240px; /* Always expanded on desktop */
  }
  
  .sidebar-nav-text {
    opacity: 1; /* Always visible on desktop */
  }
  
  /* Tables - full view for desktop */
  .responsive-table {
    overflow-x: visible;
  }
  
  /* Card list to grid for desktop */
  .card-list {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* Two-column layout for certain pages */
  .two-column-layout {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: var(--spacing-lg);
  }
  
  /* Split view layout */
  .split-view {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
  }
}

/* Large desktop styles (≥1280px) */
@media (min-width: 1280px) {
  .container {
    max-width: 1200px;
  }
  
  /* Grid system - 4 columns for large desktop */
  .grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  /* Card list to grid for large desktop */
  .card-list {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-color: #000;
    --background-color: #fff;
    --link-color: #00f;
    --border-color: #000;
  }
  
  * {
    border-color: var(--border-color) !important;
  }
  
  a, button, .clickable {
    text-decoration: underline !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
}

/* Utility classes for responsive behavior */
.hide-on-mobile {
  display: none;
}

@media (min-width: 768px) {
  .hide-on-mobile {
    display: initial;
  }
  
  .hide-on-tablet {
    display: none;
  }
}

@media (min-width: 1024px) {
  .hide-on-tablet {
    display: initial;
  }
  
  .hide-on-desktop {
    display: none;
  }
}

/* Lazy loading for images */
.lazy-image {
  opacity: 0;
  transition: opacity 0.3s;
}

.lazy-image.loaded {
  opacity: 1;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

/* Skip to content link for keyboard users */
.skip-to-content {
  position: absolute;
  left: -9999px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.skip-to-content:focus {
  position: fixed;
  top: 0;
  left: 0;
  width: auto;
  height: auto;
  padding: var(--spacing-md);
  background-color: #fff;
  z-index: 9999;
}