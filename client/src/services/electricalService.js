import axios from 'axios';

const API_BASE_URL = '/api/electrical';

class ElectricalService {
  // Panels
  async getPanels(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/panels`, { params });
    return res.data;
  }

  async getPanel(id) {
    const res = await axios.get(`${API_BASE_URL}/panels/${id}`);
    return res.data;
  }

  async createPanel(data) {
    const res = await axios.post(`${API_BASE_URL}/panels`, data);
    return res.data;
  }

  async updatePanel(id, data) {
    const res = await axios.put(`${API_BASE_URL}/panels/${id}`, data);
    return res.data;
  }

  async deletePanel(id, { force = false } = {}) {
    const res = await axios.delete(`${API_BASE_URL}/panels/${id}`, { params: { force } });
    return res.data;
  }

  // Circuits
  async getCircuitsByPanel(panelId, params = {}) {
    const res = await axios.get(`${API_BASE_URL}/panels/${panelId}/circuits`, { params });
    return res.data;
  }

  async getCircuits(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/circuits`, { params });
    return res.data;
  }

  async createCircuit(panelId, data) {
    const res = await axios.post(`${API_BASE_URL}/panels/${panelId}/circuits`, data);
    return res.data;
  }

  async updateCircuit(circuitId, data) {
    const res = await axios.put(`${API_BASE_URL}/circuits/${circuitId}`, data);
    return res.data;
  }

  async deleteCircuit(circuitId) {
    const res = await axios.delete(`${API_BASE_URL}/circuits/${circuitId}`);
    return res.data;
  }

  // Outlets
  async getOutlets(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/outlets`, { params });
    return res.data;
  }

  async getOutlet(id) {
    const res = await axios.get(`${API_BASE_URL}/outlets/${id}`);
    return res.data;
  }

  async createOutlet(data) {
    const res = await axios.post(`${API_BASE_URL}/outlets`, data);
    return res.data;
  }

  async updateOutlet(id, data) {
    const res = await axios.put(`${API_BASE_URL}/outlets/${id}`, data);
    return res.data;
  }

  async deleteOutlet(id) {
    const res = await axios.delete(`${API_BASE_URL}/outlets/${id}`);
    return res.data;
  }

  // Trace
  async traceOutlet(params = {}) {
    // expects { outletId } or { label }
    const res = await axios.get(`${API_BASE_URL}/trace`, { params });
    return res.data;
  }
}

const electricalService = new ElectricalService();
export default electricalService;
