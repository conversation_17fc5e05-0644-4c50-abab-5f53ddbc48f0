import axios from 'axios';

/**
 * Help System API Service
 * Handles client-side API calls to the Help System endpoints
 */
const helpService = {
  /**
   * Get all help entries
   * @returns {Promise<Array>} List of help entries
   */
  getEntries: async () => {
    try {
      const response = await axios.get('/api/help/entries');
      return response.data;
    } catch (error) {
      console.error('Error fetching help entries:', error);
      throw error;
    }
  },

  /**
   * Get help entry by ID
   * @param {string} id Entry ID
   * @returns {Promise<Object>} Help entry details
   */
  getEntry: async (id) => {
    try {
      const response = await axios.get(`/api/help/entries/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching help entry with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Search help entries
   * @param {string} query Search query
   * @returns {Promise<Array>} Search results
   */
  searchEntries: async (query) => {
    try {
      const response = await axios.get(`/api/help/entries/search?query=${encodeURIComponent(query)}`);
      return response.data;
    } catch (error) {
      console.error('Error searching help entries:', error);
      throw error;
    }
  },

  /**
   * Get help entries by category
   * @param {string} categoryId Category ID
   * @returns {Promise<Array>} List of help entries in the category
   */
  getEntriesByCategory: async (categoryId) => {
    try {
      const response = await axios.get(`/api/help/entries/category/${categoryId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching help entries for category ${categoryId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new help entry
   * @param {Object} entryData Entry data
   * @returns {Promise<Object>} Created entry
   */
  createEntry: async (entryData) => {
    try {
      const response = await axios.post('/api/help/entries', entryData);
      return response.data;
    } catch (error) {
      console.error('Error creating help entry:', error);
      throw error;
    }
  },

  /**
   * Update a help entry
   * @param {string} id Entry ID
   * @param {Object} entryData Updated entry data
   * @returns {Promise<Object>} Updated entry
   */
  updateEntry: async (id, entryData) => {
    try {
      const response = await axios.put(`/api/help/entries/${id}`, entryData);
      return response.data;
    } catch (error) {
      console.error(`Error updating help entry with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a help entry
   * @param {string} id Entry ID
   * @returns {Promise<Object>} Response message
   */
  deleteEntry: async (id) => {
    try {
      const response = await axios.delete(`/api/help/entries/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting help entry with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Mark a help entry as helpful
   * @param {string} id Entry ID
   * @returns {Promise<Object>} Updated helpful count
   */
  markHelpful: async (id) => {
    try {
      const response = await axios.put(`/api/help/entries/${id}/helpful`);
      return response.data;
    } catch (error) {
      console.error(`Error marking help entry ${id} as helpful:`, error);
      throw error;
    }
  },

  /**
   * Mark a help entry as unhelpful
   * @param {string} id Entry ID
   * @returns {Promise<Object>} Updated unhelpful count
   */
  markUnhelpful: async (id) => {
    try {
      const response = await axios.put(`/api/help/entries/${id}/unhelpful`);
      return response.data;
    } catch (error) {
      console.error(`Error marking help entry ${id} as unhelpful:`, error);
      throw error;
    }
  },

  /**
   * Get all help categories
   * @returns {Promise<Array>} List of help categories
   */
  getCategories: async () => {
    try {
      const response = await axios.get('/api/help/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching help categories:', error);
      throw error;
    }
  },

  /**
   * Get help categories in a tree structure
   * @returns {Promise<Array>} Tree of help categories
   */
  getCategoryTree: async () => {
    try {
      const response = await axios.get('/api/help/categories/tree');
      return response.data;
    } catch (error) {
      console.error('Error fetching help category tree:', error);
      throw error;
    }
  },

  /**
   * Get help category by ID
   * @param {string} id Category ID
   * @returns {Promise<Object>} Help category details
   */
  getCategory: async (id) => {
    try {
      const response = await axios.get(`/api/help/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching help category with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create a new help category
   * @param {Object} categoryData Category data
   * @returns {Promise<Object>} Created category
   */
  createCategory: async (categoryData) => {
    try {
      const response = await axios.post('/api/help/categories', categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating help category:', error);
      throw error;
    }
  },

  /**
   * Update a help category
   * @param {string} id Category ID
   * @param {Object} categoryData Updated category data
   * @returns {Promise<Object>} Updated category
   */
  updateCategory: async (id, categoryData) => {
    try {
      const response = await axios.put(`/api/help/categories/${id}`, categoryData);
      return response.data;
    } catch (error) {
      console.error(`Error updating help category with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a help category
   * @param {string} id Category ID
   * @returns {Promise<Object>} Response message
   */
  deleteCategory: async (id) => {
    try {
      const response = await axios.delete(`/api/help/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting help category with ID ${id}:`, error);
      throw error;
    }
  }
};

export default helpService;