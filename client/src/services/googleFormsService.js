import axios from 'axios';

/**
 * Google Forms API Service
 * Handles client-side API calls to the Google Forms endpoints
 */
const googleFormsService = {
  /**
   * Save Google Forms configuration
   * @param {Object} config Configuration object with clientId, clientSecret, redirectUri, and tokenPath
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/google-forms/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Google Forms configuration:', error);
      throw error;
    }
  },

  /**
   * Get Google Forms configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/google-forms/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Google Forms configuration:', error);
      throw error;
    }
  },

  /**
   * Get authentication URL for OAuth2 flow
   * @returns {Promise<Object>} Authentication information including URL and status
   */
  getAuthUrl: async () => {
    try {
      const response = await axios.get('/api/google-forms/auth-url');
      // Return the full response data which may include userAuth status and different URLs
      return response.data;
    } catch (error) {
      console.error('Error getting Google Forms auth URL:', error);
      throw error;
    }
  },

  /**
   * List forms in Google Forms
   * @returns {Promise<Array>} List of forms
   */
  listForms: async () => {
    try {
      const response = await axios.get('/api/google-forms/forms');
      return response.data;
    } catch (error) {
      console.error('Error listing forms from Google Forms:', error);
      throw error;
    }
  },

  /**
   * Get form details
   * @param {string} formId Form ID
   * @returns {Promise<Object>} Form details
   */
  getForm: async (formId) => {
    try {
      const response = await axios.get(`/api/google-forms/forms/${formId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting form from Google Forms:', error);
      throw error;
    }
  },

  /**
   * Get form responses
   * @param {string} formId Form ID
   * @returns {Promise<Object>} Form responses
   */
  getFormResponses: async (formId) => {
    try {
      const response = await axios.get(`/api/google-forms/forms/${formId}/responses`);
      return response.data;
    } catch (error) {
      console.error('Error getting form responses from Google Forms:', error);
      throw error;
    }
  },

  /**
   * Create a new form
   * @param {Object} formData Form data
   * @returns {Promise<Object>} Created form
   */
  createForm: async (formData) => {
    try {
      const response = await axios.post('/api/google-forms/forms', formData);
      return response.data;
    } catch (error) {
      console.error('Error creating form in Google Forms:', error);
      throw error;
    }
  },

  /**
   * Update an existing form
   * @param {string} formId Form ID
   * @param {Object} formData Form data
   * @returns {Promise<Object>} Updated form
   */
  updateForm: async (formId, formData) => {
    try {
      const response = await axios.put(`/api/google-forms/forms/${formId}`, formData);
      return response.data;
    } catch (error) {
      console.error('Error updating form in Google Forms:', error);
      throw error;
    }
  }
};

export default googleFormsService;