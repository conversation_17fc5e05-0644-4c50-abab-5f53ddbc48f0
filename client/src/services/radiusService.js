import axios from 'axios';

/**
 * RADIUS API Service
 * Handles client-side API calls to the RADIUS endpoints
 */
const radiusService = {
  /**
   * Get RADIUS server status
   * @returns {Promise<Object>} Server status
   */
  getStatus: async () => {
    try {
      const response = await axios.get('/api/radius/status');
      return response.data;
    } catch (error) {
      console.error('Error getting RADIUS server status:', error);
      throw error;
    }
  },

  /**
   * Get RADIUS server configuration
   * @returns {Promise<Object>} Server configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/radius/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error getting RADIUS server configuration:', error);
      throw error;
    }
  },

  /**
   * Update RADIUS server configuration
   * @param {Object} config Configuration object
   * @returns {Promise<Object>} Updated configuration
   */
  updateConfig: async (config) => {
    try {
      const response = await axios.post('/api/radius/config', config);
      return response.data;
    } catch (error) {
      console.error('Error updating RADIUS server configuration:', error);
      throw error;
    }
  },

  /**
   * Configure RADIUS with Google SAML
   * @param {Object} samlConfig SAML configuration
   * @returns {Promise<Object>} Configuration result
   */
  configureSaml: async (samlConfig) => {
    try {
      const response = await axios.post('/api/radius/configure-saml', { samlConfig });
      return response.data;
    } catch (error) {
      console.error('Error configuring RADIUS with SAML:', error);
      throw error;
    }
  },

  /**
   * Get Google auth URL (for Google API auth)
   * @returns {Promise<Object>} Auth URL
   */
  getGoogleAuthUrl: async () => {
    try {
      const response = await axios.get('/api/radius/google-auth-url');
      return response.data;
    } catch (error) {
      console.error('Error getting Google auth URL:', error);
      throw error;
    }
  },

  /**
   * Handle Google auth callback (for Google API auth)
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Auth result
   */
  handleGoogleAuthCallback: async (code) => {
    try {
      const response = await axios.post('/api/radius/google-auth-callback', { code });
      return response.data;
    } catch (error) {
      console.error('Error handling Google auth callback:', error);
      throw error;
    }
  },

  /**
   * Get RADIUS clients
   * @returns {Promise<Array>} List of clients
   */
  getClients: async () => {
    try {
      const response = await axios.get('/api/radius/clients');
      return response.data;
    } catch (error) {
      console.error('Error getting RADIUS clients:', error);
      throw error;
    }
  },

  /**
   * Add a new RADIUS client
   * @param {Object} client Client configuration
   * @returns {Promise<Object>} Created client
   */
  addClient: async (client) => {
    try {
      const response = await axios.post('/api/radius/clients', client);
      return response.data;
    } catch (error) {
      console.error('Error adding RADIUS client:', error);
      throw error;
    }
  },

  /**
   * Update a RADIUS client
   * @param {string} clientId Client ID
   * @param {Object} client Client configuration
   * @returns {Promise<Object>} Updated client
   */
  updateClient: async (clientId, client) => {
    try {
      const response = await axios.put(`/api/radius/clients/${clientId}`, client);
      return response.data;
    } catch (error) {
      console.error('Error updating RADIUS client:', error);
      throw error;
    }
  },

  /**
   * Delete a RADIUS client
   * @param {string} clientId Client ID
   * @returns {Promise<Object>} Response message
   */
  deleteClient: async (clientId) => {
    try {
      const response = await axios.delete(`/api/radius/clients/${clientId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting RADIUS client:', error);
      throw error;
    }
  },

  /**
   * Get authentication logs
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of authentication logs
   */
  getAuthLogs: async (params = {}) => {
    try {
      const response = await axios.get('/api/radius/logs/auth', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting RADIUS authentication logs:', error);
      throw error;
    }
  },

  /**
   * Test RADIUS server connection
   * @returns {Promise<Object>} Connection test result
   */
  testConnection: async () => {
    try {
      const response = await axios.post('/api/radius/test-connection');
      return response.data;
    } catch (error) {
      console.error('Error testing RADIUS server connection:', error);
      throw error;
    }
  },

  /**
   * Get VLAN configurations
   * @returns {Promise<Array>} List of VLAN configurations
   */
  getVlanConfigs: async () => {
    try {
      const response = await axios.get('/api/radius/vlans');
      return response.data;
    } catch (error) {
      console.error('Error getting VLAN configurations:', error);
      throw error;
    }
  },

  /**
   * Add a new VLAN configuration
   * @param {Object} vlanConfig VLAN configuration
   * @returns {Promise<Object>} Created VLAN configuration
   */
  addVlanConfig: async (vlanConfig) => {
    try {
      const response = await axios.post('/api/radius/vlans', vlanConfig);
      return response.data;
    } catch (error) {
      console.error('Error adding VLAN configuration:', error);
      throw error;
    }
  },

  /**
   * Update a VLAN configuration
   * @param {string} vlanId VLAN ID
   * @param {Object} vlanConfig VLAN configuration
   * @returns {Promise<Object>} Updated VLAN configuration
   */
  updateVlanConfig: async (vlanId, vlanConfig) => {
    try {
      const response = await axios.put(`/api/radius/vlans/${vlanId}`, vlanConfig);
      return response.data;
    } catch (error) {
      console.error('Error updating VLAN configuration:', error);
      throw error;
    }
  },

  /**
   * Delete a VLAN configuration
   * @param {string} vlanId VLAN ID
   * @returns {Promise<Object>} Response message
   */
  deleteVlanConfig: async (vlanId) => {
    try {
      const response = await axios.delete(`/api/radius/vlans/${vlanId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting VLAN configuration:', error);
      throw error;
    }
  },

  /**
   * Get group-to-VLAN mappings
   * @returns {Promise<Array>} List of group-to-VLAN mappings
   */
  getGroupVlanMappings: async () => {
    try {
      const response = await axios.get('/api/radius/group-vlan-mappings');
      return response.data;
    } catch (error) {
      console.error('Error getting group-to-VLAN mappings:', error);
      throw error;
    }
  },

  /**
   * Add a new group-to-VLAN mapping
   * @param {Object} mapping Group-to-VLAN mapping
   * @returns {Promise<Object>} Created mapping
   */
  addGroupVlanMapping: async (mapping) => {
    try {
      const response = await axios.post('/api/radius/group-vlan-mappings', mapping);
      return response.data;
    } catch (error) {
      console.error('Error adding group-to-VLAN mapping:', error);
      throw error;
    }
  },

  /**
   * Update a group-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @param {Object} mapping Group-to-VLAN mapping
   * @returns {Promise<Object>} Updated mapping
   */
  updateGroupVlanMapping: async (mappingId, mapping) => {
    try {
      const response = await axios.put(`/api/radius/group-vlan-mappings/${mappingId}`, mapping);
      return response.data;
    } catch (error) {
      console.error('Error updating group-to-VLAN mapping:', error);
      throw error;
    }
  },

  /**
   * Delete a group-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @returns {Promise<Object>} Response message
   */
  deleteGroupVlanMapping: async (mappingId) => {
    try {
      const response = await axios.delete(`/api/radius/group-vlan-mappings/${mappingId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting group-to-VLAN mapping:', error);
      throw error;
    }
  },

  /**
   * Get user-to-VLAN mappings
   * @returns {Promise<Array>} List of user-to-VLAN mappings
   */
  getUserVlanMappings: async () => {
    try {
      const response = await axios.get('/api/radius/user-vlan-mappings');
      return response.data;
    } catch (error) {
      console.error('Error getting user-to-VLAN mappings:', error);
      throw error;
    }
  },

  /**
   * Add a new user-to-VLAN mapping
   * @param {Object} mapping User-to-VLAN mapping
   * @returns {Promise<Object>} Created mapping
   */
  addUserVlanMapping: async (mapping) => {
    try {
      const response = await axios.post('/api/radius/user-vlan-mappings', mapping);
      return response.data;
    } catch (error) {
      console.error('Error adding user-to-VLAN mapping:', error);
      throw error;
    }
  },

  /**
   * Update a user-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @param {Object} mapping User-to-VLAN mapping
   * @returns {Promise<Object>} Updated mapping
   */
  updateUserVlanMapping: async (mappingId, mapping) => {
    try {
      const response = await axios.put(`/api/radius/user-vlan-mappings/${mappingId}`, mapping);
      return response.data;
    } catch (error) {
      console.error('Error updating user-to-VLAN mapping:', error);
      throw error;
    }
  },

  /**
   * Delete a user-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @returns {Promise<Object>} Response message
   */
  deleteUserVlanMapping: async (mappingId) => {
    try {
      const response = await axios.delete(`/api/radius/user-vlan-mappings/${mappingId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting user-to-VLAN mapping:', error);
      throw error;
    }
  },

  /**
   * Get VLAN for a user
   * @param {string} username User's username
   * @returns {Promise<Object>} User's VLAN information
   */
  getUserVlan: async (username) => {
    try {
      const response = await axios.get(`/api/radius/users/${username}/vlan`);
      return response.data;
    } catch (error) {
      console.error('Error getting user VLAN:', error);
      throw error;
    }
  }
};

export default radiusService;
