/**
 * Shared Service Utilities
 * Common helper functions to reduce code duplication across services
 */

/**
 * Standard API request wrapper with error handling
 */
export const apiRequest = async (axiosInstance, method, url, data = null, config = {}) => {
  try {
    const response = await axiosInstance({
      method,
      url,
      data,
      ...config
    });
    return response.data;
  } catch (error) {
    const errorMessage = error.response?.data?.message || error.message || 'Request failed';
    console.error(`API request failed: ${method.toUpperCase()} ${url}`, errorMessage);
    throw new Error(errorMessage);
  }
};

/**
 * Standardized error handling for services
 */
export const handleServiceError = (operation, error, context = {}) => {
  const errorContext = {
    operation,
    timestamp: new Date().toISOString(),
    ...context
  };
  
  console.error(`Service error in ${operation}:`, error, errorContext);
  
  // Return standardized error object
  return {
    success: false,
    error: error.message || 'Unknown error occurred',
    operation,
    context: errorContext
  };
};

/**
 * Common pagination helper
 */
export const paginateResults = (data, page = 1, limit = 50) => {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return {
    items: data.slice(startIndex, endIndex),
    pagination: {
      page,
      limit,
      total: data.length,
      totalPages: Math.ceil(data.length / limit),
      hasNext: endIndex < data.length,
      hasPrevious: startIndex > 0
    }
  };
};

/**
 * Generic filter function for arrays of objects
 */
export const filterData = (data, filters = {}) => {
  if (!Array.isArray(data) || Object.keys(filters).length === 0) {
    return data;
  }

  return data.filter(item => {
    return Object.entries(filters).every(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        return true; // Skip empty filters
      }

      const itemValue = getNestedProperty(item, key);
      
      if (typeof value === 'string') {
        return itemValue && itemValue.toString().toLowerCase().includes(value.toLowerCase());
      }
      
      if (Array.isArray(value)) {
        return value.includes(itemValue);
      }
      
      return itemValue === value;
    });
  });
};

/**
 * Get nested property from object using dot notation
 */
export const getNestedProperty = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

/**
 * Set nested property in object using dot notation
 */
export const setNestedProperty = (obj, path, value) => {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
  return obj;
};

/**
 * Standard sorting function
 */
export const sortData = (data, sortBy, sortOrder = 'asc') => {
  if (!Array.isArray(data) || !sortBy) {
    return data;
  }

  return [...data].sort((a, b) => {
    const aValue = getNestedProperty(a, sortBy);
    const bValue = getNestedProperty(b, sortBy);

    if (aValue === bValue) return 0;
    
    const comparison = aValue > bValue ? 1 : -1;
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};

/**
 * Debounce function for reducing API calls
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Cache wrapper for expensive operations
 */
export class ServiceCache {
  constructor(defaultTTL = 300000) { // 5 minutes default
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  set(key, data, ttl = this.defaultTTL) {
    this.cache.set(key, {
      data,
      expires: Date.now() + ttl
    });
  }

  clear() {
    this.cache.clear();
  }

  has(key) {
    return this.get(key) !== null;
  }
}

/**
 * Retry mechanism for failed requests
 */
export const retryRequest = async (fn, maxRetries = 3, delay = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw lastError;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
};

/**
 * Batch processor for handling multiple operations
 */
export const batchProcess = async (items, processor, batchSize = 10, delay = 100) => {
  const results = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.allSettled(
      batch.map(item => processor(item))
    );
    
    results.push(...batchResults);
    
    // Add delay between batches to prevent overwhelming the API
    if (i + batchSize < items.length && delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return results;
};

/**
 * Location utilities for building/floor filtering
 */
export const locationUtils = {
  filterByLocation: (items, buildingId = null, floorId = null) => {
    return items.filter(item => {
      if (buildingId && item.buildingId !== buildingId) return false;
      if (floorId && item.floorId !== floorId) return false;
      return true;
    });
  },

  groupByBuilding: (items) => {
    return items.reduce((groups, item) => {
      const buildingId = item.buildingId || 'unknown';
      if (!groups[buildingId]) {
        groups[buildingId] = [];
      }
      groups[buildingId].push(item);
      return groups;
    }, {});
  },

  groupByFloor: (items) => {
    return items.reduce((groups, item) => {
      const floorKey = `${item.buildingId}_${item.floorId}`;
      if (!groups[floorKey]) {
        groups[floorKey] = [];
      }
      groups[floorKey].push(item);
      return groups;
    }, {});
  }
};

/**
 * Status utilities for device management
 */
export const statusUtils = {
  normalizeStatus: (status) => {
    if (typeof status !== 'string') return 'unknown';
    
    const normalized = status.toLowerCase();
    
    // Map various status values to standard ones
    const statusMap = {
      'online': 'online',
      'connected': 'online',
      'active': 'online',
      '1': 'online',
      'true': 'online',
      'offline': 'offline',
      'disconnected': 'offline',
      'inactive': 'offline',
      '0': 'offline',
      'false': 'offline',
      'error': 'error',
      'failed': 'error',
      'warning': 'warning',
      'maintenance': 'maintenance'
    };
    
    return statusMap[normalized] || 'unknown';
  },

  getStatusColor: (status) => {
    const colors = {
      'online': '#4caf50',
      'offline': '#f44336',
      'error': '#ff5722',
      'warning': '#ff9800',
      'maintenance': '#2196f3',
      'unknown': '#9e9e9e'
    };
    
    return colors[status] || colors.unknown;
  },

  getStatusIcon: (status) => {
    const icons = {
      'online': 'check_circle',
      'offline': 'error',
      'error': 'warning',
      'warning': 'info',
      'maintenance': 'build',
      'unknown': 'help'
    };
    
    return icons[status] || icons.unknown;
  }
};

/**
 * Data validation utilities
 */
export const validationUtils = {
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidPhone: (phone) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  },

  isValidUrl: (url) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  sanitizeString: (str) => {
    if (typeof str !== 'string') return str;
    return str.trim().replace(/[<>]/g, '');
  }
};

export default {
  apiRequest,
  handleServiceError,
  paginateResults,
  filterData,
  getNestedProperty,
  setNestedProperty,
  sortData,
  debounce,
  ServiceCache,
  retryRequest,
  batchProcess,
  locationUtils,
  statusUtils,
  validationUtils
};