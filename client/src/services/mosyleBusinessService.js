import axios from 'axios';

/**
 * Mosyle Business API Service
 * Handles client-side API calls to the Mosyle Business endpoints
 */
const mosyleBusinessService = {
  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async (params = {}) => {
    try {
      const response = await axios.get('/api/mosyleBusiness/devices', { params });
      // Ensure we always return an array, even if the API returns something else
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error fetching Mosyle devices:', error);
      // Return empty array on error instead of throwing
      return [];
    }
  },

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Device details
   */
  getDevice: async (deviceId, params = {}) => {
    try {
      const response = await axios.get(`/api/mosyleBusiness/devices/${deviceId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Mosyle device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get all users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axios.get('/api/mosyleBusiness/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle users:', error);
      throw error;
    }
  },

  /**
   * Get user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  getUser: async (userId, params = {}) => {
    try {
      const response = await axios.get(`/api/mosyleBusiness/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Mosyle user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get all groups
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of groups
   */
  getGroups: async (params = {}) => {
    try {
      const response = await axios.get('/api/mosyleBusiness/groups', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle groups:', error);
      throw error;
    }
  },

  /**
   * Get group details
   * @param {string} groupId Group ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Group details
   */
  getGroup: async (groupId, params = {}) => {
    try {
      const response = await axios.get(`/api/mosyleBusiness/groups/${groupId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Mosyle group ${groupId}:`, error);
      throw error;
    }
  },

  /**
   * Send command to device
   * @param {string} deviceId Device ID
   * @param {Object} command Command object with action and parameters
   * @returns {Promise<Object>} Response message
   */
  sendCommand: async (deviceId, command) => {
    try {
      const response = await axios.post(`/api/mosyleBusiness/devices/${deviceId}/command`, command);
      return response.data;
    } catch (error) {
      console.error(`Error sending command to Mosyle device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Save Mosyle Business configuration
   * @param {Object} config Configuration object with credentials
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/mosyleBusiness/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Mosyle Business configuration:', error);
      throw error;
    }
  },

  /**
   * Get Mosyle Business configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/mosyleBusiness/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Mosyle Business configuration:', error);
      throw error;
    }
  }
};

export default mosyleBusinessService;