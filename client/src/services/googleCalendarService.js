import axios from 'axios';

/**
 * Google Calendar API Service
 * Handles client-side API calls to the Google Calendar endpoints
 */
const googleCalendarService = {
  /**
   * Set up Google Calendar with one click
   * @returns {Promise<Object>} Response message
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/google-calendar/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up Google Calendar with one click:', error);
      throw error;
    }
  },
  /**
   * Save Google Calendar configuration
   * @param {Object} config Configuration object with clientId, clientSecret, redirectUri, and tokenPath
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/google-calendar/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Google Calendar configuration:', error);
      throw error;
    }
  },

  /**
   * Get Google Calendar configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/google-calendar/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Google Calendar configuration:', error);
      throw error;
    }
  },

  /**
   * Get authentication URL for OAuth2 flow
   * @returns {Promise<Object>} Authentication information including URL and status
   */
  getAuthUrl: async () => {
    try {
      const response = await axios.get('/api/google-calendar/auth-url');
      // Return the full response data which may include userAuth status and different URLs
      return response.data;
    } catch (error) {
      console.error('Error getting Google Calendar auth URL:', error);
      throw error;
    }
  },

  /**
   * List calendars in Google Calendar
   * @returns {Promise<Array>} List of calendars
   */
  listCalendars: async () => {
    try {
      const response = await axios.get('/api/google-calendar/calendars');
      return response.data;
    } catch (error) {
      console.error('Error listing calendars from Google Calendar:', error);
      throw error;
    }
  },

  /**
   * Get calendar details
   * @param {string} calendarId Calendar ID
   * @returns {Promise<Object>} Calendar details
   */
  getCalendar: async (calendarId) => {
    try {
      const response = await axios.get(`/api/google-calendar/calendars/${calendarId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting calendar from Google Calendar:', error);
      throw error;
    }
  },

  /**
   * List events for a calendar
   * @param {string} calendarId Calendar ID
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of events
   */
  listEvents: async (calendarId, options = {}) => {
    try {
      const response = await axios.get(`/api/google-calendar/calendars/${calendarId}/events`, { params: options });
      return response.data;
    } catch (error) {
      console.error('Error listing events from Google Calendar:', error);
      throw error;
    }
  },

  /**
   * Get event details
   * @param {string} calendarId Calendar ID
   * @param {string} eventId Event ID
   * @returns {Promise<Object>} Event details
   */
  getEvent: async (calendarId, eventId) => {
    try {
      const response = await axios.get(`/api/google-calendar/calendars/${calendarId}/events/${eventId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting event from Google Calendar:', error);
      throw error;
    }
  },

  /**
   * Create a new event
   * @param {string} calendarId Calendar ID
   * @param {Object} eventData Event data
   * @returns {Promise<Object>} Created event
   */
  createEvent: async (calendarId, eventData) => {
    try {
      const response = await axios.post(`/api/google-calendar/calendars/${calendarId}/events`, eventData);
      return response.data;
    } catch (error) {
      console.error('Error creating event in Google Calendar:', error);
      throw error;
    }
  },

  /**
   * Update an existing event
   * @param {string} calendarId Calendar ID
   * @param {string} eventId Event ID
   * @param {Object} eventData Event data
   * @returns {Promise<Object>} Updated event
   */
  updateEvent: async (calendarId, eventId, eventData) => {
    try {
      const response = await axios.put(`/api/google-calendar/calendars/${calendarId}/events/${eventId}`, eventData);
      return response.data;
    } catch (error) {
      console.error('Error updating event in Google Calendar:', error);
      throw error;
    }
  },

  /**
   * Delete an event
   * @param {string} calendarId Calendar ID
   * @param {string} eventId Event ID
   * @returns {Promise<Object>} Response message
   */
  deleteEvent: async (calendarId, eventId) => {
    try {
      const response = await axios.delete(`/api/google-calendar/calendars/${calendarId}/events/${eventId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting event from Google Calendar:', error);
      throw error;
    }
  }
};

export default googleCalendarService;
