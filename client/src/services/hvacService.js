import axios from 'axios';
import dreoService from './dreoService';
import goveeService from './goveeService';
import skyportCloudService from './skyportCloudService';
import lgThinqService from './lgThinqService';
import { handleServiceError, filterData, locationUtils, statusUtils, ServiceCache } from './utils/serviceHelpers';

const API_BASE_URL = '/api/hvac';

class HvacService {
  constructor() {
    this.cache = new ServiceCache(180000); // 3 minute cache for temperature data
  }

  // ===== HVAC Units Management =====
  
  async getUnits(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/units`, { params });
    return res.data;
  }

  async getUnit(id) {
    const res = await axios.get(`${API_BASE_URL}/units/${id}`);
    return res.data;
  }

  async createUnit(data) {
    const res = await axios.post(`${API_BASE_URL}/units`, data);
    return res.data;
  }

  async updateUnit(id, data) {
    const res = await axios.put(`${API_BASE_URL}/units/${id}`, data);
    return res.data;
  }

  async deleteUnit(id, { force = false } = {}) {
    const res = await axios.delete(`${API_BASE_URL}/units/${id}`, { params: { force } });
    return res.data;
  }

  // ===== Multi-Source Temperature & HVAC Data Integration =====
  
  /**
   * Get comprehensive temperature and HVAC data from all smart device integrations
   * Consolidates data from Dreo, Govee, Skyport Cloud, LG ThinQ, and HVAC sensors
   */
  async getTemperatureData(buildingId, floorId, options = {}) {
    try {
      const cacheKey = `temp_data_${buildingId}_${floorId}_${JSON.stringify(options)}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && !options.forceRefresh) {
        return cached;
      }

      console.log(`Fetching temperature data from all sources for building ${buildingId}, floor ${floorId}`);

      // Parallel fetch from all sources
      const sourcePromises = [
        this.getHVACTemperatureData(buildingId, floorId, options),
        this.getDreoTemperatureData(buildingId, floorId),
        this.getGoveeTemperatureData(buildingId, floorId),
        this.getSkyportTemperatureData(buildingId, floorId),
        this.getLGThinQTemperatureData(buildingId, floorId)
      ];

      const results = await Promise.allSettled(sourcePromises);
      
      // Combine all successful results
      const combinedData = [];
      const errors = [];

      results.forEach((result, index) => {
        const sources = ['HVAC', 'Dreo', 'Govee', 'Skyport', 'LG ThinQ'];
        if (result.status === 'fulfilled') {
          combinedData.push(...result.value);
        } else {
          console.warn(`Failed to fetch ${sources[index]} temperature data:`, result.reason?.message);
          errors.push({ source: sources[index], error: result.reason?.message });
        }
      });

      // Sort by timestamp (most recent first)
      combinedData.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      const finalData = {
        sensors: combinedData,
        sourceCount: {
          hvac: combinedData.filter(s => s.source === 'hvac').length,
          dreo: combinedData.filter(s => s.source === 'dreo').length,
          govee: combinedData.filter(s => s.source === 'govee').length,
          skyport: combinedData.filter(s => s.source === 'skyport').length,
          lgThinq: combinedData.filter(s => s.source === 'lgThinq').length
        },
        errors,
        lastUpdated: new Date(),
        totalSensors: combinedData.length
      };

      // Cache the results
      this.cache.set(cacheKey, finalData);
      
      return finalData;
    } catch (error) {
      return handleServiceError('getTemperatureData', error, { buildingId, floorId });
    }
  }

  // ===== Individual Source Temperature Data Methods =====

  /**
   * Get temperature data from HVAC system sensors
   */
  async getHVACTemperatureData(buildingId, floorId, options = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/temperature-data`, {
        params: { buildingId, floorId, ...options }
      });
      
      return (response.data || []).map(sensor => ({
        ...sensor,
        source: 'hvac',
        deviceType: sensor.deviceType || 'hvac_sensor',
        timestamp: new Date(sensor.timestamp || Date.now())
      }));
    } catch (error) {
      console.warn('HVAC temperature data unavailable:', error.message);
      return [];
    }
  }

  /**
   * Get temperature data from Dreo portable AC units and fans
   */
  async getDreoTemperatureData(buildingId, floorId) {
    try {
      const dreoDevices = await dreoService.getDevices();
      
      return dreoDevices
        .filter(device => 
          (!buildingId || device.buildingId === buildingId) &&
          (!floorId || device.floorId === floorId) &&
          device.status?.temperature
        )
        .map(device => ({
          id: `dreo_${device.deviceId}`,
          source: 'dreo',
          deviceType: device.deviceType || 'portable_ac',
          deviceName: device.deviceName,
          location: {
            x: device.location?.x || 0,
            y: device.location?.y || 0,
            room: device.room || 'Unknown Room'
          },
          temperature: this.convertTemperature(device.status.temperature, device.status.temperatureUnit),
          humidity: device.status.humidity,
          targetTemperature: device.status.targetTemperature,
          mode: device.status.mode,
          powerState: device.status.powerOn,
          timestamp: new Date(device.status.lastUpdate || Date.now()),
          capabilities: ['temperature', 'humidity', 'cooling', 'heating']
        }));
    } catch (error) {
      console.warn('Dreo temperature data unavailable:', error.message);
      return [];
    }
  }

  /**
   * Get temperature data from Govee temperature/humidity sensors
   */
  async getGoveeTemperatureData(buildingId, floorId) {
    try {
      const goveeDevices = await goveeService.getDevices();
      const temperatureData = [];

      for (const device of goveeDevices) {
        try {
          // Filter by location
          if (buildingId && device.buildingId !== buildingId) continue;
          if (floorId && device.floorId !== floorId) continue;

          // Get device state for temperature/humidity sensors
          const deviceState = await goveeService.getDeviceState(device.device, device.model);
          
          if (deviceState.properties) {
            const tempProperty = deviceState.properties.find(p => p.name === 'temperature');
            const humidityProperty = deviceState.properties.find(p => p.name === 'humidity');
            
            if (tempProperty) {
              temperatureData.push({
                id: `govee_${device.device}`,
                source: 'govee',
                deviceType: device.deviceName.includes('Thermometer') ? 'thermometer' : 'smart_sensor',
                deviceName: device.deviceName,
                model: device.model,
                location: {
                  x: device.location?.x || 0,
                  y: device.location?.y || 0,
                  room: device.room || 'Unknown Room'
                },
                temperature: tempProperty.value,
                humidity: humidityProperty?.value,
                batteryLevel: deviceState.properties.find(p => p.name === 'battery')?.value,
                timestamp: new Date(),
                capabilities: ['temperature', 'humidity']
              });
            }
          }
        } catch (deviceError) {
          console.warn(`Failed to get Govee device ${device.device} state:`, deviceError.message);
        }
      }

      return temperatureData;
    } catch (error) {
      console.warn('Govee temperature data unavailable:', error.message);
      return [];
    }
  }

  /**
   * Get temperature data from Skyport Cloud connected devices
   */
  async getSkyportTemperatureData(buildingId, floorId) {
    try {
      const skyportDevices = await skyportCloudService.getDevices();
      const temperatureData = [];

      for (const device of skyportDevices) {
        try {
          // Filter by location
          if (buildingId && device.buildingId !== buildingId) continue;
          if (floorId && device.floorId !== floorId) continue;

          // Get detailed device info
          const deviceInfo = await skyportCloudService.getDeviceInfo(device.id);
          
          if (deviceInfo.sensors) {
            deviceInfo.sensors.forEach(sensor => {
              if (sensor.type === 'temperature' || sensor.type === 'climate') {
                temperatureData.push({
                  id: `skyport_${device.id}_${sensor.id}`,
                  source: 'skyport',
                  deviceType: sensor.type,
                  deviceName: `${device.name} - ${sensor.name}`,
                  location: {
                    x: device.location?.x || 0,
                    y: device.location?.y || 0,
                    room: device.location?.room || 'Unknown Room'
                  },
                  temperature: sensor.temperature,
                  humidity: sensor.humidity,
                  airQuality: sensor.airQuality,
                  timestamp: new Date(sensor.lastUpdate || Date.now()),
                  capabilities: ['temperature', 'humidity', 'air_quality']
                });
              }
            });
          }
        } catch (deviceError) {
          console.warn(`Failed to get Skyport device ${device.id} info:`, deviceError.message);
        }
      }

      return temperatureData;
    } catch (error) {
      console.warn('Skyport temperature data unavailable:', error.message);
      return [];
    }
  }

  /**
   * Get temperature data from LG ThinQ HVAC appliances
   */
  async getLGThinQTemperatureData(buildingId, floorId) {
    try {
      const lgDevices = await lgThinqService.getDevices();
      const temperatureData = [];

      for (const device of lgDevices) {
        try {
          // Filter by location and HVAC device types
          if (buildingId && device.buildingId !== buildingId) continue;
          if (floorId && device.floorId !== floorId) continue;
          if (!this.isHVACDevice(device.deviceType)) continue;

          // Get device status
          const deviceStatus = await lgThinqService.getDeviceStatus(device.deviceId);
          
          if (deviceStatus.temperature !== undefined) {
            temperatureData.push({
              id: `lg_${device.deviceId}`,
              source: 'lgThinq',
              deviceType: device.deviceType,
              deviceName: device.alias || device.deviceType,
              model: device.model,
              location: {
                x: device.location?.x || 0,
                y: device.location?.y || 0,
                room: device.room || 'Unknown Room'
              },
              temperature: deviceStatus.temperature,
              humidity: deviceStatus.humidity,
              targetTemperature: deviceStatus.targetTemperature,
              mode: deviceStatus.mode,
              powerState: deviceStatus.power,
              energyUsage: deviceStatus.energyUsage,
              timestamp: new Date(deviceStatus.lastUpdate || Date.now()),
              capabilities: this.getLGDeviceCapabilities(device.deviceType)
            });
          }
        } catch (deviceError) {
          console.warn(`Failed to get LG ThinQ device ${device.deviceId} status:`, deviceError.message);
        }
      }

      return temperatureData;
    } catch (error) {
      console.warn('LG ThinQ temperature data unavailable:', error.message);
      return [];
    }
  }

  // ===== Enhanced Temperature Analysis =====

  /**
   * Analyze comprehensive temperature data from all sources
   * Provides advanced insights including source analysis, device health, and HVAC efficiency
   */
  analyzeTemperatureData(temperatureDataResult) {
    if (!temperatureDataResult || !temperatureDataResult.sensors || temperatureDataResult.sensors.length === 0) {
      return {
        averageTemperature: null,
        minTemperature: null,
        maxTemperature: null,
        hotSpots: [],
        coldSpots: [],
        comfortZones: [],
        outliers: [],
        coverage: 'insufficient',
        sourceAnalysis: {},
        deviceHealth: {},
        recommendations: []
      };
    }

    const sensors = temperatureDataResult.sensors;
    const temperatures = sensors
      .map(sensor => sensor.temperature)
      .filter(temp => typeof temp === 'number' && !isNaN(temp));

    if (temperatures.length === 0) {
      return {
        averageTemperature: null,
        minTemperature: null,
        maxTemperature: null,
        hotSpots: [],
        coldSpots: [],
        comfortZones: [],
        outliers: [],
        coverage: 'no-data',
        sourceAnalysis: temperatureDataResult.sourceCount,
        deviceHealth: {},
        recommendations: ['No temperature data available. Check device connections.']
      };
    }

    const avgTemp = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length;
    const minTemp = Math.min(...temperatures);
    const maxTemp = Math.max(...temperatures);
    
    // Enhanced comfort zone analysis (adjustable based on season/preferences)
    const comfortMin = 68;
    const comfortMax = 78;
    const optimalMin = 70;
    const optimalMax = 75;
    
    // Categorize sensors with enhanced criteria
    const hotSpots = sensors.filter(sensor => sensor.temperature > comfortMax + 2);
    const coldSpots = sensors.filter(sensor => sensor.temperature < comfortMin - 2);
    const comfortZones = sensors.filter(sensor => 
      sensor.temperature >= comfortMin && sensor.temperature <= comfortMax
    );
    const optimalZones = sensors.filter(sensor =>
      sensor.temperature >= optimalMin && sensor.temperature <= optimalMax
    );
    
    // Find outliers (more than 5 degrees from average)
    const outliers = sensors.filter(sensor => 
      Math.abs(sensor.temperature - avgTemp) > 5
    );

    // Source analysis
    const sourceAnalysis = {
      ...temperatureDataResult.sourceCount,
      sourceReliability: this.analyzeSourceReliability(sensors),
      sourceDistribution: this.analyzeSourceDistribution(sensors)
    };

    // Device health analysis
    const deviceHealth = this.analyzeDeviceHealth(sensors);

    // Generate recommendations
    const recommendations = this.generateTemperatureRecommendations({
      avgTemp,
      hotSpots,
      coldSpots,
      sourceAnalysis,
      deviceHealth,
      totalSensors: sensors.length
    });

    // HVAC efficiency analysis
    const hvacEfficiency = this.analyzeHVACEfficiency(sensors);

    return {
      averageTemperature: Math.round(avgTemp * 10) / 10,
      minTemperature: minTemp,
      maxTemperature: maxTemp,
      temperatureRange: maxTemp - minTemp,
      hotSpots: hotSpots.map(spot => ({
        ...spot,
        severity: spot.temperature > comfortMax + 5 ? 'high' : 'medium',
        deviationFromComfort: spot.temperature - comfortMax
      })),
      coldSpots: coldSpots.map(spot => ({
        ...spot,
        severity: spot.temperature < comfortMin - 5 ? 'high' : 'medium',
        deviationFromComfort: comfortMin - spot.temperature
      })),
      comfortZones,
      optimalZones,
      outliers,
      coverage: this.assessCoverage(sensors.length, sourceAnalysis),
      dataPoints: sensors.length,
      sourceAnalysis,
      deviceHealth,
      hvacEfficiency,
      recommendations,
      lastAnalyzed: new Date()
    };
  }

  /**
   * Analyze reliability of different temperature sources
   */
  analyzeSourceReliability(sensors) {
    const sourceGroups = sensors.reduce((groups, sensor) => {
      if (!groups[sensor.source]) groups[sensor.source] = [];
      groups[sensor.source].push(sensor);
      return groups;
    }, {});

    const reliability = {};
    
    Object.entries(sourceGroups).forEach(([source, sourceSensors]) => {
      const recentData = sourceSensors.filter(sensor => 
        new Date() - new Date(sensor.timestamp) < 300000 // 5 minutes
      );
      
      reliability[source] = {
        totalSensors: sourceSensors.length,
        recentDataPoints: recentData.length,
        reliability: recentData.length / sourceSensors.length,
        avgResponseTime: this.calculateAvgResponseTime(sourceSensors),
        dataFreshness: this.calculateDataFreshness(sourceSensors)
      };
    });

    return reliability;
  }

  /**
   * Analyze distribution of temperature sources across building
   */
  analyzeSourceDistribution(sensors) {
    const roomDistribution = sensors.reduce((dist, sensor) => {
      const room = sensor.location?.room || 'Unknown';
      if (!dist[room]) dist[room] = {};
      if (!dist[room][sensor.source]) dist[room][sensor.source] = 0;
      dist[room][sensor.source]++;
      return dist;
    }, {});

    return roomDistribution;
  }

  /**
   * Analyze health of individual devices
   */
  analyzeDeviceHealth(sensors) {
    const deviceHealth = {
      healthy: 0,
      warning: 0,
      critical: 0,
      offline: 0,
      details: []
    };

    sensors.forEach(sensor => {
      const health = this.assessDeviceHealth(sensor);
      deviceHealth[health.status]++;
      
      if (health.status !== 'healthy') {
        deviceHealth.details.push({
          deviceId: sensor.id,
          deviceName: sensor.deviceName,
          source: sensor.source,
          status: health.status,
          issues: health.issues
        });
      }
    });

    return deviceHealth;
  }

  /**
   * Assess individual device health
   */
  assessDeviceHealth(sensor) {
    const issues = [];
    const now = new Date();
    const sensorTime = new Date(sensor.timestamp);
    const ageMinutes = (now - sensorTime) / 60000;

    // Check data freshness
    if (ageMinutes > 30) {
      issues.push('Stale data - no recent updates');
    }

    // Check battery level for wireless devices
    if (sensor.batteryLevel !== undefined && sensor.batteryLevel < 20) {
      issues.push('Low battery level');
    }

    // Check temperature range reasonableness
    if (sensor.temperature < 32 || sensor.temperature > 120) {
      issues.push('Temperature reading out of reasonable range');
    }

    // Check device connectivity
    if (sensor.powerState === false && sensor.source !== 'hvac') {
      issues.push('Device powered off');
    }

    // Determine status
    let status = 'healthy';
    if (ageMinutes > 60) status = 'offline';
    else if (issues.length > 1) status = 'critical';
    else if (issues.length > 0) status = 'warning';

    return { status, issues };
  }

  /**
   * Generate actionable recommendations based on analysis
   */
  generateTemperatureRecommendations({ avgTemp, hotSpots, coldSpots, sourceAnalysis, deviceHealth, totalSensors }) {
    const recommendations = [];

    // Temperature-based recommendations
    if (avgTemp > 78) {
      recommendations.push({
        type: 'cooling',
        priority: 'high',
        message: 'Building average temperature is above comfort zone. Consider increasing cooling.',
        action: 'Adjust HVAC settings or check cooling system efficiency'
      });
    } else if (avgTemp < 68) {
      recommendations.push({
        type: 'heating',
        priority: 'high', 
        message: 'Building average temperature is below comfort zone. Consider increasing heating.',
        action: 'Adjust HVAC settings or check heating system efficiency'
      });
    }

    // Hot spot recommendations
    if (hotSpots.length > 0) {
      recommendations.push({
        type: 'hotspots',
        priority: 'medium',
        message: `${hotSpots.length} hot spot(s) detected requiring attention.`,
        action: 'Check ventilation, sun exposure, or add targeted cooling'
      });
    }

    // Cold spot recommendations
    if (coldSpots.length > 0) {
      recommendations.push({
        type: 'coldspots',
        priority: 'medium',
        message: `${coldSpots.length} cold spot(s) detected requiring attention.`,
        action: 'Check insulation, drafts, or add targeted heating'
      });
    }

    // Coverage recommendations
    if (totalSensors < 3) {
      recommendations.push({
        type: 'coverage',
        priority: 'low',
        message: 'Limited temperature sensor coverage detected.',
        action: 'Consider adding more temperature sensors for better monitoring'
      });
    }

    // Device health recommendations
    if (deviceHealth.critical > 0) {
      recommendations.push({
        type: 'maintenance',
        priority: 'high',
        message: `${deviceHealth.critical} device(s) require immediate attention.`,
        action: 'Check device connections, replace batteries, or service equipment'
      });
    }

    return recommendations;
  }

  /**
   * Analyze HVAC system efficiency based on multi-source data
   */
  analyzeHVACEfficiency(sensors) {
    const hvacSensors = sensors.filter(s => s.source === 'hvac' || s.source === 'lgThinq' || s.source === 'dreo');
    const ambientSensors = sensors.filter(s => s.source === 'govee' || s.source === 'skyport');

    if (hvacSensors.length === 0) {
      return { status: 'unknown', message: 'No HVAC device data available' };
    }

    // Calculate temperature differential between HVAC output and ambient
    const hvacAvg = hvacSensors.reduce((sum, s) => sum + s.temperature, 0) / hvacSensors.length;
    const ambientAvg = ambientSensors.length > 0 
      ? ambientSensors.reduce((sum, s) => sum + s.temperature, 0) / ambientSensors.length
      : hvacAvg;

    const differential = Math.abs(hvacAvg - ambientAvg);
    const energyEfficient = hvacSensors.filter(s => s.energyUsage && s.energyUsage < 1000).length;

    return {
      hvacSensors: hvacSensors.length,
      ambientSensors: ambientSensors.length,
      temperatureDifferential: Math.round(differential * 10) / 10,
      efficiency: differential < 5 ? 'good' : differential < 10 ? 'fair' : 'poor',
      energyEfficientDevices: energyEfficient,
      recommendations: this.getHVACRecommendations(differential, hvacSensors)
    };
  }

  // ===== Helper Methods =====

  /**
   * Convert temperature between units
   */
  convertTemperature(temp, fromUnit = 'F', toUnit = 'F') {
    if (fromUnit === toUnit) return temp;
    
    if (fromUnit === 'C' && toUnit === 'F') {
      return (temp * 9/5) + 32;
    }
    if (fromUnit === 'F' && toUnit === 'C') {
      return (temp - 32) * 5/9;
    }
    return temp;
  }

  /**
   * Check if LG device is HVAC-related
   */
  isHVACDevice(deviceType) {
    const hvacTypes = ['AC', 'HEAT_PUMP', 'BOILER', 'FAN', 'DEHUMIDIFIER', 'AIR_PURIFIER'];
    return hvacTypes.some(type => deviceType?.toUpperCase().includes(type));
  }

  /**
   * Get capabilities for LG device types
   */
  getLGDeviceCapabilities(deviceType) {
    const capabilityMap = {
      'AC': ['temperature', 'humidity', 'cooling', 'heating', 'energy_monitoring'],
      'HEAT_PUMP': ['temperature', 'heating', 'cooling', 'energy_monitoring'],
      'DEHUMIDIFIER': ['humidity', 'air_quality'],
      'AIR_PURIFIER': ['air_quality', 'fan_speed'],
      'FAN': ['air_circulation', 'fan_speed']
    };
    
    return capabilityMap[deviceType?.toUpperCase()] || ['temperature'];
  }

  /**
   * Calculate average response time for sensors
   */
  calculateAvgResponseTime(sensors) {
    // Placeholder - would need actual response time data
    return sensors.length > 0 ? 250 : 0; // ms
  }

  /**
   * Calculate data freshness score
   */
  calculateDataFreshness(sensors) {
    const now = new Date();
    const freshData = sensors.filter(sensor => 
      (now - new Date(sensor.timestamp)) < 300000 // 5 minutes
    );
    return freshData.length / sensors.length;
  }

  /**
   * Assess coverage quality
   */
  assessCoverage(sensorCount, sourceAnalysis) {
    const sourceCount = Object.keys(sourceAnalysis).length;
    
    if (sensorCount >= 10 && sourceCount >= 3) return 'excellent';
    if (sensorCount >= 5 && sourceCount >= 2) return 'good';
    if (sensorCount >= 3) return 'fair';
    return 'poor';
  }

  /**
   * Get HVAC-specific recommendations
   */
  getHVACRecommendations(differential, hvacSensors) {
    const recommendations = [];
    
    if (differential > 10) {
      recommendations.push('Large temperature differential detected - check HVAC efficiency');
    }
    
    const highEnergyDevices = hvacSensors.filter(s => s.energyUsage && s.energyUsage > 2000);
    if (highEnergyDevices.length > 0) {
      recommendations.push('High energy usage detected - consider energy-efficient settings');
    }
    
    return recommendations;
  }

  // ===== Filter Tracking =====
  
  async getFilterStatus(unitId) {
    const res = await axios.get(`${API_BASE_URL}/units/${unitId}/filters`);
    return res.data;
  }

  async updateFilterStatus(unitId, filterData) {
    const res = await axios.put(`${API_BASE_URL}/units/${unitId}/filters`, filterData);
    return res.data;
  }

  async getMaintenanceSchedule(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/maintenance`, { params });
    return res.data;
  }

  // ===== Smart Device Control Integration =====

  /**
   * Control smart HVAC devices through their respective services
   */
  async controlSmartDevice(deviceId, deviceSource, action, params = {}) {
    try {
      let result;
      
      switch (deviceSource) {
        case 'dreo':
          result = await this.controlDreoDevice(deviceId, action, params);
          break;
        case 'lgThinq':
          result = await this.controlLGDevice(deviceId, action, params);
          break;
        case 'govee':
          result = await this.controlGoveeDevice(deviceId, action, params);
          break;
        default:
          throw new Error(`Unsupported device source: ${deviceSource}`);
      }

      // Clear cache after device control to get fresh data
      this.cache.clear();
      
      return {
        success: true,
        deviceId,
        source: deviceSource,
        action,
        result,
        timestamp: new Date()
      };
    } catch (error) {
      return handleServiceError('controlSmartDevice', error, { deviceId, deviceSource, action });
    }
  }

  /**
   * Control Dreo devices (AC units, fans)
   */
  async controlDreoDevice(deviceId, action, params) {
    const dreoDeviceId = deviceId.replace('dreo_', '');
    
    switch (action) {
      case 'setTemperature':
        return await dreoService.setTemperature(dreoDeviceId, params.temperature);
      case 'setMode':
        return await dreoService.setMode(dreoDeviceId, params.mode);
      case 'powerOn':
        return await dreoService.powerOn(dreoDeviceId);
      case 'powerOff':
        return await dreoService.powerOff(dreoDeviceId);
      case 'setFanSpeed':
        return await dreoService.setFanSpeed(dreoDeviceId, params.speed);
      default:
        throw new Error(`Unsupported Dreo action: ${action}`);
    }
  }

  /**
   * Control LG ThinQ devices
   */
  async controlLGDevice(deviceId, action, params) {
    const lgDeviceId = deviceId.replace('lg_', '');
    
    switch (action) {
      case 'setTemperature':
        return await lgThinqService.setTemperature(lgDeviceId, params.temperature);
      case 'setMode':
        return await lgThinqService.setMode(lgDeviceId, params.mode);
      case 'powerOn':
        return await lgThinqService.powerOn(lgDeviceId);
      case 'powerOff':
        return await lgThinqService.powerOff(lgDeviceId);
      default:
        throw new Error(`Unsupported LG ThinQ action: ${action}`);
    }
  }

  /**
   * Control Govee devices (limited to supported actions)
   */
  async controlGoveeDevice(deviceId, action, params) {
    const goveeDeviceId = deviceId.replace('govee_', '');
    
    switch (action) {
      case 'powerOn':
        return await goveeService.turnOn(goveeDeviceId, params.model);
      case 'powerOff':
        return await goveeService.turnOff(goveeDeviceId, params.model);
      default:
        throw new Error(`Unsupported Govee action: ${action}`);
    }
  }

  /**
   * Get all controllable smart devices
   */
  async getControllableDevices(buildingId = null, floorId = null) {
    try {
      const temperatureData = await this.getTemperatureData(buildingId, floorId);
      
      const controllableDevices = temperatureData.sensors
        .filter(sensor => ['dreo', 'lgThinq', 'govee'].includes(sensor.source))
        .map(sensor => ({
          id: sensor.id,
          name: sensor.deviceName,
          type: sensor.deviceType,
          source: sensor.source,
          location: sensor.location,
          currentTemperature: sensor.temperature,
          targetTemperature: sensor.targetTemperature,
          mode: sensor.mode,
          powerState: sensor.powerState,
          capabilities: sensor.capabilities || [],
          lastUpdate: sensor.timestamp
        }));

      return {
        devices: controllableDevices,
        total: controllableDevices.length,
        bySource: {
          dreo: controllableDevices.filter(d => d.source === 'dreo').length,
          lgThinq: controllableDevices.filter(d => d.source === 'lgThinq').length,
          govee: controllableDevices.filter(d => d.source === 'govee').length
        }
      };
    } catch (error) {
      return handleServiceError('getControllableDevices', error, { buildingId, floorId });
    }
  }

  /**
   * Optimize HVAC settings based on multi-source temperature data
   */
  async optimizeHVACSettings(buildingId, floorId, preferences = {}) {
    try {
      const temperatureData = await this.getTemperatureData(buildingId, floorId, { forceRefresh: true });
      const analysis = this.analyzeTemperatureData(temperatureData);
      
      const optimizations = [];
      const targetTemp = preferences.targetTemperature || 72;
      const tolerance = preferences.tolerance || 2;

      // Find devices that need adjustment
      const hotDevices = analysis.hotSpots.filter(spot => 
        ['dreo', 'lgThinq'].includes(spot.source) && spot.capabilities?.includes('cooling')
      );
      
      const coldDevices = analysis.coldSpots.filter(spot => 
        ['dreo', 'lgThinq'].includes(spot.source) && spot.capabilities?.includes('heating')
      );

      // Generate optimization commands for hot spots
      for (const device of hotDevices) {
        if (device.temperature > targetTemp + tolerance) {
          optimizations.push({
            deviceId: device.id,
            source: device.source,
            action: 'setTemperature',
            params: { temperature: targetTemp - 1 },
            reason: `Cooling down hot spot (${device.temperature}°F)`
          });
        }
      }

      // Generate optimization commands for cold spots
      for (const device of coldDevices) {
        if (device.temperature < targetTemp - tolerance) {
          optimizations.push({
            deviceId: device.id,
            source: device.source,
            action: 'setTemperature',
            params: { temperature: targetTemp + 1 },
            reason: `Warming up cold spot (${device.temperature}°F)`
          });
        }
      }

      return {
        analysis,
        optimizations,
        estimatedEnergySavings: this.calculateEnergySavings(optimizations),
        recommendations: analysis.recommendations
      };
    } catch (error) {
      return handleServiceError('optimizeHVACSettings', error, { buildingId, floorId });
    }
  }

  /**
   * Calculate estimated energy savings from optimizations
   */
  calculateEnergySavings(optimizations) {
    // Simplified calculation - would need more sophisticated modeling in production
    const savingsPerOptimization = 15; // percent
    const totalOptimizations = optimizations.length;
    
    return {
      estimatedSavings: Math.min(totalOptimizations * savingsPerOptimization, 50),
      optimizationCount: totalOptimizations,
      period: '24 hours'
    };
  }
}

const hvacService = new HvacService();
export default hvacService;
