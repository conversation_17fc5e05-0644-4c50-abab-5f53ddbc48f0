import axios from 'axios';

/**
 * Service for interacting with the Contacts Synchronization API
 */
class ContactSyncService {
  /**
   * Get Google OAuth URL for authentication
   * @returns {Promise<Object>} Auth URL
   */
  async getGoogleAuthUrl() {
    try {
      const response = await axios.get('/api/contacts/sync/google/auth');
      return response.data;
    } catch (error) {
      console.error('Error getting Google auth URL:', error);
      throw error;
    }
  }

  /**
   * Configure Google sync settings
   * @param {Object} config - Sync configuration
   * @param {boolean} config.enabled - Whether sync is enabled
   * @param {number} config.syncFrequency - Sync frequency in minutes
   * @param {Array<string>} config.syncCategories - Categories to sync
   * @returns {Promise<Object>} Updated configuration
   */
  async configureGoogleSync(config) {
    try {
      const response = await axios.post('/api/contacts/sync/google/configure', config);
      return response.data;
    } catch (error) {
      console.error('Error configuring Google sync:', error);
      throw error;
    }
  }

  /**
   * Trigger immediate sync with Google
   * @returns {Promise<Object>} Sync status
   */
  async syncNowGoogle() {
    try {
      const response = await axios.post('/api/contacts/sync/google/sync-now');
      return response.data;
    } catch (error) {
      console.error('Error triggering Google sync:', error);
      throw error;
    }
  }

  /**
   * Get Google sync status
   * @returns {Promise<Object>} Sync status
   */
  async getGoogleSyncStatus() {
    try {
      const response = await axios.get('/api/contacts/sync/google/status');
      return response.data;
    } catch (error) {
      console.error('Error getting Google sync status:', error);
      throw error;
    }
  }

  /**
   * Get vCard download URL for Apple contacts
   * @param {Array<string>} categories - Categories to include (optional)
   * @returns {string} Download URL
   */
  getAppleVCardUrl(categories = null) {
    let url = '/api/contacts/sync/apple/vcard';
    if (categories && categories.length > 0) {
      url += `?categories=${categories.join(',')}`;
    }
    return url;
  }

  /**
   * Configure Apple sync settings
   * @param {Object} config - Sync configuration
   * @param {boolean} config.enabled - Whether sync is enabled
   * @param {number} config.syncFrequency - Sync frequency in minutes
   * @param {Array<string>} config.syncCategories - Categories to sync
   * @returns {Promise<Object>} Updated configuration
   */
  async configureAppleSync(config) {
    try {
      const response = await axios.post('/api/contacts/sync/apple/configure', config);
      return response.data;
    } catch (error) {
      console.error('Error configuring Apple sync:', error);
      throw error;
    }
  }

  /**
   * Get Apple sync status
   * @returns {Promise<Object>} Sync status
   */
  async getAppleSyncStatus() {
    try {
      const response = await axios.get('/api/contacts/sync/apple/status');
      return response.data;
    } catch (error) {
      console.error('Error getting Apple sync status:', error);
      throw error;
    }
  }

  /**
   * Get sync logs
   * @param {Object} options - Query options
   * @param {string} options.provider - Provider (google or apple)
   * @param {number} options.limit - Number of logs to return
   * @param {number} options.offset - Offset for pagination
   * @returns {Promise<Object>} Sync logs
   */
  async getSyncLogs(options = {}) {
    try {
      const { provider, limit = 10, offset = 0 } = options;
      let url = `/api/contacts/sync/logs?limit=${limit}&offset=${offset}`;
      if (provider) {
        url += `&provider=${provider}`;
      }
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Error getting sync logs:', error);
      throw error;
    }
  }

  /**
   * Format sync status for display
   * @param {string} status - Sync status
   * @returns {Object} Formatted status with color and label
   */
  formatSyncStatus(status) {
    switch (status) {
      case 'idle':
        return { color: 'info', label: 'Idle' };
      case 'in_progress':
        return { color: 'warning', label: 'In Progress' };
      case 'success':
        return { color: 'success', label: 'Success' };
      case 'error':
        return { color: 'error', label: 'Error' };
      default:
        return { color: 'default', label: status || 'Unknown' };
    }
  }

  /**
   * Format sync frequency for display
   * @param {number} minutes - Sync frequency in minutes
   * @returns {string} Formatted frequency
   */
  formatSyncFrequency(minutes) {
    if (!minutes) return 'Never';
    
    if (minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(minutes / 1440);
      return `${days} day${days !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Format date for display
   * @param {Date|string} date - Date to format
   * @returns {string} Formatted date
   */
  formatDate(date) {
    if (!date) return 'Never';
    
    const dateObj = new Date(date);
    return dateObj.toLocaleString();
  }
}

export default new ContactSyncService();