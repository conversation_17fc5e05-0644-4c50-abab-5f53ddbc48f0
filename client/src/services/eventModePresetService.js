import axios from 'axios';

const API_BASE = '/api/event-mode-presets';

class EventModePresetService {
  
  // Get all event mode presets with filtering and pagination
  async getPresets(params = {}) {
    try {
      const response = await axios.get(API_BASE, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching event mode presets:', error);
      throw error;
    }
  }

  // Get specific preset by ID
  async getPreset(id) {
    try {
      const response = await axios.get(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching preset ${id}:`, error);
      throw error;
    }
  }

  // Get default presets for event types
  async getDefaults() {
    try {
      const response = await axios.get(`${API_BASE}/defaults`);
      return response.data;
    } catch (error) {
      console.error('Error fetching default presets:', error);
      throw error;
    }
  }

  // Get available event types
  async getEventTypes() {
    try {
      const response = await axios.get(`${API_BASE}/types`);
      return response.data;
    } catch (error) {
      console.error('Error fetching event types:', error);
      throw error;
    }
  }

  // Create new preset
  async createPreset(presetData) {
    try {
      const response = await axios.post(API_BASE, presetData);
      return response.data;
    } catch (error) {
      console.error('Error creating preset:', error);
      throw error;
    }
  }

  // Update existing preset
  async updatePreset(id, presetData) {
    try {
      const response = await axios.put(`${API_BASE}/${id}`, presetData);
      return response.data;
    } catch (error) {
      console.error(`Error updating preset ${id}:`, error);
      throw error;
    }
  }

  // Delete preset
  async deletePreset(id) {
    try {
      const response = await axios.delete(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting preset ${id}:`, error);
      throw error;
    }
  }

  // Apply preset and track usage
  async applyPreset(id, currentConfig = {}) {
    try {
      const response = await axios.post(`${API_BASE}/${id}/apply`, { currentConfig });
      return response.data;
    } catch (error) {
      console.error(`Error applying preset ${id}:`, error);
      throw error;
    }
  }

  // Add feedback/rating for preset
  async addFeedback(id, rating, comment = '') {
    try {
      const response = await axios.post(`${API_BASE}/${id}/feedback`, { rating, comment });
      return response.data;
    } catch (error) {
      console.error(`Error adding feedback for preset ${id}:`, error);
      throw error;
    }
  }

  // Get usage statistics (Admin only)
  async getUsageStats() {
    try {
      const response = await axios.get(`${API_BASE}/stats/usage`);
      return response.data;
    } catch (error) {
      console.error('Error fetching usage statistics:', error);
      throw error;
    }
  }

  // Initialize default presets (Admin only)
  async initializeDefaults() {
    try {
      const response = await axios.post(`${API_BASE}/initialize-defaults`);
      return response.data;
    } catch (error) {
      console.error('Error initializing default presets:', error);
      throw error;
    }
  }

  // Utility methods

  // Get presets by event type
  async getPresetsByType(eventType) {
    return this.getPresets({ eventType });
  }

  // Get user's presets
  async getUserPresets(userId) {
    return this.getPresets({ createdBy: userId });
  }

  // Get popular presets
  async getPopularPresets() {
    return this.getPresets({ sortBy: 'popularity', sortOrder: 'desc' });
  }

  // Get recently used presets
  async getRecentPresets() {
    return this.getPresets({ sortBy: 'recent', sortOrder: 'desc' });
  }

  // Search presets
  async searchPresets(query) {
    return this.getPresets({ search: query });
  }

  // Validate preset configuration
  validatePresetConfig(config) {
    const errors = [];

    if (!config.name || config.name.trim().length === 0) {
      errors.push('Preset name is required');
    }

    if (config.name && config.name.length > 100) {
      errors.push('Preset name must be 100 characters or less');
    }

    if (!config.eventType) {
      errors.push('Event type is required');
    }

    if (!config.layerConfig) {
      errors.push('Layer configuration is required');
    }

    if (config.quickActions && Array.isArray(config.quickActions)) {
      config.quickActions.forEach((action, index) => {
        if (!action.name) {
          errors.push(`Quick action ${index + 1} requires a name`);
        }
        if (!action.action) {
          errors.push(`Quick action ${index + 1} requires an action type`);
        }
      });
    }

    if (config.color && !/^#[0-9A-F]{6}$/i.test(config.color)) {
      errors.push('Color must be a valid hex color code');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get default layer configuration
  getDefaultLayerConfig() {
    return {
      electrical: { enabled: false, opacity: 0.5 },
      safety: { enabled: true, opacity: 1.0 },
      doors: { enabled: true, opacity: 1.0 },
      cameras: { enabled: true, opacity: 0.8 },
      hvac: { enabled: false, opacity: 0.7 },
      wifi: { enabled: false, opacity: 0.6 },
      utilities: { enabled: false, opacity: 0.5 },
      churchEvents: { enabled: true, opacity: 1.0 },
      avEquipment: { enabled: false, opacity: 1.0 },
      temperature: { enabled: false, opacity: 0.6 }
    };
  }

  // Create preset from current configuration
  createPresetFromConfig(name, eventType, currentConfig, description = '') {
    return {
      name,
      description,
      eventType,
      layerConfig: currentConfig.layers || this.getDefaultLayerConfig(),
      quickActions: currentConfig.quickActions || [],
      focusAreas: currentConfig.focusAreas || [],
      systemSettings: currentConfig.systemSettings || {},
      isPublic: false
    };
  }

  // Merge preset configuration with current state
  mergeWithCurrentConfig(presetConfig, currentConfig) {
    return {
      ...currentConfig,
      layers: {
        ...currentConfig.layers,
        ...presetConfig.layerConfig
      },
      quickActions: presetConfig.quickActions || currentConfig.quickActions || [],
      focusAreas: presetConfig.focusAreas || currentConfig.focusAreas || [],
      systemSettings: {
        ...currentConfig.systemSettings,
        ...presetConfig.systemSettings
      },
      appliedPreset: {
        id: presetConfig.id || presetConfig._id,
        name: presetConfig.name,
        eventType: presetConfig.eventType,
        appliedAt: new Date()
      }
    };
  }

  // Get quick action icon mapping
  getQuickActionIcons() {
    return {
      // AV and Media
      'check_av': 'mic',
      'start_recording': 'videocam',
      'stop_recording': 'stop',
      'conference_av': 'settings_input_hdmi',
      'sanctuary_av_check': 'speaker',
      
      // Access Control
      'unlock_doors': 'lock_open',
      'lock_doors': 'lock',
      'unlock_main_doors': 'meeting_room',
      'emergency_unlock_all': 'security',
      
      // Safety and Emergency
      'locate_aed': 'local_hospital',
      'emergency_lights': 'lightbulb',
      'emergency_shutoffs': 'power_off',
      'evacuation_mode': 'directions_run',
      
      // Environmental
      'climate_optimize': 'eco',
      'temperature_control': 'thermostat',
      'hvac_youth_mode': 'ac_unit',
      'check_wifi': 'wifi',
      
      // Event Setup
      'youth_setup': 'celebration',
      'sunday_service_setup': 'church',
      'conference_setup': 'event',
      'full_system_check': 'checklist',
      
      // Lighting
      'stage_lights': 'highlight',
      'house_lights': 'lightbulb_outline',
      'emergency_lighting': 'flashlight_on',
      
      // General
      'custom_action': 'settings',
      'maintenance_mode': 'build',
      'volunteer_tools': 'handyman'
    };
  }

  // Get event type colors
  getEventTypeColors() {
    return {
      'sunday_service': '#8BC34A',
      'youth_night': '#E91E63',
      'conference': '#673AB7',
      'wedding': '#E91E63',
      'funeral': '#607D8B',
      'community_event': '#FF9800',
      'maintenance': '#795548',
      'emergency': '#F44336',
      'custom': '#2196F3'
    };
  }

  // Format preset for display
  formatPresetForDisplay(preset) {
    const eventTypeColors = this.getEventTypeColors();
    
    return {
      ...preset,
      displayColor: preset.color || eventTypeColors[preset.eventType] || '#2196F3',
      formattedLastUsed: preset.usage?.lastUsed ? 
        new Date(preset.usage.lastUsed).toLocaleDateString() : 'Never',
      usageCount: preset.usage?.timesUsed || 0,
      rating: preset.usage?.averageRating ? 
        Math.round(preset.usage.averageRating * 10) / 10 : null,
      popularityScore: this.calculatePopularityScore(preset)
    };
  }

  // Calculate popularity score for sorting
  calculatePopularityScore(preset) {
    const usageWeight = 0.6;
    const ratingWeight = 0.4;
    const maxUsage = 100; // Normalize usage to this max
    
    const normalizedUsage = Math.min((preset.usage?.timesUsed || 0) / maxUsage, 1);
    const normalizedRating = (preset.usage?.averageRating || 3) / 5;
    
    return (normalizedUsage * usageWeight + normalizedRating * ratingWeight) * 100;
  }

  // Get recommended presets based on user activity
  async getRecommendedPresets(userRole = 'staff') {
    try {
      // Get popular presets for user's role
      const { presets } = await this.getPresets({
        sortBy: 'popularity',
        sortOrder: 'desc',
        limit: 10
      });

      // Filter by role if not admin
      const filtered = presets.filter(preset => 
        preset.isPublic && 
        (preset.allowedRoles.includes(userRole) || preset.allowedRoles.length === 0)
      );

      return filtered.slice(0, 5); // Return top 5 recommendations
    } catch (error) {
      console.error('Error getting recommended presets:', error);
      return [];
    }
  }
}

export default new EventModePresetService();