import axios from 'axios';

const API_BASE = '/api/wayfinding';

class WayfindingService {
  
  // Get all wayfinding routes with filtering and pagination
  async getRoutes(params = {}) {
    try {
      const response = await axios.get(API_BASE, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching wayfinding routes:', error);
      throw error;
    }
  }

  // Get specific route by ID
  async getRoute(id) {
    try {
      const response = await axios.get(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching route ${id}:`, error);
      throw error;
    }
  }

  // Get available route types and options
  async getTypes() {
    try {
      const response = await axios.get(`${API_BASE}/types`);
      return response.data;
    } catch (error) {
      console.error('Error fetching wayfinding types:', error);
      throw error;
    }
  }

  // Get emergency evacuation routes
  async getEmergencyRoutes(buildingId, floorId = null) {
    try {
      const params = floorId ? { floorId } : {};
      const response = await axios.get(`${API_BASE}/emergency/${buildingId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching emergency routes:', error);
      throw error;
    }
  }

  // Search routes
  async searchRoutes(query, filters = {}) {
    try {
      const params = { q: query, ...filters };
      const response = await axios.get(`${API_BASE}/search`, { params });
      return response.data;
    } catch (error) {
      console.error('Error searching routes:', error);
      throw error;
    }
  }

  // Create new route (Admin/Facilities/Security only)
  async createRoute(routeData) {
    try {
      const response = await axios.post(API_BASE, routeData);
      return response.data;
    } catch (error) {
      console.error('Error creating route:', error);
      throw error;
    }
  }

  // Update route (Creator or Admin)
  async updateRoute(id, routeData) {
    try {
      const response = await axios.put(`${API_BASE}/${id}`, routeData);
      return response.data;
    } catch (error) {
      console.error(`Error updating route ${id}:`, error);
      throw error;
    }
  }

  // Delete route (Creator or Admin)
  async deleteRoute(id) {
    try {
      const response = await axios.delete(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting route ${id}:`, error);
      throw error;
    }
  }

  // Generate PDF for route
  async generatePDF(id) {
    try {
      const response = await axios.post(`${API_BASE}/${id}/generate-pdf`, {}, {
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `wayfinding_route_${id}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true, message: 'PDF downloaded successfully' };
    } catch (error) {
      console.error(`Error generating PDF for route ${id}:`, error);
      throw error;
    }
  }

  // Add feedback for route
  async addFeedback(id, rating, comment = '', improvements = []) {
    try {
      const response = await axios.post(`${API_BASE}/${id}/feedback`, {
        rating,
        comment,
        improvements
      });
      return response.data;
    } catch (error) {
      console.error(`Error adding feedback for route ${id}:`, error);
      throw error;
    }
  }

  // Verify route (Facilities/Admin/Security only)
  async verifyRoute(id) {
    try {
      const response = await axios.put(`${API_BASE}/${id}/verify`);
      return response.data;
    } catch (error) {
      console.error(`Error verifying route ${id}:`, error);
      throw error;
    }
  }

  // Get statistics overview (Admin/Facilities/Security only)
  async getStatistics() {
    try {
      const response = await axios.get(`${API_BASE}/stats/overview`);
      return response.data;
    } catch (error) {
      console.error('Error fetching wayfinding statistics:', error);
      throw error;
    }
  }

  // Utility methods

  // Get routes by type
  async getRoutesByType(routeType, buildingId = null) {
    return this.getRoutes({ routeType, buildingId });
  }

  // Get wheelchair accessible routes
  async getAccessibleRoutes(buildingId = null) {
    return this.getRoutes({ wheelchairAccessible: true, buildingId });
  }

  // Get routes for specific building and floor
  async getRoutesForFloor(buildingId, floorId) {
    return this.getRoutes({ buildingId, floorId });
  }

  // Validate route data
  validateRouteData(data) {
    const errors = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Route name is required');
    }

    if (!data.routeType) {
      errors.push('Route type is required');
    }

    if (!data.startPoint || !data.startPoint.description) {
      errors.push('Start point description is required');
    }

    if (!data.endPoint || !data.endPoint.description) {
      errors.push('End point description is required');
    }

    if (!data.startPoint || !data.startPoint.buildingId) {
      errors.push('Start point building is required');
    }

    if (!data.endPoint || !data.endPoint.buildingId) {
      errors.push('End point building is required');
    }

    if (!data.steps || data.steps.length === 0) {
      errors.push('At least one step is required');
    }

    if (data.steps) {
      data.steps.forEach((step, index) => {
        if (!step.instruction) {
          errors.push(`Step ${index + 1} requires an instruction`);
        }
      });
    }

    // Check accessibility conflicts
    if (data.accessibility?.wheelchairAccessible && data.accessibility?.stairsRequired) {
      errors.push('Route cannot be wheelchair accessible and require stairs');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get default route structure
  getDefaultRouteStructure() {
    return {
      name: '',
      description: '',
      routeType: 'custom',
      priority: 'medium',
      startPoint: {
        buildingId: '',
        floorId: '',
        room: '',
        description: '',
        coordinates: { x: 0, y: 0 },
        landmark: ''
      },
      endPoint: {
        buildingId: '',
        floorId: '',
        room: '',
        description: '',
        coordinates: { x: 0, y: 0 },
        landmark: ''
      },
      steps: [],
      estimatedTime: {
        walking: 5,
        wheelchair: 7,
        carrying: 8
      },
      difficulty: 'easy',
      accessibility: {
        wheelchairAccessible: true,
        elevatorRequired: false,
        stairsRequired: false,
        heavyDoorsWarning: false,
        narrowPassagesWarning: false,
        notes: ''
      },
      safetyInfo: {
        emergencyExits: [],
        firstAidStations: [],
        aedLocations: [],
        utilityShutoffs: [],
        hazards: []
      },
      associatedResources: [],
      conditions: {
        timeRestrictions: [],
        eventRestrictions: [],
        seasonalConsiderations: [],
        maintenanceNotes: ''
      },
      pdfSettings: {
        includeFloorplans: true,
        includePhotos: true,
        includeSafetyInfo: true,
        paperSize: 'letter',
        orientation: 'portrait',
        colorMode: 'color',
        fontSize: 'medium',
        showGrid: false,
        includeQRCode: true
      },
      isPublic: true,
      allowedRoles: ['volunteer', 'staff'],
      status: 'active'
    };
  }

  // Get default step structure
  getDefaultStepStructure() {
    return {
      stepNumber: 1,
      instruction: '',
      direction: 'straight',
      distance: {
        value: 0,
        description: ''
      },
      landmark: '',
      warning: '',
      alternativeAction: '',
      buildingId: '',
      floorId: '',
      coordinates: { x: 0, y: 0 },
      photos: []
    };
  }

  // Create step-by-step route from coordinates
  generateStepsFromCoordinates(waypoints) {
    const steps = [];
    
    for (let i = 0; i < waypoints.length - 1; i++) {
      const current = waypoints[i];
      const next = waypoints[i + 1];
      
      // Calculate direction
      const direction = this.calculateDirection(current, next);
      
      // Calculate distance
      const distance = this.calculateDistance(current, next);
      
      steps.push({
        ...this.getDefaultStepStructure(),
        stepNumber: i + 1,
        instruction: this.generateInstructionText(current, next, direction),
        direction,
        distance: {
          value: distance,
          description: `${Math.round(distance)} feet`
        },
        coordinates: current
      });
    }
    
    // Add final destination step
    steps.push({
      ...this.getDefaultStepStructure(),
      stepNumber: steps.length + 1,
      instruction: 'You have arrived at your destination',
      direction: 'stop',
      coordinates: waypoints[waypoints.length - 1]
    });
    
    return steps;
  }

  // Calculate direction between two points
  calculateDirection(point1, point2) {
    const deltaX = point2.x - point1.x;
    const deltaY = point2.y - point1.y;
    
    const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
    
    if (angle >= -45 && angle <= 45) return 'right';
    if (angle >= 45 && angle <= 135) return 'down';
    if (angle >= 135 || angle <= -135) return 'left';
    return 'up';
  }

  // Calculate distance between two points (in feet, assuming scale)
  calculateDistance(point1, point2) {
    const deltaX = point2.x - point1.x;
    const deltaY = point2.y - point1.y;
    
    // Assuming 1 unit = 1 foot (adjust based on your floor plan scale)
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  }

  // Generate instruction text based on points and direction
  generateInstructionText(current, next, direction) {
    const directionText = {
      'straight': 'Continue straight',
      'left': 'Turn left',
      'right': 'Turn right',
      'up': 'Head north',
      'down': 'Head south'
    };
    
    return directionText[direction] || 'Continue';
  }

  // Get route type configuration
  getRouteTypeConfig() {
    return {
      evacuation: {
        label: 'Evacuation Routes',
        icon: 'directions_run',
        color: '#F44336',
        priority: 'emergency',
        description: 'Emergency evacuation paths to safety'
      },
      accessibility: {
        label: 'Accessibility Routes',
        icon: 'accessible',
        color: '#2196F3',
        priority: 'high',
        description: 'ADA-compliant pathways'
      },
      volunteer_guide: {
        label: 'Volunteer Navigation',
        icon: 'volunteer_activism',
        color: '#4CAF50',
        priority: 'medium',
        description: 'Navigation aids for volunteers'
      },
      visitor_directions: {
        label: 'Visitor Directions',
        icon: 'directions_walk',
        color: '#FF9800',
        priority: 'medium',
        description: 'Wayfinding for guests and visitors'
      },
      utility_access: {
        label: 'Utility Access',
        icon: 'build',
        color: '#795548',
        priority: 'medium',
        description: 'Routes to utility and maintenance areas'
      },
      event_setup: {
        label: 'Event Setup Routes',
        icon: 'event',
        color: '#E91E63',
        priority: 'medium',
        description: 'Paths for event preparation and setup'
      },
      safety_patrol: {
        label: 'Safety Patrol',
        icon: 'security',
        color: '#673AB7',
        priority: 'high',
        description: 'Security and safety patrol routes'
      },
      tour_guide: {
        label: 'Building Tours',
        icon: 'tour',
        color: '#009688',
        priority: 'low',
        description: 'Guided tour pathways'
      },
      custom: {
        label: 'Custom Routes',
        icon: 'route',
        color: '#607D8B',
        priority: 'medium',
        description: 'User-defined routes'
      }
    };
  }

  // Get priority configuration
  getPriorityConfig() {
    return {
      emergency: { label: 'Emergency', color: '#F44336', icon: 'priority_high', weight: 4 },
      high: { label: 'High', color: '#FF9800', icon: 'keyboard_arrow_up', weight: 3 },
      medium: { label: 'Medium', color: '#2196F3', icon: 'remove', weight: 2 },
      low: { label: 'Low', color: '#4CAF50', icon: 'keyboard_arrow_down', weight: 1 }
    };
  }

  // Get difficulty configuration
  getDifficultyConfig() {
    return {
      easy: { label: 'Easy', color: '#4CAF50', icon: 'sentiment_satisfied', description: 'Simple, direct route' },
      moderate: { label: 'Moderate', color: '#FF9800', icon: 'sentiment_neutral', description: 'Some turns or obstacles' },
      difficult: { label: 'Difficult', color: '#F44336', icon: 'sentiment_dissatisfied', description: 'Complex navigation required' }
    };
  }

  // Format route for display
  formatRouteForDisplay(route) {
    const routeTypeConfig = this.getRouteTypeConfig();
    const priorityConfig = this.getPriorityConfig();
    const difficultyConfig = this.getDifficultyConfig();
    
    return {
      ...route,
      routeTypeInfo: routeTypeConfig[route.routeType] || routeTypeConfig.custom,
      priorityInfo: priorityConfig[route.priority] || priorityConfig.medium,
      difficultyInfo: difficultyConfig[route.difficulty] || difficultyConfig.easy,
      formattedDistance: this.formatDistance(route.totalDistance),
      formattedTime: this.formatTime(route.estimatedTime?.walking),
      accessibilityStatus: this.getAccessibilityStatus(route.accessibility),
      stepCount: route.steps?.length || 0,
      hasWarnings: route.steps?.some(step => step.warning) || false,
      hasSafetyInfo: route.safetyInfo && (
        route.safetyInfo.emergencyExits?.length > 0 ||
        route.safetyInfo.hazards?.length > 0 ||
        route.safetyInfo.aedLocations?.length > 0
      )
    };
  }

  // Format distance for display
  formatDistance(distance) {
    if (!distance || distance === 0) return 'Unknown';
    
    if (distance < 100) {
      return `${Math.round(distance)} ft`;
    } else if (distance < 1000) {
      return `${Math.round(distance / 10) * 10} ft`;
    } else {
      const miles = distance / 5280;
      return `${miles.toFixed(1)} mi`;
    }
  }

  // Format time for display
  formatTime(minutes) {
    if (!minutes || minutes === 0) return 'Unknown';
    
    if (minutes < 60) {
      return `${minutes} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
  }

  // Get accessibility status
  getAccessibilityStatus(accessibility) {
    if (!accessibility) return { status: 'unknown', label: 'Unknown', color: '#757575' };
    
    if (accessibility.wheelchairAccessible && !accessibility.stairsRequired) {
      return { status: 'accessible', label: 'Fully Accessible', color: '#4CAF50' };
    } else if (accessibility.stairsRequired) {
      return { status: 'stairs', label: 'Stairs Required', color: '#F44336' };
    } else if (accessibility.elevatorRequired) {
      return { status: 'elevator', label: 'Elevator Required', color: '#FF9800' };
    } else {
      return { status: 'partial', label: 'Partially Accessible', color: '#2196F3' };
    }
  }

  // Generate QR code URL for route
  getQRCodeURL(routeId) {
    const baseUrl = window.location.origin;
    return `${baseUrl}/wayfinding/${routeId}`;
  }

  // Get emergency route suggestions
  getEmergencyRouteSuggestions() {
    return [
      {
        name: 'Primary Evacuation - Main Exit',
        description: 'Main sanctuary to primary exit',
        priority: 'emergency',
        estimatedTime: 3
      },
      {
        name: 'Alternative Evacuation - Side Exit',
        description: 'Main sanctuary to side emergency exit',
        priority: 'emergency',
        estimatedTime: 4
      },
      {
        name: 'Basement Emergency Exit',
        description: 'Fellowship hall to basement emergency exit',
        priority: 'emergency',
        estimatedTime: 2
      }
    ];
  }

  // Calculate route efficiency score
  calculateEfficiencyScore(route) {
    let score = 100;
    
    // Deduct points for complexity
    if (route.steps?.length > 10) score -= 10;
    if (route.difficulty === 'difficult') score -= 20;
    if (route.difficulty === 'moderate') score -= 10;
    
    // Deduct points for accessibility issues
    if (!route.accessibility?.wheelchairAccessible) score -= 15;
    if (route.accessibility?.stairsRequired) score -= 10;
    
    // Deduct points for safety concerns
    if (route.safetyInfo?.hazards?.length > 0) {
      score -= route.safetyInfo.hazards.length * 5;
    }
    
    // Add points for helpful features
    if (route.steps?.some(step => step.landmark)) score += 5;
    if (route.safetyInfo?.emergencyExits?.length > 0) score += 5;
    
    return Math.max(Math.min(score, 100), 0);
  }
}

export default new WayfindingService();