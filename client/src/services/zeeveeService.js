import axios from 'axios';

/**
 * Service for interacting with the ZeeVee HDbridge API
 */
const zeeveeService = {
  /**
   * Get ZeeVee device information
   * @returns {Promise<Object>} Device information
   */
  getDeviceInfo: async () => {
    try {
      const response = await axios.get('/api/zeevee/device/info');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee device information:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee system status
   * @returns {Promise<Object>} System status
   */
  getSystemStatus: async () => {
    try {
      const response = await axios.get('/api/zeevee/system/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee system status:', error);
      throw error;
    }
  },

  /**
   * Get all ZeeVee encoders
   * @returns {Promise<Array>} List of encoders
   */
  getEncoders: async () => {
    try {
      const response = await axios.get('/api/zeevee/encoders');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee encoders:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee encoder details
   * @param {string} id - Encoder ID
   * @returns {Promise<Object>} Encoder details
   */
  getEncoderDetails: async (id) => {
    try {
      const response = await axios.get(`/api/zeevee/encoders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ZeeVee encoder details for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get all ZeeVee decoders
   * @returns {Promise<Array>} List of decoders
   */
  getDecoders: async () => {
    try {
      const response = await axios.get('/api/zeevee/decoders');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee decoders:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee decoder details
   * @param {string} id - Decoder ID
   * @returns {Promise<Object>} Decoder details
   */
  getDecoderDetails: async (id) => {
    try {
      const response = await axios.get(`/api/zeevee/decoders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ZeeVee decoder details for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get all ZeeVee video channels
   * @returns {Promise<Array>} List of channels
   */
  getChannels: async () => {
    try {
      const response = await axios.get('/api/zeevee/channels');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee channels:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee channel details
   * @param {string} id - Channel ID
   * @returns {Promise<Object>} Channel details
   */
  getChannelDetails: async (id) => {
    try {
      const response = await axios.get(`/api/zeevee/channels/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ZeeVee channel details for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Set ZeeVee encoder input
   * @param {string} id - Encoder ID
   * @param {string} input - Input source
   * @returns {Promise<Object>} Response data
   */
  setEncoderInput: async (id, input) => {
    try {
      const response = await axios.put(`/api/zeevee/encoders/${id}/input`, { input });
      return response.data;
    } catch (error) {
      console.error(`Error setting ZeeVee encoder input for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Set ZeeVee decoder output
   * @param {string} id - Decoder ID
   * @param {string} output - Output destination
   * @returns {Promise<Object>} Response data
   */
  setDecoderOutput: async (id, output) => {
    try {
      const response = await axios.put(`/api/zeevee/decoders/${id}/output`, { output });
      return response.data;
    } catch (error) {
      console.error(`Error setting ZeeVee decoder output for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Reboot ZeeVee device
   * @returns {Promise<Object>} Response data
   */
  rebootDevice: async () => {
    try {
      const response = await axios.post('/api/zeevee/system/reboot');
      return response.data;
    } catch (error) {
      console.error('Error rebooting ZeeVee device:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee network settings
   * @returns {Promise<Object>} Network settings
   */
  getNetworkSettings: async () => {
    try {
      const response = await axios.get('/api/zeevee/network/settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee network settings:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee health status
   * @returns {Promise<Object>} Health status
   */
  getHealthStatus: async () => {
    try {
      const response = await axios.get('/api/zeevee/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee health status:', error);
      throw error;
    }
  },

  /**
   * Get ZeeVee configuration
   * @returns {Promise<Object>} Configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/zeevee/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching ZeeVee configuration:', error);
      throw error;
    }
  },

  /**
   * Save ZeeVee configuration
   * @param {Object} config - Configuration object
   * @param {string} config.host - Host
   * @param {number} config.port - Port
   * @param {string} config.username - Username
   * @param {string} config.password - Password
   * @returns {Promise<Object>} Response data
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/zeevee/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving ZeeVee configuration:', error);
      throw error;
    }
  },

  /**
   * Set up ZeeVee with one click
   * @returns {Promise<Object>} Response data
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/zeevee/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up ZeeVee with one click:', error);
      throw error;
    }
  }
};

export default zeeveeService;