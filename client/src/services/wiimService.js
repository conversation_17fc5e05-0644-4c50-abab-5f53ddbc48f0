import axios from 'axios';

/**
 * WiiM API Service
 * Handles client-side API calls to the WiiM endpoints
 */
const wiimService = {
  /**
   * Get device information
   * @returns {Promise<Object>} Device information
   */
  getDeviceInfo: async () => {
    try {
      const response = await axios.get('/api/wiim/device');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM device information:', error);
      throw error;
    }
  },

  /**
   * Get current playback status
   * @returns {Promise<Object>} Playback status
   */
  getPlaybackStatus: async () => {
    try {
      const response = await axios.get('/api/wiim/playback');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM playback status:', error);
      throw error;
    }
  },

  /**
   * Play
   * @returns {Promise<Object>} Response data
   */
  play: async () => {
    try {
      const response = await axios.post('/api/wiim/playback/play');
      return response.data;
    } catch (error) {
      console.error('Error sending play command to WiiM:', error);
      throw error;
    }
  },

  /**
   * Pause
   * @returns {Promise<Object>} Response data
   */
  pause: async () => {
    try {
      const response = await axios.post('/api/wiim/playback/pause');
      return response.data;
    } catch (error) {
      console.error('Error sending pause command to WiiM:', error);
      throw error;
    }
  },

  /**
   * Next track
   * @returns {Promise<Object>} Response data
   */
  next: async () => {
    try {
      const response = await axios.post('/api/wiim/playback/next');
      return response.data;
    } catch (error) {
      console.error('Error sending next command to WiiM:', error);
      throw error;
    }
  },

  /**
   * Previous track
   * @returns {Promise<Object>} Response data
   */
  previous: async () => {
    try {
      const response = await axios.post('/api/wiim/playback/previous');
      return response.data;
    } catch (error) {
      console.error('Error sending previous command to WiiM:', error);
      throw error;
    }
  },

  /**
   * Set volume
   * @param {number} volume Volume level (0-100)
   * @returns {Promise<Object>} Response data
   */
  setVolume: async (volume) => {
    try {
      const response = await axios.post('/api/wiim/playback/volume', { volume });
      return response.data;
    } catch (error) {
      console.error(`Error setting WiiM volume to ${volume}:`, error);
      throw error;
    }
  },
  
  /**
   * Check if the current playlist has ended and advance to the next playlist if needed
   * @returns {Promise<Object>} Result of the operation
   */
  checkAndAdvancePlaylist: async () => {
    try {
      const response = await axios.get('/api/wiim/playback/check-and-advance');
      return response.data;
    } catch (error) {
      console.error('Error checking and advancing playlist on WiiM:', error);
      throw error;
    }
  },

  /**
   * Get current playlist
   * @returns {Promise<Object>} Playlist data
   */
  getPlaylist: async () => {
    try {
      const response = await axios.get('/api/wiim/playlist');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM playlist:', error);
      throw error;
    }
  },

  /**
   * Play specific track from playlist
   * @param {number} index Track index in playlist
   * @returns {Promise<Object>} Response data
   */
  playTrack: async (index) => {
    try {
      const response = await axios.post(`/api/wiim/playlist/track/${index}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing track at index ${index} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Set repeat mode
   * @param {string} mode Repeat mode ('all', 'one', 'off')
   * @returns {Promise<Object>} Response data
   */
  setRepeat: async (mode) => {
    try {
      const response = await axios.post('/api/wiim/playback/repeat', { mode });
      return response.data;
    } catch (error) {
      console.error(`Error setting WiiM repeat mode to ${mode}:`, error);
      throw error;
    }
  },

  /**
   * Set shuffle mode
   * @param {boolean} enabled Whether shuffle is enabled
   * @returns {Promise<Object>} Response data
   */
  setShuffle: async (enabled) => {
    try {
      const response = await axios.post('/api/wiim/playback/shuffle', { enabled });
      return response.data;
    } catch (error) {
      console.error(`Error setting WiiM shuffle mode to ${enabled}:`, error);
      throw error;
    }
  },

  /**
   * Get list of available playlists
   * @returns {Promise<Object>} Playlists data
   */
  getPlaylists: async () => {
    try {
      const response = await axios.get('/api/wiim/playlists');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM playlists:', error);
      throw error;
    }
  },

  /**
   * Play a specific playlist
   * @param {string} playlistId Playlist ID
   * @returns {Promise<Object>} Response data
   */
  playPlaylist: async (playlistId) => {
    try {
      const response = await axios.post(`/api/wiim/playlists/${playlistId}/play`);
      return response.data;
    } catch (error) {
      console.error(`Error playing playlist ${playlistId} on WiiM:`, error);
      throw error;
    }
  },
  
  /**
   * Get songs in a specific playlist without playing it
   * @param {string} playlistId Playlist ID
   * @returns {Promise<Object>} Playlist songs data
   */
  getPlaylistSongs: async (playlistId) => {
    try {
      const response = await axios.get(`/api/wiim/playlists/${playlistId}/songs`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching songs for playlist ${playlistId} from WiiM:`, error);
      throw error;
    }
  },

  /**
   * Search for music
   * @param {string} query Search query
   * @returns {Promise<Object>} Search results
   */
  search: async (query) => {
    try {
      const response = await axios.get('/api/wiim/search', { params: { query } });
      return response.data;
    } catch (error) {
      console.error(`Error searching for "${query}" on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Get Spotify playlists
   * @returns {Promise<Object>} Spotify playlists
   */
  getSpotifyPlaylists: async () => {
    try {
      const response = await axios.get('/api/wiim/spotify/playlists');
      return response.data;
    } catch (error) {
      console.error('Error fetching Spotify playlists from WiiM:', error);
      throw error;
    }
  },

  /**
   * Play Spotify playlist
   * @param {string} playlistId Spotify playlist ID
   * @returns {Promise<Object>} Response data
   */
  playSpotifyPlaylist: async (playlistId) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/playlists/${playlistId}/play`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify playlist ${playlistId} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Search Spotify for tracks, albums, artists, or playlists
   * @param {string} query Search query
   * @param {string} type Type of search (track, album, artist, playlist)
   * @returns {Promise<Object>} Search results
   */
  searchSpotify: async (query, type) => {
    try {
      const params = { query };
      if (type) {
        params.type = type;
      }
      const response = await axios.get('/api/wiim/spotify/search', { params });
      return response.data;
    } catch (error) {
      console.error(`Error searching Spotify for "${query}":`, error);
      throw error;
    }
  },

  /**
   * Play a Spotify track
   * @param {string} trackId Spotify track ID
   * @returns {Promise<Object>} Response data
   */
  playSpotifyTrack: async (trackId) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/tracks/${trackId}/play`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify track ${trackId} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Play a Spotify album
   * @param {string} albumId Spotify album ID
   * @returns {Promise<Object>} Response data
   */
  playSpotifyAlbum: async (albumId) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/albums/${albumId}/play`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify album ${albumId} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Play a Spotify artist's top tracks
   * @param {string} artistId Spotify artist ID
   * @returns {Promise<Object>} Response data
   */
  playSpotifyArtist: async (artistId) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/artists/${artistId}/play`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify artist ${artistId} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Get available Spotify devices for playback
   * @returns {Promise<Array>} List of available Spotify devices
   */
  getSpotifyDevices: async () => {
    try {
      const response = await axios.get('/api/wiim/spotify/devices');
      return response.data;
    } catch (error) {
      // Enhanced error logging with more details
      console.error('Error fetching Spotify devices:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      
      // Create a more user-friendly error message based on the response
      if (error.response) {
        const errorData = error.response.data;
        
        if (error.response.status === 401) {
          // Authentication error
          const errorMessage = errorData.details || errorData.error || 'Authentication failed. Please check Spotify credentials.';
          const authError = new Error(errorMessage);
          authError.status = 401;
          throw authError;
        } else if (error.response.status === 403) {
          // Permission error
          const errorMessage = errorData.details || errorData.error || 'Permission denied. Your Spotify account may not have the required permissions.';
          const permissionError = new Error(errorMessage);
          permissionError.status = 403;
          throw permissionError;
        }
      }
      
      // For other errors, throw with the original message or a default one
      throw error.response?.data?.error 
        ? new Error(error.response.data.error)
        : error;
    }
  },

  /**
   * Get current Spotify playback state
   * @returns {Promise<Object>} Current playback state
   */
  getSpotifyPlaybackState: async () => {
    try {
      const response = await axios.get('/api/wiim/spotify/playback');
      return response.data;
    } catch (error) {
      console.error('Error fetching Spotify playback state:', error);
      throw error;
    }
  },
  
  /**
   * Transfer playback to the WiiM device
   * @param {boolean} play Whether to ensure playback happens on the new device
   * @returns {Promise<Object>} Response data
   */
  transferPlaybackToWiim: async (play = true) => {
    try {
      const response = await axios.post('/api/wiim/spotify/transfer-playback', { play });
      return response.data;
    } catch (error) {
      console.error('Error transferring playback to WiiM device:', error);
      throw error;
    }
  },
  
  /**
   * Get the current Spotify queue
   * @returns {Promise<Object>} Current queue
   */
  getSpotifyQueue: async () => {
    try {
      const response = await axios.get('/api/wiim/spotify/queue');
      return response.data;
    } catch (error) {
      console.error('Error fetching Spotify queue:', error);
      throw error;
    }
  },
  
  /**
   * Add an item to the Spotify queue
   * @param {string} uri Spotify URI of the item to add (track, episode)
   * @param {string} deviceId (Optional) Spotify device ID to add the item to
   * @returns {Promise<Object>} Response data
   */
  addToSpotifyQueue: async (uri, deviceId = null) => {
    try {
      const response = await axios.post('/api/wiim/spotify/queue', { uri, deviceId });
      return response.data;
    } catch (error) {
      console.error('Error adding item to Spotify queue:', error);
      throw error;
    }
  },
  
  /**
   * Skip to the next track in the Spotify queue
   * @param {string} deviceId (Optional) Spotify device ID
   * @returns {Promise<Object>} Response data
   */
  skipToNextTrack: async (deviceId = null) => {
    try {
      const response = await axios.post('/api/wiim/spotify/next', { deviceId });
      return response.data;
    } catch (error) {
      console.error('Error skipping to next track:', error);
      throw error;
    }
  },
  
  /**
   * Skip to the previous track in the Spotify queue
   * @param {string} deviceId (Optional) Spotify device ID
   * @returns {Promise<Object>} Response data
   */
  skipToPreviousTrack: async (deviceId = null) => {
    try {
      const response = await axios.post('/api/wiim/spotify/previous', { deviceId });
      return response.data;
    } catch (error) {
      console.error('Error skipping to previous track:', error);
      throw error;
    }
  },

  /**
   * Play a Spotify track using Spotify Connect API
   * @param {string} trackId Spotify track ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  playSpotifyTrackConnect: async (trackId, deviceId = null) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/tracks/${trackId}/play-connect`, { deviceId });
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify track ${trackId} using Connect API:`, error);
      throw error;
    }
  },

  /**
   * Play a Spotify album using Spotify Connect API
   * @param {string} albumId Spotify album ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  playSpotifyAlbumConnect: async (albumId, deviceId = null) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/albums/${albumId}/play-connect`, { deviceId });
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify album ${albumId} using Connect API:`, error);
      throw error;
    }
  },

  /**
   * Play a Spotify artist using Spotify Connect API
   * @param {string} artistId Spotify artist ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  playSpotifyArtistConnect: async (artistId, deviceId = null) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/artists/${artistId}/play-connect`, { deviceId });
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify artist ${artistId} using Connect API:`, error);
      throw error;
    }
  },

  /**
   * Play a Spotify playlist using Spotify Connect API
   * @param {string} playlistId Spotify playlist ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  playSpotifyPlaylistConnect: async (playlistId, deviceId = null) => {
    try {
      const response = await axios.post(`/api/wiim/spotify/playlists/${playlistId}/play-connect`, { deviceId });
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify playlist ${playlistId} using Connect API:`, error);
      throw error;
    }
  },

  /**
   * Get list of available inputs
   * @returns {Promise<Object>} Inputs data
   */
  getInputs: async () => {
    try {
      const response = await axios.get('/api/wiim/inputs');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM inputs:', error);
      throw error;
    }
  },

  /**
   * Switch to a specific input
   * @param {string} inputId Input ID
   * @returns {Promise<Object>} Response data
   */
  switchInput: async (inputId) => {
    try {
      const response = await axios.post(`/api/wiim/inputs/${inputId}/switch`);
      return response.data;
    } catch (error) {
      console.error(`Error switching to input ${inputId} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Get WiiM health status
   * @returns {Promise<Object>} Health status data
   */
  getHealthStatus: async () => {
    try {
      const response = await axios.get('/api/wiim/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM health status:', error);
      throw error;
    }
  },

  /**
   * Get WiiM configuration
   * @returns {Promise<Object>} Configuration data
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/wiim/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching WiiM configuration:', error);
      throw error;
    }
  },

  /**
   * Save WiiM configuration
   * @param {Object} config Configuration object
   * @returns {Promise<Object>} Response data
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/wiim/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving WiiM configuration:', error);
      throw error;
    }
  },

  /**
   * Set up WiiM with one click
   * @returns {Promise<Object>} Response data
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/wiim/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up WiiM with one click:', error);
      throw error;
    }
  },

  /**
   * Seek to a specific position in the current track
   * @param {number} position Position in seconds
   * @returns {Promise<Object>} Response data
   */
  seek: async (position) => {
    try {
      const response = await axios.post('/api/wiim/playback/seek', { position });
      return response.data;
    } catch (error) {
      console.error(`Error seeking to position ${position} on WiiM:`, error);
      throw error;
    }
  },

  /**
   * Get detailed information about the current track
   * @returns {Promise<Object>} Track information
   */
  getTrackInfo: async () => {
    try {
      const response = await axios.get('/api/wiim/track');
      return response.data;
    } catch (error) {
      console.error('Error fetching track information from WiiM:', error);
      throw error;
    }
  },

  /**
   * Get equalizer settings
   * @returns {Promise<Object>} Equalizer settings
   */
  getEqualizer: async () => {
    try {
      const response = await axios.get('/api/wiim/equalizer');
      return response.data;
    } catch (error) {
      console.error('Error fetching equalizer settings from WiiM:', error);
      throw error;
    }
  },

  /**
   * Set equalizer settings
   * @param {string} preset Equalizer preset name or 'custom'
   * @param {Array<number>} bands Array of band values (for custom preset)
   * @returns {Promise<Object>} Response data
   */
  setEqualizer: async (preset, bands = []) => {
    try {
      const response = await axios.post('/api/wiim/equalizer', { preset, bands });
      return response.data;
    } catch (error) {
      console.error(`Error setting equalizer on WiiM:`, error);
      throw error;
    }
  }
};

export default wiimService;