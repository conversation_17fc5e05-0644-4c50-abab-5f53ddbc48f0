import axios from 'axios';

/**
 * AV Equipment service wrapper for Phase 8 - AV Equipment Management & Technical Support
 * Provides API wrappers for audio/visual equipment management and technical support
 */
class AVEquipmentService {
  constructor() {
    this.baseURL = '/api/av-equipment';
  }

  // ===== AV Equipment Management =====

  /**
   * Get all AV equipment with optional filtering
   */
  async getAVEquipment(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching AV equipment:', error);
      throw error;
    }
  }

  /**
   * Get specific AV equipment details
   */
  async getAVEquipmentById(id) {
    try {
      const response = await axios.get(`${this.baseURL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching AV equipment:', error);
      throw error;
    }
  }

  /**
   * Create new AV equipment
   */
  async createAVEquipment(equipmentData) {
    try {
      const response = await axios.post(`${this.baseURL}`, equipmentData);
      return response.data;
    } catch (error) {
      console.error('Error creating AV equipment:', error);
      throw error;
    }
  }

  /**
   * Update AV equipment
   */
  async updateAVEquipment(id, equipmentData) {
    try {
      const response = await axios.put(`${this.baseURL}/${id}`, equipmentData);
      return response.data;
    } catch (error) {
      console.error('Error updating AV equipment:', error);
      throw error;
    }
  }

  /**
   * Delete AV equipment (soft delete)
   */
  async deleteAVEquipment(id) {
    try {
      const response = await axios.delete(`${this.baseURL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting AV equipment:', error);
      throw error;
    }
  }

  // ===== Location and Category Queries =====

  /**
   * Get AV equipment by location
   */
  async getEquipmentByLocation(buildingId, floorId, options = {}) {
    try {
      const response = await axios.get(
        `${this.baseURL}/location/${buildingId}/${floorId}`,
        { params: options }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching equipment by location:', error);
      throw error;
    }
  }

  /**
   * Get AV equipment by category
   */
  async getEquipmentByCategory(category, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/category/${category}`, { params: options });
      return response.data;
    } catch (error) {
      console.error('Error fetching equipment by category:', error);
      throw error;
    }
  }

  // ===== Maintenance and Service Operations =====

  /**
   * Get equipment requiring maintenance
   */
  async getMaintenanceDueEquipment(daysAhead = 30) {
    try {
      const response = await axios.get(`${this.baseURL}/maintenance/due`, {
        params: { daysAhead }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance due equipment:', error);
      throw error;
    }
  }

  /**
   * Add service record to equipment
   */
  async addServiceRecord(equipmentId, serviceData) {
    try {
      const response = await axios.post(`${this.baseURL}/${equipmentId}/service`, serviceData);
      return response.data;
    } catch (error) {
      console.error('Error adding service record:', error);
      throw error;
    }
  }

  /**
   * Update equipment status
   */
  async updateEquipmentStatus(equipmentId, status, notes = '') {
    try {
      const response = await axios.put(`${this.baseURL}/${equipmentId}/status`, { status, notes });
      return response.data;
    } catch (error) {
      console.error('Error updating equipment status:', error);
      throw error;
    }
  }

  // ===== Session Management =====

  /**
   * Start equipment usage session
   */
  async startEquipmentSession(equipmentId, service) {
    try {
      const response = await axios.post(`${this.baseURL}/${equipmentId}/session/start`, { service });
      return response.data;
    } catch (error) {
      console.error('Error starting equipment session:', error);
      throw error;
    }
  }

  /**
   * End equipment usage session
   */
  async endEquipmentSession(equipmentId) {
    try {
      const response = await axios.post(`${this.baseURL}/${equipmentId}/session/end`);
      return response.data;
    } catch (error) {
      console.error('Error ending equipment session:', error);
      throw error;
    }
  }

  // ===== Alert Management =====

  /**
   * Get equipment with active alerts
   */
  async getEquipmentWithAlerts(severity = null) {
    try {
      const response = await axios.get(`${this.baseURL}/alerts/active`, {
        params: severity ? { severity } : {}
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching equipment with alerts:', error);
      throw error;
    }
  }

  /**
   * Add alert to equipment
   */
  async addAlert(equipmentId, alertData) {
    try {
      const response = await axios.post(`${this.baseURL}/${equipmentId}/alert`, alertData);
      return response.data;
    } catch (error) {
      console.error('Error adding alert:', error);
      throw error;
    }
  }

  /**
   * Acknowledge equipment alert
   */
  async acknowledgeAlert(equipmentId, alertId) {
    try {
      const response = await axios.put(`${this.baseURL}/${equipmentId}/alert/${alertId}/acknowledge`);
      return response.data;
    } catch (error) {
      console.error('Error acknowledging alert:', error);
      throw error;
    }
  }

  // ===== Analytics and Reporting =====

  /**
   * Get analytics summary for AV equipment
   */
  async getAnalyticsSummary(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/analytics/summary`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching analytics summary:', error);
      throw error;
    }
  }

  /**
   * Seed demo AV equipment data
   */
  async seedDemoData(buildingId, floorId, clearExisting = false) {
    try {
      const response = await axios.post(`${this.baseURL}/seed-demo-data`, {
        buildingId,
        floorId,
        clearExisting
      });
      return response.data;
    } catch (error) {
      console.error('Error seeding demo data:', error);
      throw error;
    }
  }

  // ===== Static Helper Methods =====

  /**
   * Get equipment category options for UI
   */
  getEquipmentCategories() {
    return [
      { value: 'audio_mixer', label: 'Audio Mixer', icon: '🎛️', color: '#FF9800' },
      { value: 'microphone_wireless', label: 'Wireless Microphone', icon: '🎤', color: '#2196F3' },
      { value: 'microphone_wired', label: 'Wired Microphone', icon: '🎙️', color: '#2196F3' },
      { value: 'microphone_headset', label: 'Headset Microphone', icon: '🎧', color: '#2196F3' },
      { value: 'speakers', label: 'Speakers', icon: '🔊', color: '#4CAF50' },
      { value: 'amplifier', label: 'Amplifier', icon: '📻', color: '#795548' },
      { value: 'projector', label: 'Projector', icon: '📽️', color: '#9C27B0' },
      { value: 'screen', label: 'Screen', icon: '🖥️', color: '#607D8B' },
      { value: 'camera_ptz', label: 'PTZ Camera', icon: '📹', color: '#E91E63' },
      { value: 'camera_fixed', label: 'Fixed Camera', icon: '📷', color: '#E91E63' },
      { value: 'lighting_control', label: 'Lighting Control', icon: '💡', color: '#FFC107' },
      { value: 'lighting_fixture', label: 'Lighting Fixture', icon: '🔆', color: '#FFC107' },
      { value: 'recording_device', label: 'Recording Device', icon: '⏺️', color: '#F44336' },
      { value: 'streaming_encoder', label: 'Streaming Encoder', icon: '📡', color: '#00BCD4' },
      { value: 'monitor', label: 'Monitor', icon: '🖥️', color: '#607D8B' },
      { value: 'cable_snake', label: 'Cable Snake', icon: '🔌', color: '#424242' },
      { value: 'di_box', label: 'DI Box', icon: '📦', color: '#795548' },
      { value: 'signal_processor', label: 'Signal Processor', icon: '⚙️', color: '#9E9E9E' },
      { value: 'power_conditioner', label: 'Power Conditioner', icon: '🔋', color: '#FF5722' },
      { value: 'rack_equipment', label: 'Rack Equipment', icon: '🗄️', color: '#424242' },
      { value: 'other', label: 'Other', icon: '📋', color: '#757575' }
    ];
  }

  /**
   * Get equipment status options
   */
  getEquipmentStatuses() {
    return [
      { value: 'active', label: 'Active', color: '#4CAF50', icon: '✅' },
      { value: 'standby', label: 'Standby', color: '#FF9800', icon: '⏸️' },
      { value: 'maintenance', label: 'Maintenance', color: '#FFC107', icon: '🔧' },
      { value: 'failed', label: 'Failed', color: '#F44336', icon: '❌' },
      { value: 'retired', label: 'Retired', color: '#9E9E9E', icon: '📦' }
    ];
  }

  /**
   * Get health status options
   */
  getHealthStatuses() {
    return [
      { value: 'excellent', label: 'Excellent', color: '#4CAF50', icon: '💚' },
      { value: 'good', label: 'Good', color: '#8BC34A', icon: '💛' },
      { value: 'warning', label: 'Warning', color: '#FF9800', icon: '⚠️' },
      { value: 'critical', label: 'Critical', color: '#F44336', icon: '🔴' },
      { value: 'unknown', label: 'Unknown', color: '#9E9E9E', icon: '❓' }
    ];
  }

  /**
   * Get mounting type options
   */
  getMountingTypes() {
    return [
      { value: 'ceiling', label: 'Ceiling', icon: '⬆️' },
      { value: 'wall', label: 'Wall', icon: '🏠' },
      { value: 'floor', label: 'Floor', icon: '⬇️' },
      { value: 'rack', label: 'Rack', icon: '🗄️' },
      { value: 'portable', label: 'Portable', icon: '🎒' },
      { value: 'handheld', label: 'Handheld', icon: '✋' }
    ];
  }

  /**
   * Get service type options
   */
  getServiceTypes() {
    return [
      { value: 'installation', label: 'Installation', color: '#2196F3', icon: '🔨' },
      { value: 'maintenance', label: 'Maintenance', color: '#4CAF50', icon: '🔧' },
      { value: 'repair', label: 'Repair', color: '#FF9800', icon: '🛠️' },
      { value: 'calibration', label: 'Calibration', color: '#9C27B0', icon: '📐' },
      { value: 'firmware_update', label: 'Firmware Update', color: '#00BCD4', icon: '📱' },
      { value: 'replacement', label: 'Replacement', color: '#F44336', icon: '🔄' }
    ];
  }

  /**
   * Get alert severity options
   */
  getAlertSeverities() {
    return [
      { value: 'info', label: 'Info', color: '#2196F3', weight: 1 },
      { value: 'warning', label: 'Warning', color: '#FF9800', weight: 2 },
      { value: 'critical', label: 'Critical', color: '#F44336', weight: 3 }
    ];
  }

  /**
   * Get color for equipment category
   */
  getCategoryColor(category) {
    const categoryInfo = this.getEquipmentCategories().find(c => c.value === category);
    return categoryInfo?.color || '#9E9E9E';
  }

  /**
   * Get icon for equipment category
   */
  getCategoryIcon(category) {
    const categoryInfo = this.getEquipmentCategories().find(c => c.value === category);
    return categoryInfo?.icon || '📋';
  }

  /**
   * Get color for equipment status
   */
  getStatusColor(status) {
    const statusInfo = this.getEquipmentStatuses().find(s => s.value === status);
    return statusInfo?.color || '#9E9E9E';
  }

  /**
   * Get color for health status
   */
  getHealthColor(health) {
    const healthInfo = this.getHealthStatuses().find(h => h.value === health);
    return healthInfo?.color || '#9E9E9E';
  }

  /**
   * Format equipment for display
   */
  formatEquipmentDisplay(equipment) {
    const categoryInfo = this.getEquipmentCategories().find(c => c.value === equipment.category);
    const statusInfo = this.getEquipmentStatuses().find(s => s.value === equipment.status?.operational);
    const healthInfo = this.getHealthStatuses().find(h => h.value === equipment.status?.health);
    
    return {
      ...equipment,
      displayName: equipment.displayName || equipment.name,
      categoryIcon: categoryInfo?.icon || '📋',
      categoryLabel: categoryInfo?.label || equipment.category,
      categoryColor: categoryInfo?.color || '#9E9E9E',
      statusIcon: statusInfo?.icon || '❓',
      statusLabel: statusInfo?.label || equipment.status?.operational,
      statusColor: statusInfo?.color || '#9E9E9E',
      healthIcon: healthInfo?.icon || '❓',
      healthLabel: healthInfo?.label || equipment.status?.health,
      healthColor: healthInfo?.color || '#9E9E9E',
      isActive: equipment.status?.operational === 'active',
      hasSession: equipment.usage?.currentSession?.active || false,
      hasAlerts: equipment.alertSummary?.total > 0,
      maintenanceStatus: equipment.maintenanceStatus
    };
  }

  /**
   * Calculate maintenance priority
   */
  calculateMaintenancePriority(equipment) {
    const alerts = equipment.alertSummary || { critical: 0, warning: 0 };
    const maintenanceStatus = equipment.maintenanceStatus;
    
    if (alerts.critical > 0 || equipment.status?.operational === 'failed') {
      return { level: 'critical', score: 4, reason: 'Critical alerts or equipment failure' };
    }
    
    if (maintenanceStatus === 'overdue') {
      return { level: 'high', score: 3, reason: 'Maintenance overdue' };
    }
    
    if (alerts.warning > 0 || maintenanceStatus === 'due_soon') {
      return { level: 'medium', score: 2, reason: 'Warnings or maintenance due soon' };
    }
    
    if (maintenanceStatus === 'upcoming') {
      return { level: 'low', score: 1, reason: 'Upcoming maintenance' };
    }
    
    return { level: 'none', score: 0, reason: 'No immediate attention needed' };
  }

  /**
   * Generate equipment troubleshooting guide
   */
  generateTroubleshootingGuide(equipment) {
    const category = equipment.category;
    const commonIssues = {
      'microphone_wireless': [
        { problem: 'No audio output', solution: 'Check battery, frequency, and receiver connection', difficulty: 'easy' },
        { problem: 'Audio dropouts', solution: 'Check for interference, adjust frequency', difficulty: 'medium' },
        { problem: 'Low audio level', solution: 'Check input gain and battery level', difficulty: 'easy' }
      ],
      'projector': [
        { problem: 'No image display', solution: 'Check power, input source, and cable connections', difficulty: 'easy' },
        { problem: 'Dim or dark image', solution: 'Check lamp life, replace if needed', difficulty: 'medium' },
        { problem: 'Color issues', solution: 'Check color settings and cable connections', difficulty: 'easy' }
      ],
      'camera_ptz': [
        { problem: 'Camera not responding', solution: 'Check power and network connection', difficulty: 'easy' },
        { problem: 'Poor image quality', solution: 'Clean lens, check focus and exposure settings', difficulty: 'easy' },
        { problem: 'PTZ controls not working', solution: 'Check control protocol and address settings', difficulty: 'medium' }
      ],
      'audio_mixer': [
        { problem: 'No output audio', solution: 'Check main fader, output routing, and connections', difficulty: 'easy' },
        { problem: 'Feedback/squealing', solution: 'Reduce gain, check microphone placement', difficulty: 'medium' },
        { problem: 'Channel not working', solution: 'Check channel fader, mute, and input connections', difficulty: 'easy' }
      ]
    };

    return commonIssues[category] || [
      { problem: 'Equipment not responding', solution: 'Check power and connections', difficulty: 'easy' },
      { problem: 'Performance issues', solution: 'Check settings and restart if needed', difficulty: 'medium' }
    ];
  }

  /**
   * Validate equipment data for creation/update
   */
  validateEquipmentData(data) {
    const errors = [];

    // Required fields
    if (!data.equipmentId) errors.push('Equipment ID is required');
    if (!data.name) errors.push('Name is required');
    if (!data.category) errors.push('Category is required');
    if (!data.location?.buildingId) errors.push('Building location is required');

    // Equipment ID format validation
    if (data.equipmentId && !/^[A-Z0-9\-_]+$/i.test(data.equipmentId)) {
      errors.push('Equipment ID can only contain letters, numbers, hyphens, and underscores');
    }

    // Network configuration validation
    if (data.specifications?.connectivity?.network?.ipAddress) {
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      if (!ipRegex.test(data.specifications.connectivity.network.ipAddress)) {
        errors.push('Invalid IP address format');
      }
    }

    // Maintenance interval validation
    if (data.maintenance?.preventiveMaintenance?.interval) {
      if (data.maintenance.preventiveMaintenance.interval < 1 || data.maintenance.preventiveMaintenance.interval > 3650) {
        errors.push('Maintenance interval must be between 1 and 3650 days');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

const avEquipmentService = new AVEquipmentService();
export default avEquipmentService;