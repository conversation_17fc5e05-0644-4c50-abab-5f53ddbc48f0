import axios from 'axios';

/**
 * Get all menu items
 * @param {Object} filters Optional filters (category, type)
 * @returns {Promise<Array>} Array of menu item objects
 */
export const getMenuItems = async (filters = {}) => {
  try {
    const params = new URLSearchParams();
    if (filters.category) {
      params.append('category', filters.category);
    }
    if (filters.type) {
      params.append('type', filters.type);
    }

    const url = `/api/menu-items${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching menu items:', error);
    throw error;
  }
};

/**
 * Search menu items
 * @param {string} searchTerm Search term
 * @returns {Promise<Array>} Array of menu item objects
 */
export const searchMenuItems = async (searchTerm) => {
  try {
    const response = await axios.get(`/api/menu-items/search?q=${encodeURIComponent(searchTerm)}`);
    return response.data;
  } catch (error) {
    console.error('Error searching menu items:', error);
    throw error;
  }
};

/**
 * Get all unique categories used by menu items
 * @returns {Promise<Array>} Array of category names
 */
export const getMenuItemCategories = async () => {
  try {
    const response = await axios.get('/api/menu-items/categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching menu item categories:', error);
    throw error;
  }
};

/**
 * Get a menu item by ID
 * @param {string} id Menu item ID
 * @returns {Promise<Object>} Menu item object
 */
export const getMenuItemById = async (id) => {
  try {
    const response = await axios.get(`/api/menu-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching menu item ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new menu item
 * @param {Object} menuItemData Menu item data
 * @returns {Promise<Object>} Created menu item object
 */
export const createMenuItem = async (menuItemData) => {
  try {
    const response = await axios.post('/api/menu-items', menuItemData);
    return response.data;
  } catch (error) {
    console.error('Error creating menu item:', error);
    throw error;
  }
};

/**
 * Update a menu item
 * @param {string} id Menu item ID
 * @param {Object} menuItemData Updated menu item data
 * @returns {Promise<Object>} Updated menu item object
 */
export const updateMenuItem = async (id, menuItemData) => {
  try {
    const response = await axios.put(`/api/menu-items/${id}`, menuItemData);
    return response.data;
  } catch (error) {
    console.error(`Error updating menu item ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a menu item
 * @param {string} id Menu item ID
 * @returns {Promise<Object>} Response object
 */
export const deleteMenuItem = async (id) => {
  try {
    const response = await axios.delete(`/api/menu-items/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting menu item ${id}:`, error);
    throw error;
  }
};

/**
 * Initialize default menu items
 * @returns {Promise<Array>} Array of menu item objects
 */
export const initializeDefaultMenuItems = async () => {
  try {
    const response = await axios.post('/api/menu-items/init');
    return response.data;
  } catch (error) {
    console.error('Error initializing default menu items:', error);
    throw error;
  }
};

/**
 * Sync new default menu items (non-destructive)
 * @returns {Promise<Object>} Object with createdCount and created items
 */
export const syncNewMenuItems = async () => {
  try {
    const response = await axios.post('/api/menu-items/sync');
    return response.data;
  } catch (error) {
    console.error('Error syncing new default menu items:', error);
    throw error;
  }
};