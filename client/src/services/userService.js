import axios from 'axios';

/**
 * Get all users
 * @returns {Promise<Array>} Array of users
 */
const getAllUsers = async () => {
  try {
    const response = await axios.get('/api/users');
    return response.data;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

/**
 * Get a user by ID
 * @param {string} id User ID
 * @returns {Promise<Object>} User object
 */
const getUserById = async (id) => {
  try {
    const response = await axios.get(`/api/users/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching user with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get the current authenticated user
 * @returns {Promise<Object>} Current user object
 */
const getCurrentUser = async () => {
  try {
    const response = await axios.get('/api/users/me');
    return response.data;
  } catch (error) {
    console.error('Error fetching current user:', error);
    throw error;
  }
};

/**
 * Update user profile
 * @param {string} id User ID
 * @param {Object} userData Updated user data
 * @returns {Promise<Object>} Updated user object
 */
const updateUser = async (id, userData) => {
  try {
    const response = await axios.put(`/api/users/${id}`, userData);
    return response.data;
  } catch (error) {
    console.error(`Error updating user with ID ${id}:`, error);
    throw error;
  }
};

const userService = {
  getAllUsers,
  getUserById,
  getCurrentUser,
  updateUser
};

export default userService;