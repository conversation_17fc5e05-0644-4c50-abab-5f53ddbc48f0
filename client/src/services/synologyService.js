import axios from 'axios';

/**
 * Synology API Service
 * Handles client-side API calls to the Synology endpoints
 */
const synologyService = {
  /**
   * Set up Synology with one click
   * @param {Object} options Optional configuration options (host, port)
   * @returns {Promise<Object>} Response message
   */
  oneClickSetup: async (options = {}) => {
    try {
      const response = await axios.post('/api/synology/one-click-setup', options);
      return response.data;
    } catch (error) {
      console.error('Error setting up Synology with one click:', error);
      throw error;
    }
  },
  /**
   * Save Synology configuration
   * @param {Object} config Configuration object with host, port, username, password, and secure
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/synology/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Synology configuration:', error);
      throw error;
    }
  },

  /**
   * Get Synology configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/synology/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Synology configuration:', error);
      throw error;
    }
  },

  /**
   * List files in a directory with pagination support
   * @param {string} path Path to the directory
   * @param {Object} options Additional options including pagination and sorting
   * @param {number} options.page Page number (1-based)
   * @param {number} options.pageSize Number of items per page
   * @param {string} options.sortBy Field to sort by (name, size, time, etc.)
   * @param {string} options.sortDirection Sort direction (asc or desc)
   * @returns {Promise<Object>} Object containing files array and pagination metadata
   */
  listFiles: async (path, options = {}) => {
    try {
      const params = { path, ...options };
      const response = await axios.get('/api/synology/files', { params });
      
      // Handle both the new format (object with files and pagination) and the old format (just array of files)
      if (response.data && response.data.files) {
        // New format with pagination
        return response.data;
      } else if (Array.isArray(response.data)) {
        // Old format (just array of files)
        return {
          files: response.data,
          pagination: {
            page: 1,
            pageSize: response.data.length,
            hasMore: false
          },
          path: {
            original: path,
            adjusted: path
          }
        };
      } else {
        // Unexpected format
        console.warn('Unexpected response format from Synology API:', response.data);
        return {
          files: [],
          pagination: {
            page: 1,
            pageSize: 100,
            hasMore: false
          },
          path: {
            original: path,
            adjusted: path
          }
        };
      }
    } catch (error) {
      console.error('Error listing files from Synology:', error);
      
      // Enhance error with 2FA information if available
      if (error.response && 
          error.response.data && 
          error.response.data.requires2FA) {
        const enhancedError = new Error(error.response.data.details || 'Two-factor authentication required');
        enhancedError.requires2FA = true;
        enhancedError.details = error.response.data.details;
        throw enhancedError;
      }
      
      // Enhance error with pagination information if available
      if (error.response && 
          error.response.data && 
          error.response.data.pagination) {
        const enhancedError = new Error(error.response.data.message || 'Error listing files');
        enhancedError.pagination = error.response.data.pagination;
        throw enhancedError;
      }
      
      throw error;
    }
  },

  /**
   * Get download URL for a file
   * @param {string} path Path to the file
   * @returns {string} Download URL
   */
  getDownloadUrl: (path) => {
    const params = new URLSearchParams({ path });
    return `/api/synology/download?${params.toString()}`;
  },

  /**
   * Create a sharing link
   * @param {string} path Path to the file or folder
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Sharing link info
   */
  createSharingLink: async (path, options = {}) => {
    try {
      const response = await axios.post('/api/synology/share', { path, ...options });
      return response.data;
    } catch (error) {
      console.error('Error creating sharing link on Synology:', error);
      
      // Enhance error with 2FA information if available
      if (error.response && 
          error.response.data && 
          error.response.data.requires2FA) {
        const enhancedError = new Error(error.response.data.details || 'Two-factor authentication required');
        enhancedError.requires2FA = true;
        enhancedError.details = error.response.data.details;
        throw enhancedError;
      }
      
      throw error;
    }
  }
};

export default synologyService;
