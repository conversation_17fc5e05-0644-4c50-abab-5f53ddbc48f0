import axios from 'axios';

/**
 * Dreo Portable AC Unit API Service
 * Handles client-side API calls to the Dreo endpoints
 * Includes support for real-time updates via WebSocket
 */
const dreoService = {
  // Store device status update listeners
  statusListeners: new Map(),
  
  /**
   * Register a listener for device status updates
   * Note: Real-time updates via WebSocket have been removed as they are no longer needed.
   * This method now only stores the listener locally but no real-time updates will be received.
   * @param {string} deviceId Device ID
   * @param {Function} listener Callback function to be called when device status changes
   */
  registerStatusListener: (deviceId, listener) => {
    if (typeof listener === 'function') {
      dreoService.statusListeners.set(deviceId, listener);
      // No longer registering with server as WebSocket updates are not needed
    }
  },
  
  /**
   * Unregister a device status listener
   * @param {string} deviceId Device ID
   */
  unregisterStatusListener: (deviceId) => {
    dreoService.statusListeners.delete(deviceId);
    // No longer unregistering with server as WebSocket updates are not needed
  },
  
  /**
   * Set up Dreo with one click
   * @returns {Promise<Object>} Response message
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/dreo/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up Dreo with one click:', error);
      throw error;
    }
  },
  
  /**
   * Get all Dreo devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async (params = {}) => {
    try {
      const response = await axios.get('/api/dreo/devices', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Dreo devices:', error);
      throw error;
    }
  },

  /**
   * Get Dreo device details
   * @param {string} deviceId Device ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Device details
   */
  getDevice: async (deviceId, params = {}) => {
    try {
      const response = await axios.get(`/api/dreo/devices/${deviceId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Dreo device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Control Dreo device
   * @param {string} deviceId Device ID
   * @param {Object} command Command object with action and parameters
   * @returns {Promise<Object>} Response message
   */
  controlDevice: async (deviceId, command) => {
    try {
      const response = await axios.post(`/api/dreo/devices/${deviceId}/control`, command);
      return response.data;
    } catch (error) {
      console.error(`Error controlling Dreo device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get device status
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device status
   */
  getDeviceStatus: async (deviceId) => {
    try {
      const response = await axios.get(`/api/dreo/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Dreo device ${deviceId} status:`, error);
      throw error;
    }
  },

  /**
   * Save Dreo configuration
   * @param {Object} config Configuration object with credentials
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/dreo/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Dreo configuration:', error);
      throw error;
    }
  },

  /**
   * Get Dreo configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/dreo/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Dreo configuration:', error);
      throw error;
    }
  }
};

// WebSocket connection for real-time updates has been removed
// Live updates are no longer needed as per requirements

export default dreoService;
