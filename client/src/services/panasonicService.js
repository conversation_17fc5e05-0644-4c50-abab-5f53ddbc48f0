import axios from 'axios';

/**
 * Service for interacting with the Panasonic Pro AV Camera API
 */
const panasonicService = {
  /**
   * Initialize the Panasonic camera API
   * @returns {Promise<Object>} Initialization status
   */
  initialize: async () => {
    try {
      const response = await axios.get('/api/panasonic/initialize');
      return response.data;
    } catch (error) {
      console.error('Error initializing Panasonic camera API:', error);
      throw error;
    }
  },

  /**
   * Get Panasonic camera information
   * @returns {Promise<Object>} Camera information
   */
  getCameraInfo: async () => {
    try {
      const response = await axios.get('/api/panasonic/info');
      return response.data;
    } catch (error) {
      console.error('Error fetching Panasonic camera information:', error);
      throw error;
    }
  },

  /**
   * Get Panasonic camera status
   * @returns {Promise<Object>} Camera status
   */
  getCameraStatus: async () => {
    try {
      const response = await axios.get('/api/panasonic/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching Panasonic camera status:', error);
      throw error;
    }
  },

  /**
   * Get Panasonic camera presets
   * @returns {Promise<Array>} List of presets
   */
  getCameraPresets: async () => {
    try {
      const response = await axios.get('/api/panasonic/presets');
      return response.data;
    } catch (error) {
      console.error('Error fetching Panasonic camera presets:', error);
      throw error;
    }
  },

  /**
   * Move Panasonic camera to preset position
   * @param {string} presetId - Preset ID
   * @returns {Promise<Object>} Response data
   */
  moveToPreset: async (presetId) => {
    try {
      const response = await axios.get(`/api/panasonic/presets/${presetId}`);
      return response.data;
    } catch (error) {
      console.error(`Error moving Panasonic camera to preset ${presetId}:`, error);
      throw error;
    }
  },

  /**
   * Control Panasonic camera PTZ (Pan, Tilt, Zoom)
   * @param {Object} ptzParams - PTZ parameters
   * @param {number} ptzParams.pan - Pan value (-100 to 100)
   * @param {number} ptzParams.tilt - Tilt value (-100 to 100)
   * @param {number} ptzParams.zoom - Zoom value (-100 to 100)
   * @returns {Promise<Object>} Response data
   */
  controlPTZ: async (ptzParams) => {
    try {
      const response = await axios.post('/api/panasonic/ptz', ptzParams);
      return response.data;
    } catch (error) {
      console.error('Error controlling Panasonic camera PTZ:', error);
      throw error;
    }
  },

  /**
   * Control Panasonic camera zoom
   * @param {Object} zoomParams - Zoom parameters
   * @param {number} zoomParams.zoom - Zoom value (-100 to 100)
   * @returns {Promise<Object>} Response data
   */
  controlZoom: async (zoomParams) => {
    try {
      const response = await axios.post('/api/panasonic/zoom', zoomParams);
      return response.data;
    } catch (error) {
      console.error('Error controlling Panasonic camera zoom:', error);
      throw error;
    }
  },

  /**
   * Get Panasonic camera snapshot
   * @returns {Promise<Object>} Snapshot data
   */
  getSnapshot: async () => {
    try {
      const response = await axios.get('/api/panasonic/snapshot');
      return response.data;
    } catch (error) {
      console.error('Error fetching Panasonic camera snapshot:', error);
      throw error;
    }
  },

  /**
   * Get Panasonic camera configuration
   * @returns {Promise<Object>} Configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/panasonic/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching Panasonic camera configuration:', error);
      throw error;
    }
  },

  /**
   * Save Panasonic camera configuration
   * @param {Object} config - Configuration object
   * @param {string} config.host - Host
   * @param {number} config.port - Port
   * @param {string} config.username - Username
   * @param {string} config.password - Password
   * @returns {Promise<Object>} Response data
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/panasonic/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Panasonic camera configuration:', error);
      throw error;
    }
  }
};

export default panasonicService;