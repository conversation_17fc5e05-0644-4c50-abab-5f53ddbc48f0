import axios from 'axios';

/**
 * Ticket Tag Service
 * Provides methods for interacting with the ticket tag API
 */
const ticketTagService = {
  /**
   * Get all ticket tags
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} List of tags
   */
  getAllTags: async (params = {}) => {
    try {
      const response = await axios.get('/api/ticket-tags', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting tags:', error);
      throw error;
    }
  },

  /**
   * Get tag by ID
   * @param {string} id - Tag ID
   * @returns {Promise<Object>} Tag details
   */
  getTagById: async (id) => {
    try {
      const response = await axios.get(`/api/ticket-tags/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting tag:', error);
      throw error;
    }
  },

  /**
   * Create a new tag
   * @param {Object} tagData - Tag creation data
   * @returns {Promise<Object>} Created tag
   */
  createTag: async (tagData) => {
    try {
      const response = await axios.post('/api/ticket-tags', tagData);
      return response.data;
    } catch (error) {
      console.error('Error creating tag:', error);
      throw error;
    }
  },

  /**
   * Update a tag
   * @param {string} id - Tag ID
   * @param {Object} updateData - Tag update data
   * @returns {Promise<Object>} Updated tag
   */
  updateTag: async (id, updateData) => {
    try {
      const response = await axios.put(`/api/ticket-tags/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating tag:', error);
      throw error;
    }
  },

  /**
   * Delete a tag
   * @param {string} id - Tag ID
   * @returns {Promise<Object>} Deletion confirmation
   */
  deleteTag: async (id) => {
    try {
      const response = await axios.delete(`/api/ticket-tags/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting tag:', error);
      throw error;
    }
  },

  /**
   * Search tags by name
   * @param {string} query - Search query
   * @returns {Promise<Array>} Matching tags
   */
  searchTags: async (query) => {
    try {
      const response = await axios.get('/api/ticket-tags/search', { 
        params: { q: query }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching tags:', error);
      throw error;
    }
  },

  /**
   * Get popular tags
   * @param {number} limit - Maximum number of tags to return
   * @returns {Promise<Array>} Popular tags ordered by usage
   */
  getPopularTags: async (limit = 20) => {
    try {
      const response = await axios.get('/api/ticket-tags/popular', { 
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting popular tags:', error);
      throw error;
    }
  },

  /**
   * Get active tags for selection
   * @returns {Promise<Array>} List of active tags
   */
  getActiveTags: async () => {
    try {
      const response = await axios.get('/api/ticket-tags', { 
        params: { includeInactive: 'false' }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting active tags:', error);
      throw error;
    }
  },

  /**
   * Get tag suggestions based on partial input
   * @param {string} input - Partial tag name
   * @param {number} limit - Maximum number of suggestions
   * @returns {Promise<Array>} Tag suggestions
   */
  getTagSuggestions: async (input, limit = 10) => {
    try {
      if (!input || input.trim().length === 0) {
        return [];
      }
      
      const response = await axios.get('/api/ticket-tags/search', { 
        params: { q: input.trim(), limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting tag suggestions:', error);
      return [];
    }
  },

  /**
   * Update tag usage counts
   * @returns {Promise<Object>} Update results
   */
  updateTagCounts: async () => {
    try {
      const response = await axios.post('/api/ticket-tags/update-counts');
      return response.data;
    } catch (error) {
      console.error('Error updating tag counts:', error);
      throw error;
    }
  },

  /**
   * Get related tags for a given tag
   * @param {string} tagId - Tag ID
   * @returns {Promise<Array>} Related tags
   */
  getRelatedTags: async (tagId) => {
    try {
      const tag = await ticketTagService.getTagById(tagId);
      if (tag.relatedTags && tag.relatedTags.length > 0) {
        return tag.relatedTags;
      }
      return [];
    } catch (error) {
      console.error('Error getting related tags:', error);
      return [];
    }
  },

  /**
   * Get tags by category or context
   * @param {string} context - Context or category for filtering
   * @returns {Promise<Array>} Contextual tags
   */
  getTagsByContext: async (context) => {
    try {
      const response = await axios.get('/api/ticket-tags', { 
        params: { context }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting tags by context:', error);
      throw error;
    }
  },

  /**
   * Create multiple tags from an array of names
   * @param {Array} tagNames - Array of tag names
   * @returns {Promise<Array>} Created tags
   */
  createMultipleTags: async (tagNames) => {
    try {
      const promises = tagNames.map(name => {
        const normalizedName = name.toLowerCase().trim();
        return ticketTagService.createTag({
          name: normalizedName,
          displayName: name.trim()
        });
      });
      
      const results = await Promise.allSettled(promises);
      
      // Return successful creations and filter out duplicates/failures
      return results
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);
    } catch (error) {
      console.error('Error creating multiple tags:', error);
      throw error;
    }
  },

  /**
   * Get or create tags by names
   * @param {Array} tagNames - Array of tag names
   * @returns {Promise<Array>} Existing or created tags
   */
  getOrCreateTags: async (tagNames) => {
    try {
      const tags = [];
      
      for (const name of tagNames) {
        const normalizedName = name.toLowerCase().trim();
        
        // Try to find existing tag
        const searchResults = await ticketTagService.searchTags(normalizedName);
        const existingTag = searchResults.find(tag => tag.name === normalizedName);
        
        if (existingTag) {
          tags.push(existingTag);
        } else {
          // Create new tag
          try {
            const newTag = await ticketTagService.createTag({
              name: normalizedName,
              displayName: name.trim()
            });
            tags.push(newTag);
          } catch (createError) {
            // If creation fails (e.g., duplicate), try searching again
            console.warn(`Failed to create tag "${name}":`, createError.message);
            const retrySearch = await ticketTagService.searchTags(normalizedName);
            const retryTag = retrySearch.find(tag => tag.name === normalizedName);
            if (retryTag) {
              tags.push(retryTag);
            }
          }
        }
      }
      
      return tags;
    } catch (error) {
      console.error('Error getting or creating tags:', error);
      throw error;
    }
  },

  /**
   * Toggle tag active status
   * @param {string} id - Tag ID
   * @param {boolean} isActive - New active status
   * @returns {Promise<Object>} Updated tag
   */
  toggleTagStatus: async (id, isActive) => {
    try {
      const response = await axios.put(`/api/ticket-tags/${id}`, { isActive });
      return response.data;
    } catch (error) {
      console.error('Error toggling tag status:', error);
      throw error;
    }
  }
};

export default ticketTagService;