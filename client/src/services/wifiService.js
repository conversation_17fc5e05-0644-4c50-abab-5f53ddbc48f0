import axios from 'axios';
import unifiNetworkService from './unifiNetworkService';
import { apiRequest, handleServiceError, filterData, locationUtils, statusUtils, ServiceCache } from './utils/serviceHelpers';

/**
 * WiFi service wrapper for Phase 5 Wi-Fi coverage & APs
 * Provides API wrappers for WiFi access point and coverage management
 * Leverages existing UniFi Network integration to minimize code duplication
 */
class WiFiService {
  constructor() {
    this.baseURL = '/api/wifi';
    this.cache = new ServiceCache(300000); // 5 minute cache
  }

  // ===== Access Point Management =====

  /**
   * Get all WiFi access points with optional filtering
   */
  async getAccessPoints(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/access-points`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching WiFi access points:', error);
      throw error;
    }
  }

  /**
   * Get specific access point details
   */
  async getAccessPoint(id) {
    try {
      const response = await axios.get(`${this.baseURL}/access-points/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching WiFi access point:', error);
      throw error;
    }
  }

  /**
   * Create new access point
   */
  async createAccessPoint(accessPointData) {
    try {
      const response = await axios.post(`${this.baseURL}/access-points`, accessPointData);
      return response.data;
    } catch (error) {
      console.error('Error creating WiFi access point:', error);
      throw error;
    }
  }

  /**
   * Update access point
   */
  async updateAccessPoint(id, accessPointData) {
    try {
      const response = await axios.put(`${this.baseURL}/access-points/${id}`, accessPointData);
      return response.data;
    } catch (error) {
      console.error('Error updating WiFi access point:', error);
      throw error;
    }
  }

  /**
   * Delete access point
   */
  async deleteAccessPoint(id) {
    try {
      const response = await axios.delete(`${this.baseURL}/access-points/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting WiFi access point:', error);
      throw error;
    }
  }

  // ===== Coverage Management =====

  /**
   * Get WiFi coverage areas
   */
  async getCoverageAreas(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/coverage`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching WiFi coverage areas:', error);
      throw error;
    }
  }

  /**
   * Get specific coverage area
   */
  async getCoverageArea(id) {
    try {
      const response = await axios.get(`${this.baseURL}/coverage/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching WiFi coverage area:', error);
      throw error;
    }
  }

  /**
   * Create new coverage area
   */
  async createCoverageArea(coverageData) {
    try {
      const response = await axios.post(`${this.baseURL}/coverage`, coverageData);
      return response.data;
    } catch (error) {
      console.error('Error creating WiFi coverage area:', error);
      throw error;
    }
  }

  /**
   * Update coverage area
   */
  async updateCoverageArea(id, coverageData) {
    try {
      const response = await axios.put(`${this.baseURL}/coverage/${id}`, coverageData);
      return response.data;
    } catch (error) {
      console.error('Error updating WiFi coverage area:', error);
      throw error;
    }
  }

  /**
   * Delete coverage area
   */
  async deleteCoverageArea(id) {
    try {
      const response = await axios.delete(`${this.baseURL}/coverage/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting WiFi coverage area:', error);
      throw error;
    }
  }

  // ===== Location-based Queries =====

  /**
   * Get access points for specific floor
   */
  async getAccessPointsByLocation(buildingId, floorId, options = {}) {
    try {
      const response = await axios.get(
        `${this.baseURL}/building/${buildingId}/floor/${floorId}/access-points`,
        { params: options }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching floor access points:', error);
      throw error;
    }
  }

  /**
   * Get coverage areas for specific floor
   */
  async getCoverageAreasByLocation(buildingId, floorId) {
    try {
      const response = await axios.get(
        `${this.baseURL}/building/${buildingId}/floor/${floorId}/coverage`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching floor coverage areas:', error);
      throw error;
    }
  }

  /**
   * Get WiFi summary for floor
   */
  async getFloorWiFiSummary(buildingId, floorId) {
    try {
      const response = await axios.get(
        `${this.baseURL}/building/${buildingId}/floor/${floorId}/summary`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching WiFi floor summary:', error);
      throw error;
    }
  }

  // ===== Enhanced UniFi Integration =====

  /**
   * Sync access points with UniFi Network controller
   * Now leverages existing UniFi Network service integration
   */
  async syncWithUniFi(options = {}) {
    try {
      // Get fresh data from UniFi Network service
      const unifiAPs = await unifiNetworkService.getAccessPoints();
      
      // Process and store in WiFi database
      const syncResults = {
        synced: 0,
        created: 0,
        updated: 0,
        errors: []
      };

      for (const unifiAP of unifiAPs) {
        try {
          // Check if AP already exists in our database
          const existingAP = await this.getAccessPointByMac(unifiAP.mac);
          
          // Convert UniFi AP data to our format
          const apData = this.convertUniFiAPData(unifiAP);
          
          if (existingAP) {
            // Update existing AP
            await this.updateAccessPoint(existingAP._id, apData);
            syncResults.updated++;
          } else {
            // Create new AP
            await this.createAccessPoint(apData);
            syncResults.created++;
          }
          
          syncResults.synced++;
        } catch (apError) {
          console.error(`Error syncing AP ${unifiAP.mac}:`, apError);
          syncResults.errors.push({
            mac: unifiAP.mac,
            error: apError.message
          });
        }
      }

      // Clear cache after sync
      this.cache.clear();
      
      return syncResults;
    } catch (error) {
      return handleServiceError('syncWithUniFi', error);
    }
  }

  /**
   * Get real-time access point data from UniFi Network
   */
  async getUniFiAccessPoints(buildingId = null, floorId = null) {
    try {
      const cacheKey = `unifi_aps_${buildingId}_${floorId}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      // Get data from UniFi Network service
      const unifiAPs = await unifiNetworkService.getAccessPoints();
      
      // Filter by location if specified
      const filteredAPs = locationUtils.filterByLocation(unifiAPs, buildingId, floorId)
        .map(ap => ({
          ...ap,
          status: statusUtils.normalizeStatus(ap.state === 1 ? 'online' : 'offline'),
          signalStrength: this.calculateSignalStrength(ap),
          clientCount: ap.num_clients || 0,
          uptime: ap.uptime || 0,
          lastSeen: new Date(ap.last_seen * 1000),
          source: 'unifi'
        }));

      this.cache.set(cacheKey, filteredAPs);
      return filteredAPs;
    } catch (error) {
      return handleServiceError('getUniFiAccessPoints', error, { buildingId, floorId });
    }
  }

  /**
   * Get available UniFi devices for import
   */
  async getUniFiDevices() {
    try {
      const response = await axios.get(`${this.baseURL}/unifi-devices`);
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi devices:', error);
      throw error;
    }
  }

  // ===== Analytics and Optimization =====

  /**
   * Get coverage quality analytics
   */
  async getCoverageAnalytics(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/analytics/coverage-quality`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching coverage analytics:', error);
      throw error;
    }
  }

  /**
   * Get AP placement suggestions for event location
   */
  async getAPPlacementSuggestions(buildingId, floorId, x, y, options = {}) {
    try {
      const params = { buildingId, floorId, x, y, ...options };
      const response = await axios.get(`${this.baseURL}/suggest-ap-placement`, { params });
      return response.data;
    } catch (error) {
      console.error('Error getting AP placement suggestions:', error);
      throw error;
    }
  }

  // ===== Demo Data =====

  /**
   * Seed demo WiFi data
   */
  async seedDemoData(buildingId, floorId, clearExisting = false) {
    try {
      const response = await axios.post(`${this.baseURL}/seed-demo-data`, {
        buildingId,
        floorId,
        clearExisting
      });
      return response.data;
    } catch (error) {
      console.error('Error seeding demo WiFi data:', error);
      throw error;
    }
  }

  // ===== Static Helper Methods =====

  /**
   * Get WiFi frequency band options
   */
  getFrequencyBands() {
    return [
      { value: '2.4GHz', label: '2.4 GHz', color: '#FF9800' },
      { value: '5GHz', label: '5 GHz', color: '#2196F3' },
      { value: '6GHz', label: '6 GHz', color: '#9C27B0' },
      { value: 'Mixed', label: 'Mixed Bands', color: '#4CAF50' }
    ];
  }

  /**
   * Get coverage quality levels
   */
  getCoverageQualityLevels() {
    return [
      { value: 'excellent', label: 'Excellent', color: '#4CAF50', minScore: 80 },
      { value: 'good', label: 'Good', color: '#8BC34A', minScore: 60 },
      { value: 'fair', label: 'Fair', color: '#FFEB3B', minScore: 40 },
      { value: 'poor', label: 'Poor', color: '#FF5722', minScore: 0 }
    ];
  }

  /**
   * Get AP status types
   */
  getAPStatusTypes() {
    return [
      { value: 'connected', label: 'Connected', color: '#4CAF50' },
      { value: 'disconnected', label: 'Disconnected', color: '#F44336' },
      { value: 'adopting', label: 'Adopting', color: '#FF9800' },
      { value: 'upgrading', label: 'Upgrading', color: '#2196F3' },
      { value: 'error', label: 'Error', color: '#F44336' }
    ];
  }

  /**
   * Get event types for optimization
   */
  getEventTypes() {
    return [
      { value: 'conference', label: 'Conference', icon: '🏢' },
      { value: 'worship', label: 'Worship Service', icon: '⛪' },
      { value: 'youth_event', label: 'Youth Event', icon: '🎉' },
      { value: 'wedding', label: 'Wedding', icon: '💒' },
      { value: 'general_use', label: 'General Use', icon: '📋' },
      { value: 'live_stream', label: 'Live Stream', icon: '📹' }
    ];
  }

  /**
   * Get color for signal strength visualization
   */
  getSignalStrengthColor(strength) {
    if (strength > -50) return '#4CAF50'; // Excellent
    if (strength > -65) return '#8BC34A'; // Good
    if (strength > -75) return '#FFEB3B'; // Fair
    if (strength > -85) return '#FF9800'; // Poor
    return '#F44336'; // Very Poor
  }

  /**
   * Get color for AP health score
   */
  getHealthScoreColor(score) {
    if (score >= 90) return '#4CAF50'; // Excellent
    if (score >= 75) return '#8BC34A'; // Good
    if (score >= 60) return '#FFEB3B'; // Fair
    if (score >= 40) return '#FF9800'; // Poor
    return '#F44336'; // Critical
  }

  /**
   * Format signal strength for display
   */
  formatSignalStrength(strength) {
    if (strength > -50) return 'Excellent';
    if (strength > -65) return 'Good';
    if (strength > -75) return 'Fair';
    if (strength > -85) return 'Poor';
    return 'Very Poor';
  }

  /**
   * Calculate client density category
   */
  getClientDensityCategory(density) {
    if (density < 0.1) return { level: 'low', label: 'Low Density', color: '#4CAF50' };
    if (density < 0.3) return { level: 'medium', label: 'Medium Density', color: '#FFEB3B' };
    if (density < 0.5) return { level: 'high', label: 'High Density', color: '#FF9800' };
    return { level: 'critical', label: 'Critical Density', color: '#F44336' };
  }

  /**
   * Estimate coverage area from AP specifications
   */
  estimateCoverageArea(ap, band = '5GHz') {
    const baseRanges = {
      '2.4GHz': 150, // meters
      '5GHz': 100,   // meters
      '6GHz': 50     // meters
    };

    const baseRange = baseRanges[band] || baseRanges['5GHz'];
    
    // Adjust for mounting height and power
    const heightFactor = Math.max(0.5, Math.min(2, (ap.mounting?.height || 3) / 3));
    const powerFactor = (ap.wireless?.radios?.find(r => r.band === band)?.txPower || 20) / 20;
    
    const adjustedRange = baseRange * heightFactor * powerFactor;
    const area = Math.PI * Math.pow(adjustedRange, 2);
    
    return {
      radius: Math.round(adjustedRange),
      area: Math.round(area),
      unit: 'm²'
    };
  }

  /**
   * Generate WiFi heatmap data for visualization
   */
  generateHeatmapData(accessPoints, coverageAreas, width = 800, height = 600) {
    const gridSize = 20;
    const heatmapData = [];

    for (let x = 0; x < width; x += gridSize) {
      for (let y = 0; y < height; y += gridSize) {
        let signalStrength = -100; // Start with very weak signal
        let coveringAPs = [];

        // Calculate signal from each AP
        accessPoints.forEach(ap => {
          if (ap.status?.state !== 'connected') return;

          const dx = ap.position.x - (x / width * 100);
          const dy = ap.position.y - (y / height * 100);
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Calculate signal strength based on distance (simplified path loss model)
          const maxRange = ap.coverage?.estimatedRange?.radius5GHz || 100;
          if (distance <= maxRange) {
            // Free space path loss approximation
            const pathLoss = 20 * Math.log10(distance + 1) + 20 * Math.log10(5.8); // 5.8 GHz
            const apTxPower = ap.wireless?.radios?.find(r => r.band === '5GHz')?.txPower || 20;
            const calculatedSignal = apTxPower - pathLoss - 32.44; // Free space constant

            if (calculatedSignal > signalStrength) {
              signalStrength = calculatedSignal;
              coveringAPs.push({
                apId: ap._id,
                signal: calculatedSignal,
                distance: distance
              });
            }
          }
        });

        // Check coverage areas for additional signal information
        coverageAreas.forEach(area => {
          // Simplified point-in-polygon check for coverage areas
          if (this.isPointInPolygon(x / width * 100, y / height * 100, area.coverageArea.coordinates[0])) {
            const areaSignal = area.signalStrength?.average || -70;
            if (areaSignal > signalStrength) {
              signalStrength = areaSignal;
            }
          }
        });

        if (signalStrength > -95) { // Only include points with reasonable signal
          heatmapData.push({
            x,
            y,
            signalStrength,
            quality: this.formatSignalStrength(signalStrength),
            color: this.getSignalStrengthColor(signalStrength),
            coveringAPs
          });
        }
      }
    }

    return heatmapData;
  }

  /**
   * Check if point is inside polygon (simple ray casting)
   */
  isPointInPolygon(x, y, polygon) {
    let inside = false;
    
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0];
      const yi = polygon[i][1];
      const xj = polygon[j][0];
      const yj = polygon[j][1];
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    
    return inside;
  }

  /**
   * Calculate WiFi network performance metrics
   */
  calculateNetworkMetrics(accessPoints, coverageAreas) {
    if (!accessPoints.length && !coverageAreas.length) {
      return {
        totalCoverage: 0,
        averageSignalStrength: null,
        clientCapacity: 0,
        redundancy: 'None',
        interferenceRisk: 'Unknown'
      };
    }

    const connectedAPs = accessPoints.filter(ap => ap.status?.state === 'connected');
    const totalClients = connectedAPs.reduce((sum, ap) => sum + (ap.wireless?.clientStats?.total || 0), 0);
    const maxCapacity = connectedAPs.reduce((sum, ap) => sum + 100, 0); // Assume 100 clients per AP max

    // Calculate average signal strength from coverage areas
    const avgSignalStrength = coverageAreas.length > 0 
      ? coverageAreas.reduce((sum, area) => sum + (area.signalStrength?.average || -70), 0) / coverageAreas.length
      : connectedAPs.reduce((sum, ap) => sum - 60, 0) / (connectedAPs.length || 1); // Estimate -60dBm

    // Determine redundancy level
    let redundancy = 'None';
    if (connectedAPs.length >= 3) redundancy = 'High';
    else if (connectedAPs.length === 2) redundancy = 'Medium';
    else if (connectedAPs.length === 1) redundancy = 'Low';

    // Estimate interference risk based on AP density
    const apDensity = connectedAPs.length / 1000; // APs per 1000 m²
    let interferenceRisk = 'Low';
    if (apDensity > 0.01) interferenceRisk = 'High';
    else if (apDensity > 0.005) interferenceRisk = 'Medium';

    return {
      totalCoverage: coverageAreas.length,
      averageSignalStrength: Math.round(avgSignalStrength),
      clientCapacity: {
        current: totalClients,
        maximum: maxCapacity,
        utilization: maxCapacity > 0 ? Math.round((totalClients / maxCapacity) * 100) : 0
      },
      redundancy,
      interferenceRisk,
      connectedAPs: connectedAPs.length,
      totalAPs: accessPoints.length
    };
  }

  // ===== Helper Methods for UniFi Integration =====

  /**
   * Convert UniFi AP data to our WiFi AP format
   */
  convertUniFiAPData(unifiAP) {
    return {
      name: unifiAP.name || unifiAP.model,
      macAddress: unifiAP.mac,
      model: unifiAP.model,
      firmwareVersion: unifiAP.version,
      ipAddress: unifiAP.ip,
      status: statusUtils.normalizeStatus(unifiAP.state === 1 ? 'online' : 'offline'),
      channel24: unifiAP.radio_table?.find(r => r.name === 'wifi0')?.channel,
      channel5: unifiAP.radio_table?.find(r => r.name === 'wifi1')?.channel,
      channel6: unifiAP.radio_table?.find(r => r.name === 'wifi2')?.channel,
      frequency: this.determineFrequency(unifiAP),
      clientCount: unifiAP.num_clients || 0,
      uptime: unifiAP.uptime || 0,
      lastSeen: new Date((unifiAP.last_seen || Date.now() / 1000) * 1000),
      location: {
        x: unifiAP.x || 0,
        y: unifiAP.y || 0,
        z: unifiAP.z || 0
      },
      unifiData: {
        id: unifiAP._id,
        site_id: unifiAP.site_id,
        adopted: unifiAP.adopted,
        state: unifiAP.state,
        upgrade_to_firmware: unifiAP.upgrade_to_firmware
      },
      syncedFromUniFi: true,
      lastUniFiSync: new Date()
    };
  }

  /**
   * Get access point by MAC address
   */
  async getAccessPointByMac(macAddress) {
    try {
      const response = await axios.get(`${this.baseURL}/access-points/by-mac/${macAddress}`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Calculate signal strength indicator
   */
  calculateSignalStrength(ap) {
    // Use various UniFi metrics to determine signal strength
    if (!ap.radio_table || ap.radio_table.length === 0) {
      return 'unknown';
    }

    const avgTxPower = ap.radio_table.reduce((sum, radio) => sum + (radio.tx_power || 0), 0) / ap.radio_table.length;
    
    if (avgTxPower >= 20) return 'excellent';
    if (avgTxPower >= 15) return 'good';
    if (avgTxPower >= 10) return 'fair';
    return 'poor';
  }

  /**
   * Determine frequency bands from UniFi AP data
   */
  determineFrequency(unifiAP) {
    if (!unifiAP.radio_table) return 'unknown';
    
    const frequencies = unifiAP.radio_table
      .map(radio => {
        if (radio.channel <= 14) return '2.4GHz';
        if (radio.channel >= 36 && radio.channel <= 165) return '5GHz';
        if (radio.channel >= 1 && radio.channel <= 233) return '6GHz'; // WiFi 6E
        return 'unknown';
      })
      .filter(freq => freq !== 'unknown');
    
    if (frequencies.length === 0) return 'unknown';
    if (frequencies.length === 1) return frequencies[0];
    return 'Mixed';
  }
}

const wifiService = new WiFiService();
export default wifiService;