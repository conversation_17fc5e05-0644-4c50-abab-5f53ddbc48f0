import axios from 'axios';

/**
 * Google Admin API Service
 * Handles client-side API calls to the Google Admin endpoints
 */
const googleAdminService = {
  /**
   * Save Google Admin configuration
   * @param {Object} config Configuration object with clientId, clientSecret, redirectUri, and tokenPath
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/google-admin/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Google Admin configuration:', error);
      throw error;
    }
  },

  /**
   * Get Google Admin configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/google-admin/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Google Admin configuration:', error);
      throw error;
    }
  },

  /**
   * Get authentication URL for OAuth2 flow
   * @returns {Promise<string>} Authentication URL
   */
  getAuthUrl: async () => {
    try {
      const response = await axios.get('/api/google-admin/auth-url');
      return response.data.authUrl;
    } catch (error) {
      console.error('Error getting Google Admin auth URL:', error);
      throw error;
    }
  },

  /**
   * List users in Google Admin
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of users
   */
  listUsers: async (options = {}) => {
    try {
      const response = await axios.get('/api/google-admin/users', { params: options });
      return response.data;
    } catch (error) {
      console.error('Error listing users from Google Admin:', error);
      throw error;
    }
  },

  /**
   * Get user information
   * @param {string} userKey User email or ID
   * @returns {Promise<Object>} User information
   */
  getUser: async (userKey) => {
    try {
      const response = await axios.get(`/api/google-admin/users/${userKey}`);
      return response.data;
    } catch (error) {
      console.error('Error getting user from Google Admin:', error);
      throw error;
    }
  },

  /**
   * Create a new user
   * @param {Object} userData User data
   * @returns {Promise<Object>} Created user
   */
  createUser: async (userData) => {
    try {
      const response = await axios.post('/api/google-admin/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user in Google Admin:', error);
      throw error;
    }
  },

  /**
   * Update user information
   * @param {string} userKey User email or ID
   * @param {Object} userData User data to update
   * @returns {Promise<Object>} Updated user
   */
  updateUser: async (userKey, userData) => {
    try {
      const response = await axios.put(`/api/google-admin/users/${userKey}`, userData);
      return response.data;
    } catch (error) {
      console.error('Error updating user in Google Admin:', error);
      throw error;
    }
  },

  /**
   * Delete a user
   * @param {string} userKey User email or ID
   * @returns {Promise<Object>} Response message
   */
  deleteUser: async (userKey) => {
    try {
      const response = await axios.delete(`/api/google-admin/users/${userKey}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting user in Google Admin:', error);
      throw error;
    }
  },

  /**
   * List groups in Google Admin
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of groups
   */
  listGroups: async (options = {}) => {
    try {
      const response = await axios.get('/api/google-admin/groups', { params: options });
      return response.data;
    } catch (error) {
      console.error('Error listing groups from Google Admin:', error);
      throw error;
    }
  },

  /**
   * Add user to a group
   * @param {string} groupKey Group email or ID
   * @param {string} userEmail User email
   * @returns {Promise<Object>} Member information
   */
  addUserToGroup: async (groupKey, userEmail) => {
    try {
      const response = await axios.post(`/api/google-admin/groups/${groupKey}/members`, { userEmail });
      return response.data;
    } catch (error) {
      console.error('Error adding user to group in Google Admin:', error);
      throw error;
    }
  },

  /**
   * Remove user from a group
   * @param {string} groupKey Group email or ID
   * @param {string} memberKey Member email or ID
   * @returns {Promise<Object>} Response message
   */
  removeUserFromGroup: async (groupKey, memberKey) => {
    try {
      const response = await axios.delete(`/api/google-admin/groups/${groupKey}/members/${memberKey}`);
      return response.data;
    } catch (error) {
      console.error('Error removing user from group in Google Admin:', error);
      throw error;
    }
  },

  /**
   * Sync all users from Google Workspace to the system
   * @returns {Promise<Object>} Sync results
   */
  syncAllUsers: async () => {
    try {
      const response = await axios.post('/api/google-admin/sync-users');
      return response.data;
    } catch (error) {
      console.error('Error syncing users from Google Workspace:', error);
      throw error;
    }
  }
};

export default googleAdminService;
