import axios from 'axios';

/**
 * Presence service: tracks real user activity and updates presence to the server.
 * - Sends /api/users/me/activity at most once per minute when real activity happens.
 * - Exposes setStatus(status, statusMessage?) to set presence status.
 */
class PresenceService {
  constructor() {
    this.initialized = false;
    this.boundHandlers = [];
    this.lastSent = 0;
    this.minIntervalMs = 60 * 1000; // 1 minute
    this.visibilityState = typeof document !== 'undefined' ? document.visibilityState : 'visible';
    this.timerId = null; // Optional future use
  }

  async sendActivity() {
    const now = Date.now();
    if (now - this.lastSent < this.minIntervalMs) return;
    this.lastSent = now;
    try {
      await axios.put('/api/users/me/activity');
    } catch (err) {
      // Non-fatal
      console.warn('Presence activity update failed:', err?.response?.data || err.message);
    }
  }

  handleActivity = () => {
    // Only when tab is visible; if not visible, we treat as inactive
    if (typeof document !== 'undefined' && document.visibilityState !== 'visible') return;
    this.sendActivity();
  }

  handleVisibilityChange = () => {
    const newState = document.visibilityState;
    // On becoming visible, send an immediate activity update (throttled by lastSent)
    if (newState === 'visible') {
      this.sendActivity();
    }
    this.visibilityState = newState;
  }

  init() {
    if (this.initialized || typeof window === 'undefined') return;
    this.initialized = true;

    const events = ['mousemove', 'keydown', 'click', 'scroll', 'focus'];
    events.forEach(evt => {
      const handler = this.handleActivity;
      window.addEventListener(evt, handler, { passive: true });
      this.boundHandlers.push({ target: window, evt, handler });
    });

    if (typeof document !== 'undefined') {
      const handler = this.handleVisibilityChange;
      document.addEventListener('visibilitychange', handler);
      this.boundHandlers.push({ target: document, evt: 'visibilitychange', handler });
    }

    // Send an initial ping on init (respects throttle)
    this.sendActivity();
  }

  destroy() {
    this.boundHandlers.forEach(({ target, evt, handler }) => {
      target.removeEventListener(evt, handler);
    });
    this.boundHandlers = [];
    this.initialized = false;
  }

  async setStatus(status, statusMessage = '') {
    try {
      await axios.put('/api/users/me/status', { status, statusMessage });
    } catch (err) {
      console.error('Failed to set presence status:', err?.response?.data || err.message);
      throw err;
    }
  }
}

const presenceService = new PresenceService();
export default presenceService;
