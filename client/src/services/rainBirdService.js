import axios from 'axios';

/**
 * Rain Bird API Service
 * Handles client-side API calls to the Rain Bird endpoints
 */
const rainBirdService = {
  /**
   * One-click setup for Rain Bird
   * @returns {Promise<Object>} Setup result
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/rain-bird/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up Rain Bird with one click:', error);
      throw error;
    }
  },

  /**
   * Get all zones
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of zones
   */
  getZones: async (params = {}) => {
    try {
      const response = await axios.get('/api/rain-bird/zones', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching zones:', error);
      throw error;
    }
  },

  /**
   * Get zone details
   * @param {string} zoneId Zone ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Zone details
   */
  getZone: async (zoneId, params = {}) => {
    try {
      const response = await axios.get(`/api/rain-bird/zones/${zoneId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching zone ${zoneId}:`, error);
      throw error;
    }
  },

  /**
   * Start watering a zone
   * @param {string} zoneId Zone ID
   * @param {number} duration Duration in seconds
   * @returns {Promise<Object>} Response message
   */
  startZone: async (zoneId, duration = 300) => {
    try {
      const response = await axios.post(`/api/rain-bird/zones/${zoneId}/start`, { duration });
      return response.data;
    } catch (error) {
      console.error(`Error starting zone ${zoneId}:`, error);
      throw error;
    }
  },

  /**
   * Stop watering a zone
   * @param {string} zoneId Zone ID
   * @returns {Promise<Object>} Response message
   */
  stopZone: async (zoneId) => {
    try {
      const response = await axios.post(`/api/rain-bird/zones/${zoneId}/stop`);
      return response.data;
    } catch (error) {
      console.error(`Error stopping zone ${zoneId}:`, error);
      throw error;
    }
  },

  /**
   * Get all programs (schedules)
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of programs
   */
  getPrograms: async (params = {}) => {
    try {
      const response = await axios.get('/api/rain-bird/programs', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching programs:', error);
      throw error;
    }
  },

  /**
   * Start a program
   * @param {string} programId Program ID
   * @returns {Promise<Object>} Response message
   */
  startProgram: async (programId) => {
    try {
      const response = await axios.post(`/api/rain-bird/programs/${programId}/start`);
      return response.data;
    } catch (error) {
      console.error(`Error starting program ${programId}:`, error);
      throw error;
    }
  },

  /**
   * Stop a program
   * @param {string} programId Program ID
   * @returns {Promise<Object>} Response message
   */
  stopProgram: async (programId) => {
    try {
      const response = await axios.post(`/api/rain-bird/programs/${programId}/stop`);
      return response.data;
    } catch (error) {
      console.error(`Error stopping program ${programId}:`, error);
      throw error;
    }
  },

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  getSystemStatus: async () => {
    try {
      const response = await axios.get('/api/rain-bird/system');
      return response.data;
    } catch (error) {
      console.error('Error fetching Rain Bird system status:', error);
      throw error;
    }
  },

  /**
   * Save Rain Bird configuration
   * @param {Object} config Configuration object with credentials
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/rain-bird/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Rain Bird configuration:', error);
      throw error;
    }
  },

  /**
   * Get Rain Bird configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/rain-bird/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Rain Bird configuration:', error);
      throw error;
    }
  }
};

export default rainBirdService;