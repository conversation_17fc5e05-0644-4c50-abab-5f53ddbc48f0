import axios from 'axios';

const API_BASE = '/api/messages';

class MessageService {
  // Get all conversations for current user
  async getConversations(page = 1, limit = 20) {
    try {
      const response = await axios.get(`${API_BASE}/conversations`, {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching conversations:', error);
      throw error;
    }
  }

  // Get or create a conversation
  async getOrCreateConversation(participantIds, isGroup = false, groupName = null) {
    try {
      const response = await axios.post(`${API_BASE}/conversations`, {
        participantIds,
        isGroup,
        groupName
      });
      return response.data;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  // Get single conversation details
  async getConversation(conversationId) {
    try {
      const response = await axios.get(`${API_BASE}/conversations/${conversationId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching conversation:', error);
      throw error;
    }
  }

  // Get messages for a conversation
  async getMessages(conversationId, page = 1, limit = 50) {
    try {
      const response = await axios.get(`${API_BASE}/conversations/${conversationId}/messages`, {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  // Send a message
  async sendMessage(conversationId, content, messageType = 'text', replyTo = null) {
    try {
      const response = await axios.post(`${API_BASE}/messages`, {
        conversationId,
        content,
        messageType,
        replyTo
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Mark messages as read
  async markAsRead(conversationId) {
    try {
      const response = await axios.post(`${API_BASE}/conversations/${conversationId}/read`);
      return response.data;
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw error;
    }
  }

  // Delete a message
  async deleteMessage(messageId) {
    try {
      const response = await axios.delete(`${API_BASE}/messages/${messageId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Add reaction to message
  async addReaction(messageId, emoji) {
    try {
      const response = await axios.post(`${API_BASE}/messages/${messageId}/reactions`, {
        emoji
      });
      return response.data;
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  }

  // Set typing status
  async setTypingStatus(conversationId, isTyping) {
    try {
      const response = await axios.post(`${API_BASE}/conversations/${conversationId}/typing`, {
        isTyping
      });
      return response.data;
    } catch (error) {
      console.error('Error setting typing status:', error);
      // Don't throw for typing status errors
      return null;
    }
  }

  // Search users for new conversations
  async searchUsers(query) {
    try {
      const response = await axios.get(`${API_BASE}/users/search`, {
        params: { query }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  // Format conversation display name
  getConversationDisplayName(conversation, currentUserId) {
    if (conversation.isGroup) {
      return conversation.groupName;
    }
    
    // For 1-on-1, show the other participant's name
    const otherParticipant = conversation.participants.find(
      p => p._id !== currentUserId
    );
    return otherParticipant ? otherParticipant.name : 'Unknown User';
  }

  // Format conversation avatar
  getConversationAvatar(conversation, currentUserId) {
    if (conversation.isGroup) {
      return conversation.groupAvatar || null;
    }
    
    // For 1-on-1, show the other participant's avatar
    const otherParticipant = conversation.participants.find(
      p => p._id !== currentUserId
    );
    return otherParticipant ? otherParticipant.avatar : null;
  }

  // Format time ago
  formatTimeAgo(date) {
    const seconds = Math.floor((new Date() - new Date(date)) / 1000);
    
    let interval = Math.floor(seconds / 31536000);
    if (interval > 1) return `${interval} years ago`;
    if (interval === 1) return '1 year ago';
    
    interval = Math.floor(seconds / 2592000);
    if (interval > 1) return `${interval} months ago`;
    if (interval === 1) return '1 month ago';
    
    interval = Math.floor(seconds / 86400);
    if (interval > 1) return `${interval} days ago`;
    if (interval === 1) return '1 day ago';
    
    interval = Math.floor(seconds / 3600);
    if (interval > 1) return `${interval} hours ago`;
    if (interval === 1) return '1 hour ago';
    
    interval = Math.floor(seconds / 60);
    if (interval > 1) return `${interval} minutes ago`;
    if (interval === 1) return '1 minute ago';
    
    return 'Just now';
  }

  // Format message time
  formatMessageTime(date) {
    const messageDate = new Date(date);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (messageDate.toDateString() === today.toDateString()) {
      return messageDate.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      });
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: messageDate.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  }
}

export default new MessageService();