import axios from 'axios';

const API_BASE_URL = '/api/safety';

class SafetyService {
  // Assets
  async getAssets(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/assets`, { params });
    return res.data;
  }

  async getAsset(id) {
    const res = await axios.get(`${API_BASE_URL}/assets/${id}`);
    return res.data;
  }

  async createAsset(data) {
    const res = await axios.post(`${API_BASE_URL}/assets`, data);
    return res.data;
  }

  async updateAsset(id, data) {
    const res = await axios.put(`${API_BASE_URL}/assets/${id}`, data);
    return res.data;
  }

  async deleteAsset(id, { force = false } = {}) {
    const res = await axios.delete(`${API_BASE_URL}/assets/${id}`, { params: { force } });
    return res.data;
  }

  // Alerts & inspections (optionally used by UI)
  async getInspectionsDue(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/inspections/due`, { params });
    return res.data;
  }

  async recordInspection(assetId, data) {
    const res = await axios.post(`${API_BASE_URL}/inspections/${assetId}`, data);
    return res.data;
  }

  async getAlerts(params = {}) {
    const res = await axios.get(`${API_BASE_URL}/alerts`, { params });
    return res.data;
  }
}

const safetyService = new SafetyService();
export default safetyService;
