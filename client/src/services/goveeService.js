import axios from 'axios';

/**
 * Service for interacting with the Govee API
 */
const goveeService = {
  /**
   * Initialize the Govee API
   * @returns {Promise<Object>} Initialization status
   */
  initialize: async () => {
    try {
      const response = await axios.get('/api/govee/initialize');
      return response.data;
    } catch (error) {
      console.error('Error initializing Govee API:', error);
      throw error;
    }
  },

  /**
   * Get all Govee devices
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async () => {
    try {
      const response = await axios.get('/api/govee/devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching Govee devices:', error);
      throw error;
    }
  },

  /**
   * Get device state
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @returns {Promise<Object>} Device state
   */
  getDeviceState: async (device, model) => {
    try {
      const response = await axios.get('/api/govee/device/state', {
        params: { device, model }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching Govee device state:', error);
      throw error;
    }
  },

  /**
   * Control device
   * @param {Object} params - Control parameters
   * @param {string} params.device - Device ID
   * @param {string} params.model - Device model
   * @param {string} params.cmd - Command (turn, brightness, color, colorTem)
   * @param {*} params.value - Command value
   * @returns {Promise<Object>} Response data
   */
  controlDevice: async (params) => {
    try {
      const response = await axios.post('/api/govee/device/control', params);
      return response.data;
    } catch (error) {
      console.error('Error controlling Govee device:', error);
      throw error;
    }
  },

  /**
   * Turn device on or off
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {boolean} on - True to turn on, false to turn off
   * @returns {Promise<Object>} Response data
   */
  turnDevice: async (device, model, on) => {
    try {
      const response = await axios.post('/api/govee/device/turn', { device, model, on });
      return response.data;
    } catch (error) {
      console.error(`Error turning Govee device ${on ? 'on' : 'off'}:`, error);
      throw error;
    }
  },

  /**
   * Set device brightness
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {number} brightness - Brightness value (0-100)
   * @returns {Promise<Object>} Response data
   */
  setBrightness: async (device, model, brightness) => {
    try {
      const response = await axios.post('/api/govee/device/brightness', { device, model, brightness });
      return response.data;
    } catch (error) {
      console.error('Error setting Govee device brightness:', error);
      throw error;
    }
  },

  /**
   * Set device color
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {Object} color - Color in RGB format
   * @param {number} color.r - Red (0-255)
   * @param {number} color.g - Green (0-255)
   * @param {number} color.b - Blue (0-255)
   * @returns {Promise<Object>} Response data
   */
  setColor: async (device, model, color) => {
    try {
      const response = await axios.post('/api/govee/device/color', { device, model, color });
      return response.data;
    } catch (error) {
      console.error('Error setting Govee device color:', error);
      throw error;
    }
  },

  /**
   * Set device color temperature
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {number} temperature - Color temperature in Kelvin (2000-9000)
   * @returns {Promise<Object>} Response data
   */
  setColorTemperature: async (device, model, temperature) => {
    try {
      const response = await axios.post('/api/govee/device/color-temperature', { device, model, temperature });
      return response.data;
    } catch (error) {
      console.error('Error setting Govee device color temperature:', error);
      throw error;
    }
  },

  /**
   * Get Govee configuration
   * @returns {Promise<Object>} Configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/govee/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching Govee configuration:', error);
      throw error;
    }
  }
};

export default goveeService;