import axios from 'axios';

const API_BASE_URL = '/api/assets';

class AssetService {
  // Asset CRUD Operations
  async getAssets(params = {}) {
    try {
      const response = await axios.get(API_BASE_URL, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching assets:', error);
      throw error;
    }
  }

  async getAsset(id) {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching asset ${id}:`, error);
      throw error;
    }
  }

  async createAsset(assetData) {
    try {
      const response = await axios.post(API_BASE_URL, assetData);
      return response.data;
    } catch (error) {
      console.error('Error creating asset:', error);
      throw error;
    }
  }

  async updateAsset(id, assetData) {
    try {
      const response = await axios.put(`${API_BASE_URL}/${id}`, assetData);
      return response.data;
    } catch (error) {
      console.error(`Error updating asset ${id}:`, error);
      throw error;
    }
  }

  async deleteAsset(id) {
    try {
      const response = await axios.delete(`${API_BASE_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting asset ${id}:`, error);
      throw error;
    }
  }

  async restoreAsset(id) {
    try {
      const response = await axios.post(`${API_BASE_URL}/${id}/restore`);
      return response.data;
    } catch (error) {
      console.error(`Error restoring asset ${id}:`, error);
      throw error;
    }
  }

  // Asset Categories
  async getCategories(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching asset categories:', error);
      throw error;
    }
  }

  async getCategory(id) {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      throw error;
    }
  }

  async createCategory(categoryData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/categories`, categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  async updateCategory(id, categoryData) {
    try {
      const response = await axios.put(`${API_BASE_URL}/categories/${id}`, categoryData);
      return response.data;
    } catch (error) {
      console.error(`Error updating category ${id}:`, error);
      throw error;
    }
  }

  async deleteCategory(id, force = false) {
    try {
      const response = await axios.delete(`${API_BASE_URL}/categories/${id}`, {
        params: { force }
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting category ${id}:`, error);
      throw error;
    }
  }

  async getCategoryTree() {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories/tree`);
      return response.data;
    } catch (error) {
      console.error('Error fetching category tree:', error);
      throw error;
    }
  }

  async getCategoryStats() {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching category stats:', error);
      throw error;
    }
  }

  async getCategoryAssets(id, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories/${id}/assets`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching assets for category ${id}:`, error);
      throw error;
    }
  }

  // Asset Locations
  async getLocations(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/locations`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching asset locations:', error);
      throw error;
    }
  }

  async getLocation(id) {
    try {
      const response = await axios.get(`${API_BASE_URL}/locations/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching location ${id}:`, error);
      throw error;
    }
  }

  async createLocation(locationData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/locations`, locationData);
      return response.data;
    } catch (error) {
      console.error('Error creating location:', error);
      throw error;
    }
  }

  async updateLocation(id, locationData) {
    try {
      const response = await axios.put(`${API_BASE_URL}/locations/${id}`, locationData);
      return response.data;
    } catch (error) {
      console.error(`Error updating location ${id}:`, error);
      throw error;
    }
  }

  async deleteLocation(id, force = false) {
    try {
      const response = await axios.delete(`${API_BASE_URL}/locations/${id}`, {
        params: { force }
      });
      return response.data;
    } catch (error) {
      console.error(`Error deleting location ${id}:`, error);
      throw error;
    }
  }

  async getLocationTree(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/locations/tree`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching location tree:', error);
      throw error;
    }
  }

  async searchLocations(query, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/locations/search`, {
        params: { query, ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching locations:', error);
      throw error;
    }
  }

  async getLocationStats() {
    try {
      const response = await axios.get(`${API_BASE_URL}/locations/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching location stats:', error);
      throw error;
    }
  }

  async getLocationAssets(id, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/locations/${id}/assets`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching assets for location ${id}:`, error);
      throw error;
    }
  }

  // Asset Maintenance
  async getMaintenanceRecords(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/maintenance`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
      throw error;
    }
  }

  async getMaintenanceRecord(id) {
    try {
      const response = await axios.get(`${API_BASE_URL}/maintenance/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching maintenance record ${id}:`, error);
      throw error;
    }
  }

  async createMaintenanceRecord(recordData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/maintenance`, recordData);
      return response.data;
    } catch (error) {
      console.error('Error creating maintenance record:', error);
      throw error;
    }
  }

  async updateMaintenanceRecord(id, recordData) {
    try {
      const response = await axios.put(`${API_BASE_URL}/maintenance/${id}`, recordData);
      return response.data;
    } catch (error) {
      console.error(`Error updating maintenance record ${id}:`, error);
      throw error;
    }
  }

  async completeMaintenanceRecord(id, completionData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/maintenance/${id}/complete`, completionData);
      return response.data;
    } catch (error) {
      console.error(`Error completing maintenance record ${id}:`, error);
      throw error;
    }
  }

  async deleteMaintenanceRecord(id) {
    try {
      const response = await axios.delete(`${API_BASE_URL}/maintenance/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting maintenance record ${id}:`, error);
      throw error;
    }
  }

  async getOverdueMaintenanceRecords(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/maintenance/overdue`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching overdue maintenance records:', error);
      throw error;
    }
  }

  async getUpcomingMaintenanceRecords(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/maintenance/upcoming`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching upcoming maintenance records:', error);
      throw error;
    }
  }

  async getAssetMaintenanceStats(assetId, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/${assetId}/maintenance/stats`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching maintenance stats for asset ${assetId}:`, error);
      throw error;
    }
  }

  async scheduleRecurringMaintenance(assetId, scheduleData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/${assetId}/maintenance/schedule-recurring`, scheduleData);
      return response.data;
    } catch (error) {
      console.error(`Error scheduling recurring maintenance for asset ${assetId}:`, error);
      throw error;
    }
  }

  // Asset History and Audit
  async getAssetHistory(id, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}/history`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching history for asset ${id}:`, error);
      throw error;
    }
  }

  // Barcode and QR Code
  async getAssetBarcode(id, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}/barcode`, { 
        params,
        responseType: 'blob'
      });
      return URL.createObjectURL(response.data);
    } catch (error) {
      console.error(`Error fetching barcode for asset ${id}:`, error);
      throw error;
    }
  }

  async getAssetQRCode(id, params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/${id}/qrcode`, { 
        params,
        responseType: 'blob'
      });
      return URL.createObjectURL(response.data);
    } catch (error) {
      console.error(`Error fetching QR code for asset ${id}:`, error);
      throw error;
    }
  }

  // Bulk Operations
  async bulkUpdateAssets(assetIds, updates) {
    try {
      const response = await axios.post(`${API_BASE_URL}/bulk-update`, {
        assetIds,
        updates
      });
      return response.data;
    } catch (error) {
      console.error('Error performing bulk update:', error);
      throw error;
    }
  }

  // Export/Import
  async exportAssets(params = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/export`, { 
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting assets:', error);
      throw error;
    }
  }

  async importAssets(file, options = {}) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      Object.keys(options).forEach(key => {
        formData.append(key, options[key]);
      });

      const response = await axios.post(`${API_BASE_URL}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error importing assets:', error);
      throw error;
    }
  }

  // Utility methods
  formatCurrency(amount, currency = 'USD') {
    if (!amount && amount !== 0) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  formatDate(date) {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDateTime(date) {
    if (!date) return 'N/A';
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  calculateAge(purchaseDate) {
    if (!purchaseDate) return 'Unknown';
    const now = new Date();
    const purchase = new Date(purchaseDate);
    const diffTime = Math.abs(now - purchase);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
      return `${diffDays} days`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      return `${months} month${months > 1 ? 's' : ''}`;
    } else {
      const years = Math.floor(diffDays / 365);
      return `${years} year${years > 1 ? 's' : ''}`;
    }
  }

  getStatusColor(status) {
    const colors = {
      active: '#4caf50',
      inactive: '#757575',
      in_maintenance: '#ff9800',
      retired: '#9e9e9e',
      disposed: '#f44336',
      lost: '#f44336',
      stolen: '#f44336',
      on_loan: '#2196f3',
      reserved: '#9c27b0'
    };
    return colors[status] || '#757575';
  }

  getConditionColor(condition) {
    const colors = {
      excellent: '#4caf50',
      good: '#2196f3',
      fair: '#ff9800',
      poor: '#f44336',
      needs_repair: '#f44336'
    };
    return colors[condition] || '#757575';
  }
}

const assetService = new AssetService();
export default assetService;