import axios from 'axios';

/**
 * Service for interacting with the Colorlit Z4 Pro LED Controller API
 */
const coloritService = {
  /**
   * Get device information
   * @returns {Promise<Object>} Device information
   */
  getDeviceInfo: async () => {
    try {
      const response = await axios.get('/api/colorlit/device');
      return response.data;
    } catch (error) {
      console.error('Error fetching Colorlit device information:', error);
      throw error;
    }
  },

  /**
   * Get current status
   * @returns {Promise<Object>} Current status
   */
  getStatus: async () => {
    try {
      const response = await axios.get('/api/colorlit/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching Colorlit status:', error);
      throw error;
    }
  },

  /**
   * Set power state
   * @param {boolean} power - Whether to turn the device on (true) or off (false)
   * @returns {Promise<Object>} Response data
   */
  setPower: async (power) => {
    try {
      const response = await axios.post('/api/colorlit/power', { power });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit power state:', error);
      throw error;
    }
  },

  /**
   * Set color
   * @param {number} r - Red value (0-255)
   * @param {number} g - Green value (0-255)
   * @param {number} b - Blue value (0-255)
   * @param {number} w - White value (0-255, optional)
   * @returns {Promise<Object>} Response data
   */
  setColor: async (r, g, b, w = 0) => {
    try {
      const response = await axios.post('/api/colorlit/color', { r, g, b, w });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit color:', error);
      throw error;
    }
  },

  /**
   * Set brightness
   * @param {number} brightness - Brightness value (0-100)
   * @returns {Promise<Object>} Response data
   */
  setBrightness: async (brightness) => {
    try {
      const response = await axios.post('/api/colorlit/brightness', { brightness });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit brightness:', error);
      throw error;
    }
  },

  /**
   * Get available modes
   * @returns {Promise<Array>} List of available modes
   */
  getModes: async () => {
    try {
      const response = await axios.get('/api/colorlit/modes');
      return response.data;
    } catch (error) {
      console.error('Error fetching Colorlit modes:', error);
      throw error;
    }
  },

  /**
   * Set mode
   * @param {string} mode - Mode name
   * @returns {Promise<Object>} Response data
   */
  setMode: async (mode) => {
    try {
      const response = await axios.post('/api/colorlit/mode', { mode });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit mode:', error);
      throw error;
    }
  },

  /**
   * Set speed
   * @param {number} speed - Speed value (0-100)
   * @returns {Promise<Object>} Response data
   */
  setSpeed: async (speed) => {
    try {
      const response = await axios.post('/api/colorlit/speed', { speed });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit speed:', error);
      throw error;
    }
  },

  /**
   * Get zones
   * @returns {Promise<Array>} List of zones
   */
  getZones: async () => {
    try {
      const response = await axios.get('/api/colorlit/zones');
      return response.data;
    } catch (error) {
      console.error('Error fetching Colorlit zones:', error);
      throw error;
    }
  },

  /**
   * Select zone
   * @param {string} zoneId - Zone ID
   * @returns {Promise<Object>} Response data
   */
  selectZone: async (zoneId) => {
    try {
      const response = await axios.post('/api/colorlit/zone', { zoneId });
      return response.data;
    } catch (error) {
      console.error('Error selecting Colorlit zone:', error);
      throw error;
    }
  },

  /**
   * Get configuration
   * @returns {Promise<Object>} Configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/colorlit/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching Colorlit configuration:', error);
      throw error;
    }
  },

  /**
   * Save configuration
   * @param {Object} config - Configuration object
   * @param {string} config.host - Host
   * @param {number} config.port - Port
   * @param {string} config.apiKey - API key
   * @param {string} config.username - Username
   * @param {string} config.password - Password
   * @returns {Promise<Object>} Response data
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/colorlit/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Colorlit configuration:', error);
      throw error;
    }
  }
};

export default coloritService;