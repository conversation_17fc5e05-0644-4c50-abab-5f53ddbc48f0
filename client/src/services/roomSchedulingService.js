import axios from 'axios';

/**
 * Room Scheduling Service
 * Provides methods for interacting with the room scheduling API
 */
const roomSchedulingService = {
  /**
   * Get all rooms
   * @param {Object} filters - Optional filters
   * @returns {Promise<Array>} List of rooms
   */
  getAllRooms: async (filters = {}) => {
    try {
      const response = await axios.get('/api/rooms', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error getting rooms:', error);
      throw error;
    }
  },

  /**
   * Get room by ID
   * @param {string} id - Room ID
   * @returns {Promise<Object>} Room details
   */
  getRoomById: async (id) => {
    try {
      const response = await axios.get(`/api/rooms/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting room:', error);
      throw error;
    }
  },

  /**
   * Create a new room (admin only)
   * @param {Object} roomData - Room data
   * @returns {Promise<Object>} Created room
   */
  createRoom: async (roomData) => {
    try {
      const response = await axios.post('/api/rooms', roomData);
      return response.data;
    } catch (error) {
      console.error('Error creating room:', error);
      throw error;
    }
  },

  /**
   * Update a room (admin only)
   * @param {string} id - Room ID
   * @param {Object} roomData - Room data
   * @returns {Promise<Object>} Updated room
   */
  updateRoom: async (id, roomData) => {
    try {
      const response = await axios.put(`/api/rooms/${id}`, roomData);
      return response.data;
    } catch (error) {
      console.error('Error updating room:', error);
      throw error;
    }
  },

  /**
   * Delete a room (admin only)
   * @param {string} id - Room ID
   * @returns {Promise<Object>} Response message
   */
  deleteRoom: async (id) => {
    try {
      const response = await axios.delete(`/api/rooms/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting room:', error);
      throw error;
    }
  },

  /**
   * Find available rooms
   * @param {Object} params - Search parameters
   * @returns {Promise<Array>} List of available rooms
   */
  findAvailableRooms: async (params) => {
    try {
      const response = await axios.get('/api/rooms/available', { params });
      return response.data;
    } catch (error) {
      console.error('Error finding available rooms:', error);
      throw error;
    }
  },

  /**
   * Get all reservations
   * @param {Object} filters - Optional filters
   * @returns {Promise<Array>} List of reservations
   */
  getAllReservations: async (filters = {}) => {
    try {
      const response = await axios.get('/api/reservations', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error getting reservations:', error);
      throw error;
    }
  },

  /**
   * Get reservation by ID
   * @param {string} id - Reservation ID
   * @returns {Promise<Object>} Reservation details
   */
  getReservationById: async (id) => {
    try {
      const response = await axios.get(`/api/reservations/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting reservation:', error);
      throw error;
    }
  },

  /**
   * Create a new reservation
   * @param {Object} reservationData - Reservation data
   * @returns {Promise<Object>} Created reservation
   */
  createReservation: async (reservationData) => {
    try {
      const response = await axios.post('/api/reservations', reservationData);
      return response.data;
    } catch (error) {
      console.error('Error creating reservation:', error);
      throw error;
    }
  },

  /**
   * Update a reservation
   * @param {string} id - Reservation ID
   * @param {Object} reservationData - Reservation data
   * @returns {Promise<Object>} Updated reservation
   */
  updateReservation: async (id, reservationData) => {
    try {
      const response = await axios.put(`/api/reservations/${id}`, reservationData);
      return response.data;
    } catch (error) {
      console.error('Error updating reservation:', error);
      throw error;
    }
  },

  /**
   * Delete a reservation
   * @param {string} id - Reservation ID
   * @returns {Promise<Object>} Response message
   */
  deleteReservation: async (id) => {
    try {
      const response = await axios.delete(`/api/reservations/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting reservation:', error);
      throw error;
    }
  },

  /**
   * Approve a reservation
   * @param {string} id - Reservation ID
   * @returns {Promise<Object>} Updated reservation
   */
  approveReservation: async (id) => {
    try {
      const response = await axios.post(`/api/reservations/${id}/approve`);
      return response.data;
    } catch (error) {
      console.error('Error approving reservation:', error);
      throw error;
    }
  },

  /**
   * Reject a reservation
   * @param {string} id - Reservation ID
   * @param {Object} data - Rejection data (reason)
   * @returns {Promise<Object>} Updated reservation
   */
  rejectReservation: async (id, data) => {
    try {
      const response = await axios.post(`/api/reservations/${id}/reject`, data);
      return response.data;
    } catch (error) {
      console.error('Error rejecting reservation:', error);
      throw error;
    }
  },

  /**
   * Confirm a reservation
   * @param {string} id - Reservation ID
   * @returns {Promise<Object>} Updated reservation
   */
  confirmReservation: async (id) => {
    try {
      const response = await axios.post(`/api/reservations/${id}/confirm`);
      return response.data;
    } catch (error) {
      console.error('Error confirming reservation:', error);
      throw error;
    }
  },

  /**
   * Cancel a reservation
   * @param {string} id - Reservation ID
   * @returns {Promise<Object>} Updated reservation
   */
  cancelReservation: async (id) => {
    try {
      const response = await axios.post(`/api/reservations/${id}/cancel`);
      return response.data;
    } catch (error) {
      console.error('Error cancelling reservation:', error);
      throw error;
    }
  }
};

export default roomSchedulingService;