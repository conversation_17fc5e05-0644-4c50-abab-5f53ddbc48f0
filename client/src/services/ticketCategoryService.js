import axios from 'axios';

/**
 * Ticket Category Service
 * Provides methods for interacting with the ticket category API
 */
const ticketCategoryService = {
  /**
   * Get all ticket categories
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} List of categories
   */
  getAllCategories: async (params = {}) => {
    try {
      const response = await axios.get('/api/ticket-categories', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting categories:', error);
      throw error;
    }
  },

  /**
   * Get category hierarchy
   * @returns {Promise<Array>} Hierarchical category structure
   */
  getCategoryHierarchy: async () => {
    try {
      const response = await axios.get('/api/ticket-categories/hierarchy');
      return response.data;
    } catch (error) {
      console.error('Error getting category hierarchy:', error);
      throw error;
    }
  },

  /**
   * Get category by ID
   * @param {string} id - Category ID
   * @returns {Promise<Object>} Category details
   */
  getCategoryById: async (id) => {
    try {
      const response = await axios.get(`/api/ticket-categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting category:', error);
      throw error;
    }
  },

  /**
   * Create a new category
   * @param {Object} categoryData - Category creation data
   * @returns {Promise<Object>} Created category
   */
  createCategory: async (categoryData) => {
    try {
      const response = await axios.post('/api/ticket-categories', categoryData);
      return response.data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  },

  /**
   * Update a category
   * @param {string} id - Category ID
   * @param {Object} updateData - Category update data
   * @returns {Promise<Object>} Updated category
   */
  updateCategory: async (id, updateData) => {
    try {
      const response = await axios.put(`/api/ticket-categories/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  },

  /**
   * Delete a category
   * @param {string} id - Category ID
   * @returns {Promise<Object>} Deletion confirmation
   */
  deleteCategory: async (id) => {
    try {
      const response = await axios.delete(`/api/ticket-categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  },

  /**
   * Get active categories for dropdown/selection
   * @returns {Promise<Array>} List of active categories
   */
  getActiveCategories: async () => {
    try {
      const response = await axios.get('/api/ticket-categories', { 
        params: { includeInactive: 'false' }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting active categories:', error);
      throw error;
    }
  },

  /**
   * Get categories with subcategories for a specific parent
   * @param {string} parentId - Parent category ID
   * @returns {Promise<Array>} List of subcategories
   */
  getSubcategories: async (parentId) => {
    try {
      const response = await axios.get('/api/ticket-categories', { 
        params: { parent: parentId }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting subcategories:', error);
      throw error;
    }
  },

  /**
   * Search categories by name
   * @param {string} query - Search query
   * @returns {Promise<Array>} Matching categories
   */
  searchCategories: async (query) => {
    try {
      const response = await axios.get('/api/ticket-categories', { 
        params: { search: query }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching categories:', error);
      throw error;
    }
  },

  /**
   * Get categories with ticket counts
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Categories with usage statistics
   */
  getCategoriesWithCounts: async (params = {}) => {
    try {
      const response = await axios.get('/api/ticket-categories', { 
        params: { ...params, includeCounts: 'true' }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting categories with counts:', error);
      throw error;
    }
  },

  /**
   * Reorder categories
   * @param {Array} categoryOrders - Array of {id, sortOrder} objects
   * @returns {Promise<Object>} Update confirmation
   */
  reorderCategories: async (categoryOrders) => {
    try {
      const promises = categoryOrders.map(({ id, sortOrder }) =>
        axios.put(`/api/ticket-categories/${id}`, { sortOrder })
      );
      
      await Promise.all(promises);
      return { message: 'Categories reordered successfully' };
    } catch (error) {
      console.error('Error reordering categories:', error);
      throw error;
    }
  },

  /**
   * Toggle category active status
   * @param {string} id - Category ID
   * @param {boolean} isActive - New active status
   * @returns {Promise<Object>} Updated category
   */
  toggleCategoryStatus: async (id, isActive) => {
    try {
      const response = await axios.put(`/api/ticket-categories/${id}`, { isActive });
      return response.data;
    } catch (error) {
      console.error('Error toggling category status:', error);
      throw error;
    }
  }
};

export default ticketCategoryService;