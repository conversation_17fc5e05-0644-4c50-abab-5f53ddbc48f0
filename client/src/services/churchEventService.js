import axios from 'axios';
import googleCalendarService from './googleCalendarService';

/**
 * Church Event service wrapper for Phase 7 - Church Event Management & Room Usage
 * Provides API wrappers for church event management and Google Calendar integration
 * Leverages existing Google Calendar integration to minimize code duplication
 */
class ChurchEventService {
  constructor() {
    this.baseURL = '/api/church-events';
  }

  // ===== Church Event Management =====

  /**
   * Get all church events with optional filtering
   */
  async getChurchEvents(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching church events:', error);
      throw error;
    }
  }

  /**
   * Get specific church event details
   */
  async getChurchEvent(id) {
    try {
      const response = await axios.get(`${this.baseURL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching church event:', error);
      throw error;
    }
  }

  /**
   * Create new church event
   */
  async createChurchEvent(eventData) {
    try {
      const response = await axios.post(`${this.baseURL}`, eventData);
      return response.data;
    } catch (error) {
      console.error('Error creating church event:', error);
      throw error;
    }
  }

  /**
   * Update church event
   */
  async updateChurchEvent(id, eventData) {
    try {
      const response = await axios.put(`${this.baseURL}/${id}`, eventData);
      return response.data;
    } catch (error) {
      console.error('Error updating church event:', error);
      throw error;
    }
  }

  /**
   * Delete church event
   */
  async deleteChurchEvent(id, force = false) {
    try {
      const response = await axios.delete(`${this.baseURL}/${id}`, { 
        params: { force: force.toString() } 
      });
      return response.data;
    } catch (error) {
      console.error('Error deleting church event:', error);
      throw error;
    }
  }

  // ===== Event Management Operations =====

  /**
   * Add item to event preparation checklist
   */
  async addChecklistItem(eventId, checklistData) {
    try {
      const response = await axios.post(`${this.baseURL}/${eventId}/checklist`, checklistData);
      return response.data;
    } catch (error) {
      console.error('Error adding checklist item:', error);
      throw error;
    }
  }

  /**
   * Mark checklist item as completed
   */
  async completeChecklistItem(eventId, taskId, notes = '') {
    try {
      const response = await axios.put(`${this.baseURL}/${eventId}/checklist/${taskId}/complete`, { notes });
      return response.data;
    } catch (error) {
      console.error('Error completing checklist item:', error);
      throw error;
    }
  }

  /**
   * Update event status
   */
  async updateEventStatus(eventId, status, notes = '') {
    try {
      const response = await axios.put(`${this.baseURL}/${eventId}/status`, { status, notes });
      return response.data;
    } catch (error) {
      console.error('Error updating event status:', error);
      throw error;
    }
  }

  // ===== Location and Calendar Queries =====

  /**
   * Get events for specific building and floor
   */
  async getEventsByLocation(buildingId, floorId, options = {}) {
    try {
      const response = await axios.get(
        `${this.baseURL}/building/${buildingId}/floor/${floorId}`,
        { params: options }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching events by location:', error);
      throw error;
    }
  }

  /**
   * Get upcoming events requiring attention
   */
  async getUpcomingEvents(options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/upcoming`, { params: options });
      return response.data;
    } catch (error) {
      console.error('Error fetching upcoming events:', error);
      throw error;
    }
  }

  /**
   * Get calendar view for building
   */
  async getCalendarEvents(buildingId, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/calendar/${buildingId}`, { params: options });
      return response.data;
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  // ===== Analytics and Reporting =====

  /**
   * Get analytics summary for events
   */
  async getAnalyticsSummary(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/analytics/summary`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching analytics summary:', error);
      throw error;
    }
  }

  // ===== Google Calendar Integration =====

  /**
   * Sync church events with Google Calendar
   * Leverages existing Google Calendar service to avoid duplication
   */
  async syncWithGoogleCalendar(calendarId, options = {}) {
    try {
      // Get church events that need calendar sync
      const eventsToSync = await axios.get(`${this.baseURL}/sync-candidates`, { params: options });
      
      const syncResults = {
        synced: 0,
        errors: [],
        created: [],
        updated: []
      };

      for (const event of eventsToSync.data) {
        try {
          // Convert church event to Google Calendar format
          const calendarEvent = this.convertToGoogleCalendarEvent(event);
          
          if (event.googleCalendarEventId) {
            // Update existing calendar event
            const updatedEvent = await googleCalendarService.updateEvent(
              calendarId, 
              event.googleCalendarEventId, 
              calendarEvent
            );
            syncResults.updated.push({
              churchEventId: event._id,
              googleEventId: updatedEvent.id,
              title: event.title
            });
          } else {
            // Create new calendar event
            const createdEvent = await googleCalendarService.createEvent(calendarId, calendarEvent);
            
            // Update church event with Google Calendar ID
            await this.updateChurchEvent(event._id, {
              googleCalendarEventId: createdEvent.id
            });
            
            syncResults.created.push({
              churchEventId: event._id,
              googleEventId: createdEvent.id,
              title: event.title
            });
          }
          
          syncResults.synced++;
        } catch (syncError) {
          console.error(`Error syncing event ${event._id}:`, syncError);
          syncResults.errors.push({
            churchEventId: event._id,
            error: syncError.message
          });
        }
      }

      return syncResults;
    } catch (error) {
      console.error('Error syncing with Google Calendar:', error);
      throw error;
    }
  }

  /**
   * Import events from Google Calendar
   * Uses existing Google Calendar service integration
   */
  async importFromGoogleCalendar(calendarId, options = {}) {
    try {
      const { startDate, endDate, ...importOptions } = options;
      
      // Fetch events from Google Calendar using existing service
      const calendarEvents = await googleCalendarService.getEvents(calendarId, {
        timeMin: startDate,
        timeMax: endDate,
        ...importOptions
      });

      const importResults = {
        imported: 0,
        skipped: 0,
        errors: [],
        events: []
      };

      for (const googleEvent of calendarEvents) {
        try {
          // Check if event already exists
          const existingEvent = await axios.get(`${this.baseURL}/by-google-id/${googleEvent.id}`);
          
          if (existingEvent.data) {
            importResults.skipped++;
            continue;
          }
        } catch (notFoundError) {
          // Event doesn't exist, proceed with import
        }

        try {
          // Convert Google Calendar event to church event format
          const churchEvent = this.convertFromGoogleCalendarEvent(googleEvent);
          
          // Create church event
          const createdEvent = await this.createChurchEvent(churchEvent);
          
          importResults.imported++;
          importResults.events.push({
            churchEventId: createdEvent._id,
            googleEventId: googleEvent.id,
            title: createdEvent.title
          });
        } catch (createError) {
          console.error(`Error importing Google Calendar event ${googleEvent.id}:`, createError);
          importResults.errors.push({
            googleEventId: googleEvent.id,
            error: createError.message
          });
        }
      }

      return importResults;
    } catch (error) {
      console.error('Error importing from Google Calendar:', error);
      throw error;
    }
  }

  // ===== Helper Methods for Calendar Integration =====

  /**
   * Convert church event to Google Calendar event format
   */
  convertToGoogleCalendarEvent(churchEvent) {
    return {
      summary: churchEvent.title,
      description: `${churchEvent.description || ''}\n\nEvent Type: ${churchEvent.eventType}\nAttendance: ${churchEvent.expectedAttendance || 'TBD'}\n\nManaged by CSF Portal - Church Event Management`,
      start: {
        dateTime: churchEvent.startDateTime,
        timeZone: churchEvent.timeZone || 'America/New_York'
      },
      end: {
        dateTime: churchEvent.endDateTime,
        timeZone: churchEvent.timeZone || 'America/New_York'
      },
      location: churchEvent.location || churchEvent.rooms?.map(room => room.name).join(', '),
      attendees: churchEvent.organizers?.map(organizer => ({
        email: organizer.email,
        displayName: organizer.name
      })) || [],
      extendedProperties: {
        private: {
          csfPortalEventId: churchEvent._id,
          eventType: churchEvent.eventType,
          buildingId: churchEvent.buildingId,
          floorId: churchEvent.floorId
        }
      }
    };
  }

  /**
   * Convert Google Calendar event to church event format
   */
  convertFromGoogleCalendarEvent(googleEvent) {
    const extendedProps = googleEvent.extendedProperties?.private || {};
    
    return {
      title: googleEvent.summary,
      description: googleEvent.description,
      eventType: extendedProps.eventType || 'meeting',
      startDateTime: googleEvent.start.dateTime || googleEvent.start.date,
      endDateTime: googleEvent.end.dateTime || googleEvent.end.date,
      timeZone: googleEvent.start.timeZone,
      location: googleEvent.location,
      buildingId: extendedProps.buildingId,
      floorId: extendedProps.floorId,
      organizers: googleEvent.attendees?.map(attendee => ({
        name: attendee.displayName,
        email: attendee.email
      })) || [],
      googleCalendarEventId: googleEvent.id,
      status: 'scheduled',
      importedFromGoogleCalendar: true
    };
  }

  /**
   * Seed demo church event data
   */
  async seedDemoData(buildingId, floorId, clearExisting = false) {
    try {
      const response = await axios.post(`${this.baseURL}/seed-demo-data`, {
        buildingId,
        floorId,
        clearExisting
      });
      return response.data;
    } catch (error) {
      console.error('Error seeding demo data:', error);
      throw error;
    }
  }

  // ===== Static Helper Methods =====

  /**
   * Get event type options for UI
   */
  getEventTypes() {
    return [
      { value: 'worship_service', label: 'Worship Service', icon: '⛪', color: '#2196F3' },
      { value: 'bible_study', label: 'Bible Study', icon: '📖', color: '#4CAF50' },
      { value: 'prayer_meeting', label: 'Prayer Meeting', icon: '🙏', color: '#9C27B0' },
      { value: 'youth_group', label: 'Youth Group', icon: '🎉', color: '#FF9800' },
      { value: 'children_ministry', label: 'Children Ministry', icon: '👶', color: '#FFC107' },
      { value: 'music_practice', label: 'Music Practice', icon: '🎵', color: '#795548' },
      { value: 'committee_meeting', label: 'Committee Meeting', icon: '👥', color: '#607D8B' },
      { value: 'wedding', label: 'Wedding', icon: '💒', color: '#E91E63' },
      { value: 'funeral', label: 'Funeral', icon: '🕊️', color: '#424242' },
      { value: 'baptism', label: 'Baptism', icon: '💧', color: '#00BCD4' },
      { value: 'community_event', label: 'Community Event', icon: '🏘️', color: '#8BC34A' },
      { value: 'fellowship', label: 'Fellowship', icon: '🤝', color: '#FFEB3B' },
      { value: 'outreach', label: 'Outreach', icon: '🌍', color: '#009688' },
      { value: 'conference', label: 'Conference', icon: '🎤', color: '#3F51B5' },
      { value: 'training', label: 'Training', icon: '📚', color: '#673AB7' },
      { value: 'maintenance', label: 'Maintenance', icon: '🔧', color: '#9E9E9E' },
      { value: 'setup', label: 'Setup', icon: '⚙️', color: '#FF5722' },
      { value: 'cleanup', label: 'Cleanup', icon: '🧹', color: '#8BC34A' },
      { value: 'other', label: 'Other', icon: '📋', color: '#757575' }
    ];
  }

  /**
   * Get ministry options
   */
  getMinistries() {
    return [
      { value: 'worship', label: 'Worship', color: '#2196F3' },
      { value: 'youth', label: 'Youth', color: '#FF9800' },
      { value: 'children', label: 'Children', color: '#FFC107' },
      { value: 'music', label: 'Music', color: '#795548' },
      { value: 'outreach', label: 'Outreach', color: '#009688' },
      { value: 'fellowship', label: 'Fellowship', color: '#FFEB3B' },
      { value: 'administration', label: 'Administration', color: '#607D8B' },
      { value: 'facilities', label: 'Facilities', color: '#9E9E9E' },
      { value: 'education', label: 'Education', color: '#4CAF50' },
      { value: 'care', label: 'Care & Pastoral', color: '#9C27B0' },
      { value: 'missions', label: 'Missions', color: '#00BCD4' },
      { value: 'other', label: 'Other', color: '#757575' }
    ];
  }

  /**
   * Get event status options
   */
  getEventStatuses() {
    return [
      { value: 'draft', label: 'Draft', color: '#9E9E9E', icon: '📝' },
      { value: 'planning', label: 'Planning', color: '#FFC107', icon: '📋' },
      { value: 'approved', label: 'Approved', color: '#4CAF50', icon: '✅' },
      { value: 'preparing', label: 'Preparing', color: '#FF9800', icon: '⚙️' },
      { value: 'setup', label: 'Setup', color: '#2196F3', icon: '🔧' },
      { value: 'in_progress', label: 'In Progress', color: '#00BCD4', icon: '▶️' },
      { value: 'concluded', label: 'Concluded', color: '#795548', icon: '🏁' },
      { value: 'cleanup', label: 'Cleanup', color: '#8BC34A', icon: '🧹' },
      { value: 'completed', label: 'Completed', color: '#4CAF50', icon: '✓' },
      { value: 'cancelled', label: 'Cancelled', color: '#F44336', icon: '❌' }
    ];
  }

  /**
   * Get priority levels
   */
  getPriorityLevels() {
    return [
      { value: 'low', label: 'Low', color: '#4CAF50', weight: 1 },
      { value: 'medium', label: 'Medium', color: '#FFC107', weight: 2 },
      { value: 'high', label: 'High', color: '#FF9800', weight: 3 },
      { value: 'critical', label: 'Critical', color: '#F44336', weight: 4 }
    ];
  }

  /**
   * Get color for event type
   */
  getEventTypeColor(eventType) {
    const type = this.getEventTypes().find(t => t.value === eventType);
    return type?.color || '#9E9E9E';
  }

  /**
   * Get icon for event type
   */
  getEventTypeIcon(eventType) {
    const type = this.getEventTypes().find(t => t.value === eventType);
    return type?.icon || '📋';
  }

  /**
   * Get color for event status
   */
  getStatusColor(status) {
    const statusInfo = this.getEventStatuses().find(s => s.value === status);
    return statusInfo?.color || '#9E9E9E';
  }

  /**
   * Get color for priority level
   */
  getPriorityColor(priority) {
    const priorityInfo = this.getPriorityLevels().find(p => p.value === priority);
    return priorityInfo?.color || '#9E9E9E';
  }

  /**
   * Format event for display
   */
  formatEventDisplay(event) {
    const typeInfo = this.getEventTypes().find(t => t.value === event.eventType);
    const statusInfo = this.getEventStatuses().find(s => s.value === event.status);
    const ministryInfo = this.getMinistries().find(m => m.value === event.ministry);
    
    return {
      ...event,
      displayTitle: event.title || 'Untitled Event',
      typeIcon: typeInfo?.icon || '📋',
      typeLabel: typeInfo?.label || event.eventType,
      typeColor: typeInfo?.color || '#9E9E9E',
      statusIcon: statusInfo?.icon || '📝',
      statusLabel: statusInfo?.label || event.status,
      statusColor: statusInfo?.color || '#9E9E9E',
      ministryLabel: ministryInfo?.label || event.ministry,
      ministryColor: ministryInfo?.color || '#9E9E9E',
      priorityColor: this.getPriorityColor(event.priority),
      isUpcoming: new Date(event.startTime) > new Date(),
      isInProgress: new Date() >= new Date(event.startTime) && new Date() <= new Date(event.endTime),
      isToday: new Date(event.startTime).toDateString() === new Date().toDateString()
    };
  }

  /**
   * Calculate event preparation progress
   */
  calculatePreparationProgress(event) {
    if (!event.preparation?.checklist || event.preparation.checklist.length === 0) {
      return { total: 0, completed: 0, percentage: 0, status: 'no_checklist' };
    }
    
    const total = event.preparation.checklist.length;
    const completed = event.preparation.checklist.filter(item => item.completed).length;
    const percentage = Math.round((completed / total) * 100);
    
    let status = 'in_progress';
    if (percentage === 100) status = 'complete';
    else if (percentage === 0) status = 'not_started';
    else if (percentage >= 75) status = 'almost_complete';
    
    return { total, completed, percentage, status };
  }

  /**
   * Check if event needs attention
   */
  needsAttention(event) {
    const now = new Date();
    const eventStart = new Date(event.startTime);
    const hoursUntilEvent = (eventStart - now) / (1000 * 60 * 60);
    
    const reasons = [];
    
    // Event starting soon but not prepared
    if (hoursUntilEvent <= 2 && hoursUntilEvent > 0) {
      const prep = this.calculatePreparationProgress(event);
      if (prep.percentage < 100) {
        reasons.push('Event starts soon but preparation incomplete');
      }
    }
    
    // Event status issues
    if (event.status === 'draft' && hoursUntilEvent <= 24) {
      reasons.push('Event not approved but starts within 24 hours');
    }
    
    // Resource conflicts
    if (event.resources?.audioVisual?.required && !event.resources.audioVisual.technicianRequired) {
      reasons.push('AV equipment required but no technician assigned');
    }
    
    // Overdue checklist items
    if (event.preparation?.checklist) {
      const overdueItems = event.preparation.checklist.filter(item => 
        !item.completed && item.dueTime && new Date(item.dueTime) < now
      );
      if (overdueItems.length > 0) {
        reasons.push(`${overdueItems.length} overdue checklist items`);
      }
    }
    
    return {
      needsAttention: reasons.length > 0,
      reasons,
      severity: reasons.length >= 3 ? 'critical' : reasons.length >= 2 ? 'warning' : 'info'
    };
  }

  /**
   * Generate event preparation checklist suggestions
   */
  generateChecklistSuggestions(event) {
    const suggestions = [];
    const eventStart = new Date(event.startTime);
    
    // AV setup suggestions
    if (event.resources?.audioVisual?.required) {
      const setupTime = event.resources.audioVisual.setupTime || 60;
      suggestions.push({
        task: 'Test and set up audio/visual equipment',
        responsible: 'Tech Team',
        dueTime: new Date(eventStart.getTime() - setupTime * 60 * 1000),
        systems: ['audio', 'cameras'],
        priority: 'high'
      });
    }
    
    // Facilities preparation
    if (event.resources?.facilities?.heating || event.resources?.facilities?.cooling) {
      suggestions.push({
        task: 'Pre-condition room temperature',
        responsible: 'Facilities',
        dueTime: new Date(eventStart.getTime() - 60 * 60 * 1000), // 1 hour before
        systems: ['hvac'],
        priority: 'medium'
      });
    }
    
    // Security setup
    if (event.resources?.facilities?.security?.required) {
      suggestions.push({
        task: 'Set up security and unlock necessary doors',
        responsible: 'Security Team',
        dueTime: new Date(eventStart.getTime() - 30 * 60 * 1000), // 30 minutes before
        systems: ['doors', 'cameras'],
        priority: 'high'
      });
    }
    
    // Catering preparation
    if (event.resources?.catering?.required) {
      suggestions.push({
        task: 'Set up catering and serving area',
        responsible: 'Hospitality Team',
        dueTime: new Date(eventStart.getTime() - 45 * 60 * 1000), // 45 minutes before
        systems: [],
        priority: 'medium'
      });
    }
    
    // General setup
    suggestions.push({
      task: 'Final room setup and arrangement',
      responsible: 'Setup Team',
      dueTime: new Date(eventStart.getTime() - 15 * 60 * 1000), // 15 minutes before
      systems: [],
      priority: 'medium'
    });
    
    return suggestions;
  }

  /**
   * Validate event data for creation/update
   */
  validateEventData(data) {
    const errors = [];

    // Required fields
    if (!data.eventId) errors.push('Event ID is required');
    if (!data.title) errors.push('Title is required');
    if (!data.startTime) errors.push('Start time is required');
    if (!data.endTime) errors.push('End time is required');
    if (!data.eventType) errors.push('Event type is required');

    // Date validation
    if (data.startTime && data.endTime) {
      if (new Date(data.startTime) >= new Date(data.endTime)) {
        errors.push('End time must be after start time');
      }
    }

    // Event ID format validation
    if (data.eventId && !/^[A-Z0-9\-_]+$/i.test(data.eventId)) {
      errors.push('Event ID can only contain letters, numbers, hyphens, and underscores');
    }

    // Capacity validation
    if (data.location?.expectedAttendance && data.location?.capacity) {
      if (data.location.expectedAttendance > data.location.capacity) {
        errors.push('Expected attendance cannot exceed room capacity');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

const churchEventService = new ChurchEventService();
export default churchEventService;