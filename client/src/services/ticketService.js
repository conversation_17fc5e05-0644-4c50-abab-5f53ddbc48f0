import axios from 'axios';

/**
 * Ticket Service
 * Provides methods for interacting with the ticket management API
 */
const ticketService = {
  /**
   * Get all tickets with filtering and pagination
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Paginated tickets response
   */
  getAllTickets: async (params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting tickets:', error);
      throw error;
    }
  },

  /**
   * Get ticket by ID
   * @param {string} id - Ticket ID
   * @returns {Promise<Object>} Ticket details with comments
   */
  getTicketById: async (id) => {
    try {
      const response = await axios.get(`/api/tickets/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting ticket:', error);
      throw error;
    }
  },

  /**
   * Create a new ticket
   * @param {Object} ticketData - Ticket creation data
   * @returns {Promise<Object>} Created ticket
   */
  createTicket: async (ticketData) => {
    try {
      const response = await axios.post('/api/tickets', ticketData);
      return response.data;
    } catch (error) {
      console.error('Error creating ticket:', error);
      throw error;
    }
  },

  /**
   * Update a ticket
   * @param {string} id - Ticket ID
   * @param {Object} updateData - Ticket update data
   * @returns {Promise<Object>} Updated ticket
   */
  updateTicket: async (id, updateData) => {
    try {
      const response = await axios.put(`/api/tickets/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating ticket:', error);
      throw error;
    }
  },

  /**
   * Delete a ticket
   * @param {string} id - Ticket ID
   * @returns {Promise<Object>} Deletion confirmation
   */
  deleteTicket: async (id) => {
    try {
      const response = await axios.delete(`/api/tickets/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting ticket:', error);
      throw error;
    }
  },

  /**
   * Add a comment to a ticket
   * @param {string} id - Ticket ID
   * @param {Object} commentData - Comment data
   * @returns {Promise<Object>} Created comment
   */
  addComment: async (id, commentData) => {
    try {
      const response = await axios.post(`/api/tickets/${id}/comments`, commentData);
      return response.data;
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  },

  /**
   * Get ticket statistics
   * @param {Object} params - Query parameters (timeframe, etc.)
   * @returns {Promise<Object>} Ticket statistics
   */
  getTicketStats: async (params = {}) => {
    try {
      const response = await axios.get('/api/tickets/stats', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting ticket stats:', error);
      throw error;
    }
  },

  /**
   * Search tickets
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Promise<Object>} Search results
   */
  searchTickets: async (query, filters = {}) => {
    try {
      const params = { search: query, ...filters };
      const response = await axios.get('/api/tickets', { params });
      return response.data;
    } catch (error) {
      console.error('Error searching tickets:', error);
      throw error;
    }
  },

  /**
   * Get tickets assigned to current user
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} User's assigned tickets
   */
  getMyAssignedTickets: async (params = {}) => {
    try {
      // The backend will filter based on the authenticated user
      const response = await axios.get('/api/tickets', { 
        params: { ...params, assignedToMe: true }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting assigned tickets:', error);
      throw error;
    }
  },

  /**
   * Get tickets created by current user
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} User's created tickets
   */
  getMyCreatedTickets: async (params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { 
        params: { ...params, createdByMe: true }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting created tickets:', error);
      throw error;
    }
  },

  /**
   * Get tickets by status
   * @param {string|Array} status - Status or array of statuses
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} Tickets with specified status
   */
  getTicketsByStatus: async (status, params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { 
        params: { ...params, status }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting tickets by status:', error);
      throw error;
    }
  },

  /**
   * Get tickets by priority
   * @param {string|Array} priority - Priority or array of priorities
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} Tickets with specified priority
   */
  getTicketsByPriority: async (priority, params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { 
        params: { ...params, priority }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting tickets by priority:', error);
      throw error;
    }
  },

  /**
   * Get tickets by category
   * @param {string} category - Category name
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} Tickets in specified category
   */
  getTicketsByCategory: async (category, params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { 
        params: { ...params, category }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting tickets by category:', error);
      throw error;
    }
  },

  /**
   * Get tickets by tags
   * @param {string|Array} tags - Tag or array of tags
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} Tickets with specified tags
   */
  getTicketsByTags: async (tags, params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { 
        params: { ...params, tags }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting tickets by tags:', error);
      throw error;
    }
  },

  /**
   * Get overdue tickets
   * @param {Object} params - Additional query parameters
   * @returns {Promise<Object>} Overdue tickets
   */
  getOverdueTickets: async (params = {}) => {
    try {
      const response = await axios.get('/api/tickets', { 
        params: { 
          ...params, 
          status: ['open', 'pending', 'on_hold'],
          overdue: true
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting overdue tickets:', error);
      throw error;
    }
  },

  /**
   * Assign ticket to user
   * @param {string} id - Ticket ID
   * @param {string} userId - User ID to assign to
   * @returns {Promise<Object>} Updated ticket
   */
  assignTicket: async (id, userId) => {
    try {
      const response = await axios.put(`/api/tickets/${id}`, { 
        assignedTo: userId 
      });
      return response.data;
    } catch (error) {
      console.error('Error assigning ticket:', error);
      throw error;
    }
  },

  /**
   * Update ticket status
   * @param {string} id - Ticket ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated ticket
   */
  updateTicketStatus: async (id, status) => {
    try {
      const response = await axios.put(`/api/tickets/${id}`, { status });
      return response.data;
    } catch (error) {
      console.error('Error updating ticket status:', error);
      throw error;
    }
  },

  /**
   * Update ticket priority
   * @param {string} id - Ticket ID
   * @param {string} priority - New priority
   * @returns {Promise<Object>} Updated ticket
   */
  updateTicketPriority: async (id, priority) => {
    try {
      const response = await axios.put(`/api/tickets/${id}`, { priority });
      return response.data;
    } catch (error) {
      console.error('Error updating ticket priority:', error);
      throw error;
    }
  },

  /**
   * Add follower to ticket
   * @param {string} id - Ticket ID
   * @param {string} userId - User ID to add as follower
   * @returns {Promise<Object>} Updated ticket
   */
  addFollower: async (id, userId) => {
    try {
      // Get current ticket to preserve existing followers
      const ticket = await ticketService.getTicketById(id);
      const currentFollowers = ticket.ticket.followers.map(f => f._id);
      
      if (!currentFollowers.includes(userId)) {
        const updatedFollowers = [...currentFollowers, userId];
        const response = await axios.put(`/api/tickets/${id}`, { 
          followers: updatedFollowers 
        });
        return response.data;
      }
      
      return ticket.ticket;
    } catch (error) {
      console.error('Error adding follower:', error);
      throw error;
    }
  },

  /**
   * Remove follower from ticket
   * @param {string} id - Ticket ID
   * @param {string} userId - User ID to remove as follower
   * @returns {Promise<Object>} Updated ticket
   */
  removeFollower: async (id, userId) => {
    try {
      // Get current ticket to get existing followers
      const ticket = await ticketService.getTicketById(id);
      const currentFollowers = ticket.ticket.followers.map(f => f._id);
      
      const updatedFollowers = currentFollowers.filter(f => f !== userId);
      const response = await axios.put(`/api/tickets/${id}`, { 
        followers: updatedFollowers 
      });
      return response.data;
    } catch (error) {
      console.error('Error removing follower:', error);
      throw error;
    }
  }
};

export default ticketService;