import axios from 'axios';

/**
 * Apple Business Manager API Service
 * Handles client-side API calls to the Apple Business Manager endpoints
 */
const appleBusinessManagerService = {
  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async (params = {}) => {
    try {
      const response = await axios.get('/api/apple-business-manager/devices', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Apple Business Manager devices:', error);
      throw error;
    }
  },

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Device details
   */
  getDevice: async (deviceId, params = {}) => {
    try {
      const response = await axios.get(`/api/apple-business-manager/devices/${deviceId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Apple Business Manager device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get device MDM assignment information
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} MDM assignment information
   */
  getDeviceMdmAssignment: async (deviceId) => {
    try {
      const response = await axios.get(`/api/apple-business-manager/devices/${deviceId}/mdm`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching MDM assignment for device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Assign device to MDM service
   * @param {string} deviceId Device ID
   * @param {string} mdmServerId MDM service ID
   * @returns {Promise<Object>} Assignment result
   */
  assignDeviceToMdm: async (deviceId, mdmServerId) => {
    try {
      const response = await axios.post(`/api/apple-business-manager/devices/${deviceId}/mdm`, { mdmServerId });
      return response.data;
    } catch (error) {
      console.error(`Error assigning device ${deviceId} to MDM service ${mdmServerId}:`, error);
      throw error;
    }
  },

  /**
   * Reassign device to a different MDM service
   * @param {string} deviceId Device ID
   * @param {string} mdmServerId New MDM service ID
   * @returns {Promise<Object>} Reassignment result
   */
  reassignDeviceToMdm: async (deviceId, mdmServerId) => {
    try {
      const response = await axios.put(`/api/apple-business-manager/devices/${deviceId}/mdm`, { mdmServerId });
      return response.data;
    } catch (error) {
      console.error(`Error reassigning device ${deviceId} to MDM service ${mdmServerId}:`, error);
      throw error;
    }
  },

  /**
   * Unassign device from MDM service
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Unassignment result
   */
  unassignDeviceFromMdm: async (deviceId) => {
    try {
      const response = await axios.delete(`/api/apple-business-manager/devices/${deviceId}/mdm`);
      return response.data;
    } catch (error) {
      console.error(`Error unassigning device ${deviceId} from MDM service:`, error);
      throw error;
    }
  },

  // Configuration methods have been removed as configuration is now managed through environment variables

  /**
   * Get Apple Business Manager configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/apple-business-manager/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Apple Business Manager configuration:', error);
      throw error;
    }
  },

  /**
   * Get all MDM servers
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of MDM servers
   */
  getMdmServers: async (params = {}) => {
    try {
      const response = await axios.get('/api/apple-business-manager/mdm-servers', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Apple Business Manager MDM servers:', error);
      throw error;
    }
  }
};

export default appleBusinessManagerService;