import axios from 'axios';

/**
 * Get all people
 * @returns {Promise<Array>} Array of people
 */
export const getAllPeople = async () => {
  try {
    const response = await axios.get('/api/people');
    return response.data;
  } catch (error) {
    console.error('Error fetching people:', error);
    throw error;
  }
};

/**
 * Get a person by ID
 * @param {string} id Person ID
 * @returns {Promise<Object>} Person object
 */
export const getPersonById = async (id) => {
  try {
    const response = await axios.get(`/api/people/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching person with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Search people by query and/or category
 * @param {string} query Search query
 * @param {string} category Category to filter by
 * @returns {Promise<Array>} Array of people matching the search criteria
 */
export const searchPeople = async (query, category) => {
  try {
    const params = {};
    if (query) params.query = query;
    if (category) params.category = category;

    const response = await axios.get('/api/people/search', { params });
    return response.data;
  } catch (error) {
    console.error('Error searching people:', error);
    throw error;
  }
};

/**
 * Create a new person
 * @param {Object} personData Person data
 * @returns {Promise<Object>} Created person object
 */
export const createPerson = async (personData) => {
  try {
    const response = await axios.post('/api/people', personData);
    return response.data;
  } catch (error) {
    console.error('Error creating person:', error);
    throw error;
  }
};

/**
 * Update a person
 * @param {string} id Person ID
 * @param {Object} personData Updated person data
 * @returns {Promise<Object>} Updated person object
 */
export const updatePerson = async (id, personData) => {
  try {
    const response = await axios.put(`/api/people/${id}`, personData);
    return response.data;
  } catch (error) {
    console.error(`Error updating person with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a person
 * @param {string} id Person ID
 * @returns {Promise<Object>} Response message
 */
export const deletePerson = async (id) => {
  try {
    const response = await axios.delete(`/api/people/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting person with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Get all unique categories
 * @returns {Promise<Array>} Array of category strings
 */
export const getAllCategories = async () => {
  try {
    const response = await axios.get('/api/people/categories/all');
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

/**
 * Get people from Planning Center
 * @param {Object} params Query parameters including pagination (page, per_page) and search
 * @returns {Promise<Object>} Object containing people array and pagination metadata
 */
export const getPlanningCenterPeople = async (params = {}) => {
  try {
    const response = await axios.get('/api/planning-center/people-directory', { params });
    
    // The response now includes people array and pagination metadata
    // Format: { people: [...], pagination: {...}, meta: {...}, links: {...} }
    return response.data;
  } catch (error) {
    console.error('Error fetching Planning Center people:', error);
    throw error;
  }
};

/**
 * Get a single person from Planning Center by ID
 * @param {string} id Planning Center person ID
 * @param {Object} params Query parameters (e.g., include)
 * @returns {Promise<Object>} Person object with detailed information
 */
export const getPlanningCenterPersonById = async (id, params = {}) => {
  try {
    if (!id) {
      throw new Error('Person ID is required');
    }
    
    // Remove the 'pc_' prefix if it exists (used in the directory view)
    const personId = id.startsWith('pc_') ? id.substring(3) : id;
    
    const response = await axios.get(`/api/planning-center/people/${personId}`, { params });
    return response.data.person;
  } catch (error) {
    console.error(`Error fetching Planning Center person with ID ${id}:`, error);
    throw error;
  }
};
