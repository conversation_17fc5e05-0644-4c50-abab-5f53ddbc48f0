import axios from 'axios';

/**
 * Planning Center API Service
 * Handles client-side API calls to the Planning Center endpoints
 */
const planningCenterService = {
  /**
   * Set up Planning Center with one click
   * @returns {Promise<Object>} Response message
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/planning-center/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up Planning Center with one click:', error);
      throw error;
    }
  },
  /**
   * Get all Planning Center applications
   * @returns {Promise<Array>} List of applications
   */
  getApplications: async () => {
    try {
      const response = await axios.get('/api/planning-center/applications');
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center applications:', error);
      throw error;
    }
  },

  /**
   * Get Planning Center events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of events
   */
  getEvents: async (params = {}) => {
    try {
      const response = await axios.get('/api/planning-center/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center events:', error);
      throw error;
    }
  },

  /**
   * Get Planning Center people
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of people
   */
  getPeople: async (params = {}) => {
    try {
      const response = await axios.get('/api/planning-center/people', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center people:', error);
      throw error;
    }
  },

  /**
   * Get Planning Center people formatted for directory with pagination and search
   * @param {Object} params Query parameters including pagination (page, per_page) and search
   * @returns {Promise<Object>} Object containing people array and pagination metadata
   */
  getPeopleDirectory: async (params = {}) => {
    try {
      const response = await axios.get('/api/planning-center/people-directory', { params });
    
      // The response includes people array and pagination metadata
      // Format: { people: [...], pagination: {...}, meta: {...}, links: {...} }
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center people directory:', error);
      throw error;
    }
  },

  /**
   * Get Planning Center resources
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of resources
   */
  getResources: async (params = {}) => {
    try {
      const response = await axios.get('/api/planning-center/resources', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center resources:', error);
      throw error;
    }
  },

  /**
   * Save Planning Center configuration
   * @param {Object} config Configuration object with personalAccessToken
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/planning-center/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Planning Center configuration:', error);
      throw error;
    }
  },

  /**
   * Get Planning Center configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/planning-center/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Planning Center configuration:', error);
      throw error;
    }
  },

  /**
   * Verify Personal Access Token with Planning Center
   * @returns {Promise<Object>} Response with success status
   */
  verifyToken: async () => {
    try {
      const response = await axios.get('/api/planning-center/verify-token');
      return response.data;
    } catch (error) {
      console.error('Error verifying Planning Center token:', error);
      throw error;
    }
  }
};

export default planningCenterService;
