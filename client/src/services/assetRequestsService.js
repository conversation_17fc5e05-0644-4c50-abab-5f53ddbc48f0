import axios from 'axios';

/**
 * Asset Requests Service
 * Provides methods for interacting with the asset requests API
 */
const assetRequestsService = {
  /**
   * Get all asset requests
   * @returns {Promise<Array>} List of asset requests
   */
  getAssetRequests: async () => {
    try {
      const response = await axios.get('/api/asset-requests');
      return response.data;
    } catch (error) {
      console.error('Error getting asset requests:', error);
      throw error;
    }
  },

  /**
   * Get asset request by ID
   * @param {string} id - Asset request ID
   * @returns {Promise<Object>} Asset request details
   */
  getAssetRequestById: async (id) => {
    try {
      const response = await axios.get(`/api/asset-requests/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting asset request:', error);
      throw error;
    }
  },

  /**
   * Create a new asset request
   * @param {Object} requestData - Asset request data
   * @returns {Promise<Object>} Created asset request
   */
  createAssetRequest: async (requestData) => {
    try {
      const response = await axios.post('/api/asset-requests', requestData);
      return response.data;
    } catch (error) {
      console.error('Error creating asset request:', error);
      throw error;
    }
  },

  /**
   * Update an asset request
   * @param {string} id - Asset request ID
   * @param {Object} requestData - Asset request data
   * @returns {Promise<Object>} Updated asset request
   */
  updateAssetRequest: async (id, requestData) => {
    try {
      const response = await axios.put(`/api/asset-requests/${id}`, requestData);
      return response.data;
    } catch (error) {
      console.error('Error updating asset request:', error);
      throw error;
    }
  },

  /**
   * Delete an asset request
   * @param {string} id - Asset request ID
   * @returns {Promise<Object>} Response message
   */
  deleteAssetRequest: async (id) => {
    try {
      const response = await axios.delete(`/api/asset-requests/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting asset request:', error);
      throw error;
    }
  },

  /**
   * Approve an asset request
   * @param {string} id - Asset request ID
   * @param {Object} approvalData - Approval data
   * @returns {Promise<Object>} Updated asset request
   */
  approveAssetRequest: async (id, approvalData) => {
    try {
      const response = await axios.put(`/api/asset-requests/${id}/approve`, approvalData);
      return response.data;
    } catch (error) {
      console.error('Error approving asset request:', error);
      throw error;
    }
  },

  /**
   * Deny an asset request
   * @param {string} id - Asset request ID
   * @param {Object} denialData - Denial data
   * @returns {Promise<Object>} Updated asset request
   */
  denyAssetRequest: async (id, denialData) => {
    try {
      const response = await axios.put(`/api/asset-requests/${id}/deny`, denialData);
      return response.data;
    } catch (error) {
      console.error('Error denying asset request:', error);
      throw error;
    }
  },

  /**
   * Mark an asset request as completed
   * @param {string} id - Asset request ID
   * @param {Object} completionData - Completion data
   * @returns {Promise<Object>} Updated asset request
   */
  completeAssetRequest: async (id, completionData) => {
    try {
      const response = await axios.put(`/api/asset-requests/${id}/complete`, completionData);
      return response.data;
    } catch (error) {
      console.error('Error completing asset request:', error);
      throw error;
    }
  },

  /**
   * Cancel an asset request
   * @param {string} id - Asset request ID
   * @returns {Promise<Object>} Updated asset request
   */
  cancelAssetRequest: async (id) => {
    try {
      const response = await axios.put(`/api/asset-requests/${id}/cancel`);
      return response.data;
    } catch (error) {
      console.error('Error canceling asset request:', error);
      throw error;
    }
  }
};

export default assetRequestsService;