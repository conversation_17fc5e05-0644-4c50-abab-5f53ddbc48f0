import axios from 'axios';

/**
 * Service for handling global search functionality across the application
 */
const searchService = {
  /**
   * Search across all available content types
   * @param {string} query - The search query
   * @returns {Promise<Array>} - Array of search results
   */
  searchAll: async (query) => {
    try {
      const response = await axios.get(`/api/search?q=${encodeURIComponent(query)}`);
      return response.data;
    } catch (error) {
      console.error('Error searching:', error);
      throw error;
    }
  },

  /**
   * Get search suggestions based on partial query
   * @param {string} query - The partial search query
   * @returns {Promise<Array>} - Array of search suggestions
   */
  getSuggestions: async (query) => {
    try {
      const response = await axios.get(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
      return response.data;
    } catch (error) {
      console.error('Error getting search suggestions:', error);
      throw error;
    }
  },

  /**
   * Format search results for display
   * @param {Array} results - The search results
   * @returns {Array} - Formatted search results
   */
  formatResults: (results) => {
    return results.map(result => ({
      id: result.id,
      title: result.title,
      description: result.description,
      type: result.type,
      url: result.url,
      icon: result.icon
    }));
  }
};

export default searchService;