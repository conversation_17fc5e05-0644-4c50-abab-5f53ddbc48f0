import axios from 'axios';

/**
 * Service for interacting with the Building Tracking API
 * Manages contracts, services, inspections, and other items with expiration/renewal dates
 */
class BuildingTrackingService {
  /**
   * Get all tracking items with optional filters
   * @param {Object} filters - Optional filters
   * @param {string} filters.buildingId - Filter by building ID
   * @param {string} filters.type - Filter by item type (contract, service, inspection, etc.)
   * @param {string} filters.status - Filter by status (active, expired, pending_renewal, etc.)
   * @param {string} filters.expiringBefore - Filter by expiration date before (ISO date string)
   * @param {string} filters.expiringAfter - Filter by expiration date after (ISO date string)
   * @param {string} filters.renewalBefore - Filter by renewal date before (ISO date string)
   * @param {string} filters.renewalAfter - Filter by renewal date after (ISO date string)
   * @returns {Promise<Array>} List of tracking items
   */
  async getTrackingItems(filters = {}) {
    try {
      // Remove empty string, null, and undefined values from filters to avoid sending blank params
      const cleaned = Object.entries(filters || {}).reduce((acc, [key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {});
      const response = await axios.get('/api/building-tracking', { params: cleaned });
      return response.data;
    } catch (error) {
      console.error('Error fetching tracking items:', error);
      throw error;
    }
  }

  /**
   * Get tracking items expiring within a specified number of days
   * @param {number} days - Number of days to look ahead (default: 30)
   * @returns {Promise<Array>} List of expiring tracking items
   */
  async getExpiringItems(days = 30) {
    try {
      const response = await axios.get('/api/building-tracking/expiring', { params: { days } });
      return response.data;
    } catch (error) {
      console.error('Error fetching expiring items:', error);
      throw error;
    }
  }

  /**
   * Get all tracking items for a specific building
   * @param {string} buildingId - Building ID
   * @returns {Promise<Array>} List of tracking items for the building
   */
  async getBuildingTrackingItems(buildingId) {
    try {
      const response = await axios.get(`/api/building-tracking/building/${buildingId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tracking items for building ${buildingId}:`, error);
      throw error;
    }
  }

  /**
   * Get a tracking item by ID
   * @param {string} id - Tracking item ID
   * @returns {Promise<Object>} Tracking item details
   */
  async getTrackingItem(id) {
    try {
      const response = await axios.get(`/api/building-tracking/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tracking item ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new tracking item
   * @param {Object} trackingItemData - Tracking item data
   * @returns {Promise<Object>} Created tracking item
   */
  async createTrackingItem(trackingItemData) {
    try {
      const response = await axios.post('/api/building-tracking', trackingItemData);
      return response.data;
    } catch (error) {
      console.error('Error creating tracking item:', error);
      throw error;
    }
  }

  /**
   * Update a tracking item
   * @param {string} id - Tracking item ID
   * @param {Object} trackingItemData - Updated tracking item data
   * @returns {Promise<Object>} Updated tracking item
   */
  async updateTrackingItem(id, trackingItemData) {
    try {
      const response = await axios.put(`/api/building-tracking/${id}`, trackingItemData);
      return response.data;
    } catch (error) {
      console.error(`Error updating tracking item ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a tracking item
   * @param {string} id - Tracking item ID
   * @returns {Promise<Object>} Response message
   */
  async deleteTrackingItem(id) {
    try {
      const response = await axios.delete(`/api/building-tracking/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting tracking item ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get mock tracking items for development
   * @returns {Array} Mock tracking items
   */
  getMockTrackingItems() {
    const today = new Date();
    const nextMonth = new Date();
    nextMonth.setMonth(today.getMonth() + 1);
    
    const nextQuarter = new Date();
    nextQuarter.setMonth(today.getMonth() + 3);
    
    const nextYear = new Date();
    nextYear.setFullYear(today.getFullYear() + 1);
    
    return [
      {
        _id: '1',
        buildingId: {
          _id: '1',
          name: 'Main Office Building',
          address: {
            street: '123 Main St',
            city: 'Anytown',
            state: 'CA',
            zipCode: '12345'
          }
        },
        name: 'HVAC Maintenance Contract',
        type: 'contract',
        description: 'Quarterly maintenance of all HVAC systems',
        provider: {
          name: 'ABC HVAC Services',
          contactPerson: 'John Smith',
          phone: '************',
          email: '<EMAIL>',
          website: 'https://www.abchvac.com'
        },
        startDate: new Date(today.getFullYear(), today.getMonth() - 6, 15),
        expirationDate: nextYear,
        renewalDate: new Date(nextYear.getTime() - 30 * 24 * 60 * 60 * 1000),
        notificationDays: 45,
        status: 'active',
        cost: {
          amount: 5000,
          currency: 'USD',
          billingCycle: 'annually'
        },
        documents: [
          {
            name: 'Contract Document',
            fileUrl: '/documents/hvac-contract.pdf',
            uploadDate: today
          }
        ],
        notes: 'Includes quarterly maintenance visits and emergency service'
      },
      {
        _id: '2',
        buildingId: {
          _id: '1',
          name: 'Main Office Building',
          address: {
            street: '123 Main St',
            city: 'Anytown',
            state: 'CA',
            zipCode: '12345'
          }
        },
        name: 'Fire Alarm System Inspection',
        type: 'inspection',
        description: 'Annual inspection of fire alarm system',
        provider: {
          name: 'Safety First Inspections',
          contactPerson: 'Sarah Johnson',
          phone: '************',
          email: '<EMAIL>',
          website: 'https://www.safetyfirst.com'
        },
        startDate: new Date(today.getFullYear(), today.getMonth() - 11, 10),
        expirationDate: nextMonth,
        renewalDate: null,
        notificationDays: 30,
        status: 'pending_renewal',
        cost: {
          amount: 1200,
          currency: 'USD',
          billingCycle: 'annually'
        },
        documents: [
          {
            name: 'Last Inspection Report',
            fileUrl: '/documents/fire-inspection-report.pdf',
            uploadDate: new Date(today.getFullYear(), today.getMonth() - 11, 10)
          }
        ],
        notes: 'Required by local fire code'
      },
      {
        _id: '3',
        buildingId: {
          _id: '2',
          name: 'Warehouse Facility',
          address: {
            street: '456 Industrial Pkwy',
            city: 'Anytown',
            state: 'CA',
            zipCode: '12345'
          }
        },
        name: 'Security System Monitoring',
        type: 'service',
        description: '24/7 monitoring of security system',
        provider: {
          name: 'SecureWatch Monitoring',
          contactPerson: 'Mike Wilson',
          phone: '************',
          email: '<EMAIL>',
          website: 'https://www.securewatch.com'
        },
        startDate: new Date(today.getFullYear() - 1, today.getMonth(), 1),
        expirationDate: nextQuarter,
        renewalDate: new Date(nextQuarter.getTime() - 30 * 24 * 60 * 60 * 1000),
        notificationDays: 60,
        status: 'active',
        cost: {
          amount: 250,
          currency: 'USD',
          billingCycle: 'monthly'
        },
        documents: [
          {
            name: 'Service Agreement',
            fileUrl: '/documents/security-agreement.pdf',
            uploadDate: new Date(today.getFullYear() - 1, today.getMonth(), 1)
          }
        ],
        notes: 'Includes emergency dispatch service'
      }
    ];
  }
}

export default new BuildingTrackingService();