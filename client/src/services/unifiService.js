import unifiAccessService from './unifiAccessService';
import unifiNetworkService from './unifiNetworkService';
import unifiProtectService from './unifiProtectService';

/**
 * Unified UniFi Service
 * Consolidates UniFi Access, Network, and Protect services to reduce code duplication
 * Provides a single interface for all UniFi-related operations
 */
class UnifiService {
  constructor() {
    // Reference to individual services
    this.access = unifiAccessService;
    this.network = unifiNetworkService;
    this.protect = unifiProtectService;
  }

  // ===== Unified Device Management =====

  /**
   * Get all UniFi devices across all services
   * Consolidates data from Access (doors), Network (APs), and Protect (cameras)
   */
  async getAllDevices(buildingId = null, floorId = null) {
    try {
      const results = await Promise.allSettled([
        this.access.getDoors(),
        this.network.getAccessPoints(),
        this.protect.getCameras()
      ]);

      const devices = {
        doors: [],
        accessPoints: [],
        cameras: [],
        total: 0,
        errors: []
      };

      // Process doors (UniFi Access)
      if (results[0].status === 'fulfilled') {
        devices.doors = results[0].value
          .filter(door => !buildingId || door.buildingId === buildingId)
          .filter(door => !floorId || door.floorId === floorId)
          .map(door => ({
            ...door,
            deviceType: 'door',
            service: 'access',
            status: door.doorLockRelay?.isLocked ? 'locked' : 'unlocked'
          }));
      } else {
        devices.errors.push({ service: 'access', error: results[0].reason?.message || 'Failed to fetch doors' });
      }

      // Process access points (UniFi Network)
      if (results[1].status === 'fulfilled') {
        devices.accessPoints = results[1].value
          .filter(ap => !buildingId || ap.buildingId === buildingId)
          .filter(ap => !floorId || ap.floorId === floorId)
          .map(ap => ({
            ...ap,
            deviceType: 'accessPoint',
            service: 'network',
            status: ap.state === 1 ? 'online' : 'offline'
          }));
      } else {
        devices.errors.push({ service: 'network', error: results[1].reason?.message || 'Failed to fetch access points' });
      }

      // Process cameras (UniFi Protect)
      if (results[2].status === 'fulfilled') {
        devices.cameras = results[2].value
          .filter(camera => !buildingId || camera.buildingId === buildingId)
          .filter(camera => !floorId || camera.floorId === floorId)
          .map(camera => ({
            ...camera,
            deviceType: 'camera',
            service: 'protect',
            status: camera.isConnected ? 'online' : 'offline'
          }));
      } else {
        devices.errors.push({ service: 'protect', error: results[2].reason?.message || 'Failed to fetch cameras' });
      }

      devices.total = devices.doors.length + devices.accessPoints.length + devices.cameras.length;

      return devices;
    } catch (error) {
      console.error('Error fetching all UniFi devices:', error);
      throw error;
    }
  }

  /**
   * Get device health status across all UniFi services
   */
  async getSystemHealth() {
    try {
      const results = await Promise.allSettled([
        this.access.getSystemHealth(),
        this.network.getSystemHealth(),
        this.protect.getSystemHealth()
      ]);

      const health = {
        access: { status: 'unknown', details: null },
        network: { status: 'unknown', details: null },
        protect: { status: 'unknown', details: null },
        overall: 'unknown'
      };

      // Process Access health
      if (results[0].status === 'fulfilled') {
        health.access = { status: 'healthy', details: results[0].value };
      } else {
        health.access = { status: 'error', details: results[0].reason?.message };
      }

      // Process Network health
      if (results[1].status === 'fulfilled') {
        health.network = { status: 'healthy', details: results[1].value };
      } else {
        health.network = { status: 'error', details: results[1].reason?.message };
      }

      // Process Protect health
      if (results[2].status === 'fulfilled') {
        health.protect = { status: 'healthy', details: results[2].value };
      } else {
        health.protect = { status: 'error', details: results[2].reason?.message };
      }

      // Determine overall health
      const healthStatuses = [health.access.status, health.network.status, health.protect.status];
      if (healthStatuses.every(status => status === 'healthy')) {
        health.overall = 'healthy';
      } else if (healthStatuses.some(status => status === 'healthy')) {
        health.overall = 'partial';
      } else {
        health.overall = 'unhealthy';
      }

      return health;
    } catch (error) {
      console.error('Error checking UniFi system health:', error);
      throw error;
    }
  }

  // ===== Cross-Service Operations =====

  /**
   * Perform bulk operations across multiple UniFi services
   */
  async performBulkOperation(operation, devices = []) {
    const results = {
      successful: [],
      failed: [],
      total: devices.length
    };

    for (const device of devices) {
      try {
        let result;
        
        switch (device.service) {
          case 'access':
            result = await this._performAccessOperation(operation, device);
            break;
          case 'network':
            result = await this._performNetworkOperation(operation, device);
            break;
          case 'protect':
            result = await this._performProtectOperation(operation, device);
            break;
          default:
            throw new Error(`Unknown service: ${device.service}`);
        }

        results.successful.push({
          deviceId: device.id,
          service: device.service,
          operation,
          result
        });
      } catch (error) {
        results.failed.push({
          deviceId: device.id,
          service: device.service,
          operation,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Sync data across all UniFi services
   */
  async syncAllServices(options = {}) {
    try {
      const syncResults = await Promise.allSettled([
        this.access.syncDevices(options),
        this.network.syncDevices(options),
        this.protect.syncDevices(options)
      ]);

      return {
        access: syncResults[0].status === 'fulfilled' ? syncResults[0].value : { error: syncResults[0].reason?.message },
        network: syncResults[1].status === 'fulfilled' ? syncResults[1].value : { error: syncResults[1].reason?.message },
        protect: syncResults[2].status === 'fulfilled' ? syncResults[2].value : { error: syncResults[2].reason?.message },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error syncing all UniFi services:', error);
      throw error;
    }
  }

  // ===== Private Helper Methods =====

  async _performAccessOperation(operation, device) {
    switch (operation) {
      case 'unlock':
        return await this.access.unlockDoor(device.id, { duration: 5000 });
      case 'lock':
        return await this.access.lockDoor(device.id);
      case 'status':
        return await this.access.getDoorStatus(device.id);
      default:
        throw new Error(`Unsupported Access operation: ${operation}`);
    }
  }

  async _performNetworkOperation(operation, device) {
    switch (operation) {
      case 'restart':
        return await this.network.restartAccessPoint(device.id);
      case 'status':
        return await this.network.getAccessPointStatus(device.id);
      case 'clients':
        return await this.network.getAccessPointClients(device.id);
      default:
        throw new Error(`Unsupported Network operation: ${operation}`);
    }
  }

  async _performProtectOperation(operation, device) {
    switch (operation) {
      case 'snapshot':
        return await this.protect.getCameraSnapshot(device.id);
      case 'status':
        return await this.protect.getCameraStatus(device.id);
      case 'recording':
        return await this.protect.getCameraRecordings(device.id);
      default:
        throw new Error(`Unsupported Protect operation: ${operation}`);
    }
  }

  // ===== Convenience Methods =====

  /**
   * Get all doors - shortcut to access service
   */
  async getDoors(params = {}) {
    return await this.access.getDoors(params);
  }

  /**
   * Get all access points - shortcut to network service
   */
  async getAccessPoints(params = {}) {
    return await this.network.getAccessPoints(params);
  }

  /**
   * Get all cameras - shortcut to protect service
   */
  async getCameras(params = {}) {
    return await this.protect.getCameras(params);
  }

  /**
   * Emergency operations across all services
   */
  async emergencyLockdown(buildingId) {
    console.log(`Initiating emergency lockdown for building ${buildingId}`);
    
    try {
      // Get all doors in the building
      const doors = await this.access.getDoors({ buildingId });
      
      // Lock all doors
      const lockResults = await Promise.allSettled(
        doors.map(door => this.access.lockDoor(door.id))
      );

      return {
        buildingId,
        doorsLocked: lockResults.filter(result => result.status === 'fulfilled').length,
        doorsFailed: lockResults.filter(result => result.status === 'rejected').length,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error during emergency lockdown:', error);
      throw error;
    }
  }
}

const unifiService = new UnifiService();
export default unifiService;