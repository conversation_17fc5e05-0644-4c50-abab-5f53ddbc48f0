import axios from 'axios';

const API_BASE = '/api/volunteer-resources';

class VolunteerResourceService {
  
  // Get all volunteer resources with filtering and pagination
  async getResources(params = {}) {
    try {
      const response = await axios.get(API_BASE, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching volunteer resources:', error);
      throw error;
    }
  }

  // Get specific resource by ID
  async getResource(id) {
    try {
      const response = await axios.get(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching resource ${id}:`, error);
      throw error;
    }
  }

  // Get resource categories with counts
  async getCategories(buildingId = null, floorId = null) {
    try {
      const params = {};
      if (buildingId) params.buildingId = buildingId;
      if (floorId) params.floorId = floorId;
      
      const response = await axios.get(`${API_BASE}/categories`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching resource categories:', error);
      throw error;
    }
  }

  // Search resources
  async searchResources(query, filters = {}) {
    try {
      const params = { q: query, ...filters };
      const response = await axios.get(`${API_BASE}/search`, { params });
      return response.data;
    } catch (error) {
      console.error('Error searching resources:', error);
      throw error;
    }
  }

  // Find nearest resources
  async findNearestResources(floorId, category = null, limit = 5) {
    try {
      const params = { floorId, limit };
      if (category) params.category = category;
      
      const response = await axios.get(`${API_BASE}/nearest`, { params });
      return response.data;
    } catch (error) {
      console.error('Error finding nearest resources:', error);
      throw error;
    }
  }

  // Get popular resources
  async getPopularResources(category = null, limit = 10) {
    try {
      const params = { limit };
      if (category) params.category = category;
      
      const response = await axios.get(`${API_BASE}/popular`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching popular resources:', error);
      throw error;
    }
  }

  // Get maintenance alerts (Admin/Facilities only)
  async getMaintenanceAlerts() {
    try {
      const response = await axios.get(`${API_BASE}/maintenance-alerts`);
      return response.data;
    } catch (error) {
      console.error('Error fetching maintenance alerts:', error);
      throw error;
    }
  }

  // Create new resource (Admin/Facilities only)
  async createResource(resourceData) {
    try {
      const response = await axios.post(API_BASE, resourceData);
      return response.data;
    } catch (error) {
      console.error('Error creating resource:', error);
      throw error;
    }
  }

  // Update resource (Admin/Facilities only)
  async updateResource(id, resourceData) {
    try {
      const response = await axios.put(`${API_BASE}/${id}`, resourceData);
      return response.data;
    } catch (error) {
      console.error(`Error updating resource ${id}:`, error);
      throw error;
    }
  }

  // Delete resource (Admin only)
  async deleteResource(id) {
    try {
      const response = await axios.delete(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting resource ${id}:`, error);
      throw error;
    }
  }

  // Record resource usage
  async recordUsage(id, quantity = 1, eventId = null, notes = '') {
    try {
      const response = await axios.post(`${API_BASE}/${id}/use`, {
        quantity,
        eventId,
        notes
      });
      return response.data;
    } catch (error) {
      console.error(`Error recording usage for resource ${id}:`, error);
      throw error;
    }
  }

  // Update maintenance information (Admin/Facilities only)
  async updateMaintenance(id, maintenanceData) {
    try {
      const response = await axios.post(`${API_BASE}/${id}/maintenance`, maintenanceData);
      return response.data;
    } catch (error) {
      console.error(`Error updating maintenance for resource ${id}:`, error);
      throw error;
    }
  }

  // Acknowledge alert
  async acknowledgeAlert(resourceId, alertId) {
    try {
      const response = await axios.post(`${API_BASE}/${resourceId}/alerts/${alertId}/acknowledge`);
      return response.data;
    } catch (error) {
      console.error(`Error acknowledging alert ${alertId}:`, error);
      throw error;
    }
  }

  // Get statistics overview (Admin/Facilities only)
  async getStatistics() {
    try {
      const response = await axios.get(`${API_BASE}/stats/overview`);
      return response.data;
    } catch (error) {
      console.error('Error fetching resource statistics:', error);
      throw error;
    }
  }

  // Utility methods

  // Get resources by category
  async getResourcesByCategory(category, buildingId = null) {
    return this.getResources({ category, buildingId, includeUnavailable: false });
  }

  // Get available resources for a specific floor
  async getAvailableForFloor(floorId) {
    return this.getResources({ 
      floorId, 
      status: 'available',
      sortBy: 'popularity',
      sortOrder: 'desc'
    });
  }

  // Get user's recent usage
  async getRecentUsage(userId, days = 30) {
    // This would need additional endpoint implementation
    // For now, return empty array
    return [];
  }

  // Validate resource data
  validateResourceData(data) {
    const errors = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Resource name is required');
    }

    if (!data.category) {
      errors.push('Category is required');
    }

    if (!data.buildingId) {
      errors.push('Building is required');
    }

    if (!data.floorId) {
      errors.push('Floor is required');
    }

    if (!data.room || data.room.trim().length === 0) {
      errors.push('Room is required');
    }

    if (!data.resourceType) {
      errors.push('Resource type is required');
    }

    if (data.quantity && data.quantity.available < 0) {
      errors.push('Available quantity cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get category configuration
  getCategoryConfig() {
    return {
      cleaning_supplies: {
        label: 'Cleaning Supplies',
        icon: 'cleaning_services',
        color: '#4CAF50',
        subcategories: ['Brooms & Mops', 'Vacuum Cleaners', 'Cleaning Products', 'Paper Products']
      },
      event_supplies: {
        label: 'Event Supplies',
        icon: 'event',
        color: '#2196F3',
        subcategories: ['Tables & Chairs', 'Linens & Decorations', 'Serving Items', 'Setup Materials']
      },
      av_equipment: {
        label: 'AV Equipment',
        icon: 'mic',
        color: '#FF9800',
        subcategories: ['Microphones', 'Cables & Adapters', 'Speakers', 'Recording Equipment', 'Batteries']
      },
      signage: {
        label: 'Signage',
        icon: 'signpost',
        color: '#673AB7',
        subcategories: ['Directional Signs', 'Banners', 'Easels & Stands', 'Markers & Materials']
      },
      kitchen_supplies: {
        label: 'Kitchen Supplies',
        icon: 'kitchen',
        color: '#E91E63',
        subcategories: ['Serving Dishes', 'Utensils', 'Napkins & Plates', 'Cleaning Supplies']
      },
      office_supplies: {
        label: 'Office Supplies',
        icon: 'inventory',
        color: '#607D8B',
        subcategories: ['Paper Products', 'Writing Materials', 'Filing & Organization', 'Technology']
      },
      safety_equipment: {
        label: 'Safety Equipment',
        icon: 'safety_check',
        color: '#F44336',
        subcategories: ['First Aid', 'Flashlights', 'Emergency Supplies', 'Personal Protection']
      },
      maintenance_tools: {
        label: 'Maintenance Tools',
        icon: 'handyman',
        color: '#795548',
        subcategories: ['Basic Tools', 'Ladder Access', 'Electrical Tools', 'Plumbing Tools']
      },
      setup_equipment: {
        label: 'Setup Equipment',
        icon: 'moving',
        color: '#9C27B0',
        subcategories: ['Dollies & Carts', 'Rope & Straps', 'Moving Equipment', 'Loading Supplies']
      },
      storage_area: {
        label: 'Storage Areas',
        icon: 'warehouse',
        color: '#FF5722',
        subcategories: ['General Storage', 'Seasonal Items', 'Archive Storage', 'Overflow Storage']
      },
      other: {
        label: 'Other',
        icon: 'category',
        color: '#757575',
        subcategories: []
      }
    };
  }

  // Get resource type configuration
  getResourceTypeConfig() {
    return {
      consumable: {
        label: 'Consumable',
        description: 'Items that are used up and need replenishment',
        trackQuantity: true,
        examples: ['Paper products', 'Cleaning supplies', 'Batteries']
      },
      equipment: {
        label: 'Equipment',
        description: 'Reusable items that can be checked out',
        trackQuantity: true,
        examples: ['Vacuum cleaners', 'Microphones', 'Tables']
      },
      space: {
        label: 'Space',
        description: 'Storage areas and rooms',
        trackQuantity: false,
        examples: ['Storage closets', 'Meeting rooms', 'Work areas']
      },
      tool: {
        label: 'Tool',
        description: 'Tools for maintenance and setup',
        trackQuantity: true,
        examples: ['Screwdrivers', 'Ladders', 'Dollies']
      }
    };
  }

  // Get condition options
  getConditionOptions() {
    return [
      { value: 'excellent', label: 'Excellent', color: '#4CAF50' },
      { value: 'good', label: 'Good', color: '#8BC34A' },
      { value: 'fair', label: 'Fair', color: '#FF9800' },
      { value: 'poor', label: 'Poor', color: '#FF5722' },
      { value: 'needs_replacement', label: 'Needs Replacement', color: '#F44336' }
    ];
  }

  // Get status options
  getStatusOptions() {
    return [
      { value: 'available', label: 'Available', color: '#4CAF50', icon: 'check_circle' },
      { value: 'in_use', label: 'In Use', color: '#FF9800', icon: 'hourglass_empty' },
      { value: 'maintenance', label: 'Maintenance', color: '#2196F3', icon: 'build' },
      { value: 'missing', label: 'Missing', color: '#F44336', icon: 'error' },
      { value: 'broken', label: 'Broken', color: '#E91E63', icon: 'broken_image' },
      { value: 'reserved', label: 'Reserved', color: '#673AB7', icon: 'event_busy' }
    ];
  }

  // Format resource for display
  formatResourceForDisplay(resource) {
    const categoryConfig = this.getCategoryConfig();
    const statusConfig = this.getStatusOptions().find(s => s.value === resource.status);
    const conditionConfig = this.getConditionOptions().find(c => c.value === resource.specifications?.condition);
    
    return {
      ...resource,
      categoryInfo: categoryConfig[resource.category] || categoryConfig.other,
      statusInfo: statusConfig || { value: 'unknown', label: 'Unknown', color: '#757575' },
      conditionInfo: conditionConfig || { value: 'unknown', label: 'Unknown', color: '#757575' },
      isAvailable: resource.status === 'available' && resource.quantity?.available > 0,
      displayLocation: `${resource.room}${resource.location?.specific ? ` - ${resource.location.specific}` : ''}`,
      popularityPercent: Math.round(resource.usage?.popularityScore || 0),
      lastUsedFormatted: resource.usage?.lastUsed ? 
        new Date(resource.usage.lastUsed).toLocaleDateString() : 'Never used'
    };
  }

  // Create quick search suggestions
  getQuickSearchSuggestions() {
    return [
      { query: 'vacuum', category: 'cleaning_supplies', label: 'Vacuum Cleaners' },
      { query: 'microphone', category: 'av_equipment', label: 'Microphones' },
      { query: 'table', category: 'event_supplies', label: 'Tables' },
      { query: 'chair', category: 'event_supplies', label: 'Chairs' },
      { query: 'extension cord', category: 'av_equipment', label: 'Extension Cords' },
      { query: 'first aid', category: 'safety_equipment', label: 'First Aid Kits' },
      { query: 'banner', category: 'signage', label: 'Banners' },
      { query: 'ladder', category: 'maintenance_tools', label: 'Ladders' },
      { query: 'cart', category: 'setup_equipment', label: 'Carts & Dollies' },
      { query: 'paper towels', category: 'cleaning_supplies', label: 'Paper Towels' }
    ];
  }

  // Get emergency resource list
  getEmergencyResources() {
    return [
      { category: 'safety_equipment', query: 'first aid', priority: 1 },
      { category: 'safety_equipment', query: 'flashlight', priority: 2 },
      { category: 'maintenance_tools', query: 'ladder', priority: 3 },
      { category: 'av_equipment', query: 'battery', priority: 4 },
      { category: 'cleaning_supplies', query: 'paper towels', priority: 5 }
    ];
  }

  // Calculate distance between resources (placeholder for future GPS integration)
  calculateDistance(resource1, resource2) {
    // Simplified distance calculation based on floor difference
    if (resource1.floorId === resource2.floorId) {
      return 'Same floor';
    } else {
      const floor1 = resource1.floorId?.level || 0;
      const floor2 = resource2.floorId?.level || 0;
      const floorDiff = Math.abs(floor1 - floor2);
      return `${floorDiff} floor${floorDiff > 1 ? 's' : ''} away`;
    }
  }

  // Generate usage report data
  generateUsageReport(resources, timeRange = 30) {
    const now = new Date();
    const startDate = new Date(now.getTime() - (timeRange * 24 * 60 * 60 * 1000));
    
    return resources.map(resource => {
      const recentUsage = resource.usage?.usageHistory?.filter(usage => 
        new Date(usage.usedAt) >= startDate
      ) || [];
      
      return {
        id: resource._id,
        name: resource.name,
        category: resource.category,
        totalUsage: resource.usage?.timesUsed || 0,
        recentUsage: recentUsage.length,
        popularityScore: resource.usage?.popularityScore || 0,
        condition: resource.specifications?.condition || 'unknown',
        status: resource.status,
        lastUsed: resource.usage?.lastUsed
      };
    });
  }
}

export default new VolunteerResourceService();