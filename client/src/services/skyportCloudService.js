import axios from 'axios';

/**
 * SkyportCloud API Service
 * Handles client-side API calls to the SkyportCloud endpoints
 */
const skyportCloudService = {
  /**
   * Get user information
   * @returns {Promise<Object>} User information
   */
  getUserInfo: async () => {
    try {
      const response = await axios.get('/api/skyportcloud/user');
      return response.data;
    } catch (error) {
      console.error('Error fetching SkyportCloud user information:', error);
      throw error;
    }
  },

  /**
   * Get list of devices
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async () => {
    try {
      const response = await axios.get('/api/skyportcloud/devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching SkyportCloud devices:', error);
      throw error;
    }
  },

  /**
   * Get device information
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device information
   */
  getDeviceInfo: async (deviceId) => {
    try {
      const response = await axios.get(`/api/skyportcloud/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching SkyportCloud device ${deviceId} information:`, error);
      throw error;
    }
  },

  /**
   * Get device status
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device status
   */
  getDeviceStatus: async (deviceId) => {
    try {
      const response = await axios.get(`/api/skyportcloud/devices/${deviceId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching SkyportCloud device ${deviceId} status:`, error);
      throw error;
    }
  },

  /**
   * Set device temperature
   * @param {string} deviceId Device ID
   * @param {number} temperature Temperature in degrees (unit depends on device settings)
   * @returns {Promise<Object>} Response data
   */
  setTemperature: async (deviceId, temperature) => {
    try {
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/temperature`, { temperature });
      return response.data;
    } catch (error) {
      console.error(`Error setting temperature for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Set device mode
   * @param {string} deviceId Device ID
   * @param {string} mode Mode ('heat', 'cool', 'auto', 'off')
   * @returns {Promise<Object>} Response data
   */
  setMode: async (deviceId, mode) => {
    try {
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/mode`, { mode });
      return response.data;
    } catch (error) {
      console.error(`Error setting mode for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Set fan mode
   * @param {string} deviceId Device ID
   * @param {string} fanMode Fan mode ('auto', 'on', 'circulate')
   * @returns {Promise<Object>} Response data
   */
  setFanMode: async (deviceId, fanMode) => {
    try {
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/fan`, { fanMode });
      return response.data;
    } catch (error) {
      console.error(`Error setting fan mode for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get device schedule
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Schedule data
   */
  getSchedule: async (deviceId) => {
    try {
      const response = await axios.get(`/api/skyportcloud/devices/${deviceId}/schedule`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching schedule for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Set device schedule
   * @param {string} deviceId Device ID
   * @param {Object} schedule Schedule object
   * @returns {Promise<Object>} Response data
   */
  setSchedule: async (deviceId, schedule) => {
    try {
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/schedule`, schedule);
      return response.data;
    } catch (error) {
      console.error(`Error setting schedule for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get device energy usage
   * @param {string} deviceId Device ID
   * @param {string} period Period ('day', 'week', 'month', 'year')
   * @returns {Promise<Object>} Energy usage data
   */
  getEnergyUsage: async (deviceId, period = 'month') => {
    try {
      const response = await axios.get(`/api/skyportcloud/devices/${deviceId}/energy`, {
        params: { period }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching energy usage for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Set hold status
   * @param {string} deviceId Device ID
   * @param {boolean} enabled Whether hold is enabled
   * @param {number} until Timestamp until when the hold should be active (optional)
   * @returns {Promise<Object>} Response data
   */
  setHold: async (deviceId, enabled, until = null) => {
    try {
      const data = { enabled };
      if (until) {
        data.until = until;
      }
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/hold`, data);
      return response.data;
    } catch (error) {
      console.error(`Error setting hold for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Set away mode
   * @param {string} deviceId Device ID
   * @param {boolean} enabled Whether away mode is enabled
   * @returns {Promise<Object>} Response data
   */
  setAwayMode: async (deviceId, enabled) => {
    try {
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/away`, { enabled });
      return response.data;
    } catch (error) {
      console.error(`Error setting away mode for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get device zones (for multi-zone systems)
   * @param {string} deviceId Device ID
   * @returns {Promise<Array>} List of zones
   */
  getZones: async (deviceId) => {
    try {
      const response = await axios.get(`/api/skyportcloud/devices/${deviceId}/zones`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching zones for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Set zone settings
   * @param {string} deviceId Device ID
   * @param {string} zoneId Zone ID
   * @param {Object} settings Zone settings
   * @returns {Promise<Object>} Response data
   */
  setZoneSettings: async (deviceId, zoneId, settings) => {
    try {
      const response = await axios.post(`/api/skyportcloud/devices/${deviceId}/zones/${zoneId}`, settings);
      return response.data;
    } catch (error) {
      console.error(`Error setting zone settings for SkyportCloud device ${deviceId}, zone ${zoneId}:`, error);
      throw error;
    }
  },

  /**
   * Get SkyportCloud configuration
   * @returns {Promise<Object>} Configuration data
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/skyportcloud/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching SkyportCloud configuration:', error);
      throw error;
    }
  },

  /**
   * Save SkyportCloud configuration
   * @param {Object} config Configuration object
   * @returns {Promise<Object>} Response data
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/skyportcloud/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving SkyportCloud configuration:', error);
      throw error;
    }
  }
};

export default skyportCloudService;