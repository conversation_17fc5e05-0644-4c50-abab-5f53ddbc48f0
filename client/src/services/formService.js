import axios from 'axios';

/**
 * Form Service
 * Handles API calls for the form system
 */
const formService = {
  /**
   * Get all forms with optional filtering
   * @param {Object} filters - Optional filters (status, search)
   * @param {Number} page - Page number for pagination
   * @param {Number} limit - Number of items per page
   * @param {String} sort - Field to sort by
   * @param {String} order - Sort order (asc/desc)
   * @returns {Promise} - Promise with forms data
   */
  getForms: async (filters = {}, page = 1, limit = 10, sort = 'createdAt', order = 'desc') => {
    const { status, search } = filters;
    let url = `/api/forms?page=${page}&limit=${limit}&sort=${sort}&order=${order}`;
    
    if (status) url += `&status=${status}`;
    if (search) url += `&search=${encodeURIComponent(search)}`;
    
    const response = await axios.get(url);
    return response.data;
  },
  
  /**
   * Get a single form by ID or slug
   * @param {String} idOrSlug - Form ID or slug
   * @returns {Promise} - Promise with form data
   */
  getForm: async (idOrSlug) => {
    const response = await axios.get(`/api/forms/${idOrSlug}`);
    return response.data;
  },
  
  /**
   * Create a new form
   * @param {Object} formData - Form data
   * @returns {Promise} - Promise with created form
   */
  createForm: async (formData) => {
    const response = await axios.post('/api/forms', formData);
    return response.data;
  },
  
  /**
   * Update an existing form
   * @param {String} id - Form ID
   * @param {Object} formData - Updated form data
   * @returns {Promise} - Promise with updated form
   */
  updateForm: async (id, formData) => {
    const response = await axios.put(`/api/forms/${id}`, formData);
    return response.data;
  },
  
  /**
   * Delete a form
   * @param {String} id - Form ID
   * @returns {Promise} - Promise with deletion result
   */
  deleteForm: async (id) => {
    const response = await axios.delete(`/api/forms/${id}`);
    return response.data;
  },
  
  /**
   * Duplicate a form
   * @param {String} id - Form ID to duplicate
   * @returns {Promise} - Promise with duplicated form
   */
  duplicateForm: async (id) => {
    const response = await axios.post(`/api/forms/${id}/duplicate`);
    return response.data;
  },
  
  /**
   * Get form statistics
   * @param {String} id - Form ID
   * @returns {Promise} - Promise with form statistics
   */
  getFormStats: async (id) => {
    const response = await axios.get(`/api/forms/${id}/stats`);
    return response.data;
  },
  
  /**
   * Get submissions for a form
   * @param {String} formId - Form ID
   * @param {Object} filters - Optional filters (status)
   * @param {Number} page - Page number for pagination
   * @param {Number} limit - Number of items per page
   * @param {String} sort - Field to sort by
   * @param {String} order - Sort order (asc/desc)
   * @returns {Promise} - Promise with submissions data
   */
  getFormSubmissions: async (formId, filters = {}, page = 1, limit = 10, sort = 'createdAt', order = 'desc') => {
    const { status } = filters;
    let url = `/api/forms/${formId}/submissions?page=${page}&limit=${limit}&sort=${sort}&order=${order}`;
    
    if (status) url += `&status=${status}`;
    
    const response = await axios.get(url);
    return response.data;
  },
  
  /**
   * Get a single submission
   * @param {String} id - Submission ID
   * @returns {Promise} - Promise with submission data
   */
  getSubmission: async (id) => {
    const response = await axios.get(`/api/forms/submissions/${id}`);
    return response.data;
  },
  
  /**
   * Submit a form
   * @param {String} formId - Form ID
   * @param {Object} formData - Form data to submit
   * @param {Array} files - Optional files to upload
   * @returns {Promise} - Promise with submission result
   */
  submitForm: async (formId, formData, files = []) => {
    // If there are files, use FormData to send multipart/form-data
    if (files && files.length > 0) {
      const data = new FormData();
      data.append('data', JSON.stringify(formData));
      
      // Append each file with the field ID as the field name
      files.forEach(file => {
        data.append(file.fieldId, file.file);
      });
      
      const response = await axios.post(`/api/forms/${formId}/submissions`, data, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } else {
      // No files, send regular JSON
      const response = await axios.post(`/api/forms/${formId}/submissions`, { data: formData });
      return response.data;
    }
  },
  
  /**
   * Update a submission
   * @param {String} id - Submission ID
   * @param {Object} updateData - Data to update
   * @returns {Promise} - Promise with update result
   */
  updateSubmission: async (id, updateData) => {
    const response = await axios.put(`/api/forms/submissions/${id}`, updateData);
    return response.data;
  },
  
  /**
   * Delete a submission
   * @param {String} id - Submission ID
   * @returns {Promise} - Promise with deletion result
   */
  deleteSubmission: async (id) => {
    const response = await axios.delete(`/api/forms/submissions/${id}`);
    return response.data;
  },
  
  /**
   * Get a public form by slug
   * @param {String} slug - Form slug
   * @returns {Promise} - Promise with form data
   */
  getPublicForm: async (slug) => {
    const response = await axios.get(`/api/public-forms/${slug}`);
    return response.data;
  },
  
  /**
   * Submit a public form
   * @param {String} slug - Form slug
   * @param {Object} formData - Form data to submit
   * @param {Array} files - Optional files to upload
   * @returns {Promise} - Promise with submission result
   */
  submitPublicForm: async (slug, formData, files = []) => {
    // If there are files, use FormData to send multipart/form-data
    if (files && files.length > 0) {
      const data = new FormData();
      data.append('data', JSON.stringify(formData));
      
      // Append each file with the field ID as the field name
      files.forEach(file => {
        data.append(file.fieldId, file.file);
      });
      
      const response = await axios.post(`/api/public-forms/${slug}/submit`, data, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } else {
      // No files, send regular JSON
      const response = await axios.post(`/api/public-forms/${slug}/submit`, { data: formData });
      return response.data;
    }
  },
  
  /**
   * Get file URL for a submission file
   * @param {String} filename - File name
   * @param {Boolean} isPublic - Whether the file is from a public form
   * @returns {String} - File URL
   */
  getFileUrl: (filename, isPublic = false) => {
    if (isPublic) {
      return `/api/public-forms/submissions/files/${filename}`;
    } else {
      return `/api/forms/submissions/files/${filename}`;
    }
  }
};

export default formService;