import axios from 'axios';

/**
 * Audit Log Service
 * Handles security audit logging for BMS actions
 * Tracks critical actions, user permissions, and system access
 */
class AuditLogService {
  constructor() {
    this.baseURL = '/api/audit';
  }

  /**
   * Log a critical BMS action
   */
  async logAction(actionData) {
    try {
      const response = await axios.post(`${this.baseURL}/actions`, {
        action: actionData.action,
        entityType: actionData.entityType,
        entityId: actionData.entityId,
        userId: actionData.userId,
        details: actionData.details,
        timestamp: new Date().toISOString(),
        ipAddress: this.getClientIP(),
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId(),
        emergencyMode: actionData.emergencyMode || false,
        building: actionData.building,
        floor: actionData.floor
      });
      
      return response.data;
    } catch (error) {
      console.error('Failed to log audit action:', error);
      // Store locally if server unavailable
      this.storeLocalAuditLog(actionData);
      throw error;
    }
  }

  /**
   * Log door unlock/lock actions
   */
  async logDoorAction(doorId, action, userId, additionalData = {}) {
    return this.logAction({
      action: `door_${action}`,
      entityType: 'door',
      entityId: doorId,
      userId,
      details: {
        doorAction: action,
        duration: additionalData.duration,
        emergency: additionalData.emergency,
        ...additionalData
      },
      emergencyMode: additionalData.emergency,
      building: additionalData.buildingId,
      floor: additionalData.floorId
    });
  }

  /**
   * Log electrical system access
   */
  async logElectricalAction(action, entityType, entityId, userId, details = {}) {
    return this.logAction({
      action: `electrical_${action}`,
      entityType,
      entityId,
      userId,
      details: {
        electricalAction: action,
        ...details
      },
      building: details.buildingId,
      floor: details.floorId
    });
  }

  /**
   * Log safety equipment access
   */
  async logSafetyAction(action, assetId, userId, details = {}) {
    return this.logAction({
      action: `safety_${action}`,
      entityType: 'safety_asset',
      entityId: assetId,
      userId,
      details: {
        safetyAction: action,
        assetType: details.assetType,
        inspector: details.inspector,
        ...details
      },
      building: details.buildingId,
      floor: details.floorId
    });
  }

  /**
   * Log emergency mode activation
   */
  async logEmergencyMode(action, userId, buildingId, details = {}) {
    return this.logAction({
      action: `emergency_${action}`,
      entityType: 'building',
      entityId: buildingId,
      userId,
      details: {
        emergencyAction: action,
        triggeredBy: details.triggeredBy,
        affectedSystems: details.affectedSystems,
        ...details
      },
      emergencyMode: true,
      building: buildingId
    });
  }

  /**
   * Log admin panel edits
   */
  async logPanelEdit(action, panelId, userId, changes = {}) {
    return this.logAction({
      action: `panel_${action}`,
      entityType: 'electrical_panel',
      entityId: panelId,
      userId,
      details: {
        panelAction: action,
        changes,
        affectedCircuits: changes.circuits?.length || 0
      },
      building: changes.buildingId,
      floor: changes.floorId
    });
  }

  /**
   * Log camera access
   */
  async logCameraAccess(cameraId, action, userId, details = {}) {
    return this.logAction({
      action: `camera_${action}`,
      entityType: 'camera',
      entityId: cameraId,
      userId,
      details: {
        cameraAction: action,
        viewDuration: details.viewDuration,
        ...details
      },
      building: details.buildingId,
      floor: details.floorId
    });
  }

  /**
   * Get audit logs for a specific entity
   */
  async getEntityAuditLog(entityType, entityId, options = {}) {
    try {
      const params = {
        entityType,
        entityId,
        page: options.page || 1,
        limit: options.limit || 50,
        startDate: options.startDate,
        endDate: options.endDate,
        action: options.action,
        userId: options.userId
      };

      const response = await axios.get(`${this.baseURL}/entities/${entityType}/${entityId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get audit log:', error);
      throw error;
    }
  }

  /**
   * Get audit logs for a building
   */
  async getBuildingAuditLog(buildingId, options = {}) {
    try {
      const params = {
        buildingId,
        page: options.page || 1,
        limit: options.limit || 100,
        startDate: options.startDate,
        endDate: options.endDate,
        action: options.action,
        userId: options.userId,
        emergencyOnly: options.emergencyOnly
      };

      const response = await axios.get(`${this.baseURL}/buildings/${buildingId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get building audit log:', error);
      throw error;
    }
  }

  /**
   * Get emergency action logs
   */
  async getEmergencyLogs(buildingId, options = {}) {
    try {
      const params = {
        buildingId,
        emergencyOnly: true,
        page: options.page || 1,
        limit: options.limit || 50,
        startDate: options.startDate,
        endDate: options.endDate
      };

      const response = await axios.get(`${this.baseURL}/emergency`, { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get emergency logs:', error);
      throw error;
    }
  }

  /**
   * Get user activity summary
   */
  async getUserActivity(userId, options = {}) {
    try {
      const params = {
        userId,
        page: options.page || 1,
        limit: options.limit || 50,
        startDate: options.startDate,
        endDate: options.endDate,
        buildingId: options.buildingId
      };

      const response = await axios.get(`${this.baseURL}/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get user activity:', error);
      throw error;
    }
  }

  /**
   * Export audit logs
   */
  async exportAuditLogs(filters = {}, format = 'csv') {
    try {
      const params = {
        ...filters,
        format,
        export: true
      };

      const response = await axios.get(`${this.baseURL}/export`, { 
        params,
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `audit_log_${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      return { success: true, message: 'Audit log exported successfully' };
    } catch (error) {
      console.error('Failed to export audit logs:', error);
      throw error;
    }
  }

  /**
   * Get real-time audit log stats
   */
  async getAuditStats(buildingId, timeRange = '24h') {
    try {
      const response = await axios.get(`${this.baseURL}/stats`, {
        params: { buildingId, timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get audit stats:', error);
      throw error;
    }
  }

  /**
   * Store audit log locally when server is unavailable
   */
  storeLocalAuditLog(actionData) {
    try {
      const localLogs = JSON.parse(localStorage.getItem('bms_audit_logs') || '[]');
      localLogs.push({
        ...actionData,
        timestamp: new Date().toISOString(),
        stored_locally: true
      });
      
      // Keep only last 100 local logs
      const trimmedLogs = localLogs.slice(-100);
      localStorage.setItem('bms_audit_logs', JSON.stringify(trimmedLogs));
    } catch (error) {
      console.error('Failed to store local audit log:', error);
    }
  }

  /**
   * Sync local audit logs to server
   */
  async syncLocalLogs() {
    try {
      const localLogs = JSON.parse(localStorage.getItem('bms_audit_logs') || '[]');
      const unsynced = localLogs.filter(log => log.stored_locally);
      
      if (unsynced.length === 0) return { synced: 0 };

      const response = await axios.post(`${this.baseURL}/sync`, { logs: unsynced });
      
      // Remove synced logs from local storage
      const remaining = localLogs.filter(log => !log.stored_locally);
      localStorage.setItem('bms_audit_logs', JSON.stringify(remaining));
      
      return { synced: unsynced.length, success: true };
    } catch (error) {
      console.error('Failed to sync local logs:', error);
      throw error;
    }
  }

  /**
   * Helper methods
   */
  getClientIP() {
    // This would be populated by the server or client-side IP detection
    return 'client_ip_placeholder';
  }

  getSessionId() {
    // Get session ID from authentication context
    return sessionStorage.getItem('session_id') || 'unknown_session';
  }

  /**
   * Validate user permissions for specific actions
   */
  validatePermissions(action, userRole, entityType) {
    const permissions = {
      viewer: {
        allowed: ['view', 'inspect'],
        restricted: ['edit', 'delete', 'unlock', 'lock', 'service']
      },
      editor: {
        allowed: ['view', 'inspect', 'edit', 'service'],
        restricted: ['delete', 'unlock', 'lock']
      },
      admin: {
        allowed: ['view', 'inspect', 'edit', 'delete', 'unlock', 'lock', 'service'],
        restricted: []
      }
    };

    const userPermissions = permissions[userRole] || permissions.viewer;
    
    if (userPermissions.restricted.includes(action)) {
      return { allowed: false, reason: `Action '${action}' not permitted for role '${userRole}'` };
    }

    if (userPermissions.allowed.includes(action)) {
      return { allowed: true };
    }

    return { allowed: false, reason: `Action '${action}' not defined for role '${userRole}'` };
  }
}

const auditLogService = new AuditLogService();
export default auditLogService;