import axios from 'axios';

/**
 * Notes Service
 * Provides methods for interacting with the notes API
 */
const notesService = {
  /**
   * Get all notes for the current user
   * @returns {Promise<Array>} List of notes
   */
  getUserNotes: async () => {
    try {
      const response = await axios.get('/api/notes');
      return response.data;
    } catch (error) {
      console.error('Error getting notes:', error);
      throw error;
    }
  },

  /**
   * Get note by ID
   * @param {string} id - Note ID
   * @returns {Promise<Object>} Note details
   */
  getNoteById: async (id) => {
    try {
      const response = await axios.get(`/api/notes/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting note:', error);
      throw error;
    }
  },

  /**
   * Create a new note
   * @param {Object} noteData - Note data
   * @returns {Promise<Object>} Created note
   */
  createNote: async (noteData) => {
    try {
      const response = await axios.post('/api/notes', noteData);
      return response.data;
    } catch (error) {
      console.error('Error creating note:', error);
      throw error;
    }
  },

  /**
   * Update a note
   * @param {string} id - Note ID
   * @param {Object} noteData - Note data
   * @returns {Promise<Object>} Updated note
   */
  updateNote: async (id, noteData) => {
    try {
      const response = await axios.put(`/api/notes/${id}`, noteData);
      return response.data;
    } catch (error) {
      console.error('Error updating note:', error);
      throw error;
    }
  },

  /**
   * Delete a note
   * @param {string} id - Note ID
   * @returns {Promise<Object>} Response message
   */
  deleteNote: async (id) => {
    try {
      const response = await axios.delete(`/api/notes/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting note:', error);
      throw error;
    }
  },

  /**
   * Toggle note completion status (for to-dos)
   * @param {string} id - Note ID
   * @returns {Promise<Object>} Updated note
   */
  toggleNoteCompletion: async (id) => {
    try {
      const response = await axios.put(`/api/notes/${id}/toggle-completion`);
      return response.data;
    } catch (error) {
      console.error('Error toggling note completion:', error);
      throw error;
    }
  },

  /**
   * Toggle note pinned status
   * @param {string} id - Note ID
   * @returns {Promise<Object>} Updated note
   */
  toggleNotePinned: async (id) => {
    try {
      const response = await axios.put(`/api/notes/${id}/toggle-pinned`);
      return response.data;
    } catch (error) {
      console.error('Error toggling note pinned status:', error);
      throw error;
    }
  }
};

export default notesService;