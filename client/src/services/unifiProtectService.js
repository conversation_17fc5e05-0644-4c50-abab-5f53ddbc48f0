import axios from 'axios';

/**
 * UniFi Protect API Service
 * Handles client-side API calls to the UniFi Protect endpoints
 */
const unifiProtectService = {
  /**
   * Get all cameras
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of cameras
   */
  getCameras: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-protect/cameras', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect cameras:', error);
      throw error;
    }
  },

  /**
   * Get camera details
   * @param {string} cameraId Camera ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Camera details
   */
  getCamera: async (cameraId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-protect/cameras/${cameraId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera ${cameraId}:`, error);
      throw error;
    }
  },

  /**
   * Get camera snapshot
   * @param {string} cameraId Camera ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Camera snapshot URL
   */
  getCameraSnapshot: async (cameraId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-protect/cameras/${cameraId}/snapshot`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera ${cameraId} snapshot:`, error);
      throw error;
    }
  },

  /**
   * Get system status / bootstrap data
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} System status and bootstrap data
   */
  getSystemStatus: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-protect/system', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect system status:', error);
      throw error;
    }
  },

  /**
   * Get all events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of events
   */
  getEvents: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-protect/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect events:', error);
      throw error;
    }
  },



  /**
   * Control PTZ camera
   * @param {string} cameraId Camera ID
   * @param {string} action PTZ action (goto, patrol_start, patrol_stop, move, zoom)
   * @param {Object} params Action parameters (slot, data, etc.)
   * @returns {Promise<Object>} Response message
   */
  controlPTZ: async (cameraId, action, params = {}) => {
    try {
      const response = await axios.post(`/api/unifi-protect/cameras/${cameraId}/ptz`, {
        action,
        ...params
      });
      return response.data;
    } catch (error) {
      console.error(`Error controlling UniFi Protect PTZ camera ${cameraId}:`, error);
      throw error;
    }
  },

  /**
   * Get all viewers
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of viewers
   */
  getViewers: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-protect/viewers', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Protect viewers:', error);
      throw error;
    }
  },

  /**
   * Update viewer settings
   * @param {string} viewerId Viewer ID
   * @param {Object} settings Viewer settings to update
   * @returns {Promise<Object>} Updated viewer data
   */
  updateViewer: async (viewerId, settings) => {
    try {
      const response = await axios.patch(`/api/unifi-protect/viewers/${viewerId}`, settings);
      return response.data;
    } catch (error) {
      console.error(`Error updating UniFi Protect viewer ${viewerId}:`, error);
      throw error;
    }
  },

  /**
   * Get UniFi Protect configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/unifi-protect/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching UniFi Protect configuration:', error);
      throw error;
    }
  }
};

export default unifiProtectService;
