import axios from 'axios';

/**
 * Lenel S2 NetBox API Service
 * Handles client-side API calls to the Lenel S2 NetBox endpoints
 */
const lenelS2NetBoxService = {
  /**
   * One-click setup for Lenel S2 NetBox
   * @returns {Promise<Object>} Setup result
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/lenel-s2-netbox/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up Lenel S2 NetBox with one click:', error);
      throw error;
    }
  },
  /**
   * Get all portals
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of portals
   */
  getPortals: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/portals', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching portals:', error);
      throw error;
    }
  },

  /**
   * Get portal details
   * @param {string} portalId Portal ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Portal details
   */
  getPortal: async (portalId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/portals/${portalId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching portal ${portalId}:`, error);
      throw error;
    }
  },

  /**
   * Get all cardholders
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of cardholders
   */
  getAccessCards: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/cardholders', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching cardholders:', error);
      throw error;
    }
  },

  /**
   * Get cardholder details
   * @param {string} cardholderId Cardholder ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Cardholder details
   */
  getAccessCard: async (cardholderId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/cardholders/${cardholderId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching cardholder ${cardholderId}:`, error);
      throw error;
    }
  },

  /**
   * Get all events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of events
   */
  getAccessEvents: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching events:', error);
      throw error;
    }
  },

  /**
   * Control portal
   * @param {string} portalId Portal ID
   * @param {Object} command Command object with action and parameters
   * @returns {Promise<Object>} Response message
   */
  controlPortal: async (portalId, command) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/portals/${portalId}/control`, command);
      return response.data;
    } catch (error) {
      console.error(`Error controlling portal ${portalId}:`, error);
      throw error;
    }
  },

  /**
   * Save Lenel S2 NetBox configuration
   * @param {Object} config Configuration object with credentials
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/lenel-s2-netbox/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Lenel S2 NetBox configuration:', error);
      throw error;
    }
  },

  /**
   * Get Lenel S2 NetBox configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching Lenel S2 NetBox configuration:', error);
      throw error;
    }
  },

  /**
   * Get all access levels
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access levels
   */
  getAccessLevels: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/access-levels', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching access levels:', error);
      throw error;
    }
  },

  /**
   * Get access level details
   * @param {string} accessLevelId Access level ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Access level details
   */
  getAccessLevelDetails: async (accessLevelId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/access-levels/${accessLevelId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching access level ${accessLevelId}:`, error);
      throw error;
    }
  },

  /**
   * Get all access groups
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access groups
   */
  getAccessGroups: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/access-groups', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching access groups:', error);
      throw error;
    }
  },

  /**
   * Get access group details
   * @param {string} accessGroupId Access group ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Access group details
   */
  getAccessGroupDetails: async (accessGroupId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/access-groups/${accessGroupId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching access group ${accessGroupId}:`, error);
      throw error;
    }
  },

  /**
   * Get all badges
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of badges
   */
  getBadges: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/badges', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching badges:', error);
      throw error;
    }
  },

  /**
   * Get badge details
   * @param {string} badgeId Badge ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Badge details
   */
  getBadgeDetails: async (badgeId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/badges/${badgeId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching badge ${badgeId}:`, error);
      throw error;
    }
  },

  /**
   * Get all door schedules
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of door schedules
   */
  getDoorSchedules: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/door-schedules', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching door schedules:', error);
      throw error;
    }
  },

  /**
   * Get door schedule details
   * @param {string} scheduleId Schedule ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Door schedule details
   */
  getDoorScheduleDetails: async (scheduleId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/door-schedules/${scheduleId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching door schedule ${scheduleId}:`, error);
      throw error;
    }
  },

  /**
   * Get all doors
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of doors
   */
  getDoors: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/doors', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching doors:', error);
      throw error;
    }
  },

  /**
   * Get door status
   * @param {string} doorId Door ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Door status
   */
  getDoorStatus: async (doorId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/doors/${doorId}/status`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching door status ${doorId}:`, error);
      throw error;
    }
  },

  /**
   * Assign access level to cardholder
   * @param {string} cardholderId Cardholder ID
   * @param {string} accessLevelId Access level ID
   * @returns {Promise<Object>} Response data
   */
  assignAccessLevelToCardholder: async (cardholderId, accessLevelId) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/cardholders/${cardholderId}/access-levels/${accessLevelId}`);
      return response.data;
    } catch (error) {
      console.error(`Error assigning access level ${accessLevelId} to cardholder ${cardholderId}:`, error);
      throw error;
    }
  },

  /**
   * Remove access level from cardholder
   * @param {string} cardholderId Cardholder ID
   * @param {string} accessLevelId Access level ID
   * @returns {Promise<Object>} Response data
   */
  removeAccessLevelFromCardholder: async (cardholderId, accessLevelId) => {
    try {
      const response = await axios.delete(`/api/lenel-s2-netbox/cardholders/${cardholderId}/access-levels/${accessLevelId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing access level ${accessLevelId} from cardholder ${cardholderId}:`, error);
      throw error;
    }
  },
  
  /**
   * Lock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Response data
   */
  lockDoor: async (doorId) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/doors/${doorId}/lock`);
      return response.data;
    } catch (error) {
      console.error(`Error locking door ${doorId}:`, error);
      throw error;
    }
  },
  
  /**
   * Unlock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Response data
   */
  unlockDoor: async (doorId) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/doors/${doorId}/unlock`);
      return response.data;
    } catch (error) {
      console.error(`Error unlocking door ${doorId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new user
   * @param {Object} userData User data
   * @returns {Promise<Object>} Created user data
   */
  createUser: async (userData) => {
    try {
      const response = await axios.post('/api/lenel-s2-netbox/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  /**
   * Update an existing user
   * @param {string} userId User ID
   * @param {Object} userData Updated user data
   * @returns {Promise<Object>} Updated user data
   */
  updateUser: async (userId, userData) => {
    try {
      const response = await axios.put(`/api/lenel-s2-netbox/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Delete a user
   * @param {string} userId User ID
   * @returns {Promise<Object>} Response data
   */
  deleteUser: async (userId) => {
    try {
      const response = await axios.delete(`/api/lenel-s2-netbox/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get all users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  /**
   * Get user details
   * @param {string} userId User ID
   * @returns {Promise<Object>} User details
   */
  getUser: async (userId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new user credential
   * @param {string} userId User ID
   * @param {Object} credentialData Credential data
   * @returns {Promise<Object>} Created credential data
   */
  createUserCredential: async (userId, credentialData) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/users/${userId}/credentials`, credentialData);
      return response.data;
    } catch (error) {
      console.error(`Error creating credential for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Update a user credential
   * @param {string} userId User ID
   * @param {string} credentialId Credential ID
   * @param {Object} credentialData Updated credential data
   * @returns {Promise<Object>} Updated credential data
   */
  updateUserCredential: async (userId, credentialId, credentialData) => {
    try {
      const response = await axios.put(`/api/lenel-s2-netbox/users/${userId}/credentials/${credentialId}`, credentialData);
      return response.data;
    } catch (error) {
      console.error(`Error updating credential ${credentialId} for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Delete a user credential
   * @param {string} userId User ID
   * @param {string} credentialId Credential ID
   * @returns {Promise<Object>} Response data
   */
  deleteUserCredential: async (userId, credentialId) => {
    try {
      const response = await axios.delete(`/api/lenel-s2-netbox/users/${userId}/credentials/${credentialId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting credential ${credentialId} for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get user credentials
   * @param {string} userId User ID
   * @returns {Promise<Array>} List of user credentials
   */
  getUserCredentials: async (userId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/users/${userId}/credentials`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching credentials for user ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get user activity logs
   * @param {string} userId User ID
   * @param {Object} params Query parameters (e.g., date range)
   * @returns {Promise<Array>} List of user activity logs
   */
  getUserLogs: async (userId, params = {}) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/users/${userId}/logs`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching logs for user ${userId}:`, error);
      throw error;
    }
  },

  // ===================================================================
  // LIVE ACTIVITY LOG AND CARD MANAGEMENT METHODS
  // ===================================================================

  /**
   * Get live activity log
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of recent activity
   */
  getLiveActivityLog: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/activity-log/live', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching live activity log:', error);
      throw error;
    }
  },

  /**
   * Mark a card as lost
   * @param {string} credentialId Credential ID
   * @param {string} reason Reason for marking as lost
   * @returns {Promise<Object>} Response message
   */
  markCardAsLost: async (credentialId, reason = 'Lost') => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/credentials/${credentialId}/mark-lost`, { reason });
      return response.data;
    } catch (error) {
      console.error(`Error marking card ${credentialId} as lost:`, error);
      throw error;
    }
  },

  /**
   * Restore a lost card
   * @param {string} credentialId Credential ID
   * @returns {Promise<Object>} Response message
   */
  restoreCard: async (credentialId) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/credentials/${credentialId}/restore`);
      return response.data;
    } catch (error) {
      console.error(`Error restoring card ${credentialId}:`, error);
      throw error;
    }
  },

  // ===================================================================
  // EVACUATION MANAGEMENT METHODS
  // ===================================================================

  /**
   * Initiate an evacuation
   * @param {Object} evacuationData Evacuation details
   * @returns {Promise<Object>} Response message
   */
  initiateEvacuation: async (evacuationData) => {
    try {
      const response = await axios.post('/api/lenel-s2-netbox/evacuations', evacuationData);
      return response.data;
    } catch (error) {
      console.error('Error initiating evacuation:', error);
      throw error;
    }
  },

  /**
   * End an evacuation
   * @param {string} evacuationId Evacuation ID
   * @returns {Promise<Object>} Response message
   */
  endEvacuation: async (evacuationId) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/evacuations/${evacuationId}/end`);
      return response.data;
    } catch (error) {
      console.error(`Error ending evacuation ${evacuationId}:`, error);
      throw error;
    }
  },

  /**
   * Get evacuation status
   * @param {string} evacuationId Evacuation ID (optional)
   * @returns {Promise<Object>} Evacuation status
   */
  getEvacuationStatus: async (evacuationId = null) => {
    try {
      const url = evacuationId
        ? `/api/lenel-s2-netbox/evacuations/${evacuationId}/status`
        : '/api/lenel-s2-netbox/evacuations/status';
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching evacuation status:', error);
      throw error;
    }
  },

  /**
   * Get occupancy report
   * @returns {Promise<Object>} Occupancy report
   */
  getOccupancyReport: async () => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/occupancy/report');
      return response.data;
    } catch (error) {
      console.error('Error fetching occupancy report:', error);
      throw error;
    }
  },

  // ===================================================================
  // READER MANAGEMENT METHODS
  // ===================================================================

  /**
   * Get all readers
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of readers
   */
  getReaders: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/readers', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching readers:', error);
      throw error;
    }
  },

  /**
   * Get reader details
   * @param {string} readerId Reader ID
   * @returns {Promise<Object>} Reader details
   */
  getReaderDetails: async (readerId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/readers/${readerId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching reader details ${readerId}:`, error);
      throw error;
    }
  },

  /**
   * Get all reader groups
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of reader groups
   */
  getReaderGroups: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/reader-groups', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching reader groups:', error);
      throw error;
    }
  },

  /**
   * Get reader group details
   * @param {string} readerGroupId Reader group ID
   * @returns {Promise<Object>} Reader group details
   */
  getReaderGroupDetails: async (readerGroupId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/reader-groups/${readerGroupId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching reader group details ${readerGroupId}:`, error);
      throw error;
    }
  },

  // ===================================================================
  // PORTAL GROUP MANAGEMENT METHODS
  // ===================================================================

  /**
   * Get all portal groups
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of portal groups
   */
  getPortalGroups: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/portal-groups', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching portal groups:', error);
      throw error;
    }
  },

  /**
   * Get portal group details
   * @param {string} portalGroupId Portal group ID
   * @returns {Promise<Object>} Portal group details
   */
  getPortalGroupDetails: async (portalGroupId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/portal-groups/${portalGroupId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching portal group details ${portalGroupId}:`, error);
      throw error;
    }
  },

  // ===================================================================
  // ELEVATOR CONTROL METHODS
  // ===================================================================

  /**
   * Get all elevators
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of elevators
   */
  getElevators: async (params = {}) => {
    try {
      const response = await axios.get('/api/lenel-s2-netbox/elevators', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching elevators:', error);
      throw error;
    }
  },

  /**
   * Get elevator details
   * @param {string} elevatorId Elevator ID
   * @returns {Promise<Object>} Elevator details
   */
  getElevatorDetails: async (elevatorId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/elevators/${elevatorId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching elevator details ${elevatorId}:`, error);
      throw error;
    }
  },

  /**
   * Control elevator
   * @param {string} elevatorId Elevator ID
   * @param {Object} controlData Control parameters
   * @returns {Promise<Object>} Response message
   */
  controlElevator: async (elevatorId, controlData) => {
    try {
      const response = await axios.post(`/api/lenel-s2-netbox/elevators/${elevatorId}/control`, controlData);
      return response.data;
    } catch (error) {
      console.error(`Error controlling elevator ${elevatorId}:`, error);
      throw error;
    }
  },

  /**
   * Get elevator status
   * @param {string} elevatorId Elevator ID
   * @returns {Promise<Object>} Elevator status
   */
  getElevatorStatus: async (elevatorId) => {
    try {
      const response = await axios.get(`/api/lenel-s2-netbox/elevators/${elevatorId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching elevator status ${elevatorId}:`, error);
      throw error;
    }
  }
};

export default lenelS2NetBoxService;
