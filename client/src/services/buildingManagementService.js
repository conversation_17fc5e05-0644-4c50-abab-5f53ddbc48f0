import axios from 'axios';

/**
 * Service for interacting with the Building Management System API
 */
class BuildingManagementService {
  /**
   * Get the status of all building management integrations
   * @returns {Promise<Object>} Integration statuses
   * @deprecated Use getDetailedIntegrationStatus instead
   */
  async getIntegrationsStatus() {
    try {
      // Prefer the detailed building-management endpoint
      const detailed = await this.getDetailedIntegrationStatus(false);
      const integrations = detailed && detailed.integrations ? detailed.integrations : (detailed || {});

      // Normalize keys expected by the UI (map skyportcloud -> dreo for climate)
      const normalized = { ...integrations };
      if (normalized.skyportcloud && !normalized.dreo) {
        normalized.dreo = normalized.skyportcloud;
      }
      return normalized;
    } catch (error) {
      console.error('Error fetching detailed integrations status, falling back to legacy endpoint:', error);
      // Fallback to legacy integration tracker endpoint and try to infer shape
      try {
        const response = await axios.get('/api/integration-status');
        const data = response.data || {};
        const normalized = {};
        const mapKey = (srcKey, destKey = srcKey) => {
          const entry = data[srcKey] || data[destKey];
          if (entry) {
            const isActive = typeof entry.status === 'string' ? entry.status.toLowerCase() === 'active' : false;
            normalized[destKey] = { active: isActive, ...entry };
          }
        };
        mapKey('lenelS2NetBox');
        mapKey('unifiAccess');
        mapKey('unifiProtect');
        mapKey('unifiNetwork');
        mapKey('skyportcloud');
        // Map climate to dreo alias if present
        if (normalized.skyportcloud && !normalized.dreo) normalized.dreo = normalized.skyportcloud;
        mapKey('googleCalendar');
        return normalized;
      } catch (legacyErr) {
        console.error('Legacy integration status fallback also failed:', legacyErr);
        throw legacyErr;
      }
    }
  }
  
  /**
   * Get detailed status of all building management integrations
   * @param {boolean} forceReinitialization - Whether to force reinitialization of all integrations
   * @returns {Promise<Object>} Detailed integration statuses
   */
  async getDetailedIntegrationStatus(forceReinitialization = false) {
    try {
      const url = forceReinitialization 
        ? '/api/building-management/integrations/status?force=true'
        : '/api/building-management/integrations/status';
      
      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching detailed building management integrations status:', error);
      throw error;
    }
  }
  
  /**
   * Force reinitialization of all building management integrations
   * @returns {Promise<Object>} Updated integration statuses
   */
  async forceReinitializeIntegrations() {
    try {
      return await this.getDetailedIntegrationStatus(true);
    } catch (error) {
      console.error('Error reinitializing building management integrations:', error);
      throw error;
    }
  }
  
  /**
   * Get troubleshooting information for an integration
   * @param {string} integration - Integration name
   * @param {Object} status - Integration status
   * @returns {Object} Troubleshooting information
   */
  getTroubleshootingInfo(integration, status) {
    if (!status) {
      return {
        steps: ['Integration status not available'],
        documentation: 'Please fetch integration status first',
        resetInstructions: 'Use forceReinitializeIntegrations() to reset all integrations'
      };
    }
    
    return status.troubleshooting || {
      steps: [
        'Check that the integration is enabled in settings',
        'Verify that all required configuration fields are provided',
        'Ensure that the credentials are correct',
        'Check that the host is reachable from the server'
      ],
      documentation: `For more information, see the ${integration} documentation.`,
      resetInstructions: 'Use forceReinitializeIntegrations() to reset all integrations'
    };
  }
  
  /**
   * Get configuration guidance for an integration
   * @param {string} integration - Integration name
   * @param {Object} status - Integration status
   * @returns {string} Configuration guidance
   */
  getConfigurationGuidance(integration, status) {
    if (!status) {
      return 'Integration status not available. Please fetch integration status first.';
    }
    
    if (status.status === 'active') {
      return 'Integration is properly configured and active.';
    }
    
    if (status.status === 'disabled') {
      return 'Integration is disabled. Enable it in settings to use it.';
    }
    
    if (status.status === 'error' && status.errorType === 'configuration') {
      return `Configuration error: ${status.errorMessage || 'Missing required configuration'}. Required: ${status.configRequirements || 'Unknown'}`;
    }
    
    if (status.status === 'error' && status.errorType === 'authentication') {
      return `Authentication error: ${status.errorMessage || 'Invalid credentials'}. Please check your username, password, or API key.`;
    }
    
    if (status.status === 'error' && status.errorType === 'connection') {
      return `Connection error: ${status.errorMessage || 'Unable to connect to the service'}. Please check that the host is reachable and that any required firewall rules are in place.`;
    }
    
    return status.configRequirements || 'Please check the integration documentation for configuration requirements.';
  }

  /**
   * Get all buildings
   * @returns {Promise<Array>} List of buildings
   */
  async getBuildings() {
    try {
      const response = await axios.get('/api/buildings');
      return response.data;
    } catch (error) {
      console.error('Error fetching buildings:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Buildings API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get a building by ID
   * @param {string} id Building ID
   * @returns {Promise<Object>} Building details
   */
  async getBuilding(id) {
    try {
      const response = await axios.get(`/api/buildings/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching building ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new building
   * @param {Object} buildingData Building data
   * @returns {Promise<Object>} Created building
   */
  async createBuilding(buildingData) {
    try {
      const response = await axios.post('/api/buildings', buildingData);
      return response.data;
    } catch (error) {
      console.error('Error creating building:', error);
      throw error;
    }
  }

  /**
   * Update a building
   * @param {string} id Building ID
   * @param {Object} buildingData Building data
   * @returns {Promise<Object>} Updated building
   */
  async updateBuilding(id, buildingData) {
    try {
      const response = await axios.put(`/api/buildings/${id}`, buildingData);
      return response.data;
    } catch (error) {
      console.error(`Error updating building ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a building
   * @param {string} id Building ID
   * @returns {Promise<Object>} Response message
   */
  async deleteBuilding(id) {
    try {
      const response = await axios.delete(`/api/buildings/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting building ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all floors for a building
   * @param {string} buildingId Building ID
   * @returns {Promise<Array>} List of floors
   */
  async getBuildingFloors(buildingId) {
    try {
      const response = await axios.get(`/api/buildings/${buildingId}/floors`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching floors for building ${buildingId}:`, error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Floors API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get all floors
   * @returns {Promise<Array>} List of floors
   */
  async getFloors() {
    try {
      const response = await axios.get('/api/floors');
      return response.data;
    } catch (error) {
      console.error('Error fetching floors:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Floors API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Get a floor by ID
   * @param {string} id Floor ID
   * @returns {Promise<Object>} Floor details
   */
  async getFloor(id) {
    try {
      const response = await axios.get(`/api/floors/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching floor ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new floor
   * @param {Object} floorData Floor data
   * @param {File} floorplanFile Floorplan image file
   * @returns {Promise<Object>} Created floor
   */
  async createFloor(floorData, floorplanFile) {
    try {
      const formData = new FormData();

      // Add floor data to form
      Object.keys(floorData).forEach(key => {
        if (key === 'dimensions' || key === 'metadata') {
          formData.append(key, JSON.stringify(floorData[key]));
        } else {
          formData.append(key, floorData[key]);
        }
      });

      // Add floorplan file if provided
      if (floorplanFile) {
        formData.append('floorplan', floorplanFile);
      }

      const response = await axios.post('/api/floors', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error creating floor:', error);
      throw error;
    }
  }

  /**
   * Update a floor
   * @param {string} id Floor ID
   * @param {Object} floorData Floor data
   * @param {File} floorplanFile Floorplan image file
   * @returns {Promise<Object>} Updated floor
   */
  async updateFloor(id, floorData, floorplanFile) {
    try {
      const formData = new FormData();

      // Add floor data to form
      Object.keys(floorData).forEach(key => {
        if (key === 'dimensions' || key === 'metadata') {
          formData.append(key, JSON.stringify(floorData[key]));
        } else {
          formData.append(key, floorData[key]);
        }
      });

      // Add floorplan file if provided
      if (floorplanFile) {
        formData.append('floorplan', floorplanFile);
      }

      const response = await axios.put(`/api/floors/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data;
    } catch (error) {
      console.error(`Error updating floor ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a floor
   * @param {string} id Floor ID
   * @returns {Promise<Object>} Response message
   */
  async deleteFloor(id) {
    try {
      const response = await axios.delete(`/api/floors/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting floor ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get floorplan image URL for a floor
   * @param {string} id Floor ID
   * @returns {string} Floorplan image URL
   */
  getFloorplanUrl(id) {
    return `/api/floors/${id}/floorplan`;
  }

  /**
   * Get all icons for a floor
   * @param {string} floorId Floor ID
   * @returns {Promise<Array>} List of icons
   */
  async getFloorIcons(floorId) {
    try {
      const response = await axios.get(`/api/floors/${floorId}/icons`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching icons for floor ${floorId}:`, error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Icons API not found, returning mock data');
        return [];
      }

      throw error;
    }
  }

  /**
   * Create a new floorplan icon
   * @param {Object} iconData Icon data
   * @returns {Promise<Object>} Created icon
   */
  async createFloorplanIcon(iconData) {
    try {
      // Convert objects to JSON strings
      const formattedData = { ...iconData };
      if (formattedData.position) formattedData.position = JSON.stringify(formattedData.position);
      if (formattedData.size) formattedData.size = JSON.stringify(formattedData.size);
      if (formattedData.data) formattedData.data = JSON.stringify(formattedData.data);
      if (formattedData.metadata) formattedData.metadata = JSON.stringify(formattedData.metadata);

      const response = await axios.post('/api/floorplan-icons', formattedData);
      return response.data;
    } catch (error) {
      console.error('Error creating floorplan icon:', error);
      throw error;
    }
  }

  /**
   * Update a floorplan icon
   * @param {string} id Icon ID
   * @param {Object} iconData Icon data
   * @returns {Promise<Object>} Updated icon
   */
  async updateFloorplanIcon(id, iconData) {
    try {
      // Convert objects to JSON strings
      const formattedData = { ...iconData };
      if (formattedData.position) formattedData.position = JSON.stringify(formattedData.position);
      if (formattedData.size) formattedData.size = JSON.stringify(formattedData.size);
      if (formattedData.data) formattedData.data = JSON.stringify(formattedData.data);
      if (formattedData.metadata) formattedData.metadata = JSON.stringify(formattedData.metadata);

      const response = await axios.put(`/api/floorplan-icons/${id}`, formattedData);
      return response.data;
    } catch (error) {
      console.error(`Error updating floorplan icon ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a floorplan icon
   * @param {string} id Icon ID
   * @returns {Promise<Object>} Response message
   */
  async deleteFloorplanIcon(id) {
    try {
      const response = await axios.delete(`/api/floorplan-icons/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting floorplan icon ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update floorplan icon data
   * @param {string} id Icon ID
   * @param {Object} data Icon data
   * @returns {Promise<Object>} Updated icon
   */
  async updateFloorplanIconData(id, data) {
    try {
      const response = await axios.patch(`/api/floorplan-icons/${id}/data`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating floorplan icon data ${id}:`, error);
      throw error;
    }
  }

  /**
   * Bulk create floorplan icons
   * @param {Array} icons Array of icon data
   * @returns {Promise<Array>} Created icons
   */
  async bulkCreateFloorplanIcons(icons) {
    try {
      const response = await axios.post('/api/floorplan-icons/bulk', { icons });
      return response.data;
    } catch (error) {
      console.error('Error bulk creating floorplan icons:', error);
      throw error;
    }
  }

  /**
   * Bulk update floorplan icon positions
   * @param {Array} icons Array of icons with positions
   * @returns {Promise<Array>} Updated icons
   */
  async bulkUpdateFloorplanIconPositions(icons) {
    try {
      // Ensure each icon has valid position with x and y coordinates
      const validatedIcons = icons.map(icon => {
        // Make a copy of the icon to avoid modifying the original
        const validatedIcon = { ...icon };

        // Ensure position exists and has x and y properties
        if (!validatedIcon.position) {
          validatedIcon.position = { x: 0, y: 0 };
        } else {
          // Ensure x and y are defined
          validatedIcon.position = {
            ...validatedIcon.position,
            x: validatedIcon.position.x !== undefined ? validatedIcon.position.x : 0,
            y: validatedIcon.position.y !== undefined ? validatedIcon.position.y : 0
          };
        }

        return validatedIcon;
      });

      const response = await axios.put('/api/floorplan-icons/bulk/positions', { icons: validatedIcons });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating floorplan icon positions:', error);
      throw error;
    }
  }

  /**
   * Get the status of all building systems
   * @returns {Promise<Object>} System statuses
   */
  async getSystemStatus() {
    try {
      const response = await axios.get('/api/building-management/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching building management system status:', error);
      throw error;
    }
  }

  /**
   * Get a list of all doors from access control systems
   * @returns {Promise<Array>} List of doors
   */
  async getDoors() {
    try {
      const response = await axios.get('/api/building-management/doors');
      return response.data;
    } catch (error) {
      console.error('Error fetching doors:', error);
      throw error;
    }
  }
  
  /**
   * Control a door (lock, unlock, passage mode)
   * @param {string} doorId - ID of the door to control
   * @param {string} action - Action to perform ('lock', 'unlock', 'passage')
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async controlDoor(doorId, action, source) {
    try {
      const response = await axios.post(`/api/building-management/doors/${doorId}/control`, {
        action,
        source
      });
      return response.data;
    } catch (error) {
      console.error(`Error controlling door ${doorId} (${action}):`, error);
      throw error;
    }
  }
  
  /**
   * Unlock a door
   * @param {string} doorId - ID of the door to unlock
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async unlockDoor(doorId, source) {
    return this.controlDoor(doorId, 'unlock', source);
  }
  
  /**
   * Lock a door
   * @param {string} doorId - ID of the door to lock
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async lockDoor(doorId, source) {
    return this.controlDoor(doorId, 'lock', source);
  }
  
  /**
   * Set a door to passage mode
   * @param {string} doorId - ID of the door to set to passage mode
   * @param {string} source - Source system ('lenelS2NetBox', 'unifiAccess')
   * @returns {Promise<Object>} Response data
   */
  async setDoorPassageMode(doorId, source) {
    return this.controlDoor(doorId, 'passage', source);
  }

  /**
   * Get a list of all climate control devices
   * @returns {Promise<Array>} List of climate devices
   */
  async getClimateDevices() {
    try {
      const response = await axios.get('/api/building-management/climate-devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching climate devices:', error);
      throw error;
    }
  }

  /**
   * Get a list of all security cameras
   * @returns {Promise<Array>} List of cameras
   */
  async getCameras() {
    try {
      const response = await axios.get('/api/building-management/cameras');
      return response.data;
    } catch (error) {
      console.error('Error fetching cameras:', error);
      throw error;
    }
  }

  /**
   * Get a list of all network devices
   * @returns {Promise<Array>} List of network devices
   */
  async getNetworkDevices() {
    try {
      const response = await axios.get('/api/building-management/network-devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching network devices:', error);
      throw error;
    }
  }

  /**
   * Save building management settings
   * @param {Object} settings Settings to save
   * @returns {Promise<Object>} Saved settings
   */
  async saveSettings(settings) {
    try {
      const response = await axios.post('/api/building-management/settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error saving building management settings:', error);
      throw error;
    }
  }

  /**
   * Get building management settings
   * @returns {Promise<Object>} Settings
   */
  async getSettings() {
    try {
      const response = await axios.get('/api/building-management/settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching building management settings:', error);
      throw error;
    }
  }

  /**
   * Get automation rules
   * @returns {Promise<Array>} List of automation rules
   */
  async getAutomationRules() {
    try {
      const response = await axios.get('/api/building-management/automation/rules');
      return response.data;
    } catch (error) {
      console.error('Error fetching automation rules:', error);
      throw error;
    }
  }

  /**
   * Save an automation rule
   * @param {Object} rule Rule to save
   * @returns {Promise<Object>} Saved rule
   */
  async saveAutomationRule(rule) {
    try {
      const response = await axios.post('/api/building-management/automation/rules', rule);
      return response.data;
    } catch (error) {
      console.error('Error saving automation rule:', error);
      throw error;
    }
  }

  /**
   * Update an automation rule
   * @param {string} ruleId ID of the rule to update
   * @param {Object} rule Updated rule data
   * @returns {Promise<Object>} Updated rule
   */
  async updateAutomationRule(ruleId, rule) {
    try {
      const response = await axios.put(`/api/building-management/automation/rules/${ruleId}`, rule);
      return response.data;
    } catch (error) {
      console.error(`Error updating automation rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Delete an automation rule
   * @param {string} ruleId ID of the rule to delete
   * @returns {Promise<Object>} Response data
   */
  async deleteAutomationRule(ruleId) {
    try {
      const response = await axios.delete(`/api/building-management/automation/rules/${ruleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting automation rule ${ruleId}:`, error);
      throw error;
    }
  }

  /**
   * Get historical data for building systems
   * @param {Object} params Query parameters (timeRange, systems, etc.)
   * @returns {Promise<Object>} Historical data
   */
  async getHistoricalData(params = {}) {
    try {
      const response = await axios.get('/api/building-management/historical-data', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching historical data:', error);
      throw error;
    }
  }


  /**
   * Get all contacts
   * @param {Object} params Query parameters (category, search)
   * @returns {Promise<Array>} List of contacts
   */
  async getContacts(params = {}) {
    try {
      const response = await axios.get('/api/contacts', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching contacts:', error);

      // If the API endpoint doesn't exist yet, return mock data for development
      if (error.response && error.response.status === 404) {
        console.warn('Contacts API not found, returning mock data');
        return this.getMockContacts();
      }

      throw error;
    }
  }

  /**
   * Get a contact by ID
   * @param {string} id Contact ID
   * @returns {Promise<Object>} Contact details
   */
  async getContact(id) {
    try {
      const response = await axios.get(`/api/contacts/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching contact ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new contact
   * @param {Object} contactData Contact data
   * @returns {Promise<Object>} Created contact
   */
  async createContact(contactData) {
    try {
      const response = await axios.post('/api/contacts', contactData);
      return response.data;
    } catch (error) {
      console.error('Error creating contact:', error);
      throw error;
    }
  }

  /**
   * Update a contact
   * @param {string} id Contact ID
   * @param {Object} contactData Contact data
   * @returns {Promise<Object>} Updated contact
   */
  async updateContact(id, contactData) {
    try {
      const response = await axios.put(`/api/contacts/${id}`, contactData);
      return response.data;
    } catch (error) {
      console.error(`Error updating contact ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a contact
   * @param {string} id Contact ID
   * @returns {Promise<Object>} Response message
   */
  async deleteContact(id) {
    try {
      const response = await axios.delete(`/api/contacts/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting contact ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get mock contacts for development
   * @returns {Array} Mock contacts
   */
  getMockContacts() {
    return [
      {
        _id: '1',
        name: 'HVAC Service',
        category: 'maintenance',
        phone: '************',
        email: '<EMAIL>',
        company: 'ABC HVAC Services',
        notes: 'Available 24/7 for emergency service',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: '2',
        name: 'Security Systems',
        category: 'support',
        phone: '************',
        email: '<EMAIL>',
        company: 'SecureTech Solutions',
        notes: 'Contact for alarm system issues',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: '3',
        name: 'Emergency Plumbing',
        category: 'emergency',
        phone: '************',
        email: '<EMAIL>',
        company: 'Plumbing Professionals',
        notes: 'For water leaks and plumbing emergencies',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: '4',
        name: 'Cleaning Services',
        category: 'business',
        phone: '************',
        email: '<EMAIL>',
        company: 'CleanCrew Inc.',
        notes: 'Regular cleaning service provider',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        _id: '5',
        name: 'IT Support',
        category: 'support',
        phone: '************',
        email: '<EMAIL>',
        company: 'IT Services Group',
        notes: 'Network and computer support',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];
  }
}

export default new BuildingManagementService();
