import axios from 'axios';

const API_BASE = '/api/av-checklists';

class AVChecklistService {
  
  // Get all AV checklists with filtering and pagination
  async getChecklists(params = {}) {
    try {
      const response = await axios.get(API_BASE, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching AV checklists:', error);
      throw error;
    }
  }

  // Get specific checklist by ID
  async getChecklist(id) {
    try {
      const response = await axios.get(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching checklist ${id}:`, error);
      throw error;
    }
  }

  // Get template checklists
  async getTemplates() {
    try {
      const response = await axios.get(`${API_BASE}/templates`);
      return response.data;
    } catch (error) {
      console.error('Error fetching checklist templates:', error);
      throw error;
    }
  }

  // Get due checklists
  async getDueChecklists(includeAll = false) {
    try {
      const params = { includeAll };
      const response = await axios.get(`${API_BASE}/due`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching due checklists:', error);
      throw error;
    }
  }

  // Get available types
  async getTypes() {
    try {
      const response = await axios.get(`${API_BASE}/types`);
      return response.data;
    } catch (error) {
      console.error('Error fetching checklist types:', error);
      throw error;
    }
  }

  // Create new checklist
  async createChecklist(checklistData) {
    try {
      const response = await axios.post(API_BASE, checklistData);
      return response.data;
    } catch (error) {
      console.error('Error creating checklist:', error);
      throw error;
    }
  }

  // Create checklist from template
  async createFromTemplate(templateId, customizations = {}) {
    try {
      const response = await axios.post(`${API_BASE}/from-template/${templateId}`, customizations);
      return response.data;
    } catch (error) {
      console.error('Error creating checklist from template:', error);
      throw error;
    }
  }

  // Update checklist
  async updateChecklist(id, checklistData) {
    try {
      const response = await axios.put(`${API_BASE}/${id}`, checklistData);
      return response.data;
    } catch (error) {
      console.error(`Error updating checklist ${id}:`, error);
      throw error;
    }
  }

  // Delete checklist
  async deleteChecklist(id) {
    try {
      const response = await axios.delete(`${API_BASE}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting checklist ${id}:`, error);
      throw error;
    }
  }

  // Start checklist execution
  async startExecution(id, eventId = null) {
    try {
      const response = await axios.post(`${API_BASE}/${id}/start`, { eventId });
      return response.data;
    } catch (error) {
      console.error(`Error starting execution for checklist ${id}:`, error);
      throw error;
    }
  }

  // Complete checklist item
  async completeItem(checklistId, executionId, itemId, itemData) {
    try {
      const response = await axios.put(
        `${API_BASE}/${checklistId}/executions/${executionId}/items/${itemId}`,
        itemData
      );
      return response.data;
    } catch (error) {
      console.error(`Error completing item ${itemId}:`, error);
      throw error;
    }
  }

  // Complete entire execution
  async completeExecution(checklistId, executionId, completionData) {
    try {
      const response = await axios.put(
        `${API_BASE}/${checklistId}/executions/${executionId}/complete`,
        completionData
      );
      return response.data;
    } catch (error) {
      console.error(`Error completing execution ${executionId}:`, error);
      throw error;
    }
  }

  // Get checklist analytics
  async getAnalytics(id) {
    try {
      const response = await axios.get(`${API_BASE}/${id}/analytics`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching analytics for checklist ${id}:`, error);
      throw error;
    }
  }

  // Get overall statistics
  async getStatistics() {
    try {
      const response = await axios.get(`${API_BASE}/stats/overview`);
      return response.data;
    } catch (error) {
      console.error('Error fetching checklist statistics:', error);
      throw error;
    }
  }

  // Utility methods

  // Get checklists by type
  async getChecklistsByType(checklistType) {
    return this.getChecklists({ checklistType });
  }

  // Get checklists for service type
  async getChecklistsForService(serviceType) {
    return this.getChecklists({ serviceType });
  }

  // Get user's assigned checklists
  async getUserChecklists(userId) {
    return this.getChecklists({ assignedTo: userId });
  }

  // Search checklists
  async searchChecklists(query) {
    return this.getChecklists({ search: query });
  }

  // Validate checklist data
  validateChecklistData(data) {
    const errors = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Checklist name is required');
    }

    if (!data.checklistType) {
      errors.push('Checklist type is required');
    }

    if (!data.items || data.items.length === 0) {
      errors.push('At least one checklist item is required');
    }

    if (data.items) {
      data.items.forEach((item, index) => {
        if (!item.title) {
          errors.push(`Item ${index + 1} requires a title`);
        }
        if (!item.category) {
          errors.push(`Item ${index + 1} requires a category`);
        }
        if (item.order === undefined || item.order === null) {
          errors.push(`Item ${index + 1} requires an order number`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get default checklist structure
  getDefaultChecklistStructure() {
    return {
      name: '',
      description: '',
      checklistType: 'pre_service',
      serviceType: 'general',
      schedule: {
        frequency: 'event_based',
        autoSchedule: false
      },
      items: [],
      assignedTeams: [],
      requiredRoles: ['av_tech'],
      isTemplate: false,
      integrations: {
        calendar: {
          createEvents: false,
          reminderMinutes: [15, 60]
        },
        notifications: {
          emailNotifications: true
        },
        avEquipment: {
          autoCheckStatus: true,
          includeMaintenanceAlerts: true
        }
      }
    };
  }

  // Get default item structure
  getDefaultItemStructure() {
    return {
      title: '',
      description: '',
      category: 'general',
      priority: 'medium',
      estimatedTime: 5,
      order: 1,
      equipmentIds: [],
      equipmentNames: [],
      instructions: '',
      troubleshootingTips: [],
      warningNotes: [],
      verification: {
        required: false,
        type: 'visual',
        expectedResult: ''
      },
      dependencies: [],
      blockers: [],
      canBeAutomated: false,
      isActive: true,
      isOptional: false,
      skipConditions: []
    };
  }

  // Create sample checklist templates
  getSampleTemplates() {
    return [
      {
        name: 'Pre-Service Audio Check',
        description: 'Standard audio system verification before worship services',
        checklistType: 'pre_service',
        serviceType: 'sunday_service',
        items: [
          {
            ...this.getDefaultItemStructure(),
            title: 'Power on main audio console',
            category: 'audio',
            priority: 'critical',
            order: 1,
            instructions: 'Turn on main audio mixing console and verify all channel strips are functioning',
            estimatedTime: 2
          },
          {
            ...this.getDefaultItemStructure(),
            title: 'Test wireless microphones',
            category: 'microphones',
            priority: 'critical',
            order: 2,
            instructions: 'Check battery levels, perform sound check, verify clarity and range',
            estimatedTime: 10,
            verification: {
              required: true,
              type: 'audio_test',
              expectedResult: 'Clear audio with no dropouts or interference'
            }
          },
          {
            ...this.getDefaultItemStructure(),
            title: 'Verify monitor speakers',
            category: 'monitors',
            priority: 'high',
            order: 3,
            instructions: 'Test all monitor speakers for proper audio output and levels',
            estimatedTime: 5
          }
        ]
      },
      {
        name: 'Streaming Setup Checklist',
        description: 'Complete setup verification for live streaming services',
        checklistType: 'event_setup',
        serviceType: 'special_event',
        items: [
          {
            ...this.getDefaultItemStructure(),
            title: 'Start streaming software',
            category: 'streaming',
            priority: 'critical',
            order: 1,
            instructions: 'Launch OBS or similar streaming software, verify scene setup',
            estimatedTime: 3
          },
          {
            ...this.getDefaultItemStructure(),
            title: 'Test internet connection',
            category: 'streaming',
            priority: 'critical',
            order: 2,
            instructions: 'Verify upload speed meets streaming requirements (minimum 5 Mbps)',
            estimatedTime: 2,
            verification: {
              required: true,
              type: 'measurement',
              expectedResult: 'Upload speed ≥ 5 Mbps',
              acceptableLevels: { min: 5, unit: 'Mbps' }
            }
          },
          {
            ...this.getDefaultItemStructure(),
            title: 'Configure camera feeds',
            category: 'video',
            priority: 'high',
            order: 3,
            instructions: 'Set up all camera angles, test switching between views',
            estimatedTime: 8
          }
        ]
      }
    ];
  }

  // Generate execution report
  generateExecutionReport(execution, checklist) {
    const completedItems = execution.itemResults.filter(ir => ir.status === 'completed');
    const failedItems = execution.itemResults.filter(ir => ir.status === 'failed');
    const skippedItems = execution.itemResults.filter(ir => ir.status === 'skipped');
    
    const criticalFailures = failedItems.filter(item => {
      const checklistItem = checklist.items.find(ci => ci.id === item.itemId);
      return checklistItem && checklistItem.priority === 'critical';
    });

    return {
      summary: {
        status: execution.status,
        startTime: execution.startedAt,
        endTime: execution.completedAt,
        duration: execution.totalTimeSpent,
        completionRate: Math.round((completedItems.length / execution.itemResults.length) * 100)
      },
      itemBreakdown: {
        completed: completedItems.length,
        failed: failedItems.length,
        skipped: skippedItems.length,
        total: execution.itemResults.length
      },
      issues: {
        total: execution.issuesFound,
        critical: execution.criticalIssues,
        criticalFailures: criticalFailures.length,
        needsFollowUp: execution.followUpRequired
      },
      recommendations: execution.recommendations || [],
      followUpActions: execution.followUpActions || []
    };
  }

  // Calculate checklist complexity score
  calculateComplexityScore(checklist) {
    let score = 0;
    
    // Base score from item count
    score += checklist.items.length * 2;
    
    // Add complexity for critical items
    const criticalItems = checklist.items.filter(item => item.priority === 'critical');
    score += criticalItems.length * 5;
    
    // Add complexity for verification requirements
    const verificationItems = checklist.items.filter(item => item.verification?.required);
    score += verificationItems.length * 3;
    
    // Add complexity for dependencies
    const dependentItems = checklist.items.filter(item => item.dependencies?.length > 0);
    score += dependentItems.length * 2;
    
    // Normalize to 0-100 scale
    const maxScore = checklist.items.length * 12; // Theoretical maximum
    return Math.min(Math.round((score / maxScore) * 100), 100);
  }

  // Get checklist status badge info
  getStatusBadge(checklist) {
    const recentExecutions = checklist.completions
      ?.filter(c => c.completedAt && c.completedAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
      .length || 0;

    const successRate = checklist.analytics?.successRate || 0;
    
    let status = 'inactive';
    let color = '#757575';
    let label = 'Inactive';

    if (recentExecutions > 0) {
      if (successRate >= 90) {
        status = 'excellent';
        color = '#4CAF50';
        label = 'Excellent';
      } else if (successRate >= 75) {
        status = 'good';
        color = '#8BC34A';
        label = 'Good';
      } else if (successRate >= 50) {
        status = 'fair';
        color = '#FF9800';
        label = 'Fair';
      } else {
        status = 'poor';
        color = '#F44336';
        label = 'Needs Attention';
      }
    }

    return { status, color, label, recentExecutions, successRate };
  }

  // Format checklist for display
  formatChecklistForDisplay(checklist) {
    const types = this.getTypes();
    
    return {
      ...checklist,
      statusBadge: this.getStatusBadge(checklist),
      complexityScore: this.calculateComplexityScore(checklist),
      formattedDuration: this.formatDuration(checklist.estimatedTotalTime),
      criticalItemCount: checklist.items?.filter(item => item.priority === 'critical').length || 0,
      activeItemCount: checklist.items?.filter(item => item.isActive).length || 0,
      lastExecuted: checklist.completions?.length > 0 ? 
        new Date(Math.max(...checklist.completions.map(c => new Date(c.startedAt)))).toLocaleDateString() : 
        'Never'
    };
  }

  // Format duration in minutes to human readable
  formatDuration(minutes) {
    if (!minutes || minutes === 0) return '0 min';
    
    if (minutes < 60) {
      return `${minutes} min`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours}h`;
    }
    
    return `${hours}h ${remainingMinutes}m`;
  }

  // Get priority color and icon
  getPriorityInfo(priority) {
    const priorities = {
      critical: { color: '#F44336', icon: 'priority_high', label: 'Critical' },
      high: { color: '#FF9800', icon: 'keyboard_arrow_up', label: 'High' },
      medium: { color: '#2196F3', icon: 'remove', label: 'Medium' },
      low: { color: '#4CAF50', icon: 'keyboard_arrow_down', label: 'Low' }
    };
    
    return priorities[priority] || priorities.medium;
  }

  // Get category color and icon
  getCategoryInfo(category) {
    const categories = {
      audio: { color: '#FF9800', icon: 'volume_up', label: 'Audio' },
      video: { color: '#2196F3', icon: 'videocam', label: 'Video' },
      lighting: { color: '#FFEB3B', icon: 'lightbulb', label: 'Lighting' },
      recording: { color: '#F44336', icon: 'fiber_manual_record', label: 'Recording' },
      streaming: { color: '#9C27B0', icon: 'cast', label: 'Streaming' },
      presentation: { color: '#4CAF50', icon: 'slideshow', label: 'Presentation' },
      microphones: { color: '#795548', icon: 'mic', label: 'Microphones' },
      monitors: { color: '#607D8B', icon: 'monitor', label: 'Monitors' },
      general: { color: '#757575', icon: 'settings', label: 'General' }
    };
    
    return categories[category] || categories.general;
  }
}

export default new AVChecklistService();