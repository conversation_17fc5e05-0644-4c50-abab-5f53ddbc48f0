import axios from 'axios';

/**
 * Get all shortcut categories
 * @returns {Promise<Array>} Array of category objects
 */
export const getCategories = async () => {
  try {
    const response = await axios.get('/api/shortcut-categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching shortcut categories:', error);
    throw error;
  }
};

/**
 * Get a shortcut category by ID
 * @param {string} id Category ID
 * @returns {Promise<Object>} Category object
 */
export const getCategoryById = async (id) => {
  try {
    const response = await axios.get(`/api/shortcut-categories/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching shortcut category ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new shortcut category
 * @param {Object} categoryData Category data
 * @returns {Promise<Object>} Created category object
 */
export const createCategory = async (categoryData) => {
  try {
    const response = await axios.post('/api/shortcut-categories', categoryData);
    return response.data;
  } catch (error) {
    console.error('Error creating shortcut category:', error);
    throw error;
  }
};

/**
 * Update a shortcut category
 * @param {string} id Category ID
 * @param {Object} categoryData Updated category data
 * @returns {Promise<Object>} Updated category object
 */
export const updateCategory = async (id, categoryData) => {
  try {
    const response = await axios.put(`/api/shortcut-categories/${id}`, categoryData);
    return response.data;
  } catch (error) {
    console.error(`Error updating shortcut category ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a shortcut category
 * @param {string} id Category ID
 * @returns {Promise<Object>} Response object
 */
export const deleteCategory = async (id) => {
  try {
    const response = await axios.delete(`/api/shortcut-categories/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting shortcut category ${id}:`, error);
    throw error;
  }
};

/**
 * Initialize default categories
 * @returns {Promise<Array>} Array of category objects
 */
export const initializeDefaultCategories = async () => {
  try {
    const response = await axios.post('/api/shortcut-categories/init');
    return response.data;
  } catch (error) {
    console.error('Error initializing default shortcut categories:', error);
    throw error;
  }
};