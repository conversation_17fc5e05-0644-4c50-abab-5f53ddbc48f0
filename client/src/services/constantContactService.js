import axios from 'axios';

/**
 * Service for interacting with the Constant Contact API
 */
const constantContactService = {
  /**
   * Initialize the Constant Contact API
   * @returns {Promise<Object>} Initialization status
   */
  initialize: async () => {
    try {
      const response = await axios.get('/api/constant-contact/initialize');
      return response.data;
    } catch (error) {
      console.error('Error initializing Constant Contact API:', error);
      throw error;
    }
  },

  /**
   * Get account information
   * @returns {Promise<Object>} Account information
   */
  getAccountInfo: async () => {
    try {
      const response = await axios.get('/api/constant-contact/account');
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact account information:', error);
      throw error;
    }
  },

  /**
   * Get contact lists
   * @returns {Promise<Array>} Contact lists
   */
  getContactLists: async () => {
    try {
      const response = await axios.get('/api/constant-contact/lists');
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact lists:', error);
      throw error;
    }
  },

  /**
   * Get contacts
   * @param {Object} params - Query parameters
   * @param {number} params.limit - Number of contacts to return (default: 50)
   * @param {string} params.status - Contact status (all, active, removed, unconfirmed)
   * @returns {Promise<Object>} Contacts response
   */
  getContacts: async (params = {}) => {
    try {
      const response = await axios.get('/api/constant-contact/contacts', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact contacts:', error);
      throw error;
    }
  },

  /**
   * Get contact by email address
   * @param {string} email - Email address
   * @returns {Promise<Object>} Contact
   */
  getContactByEmail: async (email) => {
    try {
      const response = await axios.get('/api/constant-contact/contacts/email', {
        params: { email }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact contact by email:', error);
      throw error;
    }
  },

  /**
   * Create a new contact
   * @param {Object} contact - Contact data
   * @param {string} contact.email_address - Email address
   * @param {string} contact.first_name - First name
   * @param {string} contact.last_name - Last name
   * @param {Array} contact.list_memberships - List IDs to add the contact to
   * @returns {Promise<Object>} Created contact
   */
  createContact: async (contact) => {
    try {
      const response = await axios.post('/api/constant-contact/contacts', contact);
      return response.data;
    } catch (error) {
      console.error('Error creating Constant Contact contact:', error);
      throw error;
    }
  },

  /**
   * Update an existing contact
   * @param {string} contactId - Contact ID
   * @param {Object} contact - Updated contact data
   * @returns {Promise<Object>} Updated contact
   */
  updateContact: async (contactId, contact) => {
    try {
      const response = await axios.put(`/api/constant-contact/contacts/${contactId}`, contact);
      return response.data;
    } catch (error) {
      console.error('Error updating Constant Contact contact:', error);
      throw error;
    }
  },

  /**
   * Delete a contact
   * @param {string} contactId - Contact ID
   * @returns {Promise<Object>} Response data
   */
  deleteContact: async (contactId) => {
    try {
      const response = await axios.delete(`/api/constant-contact/contacts/${contactId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting Constant Contact contact:', error);
      throw error;
    }
  },

  /**
   * Get campaigns
   * @param {Object} params - Query parameters
   * @param {number} params.limit - Number of campaigns to return (default: 50)
   * @param {string} params.status - Campaign status (all, draft, scheduled, sent)
   * @returns {Promise<Object>} Campaigns response
   */
  getCampaigns: async (params = {}) => {
    try {
      const response = await axios.get('/api/constant-contact/campaigns', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact campaigns:', error);
      throw error;
    }
  },

  /**
   * Get campaign by ID
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<Object>} Campaign
   */
  getCampaign: async (campaignId) => {
    try {
      const response = await axios.get(`/api/constant-contact/campaigns/${campaignId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact campaign:', error);
      throw error;
    }
  },

  /**
   * Get campaign tracking data
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<Object>} Campaign tracking data
   */
  getCampaignTracking: async (campaignId) => {
    try {
      const response = await axios.get(`/api/constant-contact/campaigns/${campaignId}/tracking`);
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact campaign tracking:', error);
      throw error;
    }
  },

  /**
   * Get Constant Contact configuration
   * @returns {Promise<Object>} Configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/constant-contact/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching Constant Contact configuration:', error);
      throw error;
    }
  }
};

export default constantContactService;