import axios from 'axios';

/**
 * Utility Shutoff service wrapper for Phase 6 - Water & Gas Shutoffs
 * Provides API wrappers for utility shutoff management and emergency response
 */
class UtilityShutoffService {
  constructor() {
    this.baseURL = '/api/utility-shutoffs';
  }

  // ===== Utility Shutoff Management =====

  /**
   * Get all utility shutoffs with optional filtering
   */
  async getUtilityShutoffs(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching utility shutoffs:', error);
      throw error;
    }
  }

  /**
   * Get specific utility shutoff details
   */
  async getUtilityShutoff(id) {
    try {
      const response = await axios.get(`${this.baseURL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching utility shutoff:', error);
      throw error;
    }
  }

  /**
   * Create new utility shutoff
   */
  async createUtilityShutoff(shutoffData) {
    try {
      const response = await axios.post(`${this.baseURL}`, shutoffData);
      return response.data;
    } catch (error) {
      console.error('Error creating utility shutoff:', error);
      throw error;
    }
  }

  /**
   * Update utility shutoff
   */
  async updateUtilityShutoff(id, shutoffData) {
    try {
      const response = await axios.put(`${this.baseURL}/${id}`, shutoffData);
      return response.data;
    } catch (error) {
      console.error('Error updating utility shutoff:', error);
      throw error;
    }
  }

  /**
   * Delete utility shutoff
   */
  async deleteUtilityShutoff(id, force = false) {
    try {
      const response = await axios.delete(`${this.baseURL}/${id}`, { 
        params: { force: force.toString() } 
      });
      return response.data;
    } catch (error) {
      console.error('Error deleting utility shutoff:', error);
      throw error;
    }
  }

  // ===== Location-based Queries =====

  /**
   * Get utility shutoffs for specific building and floor
   */
  async getShutoffsByLocation(buildingId, floorId, includePositioned = false) {
    try {
      const response = await axios.get(
        `${this.baseURL}/building/${buildingId}/floor/${floorId}`,
        { params: { includePositioned: includePositioned.toString() } }
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching shutoffs by location:', error);
      throw error;
    }
  }

  /**
   * Get utility shutoffs by type
   */
  async getShutoffsByType(shutoffType) {
    try {
      const response = await axios.get(`${this.baseURL}/type/${shutoffType}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching shutoffs by type:', error);
      throw error;
    }
  }

  /**
   * Get critical emergency shutoffs
   */
  async getCriticalShutoffs() {
    try {
      const response = await axios.get(`${this.baseURL}/emergency/critical`);
      return response.data;
    } catch (error) {
      console.error('Error fetching critical shutoffs:', error);
      throw error;
    }
  }

  // ===== Maintenance and Inspection =====

  /**
   * Get shutoffs with due or overdue inspections
   */
  async getInspectionsDue(daysAhead = 30) {
    try {
      const response = await axios.get(`${this.baseURL}/inspections/due`, {
        params: { daysAhead }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching inspection schedule:', error);
      throw error;
    }
  }

  /**
   * Record inspection completion
   */
  async recordInspection(id, inspectionData) {
    try {
      const response = await axios.post(`${this.baseURL}/${id}/inspection`, inspectionData);
      return response.data;
    } catch (error) {
      console.error('Error recording inspection:', error);
      throw error;
    }
  }

  /**
   * Record emergency shutoff usage
   */
  async recordEmergencyUse(id, emergencyData) {
    try {
      const response = await axios.post(`${this.baseURL}/${id}/emergency-use`, emergencyData);
      return response.data;
    } catch (error) {
      console.error('Error recording emergency use:', error);
      throw error;
    }
  }

  // ===== Emergency Response =====

  /**
   * Find nearest shutoffs to a location for emergency response
   */
  async findNearestShutoffs(params) {
    try {
      const response = await axios.get(`${this.baseURL}/emergency/nearest`, { params });
      return response.data;
    } catch (error) {
      console.error('Error finding nearest shutoffs:', error);
      throw error;
    }
  }

  /**
   * Get emergency procedure information for specific shutoff
   */
  async getEmergencyProcedures(id) {
    try {
      const response = await axios.get(`${this.baseURL}/emergency/procedures/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching emergency procedures:', error);
      throw error;
    }
  }

  // ===== Analytics and Reporting =====

  /**
   * Get analytics summary for shutoffs
   */
  async getAnalyticsSummary(filters = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/analytics/summary`, { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error fetching analytics summary:', error);
      throw error;
    }
  }

  /**
   * Seed demo utility shutoff data
   */
  async seedDemoData(buildingId, floorId, clearExisting = false) {
    try {
      const response = await axios.post(`${this.baseURL}/seed-demo-data`, {
        buildingId,
        floorId,
        clearExisting
      });
      return response.data;
    } catch (error) {
      console.error('Error seeding demo data:', error);
      throw error;
    }
  }

  // ===== Static Helper Methods =====

  /**
   * Get shutoff type options for UI
   */
  getShutoffTypes() {
    return [
      { value: 'water_main', label: 'Water Main', icon: '🚰', priority: 'critical' },
      { value: 'water_zone', label: 'Water Zone', icon: '💧', priority: 'high' },
      { value: 'water_emergency', label: 'Water Emergency', icon: '🚨', priority: 'critical' },
      { value: 'gas_main', label: 'Gas Main', icon: '⛽', priority: 'critical' },
      { value: 'gas_zone', label: 'Gas Zone', icon: '🔥', priority: 'high' },
      { value: 'gas_emergency', label: 'Gas Emergency', icon: '🚨', priority: 'critical' },
      { value: 'steam_main', label: 'Steam Main', icon: '💨', priority: 'high' },
      { value: 'steam_zone', label: 'Steam Zone', icon: '♨️', priority: 'medium' },
      { value: 'compressed_air', label: 'Compressed Air', icon: '🌪️', priority: 'medium' },
      { value: 'other', label: 'Other', icon: '⚙️', priority: 'medium' }
    ];
  }

  /**
   * Get utility type categories
   */
  getUtilityTypes() {
    return [
      { value: 'water', label: 'Water', color: '#2196F3', icon: '💧' },
      { value: 'gas', label: 'Gas', color: '#FF9800', icon: '⛽' },
      { value: 'steam', label: 'Steam', color: '#9E9E9E', icon: '💨' },
      { value: 'other', label: 'Other', color: '#607D8B', icon: '⚙️' }
    ];
  }

  /**
   * Get priority levels
   */
  getPriorityLevels() {
    return [
      { value: 'critical', label: 'Critical', color: '#F44336', weight: 4 },
      { value: 'high', label: 'High', color: '#FF9800', weight: 3 },
      { value: 'medium', label: 'Medium', color: '#FFC107', weight: 2 },
      { value: 'low', label: 'Low', color: '#4CAF50', weight: 1 }
    ];
  }

  /**
   * Get shutoff states
   */
  getShutoffStates() {
    return [
      { value: 'open', label: 'Open', color: '#4CAF50', icon: '🔓' },
      { value: 'closed', label: 'Closed', color: '#F44336', icon: '🔒' },
      { value: 'partially_open', label: 'Partially Open', color: '#FF9800', icon: '🔐' },
      { value: 'unknown', label: 'Unknown', color: '#9E9E9E', icon: '❓' },
      { value: 'maintenance', label: 'Maintenance', color: '#2196F3', icon: '🔧' }
    ];
  }

  /**
   * Get condition levels
   */
  getConditionLevels() {
    return [
      { value: 'excellent', label: 'Excellent', color: '#4CAF50' },
      { value: 'good', label: 'Good', color: '#8BC34A' },
      { value: 'fair', label: 'Fair', color: '#FFC107' },
      { value: 'poor', label: 'Poor', color: '#FF9800' },
      { value: 'needs_replacement', label: 'Needs Replacement', color: '#F44336' }
    ];
  }

  /**
   * Get color for shutoff type
   */
  getShutoffTypeColor(shutoffType) {
    const typeColors = {
      'water_main': '#1976D2',
      'water_zone': '#2196F3',
      'water_emergency': '#F44336',
      'gas_main': '#E65100',
      'gas_zone': '#FF9800',
      'gas_emergency': '#F44336',
      'steam_main': '#616161',
      'steam_zone': '#9E9E9E',
      'compressed_air': '#607D8B',
      'other': '#795548'
    };
    return typeColors[shutoffType] || '#9E9E9E';
  }

  /**
   * Get color for priority level
   */
  getPriorityColor(priority) {
    const priorityColors = {
      'critical': '#F44336',
      'high': '#FF9800',
      'medium': '#FFC107',
      'low': '#4CAF50'
    };
    return priorityColors[priority] || '#9E9E9E';
  }

  /**
   * Get color for shutoff state
   */
  getStateColor(state) {
    const stateColors = {
      'open': '#4CAF50',
      'closed': '#F44336',
      'partially_open': '#FF9800',
      'unknown': '#9E9E9E',
      'maintenance': '#2196F3'
    };
    return stateColors[state] || '#9E9E9E';
  }

  /**
   * Format shutoff for display
   */
  formatShutoffDisplay(shutoff) {
    const typeInfo = this.getShutoffTypes().find(t => t.value === shutoff.shutoffType);
    const utilityType = this.getUtilityTypes().find(t => shutoff.shutoffType?.startsWith(t.value));
    
    return {
      ...shutoff,
      displayName: shutoff.name || shutoff.shutoffId,
      typeIcon: typeInfo?.icon || '⚙️',
      typeLabel: typeInfo?.label || shutoff.shutoffType,
      utilityIcon: utilityType?.icon || '⚙️',
      utilityColor: utilityType?.color || '#9E9E9E',
      priorityColor: this.getPriorityColor(shutoff.coverage?.priority),
      stateColor: this.getStateColor(shutoff.status?.currentState),
      isEmergency: shutoff.shutoffType?.includes('emergency'),
      isCritical: shutoff.coverage?.priority === 'critical' || shutoff.shutoffType?.includes('main')
    };
  }

  /**
   * Calculate emergency response priority
   */
  calculateEmergencyPriority(shutoff) {
    let score = 0;
    
    // Type priority
    if (shutoff.shutoffType?.includes('main')) score += 10;
    if (shutoff.shutoffType?.includes('emergency')) score += 8;
    if (shutoff.shutoffType?.includes('zone')) score += 5;
    
    // Priority level
    const priorityScores = { critical: 10, high: 7, medium: 5, low: 2 };
    score += priorityScores[shutoff.coverage?.priority] || 0;
    
    // Accessibility
    if (shutoff.status?.isAccessible === false) score -= 5;
    
    // Condition
    const conditionScores = { excellent: 3, good: 2, fair: 1, poor: -1, needs_replacement: -3 };
    score += conditionScores[shutoff.status?.condition] || 0;
    
    return Math.max(0, score);
  }

  /**
   * Get emergency response instructions
   */
  getEmergencyInstructions(shutoffType, utilityType) {
    const instructions = {
      water: {
        general: [
          "1. Locate the shutoff valve",
          "2. Turn clockwise to close (righty-tighty)",
          "3. Verify water stops flowing",
          "4. Contact facilities management"
        ],
        main: [
          "1. EMERGENCY: Call facilities immediately",
          "2. Locate main water shutoff",
          "3. Turn valve clockwise until tight",
          "4. Check that water pressure drops",
          "5. Notify all building occupants"
        ]
      },
      gas: {
        general: [
          "1. SAFETY FIRST: Check for gas smell",
          "2. Use proper tools (gas meter key)",
          "3. Turn 1/4 turn clockwise",
          "4. Valve should be perpendicular to pipe",
          "5. Call gas company to restore service"
        ],
        main: [
          "1. EMERGENCY: Evacuate if gas smell present",
          "2. Call gas company emergency line",
          "3. Use gas meter key - 1/4 turn only",
          "4. Do not use electrical switches or flames",
          "5. Wait for professional inspection"
        ]
      }
    };

    const type = utilityType || 'general';
    const category = shutoffType?.includes('main') ? 'main' : 'general';
    
    return instructions[type]?.[category] || instructions.general || [];
  }

  /**
   * Validate shutoff data for creation/update
   */
  validateShutoffData(data) {
    const errors = [];

    // Required fields
    if (!data.shutoffId) errors.push('Shutoff ID is required');
    if (!data.name) errors.push('Name is required');
    if (!data.buildingId) errors.push('Building is required');
    if (!data.floorId) errors.push('Floor is required');
    if (!data.shutoffType) errors.push('Shutoff type is required');

    // Format validation
    if (data.shutoffId && !/^[A-Z0-9\-_]+$/i.test(data.shutoffId)) {
      errors.push('Shutoff ID can only contain letters, numbers, hyphens, and underscores');
    }

    // Position validation
    if (data.position) {
      if (data.position.x !== undefined && (data.position.x < 0 || data.position.x > 100)) {
        errors.push('X position must be between 0 and 100');
      }
      if (data.position.y !== undefined && (data.position.y < 0 || data.position.y > 100)) {
        errors.push('Y position must be between 0 and 100');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

const utilityShutoffService = new UtilityShutoffService();
export default utilityShutoffService;