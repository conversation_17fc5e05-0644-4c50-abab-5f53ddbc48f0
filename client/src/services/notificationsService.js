import axios from 'axios';

const notificationsService = {
  getNotifications: async ({ limit = 10, onlyUnread = false } = {}) => {
    const params = new URLSearchParams();
    if (limit) params.append('limit', String(limit));
    if (onlyUnread) params.append('onlyUnread', 'true');
    const res = await axios.get(`/api/notifications?${params.toString()}`);
    return res.data;
  },

  getUnreadCount: async () => {
    const res = await axios.get('/api/notifications/unread-count');
    return res.data.count || 0;
  },

  markAsRead: async (id) => {
    const res = await axios.patch(`/api/notifications/${id}/read`);
    return res.data;
  },

  markAllAsRead: async () => {
    const res = await axios.patch('/api/notifications/mark-all-read');
    return res.data;
  },

  // Helper for development/testing
  create: async ({ title, message, type = 'info', linkUrl, metadata }) => {
    const res = await axios.post('/api/notifications', { title, message, type, linkUrl, metadata });
    return res.data;
  }
};

export default notificationsService;
