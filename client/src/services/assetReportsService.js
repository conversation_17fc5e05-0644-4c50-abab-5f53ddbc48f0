import axios from 'axios';

/**
 * Asset Reports Service
 * Provides methods for interacting with the asset reports API
 */
const assetReportsService = {
  /**
   * Get summary statistics for assets
   * @returns {Promise<Object>} Summary statistics
   */
  getSummary: async () => {
    try {
      const response = await axios.get('/api/asset-reports/summary');
      return response.data;
    } catch (error) {
      console.error('Error getting asset summary:', error);
      throw error;
    }
  },

  /**
   * Get monthly statistics for asset requests
   * @returns {Promise<Array>} Monthly statistics
   */
  getMonthlyRequestStats: async () => {
    try {
      const response = await axios.get('/api/asset-reports/requests/monthly');
      return response.data;
    } catch (error) {
      console.error('Error getting monthly request statistics:', error);
      throw error;
    }
  },

  /**
   * Get monthly statistics for asset issues
   * @returns {Promise<Array>} Monthly statistics
   */
  getMonthlyIssueStats: async () => {
    try {
      const response = await axios.get('/api/asset-reports/issues/monthly');
      return response.data;
    } catch (error) {
      console.error('Error getting monthly issue statistics:', error);
      throw error;
    }
  },

  /**
   * Get asset type distribution
   * @returns {Promise<Object>} Asset type distribution
   */
  getAssetTypeDistribution: async () => {
    try {
      const response = await axios.get('/api/asset-reports/assets/types');
      return response.data;
    } catch (error) {
      console.error('Error getting asset type distribution:', error);
      throw error;
    }
  },

  /**
   * Get issue type distribution
   * @returns {Promise<Object>} Issue type distribution
   */
  getIssueTypeDistribution: async () => {
    try {
      const response = await axios.get('/api/asset-reports/issues/types');
      return response.data;
    } catch (error) {
      console.error('Error getting issue type distribution:', error);
      throw error;
    }
  },

  /**
   * Get issue priority distribution
   * @returns {Promise<Object>} Issue priority distribution
   */
  getIssuePriorityDistribution: async () => {
    try {
      const response = await axios.get('/api/asset-reports/issues/priorities');
      return response.data;
    } catch (error) {
      console.error('Error getting issue priority distribution:', error);
      throw error;
    }
  },

  /**
   * Get issue status distribution
   * @returns {Promise<Object>} Issue status distribution
   */
  getIssueStatusDistribution: async () => {
    try {
      const response = await axios.get('/api/asset-reports/issues/statuses');
      return response.data;
    } catch (error) {
      console.error('Error getting issue status distribution:', error);
      throw error;
    }
  },

  /**
   * Get request status distribution
   * @returns {Promise<Object>} Request status distribution
   */
  getRequestStatusDistribution: async () => {
    try {
      const response = await axios.get('/api/asset-reports/requests/statuses');
      return response.data;
    } catch (error) {
      console.error('Error getting request status distribution:', error);
      throw error;
    }
  },

  /**
   * Get average resolution time for issues
   * @returns {Promise<Object>} Average resolution time
   */
  getAverageResolutionTime: async () => {
    try {
      const response = await axios.get('/api/asset-reports/issues/resolution-time');
      return response.data;
    } catch (error) {
      console.error('Error getting average resolution time:', error);
      throw error;
    }
  },

  /**
   * Get average approval time for requests
   * @returns {Promise<Object>} Average approval time
   */
  getAverageApprovalTime: async () => {
    try {
      const response = await axios.get('/api/asset-reports/requests/approval-time');
      return response.data;
    } catch (error) {
      console.error('Error getting average approval time:', error);
      throw error;
    }
  }
};

export default assetReportsService;