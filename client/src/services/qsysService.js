import axios from 'axios';

/**
 * Service for interacting with the Q-sys Core Manager API
 */
const qsysService = {
  /**
   * Get Q-sys Core Manager status
   * @returns {Promise<Object>} Status information
   */
  getStatus: async () => {
    try {
      const response = await axios.get('/api/qsys/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching Q-sys status:', error);
      throw error;
    }
  },

  /**
   * Get Q-sys Core Manager health status
   * @returns {Promise<Object>} Health status information
   */
  getHealthStatus: async () => {
    try {
      const response = await axios.get('/api/qsys/health');
      return response.data;
    } catch (error) {
      console.error('Error fetching Q-sys health status:', error);
      throw error;
    }
  },

  /**
   * Get Q-sys Core Manager components
   * @returns {Promise<Array>} List of components
   */
  getComponents: async () => {
    try {
      const response = await axios.get('/api/qsys/components');
      return response.data;
    } catch (error) {
      console.error('Error fetching Q-sys components:', error);
      throw error;
    }
  },

  /**
   * Get controls for a component
   * @param {string} componentName - Name of the component
   * @returns {Promise<Array>} List of controls
   */
  getControls: async (componentName) => {
    try {
      const response = await axios.get(`/api/qsys/components/${componentName}/controls`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching controls for component ${componentName}:`, error);
      throw error;
    }
  },

  /**
   * Get the value of a control
   * @param {string} componentName - Name of the component
   * @param {string} controlName - Name of the control
   * @returns {Promise<Object>} Control value
   */
  getControlValue: async (componentName, controlName) => {
    try {
      const response = await axios.get(`/api/qsys/components/${componentName}/controls/${controlName}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching value for control ${controlName} of component ${componentName}:`, error);
      throw error;
    }
  },

  /**
   * Set the value of a control
   * @param {string} componentName - Name of the component
   * @param {string} controlName - Name of the control
   * @param {*} value - Value to set
   * @returns {Promise<Object>} Response data
   */
  setControlValue: async (componentName, controlName, value) => {
    try {
      const response = await axios.post(`/api/qsys/components/${componentName}/controls/${controlName}`, { value });
      return response.data;
    } catch (error) {
      console.error(`Error setting value for control ${controlName} of component ${componentName}:`, error);
      throw error;
    }
  },

  /**
   * Get Q-sys configuration
   * @returns {Promise<Object>} Configuration
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/qsys/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching Q-sys configuration:', error);
      throw error;
    }
  },

  /**
   * Save Q-sys configuration
   * @param {Object} config - Configuration object
   * @param {string} config.host - Host
   * @param {number} config.port - Port
   * @param {string} config.username - Username
   * @param {string} config.password - Password
   * @returns {Promise<Object>} Response data
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/qsys/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving Q-sys configuration:', error);
      throw error;
    }
  },

  /**
   * Set up Q-sys with one click
   * @returns {Promise<Object>} Response data
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/qsys/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up Q-sys with one click:', error);
      throw error;
    }
  }
};

export default qsysService;