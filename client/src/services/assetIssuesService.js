import axios from 'axios';

/**
 * Asset Issues Service
 * Provides methods for interacting with the asset issues API
 */
const assetIssuesService = {
  /**
   * Get all asset issues
   * @returns {Promise<Array>} List of asset issues
   */
  getAssetIssues: async () => {
    try {
      const response = await axios.get('/api/asset-issues');
      return response.data;
    } catch (error) {
      console.error('Error getting asset issues:', error);
      throw error;
    }
  },

  /**
   * Get asset issue by ID
   * @param {string} id - Asset issue ID
   * @returns {Promise<Object>} Asset issue details
   */
  getAssetIssueById: async (id) => {
    try {
      const response = await axios.get(`/api/asset-issues/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting asset issue:', error);
      throw error;
    }
  },

  /**
   * Create a new asset issue
   * @param {Object} issueData - Asset issue data
   * @returns {Promise<Object>} Created asset issue
   */
  createAssetIssue: async (issueData) => {
    try {
      const response = await axios.post('/api/asset-issues', issueData);
      return response.data;
    } catch (error) {
      console.error('Error creating asset issue:', error);
      throw error;
    }
  },

  /**
   * Update an asset issue
   * @param {string} id - Asset issue ID
   * @param {Object} issueData - Asset issue data
   * @returns {Promise<Object>} Updated asset issue
   */
  updateAssetIssue: async (id, issueData) => {
    try {
      const response = await axios.put(`/api/asset-issues/${id}`, issueData);
      return response.data;
    } catch (error) {
      console.error('Error updating asset issue:', error);
      throw error;
    }
  },

  /**
   * Delete an asset issue
   * @param {string} id - Asset issue ID
   * @returns {Promise<Object>} Response message
   */
  deleteAssetIssue: async (id) => {
    try {
      const response = await axios.delete(`/api/asset-issues/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting asset issue:', error);
      throw error;
    }
  },

  /**
   * Add a comment to an asset issue
   * @param {string} id - Asset issue ID
   * @param {Object} commentData - Comment data
   * @returns {Promise<Object>} Updated asset issue
   */
  addComment: async (id, commentData) => {
    try {
      const response = await axios.post(`/api/asset-issues/${id}/comments`, commentData);
      return response.data;
    } catch (error) {
      console.error('Error adding comment to asset issue:', error);
      throw error;
    }
  },

  /**
   * Update the status of an asset issue
   * @param {string} id - Asset issue ID
   * @param {Object} statusData - Status data
   * @returns {Promise<Object>} Updated asset issue
   */
  updateStatus: async (id, statusData) => {
    try {
      const response = await axios.put(`/api/asset-issues/${id}/status`, statusData);
      return response.data;
    } catch (error) {
      console.error('Error updating asset issue status:', error);
      throw error;
    }
  },

  /**
   * Assign an asset issue to a user
   * @param {string} id - Asset issue ID
   * @param {Object} assignmentData - Assignment data
   * @returns {Promise<Object>} Updated asset issue
   */
  assignIssue: async (id, assignmentData) => {
    try {
      const response = await axios.put(`/api/asset-issues/${id}/assign`, assignmentData);
      return response.data;
    } catch (error) {
      console.error('Error assigning asset issue:', error);
      throw error;
    }
  }
};

export default assetIssuesService;