import axios from 'axios';

/**
 * LG ThinQ AC Unit API Service
 * Handles client-side API calls to the LG ThinQ endpoints
 */
const lgThinqService = {
  /**
   * Set up LG ThinQ with one click
   * @returns {Promise<Object>} Response message
   */
  oneClickSetup: async () => {
    try {
      const response = await axios.post('/api/lg-thinq/one-click-setup');
      return response.data;
    } catch (error) {
      console.error('Error setting up LG ThinQ with one click:', error);
      throw error;
    }
  },
  /**
   * Get all LG ThinQ devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async (params = {}) => {
    try {
      const response = await axios.get('/api/lg-thinq/devices', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching LG ThinQ devices:', error);
      throw error;
    }
  },

  /**
   * Get LG ThinQ device details
   * @param {string} deviceId Device ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Device details
   */
  getDevice: async (deviceId, params = {}) => {
    try {
      const response = await axios.get(`/api/lg-thinq/devices/${deviceId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching LG ThinQ device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get device status
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device status
   */
  getDeviceStatus: async (deviceId) => {
    try {
      const response = await axios.get(`/api/lg-thinq/devices/${deviceId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching LG ThinQ device ${deviceId} status:`, error);
      throw error;
    }
  },

  /**
   * Control LG ThinQ device
   * @param {string} deviceId Device ID
   * @param {Object} command Command object with action and parameters
   * @returns {Promise<Object>} Response message
   */
  controlDevice: async (deviceId, command) => {
    try {
      const response = await axios.post(`/api/lg-thinq/devices/${deviceId}/control`, command);
      return response.data;
    } catch (error) {
      console.error(`Error controlling LG ThinQ device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Save LG ThinQ configuration
   * @param {Object} config Configuration object with credentials
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/lg-thinq/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving LG ThinQ configuration:', error);
      throw error;
    }
  },

  /**
   * Get LG ThinQ configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/lg-thinq/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching LG ThinQ configuration:', error);
      throw error;
    }
  }
};

export default lgThinqService;