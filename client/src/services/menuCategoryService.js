import axios from 'axios';

/**
 * Get all menu categories
 * @returns {Promise<Array>} Array of category objects
 */
export const getCategories = async () => {
  try {
    const response = await axios.get('/api/menu-categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching menu categories:', error);
    throw error;
  }
};

/**
 * Get a menu category by ID
 * @param {string} id Category ID
 * @returns {Promise<Object>} Category object
 */
export const getCategoryById = async (id) => {
  try {
    const response = await axios.get(`/api/menu-categories/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching menu category ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new menu category
 * @param {Object} categoryData Category data
 * @returns {Promise<Object>} Created category object
 */
export const createCategory = async (categoryData) => {
  try {
    const response = await axios.post('/api/menu-categories', categoryData);
    return response.data;
  } catch (error) {
    console.error('Error creating menu category:', error);
    throw error;
  }
};

/**
 * Update a menu category
 * @param {string} id Category ID
 * @param {Object} categoryData Updated category data
 * @returns {Promise<Object>} Updated category object
 */
export const updateCategory = async (id, categoryData) => {
  try {
    const response = await axios.put(`/api/menu-categories/${id}`, categoryData);
    return response.data;
  } catch (error) {
    console.error(`Error updating menu category ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a menu category
 * @param {string} id Category ID
 * @returns {Promise<Object>} Response object
 */
export const deleteCategory = async (id) => {
  try {
    const response = await axios.delete(`/api/menu-categories/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting menu category ${id}:`, error);
    throw error;
  }
};

/**
 * Initialize default categories
 * @returns {Promise<Array>} Array of category objects
 */
export const initializeDefaultCategories = async () => {
  try {
    const response = await axios.post('/api/menu-categories/init');
    return response.data;
  } catch (error) {
    console.error('Error initializing default menu categories:', error);
    throw error;
  }
};