import axios from 'axios';

/**
 * GLPI API Service
 * Handles client-side API calls to the GLPI endpoints
 */
const glpiService = {
  /**
   * Save GLPI configuration
   * @param {Object} config Configuration object with url, appToken, userToken, username, password
   * @returns {Promise<Object>} Response message
   */
  saveConfig: async (config) => {
    try {
      const response = await axios.post('/api/glpi/config', config);
      return response.data;
    } catch (error) {
      console.error('Error saving GLPI configuration:', error);
      throw error;
    }
  },

  /**
   * Get GLPI configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/glpi/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching GLPI configuration:', error);
      throw error;
    }
  },

  /**
   * Test GLPI connection
   * @returns {Promise<Object>} Connection test result
   */
  testConnection: async () => {
    try {
      const response = await axios.get('/api/glpi/test-connection');
      return response.data;
    } catch (error) {
      console.error('Error testing GLPI connection:', error);
      throw error;
    }
  },

  /**
   * Get all assets
   * @param {Object} params Query parameters for filtering
   * @returns {Promise<Array>} List of assets
   */
  getAssets: async (params = {}) => {
    try {
      const response = await axios.get('/api/glpi/assets', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching GLPI assets:', error);
      throw error;
    }
  },

  /**
   * Get asset by ID
   * @param {string} id Asset ID
   * @returns {Promise<Object>} Asset details
   */
  getAsset: async (id) => {
    try {
      const response = await axios.get(`/api/glpi/assets/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching GLPI asset with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Search assets
   * @param {Object} params Search parameters
   * @returns {Promise<Array>} Search results
   */
  searchAssets: async (params = {}) => {
    try {
      const response = await axios.get('/api/glpi/search', { params });
      return response.data;
    } catch (error) {
      console.error('Error searching GLPI assets:', error);
      throw error;
    }
  },

  /**
   * Get all asset types
   * @returns {Promise<Array>} List of asset types
   */
  getAssetTypes: async () => {
    try {
      const response = await axios.get('/api/glpi/asset-types');
      return response.data;
    } catch (error) {
      console.error('Error fetching GLPI asset types:', error);
      throw error;
    }
  },

  /**
   * Get all asset models
   * @returns {Promise<Array>} List of asset models
   */
  getAssetModels: async () => {
    try {
      const response = await axios.get('/api/glpi/asset-models');
      return response.data;
    } catch (error) {
      console.error('Error fetching GLPI asset models:', error);
      throw error;
    }
  },

  /**
   * Get all asset manufacturers
   * @returns {Promise<Array>} List of asset manufacturers
   */
  getAssetManufacturers: async () => {
    try {
      const response = await axios.get('/api/glpi/asset-manufacturers');
      return response.data;
    } catch (error) {
      console.error('Error fetching GLPI asset manufacturers:', error);
      throw error;
    }
  },
  
  /**
   * Get all asset categories
   * @returns {Promise<Array>} List of asset categories
   */
  getAssetCategories: async () => {
    try {
      const response = await axios.get('/api/glpi/asset-categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching GLPI asset categories:', error);
      throw error;
    }
  },

  /**
   * Create a new asset
   * @param {Object} assetData Asset data
   * @returns {Promise<Object>} Created asset
   */
  createAsset: async (assetData) => {
    try {
      const response = await axios.post('/api/glpi/assets', assetData);
      return response.data;
    } catch (error) {
      console.error('Error creating GLPI asset:', error);
      throw error;
    }
  },

  /**
   * Update an asset
   * @param {string} id Asset ID
   * @param {Object} assetData Updated asset data
   * @returns {Promise<Object>} Updated asset
   */
  updateAsset: async (id, assetData) => {
    try {
      const response = await axios.put(`/api/glpi/assets/${id}`, assetData);
      return response.data;
    } catch (error) {
      console.error(`Error updating GLPI asset with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete an asset
   * @param {string} id Asset ID
   * @returns {Promise<Object>} Response message
   */
  deleteAsset: async (id) => {
    try {
      const response = await axios.delete(`/api/glpi/assets/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting GLPI asset with ID ${id}:`, error);
      throw error;
    }
  }
};

export default glpiService;