import axios from 'axios';

/**
 * Google Forms Webhook API Service
 * Handles client-side API calls to the Google Forms webhook endpoints
 */
const googleFormsWebhookService = {
  /**
   * Get all webhooks
   * @returns {Promise<Array>} List of webhooks
   */
  getAllWebhooks: async () => {
    try {
      const response = await axios.get('/api/google-forms-webhooks');
      return response.data;
    } catch (error) {
      console.error('Error fetching webhooks:', error);
      throw error;
    }
  },

  /**
   * Get webhook by ID
   * @param {string} webhookId Webhook ID
   * @returns {Promise<Object>} Webhook details
   */
  getWebhookById: async (webhookId) => {
    try {
      const response = await axios.get(`/api/google-forms-webhooks/${webhookId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching webhook:', error);
      throw error;
    }
  },

  /**
   * Create a new webhook
   * @param {Object} webhookData Webhook data
   * @returns {Promise<Object>} Created webhook
   */
  createWebhook: async (webhookData) => {
    try {
      const response = await axios.post('/api/google-forms-webhooks', webhookData);
      return response.data;
    } catch (error) {
      console.error('Error creating webhook:', error);
      throw error;
    }
  },

  /**
   * Update an existing webhook
   * @param {string} webhookId Webhook ID
   * @param {Object} webhookData Updated webhook data
   * @returns {Promise<Object>} Updated webhook
   */
  updateWebhook: async (webhookId, webhookData) => {
    try {
      const response = await axios.put(`/api/google-forms-webhooks/${webhookId}`, webhookData);
      return response.data;
    } catch (error) {
      console.error('Error updating webhook:', error);
      throw error;
    }
  },

  /**
   * Delete a webhook
   * @param {string} webhookId Webhook ID
   * @returns {Promise<Object>} Response message
   */
  deleteWebhook: async (webhookId) => {
    try {
      const response = await axios.delete(`/api/google-forms-webhooks/${webhookId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting webhook:', error);
      throw error;
    }
  },

  /**
   * Process a webhook manually
   * @param {string} webhookId Webhook ID
   * @returns {Promise<Object>} Processing results
   */
  processWebhook: async (webhookId) => {
    try {
      const response = await axios.post(`/api/google-forms-webhooks/${webhookId}/process`);
      return response.data;
    } catch (error) {
      console.error('Error processing webhook:', error);
      throw error;
    }
  },

  /**
   * Process all active webhooks (Admin only)
   * @returns {Promise<Object>} Processing results
   */
  processAllWebhooks: async () => {
    try {
      const response = await axios.post('/api/google-forms-webhooks/process-all');
      return response.data;
    } catch (error) {
      console.error('Error processing all webhooks:', error);
      throw error;
    }
  },

  /**
   * Get webhooks for a specific form
   * @param {string} formId Form ID
   * @returns {Promise<Array>} List of webhooks for the form
   */
  getWebhooksForForm: async (formId) => {
    try {
      const allWebhooks = await googleFormsWebhookService.getAllWebhooks();
      return allWebhooks.filter(webhook => webhook.formId === formId);
    } catch (error) {
      console.error('Error fetching webhooks for form:', error);
      throw error;
    }
  }
};

export default googleFormsWebhookService;