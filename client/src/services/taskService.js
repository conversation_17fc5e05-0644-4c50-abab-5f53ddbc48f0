import axios from 'axios';

/**
 * Task Service
 * Provides methods for interacting with the task management API
 */
const taskService = {
  /**
   * Get all tasks
   * @returns {Promise<Array>} List of tasks
   */
  getAllTasks: async () => {
    try {
      const response = await axios.get('/api/tasks');
      return response.data;
    } catch (error) {
      console.error('Error getting tasks:', error);
      throw error;
    }
  },

  /**
   * Get tasks by filter
   * @param {Object} filters - Filter parameters
   * @returns {Promise<Array>} Filtered list of tasks
   */
  getTasksByFilter: async (filters = {}) => {
    try {
      const response = await axios.get('/api/tasks/filter', { params: filters });
      return response.data;
    } catch (error) {
      console.error('Error getting filtered tasks:', error);
      throw error;
    }
  },

  /**
   * Get task by ID
   * @param {string} id - Task ID
   * @returns {Promise<Object>} Task details
   */
  getTaskById: async (id) => {
    try {
      const response = await axios.get(`/api/tasks/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task:', error);
      throw error;
    }
  },

  /**
   * Create a new task
   * @param {Object} taskData - Task data
   * @returns {Promise<Object>} Created task
   */
  createTask: async (taskData) => {
    try {
      const response = await axios.post('/api/tasks', taskData);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  },

  /**
   * Update a task
   * @param {string} id - Task ID
   * @param {Object} taskData - Task data
   * @returns {Promise<Object>} Updated task
   */
  updateTask: async (id, taskData) => {
    try {
      const response = await axios.put(`/api/tasks/${id}`, taskData);
      return response.data;
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  },

  /**
   * Delete a task
   * @param {string} id - Task ID
   * @returns {Promise<Object>} Response message
   */
  deleteTask: async (id) => {
    try {
      const response = await axios.delete(`/api/tasks/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  },

  /**
   * Assign a task to a user
   * @param {string} id - Task ID
   * @param {string} userId - User ID to assign to
   * @returns {Promise<Object>} Updated task
   */
  assignTask: async (id, userId) => {
    try {
      const response = await axios.put(`/api/tasks/${id}/assign`, { assignedTo: userId });
      return response.data;
    } catch (error) {
      console.error('Error assigning task:', error);
      throw error;
    }
  },

  /**
   * Update task status
   * @param {string} id - Task ID
   * @param {string} status - New status
   * @param {string} comment - Comment about the status change
   * @returns {Promise<Object>} Updated task
   */
  updateTaskStatus: async (id, status, comment) => {
    try {
      const response = await axios.put(`/api/tasks/${id}/status`, { status, comment });
      return response.data;
    } catch (error) {
      console.error('Error updating task status:', error);
      throw error;
    }
  },

  /**
   * Add a comment to a task
   * @param {string} id - Task ID
   * @param {string} text - Comment text
   * @param {boolean} isInternal - Whether the comment is internal
   * @returns {Promise<Object>} Updated task
   */
  addComment: async (id, text, isInternal = false) => {
    try {
      const response = await axios.post(`/api/tasks/${id}/comment`, { text, isInternal });
      return response.data;
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  },

  /**
   * Add an attachment to a task
   * @param {string} id - Task ID
   * @param {Object} attachmentData - Attachment data (name, url, type)
   * @returns {Promise<Object>} Updated task
   */
  addAttachment: async (id, attachmentData) => {
    try {
      const response = await axios.post(`/api/tasks/${id}/attachment`, attachmentData);
      return response.data;
    } catch (error) {
      console.error('Error adding attachment:', error);
      throw error;
    }
  },

  /**
   * Get maintenance task categories
   * @returns {Promise<Array>} List of maintenance categories
   */
  getMaintenanceCategories: async () => {
    return [
      'electrical',
      'plumbing',
      'hvac',
      'structural',
      'cleaning',
      'equipment',
      'other'
    ];
  },

  /**
   * Get maintenance task types
   * @returns {Promise<Array>} List of maintenance types
   */
  getMaintenanceTypes: async () => {
    return [
      'preventive',
      'corrective',
      'emergency',
      'inspection'
    ];
  },

  /**
   * Get task priorities
   * @returns {Promise<Array>} List of task priorities
   */
  getTaskPriorities: async () => {
    return [
      'low',
      'medium',
      'high',
      'critical'
    ];
  },

  /**
   * Get task statuses
   * @returns {Promise<Array>} List of task statuses
   */
  getTaskStatuses: async () => {
    return [
      'open',
      'in_progress',
      'on_hold',
      'completed',
      'cancelled'
    ];
  }
};

export default taskService;