import axios from 'axios';

/**
 * UniFi Network API Service
 * Handles client-side API calls to the UniFi Network endpoints
 */
const unifiNetworkService = {
  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-network/devices', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Network devices:', error);
      throw error;
    }
  },

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Device details
   */
  getDevice: async (deviceId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-network/devices/${deviceId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Network device ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Get all clients
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of clients
   */
  getClients: async (params = {}) => {
    try {
      const response = await axios.get('/api/unifi-network/clients', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Network clients:', error);
      throw error;
    }
  },

  /**
   * Get client details
   * @param {string} clientId Client ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Client details
   */
  getClient: async (clientId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-network/clients/${clientId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Network client ${clientId}:`, error);
      throw error;
    }
  },


  /**
   * Get device statistics
   * @param {string} deviceId Device ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Device statistics
   */
  getDeviceStatistics: async (deviceId, params = {}) => {
    try {
      const response = await axios.get(`/api/unifi-network/devices/${deviceId}/statistics`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Network device statistics for ${deviceId}:`, error);
      throw error;
    }
  },

  /**
   * Block client
   * @param {string} clientId Client ID
   * @returns {Promise<Object>} Response message
   */
  blockClient: async (clientId) => {
    try {
      const response = await axios.post(`/api/unifi-network/clients/${clientId}/block`);
      return response.data;
    } catch (error) {
      console.error(`Error blocking UniFi Network client ${clientId}:`, error);
      throw error;
    }
  },

  /**
   * Unblock client
   * @param {string} clientId Client ID
   * @returns {Promise<Object>} Response message
   */
  unblockClient: async (clientId) => {
    try {
      const response = await axios.post(`/api/unifi-network/clients/${clientId}/unblock`);
      return response.data;
    } catch (error) {
      console.error(`Error unblocking UniFi Network client ${clientId}:`, error);
      throw error;
    }
  },

  /**
   * Get UniFi Network configuration status
   * @returns {Promise<Object>} Configuration status
   */
  getConfig: async () => {
    try {
      const response = await axios.get('/api/unifi-network/config');
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // Configuration not found is an expected case
        return null;
      }
      console.error('Error fetching UniFi Network configuration:', error);
      throw error;
    }
  },

};

export default unifiNetworkService;
