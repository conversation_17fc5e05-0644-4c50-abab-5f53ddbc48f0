import websocketService from './websocketService';

/**
 * BMS WebSocket Service
 * Handles real-time updates for Building Management System components
 * Leverages existing websocketService infrastructure to minimize code duplication
 */
class BMSWebSocketService {
  constructor() {
    this.listeners = new Map();
    this.isInitialized = false;
    this.updateBuffer = new Map(); // Buffer updates to prevent excessive re-renders
    this.bufferDelay = 500; // ms
  }

  /**
   * Initialize BMS WebSocket service
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Ensure main websocket service is connected
      await websocketService.connect();
      
      // Register BMS-specific message handlers
      this.registerBMSHandlers();
      
      this.isInitialized = true;
      console.log('BMS WebSocket service initialized');
    } catch (error) {
      console.error('Failed to initialize BMS WebSocket service:', error);
      throw error;
    }
  }

  /**
   * Register handlers for BMS-specific messages
   */
  registerBMSHandlers() {
    // Door status updates (leveraging existing UniFi Access integration)
    websocketService.on('door-status', (data) => {
      this.handleDoorUpdate(data);
    });

    // Camera status updates (leveraging existing UniFi Protect integration)
    websocketService.on('camera-status', (data) => {
      this.handleCameraUpdate(data);
    });

    // Temperature updates (from all smart device sources: Dreo, Govee, Skyport, LG ThinQ)
    websocketService.on('temperature-updates', (data) => {
      this.handleTemperatureUpdate(data);
    });

    // HVAC status updates
    websocketService.on('hvac-status', (data) => {
      this.handleHVACUpdate(data);
    });

    // Safety asset alerts
    websocketService.on('safety-alerts', (data) => {
      this.handleSafetyAlert(data);
    });

    // WiFi status updates (leveraging UniFi Network integration)
    websocketService.on('wifi-status', (data) => {
      this.handleWiFiUpdate(data);
    });

    // Emergency alerts
    websocketService.on('emergency-alerts', (data) => {
      this.handleEmergencyAlert(data);
    });

    // System health updates
    websocketService.on('system-health', (data) => {
      this.handleSystemHealthUpdate(data);
    });
  }

  /**
   * Subscribe to specific BMS updates for a building/floor
   */
  subscribeToBMSUpdates(buildingId, floorId, options = {}) {
    const subscriptionKey = `bms_${buildingId}_${floorId}`;
    
    // Send subscription message through existing websocket
    websocketService.send({
      type: 'subscribe',
      channel: 'bms-updates',
      data: {
        buildingId,
        floorId,
        layers: options.layers || ['all'],
        updateInterval: options.updateInterval || 5000
      }
    });

    return subscriptionKey;
  }

  /**
   * Unsubscribe from BMS updates
   */
  unsubscribeFromBMSUpdates(subscriptionKey) {
    websocketService.send({
      type: 'unsubscribe',
      channel: 'bms-updates',
      subscriptionKey
    });
  }

  /**
   * Register listener for specific BMS events
   */
  onBMSUpdate(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(eventType);
      if (listeners) {
        listeners.delete(callback);
      }
    };
  }

  /**
   * Emit BMS update to registered listeners
   */
  emitBMSUpdate(eventType, data) {
    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in BMS update listener for ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * Handle door status updates (leveraging UniFi Access integration)
   */
  handleDoorUpdate(data) {
    const { doorId, status, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('door-update', {
      id: doorId,
      type: 'door',
      status: status.isLocked ? 'locked' : 'unlocked',
      lastUpdate: new Date(timestamp),
      buildingId,
      floorId,
      metadata: {
        lockRelay: status.lockRelay,
        doorSensor: status.doorSensor,
        accessAttempts: status.accessAttempts
      }
    });
  }

  /**
   * Handle camera status updates (leveraging UniFi Protect integration)
   */
  handleCameraUpdate(data) {
    const { cameraId, status, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('camera-update', {
      id: cameraId,
      type: 'camera',
      status: status.isConnected ? 'online' : 'offline',
      lastUpdate: new Date(timestamp),
      buildingId,
      floorId,
      metadata: {
        recording: status.isRecording,
        motionDetected: status.motionDetected,
        signalStrength: status.signalStrength
      }
    });
  }

  /**
   * Handle temperature updates from multiple sources (Dreo, Govee, Skyport, LG ThinQ)
   */
  handleTemperatureUpdate(data) {
    const { sensorId, source, temperature, humidity, buildingId, floorId, location, timestamp } = data;
    
    this.bufferUpdate('temperature-update', {
      id: sensorId,
      type: 'temperature_sensor',
      source: source, // dreo, govee, skyport, lgThinq, hvac
      temperature,
      humidity,
      lastUpdate: new Date(timestamp),
      buildingId,
      floorId,
      location,
      metadata: {
        trend: this.calculateTemperatureTrend(sensorId, temperature),
        alertLevel: this.getTemperatureAlertLevel(temperature)
      }
    });
  }

  /**
   * Handle HVAC status updates
   */
  handleHVACUpdate(data) {
    const { unitId, status, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('hvac-update', {
      id: unitId,
      type: 'hvac_unit',
      status: status.power ? 'running' : 'stopped',
      lastUpdate: new Date(timestamp),
      buildingId,
      floorId,
      metadata: {
        mode: status.mode,
        targetTemperature: status.targetTemperature,
        currentTemperature: status.currentTemperature,
        filterStatus: status.filterStatus,
        energyUsage: status.energyUsage
      }
    });
  }

  /**
   * Handle safety asset alerts
   */
  handleSafetyAlert(data) {
    const { assetId, alertType, severity, buildingId, floorId, timestamp } = data;
    
    // Safety alerts are emitted immediately, not buffered
    this.emitBMSUpdate('safety-alert', {
      id: assetId,
      type: 'safety_asset',
      alertType,
      severity,
      timestamp: new Date(timestamp),
      buildingId,
      floorId,
      requiresAction: severity === 'critical'
    });
  }

  /**
   * Handle WiFi status updates (leveraging UniFi Network integration)
   */
  handleWiFiUpdate(data) {
    const { apId, status, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('wifi-update', {
      id: apId,
      type: 'wifi_ap',
      status: status.state === 1 ? 'online' : 'offline',
      lastUpdate: new Date(timestamp),
      buildingId,
      floorId,
      metadata: {
        clientCount: status.clientCount,
        signalStrength: status.signalStrength,
        channel: status.channel,
        bandwidth: status.bandwidth
      }
    });
  }

  /**
   * Handle emergency alerts
   */
  handleEmergencyAlert(data) {
    const { alertType, severity, buildingId, floorId, affectedSystems, timestamp } = data;
    
    // Emergency alerts are emitted immediately
    this.emitBMSUpdate('emergency-alert', {
      alertType,
      severity,
      buildingId,
      floorId,
      affectedSystems,
      timestamp: new Date(timestamp),
      requiresImmediateAction: true
    });
  }

  /**
   * Handle system health updates
   */
  handleSystemHealthUpdate(data) {
    const { systemType, health, buildingId, timestamp } = data;
    
    this.bufferUpdate('system-health-update', {
      systemType, // 'unifi-access', 'unifi-protect', 'hvac', 'temperature-sensors'
      health, // 'healthy', 'warning', 'critical'
      buildingId,
      timestamp: new Date(timestamp),
      affectedDevices: data.affectedDevices || []
    });
  }

  /**
   * Buffer updates to prevent excessive re-renders
   */
  bufferUpdate(eventType, updateData) {
    if (!this.updateBuffer.has(eventType)) {
      this.updateBuffer.set(eventType, []);
      
      // Schedule buffer flush
      setTimeout(() => {
        const bufferedUpdates = this.updateBuffer.get(eventType) || [];
        if (bufferedUpdates.length > 0) {
          this.emitBMSUpdate(eventType, bufferedUpdates);
          this.updateBuffer.delete(eventType);
        }
      }, this.bufferDelay);
    }
    
    this.updateBuffer.get(eventType).push(updateData);
  }

  /**
   * Calculate temperature trend for a sensor
   */
  calculateTemperatureTrend(sensorId, currentTemp) {
    // Simple trend calculation - in production, this would use historical data
    const key = `temp_trend_${sensorId}`;
    const lastTemp = this[key] || currentTemp;
    this[key] = currentTemp;
    
    const diff = currentTemp - lastTemp;
    if (Math.abs(diff) < 0.5) return 'stable';
    return diff > 0 ? 'rising' : 'falling';
  }

  /**
   * Get temperature alert level
   */
  getTemperatureAlertLevel(temperature) {
    if (temperature < 60 || temperature > 85) return 'critical';
    if (temperature < 65 || temperature > 80) return 'warning';
    return 'normal';
  }

  /**
   * Send control command through WebSocket
   */
  sendControlCommand(deviceType, deviceId, command, params = {}) {
    websocketService.send({
      type: 'control-command',
      channel: 'bms-control',
      data: {
        deviceType,
        deviceId,
        command,
        params,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Request immediate status update for a device
   */
  requestStatusUpdate(deviceType, deviceId, buildingId, floorId) {
    websocketService.send({
      type: 'status-request',
      channel: 'bms-status',
      data: {
        deviceType,
        deviceId,
        buildingId,
        floorId,
        timestamp: Date.now()
      }
    });
  }

  /**
   * Set emergency mode (affects update priorities and frequencies)
   */
  setEmergencyMode(enabled, buildingId) {
    websocketService.send({
      type: 'emergency-mode',
      channel: 'bms-control',
      data: {
        enabled,
        buildingId,
        timestamp: Date.now()
      }
    });
    
    // Enhanced emergency mode with immediate system status updates
    if (enabled) {
      this.requestEmergencySystemStatus(buildingId);
    }
  }

  /**
   * Request immediate status update for all emergency-critical systems
   */
  requestEmergencySystemStatus(buildingId) {
    const emergencySystems = [
      'doors',
      'cameras', 
      'safety_assets',
      'utility_shutoffs',
      'emergency_lighting',
      'communication_systems'
    ];

    emergencySystems.forEach(systemType => {
      websocketService.send({
        type: 'emergency-status-request',
        channel: 'bms-status',
        data: {
          systemType,
          buildingId,
          priority: 'critical',
          timestamp: Date.now()
        }
      });
    });
  }

  /**
   * Subscribe to real-time layer updates for specific building/floor
   */
  subscribeToLayerUpdates(buildingId, floorId, layers = ['all']) {
    const subscriptionKey = `layer_updates_${buildingId}_${floorId}`;
    
    websocketService.send({
      type: 'subscribe',
      channel: 'layer-updates',
      data: {
        buildingId,
        floorId,
        layers: Array.isArray(layers) ? layers : ['all'],
        realTime: true,
        subscriptionKey
      }
    });

    // Register handlers for layer-specific updates
    this.registerLayerUpdateHandlers();
    
    return subscriptionKey;
  }

  /**
   * Register handlers for real-time layer updates
   */
  registerLayerUpdateHandlers() {
    // Enhanced layer update handlers
    websocketService.on('layer-electrical-update', (data) => {
      this.handleElectricalLayerUpdate(data);
    });

    websocketService.on('layer-safety-update', (data) => {
      this.handleSafetyLayerUpdate(data);
    });

    websocketService.on('layer-hvac-update', (data) => {
      this.handleHVACLayerUpdate(data);
    });

    websocketService.on('layer-wifi-update', (data) => {
      this.handleWiFiLayerUpdate(data);
    });

    websocketService.on('layer-events-update', (data) => {
      this.handleEventsLayerUpdate(data);
    });
  }

  /**
   * Handle electrical layer updates (outlet status, panel schedules, etc.)
   */
  handleElectricalLayerUpdate(data) {
    const { updateType, items, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('electrical-layer-update', {
      updateType, // 'outlets_added', 'panel_updated', 'circuit_modified'
      items,
      buildingId,
      floorId,
      timestamp: new Date(timestamp),
      layer: 'electrical'
    });
  }

  /**
   * Handle safety layer updates (inspection status, new equipment, etc.)
   */
  handleSafetyLayerUpdate(data) {
    const { updateType, assets, buildingId, floorId, timestamp } = data;
    
    // Safety updates are immediate (not buffered) for emergency response
    this.emitBMSUpdate('safety-layer-update', {
      updateType, // 'inspection_completed', 'asset_added', 'alert_triggered'
      assets,
      buildingId,
      floorId,
      timestamp: new Date(timestamp),
      layer: 'safety',
      immediate: true
    });
  }

  /**
   * Handle HVAC layer updates (temperature changes, filter status, etc.)
   */
  handleHVACLayerUpdate(data) {
    const { updateType, units, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('hvac-layer-update', {
      updateType, // 'temperature_change', 'filter_due', 'unit_status'
      units,
      buildingId,
      floorId,
      timestamp: new Date(timestamp),
      layer: 'hvac'
    });
  }

  /**
   * Handle WiFi layer updates (AP status, client counts, coverage changes)
   */
  handleWiFiLayerUpdate(data) {
    const { updateType, accessPoints, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('wifi-layer-update', {
      updateType, // 'ap_status_change', 'client_count_update', 'coverage_change'
      accessPoints,
      buildingId,
      floorId,
      timestamp: new Date(timestamp),
      layer: 'wifi'
    });
  }

  /**
   * Handle events layer updates (room usage, preparation status, etc.)
   */
  handleEventsLayerUpdate(data) {
    const { updateType, events, buildingId, floorId, timestamp } = data;
    
    this.bufferUpdate('events-layer-update', {
      updateType, // 'event_started', 'room_occupied', 'preparation_complete'
      events,
      buildingId,
      floorId,
      timestamp: new Date(timestamp),
      layer: 'events'
    });
  }

  /**
   * Get real-time layer statistics for badge counts
   */
  async getLayerCounts(buildingId, floorId, layers = []) {
    return new Promise((resolve, reject) => {
      const requestId = `count_request_${Date.now()}`;
      
      // Set up response handler
      const handleCountResponse = (data) => {
        if (data.requestId === requestId) {
          websocketService.removeEventListener('layer-counts-response', handleCountResponse);
          resolve(data.counts);
        }
      };
      
      websocketService.addEventListener('layer-counts-response', handleCountResponse);
      
      // Send count request
      websocketService.send({
        type: 'layer-counts-request',
        channel: 'bms-status',
        data: {
          requestId,
          buildingId,
          floorId,
          layers,
          timestamp: Date.now()
        }
      });
      
      // Timeout after 5 seconds
      setTimeout(() => {
        websocketService.removeEventListener('layer-counts-response', handleCountResponse);
        reject(new Error('Layer count request timeout'));
      }, 5000);
    });
  }

  /**
   * Get real-time alert counts for layer badges
   */
  async getLayerAlerts(buildingId, floorId, layers = []) {
    return new Promise((resolve, reject) => {
      const requestId = `alert_request_${Date.now()}`;
      
      const handleAlertResponse = (data) => {
        if (data.requestId === requestId) {
          websocketService.removeEventListener('layer-alerts-response', handleAlertResponse);
          resolve(data.alerts);
        }
      };
      
      websocketService.addEventListener('layer-alerts-response', handleAlertResponse);
      
      websocketService.send({
        type: 'layer-alerts-request',
        channel: 'bms-status',
        data: {
          requestId,
          buildingId,
          floorId,
          layers,
          timestamp: Date.now()
        }
      });
      
      setTimeout(() => {
        websocketService.removeEventListener('layer-alerts-response', handleAlertResponse);
        reject(new Error('Layer alert request timeout'));
      }, 5000);
    });
  }

  /**
   * Cleanup and disconnect
   */
  disconnect() {
    this.listeners.clear();
    this.updateBuffer.clear();
    this.isInitialized = false;
    // Don't disconnect main websocket as other services may be using it
  }
}

const bmsWebSocketService = new BMSWebSocketService();
export default bmsWebSocketService;