/**
 * CSF Portal Performance Utilities
 * This file contains utility functions to optimize performance, especially for mobile devices
 */

import React, { useState, useEffect, lazy, Suspense } from 'react';

/**
 * Lazy load an image with a placeholder
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Image alt text
 * @param {string} props.placeholderSrc - Placeholder image source URL
 * @param {Object} props.imgProps - Additional image props
 * @returns {JSX.Element} - Lazy loaded image component
 */
export const LazyImage = ({ src, alt, placeholderSrc, imgProps = {} }) => {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);

  useEffect(() => {
    const img = new Image();
    img.src = src;
    img.onload = () => setLoaded(true);
    img.onerror = () => setError(true);
  }, [src]);

  return (
    <img
      src={loaded ? src : placeholderSrc}
      alt={alt}
      className={`lazy-image ${loaded ? 'loaded' : ''}`}
      {...imgProps}
      style={{
        transition: 'opacity 0.3s',
        opacity: loaded ? 1 : 0.5,
        ...imgProps.style,
      }}
    />
  );
};

/**
 * Create a lazy-loaded component with a loading fallback
 * @param {Function} importFunc - Import function for the component
 * @param {JSX.Element} fallback - Fallback component to show while loading
 * @returns {React.LazyExoticComponent} - Lazy loaded component
 */
export const createLazyComponent = (importFunc, fallback = <div>Loading...</div>) => {
  const LazyComponent = lazy(importFunc);
  return (props) => (
    <Suspense fallback={fallback}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

/**
 * Hook to detect network connection status and speed
 * @returns {Object} - Network status information
 */
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState({
    online: navigator.onLine,
    effectiveType: 'unknown',
    downlink: 0,
    rtt: 0,
    saveData: false,
  });

  useEffect(() => {
    const updateNetworkStatus = () => {
      const connection = navigator.connection || 
                         navigator.mozConnection || 
                         navigator.webkitConnection;

      if (connection) {
        setNetworkStatus({
          online: navigator.onLine,
          effectiveType: connection.effectiveType || 'unknown',
          downlink: connection.downlink || 0,
          rtt: connection.rtt || 0,
          saveData: connection.saveData || false,
        });
      } else {
        setNetworkStatus({
          online: navigator.onLine,
          effectiveType: 'unknown',
          downlink: 0,
          rtt: 0,
          saveData: false,
        });
      }
    };

    // Initial update
    updateNetworkStatus();

    // Add event listeners
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Add connection change listener if available
    const connection = navigator.connection || 
                       navigator.mozConnection || 
                       navigator.webkitConnection;
    if (connection) {
      connection.addEventListener('change', updateNetworkStatus);
    }

    // Cleanup
    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
      if (connection) {
        connection.removeEventListener('change', updateNetworkStatus);
      }
    };
  }, []);

  return networkStatus;
};

/**
 * Hook to implement adaptive loading based on network conditions
 * @param {Object} options - Options for adaptive loading
 * @param {number} options.lowBandwidthThreshold - Threshold for low bandwidth in Mbps
 * @returns {Object} - Adaptive loading settings
 */
export const useAdaptiveLoading = (options = { lowBandwidthThreshold: 1.0 }) => {
  const networkStatus = useNetworkStatus();
  const [adaptiveSettings, setAdaptiveSettings] = useState({
    lowBandwidth: false,
    imageQuality: 'high',
    prefetch: true,
    offlineMode: false,
  });

  useEffect(() => {
    const { online, downlink, saveData } = networkStatus;

    setAdaptiveSettings({
      lowBandwidth: downlink < options.lowBandwidthThreshold || saveData,
      imageQuality: downlink < options.lowBandwidthThreshold || saveData ? 'low' : 'high',
      prefetch: downlink >= options.lowBandwidthThreshold && !saveData,
      offlineMode: !online,
    });
  }, [networkStatus, options.lowBandwidthThreshold]);

  return adaptiveSettings;
};

/**
 * Debounce a function call
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, wait = 300) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle a function call
 * @param {Function} func - Function to throttle
 * @param {number} limit - Limit in milliseconds
 * @returns {Function} - Throttled function
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

/**
 * Hook to optimize scroll event handling
 * @param {Function} callback - Callback function for scroll event
 * @param {Object} options - Options for scroll optimization
 * @param {boolean} options.useThrottle - Whether to use throttle instead of debounce
 * @param {number} options.delay - Delay in milliseconds
 */
export const useOptimizedScroll = (callback, options = { useThrottle: true, delay: 100 }) => {
  useEffect(() => {
    const handleScroll = options.useThrottle
      ? throttle(callback, options.delay)
      : debounce(callback, options.delay);

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [callback, options.delay, options.useThrottle]);
};

/**
 * Hook to implement virtualized list rendering
 * @param {Array} items - List items
 * @param {Object} options - Options for virtualization
 * @param {number} options.itemHeight - Height of each item in pixels
 * @param {number} options.overscan - Number of items to render outside of view
 * @returns {Object} - Virtualized list data
 */
export const useVirtualizedList = (items, options = { itemHeight: 50, overscan: 5 }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleScroll = throttle(() => {
      setScrollTop(window.scrollY);
    }, 100);

    const handleResize = debounce(() => {
      setContainerHeight(window.innerHeight);
    }, 100);

    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const { itemHeight, overscan } = options;
  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    ...item,
    index: startIndex + index,
    style: {
      position: 'absolute',
      top: (startIndex + index) * itemHeight,
      height: itemHeight,
      left: 0,
      right: 0,
    },
  }));

  return {
    visibleItems,
    totalHeight,
    startIndex,
    endIndex,
  };
};

/**
 * Hook to prefetch critical resources
 * @param {Array} resources - Array of resource URLs to prefetch
 */
export const usePrefetchResources = (resources = []) => {
  const networkStatus = useNetworkStatus();

  useEffect(() => {
    // Only prefetch if online and not on a slow connection
    if (!networkStatus.online || networkStatus.saveData || networkStatus.downlink < 1.0) {
      return;
    }

    resources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = resource;
      document.head.appendChild(link);
    });
  }, [resources, networkStatus]);
};

/**
 * Initialize performance monitoring
 */
export const initPerformanceMonitoring = () => {
  if (typeof window !== 'undefined' && 'performance' in window && 'PerformanceObserver' in window) {
    // Create performance observer for Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        // Log performance metrics
        console.log(`[Performance] ${entry.name}: ${entry.value}`);

        // Send to analytics if available
        if (window.gtag) {
          window.gtag('event', 'web_vitals', {
            event_category: 'Web Vitals',
            event_label: entry.name,
            value: Math.round(entry.value),
            non_interaction: true,
          });
        }
      });
    });

    // Observe LCP, FID, and CLS
    observer.observe({ type: 'largest-contentful-paint', buffered: true });
    observer.observe({ type: 'first-input', buffered: true });
    observer.observe({ type: 'layout-shift', buffered: true });
  }
};
