import React, { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';

// Configure axios to include credentials in requests
axios.defaults.withCredentials = true;

// Create context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Update user data
  const updateUser = (userData) => {
    if (!user) return;
    setUser({ ...user, ...userData });
  };

  // Function to fetch widget preferences and store them in localStorage
  const syncWidgetPreferences = async () => {
    try {
      const res = await axios.get('/api/users/me/widget-preferences');
      
      // Check if floating shortcut widget preferences exist
      if (res.data && res.data.floatingShortcut) {
        const { favoriteShortcuts, enabled } = res.data.floatingShortcut;
        
        // Store in localStorage
        if (favoriteShortcuts) {
          localStorage.setItem('favoriteShortcuts', JSON.stringify(favoriteShortcuts));
        }
        
        if (enabled !== undefined) {
          localStorage.setItem('floatingShortcutWidgetEnabled', enabled ? 'true' : 'false');
        }
        
        console.log('Widget preferences synced from database to localStorage');
      }
    } catch (err) {
      console.error('Error syncing widget preferences:', err);
      // Continue silently - this is not critical functionality
    }
  };

  // Load user on initial render
  useEffect(() => {
    const loadUser = async () => {
      try {
        const res = await axios.get('/api/auth/current');
        
        // If user has roles, fetch role details with permissions
        if (res.data && res.data.roles && res.data.roles.length > 0) {
          try {
            const rolesRes = await axios.get('/api/roles/details');
            // Filter roles to only include those assigned to the user
            const userRoleDetails = rolesRes.data.filter(role => 
              res.data.roles.includes(role.name)
            );
            res.data.roleDetails = userRoleDetails;
          } catch (roleErr) {
            console.error('Error loading role details:', roleErr);
            // Continue without role details
          }
        }
        
        setUser(res.data);
        
        // After user is loaded, sync widget preferences
        await syncWidgetPreferences();
        
        setLoading(false);
      } catch (err) {
        console.error('Error loading user:', err);
        setUser(null);
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  // Login with Google
  const loginWithGoogle = () => {
    window.location.href = '/api/auth/google';
  };

  // Logout
  const logout = async () => {
    try {
      await axios.get('/api/auth/logout');
      setUser(null);
    } catch (err) {
      console.error('Error logging out:', err);
      setError('Error logging out');
    }
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    if (!user) return false;
    return user.roles.includes(role);
  };

  // Check if user has admin role
  const isAdmin = () => {
    return hasRole('admin');
  };

  // Check if user has a specific permission
  const hasPermission = (permission) => {
    if (!user || !user.roles || !user.roles.length) return false;
    
    // Admin role has all permissions
    if (user.roles.includes('admin')) return true;
    
    // If user has roles but we don't have role details with permissions
    if (!user.roleDetails) return false;
    
    // Check if any of the user's roles have the required permission
    return user.roleDetails.some(role => {
      // Check for global wildcard permission
      if (role.permissions && role.permissions.includes('*')) {
        return true;
      }
      
      // Check for exact permission match
      if (role.permissions && role.permissions.includes(permission)) {
        return true;
      }
      
      // Handle hierarchical permissions (entity:level)
      if (permission.includes(':')) {
        const [entity, level] = permission.split(':');
        
        // Check for entity wildcard (entity:*)
        if (role.permissions && role.permissions.includes(`${entity}:*`)) {
          return true;
        }
        
        // Check for higher level permissions
        if (level === 'read') {
          // write, delete, and admin permissions include read
          return (role.permissions && (
            role.permissions.includes(`${entity}:write`) || 
            role.permissions.includes(`${entity}:delete`) ||
            role.permissions.includes(`${entity}:admin`)
          ));
        } else if (level === 'write') {
          // delete and admin permissions include write
          return (role.permissions && (
            role.permissions.includes(`${entity}:delete`) ||
            role.permissions.includes(`${entity}:admin`)
          ));
        } else if (level === 'delete') {
          // admin permission includes delete
          return (role.permissions && role.permissions.includes(`${entity}:admin`));
        }
      }
      
      return false;
    });
  };

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead
  
  // This function now always returns true since all integrations are active
  const hasIntegration = () => {
    return true;
  };

  // Value object to be provided to consumers
  const value = {
    user,
    loading,
    error,
    loginWithGoogle,
    logout,
    hasRole,
    isAdmin,
    hasPermission,
    hasIntegration,
    updateUser
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
