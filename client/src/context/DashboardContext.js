import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';
import { getDefaultSettings } from '../components/widgets/WidgetRegistry';

const DashboardContext = createContext();

export const useDashboard = () => useContext(DashboardContext);

export const DashboardProvider = ({ children }) => {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState(null);
  const [unsavedPreferences, setUnsavedPreferences] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Fetch dashboard preferences
  const fetchPreferences = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const res = await axios.get('/api/dashboard-preferences');
      setPreferences(res.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard preferences:', err);
      setError('Failed to load dashboard preferences');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Initial fetch
  useEffect(() => {
    fetchPreferences();
  }, [fetchPreferences]);

  // Initialize unsavedPreferences when preferences change
  useEffect(() => {
    if (preferences) {
      setUnsavedPreferences(JSON.parse(JSON.stringify(preferences)));
    }
  }, [preferences]);

  // Save dashboard preferences
  const savePreferences = async (updatedPreferences) => {
    if (!user) return;

    try {
      setLoading(true);

      // Create a deep copy of updatedPreferences
      const preferencesToSave = JSON.parse(JSON.stringify(updatedPreferences));

      // Process widgets to handle temporary IDs
      if (preferencesToSave.widgets && Array.isArray(preferencesToSave.widgets)) {
        preferencesToSave.widgets = preferencesToSave.widgets.map(widget => {
          // If the widget has a temporary ID (starts with 'temp_'), remove the _id field
          // so MongoDB can generate a valid ID
          if (widget._id && widget._id.toString().startsWith('temp_')) {
            const { _id, ...widgetWithoutId } = widget;
            return widgetWithoutId;
          }
          return widget;
        });
      }

      const res = await axios.put('/api/dashboard-preferences', preferencesToSave);
      setPreferences(res.data);
      setError(null);
      return res.data;
    } catch (err) {
      console.error('Error saving dashboard preferences:', err);
      setError('Failed to save dashboard preferences');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add a widget
  const addWidget = async (type, title, position) => {
    if (!user) return;

    // If in edit mode, update local state only
    if (isEditMode && unsavedPreferences) {
      try {
        const settings = getDefaultSettings(type);

        // Generate a temporary ID for the new widget
        const tempId = `temp_${Date.now()}`;

        // Create a new widget
        const newWidget = {
          _id: tempId,
          type,
          title,
          position,
          settings
        };

        // Add the new widget to unsavedPreferences
        const updatedPreferences = {
          ...unsavedPreferences,
          widgets: [...unsavedPreferences.widgets, newWidget]
        };

        setUnsavedPreferences(updatedPreferences);
        setHasUnsavedChanges(true);
        setError(null);
        return updatedPreferences;
      } catch (err) {
        console.error('Error adding widget:', err);
        setError('Failed to add widget');
        throw err;
      }
    } else {
      // If not in edit mode, make the API call
      try {
        setLoading(true);
        const settings = getDefaultSettings(type);
        const res = await axios.post('/api/dashboard-preferences/widgets', {
          type,
          title,
          position,
          settings
        });
        setPreferences(res.data);
        setError(null);
        return res.data;
      } catch (err) {
        console.error('Error adding widget:', err);
        setError('Failed to add widget');
        throw err;
      } finally {
        setLoading(false);
      }
    }
  };

  // Update a widget
  const updateWidget = async (widgetId, updates) => {
    if (!user) return;

    // If in edit mode, update local state only
    if (isEditMode && unsavedPreferences) {
      try {
        // Find the widget in unsavedPreferences
        const updatedWidgets = unsavedPreferences.widgets.map(widget => {
          if (widget._id.toString() === widgetId.toString()) {
            return {
              ...widget,
              ...updates
            };
          }
          return widget;
        });

        // Update unsavedPreferences
        const updatedPreferences = {
          ...unsavedPreferences,
          widgets: updatedWidgets
        };

        setUnsavedPreferences(updatedPreferences);
        setHasUnsavedChanges(true);
        setError(null);
        return updatedPreferences;
      } catch (err) {
        console.error('Error updating widget:', err);
        setError('Failed to update widget');
        throw err;
      }
    } else {
      // If not in edit mode, make the API call
      try {
        setLoading(true);
        const res = await axios.put(`/api/dashboard-preferences/widgets/${widgetId}`, updates);
        setPreferences(res.data);
        setError(null);
        return res.data;
      } catch (err) {
        console.error('Error updating widget:', err);
        setError('Failed to update widget');
        throw err;
      } finally {
        setLoading(false);
      }
    }
  };

  // Remove a widget
  const removeWidget = async (widgetId) => {
    if (!user) return;

    // If in edit mode, update local state only
    if (isEditMode && unsavedPreferences) {
      try {
        // Filter out the widget from unsavedPreferences
        const updatedWidgets = unsavedPreferences.widgets.filter(
          widget => widget._id.toString() !== widgetId.toString()
        );

        // Update unsavedPreferences
        const updatedPreferences = {
          ...unsavedPreferences,
          widgets: updatedWidgets
        };

        setUnsavedPreferences(updatedPreferences);
        setHasUnsavedChanges(true);
        setError(null);
        return updatedPreferences;
      } catch (err) {
        console.error('Error removing widget:', err);
        setError('Failed to remove widget');
        throw err;
      }
    } else {
      // If not in edit mode, make the API call
      try {
        setLoading(true);
        const res = await axios.delete(`/api/dashboard-preferences/widgets/${widgetId}`);
        setPreferences(res.data);
        setError(null);
        return res.data;
      } catch (err) {
        console.error('Error removing widget:', err);
        setError('Failed to remove widget');
        throw err;
      } finally {
        setLoading(false);
      }
    }
  };

  // Reset dashboard to default
  const resetDashboard = async () => {
    if (!user) return;

    // If in edit mode, update local state only
    if (isEditMode && unsavedPreferences) {
      try {
        // We need to fetch the default preferences from the server
        // but we won't save them yet, just update the local state
        setLoading(true);
        const res = await axios.get('/api/dashboard-preferences/default');
        setUnsavedPreferences(res.data);
        setHasUnsavedChanges(true);
        setError(null);
        setLoading(false);
        return res.data;
      } catch (err) {
        console.error('Error getting default dashboard:', err);
        setError('Failed to get default dashboard');
        setLoading(false);
        throw err;
      }
    } else {
      // If not in edit mode, make the API call to reset
      try {
        setLoading(true);
        const res = await axios.post('/api/dashboard-preferences/reset');
        setPreferences(res.data);
        setError(null);
        return res.data;
      } catch (err) {
        console.error('Error resetting dashboard:', err);
        setError('Failed to reset dashboard');
        throw err;
      } finally {
        setLoading(false);
      }
    }
  };

  // Update layout (only updates local state, doesn't save to server)
  const updateLayout = (layout) => {
    if (!user) return;

    try {
      // If unsavedPreferences is not initialized yet, initialize it from preferences
      if (!unsavedPreferences && preferences) {
        setUnsavedPreferences(JSON.parse(JSON.stringify(preferences)));
      }

      // Use preferences as a fallback if unsavedPreferences is still not available
      const currentPreferences = unsavedPreferences || preferences;
      
      // If we still don't have preferences, we can't update the layout
      if (!currentPreferences) return;

      // Create a copy of currentPreferences with updated widget positions
      const updatedPreferences = {
        ...currentPreferences,
        widgets: currentPreferences.widgets.map(widget => {
          const layoutItem = layout.find(item => item.i === widget._id.toString());
          if (layoutItem) {
            return {
              ...widget,
              position: {
                x: layoutItem.x,
                y: layoutItem.y,
                w: layoutItem.w,
                h: layoutItem.h
              }
            };
          }
          return widget;
        })
      };

      // Update local state
      setUnsavedPreferences(updatedPreferences);
      setHasUnsavedChanges(true);
    } catch (err) {
      console.error('Error updating layout:', err);
      setError('Failed to update layout');
    }
  };

  // Save dashboard changes
  const saveDashboardChanges = async () => {
    if (!user || !hasUnsavedChanges || !unsavedPreferences) return;

    try {
      setLoading(true);

      // Create a deep copy of unsavedPreferences
      const preferencesToSave = JSON.parse(JSON.stringify(unsavedPreferences));

      // Process widgets to handle temporary IDs
      if (preferencesToSave.widgets && Array.isArray(preferencesToSave.widgets)) {
        preferencesToSave.widgets = preferencesToSave.widgets.map(widget => {
          // If the widget has a temporary ID (starts with 'temp_'), remove the _id field
          // so MongoDB can generate a valid ID
          if (widget._id && widget._id.toString().startsWith('temp_')) {
            const { _id, ...widgetWithoutId } = widget;
            return widgetWithoutId;
          }
          return widget;
        });
      }

      // Save the processed preferences to the server
      const res = await axios.put('/api/dashboard-preferences', preferencesToSave);
      setPreferences(res.data);
      setHasUnsavedChanges(false);
      setError(null);
      return res.data;
    } catch (err) {
      console.error('Error saving dashboard changes:', err);
      setError('Failed to save dashboard changes');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Discard unsaved changes
  const discardChanges = () => {
    if (preferences) {
      setUnsavedPreferences(JSON.parse(JSON.stringify(preferences)));
      setHasUnsavedChanges(false);
    }
  };

  // Toggle edit mode
  const toggleEditMode = async (skipSave = false) => {
    const newEditMode = !isEditMode;

    // If exiting edit mode and there are unsaved changes, save them
    // Skip saving if skipSave is true (used when save has already been called)
    if (!newEditMode && hasUnsavedChanges && !skipSave) {
      try {
        await saveDashboardChanges();
      } catch (err) {
        console.error('Error saving dashboard changes when exiting edit mode:', err);
        // If saving fails, discard changes to prevent inconsistent state
        discardChanges();
      }
    }

    // Always update the edit mode state, regardless of whether saving succeeded
    setIsEditMode(newEditMode);
  };

  return (
    <DashboardContext.Provider
      value={{
        preferences,
        unsavedPreferences,
        loading,
        error,
        isEditMode,
        hasUnsavedChanges,
        fetchPreferences,
        savePreferences,
        addWidget,
        updateWidget,
        removeWidget,
        resetDashboard,
        updateLayout,
        toggleEditMode,
        saveDashboardChanges,
        discardChanges
      }}
    >
      {children}
    </DashboardContext.Provider>
  );
};

export default DashboardContext;
