import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';

const DebugContext = createContext();

export const useDebug = () => {
  const context = useContext(DebugContext);
  if (!context) {
    throw new Error('useDebug must be used within a DebugProvider');
  }
  return context;
};

export const DebugProvider = ({ children }) => {
  const { user } = useAuth();
  const [requests, setRequests] = useState([]);
  const [stats, setStats] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState(null);
  const [isPaused, setIsPaused] = useState(false);
  const isPausedRef = useRef(false);
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const maxRequests = 1000;

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Debug WebSocket connected');
        setIsConnected(true);
        setError(null);
        
        wsRef.current.send(JSON.stringify({
          type: 'subscribe',
          eventType: 'debug'
        }));
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          
          switch (message.type) {
            case 'welcome':
              console.log('Debug WebSocket welcome:', message);
              break;
              
            case 'event':
              if (message.eventType === 'debug') {
                const { subType, data } = message.payload;
                
                if ((subType === 'api_request' || subType === 'external_api_request') && !isPausedRef.current) {
                  setRequests(prev => {
                    const newRequests = [data, ...prev];
                    return newRequests.slice(0, maxRequests);
                  });
                }
              }
              break;
              
            case 'subscribed':
              console.log('Subscribed to:', message.eventType);
              break;
              
            case 'error':
              console.error('WebSocket error:', message.message);
              setError(message.message);
              break;
              
            case 'pong':
              break;
              
            default:
              console.log('Unknown WebSocket message:', message);
          }
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('Debug WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        
        if (!event.wasClean && user?.roles?.includes('admin')) {
          console.log('Attempting to reconnect...');
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, 3000);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Debug WebSocket error:', error);
        setError('WebSocket connection failed');
        setIsConnected(false);
      };

    } catch (err) {
      console.error('Failed to create WebSocket connection:', err);
      setError('Failed to create WebSocket connection');
    }
  }, [user]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Client disconnecting');
      wsRef.current = null;
    }
    
    setIsConnected(false);
  }, []);

  const subscribe = useCallback(() => {
    if (!user?.roles?.includes('admin')) return;
    
    connect();
  }, [connect, user]);

  const unsubscribe = useCallback(() => {
    disconnect();
  }, [disconnect]);

  const clearRequests = useCallback(async () => {
    try {
      await axios.delete('/api/debug/requests');
      setRequests([]);
    } catch (err) {
      console.error('Error clearing debug requests:', err);
      setError('Failed to clear requests');
    }
  }, []);

  const refreshStats = useCallback(async () => {
    try {
      const response = await axios.get('/api/debug/stats');
      setStats(response.data);
    } catch (err) {
      console.error('Error fetching debug stats:', err);
      setError('Failed to fetch stats');
    }
  }, []);

  const fetchInitialRequests = useCallback(async () => {
    try {
      const response = await axios.get('/api/debug/requests?limit=100');
      setRequests(response.data.requests || []);
    } catch (err) {
      console.error('Error fetching initial debug requests:', err);
      setError('Failed to fetch initial requests');
    }
  }, []);

  const togglePause = useCallback(() => {
    setIsPaused(prevState => {
      const newPausedState = !prevState;
      isPausedRef.current = newPausedState;
      return newPausedState;
    });
  }, []);

  useEffect(() => {
    if (user?.roles?.includes('admin')) {
      fetchInitialRequests();
      refreshStats();
    }
  }, [user, fetchInitialRequests, refreshStats]);

  // Keep isPausedRef in sync with isPaused state
  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  const value = {
    requests,
    stats,
    isConnected,
    error,
    isPaused,
    togglePause,
    subscribe,
    unsubscribe,
    clearRequests,
    refreshStats,
    fetchInitialRequests
  };

  return (
    <DebugContext.Provider value={value}>
      {children}
    </DebugContext.Provider>
  );
};

export default DebugContext;