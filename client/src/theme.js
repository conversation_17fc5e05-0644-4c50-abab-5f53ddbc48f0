import { createTheme } from '@mui/material/styles';

// CSF Branding Colors (Updated with better contrast)
const csfBlue = {
  main: '#1e40af', // Darker blue for better contrast
  light: '#3b82f6',
  dark: '#1e3a8a',
  contrastText: '#fff',
};

const csfGold = {
  main: '#ffb800', // Brighter gold/yellow
  light: '#ffd633',
  dark: '#cc9200',
  contrastText: '#000',
};

const brightAccents = {
  purple: '#8b5cf6',
  pink: '#ec4899',
  teal: '#14b8a6',
  orange: '#fb923c',
  green: '#10b981',
  red: '#ef4444',
};

// Create a theme instance with CSF branding
const theme = createTheme({
  palette: {
    primary: csfBlue,
    secondary: csfGold,
    background: {
      default: '#f8f9fa',
      paper: '#fff',
    },
    // Add bright accent colors to palette
    brightAccents,
  },
  typography: {
    fontFamily: [
      'Georgia',
      'Roboto',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      color: '#1f2937', // Dark gray for excellent readability
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 700,
      color: '#1f2937',
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      color: '#374151', // Slightly lighter dark gray
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      color: '#374151',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      color: '#4b5563', // Medium gray
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      color: '#4b5563',
    },
    body1: {
      color: '#6b7280', // Readable body text
    },
    body2: {
      color: '#6b7280',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 600,
          boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transform: 'translateY(-1px)',
          },
          transition: 'all 0.2s ease',
        },
        contained: {
          background: 'linear-gradient(135deg, #1e40af, #1e3a8a)',
          color: 'white',
          border: '1px solid #1e40af',
          '&:hover': {
            background: 'linear-gradient(135deg, #1e3a8a, #1d4ed8)',
            border: '1px solid #1e3a8a',
          },
        },
        outlined: {
          borderColor: '#1e40af',
          color: '#1e40af',
          borderWidth: '2px',
          '&:hover': {
            borderWidth: '2px',
            borderColor: '#1e3a8a',
            color: '#1e3a8a',
            backgroundColor: 'rgba(30, 64, 175, 0.04)',
          },
        },
        text: {
          color: '#1e40af',
          '&:hover': {
            backgroundColor: 'rgba(30, 64, 175, 0.04)',
            color: '#1e3a8a',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          background: 'linear-gradient(rgb(98, 161, 255), rgb(23, 101, 217))',
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        root: {
          '& .MuiDialog-container': {
            paddingLeft: '300px', // Offset for sidebar
            '@media (max-width: 960px)': {
              paddingLeft: 0,
            },
          },
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          marginTop: '8px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
          borderRadius: 8,
        },
      },
    },
    MuiPopover: {
      styleOverrides: {
        root: {
          '& .MuiPopover-paper': {
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            borderRadius: 8,
          },
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        html: {
          scrollbarGutter: 'stable', // Prevent content shift when scrollbars appear/disappear
        },
        body: {
          scrollbarWidth: 'thin', // For Firefox
          scrollbarColor: '#0066ff #f1f1f1', // For Firefox
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#0066ff',
            borderRadius: '4px',
            '&:hover': {
              background: '#0052cc',
            },
          },
        },
      },
    },
  },
});

export default theme;
