import { useCallback, useRef, useState } from 'react';

/**
 * useLongPress Hook
 * Provides long-press gesture detection for touch and mouse interactions
 * Integrates with FloorPlan context menu system
 */
const useLongPress = (
  onLongPress,
  onClick,
  { 
    shouldPreventDefault = true, 
    delay = 500,
    moveThreshold = 10 
  } = {}
) => {
  const [longPressTriggered, setLongPressTriggered] = useState(false);
  const timeout = useRef();
  const target = useRef();
  const startPos = useRef({ x: 0, y: 0 });

  const start = useCallback(
    (event) => {
      if (shouldPreventDefault && event.target) {
        event.target.addEventListener('touchend', preventDefault, {
          passive: false
        });
      }
      
      // Store starting position for movement detection
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const clientY = event.touches ? event.touches[0].clientY : event.clientY;
      startPos.current = { x: clientX, y: clientY };
      
      target.current = event.target;
      
      timeout.current = setTimeout(() => {
        if (onLongPress) {
          onLongPress(event);
          setLongPressTriggered(true);
        }
      }, delay);
    },
    [onLongPress, delay, shouldPreventDefault]
  );

  const clear = useCallback(
    (event, shouldTriggerClick = true) => {
      timeout.current && clearTimeout(timeout.current);
      shouldTriggerClick && !longPressTriggered && onClick && onClick(event);
      setLongPressTriggered(false);
      
      if (shouldPreventDefault && target.current) {
        target.current.removeEventListener('touchend', preventDefault);
      }
    },
    [shouldPreventDefault, onClick, longPressTriggered]
  );

  const move = useCallback(
    (event) => {
      // Check if the pointer has moved beyond threshold
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const clientY = event.touches ? event.touches[0].clientY : event.clientY;
      
      const deltaX = Math.abs(clientX - startPos.current.x);
      const deltaY = Math.abs(clientY - startPos.current.y);
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      
      if (distance > moveThreshold) {
        // Movement detected, cancel long press
        if (timeout.current) {
          clearTimeout(timeout.current);
          timeout.current = null;
        }
      }
    },
    [moveThreshold]
  );

  const preventDefault = useCallback((event) => {
    if (!event.defaultPrevented) {
      event.preventDefault();
    }
  }, []);

  return {
    onMouseDown: (e) => start(e),
    onTouchStart: (e) => start(e),
    onMouseUp: (e) => clear(e),
    onMouseLeave: (e) => clear(e, false),
    onTouchEnd: (e) => clear(e),
    onMouseMove: (e) => move(e),
    onTouchMove: (e) => move(e)
  };
};

export default useLongPress;