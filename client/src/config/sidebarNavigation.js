import {
  Dashboard as DashboardIcon,
  Link as ShortcutIcon,
  Folder as FolderIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  CalendarMonth as GoogleCalendarIcon,
  Description as GoogleFormsIcon,
  ContactPage as StaffDirectoryIcon,
  HelpOutline as HelpIcon,
  ConfirmationNumber as TicketIcon,
  Assignment as TaskManagementIcon,
  HomeWork as BuildingManagementIcon,
  Article as ArticleIcon,
  Note as NotesIcon,
  Inventory2 as AssetsIcon,
  MeetingRoom as RoomBookingIcon,
  Lock as AccessControlIcon,
  Map as FloorPlanIcon,
  Thermostat as HVACIcon,
  EmojiObjects as LightingIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';

// Core navigation items - simplified and organized by importance
export const coreNavItems = [
  {
    id: 'dashboard',
    text: 'Dashboard',
    icon: DashboardIcon,
    path: '/dashboard',
    description: 'Your personalized dashboard'
  },
  {
    id: 'staff-directory',
    text: 'Staff Directory',
    icon: StaffDirectoryIcon,
    path: '/staff-directory',
    permission: 'staffDirectory:read',
    description: 'Find and connect with staff members'
  },
  {
    id: 'tickets',
    text: 'Help Tickets',
    icon: TicketIcon,
    path: '/tickets',
    permission: 'tickets:read',
    description: 'Submit and track support requests'
  },
  {
    id: 'tasks',
    text: 'Tasks',
    icon: TaskManagementIcon,
    path: '/tasks',
    permission: 'tasks:read',
    description: 'Manage your tasks and to-dos'
  },
];

// Frequently used items
export const quickAccessItems = [
  {
    id: 'shortcuts',
    text: 'Quick Links',
    icon: ShortcutIcon,
    path: '/shortcuts',
    permission: 'shortcuts:read',
    description: 'Your frequently used links'
  },
  {
    id: 'news',
    text: 'News & Updates',
    icon: ArticleIcon,
    path: '/news',
    permission: 'news:read',
    description: 'Latest announcements'
  },
  {
    id: 'notes',
    text: 'Notes',
    icon: NotesIcon,
    path: '/notes',
    permission: 'notes:read',
    description: 'Personal notes and reminders'
  },
];

// Google Services - grouped for simplicity
export const googleServicesItems = [
  {
    id: 'drive',
    text: 'Drive Files',
    icon: FolderIcon,
    path: '/drive',
    permission: 'googleDrive:read',
    description: 'Access your Google Drive files'
  },
  {
    id: 'google-calendar',
    text: 'Calendar',
    icon: GoogleCalendarIcon,
    path: '/google-calendar',
    permission: 'googleCalendar:read',
    description: 'View and manage calendars'
  },
  {
    id: 'google-forms',
    text: 'Forms',
    icon: GoogleFormsIcon,
    path: '/google-forms',
    permission: 'googleForms:read',
    description: 'Create and manage forms'
  },
];

// Building & Facility Management - grouped together
export const buildingManagementItems = [
  {
    id: 'building-overview',
    text: 'Building Overview',
    icon: BuildingManagementIcon,
    path: '/building-management',
    permission: 'buildingManagement:read',
    description: 'Facility overview and status'
  },
  {
    id: 'room-booking',
    text: 'Room Booking',
    icon: RoomBookingIcon,
    path: '/room-booking',
    permission: 'roomBooking:read',
    description: 'Reserve rooms and spaces'
  },
  {
    id: 'access-control',
    text: 'Access Control',
    icon: AccessControlIcon,
    path: '/access-control',
    permission: 'accessControl:read',
    description: 'Manage building access and security'
  },
  {
    id: 'floor-plans',
    text: 'Floor Plans',
    icon: FloorPlanIcon,
    path: '/building-management/floor-plans',
    permission: 'buildingManagement:read',
    description: 'Interactive floor plans'
  },
];

// Advanced features - hidden by default, accessible through menu
export const advancedItems = [
  {
    id: 'people',
    text: 'People Directory',
    icon: PeopleIcon,
    path: '/people',
    permission: 'people:read',
    description: 'Extended directory features'
  },
  {
    id: 'assets',
    text: 'Asset Management',
    icon: AssetsIcon,
    path: '/assets',
    permission: 'assets:read',
    description: 'Track and manage assets'
  },
];

// Help & Support
export const helpItems = [
  {
    id: 'help',
    text: 'Help Center',
    icon: HelpIcon,
    path: '/help',
    permission: 'help:read',
    description: 'Get help and support'
  },
];

// Admin items - only shown to admins
export const adminItems = [
  {
    id: 'admin-users',
    text: 'Manage Users',
    icon: PeopleIcon,
    path: '/admin/users',
    permission: 'users:admin'
  },
  {
    id: 'admin-roles',
    text: 'Manage Roles',
    icon: SettingsIcon,
    path: '/admin/roles',
    permission: 'roles:admin'
  },
  {
    id: 'admin-shortcuts',
    text: 'Manage Shortcuts',
    icon: ShortcutIcon,
    path: '/admin/shortcuts',
    permission: 'shortcuts:admin'
  },
  {
    id: 'admin-building',
    text: 'Building Settings',
    icon: BuildingManagementIcon,
    path: '/admin/building-management',
    permission: 'buildingManagement:admin'
  },
];

// Function to get all navigation items for a user based on permissions
export const getNavigationItems = (hasPermission, isAdmin, showAdvanced = false) => {
  const filterByPermission = (items) => {
    return items.filter(item => {
      if (!item.permission) return true;
      if (isAdmin()) return true;
      return hasPermission(item.permission);
    });
  };

  return {
    core: filterByPermission(coreNavItems),
    quickAccess: filterByPermission(quickAccessItems),
    googleServices: filterByPermission(googleServicesItems),
    buildingManagement: filterByPermission(buildingManagementItems),
    advanced: showAdvanced ? filterByPermission(advancedItems) : [],
    help: filterByPermission(helpItems),
    admin: isAdmin() ? filterByPermission(adminItems) : [],
  };
};

// Simple categorization for better organization
export const categories = {
  'Quick Access': quickAccessItems,
  'Google Services': googleServicesItems,
  'Building & Facilities': buildingManagementItems,
};