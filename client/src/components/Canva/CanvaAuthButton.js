import React, { useState, useEffect } from 'react';
import { Button, CircularProgress, Typography, Box } from '@mui/material';
import canvaService from '../../services/canvaService';

const CanvaAuthButton = ({ onAuthStatusChange }) => {
  const [loading, setLoading] = useState(false);
  const [authStatus, setAuthStatus] = useState({
    authenticated: false,
    message: '',
    authUrl: null
  });

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  // Function to check if user is authenticated with Canva
  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      const response = await canvaService.checkAuth();
      const newAuthStatus = {
        authenticated: response.authenticated,
        message: response.message,
        authUrl: null
      };
      setAuthStatus(newAuthStatus);
      
      // Notify parent component of authentication status change
      if (onAuthStatusChange) {
        onAuthStatusChange(newAuthStatus.authenticated);
      }
    } catch (error) {
      const newAuthStatus = {
        authenticated: false,
        message: error.response?.data?.message || 'Error checking authentication status',
        authUrl: error.response?.data?.authUrl || null
      };
      setAuthStatus(newAuthStatus);
      
      // Notify parent component of authentication status change
      if (onAuthStatusChange) {
        onAuthStatusChange(newAuthStatus.authenticated);
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to initiate the OAuth flow
  const handleAuth = async () => {
    try {
      setLoading(true);
      const response = await canvaService.getAuthUrl();
      // Redirect to Canva authorization page
      window.location.href = response.authUrl;
    } catch (error) {
      console.error('Error getting auth URL:', error);
      setAuthStatus({
        ...authStatus,
        message: error.response?.data?.message || 'Error initiating authentication'
      });
      setLoading(false);
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      {loading ? (
        <CircularProgress size={24} />
      ) : authStatus.authenticated ? (
        <Box>
          <Typography variant="body1" color="success.main" sx={{ mb: 1 }}>
            ✅ {authStatus.message}
          </Typography>
          <Button 
            variant="outlined" 
            color="primary" 
            onClick={checkAuthStatus}
          >
            Refresh Status
          </Button>
        </Box>
      ) : (
        <Box>
          <Typography variant="body1" color="error.main" sx={{ mb: 1 }}>
            {authStatus.message}
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleAuth}
            startIcon={<img src="/canva-logo.svg" alt="Canva" height="20" />}
          >
            Connect with Canva
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default CanvaAuthButton;