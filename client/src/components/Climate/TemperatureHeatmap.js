import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Slider,
  Grid,
  Alert,
  CircularProgress,
  Tooltip,
  Card,
  CardContent
} from '@mui/material';
import {
  ThermostatAuto as ThermostatIcon,
  AcUnit as ColdIcon,
  Whatshot as HotIcon,
  CheckCircle as ComfortIcon,
  Timeline as TrendIcon
} from '@mui/icons-material';
import hvacService from '../../services/hvacService';

/**
 * TemperatureHeatmap Component
 * Visualizes temperature data across a floor plan with color-coded overlays
 * Part of Phase 4 - Climate heatmap + HVAC service tracking
 */
const TemperatureHeatmap = ({ 
  buildingId, 
  floorId, 
  width = 800, 
  height = 600,
  onTemperatureClick = null,
  showControls = true,
  autoRefresh = false
}) => {
  const [temperatureData, setTemperatureData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [heatmapEnabled, setHeatmapEnabled] = useState(true);
  const [temperatureRange, setTemperatureRange] = useState([60, 85]);
  const [colorScheme, setColorScheme] = useState('thermal');
  const [timeWindow, setTimeWindow] = useState('now');
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Auto-refresh effect
  useEffect(() => {
    let timer = null;
    if (autoRefresh && refreshInterval > 0) {
      timer = setInterval(() => {
        loadTemperatureData();
      }, refreshInterval * 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [autoRefresh, refreshInterval, buildingId, floorId]);

  // Load temperature data when component mounts or props change
  useEffect(() => {
    loadTemperatureData();
  }, [buildingId, floorId]);

  const loadTemperatureData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await hvacService.getTemperatureData(buildingId, floorId);
      setTemperatureData(data);
    } catch (err) {
      console.error('Error loading temperature data:', err);
      setError('Failed to load temperature data');
    } finally {
      setLoading(false);
    }
  };

  // Calculate temperature analysis
  const temperatureAnalysis = useMemo(() => {
    return hvacService.analyzeTemperatureData(temperatureData);
  }, [temperatureData]);

  // Color scheme definitions
  const getColorSchemes = () => ({
    thermal: {
      name: 'Thermal',
      colors: ['#0066cc', '#0099ff', '#00ccff', '#33ff99', '#ffff00', '#ff9900', '#ff3300', '#cc0000']
    },
    comfort: {
      name: 'Comfort Zone',
      colors: ['#2196f3', '#81c784', '#4caf50', '#8bc34a', '#ffeb3b', '#ff9800', '#f44336', '#d32f2f']
    },
    grayscale: {
      name: 'Grayscale',
      colors: ['#000000', '#404040', '#808080', '#a0a0a0', '#c0c0c0', '#e0e0e0', '#f0f0f0', '#ffffff']
    }
  });

  // Get color for temperature value
  const getTemperatureColor = (temperature) => {
    if (!temperature || temperature < temperatureRange[0] || temperature > temperatureRange[1]) {
      return 'rgba(128, 128, 128, 0.3)';
    }

    const schemes = getColorSchemes();
    const scheme = schemes[colorScheme];
    const colors = scheme.colors;
    
    const normalizedTemp = (temperature - temperatureRange[0]) / (temperatureRange[1] - temperatureRange[0]);
    const colorIndex = Math.floor(normalizedTemp * (colors.length - 1));
    const clampedIndex = Math.max(0, Math.min(colors.length - 1, colorIndex));
    
    return colors[clampedIndex] + '80'; // Add transparency
  };

  // Generate heatmap gradient overlay
  const generateHeatmapOverlay = () => {
    if (!heatmapEnabled || temperatureData.length === 0) return null;

    // Create SVG gradient stops for smooth interpolation
    const gridSize = 20; // Resolution of the heatmap grid
    const points = [];

    // Generate interpolated temperature grid
    for (let x = 0; x < width; x += gridSize) {
      for (let y = 0; y < height; y += gridSize) {
        // Find nearest temperature readings and interpolate
        const distances = temperatureData.map(reading => {
          const dx = (reading.x || 50) * width / 100 - x;
          const dy = (reading.y || 50) * height / 100 - y;
          return {
            distance: Math.sqrt(dx * dx + dy * dy),
            temperature: reading.currentTemperature
          };
        });

        // Inverse distance weighting
        let weightedTemp = 0;
        let totalWeight = 0;
        
        distances.forEach(({ distance, temperature }) => {
          const weight = distance === 0 ? 1000 : 1 / (distance + 1);
          weightedTemp += temperature * weight;
          totalWeight += weight;
        });

        if (totalWeight > 0) {
          points.push({
            x,
            y,
            temperature: weightedTemp / totalWeight
          });
        }
      }
    }

    return (
      <svg
        width={width}
        height={height}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 1
        }}
      >
        <defs>
          <radialGradient id="temperatureGradient">
            <stop offset="0%" stopColor="rgba(255,0,0,0.6)" />
            <stop offset="100%" stopColor="rgba(255,0,0,0.1)" />
          </radialGradient>
        </defs>
        {points.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r={gridSize}
            fill={getTemperatureColor(point.temperature)}
            style={{ mixBlendMode: 'multiply' }}
          />
        ))}
      </svg>
    );
  };

  const renderTemperatureSensors = () => {
    return temperatureData.map((sensor, index) => {
      const x = (sensor.x || 50) * width / 100;
      const y = (sensor.y || 50) * height / 100;
      const temp = sensor.currentTemperature;
      
      let statusIcon = <ThermostatIcon />;
      let statusColor = 'default';
      
      if (temp < 65) {
        statusIcon = <ColdIcon />;
        statusColor = 'info';
      } else if (temp > 78) {
        statusIcon = <HotIcon />;
        statusColor = 'error';
      } else {
        statusIcon = <ComfortIcon />;
        statusColor = 'success';
      }

      return (
        <Tooltip
          key={sensor.deviceId || index}
          title={
            <Box>
              <Typography variant="subtitle2">{sensor.name}</Typography>
              <Typography variant="body2">
                Temperature: {temp}°F
                {sensor.humidity && ` | Humidity: ${sensor.humidity}%`}
              </Typography>
              <Typography variant="caption">
                {sensor.room} | {sensor.source}
              </Typography>
            </Box>
          }
        >
          <Box
            sx={{
              position: 'absolute',
              left: x - 15,
              top: y - 15,
              width: 30,
              height: 30,
              backgroundColor: 'white',
              borderRadius: '50%',
              border: 2,
              borderColor: statusColor === 'default' ? '#ccc' : 
                          statusColor === 'success' ? '#4caf50' :
                          statusColor === 'info' ? '#2196f3' : '#f44336',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: onTemperatureClick ? 'pointer' : 'default',
              boxShadow: 2,
              zIndex: 2,
              fontSize: '14px'
            }}
            onClick={() => onTemperatureClick && onTemperatureClick(sensor)}
          >
            {statusIcon}
          </Box>
        </Tooltip>
      );
    });
  };

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Temperature Heatmap Controls
        </Typography>
        
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <FormControlLabel
              control={
                <Switch
                  checked={heatmapEnabled}
                  onChange={(e) => setHeatmapEnabled(e.target.checked)}
                />
              }
              label="Show Heatmap"
            />
          </Grid>
          
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Color Scheme</InputLabel>
              <Select
                value={colorScheme}
                onChange={(e) => setColorScheme(e.target.value)}
                label="Color Scheme"
              >
                {Object.entries(getColorSchemes()).map(([key, scheme]) => (
                  <MenuItem key={key} value={key}>
                    {scheme.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Time Window</InputLabel>
              <Select
                value={timeWindow}
                onChange={(e) => setTimeWindow(e.target.value)}
                label="Time Window"
              >
                <MenuItem value="now">Current</MenuItem>
                <MenuItem value="1h">1 Hour Avg</MenuItem>
                <MenuItem value="24h">24 Hour Avg</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={3}>
            <Typography gutterBottom>
              Temperature Range: {temperatureRange[0]}°F - {temperatureRange[1]}°F
            </Typography>
            <Slider
              value={temperatureRange}
              onChange={(e, value) => setTemperatureRange(value)}
              valueLabelDisplay="auto"
              min={50}
              max={100}
              step={1}
              marks={[
                { value: 60, label: '60°F' },
                { value: 70, label: '70°F' },
                { value: 80, label: '80°F' },
                { value: 90, label: '90°F' }
              ]}
            />
          </Grid>
        </Grid>
      </Paper>
    );
  };

  const renderAnalysisSummary = () => {
    if (!temperatureAnalysis.average) return null;

    return (
      <Paper sx={{ p: 2, mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Temperature Analysis
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <ThermostatIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{temperatureAnalysis.average}°F</Typography>
                <Typography variant="caption">Average</Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <ComfortIcon color="success" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{temperatureAnalysis.comfortZones.length}</Typography>
                <Typography variant="caption">Comfort Zones</Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <HotIcon color="error" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{temperatureAnalysis.hotSpots.length}</Typography>
                <Typography variant="caption">Hot Spots</Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <ColdIcon color="info" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{temperatureAnalysis.coldSpots.length}</Typography>
                <Typography variant="caption">Cold Spots</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading temperature data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {renderControls()}
      
      <Paper sx={{ position: 'relative', width, height, overflow: 'hidden' }}>
        {/* Background overlay for better visibility */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(240, 240, 240, 0.3)',
            zIndex: 0
          }}
        />
        
        {/* Heatmap overlay */}
        {generateHeatmapOverlay()}
        
        {/* Temperature sensors */}
        {renderTemperatureSensors()}
        
        {/* Legend */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 10,
            right: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            p: 1,
            borderRadius: 1,
            zIndex: 3
          }}
        >
          <Typography variant="caption" display="block">
            Temperature Legend
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
            <ColdIcon color="info" fontSize="small" />
            <Typography variant="caption">Cold (&lt;65°F)</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ComfortIcon color="success" fontSize="small" />
            <Typography variant="caption">Comfort (65-78°F)</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <HotIcon color="error" fontSize="small" />
            <Typography variant="caption">Hot (&gt;78°F)</Typography>
          </Box>
        </Box>
        
        {/* Data summary */}
        <Box
          sx={{
            position: 'absolute',
            top: 10,
            left: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            p: 1,
            borderRadius: 1,
            zIndex: 3
          }}
        >
          <Typography variant="caption">
            {temperatureData.length} sensors • Range: {temperatureAnalysis.min}°F - {temperatureAnalysis.max}°F
          </Typography>
        </Box>
      </Paper>
      
      {renderAnalysisSummary()}
    </Box>
  );
};

export default TemperatureHeatmap;