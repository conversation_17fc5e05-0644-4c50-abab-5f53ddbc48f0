import React from 'react';
import { 
  Box, 
  Typography, 
  Collapse,
  Button
} from '@mui/material';
import { Link, useLocation } from 'react-router-dom';
import {
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Apps as AppsIcon,
  HomeWork as BuildingIcon
} from '@mui/icons-material';

const SimplifiedSidebar = ({ navStructure, categoryOpen, setCategoryOpen, showAdvanced, setShowAdvanced }) => {
  const location = useLocation();

  return (
    <Box sx={{ padding: '1rem 0', flex: 1, overflowY: 'auto' }}>
      
      {/* Core Navigation Items - Always Visible */}
      {navStructure.core.map((item) => {
        const isActive = location.pathname === item.path;
        return (
          <Box 
            key={item.id}
            component={Link}
            to={item.path}
            sx={{
              display: 'flex',
              alignItems: 'center',
              padding: '1rem 1.5rem',
              margin: '0.5rem 1rem',
              borderRadius: '12px',
              color: isActive ? '#2563eb' : '#4a5568',
              background: isActive ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
              textDecoration: 'none',
              fontSize: '0.95rem',
              fontWeight: 500,
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(37, 99, 235, 0.1)',
                color: '#2563eb',
                transform: 'translateX(5px)'
              }
            }}
          >
            <item.icon sx={{ fontSize: '1.25rem' }} />
            <Typography sx={{ ml: 2, fontSize: 'inherit' }}>{item.text}</Typography>
          </Box>
        );
      })}
      
      {/* Quick Access Section */}
      {navStructure.quickAccess.length > 0 && (
        <Box sx={{ margin: '0.5rem 1rem', borderRadius: '12px', overflow: 'hidden' }}>
          <Box 
            onClick={(e) => { e.stopPropagation(); setCategoryOpen(prev => ({ ...prev, quickAccess: !prev.quickAccess })); }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              padding: '1rem 1.5rem',
              color: '#4a5568',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(37, 99, 235, 0.1)',
                color: '#2563eb',
                transform: 'translateX(5px)'
              }
            }}
          >
            <AppsIcon sx={{ mr: 2, fontSize: '1.25rem' }} />
            <Typography sx={{ flexGrow: 1, fontSize: '0.95rem', fontWeight: 500 }}>Quick Access</Typography>
            {categoryOpen.quickAccess ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </Box>
          <Collapse in={!!categoryOpen.quickAccess} timeout="auto" unmountOnExit>
            <Box sx={{ pl: 2 }}>
              {navStructure.quickAccess.map((item) => {
                const isActive = location.pathname === item.path;
                return (
                  <Box 
                    key={item.id}
                    component={Link}
                    to={item.path}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.75rem 1rem',
                      margin: '0.25rem 0.5rem',
                      borderRadius: '8px',
                      color: isActive ? '#2563eb' : '#4a5568',
                      background: isActive ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                      opacity: isActive ? 1 : 0.8,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(37, 99, 235, 0.1)',
                        color: '#2563eb',
                        transform: 'translateX(3px)'
                      }
                    }}
                  >
                    <item.icon sx={{ fontSize: '1.1rem' }} />
                    <Typography sx={{ ml: 2, fontSize: 'inherit' }}>{item.text}</Typography>
                  </Box>
                );
              })}
            </Box>
          </Collapse>
        </Box>
      )}
      
      {/* Building & Facilities Section */}
      {navStructure.buildingManagement && navStructure.buildingManagement.length > 0 && (
        <Box sx={{ margin: '0.5rem 1rem', borderRadius: '12px', overflow: 'hidden' }}>
          <Box 
            onClick={(e) => { e.stopPropagation(); setCategoryOpen(prev => ({ ...prev, building: !prev.building })); }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              padding: '1rem 1.5rem',
              color: '#4a5568',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(37, 99, 235, 0.1)',
                color: '#2563eb',
                transform: 'translateX(5px)'
              }
            }}
          >
            <BuildingIcon sx={{ mr: 2, fontSize: '1.25rem' }} />
            <Typography sx={{ flexGrow: 1, fontSize: '0.95rem', fontWeight: 500 }}>Building & Facilities</Typography>
            {categoryOpen.building ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </Box>
          <Collapse in={!!categoryOpen.building} timeout="auto" unmountOnExit>
            <Box sx={{ pl: 2 }}>
              {navStructure.buildingManagement.map((item) => {
                const isActive = location.pathname === item.path;
                return (
                  <Box 
                    key={item.id}
                    component={Link}
                    to={item.path}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.75rem 1rem',
                      margin: '0.25rem 0.5rem',
                      borderRadius: '8px',
                      color: isActive ? '#2563eb' : '#4a5568',
                      background: isActive ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                      opacity: isActive ? 1 : 0.8,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(37, 99, 235, 0.1)',
                        color: '#2563eb',
                        transform: 'translateX(3px)'
                      }
                    }}
                  >
                    <item.icon sx={{ fontSize: '1.1rem' }} />
                    <Typography sx={{ ml: 2, fontSize: 'inherit' }}>{item.text}</Typography>
                  </Box>
                );
              })}
            </Box>
          </Collapse>
        </Box>
      )}
      
      {/* Google Services Section */}
      {navStructure.googleServices && navStructure.googleServices.length > 0 && (
        <Box sx={{ margin: '0.5rem 1rem', borderRadius: '12px', overflow: 'hidden' }}>
          <Box 
            onClick={(e) => { e.stopPropagation(); setCategoryOpen(prev => ({ ...prev, google: !prev.google })); }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              padding: '1rem 1.5rem',
              color: '#4a5568',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(37, 99, 235, 0.1)',
                color: '#2563eb',
                transform: 'translateX(5px)'
              }
            }}
          >
            <GoogleCalendarIcon sx={{ mr: 2, fontSize: '1.25rem' }} />
            <Typography sx={{ flexGrow: 1, fontSize: '0.95rem', fontWeight: 500 }}>Google Services</Typography>
            {categoryOpen.google ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </Box>
          <Collapse in={!!categoryOpen.google} timeout="auto" unmountOnExit>
            <Box sx={{ pl: 2 }}>
              {navStructure.googleServices.map((item) => {
                const isActive = location.pathname === item.path;
                return (
                  <Box 
                    key={item.id}
                    component={Link}
                    to={item.path}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.75rem 1rem',
                      margin: '0.25rem 0.5rem',
                      borderRadius: '8px',
                      color: isActive ? '#2563eb' : '#4a5568',
                      background: isActive ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                      opacity: isActive ? 1 : 0.8,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(37, 99, 235, 0.1)',
                        color: '#2563eb',
                        transform: 'translateX(3px)'
                      }
                    }}
                  >
                    <item.icon sx={{ fontSize: '1.1rem' }} />
                    <Typography sx={{ ml: 2, fontSize: 'inherit' }}>{item.text}</Typography>
                  </Box>
                );
              })}
            </Box>
          </Collapse>
        </Box>
      )}
      
      {/* Advanced Features - Toggle for Power Users */}
      {navStructure.advanced && navStructure.advanced.length > 0 && (
        <Box sx={{ margin: '0.5rem 1rem' }}>
          <Button
            onClick={() => setShowAdvanced(!showAdvanced)}
            size="small"
            sx={{ 
              color: '#718096',
              fontSize: '0.75rem',
              textTransform: 'none'
            }}
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Features
          </Button>
          {showAdvanced && (
            <Box sx={{ mt: 1 }}>
              {navStructure.advanced.map((item) => {
                const isActive = location.pathname === item.path;
                return (
                  <Box 
                    key={item.id}
                    component={Link}
                    to={item.path}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.75rem 1rem',
                      margin: '0.25rem 0',
                      borderRadius: '8px',
                      color: isActive ? '#2563eb' : '#4a5568',
                      background: isActive ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                      opacity: isActive ? 1 : 0.8,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(37, 99, 235, 0.1)',
                        color: '#2563eb',
                        transform: 'translateX(3px)'
                      }
                    }}
                  >
                    <item.icon sx={{ fontSize: '1.1rem' }} />
                    <Typography sx={{ ml: 2, fontSize: 'inherit' }}>{item.text}</Typography>
                  </Box>
                );
              })}
            </Box>
          )}
        </Box>
      )}
      
      {/* Admin Section - Only for Admins */}
      {user && navStructure.admin && navStructure.admin.length > 0 && isAdmin() && (
        <Box sx={{ 
          padding: '1.5rem 1rem 1rem 1rem',
          borderTop: '1px solid rgba(0, 0, 0, 0.05)',
          mt: 2
        }}>
          <Typography variant="subtitle2" sx={{ 
            color: '#718096', 
            fontSize: '0.75rem', 
            fontWeight: 600, 
            textTransform: 'uppercase', 
            letterSpacing: '0.1em',
            mb: 1.5 
          }}>
            Administration
          </Typography>
          
          <Box 
            onClick={(e) => { e.stopPropagation(); setAdminOpenSection(!adminOpenSection); }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              padding: '1rem',
              margin: '0.5rem 0',
              borderRadius: '8px',
              color: '#4a5568',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(37, 99, 235, 0.1)',
                color: '#2563eb',
                transform: 'translateX(3px)'
              }
            }}
          >
            <AdminIcon sx={{ mr: 2, fontSize: '1.25rem' }} />
            <Typography sx={{ flexGrow: 1, fontSize: '0.875rem', fontWeight: 500 }}>Admin Tools</Typography>
            {adminOpenSection ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </Box>
          
          <Collapse in={adminOpenSection} timeout="auto" unmountOnExit>
            <Box sx={{ pl: 1, mt: 1 }}>
              {navStructure.admin.map((item) => {
                const isActive = location.pathname === item.path;
                
                return (
                  <Box 
                    key={item.id}
                    component={Link}
                    to={item.path}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.75rem 1rem',
                      margin: '0.25rem 0',
                      borderRadius: '8px',
                      color: isActive ? '#2563eb' : '#4a5568',
                      background: isActive ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                      opacity: isActive ? 1 : 0.8,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(37, 99, 235, 0.1)',
                        color: '#2563eb',
                        transform: 'translateX(3px)'
                      }
                    }}
                  >
                    <item.icon sx={{ fontSize: '1.1rem' }} />
                    <Typography sx={{ ml: 2, fontSize: 'inherit' }}>{item.text}</Typography>
                  </Box>
                );
              })}
            </Box>
          </Collapse>
        </Box>
      )}
    </Box>
  );
};

export default SimplifiedSidebar;