import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Badge,
  Switch,
  FormControlLabel,
  Divider,
  Paper,
  Avatar,
  Tab,
  Tabs,
  Menu
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  Build as MaintenanceIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as HealthyIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandIcon,
  Notifications as NotificationIcon,
  Schedule as ScheduleIcon,
  Assignment as ServiceIcon,
  Monitor as MonitorIcon,
  Mic as MicIcon,
  Videocam as CameraIcon,
  Speaker as SpeakerIcon,
  ElectricalServices as TechIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import avEquipmentService from '../../services/avEquipmentService';

/**
 * AVEquipmentManager - Comprehensive AV equipment management interface
 * Handles sanctuary tech, projectors, microphones, and audio/visual equipment
 */
const AVEquipmentManager = ({ 
  buildingId, 
  floorId,
  selectedCategory = null,
  onEquipmentSelect = null,
  showTechnicalSupport = true,
  compactView = false 
}) => {
  const [equipment, setEquipment] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedEquipment, setSelectedEquipment] = useState(null);
  const [equipmentDetailsOpen, setEquipmentDetailsOpen] = useState(false);
  const [serviceRecordOpen, setServiceRecordOpen] = useState(false);
  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [contextMenu, setContextMenu] = useState(null);
  const [filters, setFilters] = useState({
    category: selectedCategory || '',
    status: '',
    health: '',
    maintenanceDue: false,
    hasAlerts: false
  });
  const [newServiceRecord, setNewServiceRecord] = useState({
    type: 'maintenance',
    description: '',
    technician: '',
    cost: 0,
    duration: 0,
    partsUsed: [],
    nextServiceDate: null
  });
  const [newAlert, setNewAlert] = useState({
    type: 'maintenance_due',
    severity: 'warning',
    message: ''
  });
  const [expandedPanels, setExpandedPanels] = useState({
    overview: true,
    maintenance: false,
    alerts: false
  });

  // Load equipment data
  useEffect(() => {
    if (buildingId) {
      loadEquipment();
    }
  }, [buildingId, floorId, filters]);

  const loadEquipment = async () => {
    setLoading(true);
    setError(null);
    try {
      const filterParams = {
        buildingId,
        floorId: floorId || undefined,
        ...filters
      };
      
      const data = await avEquipmentService.getAVEquipment(filterParams);
      setEquipment(data.equipment || []);
    } catch (err) {
      console.error('Error loading AV equipment:', err);
      setError('Failed to load AV equipment');
    } finally {
      setLoading(false);
    }
  };

  const handleEquipmentClick = (equipmentItem) => {
    setSelectedEquipment(equipmentItem);
    setEquipmentDetailsOpen(true);
    
    if (onEquipmentSelect) {
      onEquipmentSelect(equipmentItem);
    }
  };

  const handleStartSession = async (equipmentItem, service = 'general') => {
    try {
      await avEquipmentService.startEquipmentSession(equipmentItem._id, service);
      await loadEquipment();
    } catch (err) {
      console.error('Error starting session:', err);
      setError('Failed to start equipment session');
    }
  };

  const handleEndSession = async (equipmentItem) => {
    try {
      await avEquipmentService.endEquipmentSession(equipmentItem._id);
      await loadEquipment();
    } catch (err) {
      console.error('Error ending session:', err);
      setError('Failed to end equipment session');
    }
  };

  const handleStatusUpdate = async (equipmentItem, newStatus, notes = '') => {
    try {
      await avEquipmentService.updateEquipmentStatus(equipmentItem._id, newStatus, notes);
      await loadEquipment();
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update equipment status');
    }
  };

  const handleAddServiceRecord = async () => {
    try {
      await avEquipmentService.addServiceRecord(selectedEquipment._id, newServiceRecord);
      setServiceRecordOpen(false);
      setNewServiceRecord({
        type: 'maintenance',
        description: '',
        technician: '',
        cost: 0,
        duration: 0,
        partsUsed: [],
        nextServiceDate: null
      });
      await loadEquipment();
    } catch (err) {
      console.error('Error adding service record:', err);
      setError('Failed to add service record');
    }
  };

  const handleAddAlert = async () => {
    try {
      await avEquipmentService.addAlert(selectedEquipment._id, newAlert);
      setAlertDialogOpen(false);
      setNewAlert({
        type: 'maintenance_due',
        severity: 'warning',
        message: ''
      });
      await loadEquipment();
    } catch (err) {
      console.error('Error adding alert:', err);
      setError('Failed to add alert');
    }
  };

  const handleAcknowledgeAlert = async (equipmentItem, alertId) => {
    try {
      await avEquipmentService.acknowledgeAlert(equipmentItem._id, alertId);
      await loadEquipment();
    } catch (err) {
      console.error('Error acknowledging alert:', err);
      setError('Failed to acknowledge alert');
    }
  };

  const togglePanel = (panel) => {
    setExpandedPanels(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  };

  const getCategoryIcon = (category) => {
    const icons = {
      'microphone_wireless': <MicIcon />,
      'microphone_wired': <MicIcon />,
      'microphone_headset': <MicIcon />,
      'projector': <MonitorIcon />,
      'camera_ptz': <CameraIcon />,
      'camera_fixed': <CameraIcon />,
      'speakers': <SpeakerIcon />,
      'audio_mixer': <TechIcon />
    };
    return icons[category] || <TechIcon />;
  };

  const getHealthIcon = (health) => {
    switch (health) {
      case 'excellent':
      case 'good':
        return <HealthyIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'critical':
        return <ErrorIcon color="error" />;
      default:
        return <TechIcon color="disabled" />;
    }
  };

  const renderEquipmentCard = (equipmentItem) => {
    const formatted = avEquipmentService.formatEquipmentDisplay(equipmentItem);
    const priority = avEquipmentService.calculateMaintenancePriority(equipmentItem);

    return (
      <Card
        key={equipmentItem._id}
        sx={{
          mb: 2,
          cursor: 'pointer',
          border: priority.level === 'critical' ? 2 : 1,
          borderColor: priority.level === 'critical' ? 'error.main' : 'divider',
          '&:hover': {
            boxShadow: 3,
            transform: 'translateY(-2px)'
          },
          transition: 'all 0.2s ease'
        }}
        onClick={() => handleEquipmentClick(equipmentItem)}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Avatar sx={{ mr: 2, bgcolor: formatted.categoryColor }}>
                  {formatted.categoryIcon}
                </Avatar>
                
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" noWrap>
                    {formatted.displayName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formatted.categoryLabel} • {equipmentItem.location?.roomName || 'Unknown Location'}
                  </Typography>
                </Box>
                
                {formatted.hasSession && (
                  <Chip 
                    label="IN USE" 
                    size="small" 
                    color="primary"
                    sx={{ mr: 1 }}
                  />
                )}
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Chip 
                  label={formatted.statusLabel}
                  size="small"
                  sx={{ 
                    backgroundColor: formatted.statusColor,
                    color: 'white'
                  }}
                />
                
                <Chip 
                  label={formatted.healthLabel}
                  size="small"
                  icon={getHealthIcon(equipmentItem.status?.health)}
                  variant="outlined"
                />
                
                {priority.level !== 'none' && (
                  <Chip 
                    label={priority.level.toUpperCase()}
                    size="small"
                    color={priority.level === 'critical' ? 'error' : 'warning'}
                  />
                )}
              </Box>
              
              {equipmentItem.specifications?.manufacturer && (
                <Typography variant="caption" color="text.secondary" display="block">
                  {equipmentItem.specifications.manufacturer} {equipmentItem.specifications.model}
                </Typography>
              )}
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
              {formatted.hasAlerts && (
                <Badge 
                  badgeContent={equipmentItem.alertSummary?.total || 0}
                  color="error"
                >
                  <NotificationIcon color="error" />
                </Badge>
              )}
              
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  setContextMenu({
                    mouseX: e.clientX,
                    mouseY: e.clientY,
                    equipment: equipmentItem
                  });
                }}
              >
                <MoreIcon />
              </IconButton>
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  };

  const renderEquipmentDetailsDialog = () => (
    <Dialog 
      open={equipmentDetailsOpen} 
      onClose={() => setEquipmentDetailsOpen(false)}
      maxWidth="lg"
      fullWidth
    >
      {selectedEquipment && (
        <>
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ bgcolor: avEquipmentService.getCategoryColor(selectedEquipment.category) }}>
                {avEquipmentService.getCategoryIcon(selectedEquipment.category)}
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {selectedEquipment.displayName || selectedEquipment.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {selectedEquipment.equipmentId}
                </Typography>
              </Box>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
              <Tab label="Overview" value="overview" />
              <Tab label="Specifications" value="specifications" />
              <Tab label="Maintenance" value="maintenance" />
              <Tab label="Usage" value="usage" />
              <Tab label="Settings" value="settings" />
            </Tabs>
            
            <Box sx={{ mt: 2 }}>
              {activeTab === 'overview' && (
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Status Information
                    </Typography>
                    
                    <List dense>
                      <ListItem>
                        <ListItemIcon>{getHealthIcon(selectedEquipment.status?.health)}</ListItemIcon>
                        <ListItemText 
                          primary="Health Status"
                          secondary={avEquipmentService.getHealthStatuses().find(h => h.value === selectedEquipment.status?.health)?.label || 'Unknown'}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon><SettingsIcon /></ListItemIcon>
                        <ListItemText 
                          primary="Operational Status"
                          secondary={avEquipmentService.getEquipmentStatuses().find(s => s.value === selectedEquipment.status?.operational)?.label || 'Unknown'}
                        />
                      </ListItem>
                      
                      {selectedEquipment.usage?.currentSession?.active && (
                        <ListItem>
                          <ListItemIcon><StartIcon color="primary" /></ListItemIcon>
                          <ListItemText 
                            primary="Current Session"
                            secondary={`Started: ${new Date(selectedEquipment.usage.currentSession.startedAt).toLocaleString()}`}
                          />
                        </ListItem>
                      )}
                    </List>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Location & Position
                    </Typography>
                    
                    <List dense>
                      <ListItem>
                        <ListItemText 
                          primary="Room"
                          secondary={selectedEquipment.location?.roomName || 'Not specified'}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemText 
                          primary="Zone"
                          secondary={selectedEquipment.location?.position?.zone || 'Not specified'}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemText 
                          primary="Mounting"
                          secondary={selectedEquipment.location?.position?.mounting || 'Not specified'}
                        />
                      </ListItem>
                    </List>
                  </Grid>
                  
                  {selectedEquipment.description && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                        Description
                      </Typography>
                      <Typography variant="body2">
                        {selectedEquipment.description}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              )}
              
              {activeTab === 'specifications' && (
                <Grid container spacing={3}>
                  {selectedEquipment.specifications && (
                    <>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                          Hardware Information
                        </Typography>
                        
                        <List dense>
                          {selectedEquipment.specifications.manufacturer && (
                            <ListItem>
                              <ListItemText 
                                primary="Manufacturer"
                                secondary={selectedEquipment.specifications.manufacturer}
                              />
                            </ListItem>
                          )}
                          
                          {selectedEquipment.specifications.model && (
                            <ListItem>
                              <ListItemText 
                                primary="Model"
                                secondary={selectedEquipment.specifications.model}
                              />
                            </ListItem>
                          )}
                          
                          {selectedEquipment.specifications.serialNumber && (
                            <ListItem>
                              <ListItemText 
                                primary="Serial Number"
                                secondary={selectedEquipment.specifications.serialNumber}
                              />
                            </ListItem>
                          )}
                        </List>
                      </Grid>
                      
                      {selectedEquipment.specifications.connectivity && (
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                            Connectivity
                          </Typography>
                          
                          <List dense>
                            {selectedEquipment.specifications.connectivity.network?.ipAddress && (
                              <ListItem>
                                <ListItemText 
                                  primary="IP Address"
                                  secondary={selectedEquipment.specifications.connectivity.network.ipAddress}
                                />
                              </ListItem>
                            )}
                            
                            {selectedEquipment.specifications.connectivity.wireless?.frequency && (
                              <ListItem>
                                <ListItemText 
                                  primary="Wireless Frequency"
                                  secondary={selectedEquipment.specifications.connectivity.wireless.frequency}
                                />
                              </ListItem>
                            )}
                          </List>
                        </Grid>
                      )}
                    </>
                  )}
                </Grid>
              )}
              
              {activeTab === 'maintenance' && (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      Maintenance History
                    </Typography>
                    <Button
                      startIcon={<AddIcon />}
                      onClick={() => setServiceRecordOpen(true)}
                      variant="outlined"
                      size="small"
                    >
                      Add Service
                    </Button>
                  </Box>
                  
                  {selectedEquipment.maintenance?.serviceHistory?.length > 0 ? (
                    <List>
                      {selectedEquipment.maintenance.serviceHistory.slice(0, 5).map((service, index) => (
                        <ListItem key={index} divider>
                          <ListItemIcon>
                            <ServiceIcon color={service.type === 'repair' ? 'error' : 'primary'} />
                          </ListItemIcon>
                          <ListItemText
                            primary={service.description || service.type}
                            secondary={`${new Date(service.date).toLocaleDateString()} • ${service.technician || 'Unknown'}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography color="text.secondary">
                      No maintenance history available
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setEquipmentDetailsOpen(false)}>Close</Button>
            
            {selectedEquipment.usage?.currentSession?.active ? (
              <Button 
                onClick={() => handleEndSession(selectedEquipment)}
                color="warning"
                startIcon={<StopIcon />}
              >
                End Session
              </Button>
            ) : (
              <Button 
                onClick={() => handleStartSession(selectedEquipment)}
                color="primary"
                startIcon={<StartIcon />}
              >
                Start Session
              </Button>
            )}
          </DialogActions>
        </>
      )}
    </Dialog>
  );

  const renderServiceRecordDialog = () => (
    <Dialog open={serviceRecordOpen} onClose={() => setServiceRecordOpen(false)} maxWidth="md" fullWidth>
      <DialogTitle>Add Service Record</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Service Type</InputLabel>
              <Select
                value={newServiceRecord.type}
                onChange={(e) => setNewServiceRecord({ ...newServiceRecord, type: e.target.value })}
              >
                {avEquipmentService.getServiceTypes().map(type => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Technician"
              value={newServiceRecord.technician}
              onChange={(e) => setNewServiceRecord({ ...newServiceRecord, technician: e.target.value })}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={newServiceRecord.description}
              onChange={(e) => setNewServiceRecord({ ...newServiceRecord, description: e.target.value })}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="Cost ($)"
              value={newServiceRecord.cost}
              onChange={(e) => setNewServiceRecord({ ...newServiceRecord, cost: parseFloat(e.target.value) || 0 })}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="Duration (minutes)"
              value={newServiceRecord.duration}
              onChange={(e) => setNewServiceRecord({ ...newServiceRecord, duration: parseInt(e.target.value) || 0 })}
            />
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setServiceRecordOpen(false)}>Cancel</Button>
        <Button 
          onClick={handleAddServiceRecord}
          variant="contained"
          disabled={!newServiceRecord.description}
        >
          Add Record
        </Button>
      </DialogActions>
    </Dialog>
  );

  const renderContextMenu = () => (
    <Menu
      open={!!contextMenu}
      onClose={() => setContextMenu(null)}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu ? { top: contextMenu.mouseY, left: contextMenu.mouseX } : undefined
      }
    >
      <MenuItem onClick={() => {
        handleEquipmentClick(contextMenu.equipment);
        setContextMenu(null);
      }}>
        <ListItemIcon><EditIcon /></ListItemIcon>
        <ListItemText>View Details</ListItemText>
      </MenuItem>
      
      {contextMenu?.equipment?.usage?.currentSession?.active ? (
        <MenuItem onClick={() => {
          handleEndSession(contextMenu.equipment);
          setContextMenu(null);
        }}>
          <ListItemIcon><StopIcon /></ListItemIcon>
          <ListItemText>End Session</ListItemText>
        </MenuItem>
      ) : (
        <MenuItem onClick={() => {
          handleStartSession(contextMenu.equipment);
          setContextMenu(null);
        }}>
          <ListItemIcon><StartIcon /></ListItemIcon>
          <ListItemText>Start Session</ListItemText>
        </MenuItem>
      )}
      
      <MenuItem onClick={() => {
        setSelectedEquipment(contextMenu.equipment);
        setServiceRecordOpen(true);
        setContextMenu(null);
      }}>
        <ListItemIcon><MaintenanceIcon /></ListItemIcon>
        <ListItemText>Add Service</ListItemText>
      </MenuItem>
    </Menu>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading AV equipment...</Typography>
        <LinearProgress sx={{ mt: 2 }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: compactView ? 1 : 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5">AV Equipment Manager</Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            startIcon={<RefreshIcon />}
            onClick={loadEquipment}
            variant="outlined"
            size="small"
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={filters.category}
                onChange={(e) => setFilters({ ...filters, category: e.target.value })}
              >
                <MenuItem value="">All Categories</MenuItem>
                {avEquipmentService.getEquipmentCategories().map(category => (
                  <MenuItem key={category.value} value={category.value}>
                    {category.icon} {category.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              >
                <MenuItem value="">All Status</MenuItem>
                {avEquipmentService.getEquipmentStatuses().map(status => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.icon} {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch 
                  checked={filters.maintenanceDue}
                  onChange={(e) => setFilters({ ...filters, maintenanceDue: e.target.checked })}
                />
              }
              label="Maintenance Due"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch 
                  checked={filters.hasAlerts}
                  onChange={(e) => setFilters({ ...filters, hasAlerts: e.target.checked })}
                />
              }
              label="Has Alerts"
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Equipment List */}
      {equipment.length === 0 ? (
        <Alert severity="info">
          No AV equipment found. Check your filters or add equipment to get started.
        </Alert>
      ) : (
        <Grid container spacing={2}>
          {equipment.map(equipmentItem => (
            <Grid item xs={12} md={6} lg={4} key={equipmentItem._id}>
              {renderEquipmentCard(equipmentItem)}
            </Grid>
          ))}
        </Grid>
      )}

      {/* Dialogs */}
      {renderEquipmentDetailsDialog()}
      {renderServiceRecordDialog()}
      {renderContextMenu()}
    </Box>
  );
};

export default AVEquipmentManager;