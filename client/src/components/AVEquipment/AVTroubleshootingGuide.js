import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Chip,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Paper,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandIcon,
  Help as HelpIcon,
  Build as ToolIcon,
  Timer as TimerIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Search as SearchIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import avEquipmentService from '../../services/avEquipmentService';

/**
 * AVTroubleshootingGuide - Interactive troubleshooting and technical support
 * Provides step-by-step problem resolution for AV equipment
 */
const AVTroubleshootingGuide = ({ 
  selectedEquipment = null,
  onCreateTicket = null,
  showEmergencyContacts = true,
  compactView = false 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProblem, setSelectedProblem] = useState(null);
  const [troubleshootingSteps, setTroubleshootingSteps] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [ticketDialogOpen, setTicketDialogOpen] = useState(false);
  const [ticketData, setTicketData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    category: 'technical_support'
  });

  // Common troubleshooting guides
  const commonProblems = {
    'microphone_wireless': [
      {
        id: 'mic_no_audio',
        problem: 'Wireless microphone has no audio output',
        difficulty: 'easy',
        estimatedTime: 5,
        symptoms: ['No sound from microphone', 'Receiver shows no signal', 'Audio level meters not moving'],
        steps: [
          {
            title: 'Check Power and Battery',
            content: 'Verify the microphone battery is installed correctly and has charge',
            action: 'Replace battery if needed',
            tools: ['Fresh batteries']
          },
          {
            title: 'Verify Frequency Settings',
            content: 'Ensure transmitter and receiver are on the same frequency/channel',
            action: 'Match frequency on both devices',
            tools: ['Equipment manual']
          },
          {
            title: 'Check Receiver Connection',
            content: 'Verify receiver is connected to mixer/amplifier with proper cables',
            action: 'Secure all audio connections',
            tools: ['XLR or 1/4" cables']
          },
          {
            title: 'Test Signal Path',
            content: 'Check mixer input gain, fader levels, and mute status',
            action: 'Adjust mixer settings appropriately',
            tools: ['Audio mixer']
          }
        ]
      },
      {
        id: 'mic_dropouts',
        problem: 'Wireless microphone has audio dropouts or interference',
        difficulty: 'medium',
        estimatedTime: 10,
        symptoms: ['Intermittent audio loss', 'Static or noise', 'Audio cutting in and out'],
        steps: [
          {
            title: 'Check for Interference',
            content: 'Look for other wireless devices that might cause interference',
            action: 'Turn off WiFi routers, cell phones, or other wireless equipment nearby',
            tools: ['RF spectrum analyzer (if available)']
          },
          {
            title: 'Verify Line of Sight',
            content: 'Ensure clear path between transmitter and receiver',
            action: 'Remove obstacles or reposition receiver',
            tools: []
          },
          {
            title: 'Change Frequency',
            content: 'Switch to a different frequency or channel',
            action: 'Use frequency coordination chart to select clear channel',
            tools: ['Equipment manual', 'Frequency coordination chart']
          },
          {
            title: 'Check Antenna Position',
            content: 'Ensure receiver antennas are properly positioned and not damaged',
            action: 'Adjust antenna position for optimal reception',
            tools: []
          }
        ]
      }
    ],
    'projector': [
      {
        id: 'proj_no_image',
        problem: 'Projector shows no image',
        difficulty: 'easy',
        estimatedTime: 5,
        symptoms: ['Blank screen', 'No image display', 'Projector on but no output'],
        steps: [
          {
            title: 'Check Power Connection',
            content: 'Verify projector is powered on and lamp is functioning',
            action: 'Check power cable and press power button',
            tools: []
          },
          {
            title: 'Verify Input Source',
            content: 'Ensure correct input source is selected on projector',
            action: 'Cycle through input sources (HDMI, VGA, etc.)',
            tools: ['Projector remote']
          },
          {
            title: 'Check Cable Connections',
            content: 'Verify all video cables are securely connected',
            action: 'Reconnect video cables at both ends',
            tools: ['Video cables']
          },
          {
            title: 'Test Source Device',
            content: 'Verify the source device (computer, media player) is outputting signal',
            action: 'Test with known working source device',
            tools: ['Laptop or test device']
          }
        ]
      },
      {
        id: 'proj_dim_image',
        problem: 'Projector image is dim or dark',
        difficulty: 'medium',
        estimatedTime: 15,
        symptoms: ['Dark or dim image', 'Poor brightness', 'Image barely visible'],
        steps: [
          {
            title: 'Check Lamp Life',
            content: 'Review projector lamp hours and status',
            action: 'Replace lamp if near end of life (check manual for hours)',
            tools: ['Replacement lamp', 'Screwdriver']
          },
          {
            title: 'Clean Air Filter',
            content: 'Check and clean projector air filter',
            action: 'Remove and clean or replace air filter',
            tools: ['Compressed air', 'Replacement filter']
          },
          {
            title: 'Adjust Brightness Settings',
            content: 'Check projector brightness and contrast settings',
            action: 'Increase brightness in projector menu',
            tools: ['Projector remote']
          },
          {
            title: 'Check Ambient Light',
            content: 'Evaluate room lighting conditions',
            action: 'Dim room lights or close blinds',
            tools: []
          }
        ]
      }
    ],
    'audio_mixer': [
      {
        id: 'mixer_no_output',
        problem: 'Audio mixer has no output sound',
        difficulty: 'easy',
        estimatedTime: 5,
        symptoms: ['No audio from speakers', 'Silent output', 'No sound at all'],
        steps: [
          {
            title: 'Check Main Output Level',
            content: 'Verify main faders and master volume are up',
            action: 'Raise main L/R faders and master volume',
            tools: []
          },
          {
            title: 'Verify Output Connections',
            content: 'Check all output cables to amplifiers/speakers',
            action: 'Secure all output connections',
            tools: ['XLR or TRS cables']
          },
          {
            title: 'Check Channel Routing',
            content: 'Ensure channels are routed to main outputs',
            action: 'Verify channel assign buttons are pressed',
            tools: []
          },
          {
            title: 'Test Individual Channels',
            content: 'Solo individual channels to isolate the problem',
            action: 'Use solo/PFL buttons to test each channel',
            tools: ['Headphones']
          }
        ]
      }
    ],
    'camera_ptz': [
      {
        id: 'camera_not_responding',
        problem: 'PTZ camera not responding to controls',
        difficulty: 'medium',
        estimatedTime: 10,
        symptoms: ['Camera not moving', 'No response to pan/tilt/zoom', 'Control interface shows disconnected'],
        steps: [
          {
            title: 'Check Power and Network',
            content: 'Verify camera has power and network connectivity',
            action: 'Check power LED and network cable connection',
            tools: ['Network cable tester']
          },
          {
            title: 'Verify IP Configuration',
            content: 'Ensure camera IP address is correct and accessible',
            action: 'Ping camera IP address from control computer',
            tools: ['Computer with network access']
          },
          {
            title: 'Check Control Protocol',
            content: 'Verify correct control protocol and address settings',
            action: 'Check VISCA, PELCO, or IP control settings',
            tools: ['Camera manual', 'Control software']
          },
          {
            title: 'Restart Camera and Controller',
            content: 'Power cycle camera and control device',
            action: 'Unplug camera for 30 seconds, then restart controller',
            tools: []
          }
        ]
      }
    ]
  };

  // Emergency contacts
  const emergencyContacts = [
    {
      name: 'AV Technical Support',
      phone: '(*************',
      email: '<EMAIL>',
      availability: '24/7 Emergency Line',
      type: 'primary'
    },
    {
      name: 'Equipment Vendor Support',
      phone: '(*************',
      email: '<EMAIL>',
      availability: 'Mon-Fri 8AM-6PM',
      type: 'vendor'
    },
    {
      name: 'Facilities Manager',
      phone: '(*************',
      email: '<EMAIL>',
      availability: 'On-call weekends',
      type: 'staff'
    }
  ];

  useEffect(() => {
    if (selectedEquipment) {
      const problems = commonProblems[selectedEquipment.category] || [];
      setTroubleshootingSteps(problems);
    }
  }, [selectedEquipment]);

  const handleProblemSelect = (problem) => {
    setSelectedProblem(problem);
    setActiveStep(0);
  };

  const handleStepComplete = (stepIndex) => {
    if (stepIndex < selectedProblem.steps.length - 1) {
      setActiveStep(stepIndex + 1);
    }
  };

  const handleCreateTicket = () => {
    if (selectedProblem) {
      setTicketData({
        ...ticketData,
        title: selectedProblem.problem,
        description: `Equipment: ${selectedEquipment?.name || 'Unknown'}\nProblem: ${selectedProblem.problem}\nSteps attempted: ${activeStep + 1}/${selectedProblem.steps.length}`
      });
    }
    setTicketDialogOpen(true);
  };

  const handleSubmitTicket = () => {
    if (onCreateTicket) {
      onCreateTicket({
        ...ticketData,
        equipmentId: selectedEquipment?._id,
        category: 'av_equipment',
        troubleshootingAttempted: selectedProblem ? {
          problemId: selectedProblem.id,
          stepsCompleted: activeStep + 1,
          totalSteps: selectedProblem.steps.length
        } : null
      });
    }
    setTicketDialogOpen(false);
  };

  const filteredProblems = troubleshootingSteps.filter(problem =>
    problem.problem.toLowerCase().includes(searchQuery.toLowerCase()) ||
    problem.symptoms.some(symptom => 
      symptom.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'error';
      default: return 'default';
    }
  };

  const renderProblemCard = (problem) => (
    <Card 
      key={problem.id}
      sx={{ 
        mb: 2, 
        cursor: 'pointer',
        '&:hover': { boxShadow: 3 }
      }}
      onClick={() => handleProblemSelect(problem)}
    >
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Typography variant="h6" sx={{ flex: 1 }}>
            {problem.problem}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Chip 
              label={problem.difficulty}
              size="small"
              color={getDifficultyColor(problem.difficulty)}
            />
            <Chip 
              label={`${problem.estimatedTime} min`}
              size="small"
              icon={<TimerIcon />}
              variant="outlined"
            />
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Common symptoms: {problem.symptoms.join(', ')}
        </Typography>
        
        <Typography variant="caption" color="text.secondary">
          {problem.steps.length} troubleshooting steps
        </Typography>
      </CardContent>
    </Card>
  );

  const renderTroubleshootingSteps = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          {selectedProblem.problem}
        </Typography>
        
        <Button
          variant="outlined"
          startIcon={<HelpIcon />}
          onClick={handleCreateTicket}
          size="small"
        >
          Get Help
        </Button>
      </Box>
      
      <Stepper activeStep={activeStep} orientation="vertical">
        {selectedProblem.steps.map((step, index) => (
          <Step key={index}>
            <StepLabel>
              <Typography variant="subtitle1">
                {step.title}
              </Typography>
            </StepLabel>
            <StepContent>
              <Typography variant="body2" paragraph>
                {step.content}
              </Typography>
              
              {step.action && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Action:</strong> {step.action}
                  </Typography>
                </Alert>
              )}
              
              {step.tools && step.tools.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="caption" color="text.secondary">
                    Tools needed: {step.tools.join(', ')}
                  </Typography>
                </Box>
              )}
              
              <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                <Button
                  onClick={() => handleStepComplete(index)}
                  variant="contained"
                  size="small"
                  startIcon={<CheckIcon />}
                >
                  {index === selectedProblem.steps.length - 1 ? 'Complete' : 'Next Step'}
                </Button>
                
                <Button
                  onClick={handleCreateTicket}
                  variant="outlined"
                  size="small"
                  startIcon={<HelpIcon />}
                >
                  Still Need Help
                </Button>
              </Box>
            </StepContent>
          </Step>
        ))}
      </Stepper>
      
      {activeStep >= selectedProblem.steps.length && (
        <Alert severity="success" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>Troubleshooting Complete!</strong> If the problem persists, please create a support ticket for further assistance.
          </Typography>
        </Alert>
      )}
    </Box>
  );

  const renderEmergencyContacts = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Emergency Contacts
      </Typography>
      
      <Grid container spacing={2}>
        {emergencyContacts.map((contact, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {contact.name}
                </Typography>
                
                <List dense>
                  <ListItem>
                    <ListItemIcon><PhoneIcon /></ListItemIcon>
                    <ListItemText 
                      primary={contact.phone}
                      secondary="Phone"
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon><EmailIcon /></ListItemIcon>
                    <ListItemText 
                      primary={contact.email}
                      secondary="Email"
                    />
                  </ListItem>
                </List>
                
                <Typography variant="caption" color="text.secondary">
                  {contact.availability}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderTicketDialog = () => (
    <Dialog open={ticketDialogOpen} onClose={() => setTicketDialogOpen(false)} maxWidth="md" fullWidth>
      <DialogTitle>Create Support Ticket</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Issue Title"
              value={ticketData.title}
              onChange={(e) => setTicketData({ ...ticketData, title: e.target.value })}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={4}
              value={ticketData.description}
              onChange={(e) => setTicketData({ ...ticketData, description: e.target.value })}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              select
              label="Priority"
              value={ticketData.priority}
              onChange={(e) => setTicketData({ ...ticketData, priority: e.target.value })}
              SelectProps={{ native: true }}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </TextField>
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setTicketDialogOpen(false)}>Cancel</Button>
        <Button 
          onClick={handleSubmitTicket}
          variant="contained"
          disabled={!ticketData.title || !ticketData.description}
        >
          Create Ticket
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{ p: compactView ? 1 : 3 }}>
      {/* Header */}
      <Typography variant="h5" gutterBottom>
        AV Troubleshooting Guide
      </Typography>
      
      {selectedEquipment && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Showing troubleshooting guides for: <strong>{selectedEquipment.name}</strong> ({selectedEquipment.category})
          </Typography>
        </Alert>
      )}

      {!selectedProblem ? (
        <Box>
          {/* Search */}
          <TextField
            fullWidth
            placeholder="Search for problems or symptoms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ mb: 3 }}
          />

          {/* Problem List */}
          <Typography variant="h6" gutterBottom>
            Common Problems
          </Typography>
          
          {filteredProblems.length === 0 ? (
            <Alert severity="info">
              No troubleshooting guides available for this equipment type or search query.
            </Alert>
          ) : (
            filteredProblems.map(problem => renderProblemCard(problem))
          )}
          
          {/* Emergency Contacts */}
          {showEmergencyContacts && (
            <Box sx={{ mt: 4 }}>
              <Divider sx={{ mb: 3 }} />
              {renderEmergencyContacts()}
            </Box>
          )}
        </Box>
      ) : (
        <Box>
          <Button
            onClick={() => setSelectedProblem(null)}
            sx={{ mb: 2 }}
            size="small"
          >
            ← Back to Problems
          </Button>
          
          {renderTroubleshootingSteps()}
        </Box>
      )}

      {/* Dialogs */}
      {renderTicketDialog()}
    </Box>
  );
};

export default AVTroubleshootingGuide;