import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Badge,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  LinearProgress,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  Build as ServiceIcon,
  FilterAlt as FilterIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Assignment as InspectionIcon,
  History as HistoryIcon,
  ThermostatAuto as HVACIcon,
  AcUnit as ACIcon,
  Whatshot as HeatIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import hvacService from '../../services/hvacService';

/**
 * HVACServiceTracker Component
 * Comprehensive HVAC service tracking with filter due badges and service history
 * Part of Phase 4 - Climate heatmap + HVAC service tracking
 */
const HVACServiceTracker = ({ 
  buildingId = null, 
  floorId = null,
  compactView = false 
}) => {
  const [units, setUnits] = useState([]);
  const [maintenanceDue, setMaintenanceDue] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedUnit, setSelectedUnit] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  
  // Dialog states
  const [filterChangeDialogOpen, setFilterChangeDialogOpen] = useState(false);
  const [serviceRecordDialogOpen, setServiceRecordDialogOpen] = useState(false);
  const [inspectionDialogOpen, setInspectionDialogOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(null);
  
  // Form states
  const [filterChangeForm, setFilterChangeForm] = useState({
    changeDate: new Date().toISOString().split('T')[0],
    newFilterBrand: '',
    newFilterPartNumber: '',
    cost: '',
    notes: ''
  });
  
  const [serviceRecordForm, setServiceRecordForm] = useState({
    serviceDate: new Date().toISOString().split('T')[0],
    serviceType: '',
    description: '',
    cost: '',
    partsReplaced: '',
    workPerformed: '',
    recommendations: '',
    nextServiceDate: '',
    serviceNotes: ''
  });

  const [inspectionForm, setInspectionForm] = useState({
    inspectionDate: new Date().toISOString().split('T')[0]
  });

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, [buildingId, floorId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const filters = {};
      if (buildingId) filters.buildingId = buildingId;
      if (floorId) filters.floorId = floorId;
      
      const [unitsData, maintenanceData, alertsData] = await Promise.all([
        hvacService.getUnits(filters),
        hvacService.getMaintenanceDue(30),
        hvacService.getAlerts()
      ]);
      
      setUnits(unitsData.units || []);
      setMaintenanceDue(maintenanceData);
      setAlerts(alertsData);
    } catch (err) {
      console.error('Error loading HVAC data:', err);
      setError('Failed to load HVAC service data');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = async () => {
    try {
      await hvacService.recordFilterChange(
        selectedUnit._id,
        selectedFilter.position,
        filterChangeForm
      );
      
      // Refresh data
      await loadData();
      
      // Reset form and close dialog
      setFilterChangeDialogOpen(false);
      setSelectedFilter(null);
      setFilterChangeForm({
        changeDate: new Date().toISOString().split('T')[0],
        newFilterBrand: '',
        newFilterPartNumber: '',
        cost: '',
        notes: ''
      });
    } catch (err) {
      console.error('Error recording filter change:', err);
      setError('Failed to record filter change');
    }
  };

  const handleServiceRecord = async () => {
    try {
      await hvacService.addServiceRecord(selectedUnit._id, serviceRecordForm);
      
      // Refresh data
      await loadData();
      
      // Reset form and close dialog
      setServiceRecordDialogOpen(false);
      setServiceRecordForm({
        serviceDate: new Date().toISOString().split('T')[0],
        serviceType: '',
        description: '',
        cost: '',
        partsReplaced: '',
        workPerformed: '',
        recommendations: '',
        nextServiceDate: '',
        serviceNotes: ''
      });
    } catch (err) {
      console.error('Error adding service record:', err);
      setError('Failed to add service record');
    }
  };

  const handleScheduleInspection = async () => {
    try {
      await hvacService.scheduleInspection(selectedUnit._id, inspectionForm.inspectionDate);
      
      // Refresh data
      await loadData();
      
      // Reset form and close dialog
      setInspectionDialogOpen(false);
      setInspectionForm({
        inspectionDate: new Date().toISOString().split('T')[0]
      });
    } catch (err) {
      console.error('Error scheduling inspection:', err);
      setError('Failed to schedule inspection');
    }
  };

  const getFilterStatusColor = (filter) => {
    const daysUntilChange = hvacService.calculateDaysUntilFilterChange(filter);
    
    if (daysUntilChange < 0) return 'error';
    if (daysUntilChange <= 7) return 'error';
    if (daysUntilChange <= 14) return 'warning';
    return 'success';
  };

  const getFilterStatusText = (filter) => {
    const daysUntilChange = hvacService.calculateDaysUntilFilterChange(filter);
    
    if (daysUntilChange < 0) return `Overdue by ${Math.abs(daysUntilChange)} days`;
    if (daysUntilChange === 0) return 'Due today';
    if (daysUntilChange <= 7) return `Due in ${daysUntilChange} days`;
    if (daysUntilChange <= 30) return `Due in ${daysUntilChange} days`;
    return 'Good';
  };

  const renderAlertsOverview = () => {
    if (alerts.length === 0) return null;

    return (
      <Alert 
        severity="warning" 
        sx={{ mb: 2 }}
        action={
          <Button color="inherit" size="small" onClick={loadData}>
            <RefreshIcon sx={{ mr: 1 }} />
            Refresh
          </Button>
        }
      >
        <Typography variant="h6">
          {alerts.length} HVAC Alert{alerts.length !== 1 ? 's' : ''} Require Attention
        </Typography>
        <Typography variant="body2">
          {alerts.filter(alert => alert.alerts.some(a => a.severity === 'critical')).length} critical, {' '}
          {alerts.filter(alert => alert.alerts.some(a => a.severity === 'warning')).length} warnings
        </Typography>
      </Alert>
    );
  };

  const renderUnitsGrid = () => {
    if (compactView) {
      return renderCompactUnitsView();
    }

    return (
      <Grid container spacing={2}>
        {units.map(unit => {
          const overdueMaintenance = maintenanceDue.find(m => m._id === unit._id);
          const unitAlerts = alerts.find(a => a._id === unit._id);
          
          return (
            <Grid item xs={12} sm={6} md={4} key={unit._id}>
              <Card 
                sx={{ 
                  height: '100%',
                  border: unitAlerts ? '2px solid #ff9800' : 'none'
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <HVACIcon sx={{ mr: 1 }} />
                    <Typography variant="h6" component="div">
                      {unit.name}
                    </Typography>
                    {unitAlerts && (
                      <Badge 
                        badgeContent={unitAlerts.alerts.length} 
                        color="warning"
                        sx={{ ml: 'auto' }}
                      >
                        <WarningIcon color="warning" />
                      </Badge>
                    )}
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {unit.unitType.replace(/_/g, ' ')} • {unit.room}
                  </Typography>
                  
                  <Chip
                    label={unit.status}
                    color={hvacService.getUnitStatuses().find(s => s.value === unit.status)?.color || 'default'}
                    size="small"
                    sx={{ mb: 2 }}
                  />
                  
                  {/* Filter Status */}
                  <Typography variant="subtitle2" gutterBottom>
                    Filter Status:
                  </Typography>
                  {unit.filters.map((filter, index) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="caption">
                          {filter.position} ({filter.size})
                        </Typography>
                        <Chip
                          label={getFilterStatusText(filter)}
                          color={getFilterStatusColor(filter)}
                          size="small"
                        />
                      </Box>
                    </Box>
                  ))}
                  
                  {/* Maintenance Status */}
                  {overdueMaintenance && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" color="warning.main">
                        Maintenance Due
                      </Typography>
                    </Box>
                  )}
                </CardContent>
                
                <CardActions>
                  <Button 
                    size="small" 
                    onClick={() => {
                      setSelectedUnit(unit);
                      setActiveTab(0);
                    }}
                  >
                    View Details
                  </Button>
                  <Button 
                    size="small" 
                    onClick={() => {
                      setSelectedUnit(unit);
                      setServiceRecordDialogOpen(true);
                    }}
                  >
                    Add Service
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          );
        })}
      </Grid>
    );
  };

  const renderCompactUnitsView = () => (
    <TableContainer component={Paper}>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Unit</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Location</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Filters</TableCell>
            <TableCell>Next Maintenance</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {units.map(unit => {
            const overdueMaintenance = maintenanceDue.find(m => m._id === unit._id);
            const overdueFilters = unit.filters.filter(f => hvacService.isFilterOverdue(f));
            const dueFilters = unit.filters.filter(f => hvacService.isFilterDue(f));
            
            return (
              <TableRow key={unit._id}>
                <TableCell>{unit.name}</TableCell>
                <TableCell>{unit.unitType.replace(/_/g, ' ')}</TableCell>
                <TableCell>{unit.room}</TableCell>
                <TableCell>
                  <Chip
                    label={unit.status}
                    color={hvacService.getUnitStatuses().find(s => s.value === unit.status)?.color || 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {overdueFilters.length > 0 && (
                    <Chip label={`${overdueFilters.length} Overdue`} color="error" size="small" sx={{ mr: 1 }} />
                  )}
                  {dueFilters.length > 0 && (
                    <Chip label={`${dueFilters.length} Due`} color="warning" size="small" />
                  )}
                  {overdueFilters.length === 0 && dueFilters.length === 0 && (
                    <Chip label="Good" color="success" size="small" />
                  )}
                </TableCell>
                <TableCell>
                  {overdueMaintenance ? (
                    <Typography variant="caption" color="warning.main">
                      Due
                    </Typography>
                  ) : (
                    <Typography variant="caption">
                      Scheduled
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Tooltip title="View Details">
                    <IconButton 
                      size="small"
                      onClick={() => {
                        setSelectedUnit(unit);
                        setActiveTab(0);
                      }}
                    >
                      <SettingsIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );

  const renderUnitDetails = () => {
    if (!selectedUnit) return null;

    return (
      <Dialog
        open={!!selectedUnit}
        onClose={() => setSelectedUnit(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <HVACIcon />
            {selectedUnit.name}
            <Chip 
              label={selectedUnit.status} 
              color={hvacService.getUnitStatuses().find(s => s.value === selectedUnit.status)?.color || 'default'}
              size="small"
            />
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Tabs value={activeTab} onChange={(e, value) => setActiveTab(value)}>
            <Tab label="Filters" icon={<FilterIcon />} />
            <Tab label="Service History" icon={<HistoryIcon />} />
            <Tab label="Maintenance" icon={<InspectionIcon />} />
          </Tabs>
          
          {/* Filters Tab */}
          {activeTab === 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Filter Status & Maintenance
              </Typography>
              
              {selectedUnit.filters.map((filter, index) => {
                const daysUntilChange = hvacService.calculateDaysUntilFilterChange(filter);
                
                return (
                  <Card key={index} sx={{ mb: 2 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">
                          {filter.position} Filter
                        </Typography>
                        <Chip
                          label={getFilterStatusText(filter)}
                          color={getFilterStatusColor(filter)}
                        />
                      </Box>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2">
                            <strong>Size:</strong> {filter.size}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Type:</strong> {filter.filterType}
                          </Typography>
                          {filter.merv && (
                            <Typography variant="body2">
                              <strong>MERV:</strong> {filter.merv}
                            </Typography>
                          )}
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2">
                            <strong>Last Changed:</strong> {filter.lastChanged ? new Date(filter.lastChanged).toLocaleDateString() : 'Never'}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Next Change:</strong> {new Date(filter.nextChangeDate).toLocaleDateString()}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Brand:</strong> {filter.brand || 'Not specified'}
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                    
                    <CardActions>
                      <Button
                        startIcon={<ServiceIcon />}
                        onClick={() => {
                          setSelectedFilter(filter);
                          setFilterChangeDialogOpen(true);
                        }}
                        color={getFilterStatusColor(filter)}
                      >
                        Record Filter Change
                      </Button>
                    </CardActions>
                  </Card>
                );
              })}
            </Box>
          )}
          
          {/* Service History Tab */}
          {activeTab === 1 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Service History
              </Typography>
              
              {selectedUnit.serviceHistory && selectedUnit.serviceHistory.length > 0 ? (
                <List>
                  {selectedUnit.serviceHistory
                    .sort((a, b) => new Date(b.serviceDate) - new Date(a.serviceDate))
                    .map((service, index) => (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemIcon>
                            {hvacService.getServiceTypes().find(t => t.value === service.serviceType)?.icon || <ServiceIcon />}
                          </ListItemIcon>
                          <ListItemText
                            primary={service.description}
                            secondary={
                              <Box>
                                <Typography variant="caption">
                                  {new Date(service.serviceDate).toLocaleDateString()} • {service.serviceType.replace(/_/g, ' ')}
                                </Typography>
                                {service.cost && (
                                  <Typography variant="caption" display="block">
                                    Cost: ${service.cost}
                                  </Typography>
                                )}
                                {service.workPerformed && (
                                  <Typography variant="caption" display="block">
                                    {service.workPerformed}
                                  </Typography>
                                )}
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < selectedUnit.serviceHistory.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                </List>
              ) : (
                <Typography color="text.secondary">
                  No service history recorded
                </Typography>
              )}
            </Box>
          )}
          
          {/* Maintenance Tab */}
          {activeTab === 2 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Maintenance Schedule
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Last Inspection:</strong> {' '}
                    {selectedUnit.maintenanceSchedule?.lastInspection 
                      ? new Date(selectedUnit.maintenanceSchedule.lastInspection).toLocaleDateString()
                      : 'Never'
                    }
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">
                    <strong>Next Inspection:</strong> {' '}
                    {selectedUnit.maintenanceSchedule?.nextInspection 
                      ? new Date(selectedUnit.maintenanceSchedule.nextInspection).toLocaleDateString()
                      : 'Not scheduled'
                    }
                  </Typography>
                </Grid>
              </Grid>
              
              <Button
                startIcon={<ScheduleIcon />}
                onClick={() => setInspectionDialogOpen(true)}
                sx={{ mt: 2 }}
              >
                Schedule Inspection
              </Button>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setServiceRecordDialogOpen(true)} startIcon={<AddIcon />}>
            Add Service Record
          </Button>
          <Button onClick={() => setSelectedUnit(null)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render filter change dialog
  const renderFilterChangeDialog = () => (
    <Dialog open={filterChangeDialogOpen} onClose={() => setFilterChangeDialogOpen(false)} maxWidth="sm" fullWidth>
      <DialogTitle>Record Filter Change</DialogTitle>
      <DialogContent>
        {selectedFilter && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              {selectedFilter.position} Filter ({selectedFilter.size})
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Change Date"
                  type="date"
                  value={filterChangeForm.changeDate}
                  onChange={(e) => setFilterChangeForm({...filterChangeForm, changeDate: e.target.value})}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Cost"
                  type="number"
                  value={filterChangeForm.cost}
                  onChange={(e) => setFilterChangeForm({...filterChangeForm, cost: e.target.value})}
                  InputProps={{ startAdornment: '$' }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="New Filter Brand"
                  value={filterChangeForm.newFilterBrand}
                  onChange={(e) => setFilterChangeForm({...filterChangeForm, newFilterBrand: e.target.value})}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Part Number"
                  value={filterChangeForm.newFilterPartNumber}
                  onChange={(e) => setFilterChangeForm({...filterChangeForm, newFilterPartNumber: e.target.value})}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={filterChangeForm.notes}
                  onChange={(e) => setFilterChangeForm({...filterChangeForm, notes: e.target.value})}
                />
              </Grid>
            </Grid>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setFilterChangeDialogOpen(false)}>Cancel</Button>
        <Button onClick={handleFilterChange} variant="contained">Record Change</Button>
      </DialogActions>
    </Dialog>
  );

  // Render service record dialog
  const renderServiceRecordDialog = () => (
    <Dialog open={serviceRecordDialogOpen} onClose={() => setServiceRecordDialogOpen(false)} maxWidth="md" fullWidth>
      <DialogTitle>Add Service Record</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Service Date"
              type="date"
              value={serviceRecordForm.serviceDate}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, serviceDate: e.target.value})}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Service Type</InputLabel>
              <Select
                value={serviceRecordForm.serviceType}
                onChange={(e) => setServiceRecordForm({...serviceRecordForm, serviceType: e.target.value})}
                label="Service Type"
              >
                {hvacService.getServiceTypes().map(type => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              value={serviceRecordForm.description}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, description: e.target.value})}
              required
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Cost"
              type="number"
              value={serviceRecordForm.cost}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, cost: e.target.value})}
              InputProps={{ startAdornment: '$' }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Next Service Date"
              type="date"
              value={serviceRecordForm.nextServiceDate}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, nextServiceDate: e.target.value})}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Work Performed"
              multiline
              rows={3}
              value={serviceRecordForm.workPerformed}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, workPerformed: e.target.value})}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Parts Replaced"
              value={serviceRecordForm.partsReplaced}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, partsReplaced: e.target.value})}
              placeholder="Comma separated list"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Recommendations"
              multiline
              rows={2}
              value={serviceRecordForm.recommendations}
              onChange={(e) => setServiceRecordForm({...serviceRecordForm, recommendations: e.target.value})}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setServiceRecordDialogOpen(false)}>Cancel</Button>
        <Button onClick={handleServiceRecord} variant="contained">Add Service Record</Button>
      </DialogActions>
    </Dialog>
  );

  // Render inspection scheduling dialog
  const renderInspectionDialog = () => (
    <Dialog open={inspectionDialogOpen} onClose={() => setInspectionDialogOpen(false)} maxWidth="sm" fullWidth>
      <DialogTitle>Schedule Inspection</DialogTitle>
      <DialogContent>
        <TextField
          fullWidth
          label="Inspection Date"
          type="date"
          value={inspectionForm.inspectionDate}
          onChange={(e) => setInspectionForm({...inspectionForm, inspectionDate: e.target.value})}
          InputLabelProps={{ shrink: true }}
          sx={{ mt: 2 }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setInspectionDialogOpen(false)}>Cancel</Button>
        <Button onClick={handleScheduleInspection} variant="contained">Schedule</Button>
      </DialogActions>
    </Dialog>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading HVAC service data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">
          HVAC Service Tracker
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          onClick={loadData}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Alerts Overview */}
      {renderAlertsOverview()}

      {/* Units Grid/Table */}
      {renderUnitsGrid()}

      {/* Dialogs */}
      {renderUnitDetails()}
      {renderFilterChangeDialog()}
      {renderServiceRecordDialog()}
      {renderInspectionDialog()}
    </Box>
  );
};

export default HVACServiceTracker;