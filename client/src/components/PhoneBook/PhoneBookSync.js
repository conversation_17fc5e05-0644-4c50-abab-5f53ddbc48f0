import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  FormControl,
  FormControlLabel,
  Switch,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  CircularProgress,
  Alert,
  AlertTitle,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormGroup,
  Checkbox,
  Tooltip
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import DownloadIcon from '@mui/icons-material/Download';
import GoogleIcon from '@mui/icons-material/Google';
import AppleIcon from '@mui/icons-material/Apple';
import InfoIcon from '@mui/icons-material/Info';
import HistoryIcon from '@mui/icons-material/History';
import contactSyncService from '../../services/contactSyncService';

// Define tab indices
const SYNC_TABS = {
  GOOGLE: 0,
  APPLE: 1
};

// Define sync frequency options (in minutes)
const SYNC_FREQUENCY_OPTIONS = [
  { value: 60, label: '1 hour' },
  { value: 180, label: '3 hours' },
  { value: 360, label: '6 hours' },
  { value: 720, label: '12 hours' },
  { value: 1440, label: '1 day' },
  { value: 10080, label: '1 week' }
];

// Define category options
const CATEGORY_OPTIONS = [
  { value: 'business', label: 'Business' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'support', label: 'Support' },
  { value: 'emergency', label: 'Emergency' },
  { value: 'other', label: 'Other' }
];

const PhoneBookSync = () => {
  // State for tabs
  const [activeTab, setActiveTab] = useState(SYNC_TABS.GOOGLE);

  // State for Google sync
  const [googleSyncStatus, setGoogleSyncStatus] = useState(null);
  const [googleSyncEnabled, setGoogleSyncEnabled] = useState(false);
  const [googleSyncFrequency, setGoogleSyncFrequency] = useState(60);
  const [googleSyncCategories, setGoogleSyncCategories] = useState([]);
  const [googleSyncLoading, setGoogleSyncLoading] = useState(false);
  const [googleSyncError, setGoogleSyncError] = useState(null);

  // State for Apple sync
  const [appleSyncStatus, setAppleSyncStatus] = useState(null);
  const [appleSyncEnabled, setAppleSyncEnabled] = useState(false);
  const [appleSyncCategories, setAppleSyncCategories] = useState([]);
  const [appleSyncLoading, setAppleSyncLoading] = useState(false);
  const [appleSyncError, setAppleSyncError] = useState(null);

  // State for sync logs
  const [syncLogs, setSyncLogs] = useState([]);
  const [logsLoading, setLogsLoading] = useState(false);
  const [openLogsDialog, setOpenLogsDialog] = useState(false);

  // Load sync status on component mount
  useEffect(() => {
    loadGoogleSyncStatus();
    loadAppleSyncStatus();
  }, []);

  // Load Google sync status
  const loadGoogleSyncStatus = async () => {
    try {
      setGoogleSyncLoading(true);
      setGoogleSyncError(null);
      
      const status = await contactSyncService.getGoogleSyncStatus();
      
      setGoogleSyncStatus(status);
      
      if (status.configured) {
        setGoogleSyncEnabled(status.enabled);
        setGoogleSyncFrequency(status.syncFrequency);
        setGoogleSyncCategories(status.syncCategories || []);
      }
    } catch (error) {
      console.error('Error loading Google sync status:', error);
      setGoogleSyncError('Failed to load Google sync status. Please try again.');
    } finally {
      setGoogleSyncLoading(false);
    }
  };

  // Load Apple sync status
  const loadAppleSyncStatus = async () => {
    try {
      setAppleSyncLoading(true);
      setAppleSyncError(null);
      
      const status = await contactSyncService.getAppleSyncStatus();
      
      setAppleSyncStatus(status);
      
      if (status.configured) {
        setAppleSyncEnabled(status.enabled);
        setAppleSyncCategories(status.syncCategories || []);
      }
    } catch (error) {
      console.error('Error loading Apple sync status:', error);
      setAppleSyncError('Failed to load Apple sync status. Please try again.');
    } finally {
      setAppleSyncLoading(false);
    }
  };

  // Load sync logs
  const loadSyncLogs = async () => {
    try {
      setLogsLoading(true);
      
      const provider = activeTab === SYNC_TABS.GOOGLE ? 'google' : 'apple';
      const response = await contactSyncService.getSyncLogs({ provider, limit: 10 });
      
      setSyncLogs(response.logs);
    } catch (error) {
      console.error('Error loading sync logs:', error);
    } finally {
      setLogsLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle Google auth
  const handleGoogleAuth = async () => {
    try {
      setGoogleSyncLoading(true);
      setGoogleSyncError(null);
      
      const { authUrl } = await contactSyncService.getGoogleAuthUrl();
      
      // Redirect to Google auth URL
      window.location.href = authUrl;
    } catch (error) {
      console.error('Error getting Google auth URL:', error);
      setGoogleSyncError('Failed to connect to Google. Please try again.');
      setGoogleSyncLoading(false);
    }
  };

  // Handle Google sync now
  const handleGoogleSyncNow = async () => {
    try {
      setGoogleSyncLoading(true);
      setGoogleSyncError(null);
      
      await contactSyncService.syncNowGoogle();
      
      // Reload status after a short delay
      setTimeout(() => {
        loadGoogleSyncStatus();
      }, 1000);
    } catch (error) {
      console.error('Error triggering Google sync:', error);
      setGoogleSyncError('Failed to sync with Google. Please try again.');
      setGoogleSyncLoading(false);
    }
  };

  // Handle Google sync configuration
  const handleSaveGoogleConfig = async () => {
    try {
      setGoogleSyncLoading(true);
      setGoogleSyncError(null);
      
      await contactSyncService.configureGoogleSync({
        enabled: googleSyncEnabled,
        syncFrequency: googleSyncFrequency,
        syncCategories: googleSyncCategories
      });
      
      // Reload status
      await loadGoogleSyncStatus();
    } catch (error) {
      console.error('Error saving Google sync configuration:', error);
      setGoogleSyncError('Failed to save configuration. Please try again.');
      setGoogleSyncLoading(false);
    }
  };

  // Handle Apple sync configuration
  const handleSaveAppleConfig = async () => {
    try {
      setAppleSyncLoading(true);
      setAppleSyncError(null);
      
      await contactSyncService.configureAppleSync({
        enabled: appleSyncEnabled,
        syncCategories: appleSyncCategories
      });
      
      // Reload status
      await loadAppleSyncStatus();
    } catch (error) {
      console.error('Error saving Apple sync configuration:', error);
      setAppleSyncError('Failed to save configuration. Please try again.');
      setAppleSyncLoading(false);
    }
  };

  // Handle category toggle
  const handleCategoryToggle = (category, provider) => {
    if (provider === 'google') {
      const newCategories = [...googleSyncCategories];
      
      if (newCategories.includes(category)) {
        // Remove category
        const index = newCategories.indexOf(category);
        newCategories.splice(index, 1);
      } else {
        // Add category
        newCategories.push(category);
      }
      
      setGoogleSyncCategories(newCategories);
    } else if (provider === 'apple') {
      const newCategories = [...appleSyncCategories];
      
      if (newCategories.includes(category)) {
        // Remove category
        const index = newCategories.indexOf(category);
        newCategories.splice(index, 1);
      } else {
        // Add category
        newCategories.push(category);
      }
      
      setAppleSyncCategories(newCategories);
    }
  };

  // Handle logs dialog
  const handleOpenLogsDialog = () => {
    loadSyncLogs();
    setOpenLogsDialog(true);
  };

  const handleCloseLogsDialog = () => {
    setOpenLogsDialog(false);
  };

  // Render Google sync tab
  const renderGoogleSyncTab = () => {
    if (googleSyncLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (googleSyncError) {
      return (
        <Alert severity="error" sx={{ mt: 2 }}>
          {googleSyncError}
        </Alert>
      );
    }

    if (!googleSyncStatus || !googleSyncStatus.configured) {
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" gutterBottom>
            Connect your Google account to sync contacts with Google Contacts.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<GoogleIcon />}
            onClick={handleGoogleAuth}
            sx={{ mt: 2 }}
          >
            Connect to Google
          </Button>
        </Box>
      );
    }

    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Google Contacts Sync</AlertTitle>
          Sync your phone book contacts with Google Contacts. Your contacts will be added to a group named "CSF Portal Contacts".
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormGroup>
              <FormControlLabel
                control={
                  <Switch
                    checked={googleSyncEnabled}
                    onChange={(e) => setGoogleSyncEnabled(e.target.checked)}
                    color="primary"
                  />
                }
                label="Enable automatic sync"
              />
            </FormGroup>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth disabled={!googleSyncEnabled}>
              <InputLabel>Sync Frequency</InputLabel>
              <Select
                value={googleSyncFrequency}
                onChange={(e) => setGoogleSyncFrequency(e.target.value)}
                label="Sync Frequency"
              >
                {SYNC_FREQUENCY_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Categories to Sync
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {CATEGORY_OPTIONS.map((option) => (
                <FormControlLabel
                  key={option.value}
                  control={
                    <Checkbox
                      checked={googleSyncCategories.includes(option.value)}
                      onChange={() => handleCategoryToggle(option.value, 'google')}
                    />
                  }
                  label={option.label}
                />
              ))}
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSaveGoogleConfig}
              >
                Save Configuration
              </Button>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<RefreshIcon />}
                onClick={handleGoogleSyncNow}
                disabled={googleSyncStatus.syncStatus === 'in_progress'}
              >
                Sync Now
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              Sync Status
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Chip
                label={contactSyncService.formatSyncStatus(googleSyncStatus.syncStatus).label}
                color={contactSyncService.formatSyncStatus(googleSyncStatus.syncStatus).color}
              />
              <Typography variant="body2">
                Last sync: {contactSyncService.formatDate(googleSyncStatus.lastSyncTime)}
              </Typography>
              <IconButton size="small" onClick={handleOpenLogsDialog}>
                <HistoryIcon />
              </IconButton>
            </Box>
            {googleSyncStatus.syncError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {googleSyncStatus.syncError}
              </Alert>
            )}
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render Apple sync tab
  const renderAppleSyncTab = () => {
    if (appleSyncLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (appleSyncError) {
      return (
        <Alert severity="error" sx={{ mt: 2 }}>
          {appleSyncError}
        </Alert>
      );
    }

    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Apple Contacts Sync</AlertTitle>
          Download your phone book contacts as a vCard file that can be imported into the iOS Contacts app.
        </Alert>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Categories to Include
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {CATEGORY_OPTIONS.map((option) => (
                <FormControlLabel
                  key={option.value}
                  control={
                    <Checkbox
                      checked={appleSyncCategories.includes(option.value)}
                      onChange={() => handleCategoryToggle(option.value, 'apple')}
                    />
                  }
                  label={option.label}
                />
              ))}
            </Box>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSaveAppleConfig}
              >
                Save Configuration
              </Button>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<DownloadIcon />}
                href={contactSyncService.getAppleVCardUrl(appleSyncCategories)}
                target="_blank"
                download
              >
                Download vCard
              </Button>
            </Box>
          </Grid>

          {appleSyncStatus && appleSyncStatus.configured && (
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Sync Status
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="body2">
                  Last download: {contactSyncService.formatDate(appleSyncStatus.lastSyncTime)}
                </Typography>
                <IconButton size="small" onClick={handleOpenLogsDialog}>
                  <HistoryIcon />
                </IconButton>
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // Render sync logs dialog
  const renderSyncLogsDialog = () => {
    return (
      <Dialog
        open={openLogsDialog}
        onClose={handleCloseLogsDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Sync Logs</DialogTitle>
        <DialogContent>
          {logsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List>
              {syncLogs.length > 0 ? (
                syncLogs.map((log) => (
                  <ListItem key={log._id}>
                    <ListItemText
                      primary={`${log.provider.charAt(0).toUpperCase() + log.provider.slice(1)} Sync`}
                      secondary={
                        <>
                          <Typography variant="body2" component="span">
                            {contactSyncService.formatDate(log.startTime)}
                          </Typography>
                          <br />
                          <Typography variant="body2" component="span">
                            Status: {log.status}
                          </Typography>
                          {log.details && (
                            <>
                              <br />
                              <Typography variant="body2" component="span">
                                {log.details}
                              </Typography>
                            </>
                          )}
                        </>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Chip
                        label={contactSyncService.formatSyncStatus(log.status).label}
                        color={contactSyncService.formatSyncStatus(log.status).color}
                        size="small"
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                ))
              ) : (
                <Typography variant="body1" align="center">
                  No sync logs found
                </Typography>
              )}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseLogsDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <Paper sx={{ mt: 3 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="sync tabs"
          variant="fullWidth"
        >
          <Tab
            icon={<GoogleIcon />}
            label="Google Contacts"
            id="sync-tab-0"
            aria-controls="sync-tabpanel-0"
          />
          <Tab
            icon={<AppleIcon />}
            label="Apple Contacts"
            id="sync-tab-1"
            aria-controls="sync-tabpanel-1"
          />
        </Tabs>
      </Box>
      
      <Box
        role="tabpanel"
        hidden={activeTab !== SYNC_TABS.GOOGLE}
        id="sync-tabpanel-0"
        aria-labelledby="sync-tab-0"
      >
        {activeTab === SYNC_TABS.GOOGLE && renderGoogleSyncTab()}
      </Box>
      
      <Box
        role="tabpanel"
        hidden={activeTab !== SYNC_TABS.APPLE}
        id="sync-tabpanel-1"
        aria-labelledby="sync-tab-1"
      >
        {activeTab === SYNC_TABS.APPLE && renderAppleSyncTab()}
      </Box>
      
      {renderSyncLogsDialog()}
    </Paper>
  );
};

export default PhoneBookSync;