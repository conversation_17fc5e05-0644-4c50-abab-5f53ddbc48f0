import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  InputAdornment,
  CircularProgress,
  Alert,
  Collapse
} from '@mui/material';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import BusinessIcon from '@mui/icons-material/Business';
import CategoryIcon from '@mui/icons-material/Category';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import ConstructionIcon from '@mui/icons-material/Construction';
import SupportIcon from '@mui/icons-material/Support';
import EmergencyIcon from '@mui/icons-material/ReportProblem';
import WorkIcon from '@mui/icons-material/Work';
import HelpIcon from '@mui/icons-material/Help';
import SyncIcon from '@mui/icons-material/Sync';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import buildingManagementService from '../../services/buildingManagementService';
import PhoneBookSync from './PhoneBookSync';

// Define tab indices for view modes
const VIEW_MODES = {
  ALL: 0,
  BUSINESS: 1,
  MAINTENANCE: 2,
  SUPPORT: 3,
  EMERGENCY: 4,
  OTHER: 5
};

// Map categories to icons
const CATEGORY_ICONS = {
  'business': <WorkIcon />,
  'maintenance': <ConstructionIcon />,
  'support': <SupportIcon />,
  'emergency': <EmergencyIcon />,
  'other': <HelpIcon />
};

const PhoneBook = () => {
  // State for contacts
  const [contacts, setContacts] = useState([]);
  const [filteredContacts, setFilteredContacts] = useState([]);
  const [selectedContact, setSelectedContact] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState(VIEW_MODES.ALL);
  
  // State for sync panel
  const [syncPanelOpen, setSyncPanelOpen] = useState(false);

  // State for dialogs
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  // State for form data
  const [formData, setFormData] = useState({
    name: '',
    category: 'business',
    phone: '',
    email: '',
    company: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    },
    notes: ''
  });

  // Fetch contacts on component mount
  useEffect(() => {
    const fetchContacts = async () => {
      try {
        setLoading(true);
        const data = await buildingManagementService.getContacts();
        setContacts(data);
        setFilteredContacts(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching contacts:', err);
        setError('Failed to load contacts. Please try again later.');
        setLoading(false);
      }
    };

    fetchContacts();
  }, []);

  // Filter contacts based on search term and view mode
  useEffect(() => {
    let filtered = [...contacts];

    // Filter by category based on view mode
    if (viewMode !== VIEW_MODES.ALL) {
      const categoryMap = {
        [VIEW_MODES.BUSINESS]: 'business',
        [VIEW_MODES.MAINTENANCE]: 'maintenance',
        [VIEW_MODES.SUPPORT]: 'support',
        [VIEW_MODES.EMERGENCY]: 'emergency',
        [VIEW_MODES.OTHER]: 'other'
      };
      
      filtered = filtered.filter(contact => contact.category === categoryMap[viewMode]);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(contact => 
        contact.name.toLowerCase().includes(term) ||
        (contact.company && contact.company.toLowerCase().includes(term)) ||
        contact.phone.includes(term) ||
        (contact.email && contact.email.toLowerCase().includes(term))
      );
    }

    setFilteredContacts(filtered);
  }, [contacts, searchTerm, viewMode]);

  // Handle view mode change
  const handleViewModeChange = (event, newValue) => {
    setViewMode(newValue);
  };

  // Handle search term change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle contact selection
  const handleContactSelect = (contact) => {
    setSelectedContact(contact);
  };

  // Handle form data change
  const handleFormChange = (event) => {
    const { name, value } = event.target;
    
    if (name.includes('.')) {
      // Handle nested fields (address)
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      // Handle top-level fields
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle add dialog
  const handleOpenAddDialog = () => {
    setFormData({
      name: '',
      category: 'business',
      phone: '',
      email: '',
      company: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA'
      },
      notes: ''
    });
    setOpenAddDialog(true);
  };

  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
  };

  const handleAddContact = async () => {
    try {
      const newContact = await buildingManagementService.createContact(formData);
      setContacts(prev => [...prev, newContact]);
      setOpenAddDialog(false);
    } catch (err) {
      console.error('Error adding contact:', err);
      setError('Failed to add contact. Please try again later.');
    }
  };

  // Handle edit dialog
  const handleOpenEditDialog = (contact) => {
    setFormData({
      name: contact.name,
      category: contact.category,
      phone: contact.phone,
      email: contact.email || '',
      company: contact.company || '',
      address: contact.address || {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA'
      },
      notes: contact.notes || ''
    });
    setSelectedContact(contact);
    setOpenEditDialog(true);
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const handleUpdateContact = async () => {
    try {
      const updatedContact = await buildingManagementService.updateContact(selectedContact._id, formData);
      setContacts(prev => prev.map(c => c._id === updatedContact._id ? updatedContact : c));
      setSelectedContact(updatedContact);
      setOpenEditDialog(false);
    } catch (err) {
      console.error('Error updating contact:', err);
      setError('Failed to update contact. Please try again later.');
    }
  };

  // Handle delete dialog
  const handleOpenDeleteDialog = (contact) => {
    setSelectedContact(contact);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleDeleteContact = async () => {
    try {
      await buildingManagementService.deleteContact(selectedContact._id);
      setContacts(prev => prev.filter(c => c._id !== selectedContact._id));
      setSelectedContact(null);
      setOpenDeleteDialog(false);
    } catch (err) {
      console.error('Error deleting contact:', err);
      setError('Failed to delete contact. Please try again later.');
    }
  };

  // Get category icon
  const getCategoryIcon = (category) => {
    return CATEGORY_ICONS[category] || <CategoryIcon />;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Phone Book
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button 
            variant="outlined" 
            color="primary"
            startIcon={syncPanelOpen ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            endIcon={<SyncIcon />}
            onClick={() => setSyncPanelOpen(!syncPanelOpen)}
          >
            {syncPanelOpen ? 'Hide Sync Options' : 'Sync Options'}
          </Button>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenAddDialog}
          >
            Add Contact
          </Button>
        </Box>
      </Box>
      
      {/* Sync Panel */}
      <Collapse in={syncPanelOpen}>
        <PhoneBookSync />
      </Collapse>

      <Grid container spacing={3}>
        {/* Search and Filter */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search contacts..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Tabs
                  value={viewMode}
                  onChange={handleViewModeChange}
                  indicatorColor="primary"
                  textColor="primary"
                  variant="scrollable"
                  scrollButtons="auto"
                >
                  <Tab label="All" />
                  <Tab label="Business" icon={<WorkIcon />} />
                  <Tab label="Maintenance" icon={<ConstructionIcon />} />
                  <Tab label="Support" icon={<SupportIcon />} />
                  <Tab label="Emergency" icon={<EmergencyIcon />} />
                  <Tab label="Other" icon={<HelpIcon />} />
                </Tabs>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Contact List */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardHeader title="Contacts" />
            <CardContent sx={{ p: 0 }}>
              {filteredContacts.length > 0 ? (
                <List>
                  {filteredContacts.map(contact => (
                    <React.Fragment key={contact._id}>
                      <ListItem 
                        button
                        selected={selectedContact && selectedContact._id === contact._id}
                        onClick={() => handleContactSelect(contact)}
                      >
                        <ListItemIcon>
                          {getCategoryIcon(contact.category)}
                        </ListItemIcon>
                        <ListItemText 
                          primary={contact.name} 
                          secondary={contact.company || contact.phone} 
                        />
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body1">No contacts found</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Contact Details */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%' }}>
            <CardHeader 
              title="Contact Details" 
              action={
                selectedContact && (
                  <Box>
                    <IconButton 
                      color="primary" 
                      onClick={() => handleOpenEditDialog(selectedContact)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      color="error" 
                      onClick={() => handleOpenDeleteDialog(selectedContact)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                )
              }
            />
            <CardContent>
              {selectedContact ? (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="h6">{selectedContact.name}</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      {getCategoryIcon(selectedContact.category)}
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        {selectedContact.category.charAt(0).toUpperCase() + selectedContact.category.slice(1)}
                      </Typography>
                    </Box>
                  </Grid>

                  {selectedContact.company && (
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ mr: 1 }} />
                        <Typography variant="body1">{selectedContact.company}</Typography>
                      </Box>
                    </Grid>
                  )}

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PhoneIcon sx={{ mr: 1 }} />
                      <Typography variant="body1">{selectedContact.phone}</Typography>
                    </Box>
                  </Grid>

                  {selectedContact.email && (
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EmailIcon sx={{ mr: 1 }} />
                        <Typography variant="body1">{selectedContact.email}</Typography>
                      </Box>
                    </Grid>
                  )}

                  {selectedContact.address && (selectedContact.address.street || selectedContact.address.city) && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>Address:</Typography>
                      <Typography variant="body2">
                        {selectedContact.address.street && `${selectedContact.address.street}, `}
                        {selectedContact.address.city && `${selectedContact.address.city}, `}
                        {selectedContact.address.state && `${selectedContact.address.state} `}
                        {selectedContact.address.zipCode && selectedContact.address.zipCode}
                      </Typography>
                    </Grid>
                  )}

                  {selectedContact.notes && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>Notes:</Typography>
                      <Typography variant="body2">{selectedContact.notes}</Typography>
                    </Grid>
                  )}
                </Grid>
              ) : (
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body1">Select a contact to view details</Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Contact Dialog */}
      <Dialog open={openAddDialog} onClose={handleCloseAddDialog} maxWidth="md" fullWidth>
        <DialogTitle>Add New Contact</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleFormChange}
                >
                  <MenuItem value="business">Business</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                  <MenuItem value="support">Support</MenuItem>
                  <MenuItem value="emergency">Emergency</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Company"
                name="company"
                value={formData.company}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>Address</Typography>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Street"
                name="address.street"
                value={formData.address.street}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="City"
                name="address.city"
                value={formData.address.city}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="State"
                name="address.state"
                value={formData.address.state}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Zip Code"
                name="address.zipCode"
                value={formData.address.zipCode}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="notes"
                value={formData.notes}
                onChange={handleFormChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddDialog}>Cancel</Button>
          <Button 
            onClick={handleAddContact} 
            variant="contained" 
            color="primary"
            disabled={!formData.name || !formData.phone}
          >
            Add Contact
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Contact Dialog */}
      <Dialog open={openEditDialog} onClose={handleCloseEditDialog} maxWidth="md" fullWidth>
        <DialogTitle>Edit Contact</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleFormChange}
                >
                  <MenuItem value="business">Business</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                  <MenuItem value="support">Support</MenuItem>
                  <MenuItem value="emergency">Emergency</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Company"
                name="company"
                value={formData.company}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>Address</Typography>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Street"
                name="address.street"
                value={formData.address.street}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="City"
                name="address.city"
                value={formData.address.city}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="State"
                name="address.state"
                value={formData.address.state}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Zip Code"
                name="address.zipCode"
                value={formData.address.zipCode}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="notes"
                value={formData.notes}
                onChange={handleFormChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Cancel</Button>
          <Button 
            onClick={handleUpdateContact} 
            variant="contained" 
            color="primary"
            disabled={!formData.name || !formData.phone}
          >
            Update Contact
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Contact Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Contact</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete {selectedContact?.name}? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button 
            onClick={handleDeleteContact} 
            variant="contained" 
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PhoneBook;