import React, { useState, useEffect, useRef } from 'react';
import {
  Paper,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Badge
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';
import websocketService from '../../services/websocketService';

const LiveActivityLog = () => {
  const [activityLog, setActivityLog] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [newActivityCount, setNewActivityCount] = useState(0);
  const intervalRef = useRef(null);
  const lastUpdateRef = useRef(null);

  // Fetch activity log
  const fetchActivityLog = async (showLoading = false) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      const filters = {
        limit: 100,
        startDate: lastUpdateRef.current || new Date(Date.now() - 60 * 60 * 1000).toISOString()
      };

      const newActivity = await lenelS2NetBoxService.getLiveActivityLog(filters);
      
      if (lastUpdateRef.current && newActivity.length > 0) {
        // Count new activities since last update
        const newCount = newActivity.filter(activity => 
          new Date(activity.timestamp) > new Date(lastUpdateRef.current)
        ).length;
        
        if (newCount > 0) {
          setNewActivityCount(prev => prev + newCount);
        }
      }

      setActivityLog(newActivity);
      lastUpdateRef.current = new Date().toISOString();
    } catch (err) {
      console.error('Error fetching activity log:', err);
      setError('Failed to fetch activity log. Please try again.');
    } finally {
      if (showLoading) setLoading(false);
    }
  };

  // WebSocket event handler for real-time updates
  const handleWebSocketUpdate = (newActivity) => {
    if (newActivity && Array.isArray(newActivity)) {
      setActivityLog(prev => {
        // Merge new activities with existing ones, avoiding duplicates
        const existingIds = new Set(prev.map(activity => activity.id));
        const uniqueNewActivities = newActivity.filter(activity => !existingIds.has(activity.id));

        if (uniqueNewActivities.length > 0) {
          setNewActivityCount(prevCount => prevCount + uniqueNewActivities.length);
          return [...uniqueNewActivities, ...prev].slice(0, 100); // Keep only latest 100
        }

        return prev;
      });
    }
  };

  // Set up WebSocket connection and auto refresh
  useEffect(() => {
    // Try to connect to WebSocket for real-time updates
    websocketService.connect().then(() => {
      console.log('WebSocket connected for activity log');
      websocketService.addEventListener('activity-log', handleWebSocketUpdate);
    }).catch(error => {
      console.warn('WebSocket connection failed, falling back to polling:', error);
    });

    if (autoRefresh) {
      // Initial fetch
      fetchActivityLog(true);

      // Set up interval for auto refresh (as fallback or primary method)
      intervalRef.current = setInterval(() => {
        fetchActivityLog(false);
      }, 10000); // Refresh every 10 seconds
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      // Clean up WebSocket listener
      websocketService.removeEventListener('activity-log', handleWebSocketUpdate);
    };
  }, [autoRefresh]);

  // Handle manual refresh
  const handleRefresh = () => {
    setNewActivityCount(0);
    fetchActivityLog(true);
  };

  // Toggle auto refresh
  const handleAutoRefreshToggle = (event) => {
    setAutoRefresh(event.target.checked);
    if (!event.target.checked) {
      setNewActivityCount(0);
    }
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Live Activity Log
          {newActivityCount > 0 && (
            <Badge badgeContent={newActivityCount} color="primary" sx={{ ml: 2 }}>
              <span></span>
            </Badge>
          )}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={autoRefresh}
                onChange={handleAutoRefreshToggle}
                color="primary"
              />
            }
            label="Auto Refresh"
          />
          
          <Tooltip title="Refresh Now">
            <IconButton onClick={handleRefresh} disabled={loading}>
              {loading ? <CircularProgress size={20} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Timestamp</TableCell>
              <TableCell>Event</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Location</TableCell>
              <TableCell>Person</TableCell>
              <TableCell>Severity</TableCell>
              <TableCell>Description</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {activityLog.length > 0 ? (
              activityLog.map((activity, index) => (
                <TableRow 
                  key={activity.id || index}
                  sx={{ 
                    '&:nth-of-type(odd)': { backgroundColor: 'action.hover' },
                    opacity: new Date(activity.timestamp) > new Date(lastUpdateRef.current || 0) ? 1 : 0.8
                  }}
                >
                  <TableCell>
                    <Typography variant="body2">
                      {formatTimestamp(activity.timestamp)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {activity.eventName}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {activity.type || 'Event'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {activity.portalName || activity.readerName || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {activity.personId || 'System'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={activity.severity || 'Info'}
                      color={getSeverityColor(activity.severity)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {activity.description || activity.reason || 'No description'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  <Typography variant="body2" color="text.secondary">
                    {loading ? 'Loading activity log...' : 'No recent activity found'}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {autoRefresh && (
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PlayIcon fontSize="small" />
            Auto-refreshing every 10 seconds
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

export default LiveActivityLog;
