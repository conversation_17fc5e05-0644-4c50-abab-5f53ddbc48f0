import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box
} from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';

/**
 * A reusable dialog component that displays a "Feature Coming Soon" message
 * to replace the generic alert popups for features that are not yet implemented.
 */
const FeatureComingSoonDialog = ({ 
  open, 
  onClose, 
  title = "Feature Coming Soon", 
  featureName,
  entityId = null
}) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <InfoIcon color="primary" />
          {title}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Typography variant="body1" gutterBottom>
          {featureName} {entityId ? `for ID: ${entityId}` : ''} is currently under development and will be available soon.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Our team is working to implement this functionality. Thank you for your patience.
        </Typography>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} variant="contained" color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FeatureComingSoonDialog;