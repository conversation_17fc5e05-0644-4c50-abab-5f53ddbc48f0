import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Alert,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import {
  Sensors as SensorsIcon,
  Visibility as VisibilityIcon,
  Group as GroupIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';

const ReaderManagement = () => {
  const [readers, setReaders] = useState([]);
  const [readerGroups, setReaderGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedReader, setSelectedReader] = useState(null);
  const [selectedReaderGroup, setSelectedReaderGroup] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Fetch readers and reader groups
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [readersData, readerGroupsData] = await Promise.all([
        lenelS2NetBoxService.getReaders(),
        lenelS2NetBoxService.getReaderGroups()
      ]);

      setReaders(readersData);
      setReaderGroups(readerGroupsData);
    } catch (err) {
      console.error('Error fetching reader data:', err);
      setError('Failed to fetch reader data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, []);

  // Handle view reader details
  const handleViewReaderDetails = async (reader) => {
    try {
      setDetailsLoading(true);
      setSelectedReader(reader);
      setDetailsOpen(true);

      // Fetch detailed information
      const details = await lenelS2NetBoxService.getReaderDetails(reader.id);
      setSelectedReader(details);
    } catch (err) {
      console.error('Error fetching reader details:', err);
      setError('Failed to fetch reader details');
    } finally {
      setDetailsLoading(false);
    }
  };

  // Handle view reader group details
  const handleViewReaderGroupDetails = async (readerGroup) => {
    try {
      setDetailsLoading(true);
      setSelectedReaderGroup(readerGroup);
      setDetailsOpen(true);

      // Fetch detailed information
      const details = await lenelS2NetBoxService.getReaderGroupDetails(readerGroup.id);
      setSelectedReaderGroup(details);
    } catch (err) {
      console.error('Error fetching reader group details:', err);
      setError('Failed to fetch reader group details');
    } finally {
      setDetailsLoading(false);
    }
  };

  // Handle close details
  const handleCloseDetails = () => {
    setDetailsOpen(false);
    setSelectedReader(null);
    setSelectedReaderGroup(null);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getStatusColor = (status, online) => {
    if (online) return 'success';
    return 'error';
  };

  const getStatusIcon = (online) => {
    return online ? <CheckCircleIcon /> : <ErrorIcon />;
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Reader Management
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab 
            icon={<SensorsIcon />} 
            iconPosition="start" 
            label="Readers" 
          />
          <Tab 
            icon={<GroupIcon />} 
            iconPosition="start" 
            label="Reader Groups" 
          />
        </Tabs>
      </Box>

      {/* Readers Tab */}
      {activeTab === 0 && (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Readers</Typography>
            <Button 
              variant="contained" 
              color="primary" 
              startIcon={<AddIcon />}
              onClick={() => alert("Create Reader functionality will be implemented here")}
            >
              Add Reader
            </Button>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : readers.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Portal</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Last Activity</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {readers.map((reader) => (
                    <TableRow key={reader.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(reader.online)}
                          {reader.name}
                        </Box>
                      </TableCell>
                      <TableCell>{reader.type}</TableCell>
                      <TableCell>{reader.portalName || 'N/A'}</TableCell>
                      <TableCell>
                        <Chip 
                          label={reader.status} 
                          color={getStatusColor(reader.status, reader.online)} 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell>
                        {reader.lastActivity ? new Date(reader.lastActivity).toLocaleString() : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="View Details">
                            <IconButton 
                              size="small" 
                              color="primary" 
                              onClick={() => handleViewReaderDetails(reader)}
                            >
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit Reader">
                            <IconButton 
                              size="small" 
                              color="primary" 
                              onClick={() => alert(`Edit Reader ${reader.id} functionality will be implemented here`)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Reader">
                            <IconButton 
                              size="small" 
                              color="error" 
                              onClick={() => alert(`Delete Reader ${reader.id} functionality will be implemented here`)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography variant="body1">No readers found.</Typography>
          )}
        </Paper>
      )}

      {/* Reader Groups Tab */}
      {activeTab === 1 && (
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">Reader Groups</Typography>
            <Button 
              variant="contained" 
              color="primary" 
              startIcon={<AddIcon />}
              onClick={() => alert("Create Reader Group functionality will be implemented here")}
            >
              Add Reader Group
            </Button>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : readerGroups.length > 0 ? (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Reader Count</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {readerGroups.map((group) => (
                    <TableRow key={group.id}>
                      <TableCell>{group.name}</TableCell>
                      <TableCell>{group.description || 'N/A'}</TableCell>
                      <TableCell>{group.readerCount}</TableCell>
                      <TableCell>
                        <Chip 
                          label={group.status} 
                          color="success" 
                          size="small" 
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="View Details">
                            <IconButton 
                              size="small" 
                              color="primary" 
                              onClick={() => handleViewReaderGroupDetails(group)}
                            >
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit Reader Group">
                            <IconButton 
                              size="small" 
                              color="primary" 
                              onClick={() => alert(`Edit Reader Group ${group.id} functionality will be implemented here`)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Reader Group">
                            <IconButton 
                              size="small" 
                              color="error" 
                              onClick={() => alert(`Delete Reader Group ${group.id} functionality will be implemented here`)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography variant="body1">No reader groups found.</Typography>
          )}
        </Paper>
      )}

      {/* Details Dialog */}
      <Dialog open={detailsOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon />
            {selectedReader ? `Reader: ${selectedReader.name}` : 
             selectedReaderGroup ? `Reader Group: ${selectedReaderGroup.name}` : 'Details'}
            {detailsLoading && (
              <CircularProgress size={20} sx={{ ml: 1 }} />
            )}
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {selectedReader && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Reader Information
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Typography variant="body2">
                        <strong>ID:</strong> {selectedReader.id}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Name:</strong> {selectedReader.name}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Type:</strong> {selectedReader.type}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Description:</strong> {selectedReader.description || 'N/A'}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <strong>Status:</strong>
                        <Chip
                          label={selectedReader.status}
                          color={getStatusColor(selectedReader.status, selectedReader.online)}
                          size="small"
                        />
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Portal Information
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Typography variant="body2">
                        <strong>Portal Key:</strong> {selectedReader.portalKey || 'N/A'}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Portal Name:</strong> {selectedReader.portalName || 'N/A'}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Last Activity:</strong> {selectedReader.lastActivity ? new Date(selectedReader.lastActivity).toLocaleString() : 'N/A'}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {selectedReaderGroup && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Group Information
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Typography variant="body2">
                        <strong>ID:</strong> {selectedReaderGroup.id}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Name:</strong> {selectedReaderGroup.name}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Description:</strong> {selectedReaderGroup.description || 'N/A'}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Reader Count:</strong> {selectedReaderGroup.readerCount}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Readers in Group
                    </Typography>
                    {selectedReaderGroup.readers && selectedReaderGroup.readers.length > 0 ? (
                      <List dense>
                        {selectedReaderGroup.readers.map((reader) => (
                          <ListItem key={reader.key}>
                            <ListItemText
                              primary={reader.name}
                              secondary={reader.portalName ? `Portal: ${reader.portalName}` : 'No portal assigned'}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No readers in this group
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReaderManagement;
