import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  TextField,
  Divider
} from '@mui/material';
import {
  Elevator as ElevatorIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
  Stop as StopIcon,
  Security as SecurityIcon,
  AccessTime as TimeIcon,
  People as PeopleIcon
} from '@mui/icons-material';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';

const ElevatorControl = ({ 
  open, 
  onClose, 
  elevator, 
  onElevatorControlled 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [elevatorStatus, setElevatorStatus] = useState(null);
  const [controlAction, setControlAction] = useState('enable');
  const [selectedFloors, setSelectedFloors] = useState([]);
  const [accessLevel, setAccessLevel] = useState('');
  const [duration, setDuration] = useState('');

  // Fetch elevator status when dialog opens
  useEffect(() => {
    if (open && elevator?.id) {
      fetchElevatorStatus();
    }
  }, [open, elevator]);

  // Fetch elevator status
  const fetchElevatorStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const status = await lenelS2NetBoxService.getElevatorStatus(elevator.id);
      setElevatorStatus(status);
    } catch (err) {
      console.error('Error fetching elevator status:', err);
      setError('Failed to fetch elevator status');
    } finally {
      setLoading(false);
    }
  };

  // Handle floor selection
  const handleFloorToggle = (floorNumber) => {
    setSelectedFloors(prev => 
      prev.includes(floorNumber)
        ? prev.filter(f => f !== floorNumber)
        : [...prev, floorNumber]
    );
  };

  // Handle control action submission
  const handleControlSubmit = async () => {
    if (!elevator?.id) {
      setError('No elevator selected');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const controlData = {
        action: controlAction,
        floors: selectedFloors,
        accessLevel: accessLevel || null,
        duration: duration ? parseInt(duration) : null
      };

      const result = await lenelS2NetBoxService.controlElevator(elevator.id, controlData);
      
      setSuccess(result.message);
      
      // Refresh elevator status
      await fetchElevatorStatus();
      
      // Notify parent component
      if (onElevatorControlled) {
        onElevatorControlled(elevator.id, controlData);
      }

      // Reset form
      setSelectedFloors([]);
      setAccessLevel('');
      setDuration('');

    } catch (err) {
      console.error('Error controlling elevator:', err);
      setError(err.message || 'Failed to control elevator');
    } finally {
      setLoading(false);
    }
  };

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setError(null);
      setSuccess(null);
      setControlAction('enable');
      setSelectedFloors([]);
      setAccessLevel('');
      setDuration('');
    }
  }, [open]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'online':
      case 'active':
        return 'success';
      case 'offline':
      case 'error':
        return 'error';
      case 'maintenance':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getDirectionIcon = (direction) => {
    switch (direction?.toLowerCase()) {
      case 'up':
        return <ArrowUpIcon />;
      case 'down':
        return <ArrowDownIcon />;
      default:
        return <StopIcon />;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ElevatorIcon />
          Elevator Control - {elevator?.name}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {/* Elevator Status */}
        {elevatorStatus && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Status
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Status
                    </Typography>
                    <Chip
                      label={elevatorStatus.status}
                      color={getStatusColor(elevatorStatus.status)}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Current Floor
                    </Typography>
                    <Typography variant="h4" sx={{ mt: 1 }}>
                      {elevatorStatus.currentFloor || 'N/A'}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Direction
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
                      {getDirectionIcon(elevatorStatus.direction)}
                      <Typography variant="body1" sx={{ ml: 1 }}>
                        {elevatorStatus.direction || 'Stationary'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Occupancy
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
                      <PeopleIcon />
                      <Typography variant="h6" sx={{ ml: 1 }}>
                        {elevatorStatus.occupancy || 0}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
              
              {elevatorStatus.lastActivity && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                  Last Activity: {new Date(elevatorStatus.lastActivity).toLocaleString()}
                </Typography>
              )}
            </CardContent>
          </Card>
        )}

        {/* Control Actions */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Control Actions
            </Typography>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Action</InputLabel>
              <Select
                value={controlAction}
                onChange={(e) => setControlAction(e.target.value)}
                label="Action"
              >
                <MenuItem value="enable">Enable Access</MenuItem>
                <MenuItem value="disable">Disable Access</MenuItem>
                <MenuItem value="restrict">Restrict Floors</MenuItem>
              </Select>
            </FormControl>

            {/* Floor Selection */}
            {elevator?.availableFloors && elevator.availableFloors.length > 0 && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Select Floors {controlAction === 'restrict' ? '(Restricted)' : '(Allowed)'}
                </Typography>
                
                <List dense>
                  {elevator.availableFloors.map((floor) => (
                    <ListItem key={floor.number} dense>
                      <ListItemIcon>
                        <Checkbox
                          checked={selectedFloors.includes(floor.number)}
                          onChange={() => handleFloorToggle(floor.number)}
                          disabled={!floor.accessible}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={floor.name}
                        secondary={!floor.accessible ? 'Not accessible' : null}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {/* Access Level */}
            <TextField
              fullWidth
              label="Access Level (Optional)"
              value={accessLevel}
              onChange={(e) => setAccessLevel(e.target.value)}
              placeholder="e.g., Executive, Standard, Visitor"
              sx={{ mb: 2 }}
            />

            {/* Duration */}
            <TextField
              fullWidth
              label="Duration (Minutes, Optional)"
              type="number"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              placeholder="Leave empty for permanent"
              sx={{ mb: 2 }}
            />

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {success}
              </Alert>
            )}

            <Alert 
              severity={controlAction === 'disable' ? 'warning' : 'info'} 
              sx={{ mb: 2 }}
            >
              {controlAction === 'enable' && 'This will enable elevator access for the selected floors.'}
              {controlAction === 'disable' && 'Warning: This will disable all elevator access. Use with caution.'}
              {controlAction === 'restrict' && 'This will restrict access to only the selected floors.'}
            </Alert>
          </CardContent>
        </Card>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleControlSubmit}
          variant="contained"
          color={controlAction === 'disable' ? 'error' : 'primary'}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : <SecurityIcon />}
        >
          {loading ? 'Processing...' : `${controlAction.charAt(0).toUpperCase() + controlAction.slice(1)} Access`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ElevatorControl;
