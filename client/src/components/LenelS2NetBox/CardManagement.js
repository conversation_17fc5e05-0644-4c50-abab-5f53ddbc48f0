import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import {
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';

const CardManagement = ({ 
  open, 
  onClose, 
  credential, 
  onCardStatusChanged 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [reason, setReason] = useState('');
  const [action, setAction] = useState('mark-lost'); // 'mark-lost' or 'restore'

  const isLost = credential?.status === 'Lost' || credential?.disabled;

  // Handle form submission
  const handleSubmit = async () => {
    if (!credential?.id) {
      setError('No credential selected');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      let result;
      
      if (action === 'mark-lost') {
        if (!reason.trim()) {
          setError('Please provide a reason for marking the card as lost');
          setLoading(false);
          return;
        }
        result = await lenelS2NetBoxService.markCardAsLost(credential.id, reason);
      } else {
        result = await lenelS2NetBoxService.restoreCard(credential.id);
      }

      setSuccess(result.message);
      
      // Notify parent component of the change
      if (onCardStatusChanged) {
        onCardStatusChanged(credential.id, result.status);
      }

      // Close dialog after a short delay
      setTimeout(() => {
        onClose();
      }, 2000);

    } catch (err) {
      console.error('Error managing card:', err);
      setError(err.message || 'Failed to update card status');
    } finally {
      setLoading(false);
    }
  };

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setError(null);
      setSuccess(null);
      setReason('');
      setAction(isLost ? 'restore' : 'mark-lost');
    }
  }, [open, isLost]);

  const getActionColor = () => {
    return action === 'mark-lost' ? 'error' : 'success';
  };

  const getActionIcon = () => {
    return action === 'mark-lost' ? <WarningIcon /> : <CheckCircleIcon />;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getActionIcon()}
          Card Management
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {credential && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              Card Information
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Typography variant="body2">
                <strong>Card ID:</strong> {credential.id}
              </Typography>
              {credential.encodedNum && (
                <Typography variant="body2">
                  <strong>Card Number:</strong> {credential.encodedNum}
                </Typography>
              )}
              {credential.cardFormat && (
                <Typography variant="body2">
                  <strong>Format:</strong> {credential.cardFormat}
                </Typography>
              )}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <strong>Current Status:</strong>
                <Chip
                  label={isLost ? 'Lost/Disabled' : 'Active'}
                  color={isLost ? 'error' : 'success'}
                  size="small"
                />
              </Box>
            </Box>
          </Box>
        )}

        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel>Action</InputLabel>
          <Select
            value={action}
            onChange={(e) => setAction(e.target.value)}
            label="Action"
          >
            <MenuItem value="mark-lost" disabled={isLost}>
              Mark as Lost
            </MenuItem>
            <MenuItem value="restore" disabled={!isLost}>
              Restore Card
            </MenuItem>
          </Select>
        </FormControl>

        {action === 'mark-lost' && (
          <TextField
            fullWidth
            label="Reason for marking as lost"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            multiline
            rows={3}
            placeholder="e.g., Card reported stolen, Employee terminated, Card damaged"
            required
            sx={{ mb: 2 }}
          />
        )}

        {action === 'restore' && (
          <Alert severity="info" sx={{ mb: 2 }}>
            This will restore the card to active status and re-enable access.
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Alert 
          severity={action === 'mark-lost' ? 'warning' : 'info'} 
          sx={{ mb: 2 }}
        >
          {action === 'mark-lost' 
            ? 'Warning: This will immediately disable the card and prevent access. This action can be reversed by restoring the card.'
            : 'This will restore the card to active status and re-enable all associated access permissions.'
          }
        </Alert>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color={getActionColor()}
          variant="contained"
          disabled={loading || (action === 'mark-lost' && !reason.trim())}
          startIcon={loading ? <CircularProgress size={20} /> : getActionIcon()}
        >
          {loading 
            ? 'Processing...' 
            : action === 'mark-lost' 
              ? 'Mark as Lost' 
              : 'Restore Card'
          }
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CardManagement;
