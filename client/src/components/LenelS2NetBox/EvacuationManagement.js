import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  CardActions,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  ReportProblem as EmergencyIcon,
  CheckCircle as CheckCircleIcon,
  People as PeopleIcon,
  LocationOn as LocationIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';

const EvacuationManagement = () => {
  const [evacuationStatus, setEvacuationStatus] = useState(null);
  const [occupancyReport, setOccupancyReport] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [initiateDialogOpen, setInitiateDialogOpen] = useState(false);
  const [evacuationForm, setEvacuationForm] = useState({
    evacuationName: '',
    reason: '',
    evacuationLevel: 'Building',
    affectedAreas: []
  });

  // Fetch evacuation status and occupancy
  const fetchEvacuationData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statusData, occupancyData] = await Promise.all([
        lenelS2NetBoxService.getEvacuationStatus(),
        lenelS2NetBoxService.getOccupancyReport()
      ]);

      setEvacuationStatus(statusData);
      setOccupancyReport(occupancyData);
    } catch (err) {
      console.error('Error fetching evacuation data:', err);
      setError('Failed to fetch evacuation data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchEvacuationData();
    
    // Set up periodic refresh for occupancy data
    const interval = setInterval(fetchEvacuationData, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Handle form input changes
  const handleFormChange = (field, value) => {
    setEvacuationForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Initiate evacuation
  const handleInitiateEvacuation = async () => {
    try {
      setLoading(true);
      setError(null);

      await lenelS2NetBoxService.initiateEvacuation(evacuationForm);
      
      // Refresh data
      await fetchEvacuationData();
      
      // Close dialog and reset form
      setInitiateDialogOpen(false);
      setEvacuationForm({
        evacuationName: '',
        reason: '',
        evacuationLevel: 'Building',
        affectedAreas: []
      });
    } catch (err) {
      console.error('Error initiating evacuation:', err);
      setError('Failed to initiate evacuation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // End evacuation
  const handleEndEvacuation = async (evacuationId) => {
    try {
      setLoading(true);
      setError(null);

      await lenelS2NetBoxService.endEvacuation(evacuationId);
      
      // Refresh data
      await fetchEvacuationData();
    } catch (err) {
      console.error('Error ending evacuation:', err);
      setError('Failed to end evacuation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const hasActiveEvacuations = evacuationStatus?.activeEvacuations?.length > 0;

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Evacuation Management
        </Typography>
        
        <Button
          variant="contained"
          color="error"
          startIcon={<EmergencyIcon />}
          onClick={() => setInitiateDialogOpen(true)}
          disabled={loading}
        >
          Initiate Evacuation
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Evacuation Status */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Evacuation Status
            </Typography>
            
            {hasActiveEvacuations ? (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  <strong>ACTIVE EVACUATION IN PROGRESS</strong>
                </Alert>
                
                {evacuationStatus.activeEvacuations.map((evacuation) => (
                  <Card key={evacuation.id} sx={{ mb: 2 }}>
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        {evacuation.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {evacuation.reason}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                        <TimeIcon fontSize="small" />
                        <Typography variant="caption">
                          Started: {new Date(evacuation.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        color="success"
                        startIcon={<CheckCircleIcon />}
                        onClick={() => handleEndEvacuation(evacuation.id)}
                        disabled={loading}
                      >
                        End Evacuation
                      </Button>
                    </CardActions>
                  </Card>
                ))}
              </Box>
            ) : (
              <Alert severity="success">
                No active evacuations. Building status: Normal
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Occupancy Report */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Current Occupancy
            </Typography>
            
            {occupancyReport ? (
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <PeopleIcon />
                  <Typography variant="h4">
                    {occupancyReport.totalOccupants}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    people in building
                  </Typography>
                </Box>

                {occupancyReport.areaOccupancy && occupancyReport.areaOccupancy.length > 0 && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Area Breakdown:
                    </Typography>
                    <List dense>
                      {occupancyReport.areaOccupancy.map((area, index) => (
                        <ListItem key={index} sx={{ px: 0 }}>
                          <ListItemText
                            primary={area.area}
                            secondary={`${area.occupantCount} occupants`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                <Typography variant="caption" color="text.secondary">
                  Last updated: {new Date(occupancyReport.lastUpdated).toLocaleString()}
                </Typography>
              </Box>
            ) : (
              <CircularProgress />
            )}
          </Paper>
        </Grid>

        {/* Current Occupants List */}
        {occupancyReport?.occupants && occupancyReport.occupants.length > 0 && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Current Occupants
              </Typography>
              
              <List>
                {occupancyReport.occupants.map((occupant, index) => (
                  <React.Fragment key={occupant.personId || index}>
                    <ListItem>
                      <ListItemText
                        primary={occupant.personId}
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LocationIcon fontSize="small" />
                            {occupant.location}
                            <Typography variant="caption" sx={{ ml: 1 }}>
                              Since: {new Date(occupant.timestamp).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                      <Chip
                        label={occupant.status}
                        color="success"
                        size="small"
                      />
                    </ListItem>
                    {index < occupancyReport.occupants.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Initiate Evacuation Dialog */}
      <Dialog open={initiateDialogOpen} onClose={() => setInitiateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EmergencyIcon color="error" />
            Initiate Evacuation
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 3 }}>
            This will initiate an emergency evacuation procedure. Only use in case of actual emergency.
          </Alert>

          <TextField
            fullWidth
            label="Evacuation Name"
            value={evacuationForm.evacuationName}
            onChange={(e) => handleFormChange('evacuationName', e.target.value)}
            sx={{ mb: 2 }}
            placeholder="e.g., Fire Evacuation, Security Alert"
          />

          <TextField
            fullWidth
            label="Reason"
            value={evacuationForm.reason}
            onChange={(e) => handleFormChange('reason', e.target.value)}
            multiline
            rows={3}
            sx={{ mb: 2 }}
            placeholder="Describe the reason for evacuation"
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Evacuation Level</InputLabel>
            <Select
              value={evacuationForm.evacuationLevel}
              onChange={(e) => handleFormChange('evacuationLevel', e.target.value)}
              label="Evacuation Level"
            >
              <MenuItem value="Floor">Single Floor</MenuItem>
              <MenuItem value="Zone">Zone/Area</MenuItem>
              <MenuItem value="Building">Entire Building</MenuItem>
              <MenuItem value="Campus">Campus Wide</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setInitiateDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleInitiateEvacuation}
            color="error"
            variant="contained"
            disabled={loading || !evacuationForm.evacuationName || !evacuationForm.reason}
            startIcon={loading ? <CircularProgress size={20} /> : <EmergencyIcon />}
          >
            {loading ? 'Initiating...' : 'Initiate Evacuation'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EvacuationManagement;
