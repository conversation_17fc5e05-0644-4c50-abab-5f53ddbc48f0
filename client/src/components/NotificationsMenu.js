import React, { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  IconButton,
  Badge,
  Menu,
  MenuItem,
  ListItemText,
  ListItemIcon,
  Typography,
  Box,
  Divider,
  CircularProgress,
  Chip
} from '@mui/material';
import { Notifications as NotificationsIcon } from '@mui/icons-material';
import notificationsService from '../services/notificationsService';

const isNew = (createdAt) => {
  try {
    const created = new Date(createdAt).getTime();
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    return now - created < oneDay;
  } catch (e) {
    return false;
  }
};

const NotificationsMenu = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const open = Boolean(anchorEl);

  const fetchUnreadCount = async () => {
    try {
      const count = await notificationsService.getUnreadCount();
      setUnreadCount(count);
    } catch (err) {
      // silent fail for badge; optionally log
      console.error('Failed to fetch unread count', err);
    }
  };

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      const list = await notificationsService.getNotifications({ limit: 10 });
      setNotifications(list);
      setLoading(false);
    } catch (err) {
      console.error('Failed to fetch notifications', err);
      setError('Failed to load notifications');
      setLoading(false);
    }
  };

  useEffect(() => {
    // initial load for badge
    fetchUnreadCount();
    // poll every 60s to keep badge fresh
    const id = setInterval(fetchUnreadCount, 60000);
    return () => clearInterval(id);
  }, []);

  const handleOpen = async (event) => {
    setAnchorEl(event.currentTarget);
    await fetchNotifications();
  };

  const handleClose = () => setAnchorEl(null);

  const handleItemClick = async (n) => {
    try {
      if (!n.read) {
        setNotifications((prev) => prev.map((x) => (x._id === n._id ? { ...x, read: true } : x)));
        setUnreadCount((c) => Math.max(0, c - 1));
        // fire and forget
        notificationsService.markAsRead(n._id).catch(() => {});
      }
      if (n.linkUrl) {
        // internal routes assumed if start with '/'
        if (n.linkUrl.startsWith('/')) navigate(n.linkUrl);
        else window.open(n.linkUrl, '_blank', 'noopener');
      }
      handleClose();
    } catch (err) {
      console.error('Error handling notification click', err);
    }
  };

  const handleMarkAllRead = async () => {
    try {
      setNotifications((prev) => prev.map((x) => ({ ...x, read: true })));
      setUnreadCount(0);
      await notificationsService.markAllAsRead();
    } catch (err) {
      console.error('Failed to mark all as read', err);
    }
  };

  const hasNotifications = notifications && notifications.length > 0;

  return (
    <>
      <IconButton
        color="inherit"
        size="small"
        sx={{ ml: 2 }}
        aria-label="notifications"
        aria-controls="notifications-menu"
        aria-haspopup="true"
        onClick={handleOpen}
      >
        <Badge badgeContent={unreadCount} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>

      <Menu
        id="notifications-menu"
        anchorEl={anchorEl}
        keepMounted
        open={open}
        onClose={handleClose}
        PaperProps={{ sx: { width: 360, maxWidth: '90vw' } }}
      >
        <Box sx={{ px: 2, py: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="subtitle1">Notifications</Typography>
          <Typography variant="body2" color="primary" sx={{ cursor: 'pointer' }} onClick={handleMarkAllRead}>
            Mark all as read
          </Typography>
        </Box>
        <Divider />
        {loading ? (
          <Box sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <CircularProgress size={20} />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Typography color="error" variant="body2">{error}</Typography>
          </Box>
        ) : !hasNotifications ? (
          <Box sx={{ p: 2 }}>
            <Typography variant="body2" color="text.secondary">You're all caught up.</Typography>
          </Box>
        ) : (
          notifications.map((n) => (
            <MenuItem key={n._id} onClick={() => handleItemClick(n)} dense>
              <ListItemIcon>
                <NotificationsIcon fontSize="small" color={n.read ? 'disabled' : 'primary'} />
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" fontWeight={n.read ? 400 : 600}>{n.title}</Typography>
                    {isNew(n.createdAt) && <Chip label="New" size="small" color="success" variant="outlined" />}
                  </Box>
                }
                secondary={
                  <Typography variant="caption" color="text.secondary" noWrap>
                    {n.message}
                  </Typography>
                }
              />
            </MenuItem>
          ))
        )}
      </Menu>
    </>
  );
};

export default NotificationsMenu;
