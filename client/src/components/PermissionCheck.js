import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Box, Typography, Button } from '@mui/material';

/**
 * Higher-order component that checks if the user has the required permission
 * If not, it either redirects to the dashboard or shows an access denied message
 * 
 * @param {React.Component} Component - The component to render if permission check passes
 * @param {string} requiredPermission - The permission required to access this component
 * @param {boolean} redirectToDashboard - Whether to redirect to dashboard on permission failure (default: false)
 * @returns {React.Component} - The wrapped component with permission check
 */
export const withPermissionCheck = (Component, requiredPermission, redirectToDashboard = false) => {
  return (props) => {
    const { hasPermission, user } = useAuth();
    
    // If user is not authenticated, they should be redirected at the route level
    if (!user) {
      return <Navigate to="/login" />;
    }
    
    // If no permission is required or user has the required permission, render the component
    if (!requiredPermission || hasPermission(requiredPermission)) {
      return <Component {...props} />;
    }
    
    // If redirectToDashboard is true, redirect to dashboard
    if (redirectToDashboard) {
      return <Navigate to="/dashboard" />;
    }
    
    // Otherwise, show access denied message
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h4" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1" paragraph>
          You don't have permission to access this page.
        </Typography>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={() => window.history.back()}
        >
          Go Back
        </Button>
        <Button 
          variant="outlined" 
          color="primary" 
          onClick={() => window.location.href = '/dashboard'}
          sx={{ ml: 2 }}
        >
          Go to Dashboard
        </Button>
      </Box>
    );
  };
};

/**
 * Component that checks if the user has the required permission
 * If not, it either redirects to the dashboard or shows an access denied message
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The children to render if permission check passes
 * @param {string} props.requiredPermission - The permission required to access this component
 * @param {boolean} props.redirectToDashboard - Whether to redirect to dashboard on permission failure (default: false)
 * @returns {React.ReactNode} - The children or access denied message
 */
export const PermissionCheck = ({ children, requiredPermission, redirectToDashboard = false }) => {
  const { hasPermission, user } = useAuth();
  
  // If user is not authenticated, they should be redirected at the route level
  if (!user) {
    return <Navigate to="/login" />;
  }
  
  // If no permission is required or user has the required permission, render the children
  if (!requiredPermission || hasPermission(requiredPermission)) {
    return children;
  }
  
  // If redirectToDashboard is true, redirect to dashboard
  if (redirectToDashboard) {
    return <Navigate to="/dashboard" />;
  }
  
  // Otherwise, show access denied message
  return (
    <Box sx={{ textAlign: 'center', mt: 4 }}>
      <Typography variant="h4" gutterBottom>
        Access Denied
      </Typography>
      <Typography variant="body1" paragraph>
        You don't have permission to access this page.
      </Typography>
      <Button 
        variant="contained" 
        color="primary" 
        onClick={() => window.history.back()}
      >
        Go Back
      </Button>
      <Button 
        variant="outlined" 
        color="primary" 
        onClick={() => window.location.href = '/dashboard'}
        sx={{ ml: 2 }}
      >
        Go to Dashboard
      </Button>
    </Box>
  );
};

export default PermissionCheck;