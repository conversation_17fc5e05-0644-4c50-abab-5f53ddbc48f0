import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Divider
} from '@mui/material';
import {
  LocalFireDepartment as FireIcon,
  LocalHospital as MedicalIcon,
  Healing as FirstAidIcon,
  Warning as ChemicalIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  DirectionsRun as DirectionsIcon
} from '@mui/icons-material';
import safetyService from '../../services/safetyService';

/**
 * EmergencyLocator Component
 * Crisis-optimized interface for finding nearest emergency equipment
 * Designed for use during actual emergencies with large buttons and clear information
 */
const EmergencyLocator = ({ 
  buildingId = null, 
  floorId = null, 
  room = null,
  kioskMode = false 
}) => {
  const [selectedEmergency, setSelectedEmergency] = useState(null);
  const [nearestEquipment, setNearestEquipment] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const emergencyTypes = [
    {
      type: 'fire',
      label: 'FIRE',
      description: 'Fire or smoke emergency',
      icon: <FireIcon sx={{ fontSize: 48 }} />,
      color: '#d32f2f',
      backgroundColor: '#ffebee',
      instructions: 'Locate nearest fire extinguisher. Call 911 immediately.'
    },
    {
      type: 'medical',
      label: 'MEDICAL',
      description: 'Heart attack, cardiac arrest',
      icon: <MedicalIcon sx={{ fontSize: 48 }} />,
      color: '#f57c00',
      backgroundColor: '#fff3e0',
      instructions: 'Locate nearest AED (defibrillator). Call 911 immediately.'
    },
    {
      type: 'injury',
      label: 'INJURY',
      description: 'Cuts, injuries, bleeding',
      icon: <FirstAidIcon sx={{ fontSize: 48 }} />,
      color: '#1976d2',
      backgroundColor: '#e3f2fd',
      instructions: 'Locate nearest first aid kit. Call 911 if severe.'
    },
    {
      type: 'chemical',
      label: 'CHEMICAL',
      description: 'Chemical splash, exposure',
      icon: <ChemicalIcon sx={{ fontSize: 48 }} />,
      color: '#388e3c',
      backgroundColor: '#e8f5e8',
      instructions: 'Locate nearest eyewash station. Flush immediately.'
    }
  ];

  useEffect(() => {
    if (selectedEmergency) {
      findNearestEquipment(selectedEmergency.type);
    }
  }, [selectedEmergency, buildingId, floorId, room]);

  const findNearestEquipment = async (emergencyType) => {
    try {
      setLoading(true);
      setError(null);

      let equipment = [];
      
      switch (emergencyType) {
        case 'fire':
          equipment = await safetyService.findNearestFireExtinguisher(buildingId, floorId, room);
          break;
        case 'medical':
          equipment = await safetyService.findNearestAED(buildingId, floorId, room);
          break;
        case 'injury':
          equipment = await safetyService.findNearestFirstAid(buildingId, floorId, room);
          break;
        case 'chemical':
          equipment = await safetyService.findNearestAssets({
            assetType: 'eyewash_station',
            buildingId,
            floorId,
            room,
            limit: 2
          });
          break;
        default:
          equipment = [];
      }

      setNearestEquipment(equipment);
    } catch (err) {
      console.error('Error finding nearest equipment:', err);
      setError('Unable to locate equipment. Contact emergency services immediately.');
    } finally {
      setLoading(false);
    }
  };

  const handleEmergencySelect = (emergency) => {
    setSelectedEmergency(emergency);
  };

  const handleBack = () => {
    setSelectedEmergency(null);
    setNearestEquipment([]);
    setError(null);
  };

  const callEmergency = () => {
    if (window.confirm('This will attempt to call emergency services (911). Continue?')) {
      window.location.href = 'tel:911';
    }
  };

  const getLocationDescription = (asset) => {
    const parts = [];
    if (asset.room) parts.push(asset.room);
    if (asset.floorId?.name) parts.push(asset.floorId.name);
    if (asset.buildingId?.name) parts.push(asset.buildingId.name);
    return parts.join(' • ') || 'Location not specified';
  };

  // Emergency selection screen
  if (!selectedEmergency) {
    return (
      <Box sx={{ 
        minHeight: '100vh', 
        backgroundColor: '#f5f5f5',
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}>
        <Paper sx={{ 
          width: '100%', 
          maxWidth: 800, 
          p: 4, 
          textAlign: 'center',
          mb: 3
        }}>
          <Typography 
            variant="h2" 
            component="h1" 
            gutterBottom 
            sx={{ 
              fontWeight: 'bold', 
              color: '#d32f2f',
              mb: 2
            }}
          >
            🚨 EMERGENCY
          </Typography>
          
          <Typography variant="h5" gutterBottom sx={{ mb: 4 }}>
            Select the type of emergency to locate nearest equipment
          </Typography>

          <Alert 
            severity="error" 
            sx={{ 
              fontSize: '1.2rem', 
              mb: 4,
              '& .MuiAlert-message': { width: '100%', textAlign: 'center' }
            }}
          >
            <strong>FOR IMMEDIATE LIFE-THREATENING EMERGENCIES: CALL 911 FIRST</strong>
          </Alert>

          <Button
            variant="contained"
            color="error"
            size="large"
            startIcon={<PhoneIcon />}
            onClick={callEmergency}
            sx={{ 
              fontSize: '1.5rem',
              p: 2,
              mb: 4,
              minWidth: 200
            }}
          >
            CALL 911
          </Button>
        </Paper>

        <Grid container spacing={3} sx={{ maxWidth: 1000 }}>
          {emergencyTypes.map((emergency) => (
            <Grid item xs={12} sm={6} key={emergency.type}>
              <Card 
                sx={{ 
                  minHeight: 200,
                  cursor: 'pointer',
                  backgroundColor: emergency.backgroundColor,
                  border: `3px solid ${emergency.color}`,
                  '&:hover': {
                    boxShadow: 6,
                    transform: 'scale(1.02)'
                  },
                  transition: 'all 0.2s'
                }}
                onClick={() => handleEmergencySelect(emergency)}
              >
                <CardContent sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center',
                  textAlign: 'center',
                  p: 3
                }}>
                  <Box sx={{ color: emergency.color, mb: 2 }}>
                    {emergency.icon}
                  </Box>
                  
                  <Typography 
                    variant="h4" 
                    component="h3" 
                    gutterBottom
                    sx={{ 
                      fontWeight: 'bold',
                      color: emergency.color,
                      mb: 1
                    }}
                  >
                    {emergency.label}
                  </Typography>
                  
                  <Typography 
                    variant="h6" 
                    color="text.secondary"
                    sx={{ mb: 2 }}
                  >
                    {emergency.description}
                  </Typography>

                  <Typography 
                    variant="body2"
                    sx={{ 
                      fontSize: '1rem',
                      fontStyle: 'italic',
                      color: emergency.color
                    }}
                  >
                    Tap to locate equipment
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  // Equipment location screen
  return (
    <Box sx={{ 
      minHeight: '100vh', 
      backgroundColor: selectedEmergency.backgroundColor,
      p: 2,
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <Paper sx={{ p: 2, mb: 3, backgroundColor: 'white' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Box sx={{ color: selectedEmergency.color }}>
            {selectedEmergency.icon}
          </Box>
          <Typography 
            variant="h3" 
            sx={{ 
              fontWeight: 'bold',
              color: selectedEmergency.color
            }}
          >
            {selectedEmergency.label} EMERGENCY
          </Typography>
        </Box>

        <Alert 
          severity="error" 
          sx={{ 
            fontSize: '1.1rem',
            mb: 2
          }}
        >
          {selectedEmergency.instructions}
        </Alert>

        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            color="error"
            size="large"
            startIcon={<PhoneIcon />}
            onClick={callEmergency}
            sx={{ fontSize: '1.2rem', p: 1.5 }}
          >
            CALL 911
          </Button>

          <Button
            variant="outlined"
            size="large"
            onClick={handleBack}
            sx={{ fontSize: '1.2rem', p: 1.5 }}
          >
            ← Back to Emergency Types
          </Button>
        </Box>
      </Paper>

      {/* Equipment List */}
      <Paper sx={{ flex: 1, p: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
          Nearest Equipment:
        </Typography>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress size={60} />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3, fontSize: '1.1rem' }}>
            {error}
          </Alert>
        )}

        {!loading && nearestEquipment.length === 0 && !error && (
          <Alert severity="warning" sx={{ fontSize: '1.1rem' }}>
            No equipment found in this area. Contact building management or emergency services.
          </Alert>
        )}

        {!loading && nearestEquipment.length > 0 && (
          <List>
            {nearestEquipment.map((asset, index) => (
              <React.Fragment key={asset._id}>
                <ListItem 
                  sx={{ 
                    backgroundColor: index === 0 ? 'action.selected' : 'transparent',
                    borderRadius: 2,
                    mb: 2,
                    border: index === 0 ? '2px solid' : '1px solid',
                    borderColor: index === 0 ? selectedEmergency.color : 'divider'
                  }}
                >
                  <ListItemIcon>
                    <LocationIcon 
                      sx={{ 
                        fontSize: 40, 
                        color: index === 0 ? selectedEmergency.color : 'action.active'
                      }} 
                    />
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Typography variant="h5" component="div">
                        {index === 0 && (
                          <Chip 
                            label="NEAREST" 
                            color="primary" 
                            sx={{ 
                              mr: 1, 
                              fontWeight: 'bold',
                              backgroundColor: selectedEmergency.color,
                              color: 'white'
                            }} 
                          />
                        )}
                        {asset.name}
                      </Typography>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="h6" component="div" sx={{ mb: 1 }}>
                          📍 {getLocationDescription(asset)}
                        </Typography>
                        
                        <Typography variant="body1" color="text.secondary">
                          Asset ID: {asset.assetId}
                        </Typography>
                        
                        {asset.specifications?.capacity && (
                          <Typography variant="body1" color="text.secondary">
                            Capacity: {asset.specifications.capacity}
                          </Typography>
                        )}
                        
                        <Chip
                          label={safetyService.getInspectionStatuses().find(s => s.value === asset.inspection?.status)?.label || 'Unknown'}
                          color={safetyService.getInspectionStatusColor(asset.inspection?.status)}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </Box>
                    }
                  />
                  
                  <CardActions>
                    <Button
                      variant="outlined"
                      startIcon={<DirectionsIcon />}
                      disabled // Future: GPS directions
                      sx={{ minWidth: 120 }}
                    >
                      Directions
                    </Button>
                  </CardActions>
                </ListItem>
                
                {index < nearestEquipment.length - 1 && <Divider sx={{ my: 2 }} />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Paper>
    </Box>
  );
};

export default EmergencyLocator;