import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  Paper,
  InputAdornment
} from '@mui/material';
import { Checkbox, FormControlLabel, List, ListItem, ListItemText, CircularProgress, Alert } from '@mui/material';
import buildingManagementService from '../../services/buildingManagementService';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import AttachFileIcon from '@mui/icons-material/AttachFile';

/**
 * Component for creating or editing a tracking item
 */
const TrackingItemForm = ({
  open,
  onClose,
  onSave,
  item = null,
  buildings = [],
  isLoading = false
}) => {
  // Initialize form state
  const [formData, setFormData] = useState({
    buildingId: '',
    name: '',
    type: 'contract',
    description: '',
    provider: {
      name: '',
      contactPerson: '',
      phone: '',
      email: '',
      website: ''
    },
    providerContactId: null,
    createProviderContact: true,
    startDate: new Date(),
    expirationDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    renewalDate: null,
    notificationDays: 30,
    status: 'active',
    cost: {
      amount: '',
      currency: 'USD',
      billingCycle: 'annually'
    },
    documents: [],
    notes: ''
  });
  
  // Form validation state
  const [errors, setErrors] = useState({});
  
  // Phonebook linking state
  const [linkPanelOpen, setLinkPanelOpen] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [contactsLoading, setContactsLoading] = useState(false);
  const [contactSearch, setContactSearch] = useState('');
  const [linkedContact, setLinkedContact] = useState(null);
  const [autoSuggestion, setAutoSuggestion] = useState(null);
  const [autoSuggestionDismissed, setAutoSuggestionDismissed] = useState(false);
  
  // Initialize form with item data if editing
  useEffect(() => {
    if (item) {
      // Convert string dates to Date objects
      const itemData = { ...item };
      if (itemData.startDate) {
        itemData.startDate = new Date(itemData.startDate);
      }
      if (itemData.expirationDate) {
        itemData.expirationDate = new Date(itemData.expirationDate);
      }
      if (itemData.renewalDate) {
        itemData.renewalDate = new Date(itemData.renewalDate);
      }
      
      // Handle nested objects
      if (!itemData.provider) {
        itemData.provider = {
          name: '',
          contactPerson: '',
          phone: '',
          email: '',
          website: ''
        };
      }
      
      if (!itemData.cost) {
        itemData.cost = {
          amount: '',
          currency: 'USD',
          billingCycle: 'annually'
        };
      }
      
      if (!itemData.documents) {
        itemData.documents = [];
      }
      
      // Normalize providerContactId and set linkedContact state
      let providerContactObj = null;
      if (itemData.providerContactId) {
        if (typeof itemData.providerContactId === 'object' && itemData.providerContactId._id) {
          providerContactObj = itemData.providerContactId;
          itemData.providerContactId = itemData.providerContactId._id;
        }
      } else {
        itemData.providerContactId = null;
      }
      if (itemData.createProviderContact === undefined) {
        itemData.createProviderContact = true;
      }
      
      setFormData(itemData);
      setLinkedContact(providerContactObj);
    } else {
      // Reset form for new item
      setFormData({
        buildingId: '',
        name: '',
        type: 'contract',
        description: '',
        provider: {
          name: '',
          contactPerson: '',
          phone: '',
          email: '',
          website: ''
        },
        providerContactId: null,
        createProviderContact: true,
        startDate: new Date(),
        expirationDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        renewalDate: null,
        notificationDays: 30,
        status: 'active',
        cost: {
          amount: '',
          currency: 'USD',
          billingCycle: 'annually'
        },
        documents: [],
        notes: ''
      });
    }
    
    // Reset errors
    setErrors({});
  }, [item, open]);
  
  // Load linked contact details when we only have an ID
  useEffect(() => {
    const id = formData?.providerContactId;
    if (!id) return;
    if (linkedContact && linkedContact._id === id) return;
    (async () => {
      try {
        const contact = await buildingManagementService.getContact(id);
        setLinkedContact(contact);
      } catch (e) {
        console.warn('Failed to load linked contact:', e);
      }
    })();
  }, [formData?.providerContactId]);
  
  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Handle nested fields
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };
  
  // Handle date changes
  const handleDateChange = (name, date) => {
    setFormData(prev => ({
      ...prev,
      [name]: date
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };
  
  // Add a document
  const handleAddDocument = () => {
    setFormData(prev => ({
      ...prev,
      documents: [
        ...prev.documents,
        {
          name: '',
          fileUrl: '',
          uploadDate: new Date()
        }
      ]
    }));
  };
  
  // Update document field
  const handleDocumentChange = (index, field, value) => {
    const updatedDocuments = [...formData.documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      [field]: value
    };
    
    setFormData(prev => ({
      ...prev,
      documents: updatedDocuments
    }));
  };
  
  // Remove a document
  const handleRemoveDocument = (index) => {
    const updatedDocuments = [...formData.documents];
    updatedDocuments.splice(index, 1);
    
    setFormData(prev => ({
      ...prev,
      documents: updatedDocuments
    }));
  };
  
  // Phone Book linking helpers
  const loadContacts = async (term) => {
    setContactsLoading(true);
    try {
      const res = await buildingManagementService.getContacts({ search: term });
      setContacts(Array.isArray(res) ? res : []);
    } catch (e) {
      console.error('Error loading contacts:', e);
      setContacts([]);
    } finally {
      setContactsLoading(false);
    }
  };
  
  const openLinkPanel = () => {
    setLinkPanelOpen(true);
    const initial = contactSearch || formData.provider?.name || formData.provider?.contactPerson || '';
    setContactSearch(initial);
    loadContacts(initial || '');
  };
  
  const handleSelectContact = (contact) => {
    setLinkedContact(contact);
    setFormData(prev => ({
      ...prev,
      providerContactId: contact?._id || null,
      provider: {
        name: prev.provider?.name || contact?.company || contact?.name || '',
        contactPerson: prev.provider?.contactPerson || contact?.name || '',
        phone: prev.provider?.phone || contact?.phone || '',
        email: prev.provider?.email || contact?.email || '',
        website: prev.provider?.website || ''
      }
    }));
    setLinkPanelOpen(false);
    setAutoSuggestion(null);
    setAutoSuggestionDismissed(false);
  };
  
  const handleClearLink = () => {
    setLinkedContact(null);
    setFormData(prev => ({ ...prev, providerContactId: null }));
  };
  
  const handleContactSearchChange = (e) => {
    setContactSearch(e.target.value);
  };
  
  const handleSearchSubmit = async (e) => {
    if (e && e.preventDefault) e.preventDefault();
    await loadContacts(contactSearch);
  };
  
  const handleCreateProviderContactChange = (e) => {
    const checked = e.target.checked;
    setFormData(prev => ({ ...prev, createProviderContact: checked }));
  };
  
  // Auto-suggestion based on provider fields
  useEffect(() => {
    if (linkedContact || autoSuggestionDismissed) {
      setAutoSuggestion(null);
      return;
    }
    const provider = formData.provider || {};
    const term = provider.email || provider.phone || provider.name || provider.contactPerson;
    if (!term) {
      setAutoSuggestion(null);
      return;
    }
    let cancelled = false;
    const timeout = setTimeout(async () => {
      try {
        const results = await buildingManagementService.getContacts({ search: term });
        if (cancelled) return;
        const list = Array.isArray(results) ? results : [];
        let match = null;
        if (provider.email) {
          const email = provider.email.toLowerCase();
          match = list.find(c => (c.email || '').toLowerCase() === email) || null;
        }
        if (!match && provider.phone) {
          const norm = (s) => (s || '').replace(/\D/g, '');
          match = list.find(c => norm(c.phone) === norm(provider.phone)) || null;
        }
        if (!match && provider.name) {
          const eq = (a,b) => (a||'').trim().toLowerCase() === (b||'').trim().toLowerCase();
          match = list.find(c => eq(c.company, provider.name) || eq(c.name, provider.name)) || null;
        }
        if (!match && provider.contactPerson) {
          const eq = (a,b) => (a||'').trim().toLowerCase() === (b||'').trim().toLowerCase();
          match = list.find(c => eq(c.name, provider.contactPerson)) || null;
        }
        setAutoSuggestion(match);
      } catch (e) {
        console.warn('Auto-suggest lookup failed:', e);
      }
    }, 400);
    return () => { cancelled = true; clearTimeout(timeout); };
  }, [formData.provider, linkedContact, autoSuggestionDismissed]);
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.buildingId) {
      newErrors.buildingId = 'Building is required';
    }
    
    if (!formData.name) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.type) {
      newErrors.type = 'Type is required';
    }
    
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }
    
    if (!formData.expirationDate) {
      newErrors.expirationDate = 'Expiration date is required';
    }
    
    // Date validation
    if (formData.startDate && formData.expirationDate && 
        formData.startDate > formData.expirationDate) {
      newErrors.expirationDate = 'Expiration date must be after start date';
    }
    
    if (formData.renewalDate && formData.expirationDate && 
        formData.renewalDate < formData.expirationDate) {
      newErrors.renewalDate = 'Renewal date should be after expiration date';
    }
    
    // Email validation
    if (formData.provider.email && 
        !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.provider.email)) {
      newErrors['provider.email'] = 'Invalid email address';
    }
    
    // Cost validation
    if (formData.cost.amount && isNaN(Number(formData.cost.amount))) {
      newErrors['cost.amount'] = 'Cost must be a number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {item ? 'Edit Tracking Item' : 'Add New Tracking Item'}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Basic Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.buildingId}>
                <InputLabel>Building *</InputLabel>
                <Select
                  name="buildingId"
                  value={formData.buildingId}
                  onChange={handleChange}
                  label="Building *"
                >
                  {buildings.map(building => (
                    <MenuItem key={building._id} value={building._id}>
                      {building.name}
                    </MenuItem>
                  ))}
                </Select>
                {errors.buildingId && (
                  <FormHelperText>{errors.buildingId}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.type}>
                <InputLabel>Type *</InputLabel>
                <Select
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  label="Type *"
                >
                  <MenuItem value="contract">Contract</MenuItem>
                  <MenuItem value="service">Service</MenuItem>
                  <MenuItem value="inspection">Inspection</MenuItem>
                  <MenuItem value="license">License</MenuItem>
                  <MenuItem value="certification">Certification</MenuItem>
                  <MenuItem value="warranty">Warranty</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
                {errors.type && (
                  <FormHelperText>{errors.type}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="name"
                label="Name *"
                value={formData.name}
                onChange={handleChange}
                fullWidth
                error={!!errors.name}
                helperText={errors.name}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleChange}
                fullWidth
                multiline
                rows={3}
              />
            </Grid>
            
            {/* Provider Information */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Provider Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="provider.name"
                label="Provider Name"
                value={formData.provider.name}
                onChange={handleChange}
                fullWidth
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="provider.contactPerson"
                label="Contact Person"
                value={formData.provider.contactPerson}
                onChange={handleChange}
                fullWidth
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="provider.phone"
                label="Phone"
                value={formData.provider.phone}
                onChange={handleChange}
                fullWidth
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="provider.email"
                label="Email"
                value={formData.provider.email}
                onChange={handleChange}
                fullWidth
                error={!!errors['provider.email']}
                helperText={errors['provider.email']}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                name="provider.website"
                label="Website"
                value={formData.provider.website}
                onChange={handleChange}
                fullWidth
              />
            </Grid>

            {/* Phone Book Linking */}
            <Grid item xs={12}>
              { (formData.providerContactId || linkedContact) ? (
                <Alert 
                  severity="success"
                  action={
                    <Box display="flex" gap={1}>
                      <Button size="small" onClick={openLinkPanel}>Change</Button>
                      <Button size="small" onClick={handleClearLink}>Clear link</Button>
                    </Box>
                  }
                  sx={{ mb: 2 }}
                >
                  Linked to Phone Book: {linkedContact?.company || linkedContact?.name || 'Contact selected'}
                </Alert>
              ) : (
                <>
                  {autoSuggestion && !autoSuggestionDismissed && (
                    <Alert 
                      severity="info"
                      action={
                        <Box display="flex" gap={1}>
                          <Button size="small" onClick={() => handleSelectContact(autoSuggestion)}>Use</Button>
                          <Button size="small" onClick={() => setAutoSuggestionDismissed(true)}>Dismiss</Button>
                        </Box>
                      }
                      sx={{ mb: 2 }}
                    >
                      We found a matching Phone Book contact: {autoSuggestion.company || autoSuggestion.name}
                    </Alert>
                  )}
                  <Button variant="outlined" startIcon={<AddIcon />} onClick={openLinkPanel} sx={{ mb: 1 }}>
                    Link Provider from Phone Book
                  </Button>
                </>
              )}
            </Grid>

            {linkPanelOpen && (
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Box component="form" onSubmit={handleSearchSubmit} display="flex" gap={2} mb={2}>
                    <TextField 
                      label="Search contacts" 
                      value={contactSearch} 
                      onChange={handleContactSearchChange} 
                      fullWidth
                      InputProps={{
                        endAdornment: contactsLoading ? (
                          <InputAdornment position="end">
                            <CircularProgress size={20} />
                          </InputAdornment>
                        ) : null
                      }}
                    />
                    <Button type="submit" variant="contained">Search</Button>
                    <Button onClick={() => setLinkPanelOpen(false)}>Close</Button>
                  </Box>
                  {contacts.length === 0 && !contactsLoading && (
                    <Typography variant="body2" color="text.secondary">No contacts found.</Typography>
                  )}
                  <List>
                    {contacts.map(c => (
                      <ListItem key={c._id} divider secondaryAction={
                        <Button onClick={() => handleSelectContact(c)} size="small" variant="outlined">Use</Button>
                      }>
                        <ListItemText 
                          primary={`${c.name}${c.company ? ' — ' + c.company : ''}`}
                          secondary={`${c.phone || ''}${c.email ? ' • ' + c.email : ''}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              </Grid>
            )}

            {/* Create Phone Book Entry toggle */}
            <Grid item xs={12}>
              <FormControlLabel 
                control={<Checkbox checked={!!formData.createProviderContact} onChange={handleCreateProviderContactChange} />}
                label="Create a Phone Book entry for this provider if not using an existing contact"
              />
            </Grid>
            
            {/* Dates and Status */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Dates and Status
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <DatePicker
                label="Start Date *"
                value={formData.startDate}
                onChange={(date) => handleDateChange('startDate', date)}
                slotProps={{ 
                  textField: { 
                    fullWidth: true,
                    error: !!errors.startDate,
                    helperText: errors.startDate
                  } 
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <DatePicker
                label="Expiration Date *"
                value={formData.expirationDate}
                onChange={(date) => handleDateChange('expirationDate', date)}
                slotProps={{ 
                  textField: { 
                    fullWidth: true,
                    error: !!errors.expirationDate,
                    helperText: errors.expirationDate
                  } 
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <DatePicker
                label="Renewal Date"
                value={formData.renewalDate}
                onChange={(date) => handleDateChange('renewalDate', date)}
                slotProps={{ 
                  textField: { 
                    fullWidth: true,
                    error: !!errors.renewalDate,
                    helperText: errors.renewalDate
                  } 
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="notificationDays"
                label="Notification Days Before Expiration"
                value={formData.notificationDays}
                onChange={handleChange}
                type="number"
                fullWidth
                InputProps={{
                  endAdornment: <InputAdornment position="end">days</InputAdornment>,
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Status"
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="expired">Expired</MenuItem>
                  <MenuItem value="pending_renewal">Pending Renewal</MenuItem>
                  <MenuItem value="renewed">Renewed</MenuItem>
                  <MenuItem value="terminated">Terminated</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {/* Cost Information */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Cost Information
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <TextField
                name="cost.amount"
                label="Cost Amount"
                value={formData.cost.amount}
                onChange={handleChange}
                fullWidth
                error={!!errors['cost.amount']}
                helperText={errors['cost.amount']}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Currency</InputLabel>
                <Select
                  name="cost.currency"
                  value={formData.cost.currency}
                  onChange={handleChange}
                  label="Currency"
                >
                  <MenuItem value="USD">USD</MenuItem>
                  <MenuItem value="EUR">EUR</MenuItem>
                  <MenuItem value="GBP">GBP</MenuItem>
                  <MenuItem value="CAD">CAD</MenuItem>
                  <MenuItem value="AUD">AUD</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Billing Cycle</InputLabel>
                <Select
                  name="cost.billingCycle"
                  value={formData.cost.billingCycle}
                  onChange={handleChange}
                  label="Billing Cycle"
                >
                  <MenuItem value="one-time">One-time</MenuItem>
                  <MenuItem value="monthly">Monthly</MenuItem>
                  <MenuItem value="quarterly">Quarterly</MenuItem>
                  <MenuItem value="annually">Annually</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {/* Documents */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Typography variant="subtitle1" fontWeight="bold">
                  Documents
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddDocument}
                  size="small"
                >
                  Add Document
                </Button>
              </Box>
            </Grid>
            
            {formData.documents.length === 0 ? (
              <Grid item xs={12}>
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 2, 
                    textAlign: 'center',
                    color: 'text.secondary'
                  }}
                >
                  <AttachFileIcon sx={{ fontSize: 40, opacity: 0.5, mb: 1 }} />
                  <Typography>
                    No documents attached
                  </Typography>
                  <Button
                    startIcon={<AddIcon />}
                    onClick={handleAddDocument}
                    sx={{ mt: 1 }}
                  >
                    Add Document
                  </Button>
                </Paper>
              </Grid>
            ) : (
              formData.documents.map((doc, index) => (
                <Grid item xs={12} key={index}>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={5}>
                        <TextField
                          label="Document Name"
                          value={doc.name}
                          onChange={(e) => handleDocumentChange(index, 'name', e.target.value)}
                          fullWidth
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={5}>
                        <TextField
                          label="File URL"
                          value={doc.fileUrl}
                          onChange={(e) => handleDocumentChange(index, 'fileUrl', e.target.value)}
                          fullWidth
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Box display="flex" justifyContent="flex-end" height="100%" alignItems="center">
                          <IconButton
                            color="error"
                            onClick={() => handleRemoveDocument(index)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Grid>
                    </Grid>
                  </Paper>
                </Grid>
              ))
            )}
            
            {/* Notes */}
            <Grid item xs={12}>
              <Divider sx={{ my: 1 }} />
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Notes
              </Typography>
              <TextField
                name="notes"
                label="Notes"
                value={formData.notes}
                onChange={handleChange}
                fullWidth
                multiline
                rows={4}
              />
            </Grid>
          </Grid>
        </LocalizationProvider>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          color="primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : (item ? 'Update' : 'Create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TrackingItemForm;