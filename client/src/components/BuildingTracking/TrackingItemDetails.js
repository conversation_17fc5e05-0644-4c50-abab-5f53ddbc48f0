import React from 'react';
import {
  <PERSON>,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Grid,
  Divider,
  Chip,
  Paper,
  Link,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import WarningIcon from '@mui/icons-material/Warning';
import EventIcon from '@mui/icons-material/Event';
import BusinessIcon from '@mui/icons-material/Business';
import CategoryIcon from '@mui/icons-material/Category';
import DescriptionIcon from '@mui/icons-material/Description';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LanguageIcon from '@mui/icons-material/Language';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import NoteIcon from '@mui/icons-material/Note';
import { format } from 'date-fns';

/**
 * Component for displaying the details of a tracking item
 */
const TrackingItemDetails = ({
  open,
  onClose,
  onEdit,
  item
}) => {
  if (!item) return null;
  
  // Format dates
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMMM d, yyyy');
  };
  
  // Format status
  const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };
  
  // Get status chip color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'expired':
        return 'error';
      case 'pending_renewal':
        return 'warning';
      case 'renewed':
        return 'info';
      case 'terminated':
        return 'default';
      case 'draft':
        return 'secondary';
      default:
        return 'default';
    }
  };
  
  // Check if an item is expiring soon (within 30 days)
  const isExpiringSoon = (expirationDate) => {
    if (!expirationDate) return false;
    
    const today = new Date();
    const expDate = new Date(expirationDate);
    const diffTime = expDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays >= 0 && diffDays <= 30;
  };
  
  // Format currency
  const formatCurrency = (amount, currency) => {
    if (!amount) return 'N/A';
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(amount);
  };
  
  // Format billing cycle
  const formatBillingCycle = (cycle) => {
    if (!cycle) return '';
    
    switch (cycle) {
      case 'one-time':
        return '(one-time)';
      case 'monthly':
        return '(monthly)';
      case 'quarterly':
        return '(quarterly)';
      case 'annually':
        return '(annually)';
      case 'other':
        return '(other)';
      default:
        return '';
    }
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center">
            <Typography variant="h6" component="span">
              {item.name}
            </Typography>
            {isExpiringSoon(item.expirationDate) && (
              <Chip
                icon={<WarningIcon />}
                label="Expiring Soon"
                color="warning"
                size="small"
                sx={{ ml: 2 }}
              />
            )}
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Basic Information
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell width="30%" sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <BusinessIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Building</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.buildingId?.name || 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <CategoryIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Type</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Chip
                          label={item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                          size="small"
                          color={
                            item.type === 'contract' ? 'primary' :
                            item.type === 'service' ? 'secondary' :
                            item.type === 'inspection' ? 'info' :
                            'default'
                          }
                        />
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <DescriptionIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Description</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.description || 'No description provided'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
          
          {/* Provider Information */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Provider Information
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell width="40%" sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <BusinessIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Provider</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.provider?.name || 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Contact Person</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.provider?.contactPerson || 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <PhoneIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Phone</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.provider?.phone || 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <EmailIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Email</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.provider?.email ? (
                            <Link href={`mailto:${item.provider.email}`}>
                              {item.provider.email}
                            </Link>
                          ) : 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <LanguageIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Website</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.provider?.website ? (
                            <Link href={item.provider.website} target="_blank" rel="noopener noreferrer">
                              {item.provider.website}
                            </Link>
                          ) : 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
          
          {/* Dates and Status */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Dates and Status
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell width="40%" sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <CalendarTodayIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Start Date</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {formatDate(item.startDate)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <EventIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Expiration Date</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2" fontWeight={isExpiringSoon(item.expirationDate) ? 'bold' : 'normal'} color={isExpiringSoon(item.expirationDate) ? 'warning.main' : 'inherit'}>
                          {formatDate(item.expirationDate)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <CalendarTodayIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Renewal Date</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {formatDate(item.renewalDate)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <NotificationsIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Notification Days</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {item.notificationDays || 30} days before expiration
                        </Typography>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <CategoryIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Status</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Chip
                          label={formatStatus(item.status)}
                          size="small"
                          color={getStatusColor(item.status)}
                        />
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
          
          {/* Cost Information */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Cost Information
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableBody>
                    <TableRow>
                      <TableCell width="40%" sx={{ borderBottom: 'none' }}>
                        <Box display="flex" alignItems="center">
                          <MonetizationOnIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2">Cost</Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2">
                          {formatCurrency(item.cost?.amount, item.cost?.currency)} {formatBillingCycle(item.cost?.billingCycle)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>
          
          {/* Documents */}
          <Grid item xs={12} md={6}>
            <Paper variant="outlined" sx={{ p: 2, height: '100%' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Documents
              </Typography>
              {item.documents && item.documents.length > 0 ? (
                <TableContainer>
                  <Table size="small">
                    <TableBody>
                      {item.documents.map((doc, index) => (
                        <TableRow key={index}>
                          <TableCell width="40%" sx={{ borderBottom: index === item.documents.length - 1 ? 'none' : undefined }}>
                            <Box display="flex" alignItems="center">
                              <AttachFileIcon fontSize="small" sx={{ mr: 1 }} />
                              <Typography variant="body2">{doc.name || `Document ${index + 1}`}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell sx={{ borderBottom: index === item.documents.length - 1 ? 'none' : undefined }}>
                            <Typography variant="body2">
                              {doc.fileUrl ? (
                                <Link href={doc.fileUrl} target="_blank" rel="noopener noreferrer">
                                  View Document
                                </Link>
                              ) : 'No file URL provided'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" p={2}>
                  <AttachFileIcon sx={{ fontSize: 40, opacity: 0.5, mb: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    No documents attached
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
          
          {/* Notes */}
          {item.notes && (
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  <Box display="flex" alignItems="center">
                    <NoteIcon fontSize="small" sx={{ mr: 1 }} />
                    <span>Notes</span>
                  </Box>
                </Typography>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                  {item.notes || 'No notes provided'}
                </Typography>
              </Paper>
            </Grid>
          )}
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
        <Button 
          onClick={() => onEdit(item)} 
          variant="contained" 
          color="primary"
        >
          Edit
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TrackingItemDetails;