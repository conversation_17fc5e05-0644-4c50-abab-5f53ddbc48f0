import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Chip,
  IconButton,
  Tooltip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  Alert,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import WarningIcon from '@mui/icons-material/Warning';
import { format } from 'date-fns';

/**
 * Component for displaying a list of tracking items with filtering and sorting options
 */
const TrackingItemsList = ({
  items = [],
  buildings = [],
  loading = false,
  error = null,
  onAddItem,
  onEditItem,
  onViewItem,
  onDeleteItem,
  onFilterChange
}) => {
  // State for sorting and pagination
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState('expirationDate');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // State for filters
  const [filters, setFilters] = useState({
    buildingId: '',
    type: '',
    status: '',
    expiringBefore: null,
    expiringAfter: null
  });
  
  // State for showing filter panel
  const [showFilters, setShowFilters] = useState(false);
  
  // Apply filters when they change
  useEffect(() => {
    if (onFilterChange) {
      // Convert dates to ISO strings for API
      const apiFilters = { ...filters };
      if (apiFilters.expiringBefore) {
        apiFilters.expiringBefore = apiFilters.expiringBefore.toISOString();
      }
      if (apiFilters.expiringAfter) {
        apiFilters.expiringAfter = apiFilters.expiringAfter.toISOString();
      }
      
      onFilterChange(apiFilters);
    }
  }, [filters, onFilterChange]);
  
  // Handle sort request
  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };
  
  // Handle filter change
  const handleFilterChange = (name, value) => {
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setPage(0); // Reset to first page when filters change
  };
  
  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      buildingId: '',
      type: '',
      status: '',
      expiringBefore: null,
      expiringAfter: null
    });
  };
  
  // Handle pagination changes
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // Get status chip color based on status
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'expired':
        return 'error';
      case 'pending_renewal':
        return 'warning';
      case 'renewed':
        return 'info';
      case 'terminated':
        return 'default';
      case 'draft':
        return 'secondary';
      default:
        return 'default';
    }
  };
  
  // Format status for display
  const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };
  
  // Check if an item is expiring soon (within 30 days)
  const isExpiringSoon = (expirationDate) => {
    if (!expirationDate) return false;
    
    const today = new Date();
    const expDate = new Date(expirationDate);
    const diffTime = expDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays >= 0 && diffDays <= 30;
  };
  
  // Sort function
  const sortedItems = useMemo(() => {
    const comparator = (a, b) => {
      let aValue = a[orderBy];
      let bValue = b[orderBy];
      
      // Handle nested properties
      if (orderBy === 'buildingId') {
        aValue = a.buildingId?.name || '';
        bValue = b.buildingId?.name || '';
      } else if (orderBy === 'provider') {
        aValue = a.provider?.name || '';
        bValue = b.provider?.name || '';
      }
      
      // Handle dates
      if (orderBy === 'startDate' || orderBy === 'expirationDate' || orderBy === 'renewalDate') {
        aValue = aValue ? new Date(aValue).getTime() : 0;
        bValue = bValue ? new Date(bValue).getTime() : 0;
      }
      
      if (bValue < aValue) {
        return order === 'asc' ? 1 : -1;
      }
      if (bValue > aValue) {
        return order === 'asc' ? -1 : 1;
      }
      return 0;
    };
    
    return [...items].sort(comparator);
  }, [items, order, orderBy]);
  
  // Get current page items
  const currentItems = sortedItems.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );
  
  // Render loading state
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Box p={2}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }
  
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6" component="h2">
          Tracking Items
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ mr: 1 }}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={onAddItem}
          >
            Add Item
          </Button>
        </Box>
      </Box>
      
      {/* Filter panel */}
      {showFilters && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Building</InputLabel>
                <Select
                  value={filters.buildingId}
                  label="Building"
                  onChange={(e) => handleFilterChange('buildingId', e.target.value)}
                >
                  <MenuItem value="">All Buildings</MenuItem>
                  {buildings.map((building) => (
                    <MenuItem key={building._id} value={building._id}>
                      {building.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={filters.type}
                  label="Type"
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="contract">Contract</MenuItem>
                  <MenuItem value="service">Service</MenuItem>
                  <MenuItem value="inspection">Inspection</MenuItem>
                  <MenuItem value="license">License</MenuItem>
                  <MenuItem value="certification">Certification</MenuItem>
                  <MenuItem value="warranty">Warranty</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="expired">Expired</MenuItem>
                  <MenuItem value="pending_renewal">Pending Renewal</MenuItem>
                  <MenuItem value="renewed">Renewed</MenuItem>
                  <MenuItem value="terminated">Terminated</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Expiring After"
                  value={filters.expiringAfter}
                  onChange={(date) => handleFilterChange('expiringAfter', date)}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Expiring Before"
                  value={filters.expiringBefore}
                  onChange={(date) => handleFilterChange('expiringBefore', date)}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                onClick={handleClearFilters}
                fullWidth
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}
      
      {/* Items table */}
      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'name'}
                  direction={orderBy === 'name' ? order : 'asc'}
                  onClick={() => handleRequestSort('name')}
                >
                  Name
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'buildingId'}
                  direction={orderBy === 'buildingId' ? order : 'asc'}
                  onClick={() => handleRequestSort('buildingId')}
                >
                  Building
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'type'}
                  direction={orderBy === 'type' ? order : 'asc'}
                  onClick={() => handleRequestSort('type')}
                >
                  Type
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'provider'}
                  direction={orderBy === 'provider' ? order : 'asc'}
                  onClick={() => handleRequestSort('provider')}
                >
                  Provider
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'expirationDate'}
                  direction={orderBy === 'expirationDate' ? order : 'asc'}
                  onClick={() => handleRequestSort('expirationDate')}
                >
                  Expiration Date
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={orderBy === 'status'}
                  direction={orderBy === 'status' ? order : 'asc'}
                  onClick={() => handleRequestSort('status')}
                >
                  Status
                </TableSortLabel>
              </TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {currentItems.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No tracking items found
                </TableCell>
              </TableRow>
            ) : (
              currentItems.map((item) => (
                <TableRow key={item._id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      {isExpiringSoon(item.expirationDate) && (
                        <Tooltip title="Expiring soon">
                          <WarningIcon color="warning" fontSize="small" sx={{ mr: 1 }} />
                        </Tooltip>
                      )}
                      {item.name}
                    </Box>
                  </TableCell>
                  <TableCell>{item.buildingId?.name || 'N/A'}</TableCell>
                  <TableCell>
                    <Chip
                      label={item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                      size="small"
                      color={
                        item.type === 'contract' ? 'primary' :
                        item.type === 'service' ? 'secondary' :
                        item.type === 'inspection' ? 'info' :
                        'default'
                      }
                    />
                  </TableCell>
                  <TableCell>{item.provider?.name || 'N/A'}</TableCell>
                  <TableCell>
                    {item.expirationDate
                      ? format(new Date(item.expirationDate), 'MMM d, yyyy')
                      : 'N/A'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={formatStatus(item.status)}
                      size="small"
                      color={getStatusColor(item.status)}
                    />
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => onViewItem(item)}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton
                        size="small"
                        onClick={() => onEditItem(item)}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton
                        size="small"
                        onClick={() => onDeleteItem(item)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={sortedItems.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>
    </Box>
  );
};

export default TrackingItemsList;