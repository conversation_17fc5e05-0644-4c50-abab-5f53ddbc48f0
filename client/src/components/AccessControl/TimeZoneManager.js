import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  TextField,
  Paper,
  Grid,
  Button,
  Alert,
  Divider,
  Switch,
  FormControlLabel,
  Tooltip,
  IconButton
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { DateTime } from 'luxon';

/**
 * TimeZoneManager component for managing time zones in the access control system
 * Provides functionality for:
 * - Selecting time zones
 * - Converting times between time zones
 * - Handling daylight saving time
 */
const TimeZoneManager = ({ onTimeZoneChange }) => {
  // List of common time zones
  const timeZones = [
    { id: 'UTC', name: 'UTC (Coordinated Universal Time)', offset: 0 },
    { id: 'America/New_York', name: 'Eastern Time (ET)', offset: -5 },
    { id: 'America/Chicago', name: 'Central Time (CT)', offset: -6 },
    { id: 'America/Denver', name: 'Mountain Time (MT)', offset: -7 },
    { id: 'America/Los_Angeles', name: 'Pacific Time (PT)', offset: -8 },
    { id: 'America/Anchorage', name: 'Alaska Time (AKT)', offset: -9 },
    { id: 'Pacific/Honolulu', name: 'Hawaii Time (HT)', offset: -10 },
    { id: 'Europe/London', name: 'Greenwich Mean Time (GMT)', offset: 0 },
    { id: 'Europe/Paris', name: 'Central European Time (CET)', offset: 1 },
    { id: 'Europe/Athens', name: 'Eastern European Time (EET)', offset: 2 },
    { id: 'Asia/Dubai', name: 'Gulf Standard Time (GST)', offset: 4 },
    { id: 'Asia/Kolkata', name: 'India Standard Time (IST)', offset: 5.5 },
    { id: 'Asia/Singapore', name: 'Singapore Time (SGT)', offset: 8 },
    { id: 'Asia/Tokyo', name: 'Japan Standard Time (JST)', offset: 9 },
    { id: 'Australia/Sydney', name: 'Australian Eastern Time (AET)', offset: 10 },
    { id: 'Pacific/Auckland', name: 'New Zealand Time (NZT)', offset: 12 }
  ];

  // State for selected time zone and current time
  const [selectedTimeZone, setSelectedTimeZone] = useState('UTC');
  const [currentTime, setCurrentTime] = useState(DateTime.now().setZone('UTC'));
  const [respectDST, setRespectDST] = useState(true);
  const [conversionSource, setConversionSource] = useState('');
  const [conversionTarget, setConversionTarget] = useState('');
  const [conversionTime, setConversionTime] = useState('');
  const [convertedTime, setConvertedTime] = useState('');
  const [error, setError] = useState(null);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(DateTime.now().setZone(selectedTimeZone));
    }, 1000);

    return () => clearInterval(timer);
  }, [selectedTimeZone]);

  // Handle time zone change
  const handleTimeZoneChange = (event) => {
    const newTimeZone = event.target.value;
    setSelectedTimeZone(newTimeZone);
    
    // Update current time with new time zone
    setCurrentTime(DateTime.now().setZone(newTimeZone));
    
    // Notify parent component if callback provided
    if (onTimeZoneChange) {
      onTimeZoneChange(newTimeZone);
    }
  };

  // Handle DST toggle
  const handleDSTToggle = (event) => {
    setRespectDST(event.target.checked);
  };

  // Handle time conversion
  const handleConvert = () => {
    try {
      setError(null);
      
      if (!conversionSource || !conversionTarget || !conversionTime) {
        setError('Please fill in all fields for time conversion');
        return;
      }
      
      // Parse the input time
      const [hours, minutes] = conversionTime.split(':').map(Number);
      
      // Create a DateTime object in the source time zone
      let sourceTime = DateTime.now().setZone(conversionSource).set({
        hour: hours,
        minute: minutes,
        second: 0,
        millisecond: 0
      });
      
      // If DST should be ignored, adjust the time
      if (!respectDST) {
        // Get the fixed offset without DST
        const sourceZone = timeZones.find(tz => tz.id === conversionSource);
        const targetZone = timeZones.find(tz => tz.id === conversionTarget);
        
        if (sourceZone && targetZone) {
          // Calculate the time difference using fixed offsets
          const hoursDiff = targetZone.offset - sourceZone.offset;
          sourceTime = sourceTime.plus({ hours: hoursDiff });
        }
      } else {
        // Convert to target time zone respecting DST
        sourceTime = sourceTime.setZone(conversionTarget);
      }
      
      // Format the result
      setConvertedTime(sourceTime.toFormat('HH:mm'));
    } catch (error) {
      console.error('Error converting time:', error);
      setError('Error converting time. Please check your input.');
    }
  };

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <AccessTimeIcon sx={{ mr: 1 }} />
        <Typography variant="h6" component="h2">
          Time Zone Management
        </Typography>
        <Tooltip title="Time zone management allows you to configure schedules across multiple time zones and handle daylight saving time transitions.">
          <IconButton size="small" sx={{ ml: 1 }}>
            <HelpOutlineIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
      
      <Divider sx={{ mb: 3 }} />
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom>
            System Time Zone
          </Typography>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="time-zone-select-label">Time Zone</InputLabel>
            <Select
              labelId="time-zone-select-label"
              id="time-zone-select"
              value={selectedTimeZone}
              label="Time Zone"
              onChange={handleTimeZoneChange}
            >
              {timeZones.map((tz) => (
                <MenuItem key={tz.id} value={tz.id}>
                  {tz.name} (UTC{tz.offset >= 0 ? '+' : ''}{tz.offset})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Current time in {selectedTimeZone}:
            </Typography>
            <Typography variant="h6">
              {currentTime.toFormat('yyyy-MM-dd HH:mm:ss')}
            </Typography>
          </Box>
          
          <FormControlLabel
            control={
              <Switch
                checked={respectDST}
                onChange={handleDSTToggle}
                color="primary"
              />
            }
            label="Respect Daylight Saving Time"
          />
          
          <Tooltip title="When enabled, the system will automatically adjust times during DST transitions. When disabled, fixed UTC offsets will be used.">
            <IconButton size="small">
              <InfoIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle1" gutterBottom>
            Time Conversion Tool
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="source-time-zone-label">From Time Zone</InputLabel>
            <Select
              labelId="source-time-zone-label"
              id="source-time-zone"
              value={conversionSource}
              label="From Time Zone"
              onChange={(e) => setConversionSource(e.target.value)}
            >
              {timeZones.map((tz) => (
                <MenuItem key={tz.id} value={tz.id}>
                  {tz.name} (UTC{tz.offset >= 0 ? '+' : ''}{tz.offset})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel id="target-time-zone-label">To Time Zone</InputLabel>
            <Select
              labelId="target-time-zone-label"
              id="target-time-zone"
              value={conversionTarget}
              label="To Time Zone"
              onChange={(e) => setConversionTarget(e.target.value)}
            >
              {timeZones.map((tz) => (
                <MenuItem key={tz.id} value={tz.id}>
                  {tz.name} (UTC{tz.offset >= 0 ? '+' : ''}{tz.offset})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={6}>
              <TextField
                label="Time (HH:MM)"
                type="time"
                value={conversionTime}
                onChange={(e) => setConversionTime(e.target.value)}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 60 }}
                fullWidth
              />
            </Grid>
            <Grid item xs={6}>
              <Button 
                variant="contained" 
                onClick={handleConvert}
                fullWidth
              >
                Convert
              </Button>
            </Grid>
          </Grid>
          
          {convertedTime && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Converted time:
              </Typography>
              <Typography variant="h6">
                {convertedTime}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {respectDST ? 'Including DST adjustments' : 'Without DST adjustments'}
              </Typography>
            </Box>
          )}
        </Grid>
      </Grid>
    </Paper>
  );
};

export default TimeZoneManager;