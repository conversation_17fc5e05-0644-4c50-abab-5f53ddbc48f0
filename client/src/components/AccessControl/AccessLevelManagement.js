import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import ScheduleIcon from '@mui/icons-material/Schedule';

import accessControlService from '../../services/accessControlService';

const AccessLevelManagement = ({ configStatus }) => {
  // State for access level list
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [accessLevels, setAccessLevels] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filterSystem, setFilterSystem] = useState('all');

  // Fetch access levels
  const fetchAccessLevels = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // This would be an actual API call in a real implementation
      const data = await accessControlService.getAllAccessLevels();
      setAccessLevels(data || []);
      
    } catch (err) {
      console.error('Error fetching access levels:', err);
      setError('Failed to load access levels. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Load access levels on component mount
  useEffect(() => {
    fetchAccessLevels();
  }, []);

  // Handle refresh button click
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAccessLevels();
    setRefreshing(false);
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Handle system filter change
  const handleFilterChange = (event) => {
    setFilterSystem(event.target.value);
    setPage(0);
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Filter access levels based on search term and system filter
  const filteredAccessLevels = accessLevels.filter(level => {
    const matchesSearch = 
      level.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      level.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = 
      filterSystem === 'all' || 
      level.system === filterSystem;
    
    return matchesSearch && matchesFilter;
  });

  // Calculate pagination
  const paginatedAccessLevels = filteredAccessLevels.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Check if any access control system is configured
  const isConfigured = configStatus.unifiAccess || configStatus.lenelS2NetBox;

  if (!isConfigured) {
    return (
      <Alert severity="warning">
        No access control systems are configured. Access level management requires at least one configured access control system.
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" component="h2">
          <VpnKeyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Access Level Management
        </Typography>
        <Box>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Add Access Level">
            <IconButton>
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Paper sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={5}>
            <TextField
              fullWidth
              variant="outlined"
              label="Search Access Levels"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth size="small">
              <InputLabel>System</InputLabel>
              <Select
                value={filterSystem}
                onChange={handleFilterChange}
                label="System"
              >
                <MenuItem value="all">All Systems</MenuItem>
                {configStatus.unifiAccess && (
                  <MenuItem value="unifiAccess">Unifi Access</MenuItem>
                )}
                {configStatus.lenelS2NetBox && (
                  <MenuItem value="lenelS2NetBox">Lenel S2 NetBox</MenuItem>
                )}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              color="primary"
            >
              Add Access Level
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Access Level Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>System</TableCell>
                <TableCell>Doors</TableCell>
                <TableCell>Schedule</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : paginatedAccessLevels.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="text.secondary">
                      {searchTerm || filterSystem !== 'all' 
                        ? 'No access levels match your search criteria.' 
                        : 'No access levels available. Click "Add Access Level" to create one.'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedAccessLevels.map((level) => (
                  <TableRow key={level.id}>
                    <TableCell>{level.name}</TableCell>
                    <TableCell>{level.description}</TableCell>
                    <TableCell>
                      <Chip 
                        label={level.system === 'unifiAccess' ? 'Unifi Access' : 'Lenel S2 NetBox'} 
                        size="small" 
                        color={level.system === 'unifiAccess' ? 'primary' : 'secondary'}
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title={level.doors?.map(door => door.name).join(', ') || 'No doors assigned'}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <MeetingRoomIcon fontSize="small" sx={{ mr: 1 }} />
                          {level.doors?.length || 0} door(s)
                        </Box>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={level.schedule?.name || 'No schedule assigned'}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <ScheduleIcon fontSize="small" sx={{ mr: 1 }} />
                          {level.schedule?.name || 'None'}
                        </Box>
                      </Tooltip>
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Edit">
                        <IconButton size="small">
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton size="small">
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredAccessLevels.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
      
      <Box sx={{ mt: 3 }}>
        <Alert severity="info">
          Access level management allows you to define which doors users can access and when. Create access levels and assign them to users to control building access.
        </Alert>
      </Box>
    </Box>
  );
};

export default AccessLevelManagement;