import React, { useState, useEffect, useRef } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Chip,
  Switch,
  FormControlLabel,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Badge
} from '@mui/material';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import websocketService from '../../services/websocketService';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import FilterListIcon from '@mui/icons-material/FilterList';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import ScheduleIcon from '@mui/icons-material/Schedule';
import WarningIcon from '@mui/icons-material/Warning';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import VisibilityIcon from '@mui/icons-material/Visibility';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import SecurityIcon from '@mui/icons-material/Security';

import accessControlService from '../../services/accessControlService';

const DoorManagement = ({ configStatus }) => {
  // State for door list
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [doors, setDoors] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSystem, setFilterSystem] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  
  // State for door control
  const [controllingDoor, setControllingDoor] = useState(false);
  const [controlError, setControlError] = useState(null);
  const [controlSuccess, setControlSuccess] = useState(false);
  const [selectedDoor, setSelectedDoor] = useState(null);
  const [doorDetailsOpen, setDoorDetailsOpen] = useState(false);
  const [doorControlDialogOpen, setDoorControlDialogOpen] = useState(false);
  const [doorControlAction, setDoorControlAction] = useState('unlock');
  
  // State for door details
  const [doorDetails, setDoorDetails] = useState(null);
  const [doorEventsLoading, setDoorEventsLoading] = useState(false);
  const [doorEvents, setDoorEvents] = useState([]);
  const [doorSchedulesLoading, setDoorSchedulesLoading] = useState(false);
  const [doorSchedules, setDoorSchedules] = useState([]);
  const [newDoorEventsCount, setNewDoorEventsCount] = useState(0);
  const lastDoorEventUpdateRef = useRef(new Date());

  // Handler for WebSocket door events
  const handleDoorEvents = (events) => {
    if (!events || !Array.isArray(events) || events.length === 0 || !selectedDoor) return;
    
    // Filter events for the currently selected door
    const doorId = selectedDoor.id;
    const relevantEvents = events.filter(event => 
      event.doorId === doorId || event.door === selectedDoor.name
    );
    
    if (relevantEvents.length === 0) return;
    
    setDoorEvents(prevEvents => {
      // Get existing event IDs to avoid duplicates
      const existingIds = new Set(prevEvents.map(event => event.id));
      
      // Filter out duplicates and add new events
      const newEvents = relevantEvents.filter(event => !existingIds.has(event.id));
      
      if (newEvents.length === 0) return prevEvents;
      
      // Update new events count
      setNewDoorEventsCount(prev => prev + newEvents.length);
      
      // Return updated events with new events at the top
      return [...newEvents, ...prevEvents].slice(0, 100); // Keep only the latest 100 events
    });
  };

  // Set up WebSocket connection
  useEffect(() => {
    // Connect to WebSocket server
    websocketService.connect().then(() => {
      console.log('WebSocket connected for door management');
      
      // Subscribe to door events
      websocketService.subscribe('door-events');
      
      // Add event listener
      websocketService.addEventListener('door-events', handleDoorEvents);
    }).catch(error => {
      console.warn('WebSocket connection failed for door management:', error);
    });
    
    // Clean up on unmount
    return () => {
      websocketService.removeEventListener('door-events', handleDoorEvents);
    };
  }, []);

  // Fetch doors on component mount
  useEffect(() => {
    fetchDoors();
  }, []);

  const fetchDoors = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await accessControlService.getAllDoors();
      setDoors(data);
    } catch (err) {
      console.error('Error fetching doors:', err);
      setError('Failed to load doors. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleFilterSystemChange = (event) => {
    setFilterSystem(event.target.value);
    setPage(0);
  };

  const handleFilterStatusChange = (event) => {
    setFilterStatus(event.target.value);
    setPage(0);
  };

  const handleRefresh = () => {
    fetchDoors();
  };

  // Door control handlers
  const handleOpenDoorControlDialog = (door) => {
    setSelectedDoor(door);
    setDoorControlDialogOpen(true);
    setDoorControlAction('unlock');
    setControlError(null);
    setControlSuccess(false);
  };

  const handleCloseDoorControlDialog = () => {
    setDoorControlDialogOpen(false);
    // If we successfully controlled a door, refresh the list
    if (controlSuccess) {
      fetchDoors();
    }
  };

  const handleDoorControlActionChange = (event) => {
    setDoorControlAction(event.target.value);
  };

  const handleControlDoor = async () => {
    if (!selectedDoor || !doorControlAction) return;
    
    try {
      setControllingDoor(true);
      setControlError(null);
      setControlSuccess(false);
      
      await accessControlService.controlDoor(
        selectedDoor.id,
        selectedDoor.system,
        doorControlAction
      );
      
      setControlSuccess(true);
    } catch (err) {
      console.error('Error controlling door:', err);
      setControlError(err.response?.data?.error || 'Failed to control door. Please try again.');
    } finally {
      setControllingDoor(false);
    }
  };

  // Door details handlers
  const handleOpenDoorDetails = (door) => {
    setSelectedDoor(door);
    setDoorDetailsOpen(true);
    fetchDoorDetails(door);
  };

  const handleCloseDoorDetails = () => {
    setDoorDetailsOpen(false);
  };

  const fetchDoorDetails = async (door) => {
    // In a real implementation, we would fetch door details, events, and schedules
    // For now, we'll create mock data
    
    // Mock door details
    setDoorDetails({
      ...door,
      model: door.system === 'unifi-access' ? 'Unifi Access Hub' : 'Lenel S2 NetBox Controller',
      firmwareVersion: '1.2.3',
      lastSeen: new Date().toISOString(),
      batteryLevel: door.system === 'unifi-access' ? '85%' : 'N/A',
      connectionType: door.system === 'unifi-access' ? 'Wireless' : 'Wired',
      ipAddress: door.system === 'unifi-access' ? '*************' : '*************'
    });
    
    // Mock door events
    setDoorEventsLoading(true);
    setTimeout(() => {
      const mockEvents = [
        {
          id: '1',
          type: 'access_granted',
          timestamp: new Date(Date.now() - 5 * 60000).toISOString(), // 5 minutes ago
          user: 'John Doe',
          details: 'Access granted via card'
        },
        {
          id: '2',
          type: 'access_denied',
          timestamp: new Date(Date.now() - 15 * 60000).toISOString(), // 15 minutes ago
          user: 'Jane Smith',
          details: 'Invalid credentials'
        },
        {
          id: '3',
          type: 'door_held_open',
          timestamp: new Date(Date.now() - 30 * 60000).toISOString(), // 30 minutes ago
          user: null,
          details: 'Door held open for 30 seconds'
        },
        {
          id: '4',
          type: 'door_forced_open',
          timestamp: new Date(Date.now() - 45 * 60000).toISOString(), // 45 minutes ago
          user: null,
          details: 'Door forced open alarm triggered'
        },
        {
          id: '5',
          type: 'schedule_activated',
          timestamp: new Date(Date.now() - 60 * 60000).toISOString(), // 1 hour ago
          user: null,
          details: 'Business hours schedule activated'
        }
      ];
      setDoorEvents(mockEvents);
      setDoorEventsLoading(false);
    }, 1000);
    
    // Mock door schedules
    setDoorSchedulesLoading(true);
    setTimeout(() => {
      const mockSchedules = [
        {
          id: '1',
          name: 'Business Hours',
          description: 'Monday to Friday, 9 AM to 5 PM',
          active: true,
          timeWindows: [
            { days: 'Monday-Friday', startTime: '09:00', endTime: '17:00' }
          ]
        },
        {
          id: '2',
          name: 'Weekend Access',
          description: 'Saturday and Sunday, 10 AM to 2 PM',
          active: false,
          timeWindows: [
            { days: 'Saturday-Sunday', startTime: '10:00', endTime: '14:00' }
          ]
        },
        {
          id: '3',
          name: 'After Hours',
          description: 'Extended hours for authorized personnel',
          active: true,
          timeWindows: [
            { days: 'Monday-Friday', startTime: '17:00', endTime: '22:00' }
          ]
        }
      ];
      setDoorSchedules(mockSchedules);
      setDoorSchedulesLoading(false);
    }, 1500);
  };

  // Filter and search doors
  const filteredDoors = doors.filter(door => {
    // Filter by system
    if (filterSystem !== 'all' && door.system !== filterSystem) {
      return false;
    }
    
    // Filter by status
    if (filterStatus !== 'all') {
      const doorStatus = door.status ? door.status.toLowerCase() : '';
      if (filterStatus === 'locked' && doorStatus !== 'locked') {
        return false;
      }
      if (filterStatus === 'unlocked' && doorStatus !== 'unlocked') {
        return false;
      }
      if (filterStatus === 'alarm' && !['alarm', 'forced', 'held'].includes(doorStatus)) {
        return false;
      }
    }
    
    // Search by name, location
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        (door.name && door.name.toLowerCase().includes(searchLower)) ||
        (door.location && door.location.toLowerCase().includes(searchLower))
      );
    }
    
    return true;
  });

  // Pagination
  const paginatedDoors = filteredDoors.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  // Helper functions
  const getDoorStatusChip = (door) => {
    const status = door.status ? door.status.toLowerCase() : 'unknown';
    
    switch (status) {
      case 'locked':
        return <Chip icon={<LockIcon />} label="Locked" color="success" size="small" />;
      case 'unlocked':
        return <Chip icon={<LockOpenIcon />} label="Unlocked" color="primary" size="small" />;
      case 'held':
      case 'held_open':
        return <Chip icon={<WarningIcon />} label="Held Open" color="warning" size="small" />;
      case 'forced':
      case 'forced_open':
        return <Chip icon={<ErrorIcon />} label="Forced Open" color="error" size="small" />;
      case 'alarm':
        return <Chip icon={<ErrorIcon />} label="Alarm" color="error" size="small" />;
      default:
        return <Chip icon={<InfoIcon />} label="Unknown" color="default" size="small" />;
    }
  };

  const getSystemChip = (system) => {
    return (
      <Chip
        icon={<SecurityIcon />}
        label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
        color={system === 'unifi-access' ? 'primary' : 'secondary'}
        size="small"
      />
    );
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Unknown';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    
    return date.toLocaleDateString();
  };

  const getEventIcon = (eventType) => {
    switch (eventType) {
      case 'access_granted':
        return <LockOpenIcon color="success" />;
      case 'access_denied':
        return <LockIcon color="error" />;
      case 'door_held_open':
        return <WarningIcon color="warning" />;
      case 'door_forced_open':
        return <ErrorIcon color="error" />;
      case 'schedule_activated':
        return <AccessTimeIcon color="info" />;
      default:
        return <InfoIcon color="primary" />;
    }
  };
  
  // Function to convert events to CSV format
  const convertEventsToCSV = (events) => {
    if (!events || events.length === 0) return '';
    
    // Define CSV headers
    const headers = ['Event Type', 'Details', 'Timestamp', 'User', 'Door'];
    
    // Create CSV content
    let csvContent = headers.join(',') + '\n';
    
    events.forEach(event => {
      const eventType = event.type.replace(/_/g, ' ');
      const details = event.details.replace(/,/g, ';'); // Replace commas with semicolons to avoid CSV issues
      const timestamp = event.timestamp ? new Date(event.timestamp).toLocaleString() : '';
      const user = event.user || '';
      const door = selectedDoor?.name || '';
      
      // Add row to CSV
      csvContent += `"${eventType}","${details}","${timestamp}","${user}","${door}"\n`;
    });
    
    return csvContent;
  };
  
  // Function to download CSV file
  const downloadCSV = (csvContent, filename = 'door-events.csv') => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Function to handle export button click
  const handleExportEvents = () => {
    if (!doorEvents || doorEvents.length === 0) {
      alert('No events to export');
      return;
    }
    
    const doorName = selectedDoor?.name?.replace(/\s+/g, '-').toLowerCase() || 'door';
    const csvContent = convertEventsToCSV(doorEvents);
    const filename = `${doorName}-events-${new Date().toISOString().slice(0, 10)}.csv`;
    downloadCSV(csvContent, filename);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Door Management
        </Typography>
        <Tooltip title="Refresh">
          <IconButton onClick={handleRefresh} disabled={loading}>
            {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TextField
            label="Search Doors"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ mr: 2, flexGrow: 1 }}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
            }}
          />
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
            <InputLabel>System</InputLabel>
            <Select
              value={filterSystem}
              onChange={handleFilterSystemChange}
              label="System"
            >
              <MenuItem value="all">All Systems</MenuItem>
              <MenuItem value="unifi-access">Unifi Access</MenuItem>
              <MenuItem value="lenel-s2-netbox">Lenel S2 NetBox</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filterStatus}
              onChange={handleFilterStatusChange}
              label="Status"
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="locked">Locked</MenuItem>
              <MenuItem value="unlocked">Unlocked</MenuItem>
              <MenuItem value="alarm">Alarm</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <Grid container spacing={2}>
              {paginatedDoors.length > 0 ? (
                paginatedDoors.map((door) => (
                  <Grid item xs={12} sm={6} md={4} key={door.id}>
                    <Card variant="outlined">
                      <CardHeader
                        title={door.name}
                        subheader={door.location || 'No location'}
                        action={
                          <Tooltip title="View Details">
                            <IconButton onClick={() => handleOpenDoorDetails(door)}>
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                        }
                      />
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                          <Box>
                            {getDoorStatusChip(door)}
                          </Box>
                          <Box>
                            {getSystemChip(door.system)}
                          </Box>
                        </Box>
                        
                        {door.lastActivity && (
                          <Typography variant="body2" color="text.secondary">
                            Last Activity: {formatTimestamp(door.lastActivity)}
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        <Button 
                          size="small" 
                          startIcon={<LockOpenIcon />}
                          onClick={() => handleOpenDoorControlDialog(door)}
                        >
                          Control
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body1">
                      No doors found
                    </Typography>
                  </Paper>
                </Grid>
              )}
            </Grid>
            
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <TablePagination
                rowsPerPageOptions={[6, 12, 24]}
                component="div"
                count={filteredDoors.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </Box>
          </>
        )}
      </Paper>

      {/* Door Control Dialog */}
      <Dialog
        open={doorControlDialogOpen}
        onClose={handleCloseDoorControlDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Control Door: {selectedDoor?.name}
        </DialogTitle>
        
        <DialogContent>
          {controlError && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {controlError}
            </Alert>
          )}
          
          {controlSuccess ? (
            <Alert severity="success" sx={{ mb: 3 }}>
              Door control command sent successfully.
            </Alert>
          ) : (
            <Box sx={{ p: 1 }}>
              <Typography variant="body1" gutterBottom>
                Select an action to perform on this door:
              </Typography>
              
              <FormControl fullWidth sx={{ mt: 2 }}>
                <InputLabel>Action</InputLabel>
                <Select
                  value={doorControlAction}
                  onChange={handleDoorControlActionChange}
                  label="Action"
                >
                  <MenuItem value="unlock">Unlock Door</MenuItem>
                  <MenuItem value="lock">Lock Door</MenuItem>
                  <MenuItem value="momentary-unlock">Momentary Unlock (Pulse)</MenuItem>
                </Select>
              </FormControl>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Door:</strong> {selectedDoor?.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Location:</strong> {selectedDoor?.location || 'N/A'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>System:</strong> {selectedDoor?.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Current Status:</strong> {selectedDoor?.status || 'Unknown'}
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDoorControlDialog}>
            {controlSuccess ? 'Close' : 'Cancel'}
          </Button>
          {!controlSuccess && (
            <Button
              variant="contained"
              onClick={handleControlDoor}
              disabled={controllingDoor}
              startIcon={controllingDoor ? <CircularProgress size={20} /> : null}
            >
              {controllingDoor ? 'Sending...' : 'Send Command'}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Door Details Dialog */}
      <Dialog
        open={doorDetailsOpen}
        onClose={handleCloseDoorDetails}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Door Details: {selectedDoor?.name}
        </DialogTitle>
        
        <DialogContent>
          <Grid container spacing={3}>
            {/* Door Information */}
            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Door Information
                </Typography>
                
                <Grid container spacing={1}>
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Name:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.name || 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Location:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.location || 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      System:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Status:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    {doorDetails && getDoorStatusChip(doorDetails)}
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Model:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.model || 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Firmware:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.firmwareVersion || 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Last Seen:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.lastSeen ? formatTimestamp(doorDetails.lastSeen) : 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      Connection:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.connectionType || 'N/A'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={4}>
                    <Typography variant="body2" color="text.secondary">
                      IP Address:
                    </Typography>
                  </Grid>
                  <Grid item xs={8}>
                    <Typography variant="body2">
                      {doorDetails?.ipAddress || 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
              
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Door Schedules
                </Typography>
                
                {doorSchedulesLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                    <CircularProgress size={24} />
                  </Box>
                ) : doorSchedules.length > 0 ? (
                  <List dense>
                    {doorSchedules.map((schedule, index) => (
                      <React.Fragment key={schedule.id}>
                        <ListItem>
                          <ListItemIcon>
                            <ScheduleIcon color={schedule.active ? 'primary' : 'disabled'} />
                          </ListItemIcon>
                          <ListItemText
                            primary={schedule.name}
                            secondary={schedule.description}
                          />
                          <ListItemSecondaryAction>
                            <Switch
                              edge="end"
                              checked={schedule.active}
                              onChange={() => {}}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < doorSchedules.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No schedules found for this door.
                  </Typography>
                )}
              </Paper>
            </Grid>
            
            {/* Recent Events */}
            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography 
                    variant="h6" 
                    onClick={() => setNewDoorEventsCount(0)} 
                    sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                  >
                    Recent Events
                    {newDoorEventsCount > 0 && (
                      <Badge 
                        badgeContent={newDoorEventsCount} 
                        color="primary" 
                        sx={{ ml: 2 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setNewDoorEventsCount(0);
                        }}
                      >
                        <span></span>
                      </Badge>
                    )}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {doorEvents && doorEvents.length > 0 && (
                      <Tooltip title="Export events to CSV">
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<FileDownloadIcon />}
                          onClick={handleExportEvents}
                        >
                          Export
                        </Button>
                      </Tooltip>
                    )}
                  </Box>
                </Box>
                
                {doorEventsLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                    <CircularProgress size={24} />
                  </Box>
                ) : doorEvents.length > 0 ? (
                  <List>
                    {doorEvents.map((event, index) => (
                      <React.Fragment key={event.id}>
                        <ListItem>
                          <ListItemIcon>
                            {getEventIcon(event.type)}
                          </ListItemIcon>
                          <ListItemText
                            primary={event.details}
                            secondary={
                              <>
                                {event.user && <span>User: {event.user} | </span>}
                                {formatTimestamp(event.timestamp)}
                              </>
                            }
                          />
                        </ListItem>
                        {index < doorEvents.length - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No recent events found for this door.
                  </Typography>
                )}
              </Paper>
            </Grid>
          </Grid>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseDoorDetails}>Close</Button>
          <Button
            variant="contained"
            onClick={() => {
              handleCloseDoorDetails();
              handleOpenDoorControlDialog(selectedDoor);
            }}
          >
            Control Door
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DoorManagement;