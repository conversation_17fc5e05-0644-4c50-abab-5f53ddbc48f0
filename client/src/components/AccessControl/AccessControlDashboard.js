import React, { useState, useEffect, useRef } from 'react';
import { 
  Typo<PERSON>, 
  <PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardHeader,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Button,
  Chip,
  Tooltip,
  Badge
} from '@mui/material';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import websocketService from '../../services/websocketService';
import PeopleIcon from '@mui/icons-material/People';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import EventIcon from '@mui/icons-material/Event';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PersonIcon from '@mui/icons-material/Person';
import WarningIcon from '@mui/icons-material/Warning';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';

import accessControlService from '../../services/accessControlService';

const AccessControlDashboard = ({ configStatus, onRefresh }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    users: [],
    doors: [],
    accessLevels: [],
    schedules: [],
    holidays: [],
    recentEvents: []
  });
  const [newEventsCount, setNewEventsCount] = useState(0);
  const lastUpdateRef = useRef(new Date());

  // Handler for generic access control events
  const handleAccessControlEvents = (events) => {
    if (!events || !Array.isArray(events) || events.length === 0) return;
    
    setDashboardData(prevData => {
      // Get existing event IDs to avoid duplicates
      const existingIds = new Set(prevData.recentEvents.map(event => event.id));
      
      // Filter out duplicates and add new events
      const newEvents = events.filter(event => !existingIds.has(event.id));
      
      if (newEvents.length === 0) return prevData;
      
      // Update new events count
      setNewEventsCount(prev => prev + newEvents.length);
      
      // Return updated dashboard data with new events at the top
      return {
        ...prevData,
        recentEvents: [...newEvents, ...prevData.recentEvents].slice(0, 100) // Keep only the latest 100 events
      };
    });
  };
  
  // Handler for access attempt events
  const handleAccessAttempt = (accessAttempt) => {
    if (!accessAttempt) return;
    
    // Create an event object from the access attempt
    const event = {
      id: `access-attempt-${accessAttempt.timestamp}-${accessAttempt.userId || 'unknown'}`,
      type: 'access-attempt',
      timestamp: accessAttempt.timestamp || new Date().toISOString(),
      title: accessAttempt.granted ? 'Access Granted' : 'Access Denied',
      description: `${accessAttempt.userName || 'Unknown user'} at ${accessAttempt.doorName || 'unknown door'}`,
      status: accessAttempt.granted ? 'success' : 'error',
      details: accessAttempt
    };
    
    // Add to recent events
    setDashboardData(prevData => {
      return {
        ...prevData,
        recentEvents: [event, ...prevData.recentEvents].slice(0, 100)
      };
    });
    
    // Increment new events counter
    setNewEventsCount(prev => prev + 1);
  };
  
  // Handler for policy change events
  const handlePolicyChange = (policyChange) => {
    if (!policyChange) return;
    
    // Create an event object from the policy change
    const event = {
      id: `policy-change-${policyChange.timestamp}-${policyChange.policyId || 'unknown'}`,
      type: 'policy-change',
      timestamp: policyChange.timestamp || new Date().toISOString(),
      title: 'Policy Updated',
      description: `Policy "${policyChange.policyName || 'Unknown'}" was ${policyChange.action || 'updated'}`,
      status: 'info',
      details: policyChange
    };
    
    // Add to recent events
    setDashboardData(prevData => {
      return {
        ...prevData,
        recentEvents: [event, ...prevData.recentEvents].slice(0, 100)
      };
    });
    
    // Increment new events counter
    setNewEventsCount(prev => prev + 1);
  };
  
  // Handler for access level change events
  const handleAccessLevelChange = (accessLevelChange) => {
    if (!accessLevelChange) return;
    
    // Create an event object from the access level change
    const event = {
      id: `access-level-change-${accessLevelChange.timestamp}-${accessLevelChange.accessLevelId || 'unknown'}`,
      type: 'access-level-change',
      timestamp: accessLevelChange.timestamp || new Date().toISOString(),
      title: 'Access Level Updated',
      description: `Access level "${accessLevelChange.accessLevelName || 'Unknown'}" was ${accessLevelChange.action || 'updated'}`,
      status: 'info',
      details: accessLevelChange
    };
    
    // Add to recent events
    setDashboardData(prevData => {
      return {
        ...prevData,
        recentEvents: [event, ...prevData.recentEvents].slice(0, 100)
      };
    });
    
    // Increment new events counter
    setNewEventsCount(prev => prev + 1);
  };
  
  // Handler for user status change events
  const handleUserStatusChange = (userStatusChange) => {
    if (!userStatusChange) return;
    
    // Create an event object from the user status change
    const event = {
      id: `user-status-change-${userStatusChange.timestamp}-${userStatusChange.userId || 'unknown'}`,
      type: 'user-status-change',
      timestamp: userStatusChange.timestamp || new Date().toISOString(),
      title: 'User Status Changed',
      description: `User "${userStatusChange.userName || 'Unknown'}" status changed to ${userStatusChange.status || 'unknown'}`,
      status: userStatusChange.status === 'active' ? 'success' : (userStatusChange.status === 'disabled' ? 'error' : 'warning'),
      details: userStatusChange
    };
    
    // Add to recent events
    setDashboardData(prevData => {
      return {
        ...prevData,
        recentEvents: [event, ...prevData.recentEvents].slice(0, 100)
      };
    });
    
    // Increment new events counter
    setNewEventsCount(prev => prev + 1);
  };
  
  // Handler for system status change events
  const handleSystemStatusChange = (systemStatusChange) => {
    if (!systemStatusChange) return;
    
    // Create an event object from the system status change
    const event = {
      id: `system-status-change-${systemStatusChange.timestamp}-${systemStatusChange.system || 'unknown'}`,
      type: 'system-status-change',
      timestamp: systemStatusChange.timestamp || new Date().toISOString(),
      title: 'System Status Changed',
      description: `${systemStatusChange.system || 'Unknown system'} status changed to ${systemStatusChange.status || 'unknown'}`,
      status: systemStatusChange.status === 'online' ? 'success' : (systemStatusChange.status === 'offline' ? 'error' : 'warning'),
      details: systemStatusChange
    };
    
    // Add to recent events
    setDashboardData(prevData => {
      return {
        ...prevData,
        recentEvents: [event, ...prevData.recentEvents].slice(0, 100)
      };
    });
    
    // Increment new events counter
    setNewEventsCount(prev => prev + 1);
  };

  // Set up WebSocket connection
  useEffect(() => {
    // Connect to WebSocket server
    websocketService.connect().then(() => {
      console.log('WebSocket connected for access control dashboard');
      
      // Subscribe to access control events
      websocketService.subscribe('access-control-events');
      websocketService.subscribe('access-attempt');
      websocketService.subscribe('policy-change');
      websocketService.subscribe('access-level-change');
      websocketService.subscribe('user-status-change');
      websocketService.subscribe('system-status-change');
      
      // Add event listeners
      websocketService.addEventListener('access-control-events', handleAccessControlEvents);
      websocketService.addEventListener('access-attempt', handleAccessAttempt);
      websocketService.addEventListener('policy-change', handlePolicyChange);
      websocketService.addEventListener('access-level-change', handleAccessLevelChange);
      websocketService.addEventListener('user-status-change', handleUserStatusChange);
      websocketService.addEventListener('system-status-change', handleSystemStatusChange);
      
      console.log('Subscribed to all access control event types');
    }).catch(error => {
      console.warn('WebSocket connection failed for access control dashboard:', error);
    });
    
    // Clean up on unmount
    return () => {
      websocketService.removeEventListener('access-control-events', handleAccessControlEvents);
      websocketService.removeEventListener('access-attempt', handleAccessAttempt);
      websocketService.removeEventListener('policy-change', handlePolicyChange);
      websocketService.removeEventListener('access-level-change', handleAccessLevelChange);
      websocketService.removeEventListener('user-status-change', handleUserStatusChange);
      websocketService.removeEventListener('system-status-change', handleSystemStatusChange);
    };
  }, []);

  // Fetch dashboard data
  useEffect(() => {
    fetchDashboardData();
  }, [configStatus]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch data in parallel
      const [users, doors, accessLevels, schedules, holidays] = await Promise.all([
        accessControlService.getAllUsers(),
        accessControlService.getAllDoors(),
        accessControlService.getAllAccessLevels(),
        accessControlService.getAllSchedules(),
        accessControlService.getAllHolidays()
      ]);

      // For recent events, we would typically fetch from a dedicated endpoint
      // For now, we'll create some mock data
      const mockRecentEvents = [
        {
          id: '1',
          type: 'access_granted',
          timestamp: new Date(Date.now() - 5 * 60000).toISOString(), // 5 minutes ago
          user: users[0]?.name || 'Unknown User',
          door: doors[0]?.name || 'Main Entrance',
          system: 'unifi-access'
        },
        {
          id: '2',
          type: 'access_denied',
          timestamp: new Date(Date.now() - 15 * 60000).toISOString(), // 15 minutes ago
          user: users[1]?.name || 'Jane Smith',
          door: doors[1]?.name || 'Server Room',
          system: 'lenel-s2-netbox'
        },
        {
          id: '3',
          type: 'door_held_open',
          timestamp: new Date(Date.now() - 30 * 60000).toISOString(), // 30 minutes ago
          door: doors[2]?.name || 'Back Entrance',
          system: 'unifi-access'
        },
        {
          id: '4',
          type: 'door_forced_open',
          timestamp: new Date(Date.now() - 45 * 60000).toISOString(), // 45 minutes ago
          door: doors[3]?.name || 'Storage Room',
          system: 'lenel-s2-netbox'
        },
        {
          id: '5',
          type: 'schedule_activated',
          timestamp: new Date(Date.now() - 60 * 60000).toISOString(), // 1 hour ago
          door: doors[0]?.name || 'Main Entrance',
          schedule: schedules[0]?.name || 'Business Hours',
          system: 'unifi-access'
        }
      ];

      setDashboardData({
        users,
        doors,
        accessLevels,
        schedules,
        holidays,
        recentEvents: mockRecentEvents
      });
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const getSystemCounts = (items) => {
    if (!items || !Array.isArray(items)) return { unifiAccess: 0, lenelS2NetBox: 0, total: 0 };
    
    const unifiAccess = items.filter(item => item.system === 'unifi-access').length;
    const lenelS2NetBox = items.filter(item => item.system === 'lenel-s2-netbox').length;
    
    return {
      unifiAccess,
      lenelS2NetBox,
      total: items.length
    };
  };

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Unknown';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    
    return date.toLocaleDateString();
  };

  const getEventIcon = (eventType) => {
    switch (eventType) {
      case 'access_granted':
        return <LockOpenIcon color="success" />;
      case 'access_denied':
        return <LockIcon color="error" />;
      case 'door_held_open':
        return <WarningIcon color="warning" />;
      case 'door_forced_open':
        return <ErrorIcon color="error" />;
      case 'schedule_activated':
        return <AccessTimeIcon color="info" />;
      default:
        return <InfoIcon color="primary" />;
    }
  };

  const getEventText = (event) => {
    switch (event.type) {
      case 'access_granted':
        return `${event.user} accessed ${event.door}`;
      case 'access_denied':
        return `${event.user} was denied access to ${event.door}`;
      case 'door_held_open':
        return `${event.door} was held open`;
      case 'door_forced_open':
        return `${event.door} was forced open`;
      case 'schedule_activated':
        return `Schedule "${event.schedule}" activated for ${event.door}`;
      default:
        return `Unknown event at ${event.door}`;
    }
  };
  
  // Function to convert events to CSV format
  const convertEventsToCSV = (events) => {
    if (!events || events.length === 0) return '';
    
    // Define CSV headers
    const headers = ['Event Type', 'Description', 'Timestamp', 'User', 'Door', 'System'];
    
    // Create CSV content
    let csvContent = headers.join(',') + '\n';
    
    events.forEach(event => {
      const eventType = event.type.replace(/_/g, ' ');
      const description = getEventText(event).replace(/,/g, ';'); // Replace commas with semicolons to avoid CSV issues
      const timestamp = event.timestamp ? new Date(event.timestamp).toLocaleString() : '';
      const user = event.user || '';
      const door = event.door || '';
      const system = event.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox';
      
      // Add row to CSV
      csvContent += `"${eventType}","${description}","${timestamp}","${user}","${door}","${system}"\n`;
    });
    
    return csvContent;
  };
  
  // Function to download CSV file
  const downloadCSV = (csvContent, filename = 'access-events.csv') => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Function to handle export button click
  const handleExportEvents = () => {
    if (!dashboardData.recentEvents || dashboardData.recentEvents.length === 0) {
      alert('No events to export');
      return;
    }
    
    const csvContent = convertEventsToCSV(dashboardData.recentEvents);
    const filename = `access-events-${new Date().toISOString().slice(0, 10)}.csv`;
    downloadCSV(csvContent, filename);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  const userCounts = getSystemCounts(dashboardData.users);
  const doorCounts = getSystemCounts(dashboardData.doors);
  const accessLevelCounts = getSystemCounts(dashboardData.accessLevels);
  const scheduleCounts = getSystemCounts(dashboardData.schedules);

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Access Control Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        {/* Key Metrics */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Key Metrics
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6} sm={3}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <PeopleIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div">
                        Users
                      </Typography>
                    </Box>
                    <Typography variant="h4" component="div">
                      {userCounts.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Unifi: {userCounts.unifiAccess} | Lenel: {userCounts.lenelS2NetBox}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <MeetingRoomIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div">
                        Doors
                      </Typography>
                    </Box>
                    <Typography variant="h4" component="div">
                      {doorCounts.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Unifi: {doorCounts.unifiAccess} | Lenel: {doorCounts.lenelS2NetBox}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <VpnKeyIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div">
                        Access Levels
                      </Typography>
                    </Box>
                    <Typography variant="h4" component="div">
                      {accessLevelCounts.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Unifi: {accessLevelCounts.unifiAccess} | Lenel: {accessLevelCounts.lenelS2NetBox}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <AccessTimeIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div">
                        Schedules
                      </Typography>
                    </Box>
                    <Typography variant="h4" component="div">
                      {scheduleCounts.total}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Unifi: {scheduleCounts.unifiAccess} | Lenel: {scheduleCounts.lenelS2NetBox}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Upcoming Holidays */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Upcoming Holidays
            </Typography>
            {dashboardData.holidays && dashboardData.holidays.length > 0 ? (
              <List dense>
                {dashboardData.holidays.slice(0, 5).map((holiday, index) => (
                  <React.Fragment key={holiday.id || index}>
                    <ListItem>
                      <ListItemIcon>
                        <EventIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary={holiday.name} 
                        secondary={new Date(holiday.date).toLocaleDateString()} 
                      />
                      <Chip 
                        size="small" 
                        label={holiday.systemName || holiday.system} 
                        color={holiday.system === 'unifi-access' ? 'primary' : 'secondary'}
                      />
                    </ListItem>
                    {index < Math.min(dashboardData.holidays.length, 5) - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No upcoming holidays found.
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* Recent Events */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" onClick={() => setNewEventsCount(0)} sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                Recent Events
                {newEventsCount > 0 && (
                  <Badge 
                    badgeContent={newEventsCount} 
                    color="primary" 
                    sx={{ ml: 2 }}
                    onClick={() => setNewEventsCount(0)}
                  >
                    <span></span>
                  </Badge>
                )}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {dashboardData.recentEvents && dashboardData.recentEvents.length > 0 && (
                  <Tooltip title="Export events to CSV">
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<FileDownloadIcon />}
                      onClick={handleExportEvents}
                    >
                      Export
                    </Button>
                  </Tooltip>
                )}
              </Box>
            </Box>
            {dashboardData.recentEvents && dashboardData.recentEvents.length > 0 ? (
              <List>
                {dashboardData.recentEvents.map((event, index) => (
                  <React.Fragment key={event.id || index}>
                    <ListItem>
                      <ListItemIcon>
                        {getEventIcon(event.type)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={getEventText(event)} 
                        secondary={formatTimestamp(event.timestamp)} 
                      />
                      <Chip 
                        size="small" 
                        label={event.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'} 
                        color={event.system === 'unifi-access' ? 'primary' : 'secondary'}
                      />
                    </ListItem>
                    {index < dashboardData.recentEvents.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No recent events found.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AccessControlDashboard;