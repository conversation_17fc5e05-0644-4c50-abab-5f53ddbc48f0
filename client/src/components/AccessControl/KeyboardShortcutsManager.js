import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Switch,
  FormControlLabel,
  TextField,
  Chip,
  Alert
} from '@mui/material';
import KeyboardIcon from '@mui/icons-material/Keyboard';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import RestoreIcon from '@mui/icons-material/Restore';
import InfoIcon from '@mui/icons-material/Info';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

/**
 * KeyboardShortcutsManager component
 * Provides keyboard shortcuts for common actions in the access control system
 */
const KeyboardShortcutsManager = ({ onShortcutTriggered }) => {
  // Default keyboard shortcuts
  const defaultShortcuts = [
    { id: 'refresh', name: 'Refresh Data', key: 'r', modifiers: ['ctrl'], description: 'Refresh all data', action: 'refresh' },
    { id: 'new-user', name: 'New User', key: 'u', modifiers: ['ctrl'], description: 'Create a new user', action: 'new-user' },
    { id: 'new-schedule', name: 'New Schedule', key: 's', modifiers: ['ctrl'], description: 'Create a new schedule', action: 'new-schedule' },
    { id: 'search', name: 'Search', key: 'f', modifiers: ['ctrl'], description: 'Focus on search field', action: 'search' },
    { id: 'dashboard', name: 'Dashboard', key: '1', modifiers: ['alt'], description: 'Go to dashboard tab', action: 'dashboard' },
    { id: 'users', name: 'Users', key: '2', modifiers: ['alt'], description: 'Go to users tab', action: 'users' },
    { id: 'doors', name: 'Doors', key: '3', modifiers: ['alt'], description: 'Go to doors tab', action: 'doors' },
    { id: 'access-levels', name: 'Access Levels', key: '4', modifiers: ['alt'], description: 'Go to access levels tab', action: 'access-levels' },
    { id: 'schedules', name: 'Schedules', key: '5', modifiers: ['alt'], description: 'Go to schedules tab', action: 'schedules' },
    { id: 'holidays', name: 'Holidays', key: '6', modifiers: ['alt'], description: 'Go to holidays tab', action: 'holidays' },
    { id: 'policies', name: 'Policies', key: '7', modifiers: ['alt'], description: 'Go to policies tab', action: 'policies' },
    { id: 'settings', name: 'Settings', key: '8', modifiers: ['alt'], description: 'Go to settings tab', action: 'settings' },
    { id: 'help', name: 'Help', key: 'h', modifiers: ['ctrl'], description: 'Show keyboard shortcuts help', action: 'help' }
  ];

  // State for shortcuts and dialog
  const [shortcuts, setShortcuts] = useState([]);
  const [shortcutsEnabled, setShortcutsEnabled] = useState(true);
  const [helpDialogOpen, setHelpDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentShortcut, setCurrentShortcut] = useState(null);
  const [editingKey, setEditingKey] = useState('');
  const [editingModifiers, setEditingModifiers] = useState([]);
  const [editError, setEditError] = useState('');

  // Load shortcuts from localStorage on component mount
  useEffect(() => {
    const savedShortcuts = localStorage.getItem('accessControlKeyboardShortcuts');
    const savedEnabled = localStorage.getItem('accessControlKeyboardShortcutsEnabled');
    
    if (savedShortcuts) {
      try {
        setShortcuts(JSON.parse(savedShortcuts));
      } catch (error) {
        console.error('Error parsing saved shortcuts:', error);
        setShortcuts(defaultShortcuts);
      }
    } else {
      setShortcuts(defaultShortcuts);
    }
    
    if (savedEnabled !== null) {
      setShortcutsEnabled(savedEnabled === 'true');
    }
  }, []);

  // Save shortcuts to localStorage when they change
  useEffect(() => {
    if (shortcuts.length > 0) {
      localStorage.setItem('accessControlKeyboardShortcuts', JSON.stringify(shortcuts));
    }
  }, [shortcuts]);

  // Save enabled state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('accessControlKeyboardShortcutsEnabled', shortcutsEnabled.toString());
  }, [shortcutsEnabled]);

  // Set up keyboard event listener
  useEffect(() => {
    if (!shortcutsEnabled) return;

    const handleKeyDown = (event) => {
      // Ignore key events in input fields
      if (
        event.target.tagName === 'INPUT' ||
        event.target.tagName === 'TEXTAREA' ||
        event.target.isContentEditable
      ) {
        return;
      }

      const key = event.key.toLowerCase();
      const modifiers = [];
      
      if (event.ctrlKey) modifiers.push('ctrl');
      if (event.altKey) modifiers.push('alt');
      if (event.shiftKey) modifiers.push('shift');
      if (event.metaKey) modifiers.push('meta');

      // Find matching shortcut
      const matchedShortcut = shortcuts.find(shortcut => {
        if (shortcut.key.toLowerCase() !== key) return false;
        
        // Check if all required modifiers are pressed
        const requiredModifiers = shortcut.modifiers || [];
        if (requiredModifiers.length !== modifiers.length) return false;
        
        return requiredModifiers.every(mod => modifiers.includes(mod));
      });

      if (matchedShortcut) {
        // Prevent default browser behavior for this shortcut
        event.preventDefault();
        
        // Handle special case for help shortcut
        if (matchedShortcut.action === 'help') {
          setHelpDialogOpen(true);
          return;
        }
        
        // Notify parent component
        if (onShortcutTriggered) {
          onShortcutTriggered(matchedShortcut.action, matchedShortcut);
        }
        
        console.log(`Keyboard shortcut triggered: ${matchedShortcut.name}`);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts, shortcutsEnabled, onShortcutTriggered]);

  // Format key combination for display
  const formatKeyCombination = (shortcut) => {
    const modifiers = shortcut.modifiers || [];
    const formattedModifiers = modifiers.map(mod => {
      switch (mod) {
        case 'ctrl': return 'Ctrl';
        case 'alt': return 'Alt';
        case 'shift': return 'Shift';
        case 'meta': return 'Meta';
        default: return mod;
      }
    });
    
    const key = shortcut.key.toUpperCase();
    
    return [...formattedModifiers, key].join(' + ');
  };

  // Handle shortcut edit
  const handleEditShortcut = (shortcut) => {
    setCurrentShortcut(shortcut);
    setEditingKey(shortcut.key);
    setEditingModifiers([...shortcut.modifiers]);
    setEditError('');
    setEditDialogOpen(true);
  };

  // Handle shortcut save
  const handleSaveShortcut = () => {
    // Validate shortcut
    if (!editingKey) {
      setEditError('Key is required');
      return;
    }
    
    // Check for conflicts
    const conflictingShortcut = shortcuts.find(s => 
      s.id !== currentShortcut.id && 
      s.key.toLowerCase() === editingKey.toLowerCase() && 
      JSON.stringify(s.modifiers.sort()) === JSON.stringify(editingModifiers.sort())
    );
    
    if (conflictingShortcut) {
      setEditError(`Conflicts with existing shortcut: ${conflictingShortcut.name}`);
      return;
    }
    
    // Update shortcut
    setShortcuts(shortcuts.map(s => 
      s.id === currentShortcut.id 
        ? { ...s, key: editingKey, modifiers: editingModifiers } 
        : s
    ));
    
    setEditDialogOpen(false);
  };

  // Handle modifier toggle
  const handleModifierToggle = (modifier) => {
    if (editingModifiers.includes(modifier)) {
      setEditingModifiers(editingModifiers.filter(m => m !== modifier));
    } else {
      setEditingModifiers([...editingModifiers, modifier]);
    }
  };

  // Handle key capture
  const handleKeyCapture = (event) => {
    // Ignore modifier keys when pressed alone
    if (['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
      return;
    }
    
    setEditingKey(event.key.toLowerCase());
    event.preventDefault();
  };

  // Reset shortcuts to default
  const resetToDefault = () => {
    setShortcuts(defaultShortcuts);
  };

  return (
    <>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <KeyboardIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Keyboard Shortcuts</Typography>
          </Box>
          
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={shortcutsEnabled}
                  onChange={(e) => setShortcutsEnabled(e.target.checked)}
                  color="primary"
                />
              }
              label="Enable Shortcuts"
            />
            
            <Tooltip title="Show All Shortcuts">
              <IconButton onClick={() => setHelpDialogOpen(true)}>
                <HelpOutlineIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Keyboard shortcuts provide quick access to common actions. You can customize the shortcuts to match your preferences.
        </Typography>
        
        <Divider sx={{ my: 2 }} />
        
        <Typography variant="subtitle1" gutterBottom>
          Common Shortcuts
        </Typography>
        
        <Grid container spacing={2}>
          {shortcuts.slice(0, 6).map((shortcut) => (
            <Grid item xs={12} sm={6} md={4} key={shortcut.id}>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                p: 1,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                bgcolor: 'background.paper'
              }}>
                <Box>
                  <Typography variant="body2">{shortcut.name}</Typography>
                  <Chip 
                    label={formatKeyCombination(shortcut)} 
                    size="small" 
                    color="primary" 
                    sx={{ mt: 0.5 }}
                  />
                </Box>
                
                <Tooltip title="Edit Shortcut">
                  <IconButton 
                    size="small" 
                    onClick={() => handleEditShortcut(shortcut)}
                    disabled={!shortcutsEnabled}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          ))}
        </Grid>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Button 
            variant="outlined" 
            startIcon={<RestoreIcon />}
            onClick={resetToDefault}
          >
            Reset to Default
          </Button>
          
          <Button 
            variant="outlined" 
            startIcon={<HelpOutlineIcon />}
            onClick={() => setHelpDialogOpen(true)}
          >
            View All Shortcuts
          </Button>
        </Box>
      </Paper>
      
      {/* Help Dialog */}
      <Dialog
        open={helpDialogOpen}
        onClose={() => setHelpDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <KeyboardIcon sx={{ mr: 1 }} />
            Keyboard Shortcuts
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            The following keyboard shortcuts are available in the access control system. You can customize these shortcuts by clicking the edit button.
          </Typography>
          
          <List>
            {shortcuts.map((shortcut) => (
              <ListItem
                key={shortcut.id}
                secondaryAction={
                  <Tooltip title="Edit Shortcut">
                    <IconButton 
                      edge="end" 
                      onClick={() => {
                        handleEditShortcut(shortcut);
                        setHelpDialogOpen(false);
                      }}
                      disabled={!shortcutsEnabled}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                }
              >
                <ListItemIcon>
                  <Chip 
                    label={formatKeyCombination(shortcut)} 
                    color="primary" 
                  />
                </ListItemIcon>
                <ListItemText 
                  primary={shortcut.name} 
                  secondary={shortcut.description} 
                />
              </ListItem>
            ))}
          </List>
          
          <Divider sx={{ my: 2 }} />
          
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              Tip: Press <strong>Ctrl + H</strong> at any time to open this shortcuts help dialog.
            </Typography>
          </Alert>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setHelpDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Edit Shortcut Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
      >
        <DialogTitle>Edit Shortcut</DialogTitle>
        
        <DialogContent>
          {currentShortcut && (
            <>
              <Typography variant="subtitle1" gutterBottom>
                {currentShortcut.name}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" paragraph>
                {currentShortcut.description}
              </Typography>
              
              {editError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {editError}
                </Alert>
              )}
              
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Key
                </Typography>
                
                <TextField
                  fullWidth
                  value={editingKey}
                  onKeyDown={handleKeyCapture}
                  placeholder="Press any key"
                  InputProps={{
                    readOnly: true,
                  }}
                  helperText="Click and press a key to capture"
                />
              </Box>
              
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Modifiers
                </Typography>
                
                <Grid container spacing={1}>
                  {['ctrl', 'alt', 'shift', 'meta'].map((modifier) => (
                    <Grid item key={modifier}>
                      <Chip
                        label={modifier.charAt(0).toUpperCase() + modifier.slice(1)}
                        color={editingModifiers.includes(modifier) ? 'primary' : 'default'}
                        onClick={() => handleModifierToggle(modifier)}
                        clickable
                      />
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSaveShortcut} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default KeyboardShortcutsManager;