import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  IconButton,
  Tooltip,
  Divider,
  Switch,
  FormControlLabel,
  FormGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tabs,
  Tab
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import RefreshIcon from '@mui/icons-material/Refresh';
import SettingsIcon from '@mui/icons-material/Settings';
import SecurityIcon from '@mui/icons-material/Security';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import SyncIcon from '@mui/icons-material/Sync';
import NotificationsIcon from '@mui/icons-material/Notifications';
import BackupIcon from '@mui/icons-material/Backup';
import RestoreIcon from '@mui/icons-material/Restore';
import BuildIcon from '@mui/icons-material/Build';
import StorageIcon from '@mui/icons-material/Storage';

import accessControlService from '../../services/accessControlService';

const SystemSettings = ({ configStatus }) => {
  // State for settings
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [settings, setSettings] = useState({
    unifiAccess: {},
    lenelS2NetBox: {}
  });
  const [activeTab, setActiveTab] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  // Placeholder for fetching settings - to be implemented
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      // This would be replaced with an actual API call when implemented
      // const data = await accessControlService.getSystemSettings();
      // setSettings(data);
      
      // For now, just set placeholder settings
      setSettings({
        unifiAccess: {
          autoSync: true,
          syncInterval: 30,
          notifyOnFailure: true,
          logLevel: 'info',
          retentionDays: 90
        },
        lenelS2NetBox: {
          autoSync: true,
          syncInterval: 60,
          notifyOnFailure: true,
          logLevel: 'info',
          retentionDays: 90
        }
      });
      
    } catch (err) {
      console.error('Error fetching system settings:', err);
      setError('Failed to load system settings. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Handle refresh button click
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSettings();
    setRefreshing(false);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle settings change
  const handleSettingChange = (system, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [system]: {
        ...prev[system],
        [setting]: value
      }
    }));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      // This would be replaced with an actual API call when implemented
      // await accessControlService.updateSystemSettings(settings);
      
      // For now, just simulate a successful save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess('System settings saved successfully.');
    } catch (err) {
      console.error('Error saving system settings:', err);
      setError('Failed to save system settings. Please try again later.');
    } finally {
      setSaving(false);
    }
  };

  // Check if any access control system is configured
  const isConfigured = configStatus.unifiAccess || configStatus.lenelS2NetBox;

  if (!isConfigured) {
    return (
      <Alert severity="warning">
        No access control systems are configured. System settings management requires at least one configured access control system.
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" component="h2">
          <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          System Settings
        </Typography>
        <Box>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Save Settings">
            <IconButton onClick={handleSaveSettings} disabled={saving} color="primary">
              {saving ? <CircularProgress size={24} /> : <SaveIcon />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="system settings tabs"
          >
            {configStatus.unifiAccess && (
              <Tab label="Unifi Access" icon={<SecurityIcon />} />
            )}
            {configStatus.lenelS2NetBox && (
              <Tab label="Lenel S2 NetBox" icon={<SecurityIcon />} />
            )}
            <Tab label="General" icon={<SettingsIcon />} />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* Unifi Access Settings Tab */}
          {activeTab === 0 && configStatus.unifiAccess && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Synchronization Settings" avatar={<SyncIcon />} />
                  <CardContent>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.unifiAccess.autoSync}
                            onChange={(e) => handleSettingChange('unifiAccess', 'autoSync', e.target.checked)}
                          />
                        }
                        label="Automatic Synchronization"
                      />
                      <FormControl fullWidth sx={{ mt: 2 }}>
                        <InputLabel>Sync Interval (minutes)</InputLabel>
                        <Select
                          value={settings.unifiAccess.syncInterval}
                          onChange={(e) => handleSettingChange('unifiAccess', 'syncInterval', e.target.value)}
                          label="Sync Interval (minutes)"
                          disabled={!settings.unifiAccess.autoSync}
                        >
                          <MenuItem value={15}>15 minutes</MenuItem>
                          <MenuItem value={30}>30 minutes</MenuItem>
                          <MenuItem value={60}>1 hour</MenuItem>
                          <MenuItem value={120}>2 hours</MenuItem>
                          <MenuItem value={360}>6 hours</MenuItem>
                          <MenuItem value={720}>12 hours</MenuItem>
                          <MenuItem value={1440}>24 hours</MenuItem>
                        </Select>
                      </FormControl>
                    </FormGroup>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Notification Settings" avatar={<NotificationsIcon />} />
                  <CardContent>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.unifiAccess.notifyOnFailure}
                            onChange={(e) => handleSettingChange('unifiAccess', 'notifyOnFailure', e.target.checked)}
                          />
                        }
                        label="Notify on Synchronization Failure"
                      />
                    </FormGroup>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Logging Settings" avatar={<StorageIcon />} />
                  <CardContent>
                    <FormControl fullWidth>
                      <InputLabel>Log Level</InputLabel>
                      <Select
                        value={settings.unifiAccess.logLevel}
                        onChange={(e) => handleSettingChange('unifiAccess', 'logLevel', e.target.value)}
                        label="Log Level"
                      >
                        <MenuItem value="error">Error</MenuItem>
                        <MenuItem value="warn">Warning</MenuItem>
                        <MenuItem value="info">Info</MenuItem>
                        <MenuItem value="debug">Debug</MenuItem>
                      </Select>
                    </FormControl>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <InputLabel>Log Retention (days)</InputLabel>
                      <Select
                        value={settings.unifiAccess.retentionDays}
                        onChange={(e) => handleSettingChange('unifiAccess', 'retentionDays', e.target.value)}
                        label="Log Retention (days)"
                      >
                        <MenuItem value={30}>30 days</MenuItem>
                        <MenuItem value={60}>60 days</MenuItem>
                        <MenuItem value={90}>90 days</MenuItem>
                        <MenuItem value={180}>180 days</MenuItem>
                        <MenuItem value={365}>365 days</MenuItem>
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Maintenance" avatar={<BuildIcon />} />
                  <CardContent>
                    <Button
                      variant="outlined"
                      startIcon={<SyncIcon />}
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      Force Synchronization
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<BackupIcon />}
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      Backup Configuration
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<RestoreIcon />}
                      fullWidth
                    >
                      Restore Configuration
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Lenel S2 NetBox Settings Tab */}
          {activeTab === 1 && configStatus.lenelS2NetBox && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Synchronization Settings" avatar={<SyncIcon />} />
                  <CardContent>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.lenelS2NetBox.autoSync}
                            onChange={(e) => handleSettingChange('lenelS2NetBox', 'autoSync', e.target.checked)}
                          />
                        }
                        label="Automatic Synchronization"
                      />
                      <FormControl fullWidth sx={{ mt: 2 }}>
                        <InputLabel>Sync Interval (minutes)</InputLabel>
                        <Select
                          value={settings.lenelS2NetBox.syncInterval}
                          onChange={(e) => handleSettingChange('lenelS2NetBox', 'syncInterval', e.target.value)}
                          label="Sync Interval (minutes)"
                          disabled={!settings.lenelS2NetBox.autoSync}
                        >
                          <MenuItem value={15}>15 minutes</MenuItem>
                          <MenuItem value={30}>30 minutes</MenuItem>
                          <MenuItem value={60}>1 hour</MenuItem>
                          <MenuItem value={120}>2 hours</MenuItem>
                          <MenuItem value={360}>6 hours</MenuItem>
                          <MenuItem value={720}>12 hours</MenuItem>
                          <MenuItem value={1440}>24 hours</MenuItem>
                        </Select>
                      </FormControl>
                    </FormGroup>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Notification Settings" avatar={<NotificationsIcon />} />
                  <CardContent>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={settings.lenelS2NetBox.notifyOnFailure}
                            onChange={(e) => handleSettingChange('lenelS2NetBox', 'notifyOnFailure', e.target.checked)}
                          />
                        }
                        label="Notify on Synchronization Failure"
                      />
                    </FormGroup>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Logging Settings" avatar={<StorageIcon />} />
                  <CardContent>
                    <FormControl fullWidth>
                      <InputLabel>Log Level</InputLabel>
                      <Select
                        value={settings.lenelS2NetBox.logLevel}
                        onChange={(e) => handleSettingChange('lenelS2NetBox', 'logLevel', e.target.value)}
                        label="Log Level"
                      >
                        <MenuItem value="error">Error</MenuItem>
                        <MenuItem value="warn">Warning</MenuItem>
                        <MenuItem value="info">Info</MenuItem>
                        <MenuItem value="debug">Debug</MenuItem>
                      </Select>
                    </FormControl>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <InputLabel>Log Retention (days)</InputLabel>
                      <Select
                        value={settings.lenelS2NetBox.retentionDays}
                        onChange={(e) => handleSettingChange('lenelS2NetBox', 'retentionDays', e.target.value)}
                        label="Log Retention (days)"
                      >
                        <MenuItem value={30}>30 days</MenuItem>
                        <MenuItem value={60}>60 days</MenuItem>
                        <MenuItem value={90}>90 days</MenuItem>
                        <MenuItem value={180}>180 days</MenuItem>
                        <MenuItem value={365}>365 days</MenuItem>
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined">
                  <CardHeader title="Maintenance" avatar={<BuildIcon />} />
                  <CardContent>
                    <Button
                      variant="outlined"
                      startIcon={<SyncIcon />}
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      Force Synchronization
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<BackupIcon />}
                      fullWidth
                      sx={{ mb: 2 }}
                    >
                      Backup Configuration
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<RestoreIcon />}
                      fullWidth
                    >
                      Restore Configuration
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* General Settings Tab */}
          {activeTab === (configStatus.unifiAccess && configStatus.lenelS2NetBox ? 2 : 
                         configStatus.unifiAccess || configStatus.lenelS2NetBox ? 1 : 0) && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardHeader title="Global Settings" avatar={<SettingsIcon />} />
                  <CardContent>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      These settings apply to all configured access control systems.
                    </Typography>
                    <Alert severity="info" sx={{ mb: 2 }}>
                      Global settings configuration is under development and will be available in a future update.
                    </Alert>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </Box>
      </Paper>
      
      <Box sx={{ mt: 3 }}>
        <Alert severity="info">
          System settings allow you to configure how the access control systems integrate with the portal. Adjust synchronization schedules, notification preferences, and maintenance options.
        </Alert>
      </Box>
    </Box>
  );
};

export default SystemSettings;