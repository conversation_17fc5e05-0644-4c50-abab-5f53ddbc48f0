import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import TimeZoneManager from './TimeZoneManager';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import ScheduleIcon from '@mui/icons-material/Schedule';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import TimerIcon from '@mui/icons-material/Timer';
import EventIcon from '@mui/icons-material/Event';
import SecurityIcon from '@mui/icons-material/Security';

import accessControlService from '../../services/accessControlService';

// Schedule creation steps
const steps = ['Basic Information', 'Time Windows', 'Door Selection', 'Review'];

// Days of the week
const daysOfWeek = [
  { value: 'monday', label: 'Monday' },
  { value: 'tuesday', label: 'Tuesday' },
  { value: 'wednesday', label: 'Wednesday' },
  { value: 'thursday', label: 'Thursday' },
  { value: 'friday', label: 'Friday' },
  { value: 'saturday', label: 'Saturday' },
  { value: 'sunday', label: 'Sunday' }
];

// Common time zones
const timeZones = [
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'America/Anchorage', label: 'Alaska Time (AKT)' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HT)' },
  { value: 'America/Phoenix', label: 'Arizona (MST, no DST)' },
  { value: 'Europe/London', label: 'London (GMT/BST)' },
  { value: 'Europe/Paris', label: 'Central European Time (CET)' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' }
];

const ScheduleManagement = ({ configStatus }) => {
  // State for schedule list
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [schedules, setSchedules] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSystem, setFilterSystem] = useState('all');
  
  // State for schedule creation dialog
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [scheduleFormData, setScheduleFormData] = useState({
    name: '',
    description: '',
    timeZone: 'America/New_York', // Default time zone
    handleDaylightSaving: true, // Default to handling daylight saving time
    systems: [],
    timeWindows: [
      {
        days: [],
        startTime: '09:00',
        endTime: '17:00'
      }
    ],
    doors: []
  });
  const [formErrors, setFormErrors] = useState({});
  const [creatingSchedule, setCreatingSchedule] = useState(false);
  const [createSuccess, setCreateSuccess] = useState(false);
  const [createError, setCreateError] = useState(null);
  
  // State for door selection
  const [doors, setDoors] = useState([]);
  const [doorsLoading, setDoorsLoading] = useState(false);

  // Fetch schedules on component mount
  useEffect(() => {
    fetchSchedules();
  }, []);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await accessControlService.getAllSchedules();
      setSchedules(data);
    } catch (err) {
      console.error('Error fetching schedules:', err);
      setError('Failed to load schedules. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchDoors = async () => {
    try {
      setDoorsLoading(true);
      const data = await accessControlService.getAllDoors();
      setDoors(data);
    } catch (err) {
      console.error('Error fetching doors:', err);
      // We don't set the main error state here to avoid disrupting the whole page
    } finally {
      setDoorsLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleFilterChange = (event) => {
    setFilterSystem(event.target.value);
    setPage(0);
  };

  const handleRefresh = () => {
    fetchSchedules();
  };

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setCreateDialogOpen(true);
    setActiveStep(0);
    setScheduleFormData({
      name: '',
      description: '',
      timeZone: 'America/New_York', // Default time zone
      handleDaylightSaving: true, // Default to handling daylight saving time
      systems: [],
      timeWindows: [
        {
          days: [],
          startTime: '09:00',
          endTime: '17:00'
        }
      ],
      doors: []
    });
    setFormErrors({});
    setCreateSuccess(false);
    setCreateError(null);
  };

  const handleCloseCreateDialog = () => {
    setCreateDialogOpen(false);
    // If we successfully created a schedule, refresh the list
    if (createSuccess) {
      fetchSchedules();
    }
  };

  // Step navigation
  const handleNext = () => {
    if (activeStep === 0) {
      // Validate basic information
      const errors = {};
      if (!scheduleFormData.name) errors.name = 'Schedule name is required';
      if (!scheduleFormData.timeZone) errors.timeZone = 'Time zone is required';
      if (scheduleFormData.systems.length === 0) {
        errors.systems = 'At least one system must be selected';
      }
      
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }
    } else if (activeStep === 1) {
      // Validate time windows
      const errors = {};
      if (scheduleFormData.timeWindows.length === 0) {
        errors.timeWindows = 'At least one time window is required';
      } else {
        const invalidWindows = scheduleFormData.timeWindows.filter(window => 
          window.days.length === 0 || !window.startTime || !window.endTime
        );
        
        if (invalidWindows.length > 0) {
          errors.timeWindows = 'All time windows must have days, start time, and end time';
        }
      }
      
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }
    } else if (activeStep === 2) {
      // Validate door selection
      if (scheduleFormData.doors.length === 0) {
        setFormErrors({ doors: 'At least one door must be selected' });
        return;
      }
    }
    
    // If we're moving to the door selection step, fetch doors
    if (activeStep === 1) {
      fetchDoors();
    }
    
    setFormErrors({});
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Form input handlers
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setScheduleFormData({
      ...scheduleFormData,
      [name]: value
    });
  };

  const handleSystemToggle = (system) => {
    const currentSystems = [...scheduleFormData.systems];
    const systemIndex = currentSystems.indexOf(system);
    
    if (systemIndex === -1) {
      currentSystems.push(system);
    } else {
      currentSystems.splice(systemIndex, 1);
    }
    
    setScheduleFormData({
      ...scheduleFormData,
      systems: currentSystems
    });
  };

  const handleTimeWindowChange = (index, field, value) => {
    const updatedWindows = [...scheduleFormData.timeWindows];
    updatedWindows[index] = {
      ...updatedWindows[index],
      [field]: value
    };
    
    setScheduleFormData({
      ...scheduleFormData,
      timeWindows: updatedWindows
    });
  };

  const handleAddTimeWindow = () => {
    setScheduleFormData({
      ...scheduleFormData,
      timeWindows: [
        ...scheduleFormData.timeWindows,
        {
          days: [],
          startTime: '09:00',
          endTime: '17:00'
        }
      ]
    });
  };

  const handleRemoveTimeWindow = (index) => {
    const updatedWindows = [...scheduleFormData.timeWindows];
    updatedWindows.splice(index, 1);
    
    setScheduleFormData({
      ...scheduleFormData,
      timeWindows: updatedWindows
    });
  };

  const handleDayToggle = (windowIndex, day) => {
    const updatedWindows = [...scheduleFormData.timeWindows];
    const currentDays = [...updatedWindows[windowIndex].days];
    const dayIndex = currentDays.indexOf(day);
    
    if (dayIndex === -1) {
      currentDays.push(day);
    } else {
      currentDays.splice(dayIndex, 1);
    }
    
    updatedWindows[windowIndex] = {
      ...updatedWindows[windowIndex],
      days: currentDays
    };
    
    setScheduleFormData({
      ...scheduleFormData,
      timeWindows: updatedWindows
    });
  };

  const handleDoorToggle = (doorId) => {
    const currentDoors = [...scheduleFormData.doors];
    const doorIndex = currentDoors.indexOf(doorId);
    
    if (doorIndex === -1) {
      currentDoors.push(doorId);
    } else {
      currentDoors.splice(doorIndex, 1);
    }
    
    setScheduleFormData({
      ...scheduleFormData,
      doors: currentDoors
    });
  };

  // Create schedule
  const handleCreateSchedule = async () => {
    try {
      setCreatingSchedule(true);
      setCreateError(null);
      
      const result = await accessControlService.createSchedule(scheduleFormData);
      
      setCreateSuccess(true);
      setActiveStep(steps.length); // Move to success step
    } catch (err) {
      console.error('Error creating schedule:', err);
      setCreateError(err.response?.data?.error || 'Failed to create schedule. Please try again.');
    } finally {
      setCreatingSchedule(false);
    }
  };

  // Filter and search schedules
  const filteredSchedules = schedules.filter(schedule => {
    // Filter by system
    if (filterSystem !== 'all') {
      if (!schedule.system || schedule.system !== filterSystem) {
        return false;
      }
    }
    
    // Search by name, description
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        (schedule.name && schedule.name.toLowerCase().includes(searchLower)) ||
        (schedule.description && schedule.description.toLowerCase().includes(searchLower))
      );
    }
    
    return true;
  });

  // Pagination
  const paginatedSchedules = filteredSchedules.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  // Helper functions
  const formatTimeWindow = (timeWindow, tz) => {
    if (!timeWindow) return 'N/A';
    
    const days = timeWindow.days.map(day => 
      day.charAt(0).toUpperCase() + day.slice(1).toLowerCase()
    ).join(', ');
    
    let formattedTime = `${days} ${timeWindow.startTime} - ${timeWindow.endTime}`;
    
    // Add time zone information if available
    if (tz) {
      const tzLabel = timeZones.find(timezone => timezone.value === tz)?.label || tz;
      formattedTime += ` (${tzLabel})`;
    }
    
    return formattedTime;
  };

  const getSystemChip = (system) => {
    return (
      <Chip
        icon={<SecurityIcon />}
        label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
        color={system === 'unifi-access' ? 'primary' : 'secondary'}
        size="small"
      />
    );
  };

  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0: // Basic Information
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                name="name"
                label="Schedule Name"
                value={scheduleFormData.name}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.name}
                helperText={formErrors.name}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={scheduleFormData.description}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={2}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Time Zone Settings
              </Typography>
              
              {formErrors.timeZone && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {formErrors.timeZone}
                </Alert>
              )}
              
              <TimeZoneManager 
                onTimeZoneChange={(newTimeZone) => {
                  setScheduleFormData({
                    ...scheduleFormData,
                    timeZone: newTimeZone
                  });
                }}
              />
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Selected time zone: <strong>{scheduleFormData.timeZone}</strong>
                </Typography>
                <FormControlLabel
                  control={
                    <Checkbox
                      name="handleDaylightSaving"
                      checked={scheduleFormData.handleDaylightSaving}
                      onChange={(e) => {
                        setScheduleFormData({
                          ...scheduleFormData,
                          handleDaylightSaving: e.target.checked
                        });
                      }}
                      color="primary"
                    />
                  }
                  label="Apply these time zone settings to all schedules"
                />
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Select Systems
              </Typography>
              
              {formErrors.systems && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {formErrors.systems}
                </Alert>
              )}
              
              <Grid container spacing={2}>
                {configStatus.unifiAccess && (
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={scheduleFormData.systems.includes('unifi-access')}
                          onChange={() => handleSystemToggle('unifi-access')}
                          color="primary"
                        />
                      }
                      label="Unifi Access"
                    />
                  </Grid>
                )}
                
                {configStatus.lenelS2NetBox && (
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={scheduleFormData.systems.includes('lenel-s2-netbox')}
                          onChange={() => handleSystemToggle('lenel-s2-netbox')}
                          color="primary"
                        />
                      }
                      label="Lenel S2 NetBox"
                    />
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
        );
      
      case 1: // Time Windows
        return (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Define Time Windows
            </Typography>
            
            {formErrors.timeWindows && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {formErrors.timeWindows}
              </Alert>
            )}
            
            {scheduleFormData.timeWindows.map((timeWindow, index) => (
              <Paper key={index} variant="outlined" sx={{ p: 2, mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="subtitle2">
                    Time Window #{index + 1}
                  </Typography>
                  
                  {scheduleFormData.timeWindows.length > 1 && (
                    <Button
                      size="small"
                      color="error"
                      onClick={() => handleRemoveTimeWindow(index)}
                      startIcon={<DeleteIcon />}
                    >
                      Remove
                    </Button>
                  )}
                </Box>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body2" gutterBottom>
                      Select Days:
                    </Typography>
                    <FormGroup row>
                      {daysOfWeek.map(day => (
                        <FormControlLabel
                          key={day.value}
                          control={
                            <Checkbox
                              checked={timeWindow.days.includes(day.value)}
                              onChange={() => handleDayToggle(index, day.value)}
                              size="small"
                            />
                          }
                          label={day.label}
                        />
                      ))}
                    </FormGroup>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Start Time"
                      type="time"
                      value={timeWindow.startTime}
                      onChange={(e) => handleTimeWindowChange(index, 'startTime', e.target.value)}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      inputProps={{ step: 300 }} // 5 min steps
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="End Time"
                      type="time"
                      value={timeWindow.endTime}
                      onChange={(e) => handleTimeWindowChange(index, 'endTime', e.target.value)}
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      inputProps={{ step: 300 }} // 5 min steps
                    />
                  </Grid>
                </Grid>
              </Paper>
            ))}
            
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddTimeWindow}
            >
              Add Time Window
            </Button>
          </Box>
        );
      
      case 2: // Door Selection
        return (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Select Doors
            </Typography>
            
            {formErrors.doors && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {formErrors.doors}
              </Alert>
            )}
            
            {doorsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Box>
                <Typography variant="body2" gutterBottom>
                  Selected: {scheduleFormData.doors.length} door(s)
                </Typography>
                
                <Grid container spacing={2}>
                  {doors.filter(door => 
                    scheduleFormData.systems.includes(door.system)
                  ).map(door => (
                    <Grid item xs={12} sm={6} md={4} key={door.id}>
                      <Card 
                        variant="outlined" 
                        sx={{ 
                          height: '100%',
                          border: scheduleFormData.doors.includes(door.id) ? 2 : 1,
                          borderColor: scheduleFormData.doors.includes(door.id) ? 'primary.main' : 'divider'
                        }}
                      >
                        <CardHeader
                          title={door.name}
                          subheader={door.location || 'No location'}
                          action={
                            <Checkbox
                              checked={scheduleFormData.doors.includes(door.id)}
                              onChange={() => handleDoorToggle(door.id)}
                              color="primary"
                            />
                          }
                        />
                        <CardContent>
                          {getSystemChip(door.system)}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </Box>
        );
      
      case 3: // Review
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Schedule
            </Typography>
            
            <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Basic Information
              </Typography>
              <Typography variant="body2">
                <strong>Name:</strong> {scheduleFormData.name}
              </Typography>
              {scheduleFormData.description && (
                <Typography variant="body2">
                  <strong>Description:</strong> {scheduleFormData.description}
                </Typography>
              )}
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2">
                  <strong>Systems:</strong>
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                  {scheduleFormData.systems.map(system => (
                    <Chip 
                      key={system}
                      label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
                      color={system === 'unifi-access' ? 'primary' : 'secondary'}
                      size="small"
                    />
                  ))}
                </Box>
              </Box>
            </Paper>
            
            <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Time Windows
              </Typography>
              <List dense>
                {scheduleFormData.timeWindows.map((window, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <AccessTimeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={`Window #${index + 1}`}
                      secondary={formatTimeWindow(window)}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
            
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Selected Doors ({scheduleFormData.doors.length})
              </Typography>
              <List dense>
                {doors.filter(door => 
                  scheduleFormData.doors.includes(door.id)
                ).map(door => (
                  <ListItem key={door.id}>
                    <ListItemIcon>
                      <MeetingRoomIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={door.name}
                      secondary={door.location || 'No location'}
                    />
                    {getSystemChip(door.system)}
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Box>
        );
      
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Schedule Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenCreateDialog}
        >
          Create Schedule
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TextField
            label="Search Schedules"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ mr: 2, flexGrow: 1 }}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
            }}
          />
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
            <InputLabel>System</InputLabel>
            <Select
              value={filterSystem}
              onChange={handleFilterChange}
              label="System"
            >
              <MenuItem value="all">All Systems</MenuItem>
              <MenuItem value="unifi-access">Unifi Access</MenuItem>
              <MenuItem value="lenel-s2-netbox">Lenel S2 NetBox</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Time Windows</TableCell>
                    <TableCell>System</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedSchedules.length > 0 ? (
                    paginatedSchedules.map((schedule) => (
                      <TableRow key={schedule.id}>
                        <TableCell>{schedule.name}</TableCell>
                        <TableCell>{schedule.description || 'N/A'}</TableCell>
                        <TableCell>
                          {schedule.timeWindows && schedule.timeWindows.length > 0 ? (
                            <List dense disablePadding>
                              {schedule.timeWindows.slice(0, 2).map((window, index) => (
                                <ListItem key={index} disablePadding>
                                  <ListItemText
                                    primary={formatTimeWindow(window)}
                                    primaryTypographyProps={{ variant: 'body2' }}
                                  />
                                </ListItem>
                              ))}
                              {schedule.timeWindows.length > 2 && (
                                <ListItem disablePadding>
                                  <ListItemText
                                    primary={`+${schedule.timeWindows.length - 2} more...`}
                                    primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                                  />
                                </ListItem>
                              )}
                            </List>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell>
                          {getSystemChip(schedule.system)}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Edit">
                            <IconButton size="small">
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton size="small">
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        No schedules found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredSchedules.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Schedule Creation Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseCreateDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {createSuccess ? 'Schedule Created Successfully' : 'Create New Schedule'}
        </DialogTitle>
        
        <DialogContent>
          {createSuccess ? (
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Schedule Created Successfully
              </Typography>
              <Typography variant="body1">
                The schedule "{scheduleFormData.name}" has been created in the selected systems.
              </Typography>
            </Box>
          ) : (
            <>
              {createError && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {createError}
                </Alert>
              )}
              
              <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
              
              {getStepContent(activeStep)}
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          {createSuccess ? (
            <Button onClick={handleCloseCreateDialog}>Close</Button>
          ) : (
            <>
              <Button onClick={handleCloseCreateDialog}>Cancel</Button>
              {activeStep > 0 && (
                <Button onClick={handleBack}>
                  Back
                </Button>
              )}
              {activeStep < steps.length - 1 ? (
                <Button variant="contained" onClick={handleNext}>
                  Next
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleCreateSchedule}
                  disabled={creatingSchedule}
                  startIcon={creatingSchedule ? <CircularProgress size={20} /> : null}
                >
                  {creatingSchedule ? 'Creating...' : 'Create Schedule'}
                </Button>
              )}
            </>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ScheduleManagement;