import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import EventIcon from '@mui/icons-material/Event';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SecurityIcon from '@mui/icons-material/Security';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import RepeatIcon from '@mui/icons-material/Repeat';

import accessControlService from '../../services/accessControlService';

const HolidayManagement = ({ configStatus }) => {
  // State for holiday list
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [holidays, setHolidays] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSystem, setFilterSystem] = useState('all');
  const [filterYear, setFilterYear] = useState('all');
  
  // State for holiday creation dialog
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [holidayFormData, setHolidayFormData] = useState({
    name: '',
    date: new Date(),
    description: '',
    systems: [],
    recurring: false
  });
  const [formErrors, setFormErrors] = useState({});
  const [creatingHoliday, setCreatingHoliday] = useState(false);
  const [createSuccess, setCreateSuccess] = useState(false);
  const [createError, setCreateError] = useState(null);

  // Fetch holidays on component mount
  useEffect(() => {
    fetchHolidays();
  }, []);

  const fetchHolidays = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await accessControlService.getAllHolidays();
      setHolidays(data);
    } catch (err) {
      console.error('Error fetching holidays:', err);
      setError('Failed to load holidays. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleFilterSystemChange = (event) => {
    setFilterSystem(event.target.value);
    setPage(0);
  };

  const handleFilterYearChange = (event) => {
    setFilterYear(event.target.value);
    setPage(0);
  };

  const handleRefresh = () => {
    fetchHolidays();
  };

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setCreateDialogOpen(true);
    setHolidayFormData({
      name: '',
      date: new Date(),
      description: '',
      systems: [],
      recurring: false
    });
    setFormErrors({});
    setCreateSuccess(false);
    setCreateError(null);
  };

  const handleCloseCreateDialog = () => {
    setCreateDialogOpen(false);
    // If we successfully created a holiday, refresh the list
    if (createSuccess) {
      fetchHolidays();
    }
  };

  // Form input handlers
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setHolidayFormData({
      ...holidayFormData,
      [name]: value
    });
  };

  const handleDateChange = (newDate) => {
    setHolidayFormData({
      ...holidayFormData,
      date: newDate
    });
  };

  const handleSystemToggle = (system) => {
    const currentSystems = [...holidayFormData.systems];
    const systemIndex = currentSystems.indexOf(system);
    
    if (systemIndex === -1) {
      currentSystems.push(system);
    } else {
      currentSystems.splice(systemIndex, 1);
    }
    
    setHolidayFormData({
      ...holidayFormData,
      systems: currentSystems
    });
  };

  const handleRecurringToggle = (event) => {
    setHolidayFormData({
      ...holidayFormData,
      recurring: event.target.checked
    });
  };

  // Create holiday
  const handleCreateHoliday = async () => {
    // Validate form
    const errors = {};
    if (!holidayFormData.name) errors.name = 'Holiday name is required';
    if (!holidayFormData.date) errors.date = 'Date is required';
    if (holidayFormData.systems.length === 0) {
      errors.systems = 'At least one system must be selected';
    }
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    try {
      setCreatingHoliday(true);
      setCreateError(null);
      
      // Format date for API
      const formattedDate = holidayFormData.date.toISOString().split('T')[0];
      
      const result = await accessControlService.createHoliday({
        ...holidayFormData,
        date: formattedDate
      });
      
      setCreateSuccess(true);
    } catch (err) {
      console.error('Error creating holiday:', err);
      setCreateError(err.response?.data?.error || 'Failed to create holiday. Please try again.');
    } finally {
      setCreatingHoliday(false);
    }
  };

  // Filter and search holidays
  const filteredHolidays = holidays.filter(holiday => {
    // Filter by system
    if (filterSystem !== 'all') {
      if (!holiday.system || holiday.system !== filterSystem) {
        return false;
      }
    }
    
    // Filter by year
    if (filterYear !== 'all') {
      const holidayYear = new Date(holiday.date).getFullYear().toString();
      if (holidayYear !== filterYear) {
        return false;
      }
    }
    
    // Search by name, description
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        (holiday.name && holiday.name.toLowerCase().includes(searchLower)) ||
        (holiday.description && holiday.description.toLowerCase().includes(searchLower))
      );
    }
    
    return true;
  });

  // Pagination
  const paginatedHolidays = filteredHolidays.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  // Helper functions
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSystemChip = (system) => {
    return (
      <Chip
        icon={<SecurityIcon />}
        label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
        color={system === 'unifi-access' ? 'primary' : 'secondary'}
        size="small"
      />
    );
  };

  // Get unique years from holidays for filtering
  const getYears = () => {
    const years = new Set();
    const currentYear = new Date().getFullYear();
    
    // Add current year and next year
    years.add(currentYear.toString());
    years.add((currentYear + 1).toString());
    
    // Add years from existing holidays
    holidays.forEach(holiday => {
      if (holiday.date) {
        const year = new Date(holiday.date).getFullYear().toString();
        years.add(year);
      }
    });
    
    return Array.from(years).sort();
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Holiday Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleOpenCreateDialog}
        >
          Add Holiday
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TextField
            label="Search Holidays"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ mr: 2, flexGrow: 1 }}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
            }}
          />
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
            <InputLabel>System</InputLabel>
            <Select
              value={filterSystem}
              onChange={handleFilterSystemChange}
              label="System"
            >
              <MenuItem value="all">All Systems</MenuItem>
              <MenuItem value="unifi-access">Unifi Access</MenuItem>
              <MenuItem value="lenel-s2-netbox">Lenel S2 NetBox</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 120, mr: 2 }}>
            <InputLabel>Year</InputLabel>
            <Select
              value={filterYear}
              onChange={handleFilterYearChange}
              label="Year"
            >
              <MenuItem value="all">All Years</MenuItem>
              {getYears().map(year => (
                <MenuItem key={year} value={year}>{year}</MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>System</TableCell>
                    <TableCell>Recurring</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedHolidays.length > 0 ? (
                    paginatedHolidays.map((holiday) => (
                      <TableRow key={holiday.id}>
                        <TableCell>{holiday.name}</TableCell>
                        <TableCell>{formatDate(holiday.date)}</TableCell>
                        <TableCell>{holiday.description || 'N/A'}</TableCell>
                        <TableCell>
                          {getSystemChip(holiday.system)}
                        </TableCell>
                        <TableCell>
                          {holiday.recurring ? (
                            <Chip icon={<RepeatIcon />} label="Yes" size="small" color="info" />
                          ) : (
                            <Chip label="No" size="small" />
                          )}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Edit">
                            <IconButton size="small">
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton size="small">
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        No holidays found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredHolidays.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Holiday Creation Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseCreateDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {createSuccess ? 'Holiday Created Successfully' : 'Add New Holiday'}
        </DialogTitle>
        
        <DialogContent>
          {createSuccess ? (
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Holiday Created Successfully
              </Typography>
              <Typography variant="body1">
                The holiday "{holidayFormData.name}" has been added to the selected systems.
              </Typography>
            </Box>
          ) : (
            <Box sx={{ pt: 2 }}>
              {createError && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {createError}
                </Alert>
              )}
              
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    name="name"
                    label="Holiday Name"
                    value={holidayFormData.name}
                    onChange={handleInputChange}
                    fullWidth
                    required
                    error={!!formErrors.name}
                    helperText={formErrors.name}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="Holiday Date"
                      value={holidayFormData.date}
                      onChange={handleDateChange}
                      renderInput={(params) => (
                        <TextField 
                          {...params} 
                          fullWidth 
                          required
                          error={!!formErrors.date}
                          helperText={formErrors.date}
                        />
                      )}
                    />
                  </LocalizationProvider>
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    name="description"
                    label="Description"
                    value={holidayFormData.description}
                    onChange={handleInputChange}
                    fullWidth
                    multiline
                    rows={2}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={holidayFormData.recurring}
                        onChange={handleRecurringToggle}
                        color="primary"
                      />
                    }
                    label="Recurring yearly"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Select Systems
                  </Typography>
                  
                  {formErrors.systems && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {formErrors.systems}
                    </Alert>
                  )}
                  
                  <Grid container spacing={2}>
                    {configStatus.unifiAccess && (
                      <Grid item xs={12} md={6}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={holidayFormData.systems.includes('unifi-access')}
                              onChange={() => handleSystemToggle('unifi-access')}
                              color="primary"
                            />
                          }
                          label="Unifi Access"
                        />
                      </Grid>
                    )}
                    
                    {configStatus.lenelS2NetBox && (
                      <Grid item xs={12} md={6}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={holidayFormData.systems.includes('lenel-s2-netbox')}
                              onChange={() => handleSystemToggle('lenel-s2-netbox')}
                              color="primary"
                            />
                          }
                          label="Lenel S2 NetBox"
                        />
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          {createSuccess ? (
            <Button onClick={handleCloseCreateDialog}>Close</Button>
          ) : (
            <>
              <Button onClick={handleCloseCreateDialog}>Cancel</Button>
              <Button
                variant="contained"
                onClick={handleCreateHoliday}
                disabled={creatingHoliday}
                startIcon={creatingHoliday ? <CircularProgress size={20} /> : null}
              >
                {creatingHoliday ? 'Creating...' : 'Create Holiday'}
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Calendar View */}
      <Paper sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <CalendarMonthIcon sx={{ mr: 1 }} />
          <Typography variant="h6">
            Upcoming Holidays
          </Typography>
        </Box>
        
        <Grid container spacing={2}>
          {filteredHolidays
            .filter(holiday => new Date(holiday.date) >= new Date())
            .sort((a, b) => new Date(a.date) - new Date(b.date))
            .slice(0, 6)
            .map(holiday => (
              <Grid item xs={12} sm={6} md={4} key={holiday.id}>
                <Card variant="outlined">
                  <CardHeader
                    title={holiday.name}
                    subheader={formatDate(holiday.date)}
                    avatar={<EventIcon color="primary" />}
                  />
                  <CardContent>
                    {holiday.description && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {holiday.description}
                      </Typography>
                    )}
                    <Box sx={{ mt: 1, display: 'flex', alignItems: 'center' }}>
                      {getSystemChip(holiday.system)}
                      {holiday.recurring && (
                        <Chip 
                          icon={<RepeatIcon />} 
                          label="Recurring" 
                          size="small" 
                          color="info" 
                          sx={{ ml: 1 }} 
                        />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          
          {filteredHolidays.filter(holiday => new Date(holiday.date) >= new Date()).length === 0 && (
            <Grid item xs={12}>
              <Typography variant="body1" align="center" color="text.secondary">
                No upcoming holidays found
              </Typography>
            </Grid>
          )}
        </Grid>
      </Paper>
    </Box>
  );
};

export default HolidayManagement;