import React, { useState, useEffect, useRef } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Stepper,
  Step,
  StepLabel,
  Chip,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
  Avatar,
  Badge,
  Radio,
  RadioGroup
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import BadgeIcon from '@mui/icons-material/Badge';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import FilterListIcon from '@mui/icons-material/FilterList';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

import accessControlService from '../../services/accessControlService';
import UserDetail from './UserDetail';

// User creation steps
const steps = ['Basic Information', 'System Selection', 'Access Levels', 'Card Assignment', 'Review'];

const UserManagement = ({ configStatus }) => {
  // State for user list
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSystem, setFilterSystem] = useState('all');
  
  // State for card expiration notifications
  const [expiringCards, setExpiringCards] = useState([]);
  const [showExpirationAlert, setShowExpirationAlert] = useState(false);
  
  // State for bulk access level updates
  const [bulkUpdateDialogOpen, setBulkUpdateDialogOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [bulkAccessLevels, setBulkAccessLevels] = useState({
    unifiAccess: [],
    lenelS2NetBox: []
  });
  const [bulkUpdateMode, setBulkUpdateMode] = useState('add'); // 'add', 'remove', or 'replace'
  const [bulkUpdateError, setBulkUpdateError] = useState(null);
  
  // State for user detail dialog
  const [userDetailDialogOpen, setUserDetailDialogOpen] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState(null);
  
  // Refs for file inputs
  const photoInputRef = useRef(null);
  const csvImportRef = useRef(null);
  
  // State for user creation dialog
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [userFormData, setUserFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    department: '',
    title: '',
    systems: [],
    accessLevels: {
      unifiAccess: [],
      lenelS2NetBox: []
    },
    cards: [],
    profilePhoto: null
  });
  const [photoPreview, setPhotoPreview] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [creatingUser, setCreatingUser] = useState(false);
  const [createSuccess, setCreateSuccess] = useState(false);
  const [createError, setCreateError] = useState(null);
  
  // Handle profile photo upload
  const handlePhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      if (!file.type.match('image.*')) {
        setFormErrors({
          ...formErrors,
          profilePhoto: 'Please upload an image file (jpg, png, etc.)'
        });
        return;
      }
      
      // Check file size (limit to 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setFormErrors({
          ...formErrors,
          profilePhoto: 'File size should be less than 5MB'
        });
        return;
      }
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
      
      // Update form data
      setUserFormData({
        ...userFormData,
        profilePhoto: file
      });
      
      // Clear any previous errors
      if (formErrors.profilePhoto) {
        const newErrors = { ...formErrors };
        delete newErrors.profilePhoto;
        setFormErrors(newErrors);
      }
    }
  };
  
  // State for access levels
  const [accessLevels, setAccessLevels] = useState({
    unifiAccess: [],
    lenelS2NetBox: []
  });
  const [loadingAccessLevels, setLoadingAccessLevels] = useState(false);
  
  // State for card management
  const [newCard, setNewCard] = useState({
    cardNumber: '',
    cardFormat: 'standard',
    description: '',
    expirationDate: null
  });
  
  // State for batch card enrollment
  const [batchCardDialogOpen, setBatchCardDialogOpen] = useState(false);
  const [batchCardInput, setBatchCardInput] = useState('');
  const [batchCardFormat, setBatchCardFormat] = useState('standard');
  const [batchCardPrefix, setBatchCardPrefix] = useState('');
  const [batchCardStartNumber, setBatchCardStartNumber] = useState('');
  const [batchCardCount, setBatchCardCount] = useState('');
  const [batchCardError, setBatchCardError] = useState(null);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);
  
  // Handle opening the user detail dialog
  const handleOpenUserDetail = (userId) => {
    setSelectedUserId(userId);
    setUserDetailDialogOpen(true);
  };
  
  // Handle closing the user detail dialog
  const handleCloseUserDetail = () => {
    setUserDetailDialogOpen(false);
    setSelectedUserId(null);
  };
  
  // Handle user updates from the UserDetail component
  const handleUserUpdated = (updatedUser) => {
    if (!updatedUser) {
      // User was deleted, refresh the list
      fetchUsers();
    } else {
      // User was updated, update the list
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === updatedUser.id ? updatedUser : user
        )
      );
    }
  };
  
  // Check for expiring cards
  const checkExpiringCards = (users) => {
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30);
    
    const expiring = [];
    
    users.forEach(user => {
      if (user.cards && Array.isArray(user.cards)) {
        user.cards.forEach(card => {
          if (card.expirationDate) {
            const expirationDate = new Date(card.expirationDate);
            
            // Check if card expires within the next 30 days
            if (expirationDate > today && expirationDate <= thirtyDaysFromNow) {
              expiring.push({
                userId: user.id,
                userName: user.name || `${user.firstName} ${user.lastName}`,
                cardNumber: card.cardNumber,
                expirationDate: expirationDate,
                daysUntilExpiration: Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24))
              });
            }
          }
        });
      }
    });
    
    // Sort by days until expiration (ascending)
    expiring.sort((a, b) => a.daysUntilExpiration - b.daysUntilExpiration);
    
    setExpiringCards(expiring);
    setShowExpirationAlert(expiring.length > 0);
  };

  // Fetch access levels when needed
  useEffect(() => {
    if (activeStep === 2 && createDialogOpen) {
      fetchAccessLevels();
    }
  }, [activeStep, createDialogOpen]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
    
      // Pass pagination, search, and filter parameters to the API
      const params = {
        page: page,
        limit: rowsPerPage,
        search: searchTerm,
        system: filterSystem !== 'all' ? filterSystem : undefined
      };
    
      const response = await accessControlService.getAllUsers(params);
    
      // Handle the new response format
      if (response.users && response.pagination) {
        setUsers(response.users);
        setTotalUsers(response.pagination.total);
      
        // Check for expiring cards
        checkExpiringCards(response.users);
      } else {
        // Fallback for backward compatibility
        setUsers(response);
        setTotalUsers(response.length);
      
        // Check for expiring cards
        checkExpiringCards(response);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAccessLevels = async () => {
    try {
      setLoadingAccessLevels(true);
      const data = await accessControlService.getAllAccessLevels();
      
      // Separate access levels by system
      const unifiAccessLevels = data.filter(level => level.system === 'unifi-access');
      const lenelS2NetBoxLevels = data.filter(level => level.system === 'lenel-s2-netbox');
      
      setAccessLevels({
        unifiAccess: unifiAccessLevels,
        lenelS2NetBox: lenelS2NetBoxLevels
      });
    } catch (err) {
      console.error('Error fetching access levels:', err);
      // We don't set the main error state here to avoid disrupting the whole page
    } finally {
      setLoadingAccessLevels(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    // Fetch users with new page
    fetchUsers();
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
    // Fetch users with new rowsPerPage
    fetchUsers();
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
    // Fetch users with new search term
    fetchUsers();
  };

  const handleFilterChange = (event) => {
    setFilterSystem(event.target.value);
    setPage(0);
    // Fetch users with new filter
    fetchUsers();
  };

  const handleRefresh = () => {
    fetchUsers();
  };
  
  // CSV Import/Export functions
  const handleCsvImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;
    
    // Check file type
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setError('Please upload a CSV file');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const csvData = e.target.result;
        const lines = csvData.split('\n');
        
        // Extract headers
        const headers = lines[0].split(',').map(header => header.trim());
        
        // Validate required headers
        const requiredHeaders = ['firstName', 'lastName'];
        const missingHeaders = requiredHeaders.filter(header => !headers.includes(header));
        
        if (missingHeaders.length > 0) {
          setError(`CSV is missing required headers: ${missingHeaders.join(', ')}`);
          return;
        }
        
        // Parse data rows
        const users = [];
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue; // Skip empty lines
          
          const values = lines[i].split(',').map(value => value.trim());
          
          // Create user object
          const user = {};
          headers.forEach((header, index) => {
            user[header] = values[index] || '';
          });
          
          // Validate required fields
          if (!user.firstName || !user.lastName) {
            continue; // Skip invalid users
          }
          
          users.push(user);
        }
        
        if (users.length === 0) {
          setError('No valid users found in CSV');
          return;
        }
        
        // Confirm import
        if (window.confirm(`Import ${users.length} users?`)) {
          // In a real implementation, we would call an API to batch import users
          // For now, we'll just show a success message
          alert(`Successfully imported ${users.length} users`);
          fetchUsers(); // Refresh the user list
        }
      } catch (err) {
        console.error('Error parsing CSV:', err);
        setError('Failed to parse CSV file. Please check the format.');
      }
    };
    
    reader.onerror = () => {
      setError('Failed to read CSV file');
    };
    
    reader.readAsText(file);
    
    // Reset the file input
    event.target.value = '';
  };
  
  const handleCsvExport = () => {
    try {
      // Create CSV content
      const headers = ['firstName', 'lastName', 'email', 'phone', 'department', 'title', 'systems'];
      const csvContent = [
        headers.join(','),
        ...users.map(user => {
          return [
            user.firstName || '',
            user.lastName || '',
            user.email || '',
            user.phone || '',
            user.department || '',
            user.title || '',
            (user.systems || []).join(';')
          ].join(',');
        })
      ].join('\n');
      
      // Create a blob and download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error('Error exporting CSV:', err);
      setError('Failed to export users to CSV');
    }
  };
  
  // Bulk access level update handlers
  const handleOpenBulkUpdateDialog = () => {
    // Reset the state
    setBulkUpdateDialogOpen(true);
    setSelectedUsers([]);
    setBulkAccessLevels({
      unifiAccess: [],
      lenelS2NetBox: []
    });
    setBulkUpdateMode('add');
    setBulkUpdateError(null);
    
    // Fetch access levels if not already loaded
    if (!accessLevels.unifiAccess.length && !accessLevels.lenelS2NetBox.length) {
      fetchAccessLevels();
    }
  };
  
  const handleCloseBulkUpdateDialog = () => {
    setBulkUpdateDialogOpen(false);
  };
  
  const handleSelectUser = (userId, checked) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, userId]);
    } else {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    }
  };
  
  const handleSelectAllUsers = (checked) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };
  
  const handleBulkUpdateModeChange = (event) => {
    setBulkUpdateMode(event.target.value);
  };
  
  const handleBulkAccessLevelChange = (system, levelId, checked) => {
    const currentLevels = [...bulkAccessLevels[system]];
    
    if (checked) {
      if (!currentLevels.includes(levelId)) {
        currentLevels.push(levelId);
      }
    } else {
      const levelIndex = currentLevels.indexOf(levelId);
      if (levelIndex !== -1) {
        currentLevels.splice(levelIndex, 1);
      }
    }
    
    setBulkAccessLevels({
      ...bulkAccessLevels,
      [system]: currentLevels
    });
  };
  
  const handleApplyBulkUpdate = async () => {
    try {
      setBulkUpdateError(null);
      
      // Validate
      if (selectedUsers.length === 0) {
        setBulkUpdateError('Please select at least one user');
        return;
      }
      
      if (bulkAccessLevels.unifiAccess.length === 0 && bulkAccessLevels.lenelS2NetBox.length === 0) {
        setBulkUpdateError('Please select at least one access level');
        return;
      }
      
      // Show loading state
      setLoading(true);
      
      // Call the API to update access levels
      const result = await accessControlService.bulkUpdateAccessLevels(
        selectedUsers,
        bulkAccessLevels,
        bulkUpdateMode
      );
      
      // Refresh the user list to get updated data
      await fetchUsers();
      
      // Close the dialog
      handleCloseBulkUpdateDialog();
      
      // Show success message
      alert(result.message || `Successfully updated access levels for ${selectedUsers.length} user(s)`);
    } catch (err) {
      console.error('Error applying bulk update:', err);
      setBulkUpdateError(err.response?.data?.error || 'Failed to apply bulk update. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Dialog handlers
  const handleOpenCreateDialog = () => {
    setCreateDialogOpen(true);
    setActiveStep(0);
    setUserFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      department: '',
      title: '',
      systems: [],
      accessLevels: {
        unifiAccess: [],
        lenelS2NetBox: []
      },
      cards: [],
      profilePhoto: null
    });
    setPhotoPreview(null);
    setFormErrors({});
    setCreateSuccess(false);
    setCreateError(null);
  };

  const handleCloseCreateDialog = () => {
    setCreateDialogOpen(false);
    // If we successfully created a user, refresh the list
    if (createSuccess) {
      fetchUsers();
    }
  };

  // Step navigation
  const handleNext = () => {
    if (activeStep === 0) {
      // Validate basic information
      const errors = {};
      if (!userFormData.firstName) errors.firstName = 'First name is required';
      if (!userFormData.lastName) errors.lastName = 'Last name is required';
      if (userFormData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userFormData.email)) {
        errors.email = 'Invalid email format';
      }
      
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }
    } else if (activeStep === 1) {
      // Validate system selection
      if (userFormData.systems.length === 0) {
        setFormErrors({ systems: 'At least one system must be selected' });
        return;
      }
    }
    
    setFormErrors({});
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Form input handlers
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setUserFormData({
      ...userFormData,
      [name]: value
    });
  };

  const handleSystemToggle = (system) => {
    const currentSystems = [...userFormData.systems];
    const systemIndex = currentSystems.indexOf(system);
    
    if (systemIndex === -1) {
      currentSystems.push(system);
    } else {
      currentSystems.splice(systemIndex, 1);
    }
    
    setUserFormData({
      ...userFormData,
      systems: currentSystems
    });
  };

  const handleAccessLevelChange = (system, levelId, checked) => {
    const currentLevels = [...userFormData.accessLevels[system]];
    
    if (checked) {
      if (!currentLevels.includes(levelId)) {
        currentLevels.push(levelId);
      }
    } else {
      const levelIndex = currentLevels.indexOf(levelId);
      if (levelIndex !== -1) {
        currentLevels.splice(levelIndex, 1);
      }
    }
    
    setUserFormData({
      ...userFormData,
      accessLevels: {
        ...userFormData.accessLevels,
        [system]: currentLevels
      }
    });
  };

  const handleAddCard = () => {
    if (!newCard.cardNumber) {
      setFormErrors({ cardNumber: 'Card number is required' });
      return;
    }
    
    setUserFormData({
      ...userFormData,
      cards: [...userFormData.cards, { ...newCard }]
    });
    
    setNewCard({
      cardNumber: '',
      cardFormat: 'standard',
      description: '',
      expirationDate: null
    });
    
    setFormErrors({});
  };

  const handleRemoveCard = (index) => {
    const updatedCards = [...userFormData.cards];
    updatedCards.splice(index, 1);
    
    setUserFormData({
      ...userFormData,
      cards: updatedCards
    });
  };

  const handleCardInputChange = (event) => {
    const { name, value } = event.target;
    setNewCard({
      ...newCard,
      [name]: value
    });
  };
  
  // Batch card enrollment handlers
  const handleOpenBatchCardDialog = () => {
    setBatchCardDialogOpen(true);
    setBatchCardInput('');
    setBatchCardFormat('standard');
    setBatchCardPrefix('');
    setBatchCardStartNumber('');
    setBatchCardCount('');
    setBatchCardError(null);
  };
  
  const handleCloseBatchCardDialog = () => {
    setBatchCardDialogOpen(false);
  };
  
  const handleBatchCardInputChange = (event) => {
    setBatchCardInput(event.target.value);
  };
  
  const handleBatchCardFormatChange = (event) => {
    setBatchCardFormat(event.target.value);
  };
  
  const handleBatchCardPrefixChange = (event) => {
    setBatchCardPrefix(event.target.value);
  };
  
  const handleBatchCardStartNumberChange = (event) => {
    // Only allow numbers
    const value = event.target.value.replace(/[^0-9]/g, '');
    setBatchCardStartNumber(value);
  };
  
  const handleBatchCardCountChange = (event) => {
    // Only allow numbers
    const value = event.target.value.replace(/[^0-9]/g, '');
    setBatchCardCount(value);
  };
  
  const handleAddBatchCards = () => {
    try {
      setBatchCardError(null);
      let newCards = [];
      
      // Method 1: Manual input of multiple card numbers
      if (batchCardInput.trim()) {
        // Split by newline, comma, or space
        const cardNumbers = batchCardInput
          .split(/[\n,\s]+/)
          .map(num => num.trim())
          .filter(num => num); // Remove empty strings
        
        if (cardNumbers.length === 0) {
          setBatchCardError('No valid card numbers found');
          return;
        }
        
        newCards = cardNumbers.map(cardNumber => ({
          cardNumber,
          cardFormat: batchCardFormat,
          description: 'Batch enrolled',
          expirationDate: null
        }));
      }
      // Method 2: Auto-generate sequential card numbers
      else if (batchCardPrefix !== '' || batchCardStartNumber !== '') {
        const count = parseInt(batchCardCount, 10);
        const startNumber = parseInt(batchCardStartNumber, 10) || 0;
        
        if (!count || count <= 0 || count > 1000) {
          setBatchCardError('Please enter a valid count (1-1000)');
          return;
        }
        
        for (let i = 0; i < count; i++) {
          const cardNumber = `${batchCardPrefix}${(startNumber + i).toString().padStart(batchCardStartNumber.length, '0')}`;
          newCards.push({
            cardNumber,
            cardFormat: batchCardFormat,
            description: `Auto-generated (${i + 1}/${count})`,
            expirationDate: null
          });
        }
      } else {
        setBatchCardError('Please enter either card numbers or prefix/start number');
        return;
      }
      
      // Add the new cards to the user form data
      setUserFormData({
        ...userFormData,
        cards: [...userFormData.cards, ...newCards]
      });
      
      // Close the dialog
      handleCloseBatchCardDialog();
    } catch (err) {
      console.error('Error adding batch cards:', err);
      setBatchCardError('An error occurred while adding batch cards');
    }
  };

  // Create user
  const handleCreateUser = async () => {
    try {
      setCreatingUser(true);
      setCreateError(null);
      
      const result = await accessControlService.createUser(userFormData);
      
      setCreateSuccess(true);
      setActiveStep(steps.length); // Move to success step
    } catch (err) {
      console.error('Error creating user:', err);
      setCreateError(err.response?.data?.error || 'Failed to create user. Please try again.');
    } finally {
      setCreatingUser(false);
    }
  };

  // Server-side filtering, searching, and pagination is now handled by the API

  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0: // Basic Information
        return (
          <Grid container spacing={2}>
            {/* Profile Photo Upload */}
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <Box sx={{ textAlign: 'center' }}>
                <input
                  type="file"
                  accept="image/*"
                  style={{ display: 'none' }}
                  ref={photoInputRef}
                  onChange={handlePhotoUpload}
                />
                <Badge
                  overlap="circular"
                  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  badgeContent={
                    <IconButton 
                      color="primary" 
                      sx={{ 
                        bgcolor: 'background.paper',
                        border: '1px solid',
                        borderColor: 'divider'
                      }}
                      onClick={() => photoInputRef.current.click()}
                    >
                      <PhotoCameraIcon fontSize="small" />
                    </IconButton>
                  }
                >
                  <Avatar
                    sx={{ width: 100, height: 100 }}
                    src={photoPreview}
                  >
                    {photoPreview ? null : <AccountCircleIcon sx={{ width: 80, height: 80 }} />}
                  </Avatar>
                </Badge>
                <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                  Click to upload profile photo
                </Typography>
                {formErrors.profilePhoto && (
                  <Typography variant="caption" color="error">
                    {formErrors.profilePhoto}
                  </Typography>
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                name="firstName"
                label="First Name"
                value={userFormData.firstName}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.firstName}
                helperText={formErrors.firstName}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="lastName"
                label="Last Name"
                value={userFormData.lastName}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.lastName}
                helperText={formErrors.lastName}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="email"
                label="Email"
                type="email"
                value={userFormData.email}
                onChange={handleInputChange}
                fullWidth
                error={!!formErrors.email}
                helperText={formErrors.email}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="phone"
                label="Phone"
                value={userFormData.phone}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="department"
                label="Department"
                value={userFormData.department}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="title"
                label="Job Title"
                value={userFormData.title}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
          </Grid>
        );
      
      case 1: // System Selection
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              Select which access control systems this user should be added to:
            </Typography>
            
            {formErrors.systems && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {formErrors.systems}
              </Alert>
            )}
            
            <Grid container spacing={2}>
              {configStatus.unifiAccess && (
                <Grid item xs={12} md={6}>
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      height: '100%',
                      border: userFormData.systems.includes('unifi-access') ? 2 : 1,
                      borderColor: userFormData.systems.includes('unifi-access') ? 'primary.main' : 'divider'
                    }}
                  >
                    <CardHeader
                      title="Unifi Access"
                      action={
                        <Checkbox
                          checked={userFormData.systems.includes('unifi-access')}
                          onChange={() => handleSystemToggle('unifi-access')}
                          color="primary"
                        />
                      }
                    />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Add this user to Unifi Access system. The user will be able to access doors controlled by Unifi Access.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}
              
              {configStatus.lenelS2NetBox && (
                <Grid item xs={12} md={6}>
                  <Card 
                    variant="outlined" 
                    sx={{ 
                      height: '100%',
                      border: userFormData.systems.includes('lenel-s2-netbox') ? 2 : 1,
                      borderColor: userFormData.systems.includes('lenel-s2-netbox') ? 'primary.main' : 'divider'
                    }}
                  >
                    <CardHeader
                      title="Lenel S2 NetBox"
                      action={
                        <Checkbox
                          checked={userFormData.systems.includes('lenel-s2-netbox')}
                          onChange={() => handleSystemToggle('lenel-s2-netbox')}
                          color="primary"
                        />
                      }
                    />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        Add this user to Lenel S2 NetBox system. The user will be able to access doors controlled by Lenel S2 NetBox.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          </Box>
        );
      
      case 2: // Access Levels
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              Assign access levels to the user:
            </Typography>
            
            {loadingAccessLevels ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Tabs
                value={userFormData.systems.length > 0 ? userFormData.systems[0] : false}
                onChange={(e, newValue) => {}}
                variant="scrollable"
                scrollButtons="auto"
                sx={{ mb: 2 }}
              >
                {userFormData.systems.map(system => (
                  <Tab 
                    key={system} 
                    value={system} 
                    label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'} 
                  />
                ))}
              </Tabs>
            )}
            
            {userFormData.systems.includes('unifi-access') && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Unifi Access Levels
                </Typography>
                {accessLevels.unifiAccess.length > 0 ? (
                  <Grid container spacing={1}>
                    {accessLevels.unifiAccess.map(level => (
                      <Grid item xs={12} sm={6} md={4} key={level.id}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={userFormData.accessLevels.unifiAccess.includes(level.id)}
                              onChange={(e) => handleAccessLevelChange('unifiAccess', level.id, e.target.checked)}
                            />
                          }
                          label={level.name}
                        />
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No access levels found for Unifi Access.
                  </Typography>
                )}
              </Box>
            )}
            
            {userFormData.systems.includes('lenel-s2-netbox') && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Lenel S2 NetBox Access Levels
                </Typography>
                {accessLevels.lenelS2NetBox.length > 0 ? (
                  <Grid container spacing={1}>
                    {accessLevels.lenelS2NetBox.map(level => (
                      <Grid item xs={12} sm={6} md={4} key={level.id}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={userFormData.accessLevels.lenelS2NetBox.includes(level.id)}
                              onChange={(e) => handleAccessLevelChange('lenelS2NetBox', level.id, e.target.checked)}
                            />
                          }
                          label={level.name}
                        />
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No access levels found for Lenel S2 NetBox.
                  </Typography>
                )}
              </Box>
            )}
          </Box>
        );
      
      case 3: // Card Assignment
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              Assign access cards to the user:
            </Typography>
            
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={3}>
                <TextField
                  name="cardNumber"
                  label="Card Number"
                  value={newCard.cardNumber}
                  onChange={handleCardInputChange}
                  fullWidth
                  error={!!formErrors.cardNumber}
                  helperText={formErrors.cardNumber}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Card Format</InputLabel>
                  <Select
                    name="cardFormat"
                    value={newCard.cardFormat}
                    onChange={handleCardInputChange}
                  >
                    <MenuItem value="standard">Standard</MenuItem>
                    <MenuItem value="hid">HID</MenuItem>
                    <MenuItem value="mifare">Mifare</MenuItem>
                    <MenuItem value="prox">Proximity</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  name="description"
                  label="Description"
                  value={newCard.description}
                  onChange={handleCardInputChange}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Expiration Date (Optional)"
                    value={newCard.expirationDate}
                    onChange={(date) => {
                      setNewCard({
                        ...newCard,
                        expirationDate: date
                      });
                    }}
                    renderInput={(params) => (
                      <TextField {...params} fullWidth />
                    )}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={handleAddCard}
                  >
                    Add Card
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<CloudUploadIcon />}
                    onClick={handleOpenBatchCardDialog}
                    color="secondary"
                  >
                    Batch Enrollment
                  </Button>
                </Box>
              </Grid>
            </Grid>
            
            {userFormData.cards.length > 0 ? (
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Card Number</TableCell>
                      <TableCell>Format</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Expiration</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userFormData.cards.map((card, index) => (
                      <TableRow key={index}>
                        <TableCell>{card.cardNumber}</TableCell>
                        <TableCell>{card.cardFormat}</TableCell>
                        <TableCell>{card.description}</TableCell>
                        <TableCell>
                          {card.expirationDate ? new Date(card.expirationDate).toLocaleDateString() : 'No expiration'}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton size="small" onClick={() => handleRemoveCard(index)}>
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No cards assigned yet.
              </Typography>
            )}
          </Box>
        );
      
      case 4: // Review
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review User Information
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <Avatar
                      sx={{ width: 80, height: 80, mr: 2 }}
                      src={photoPreview}
                    >
                      {photoPreview ? null : <AccountCircleIcon sx={{ width: 60, height: 60 }} />}
                    </Avatar>
                    <Box>
                      <Typography variant="subtitle1" gutterBottom>
                        Basic Information
                      </Typography>
                      <Typography variant="body2">
                        <strong>Name:</strong> {userFormData.firstName} {userFormData.lastName}
                      </Typography>
                      {userFormData.email && (
                        <Typography variant="body2">
                          <strong>Email:</strong> {userFormData.email}
                        </Typography>
                      )}
                      {userFormData.phone && (
                        <Typography variant="body2">
                          <strong>Phone:</strong> {userFormData.phone}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  {userFormData.department && (
                    <Typography variant="body2">
                      <strong>Department:</strong> {userFormData.department}
                    </Typography>
                  )}
                  {userFormData.title && (
                    <Typography variant="body2">
                      <strong>Job Title:</strong> {userFormData.title}
                    </Typography>
                  )}
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Systems
                  </Typography>
                  {userFormData.systems.map(system => (
                    <Chip 
                      key={system}
                      label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
                      color="primary"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Paper>
              </Grid>
              
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Access Levels
                  </Typography>
                  
                  {userFormData.systems.includes('unifi-access') && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight="bold">
                        Unifi Access:
                      </Typography>
                      {userFormData.accessLevels.unifiAccess.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                          {userFormData.accessLevels.unifiAccess.map(levelId => {
                            const level = accessLevels.unifiAccess.find(l => l.id === levelId);
                            return (
                              <Chip 
                                key={levelId}
                                label={level ? level.name : levelId}
                                size="small"
                              />
                            );
                          })}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No access levels assigned.
                        </Typography>
                      )}
                    </Box>
                  )}
                  
                  {userFormData.systems.includes('lenel-s2-netbox') && (
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        Lenel S2 NetBox:
                      </Typography>
                      {userFormData.accessLevels.lenelS2NetBox.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                          {userFormData.accessLevels.lenelS2NetBox.map(levelId => {
                            const level = accessLevels.lenelS2NetBox.find(l => l.id === levelId);
                            return (
                              <Chip 
                                key={levelId}
                                label={level ? level.name : levelId}
                                size="small"
                              />
                            );
                          })}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No access levels assigned.
                        </Typography>
                      )}
                    </Box>
                  )}
                </Paper>
              </Grid>
              
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Cards
                  </Typography>
                  
                  {userFormData.cards.length > 0 ? (
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Card Number</TableCell>
                            <TableCell>Format</TableCell>
                            <TableCell>Description</TableCell>
                            <TableCell>Expiration</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {userFormData.cards.map((card, index) => (
                            <TableRow key={index}>
                              <TableCell>{card.cardNumber}</TableCell>
                              <TableCell>{card.cardFormat}</TableCell>
                              <TableCell>{card.description}</TableCell>
                              <TableCell>
                                {card.expirationDate ? new Date(card.expirationDate).toLocaleDateString() : 'No expiration'}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No cards assigned.
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </Box>
        );
      
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      {/* Hidden file input for CSV import */}
      <input
        type="file"
        accept=".csv"
        style={{ display: 'none' }}
        ref={csvImportRef}
        onChange={handleCsvImport}
      />
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          User Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<FileUploadIcon />}
            onClick={() => csvImportRef.current.click()}
            sx={{ mr: 1 }}
          >
            Import CSV
          </Button>
          <Button
            variant="outlined"
            startIcon={<FileDownloadIcon />}
            onClick={handleCsvExport}
            sx={{ mr: 1 }}
          >
            Export CSV
          </Button>
          <Button
            variant="outlined"
            startIcon={<VpnKeyIcon />}
            onClick={handleOpenBulkUpdateDialog}
            sx={{ mr: 1 }}
            color="secondary"
          >
            Bulk Access Update
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<PersonAddIcon />}
            onClick={handleOpenCreateDialog}
          >
            Add New User
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {showExpirationAlert && expiringCards.length > 0 && (
        <Alert 
          severity="warning" 
          sx={{ mb: 3 }}
          action={
            <Button 
              color="inherit" 
              size="small"
              onClick={() => setShowExpirationAlert(false)}
            >
              Dismiss
            </Button>
          }
        >
          <Typography variant="subtitle1" gutterBottom>
            {expiringCards.length} card{expiringCards.length > 1 ? 's' : ''} expiring soon
          </Typography>
          <Box component="ul" sx={{ mt: 1, pl: 2 }}>
            {expiringCards.slice(0, 3).map((card, index) => (
              <Box component="li" key={index} sx={{ mb: 0.5 }}>
                <Typography variant="body2">
                  <strong>{card.userName}</strong>: Card {card.cardNumber} expires in {card.daysUntilExpiration} day{card.daysUntilExpiration > 1 ? 's' : ''} ({new Date(card.expirationDate).toLocaleDateString()})
                </Typography>
              </Box>
            ))}
            {expiringCards.length > 3 && (
              <Box component="li">
                <Typography variant="body2">
                  ...and {expiringCards.length - 3} more
                </Typography>
              </Box>
            )}
          </Box>
        </Alert>
      )}

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TextField
            label="Search Users"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ mr: 2, flexGrow: 1 }}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
            }}
          />
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
            <InputLabel>Filter System</InputLabel>
            <Select
              value={filterSystem}
              onChange={handleFilterChange}
              label="Filter System"
            >
              <MenuItem value="all">All Systems</MenuItem>
              <MenuItem value="unifi-access">Unifi Access</MenuItem>
              <MenuItem value="lenel-s2-netbox">Lenel S2 NetBox</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Department</TableCell>
                    <TableCell>Systems</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.length > 0 ? (
                    users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          {user.name || `${user.firstName || ''} ${user.lastName || ''}`}
                        </TableCell>
                        <TableCell>{user.email || 'N/A'}</TableCell>
                        <TableCell>{user.department || 'N/A'}</TableCell>
                        <TableCell>
                          {user.systems && user.systems.map(system => (
                            <Chip
                              key={system}
                              label={system === 'unifi-access' ? 'Unifi' : 'Lenel'}
                              size="small"
                              color={system === 'unifi-access' ? 'primary' : 'secondary'}
                              sx={{ mr: 0.5 }}
                            />
                          ))}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="Edit">
                            <IconButton 
                              size="small"
                              onClick={() => handleOpenUserDetail(user.id)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton size="small">
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        No users found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={totalUsers}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Bulk Access Level Update Dialog */}
      <Dialog
        open={bulkUpdateDialogOpen}
        onClose={handleCloseBulkUpdateDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Bulk Update Access Levels
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {bulkUpdateError && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {bulkUpdateError}
              </Alert>
            )}
            
            <Typography variant="body1" gutterBottom>
              Update access levels for multiple users at once.
            </Typography>
            
            {/* Step 1: Select Users */}
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Step 1: Select Users
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedUsers.length === users.length && users.length > 0}
                      indeterminate={selectedUsers.length > 0 && selectedUsers.length < users.length}
                      onChange={(e) => handleSelectAllUsers(e.target.checked)}
                    />
                  }
                  label={`Select All Users (${users.length})`}
                />
              </Box>
              
              <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 200 }}>
                <Table size="small" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox"></TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Systems</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selectedUsers.includes(user.id)}
                            onChange={(e) => handleSelectUser(user.id, e.target.checked)}
                          />
                        </TableCell>
                        <TableCell>
                          {user.name || `${user.firstName || ''} ${user.lastName || ''}`}
                        </TableCell>
                        <TableCell>{user.email || 'N/A'}</TableCell>
                        <TableCell>
                          {user.systems && user.systems.map(system => (
                            <Chip
                              key={system}
                              label={system === 'unifi-access' ? 'Unifi' : 'Lenel'}
                              size="small"
                              color={system === 'unifi-access' ? 'primary' : 'secondary'}
                              sx={{ mr: 0.5 }}
                            />
                          ))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {selectedUsers.length} user(s) selected
              </Typography>
            </Paper>
            
            {/* Step 2: Select Update Mode */}
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Step 2: Select Update Mode
              </Typography>
              
              <FormControl component="fieldset">
                <RadioGroup
                  value={bulkUpdateMode}
                  onChange={handleBulkUpdateModeChange}
                >
                  <FormControlLabel 
                    value="add" 
                    control={<Radio />} 
                    label="Add access levels to existing ones" 
                  />
                  <FormControlLabel 
                    value="remove" 
                    control={<Radio />} 
                    label="Remove access levels from existing ones" 
                  />
                  <FormControlLabel 
                    value="replace" 
                    control={<Radio />} 
                    label="Replace existing access levels with selected ones" 
                  />
                </RadioGroup>
              </FormControl>
            </Paper>
            
            {/* Step 3: Select Access Levels */}
            <Paper variant="outlined" sx={{ p: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Step 3: Select Access Levels
              </Typography>
              
              {loadingAccessLevels ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Box>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Unifi Access Levels
                  </Typography>
                  
                  {accessLevels.unifiAccess.length > 0 ? (
                    <Grid container spacing={1}>
                      {accessLevels.unifiAccess.map(level => (
                        <Grid item xs={12} sm={6} md={4} key={level.id}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={bulkAccessLevels.unifiAccess.includes(level.id)}
                                onChange={(e) => handleBulkAccessLevelChange('unifiAccess', level.id, e.target.checked)}
                              />
                            }
                            label={level.name}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No access levels found for Unifi Access.
                    </Typography>
                  )}
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <Typography variant="subtitle2" gutterBottom>
                    Lenel S2 NetBox Access Levels
                  </Typography>
                  
                  {accessLevels.lenelS2NetBox.length > 0 ? (
                    <Grid container spacing={1}>
                      {accessLevels.lenelS2NetBox.map(level => (
                        <Grid item xs={12} sm={6} md={4} key={level.id}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={bulkAccessLevels.lenelS2NetBox.includes(level.id)}
                                onChange={(e) => handleBulkAccessLevelChange('lenelS2NetBox', level.id, e.target.checked)}
                              />
                            }
                            label={level.name}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No access levels found for Lenel S2 NetBox.
                    </Typography>
                  )}
                </Box>
              )}
            </Paper>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseBulkUpdateDialog}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleApplyBulkUpdate}
            startIcon={<VpnKeyIcon />}
            disabled={selectedUsers.length === 0 || (bulkAccessLevels.unifiAccess.length === 0 && bulkAccessLevels.lenelS2NetBox.length === 0)}
          >
            Apply Updates
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Batch Card Enrollment Dialog */}
      <Dialog
        open={batchCardDialogOpen}
        onClose={handleCloseBatchCardDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Batch Card Enrollment
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {batchCardError && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {batchCardError}
              </Alert>
            )}
            
            <Typography variant="body1" gutterBottom>
              Choose a method to enroll multiple cards at once:
            </Typography>
            
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Method 1: Enter Multiple Card Numbers
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Enter one card number per line, or separate them with commas or spaces.
              </Typography>
              
              <TextField
                label="Card Numbers"
                multiline
                rows={4}
                fullWidth
                value={batchCardInput}
                onChange={handleBatchCardInputChange}
                placeholder="Example:
123456789
987654321
111222333"
                sx={{ mb: 2 }}
              />
            </Paper>
            
            <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle1" gutterBottom>
                Method 2: Auto-Generate Sequential Cards
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Generate a sequence of card numbers with a prefix and starting number.
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Prefix (Optional)"
                    fullWidth
                    value={batchCardPrefix}
                    onChange={handleBatchCardPrefixChange}
                    placeholder="ABC-"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Start Number"
                    fullWidth
                    value={batchCardStartNumber}
                    onChange={handleBatchCardStartNumberChange}
                    placeholder="1000"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    label="Number of Cards"
                    fullWidth
                    value={batchCardCount}
                    onChange={handleBatchCardCountChange}
                    placeholder="10"
                  />
                </Grid>
              </Grid>
            </Paper>
            
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Card Format</InputLabel>
              <Select
                value={batchCardFormat}
                onChange={handleBatchCardFormatChange}
                label="Card Format"
              >
                <MenuItem value="standard">Standard</MenuItem>
                <MenuItem value="hid">HID</MenuItem>
                <MenuItem value="mifare">Mifare</MenuItem>
                <MenuItem value="prox">Proximity</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleCloseBatchCardDialog}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleAddBatchCards}
            startIcon={<AddIcon />}
          >
            Add Cards
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* User Creation Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseCreateDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {createSuccess ? 'User Created Successfully' : 'Create New User'}
        </DialogTitle>
        
        <DialogContent>
          {createSuccess ? (
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <CheckCircleIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                User Created Successfully
              </Typography>
              <Typography variant="body1">
                {userFormData.firstName} {userFormData.lastName} has been added to the selected systems.
              </Typography>
            </Box>
          ) : (
            <>
              {createError && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {createError}
                </Alert>
              )}
              
              <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
              
              {getStepContent(activeStep)}
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          {createSuccess ? (
            <Button onClick={handleCloseCreateDialog}>Close</Button>
          ) : (
            <>
              <Button onClick={handleCloseCreateDialog}>Cancel</Button>
              {activeStep > 0 && (
                <Button onClick={handleBack}>
                  Back
                </Button>
              )}
              {activeStep < steps.length - 1 ? (
                <Button variant="contained" onClick={handleNext}>
                  Next
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleCreateUser}
                  disabled={creatingUser}
                  startIcon={creatingUser ? <CircularProgress size={20} /> : null}
                >
                  {creatingUser ? 'Creating...' : 'Create User'}
                </Button>
              )}
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* User Detail Dialog */}
      <Dialog
        open={userDetailDialogOpen}
        onClose={handleCloseUserDetail}
        maxWidth="md"
        fullWidth
      >
        <DialogContent sx={{ p: 0 }}>
          {selectedUserId && (
            <UserDetail
              userId={selectedUserId}
              onClose={handleCloseUserDetail}
              onUserUpdated={handleUserUpdated}
            />
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default UserManagement;