import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import BadgeIcon from '@mui/icons-material/Badge';
import SecurityIcon from '@mui/icons-material/Security';
import SettingsIcon from '@mui/icons-material/Settings';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import BlockIcon from '@mui/icons-material/Block';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import CloudSyncIcon from '@mui/icons-material/CloudSync';

import accessControlService from '../../services/accessControlService';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-tabpanel-${index}`}
      aria-labelledby={`user-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const UserDetail = ({ userId, onClose, onUserUpdated }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    department: '',
    title: ''
  });
  const [formErrors, setFormErrors] = useState({});
  
  // State for service provisioning operations
  const [provisioningOperation, setProvisioningOperation] = useState(null);
  const [operationStatus, setOperationStatus] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [confirmSystem, setConfirmSystem] = useState(null);

  // Fetch user data on component mount
  useEffect(() => {
    fetchUserData();
  }, [userId]);

  // Fetch user data from the API
  const fetchUserData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const userData = await accessControlService.getUserById(userId);
      setUser(userData);
      setFormData({
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        department: userData.department || '',
        title: userData.title || ''
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
      setError('Failed to load user data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const errors = {};
    if (!formData.firstName) errors.firstName = 'First name is required';
    if (!formData.lastName) errors.lastName = 'Last name is required';
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    setFormErrors({});
    setLoading(true);
    
    try {
      const updatedUser = await accessControlService.updateUser(userId, {
        ...formData,
        systems: user.systems
      });
      
      setUser(updatedUser);
      if (onUserUpdated) onUserUpdated(updatedUser);
      
      // Show success message
      setOperationStatus({
        success: true,
        message: 'User information updated successfully'
      });
    } catch (error) {
      console.error('Error updating user:', error);
      setOperationStatus({
        success: false,
        message: 'Failed to update user information'
      });
    } finally {
      setLoading(false);
    }
  };

  // Open confirmation dialog for service provisioning actions
  const handleConfirmAction = (action, system = null) => {
    setConfirmAction(action);
    setConfirmSystem(system);
    setConfirmDialogOpen(true);
  };

  // Close confirmation dialog
  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
    setConfirmAction(null);
    setConfirmSystem(null);
  };

  // Execute the confirmed action
  const handleExecuteAction = async () => {
    setConfirmDialogOpen(false);
    setProvisioningOperation(confirmAction);
    setOperationStatus(null);
    setLoading(true);
    
    try {
      let result;
      
      switch (confirmAction) {
        case 'create':
          // Add user to the selected system
          result = await accessControlService.updateUser(userId, {
            ...formData,
            systems: [...user.systems, confirmSystem]
          });
          setOperationStatus({
            success: true,
            message: `User successfully provisioned to ${confirmSystem === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}`
          });
          break;
          
        case 'delete':
          // Remove user from all systems
          result = await accessControlService.deleteUser(userId);
          setOperationStatus({
            success: true,
            message: 'User successfully deleted from all systems'
          });
          // Close the detail view after successful deletion
          setTimeout(() => {
            if (onClose) onClose();
            if (onUserUpdated) onUserUpdated(null);
          }, 2000);
          break;
          
        case 'disable':
          // Disable user in all systems
          result = await accessControlService.updateUserStatus(userId, { enabled: false });
          setOperationStatus({
            success: true,
            message: 'User successfully disabled in all systems'
          });
          break;
          
        case 'enable':
          // Enable user in all systems
          result = await accessControlService.updateUserStatus(userId, { enabled: true });
          setOperationStatus({
            success: true,
            message: 'User successfully enabled in all systems'
          });
          break;
          
        default:
          setOperationStatus({
            success: false,
            message: 'Unknown action'
          });
      }
      
      // Refresh user data if the operation was successful
      if (confirmAction !== 'delete') {
        await fetchUserData();
      }
      
      if (onUserUpdated && result) {
        onUserUpdated(result);
      }
    } catch (error) {
      console.error(`Error executing ${confirmAction} action:`, error);
      setOperationStatus({
        success: false,
        message: `Failed to ${confirmAction} user: ${error.message || 'Unknown error'}`
      });
    } finally {
      setLoading(false);
      setProvisioningOperation(null);
    }
  };

  // Render loading state
  if (loading && !user) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error && !user) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  // Determine if user is enabled in all systems
  const isUserEnabled = user && (
    (user.unifiAccessData && user.unifiAccessData.enabled !== false) &&
    (user.lenelS2NetBoxData && user.lenelS2NetBoxData.enabled !== false)
  );

  return (
    <Box>
      {/* User header */}
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
        <Avatar sx={{ width: 64, height: 64, mr: 2 }}>
          {user?.firstName?.charAt(0) || user?.lastName?.charAt(0) || <PersonIcon />}
        </Avatar>
        <Box>
          <Typography variant="h5">
            {user?.firstName} {user?.lastName}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {user?.email || 'No email'}
          </Typography>
          <Box sx={{ mt: 1 }}>
            {user?.systems?.map(system => (
              <Chip
                key={system}
                label={system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}
                size="small"
                color={system === 'unifi-access' ? 'primary' : 'secondary'}
                sx={{ mr: 0.5 }}
              />
            ))}
            <Chip
              label={isUserEnabled ? 'Enabled' : 'Disabled'}
              size="small"
              color={isUserEnabled ? 'success' : 'error'}
              icon={isUserEnabled ? <CheckCircleIcon /> : <CancelIcon />}
              sx={{ ml: 1 }}
            />
          </Box>
        </Box>
      </Box>
      
      <Divider />
      
      {/* Operation status message */}
      {operationStatus && (
        <Alert 
          severity={operationStatus.success ? 'success' : 'error'}
          sx={{ m: 2 }}
          onClose={() => setOperationStatus(null)}
        >
          {operationStatus.message}
        </Alert>
      )}
      
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="user detail tabs">
          <Tab label="Profile" icon={<PersonIcon />} iconPosition="start" />
          <Tab label="Credentials" icon={<BadgeIcon />} iconPosition="start" />
          <Tab label="Access Levels" icon={<SecurityIcon />} iconPosition="start" />
          <Tab label="Service Provisioning" icon={<SettingsIcon />} iconPosition="start" />
        </Tabs>
      </Box>
      
      {/* Profile Tab */}
      <TabPanel value={activeTab} index={0}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="firstName"
                label="First Name"
                value={formData.firstName}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.firstName}
                helperText={formErrors.firstName}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="lastName"
                label="Last Name"
                value={formData.lastName}
                onChange={handleInputChange}
                fullWidth
                required
                error={!!formErrors.lastName}
                helperText={formErrors.lastName}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="email"
                label="Email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="phone"
                label="Phone"
                value={formData.phone}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="department"
                label="Department"
                value={formData.department}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="title"
                label="Job Title"
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button 
                  type="submit" 
                  variant="contained" 
                  disabled={loading}
                >
                  {loading ? <CircularProgress size={24} /> : 'Save Changes'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </TabPanel>
      
      {/* Credentials Tab */}
      <TabPanel value={activeTab} index={1}>
        <Card variant="outlined">
          <CardHeader title="Access Cards" />
          <CardContent>
            {user?.cards && user.cards.length > 0 ? (
              <List>
                {user.cards.map((card, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <BadgeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={card.cardNumber}
                      secondary={`${card.cardFormat || 'Standard'} ${card.description ? `- ${card.description}` : ''}`}
                    />
                    {card.expirationDate && (
                      <Chip 
                        label={`Expires: ${new Date(card.expirationDate).toLocaleDateString()}`}
                        size="small"
                        color={new Date(card.expirationDate) < new Date() ? 'error' : 'default'}
                      />
                    )}
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" color="text.secondary">
                No cards assigned to this user.
              </Typography>
            )}
          </CardContent>
        </Card>
      </TabPanel>
      
      {/* Access Levels Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={2}>
          {/* Unifi Access Levels */}
          {user?.systems?.includes('unifi-access') && (
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardHeader title="Unifi Access Levels" />
                <CardContent>
                  {user?.unifiAccessData?.accessLevels?.length > 0 ? (
                    <List>
                      {user.unifiAccessData.accessLevels.map((level, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <SecurityIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={level.name}
                            secondary={level.description}
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No access levels assigned in Unifi Access.
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          )}
          
          {/* Lenel S2 NetBox Access Levels */}
          {user?.systems?.includes('lenel-s2-netbox') && (
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardHeader title="Lenel S2 NetBox Access Levels" />
                <CardContent>
                  {user?.lenelS2NetBoxData?.accessLevels?.length > 0 ? (
                    <List>
                      {user.lenelS2NetBoxData.accessLevels.map((level, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <SecurityIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={level.name}
                            secondary={level.description}
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No access levels assigned in Lenel S2 NetBox.
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </TabPanel>
      
      {/* Service Provisioning Tab */}
      <TabPanel value={activeTab} index={3}>
        <Typography variant="h6" gutterBottom>
          Service Provisioning
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Manage this user's accounts across all integrated systems. You can create, disable, or remove accounts with a single click.
        </Typography>
        
        <Card variant="outlined" sx={{ mb: 3 }}>
          <CardHeader title="System Status" />
          <CardContent>
            <List>
              {/* Unifi Access */}
              <ListItem>
                <ListItemIcon>
                  <CloudSyncIcon color={user?.systems?.includes('unifi-access') ? 'primary' : 'disabled'} />
                </ListItemIcon>
                <ListItemText
                  primary="Unifi Access"
                  secondary={user?.systems?.includes('unifi-access') 
                    ? `Provisioned - ${user?.unifiAccessData?.enabled === false ? 'Disabled' : 'Enabled'}`
                    : 'Not Provisioned'}
                />
                <ListItemSecondaryAction>
                  {user?.systems?.includes('unifi-access') ? (
                    <>
                      {user?.unifiAccessData?.enabled === false ? (
                        <Tooltip title="Enable Account">
                          <IconButton 
                            edge="end" 
                            color="success"
                            onClick={() => handleConfirmAction('enable')}
                            disabled={loading}
                          >
                            <PowerSettingsNewIcon />
                          </IconButton>
                        </Tooltip>
                      ) : (
                        <Tooltip title="Disable Account">
                          <IconButton 
                            edge="end" 
                            color="warning"
                            onClick={() => handleConfirmAction('disable')}
                            disabled={loading}
                          >
                            <BlockIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </>
                  ) : (
                    <Tooltip title="Create Account">
                      <IconButton 
                        edge="end" 
                        color="primary"
                        onClick={() => handleConfirmAction('create', 'unifi-access')}
                        disabled={loading}
                      >
                        <AddIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
              
              {/* Lenel S2 NetBox */}
              <ListItem>
                <ListItemIcon>
                  <CloudSyncIcon color={user?.systems?.includes('lenel-s2-netbox') ? 'secondary' : 'disabled'} />
                </ListItemIcon>
                <ListItemText
                  primary="Lenel S2 NetBox"
                  secondary={user?.systems?.includes('lenel-s2-netbox') 
                    ? `Provisioned - ${user?.lenelS2NetBoxData?.enabled === false ? 'Disabled' : 'Enabled'}`
                    : 'Not Provisioned'}
                />
                <ListItemSecondaryAction>
                  {user?.systems?.includes('lenel-s2-netbox') ? (
                    <>
                      {user?.lenelS2NetBoxData?.enabled === false ? (
                        <Tooltip title="Enable Account">
                          <IconButton 
                            edge="end" 
                            color="success"
                            onClick={() => handleConfirmAction('enable')}
                            disabled={loading}
                          >
                            <PowerSettingsNewIcon />
                          </IconButton>
                        </Tooltip>
                      ) : (
                        <Tooltip title="Disable Account">
                          <IconButton 
                            edge="end" 
                            color="warning"
                            onClick={() => handleConfirmAction('disable')}
                            disabled={loading}
                          >
                            <BlockIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </>
                  ) : (
                    <Tooltip title="Create Account">
                      <IconButton 
                        edge="end" 
                        color="secondary"
                        onClick={() => handleConfirmAction('create', 'lenel-s2-netbox')}
                        disabled={loading}
                      >
                        <AddIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            </List>
          </CardContent>
        </Card>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={() => handleConfirmAction('delete')}
            disabled={loading || (!user?.systems?.includes('unifi-access') && !user?.systems?.includes('lenel-s2-netbox'))}
          >
            Delete User From All Systems
          </Button>
          
          <Button
            variant="outlined"
            color={isUserEnabled ? 'warning' : 'success'}
            startIcon={isUserEnabled ? <BlockIcon /> : <PowerSettingsNewIcon />}
            onClick={() => handleConfirmAction(isUserEnabled ? 'disable' : 'enable')}
            disabled={loading || (!user?.systems?.includes('unifi-access') && !user?.systems?.includes('lenel-s2-netbox'))}
          >
            {isUserEnabled ? 'Disable' : 'Enable'} User In All Systems
          </Button>
        </Box>
      </TabPanel>
      
      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCloseConfirmDialog}
      >
        <DialogTitle>
          {confirmAction === 'create' && 'Create Account'}
          {confirmAction === 'delete' && 'Delete User'}
          {confirmAction === 'disable' && 'Disable User'}
          {confirmAction === 'enable' && 'Enable User'}
        </DialogTitle>
        <DialogContent>
          {confirmAction === 'create' && (
            <Typography>
              Are you sure you want to create an account for {user?.firstName} {user?.lastName} in {confirmSystem === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox'}?
            </Typography>
          )}
          {confirmAction === 'delete' && (
            <Typography>
              Are you sure you want to delete {user?.firstName} {user?.lastName} from all systems? This action cannot be undone.
            </Typography>
          )}
          {confirmAction === 'disable' && (
            <Typography>
              Are you sure you want to disable {user?.firstName} {user?.lastName} in all systems?
            </Typography>
          )}
          {confirmAction === 'enable' && (
            <Typography>
              Are you sure you want to enable {user?.firstName} {user?.lastName} in all systems?
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialog}>Cancel</Button>
          <Button 
            onClick={handleExecuteAction} 
            color={confirmAction === 'delete' ? 'error' : 'primary'}
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserDetail;