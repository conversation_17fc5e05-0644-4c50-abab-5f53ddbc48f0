import React from 'react';
import { Box, Typography } from '@mui/material';

/**
 * PageHeader component for displaying a page header with title, subtitle, and icon
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - The title of the page
 * @param {string} props.subtitle - The subtitle or description of the page
 * @param {React.ReactNode} props.icon - The icon to display in the header
 * @returns {React.ReactElement} The rendered PageHeader component
 */
const PageHeader = ({ title, subtitle, icon }) => {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
      {icon && (
        <Box sx={{ mr: 2, display: 'flex', alignItems: 'center' }}>
          {icon}
        </Box>
      )}
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          {title}
        </Typography>
        {subtitle && (
          <Typography variant="subtitle1" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default PageHeader;