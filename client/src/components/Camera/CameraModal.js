import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  IconButton,
  Chip,
  Grid,
  Paper,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
  <PERSON>lider
} from '@mui/material';
import {
  Close as CloseIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  OpenInNew as ExternalLinkIcon,
  CameraAlt as CameraIcon,
  VideoCall as VideoIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import axios from 'axios';

/**
 * CameraModal Component
 * Enhanced modal for viewing camera snapshots and live streams with controls
 * Supports UniFi Protect camera integration with thumbnails and live view
 */
const CameraModal = ({ 
  open, 
  onClose, 
  camera = null,
  initialView = 'snapshot' // 'snapshot' or 'live'
}) => {
  const [activeTab, setActiveTab] = useState(initialView);
  const [snapshotUrl, setSnapshotUrl] = useState(null);
  const [snapshotLoading, setSnapshotLoading] = useState(false);
  const [snapshotError, setSnapshotError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(5); // seconds
  const [cameraDetails, setCameraDetails] = useState(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(null);

  // Auto refresh timer
  useEffect(() => {
    let timer = null;
    if (autoRefresh && activeTab === 'snapshot' && camera) {
      timer = setInterval(() => {
        loadSnapshot();
      }, refreshInterval * 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [autoRefresh, refreshInterval, activeTab, camera]);

  // Load initial data when camera changes
  useEffect(() => {
    if (camera && open) {
      loadSnapshot();
      loadCameraDetails();
    } else {
      // Reset state when modal closes
      setSnapshotUrl(null);
      setSnapshotError(null);
      setCameraDetails(null);
    }
  }, [camera, open]);

  const loadSnapshot = async () => {
    if (!camera) return;

    try {
      setSnapshotLoading(true);
      setSnapshotError(null);

      // Build snapshot URL
      const baseUrl = camera.snapshotUrl || `/api/unifi-protect/cameras/${camera.id}/snapshot`;
      const url = camera.metadata?.instanceId 
        ? `${baseUrl}?instance=${camera.metadata.instanceId}&t=${Date.now()}`
        : `${baseUrl}?t=${Date.now()}`;

      // Fetch as blob to handle binary data
      const response = await axios.get(url, {
        responseType: 'blob',
        timeout: 10000 // 10 second timeout
      });

      const imageUrl = URL.createObjectURL(response.data);
      setSnapshotUrl(imageUrl);
      setLastRefresh(new Date());
    } catch (error) {
      console.error('Error loading camera snapshot:', error);
      setSnapshotError(error.response?.data?.message || 'Failed to load camera snapshot');
    } finally {
      setSnapshotLoading(false);
    }
  };

  const loadCameraDetails = async () => {
    if (!camera) return;

    try {
      setDetailsLoading(true);
      
      const url = camera.metadata?.instanceId 
        ? `/api/unifi-protect/cameras/${camera.id}?instance=${camera.metadata.instanceId}`
        : `/api/unifi-protect/cameras/${camera.id}`;

      const response = await axios.get(url);
      setCameraDetails(response.data);
    } catch (error) {
      console.error('Error loading camera details:', error);
    } finally {
      setDetailsLoading(false);
    }
  };

  const handleRefresh = () => {
    if (activeTab === 'snapshot') {
      loadSnapshot();
    }
  };

  const openInProtectConsole = () => {
    if (camera && camera.metadata?.instanceId) {
      // Open UniFi Protect console in new tab
      // This would need the actual protect console URL
      const protectUrl = `https://${camera.metadata.host || 'protect-console'}/protect/cameras/${camera.id}`;
      window.open(protectUrl, '_blank');
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    if (newValue === 'snapshot' && camera) {
      loadSnapshot();
    }
  };

  const renderSnapshotView = () => (
    <Box sx={{ position: 'relative', minHeight: 400 }}>
      {snapshotLoading && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: 400 
        }}>
          <CircularProgress size={60} />
        </Box>
      )}

      {snapshotError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {snapshotError}
          <Button 
            onClick={loadSnapshot} 
            size="small" 
            sx={{ ml: 2 }}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        </Alert>
      )}

      {snapshotUrl && (
        <Box sx={{ textAlign: 'center' }}>
          <img
            src={snapshotUrl}
            alt={`${camera?.name} snapshot`}
            style={{
              maxWidth: '100%',
              maxHeight: '70vh',
              borderRadius: 8,
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
            }}
            onError={() => setSnapshotError('Failed to display snapshot')}
          />
          
          {lastRefresh && (
            <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
              Last updated: {lastRefresh.toLocaleTimeString()}
            </Typography>
          )}
        </Box>
      )}

      {/* Snapshot Controls */}
      <Paper sx={{ p: 2, mt: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <FormControlLabel
              control={
                <Switch
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                />
              }
              label="Auto Refresh"
            />
          </Grid>
          
          {autoRefresh && (
            <Grid item xs>
              <Typography gutterBottom>
                Refresh Interval: {refreshInterval}s
              </Typography>
              <Slider
                value={refreshInterval}
                onChange={(e, value) => setRefreshInterval(value)}
                min={1}
                max={30}
                step={1}
                marks={[
                  { value: 1, label: '1s' },
                  { value: 5, label: '5s' },
                  { value: 10, label: '10s' },
                  { value: 30, label: '30s' }
                ]}
                sx={{ ml: 2, mr: 2 }}
              />
            </Grid>
          )}
          
          <Grid item>
            <Button
              onClick={handleRefresh}
              startIcon={<RefreshIcon />}
              disabled={snapshotLoading}
            >
              Refresh Now
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );

  const renderLiveView = () => (
    <Box sx={{ minHeight: 400 }}>
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>Live Stream Placeholder</Typography>
        <Typography>
          Live streaming would require additional implementation with WebRTC or HLS streaming.
          For now, use the snapshot view with auto-refresh for near real-time monitoring.
        </Typography>
      </Alert>
      
      <Paper sx={{ p: 4, textAlign: 'center', backgroundColor: '#f5f5f5' }}>
        <VideoIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          Live Stream Not Available
        </Typography>
        <Typography color="text.secondary">
          Switch to Snapshot tab for camera images
        </Typography>
      </Paper>
    </Box>
  );

  const renderCameraInfo = () => (
    <Paper sx={{ p: 2, mt: 2 }}>
      <Typography variant="h6" gutterBottom>Camera Information</Typography>
      
      {detailsLoading ? (
        <CircularProgress size={24} />
      ) : cameraDetails ? (
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">Status</Typography>
            <Chip 
              label={cameraDetails.state || camera?.status || 'Unknown'} 
              color={cameraDetails.state === 'CONNECTED' ? 'success' : 'error'}
              size="small"
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">Source</Typography>
            <Typography variant="body1">{camera?.source || 'Unknown'}</Typography>
          </Grid>
          
          {cameraDetails.resolution && (
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Resolution</Typography>
              <Typography variant="body1">{cameraDetails.resolution}</Typography>
            </Grid>
          )}
          
          {cameraDetails.location && (
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">Location</Typography>
              <Typography variant="body1">{cameraDetails.location}</Typography>
            </Grid>
          )}
          
          {camera?.metadata?.instanceId && (
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary">Instance ID</Typography>
              <Typography variant="body1">{camera.metadata.instanceId}</Typography>
            </Grid>
          )}
        </Grid>
      ) : (
        <Typography color="text.secondary">Camera details not available</Typography>
      )}
    </Paper>
  );

  if (!camera) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          minHeight: '70vh',
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CameraIcon />
            <Box>
              <Typography variant="h6">{camera.name}</Typography>
              <Typography variant="body2" color="text.secondary">
                {camera.location || 'Location not specified'}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={openInProtectConsole} title="Open in UniFi Protect Console">
              <ExternalLinkIcon />
            </IconButton>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
          <Tab 
            value="snapshot" 
            label="Snapshot" 
            icon={<CameraIcon />}
            iconPosition="start"
          />
          <Tab 
            value="live" 
            label="Live Stream" 
            icon={<VideoIcon />}
            iconPosition="start"
          />
          <Tab 
            value="info" 
            label="Details" 
            icon={<SettingsIcon />}
            iconPosition="start"
          />
        </Tabs>

        {activeTab === 'snapshot' && renderSnapshotView()}
        {activeTab === 'live' && renderLiveView()}
        {activeTab === 'info' && renderCameraInfo()}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
        {activeTab === 'snapshot' && (
          <Button
            onClick={handleRefresh}
            variant="contained"
            startIcon={<RefreshIcon />}
            disabled={snapshotLoading}
          >
            Refresh Snapshot
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CameraModal;