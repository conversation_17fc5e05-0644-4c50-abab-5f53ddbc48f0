import React from 'react';
import { 
  Box, 
  Typography, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Button, 
  IconButton, 
  Grid,
  TextField,
  List,
  ListItem,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon
} from '@mui/icons-material';

/**
 * AssignmentRules Component
 * Allows configuring rules for automatically assigning tasks based on form responses
 * 
 * @param {Object} props Component props
 * @param {Array} props.assignmentRules Current assignment rules
 * @param {Array} props.formFields Available form fields
 * @param {Array} props.users Available users for assignment
 * @param {Function} props.onChange Callback when assignment rules change
 */
const AssignmentRules = ({ assignmentRules = [], formFields = [], users = [], onChange }) => {
  // Add a new assignment rule
  const handleAddRule = () => {
    const newRules = [...assignmentRules];
    newRules.push({
      field: '',
      value: '',
      assignTo: '',
      priority: newRules.length // Default priority is the current length (lowest priority)
    });
    
    onChange(newRules);
  };

  // Update an assignment rule
  const handleRuleChange = (index, field, value) => {
    const newRules = [...assignmentRules];
    newRules[index] = {
      ...newRules[index],
      [field]: value
    };
    
    onChange(newRules);
  };

  // Remove an assignment rule
  const handleRemoveRule = (index) => {
    const newRules = [...assignmentRules];
    newRules.splice(index, 1);
    
    // Update priorities to ensure they remain sequential
    newRules.forEach((rule, i) => {
      rule.priority = i;
    });
    
    onChange(newRules);
  };

  // Move a rule up (higher priority)
  const handleMoveUp = (index) => {
    if (index === 0) return; // Already at the top
    
    const newRules = [...assignmentRules];
    const temp = newRules[index];
    newRules[index] = newRules[index - 1];
    newRules[index - 1] = temp;
    
    // Update priorities
    newRules.forEach((rule, i) => {
      rule.priority = i;
    });
    
    onChange(newRules);
  };

  // Move a rule down (lower priority)
  const handleMoveDown = (index) => {
    if (index === assignmentRules.length - 1) return; // Already at the bottom
    
    const newRules = [...assignmentRules];
    const temp = newRules[index];
    newRules[index] = newRules[index + 1];
    newRules[index + 1] = temp;
    
    // Update priorities
    newRules.forEach((rule, i) => {
      rule.priority = i;
    });
    
    onChange(newRules);
  };

  return (
    <Box>
      <Typography variant="body1" paragraph>
        Configure rules for automatically assigning tasks based on form responses. 
        Rules are evaluated in order, and the first matching rule is used.
      </Typography>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="subtitle1">
          Assignment Rules
        </Typography>
        <Button
          variant="outlined"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleAddRule}
        >
          Add Rule
        </Button>
      </Box>
      
      {assignmentRules.length === 0 ? (
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2, mb: 2 }}>
          No assignment rules. Add a rule to automatically assign tasks based on form responses.
        </Typography>
      ) : (
        <List>
          {assignmentRules.map((rule, index) => (
            <ListItem 
              key={index}
              sx={{ 
                bgcolor: 'background.paper',
                borderRadius: 1,
                mb: 1,
                border: '1px solid',
                borderColor: 'divider',
                p: 2
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Form Field</InputLabel>
                    <Select
                      value={rule.field || ''}
                      onChange={(e) => handleRuleChange(index, 'field', e.target.value)}
                      label="Form Field"
                    >
                      <MenuItem value="">
                        <em>Select a field</em>
                      </MenuItem>
                      {formFields.map(field => (
                        <MenuItem key={field.id} value={field.id}>
                          {field.title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Value"
                    value={rule.value || ''}
                    onChange={(e) => handleRuleChange(index, 'value', e.target.value)}
                    placeholder="Value to match"
                  />
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Assign To</InputLabel>
                    <Select
                      value={rule.assignTo || ''}
                      onChange={(e) => handleRuleChange(index, 'assignTo', e.target.value)}
                      label="Assign To"
                    >
                      <MenuItem value="">
                        <em>Select a user</em>
                      </MenuItem>
                      {users.map(user => (
                        <MenuItem key={user._id} value={user._id}>
                          {user.name} ({user.email})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} md={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  <Tooltip title="Move Up (Higher Priority)">
                    <span>
                      <IconButton
                        size="small"
                        onClick={() => handleMoveUp(index)}
                        disabled={index === 0}
                      >
                        <ArrowUpIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                  
                  <Tooltip title="Move Down (Lower Priority)">
                    <span>
                      <IconButton
                        size="small"
                        onClick={() => handleMoveDown(index)}
                        disabled={index === assignmentRules.length - 1}
                      >
                        <ArrowDownIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                  
                  <Tooltip title="Delete Rule">
                    <IconButton
                      size="small"
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleRemoveRule(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Grid>
              </Grid>
            </ListItem>
          ))}
        </List>
      )}
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Rules are evaluated in order from top to bottom. The first matching rule is used.
          If no rules match, the default assignee will be used (if specified).
        </Typography>
      </Box>
    </Box>
  );
};

export default AssignmentRules;