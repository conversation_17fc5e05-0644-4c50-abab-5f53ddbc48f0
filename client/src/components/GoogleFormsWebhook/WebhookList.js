import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemSecondaryAction, 
  IconButton, 
  Button, 
  Chip, 
  Tooltip, 
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon, 
  PlayArrow as ProcessIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon
} from '@mui/icons-material';
import googleFormsWebhookService from '../../services/googleFormsWebhookService';
import { useNavigate } from 'react-router-dom';

/**
 * WebhookList Component
 * Displays a list of webhooks for a specific form or all webhooks
 * 
 * @param {Object} props Component props
 * @param {string} [props.formId] Optional form ID to filter webhooks
 * @param {string} [props.formName] Optional form name for display
 * @param {Function} [props.onCreateWebhook] Optional callback when create button is clicked
 * @param {Function} [props.onEditWebhook] Optional callback when edit button is clicked
 * @param {boolean} [props.embedded] Whether this component is embedded in another page
 */
const WebhookList = ({ 
  formId, 
  formName, 
  onCreateWebhook, 
  onEditWebhook, 
  embedded = false 
}) => {
  const navigate = useNavigate();
  const [webhooks, setWebhooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [processingWebhook, setProcessingWebhook] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [webhookToDelete, setWebhookToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch webhooks on component mount or when formId changes
  useEffect(() => {
    const fetchWebhooks = async () => {
      try {
        setLoading(true);
        setError(null);
        
        let webhookData;
        if (formId) {
          // Fetch webhooks for a specific form
          webhookData = await googleFormsWebhookService.getWebhooksForForm(formId);
        } else {
          // Fetch all webhooks
          webhookData = await googleFormsWebhookService.getAllWebhooks();
        }
        
        setWebhooks(webhookData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching webhooks:', err);
        setError('Failed to load webhooks. Please try again.');
        setLoading(false);
      }
    };

    fetchWebhooks();
  }, [formId, refreshTrigger]);

  // Handle create webhook button click
  const handleCreateWebhook = () => {
    if (onCreateWebhook) {
      onCreateWebhook(formId, formName);
    } else {
      // Navigate to create webhook page
      navigate('/google-forms/webhooks/create', { 
        state: { formId, formName } 
      });
    }
  };

  // Handle edit webhook button click
  const handleEditWebhook = (webhook) => {
    if (onEditWebhook) {
      onEditWebhook(webhook);
    } else {
      // Navigate to edit webhook page
      navigate(`/google-forms/webhooks/${webhook._id}`, { 
        state: { webhook } 
      });
    }
  };

  // Handle delete webhook button click
  const handleDeleteClick = (webhook) => {
    setWebhookToDelete(webhook);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!webhookToDelete) return;
    
    try {
      setDeleteLoading(true);
      await googleFormsWebhookService.deleteWebhook(webhookToDelete._id);
      setDeleteDialogOpen(false);
      setWebhookToDelete(null);
      setDeleteLoading(false);
      
      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
    } catch (err) {
      console.error('Error deleting webhook:', err);
      setError('Failed to delete webhook. Please try again.');
      setDeleteLoading(false);
    }
  };

  // Handle process webhook button click
  const handleProcessWebhook = async (webhookId) => {
    try {
      setProcessingWebhook(webhookId);
      await googleFormsWebhookService.processWebhook(webhookId);
      
      // Refresh the list
      setRefreshTrigger(prev => prev + 1);
      setProcessingWebhook(null);
    } catch (err) {
      console.error('Error processing webhook:', err);
      setError('Failed to process webhook. Please try again.');
      setProcessingWebhook(null);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant={embedded ? 'h6' : 'h5'}>
          {formId ? `Webhooks for ${formName || 'Selected Form'}` : 'All Webhooks'}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateWebhook}
          disabled={formId ? false : true}
        >
          Create Webhook
        </Button>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Loading indicator */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* No webhooks message */}
          {webhooks.length === 0 ? (
            <Box sx={{ textAlign: 'center', my: 4 }}>
              <Typography variant="body1" color="text.secondary">
                {formId 
                  ? 'No webhooks found for this form. Create a webhook to automatically generate tasks from form responses.'
                  : 'No webhooks found. Select a form to create webhooks.'}
              </Typography>
            </Box>
          ) : (
            /* Webhooks list */
            <List>
              {webhooks.map((webhook) => (
                <ListItem 
                  key={webhook._id} 
                  divider
                  sx={{ 
                    bgcolor: webhook.active ? 'background.paper' : 'action.hover',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {webhook.formName}
                        <Chip 
                          icon={webhook.active ? <ActiveIcon /> : <InactiveIcon />}
                          label={webhook.active ? 'Active' : 'Inactive'}
                          color={webhook.active ? 'success' : 'default'}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography variant="body2" component="span">
                          Task Type: {webhook.taskType === 'maintenance' ? 'Maintenance Task' : 'General Task'}
                        </Typography>
                        <br />
                        <Typography variant="body2" component="span">
                          Last Checked: {formatDate(webhook.lastChecked)}
                        </Typography>
                        <br />
                        <Typography variant="body2" component="span">
                          Processed Responses: {webhook.processedResponses ? webhook.processedResponses.length : 0}
                        </Typography>
                      </>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Process Webhook">
                      <IconButton 
                        edge="end" 
                        aria-label="process" 
                        onClick={() => handleProcessWebhook(webhook._id)}
                        disabled={processingWebhook === webhook._id}
                      >
                        {processingWebhook === webhook._id ? (
                          <CircularProgress size={24} />
                        ) : (
                          <ProcessIcon />
                        )}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Webhook">
                      <IconButton 
                        edge="end" 
                        aria-label="edit" 
                        onClick={() => handleEditWebhook(webhook)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Webhook">
                      <IconButton 
                        edge="end" 
                        aria-label="delete" 
                        onClick={() => handleDeleteClick(webhook)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </>
      )}

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Webhook</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this webhook for "{webhookToDelete?.formName}"? 
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDeleteDialogOpen(false)} 
            disabled={deleteLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={24} /> : null}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WebhookList;