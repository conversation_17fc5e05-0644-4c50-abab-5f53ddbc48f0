import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Button, 
  FormControlLabel, 
  Switch, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  CircularProgress,
  Alert,
  Divider,
  Paper,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { 
  Save as SaveIcon, 
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import googleFormsService from '../../services/googleFormsService';
import googleFormsWebhookService from '../../services/googleFormsWebhookService';
import FieldMapping from './FieldMapping';
import AssignmentRules from './AssignmentRules';

/**
 * WebhookForm Component
 * Form for creating or editing a webhook
 * 
 * @param {Object} props Component props
 * @param {string} [props.formId] Form ID for creating a new webhook
 * @param {string} [props.formName] Form name for creating a new webhook
 * @param {Object} [props.webhook] Existing webhook for editing
 * @param {Function} [props.onSave] Callback when webhook is saved
 * @param {Function} [props.onCancel] Callback when form is cancelled
 */
const WebhookForm = ({ 
  formId, 
  formName, 
  webhook, 
  onSave, 
  onCancel 
}) => {
  // Form state
  const [formData, setFormData] = useState({
    formId: '',
    formName: '',
    active: true,
    taskType: 'general',
    fieldMappings: {
      titleField: '',
      descriptionField: '',
      priorityField: '',
      dueDateField: '',
      tagsField: '',
      customFields: []
    },
    assignmentRules: [],
    defaultAssignee: ''
  });

  // Form fields from the selected Google Form
  const [formFields, setFormFields] = useState([]);
  
  // Loading and error states
  const [loading, setLoading] = useState(false);
  const [loadingForm, setLoadingForm] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // Users for assignment
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Initialize form data from props
  useEffect(() => {
    if (webhook) {
      // Editing existing webhook
      setFormData({
        formId: webhook.formId,
        formName: webhook.formName,
        active: webhook.active,
        taskType: webhook.taskType || 'general',
        fieldMappings: webhook.fieldMappings || {
          titleField: '',
          descriptionField: '',
          priorityField: '',
          dueDateField: '',
          tagsField: '',
          customFields: []
        },
        assignmentRules: webhook.assignmentRules || [],
        defaultAssignee: webhook.defaultAssignee || ''
      });
    } else if (formId && formName) {
      // Creating new webhook for a specific form
      setFormData(prev => ({
        ...prev,
        formId,
        formName
      }));
    }
  }, [webhook, formId, formName]);

  // Fetch form fields when form ID changes
  useEffect(() => {
    const fetchFormFields = async () => {
      if (!formData.formId) return;
      
      try {
        setLoadingForm(true);
        setError(null);
        
        // Get form details
        const formDetails = await googleFormsService.getForm(formData.formId);
        
        // Extract fields from form
        const fields = [];
        if (formDetails && formDetails.items) {
          formDetails.items.forEach(item => {
            if (item.questionItem && item.questionItem.question) {
              fields.push({
                id: item.questionId,
                title: item.title,
                type: item.questionItem.question.questionType
              });
            }
          });
        }
        
        setFormFields(fields);
        setLoadingForm(false);
      } catch (err) {
        console.error('Error fetching form fields:', err);
        setError('Failed to load form fields. Please try again.');
        setLoadingForm(false);
      }
    };

    fetchFormFields();
  }, [formData.formId]);

  // Fetch users for assignment
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoadingUsers(true);
        
        // This would be replaced with your actual API call to get users
        // For now, we'll use a placeholder
        const response = await fetch('/api/users');
        const userData = await response.json();
        
        setUsers(userData);
        setLoadingUsers(false);
      } catch (err) {
        console.error('Error fetching users:', err);
        setLoadingUsers(false);
      }
    };

    fetchUsers();
  }, []);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    
    if (name === 'active') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle field mappings changes
  const handleFieldMappingsChange = (fieldMappings) => {
    setFormData(prev => ({
      ...prev,
      fieldMappings
    }));
  };

  // Handle assignment rules changes
  const handleAssignmentRulesChange = (assignmentRules) => {
    setFormData(prev => ({
      ...prev,
      assignmentRules
    }));
  };

  // Handle default assignee change
  const handleDefaultAssigneeChange = (e) => {
    setFormData(prev => ({
      ...prev,
      defaultAssignee: e.target.value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      
      // Validate required fields
      if (!formData.formId || !formData.formName || !formData.fieldMappings.titleField) {
        setError('Form ID, form name, and title field mapping are required');
        setLoading(false);
        return;
      }
      
      let result;
      if (webhook) {
        // Update existing webhook
        result = await googleFormsWebhookService.updateWebhook(webhook._id, formData);
      } else {
        // Create new webhook
        result = await googleFormsWebhookService.createWebhook(formData);
      }
      
      setSuccess(webhook ? 'Webhook updated successfully' : 'Webhook created successfully');
      setLoading(false);
      
      // Call onSave callback if provided
      if (onSave) {
        onSave(result);
      }
    } catch (err) {
      console.error('Error saving webhook:', err);
      setError(err.response?.data?.message || 'Failed to save webhook. Please try again.');
      setLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        {/* Header */}
        <Typography variant="h5" gutterBottom>
          {webhook ? 'Edit Webhook' : 'Create Webhook'}
        </Typography>
        
        {/* Error and success messages */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}
        
        {/* Basic settings */}
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Form ID"
              name="formId"
              value={formData.formId}
              onChange={handleChange}
              margin="normal"
              required
              disabled={!!webhook || !!formId}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Form Name"
              name="formName"
              value={formData.formName}
              onChange={handleChange}
              margin="normal"
              required
              disabled={!!webhook || !!formName}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Task Type</InputLabel>
              <Select
                name="taskType"
                value={formData.taskType}
                onChange={handleChange}
                label="Task Type"
              >
                <MenuItem value="general">General Task</MenuItem>
                <MenuItem value="maintenance">Maintenance Task</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.active}
                  onChange={handleChange}
                  name="active"
                  color="primary"
                />
              }
              label="Active"
              sx={{ mt: 2 }}
            />
          </Grid>
        </Grid>
      </Paper>
      
      {/* Field Mappings */}
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Field Mappings
        </Typography>
        
        {loadingForm ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <FieldMapping
            fieldMappings={formData.fieldMappings}
            formFields={formFields}
            onChange={handleFieldMappingsChange}
          />
        )}
      </Paper>
      
      {/* Assignment Rules */}
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Assignment Rules
        </Typography>
        
        {loadingUsers ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <AssignmentRules
              assignmentRules={formData.assignmentRules}
              formFields={formFields}
              users={users}
              onChange={handleAssignmentRulesChange}
            />
            
            <Box sx={{ mt: 3 }}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Default Assignee</InputLabel>
                <Select
                  value={formData.defaultAssignee}
                  onChange={handleDefaultAssigneeChange}
                  label="Default Assignee"
                >
                  <MenuItem value="">
                    <em>None (Leave Unassigned)</em>
                  </MenuItem>
                  {users.map(user => (
                    <MenuItem key={user._id} value={user._id}>
                      {user.name} ({user.email})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </>
        )}
      </Paper>
      
      {/* Form actions */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        {onCancel && (
          <Button
            variant="outlined"
            onClick={onCancel}
            sx={{ mr: 2 }}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
        
        <Button
          type="submit"
          variant="contained"
          color="primary"
          startIcon={loading ? <CircularProgress size={24} /> : <SaveIcon />}
          disabled={loading}
        >
          {loading ? 'Saving...' : (webhook ? 'Update Webhook' : 'Create Webhook')}
        </Button>
      </Box>
    </Box>
  );
};

export default WebhookForm;