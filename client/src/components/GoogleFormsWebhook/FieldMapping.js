import React from 'react';
import { 
  Box, 
  Typography, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Button, 
  IconButton, 
  Grid,
  Divider,
  Paper,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon
} from '@mui/icons-material';

/**
 * FieldMapping Component
 * Allows configuring field mappings from form fields to task properties
 * 
 * @param {Object} props Component props
 * @param {Object} props.fieldMappings Current field mappings
 * @param {Array} props.formFields Available form fields
 * @param {Function} props.onChange Callback when field mappings change
 */
const FieldMapping = ({ fieldMappings, formFields, onChange }) => {
  // Handle field mapping change
  const handleFieldChange = (e) => {
    const { name, value } = e.target;
    
    onChange({
      ...fieldMappings,
      [name]: value
    });
  };

  // Add a custom field mapping
  const handleAddCustomField = () => {
    const customFields = [...(fieldMappings.customFields || [])];
    customFields.push({
      formField: '',
      taskField: ''
    });
    
    onChange({
      ...fieldMappings,
      customFields
    });
  };

  // Update a custom field mapping
  const handleCustomFieldChange = (index, field, value) => {
    const customFields = [...(fieldMappings.customFields || [])];
    customFields[index] = {
      ...customFields[index],
      [field]: value
    };
    
    onChange({
      ...fieldMappings,
      customFields
    });
  };

  // Remove a custom field mapping
  const handleRemoveCustomField = (index) => {
    const customFields = [...(fieldMappings.customFields || [])];
    customFields.splice(index, 1);
    
    onChange({
      ...fieldMappings,
      customFields
    });
  };

  return (
    <Box>
      <Typography variant="body1" paragraph>
        Map form fields to task properties. The title field is required.
      </Typography>
      
      {/* Required field mappings */}
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin="normal" required>
            <InputLabel>Title Field</InputLabel>
            <Select
              name="titleField"
              value={fieldMappings.titleField || ''}
              onChange={handleFieldChange}
              label="Title Field"
            >
              <MenuItem value="">
                <em>Select a field</em>
              </MenuItem>
              {formFields.map(field => (
                <MenuItem key={field.id} value={field.id}>
                  {field.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>Description Field</InputLabel>
            <Select
              name="descriptionField"
              value={fieldMappings.descriptionField || ''}
              onChange={handleFieldChange}
              label="Description Field"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {formFields.map(field => (
                <MenuItem key={field.id} value={field.id}>
                  {field.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
      
      {/* Optional field mappings */}
      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <FormControl fullWidth margin="normal">
            <InputLabel>Priority Field</InputLabel>
            <Select
              name="priorityField"
              value={fieldMappings.priorityField || ''}
              onChange={handleFieldChange}
              label="Priority Field"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {formFields.map(field => (
                <MenuItem key={field.id} value={field.id}>
                  {field.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <FormControl fullWidth margin="normal">
            <InputLabel>Due Date Field</InputLabel>
            <Select
              name="dueDateField"
              value={fieldMappings.dueDateField || ''}
              onChange={handleFieldChange}
              label="Due Date Field"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {formFields.map(field => (
                <MenuItem key={field.id} value={field.id}>
                  {field.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <FormControl fullWidth margin="normal">
            <InputLabel>Tags Field</InputLabel>
            <Select
              name="tagsField"
              value={fieldMappings.tagsField || ''}
              onChange={handleFieldChange}
              label="Tags Field"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {formFields.map(field => (
                <MenuItem key={field.id} value={field.id}>
                  {field.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
      
      {/* Custom field mappings */}
      <Box sx={{ mt: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">
            Custom Field Mappings
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleAddCustomField}
          >
            Add Custom Field
          </Button>
        </Box>
        
        {(!fieldMappings.customFields || fieldMappings.customFields.length === 0) ? (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2, mb: 2 }}>
            No custom field mappings. Add a custom field mapping to map form fields to custom task properties.
          </Typography>
        ) : (
          <List>
            {fieldMappings.customFields.map((customField, index) => (
              <ListItem 
                key={index}
                sx={{ 
                  bgcolor: 'background.paper',
                  borderRadius: 1,
                  mb: 1,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12} md={5}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Form Field</InputLabel>
                      <Select
                        value={customField.formField || ''}
                        onChange={(e) => handleCustomFieldChange(index, 'formField', e.target.value)}
                        label="Form Field"
                      >
                        <MenuItem value="">
                          <em>Select a field</em>
                        </MenuItem>
                        {formFields.map(field => (
                          <MenuItem key={field.id} value={field.id}>
                            {field.title}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={5}>
                    <TextField
                      fullWidth
                      size="small"
                      label="Task Field Name"
                      value={customField.taskField || ''}
                      onChange={(e) => handleCustomFieldChange(index, 'taskField', e.target.value)}
                      placeholder="e.g., location, category, etc."
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={2} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleRemoveCustomField(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Note: The title field is required. Other fields are optional.
        </Typography>
      </Box>
    </Box>
  );
};

export default FieldMapping;