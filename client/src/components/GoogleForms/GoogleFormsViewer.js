import React, { useState, useEffect } from 'react';
import { 
  Box, 
  CircularProgress, 
  Alert,
  Paper,
  IconButton,
  Tooltip,
  Typography
} from '@mui/material';
import { 
  OpenInNew as OpenInNewIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Add as AddIcon
} from '@mui/icons-material';
import axios from 'axios';

/**
 * Google Forms Viewer Component
 * Embeds a Google Form within an iframe
 * 
 * @param {Object} props Component props
 * @param {string} props.formId The ID of the form to display
 * @param {string} props.formUrl The URL of the form to display (alternative to formId)
 * @param {string} props.title The title of the form
 * @param {boolean} props.fullWidth Whether the form should take up the full width
 * @param {boolean} props.fullHeight Whether the form should take up the full height
 * @param {function} props.onAddToShortcuts Callback when the "Add to Shortcuts" button is clicked
 */
const GoogleFormsViewer = ({ 
  formId, 
  formUrl, 
  title, 
  fullWidth = false, 
  fullHeight = false,
  onAddToShortcuts
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [embedUrl, setEmbedUrl] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    const generateEmbedUrl = () => {
      try {
        // If formUrl is provided, use it
        if (formUrl) {
          // Add embedded=true parameter to the URL
          const url = new URL(formUrl);
          url.searchParams.set('embedded', 'true');
          setEmbedUrl(url.toString());
          setLoading(false);
          return;
        }
        
        // If formId is provided, construct the URL
        if (formId) {
          const url = `https://docs.google.com/forms/d/e/${formId}/viewform?embedded=true`;
          setEmbedUrl(url);
          setLoading(false);
          return;
        }
        
        // If neither formUrl nor formId is provided, show an error
        setError('No form URL or ID provided');
        setLoading(false);
      } catch (err) {
        console.error('Error generating embed URL:', err);
        setError('Failed to generate embed URL');
        setLoading(false);
      }
    };

    generateEmbedUrl();
  }, [formId, formUrl]);

  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleOpenInNewTab = () => {
    window.open(formUrl || embedUrl, '_blank');
  };

  const handleAddToShortcuts = () => {
    if (onAddToShortcuts) {
      onAddToShortcuts({
        formId,
        formUrl: formUrl || embedUrl,
        title
      });
    }
  };

  // Calculate iframe dimensions based on fullscreen state and props
  const iframeHeight = isFullscreen ? 'calc(100vh - 200px)' : (fullHeight ? '800px' : '600px');
  const iframeWidth = fullWidth || isFullscreen ? '100%' : '800px';

  return (
    <Box sx={{ width: '100%', mb: 4 }}>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <Box>
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            mb: 2,
            px: 2,
            py: 1,
            bgcolor: 'background.paper',
            borderRadius: '4px 4px 0 0',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}>
            <Typography variant="h6">{title || 'Google Form'}</Typography>
            <Box>
              {onAddToShortcuts && (
                <Tooltip title="Add to Shortcuts">
                  <IconButton onClick={handleAddToShortcuts} color="primary">
                    <AddIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip title="Toggle Fullscreen">
                <IconButton onClick={handleFullscreenToggle} color="primary">
                  {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
              <Tooltip title="Open in New Tab">
                <IconButton onClick={handleOpenInNewTab} color="primary">
                  <OpenInNewIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <Box sx={{ 
            width: '100%', 
            height: iframeHeight,
            overflow: 'hidden',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: '0 0 4px 4px'
          }}>
            <iframe
              src={embedUrl}
              width={iframeWidth}
              height="100%"
              frameBorder="0"
              marginHeight="0"
              marginWidth="0"
              title={title || "Google Form"}
              style={{ border: 'none' }}
            >
              Loading…
            </iframe>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default GoogleFormsViewer;