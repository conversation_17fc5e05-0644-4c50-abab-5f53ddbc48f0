import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardHeader,
  CardContent,
  Grid,
  Typography,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  LocalFireDepartment as FireExtinguisherIcon,
  ExitToApp as ExitIcon,
  HealthAndSafety as FirstAidIcon,
  LocationOn as LocationIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import safetyService from '../../services/safetyService';

const SafetyAssetsPanel = ({ buildingId, floorId, onHighlightAsset, onClearHighlight }) => {
  const [safetyAssets, setSafetyAssets] = useState([]);
  const [filteredAssets, setFilteredAssets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState(null);
  const [newAsset, setNewAsset] = useState({
    type: '',
    label: '',
    location: '',
    status: 'active',
    lastInspection: '',
    nextInspection: '',
    notes: ''
  });

  const assetTypes = [
    { value: 'fire_extinguisher', label: 'Fire Extinguisher', icon: <FireExtinguisherIcon /> },
    { value: 'emergency_exit', label: 'Emergency Exit', icon: <ExitIcon /> },
    { value: 'first_aid', label: 'First Aid Kit', icon: <FirstAidIcon /> },
    { value: 'aed', label: 'AED', icon: <FirstAidIcon /> }
  ];

  const statusTypes = [
    { value: 'active', label: 'Active', color: 'success' },
    { value: 'needs_inspection', label: 'Needs Inspection', color: 'warning' },
    { value: 'maintenance_required', label: 'Maintenance Required', color: 'error' },
    { value: 'out_of_service', label: 'Out of Service', color: 'error' }
  ];

  useEffect(() => {
    fetchSafetyAssets();
  }, [buildingId, floorId]);

  useEffect(() => {
    filterAssets();
  }, [safetyAssets, selectedType, selectedStatus]);

  const fetchSafetyAssets = async () => {
    try {
      setLoading(true);
      const assets = await safetyService.getSafetyAssets({
        buildingId,
        floorId
      });
      setSafetyAssets(assets || []);
    } catch (err) {
      console.error('Error fetching safety assets:', err);
      setError('Failed to load safety assets');
    } finally {
      setLoading(false);
    }
  };

  const filterAssets = () => {
    let filtered = [...safetyAssets];
    
    if (selectedType !== 'all') {
      filtered = filtered.filter(asset => asset.type === selectedType);
    }
    
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(asset => asset.status === selectedStatus);
    }
    
    setFilteredAssets(filtered);
  };

  const getAssetTypeInfo = (type) => {
    return assetTypes.find(t => t.value === type) || { label: type, icon: <LocationIcon /> };
  };

  const getStatusInfo = (status) => {
    return statusTypes.find(s => s.value === status) || { label: status, color: 'default' };
  };

  const handleAssetClick = (asset) => {
    setSelectedAsset(asset);
    if (onHighlightAsset) {
      onHighlightAsset(asset);
    }
  };

  const handleAddAsset = () => {
    setNewAsset({
      type: '',
      label: '',
      location: '',
      status: 'active',
      lastInspection: '',
      nextInspection: '',
      notes: ''
    });
    setAddDialogOpen(true);
  };

  const handleEditAsset = (asset) => {
    setNewAsset({
      ...asset,
      lastInspection: asset.lastInspection ? asset.lastInspection.split('T')[0] : '',
      nextInspection: asset.nextInspection ? asset.nextInspection.split('T')[0] : ''
    });
    setSelectedAsset(asset);
    setEditDialogOpen(true);
  };

  const handleSaveAsset = async () => {
    try {
      if (selectedAsset) {
        // Update existing asset
        await safetyService.updateSafetyAsset(selectedAsset._id, newAsset);
      } else {
        // Create new asset
        await safetyService.createSafetyAsset({
          ...newAsset,
          buildingId,
          floorId
        });
      }
      
      await fetchSafetyAssets();
      setAddDialogOpen(false);
      setEditDialogOpen(false);
      setSelectedAsset(null);
    } catch (err) {
      console.error('Error saving safety asset:', err);
      setError('Failed to save safety asset');
    }
  };

  const handleDeleteAsset = async (assetId) => {
    if (!window.confirm('Are you sure you want to delete this safety asset?')) {
      return;
    }
    
    try {
      await safetyService.deleteSafetyAsset(assetId);
      await fetchSafetyAssets();
    } catch (err) {
      console.error('Error deleting safety asset:', err);
      setError('Failed to delete safety asset');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Loading safety assets...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Safety Assets"
        subheader="Emergency equipment and safety devices"
        action={
          <Button
            variant="contained"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleAddAsset}
          >
            Add Asset
          </Button>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6}>
            <FormControl fullWidth size="small">
              <InputLabel>Type</InputLabel>
              <Select
                value={selectedType}
                label="Type"
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                {assetTypes.map(type => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6}>
            <FormControl fullWidth size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={selectedStatus}
                label="Status"
                onChange={(e) => setSelectedStatus(e.target.value)}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                {statusTypes.map(status => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Assets List */}
        <List>
          {filteredAssets.length > 0 ? (
            filteredAssets.map(asset => {
              const typeInfo = getAssetTypeInfo(asset.type);
              const statusInfo = getStatusInfo(asset.status);
              
              return (
                <ListItem
                  key={asset._id}
                  secondaryAction={
                    <Box>
                      <Button
                        size="small"
                        onClick={() => handleEditAsset(asset)}
                      >
                        <EditIcon fontSize="small" />
                      </Button>
                      <Button
                        size="small"
                        color="error"
                        onClick={() => handleDeleteAsset(asset._id)}
                      >
                        <DeleteIcon fontSize="small" />
                      </Button>
                    </Box>
                  }
                >
                  <ListItemButton onClick={() => handleAssetClick(asset)}>
                    <ListItemIcon>
                      {typeInfo.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2">
                            {asset.label || typeInfo.label}
                          </Typography>
                          <Chip
                            label={statusInfo.label}
                            size="small"
                            color={statusInfo.color}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {asset.location}
                          </Typography>
                          {asset.nextInspection && (
                            <Typography variant="caption" color="text.secondary">
                              Next inspection: {new Date(asset.nextInspection).toLocaleDateString()}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItemButton>
                </ListItem>
              );
            })
          ) : (
            <ListItem>
              <ListItemText
                primary="No safety assets found"
                secondary="Click 'Add Asset' to register safety equipment"
              />
            </ListItem>
          )}
        </List>

        {/* Add/Edit Asset Dialog */}
        <Dialog
          open={addDialogOpen || editDialogOpen}
          onClose={() => {
            setAddDialogOpen(false);
            setEditDialogOpen(false);
            setSelectedAsset(null);
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            {selectedAsset ? 'Edit Safety Asset' : 'Add Safety Asset'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={newAsset.type}
                    label="Type"
                    onChange={(e) => setNewAsset({ ...newAsset, type: e.target.value })}
                    required
                  >
                    {assetTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {type.icon}
                          {type.label}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Label"
                  value={newAsset.label}
                  onChange={(e) => setNewAsset({ ...newAsset, label: e.target.value })}
                  placeholder="e.g., FE-01, Exit A, First Aid Station 1"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Location"
                  value={newAsset.location}
                  onChange={(e) => setNewAsset({ ...newAsset, location: e.target.value })}
                  placeholder="e.g., Near main entrance, Hallway outside Room 101"
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={newAsset.status}
                    label="Status"
                    onChange={(e) => setNewAsset({ ...newAsset, status: e.target.value })}
                  >
                    {statusTypes.map(status => (
                      <MenuItem key={status.value} value={status.value}>
                        {status.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Last Inspection"
                  type="date"
                  value={newAsset.lastInspection}
                  onChange={(e) => setNewAsset({ ...newAsset, lastInspection: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Next Inspection"
                  type="date"
                  value={newAsset.nextInspection}
                  onChange={(e) => setNewAsset({ ...newAsset, nextInspection: e.target.value })}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={3}
                  value={newAsset.notes}
                  onChange={(e) => setNewAsset({ ...newAsset, notes: e.target.value })}
                  placeholder="Additional notes about this safety asset..."
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => {
              setAddDialogOpen(false);
              setEditDialogOpen(false);
              setSelectedAsset(null);
            }}>
              Cancel
            </Button>
            <Button onClick={handleSaveAsset} variant="contained">
              {selectedAsset ? 'Update' : 'Add'} Asset
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default SafetyAssetsPanel;