import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Autocomplete,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider
} from '@mui/material';
import {
  ElectricBolt as ElectricIcon,
  Search as SearchIcon,
  LocationOn as LocationIcon,
  ContentCopy as CopyIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import electricalService from '../../services/electricalService';

const OutletTracePanel = ({ onHighlightPath, onClearHighlight }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [outlets, setOutlets] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [selectedOutlet, setSelectedOutlet] = useState(null);
  const [traceResult, setTraceResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch all outlets for typeahead
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await electricalService.getOutlets();
        setOutlets(response.items || []);
      } catch (err) {
        console.error('Error fetching outlets:', err);
        setError('Failed to load outlets');
      }
    };
    
    fetchOutlets();
  }, []);

  // Filter outlets based on search query
  useEffect(() => {
    if (searchQuery) {
      const filtered = outlets.filter(outlet => 
        outlet.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (outlet.room && outlet.room.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setSearchResults(filtered.slice(0, 10)); // Limit to 10 results
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, outlets]);

  const handleTraceOutlet = async (outlet) => {
    if (!outlet) return;

    setLoading(true);
    setError(null);
    setSelectedOutlet(outlet);

    try {
      const trace = await electricalService.traceOutlet(outlet._id);
      setTraceResult(trace);
      
      // Notify parent component to highlight the path
      if (onHighlightPath && trace.path) {
        onHighlightPath(trace.path);
      }
    } catch (err) {
      console.error('Error tracing outlet:', err);
      setError('Failed to trace outlet. Please check if the outlet is properly linked to a panel and circuit.');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyDirections = () => {
    if (!traceResult) return;

    const directions = `Outlet: ${traceResult.outlet.label} (${traceResult.outlet.room})
Panel: ${traceResult.panel.name} (${traceResult.panel.room})
Circuit: ${traceResult.circuit.label || traceResult.circuit.number}
Breaker: ${traceResult.outlet.breakerNumber}`;

    navigator.clipboard.writeText(directions).then(() => {
      // Could add a snackbar notification here
      console.log('Directions copied to clipboard');
    });
  };

  const handleClearTrace = () => {
    setTraceResult(null);
    setSelectedOutlet(null);
    if (onClearHighlight) {
      onClearHighlight();
    }
  };

  return (
    <Card sx={{ maxWidth: 400, height: 'fit-content' }}>
      <CardHeader
        title="Outlet Trace"
        subheader="Find the breaker panel for any outlet"
        avatar={<ElectricIcon color="primary" />}
      />
      <CardContent>
        {/* Search Input */}
        <Autocomplete
          options={searchResults}
          getOptionLabel={(option) => `${option.label} - ${option.room || 'No Room'}`}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Search outlet..."
              placeholder="Type outlet label (e.g., LO-01)"
              fullWidth
              InputProps={{
                ...params.InputProps,
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          )}
          onInputChange={(event, value) => setSearchQuery(value)}
          onChange={(event, value) => {
            if (value) {
              handleTraceOutlet(value);
            }
          }}
          loading={loading}
          sx={{ mb: 2 }}
        />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress size={24} />
            <Typography sx={{ ml: 1 }}>Tracing outlet...</Typography>
          </Box>
        )}

        {traceResult && !loading && (
          <Paper elevation={1} sx={{ p: 2, mt: 2 }}>
            <Typography variant="h6" gutterBottom color="primary">
              Trace Results
            </Typography>
            
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <ElectricIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Outlet"
                  secondary={`${traceResult.outlet.label} (${traceResult.outlet.room || 'No Room'})`}
                />
              </ListItem>
              
              <Divider />
              
              <ListItem>
                <ListItemIcon>
                  <LocationIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Panel"
                  secondary={`${traceResult.panel.name} - ${traceResult.panel.room || 'No Room'}`}
                />
              </ListItem>
              
              {traceResult.circuit && (
                <>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <ElectricIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Circuit"
                      secondary={`Circuit ${traceResult.circuit.number} - ${traceResult.circuit.label || 'No Label'}`}
                    />
                  </ListItem>
                </>
              )}
              
              {traceResult.outlet.breakerNumber && (
                <>
                  <Divider />
                  <ListItem>
                    <ListItemIcon>
                      <ElectricIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Breaker"
                      secondary={`Breaker #${traceResult.outlet.breakerNumber}`}
                    />
                  </ListItem>
                </>
              )}
            </List>

            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<CopyIcon />}
                onClick={handleCopyDirections}
              >
                Copy Directions
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={handleClearTrace}
              >
                Clear
              </Button>
            </Box>
          </Paper>
        )}

        {!traceResult && !loading && !error && (
          <Alert severity="info" sx={{ mt: 2 }}>
            Type an outlet label to find its breaker panel and circuit information.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default OutletTracePanel;