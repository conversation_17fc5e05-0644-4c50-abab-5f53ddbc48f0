import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Checkbox,
  FormControlLabel,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress
} from '@mui/material';
import {
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  MeetingRoom as DoorIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  SelectAll as SelectAllIcon,
  DeselectAll as DeselectAllIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import buildingManagementService from '../../services/buildingManagementService';

/**
 * DoorBulkManagement Component
 * Provides bulk operations for door management including status monitoring and control
 */
const DoorBulkManagement = ({ 
  open, 
  onClose, 
  buildingId = null,
  floorId = null,
  initialDoors = []
}) => {
  const [doors, setDoors] = useState([]);
  const [selectedDoors, setSelectedDoors] = useState(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [bulkOperation, setBulkOperation] = useState('');
  const [operationInProgress, setOperationInProgress] = useState(false);
  const [operationResults, setOperationResults] = useState([]);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterSource, setFilterSource] = useState('all');

  // Load doors when component opens
  useEffect(() => {
    if (open) {
      if (initialDoors && initialDoors.length > 0) {
        setDoors(initialDoors);
      } else {
        loadDoors();
      }
    } else {
      // Reset state when dialog closes
      setSelectedDoors(new Set());
      setError(null);
      setSuccess(null);
      setOperationResults([]);
    }
  }, [open, initialDoors]);

  const loadDoors = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const doorsData = await buildingManagementService.getDoors();
      
      // Filter by building/floor if specified
      let filteredDoors = doorsData;
      if (buildingId) {
        filteredDoors = filteredDoors.filter(door => door.buildingId === buildingId);
      }
      if (floorId) {
        filteredDoors = filteredDoors.filter(door => door.floorId === floorId);
      }
      
      setDoors(filteredDoors);
    } catch (err) {
      console.error('Error loading doors:', err);
      setError('Failed to load doors. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getFilteredDoors = () => {
    return doors.filter(door => {
      if (filterStatus !== 'all' && door.status !== filterStatus) {
        return false;
      }
      if (filterSource !== 'all' && door.source !== filterSource) {
        return false;
      }
      return true;
    });
  };

  const handleSelectAll = () => {
    const filteredDoors = getFilteredDoors();
    if (selectedDoors.size === filteredDoors.length) {
      // Deselect all
      setSelectedDoors(new Set());
    } else {
      // Select all filtered doors
      setSelectedDoors(new Set(filteredDoors.map(door => door.id)));
    }
  };

  const handleDoorSelection = (doorId, checked) => {
    const newSelected = new Set(selectedDoors);
    if (checked) {
      newSelected.add(doorId);
    } else {
      newSelected.delete(doorId);
    }
    setSelectedDoors(newSelected);
  };

  const executeBulkOperation = async () => {
    if (!bulkOperation || selectedDoors.size === 0) return;

    try {
      setOperationInProgress(true);
      setError(null);
      setSuccess(null);
      setOperationResults([]);

      const selectedDoorsList = doors.filter(door => selectedDoors.has(door.id));
      const results = [];

      // Execute operation for each selected door
      for (const door of selectedDoorsList) {
        try {
          let result = null;
          
          switch (bulkOperation) {
            case 'unlock':
              result = await buildingManagementService.unlockDoor(door.id);
              break;
            case 'lock':
              result = await buildingManagementService.lockDoor(door.id);
              break;
            case 'passage':
              result = await buildingManagementService.setDoorPassageMode(door.id);
              break;
            case 'refresh':
              result = await buildingManagementService.refreshDoorStatus(door.id);
              break;
            default:
              throw new Error('Unknown operation');
          }

          results.push({
            door,
            success: true,
            message: `${bulkOperation} operation completed successfully`,
            result
          });

        } catch (err) {
          results.push({
            door,
            success: false,
            message: err.response?.data?.message || err.message || 'Operation failed',
            error: err
          });
        }
      }

      setOperationResults(results);
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;
      
      if (failureCount === 0) {
        setSuccess(`Successfully performed ${bulkOperation} on ${successCount} doors`);
      } else {
        setError(`Operation completed with ${successCount} successes and ${failureCount} failures`);
      }

      // Refresh doors list to get updated status
      await loadDoors();
      
    } catch (err) {
      console.error('Bulk operation error:', err);
      setError('Failed to execute bulk operation');
    } finally {
      setOperationInProgress(false);
    }
  };

  const getDoorStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'locked':
      case 'closed':
        return 'success';
      case 'unlocked':
      case 'open':
        return 'warning';
      case 'alarm':
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getDoorStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'locked':
      case 'closed':
        return <LockIcon />;
      case 'unlocked':
      case 'open':
        return <UnlockIcon />;
      case 'alarm':
      case 'error':
        return <ErrorIcon />;
      default:
        return <DoorIcon />;
    }
  };

  const getOperationIcon = (operation) => {
    switch (operation) {
      case 'unlock':
        return <UnlockIcon />;
      case 'lock':
        return <LockIcon />;
      case 'passage':
        return <DoorIcon />;
      case 'refresh':
        return <RefreshIcon />;
      default:
        return <SecurityIcon />;
    }
  };

  const filteredDoors = getFilteredDoors();
  const allSelected = selectedDoors.size === filteredDoors.length && filteredDoors.length > 0;
  const someSelected = selectedDoors.size > 0 && selectedDoors.size < filteredDoors.length;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh', maxHeight: '90vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SecurityIcon />
            <Typography variant="h6">Door Bulk Management</Typography>
          </Box>
          <Chip 
            label={`${filteredDoors.length} doors`} 
            color="primary" 
            variant="outlined" 
          />
        </Box>
      </DialogTitle>

      <DialogContent>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        {!loading && (
          <>
            {/* Filters and Controls */}
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Filter by Status</InputLabel>
                      <Select
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                        label="Filter by Status"
                      >
                        <MenuItem value="all">All Status</MenuItem>
                        <MenuItem value="locked">Locked</MenuItem>
                        <MenuItem value="unlocked">Unlocked</MenuItem>
                        <MenuItem value="open">Open</MenuItem>
                        <MenuItem value="closed">Closed</MenuItem>
                        <MenuItem value="alarm">Alarm</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Filter by Source</InputLabel>
                      <Select
                        value={filterSource}
                        onChange={(e) => setFilterSource(e.target.value)}
                        label="Filter by Source"
                      >
                        <MenuItem value="all">All Sources</MenuItem>
                        <MenuItem value="lenelS2NetBox">Lenel S2</MenuItem>
                        <MenuItem value="unifiAccess">UniFi Access</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Bulk Operation</InputLabel>
                      <Select
                        value={bulkOperation}
                        onChange={(e) => setBulkOperation(e.target.value)}
                        label="Bulk Operation"
                        disabled={selectedDoors.size === 0}
                      >
                        <MenuItem value="">Select Operation</MenuItem>
                        <MenuItem value="unlock">Unlock Doors</MenuItem>
                        <MenuItem value="lock">Lock Doors</MenuItem>
                        <MenuItem value="passage">Set Passage Mode</MenuItem>
                        <MenuItem value="refresh">Refresh Status</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} sm={3}>
                    <Button
                      onClick={executeBulkOperation}
                      variant="contained"
                      fullWidth
                      disabled={!bulkOperation || selectedDoors.size === 0 || operationInProgress}
                      startIcon={getOperationIcon(bulkOperation)}
                    >
                      {operationInProgress ? 'Processing...' : 'Execute'}
                    </Button>
                  </Grid>
                </Grid>

                {operationInProgress && (
                  <Box sx={{ mt: 2 }}>
                    <LinearProgress />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Processing bulk operation...
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>

            {/* Door List */}
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell padding="checkbox">
                      <Tooltip title={allSelected ? "Deselect All" : "Select All"}>
                        <Checkbox
                          indeterminate={someSelected}
                          checked={allSelected}
                          onChange={handleSelectAll}
                          disabled={filteredDoors.length === 0}
                        />
                      </Tooltip>
                    </TableCell>
                    <TableCell>Door Name</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Source</TableCell>
                    <TableCell>Last Activity</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredDoors.map((door) => (
                    <TableRow 
                      key={door.id}
                      hover
                      selected={selectedDoors.has(door.id)}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedDoors.has(door.id)}
                          onChange={(e) => handleDoorSelection(door.id, e.target.checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getDoorStatusIcon(door.status)}
                          {door.name}
                        </Box>
                      </TableCell>
                      <TableCell>{door.location || 'Unknown'}</TableCell>
                      <TableCell>
                        <Chip
                          label={door.status || 'Unknown'}
                          color={getDoorStatusColor(door.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={door.source}
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {door.lastActivity ? new Date(door.lastActivity).toLocaleString() : 'Unknown'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {filteredDoors.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <DoorIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  No doors found
                </Typography>
                <Typography color="text.secondary">
                  Try adjusting your filters or refresh the data
                </Typography>
              </Box>
            )}

            {/* Operation Results */}
            {operationResults.length > 0 && (
              <Card sx={{ mt: 2 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Operation Results
                  </Typography>
                  <List dense>
                    {operationResults.map((result, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          {result.success ? 
                            <SuccessIcon color="success" /> : 
                            <ErrorIcon color="error" />
                          }
                        </ListItemIcon>
                        <ListItemText
                          primary={result.door.name}
                          secondary={result.message}
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Typography variant="body2" color="text.secondary" sx={{ mr: 'auto' }}>
          {selectedDoors.size} of {filteredDoors.length} doors selected
        </Typography>
        
        <Button onClick={loadDoors} disabled={loading || operationInProgress}>
          <RefreshIcon sx={{ mr: 1 }} />
          Refresh
        </Button>
        
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DoorBulkManagement;