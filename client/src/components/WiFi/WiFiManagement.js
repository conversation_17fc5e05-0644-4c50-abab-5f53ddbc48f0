import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Alert,
  CircularProgress,
  Tooltip,
  Card,
  CardContent,
  CardActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Badge
} from '@mui/material';
import {
  Router as APIcon,
  SignalWifi4Bar as StrongSignalIcon,
  SignalWifi3Bar as GoodSignalIcon,
  SignalWifi2Bar as FairSignalIcon,
  SignalWifi1Bar as WeakSignalIcon,
  SignalWifiOff as NoSignalIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Sync as SyncIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  LocationOn as LocationIcon,
  Speed as SpeedIcon,
  People as ClientsIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import wifiService from '../../services/wifiService';

/**
 * WiFiManagement Component
 * Comprehensive WiFi access point and coverage management interface
 * Part of Phase 5 - Wi-Fi coverage & APs
 */
const WiFiManagement = ({ 
  buildingId, 
  floorId, 
  compactView = false 
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [accessPoints, setAccessPoints] = useState([]);
  const [coverageAreas, setCoverageAreas] = useState([]);
  const [unifiDevices, setUnifiDevices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Dialog states
  const [apDialogOpen, setApDialogOpen] = useState(false);
  const [coverageDialogOpen, setCoverageDialogOpen] = useState(false);
  const [syncDialogOpen, setSyncDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  
  // Form states
  const [apForm, setApForm] = useState({
    deviceId: '',
    position: { x: 50, y: 50 },
    room: '',
    mounting: { type: 'ceiling', height: 3 }
  });
  
  const [coverageForm, setCoverageForm] = useState({
    name: '',
    coverageArea: { type: 'Polygon', coordinates: [[]] },
    signalStrength: { average: -60 },
    quality: { excellent: 70, good: 25, fair: 5, poor: 0 },
    ssid: '',
    frequency: '5GHz'
  });

  useEffect(() => {
    loadData();
  }, [buildingId, floorId]);

  const loadData = async () => {
    if (!buildingId || !floorId) return;

    try {
      setLoading(true);
      setError(null);
      
      const [apsData, coverageData] = await Promise.all([
        wifiService.getAccessPointsByLocation(buildingId, floorId, { includeOffline: true }),
        wifiService.getCoverageAreasByLocation(buildingId, floorId)
      ]);
      
      setAccessPoints(apsData);
      setCoverageAreas(coverageData);
    } catch (err) {
      console.error('Error loading WiFi data:', err);
      setError('Failed to load WiFi data');
    } finally {
      setLoading(false);
    }
  };

  const loadUnifiDevices = async () => {
    try {
      const devices = await wifiService.getUniFiDevices();
      setUnifiDevices(devices);
    } catch (err) {
      console.error('Error loading UniFi devices:', err);
      setError('Failed to load UniFi devices');
    }
  };

  // Handle access point operations
  const handleCreateAP = async () => {
    try {
      await wifiService.createAccessPoint({
        ...apForm,
        buildingId,
        floorId
      });
      
      setApDialogOpen(false);
      setApForm({
        deviceId: '',
        position: { x: 50, y: 50 },
        room: '',
        mounting: { type: 'ceiling', height: 3 }
      });
      loadData();
    } catch (err) {
      console.error('Error creating access point:', err);
      setError('Failed to create access point');
    }
  };

  const handleUpdateAP = async () => {
    try {
      await wifiService.updateAccessPoint(selectedItem._id, apForm);
      setApDialogOpen(false);
      setSelectedItem(null);
      loadData();
    } catch (err) {
      console.error('Error updating access point:', err);
      setError('Failed to update access point');
    }
  };

  const handleDeleteAP = async (apId) => {
    if (!window.confirm('Are you sure you want to delete this access point?')) return;
    
    try {
      await wifiService.deleteAccessPoint(apId);
      loadData();
    } catch (err) {
      console.error('Error deleting access point:', err);
      setError('Failed to delete access point');
    }
  };

  // Handle coverage area operations
  const handleCreateCoverage = async () => {
    try {
      await wifiService.createCoverageArea({
        ...coverageForm,
        buildingId,
        floorId
      });
      
      setCoverageDialogOpen(false);
      setCoverageForm({
        name: '',
        coverageArea: { type: 'Polygon', coordinates: [[]] },
        signalStrength: { average: -60 },
        quality: { excellent: 70, good: 25, fair: 5, poor: 0 },
        ssid: '',
        frequency: '5GHz'
      });
      loadData();
    } catch (err) {
      console.error('Error creating coverage area:', err);
      setError('Failed to create coverage area');
    }
  };

  const handleSyncWithUnifi = async () => {
    try {
      setLoading(true);
      const result = await wifiService.syncWithUniFi({ updateExisting: true });
      setSyncDialogOpen(false);
      loadData();
      
      // Show sync results
      if (result.results) {
        alert(`Sync completed: ${result.results.synced} devices processed, ${result.results.updated} updated`);
      }
    } catch (err) {
      console.error('Error syncing with UniFi:', err);
      setError('Failed to sync with UniFi');
    } finally {
      setLoading(false);
    }
  };

  // Get status icon for AP
  const getAPStatusIcon = (ap) => {
    const health = ap.healthScore || 0;
    const isConnected = ap.status?.state === 'connected';
    
    if (!isConnected) return <NoSignalIcon color="disabled" />;
    if (health >= 90) return <StrongSignalIcon color="success" />;
    if (health >= 75) return <GoodSignalIcon color="success" />;
    if (health >= 60) return <FairSignalIcon color="warning" />;
    if (health >= 40) return <WeakSignalIcon color="warning" />;
    return <NoSignalIcon color="error" />;
  };

  // Render access points table
  const renderAccessPointsTable = () => (
    <TableContainer component={Paper}>
      <Table size={compactView ? "small" : "medium"}>
        <TableHead>
          <TableRow>
            <TableCell>Status</TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Location</TableCell>
            <TableCell>Clients</TableCell>
            <TableCell>Health</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {accessPoints.map((ap) => (
            <TableRow key={ap._id}>
              <TableCell>
                <Tooltip title={`${ap.status?.state || 'unknown'} - Health: ${ap.healthScore || 0}%`}>
                  {getAPStatusIcon(ap)}
                </Tooltip>
              </TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    {ap.deviceInfo?.name || `AP-${ap.deviceId?.slice(-6)}`}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {ap.deviceInfo?.model} • {ap.deviceId}
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <Box>
                  <Typography variant="body2">{ap.room || 'Unspecified'}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    ({ap.position?.x}, {ap.position?.y})
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <Badge 
                  badgeContent={ap.wireless?.clientStats?.total || 0} 
                  color="primary"
                  max={999}
                >
                  <ClientsIcon color="action" />
                </Badge>
              </TableCell>
              <TableCell>
                <Chip
                  label={`${ap.healthScore || 0}%`}
                  size="small"
                  color={
                    (ap.healthScore || 0) >= 90 ? 'success' :
                    (ap.healthScore || 0) >= 75 ? 'primary' :
                    (ap.healthScore || 0) >= 60 ? 'warning' : 'error'
                  }
                />
              </TableCell>
              <TableCell>
                <IconButton
                  size="small"
                  onClick={() => {
                    setSelectedItem(ap);
                    setApForm({
                      deviceId: ap.deviceId,
                      position: ap.position,
                      room: ap.room,
                      mounting: ap.mounting
                    });
                    setApDialogOpen(true);
                  }}
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleDeleteAP(ap._id)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render coverage areas table
  const renderCoverageTable = () => (
    <TableContainer component={Paper}>
      <Table size={compactView ? "small" : "medium"}>
        <TableHead>
          <TableRow>
            <TableCell>Name</TableCell>
            <TableCell>Signal Strength</TableCell>
            <TableCell>Quality Score</TableCell>
            <TableCell>Frequency</TableCell>
            <TableCell>Capacity</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {coverageAreas.map((area) => (
            <TableRow key={area._id}>
              <TableCell>
                <Typography variant="body2" fontWeight="bold">
                  {area.name}
                </Typography>
              </TableCell>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {wifiService.getSignalStrengthColor(area.signalStrength?.average) && (
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: wifiService.getSignalStrengthColor(area.signalStrength?.average)
                      }}
                    />
                  )}
                  <Typography variant="body2">
                    {area.signalStrength?.average || 'N/A'} dBm
                  </Typography>
                </Box>
              </TableCell>
              <TableCell>
                <Chip
                  label={`${area.quality?.overallScore || 0}%`}
                  size="small"
                  color={
                    (area.quality?.overallScore || 0) >= 80 ? 'success' :
                    (area.quality?.overallScore || 0) >= 60 ? 'primary' :
                    (area.quality?.overallScore || 0) >= 40 ? 'warning' : 'error'
                  }
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={area.frequency}
                  size="small"
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <Typography variant="body2">
                  {area.eventSuitability?.recommendedMaxDevices || 0} devices
                </Typography>
              </TableCell>
              <TableCell>
                <IconButton
                  size="small"
                  onClick={() => {
                    setSelectedItem(area);
                    setCoverageForm(area);
                    setCoverageDialogOpen(true);
                  }}
                >
                  <EditIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render analytics cards
  const renderAnalytics = () => {
    const connectedAPs = accessPoints.filter(ap => ap.status?.state === 'connected');
    const totalClients = connectedAPs.reduce((sum, ap) => sum + (ap.wireless?.clientStats?.total || 0), 0);
    const avgHealth = connectedAPs.length > 0 
      ? connectedAPs.reduce((sum, ap) => sum + (ap.healthScore || 0), 0) / connectedAPs.length 
      : 0;
    const avgSignalStrength = coverageAreas.length > 0 
      ? coverageAreas.reduce((sum, area) => sum + (area.signalStrength?.average || -70), 0) / coverageAreas.length
      : null;

    return (
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <APIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4">{connectedAPs.length}/{accessPoints.length}</Typography>
              <Typography variant="body2" color="text.secondary">
                Active Access Points
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ClientsIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4">{totalClients}</Typography>
              <Typography variant="body2" color="text.secondary">
                Connected Devices
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SpeedIcon color="info" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4">{Math.round(avgHealth)}%</Typography>
              <Typography variant="body2" color="text.secondary">
                Average Health
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <StrongSignalIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4">
                {avgSignalStrength ? `${Math.round(avgSignalStrength)}` : 'N/A'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Signal (dBm)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading WiFi management data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Header with actions */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          WiFi Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SyncIcon />}
            onClick={() => {
              loadUnifiDevices();
              setSyncDialogOpen(true);
            }}
            size="small"
          >
            Sync UniFi
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setApDialogOpen(true)}
            size="small"
          >
            Add AP
          </Button>
        </Box>
      </Box>

      {/* Analytics section */}
      {!compactView && (
        <Box sx={{ mb: 3 }}>
          {renderAnalytics()}
        </Box>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label={`Access Points (${accessPoints.length})`} />
          <Tab label={`Coverage Areas (${coverageAreas.length})`} />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      {/* Tab content */}
      {currentTab === 0 && renderAccessPointsTable()}
      {currentTab === 1 && renderCoverageTable()}
      {currentTab === 2 && renderAnalytics()}

      {/* Add/Edit AP Dialog */}
      <Dialog open={apDialogOpen} onClose={() => setApDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedItem ? 'Edit Access Point' : 'Add Access Point'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Device ID (MAC Address)"
                value={apForm.deviceId}
                onChange={(e) => setApForm({ ...apForm, deviceId: e.target.value.toUpperCase() })}
                placeholder="AA:BB:CC:DD:EE:FF"
                disabled={!!selectedItem}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="X Position"
                type="number"
                value={apForm.position.x}
                onChange={(e) => setApForm({ 
                  ...apForm, 
                  position: { ...apForm.position, x: parseFloat(e.target.value) }
                })}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Y Position"
                type="number"
                value={apForm.position.y}
                onChange={(e) => setApForm({ 
                  ...apForm, 
                  position: { ...apForm.position, y: parseFloat(e.target.value) }
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Room/Location"
                value={apForm.room}
                onChange={(e) => setApForm({ ...apForm, room: e.target.value })}
              />
            </Grid>
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Mounting Type</InputLabel>
                <Select
                  value={apForm.mounting.type}
                  onChange={(e) => setApForm({ 
                    ...apForm, 
                    mounting: { ...apForm.mounting, type: e.target.value }
                  })}
                  label="Mounting Type"
                >
                  <MenuItem value="ceiling">Ceiling</MenuItem>
                  <MenuItem value="wall">Wall</MenuItem>
                  <MenuItem value="pole">Pole</MenuItem>
                  <MenuItem value="desk">Desk</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Height (meters)"
                type="number"
                value={apForm.mounting.height}
                onChange={(e) => setApForm({ 
                  ...apForm, 
                  mounting: { ...apForm.mounting, height: parseFloat(e.target.value) }
                })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={selectedItem ? handleUpdateAP : handleCreateAP}
            variant="contained"
          >
            {selectedItem ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* UniFi Sync Dialog */}
      <Dialog open={syncDialogOpen} onClose={() => setSyncDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Sync with UniFi Network</DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Available UniFi access points that can be imported:
          </Typography>
          {unifiDevices.length > 0 ? (
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Model</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Imported</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {unifiDevices.map((device) => (
                    <TableRow key={device.deviceId}>
                      <TableCell>{device.name}</TableCell>
                      <TableCell>{device.model}</TableCell>
                      <TableCell>
                        <Chip
                          label={device.state}
                          size="small"
                          color={device.state === 'connected' ? 'success' : 'default'}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={device.isImported ? 'Yes' : 'No'}
                          size="small"
                          color={device.isImported ? 'success' : 'default'}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">
              No UniFi devices found or UniFi integration not configured.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSyncDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSyncWithUnifi}
            variant="contained"
            startIcon={<SyncIcon />}
          >
            Sync All
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WiFiManagement;