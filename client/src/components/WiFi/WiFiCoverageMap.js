import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Slider,
  Grid,
  Alert,
  CircularProgress,
  Tooltip,
  Card,
  CardContent,
  Chip,
  Button
} from '@mui/material';
import {
  Wifi as WiFiIcon,
  SignalWifi4Bar as StrongSignalIcon,
  SignalWifi3Bar as GoodSignalIcon,
  SignalWifi2Bar as FairSignalIcon,
  SignalWifi1Bar as WeakSignalIcon,
  SignalWifiOff as NoSignalIcon,
  Router as APIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import wifiService from '../../services/wifiService';

/**
 * WiFiCoverageMap Component
 * Visualizes WiFi coverage and access points on a floor plan
 * Part of Phase 5 - Wi-Fi coverage & APs
 */
const WiFiCoverageMap = ({ 
  buildingId, 
  floorId, 
  width = 800, 
  height = 600,
  showControls = true,
  showAccessPoints = true,
  showCoverageAreas = true,
  onAPClick = null,
  onCoverageClick = null,
  autoRefresh = false
}) => {
  const [accessPoints, setAccessPoints] = useState([]);
  const [coverageAreas, setCoverageAreas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Display options
  const [visualizationMode, setVisualizationMode] = useState('signal_strength');
  const [showHeatmap, setShowHeatmap] = useState(true);
  const [selectedBand, setSelectedBand] = useState('5GHz');
  const [opacity, setOpacity] = useState(0.6);
  const [showAPLabels, setShowAPLabels] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Auto-refresh effect
  useEffect(() => {
    let timer = null;
    if (autoRefresh && refreshInterval > 0) {
      timer = setInterval(() => {
        loadWiFiData();
      }, refreshInterval * 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [autoRefresh, refreshInterval, buildingId, floorId]);

  // Load WiFi data when component mounts or props change
  useEffect(() => {
    loadWiFiData();
  }, [buildingId, floorId]);

  const loadWiFiData = async () => {
    if (!buildingId || !floorId) return;

    try {
      setLoading(true);
      setError(null);
      
      const [apsData, coverageData] = await Promise.all([
        showAccessPoints ? wifiService.getAccessPointsByLocation(buildingId, floorId, { includeOffline: true }) : [],
        showCoverageAreas ? wifiService.getCoverageAreasByLocation(buildingId, floorId) : []
      ]);
      
      setAccessPoints(apsData);
      setCoverageAreas(coverageData);
    } catch (err) {
      console.error('Error loading WiFi data:', err);
      setError('Failed to load WiFi data');
    } finally {
      setLoading(false);
    }
  };

  // Generate heatmap data
  const heatmapData = useMemo(() => {
    if (!showHeatmap || (!accessPoints.length && !coverageAreas.length)) return [];
    
    return wifiService.generateHeatmapData(accessPoints, coverageAreas, width, height);
  }, [accessPoints, coverageAreas, showHeatmap, width, height]);

  // Filter access points by selected band
  const filteredAccessPoints = useMemo(() => {
    if (selectedBand === 'all') return accessPoints;
    
    return accessPoints.filter(ap => 
      ap.wireless?.radios?.some(radio => radio.band === selectedBand && radio.enabled)
    );
  }, [accessPoints, selectedBand]);

  // Get WiFi icon based on signal strength
  const getWiFiIcon = (strength, size = 'medium') => {
    if (strength > -50) return <StrongSignalIcon fontSize={size} />;
    if (strength > -65) return <GoodSignalIcon fontSize={size} />;
    if (strength > -75) return <FairSignalIcon fontSize={size} />;
    if (strength > -85) return <WeakSignalIcon fontSize={size} />;
    return <NoSignalIcon fontSize={size} />;
  };

  // Render coverage areas
  const renderCoverageAreas = () => {
    if (!showCoverageAreas) return null;

    return coverageAreas.map(area => {
      const coordinates = area.coverageArea.coordinates[0];
      const pathData = coordinates.map((coord, index) => 
        `${index === 0 ? 'M' : 'L'} ${coord[0] * width / 100} ${coord[1] * height / 100}`
      ).join(' ') + ' Z';

      let fillColor = '#2196f3';
      if (visualizationMode === 'signal_strength') {
        fillColor = wifiService.getSignalStrengthColor(area.signalStrength?.average || -70);
      } else if (visualizationMode === 'quality') {
        const quality = area.quality?.overallScore || 50;
        fillColor = quality >= 80 ? '#4caf50' : 
                   quality >= 60 ? '#8bc34a' : 
                   quality >= 40 ? '#ffeb3b' : '#f44336';
      } else if (visualizationMode === 'capacity') {
        const capacity = area.eventSuitability?.recommendedMaxDevices || 50;
        fillColor = capacity >= 150 ? '#4caf50' : 
                   capacity >= 100 ? '#ffeb3b' : '#ff9800';
      }

      return (
        <g key={area._id}>
          <path
            d={pathData}
            fill={fillColor}
            fillOpacity={opacity}
            stroke={fillColor}
            strokeWidth="2"
            strokeOpacity={0.8}
            style={{ cursor: onCoverageClick ? 'pointer' : 'default' }}
            onClick={() => onCoverageClick && onCoverageClick(area)}
          />
          
          {/* Coverage area label */}
          <text
            x={coordinates.reduce((sum, coord) => sum + coord[0], 0) / coordinates.length * width / 100}
            y={coordinates.reduce((sum, coord) => sum + coord[1], 0) / coordinates.length * height / 100}
            textAnchor="middle"
            fontSize="12"
            fill="#333"
            fontWeight="bold"
            style={{ pointerEvents: 'none' }}
          >
            {area.name}
          </text>
        </g>
      );
    });
  };

  // Render access points
  const renderAccessPoints = () => {
    if (!showAccessPoints) return null;

    return filteredAccessPoints.map(ap => {
      const x = (ap.position?.x || 50) * width / 100;
      const y = (ap.position?.y || 50) * height / 100;
      const isConnected = ap.status?.state === 'connected';
      const clientCount = ap.wireless?.clientStats?.total || 0;
      
      // Get status color
      let statusColor = '#ccc';
      if (isConnected) {
        const health = ap.healthScore || 75;
        statusColor = health >= 90 ? '#4caf50' : 
                     health >= 75 ? '#8bc34a' : 
                     health >= 60 ? '#ffeb3b' : 
                     health >= 40 ? '#ff9800' : '#f44336';
      }

      // Show coverage range if enabled
      const showRange = visualizationMode === 'coverage_range';
      const range = ap.coverage?.estimatedRange?.radius5GHz || 50;

      return (
        <g key={ap._id}>
          {/* Coverage range circle */}
          {showRange && isConnected && (
            <circle
              cx={x}
              cy={y}
              r={range * Math.min(width, height) / 1000} // Scale range to map
              fill="none"
              stroke={statusColor}
              strokeWidth="1"
              strokeDasharray="5,5"
              opacity={0.5}
            />
          )}
          
          {/* Access point icon */}
          <circle
            cx={x}
            cy={y}
            r="20"
            fill="white"
            stroke={statusColor}
            strokeWidth="3"
            style={{ cursor: onAPClick ? 'pointer' : 'default' }}
            onClick={() => onAPClick && onAPClick(ap)}
          />
          
          {/* AP icon */}
          <g transform={`translate(${x-12}, ${y-12})`}>
            <APIcon sx={{ fontSize: 24, color: statusColor }} />
          </g>
          
          {/* Client count badge */}
          {clientCount > 0 && (
            <g>
              <circle
                cx={x + 15}
                cy={y - 15}
                r="10"
                fill="#2196f3"
                stroke="white"
                strokeWidth="2"
              />
              <text
                x={x + 15}
                y={y - 10}
                textAnchor="middle"
                fontSize="10"
                fill="white"
                fontWeight="bold"
              >
                {clientCount > 99 ? '99+' : clientCount}
              </text>
            </g>
          )}
          
          {/* AP label */}
          {showAPLabels && (
            <text
              x={x}
              y={y + 35}
              textAnchor="middle"
              fontSize="11"
              fill="#333"
              fontWeight="bold"
              style={{ pointerEvents: 'none' }}
            >
              {ap.deviceInfo?.name || `AP-${ap.deviceId?.slice(-6)}`}
            </text>
          )}
        </g>
      );
    });
  };

  // Render heatmap overlay
  const renderHeatmap = () => {
    if (!showHeatmap || heatmapData.length === 0) return null;

    return heatmapData.map((point, index) => (
      <circle
        key={index}
        cx={point.x}
        cy={point.y}
        r="15"
        fill={point.color}
        opacity={opacity * 0.7}
        style={{ mixBlendMode: 'multiply' }}
      />
    ));
  };

  // Render controls
  const renderControls = () => {
    if (!showControls) return null;

    return (
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          WiFi Coverage Controls
        </Typography>
        
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Visualization</InputLabel>
              <Select
                value={visualizationMode}
                onChange={(e) => setVisualizationMode(e.target.value)}
                label="Visualization"
              >
                <MenuItem value="signal_strength">Signal Strength</MenuItem>
                <MenuItem value="quality">Coverage Quality</MenuItem>
                <MenuItem value="capacity">Device Capacity</MenuItem>
                <MenuItem value="coverage_range">Coverage Range</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Frequency Band</InputLabel>
              <Select
                value={selectedBand}
                onChange={(e) => setSelectedBand(e.target.value)}
                label="Frequency Band"
              >
                <MenuItem value="all">All Bands</MenuItem>
                <MenuItem value="2.4GHz">2.4 GHz</MenuItem>
                <MenuItem value="5GHz">5 GHz</MenuItem>
                <MenuItem value="6GHz">6 GHz</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={showHeatmap}
                  onChange={(e) => setShowHeatmap(e.target.checked)}
                />
              }
              label="Heatmap"
            />
          </Grid>
          
          <Grid item xs={12} sm={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={showAPLabels}
                  onChange={(e) => setShowAPLabels(e.target.checked)}
                />
              }
              label="AP Labels"
            />
          </Grid>
          
          <Grid item xs={12} sm={2}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadWiFiData}
              disabled={loading}
              size="small"
            >
              Refresh
            </Button>
          </Grid>
          
          <Grid item xs={12}>
            <Typography gutterBottom>
              Opacity: {Math.round(opacity * 100)}%
            </Typography>
            <Slider
              value={opacity}
              onChange={(e, value) => setOpacity(value)}
              min={0.1}
              max={1}
              step={0.1}
              marks={[
                { value: 0.3, label: '30%' },
                { value: 0.6, label: '60%' },
                { value: 1, label: '100%' }
              ]}
            />
          </Grid>
        </Grid>
      </Paper>
    );
  };

  // Render statistics summary
  const renderStatistics = () => {
    const connectedAPs = accessPoints.filter(ap => ap.status?.state === 'connected');
    const totalClients = connectedAPs.reduce((sum, ap) => sum + (ap.wireless?.clientStats?.total || 0), 0);
    const avgSignalStrength = coverageAreas.length > 0 
      ? coverageAreas.reduce((sum, area) => sum + (area.signalStrength?.average || -70), 0) / coverageAreas.length
      : null;

    return (
      <Paper sx={{ p: 2, mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Network Statistics
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <APIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{connectedAPs.length}/{accessPoints.length}</Typography>
                <Typography variant="caption">Access Points</Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <WiFiIcon color="success" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{totalClients}</Typography>
                <Typography variant="caption">Connected Devices</Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                {getWiFiIcon(avgSignalStrength, 'large')}
                <Typography variant="h6">
                  {avgSignalStrength ? `${Math.round(avgSignalStrength)} dBm` : 'N/A'}
                </Typography>
                <Typography variant="caption">Avg Signal</Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center', py: 1 }}>
                <WiFiIcon color="info" sx={{ fontSize: 32, mb: 1 }} />
                <Typography variant="h6">{coverageAreas.length}</Typography>
                <Typography variant="caption">Coverage Areas</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        
        {/* Band distribution */}
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Frequency Bands:
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {wifiService.getFrequencyBands().map(band => {
              const count = accessPoints.filter(ap => 
                ap.wireless?.radios?.some(r => r.band === band.value && r.enabled)
              ).length;
              
              return (
                <Chip
                  key={band.value}
                  label={`${band.label}: ${count}`}
                  size="small"
                  sx={{ backgroundColor: band.color, color: 'white' }}
                />
              );
            })}
          </Box>
        </Box>
      </Paper>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          Loading WiFi data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {renderControls()}
      
      <Paper sx={{ position: 'relative', width, height, overflow: 'hidden' }}>
        {/* Background overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(245, 245, 245, 0.3)',
            zIndex: 0
          }}
        />
        
        {/* SVG overlay for WiFi visualization */}
        <svg
          width={width}
          height={height}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1
          }}
        >
          {/* Heatmap layer */}
          {renderHeatmap()}
          
          {/* Coverage areas layer */}
          {renderCoverageAreas()}
          
          {/* Access points layer */}
          {renderAccessPoints()}
        </svg>
        
        {/* Legend */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 10,
            right: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            p: 1,
            borderRadius: 1,
            zIndex: 3
          }}
        >
          <Typography variant="caption" display="block">
            WiFi Coverage Legend
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
            <StrongSignalIcon color="success" fontSize="small" />
            <Typography variant="caption">Excellent (&gt;-50dBm)</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <GoodSignalIcon color="primary" fontSize="small" />
            <Typography variant="caption">Good (-50 to -65dBm)</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FairSignalIcon color="warning" fontSize="small" />
            <Typography variant="caption">Fair (-65 to -75dBm)</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WeakSignalIcon color="error" fontSize="small" />
            <Typography variant="caption">Poor (&lt;-75dBm)</Typography>
          </Box>
        </Box>
        
        {/* Summary info */}
        <Box
          sx={{
            position: 'absolute',
            top: 10,
            left: 10,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            p: 1,
            borderRadius: 1,
            zIndex: 3
          }}
        >
          <Typography variant="caption">
            {accessPoints.length} APs • {coverageAreas.length} Coverage Areas
            {selectedBand !== 'all' && ` • ${selectedBand} Band`}
          </Typography>
        </Box>
      </Paper>
      
      {renderStatistics()}
    </Box>
  );
};

export default WiFiCoverageMap;