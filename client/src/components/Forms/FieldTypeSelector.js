import React, { useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>u,
  <PERSON>uItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Grid,
  Paper,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  TextFields as TextFieldsIcon,
  ShortText as ShortTextIcon,
  Subject as SubjectIcon,
  Numbers as NumbersIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarTodayIcon,
  AccessTime as AccessTimeIcon,
  CheckBox as CheckBoxIcon,
  RadioButtonChecked as RadioButtonCheckedIcon,
  ArrowDropDownCircle as ArrowDropDownCircleIcon,
  List as ListIcon,
  Upload as UploadIcon,
  Image as ImageIcon,
  Draw as DrawIcon,
  Person as PersonIcon,
  Home as HomeIcon,
  Star as StarIcon,
  LinearScale as LinearScaleIcon,
  Visibility as VisibilityIcon,
  Code as CodeIcon,
  HorizontalRule as HorizontalRuleIcon,
  ViewDay as ViewDayIcon
} from '@mui/icons-material';

/**
 * Field Type Selector Component
 * Allows users to select a field type to add to a form
 */
const FieldTypeSelector = ({ onSelectFieldType }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
    setSelectedCategory(null);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
    setSelectedCategory(null);
  };
  
  const handleCategorySelect = (category) => {
    setSelectedCategory(category);
  };
  
  const handleFieldTypeSelect = (fieldType) => {
    onSelectFieldType(fieldType);
    handleClose();
  };
  
  // Field type categories
  const categories = [
    {
      id: 'basic',
      name: 'Basic Fields',
      fields: [
        { type: 'text', label: 'Text', icon: <ShortTextIcon /> },
        { type: 'textarea', label: 'Paragraph Text', icon: <SubjectIcon /> },
        { type: 'number', label: 'Number', icon: <NumbersIcon /> },
        { type: 'email', label: 'Email', icon: <EmailIcon /> },
        { type: 'phone', label: 'Phone', icon: <PhoneIcon /> }
      ]
    },
    {
      id: 'date-time',
      name: 'Date & Time',
      fields: [
        { type: 'date', label: 'Date', icon: <CalendarTodayIcon /> },
        { type: 'time', label: 'Time', icon: <AccessTimeIcon /> },
        { type: 'datetime', label: 'Date & Time', icon: <CalendarTodayIcon /> }
      ]
    },
    {
      id: 'choice',
      name: 'Choice Fields',
      fields: [
        { type: 'select', label: 'Dropdown', icon: <ArrowDropDownCircleIcon /> },
        { type: 'multiselect', label: 'Multi-Select', icon: <ListIcon /> },
        { type: 'radio', label: 'Radio Buttons', icon: <RadioButtonCheckedIcon /> },
        { type: 'checkbox', label: 'Checkboxes', icon: <CheckBoxIcon /> }
      ]
    },
    {
      id: 'file',
      name: 'File Upload',
      fields: [
        { type: 'file', label: 'File Upload', icon: <UploadIcon /> },
        { type: 'image', label: 'Image Upload', icon: <ImageIcon /> },
        { type: 'signature', label: 'Signature', icon: <DrawIcon /> }
      ]
    },
    {
      id: 'complex',
      name: 'Complex Fields',
      fields: [
        { type: 'name', label: 'Name', icon: <PersonIcon /> },
        { type: 'address', label: 'Address', icon: <HomeIcon /> },
        { type: 'rating', label: 'Rating', icon: <StarIcon /> },
        { type: 'slider', label: 'Slider', icon: <LinearScaleIcon /> }
      ]
    },
    {
      id: 'layout',
      name: 'Layout Elements',
      fields: [
        { type: 'hidden', label: 'Hidden Field', icon: <VisibilityIcon /> },
        { type: 'html', label: 'HTML Content', icon: <CodeIcon /> },
        { type: 'divider', label: 'Divider', icon: <HorizontalRuleIcon /> },
        { type: 'page_break', label: 'Page Break', icon: <ViewDayIcon /> }
      ]
    }
  ];
  
  return (
    <>
      <Button
        variant="contained"
        color="primary"
        startIcon={<AddIcon />}
        onClick={handleClick}
      >
        Add Field
      </Button>
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          style: {
            width: selectedCategory ? 400 : 200,
            maxHeight: 500
          }
        }}
      >
        {!selectedCategory ? (
          // Show categories
          categories.map((category) => (
            <MenuItem
              key={category.id}
              onClick={() => handleCategorySelect(category)}
            >
              <ListItemText primary={category.name} />
            </MenuItem>
          ))
        ) : (
          // Show field types for selected category
          <div>
            <MenuItem onClick={() => setSelectedCategory(null)}>
              <Typography variant="subtitle2" color="primary">
                ← Back to Categories
              </Typography>
            </MenuItem>
            <Divider />
            <Typography variant="subtitle1" sx={{ p: 2, fontWeight: 'bold' }}>
              {selectedCategory.name}
            </Typography>
            <Grid container spacing={1} sx={{ p: 1 }}>
              {selectedCategory.fields.map((field) => (
                <Grid item xs={6} key={field.type}>
                  <Tooltip title={field.label} placement="top">
                    <Paper
                      elevation={2}
                      sx={{
                        p: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        cursor: 'pointer',
                        '&:hover': {
                          bgcolor: 'action.hover'
                        }
                      }}
                      onClick={() => handleFieldTypeSelect(field.type)}
                    >
                      <ListItemIcon sx={{ minWidth: 'auto', justifyContent: 'center' }}>
                        {field.icon}
                      </ListItemIcon>
                      <Typography variant="caption" align="center">
                        {field.label}
                      </Typography>
                    </Paper>
                  </Tooltip>
                </Grid>
              ))}
            </Grid>
          </div>
        )}
      </Menu>
    </>
  );
};

export default FieldTypeSelector;