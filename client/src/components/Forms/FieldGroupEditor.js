import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Divider,
  FormControlLabel,
  Grid,
  Switch,
  TextField,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import ConditionBuilder from './ConditionBuilder';

/**
 * Field Group Editor Component
 * Allows editing of field group properties and conditional display logic
 */
const FieldGroupEditor = ({ group, allGroups, allFields, onUpdate, onDelete }) => {
  const [expanded, setExpanded] = useState(false);
  
  // Handle group property change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    onUpdate({
      ...group,
      [name]: value
    });
  };
  
  // Handle conditional display change
  const handleConditionalDisplayChange = (enabled) => {
    onUpdate({
      ...group,
      conditionalDisplay: {
        ...group.conditionalDisplay,
        enabled
      }
    });
  };
  
  // Update conditions for conditional display
  const updateConditions = (conditions, logicType) => {
    onUpdate({
      ...group,
      conditionalDisplay: {
        ...group.conditionalDisplay,
        conditions,
        logicType
      }
    });
  };
  
  // Get all fields from other groups for conditions
  const getAvailableFields = () => {
    // If we don't have allGroups or allFields, return empty array
    if (!allGroups || !allFields) {
      return [];
    }
    
    // Get fields from other groups
    const otherGroupFields = allGroups
      .filter(g => g.title !== group.title) // Exclude current group
      .flatMap(g => g.fields);
    
    // If we don't have otherGroupFields, use allFields
    return otherGroupFields.length > 0 ? otherGroupFields : allFields;
  };
  
  return (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12} md={8}>
          <TextField
            fullWidth
            label="Group Title"
            name="title"
            value={group.title}
            onChange={handleChange}
            required
          />
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={onDelete}
            >
              Delete Group
            </Button>
          </Box>
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Group Description"
            name="description"
            value={group.description || ''}
            onChange={handleChange}
            multiline
            rows={2}
            helperText="Optional description for this group of fields"
          />
        </Grid>
      </Grid>
      
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded(!expanded)}
        sx={{ mt: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Conditional Display Settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <FormControlLabel
            control={
              <Switch
                checked={group.conditionalDisplay?.enabled || false}
                onChange={(e) => handleConditionalDisplayChange(e.target.checked)}
                color="primary"
              />
            }
            label="Enable Conditional Display"
          />
          
          {group.conditionalDisplay?.enabled && (
            <Box mt={2}>
              <Typography variant="body2" gutterBottom>
                This group will only be displayed when the following conditions are met:
              </Typography>
              
              <ConditionBuilder
                conditions={group.conditionalDisplay.conditions || []}
                logicType={group.conditionalDisplay.logicType || 'and'}
                availableFields={getAvailableFields()}
                onChange={updateConditions}
              />
              
              {(!allGroups || !allFields || getAvailableFields().length === 0) && (
                <Typography variant="caption" color="error" sx={{ display: 'block', mt: 1 }}>
                  Note: You need to add fields to other groups first to create conditions.
                </Typography>
              )}
            </Box>
          )}
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default FieldGroupEditor;