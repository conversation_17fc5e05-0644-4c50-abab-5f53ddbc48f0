import React, { useState } from 'react';
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  Tab,
  Tabs,
  TextField,
  Typography,
  Chip,
  OutlinedInput,
  Checkbox,
  ListItemText
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

/**
 * Form Settings Component
 * Allows configuration of form settings like permissions, notifications, and integrations
 */
const FormSettings = ({ form, onUpdate }) => {
  const [activeTab, setActiveTab] = useState(0);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle form property change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    onUpdate({
      ...form,
      [name]: value
    });
  };
  
  // Handle nested property change
  const handleNestedChange = (category, name, value) => {
    onUpdate({
      ...form,
      [category]: {
        ...form[category],
        [name]: value
      }
    });
  };
  
  // Handle permissions change
  const handlePermissionsChange = (field, value) => {
    onUpdate({
      ...form,
      permissions: {
        ...form.permissions,
        [field]: value
      }
    });
  };
  
  // Render general settings
  const renderGeneralSettings = () => (
    <Grid container spacing={2}>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth margin="normal">
          <InputLabel>Form Type</InputLabel>
          <Select
            value={form.formType || 'standard'}
            onChange={(e) => handleChange({ target: { name: 'formType', value: e.target.value } })}
            label="Form Type"
          >
            <MenuItem value="standard">Standard Form</MenuItem>
            <MenuItem value="multistep">Multi-step Form</MenuItem>
            <MenuItem value="survey">Survey</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth margin="normal">
          <InputLabel>Form Status</InputLabel>
          <Select
            value={form.status || 'draft'}
            onChange={(e) => handleChange({ target: { name: 'status', value: e.target.value } })}
            label="Form Status"
          >
            <MenuItem value="draft">Draft</MenuItem>
            <MenuItem value="published">Published</MenuItem>
            <MenuItem value="archived">Archived</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom>
          Submission Settings
        </Typography>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Success Message"
          name="successMessage"
          value={form.submission?.successMessage || 'Thank you for your submission!'}
          onChange={(e) => handleNestedChange('submission', 'successMessage', e.target.value)}
          multiline
          rows={2}
          margin="normal"
          helperText="Message to display after successful form submission"
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Redirect URL"
          name="redirectUrl"
          value={form.submission?.redirectUrl || ''}
          onChange={(e) => handleNestedChange('submission', 'redirectUrl', e.target.value)}
          margin="normal"
          helperText="Optional URL to redirect to after form submission"
        />
      </Grid>
    </Grid>
  );
  
  // Render styling settings
  const renderStylingSettings = () => (
    <Grid container spacing={2}>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth margin="normal">
          <InputLabel>Theme</InputLabel>
          <Select
            value={form.styling?.theme || 'default'}
            onChange={(e) => handleNestedChange('styling', 'theme', e.target.value)}
            label="Theme"
          >
            <MenuItem value="default">Default</MenuItem>
            <MenuItem value="light">Light</MenuItem>
            <MenuItem value="dark">Dark</MenuItem>
            <MenuItem value="colorful">Colorful</MenuItem>
            <MenuItem value="minimal">Minimal</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Primary Color"
          type="color"
          value={form.styling?.primaryColor || '#3f51b5'}
          onChange={(e) => handleNestedChange('styling', 'primaryColor', e.target.value)}
          margin="normal"
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Background Color"
          type="color"
          value={form.styling?.backgroundColor || '#ffffff'}
          onChange={(e) => handleNestedChange('styling', 'backgroundColor', e.target.value)}
          margin="normal"
          InputLabelProps={{ shrink: true }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Font Family"
          value={form.styling?.fontFamily || 'Roboto, sans-serif'}
          onChange={(e) => handleNestedChange('styling', 'fontFamily', e.target.value)}
          margin="normal"
          helperText="CSS font-family value (e.g., 'Arial, sans-serif')"
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Custom CSS"
          value={form.styling?.customCSS || ''}
          onChange={(e) => handleNestedChange('styling', 'customCSS', e.target.value)}
          margin="normal"
          multiline
          rows={4}
          helperText="Custom CSS to apply to the form"
        />
      </Grid>
    </Grid>
  );
  
  // Render permissions settings
  const renderPermissionsSettings = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <FormControl fullWidth margin="normal">
          <InputLabel>Access Level</InputLabel>
          <Select
            value={form.permissions?.access || 'authenticated'}
            onChange={(e) => handlePermissionsChange('access', e.target.value)}
            label="Access Level"
          >
            <MenuItem value="public">Public (Anyone can access)</MenuItem>
            <MenuItem value="authenticated">Authenticated (Any logged-in user)</MenuItem>
            <MenuItem value="specific_roles">Specific Roles</MenuItem>
            <MenuItem value="specific_users">Specific Users</MenuItem>
            <MenuItem value="specific_groups">Specific Groups</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      {form.permissions?.access === 'specific_roles' && (
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Access Roles"
            placeholder="Enter role names separated by commas"
            value={form.permissions?.accessRoles?.join(', ') || ''}
            onChange={(e) => {
              const roles = e.target.value.split(',').map(role => role.trim()).filter(Boolean);
              handlePermissionsChange('accessRoles', roles);
            }}
            margin="normal"
            helperText="Roles that can access this form"
          />
        </Grid>
      )}
      
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom>
          Management Permissions
        </Typography>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Management Roles"
          placeholder="Enter role names separated by commas"
          value={form.permissions?.manageRoles?.join(', ') || ''}
          onChange={(e) => {
            const roles = e.target.value.split(',').map(role => role.trim()).filter(Boolean);
            handlePermissionsChange('manageRoles', roles);
          }}
          margin="normal"
          helperText="Roles that can manage this form"
        />
      </Grid>
      
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom>
          Submission Viewing Permissions
        </Typography>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="View Submissions Roles"
          placeholder="Enter role names separated by commas"
          value={form.permissions?.viewSubmissionsRoles?.join(', ') || ''}
          onChange={(e) => {
            const roles = e.target.value.split(',').map(role => role.trim()).filter(Boolean);
            handlePermissionsChange('viewSubmissionsRoles', roles);
          }}
          margin="normal"
          helperText="Roles that can view submissions for this form"
        />
      </Grid>
    </Grid>
  );
  
  // Render integrations settings
  const renderIntegrationsSettings = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Ticket Creation
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={form.submission?.createTicket?.enabled || false}
              onChange={(e) => {
                const createTicket = {
                  ...(form.submission?.createTicket || {}),
                  enabled: e.target.checked
                };
                handleNestedChange('submission', 'createTicket', createTicket);
              }}
              color="primary"
            />
          }
          label="Create a ticket from form submissions"
        />
      </Grid>
      
      {form.submission?.createTicket?.enabled && (
        <>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Subject Field"
              value={form.submission?.createTicket?.fieldMappings?.subject || ''}
              onChange={(e) => {
                const fieldMappings = {
                  ...(form.submission?.createTicket?.fieldMappings || {}),
                  subject: e.target.value
                };
                const createTicket = {
                  ...(form.submission?.createTicket || {}),
                  fieldMappings
                };
                handleNestedChange('submission', 'createTicket', createTicket);
              }}
              margin="normal"
              helperText="Field ID to use for ticket subject"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Description Field"
              value={form.submission?.createTicket?.fieldMappings?.description || ''}
              onChange={(e) => {
                const fieldMappings = {
                  ...(form.submission?.createTicket?.fieldMappings || {}),
                  description: e.target.value
                };
                const createTicket = {
                  ...(form.submission?.createTicket || {}),
                  fieldMappings
                };
                handleNestedChange('submission', 'createTicket', createTicket);
              }}
              margin="normal"
              helperText="Field ID to use for ticket description"
            />
          </Grid>
        </>
      )}
      
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom>
          Task Creation
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={form.submission?.createTask?.enabled || false}
              onChange={(e) => {
                const createTask = {
                  ...(form.submission?.createTask || {}),
                  enabled: e.target.checked
                };
                handleNestedChange('submission', 'createTask', createTask);
              }}
              color="primary"
            />
          }
          label="Create a task from form submissions"
        />
      </Grid>
      
      {form.submission?.createTask?.enabled && (
        <>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Title Field"
              value={form.submission?.createTask?.fieldMappings?.title || ''}
              onChange={(e) => {
                const fieldMappings = {
                  ...(form.submission?.createTask?.fieldMappings || {}),
                  title: e.target.value
                };
                const createTask = {
                  ...(form.submission?.createTask || {}),
                  fieldMappings
                };
                handleNestedChange('submission', 'createTask', createTask);
              }}
              margin="normal"
              helperText="Field ID to use for task title"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Description Field"
              value={form.submission?.createTask?.fieldMappings?.description || ''}
              onChange={(e) => {
                const fieldMappings = {
                  ...(form.submission?.createTask?.fieldMappings || {}),
                  description: e.target.value
                };
                const createTask = {
                  ...(form.submission?.createTask || {}),
                  fieldMappings
                };
                handleNestedChange('submission', 'createTask', createTask);
              }}
              margin="normal"
              helperText="Field ID to use for task description"
            />
          </Grid>
        </>
      )}
    </Grid>
  );
  
  return (
    <Box>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
        variant="fullWidth"
        sx={{ mb: 2 }}
      >
        <Tab label="General" />
        <Tab label="Styling" />
        <Tab label="Permissions" />
        <Tab label="Integrations" />
      </Tabs>
      
      {activeTab === 0 && renderGeneralSettings()}
      {activeTab === 1 && renderStylingSettings()}
      {activeTab === 2 && renderPermissionsSettings()}
      {activeTab === 3 && renderIntegrationsSettings()}
    </Box>
  );
};

export default FormSettings;