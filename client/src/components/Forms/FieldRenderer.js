import React, { useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Radio,
  RadioGroup,
  Rating,
  Select,
  Slider,
  TextField,
  Typography
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled component for file upload
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

/**
 * Field Renderer Component
 * Renders a form field based on its type
 */
const FieldRenderer = ({ field, value, onChange, onFileUpload, error }) => {
  const [showPassword, setShowPassword] = useState(false);
  
  // Toggle password visibility
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  // Handle checkbox/multiselect change
  const handleMultiChange = (selectedValue) => {
    const currentValues = Array.isArray(value) ? value : [];
    
    if (currentValues.includes(selectedValue)) {
      onChange(currentValues.filter(v => v !== selectedValue));
    } else {
      onChange([...currentValues, selectedValue]);
    }
  };
  
  // Handle file change
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    onFileUpload(files);
    
    // Clear the input value so the same file can be selected again
    e.target.value = '';
  };
  
  // Calculate grid size based on field width
  const getGridSize = () => {
    switch (field.styling?.width) {
      case 'half': return 6;
      case 'third': return 4;
      case 'quarter': return 3;
      default: return 12;
    }
  };
  
  // Wrap field in grid item
  const FieldWrapper = ({ children }) => (
    <Grid item xs={12} md={getGridSize()} className={field.styling?.cssClass || ''}>
      {children}
    </Grid>
  );
  
  // Render field based on type
  switch (field.type) {
    case 'text':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            inputProps={{
              maxLength: field.validation?.maxLength,
              minLength: field.validation?.minLength,
              pattern: field.validation?.pattern
            }}
          />
        </FieldWrapper>
      );
      
    case 'textarea':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            multiline
            rows={4}
            inputProps={{
              maxLength: field.validation?.maxLength,
              minLength: field.validation?.minLength
            }}
          />
        </FieldWrapper>
      );
      
    case 'number':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type="number"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            InputProps={{
              inputProps: {
                min: field.options?.min,
                max: field.options?.max,
                step: field.options?.step || 1
              }
            }}
          />
        </FieldWrapper>
      );
      
    case 'email':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type="email"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            inputProps={{
              pattern: field.validation?.pattern || '[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$'
            }}
          />
        </FieldWrapper>
      );
      
    case 'phone':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type="tel"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            inputProps={{
              pattern: field.validation?.pattern
            }}
          />
        </FieldWrapper>
      );
      
    case 'password':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type={showPassword ? 'text' : 'password'}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || ''}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleTogglePasswordVisibility}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
        </FieldWrapper>
      );
      
    case 'date':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />
        </FieldWrapper>
      );
      
    case 'time':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type="time"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />
        </FieldWrapper>
      );
      
    case 'datetime':
      return (
        <FieldWrapper>
          <TextField
            fullWidth
            label={field.label}
            type="datetime-local"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            required={field.required}
            error={!!error}
            helperText={error || field.description || ''}
            variant="outlined"
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />
        </FieldWrapper>
      );
      
    case 'select':
      return (
        <FieldWrapper>
          <FormControl 
            fullWidth 
            variant="outlined" 
            margin="normal"
            error={!!error}
            required={field.required}
          >
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              label={field.label}
            >
              {field.options?.choices?.map((choice) => (
                <MenuItem key={choice.value} value={choice.value}>
                  {choice.label}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>{error || field.description || ''}</FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'multiselect':
      return (
        <FieldWrapper>
          <FormControl 
            fullWidth 
            variant="outlined" 
            margin="normal"
            error={!!error}
            required={field.required}
          >
            <InputLabel>{field.label}</InputLabel>
            <Select
              multiple
              value={Array.isArray(value) ? value : []}
              onChange={(e) => onChange(e.target.value)}
              input={<OutlinedInput label={field.label} />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const choice = field.options?.choices?.find(c => c.value === value);
                    return (
                      <Chip key={value} label={choice ? choice.label : value} />
                    );
                  })}
                </Box>
              )}
            >
              {field.options?.choices?.map((choice) => (
                <MenuItem key={choice.value} value={choice.value}>
                  <Checkbox checked={(Array.isArray(value) ? value : []).indexOf(choice.value) > -1} />
                  <ListItemText primary={choice.label} />
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>{error || field.description || ''}</FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'radio':
      return (
        <FieldWrapper>
          <FormControl 
            component="fieldset" 
            margin="normal"
            error={!!error}
            required={field.required}
            fullWidth
          >
            <FormLabel component="legend">{field.label}</FormLabel>
            <RadioGroup
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
            >
              {field.options?.choices?.map((choice) => (
                <FormControlLabel
                  key={choice.value}
                  value={choice.value}
                  control={<Radio />}
                  label={choice.label}
                />
              ))}
            </RadioGroup>
            <FormHelperText>{error || field.description || ''}</FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'checkbox':
      return (
        <FieldWrapper>
          <FormControl 
            component="fieldset" 
            margin="normal"
            error={!!error}
            required={field.required}
            fullWidth
          >
            <FormLabel component="legend">{field.label}</FormLabel>
            <FormGroup>
              {field.options?.choices?.map((choice) => (
                <FormControlLabel
                  key={choice.value}
                  control={
                    <Checkbox
                      checked={Array.isArray(value) && value.includes(choice.value)}
                      onChange={() => handleMultiChange(choice.value)}
                    />
                  }
                  label={choice.label}
                />
              ))}
            </FormGroup>
            <FormHelperText>{error || field.description || ''}</FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'file':
    case 'image':
      return (
        <FieldWrapper>
          <FormControl 
            fullWidth 
            margin="normal"
            error={!!error}
            required={field.required}
          >
            <FormLabel>{field.label}</FormLabel>
            <Box sx={{ mt: 1 }}>
              <Button
                component="label"
                variant="outlined"
                startIcon={<CloudUploadIcon />}
              >
                Upload {field.type === 'image' ? 'Image' : 'File'}
                <VisuallyHiddenInput 
                  type="file" 
                  onChange={handleFileChange}
                  multiple={field.options?.maxFiles > 1}
                  accept={field.options?.allowedFileTypes?.join(',')}
                />
              </Button>
            </Box>
            <FormHelperText>
              {error || field.description || ''}
              {field.options?.allowedFileTypes?.length > 0 && (
                <span>
                  {' '}Allowed types: {field.options.allowedFileTypes.join(', ')}
                </span>
              )}
              {field.options?.maxFileSize && (
                <span>
                  {' '}Max size: {(field.options.maxFileSize / 1024 / 1024).toFixed(2)} MB
                </span>
              )}
            </FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'slider':
      return (
        <FieldWrapper>
          <FormControl 
            fullWidth 
            margin="normal"
            error={!!error}
            required={field.required}
          >
            <FormLabel>{field.label}</FormLabel>
            <Box sx={{ px: 2, pt: 2, pb: 1 }}>
              <Slider
                value={typeof value === 'number' ? value : 0}
                onChange={(e, newValue) => onChange(newValue)}
                min={field.options?.min || 0}
                max={field.options?.max || 100}
                step={field.options?.step || 1}
                valueLabelDisplay="auto"
                marks={[
                  { value: field.options?.min || 0, label: field.options?.min || 0 },
                  { value: field.options?.max || 100, label: field.options?.max || 100 }
                ]}
              />
            </Box>
            <FormHelperText>{error || field.description || ''}</FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'rating':
      return (
        <FieldWrapper>
          <FormControl 
            fullWidth 
            margin="normal"
            error={!!error}
            required={field.required}
          >
            <FormLabel>{field.label}</FormLabel>
            <Box sx={{ pt: 1 }}>
              <Rating
                value={typeof value === 'number' ? value : 0}
                onChange={(e, newValue) => onChange(newValue)}
                max={field.options?.max || 5}
                precision={field.options?.step || 1}
              />
            </Box>
            <FormHelperText>{error || field.description || ''}</FormHelperText>
          </FormControl>
        </FieldWrapper>
      );
      
    case 'html':
      return (
        <FieldWrapper>
          <Box 
            sx={{ mt: 2, mb: 2 }}
            dangerouslySetInnerHTML={{ __html: field.options?.htmlContent || '' }}
          />
        </FieldWrapper>
      );
      
    case 'divider':
      return (
        <Grid item xs={12}>
          <Box sx={{ my: 2 }}>
            <hr />
          </Box>
        </Grid>
      );
      
    case 'hidden':
      return (
        <input 
          type="hidden" 
          name={field.fieldId} 
          value={value || ''} 
        />
      );
      
    default:
      return (
        <FieldWrapper>
          <Typography color="error">
            Unknown field type: {field.type}
          </Typography>
        </FieldWrapper>
      );
  }
};

export default FieldRenderer;