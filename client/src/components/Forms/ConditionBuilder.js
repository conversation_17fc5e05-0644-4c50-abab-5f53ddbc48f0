import React from 'react';
import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Typography
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';

/**
 * Condition Builder Component
 * Allows building conditional logic for fields and field groups
 */
const ConditionBuilder = ({ conditions, logicType, availableFields, onChange }) => {
  // Add a new condition
  const addCondition = () => {
    const newCondition = {
      fieldId: availableFields.length > 0 ? availableFields[0].fieldId : '',
      operator: 'equals',
      value: ''
    };
    
    onChange([...conditions, newCondition], logicType);
  };
  
  // Remove a condition
  const removeCondition = (index) => {
    const updatedConditions = conditions.filter((_, i) => i !== index);
    onChange(updatedConditions, logicType);
  };
  
  // Update a condition
  const updateCondition = (index, field, value) => {
    const updatedConditions = [...conditions];
    updatedConditions[index] = {
      ...updatedConditions[index],
      [field]: value
    };
    
    onChange(updatedConditions, logicType);
  };
  
  // Change logic type (AND/OR)
  const handleLogicTypeChange = (event) => {
    onChange(conditions, event.target.value);
  };
  
  // Get field type by fieldId
  const getFieldType = (fieldId) => {
    const field = availableFields.find(f => f.fieldId === fieldId);
    return field ? field.type : 'text';
  };
  
  // Render operator options based on field type
  const renderOperatorOptions = (fieldType) => {
    const commonOperators = [
      { value: 'equals', label: 'Equals' },
      { value: 'not_equals', label: 'Not Equals' },
      { value: 'is_empty', label: 'Is Empty' },
      { value: 'is_not_empty', label: 'Is Not Empty' }
    ];
    
    const textOperators = [
      { value: 'contains', label: 'Contains' },
      { value: 'not_contains', label: 'Does Not Contain' },
      { value: 'starts_with', label: 'Starts With' },
      { value: 'ends_with', label: 'Ends With' }
    ];
    
    const numberOperators = [
      { value: 'greater_than', label: 'Greater Than' },
      { value: 'less_than', label: 'Less Than' }
    ];
    
    switch (fieldType) {
      case 'text':
      case 'textarea':
      case 'email':
      case 'phone':
      case 'name':
      case 'address':
        return [...commonOperators, ...textOperators];
      case 'number':
      case 'slider':
      case 'rating':
        return [...commonOperators, ...numberOperators];
      case 'date':
      case 'time':
      case 'datetime':
        return [...commonOperators, ...numberOperators];
      default:
        return commonOperators;
    }
  };
  
  // Render value input based on field type and operator
  const renderValueInput = (condition, index) => {
    const { fieldId, operator, value } = condition;
    
    // No value needed for these operators
    if (['is_empty', 'is_not_empty'].includes(operator)) {
      return null;
    }
    
    const fieldType = getFieldType(fieldId);
    const field = availableFields.find(f => f.fieldId === fieldId);
    
    switch (fieldType) {
      case 'select':
      case 'radio':
        return (
          <FormControl fullWidth>
            <InputLabel>Value</InputLabel>
            <Select
              value={value}
              onChange={(e) => updateCondition(index, 'value', e.target.value)}
              label="Value"
            >
              {field?.options?.choices?.map((choice) => (
                <MenuItem key={choice.value} value={choice.value}>
                  {choice.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      
      case 'checkbox':
      case 'multiselect':
        return (
          <FormControl fullWidth>
            <InputLabel>Value</InputLabel>
            <Select
              value={value}
              onChange={(e) => updateCondition(index, 'value', e.target.value)}
              label="Value"
            >
              {field?.options?.choices?.map((choice) => (
                <MenuItem key={choice.value} value={choice.value}>
                  {choice.label}
                </MenuItem>
              ))}
            </Select>
            <Typography variant="caption" color="textSecondary">
              For multi-select fields, the condition will match if any selected option matches.
            </Typography>
          </FormControl>
        );
      
      case 'number':
      case 'slider':
      case 'rating':
        return (
          <TextField
            fullWidth
            label="Value"
            type="number"
            value={value}
            onChange={(e) => updateCondition(index, 'value', e.target.value)}
          />
        );
      
      case 'date':
        return (
          <TextField
            fullWidth
            label="Value"
            type="date"
            value={value}
            onChange={(e) => updateCondition(index, 'value', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        );
      
      case 'time':
        return (
          <TextField
            fullWidth
            label="Value"
            type="time"
            value={value}
            onChange={(e) => updateCondition(index, 'value', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        );
      
      case 'datetime':
        return (
          <TextField
            fullWidth
            label="Value"
            type="datetime-local"
            value={value}
            onChange={(e) => updateCondition(index, 'value', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        );
      
      default:
        return (
          <TextField
            fullWidth
            label="Value"
            value={value}
            onChange={(e) => updateCondition(index, 'value', e.target.value)}
          />
        );
    }
  };
  
  return (
    <Box>
      {/* Logic Type Selector (AND/OR) */}
      {conditions.length > 1 && (
        <Box mb={2}>
          <Typography variant="subtitle2" gutterBottom>
            Match Type:
          </Typography>
          <RadioGroup
            row
            value={logicType}
            onChange={handleLogicTypeChange}
          >
            <FormControlLabel
              value="and"
              control={<Radio />}
              label="Match ALL conditions (AND)"
            />
            <FormControlLabel
              value="or"
              control={<Radio />}
              label="Match ANY condition (OR)"
            />
          </RadioGroup>
        </Box>
      )}
      
      {/* Conditions */}
      {conditions.length === 0 ? (
        <Typography variant="body2" color="textSecondary" gutterBottom>
          No conditions added yet. Add a condition to start building your logic.
        </Typography>
      ) : (
        conditions.map((condition, index) => (
          <Box
            key={index}
            sx={{
              p: 2,
              mb: 2,
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1
            }}
          >
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel>Field</InputLabel>
                  <Select
                    value={condition.fieldId}
                    onChange={(e) => updateCondition(index, 'fieldId', e.target.value)}
                    label="Field"
                  >
                    {availableFields.map((field) => (
                      <MenuItem key={field.fieldId} value={field.fieldId}>
                        {field.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <InputLabel>Operator</InputLabel>
                  <Select
                    value={condition.operator}
                    onChange={(e) => updateCondition(index, 'operator', e.target.value)}
                    label="Operator"
                  >
                    {renderOperatorOptions(getFieldType(condition.fieldId)).map((op) => (
                      <MenuItem key={op.value} value={op.value}>
                        {op.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                {renderValueInput(condition, index)}
              </Grid>
              
              <Grid item xs={12} sm={1}>
                <IconButton
                  color="error"
                  onClick={() => removeCondition(index)}
                >
                  <DeleteIcon />
                </IconButton>
              </Grid>
            </Grid>
          </Box>
        ))
      )}
      
      {/* Add Condition Button */}
      <Button
        startIcon={<AddIcon />}
        onClick={addCondition}
        variant="outlined"
        disabled={availableFields.length === 0}
      >
        Add Condition
      </Button>
      
      {availableFields.length === 0 && (
        <Typography variant="caption" color="error" sx={{ display: 'block', mt: 1 }}>
          No fields available for conditions. Add other fields to the form first.
        </Typography>
      )}
    </Box>
  );
};

export default ConditionBuilder;