import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Radio,
  RadioGroup,
  Rating,
  Select,
  Slider,
  Step,
  StepLabel,
  Stepper,
  Switch,
  TextField,
  Typography,
  Checkbox,
  OutlinedInput,
  Chip,
  ListItemText,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon,
  Send as SendIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Styled component for file upload
const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

/**
 * Form Preview Component
 * Renders a preview of the form as it would appear to users
 */
const FormPreview = ({ form }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [showPassword, setShowPassword] = useState({});
  const [errors, setErrors] = useState({});
  
  // Initialize form data with default values
  useEffect(() => {
    const initialData = {};
    
    form.fieldGroups.forEach(group => {
      group.fields.forEach(field => {
        if (field.defaultValue !== undefined && field.defaultValue !== null) {
          initialData[field.fieldId] = field.defaultValue;
        } else if (['checkbox', 'multiselect'].includes(field.type)) {
          initialData[field.fieldId] = [];
        } else if (field.type === 'rating') {
          initialData[field.fieldId] = 0;
        } else {
          initialData[field.fieldId] = '';
        }
      });
    });
    
    setFormData(initialData);
  }, [form]);
  
  // Handle field value change
  const handleChange = (fieldId, value) => {
    setFormData(prevData => ({
      ...prevData,
      [fieldId]: value
    }));
    
    // Clear error for this field
    if (errors[fieldId]) {
      setErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors[fieldId];
        return newErrors;
      });
    }
  };
  
  // Handle checkbox/multiselect change
  const handleMultiChange = (fieldId, value) => {
    setFormData(prevData => {
      const currentValues = Array.isArray(prevData[fieldId]) ? prevData[fieldId] : [];
      
      if (currentValues.includes(value)) {
        return {
          ...prevData,
          [fieldId]: currentValues.filter(v => v !== value)
        };
      } else {
        return {
          ...prevData,
          [fieldId]: [...currentValues, value]
        };
      }
    });
  };
  
  // Toggle password visibility
  const handleTogglePasswordVisibility = (fieldId) => {
    setShowPassword(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };
  
  // Handle next step in multistep form
  const handleNext = () => {
    // Validate current step
    const currentGroupFields = form.fieldGroups[activeStep].fields;
    const newErrors = {};
    
    currentGroupFields.forEach(field => {
      if (field.required) {
        const value = formData[field.fieldId];
        if (value === undefined || value === null || value === '' || 
            (Array.isArray(value) && value.length === 0)) {
          newErrors[field.fieldId] = `${field.label} is required`;
        }
      }
    });
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setActiveStep(prevStep => prevStep + 1);
  };
  
  // Handle previous step in multistep form
  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors = {};
    
    form.fieldGroups.forEach(group => {
      group.fields.forEach(field => {
        if (field.required) {
          const value = formData[field.fieldId];
          if (value === undefined || value === null || value === '' || 
              (Array.isArray(value) && value.length === 0)) {
            newErrors[field.fieldId] = `${field.label} is required`;
          }
        }
      });
    });
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    // In a real implementation, this would submit the form data
    console.log('Form submitted:', formData);
    alert('Form submitted successfully! (This is just a preview)');
  };
  
  // Check if a field or group should be displayed based on conditional logic
  const shouldDisplay = (conditionalDisplay) => {
    if (!conditionalDisplay || !conditionalDisplay.enabled) {
      return true;
    }
    
    const { conditions, logicType } = conditionalDisplay;
    
    if (!conditions || conditions.length === 0) {
      return true;
    }
    
    const results = conditions.map(condition => {
      const { fieldId, operator, value } = condition;
      const fieldValue = formData[fieldId];
      
      switch (operator) {
        case 'equals':
          return fieldValue === value;
        case 'not_equals':
          return fieldValue !== value;
        case 'contains':
          return String(fieldValue).includes(value);
        case 'not_contains':
          return !String(fieldValue).includes(value);
        case 'greater_than':
          return Number(fieldValue) > Number(value);
        case 'less_than':
          return Number(fieldValue) < Number(value);
        case 'starts_with':
          return String(fieldValue).startsWith(value);
        case 'ends_with':
          return String(fieldValue).endsWith(value);
        case 'is_empty':
          return fieldValue === '' || fieldValue === null || fieldValue === undefined || 
                 (Array.isArray(fieldValue) && fieldValue.length === 0);
        case 'is_not_empty':
          return fieldValue !== '' && fieldValue !== null && fieldValue !== undefined && 
                 (!Array.isArray(fieldValue) || fieldValue.length > 0);
        default:
          return true;
      }
    });
    
    return logicType === 'and' 
      ? results.every(result => result) 
      : results.some(result => result);
  };
  
  // Render a field based on its type
  const renderField = (field) => {
    if (!shouldDisplay(field.conditionalDisplay)) {
      return null;
    }
    
    const fieldId = field.fieldId;
    const value = formData[fieldId] !== undefined ? formData[fieldId] : '';
    const error = errors[fieldId];
    
    // Calculate grid size based on field width
    const getGridSize = () => {
      switch (field.styling?.width) {
        case 'half': return 6;
        case 'third': return 4;
        case 'quarter': return 3;
        default: return 12;
      }
    };
    
    // Wrap field in grid item
    const FieldWrapper = ({ children }) => (
      <Grid item xs={12} md={getGridSize()}>
        {children}
      </Grid>
    );
    
    switch (field.type) {
      case 'text':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              placeholder={field.placeholder || ''}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
            />
          </FieldWrapper>
        );
        
      case 'textarea':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              placeholder={field.placeholder || ''}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
              multiline
              rows={4}
            />
          </FieldWrapper>
        );
        
      case 'number':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type="number"
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              placeholder={field.placeholder || ''}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
              InputProps={{
                inputProps: {
                  min: field.options?.min,
                  max: field.options?.max,
                  step: field.options?.step || 1
                }
              }}
            />
          </FieldWrapper>
        );
        
      case 'email':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type="email"
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              placeholder={field.placeholder || ''}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
            />
          </FieldWrapper>
        );
        
      case 'phone':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type="tel"
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              placeholder={field.placeholder || ''}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
            />
          </FieldWrapper>
        );
        
      case 'password':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type={showPassword[fieldId] ? 'text' : 'password'}
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              placeholder={field.placeholder || ''}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => handleTogglePasswordVisibility(fieldId)}
                      edge="end"
                    >
                      {showPassword[fieldId] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </FieldWrapper>
        );
        
      case 'date':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type="date"
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
              InputLabelProps={{ shrink: true }}
            />
          </FieldWrapper>
        );
        
      case 'time':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type="time"
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
              InputLabelProps={{ shrink: true }}
            />
          </FieldWrapper>
        );
        
      case 'datetime':
        return (
          <FieldWrapper>
            <TextField
              fullWidth
              label={field.label}
              type="datetime-local"
              value={value}
              onChange={(e) => handleChange(fieldId, e.target.value)}
              required={field.required}
              error={!!error}
              helperText={error || field.description || ''}
              variant="outlined"
              margin="normal"
              InputLabelProps={{ shrink: true }}
            />
          </FieldWrapper>
        );
        
      case 'select':
        return (
          <FieldWrapper>
            <FormControl 
              fullWidth 
              variant="outlined" 
              margin="normal"
              error={!!error}
              required={field.required}
            >
              <InputLabel>{field.label}</InputLabel>
              <Select
                value={value}
                onChange={(e) => handleChange(fieldId, e.target.value)}
                label={field.label}
              >
                {field.options?.choices?.map((choice) => (
                  <MenuItem key={choice.value} value={choice.value}>
                    {choice.label}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>{error || field.description || ''}</FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'multiselect':
        return (
          <FieldWrapper>
            <FormControl 
              fullWidth 
              variant="outlined" 
              margin="normal"
              error={!!error}
              required={field.required}
            >
              <InputLabel>{field.label}</InputLabel>
              <Select
                multiple
                value={Array.isArray(value) ? value : []}
                onChange={(e) => handleChange(fieldId, e.target.value)}
                input={<OutlinedInput label={field.label} />}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => {
                      const choice = field.options?.choices?.find(c => c.value === value);
                      return (
                        <Chip key={value} label={choice ? choice.label : value} />
                      );
                    })}
                  </Box>
                )}
              >
                {field.options?.choices?.map((choice) => (
                  <MenuItem key={choice.value} value={choice.value}>
                    <Checkbox checked={(Array.isArray(value) ? value : []).indexOf(choice.value) > -1} />
                    <ListItemText primary={choice.label} />
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>{error || field.description || ''}</FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'radio':
        return (
          <FieldWrapper>
            <FormControl 
              component="fieldset" 
              margin="normal"
              error={!!error}
              required={field.required}
              fullWidth
            >
              <FormLabel component="legend">{field.label}</FormLabel>
              <RadioGroup
                value={value}
                onChange={(e) => handleChange(fieldId, e.target.value)}
              >
                {field.options?.choices?.map((choice) => (
                  <FormControlLabel
                    key={choice.value}
                    value={choice.value}
                    control={<Radio />}
                    label={choice.label}
                  />
                ))}
              </RadioGroup>
              <FormHelperText>{error || field.description || ''}</FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'checkbox':
        return (
          <FieldWrapper>
            <FormControl 
              component="fieldset" 
              margin="normal"
              error={!!error}
              required={field.required}
              fullWidth
            >
              <FormLabel component="legend">{field.label}</FormLabel>
              <FormGroup>
                {field.options?.choices?.map((choice) => (
                  <FormControlLabel
                    key={choice.value}
                    control={
                      <Checkbox
                        checked={Array.isArray(value) && value.includes(choice.value)}
                        onChange={() => handleMultiChange(fieldId, choice.value)}
                      />
                    }
                    label={choice.label}
                  />
                ))}
              </FormGroup>
              <FormHelperText>{error || field.description || ''}</FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'file':
      case 'image':
        return (
          <FieldWrapper>
            <FormControl 
              fullWidth 
              margin="normal"
              error={!!error}
              required={field.required}
            >
              <FormLabel>{field.label}</FormLabel>
              <Box sx={{ mt: 1 }}>
                <Button
                  component="label"
                  variant="outlined"
                  startIcon={<CloudUploadIcon />}
                >
                  Upload {field.type === 'image' ? 'Image' : 'File'}
                  <VisuallyHiddenInput type="file" />
                </Button>
              </Box>
              <FormHelperText>
                {error || field.description || ''}
                {field.options?.allowedFileTypes?.length > 0 && (
                  <span>
                    {' '}Allowed types: {field.options.allowedFileTypes.join(', ')}
                  </span>
                )}
                {field.options?.maxFileSize && (
                  <span>
                    {' '}Max size: {(field.options.maxFileSize / 1024 / 1024).toFixed(2)} MB
                  </span>
                )}
              </FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'slider':
        return (
          <FieldWrapper>
            <FormControl 
              fullWidth 
              margin="normal"
              error={!!error}
              required={field.required}
            >
              <FormLabel>{field.label}</FormLabel>
              <Box sx={{ px: 2, pt: 2, pb: 1 }}>
                <Slider
                  value={typeof value === 'number' ? value : 0}
                  onChange={(e, newValue) => handleChange(fieldId, newValue)}
                  min={field.options?.min || 0}
                  max={field.options?.max || 100}
                  step={field.options?.step || 1}
                  valueLabelDisplay="auto"
                  marks={[
                    { value: field.options?.min || 0, label: field.options?.min || 0 },
                    { value: field.options?.max || 100, label: field.options?.max || 100 }
                  ]}
                />
              </Box>
              <FormHelperText>{error || field.description || ''}</FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'rating':
        return (
          <FieldWrapper>
            <FormControl 
              fullWidth 
              margin="normal"
              error={!!error}
              required={field.required}
            >
              <FormLabel>{field.label}</FormLabel>
              <Box sx={{ pt: 1 }}>
                <Rating
                  value={typeof value === 'number' ? value : 0}
                  onChange={(e, newValue) => handleChange(fieldId, newValue)}
                  max={field.options?.max || 5}
                  precision={field.options?.step || 1}
                />
              </Box>
              <FormHelperText>{error || field.description || ''}</FormHelperText>
            </FormControl>
          </FieldWrapper>
        );
        
      case 'html':
        return (
          <FieldWrapper>
            <Box 
              sx={{ mt: 2, mb: 2 }}
              dangerouslySetInnerHTML={{ __html: field.options?.htmlContent || '' }}
            />
          </FieldWrapper>
        );
        
      case 'divider':
        return (
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
          </Grid>
        );
        
      default:
        return null;
    }
  };
  
  // Render a group of fields
  const renderGroup = (group, index) => {
    if (!shouldDisplay(group.conditionalDisplay)) {
      return null;
    }
    
    return (
      <Box key={index} sx={{ mb: 4 }}>
        {group.title && (
          <Typography variant="h6" gutterBottom>
            {group.title}
          </Typography>
        )}
        
        {group.description && (
          <Typography variant="body2" color="textSecondary" paragraph>
            {group.description}
          </Typography>
        )}
        
        <Grid container spacing={2}>
          {group.fields.map((field) => (
            <React.Fragment key={field.fieldId}>
              {renderField(field)}
            </React.Fragment>
          ))}
        </Grid>
      </Box>
    );
  };
  
  // Determine if the form is multistep
  const isMultistep = form.formType === 'multistep';
  
  // Apply form styling
  const formStyle = {
    backgroundColor: form.styling?.backgroundColor || '#ffffff',
    fontFamily: form.styling?.fontFamily || 'Roboto, sans-serif',
    color: form.styling?.primaryColor || '#3f51b5'
  };
  
  return (
    <Box sx={{ ...formStyle, p: 2 }}>
      <Typography variant="h5" gutterBottom align="center" sx={{ mb: 3 }}>
        {form.title}
      </Typography>
      
      {form.description && (
        <Typography variant="body1" paragraph align="center" sx={{ mb: 4 }}>
          {form.description}
        </Typography>
      )}
      
      {isMultistep && (
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {form.fieldGroups.map((group, index) => (
            <Step key={index}>
              <StepLabel>{group.title}</StepLabel>
            </Step>
          ))}
        </Stepper>
      )}
      
      <form onSubmit={handleSubmit}>
        <Paper elevation={2} sx={{ p: 3 }}>
          {isMultistep ? (
            // Render only the active group for multistep forms
            renderGroup(form.fieldGroups[activeStep], activeStep)
          ) : (
            // Render all groups for standard forms
            form.fieldGroups.map((group, index) => renderGroup(group, index))
          )}
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            {isMultistep && (
              <>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  startIcon={<NavigateBeforeIcon />}
                >
                  Back
                </Button>
                
                {activeStep < form.fieldGroups.length - 1 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleNext}
                    endIcon={<NavigateNextIcon />}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    endIcon={<SendIcon />}
                  >
                    Submit
                  </Button>
                )}
              </>
            )}
            
            {!isMultistep && (
              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                endIcon={<SendIcon />}
              >
                Submit
              </Button>
            )}
          </Box>
        </Paper>
      </form>
    </Box>
  );
};

export default FormPreview;