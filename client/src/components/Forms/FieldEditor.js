import React, { useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  Tab,
  Tabs,
  TextField,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import ConditionBuilder from './ConditionBuilder';

/**
 * Field Editor Component
 * Allows editing of field properties, validation, and conditional logic
 */
const FieldEditor = ({ field, allFields, onUpdate }) => {
  const [activeTab, setActiveTab] = useState(0);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle field property change
  const handleChange = (e) => {
    const { name, value, checked, type } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    
    onUpdate({
      ...field,
      [name]: newValue
    });
  };
  
  // Handle nested property change
  const handleNestedChange = (category, name, value) => {
    onUpdate({
      ...field,
      [category]: {
        ...field[category],
        [name]: value
      }
    });
  };
  
  // Handle options change for select, radio, checkbox fields
  const handleChoiceChange = (index, key, value) => {
    const updatedChoices = [...field.options.choices];
    updatedChoices[index] = {
      ...updatedChoices[index],
      [key]: value
    };
    
    onUpdate({
      ...field,
      options: {
        ...field.options,
        choices: updatedChoices
      }
    });
  };
  
  // Add a new choice option
  const addChoice = () => {
    const newChoice = {
      label: `Option ${field.options.choices.length + 1}`,
      value: `option_${field.options.choices.length + 1}`
    };
    
    onUpdate({
      ...field,
      options: {
        ...field.options,
        choices: [...field.options.choices, newChoice]
      }
    });
  };
  
  // Remove a choice option
  const removeChoice = (index) => {
    const updatedChoices = field.options.choices.filter((_, i) => i !== index);
    
    onUpdate({
      ...field,
      options: {
        ...field.options,
        choices: updatedChoices
      }
    });
  };
  
  // Handle conditional display change
  const handleConditionalDisplayChange = (enabled) => {
    onUpdate({
      ...field,
      conditionalDisplay: {
        ...field.conditionalDisplay,
        enabled
      }
    });
  };
  
  // Update conditions for conditional display
  const updateConditions = (conditions, logicType) => {
    onUpdate({
      ...field,
      conditionalDisplay: {
        ...field.conditionalDisplay,
        conditions,
        logicType
      }
    });
  };
  
  // Render basic field properties
  const renderBasicProperties = () => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Field Label"
          name="label"
          value={field.label}
          onChange={handleChange}
          required
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Field ID"
          name="fieldId"
          value={field.fieldId}
          onChange={handleChange}
          disabled
          helperText="Unique identifier for this field (auto-generated)"
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          name="description"
          value={field.description}
          onChange={handleChange}
          multiline
          rows={2}
          helperText="Optional help text to display below the field"
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Placeholder"
          name="placeholder"
          value={field.placeholder || ''}
          onChange={handleChange}
          helperText="Text to display when the field is empty"
        />
      </Grid>
      
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={field.required}
              onChange={handleChange}
              name="required"
              color="primary"
            />
          }
          label="Required Field"
        />
      </Grid>
      
      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>Field Width</InputLabel>
          <Select
            value={field.styling?.width || 'full'}
            onChange={(e) => handleNestedChange('styling', 'width', e.target.value)}
            label="Field Width"
          >
            <MenuItem value="full">Full Width</MenuItem>
            <MenuItem value="half">Half Width</MenuItem>
            <MenuItem value="third">One Third</MenuItem>
            <MenuItem value="quarter">One Quarter</MenuItem>
          </Select>
          <FormHelperText>Controls how much horizontal space this field takes</FormHelperText>
        </FormControl>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="CSS Class"
          value={field.styling?.cssClass || ''}
          onChange={(e) => handleNestedChange('styling', 'cssClass', e.target.value)}
          helperText="Optional CSS class to apply to this field"
        />
      </Grid>
    </Grid>
  );
  
  // Render field-specific options
  const renderFieldOptions = () => {
    switch (field.type) {
      case 'select':
      case 'multiselect':
      case 'radio':
      case 'checkbox':
        return (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Choices
            </Typography>
            {field.options?.choices?.map((choice, index) => (
              <Grid container spacing={2} key={index} alignItems="center" sx={{ mb: 1 }}>
                <Grid item xs={5}>
                  <TextField
                    fullWidth
                    label="Label"
                    value={choice.label}
                    onChange={(e) => handleChoiceChange(index, 'label', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={5}>
                  <TextField
                    fullWidth
                    label="Value"
                    value={choice.value}
                    onChange={(e) => handleChoiceChange(index, 'value', e.target.value)}
                    size="small"
                  />
                </Grid>
                <Grid item xs={2}>
                  <IconButton
                    color="error"
                    onClick={() => removeChoice(index)}
                    disabled={field.options.choices.length <= 1}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Grid>
              </Grid>
            ))}
            <Button
              startIcon={<AddIcon />}
              onClick={addChoice}
              variant="outlined"
              size="small"
              sx={{ mt: 1 }}
            >
              Add Choice
            </Button>
          </Box>
        );
        
      case 'number':
      case 'slider':
        return (
          <Grid container spacing={2}>
            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Min Value"
                type="number"
                value={field.options?.min || 0}
                onChange={(e) => handleNestedChange('options', 'min', Number(e.target.value))}
                size="small"
              />
            </Grid>
            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Max Value"
                type="number"
                value={field.options?.max || 100}
                onChange={(e) => handleNestedChange('options', 'max', Number(e.target.value))}
                size="small"
              />
            </Grid>
            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Step"
                type="number"
                value={field.options?.step || 1}
                onChange={(e) => handleNestedChange('options', 'step', Number(e.target.value))}
                size="small"
              />
            </Grid>
          </Grid>
        );
        
      case 'file':
      case 'image':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Max File Size (bytes)"
                type="number"
                value={field.options?.maxFileSize || 5242880}
                onChange={(e) => handleNestedChange('options', 'maxFileSize', Number(e.target.value))}
                helperText="Default: 5MB (5242880 bytes)"
                InputProps={{
                  endAdornment: <InputAdornment position="end">bytes</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Allowed File Types"
                value={field.options?.allowedFileTypes?.join(', ') || ''}
                onChange={(e) => {
                  const types = e.target.value.split(',').map(t => t.trim()).filter(t => t);
                  handleNestedChange('options', 'allowedFileTypes', types);
                }}
                helperText="Comma-separated MIME types (e.g., image/jpeg, image/png, application/pdf)"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Max Files"
                type="number"
                value={field.options?.maxFiles || 1}
                onChange={(e) => handleNestedChange('options', 'maxFiles', Number(e.target.value))}
                helperText="Maximum number of files that can be uploaded"
              />
            </Grid>
          </Grid>
        );
        
      case 'date':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Date Format"
                value={field.options?.dateFormat || 'YYYY-MM-DD'}
                onChange={(e) => handleNestedChange('options', 'dateFormat', e.target.value)}
                helperText="Format for displaying dates (e.g., YYYY-MM-DD, MM/DD/YYYY)"
              />
            </Grid>
          </Grid>
        );
        
      case 'time':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Time Format"
                value={field.options?.timeFormat || 'HH:mm'}
                onChange={(e) => handleNestedChange('options', 'timeFormat', e.target.value)}
                helperText="Format for displaying times (e.g., HH:mm, hh:mm a)"
              />
            </Grid>
          </Grid>
        );
        
      case 'html':
        return (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="HTML Content"
                value={field.options?.htmlContent || ''}
                onChange={(e) => handleNestedChange('options', 'htmlContent', e.target.value)}
                multiline
                rows={4}
                helperText="HTML content to display in the form"
              />
            </Grid>
          </Grid>
        );
        
      default:
        return (
          <Typography variant="body2" color="textSecondary">
            No additional options for this field type.
          </Typography>
        );
    }
  };
  
  // Render validation options
  const renderValidation = () => (
    <Grid container spacing={2}>
      {['text', 'textarea', 'email', 'phone'].includes(field.type) && (
        <>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Min Length"
              type="number"
              value={field.validation?.minLength || ''}
              onChange={(e) => handleNestedChange('validation', 'minLength', e.target.value ? Number(e.target.value) : '')}
              helperText="Minimum number of characters"
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Max Length"
              type="number"
              value={field.validation?.maxLength || ''}
              onChange={(e) => handleNestedChange('validation', 'maxLength', e.target.value ? Number(e.target.value) : '')}
              helperText="Maximum number of characters"
            />
          </Grid>
        </>
      )}
      
      {['text', 'email', 'phone'].includes(field.type) && (
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Pattern (Regex)"
            value={field.validation?.pattern || ''}
            onChange={(e) => handleNestedChange('validation', 'pattern', e.target.value)}
            helperText="Regular expression pattern for validation"
          />
        </Grid>
      )}
      
      {field.validation?.pattern && (
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Pattern Description"
            value={field.validation?.patternDescription || ''}
            onChange={(e) => handleNestedChange('validation', 'patternDescription', e.target.value)}
            helperText="Description of the pattern for users"
          />
        </Grid>
      )}
    </Grid>
  );
  
  // Render conditional display options
  const renderConditionalDisplay = () => (
    <Box>
      <FormControlLabel
        control={
          <Switch
            checked={field.conditionalDisplay?.enabled || false}
            onChange={(e) => handleConditionalDisplayChange(e.target.checked)}
            color="primary"
          />
        }
        label="Enable Conditional Display"
      />
      
      {field.conditionalDisplay?.enabled && (
        <Box mt={2}>
          <Typography variant="body2" gutterBottom>
            This field will only be displayed when the following conditions are met:
          </Typography>
          
          <ConditionBuilder
            conditions={field.conditionalDisplay.conditions || []}
            logicType={field.conditionalDisplay.logicType || 'and'}
            availableFields={allFields.filter(f => f.fieldId !== field.fieldId)}
            onChange={updateConditions}
          />
        </Box>
      )}
    </Box>
  );
  
  return (
    <Box>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
        variant="fullWidth"
        sx={{ mb: 2 }}
      >
        <Tab label="Basic" />
        <Tab label="Options" />
        <Tab label="Validation" />
        <Tab label="Conditional" />
      </Tabs>
      
      {activeTab === 0 && renderBasicProperties()}
      {activeTab === 1 && renderFieldOptions()}
      {activeTab === 2 && renderValidation()}
      {activeTab === 3 && renderConditionalDisplay()}
    </Box>
  );
};

export default FieldEditor;