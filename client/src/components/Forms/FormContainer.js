import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Divider,
  Paper,
  Step,
  StepLabel,
  Stepper,
  Typography
} from '@mui/material';
import {
  NavigateNext as NavigateNextIcon,
  NavigateBefore as NavigateBeforeIcon,
  Send as SendIcon
} from '@mui/icons-material';
import FieldRenderer from './FieldRenderer';

/**
 * Form Container Component
 * Renders a form with fields and handles form state
 */
const FormContainer = ({
  form,
  formData,
  onFormDataChange,
  onFileChange,
  onSubmit,
  errors = {},
  submitting = false
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [stepErrors, setStepErrors] = useState({});
  const [files, setFiles] = useState([]);
  
  // Reset active step when form changes
  useEffect(() => {
    setActiveStep(0);
  }, [form]);
  
  // Determine if the form is multistep
  const isMultistep = form.formType === 'multistep';
  
  // Handle field value change
  const handleChange = (fieldId, value) => {
    const newFormData = { ...formData, [fieldId]: value };
    onFormDataChange(newFormData);
    
    // Clear error for this field
    if (errors[fieldId] || stepErrors[fieldId]) {
      const newErrors = { ...stepErrors };
      delete newErrors[fieldId];
      setStepErrors(newErrors);
    }
  };
  
  // Handle file upload
  const handleFileUpload = (fieldId, uploadedFiles) => {
    // Add field ID to each file
    const newFiles = [...files];
    
    // Remove existing files for this field
    const filteredFiles = newFiles.filter(file => file.fieldId !== fieldId);
    
    // Add new files
    uploadedFiles.forEach(file => {
      filteredFiles.push({
        fieldId,
        file
      });
    });
    
    setFiles(filteredFiles);
    onFileChange(filteredFiles);
  };
  
  // Handle next step in multistep form
  const handleNext = () => {
    // Validate current step
    const currentGroupFields = form.fieldGroups[activeStep].fields;
    const newErrors = {};
    
    currentGroupFields.forEach(field => {
      // Skip fields that should not be displayed based on conditional logic
      if (!shouldDisplay(field.conditionalDisplay)) {
        return;
      }
      
      if (field.required) {
        const value = formData[field.fieldId];
        if (value === undefined || value === null || value === '' || 
            (Array.isArray(value) && value.length === 0)) {
          newErrors[field.fieldId] = `${field.label} is required`;
        }
      }
    });
    
    if (Object.keys(newErrors).length > 0) {
      setStepErrors(newErrors);
      return;
    }
    
    setActiveStep(prevStep => prevStep + 1);
  };
  
  // Handle previous step in multistep form
  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors = {};
    
    form.fieldGroups.forEach(group => {
      // Skip groups that should not be displayed based on conditional logic
      if (!shouldDisplay(group.conditionalDisplay)) {
        return;
      }
      
      group.fields.forEach(field => {
        // Skip fields that should not be displayed based on conditional logic
        if (!shouldDisplay(field.conditionalDisplay)) {
          return;
        }
        
        if (field.required) {
          const value = formData[field.fieldId];
          if (value === undefined || value === null || value === '' || 
              (Array.isArray(value) && value.length === 0)) {
            newErrors[field.fieldId] = `${field.label} is required`;
          }
        }
      });
    });
    
    if (Object.keys(newErrors).length > 0) {
      setStepErrors(newErrors);
      return;
    }
    
    // Submit form
    onSubmit(formData);
  };
  
  // Check if a field or group should be displayed based on conditional logic
  const shouldDisplay = (conditionalDisplay) => {
    if (!conditionalDisplay || !conditionalDisplay.enabled) {
      return true;
    }
    
    const { conditions, logicType } = conditionalDisplay;
    
    if (!conditions || conditions.length === 0) {
      return true;
    }
    
    const results = conditions.map(condition => {
      const { fieldId, operator, value } = condition;
      const fieldValue = formData[fieldId];
      
      switch (operator) {
        case 'equals':
          return fieldValue === value;
        case 'not_equals':
          return fieldValue !== value;
        case 'contains':
          return String(fieldValue).includes(value);
        case 'not_contains':
          return !String(fieldValue).includes(value);
        case 'greater_than':
          return Number(fieldValue) > Number(value);
        case 'less_than':
          return Number(fieldValue) < Number(value);
        case 'starts_with':
          return String(fieldValue).startsWith(value);
        case 'ends_with':
          return String(fieldValue).endsWith(value);
        case 'is_empty':
          return fieldValue === '' || fieldValue === null || fieldValue === undefined || 
                 (Array.isArray(fieldValue) && fieldValue.length === 0);
        case 'is_not_empty':
          return fieldValue !== '' && fieldValue !== null && fieldValue !== undefined && 
                 (!Array.isArray(fieldValue) || fieldValue.length > 0);
        default:
          return true;
      }
    });
    
    return logicType === 'and' 
      ? results.every(result => result) 
      : results.some(result => result);
  };
  
  // Render a group of fields
  const renderGroup = (group, index) => {
    if (!shouldDisplay(group.conditionalDisplay)) {
      return null;
    }
    
    return (
      <Box key={index} sx={{ mb: 4 }}>
        {group.title && (
          <Typography variant="h6" gutterBottom>
            {group.title}
          </Typography>
        )}
        
        {group.description && (
          <Typography variant="body2" color="textSecondary" paragraph>
            {group.description}
          </Typography>
        )}
        
        {group.fields.map((field) => (
          <React.Fragment key={field.fieldId}>
            {shouldDisplay(field.conditionalDisplay) && (
              <FieldRenderer
                field={field}
                value={formData[field.fieldId]}
                onChange={(value) => handleChange(field.fieldId, value)}
                onFileUpload={(files) => handleFileUpload(field.fieldId, files)}
                error={errors[field.fieldId] || stepErrors[field.fieldId]}
              />
            )}
          </React.Fragment>
        ))}
      </Box>
    );
  };
  
  // Apply form styling
  const formStyle = {
    backgroundColor: form.styling?.backgroundColor || '#ffffff',
    fontFamily: form.styling?.fontFamily || 'Roboto, sans-serif',
    color: form.styling?.primaryColor || '#3f51b5'
  };
  
  return (
    <Box sx={{ ...formStyle }}>
      <Typography variant="h5" gutterBottom align="center" sx={{ mb: 3 }}>
        {form.title}
      </Typography>
      
      {form.description && (
        <Typography variant="body1" paragraph align="center" sx={{ mb: 4 }}>
          {form.description}
        </Typography>
      )}
      
      {isMultistep && (
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {form.fieldGroups.map((group, index) => (
            <Step key={index}>
              <StepLabel>{group.title}</StepLabel>
            </Step>
          ))}
        </Stepper>
      )}
      
      <form onSubmit={handleSubmit}>
        <Paper elevation={2} sx={{ p: 3 }}>
          {isMultistep ? (
            // Render only the active group for multistep forms
            renderGroup(form.fieldGroups[activeStep], activeStep)
          ) : (
            // Render all groups for standard forms
            form.fieldGroups.map((group, index) => renderGroup(group, index))
          )}
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            {isMultistep && (
              <>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  startIcon={<NavigateBeforeIcon />}
                >
                  Back
                </Button>
                
                {activeStep < form.fieldGroups.length - 1 ? (
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleNext}
                    endIcon={<NavigateNextIcon />}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    endIcon={<SendIcon />}
                    disabled={submitting}
                  >
                    {submitting ? <CircularProgress size={24} /> : 'Submit'}
                  </Button>
                )}
              </>
            )}
            
            {!isMultistep && (
              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                endIcon={submitting ? <CircularProgress size={24} /> : <SendIcon />}
                disabled={submitting}
              >
                {submitting ? 'Submitting...' : 'Submit'}
              </Button>
            )}
          </Box>
        </Paper>
      </form>
    </Box>
  );
};

export default FormContainer;