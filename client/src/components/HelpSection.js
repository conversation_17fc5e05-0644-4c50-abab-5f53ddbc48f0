import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

/**
 * HelpSection component for displaying a section of help content with a title
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - The title of the help section
 * @param {React.ReactNode} props.children - The content of the help section
 * @returns {React.ReactElement} The rendered HelpSection component
 */
const HelpSection = ({ title, children }) => {
  return (
    <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        {title}
      </Typography>
      <Box>
        {children}
      </Box>
    </Paper>
  );
};

export default HelpSection;