import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  Card<PERSON>ontent,
  CardHeader,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Badge,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  CheckCircle as CompleteIcon,
  RadioButtonUnchecked as PendingIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  Assignment as TaskIcon,
  Person as PersonIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandIcon,
  Settings as SystemIcon,
  CheckBox as CheckBoxIcon,
  Notifications as NotificationIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import churchEventService from '../../services/churchEventService';

/**
 * EventPreparationTracker - Comprehensive event preparation management
 * Handles checklists, system checks, and preparation workflows
 */
const EventPreparationTracker = ({ 
  eventId, 
  onEventUpdate = null,
  showSystemsIntegration = true,
  compactView = false 
}) => {
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [addItemOpen, setAddItemOpen] = useState(false);
  const [editItemOpen, setEditItemOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [newItem, setNewItem] = useState({
    task: '',
    responsible: '',
    dueTime: new Date(),
    estimatedDuration: 30,
    systems: [],
    priority: 'medium'
  });
  const [expandedSections, setExpandedSections] = useState({
    checklist: true,
    systems: true,
    timeline: false
  });

  // Load event data
  useEffect(() => {
    if (eventId) {
      loadEvent();
    }
  }, [eventId]);

  const loadEvent = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await churchEventService.getChurchEvent(eventId);
      setEvent(data);
    } catch (err) {
      console.error('Error loading event:', err);
      setError('Failed to load event details');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteItem = async (taskId, notes = '') => {
    try {
      await churchEventService.completeChecklistItem(eventId, taskId, notes);
      await loadEvent();
      
      if (onEventUpdate) {
        onEventUpdate();
      }
    } catch (err) {
      console.error('Error completing checklist item:', err);
      setError('Failed to complete checklist item');
    }
  };

  const handleAddItem = async () => {
    try {
      await churchEventService.addChecklistItem(eventId, newItem);
      setAddItemOpen(false);
      setNewItem({
        task: '',
        responsible: '',
        dueTime: new Date(),
        estimatedDuration: 30,
        systems: [],
        priority: 'medium'
      });
      await loadEvent();
      
      if (onEventUpdate) {
        onEventUpdate();
      }
    } catch (err) {
      console.error('Error adding checklist item:', err);
      setError('Failed to add checklist item');
    }
  };

  const handleUpdateEventStatus = async (newStatus) => {
    try {
      await churchEventService.updateEventStatus(eventId, newStatus);
      await loadEvent();
      
      if (onEventUpdate) {
        onEventUpdate();
      }
    } catch (err) {
      console.error('Error updating event status:', err);
      setError('Failed to update event status');
    }
  };

  const generateSuggestions = () => {
    if (!event) return;
    
    const suggestions = churchEventService.generateChecklistSuggestions(event);
    // Add each suggestion to the event checklist
    suggestions.forEach(suggestion => {
      // This would typically call the API to add each suggestion
      console.log('Suggested checklist item:', suggestion);
    });
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getItemPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getSystemIcon = (system) => {
    const icons = {
      hvac: '🌡️',
      electrical: '⚡',
      lighting: '💡',
      audio: '🔊',
      cameras: '📹',
      doors: '🚪',
      wifi: '📶',
      safety: '🛡️'
    };
    return icons[system] || '⚙️';
  };

  const renderChecklistItem = (item, index) => {
    const isOverdue = item.dueTime && new Date(item.dueTime) < new Date() && !item.completed;
    const isDueSoon = item.dueTime && 
      new Date(item.dueTime) > new Date() && 
      new Date(item.dueTime) < new Date(Date.now() + 60 * 60 * 1000); // Due within 1 hour

    return (
      <ListItem
        key={item._id || index}
        sx={{
          border: 1,
          borderColor: isOverdue ? 'error.main' : isDueSoon ? 'warning.main' : 'divider',
          borderRadius: 1,
          mb: 1,
          backgroundColor: item.completed ? 'success.light' : 'background.paper'
        }}
      >
        <ListItemIcon>
          <IconButton 
            onClick={() => handleCompleteItem(item._id)}
            disabled={item.completed}
            color={item.completed ? 'success' : 'default'}
          >
            {item.completed ? <CompleteIcon /> : <PendingIcon />}
          </IconButton>
        </ListItemIcon>
        
        <ListItemText
          primary={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography 
                variant="body1" 
                sx={{ textDecoration: item.completed ? 'line-through' : 'none' }}
              >
                {item.task}
              </Typography>
              
              {item.priority && item.priority !== 'medium' && (
                <Chip 
                  label={item.priority.toUpperCase()}
                  size="small"
                  color={getItemPriorityColor(item.priority)}
                  sx={{ fontSize: '0.7rem' }}
                />
              )}
              
              {isOverdue && (
                <Tooltip title="Overdue">
                  <ErrorIcon color="error" sx={{ fontSize: 16 }} />
                </Tooltip>
              )}
              
              {isDueSoon && (
                <Tooltip title="Due soon">
                  <WarningIcon color="warning" sx={{ fontSize: 16 }} />
                </Tooltip>
              )}
            </Box>
          }
          secondary={
            <Box>
              <Typography variant="caption" color="text.secondary">
                {item.responsible} • {item.estimatedDuration || 0} minutes
              </Typography>
              
              {item.dueTime && (
                <Typography variant="caption" display="block" color="text.secondary">
                  Due: {new Date(item.dueTime).toLocaleString()}
                </Typography>
              )}
              
              {item.systems && item.systems.length > 0 && (
                <Box sx={{ mt: 0.5 }}>
                  {item.systems.map(system => (
                    <Chip
                      key={system}
                      label={`${getSystemIcon(system)} ${system}`}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 0.5, fontSize: '0.7rem' }}
                    />
                  ))}
                </Box>
              )}
            </Box>
          }
        />
        
        <ListItemSecondaryAction>
          <IconButton 
            size="small" 
            onClick={() => {
              setSelectedItem(item);
              setEditItemOpen(true);
            }}
          >
            <EditIcon />
          </IconButton>
        </ListItemSecondaryAction>
      </ListItem>
    );
  };

  const renderSystemsChecks = () => {
    if (!event?.preparation?.systemsChecks) return null;

    return (
      <Accordion 
        expanded={expandedSections.systems} 
        onChange={() => toggleSection('systems')}
      >
        <AccordionSummary expandIcon={<ExpandIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SystemIcon />
            <Typography variant="h6">Building Systems Checks</Typography>
            <Badge 
              badgeContent={event.preparation.systemsChecks.filter(s => s.status === 'error').length}
              color="error"
            >
              <Badge 
                badgeContent={event.preparation.systemsChecks.filter(s => s.status === 'warning').length}
                color="warning"
              />
            </Badge>
          </Box>
        </AccordionSummary>
        
        <AccordionDetails>
          <List>
            {event.preparation.systemsChecks.map((check, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <Box sx={{ fontSize: '1.5rem' }}>
                    {getSystemIcon(check.system)}
                  </Box>
                </ListItemIcon>
                
                <ListItemText
                  primary={check.system.toUpperCase()}
                  secondary={
                    <Box>
                      <Chip 
                        label={check.status.replace('_', ' ').toUpperCase()}
                        size="small"
                        color={
                          check.status === 'ok' ? 'success' :
                          check.status === 'warning' ? 'warning' :
                          check.status === 'error' ? 'error' : 'default'
                        }
                        sx={{ mr: 1 }}
                      />
                      
                      {check.lastChecked && (
                        <Typography variant="caption" display="block">
                          Last checked: {new Date(check.lastChecked).toLocaleString()}
                        </Typography>
                      )}
                      
                      {check.notes && (
                        <Typography variant="caption" display="block">
                          {check.notes}
                        </Typography>
                      )}
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <IconButton size="small">
                    <RefreshIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderAddItemDialog = () => (
    <Dialog open={addItemOpen} onClose={() => setAddItemOpen(false)} maxWidth="md" fullWidth>
      <DialogTitle>Add Checklist Item</DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Task Description"
              value={newItem.task}
              onChange={(e) => setNewItem({ ...newItem, task: e.target.value })}
              multiline
              rows={2}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Responsible Person/Team"
              value={newItem.responsible}
              onChange={(e) => setNewItem({ ...newItem, responsible: e.target.value })}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Priority</InputLabel>
              <Select
                value={newItem.priority}
                onChange={(e) => setNewItem({ ...newItem, priority: e.target.value })}
              >
                <MenuItem value="low">Low</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="critical">Critical</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DateTimePicker
                label="Due Time"
                value={newItem.dueTime}
                onChange={(date) => setNewItem({ ...newItem, dueTime: date })}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </LocalizationProvider>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              type="number"
              label="Estimated Duration (minutes)"
              value={newItem.estimatedDuration}
              onChange={(e) => setNewItem({ ...newItem, estimatedDuration: parseInt(e.target.value) || 0 })}
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Related Systems</InputLabel>
              <Select
                multiple
                value={newItem.systems}
                onChange={(e) => setNewItem({ ...newItem, systems: e.target.value })}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip 
                        key={value} 
                        label={`${getSystemIcon(value)} ${value}`} 
                        size="small" 
                      />
                    ))}
                  </Box>
                )}
              >
                {['hvac', 'electrical', 'lighting', 'audio', 'cameras', 'doors', 'wifi', 'safety'].map((system) => (
                  <MenuItem key={system} value={system}>
                    {getSystemIcon(system)} {system.toUpperCase()}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setAddItemOpen(false)}>Cancel</Button>
        <Button 
          onClick={handleAddItem} 
          variant="contained"
          disabled={!newItem.task || !newItem.responsible}
        >
          Add Item
        </Button>
      </DialogActions>
    </Dialog>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading event preparation...</Typography>
        <LinearProgress sx={{ mt: 2 }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!event) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No event data available
      </Alert>
    );
  }

  const preparation = churchEventService.calculatePreparationProgress(event);
  const attention = churchEventService.needsAttention(event);

  return (
    <Box sx={{ p: compactView ? 1 : 3 }}>
      {/* Event Header */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6">
                {churchEventService.getEventTypeIcon(event.eventType)} {event.title}
              </Typography>
              
              <Chip 
                label={event.status.replace('_', ' ').toUpperCase()}
                size="small"
                sx={{ 
                  backgroundColor: churchEventService.getStatusColor(event.status),
                  color: 'white'
                }}
              />
              
              {attention.needsAttention && (
                <Tooltip title={attention.reasons.join(', ')}>
                  <WarningIcon color="warning" />
                </Tooltip>
              )}
            </Box>
          }
          subheader={
            <Box>
              <Typography variant="body2" color="text.secondary">
                {new Date(event.startTime).toLocaleString()} - {new Date(event.endTime).toLocaleString()}
              </Typography>
              
              {event.location?.roomName && (
                <Typography variant="body2" color="text.secondary">
                  📍 {event.location.roomName}
                </Typography>
              )}
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                startIcon={<AddIcon />}
                onClick={() => setAddItemOpen(true)}
                size="small"
                variant="outlined"
              >
                Add Task
              </Button>
              
              <Button
                startIcon={<NotificationIcon />}
                onClick={generateSuggestions}
                size="small"
                variant="outlined"
              >
                Suggestions
              </Button>
            </Box>
          }
        />
        
        <CardContent>
          {/* Progress Overview */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">
                Preparation Progress: {preparation.completed}/{preparation.total} tasks
              </Typography>
              <Typography variant="body2">
                {preparation.percentage}%
              </Typography>
            </Box>
            
            <LinearProgress 
              variant="determinate" 
              value={preparation.percentage}
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: 'grey.200',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: preparation.percentage === 100 ? 'success.main' : 'primary.main'
                }
              }}
            />
          </Box>
          
          {/* Status Actions */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {['planning', 'approved', 'preparing', 'setup', 'in_progress'].includes(event.status) && (
              <Button
                size="small"
                startIcon={<StartIcon />}
                onClick={() => {
                  const nextStatus = {
                    'planning': 'approved',
                    'approved': 'preparing', 
                    'preparing': 'setup',
                    'setup': 'in_progress'
                  }[event.status];
                  if (nextStatus) handleUpdateEventStatus(nextStatus);
                }}
              >
                Move to {event.status === 'planning' ? 'Approved' : 
                        event.status === 'approved' ? 'Preparing' :
                        event.status === 'preparing' ? 'Setup' : 'In Progress'}
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Preparation Checklist */}
      <Accordion 
        expanded={expandedSections.checklist} 
        onChange={() => toggleSection('checklist')}
      >
        <AccordionSummary expandIcon={<ExpandIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CheckBoxIcon />
            <Typography variant="h6">Preparation Checklist</Typography>
            <Badge 
              badgeContent={preparation.total - preparation.completed}
              color="primary"
            />
          </Box>
        </AccordionSummary>
        
        <AccordionDetails>
          {event.preparation?.checklist && event.preparation.checklist.length > 0 ? (
            <List>
              {event.preparation.checklist.map((item, index) => renderChecklistItem(item, index))}
            </List>
          ) : (
            <Alert severity="info">
              No checklist items yet. Click "Add Task" to get started.
            </Alert>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Building Systems Integration */}
      {showSystemsIntegration && renderSystemsChecks()}

      {/* Dialogs */}
      {renderAddItemDialog()}
    </Box>
  );
};

export default EventPreparationTracker;