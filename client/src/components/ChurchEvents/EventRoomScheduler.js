import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Button,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  ButtonGroup,
  Slider,
  Switch,
  FormControlLabel,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent
} from '@mui/material';
import {
  ChevronLeft as PrevIcon,
  ChevronRight as NextIcon,
  Today as TodayIcon,
  Event as EventIcon,
  AccessTime as TimeIcon,
  LocationOn as LocationIcon,
  Group as AttendanceIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  ViewColumn as ColumnIcon,
  ViewDay as DayIcon,
  ViewWeek as WeekIcon,
  Room as RoomIcon,
  Schedule as ScheduleIcon,
  WarningAmber as ConflictIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import churchEventService from '../../services/churchEventService';
import buildingManagementService from '../../services/buildingManagementService';

/**
 * EventRoomScheduler - Visual room scheduling and capacity management
 * Shows events on a timeline grid with room usage visualization
 */
const EventRoomScheduler = ({ 
  buildingId,
  selectedDate = new Date(),
  view = 'day',
  onEventSelect = null,
  onCreateEvent = null,
  showCapacityUtilization = true,
  compactView = false 
}) => {
  const [currentDate, setCurrentDate] = useState(selectedDate);
  const [schedulerView, setSchedulerView] = useState(view);
  const [events, setEvents] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [eventDetailsOpen, setEventDetailsOpen] = useState(false);
  const [createEventOpen, setCreateEventOpen] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [timeScale, setTimeScale] = useState(60); // Minutes per hour slot
  const [showConflicts, setShowConflicts] = useState(true);
  const [filterByCapacity, setFilterByCapacity] = useState(false);
  const [minimumCapacity, setMinimumCapacity] = useState(0);
  const [showCapacityUtilizationState, setShowCapacityUtilizationState] = useState(showCapacityUtilization);

    useEffect(() => {
      setShowCapacityUtilizationState(showCapacityUtilization);
    }, [showCapacityUtilization]);

  // Time slots for the grid (24 hours)
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 0; hour < 24; hour++) {
      const slotsPerHour = 60 / timeScale;
      for (let slot = 0; slot < slotsPerHour; slot++) {
        const time = new Date();
        time.setHours(hour, slot * timeScale, 0, 0);
        slots.push(time);
      }
    }
    return slots;
  }, [timeScale]);

  // Calculate date range for current view
  const dateRange = useMemo(() => {
    const start = new Date(currentDate);
    const end = new Date(currentDate);
    
    if (schedulerView === 'day') {
      end.setDate(start.getDate() + 1);
    } else if (schedulerView === 'week') {
      const dayOfWeek = start.getDay();
      const diff = start.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
      start.setDate(diff);
      end.setDate(start.getDate() + 7);
    }
    
    return { start, end };
  }, [currentDate, schedulerView]);

  // Load data on mount and when parameters change
  useEffect(() => {
    if (buildingId) {
      loadSchedulerData();
    }
  }, [buildingId, dateRange, schedulerView]);

  const loadSchedulerData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load events for date range
      const eventsResponse = await churchEventService.getCalendarEvents(buildingId, {
        startDate: dateRange.start.toISOString(),
        endDate: dateRange.end.toISOString(),
        view: schedulerView
      });
      
      // Load rooms for building
      const roomsResponse = await buildingManagementService.getRooms(buildingId);
      
      setEvents(eventsResponse.events || []);
      setRooms(roomsResponse.filter(room => 
        !filterByCapacity || (room.capacity && room.capacity >= minimumCapacity)
      ));
      
    } catch (err) {
      console.error('Error loading scheduler data:', err);
      setError('Failed to load scheduling data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateNavigation = (direction) => {
    const newDate = new Date(currentDate);
    
    if (schedulerView === 'day') {
      newDate.setDate(currentDate.getDate() + direction);
    } else if (schedulerView === 'week') {
      newDate.setDate(currentDate.getDate() + (direction * 7));
    }
    
    setCurrentDate(newDate);
  };

  const handleTimeSlotClick = (room, timeSlot) => {
    setSelectedTimeSlot({
      room,
      startTime: timeSlot,
      endTime: new Date(timeSlot.getTime() + 60 * 60 * 1000) // 1 hour default
    });
    setCreateEventOpen(true);
  };

  const getEventsForRoomAndTime = (room, timeSlot) => {
    const slotEnd = new Date(timeSlot.getTime() + timeScale * 60 * 1000);
    
    return events.filter(event => {
      const eventStart = new Date(event.startTime);
      const eventEnd = new Date(event.endTime);
      
      // Check if event overlaps with time slot and is in the same room
      return (
        event.location?.roomId === room._id &&
        eventStart < slotEnd &&
        eventEnd > timeSlot
      );
    });
  };

  const calculateEventDuration = (event) => {
    const start = new Date(event.startTime);
    const end = new Date(event.endTime);
    return (end - start) / (1000 * 60); // Duration in minutes
  };

  const calculateEventPosition = (event, timeSlot) => {
    const eventStart = new Date(event.startTime);
    const slotStart = timeSlot;
    const slotDuration = timeScale;
    
    // Calculate how far into the slot the event starts
    const offsetMinutes = Math.max(0, (eventStart - slotStart) / (1000 * 60));
    const offsetPercent = (offsetMinutes / slotDuration) * 100;
    
    // Calculate how much of the slot the event occupies
    const eventDuration = calculateEventDuration(event);
    const slotCoverage = Math.min(slotDuration, eventDuration - offsetMinutes);
    const widthPercent = (slotCoverage / slotDuration) * 100;
    
    return { offsetPercent, widthPercent };
  };

  const detectConflicts = () => {
    const conflicts = [];
    
    rooms.forEach(room => {
      const roomEvents = events.filter(event => event.location?.roomId === room._id);
      
      for (let i = 0; i < roomEvents.length; i++) {
        for (let j = i + 1; j < roomEvents.length; j++) {
          const event1 = roomEvents[i];
          const event2 = roomEvents[j];
          
          const start1 = new Date(event1.startTime);
          const end1 = new Date(event1.endTime);
          const start2 = new Date(event2.startTime);
          const end2 = new Date(event2.endTime);
          
          // Check for overlap
          if (start1 < end2 && start2 < end1) {
            conflicts.push({
              room: room.name,
              events: [event1, event2],
              type: 'time_overlap'
            });
          }
        }
      }
    });
    
    return conflicts;
  };

  const renderTimeSlot = (room, timeSlot, slotIndex) => {
    const slotEvents = getEventsForRoomAndTime(room, timeSlot);
    const hasConflict = slotEvents.length > 1;
    const isCurrentHour = timeSlot.getHours() === new Date().getHours() && 
                          timeSlot.toDateString() === new Date().toDateString();

    return (
      <Box
        key={slotIndex}
        sx={{
          minHeight: 60,
          border: 1,
          borderColor: hasConflict && showConflicts ? 'error.main' : 'divider',
          backgroundColor: isCurrentHour ? 'primary.light' : 'background.paper',
          cursor: 'pointer',
          position: 'relative',
          '&:hover': {
            backgroundColor: hasConflict ? 'error.light' : 'action.hover'
          }
        }}
        onClick={() => handleTimeSlotClick(room, timeSlot)}
      >
        {/* Time slot events */}
        {slotEvents.map((event, eventIndex) => {
          const { offsetPercent, widthPercent } = calculateEventPosition(event, timeSlot);
          const formatted = churchEventService.formatEventDisplay(event);
          
          return (
            <Paper
              key={event._id}
              elevation={2}
              sx={{
                position: 'absolute',
                top: `${eventIndex * 25}%`,
                left: `${offsetPercent}%`,
                width: `${widthPercent}%`,
                height: '22%',
                backgroundColor: formatted.typeColor,
                color: 'white',
                p: 0.5,
                cursor: 'pointer',
                zIndex: 10 + eventIndex,
                overflow: 'hidden'
              }}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedEvent(event);
                setEventDetailsOpen(true);
                if (onEventSelect) onEventSelect(event);
              }}
            >
              <Typography variant="caption" noWrap>
                {formatted.typeIcon} {event.title}
              </Typography>
            </Paper>
          );
        })}
        
        {/* Conflict indicator */}
        {hasConflict && showConflicts && (
          <Tooltip title={`${slotEvents.length} overlapping events`}>
            <ConflictIcon 
              color="error" 
              sx={{ 
                position: 'absolute', 
                top: 2, 
                right: 2, 
                fontSize: 16,
                zIndex: 20
              }} 
            />
          </Tooltip>
        )}
      </Box>
    );
  };

  const renderRoomRow = (room) => {
    const roomEvents = events.filter(event => event.location?.roomId === room._id);
    const utilizationPercent = roomEvents.length > 0 ? 
      Math.round((roomEvents.reduce((acc, event) => acc + calculateEventDuration(event), 0) / (24 * 60)) * 100) : 0;

    return (
      <Grid container key={room._id} sx={{ borderBottom: 1, borderColor: 'divider' }}>
        {/* Room info column */}
        <Grid item xs={3} sx={{ p: 1, borderRight: 1, borderColor: 'divider' }}>
          <Box>
            <Typography variant="subtitle2" fontWeight="bold">
              {room.name}
            </Typography>
            
            <Typography variant="caption" color="text.secondary" display="block">
              {room.floor?.name} • Capacity: {room.capacity || 'N/A'}
            </Typography>
            
            {showCapacityUtilizationState && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="caption">
                  Utilization: {utilizationPercent}%
                </Typography>
                <Box 
                  sx={{ 
                    width: '100%', 
                    height: 4, 
                    backgroundColor: 'grey.300', 
                    borderRadius: 2, 
                    mt: 0.5 
                  }}
                >
                  <Box 
                    sx={{ 
                      width: `${utilizationPercent}%`, 
                      height: '100%', 
                      backgroundColor: utilizationPercent > 80 ? 'error.main' : 
                                      utilizationPercent > 60 ? 'warning.main' : 'success.main',
                      borderRadius: 2 
                    }} 
                  />
                </Box>
              </Box>
            )}
            
            <Chip 
              size="small" 
              label={`${roomEvents.length} events`}
              sx={{ mt: 0.5, fontSize: '0.7rem' }}
            />
          </Box>
        </Grid>
        
        {/* Time slots */}
        <Grid item xs={9}>
          <Grid container>
            {timeSlots.map((timeSlot, slotIndex) => (
              <Grid 
                item 
                key={slotIndex}
                xs={schedulerView === 'day' ? 1 : 0.5}
                sx={{ minWidth: schedulerView === 'day' ? 80 : 40 }}
              >
                {renderTimeSlot(room, timeSlot, slotIndex)}
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
    );
  };

  const renderConflictsSummary = () => {
    const conflicts = detectConflicts();
    
    if (conflicts.length === 0) return null;

    return (
      <Alert severity="warning" sx={{ mb: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          ⚠️ {conflicts.length} Scheduling Conflicts Detected
        </Typography>
        
        <List dense>
          {conflicts.slice(0, 3).map((conflict, index) => (
            <ListItem key={index} sx={{ pl: 0 }}>
              <ListItemText
                primary={`${conflict.room}: ${conflict.events.length} overlapping events`}
                secondary={conflict.events.map(e => e.title).join(' & ')}
              />
            </ListItem>
          ))}
        </List>
        
        {conflicts.length > 3 && (
          <Typography variant="caption">
            +{conflicts.length - 3} more conflicts
          </Typography>
        )}
      </Alert>
    );
  };

  const renderEventDetailsDialog = () => (
    <Dialog 
      open={eventDetailsOpen} 
      onClose={() => setEventDetailsOpen(false)}
      maxWidth="sm"
      fullWidth
    >
      {selectedEvent && (
        <>
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body1">
                {churchEventService.getEventTypeIcon(selectedEvent.eventType)}
              </Typography>
              <Typography variant="h6">
                {selectedEvent.title}
              </Typography>
            </Box>
          </DialogTitle>
          
          <DialogContent>
            <List>
              <ListItem>
                <ListItemIcon><TimeIcon /></ListItemIcon>
                <ListItemText 
                  primary="Time"
                  secondary={`${new Date(selectedEvent.startTime).toLocaleString()} - ${new Date(selectedEvent.endTime).toLocaleString()}`}
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon><LocationIcon /></ListItemIcon>
                <ListItemText 
                  primary="Location"
                  secondary={selectedEvent.location?.roomName || 'Not specified'}
                />
              </ListItem>
              
              {selectedEvent.location?.expectedAttendance && (
                <ListItem>
                  <ListItemIcon><AttendanceIcon /></ListItemIcon>
                  <ListItemText 
                    primary="Expected Attendance"
                    secondary={`${selectedEvent.location.expectedAttendance} people`}
                  />
                </ListItem>
              )}
            </List>
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setEventDetailsOpen(false)}>Close</Button>
            <Button variant="contained" onClick={() => {
              setEventDetailsOpen(false);
              if (onEventSelect) onEventSelect(selectedEvent);
            }}>
              View Details
            </Button>
          </DialogActions>
        </>
      )}
    </Dialog>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading room schedule...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: compactView ? 1 : 3 }}>
      {/* Header Controls */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="h5">Room Scheduler</Typography>
          
          <ButtonGroup size="small">
            <Button 
              variant={schedulerView === 'day' ? 'contained' : 'outlined'}
              onClick={() => setSchedulerView('day')}
              startIcon={<DayIcon />}
            >
              Day
            </Button>
            <Button 
              variant={schedulerView === 'week' ? 'contained' : 'outlined'}
              onClick={() => setSchedulerView('week')}
              startIcon={<WeekIcon />}
            >
              Week
            </Button>
          </ButtonGroup>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton onClick={() => handleDateNavigation(-1)}>
            <PrevIcon />
          </IconButton>
          
          <Button onClick={() => setCurrentDate(new Date())} startIcon={<TodayIcon />}>
            Today
          </Button>
          
          <IconButton onClick={() => handleDateNavigation(1)}>
            <NextIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Date Display */}
      <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
        {schedulerView === 'day' ? 
          currentDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) :
          `${dateRange.start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${dateRange.end.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`
        }
      </Typography>

      {/* Controls */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch 
                  checked={showConflicts}
                  onChange={(e) => setShowConflicts(e.target.checked)}
                />
              }
              label="Highlight Conflicts"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <FormControlLabel
              control={
                <Switch 
                  checked={showCapacityUtilizationState}
                  onChange={(e) => setShowCapacityUtilizationState(e.target.checked)}
                />
              }
              label="Show Utilization"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="caption" gutterBottom>
              Time Scale: {timeScale} min slots
            </Typography>
            <Slider
              value={timeScale}
              onChange={(e, value) => setTimeScale(value)}
              min={15}
              max={120}
              step={15}
              marks={[
                { value: 15, label: '15m' },
                { value: 30, label: '30m' },
                { value: 60, label: '1h' },
                { value: 120, label: '2h' }
              ]}
              size="small"
            />
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Button
              startIcon={<AddIcon />}
              onClick={() => setCreateEventOpen(true)}
              variant="outlined"
              size="small"
            >
              New Event
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Conflicts Summary */}
      {showConflicts && renderConflictsSummary()}

      {/* Time Header */}
      <Grid container sx={{ borderBottom: 2, borderColor: 'divider', backgroundColor: 'grey.50' }}>
        <Grid item xs={3} sx={{ p: 1, borderRight: 1, borderColor: 'divider' }}>
          <Typography variant="subtitle2" fontWeight="bold">
            Rooms ({rooms.length})
          </Typography>
        </Grid>
        
        <Grid item xs={9}>
          <Grid container>
            {timeSlots.filter((_, index) => index % (schedulerView === 'day' ? 1 : 2) === 0).map((timeSlot, index) => (
              <Grid 
                item 
                key={index}
                xs={schedulerView === 'day' ? 1 : 0.5}
                sx={{ 
                  minWidth: schedulerView === 'day' ? 80 : 40,
                  textAlign: 'center',
                  p: 0.5,
                  borderRight: 1,
                  borderColor: 'divider'
                }}
              >
                <Typography variant="caption">
                  {timeSlot.toLocaleTimeString('en-US', { 
                    hour: 'numeric', 
                    minute: timeScale < 60 ? '2-digit' : undefined 
                  })}
                </Typography>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>

      {/* Schedule Grid */}
      <Box sx={{ overflowX: 'auto' }}>
        {rooms.map(room => renderRoomRow(room))}
      </Box>

      {/* No Rooms Message */}
      {rooms.length === 0 && (
        <Alert severity="info" sx={{ mt: 2 }}>
          No rooms available for scheduling. Check your building configuration.
        </Alert>
      )}

      {/* Dialogs */}
      {renderEventDetailsDialog()}
    </Box>
  );
};

export default EventRoomScheduler;