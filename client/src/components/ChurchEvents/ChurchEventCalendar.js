import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Badge,
  ButtonGroup,
  Paper,
  Divider,
  Avatar
} from '@mui/material';
import {
  ChevronLeft as PrevIcon,
  ChevronRight as NextIcon,
  Today as TodayIcon,
  Event as EventIcon,
  AccessTime as TimeIcon,
  LocationOn as LocationIcon,
  Group as AttendanceIcon,
  Warning as WarningIcon,
  CheckCircle as CompleteIcon,
  Schedule as PrepIcon,
  Phone as PhoneIcon,
  Email as EmailIcon
} from '@mui/icons-material';
import churchEventService from '../../services/churchEventService';

/**
 * ChurchEventCalendar - Calendar view for church events with room usage visualization
 * Displays events on floor plans and provides preparation management
 */
const ChurchEventCalendar = ({ 
  buildingId, 
  floorId, 
  view = 'week',
  onEventSelect = null,
  showPreparationStatus = true,
  compactView = false 
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState([]);
  const [calendarData, setCalendarData] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [eventDetailsOpen, setEventDetailsOpen] = useState(false);
  const [calendarView, setCalendarView] = useState(view);

  // Calculate date range based on current view
  const dateRange = useMemo(() => {
    const start = new Date(currentDate);
    const end = new Date(currentDate);
    
    if (calendarView === 'day') {
      end.setDate(start.getDate() + 1);
    } else if (calendarView === 'week') {
      // Start from Monday of current week
      const dayOfWeek = start.getDay();
      const diff = start.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1);
      start.setDate(diff);
      end.setDate(start.getDate() + 7);
    } else if (calendarView === 'month') {
      start.setDate(1);
      end.setMonth(start.getMonth() + 1);
      end.setDate(0);
    }
    
    return { start, end };
  }, [currentDate, calendarView]);

  // Load events for current date range
  useEffect(() => {
    if (buildingId) {
      loadEvents();
    }
  }, [buildingId, floorId, dateRange, calendarView]);

  const loadEvents = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await churchEventService.getCalendarEvents(buildingId, {
        startDate: dateRange.start.toISOString(),
        endDate: dateRange.end.toISOString(),
        view: calendarView
      });
      
      setEvents(data.events || []);
      setCalendarData(data.calendar || {});
    } catch (err) {
      console.error('Error loading events:', err);
      setError('Failed to load church events');
    } finally {
      setLoading(false);
    }
  };

  const handleEventClick = (event) => {
    setSelectedEvent(event);
    setEventDetailsOpen(true);
    
    if (onEventSelect) {
      onEventSelect(event);
    }
  };

  const handleDateNavigation = (direction) => {
    const newDate = new Date(currentDate);
    
    if (calendarView === 'day') {
      newDate.setDate(currentDate.getDate() + direction);
    } else if (calendarView === 'week') {
      newDate.setDate(currentDate.getDate() + (direction * 7));
    } else if (calendarView === 'month') {
      newDate.setMonth(currentDate.getMonth() + direction);
    }
    
    setCurrentDate(newDate);
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const formatDateRange = () => {
    if (calendarView === 'day') {
      return currentDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    } else if (calendarView === 'week') {
      const end = new Date(dateRange.start);
      end.setDate(dateRange.start.getDate() + 6);
      return `${dateRange.start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
    } else if (calendarView === 'month') {
      return currentDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
    }
  };

  const getEventsForDay = (date) => {
    const dateKey = date.toISOString().split('T')[0];
    return calendarData[dateKey] || [];
  };

  const renderEventCard = (event, isCompact = false) => {
    const formatted = churchEventService.formatEventDisplay(event);
    const preparation = churchEventService.calculatePreparationProgress(event);
    const attention = churchEventService.needsAttention(event);

    return (
      <Card
        key={event._id}
        sx={{
          mb: isCompact ? 0.5 : 1,
          cursor: 'pointer',
          border: attention.needsAttention ? 2 : 1,
          borderColor: attention.needsAttention ? 'warning.main' : 'divider',
          '&:hover': {
            boxShadow: 3,
            transform: 'translateY(-2px)'
          },
          transition: 'all 0.2s ease'
        }}
        onClick={() => handleEventClick(event)}
      >
        <CardContent sx={{ p: isCompact ? 1 : 2, '&:last-child': { pb: isCompact ? 1 : 2 } }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="body2" sx={{ mr: 1 }}>
                  {formatted.typeIcon}
                </Typography>
                <Typography 
                  variant={isCompact ? 'body2' : 'subtitle1'} 
                  fontWeight="bold"
                  noWrap
                >
                  {formatted.displayTitle}
                </Typography>
                {attention.needsAttention && (
                  <Tooltip title={attention.reasons.join(', ')}>
                    <WarningIcon color="warning" sx={{ ml: 1, fontSize: 16 }} />
                  </Tooltip>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <TimeIcon sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  {new Date(event.startTime).toLocaleTimeString('en-US', { 
                    hour: 'numeric', 
                    minute: '2-digit' 
                  })} - {new Date(event.endTime).toLocaleTimeString('en-US', { 
                    hour: 'numeric', 
                    minute: '2-digit' 
                  })}
                </Typography>
              </Box>
              
              {event.location?.roomName && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <LocationIcon sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {event.location.roomName}
                  </Typography>
                </Box>
              )}
              
              {event.location?.expectedAttendance && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <AttendanceIcon sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
                  <Typography variant="caption" color="text.secondary">
                    {event.location.expectedAttendance} expected
                  </Typography>
                </Box>
              )}
            </Box>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 0.5 }}>
              <Chip 
                label={formatted.statusLabel}
                size="small"
                sx={{ 
                  backgroundColor: formatted.statusColor,
                  color: 'white',
                  fontSize: '0.7rem'
                }}
              />
              
              {showPreparationStatus && preparation.total > 0 && (
                <Chip 
                  label={`${preparation.completed}/${preparation.total}`}
                  size="small"
                  icon={preparation.percentage === 100 ? <CompleteIcon /> : <PrepIcon />}
                  color={preparation.percentage === 100 ? 'success' : 'default'}
                  sx={{ fontSize: '0.7rem' }}
                />
              )}
              
              {event.priority === 'high' || event.priority === 'critical' && (
                <Chip 
                  label={event.priority.toUpperCase()}
                  size="small"
                  color={event.priority === 'critical' ? 'error' : 'warning'}
                  sx={{ fontSize: '0.7rem' }}
                />
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  };

  const renderDayView = () => {
    const dayEvents = getEventsForDay(currentDate);
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Events for {formatDateRange()}
        </Typography>
        
        {dayEvents.length === 0 ? (
          <Alert severity="info">No events scheduled for this day</Alert>
        ) : (
          <Box>
            {dayEvents.map(event => renderEventCard(event))}
          </Box>
        )}
      </Box>
    );
  };

  const renderWeekView = () => {
    const weekDays = [];
    const startDate = new Date(dateRange.start);
    
    for (let i = 0; i < 7; i++) {
      const day = new Date(startDate);
      day.setDate(startDate.getDate() + i);
      weekDays.push(day);
    }
    
    return (
      <Grid container spacing={1}>
        {weekDays.map((day, index) => {
          const dayEvents = getEventsForDay(day);
          const isToday = day.toDateString() === new Date().toDateString();
          
          return (
            <Grid item xs={12} md key={index} sx={{ minHeight: 200 }}>
              <Paper 
                sx={{ 
                  p: 1, 
                  height: '100%',
                  backgroundColor: isToday ? 'primary.light' : 'background.paper',
                  opacity: isToday ? 1 : 0.9
                }}
              >
                <Typography 
                  variant="subtitle2" 
                  fontWeight="bold" 
                  gutterBottom
                  sx={{ color: isToday ? 'primary.contrastText' : 'text.primary' }}
                >
                  {day.toLocaleDateString('en-US', { weekday: 'short', month: 'numeric', day: 'numeric' })}
                </Typography>
                
                <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
                  {dayEvents.map(event => renderEventCard(event, true))}
                </Box>
                
                {dayEvents.length === 0 && (
                  <Typography variant="caption" color="text.secondary">
                    No events
                  </Typography>
                )}
              </Paper>
            </Grid>
          );
        })}
      </Grid>
    );
  };

  const renderMonthView = () => {
    // Simplified month view - can be enhanced later
    const monthEvents = events.filter(event => {
      const eventDate = new Date(event.startTime);
      return eventDate.getMonth() === currentDate.getMonth() && 
             eventDate.getFullYear() === currentDate.getFullYear();
    });

    const eventsByDate = monthEvents.reduce((acc, event) => {
      const dateKey = new Date(event.startTime).getDate();
      if (!acc[dateKey]) acc[dateKey] = [];
      acc[dateKey].push(event);
      return acc;
    }, {});

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Events in {formatDateRange()}
        </Typography>
        
        <Grid container spacing={2}>
          {Object.entries(eventsByDate).map(([day, dayEvents]) => (
            <Grid item xs={12} sm={6} md={4} key={day}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  {new Date(currentDate.getFullYear(), currentDate.getMonth(), parseInt(day)).toLocaleDateString('en-US', { 
                    weekday: 'short', 
                    day: 'numeric' 
                  })}
                </Typography>
                
                {dayEvents.map(event => renderEventCard(event, true))}
              </Paper>
            </Grid>
          ))}
        </Grid>
        
        {monthEvents.length === 0 && (
          <Alert severity="info">No events scheduled for this month</Alert>
        )}
      </Box>
    );
  };

  const renderEventDetailsDialog = () => (
    <Dialog 
      open={eventDetailsOpen} 
      onClose={() => setEventDetailsOpen(false)}
      maxWidth="md"
      fullWidth
    >
      {selectedEvent && (
        <>
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="body1">
                {churchEventService.getEventTypeIcon(selectedEvent.eventType)}
              </Typography>
              <Typography variant="h6">
                {selectedEvent.title}
              </Typography>
              <Chip 
                label={selectedEvent.status}
                size="small"
                sx={{ 
                  backgroundColor: churchEventService.getStatusColor(selectedEvent.status),
                  color: 'white'
                }}
              />
            </Box>
          </DialogTitle>
          
          <DialogContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                  Event Details
                </Typography>
                
                <List dense>
                  <ListItem>
                    <ListItemIcon><EventIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Type"
                      secondary={churchEventService.getEventTypes().find(t => t.value === selectedEvent.eventType)?.label || selectedEvent.eventType}
                    />
                  </ListItem>
                  
                  <ListItem>
                    <ListItemIcon><TimeIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Time"
                      secondary={`${new Date(selectedEvent.startTime).toLocaleString()} - ${new Date(selectedEvent.endTime).toLocaleString()}`}
                    />
                  </ListItem>
                  
                  {selectedEvent.location?.roomName && (
                    <ListItem>
                      <ListItemIcon><LocationIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Location"
                        secondary={selectedEvent.location.roomName}
                      />
                    </ListItem>
                  )}
                  
                  {selectedEvent.location?.expectedAttendance && (
                    <ListItem>
                      <ListItemIcon><AttendanceIcon /></ListItemIcon>
                      <ListItemText 
                        primary="Expected Attendance"
                        secondary={`${selectedEvent.location.expectedAttendance} people`}
                      />
                    </ListItem>
                  )}
                </List>
              </Grid>
              
              <Grid item xs={12} md={6}>
                {selectedEvent.organizer && (
                  <>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Organizer
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                        {selectedEvent.organizer.name?.charAt(0) || 'U'}
                      </Avatar>
                      <Box>
                        <Typography variant="body1">
                          {selectedEvent.organizer.name}
                        </Typography>
                        {selectedEvent.organizer.email && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <EmailIcon sx={{ fontSize: 16, mr: 0.5 }} />
                            <Typography variant="caption">
                              {selectedEvent.organizer.email}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </Box>
                  </>
                )}
                
                {selectedEvent.preparation?.checklist && selectedEvent.preparation.checklist.length > 0 && (
                  <>
                    <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                      Preparation Checklist
                    </Typography>
                    
                    <List dense>
                      {selectedEvent.preparation.checklist.slice(0, 3).map((item, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            {item.completed ? 
                              <CompleteIcon color="success" /> : 
                              <PrepIcon color="action" />
                            }
                          </ListItemIcon>
                          <ListItemText 
                            primary={item.task}
                            secondary={`${item.responsible} - ${item.completed ? 'Completed' : 'Pending'}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                    
                    {selectedEvent.preparation.checklist.length > 3 && (
                      <Typography variant="caption" color="text.secondary">
                        +{selectedEvent.preparation.checklist.length - 3} more items
                      </Typography>
                    )}
                  </>
                )}
              </Grid>
              
              {selectedEvent.description && (
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                    Description
                  </Typography>
                  <Typography variant="body2">
                    {selectedEvent.description}
                  </Typography>
                </Grid>
              )}
            </Grid>
          </DialogContent>
          
          <DialogActions>
            <Button onClick={() => setEventDetailsOpen(false)}>
              Close
            </Button>
          </DialogActions>
        </>
      )}
    </Dialog>
  );

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        <Typography variant="h6">Calendar Error</Typography>
        <Typography>{error}</Typography>
      </Alert>
    );
  }

  return (
    <Box>
      {/* Calendar Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="h5" component="h1">
            Church Events
          </Typography>
          
          <ButtonGroup size="small">
            <Button 
              variant={calendarView === 'day' ? 'contained' : 'outlined'}
              onClick={() => setCalendarView('day')}
            >
              Day
            </Button>
            <Button 
              variant={calendarView === 'week' ? 'contained' : 'outlined'}
              onClick={() => setCalendarView('week')}
            >
              Week
            </Button>
            <Button 
              variant={calendarView === 'month' ? 'contained' : 'outlined'}
              onClick={() => setCalendarView('month')}
            >
              Month
            </Button>
          </ButtonGroup>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton onClick={() => handleDateNavigation(-1)}>
            <PrevIcon />
          </IconButton>
          
          <Button onClick={handleToday} startIcon={<TodayIcon />}>
            Today
          </Button>
          
          <IconButton onClick={() => handleDateNavigation(1)}>
            <NextIcon />
          </IconButton>
        </Box>
      </Box>
      
      {/* Date Range Display */}
      <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
        {formatDateRange()}
      </Typography>
      
      {/* Calendar Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <Typography>Loading events...</Typography>
        </Box>
      ) : (
        <>
          {calendarView === 'day' && renderDayView()}
          {calendarView === 'week' && renderWeekView()}
          {calendarView === 'month' && renderMonthView()}
        </>
      )}
      
      {/* Event Details Dialog */}
      {renderEventDetailsDialog()}
    </Box>
  );
};

export default ChurchEventCalendar;