import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Badge,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Fab,
  Slide,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  ReportProblem as EmergencyIcon,
  ExitToApp as ExitIcon,
  LocalFireDepartment as FireIcon,
  LocalHospital as MedicalIcon,
  Security as SecurityIcon,
  Phone as PhoneIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Videocam as CameraIcon,
  Thermostat as TemperatureIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Close as CloseIcon,
  Water as UtilityIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';

// Import existing services for emergency actions
import unifiAccessService from '../../services/unifiAccessService';
import unifiProtectService from '../../services/unifiProtectService';
import safetyService from '../../services/safetyService';
import utilityShutoffService from '../../services/utilityShutoffService';
import bmsWebSocketService from '../../services/bmsWebSocketService';

/**
 * Emergency Mode Interface
 * Crisis-optimized interface for emergency response with one-tap actions
 * Leverages existing integrations for immediate access to critical systems
 */
const EmergencyMode = ({
  buildingId,
  floorId,
  onExit,
  onShowLayer,
  onHighlightItem,
  emergencyType = null // 'fire', 'medical', 'security', 'evacuation'
}) => {
  const [activeEmergency, setActiveEmergency] = useState(emergencyType);
  const [criticalAssets, setCriticalAssets] = useState({
    doors: [],
    cameras: [],
    safetyEquipment: [],
    shutoffs: [],
    emergencyContacts: []
  });
  const [loading, setLoading] = useState(true);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [actionStatus, setActionStatus] = useState(null);
  const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null });

  useEffect(() => {
    loadCriticalAssets();
    initializeEmergencyMode();
  }, [buildingId, floorId]);

  const initializeEmergencyMode = async () => {
    try {
      // Initialize WebSocket for real-time emergency updates
      await bmsWebSocketService.initialize();
      bmsWebSocketService.setEmergencyMode(true, buildingId);
      
      // Subscribe to emergency alerts
      bmsWebSocketService.onBMSUpdate('emergency-alert', handleEmergencyAlert);
      bmsWebSocketService.onBMSUpdate('safety-alert', handleSafetyAlert);
      
    } catch (error) {
      console.error('Failed to initialize emergency mode:', error);
    }
  };

  const loadCriticalAssets = async () => {
    try {
      setLoading(true);
      
      // Load critical assets in parallel using existing services
      const [doors, cameras, safetyEquipment, shutoffs] = await Promise.allSettled([
        unifiAccessService.getDoors({ buildingId, floorId, emergency: true }),
        unifiProtectService.getCameras({ buildingId, floorId }),
        safetyService.getSafetyAssets({ buildingId, floorId, priority: 'high' }),
        utilityShutoffService.getUtilityShutoffs({ buildingId, floorId, emergency: true })
      ]);

      setCriticalAssets({
        doors: doors.status === 'fulfilled' ? doors.value : [],
        cameras: cameras.status === 'fulfilled' ? cameras.value : [],
        safetyEquipment: safetyEquipment.status === 'fulfilled' ? safetyEquipment.value : [],
        shutoffs: shutoffs.status === 'fulfilled' ? shutoffs.value : [],
        emergencyContacts: getEmergencyContacts()
      });
      
    } catch (error) {
      console.error('Failed to load critical assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEmergencyContacts = () => [
    { name: 'Fire Department', number: '911', type: 'fire' },
    { name: 'Police', number: '911', type: 'security' },
    { name: 'Medical Emergency', number: '911', type: 'medical' },
    { name: 'Building Security', number: '(555) 123-4567', type: 'security' },
    { name: 'Facilities Manager', number: '(555) 123-4568', type: 'utility' }
  ];


  const executeEmergencyAction = async (actionType, params = {}) => {
    try {
      setActionInProgress(true);
      let result = null;

      switch (actionType) {
        case 'emergency_unlock_all':
          result = await emergencyUnlockAllDoors();
          break;
        case 'lock_building':
          result = await lockBuilding();
          break;
        case 'view_all_cameras':
          result = await viewAllCameras();
          break;
        case 'find_nearest_safety':
          result = await findNearestSafetyEquipment(params.equipmentType);
          break;
        case 'emergency_shutoff':
          result = await emergencyShutoff(params.utilityType);
          break;
        case 'call_emergency':
          result = await callEmergencyContact(params.contactType);
          break;
        default:
          throw new Error(`Unknown emergency action: ${actionType}`);
      }

      setActionStatus({
        type: 'success',
        message: result.message || 'Action completed successfully',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Emergency action failed:', error);
      setActionStatus({
        type: 'error',
        message: error.message || 'Action failed',
        timestamp: new Date()
      });
    } finally {
      setActionInProgress(false);
      setConfirmDialog({ open: false, action: null });
    }
  };

  const emergencyUnlockAllDoors = async () => {
    const results = await Promise.allSettled(
      criticalAssets.doors.map(door => 
        unifiAccessService.unlockDoor(door.id, { duration: 0, emergency: true })
      )
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    // Highlight unlocked doors on map
    criticalAssets.doors.forEach(door => {
      onHighlightItem(door.id, 'emergency_unlock');
    });
    
    return {
      message: `Emergency unlock: ${successful} doors unlocked, ${failed} failed`,
      successful,
      failed
    };
  };

  const lockBuilding = async () => {
    const results = await Promise.allSettled(
      criticalAssets.doors.map(door => 
        unifiAccessService.lockDoor(door.id, { emergency: true })
      )
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    return { message: `Building secured: ${successful} doors locked` };
  };

  const viewAllCameras = async () => {
    // Show camera layer and highlight all cameras
    onShowLayer('cameras', true);
    criticalAssets.cameras.forEach(camera => {
      onHighlightItem(camera.id, 'emergency_view');
    });
    
    return { message: `${criticalAssets.cameras.length} cameras now visible` };
  };

  const findNearestSafetyEquipment = async (equipmentType) => {
    const equipment = criticalAssets.safetyEquipment.filter(
      item => item.assetType === equipmentType
    );
    
    // Show safety layer and highlight nearest equipment
    onShowLayer('safety', true);
    equipment.slice(0, 3).forEach(item => {
      onHighlightItem(item.id, 'emergency_safety');
    });
    
    return { message: `${equipment.length} ${equipmentType} units highlighted` };
  };

  const emergencyShutoff = async (utilityType) => {
    const shutoffs = criticalAssets.shutoffs.filter(
      shutoff => shutoff.utilityType === utilityType
    );
    
    // Show utility layer and highlight shutoffs
    onShowLayer('utility', true);
    shutoffs.forEach(shutoff => {
      onHighlightItem(shutoff.id, 'emergency_shutoff');
    });
    
    return { message: `${shutoffs.length} ${utilityType} shutoffs located` };
  };

  const callEmergencyContact = async (contactType) => {
    const contact = criticalAssets.emergencyContacts.find(c => c.type === contactType);
    if (contact) {
      // Log emergency call for audit trail
      console.log(`Emergency call initiated: ${contactType} - ${contact.name} at ${new Date().toISOString()}`);
      
      // Record emergency action in system logs
      try {
        await bmsWebSocketService.sendControlCommand('emergency', 'call_log', 'log_emergency_call', {
          contactType,
          contactName: contact.name,
          contactNumber: contact.number,
          buildingId,
          floorId,
          timestamp: new Date().toISOString(),
          initiatedBy: 'Emergency Mode Interface'
        });
      } catch (error) {
        console.error('Failed to log emergency call:', error);
      }
      
      // Use both tel: link and optional SIP/VoIP integration
      window.open(`tel:${contact.number}`);
      
      // Voice announcement for accessibility
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(
          `Emergency call placed to ${contact.name}. Help is on the way.`
        );
        utterance.rate = 0.8;
        utterance.pitch = 1.0;
        window.speechSynthesis.speak(utterance);
      }
      
      return { message: `Calling ${contact.name} at ${contact.number}` };
    }
    throw new Error('Emergency contact not found');
  };

  const handleEmergencyAlert = (alertData) => {
    // Handle real-time emergency alerts from WebSocket
    setActionStatus({
      type: 'alert',
      message: alertData.message || 'Emergency alert received',
      severity: alertData.severity || 'error',
      timestamp: new Date()
    });

    // Voice announcement for critical alerts
    if (alertData.severity === 'critical' && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(
        `Critical emergency alert: ${alertData.message}`
      );
      utterance.rate = 0.9;
      utterance.pitch = 1.2;
      window.speechSynthesis.speak(utterance);
    }
  };

  const handleSafetyAlert = (alertData) => {
    // Handle safety-specific alerts
    setActionStatus({
      type: 'safety',
      message: alertData.message || 'Safety alert received',
      severity: alertData.severity || 'warning',
      timestamp: new Date()
    });
  };

  const executeQuickAction = async (actionType, params = {}) => {
    try {
      setActionInProgress(true);
      
      // Log all emergency actions for audit trail
      const auditLog = {
        action: actionType,
        params,
        buildingId,
        floorId,
        timestamp: new Date().toISOString(),
        user: 'Emergency Mode Interface'
      };
      
      console.log('Emergency action executed:', auditLog);
      
      let result;
      switch (actionType) {
        case 'emergency_unlock_all':
          result = await emergencyUnlockAll();
          break;
        case 'lock_building':
          result = await lockBuilding();
          break;
        case 'view_all_cameras':
          result = await viewAllCameras();
          break;
        case 'find_nearest_safety':
          result = await findNearestSafetyEquipment(params.equipmentType);
          break;
        case 'emergency_shutoff':
          result = await emergencyShutoff(params.utilityType);
          break;
        case 'call_emergency':
          result = await callEmergencyContact(params.contactType);
          break;
        default:
          throw new Error(`Unknown emergency action: ${actionType}`);
      }
      
      // Send audit log to server
      try {
        await bmsWebSocketService.sendControlCommand('emergency', 'audit_log', 'log_emergency_action', {
          ...auditLog,
          result: result.message,
          success: true
        });
      } catch (logError) {
        console.error('Failed to send audit log:', logError);
      }
      
      setActionStatus({
        type: 'success',
        message: result.message,
        severity: 'success',
        timestamp: new Date()
      });
      
    } catch (error) {
      console.error('Emergency action failed:', error);
      
      // Log failure
      try {
        await bmsWebSocketService.sendControlCommand('emergency', 'audit_log', 'log_emergency_action', {
          action: actionType,
          params,
          buildingId,
          floorId,
          timestamp: new Date().toISOString(),
          user: 'Emergency Mode Interface',
          error: error.message,
          success: false
        });
      } catch (logError) {
        console.error('Failed to send failure audit log:', logError);
      }
      
      setActionStatus({
        type: 'error',
        message: `Failed: ${error.message}`,
        severity: 'error',
        timestamp: new Date()
      });
    } finally {
      setActionInProgress(false);
      setConfirmDialog({ open: false, action: null });
    }
  };

  const confirmAction = (actionType, params = {}) => {
    setConfirmDialog({
      open: true,
      action: { type: actionType, params },
      title: getActionTitle(actionType),
      description: getActionDescription(actionType, params)
    });
  };

  const getActionTitle = (actionType) => {
    const titles = {
      emergency_unlock_all: 'Emergency Unlock All Doors',
      lock_building: 'Lock Down Building',
      view_all_cameras: 'View All Cameras',
      find_nearest_safety: 'Locate Safety Equipment',
      emergency_shutoff: 'Emergency Utility Shutoff',
      call_emergency: 'Call Emergency Services'
    };
    return titles[actionType] || 'Confirm Action';
  };

  const getActionDescription = (actionType, params) => {
    const descriptions = {
      emergency_unlock_all: 'This will unlock ALL doors in the building for emergency evacuation.',
      lock_building: 'This will lock ALL doors in the building for security lockdown.',
      view_all_cameras: 'This will display all security cameras on the floor plan.',
      find_nearest_safety: `This will locate the nearest ${params.equipmentType || 'safety equipment'}.`,
      emergency_shutoff: `This will locate ${params.utilityType || 'utility'} shutoff valves.`,
      call_emergency: 'This will initiate an emergency call.'
    };
    return descriptions[actionType] || 'Are you sure you want to perform this action?';
  };

  const exitEmergencyMode = () => {
    // Cleanup emergency mode
    bmsWebSocketService.setEmergencyMode(false, buildingId);
    onExit();
  };

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <LinearProgress color="error" />
        <Typography variant="h6" sx={{ mt: 2, color: 'error.main' }}>
          Loading Emergency Interface...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100vh', bgcolor: 'error.dark', color: 'error.contrastText', overflow: 'auto' }}>
      {/* Emergency Header */}
      <Paper 
        sx={{ 
          p: 2, 
          bgcolor: 'error.main', 
          color: 'error.contrastText',
          borderRadius: 0
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <EmergencyIcon sx={{ fontSize: 40 }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700 }}>
                EMERGENCY MODE
              </Typography>
              <Typography variant="subtitle1">
                Building: {buildingId} | Floor: {floorId}
              </Typography>
            </Box>
          </Box>
          
          <Button
            variant="contained"
            startIcon={<ExitIcon />}
            onClick={exitEmergencyMode}
            sx={{ bgcolor: 'error.light', '&:hover': { bgcolor: 'error.main' } }}
          >
            Exit Emergency Mode
          </Button>
        </Box>
      </Paper>

      {/* Action Status */}
      {actionStatus && (
        <Slide in={true} direction="down">
          <Alert 
            severity={actionStatus.type === 'error' ? 'error' : 'success'}
            sx={{ m: 2 }}
            onClose={() => setActionStatus(null)}
          >
            {actionStatus.message}
          </Alert>
        </Slide>
      )}

      {/* Quick Emergency Actions */}
      <Grid container spacing={2} sx={{ p: 2 }}>
        {/* Doors & Access */}
        <Grid item xs={12} md={6}>
          <Card sx={{ bgcolor: 'error.light', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LockIcon />
                Doors & Access
                <Badge badgeContent={criticalAssets.doors.length} color="secondary" />
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  fullWidth
                  size="large"
                  color="warning"
                  startIcon={<UnlockIcon />}
                  onClick={() => confirmAction('emergency_unlock_all')}
                  disabled={actionInProgress}
                >
                  EMERGENCY UNLOCK ALL
                </Button>
                
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<LockIcon />}
                  onClick={() => confirmAction('lock_building')}
                  disabled={actionInProgress}
                  sx={{ borderColor: 'white', color: 'white' }}
                >
                  Lock Building
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Cameras & Security */}
        <Grid item xs={12} md={6}>
          <Card sx={{ bgcolor: 'error.light', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CameraIcon />
                Cameras & Security
                <Badge badgeContent={criticalAssets.cameras.length} color="secondary" />
              </Typography>
              
              <Button
                variant="contained"
                fullWidth
                size="large"
                startIcon={<ViewIcon />}
                onClick={() => executeEmergencyAction('view_all_cameras')}
                disabled={actionInProgress}
              >
                View All Cameras
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Safety Equipment */}
        <Grid item xs={12} md={6}>
          <Card sx={{ bgcolor: 'error.light', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FireIcon />
                Safety Equipment
                <Badge badgeContent={criticalAssets.safetyEquipment.length} color="secondary" />
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<FireIcon />}
                  onClick={() => executeEmergencyAction('find_nearest_safety', { equipmentType: 'fire_extinguisher' })}
                  disabled={actionInProgress}
                >
                  Find Fire Extinguishers
                </Button>
                
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<MedicalIcon />}
                  onClick={() => executeEmergencyAction('find_nearest_safety', { equipmentType: 'aed' })}
                  disabled={actionInProgress}
                  sx={{ borderColor: 'white', color: 'white' }}
                >
                  Find AEDs
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Utility Shutoffs */}
        <Grid item xs={12} md={6}>
          <Card sx={{ bgcolor: 'error.light', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <UtilityIcon />
                Emergency Shutoffs
                <Badge badgeContent={criticalAssets.shutoffs.length} color="secondary" />
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<UtilityIcon />}
                  onClick={() => executeEmergencyAction('emergency_shutoff', { utilityType: 'water' })}
                  disabled={actionInProgress}
                >
                  Water Shutoffs
                </Button>
                
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<UtilityIcon />}
                  onClick={() => executeEmergencyAction('emergency_shutoff', { utilityType: 'gas' })}
                  disabled={actionInProgress}
                  sx={{ borderColor: 'white', color: 'white' }}
                >
                  Gas Shutoffs
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Emergency Contacts */}
      <Card sx={{ m: 2, bgcolor: 'error.light' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PhoneIcon />
            Emergency Contacts
          </Typography>
          
          <Grid container spacing={1}>
            {criticalAssets.emergencyContacts.map((contact, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<PhoneIcon />}
                  onClick={() => executeEmergencyAction('call_emergency', { contactType: contact.type })}
                  sx={{ 
                    borderColor: 'white', 
                    color: 'white',
                    justifyContent: 'flex-start'
                  }}
                >
                  <Box sx={{ textAlign: 'left' }}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {contact.name}
                    </Typography>
                    <Typography variant="caption">
                      {contact.number}
                    </Typography>
                  </Box>
                </Button>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, action: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ bgcolor: 'error.main', color: 'error.contrastText' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon />
            {confirmDialog.title}
          </Box>
        </DialogTitle>
        
        <DialogContent sx={{ pt: 2 }}>
          <Typography>
            {confirmDialog.description}
          </Typography>
          
          <Alert severity="warning" sx={{ mt: 2 }}>
            This action will be logged and may trigger additional emergency protocols.
          </Alert>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setConfirmDialog({ open: false, action: null })}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => executeEmergencyAction(confirmDialog.action?.type, confirmDialog.action?.params)}
            disabled={actionInProgress}
          >
            {actionInProgress ? 'Processing...' : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Emergency Action Fab */}
      <Fab
        color="error"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        disabled={actionInProgress}
      >
        <EmergencyIcon />
      </Fab>
    </Box>
  );
};

export default EmergencyMode;