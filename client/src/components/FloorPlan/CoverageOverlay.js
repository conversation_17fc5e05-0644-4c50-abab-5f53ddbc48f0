import React, { useState, useEffect, useRef } from 'react';
import { Box, Paper, Typography, IconButton, Tooltip, Slider, Chip } from '@mui/material';
import { 
  Wifi as WiFiIcon, 
  Thermostat as TemperatureIcon,
  Opacity as OpacityIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';

// Import existing services to leverage integrations
import hvacService from '../../services/hvacService';
import wifiService from '../../services/wifiService';

/**
 * CoverageOverlay Component
 * Renders coverage overlays (WiFi signal strength, temperature heatmaps) on floor plans
 * Leverages existing service integrations for data sources
 */
const CoverageOverlay = ({
  type = 'wifi', // 'wifi' or 'temperature'
  buildingId,
  floorId,
  canvasRef,
  visible = true,
  opacity = 0.6,
  onOpacityChange,
  realTimeUpdates = true
}) => {
  const [coverageData, setCoverageData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const overlayCanvasRef = useRef(null);
  const animationFrameRef = useRef(null);

  useEffect(() => {
    if (visible && buildingId && floorId) {
      loadCoverageData();
    }
  }, [type, buildingId, floorId, visible]);

  useEffect(() => {
    if (coverageData && visible) {
      renderOverlay();
    }
  }, [coverageData, visible, opacity]);

  useEffect(() => {
    let interval;
    if (realTimeUpdates && visible) {
      interval = setInterval(() => {
        loadCoverageData();
      }, 30000); // Update every 30 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [realTimeUpdates, visible, type, buildingId, floorId]);

  const loadCoverageData = async () => {
    try {
      setLoading(true);
      setError(null);

      let data;
      switch (type) {
        case 'wifi':
          data = await loadWiFiCoverageData();
          break;
        case 'temperature':
          data = await loadTemperatureHeatmapData();
          break;
        default:
          throw new Error(`Unsupported coverage type: ${type}`);
      }

      setCoverageData(data);
    } catch (err) {
      console.error('Error loading coverage data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadWiFiCoverageData = async () => {
    // Use existing WiFi service with UniFi integration
    const accessPoints = await wifiService.getAccessPoints(buildingId, floorId);
    const coverageAreas = await wifiService.getCoverageAreas(buildingId, floorId);
    
    // Generate coverage grid based on AP positions and signal strengths
    const coverage = generateWiFiCoverageGrid(accessPoints, coverageAreas);
    
    return {
      type: 'wifi',
      accessPoints,
      coverageAreas,
      grid: coverage,
      colorScale: [
        { value: -30, color: [0, 255, 0, 255] },    // Excellent (green)
        { value: -50, color: [255, 255, 0, 255] },  // Good (yellow)
        { value: -70, color: [255, 165, 0, 255] },  // Fair (orange)
        { value: -90, color: [255, 0, 0, 255] },    // Poor (red)
        { value: -100, color: [128, 128, 128, 255] } // No signal (gray)
      ]
    };
  };

  const loadTemperatureHeatmapData = async () => {
    // Use enhanced HVAC service that consolidates multiple sources
    const tempData = await hvacService.getTemperatureData(buildingId, floorId, {
      includeHeatmap: true,
      sources: ['hvac', 'dreo', 'govee', 'skyport', 'lgThinq']
    });
    
    // Generate interpolated temperature grid
    const heatmap = generateTemperatureHeatmap(tempData.sensors, tempData.zones);
    
    return {
      type: 'temperature',
      sensors: tempData.sensors,
      zones: tempData.zones,
      grid: heatmap,
      colorScale: [
        { value: 60, color: [0, 0, 255, 255] },     // Cold (blue)
        { value: 68, color: [0, 255, 255, 255] },   // Cool (cyan)
        { value: 72, color: [0, 255, 0, 255] },     // Comfortable (green)
        { value: 76, color: [255, 255, 0, 255] },   // Warm (yellow)
        { value: 80, color: [255, 165, 0, 255] },   // Hot (orange)
        { value: 85, color: [255, 0, 0, 255] }      // Very hot (red)
      ]
    };
  };

  const generateWiFiCoverageGrid = (accessPoints, coverageAreas) => {
    if (!canvasRef?.current) return null;

    const canvas = canvasRef.current;
    const gridWidth = Math.floor(canvas.width / 10);
    const gridHeight = Math.floor(canvas.height / 10);
    const grid = [];

    for (let y = 0; y < gridHeight; y++) {
      const row = [];
      for (let x = 0; x < gridWidth; x++) {
        const worldX = (x / gridWidth) * canvas.width;
        const worldY = (y / gridHeight) * canvas.height;
        
        // Calculate signal strength at this point based on AP positions
        let maxSignal = -100; // Start with no signal
        
        accessPoints.forEach(ap => {
          if (ap.x !== undefined && ap.y !== undefined) {
            const distance = Math.sqrt(
              Math.pow(worldX - ap.x, 2) + Math.pow(worldY - ap.y, 2)
            );
            
            // Simple signal propagation model
            const signalStrength = ap.signalStrength || -30;
            const distanceAttenuation = Math.max(-90, signalStrength - (distance / 10));
            maxSignal = Math.max(maxSignal, distanceAttenuation);
          }
        });
        
        row.push(maxSignal);
      }
      grid.push(row);
    }

    return { width: gridWidth, height: gridHeight, data: grid };
  };

  const generateTemperatureHeatmap = (sensors, zones) => {
    if (!canvasRef?.current || !sensors?.length) return null;

    const canvas = canvasRef.current;
    const gridWidth = Math.floor(canvas.width / 20);
    const gridHeight = Math.floor(canvas.height / 20);
    const grid = [];

    for (let y = 0; y < gridHeight; y++) {
      const row = [];
      for (let x = 0; x < gridWidth; x++) {
        const worldX = (x / gridWidth) * canvas.width;
        const worldY = (y / gridHeight) * canvas.height;
        
        // Interpolate temperature using inverse distance weighting
        let temperature = interpolateTemperature(worldX, worldY, sensors);
        row.push(temperature);
      }
      grid.push(row);
    }

    return { width: gridWidth, height: gridHeight, data: grid };
  };

  const interpolateTemperature = (x, y, sensors) => {
    if (!sensors || sensors.length === 0) return 72; // Default comfortable temperature

    let weightedSum = 0;
    let weightSum = 0;

    sensors.forEach(sensor => {
      if (sensor.x !== undefined && sensor.y !== undefined && sensor.temperature !== undefined) {
        const distance = Math.sqrt(
          Math.pow(x - sensor.x, 2) + Math.pow(y - sensor.y, 2)
        );
        
        // Avoid division by zero
        const weight = distance === 0 ? 1000 : 1 / (distance + 1);
        
        weightedSum += sensor.temperature * weight;
        weightSum += weight;
      }
    });

    return weightSum > 0 ? weightedSum / weightSum : 72;
  };

  const renderOverlay = () => {
    if (!overlayCanvasRef.current || !coverageData?.grid || !visible) return;

    const canvas = overlayCanvasRef.current;
    const ctx = canvas.getContext('2d');
    const { grid, colorScale } = coverageData;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set global opacity
    ctx.globalAlpha = opacity;

    const cellWidth = canvas.width / grid.width;
    const cellHeight = canvas.height / grid.height;

    // Render grid cells
    for (let y = 0; y < grid.height; y++) {
      for (let x = 0; x < grid.width; x++) {
        const value = grid.data[y][x];
        const color = interpolateColor(value, colorScale);
        
        ctx.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, ${color[3] / 255})`;
        ctx.fillRect(x * cellWidth, y * cellHeight, cellWidth, cellHeight);
      }
    }

    // Reset global alpha
    ctx.globalAlpha = 1.0;
  };

  const interpolateColor = (value, colorScale) => {
    // Find the two colors to interpolate between
    for (let i = 0; i < colorScale.length - 1; i++) {
      const lower = colorScale[i];
      const upper = colorScale[i + 1];
      
      if (value >= lower.value && value <= upper.value) {
        const ratio = (value - lower.value) / (upper.value - lower.value);
        return [
          Math.round(lower.color[0] + (upper.color[0] - lower.color[0]) * ratio),
          Math.round(lower.color[1] + (upper.color[1] - lower.color[1]) * ratio),
          Math.round(lower.color[2] + (upper.color[2] - lower.color[2]) * ratio),
          Math.round(lower.color[3] + (upper.color[3] - lower.color[3]) * ratio)
        ];
      }
    }
    
    // Default to the last color if value is beyond range
    return colorScale[colorScale.length - 1].color;
  };

  const getOverlayStyle = () => {
    if (!canvasRef?.current) return {};

    const parentCanvas = canvasRef.current;
    return {
      position: 'absolute',
      top: 0,
      left: 0,
      width: parentCanvas.width,
      height: parentCanvas.height,
      pointerEvents: 'none',
      opacity: visible ? 1 : 0,
      transition: 'opacity 0.3s ease'
    };
  };

  const getLegendItems = () => {
    if (!coverageData?.colorScale) return [];

    return coverageData.colorScale.map((item, index) => ({
      value: item.value,
      color: `rgba(${item.color[0]}, ${item.color[1]}, ${item.color[2]}, 0.8)`,
      label: type === 'wifi' 
        ? `${item.value} dBm` 
        : `${item.value}°F`
    }));
  };

  if (!visible) return null;

  return (
    <>
      {/* Overlay Canvas */}
      <canvas
        ref={overlayCanvasRef}
        style={getOverlayStyle()}
        width={canvasRef?.current?.width || 800}
        height={canvasRef?.current?.height || 600}
      />

      {/* Legend */}
      <Paper
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 16,
          p: 2,
          bgcolor: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          minWidth: 200
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          {type === 'wifi' ? <WiFiIcon /> : <TemperatureIcon />}
          <Typography variant="subtitle2" fontWeight="bold">
            {type === 'wifi' ? 'WiFi Signal Strength' : 'Temperature'}
          </Typography>
        </Box>

        {/* Opacity Control */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" gutterBottom>
            Opacity
          </Typography>
          <Slider
            size="small"
            value={opacity * 100}
            onChange={(e, value) => onOpacityChange?.(value / 100)}
            min={10}
            max={100}
            step={10}
            valueLabelDisplay="auto"
            valueLabelFormat={(value) => `${value}%`}
          />
        </Box>

        {/* Color Legend */}
        <Box>
          <Typography variant="caption" gutterBottom>
            Legend
          </Typography>
          {getLegendItems().map((item, index) => (
            <Box 
              key={index} 
              sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1, 
                mb: 0.5 
              }}
            >
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  backgroundColor: item.color,
                  border: '1px solid #ccc',
                  borderRadius: 0.5
                }}
              />
              <Typography variant="caption">
                {item.label}
              </Typography>
            </Box>
          ))}
        </Box>

        {/* Status Indicators */}
        <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
          {loading && (
            <Chip 
              label="Updating..." 
              size="small" 
              color="info" 
              variant="outlined"
            />
          )}
          {error && (
            <Chip 
              label="Error" 
              size="small" 
              color="error" 
              variant="outlined"
            />
          )}
          {realTimeUpdates && (
            <Chip 
              label="Live" 
              size="small" 
              color="success" 
              variant="outlined"
            />
          )}
        </Box>
      </Paper>
    </>
  );
};

export default CoverageOverlay;