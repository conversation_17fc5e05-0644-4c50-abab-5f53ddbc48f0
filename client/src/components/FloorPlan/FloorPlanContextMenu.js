import React from 'react';
import {
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
  Power as ElectricalIcon,
  NavigateNext as NavigateIcon,
  Camera as CameraIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Thermostat as TemperatureIcon,
  Air as HVACIcon,
  Wifi as WiFiIcon,
  LocalFireDepartment as SafetyIcon,
  Water as UtilityIcon,
  Event as EventIcon,
  Build as ServiceIcon,
  History as HistoryIcon,
  ContentCopy as CopyIcon,
  Launch as OpenIcon
} from '@mui/icons-material';

/**
 * FloorPlanContextMenu Component
 * Provides context-sensitive actions for floorplan items via right-click or long-press
 * Integrates with existing services and maintains BMS functionality
 */
const FloorPlanContextMenu = ({
  anchorEl,
  open,
  onClose,
  selectedItem,
  onAction,
  userRole = 'viewer', // 'admin', 'editor', 'viewer'
  emergencyMode = false,
  kioskMode = false
}) => {
  if (!selectedItem) return null;

  const { type, customType, deviceId, data, metadata } = selectedItem;

  // Define available actions based on item type and user permissions
  const getAvailableActions = () => {
    const actions = [];

    // View/Inspect actions (available to all users)
    actions.push({
      id: 'inspect',
      label: 'Inspect Details',
      icon: <InfoIcon />,
      action: () => onAction('inspect', selectedItem),
      emergency: true
    });

    // Type-specific quick actions
    switch (type) {
      case 'door':
        if (!kioskMode) {
          actions.push({
            id: 'view_camera',
            label: 'View Door Camera',
            icon: <CameraIcon />,
            action: () => onAction('viewCamera', selectedItem),
            emergency: true
          });
          
          if (userRole !== 'viewer') {
            const isLocked = data?.value === 'locked';
            actions.push({
              id: 'toggle_lock',
              label: isLocked ? 'Unlock Door' : 'Lock Door',
              icon: isLocked ? <UnlockIcon /> : <LockIcon />,
              action: () => onAction('toggleLock', selectedItem),
              emergency: true
            });
          }
        }
        break;

      case 'camera':
        actions.push({
          id: 'view_live',
          label: 'View Live Feed',
          icon: <CameraIcon />,
          action: () => onAction('viewLive', selectedItem),
          emergency: true
        });
        
        if (!kioskMode && userRole !== 'viewer') {
          actions.push({
            id: 'camera_controls',
            label: 'Camera Controls',
            icon: <EditIcon />,
            action: () => onAction('cameraControls', selectedItem)
          });
        }
        break;

      case 'power':
        if (customType === 'outlet') {
          actions.push({
            id: 'trace_circuit',
            label: 'Trace to Panel',
            icon: <NavigateIcon />,
            action: () => onAction('traceCircuit', selectedItem),
            emergency: true
          });
        } else if (customType === 'panel') {
          actions.push({
            id: 'view_schedule',
            label: 'View Panel Schedule',
            icon: <ViewIcon />,
            action: () => onAction('viewSchedule', selectedItem),
            emergency: true
          });
        }
        
        if (!kioskMode && userRole === 'admin') {
          actions.push({
            id: 'electrical_admin',
            label: 'Electrical Admin',
            icon: <ElectricalIcon />,
            action: () => onAction('electricalAdmin', selectedItem)
          });
        }
        break;

      case 'temperature':
        actions.push({
          id: 'temperature_history',
          label: 'Temperature History',
          icon: <HistoryIcon />,
          action: () => onAction('temperatureHistory', selectedItem)
        });
        break;

      case 'hvac':
        actions.push({
          id: 'hvac_status',
          label: 'HVAC Status',
          icon: <HVACIcon />,
          action: () => onAction('hvacStatus', selectedItem),
          emergency: true
        });
        
        if (!kioskMode && userRole !== 'viewer') {
          actions.push({
            id: 'hvac_service',
            label: 'Service Tracker',
            icon: <ServiceIcon />,
            action: () => onAction('hvacService', selectedItem)
          });
        }
        break;

      case 'network':
        if (customType === 'wifi-ap') {
          actions.push({
            id: 'wifi_status',
            label: 'WiFi AP Status',
            icon: <WiFiIcon />,
            action: () => onAction('wifiStatus', selectedItem)
          });
          
          if (!kioskMode && userRole !== 'viewer') {
            actions.push({
              id: 'wifi_management',
              label: 'WiFi Management',
              icon: <EditIcon />,
              action: () => onAction('wifiManagement', selectedItem)
            });
          }
        }
        break;

      case 'fire':
        actions.push({
          id: 'nearest_safety',
          label: 'Find Nearest Equipment',
          icon: <SafetyIcon />,
          action: () => onAction('findNearest', selectedItem),
          emergency: true
        });
        
        if (!kioskMode && userRole !== 'viewer') {
          actions.push({
            id: 'safety_inspection',
            label: 'Safety Inspection',
            icon: <ServiceIcon />,
            action: () => onAction('safetyInspection', selectedItem)
          });
        }
        break;

      case 'water':
        if (customType === 'shutoff') {
          actions.push({
            id: 'emergency_shutoff',
            label: 'Emergency Shutoff',
            icon: <UtilityIcon />,
            action: () => onAction('emergencyShutoff', selectedItem),
            emergency: true
          });
          
          actions.push({
            id: 'shutoff_procedure',
            label: 'View Procedure',
            icon: <InfoIcon />,
            action: () => onAction('shutoffProcedure', selectedItem),
            emergency: true
          });
        }
        break;

      case 'custom':
        if (customType === 'event') {
          actions.push({
            id: 'event_details',
            label: 'Event Details',
            icon: <EventIcon />,
            action: () => onAction('eventDetails', selectedItem)
          });
        }
        break;
    }

    // Common actions for non-kiosk mode
    if (!kioskMode) {
      actions.push({ divider: true });
      
      actions.push({
        id: 'copy_location',
        label: 'Copy Location',
        icon: <CopyIcon />,
        action: () => onAction('copyLocation', selectedItem)
      });

      // Navigation actions
      if (metadata?.room) {
        actions.push({
          id: 'navigate_to',
          label: `Navigate to ${metadata.room}`,
          icon: <NavigateIcon />,
          action: () => onAction('navigateTo', selectedItem)
        });
      }

      // Admin actions
      if (userRole === 'admin' && !emergencyMode) {
        actions.push({ divider: true });
        
        actions.push({
          id: 'edit_item',
          label: 'Edit Item',
          icon: <EditIcon />,
          action: () => onAction('edit', selectedItem)
        });
        
        actions.push({
          id: 'view_history',
          label: 'View History',
          icon: <HistoryIcon />,
          action: () => onAction('viewHistory', selectedItem)
        });
        
        actions.push({
          id: 'delete_item',
          label: 'Delete Item',
          icon: <DeleteIcon />,
          action: () => onAction('delete', selectedItem)
        });
      }
    }

    // Filter actions based on mode
    if (emergencyMode) {
      return actions.filter(action => action.emergency || action.divider);
    }

    return actions;
  };

  const availableActions = getAvailableActions();

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          minWidth: 200,
          '& .MuiMenuItem-root': {
            px: 2,
            py: 1
          }
        }
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'center'
      }}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'center'
      }}
    >
      {/* Header */}
      <MenuItem disabled>
        <Typography variant="subtitle2" fontWeight="bold">
          {selectedItem.label || `${type} ${customType || ''}`}
        </Typography>
      </MenuItem>
      
      {selectedItem.room && (
        <MenuItem disabled>
          <Typography variant="caption" color="text.secondary">
            {selectedItem.room}
          </Typography>
        </MenuItem>
      )}
      
      <Divider />

      {/* Actions */}
      {availableActions.map((action, index) => {
        if (action.divider) {
          return <Divider key={`divider-${index}`} />;
        }

        return (
          <MenuItem
            key={action.id}
            onClick={() => {
              action.action();
              onClose();
            }}
            disabled={action.disabled}
          >
            <ListItemIcon>
              {action.icon}
            </ListItemIcon>
            <ListItemText>
              {action.label}
            </ListItemText>
          </MenuItem>
        );
      })}

      {availableActions.length === 1 && (
        <MenuItem disabled>
          <Typography variant="caption" color="text.secondary">
            No actions available
          </Typography>
        </MenuItem>
      )}
    </Menu>
  );
};

export default FloorPlanContextMenu;