import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Close as CloseIcon,
  Thermostat as TemperatureIcon,
  Schedule as ScheduleIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Videocam as CameraIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon
} from '@mui/icons-material';

// Import existing services for layer interactions
import hvacService from '../../services/hvacService';
import unifiAccessService from '../../services/unifiAccessService';
import unifiProtectService from '../../services/unifiProtectService';

/**
 * LayerInteractions Component
 * Provides interactive overlays and controls for different layer types
 * Implements behaviors still marked as [Planned] in bmsplan.md
 */

/**
 * Temperature Heatmap Interaction Component
 * Provides time window controls and hover/inspect capabilities
 */
export const TemperatureHeatmapInteraction = ({
  buildingId,
  floorId,
  onTemperatureData,
  onTimeWindowChange
}) => {
  const [timeWindow, setTimeWindow] = useState('now');
  const [temperatureData, setTemperatureData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hoveredPoint, setHoveredPoint] = useState(null);

  const timeWindows = [
    { value: 'now', label: 'Current', description: 'Real-time readings' },
    { value: '1h', label: '1 Hour Avg', description: 'Average over last hour' },
    { value: '24h', label: '24 Hour Avg', description: 'Average over last 24 hours' },
    { value: '7d', label: '7 Day Trend', description: 'Trend over last week' }
  ];

  useEffect(() => {
    loadTemperatureData();
  }, [timeWindow, buildingId, floorId]);

  const loadTemperatureData = async () => {
    try {
      setLoading(true);
      
      // Use enhanced HVAC service with time window support
      const data = await hvacService.getTemperatureData(buildingId, floorId, {
        timeWindow,
        includeHeatmap: true,
        includeTrends: timeWindow !== 'now'
      });

      setTemperatureData(data);
      onTemperatureData?.(data);
      
    } catch (error) {
      console.error('Failed to load temperature data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTimeWindowChange = (newWindow) => {
    setTimeWindow(newWindow);
    onTimeWindowChange?.(newWindow);
  };

  const handlePointHover = (pointData) => {
    setHoveredPoint(pointData);
  };

  return (
    <Box sx={{ position: 'relative' }}>
      {/* Temperature Controls */}
      <Paper 
        sx={{ 
          position: 'absolute',
          top: 16,
          right: 16,
          p: 2,
          minWidth: 280,
          zIndex: 1100,
          bgcolor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(8px)'
        }}
      >
        <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TemperatureIcon />
          Temperature Heatmap
          {loading && <CircularProgress size={16} />}
        </Typography>

        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel>Time Window</InputLabel>
          <Select
            value={timeWindow}
            onChange={(e) => handleTimeWindowChange(e.target.value)}
            label="Time Window"
          >
            {timeWindows.map((window) => (
              <MenuItem key={window.value} value={window.value}>
                <Box>
                  <Typography variant="body2">{window.label}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {window.description}
                  </Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {temperatureData && (
          <Box>
            <Typography variant="caption" gutterBottom>
              Temperature Range: {temperatureData.minTemp?.toFixed(1)}°F - {temperatureData.maxTemp?.toFixed(1)}°F
            </Typography>
            <Typography variant="caption" display="block" gutterBottom>
              Active Sensors: {temperatureData.sensors?.length || 0}
            </Typography>
            
            {timeWindow !== 'now' && temperatureData.trend && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  Trend: {temperatureData.trend > 0 ? '↗ Rising' : temperatureData.trend < 0 ? '↘ Falling' : '→ Stable'}
                </Typography>
              </Box>
            )}
          </Box>
        )}

        <Button
          size="small"
          startIcon={<RefreshIcon />}
          onClick={loadTemperatureData}
          disabled={loading}
          sx={{ mt: 1 }}
        >
          Refresh
        </Button>
      </Paper>

      {/* Hover Tooltip */}
      {hoveredPoint && (
        <Paper
          sx={{
            position: 'absolute',
            left: hoveredPoint.x + 10,
            top: hoveredPoint.y - 10,
            p: 1,
            zIndex: 1200,
            bgcolor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            borderRadius: 1
          }}
        >
          <Typography variant="caption">
            {hoveredPoint.temperature?.toFixed(1)}°F
          </Typography>
          {hoveredPoint.humidity && (
            <Typography variant="caption" display="block">
              Humidity: {hoveredPoint.humidity?.toFixed(1)}%
            </Typography>
          )}
          {hoveredPoint.source && (
            <Typography variant="caption" display="block">
              Source: {hoveredPoint.source}
            </Typography>
          )}
        </Paper>
      )}
    </Box>
  );
};

/**
 * Door Control Interaction Component
 * Provides lock/unlock controls and status display
 */
export const DoorControlInteraction = ({
  doorId,
  doorData,
  onDoorAction,
  emergencyMode = false,
  userPermissions = {}
}) => {
  const [doorStatus, setDoorStatus] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);

  useEffect(() => {
    if (doorId) {
      loadDoorStatus();
      // Set up real-time updates
      const interval = setInterval(loadDoorStatus, 5000);
      return () => clearInterval(interval);
    }
  }, [doorId]);

  const loadDoorStatus = async () => {
    try {
      const status = await unifiAccessService.getDoorStatus(doorId);
      setDoorStatus(status);
    } catch (error) {
      console.error('Failed to load door status:', error);
    }
  };

  const handleDoorAction = async (action, params = {}) => {
    if (!userPermissions.doorControl && !emergencyMode) {
      return;
    }

    const requiresConfirmation = ['unlock', 'lock'].includes(action) && !emergencyMode;
    
    if (requiresConfirmation) {
      setPendingAction({ action, params });
      setShowConfirmDialog(true);
      return;
    }

    await executeDoorAction(action, params);
  };

  const executeDoorAction = async (action, params = {}) => {
    try {
      setActionLoading(true);
      
      let result;
      switch (action) {
        case 'unlock':
          result = await unifiAccessService.unlockDoor(doorId, { 
            duration: params.duration || 5000, 
            emergency: emergencyMode 
          });
          break;
        case 'lock':
          result = await unifiAccessService.lockDoor(doorId, { emergency: emergencyMode });
          break;
        case 'momentary_unlock':
          result = await unifiAccessService.unlockDoor(doorId, { duration: 3000 });
          break;
        default:
          throw new Error(`Unknown door action: ${action}`);
      }

      onDoorAction?.(doorId, action, result);
      await loadDoorStatus(); // Refresh status

    } catch (error) {
      console.error('Door action failed:', error);
    } finally {
      setActionLoading(false);
      setShowConfirmDialog(false);
      setPendingAction(null);
    }
  };

  if (!doorStatus) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CircularProgress size={16} />
        <Typography variant="caption">Loading door status...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Door Status Display */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        {doorStatus.isLocked ? (
          <Chip 
            icon={<LockIcon />} 
            label="Locked" 
            color="success" 
            size="small" 
          />
        ) : (
          <Chip 
            icon={<UnlockIcon />} 
            label="Unlocked" 
            color="warning" 
            size="small" 
          />
        )}
        
        <Chip 
          label={doorStatus.doorSensor ? 'Closed' : 'Open'} 
          color={doorStatus.doorSensor ? 'default' : 'info'}
          size="small" 
        />
      </Box>

      {/* Door Controls */}
      {(userPermissions.doorControl || emergencyMode) && (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            size="small"
            variant="outlined"
            startIcon={doorStatus.isLocked ? <UnlockIcon /> : <LockIcon />}
            onClick={() => handleDoorAction(doorStatus.isLocked ? 'unlock' : 'lock')}
            disabled={actionLoading}
            color={emergencyMode ? 'error' : 'primary'}
          >
            {doorStatus.isLocked ? 'Unlock' : 'Lock'}
          </Button>
          
          {doorStatus.isLocked && (
            <Button
              size="small"
              variant="text"
              onClick={() => handleDoorAction('momentary_unlock', { duration: 5000 })}
              disabled={actionLoading}
            >
              Unlock 5s
            </Button>
          )}
        </Box>
      )}

      {/* Action Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)}>
        <DialogTitle>
          Confirm Door Action
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to {pendingAction?.action} this door?
          </Typography>
          {emergencyMode && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Emergency mode is active. This action will be logged.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirmDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={() => executeDoorAction(pendingAction?.action, pendingAction?.params)}
            color="primary"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={20} /> : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

/**
 * Camera Live View Interaction Component
 * Provides thumbnail and live view modal functionality
 */
export const CameraLiveViewInteraction = ({
  cameraId,
  cameraData,
  onCameraAction
}) => {
  const [liveViewOpen, setLiveViewOpen] = useState(false);
  const [snapshot, setSnapshot] = useState(null);
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (cameraId && autoRefresh) {
      loadSnapshot();
      const interval = setInterval(loadSnapshot, 10000); // Refresh every 10 seconds
      return () => clearInterval(interval);
    }
  }, [cameraId, autoRefresh]);

  const loadSnapshot = async () => {
    try {
      setLoading(true);
      const snapshotData = await unifiProtectService.getCameraSnapshot(cameraId);
      setSnapshot(snapshotData);
    } catch (error) {
      console.error('Failed to load camera snapshot:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLiveView = () => {
    setLiveViewOpen(true);
    onCameraAction?.(cameraId, 'live_view_opened');
  };

  const handleSnapshotClick = () => {
    loadSnapshot();
    onCameraAction?.(cameraId, 'snapshot_refreshed');
  };

  return (
    <Box>
      {/* Camera Thumbnail */}
      <Box 
        sx={{ 
          position: 'relative',
          width: 120,
          height: 80,
          border: 1,
          borderColor: 'divider',
          borderRadius: 1,
          overflow: 'hidden',
          cursor: 'pointer',
          bgcolor: 'grey.100'
        }}
        onClick={handleLiveView}
      >
        {snapshot && (
          <img
            src={snapshot.url}
            alt="Camera snapshot"
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
          />
        )}
        
        {loading && (
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)'
          }}>
            <CircularProgress size={24} sx={{ color: 'white' }} />
          </Box>
        )}

        {/* Overlay Controls */}
        <Box sx={{
          position: 'absolute',
          bottom: 4,
          right: 4,
          display: 'flex',
          gap: 0.5
        }}>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleSnapshotClick();
            }}
            sx={{ bgcolor: 'rgba(0, 0, 0, 0.6)', color: 'white', '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' } }}
          >
            <RefreshIcon fontSize="small" />
          </IconButton>
          
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleLiveView();
            }}
            sx={{ bgcolor: 'rgba(0, 0, 0, 0.6)', color: 'white', '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.8)' } }}
          >
            <PlayIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* Auto-refresh Toggle */}
      <FormControlLabel
        control={
          <Switch
            checked={autoRefresh}
            onChange={(e) => setAutoRefresh(e.target.checked)}
            size="small"
          />
        }
        label="Auto-refresh"
        sx={{ mt: 1 }}
      />

      {/* Live View Modal */}
      <Dialog 
        open={liveViewOpen} 
        onClose={() => setLiveViewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Camera Live View: {cameraData?.name || cameraId}
          </Typography>
          <IconButton onClick={() => setLiveViewOpen(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ 
            width: '100%', 
            height: 400, 
            bgcolor: 'black', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            position: 'relative'
          }}>
            {/* Live video stream would go here */}
            <Typography color="white">
              Live video stream (integration with UniFi Protect)
            </Typography>
            
            <Box sx={{
              position: 'absolute',
              bottom: 16,
              right: 16,
              display: 'flex',
              gap: 1
            }}>
              <IconButton sx={{ color: 'white' }}>
                <FullscreenIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default {
  TemperatureHeatmapInteraction,
  DoorControlInteraction,
  CameraLiveViewInteraction
};