import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  CardHeader,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Grid,
  Divider,
  Alert
} from '@mui/material';
import {
  ElectricalServices as ElectricalServicesIcon,
  Power as PowerOutletIcon,
  Close as CloseIcon,
  Download as DownloadIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import electricalService from '../../services/electricalService';

/**
 * ElectricalPanelSchedule Component
 * Displays a detailed panel schedule with circuits and connected outlets
 */
const ElectricalPanelSchedule = ({ 
  panelId, 
  onClose,
  kioskMode = false 
}) => {
  const [panel, setPanel] = useState(null);
  const [circuits, setCircuits] = useState([]);
  const [outlets, setOutlets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (panelId) {
      fetchPanelData();
    }
  }, [panelId]);

  const fetchPanelData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [panelData, circuitsData, outletsData] = await Promise.all([
        electricalService.getPanel(panelId),
        electricalService.getCircuits(panelId),
        electricalService.getOutlets({ panelId })
      ]);
      
      setPanel(panelData);
      setCircuits(circuitsData);
      setOutlets(outletsData);
    } catch (err) {
      console.error('Error fetching panel data:', err);
      setError('Failed to load panel information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getBreakerTypeColor = (breakerType) => {
    switch (breakerType) {
      case 'GFCI':
        return 'warning';
      case 'AFCI':
        return 'info';
      case 'Dual':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getOutletsForCircuit = (circuitId) => {
    return outlets.filter(outlet => outlet.circuitId === circuitId);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // Future implementation: export to PDF or CSV
    console.log('Export panel schedule');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>Loading panel schedule...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!panel) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info">No panel data available.</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', maxWidth: 800, mx: 'auto' }}>
      {/* Header */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ElectricalServicesIcon />
            <Typography variant="h5">
              {panel.name} {panel.code && `(${panel.code})`}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            {!kioskMode && (
              <>
                <Tooltip title="Print Schedule">
                  <IconButton onClick={handlePrint}>
                    <PrintIcon />
                  </IconButton>
                </Tooltip>
                
                <Tooltip title="Export Schedule">
                  <IconButton onClick={handleExport}>
                    <DownloadIcon />
                  </IconButton>
                </Tooltip>
              </>
            )}
            
            {onClose && (
              <Tooltip title="Close">
                <IconButton onClick={onClose}>
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
        
        {/* Panel Info */}
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">
              Location: {panel.room || 'Not specified'}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">
              Circuits: {circuits.length}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            {panel.notes && (
              <Typography variant="body2" color="text.secondary">
                Notes: {panel.notes}
              </Typography>
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* Circuit Schedule Table */}
      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell><strong>Circuit</strong></TableCell>
              <TableCell><strong>Label</strong></TableCell>
              <TableCell><strong>Type</strong></TableCell>
              <TableCell><strong>Amp</strong></TableCell>
              <TableCell><strong>Outlets</strong></TableCell>
              <TableCell><strong>Status</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {circuits.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Typography color="text.secondary">
                    No circuits configured for this panel
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              circuits.map((circuit) => {
                const circuitOutlets = getOutletsForCircuit(circuit._id);
                
                return (
                  <TableRow key={circuit._id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {circuit.number}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {circuit.label || 'Unlabeled Circuit'}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={circuit.breakerType || 'Standard'}
                        size="small"
                        color={getBreakerTypeColor(circuit.breakerType)}
                        variant="outlined"
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {circuit.amperage ? `${circuit.amperage}A` : '—'}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {circuitOutlets.length === 0 ? (
                          <Typography variant="caption" color="text.secondary">
                            No outlets
                          </Typography>
                        ) : (
                          circuitOutlets.map((outlet) => (
                            <Chip
                              key={outlet._id}
                              label={outlet.label}
                              size="small"
                              variant="outlined"
                              icon={<PowerOutletIcon />}
                              sx={{ fontSize: '0.75rem' }}
                            />
                          ))
                        )}
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={circuitOutlets.length > 0 ? 'In Use' : 'Available'}
                        size="small"
                        color={circuitOutlets.length > 0 ? 'success' : 'default'}
                        variant="outlined"
                      />
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mt: 2 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {circuits.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Circuits
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {outlets.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Connected Outlets
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {circuits.filter(c => getOutletsForCircuit(c._id).length === 0).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Available Circuits
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .no-print {
            display: none !important;
          }
          
          body {
            margin: 0;
            padding: 20px;
          }
          
          table {
            font-size: 12px;
          }
          
          .MuiChip-root {
            border: 1px solid #ccc;
            background: white !important;
            color: black !important;
          }
        }
      `}</style>
    </Box>
  );
};

export default ElectricalPanelSchedule;