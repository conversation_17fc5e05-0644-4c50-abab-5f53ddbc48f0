import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Button,
  ButtonGroup,
  Chip,
  Divider,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Collapse,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Close as CloseIcon,
  Info as InfoIcon,
  Build as ServiceIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  ReportProblem as EmergencyIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Videocam as CameraIcon,
  Thermostat as TemperatureIcon,
  Power as PowerIcon,
  Wifi as WiFiIcon,
  LocalFireDepartment as SafetyIcon,
  ContentCopy as CopyIcon,
  OpenInNew as ExternalIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

// Import existing services to reuse integrations
import electricalService from '../../services/electricalService';
import unifiAccessService from '../../services/unifiAccessService';
import unifiProtectService from '../../services/unifiProtectService';
import hvacService from '../../services/hvacService';
import safetyService from '../../services/safetyService';
import wifiService from '../../services/wifiService';

/**
 * Inspector Panel Component
 * Provides detailed information and actions for selected floor plan items
 * Leverages existing service integrations to minimize code duplication
 */
const Inspector = ({
  selectedItem = null,
  onClose,
  onItemAction,
  onItemUpdate,
  onNavigate,
  onDeviceUpdate,
  emergencyMode = false,
  kioskMode = false,
  currentUser = null,
  position = 'right', // 'right', 'left', 'bottom'
  width = 350,
  buildingId = null,
  floorId = null
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [itemDetails, setItemDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);
  const [actionHistory, setActionHistory] = useState([]);
  const [showActionDialog, setShowActionDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);

  useEffect(() => {
    if (selectedItem) {
      loadItemDetails();
      loadActionHistory();
    }
  }, [selectedItem]);

  const loadItemDetails = async () => {
    if (!selectedItem) return;

    try {
      setLoading(true);
      setError(null);

      let details = null;

      switch (selectedItem.type) {
        case 'electrical_outlet':
          details = await loadElectricalDetails();
          break;
        case 'electrical_panel':
          details = await loadElectricalPanelDetails();
          break;
        case 'door':
          details = await loadDoorDetails();
          break;
        case 'camera':
          details = await loadCameraDetails();
          break;
        case 'temperature_sensor':
          details = await loadTemperatureDetails();
          break;
        case 'hvac_unit':
          details = await loadHVACDetails();
          break;
        case 'safety_asset':
          details = await loadSafetyDetails();
          break;
        case 'wifi_ap':
          details = await loadWiFiDetails();
          break;
        default:
          details = selectedItem;
      }

      setItemDetails(details);
    } catch (err) {
      console.error('Error loading item details:', err);
      setError(err.message || 'Failed to load item details');
    } finally {
      setLoading(false);
    }
  };

  const loadElectricalDetails = async () => {
    const outlet = await electricalService.getOutlet(selectedItem.id);
    if (outlet.panelId && outlet.circuitId) {
      const traceData = await electricalService.traceOutlet(selectedItem.id);
      return { ...outlet, traceData };
    }
    return outlet;
  };

  const loadElectricalPanelDetails = async () => {
    const panel = await electricalService.getPanel(selectedItem.id);
    const circuits = await electricalService.getPanelCircuits(selectedItem.id);
    return { ...panel, circuits };
  };

  const loadDoorDetails = async () => {
    const door = await unifiAccessService.getDoor(selectedItem.id);
    const doorStatus = await unifiAccessService.getDoorStatus(selectedItem.id);
    return { ...door, status: doorStatus };
  };

  const loadCameraDetails = async () => {
    const camera = await unifiProtectService.getCamera(selectedItem.id);
    return camera;
  };

  const loadTemperatureDetails = async () => {
    // Use enhanced HVAC service that consolidates multiple sources
    const tempData = await hvacService.getTemperatureData(
      selectedItem.buildingId, 
      selectedItem.floorId
    );
    const sensor = tempData.sensors.find(s => s.id === selectedItem.id);
    return sensor;
  };

  const loadHVACDetails = async () => {
    const unit = await hvacService.getUnit(selectedItem.id);
    const filterStatus = await hvacService.getFilterStatus(selectedItem.id);
    return { ...unit, filterStatus };
  };

  const loadSafetyDetails = async () => {
    const asset = await safetyService.getSafetyAsset(selectedItem.id);
    return asset;
  };

  const loadWiFiDetails = async () => {
    const ap = await wifiService.getAccessPoint(selectedItem.id);
    return ap;
  };

  const loadActionHistory = async () => {
    // Mock action history - in production, this would come from audit logs
    setActionHistory([
      {
        id: 1,
        action: 'Viewed',
        user: 'System',
        timestamp: new Date(Date.now() - 300000),
        details: 'Item viewed in floor plan'
      },
      {
        id: 2,
        action: 'Inspected',
        user: 'John Doe',
        timestamp: new Date(Date.now() - 86400000),
        details: 'Routine inspection completed'
      }
    ]);
  };

  const handleAction = async (actionType, params = {}) => {
    if (emergencyMode && !isEmergencyAction(actionType)) {
      setError('Only emergency actions are allowed in emergency mode');
      return;
    }

    if (requiresConfirmation(actionType)) {
      setPendingAction({ type: actionType, params });
      setShowActionDialog(true);
      return;
    }

    await executeAction(actionType, params);
  };

  const executeAction = async (actionType, params = {}) => {
    try {
      setActionLoading(true);
      setError(null);

      let result = null;

      switch (selectedItem.type) {
        case 'door':
          result = await executeDoorAction(actionType, params);
          break;
        case 'camera':
          result = await executeCameraAction(actionType, params);
          break;
        case 'hvac_unit':
          result = await executeHVACAction(actionType, params);
          break;
        case 'temperature_sensor':
          result = await executeTemperatureAction(actionType, params);
          break;
        case 'electrical_outlet':
          result = await executeElectricalAction(actionType, params);
          break;
        case 'safety_asset':
          result = await executeSafetyAction(actionType, params);
          break;
        case 'wifi_ap':
          result = await executeWiFiAction(actionType, params);
          break;
        default:
          throw new Error(`Unsupported action for item type: ${selectedItem.type}`);
      }

      if (onItemAction) {
        onItemAction(selectedItem, actionType, result);
      }

      // Reload details after action
      await loadItemDetails();
      
    } catch (err) {
      console.error('Error executing action:', err);
      setError(err.message || 'Action failed');
    } finally {
      setActionLoading(false);
      setShowActionDialog(false);
      setPendingAction(null);
    }
  };

  const executeDoorAction = async (actionType, params) => {
    switch (actionType) {
      case 'unlock':
        return await unifiAccessService.unlockDoor(selectedItem.id, { duration: params.duration || 5000 });
      case 'lock':
        return await unifiAccessService.lockDoor(selectedItem.id);
      case 'status':
        return await unifiAccessService.getDoorStatus(selectedItem.id);
      default:
        throw new Error(`Unsupported door action: ${actionType}`);
    }
  };

  const executeCameraAction = async (actionType, params) => {
    switch (actionType) {
      case 'snapshot':
        return await unifiProtectService.getCameraSnapshot(selectedItem.id);
      case 'view_live':
        // This would open a live view modal
        return { success: true, message: 'Live view opened' };
      default:
        throw new Error(`Unsupported camera action: ${actionType}`);
    }
  };

  const executeHVACAction = async (actionType, params) => {
    switch (actionType) {
      case 'set_temperature':
        return await hvacService.controlSmartDevice(
          selectedItem.id, 
          itemDetails.source, 
          'setTemperature', 
          { temperature: params.temperature }
        );
      case 'set_mode':
        return await hvacService.controlSmartDevice(
          selectedItem.id, 
          itemDetails.source, 
          'setMode', 
          { mode: params.mode }
        );
      case 'power_toggle':
        const action = itemDetails.powerState ? 'powerOff' : 'powerOn';
        return await hvacService.controlSmartDevice(selectedItem.id, itemDetails.source, action);
      default:
        throw new Error(`Unsupported HVAC action: ${actionType}`);
    }
  };

  const executeTemperatureAction = async (actionType, params) => {
    switch (actionType) {
      case 'refresh':
        return await hvacService.getTemperatureData(
          selectedItem.buildingId, 
          selectedItem.floorId, 
          { forceRefresh: true }
        );
      default:
        throw new Error(`Unsupported temperature action: ${actionType}`);
    }
  };

  const executeElectricalAction = async (actionType, params) => {
    switch (actionType) {
      case 'trace':
        return await electricalService.traceOutlet(selectedItem.id);
      case 'copy_directions':
        if (itemDetails.traceData) {
          const directions = `${itemDetails.label} → Panel ${itemDetails.traceData.panel.name}, Breaker ${itemDetails.breakerNumber}`;
          navigator.clipboard.writeText(directions);
          return { success: true, message: 'Directions copied to clipboard' };
        }
        throw new Error('No trace data available');
      default:
        throw new Error(`Unsupported electrical action: ${actionType}`);
    }
  };

  const executeSafetyAction = async (actionType, params) => {
    switch (actionType) {
      case 'mark_inspected':
        return await safetyService.markInspected(selectedItem.id, {
          inspector: currentUser?.name || 'Unknown',
          notes: params.notes || ''
        });
      case 'report_issue':
        return await safetyService.reportIssue(selectedItem.id, {
          reporter: currentUser?.name || 'Unknown',
          issue: params.issue || ''
        });
      default:
        throw new Error(`Unsupported safety action: ${actionType}`);
    }
  };

  const executeWiFiAction = async (actionType, params) => {
    switch (actionType) {
      case 'restart':
        // This would need UniFi Network integration
        return { success: true, message: 'Access point restart initiated' };
      case 'view_clients':
        // This would show connected clients
        return { success: true, message: 'Client list opened' };
      default:
        throw new Error(`Unsupported WiFi action: ${actionType}`);
    }
  };

  const isEmergencyAction = (actionType) => {
    const emergencyActions = ['unlock', 'lock', 'emergency_call', 'mark_inspected', 'report_issue'];
    return emergencyActions.includes(actionType);
  };

  const requiresConfirmation = (actionType) => {
    const confirmationActions = ['unlock', 'power_toggle', 'restart'];
    return confirmationActions.includes(actionType);
  };

  const getItemIcon = () => {
    const iconMap = {
      electrical_outlet: <PowerIcon />,
      electrical_panel: <PowerIcon />,
      door: <LockIcon />,
      camera: <CameraIcon />,
      temperature_sensor: <TemperatureIcon />,
      hvac_unit: <TemperatureIcon />,
      safety_asset: <SafetyIcon />,
      wifi_ap: <WiFiIcon />
    };
    return iconMap[selectedItem?.type] || <InfoIcon />;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'online':
      case 'connected':
      case 'active':
      case 'locked':
        return 'success';
      case 'offline':
      case 'disconnected':
      case 'inactive':
        return 'error';
      case 'warning':
      case 'unlocked':
        return 'warning';
      default:
        return 'default';
    }
  };

  const renderItemActions = () => {
    if (!itemDetails) return null;

    const actions = getAvailableActions();
    
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Actions
        </Typography>
        <ButtonGroup size="small" orientation="vertical" fullWidth>
          {actions.map((action) => (
            <Button
              key={action.id}
              startIcon={action.icon}
              onClick={() => handleAction(action.id, action.defaultParams)}
              disabled={actionLoading || (emergencyMode && !action.emergency)}
              color={action.emergency ? 'error' : 'primary'}
            >
              {action.label}
            </Button>
          ))}
        </ButtonGroup>
      </Box>
    );
  };

  const getAvailableActions = () => {
    const actions = [];

    switch (selectedItem.type) {
      case 'door':
        actions.push(
          { id: 'unlock', label: 'Unlock (5s)', icon: <UnlockIcon />, emergency: true },
          { id: 'lock', label: 'Lock', icon: <LockIcon />, emergency: true },
          { id: 'status', label: 'Refresh Status', icon: <RefreshIcon /> }
        );
        break;
      
      case 'camera':
        actions.push(
          { id: 'snapshot', label: 'Take Snapshot', icon: <CameraIcon /> },
          { id: 'view_live', label: 'View Live', icon: <PlayIcon /> }
        );
        break;
      
      case 'hvac_unit':
        if (itemDetails.capabilities?.includes('temperature')) {
          actions.push(
            { id: 'set_temperature', label: 'Set Temperature', icon: <TemperatureIcon /> },
            { id: 'set_mode', label: 'Change Mode', icon: <SettingsIcon /> }
          );
        }
        if (itemDetails.capabilities?.includes('cooling') || itemDetails.capabilities?.includes('heating')) {
          actions.push(
            { id: 'power_toggle', label: itemDetails.powerState ? 'Turn Off' : 'Turn On', icon: <PowerIcon /> }
          );
        }
        break;
      
      case 'electrical_outlet':
        actions.push(
          { id: 'trace', label: 'Find Breaker', icon: <PowerIcon /> },
          { id: 'copy_directions', label: 'Copy Directions', icon: <CopyIcon /> }
        );
        break;
      
      case 'safety_asset':
        actions.push(
          { id: 'mark_inspected', label: 'Mark Inspected', icon: <CheckIcon />, emergency: true },
          { id: 'report_issue', label: 'Report Issue', icon: <WarningIcon />, emergency: true }
        );
        break;
      
      case 'wifi_ap':
        actions.push(
          { id: 'restart', label: 'Restart AP', icon: <RefreshIcon /> },
          { id: 'view_clients', label: 'View Clients', icon: <WiFiIcon /> }
        );
        break;
    }

    return actions;
  };

  if (!selectedItem) {
    return null;
  }

  return (
    <>
      <Paper 
        sx={{ 
          position: 'absolute',
          top: 16,
          left: 16,
          width: 350,
          maxHeight: '80vh',
          overflowY: 'auto',
          zIndex: 1000,
          bgcolor: emergencyMode ? 'error.light' : 'background.paper',
          border: emergencyMode ? 2 : 1,
          borderColor: emergencyMode ? 'error.main' : 'divider'
        }}
      >
        {/* Header */}
        <Box 
          sx={{ 
            p: 2, 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            bgcolor: emergencyMode ? 'error.main' : 'primary.main',
            color: emergencyMode ? 'error.contrastText' : 'primary.contrastText'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getItemIcon()}
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Inspector
            </Typography>
            {emergencyMode && <EmergencyIcon />}
          </Box>
          
          <IconButton onClick={onClose} sx={{ color: 'inherit' }}>
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Content */}
        <Box sx={{ p: 2 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {/* Item Header */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {itemDetails?.name || itemDetails?.label || selectedItem.name}
                </Typography>
                
                <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                  <Chip 
                    label={selectedItem.type.replace('_', ' ')} 
                    size="small" 
                    color="primary"
                  />
                  {itemDetails?.status && (
                    <Chip 
                      label={itemDetails.status} 
                      size="small"
                      color={getStatusColor(itemDetails.status)}
                    />
                  )}
                </Box>
                
                {itemDetails?.room && (
                  <Typography variant="body2" color="text.secondary">
                    Location: {itemDetails.room}
                  </Typography>
                )}
              </Box>

              {/* Tabs */}
              <Tabs 
                value={activeTab} 
                onChange={(e, newValue) => setActiveTab(newValue)}
                sx={{ mb: 2 }}
              >
                <Tab label="Details" />
                <Tab label="Actions" />
                <Tab label="History" />
              </Tabs>

              {/* Tab Content */}
              {activeTab === 0 && (
                <Box>
                  {/* Item-specific details */}
                  {renderItemDetails()}
                </Box>
              )}

              {activeTab === 1 && (
                <Box>
                  {renderItemActions()}
                </Box>
              )}

              {activeTab === 2 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Recent Activity
                  </Typography>
                  <List dense>
                    {actionHistory.map((action) => (
                      <ListItem key={action.id}>
                        <ListItemIcon>
                          <HistoryIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${action.action} by ${action.user}`}
                          secondary={`${action.timestamp.toLocaleDateString()} - ${action.details}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </>
          )}
        </Box>
      </Paper>

      {/* Action Confirmation Dialog */}
      <Dialog 
        open={showActionDialog} 
        onClose={() => setShowActionDialog(false)}
      >
        <DialogTitle>
          Confirm Action
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to {pendingAction?.type} this {selectedItem?.type}?
          </Typography>
          {emergencyMode && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Emergency mode is active. This action will be logged.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowActionDialog(false)}>
            Cancel
          </Button>
          <Button 
            onClick={() => executeAction(pendingAction?.type, pendingAction?.params)}
            color="primary"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={20} /> : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

  // Helper function to render item-specific details
  const renderItemDetails = () => {
    if (!itemDetails) return null;

    switch (selectedItem.type) {
      case 'electrical_outlet':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Circuit" 
                secondary={itemDetails.circuitId ? `Circuit ${itemDetails.breakerNumber || 'Unknown'}` : 'Not assigned'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Panel" 
                secondary={itemDetails.panelId ? itemDetails.traceData?.panel?.name || 'Unknown Panel' : 'Not assigned'} 
              />
            </ListItem>
            {itemDetails.traceData && (
              <ListItem>
                <ListItemText 
                  primary="Path" 
                  secondary={`${itemDetails.label} → ${itemDetails.traceData.panel.name}`} 
                />
              </ListItem>
            )}
          </List>
        );

      case 'electrical_panel':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Panel Code" 
                secondary={itemDetails.code || 'No code assigned'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Circuits" 
                secondary={`${itemDetails.circuits?.length || 0} circuits configured`} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Service Rating" 
                secondary={itemDetails.serviceRating || 'Not specified'} 
              />
            </ListItem>
          </List>
        );

      case 'door':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Lock Status" 
                secondary={itemDetails.status?.isLocked ? 'Locked' : 'Unlocked'} 
              />
              <ListItemIcon>
                {itemDetails.status?.isLocked ? <LockIcon color="success" /> : <UnlockIcon color="warning" />}
              </ListItemIcon>
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Door Sensor" 
                secondary={itemDetails.status?.doorSensor ? 'Closed' : 'Open'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Last Activity" 
                secondary={itemDetails.status?.lastActivity ? new Date(itemDetails.status.lastActivity).toLocaleString() : 'No recent activity'} 
              />
            </ListItem>
          </List>
        );

      case 'camera':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Status" 
                secondary={itemDetails.isConnected ? 'Online' : 'Offline'} 
              />
              <ListItemIcon>
                {itemDetails.isConnected ? <CheckIcon color="success" /> : <ErrorIcon color="error" />}
              </ListItemIcon>
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Recording" 
                secondary={itemDetails.isRecording ? 'Active' : 'Inactive'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Resolution" 
                secondary={`${itemDetails.videoSettings?.width}x${itemDetails.videoSettings?.height}` || 'Unknown'} 
              />
            </ListItem>
          </List>
        );

      case 'temperature_sensor':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Current Temperature" 
                secondary={`${itemDetails.temperature?.toFixed(1) || 'Unknown'}°F`} 
              />
              <Chip 
                label={itemDetails.trend || 'stable'} 
                size="small" 
                color={itemDetails.trend === 'rising' ? 'warning' : itemDetails.trend === 'falling' ? 'info' : 'default'}
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Humidity" 
                secondary={`${itemDetails.humidity?.toFixed(1) || 'Unknown'}%`} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Last Updated" 
                secondary={itemDetails.lastUpdate ? new Date(itemDetails.lastUpdate).toLocaleString() : 'Unknown'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Source" 
                secondary={itemDetails.source || 'Unknown'} 
              />
            </ListItem>
          </List>
        );

      case 'hvac_unit':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Power Status" 
                secondary={itemDetails.powerState ? 'On' : 'Off'} 
              />
              <ListItemIcon>
                {itemDetails.powerState ? <PlayIcon color="success" /> : <StopIcon color="default" />}
              </ListItemIcon>
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Current Temperature" 
                secondary={`${itemDetails.currentTemperature?.toFixed(1) || 'Unknown'}°F`} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Target Temperature" 
                secondary={`${itemDetails.targetTemperature?.toFixed(1) || 'Unknown'}°F`} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Mode" 
                secondary={itemDetails.mode || 'Unknown'} 
              />
            </ListItem>
            {itemDetails.filterStatus && (
              <ListItem>
                <ListItemText 
                  primary="Filter Status" 
                  secondary={`${itemDetails.filterStatus.daysRemaining || 'Unknown'} days remaining`} 
                />
                <Chip 
                  label={itemDetails.filterStatus.status || 'Unknown'} 
                  size="small" 
                  color={itemDetails.filterStatus.status === 'good' ? 'success' : itemDetails.filterStatus.status === 'due' ? 'warning' : 'error'}
                />
              </ListItem>
            )}
          </List>
        );

      case 'safety_asset':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Asset Type" 
                secondary={itemDetails.assetType?.replace('_', ' ') || 'Unknown'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Inspection Status" 
                secondary={itemDetails.inspection?.status || 'Unknown'} 
              />
              <Chip 
                label={itemDetails.inspection?.status || 'Unknown'} 
                size="small" 
                color={itemDetails.inspection?.status === 'current' ? 'success' : itemDetails.inspection?.status === 'due' ? 'warning' : 'error'}
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Next Inspection" 
                secondary={itemDetails.inspection?.nextInspection ? new Date(itemDetails.inspection.nextInspection).toLocaleDateString() : 'Not scheduled'} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Last Inspector" 
                secondary={itemDetails.inspection?.inspector || 'Unknown'} 
              />
            </ListItem>
          </List>
        );

      case 'wifi_ap':
        return (
          <List dense>
            <ListItem>
              <ListItemText 
                primary="Status" 
                secondary={itemDetails.state === 1 ? 'Online' : 'Offline'} 
              />
              <ListItemIcon>
                {itemDetails.state === 1 ? <CheckIcon color="success" /> : <ErrorIcon color="error" />}
              </ListItemIcon>
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Connected Clients" 
                secondary={`${itemDetails.clientCount || 0} devices`} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Channel" 
                secondary={`Channel ${itemDetails.channel || 'Unknown'}`} 
              />
            </ListItem>
            <ListItem>
              <ListItemText 
                primary="Signal Strength" 
                secondary={`${itemDetails.signalStrength || 'Unknown'} dBm`} 
              />
            </ListItem>
          </List>
        );

      default:
        return (
          <Typography variant="body2" color="text.secondary">
            No detailed information available for this item type.
          </Typography>
        );
    }
  };

  /**
   * Enhanced context menu integration for quick actions
   */
  const handleQuickAction = async (actionType, item) => {
    switch (actionType) {
      case 'inspect':
        // Item already selected in inspector
        break;
      case 'viewCamera':
        await handleAction('view_live');
        break;
      case 'toggleLock':
        const lockAction = itemDetails?.status?.isLocked ? 'unlock' : 'lock';
        await handleAction(lockAction);
        break;
      case 'viewLive':
        await handleAction('view_live');
        break;
      case 'traceCircuit':
        await handleAction('trace');
        break;
      case 'viewSchedule':
        // Would open panel schedule view
        break;
      case 'hvacStatus':
        await handleAction('status');
        break;
      case 'wifiStatus':
        await handleAction('status');
        break;
      case 'findNearest':
        // Would trigger nearest safety equipment finder
        break;
      case 'emergencyShutoff':
        // Would trigger emergency shutoff procedure
        break;
      case 'copyLocation':
        const locationText = `${itemDetails?.name || selectedItem.name} - ${itemDetails?.room || 'Unknown Location'}`;
        navigator.clipboard.writeText(locationText);
        break;
      case 'navigateTo':
        // Would trigger navigation to item location
        if (onItemAction) {
          onItemAction(selectedItem, 'navigate', itemDetails);
        }
        break;
      default:
        console.warn(`Unhandled quick action: ${actionType}`);
    }
  };

export default Inspector;