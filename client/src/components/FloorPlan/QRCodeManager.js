import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  QrCode as QRIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  ContentCopy as CopyIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon
} from '@mui/icons-material';

// Import QR code generation library (you'll need to install qrcode)
// npm install qrcode
import QRCode from 'qrcode';

// Import existing services
import electricalService from '../../services/electricalService';
import safetyService from '../../services/safetyService';
import hvacService from '../../services/hvacService';

/**
 * QRCodeManager Component
 * Generates and manages QR codes for building assets
 * Integrates with CSV import functionality for bulk QR generation
 */
const QRCodeManager = ({
  open = false,
  onClose,
  buildingId,
  floorId,
  itemType = 'outlet', // 'outlet', 'panel', 'safety_asset', 'hvac_unit'
  itemData = null,
  onQRGenerated
}) => {
  const [qrDialogOpen, setQRDialogOpen] = useState(false);
  const [bulkGenerateOpen, setBulkGenerateOpen] = useState(false);
  const [qrCodeDataURL, setQRCodeDataURL] = useState('');
  const [qrCodeText, setQRCodeText] = useState('');
  const [customText, setCustomText] = useState('');
  const [qrSize, setQRSize] = useState(256);
  const [generating, setGenerating] = useState(false);
  const [generatedCodes, setGeneratedCodes] = useState([]);
  const [bulkItems, setBulkItems] = useState([]);

  const qrSizes = [
    { value: 128, label: 'Small (128px)' },
    { value: 256, label: 'Medium (256px)' },
    { value: 512, label: 'Large (512px)' },
    { value: 1024, label: 'Extra Large (1024px)' }
  ];

  const generateQRCode = async (text, size = 256) => {
    try {
      const options = {
        width: size,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      };

      const dataURL = await QRCode.toDataURL(text, options);
      return dataURL;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  };

  const handleGenerateQR = async () => {
    if (!itemData) return;

    try {
      setGenerating(true);

      // Generate QR code text based on item type
      let qrText = '';
      switch (itemType) {
        case 'outlet':
          qrText = `OUTLET:${itemData.label}:${buildingId}:${floorId}`;
          break;
        case 'panel':
          qrText = `PANEL:${itemData.code || itemData.name}:${buildingId}:${floorId}`;
          break;
        case 'safety_asset':
          qrText = `SAFETY:${itemData.assetId}:${itemData.assetType}:${buildingId}:${floorId}`;
          break;
        case 'hvac_unit':
          qrText = `HVAC:${itemData.name}:${buildingId}:${floorId}`;
          break;
        default:
          qrText = customText || `${itemType}:${itemData.id}:${buildingId}:${floorId}`;
      }

      const dataURL = await generateQRCode(qrText, qrSize);
      setQRCodeDataURL(dataURL);
      setQRCodeText(qrText);
      setQRDialogOpen(true);

      // Update item with QR code in database
      await updateItemWithQR(qrText);

    } catch (error) {
      console.error('Error generating QR code:', error);
    } finally {
      setGenerating(false);
    }
  };

  const updateItemWithQR = async (qrCode) => {
    try {
      switch (itemType) {
        case 'outlet':
          await electricalService.updateOutlet(itemData._id, { qrCode });
          break;
        case 'panel':
          await electricalService.updatePanel(itemData._id, { qrCode });
          break;
        case 'safety_asset':
          await safetyService.updateSafetyAsset(itemData._id, { qrCode });
          break;
        case 'hvac_unit':
          await hvacService.updateUnit(itemData._id, { qrCode });
          break;
      }

      onQRGenerated?.(itemData._id, qrCode);
    } catch (error) {
      console.error('Error updating item with QR code:', error);
    }
  };

  const handleBulkGenerate = async () => {
    try {
      setGenerating(true);
      setBulkGenerateOpen(true);

      // Load all items of the selected type
      let items = [];
      switch (itemType) {
        case 'outlet':
          const outletResponse = await electricalService.getOutlets({ buildingId, floorId });
          items = outletResponse.items || [];
          break;
        case 'panel':
          const panelResponse = await electricalService.getPanels({ buildingId, floorId });
          items = panelResponse.items || [];
          break;
        case 'safety_asset':
          const safetyResponse = await safetyService.getSafetyAssets({ buildingId, floorId });
          items = safetyResponse.items || [];
          break;
        case 'hvac_unit':
          const hvacResponse = await hvacService.getUnits({ buildingId, floorId });
          items = hvacResponse.items || [];
          break;
      }

      setBulkItems(items);

      // Generate QR codes for all items
      const generatedCodes = [];
      for (const item of items) {
        let qrText = '';
        switch (itemType) {
          case 'outlet':
            qrText = `OUTLET:${item.label}:${buildingId}:${floorId}`;
            break;
          case 'panel':
            qrText = `PANEL:${item.code || item.name}:${buildingId}:${floorId}`;
            break;
          case 'safety_asset':
            qrText = `SAFETY:${item.assetId}:${item.assetType}:${buildingId}:${floorId}`;
            break;
          case 'hvac_unit':
            qrText = `HVAC:${item.name}:${buildingId}:${floorId}`;
            break;
        }

        const dataURL = await generateQRCode(qrText, 256);
        generatedCodes.push({
          id: item._id,
          label: item.label || item.name || item.assetId,
          qrText,
          dataURL,
          item
        });

        // Update item in database
        await updateItemWithQRById(item._id, qrText);
      }

      setGeneratedCodes(generatedCodes);

    } catch (error) {
      console.error('Error bulk generating QR codes:', error);
    } finally {
      setGenerating(false);
    }
  };

  const updateItemWithQRById = async (itemId, qrCode) => {
    try {
      switch (itemType) {
        case 'outlet':
          await electricalService.updateOutlet(itemId, { qrCode });
          break;
        case 'panel':
          await electricalService.updatePanel(itemId, { qrCode });
          break;
        case 'safety_asset':
          await safetyService.updateSafetyAsset(itemId, { qrCode });
          break;
        case 'hvac_unit':
          await hvacService.updateUnit(itemId, { qrCode });
          break;
      }
    } catch (error) {
      console.error('Error updating item with QR code:', error);
    }
  };

  const handleDownloadQR = () => {
    const link = document.createElement('a');
    link.download = `qr-${itemData?.label || itemData?.name || 'code'}.png`;
    link.href = qrCodeDataURL;
    link.click();
  };

  const handlePrintQR = () => {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>QR Code - ${itemData?.label || itemData?.name}</title>
          <style>
            body { margin: 0; padding: 20px; text-align: center; font-family: Arial, sans-serif; }
            .qr-container { page-break-inside: avoid; margin-bottom: 30px; }
            .qr-code { max-width: 200px; height: auto; }
            .qr-label { margin-top: 10px; font-size: 12px; font-weight: bold; }
            .qr-text { margin-top: 5px; font-size: 10px; color: #666; word-break: break-all; }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <img src="${qrCodeDataURL}" alt="QR Code" class="qr-code" />
            <div class="qr-label">${itemData?.label || itemData?.name}</div>
            <div class="qr-text">${qrCodeText}</div>
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const handleBulkDownload = () => {
    // Create a zip file with all QR codes (simplified version)
    const zip = new JSZip();
    
    generatedCodes.forEach((code, index) => {
      const base64Data = code.dataURL.split(',')[1];
      zip.file(`${code.label.replace(/[^a-zA-Z0-9]/g, '_')}_qr.png`, base64Data, { base64: true });
    });

    zip.generateAsync({ type: 'blob' }).then((content) => {
      const link = document.createElement('a');
      link.download = `${itemType}_qr_codes_${new Date().toISOString().split('T')[0]}.zip`;
      link.href = URL.createObjectURL(content);
      link.click();
    });
  };

  const handleBulkPrint = () => {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>QR Codes - ${itemType}</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            .qr-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .qr-container { page-break-inside: avoid; text-align: center; border: 1px solid #ddd; padding: 10px; }
            .qr-code { max-width: 150px; height: auto; }
            .qr-label { margin-top: 10px; font-size: 12px; font-weight: bold; }
            .qr-text { margin-top: 5px; font-size: 8px; color: #666; word-break: break-all; }
          </style>
        </head>
        <body>
          <h1>QR Codes for ${itemType.replace('_', ' ').toUpperCase()}</h1>
          <div class="qr-grid">
            ${generatedCodes.map(code => `
              <div class="qr-container">
                <img src="${code.dataURL}" alt="QR Code" class="qr-code" />
                <div class="qr-label">${code.label}</div>
                <div class="qr-text">${code.qrText}</div>
              </div>
            `).join('')}
          </div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  const copyQRText = () => {
    navigator.clipboard.writeText(qrCodeText);
  };

  return (
    <>
      {/* Main QR Manager Dialog */}
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          QR Code Manager
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Generate QR codes for {itemType.replace('_', ' ')} items to enable quick mobile access
            </Typography>
          </Box>

          {itemData && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Selected Item: {itemData.label || itemData.name || itemData.assetId}
              </Typography>
              
              <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                <InputLabel>QR Code Size</InputLabel>
                <Select
                  value={qrSize}
                  onChange={(e) => setQRSize(e.target.value)}
                  label="QR Code Size"
                >
                  {qrSizes.map((size) => (
                    <MenuItem key={size.value} value={size.value}>
                      {size.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                size="small"
                label="Custom QR Text (optional)"
                value={customText}
                onChange={(e) => setCustomText(e.target.value)}
                placeholder={`Auto-generated: ${itemType}:${itemData.label || itemData.name}:...`}
                sx={{ mb: 2 }}
              />

              <Button
                variant="contained"
                startIcon={generating ? <CircularProgress size={16} /> : <QRIcon />}
                onClick={handleGenerateQR}
                disabled={generating}
                fullWidth
              >
                Generate QR Code
              </Button>
            </Box>
          )}

          <Box>
            <Button
              variant="outlined"
              startIcon={generating ? <CircularProgress size={16} /> : <QRIcon />}
              onClick={handleBulkGenerate}
              disabled={generating}
              fullWidth
            >
              Bulk Generate for All {itemType.replace('_', ' ')}s
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Single QR Code Dialog */}
      <Dialog open={qrDialogOpen} onClose={() => setQRDialogOpen(false)} maxWidth="sm">
        <DialogTitle>
          Generated QR Code
        </DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', mb: 2 }}>
            {qrCodeDataURL && (
              <img 
                src={qrCodeDataURL} 
                alt="Generated QR Code" 
                style={{ maxWidth: '100%', height: 'auto' }}
              />
            )}
          </Box>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="text.secondary">
              QR Code Text:
            </Typography>
            <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
              {qrCodeText}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
            <Button
              startIcon={<DownloadIcon />}
              onClick={handleDownloadQR}
              size="small"
            >
              Download
            </Button>
            <Button
              startIcon={<PrintIcon />}
              onClick={handlePrintQR}
              size="small"
            >
              Print
            </Button>
            <Button
              startIcon={<CopyIcon />}
              onClick={copyQRText}
              size="small"
            >
              Copy Text
            </Button>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setQRDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Bulk QR Codes Dialog */}
      <Dialog 
        open={bulkGenerateOpen} 
        onClose={() => setBulkGenerateOpen(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          Bulk QR Codes Generated ({generatedCodes.length})
        </DialogTitle>
        <DialogContent>
          {generating ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CircularProgress />
              <Typography variant="body2" sx={{ mt: 2 }}>
                Generating QR codes...
              </Typography>
            </Box>
          ) : (
            <>
              <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
                <Button
                  startIcon={<PrintIcon />}
                  onClick={handleBulkPrint}
                  disabled={generatedCodes.length === 0}
                >
                  Print All
                </Button>
                <Button
                  startIcon={<DownloadIcon />}
                  onClick={handleBulkDownload}
                  disabled={generatedCodes.length === 0}
                >
                  Download ZIP
                </Button>
              </Box>

              <Grid container spacing={2}>
                {generatedCodes.map((code) => (
                  <Grid item xs={6} sm={4} md={3} key={code.id}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <img 
                        src={code.dataURL} 
                        alt={`QR for ${code.label}`}
                        style={{ width: '100%', height: 'auto', maxWidth: 120 }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {code.label}
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkGenerateOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default QRCodeManager;