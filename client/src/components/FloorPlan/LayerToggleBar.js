import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Badge,
  Chip,
  ButtonGroup,
  Button,
  Collapse,
  Divider,
  FormControlLabel,
  Switch,
  Slider,
  Grid,
  Alert
} from '@mui/material';
import {
  Power as ElectricalIcon,
  Thermostat as TemperatureIcon,
  Door as DoorIcon,
  Videocam as CameraIcon,
  Air as HVACIcon,
  LocalFireDepartment as SafetyIcon,
  Wifi as WiFiIcon,
  Water as UtilityIcon,
  Event as EventIcon,
  Audiotrack as AVIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Settings as SettingsIcon,
  ReportProblem as EmergencyIcon,
  Kiosk as KioskIcon
} from '@mui/icons-material';

/**
 * LayerToggleBar Component
 * Advanced layer control interface for FloorPlanViewer with grouped toggles,
 * badge counts, opacity controls, and emergency/kiosk mode support
 */
const LayerToggleBar = ({
  layers = {},
  onLayerToggle,
  onLayerOpacityChange,
  layerCounts = {},
  alerts = {},
  emergencyMode = false,
  kioskMode = false,
  onEmergencyMode,
  onKioskMode,
  orientation = 'horizontal' // 'horizontal' or 'vertical'
}) => {
  const [expandedGroups, setExpandedGroups] = useState({});
  const [showSettings, setShowSettings] = useState(false);

  // Layer group definitions with existing integration reuse
  const layerGroups = [
    {
      id: 'infrastructure',
      name: 'Infrastructure',
      icon: <ElectricalIcon />,
      layers: [
        {
          id: 'electrical',
          name: 'Electrical',
          icon: <ElectricalIcon />,
          description: 'Outlets, panels, and circuits',
          emergencyPriority: 2
        },
        {
          id: 'temperature',
          name: 'Temperature',
          icon: <TemperatureIcon />,
          description: 'Temperature sensors and heatmap',
          emergencyPriority: 3
        },
        {
          id: 'hvac',
          name: 'HVAC',
          icon: <HVACIcon />,
          description: 'HVAC units and filters',
          emergencyPriority: 3
        },
        {
          id: 'utility',
          name: 'Shutoffs',
          icon: <UtilityIcon />,
          description: 'Water and gas shutoffs',
          emergencyPriority: 1
        }
      ]
    },
    {
      id: 'security',
      name: 'Security & Safety',
      icon: <SafetyIcon />,
      layers: [
        {
          id: 'doors',
          name: 'Doors',
          icon: <DoorIcon />,
          description: 'Door locks and access control',
          emergencyPriority: 1
        },
        {
          id: 'cameras',
          name: 'Cameras',
          icon: <CameraIcon />,
          description: 'Security cameras and coverage',
          emergencyPriority: 1
        },
        {
          id: 'safety',
          name: 'Safety Assets',
          icon: <SafetyIcon />,
          description: 'Fire extinguishers, AEDs, first aid',
          emergencyPriority: 1
        }
      ]
    },
    {
      id: 'technology',
      name: 'Technology',
      icon: <WiFiIcon />,
      layers: [
        {
          id: 'wifi',
          name: 'WiFi',
          icon: <WiFiIcon />,
          description: 'Access points and coverage',
          emergencyPriority: 4
        },
        {
          id: 'av',
          name: 'AV Equipment',
          icon: <AVIcon />,
          description: 'Audio/visual equipment',
          emergencyPriority: 4
        }
      ]
    },
    {
      id: 'events',
      name: 'Events & Resources',
      icon: <EventIcon />,
      layers: [
        {
          id: 'events',
          name: 'Church Events',
          icon: <EventIcon />,
          description: 'Event scheduling and room usage',
          emergencyPriority: 5
        },
        {
          id: 'volunteers',
          name: 'Volunteer Resources',
          icon: <EventIcon />,
          description: 'Volunteer tools and supplies',
          emergencyPriority: 5
        },
        {
          id: 'wayfinding',
          name: 'Wayfinding',
          icon: <EventIcon />,
          description: 'Navigation routes and signage',
          emergencyPriority: 2
        }
      ]
    }
  ];

  // Emergency mode layer priorities
  const getEmergencyLayers = () => {
    if (!emergencyMode) return layerGroups;
    
    // In emergency mode, only show priority 1-2 layers
    return layerGroups.map(group => ({
      ...group,
      layers: group.layers.filter(layer => layer.emergencyPriority <= 2)
    })).filter(group => group.layers.length > 0);
  };

  const handleGroupToggle = (groupId) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  const handleLayerToggle = (layerId) => {
    if (onLayerToggle) {
      onLayerToggle(layerId, !layers[layerId]);
    }
  };

  const handleOpacityChange = (layerId, opacity) => {
    if (onLayerOpacityChange) {
      onLayerOpacityChange(layerId, opacity);
    }
  };

  const getLayerCount = (layerId) => {
    return layerCounts[layerId] || 0;
  };

  const getLayerAlerts = (layerId) => {
    return alerts[layerId] || 0;
  };

  const renderLayerButton = (layer) => {
    const isActive = layers[layer.id];
    const count = getLayerCount(layer.id);
    const alertCount = getLayerAlerts(layer.id);

    return (
      <Box key={layer.id} sx={{ mb: 1 }}>
        <ButtonGroup
          variant={isActive ? 'contained' : 'outlined'}
          size="small"
          sx={{ width: '100%' }}
        >
          <Button
            onClick={() => handleLayerToggle(layer.id)}
            startIcon={
              <Badge 
                badgeContent={alertCount} 
                color="error" 
                invisible={alertCount === 0}
              >
                {layer.icon}
              </Badge>
            }
            sx={{ 
              flexGrow: 1, 
              justifyContent: 'flex-start',
              textTransform: 'none'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: isActive ? 600 : 400 }}>
                {layer.name}
              </Typography>
              {count > 0 && (
                <Chip 
                  label={count} 
                  size="small" 
                  color={isActive ? 'secondary' : 'default'}
                  sx={{ height: 20, fontSize: '0.75rem' }}
                />
              )}
            </Box>
          </Button>
          
          {isActive && (
            <Tooltip title="Layer Settings">
              <IconButton
                size="small"
                onClick={() => setExpandedGroups(prev => ({
                  ...prev,
                  [`${layer.id}_settings`]: !prev[`${layer.id}_settings`]
                }))}
              >
                <SettingsIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </ButtonGroup>

        {/* Layer Settings Collapse */}
        <Collapse in={isActive && expandedGroups[`${layer.id}_settings`]}>
          <Paper sx={{ p: 1, mt: 1, ml: 2, bgcolor: 'action.hover' }}>
            <Typography variant="caption" gutterBottom>
              Opacity
            </Typography>
            <Slider
              size="small"
              value={(layers[`${layer.id}_opacity`] || 1) * 100}
              onChange={(e, value) => handleOpacityChange(layer.id, value / 100)}
              min={10}
              max={100}
              step={10}
              marks
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value}%`}
            />
            {kioskMode && (
              <Typography variant="caption" color="text.secondary">
                {layer.description}
              </Typography>
            )}
          </Paper>
        </Collapse>
      </Box>
    );
  };

  const renderGroupHeader = (group) => {
    const isExpanded = expandedGroups[group.id];
    const groupLayerCount = group.layers.reduce((sum, layer) => sum + getLayerCount(layer.id), 0);
    const groupAlerts = group.layers.reduce((sum, layer) => sum + getLayerAlerts(layer.id), 0);

    return (
      <Button
        fullWidth
        onClick={() => handleGroupToggle(group.id)}
        startIcon={
          <Badge badgeContent={groupAlerts} color="error" invisible={groupAlerts === 0}>
            {group.icon}
          </Badge>
        }
        endIcon={isExpanded ? <CollapseIcon /> : <ExpandIcon />}
        sx={{
          justifyContent: 'space-between',
          textTransform: 'none',
          bgcolor: isExpanded ? 'action.selected' : 'transparent',
          '&:hover': {
            bgcolor: 'action.hover'
          }
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            {group.name}
          </Typography>
          {groupLayerCount > 0 && (
            <Chip 
              label={groupLayerCount} 
              size="small" 
              color="primary"
              sx={{ height: 18, fontSize: '0.7rem' }}
            />
          )}
        </Box>
      </Button>
    );
  };

  const displayGroups = getEmergencyLayers();

  return (
    <Paper 
      sx={{ 
        p: 2, 
        minWidth: orientation === 'vertical' ? 280 : 'auto',
        maxHeight: orientation === 'vertical' ? '80vh' : 'auto',
        overflowY: 'auto',
        bgcolor: emergencyMode ? 'error.light' : 'background.paper',
        border: emergencyMode ? 2 : 0,
        borderColor: emergencyMode ? 'error.main' : 'transparent'
      }}
    >
      {/* Header with mode controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          {emergencyMode ? 'Emergency Layers' : 'Floor Plan Layers'}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          {onEmergencyMode && (
            <Tooltip title={emergencyMode ? 'Exit Emergency Mode' : 'Emergency Mode'}>
              <IconButton
                onClick={onEmergencyMode}
                color={emergencyMode ? 'error' : 'default'}
                size="small"
              >
                <EmergencyIcon />
              </IconButton>
            </Tooltip>
          )}
          
          {onKioskMode && (
            <Tooltip title={kioskMode ? 'Exit Kiosk Mode' : 'Kiosk Mode'}>
              <IconButton
                onClick={onKioskMode}
                color={kioskMode ? 'primary' : 'default'}
                size="small"
              >
                <KioskIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Emergency mode alert */}
      {emergencyMode && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Emergency Mode: Only critical layers are shown
        </Alert>
      )}

      {/* Quick Actions */}
      {!emergencyMode && (
        <Box sx={{ mb: 2 }}>
          <ButtonGroup size="small" fullWidth>
            <Button
              onClick={() => {
                displayGroups.forEach(group => {
                  group.layers.forEach(layer => {
                    if (!layers[layer.id]) handleLayerToggle(layer.id);
                  });
                });
              }}
            >
              Show All
            </Button>
            <Button
              onClick={() => {
                displayGroups.forEach(group => {
                  group.layers.forEach(layer => {
                    if (layers[layer.id]) handleLayerToggle(layer.id);
                  });
                });
              }}
            >
              Hide All
            </Button>
          </ButtonGroup>
        </Box>
      )}

      {/* Layer Groups */}
      {displayGroups.map((group, index) => (
        <Box key={group.id} sx={{ mb: 2 }}>
          {renderGroupHeader(group)}
          
          <Collapse in={expandedGroups[group.id]}>
            <Box sx={{ mt: 1, ml: 1 }}>
              {group.layers.map(renderLayerButton)}
            </Box>
          </Collapse>
          
          {index < displayGroups.length - 1 && <Divider sx={{ mt: 2 }} />}
        </Box>
      ))}

      {/* Global Settings */}
      {!emergencyMode && (
        <Box sx={{ mt: 2, pt: 2, borderTop: 1, borderColor: 'divider' }}>
          <FormControlLabel
            control={
              <Switch
                checked={showSettings}
                onChange={(e) => setShowSettings(e.target.checked)}
                size="small"
              />
            }
            label={
              <Typography variant="caption">
                Show Layer Details
              </Typography>
            }
          />
        </Box>
      )}

      {/* Layer Statistics */}
      {showSettings && !emergencyMode && (
        <Box sx={{ mt: 2, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary" gutterBottom>
            Layer Statistics
          </Typography>
          <Grid container spacing={1}>
            {displayGroups.map(group => 
              group.layers.map(layer => (
                <Grid item xs={6} key={layer.id}>
                  <Typography variant="caption">
                    {layer.name}: {getLayerCount(layer.id)}
                  </Typography>
                </Grid>
              ))
            )}
          </Grid>
        </Box>
      )}
    </Paper>
  );
};

export default LayerToggleBar;