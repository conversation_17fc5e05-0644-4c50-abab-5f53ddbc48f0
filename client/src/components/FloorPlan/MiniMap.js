import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Zoom,
  Collapse
} from '@mui/material';
import {
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  CenterFocusStrong as CenterIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as ExitFullscreenIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon
} from '@mui/icons-material';

/**
 * MiniMap Component
 * Provides overview navigation for FloorPlanViewer with viewport indicator,
 * zoom controls, and quick navigation capabilities
 */
const MiniMap = ({
  floorplanImageUrl,
  currentView = { x: 0, y: 0, zoom: 1 },
  floorDimensions = { width: 1000, height: 800 },
  viewportDimensions = { width: 800, height: 600 },
  onViewChange,
  layers = {},
  layerCounts = {},
  alerts = {},
  emergencyMode = false,
  collapsed = false,
  onCollapse
}) => {
  const canvasRef = useRef(null);
  const [isExpanded, setIsExpanded] = useState(!collapsed);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });

  // MiniMap dimensions
  const MINIMAP_WIDTH = 200;
  const MINIMAP_HEIGHT = 160;

  // Calculate viewport rectangle on minimap
  const getViewportRect = () => {
    const scaleX = MINIMAP_WIDTH / floorDimensions.width;
    const scaleY = MINIMAP_HEIGHT / floorDimensions.height;
    
    const viewportWidth = (viewportDimensions.width / currentView.zoom) * scaleX;
    const viewportHeight = (viewportDimensions.height / currentView.zoom) * scaleY;
    
    const viewportX = (-currentView.x * scaleX) - (viewportWidth / 2);
    const viewportY = (-currentView.y * scaleY) - (viewportHeight / 2);
    
    return {
      x: Math.max(0, Math.min(MINIMAP_WIDTH - viewportWidth, viewportX)),
      y: Math.max(0, Math.min(MINIMAP_HEIGHT - viewportHeight, viewportY)),
      width: Math.min(viewportWidth, MINIMAP_WIDTH),
      height: Math.min(viewportHeight, MINIMAP_HEIGHT)
    };
  };

  // Convert minimap coordinates to floor coordinates
  const minimapToFloorCoords = (minimapX, minimapY) => {
    const scaleX = floorDimensions.width / MINIMAP_WIDTH;
    const scaleY = floorDimensions.height / MINIMAP_HEIGHT;
    
    return {
      x: minimapX * scaleX,
      y: minimapY * scaleY
    };
  };

  // Render minimap with layers and viewport indicator
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT);

    // Draw floorplan background
    if (floorplanImageUrl) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT);
        drawLayerOverlays(ctx);
        drawViewportIndicator(ctx);
      };
      img.src = floorplanImageUrl;
    } else {
      // Draw placeholder background
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT);
      ctx.strokeStyle = '#ddd';
      ctx.strokeRect(0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT);
      
      drawLayerOverlays(ctx);
      drawViewportIndicator(ctx);
    }
  }, [floorplanImageUrl, currentView, layers, layerCounts, alerts, emergencyMode]);

  const drawLayerOverlays = (ctx) => {
    const scaleX = MINIMAP_WIDTH / floorDimensions.width;
    const scaleY = MINIMAP_HEIGHT / floorDimensions.height;

    // Draw active layers as colored dots/indicators
    Object.entries(layers).forEach(([layerId, isActive]) => {
      if (!isActive) return;

      const count = layerCounts[layerId] || 0;
      const alertCount = alerts[layerId] || 0;

      if (count > 0) {
        // Get layer color
        const layerColor = getLayerColor(layerId, alertCount > 0);
        
        // Draw simplified layer representation
        ctx.fillStyle = layerColor;
        ctx.globalAlpha = 0.6;
        
        // For simplicity, distribute dots across the minimap
        // In a real implementation, you'd use actual item coordinates
        for (let i = 0; i < Math.min(count, 20); i++) {
          const x = (Math.random() * 0.8 + 0.1) * MINIMAP_WIDTH;
          const y = (Math.random() * 0.8 + 0.1) * MINIMAP_HEIGHT;
          
          ctx.beginPath();
          ctx.arc(x, y, alertCount > 0 ? 3 : 2, 0, 2 * Math.PI);
          ctx.fill();
        }
        
        ctx.globalAlpha = 1;
      }
    });
  };

  const drawViewportIndicator = (ctx) => {
    const viewportRect = getViewportRect();
    
    // Draw viewport rectangle
    ctx.strokeStyle = emergencyMode ? '#f44336' : '#1976d2';
    ctx.lineWidth = 2;
    ctx.setLineDash([4, 4]);
    ctx.strokeRect(
      viewportRect.x,
      viewportRect.y,
      viewportRect.width,
      viewportRect.height
    );
    
    // Draw viewport fill
    ctx.fillStyle = emergencyMode ? 'rgba(244, 67, 54, 0.1)' : 'rgba(25, 118, 210, 0.1)';
    ctx.fillRect(
      viewportRect.x,
      viewportRect.y,
      viewportRect.width,
      viewportRect.height
    );
    
    ctx.setLineDash([]);
  };

  const getLayerColor = (layerId, hasAlert) => {
    if (hasAlert) return '#f44336'; // Red for alerts
    
    const layerColors = {
      electrical: '#ff9800',
      temperature: '#e91e63',
      doors: '#4caf50',
      cameras: '#2196f3',
      hvac: '#9c27b0',
      safety: '#f44336',
      wifi: '#00bcd4',
      utility: '#607d8b',
      events: '#3f51b5',
      av: '#795548'
    };
    
    return layerColors[layerId] || '#9e9e9e';
  };

  const handleMouseDown = (e) => {
    if (!isExpanded) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setIsDragging(true);
    setLastMousePos({ x, y });
    
    // Navigate to clicked position
    handleNavigation(x, y);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !isExpanded) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    handleNavigation(x, y);
    setLastMousePos({ x, y });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleNavigation = (minimapX, minimapY) => {
    if (!onViewChange) return;
    
    const floorCoords = minimapToFloorCoords(minimapX, minimapY);
    
    // Calculate new view position to center on clicked point
    const newView = {
      ...currentView,
      x: -(floorCoords.x - (viewportDimensions.width / (2 * currentView.zoom))),
      y: -(floorCoords.y - (viewportDimensions.height / (2 * currentView.zoom)))
    };
    
    onViewChange(newView);
  };

  const handleZoomIn = () => {
    if (!onViewChange) return;
    
    const newZoom = Math.min(currentView.zoom * 1.5, 5);
    onViewChange({ ...currentView, zoom: newZoom });
  };

  const handleZoomOut = () => {
    if (!onViewChange) return;
    
    const newZoom = Math.max(currentView.zoom / 1.5, 0.1);
    onViewChange({ ...currentView, zoom: newZoom });
  };

  const handleCenter = () => {
    if (!onViewChange) return;
    
    onViewChange({
      x: 0,
      y: 0,
      zoom: 1
    });
  };

  const handleFullscreen = () => {
    if (onViewChange) {
      // Fit to viewport
      const scaleX = viewportDimensions.width / floorDimensions.width;
      const scaleY = viewportDimensions.height / floorDimensions.height;
      const fitZoom = Math.min(scaleX, scaleY) * 0.9;
      
      onViewChange({
        x: 0,
        y: 0,
        zoom: fitZoom
      });
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (onCollapse) {
      onCollapse(!isExpanded);
    }
  };

  return (
    <Paper 
      sx={{ 
        position: 'absolute',
        top: 16,
        right: 16,
        zIndex: 1000,
        overflow: 'hidden',
        bgcolor: emergencyMode ? 'error.light' : 'background.paper',
        border: emergencyMode ? 2 : 1,
        borderColor: emergencyMode ? 'error.main' : 'divider'
      }}
    >
      {/* Header */}
      <Box 
        sx={{ 
          p: 1, 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          bgcolor: emergencyMode ? 'error.main' : 'action.hover',
          color: emergencyMode ? 'error.contrastText' : 'text.primary'
        }}
      >
        <Typography variant="caption" sx={{ fontWeight: 600 }}>
          {emergencyMode ? 'Emergency Map' : 'Navigation'}
        </Typography>
        
        <IconButton 
          size="small" 
          onClick={toggleExpanded}
          sx={{ color: 'inherit' }}
        >
          {isExpanded ? <CollapseIcon /> : <ExpandIcon />}
        </IconButton>
      </Box>

      {/* MiniMap Content */}
      <Collapse in={isExpanded}>
        <Box sx={{ p: 1 }}>
          {/* Canvas */}
          <Box 
            sx={{ 
              position: 'relative', 
              cursor: isDragging ? 'grabbing' : 'crosshair',
              border: 1,
              borderColor: 'divider',
              borderRadius: 1,
              overflow: 'hidden'
            }}
          >
            <canvas
              ref={canvasRef}
              width={MINIMAP_WIDTH}
              height={MINIMAP_HEIGHT}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
              style={{ display: 'block' }}
            />
            
            {/* Zoom level indicator */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 4,
                left: 4,
                bgcolor: 'rgba(0,0,0,0.7)',
                color: 'white',
                px: 1,
                py: 0.5,
                borderRadius: 0.5,
                fontSize: '0.7rem'
              }}
            >
              {Math.round(currentView.zoom * 100)}%
            </Box>
          </Box>

          {/* Controls */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
            <Box>
              <Tooltip title="Zoom In">
                <IconButton size="small" onClick={handleZoomIn}>
                  <ZoomInIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Zoom Out">
                <IconButton size="small" onClick={handleZoomOut}>
                  <ZoomOutIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            
            <Box>
              <Tooltip title="Center View">
                <IconButton size="small" onClick={handleCenter}>
                  <CenterIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Fit to View">
                <IconButton size="small" onClick={handleFullscreen}>
                  <FullscreenIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Layer Legend */}
          {Object.keys(layers).some(layerId => layers[layerId]) && (
            <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider' }}>
              <Typography variant="caption" color="text.secondary" gutterBottom>
                Active Layers
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {Object.entries(layers)
                  .filter(([layerId, isActive]) => isActive)
                  .map(([layerId]) => (
                    <Box
                      key={layerId}
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor: getLayerColor(layerId, alerts[layerId] > 0)
                      }}
                    />
                  ))
                }
              </Box>
            </Box>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default MiniMap;