import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Collapse,
  Chip,
  IconButton,
  Tooltip,
  Button,
  ButtonGroup,
  Divider,
  Badge,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Power as ElectricalIcon,
  Thermostat as TemperatureIcon,
  Door as DoorIcon,
  Videocam as CameraIcon,
  Air as HVACIcon,
  LocalFireDepartment as SafetyIcon,
  Wifi as WiFiIcon,
  Water as UtilityIcon,
  Event as EventIcon,
  Audiotrack as AVIcon,
  FilterList as FilterIcon,
  BookmarkBorder as SaveIcon,
  History as HistoryIcon,
  LocationOn as LocationIcon,
  Room as RoomIcon
} from '@mui/icons-material';

// Import existing services for search functionality
import electricalService from '../../services/electricalService';
import safetyService from '../../services/safetyService';
import hvacService from '../../services/hvacService';
import wifiService from '../../services/wifiService';
import utilityShutoffService from '../../services/utilityShutoffService';
import buildingManagementService from '../../services/buildingManagementService';

/**
 * LeftSidebar Component
 * Provides layer filters, quick actions, search results, and navigation
 * Leverages existing service integrations for comprehensive building search
 */
const LeftSidebar = ({
  buildingId,
  floorId,
  onLayerToggle,
  onItemSelect,
  onQuickAction,
  selectedLayers = {},
  width = 320,
  collapsed = false,
  onCollapse
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [expandedSections, setExpandedSections] = useState({
    search: true,
    layers: true,
    quickActions: false,
    filters: false
  });

  // Quick action categories leveraging existing integrations
  const quickActions = [
    {
      id: 'find_breaker',
      label: 'Find Breaker',
      icon: <ElectricalIcon />,
      description: 'Trace outlet to electrical panel',
      action: () => onQuickAction('electrical_trace'),
      color: 'warning'
    },
    {
      id: 'nearest_safety',
      label: 'Nearest Safety Equipment',
      icon: <SafetyIcon />,
      description: 'Find nearest fire extinguisher or AED',
      action: () => onQuickAction('safety_locator'),
      color: 'error'
    },
    {
      id: 'emergency_shutoff',
      label: 'Emergency Shutoffs',
      icon: <UtilityIcon />,
      description: 'Locate utility shutoff valves',
      action: () => onQuickAction('emergency_shutoffs'),
      color: 'error'
    },
    {
      id: 'temperature_check',
      label: 'Temperature Check',
      icon: <TemperatureIcon />,
      description: 'View temperature heatmap',
      action: () => onQuickAction('temperature_heatmap'),
      color: 'info'
    },
    {
      id: 'wifi_coverage',
      label: 'WiFi Coverage',
      icon: <WiFiIcon />,
      description: 'Check WiFi signal strength',
      action: () => onQuickAction('wifi_coverage'),
      color: 'success'
    },
    {
      id: 'door_status',
      label: 'Door Status',
      icon: <DoorIcon />,
      description: 'View all door lock status',
      action: () => onQuickAction('door_overview'),
      color: 'primary'
    }
  ];

  // Layer categories with badge counts
  const layerCategories = [
    {
      id: 'infrastructure',
      name: 'Infrastructure',
      icon: <ElectricalIcon />,
      layers: ['electrical', 'hvac', 'utility'],
      color: 'warning.main'
    },
    {
      id: 'security',
      name: 'Security & Safety',
      icon: <SafetyIcon />,
      layers: ['doors', 'cameras', 'safety'],
      color: 'error.main'
    },
    {
      id: 'technology',
      name: 'Technology',
      icon: <WiFiIcon />,
      layers: ['wifi', 'av_equipment'],
      color: 'success.main'
    },
    {
      id: 'events',
      name: 'Events & Operations',
      icon: <EventIcon />,
      layers: ['events', 'volunteers', 'wayfinding'],
      color: 'info.main'
    }
  ];

  useEffect(() => {
    // Load search history from localStorage
    const savedHistory = localStorage.getItem('bms_search_history');
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory));
    }
  }, []);

  const handleSearch = async (query) => {
    if (!query.trim() || query.length < 2) {
      setSearchResults([]);
      return;
    }

    setSearchLoading(true);
    try {
      // Search across all systems using existing services
      const searchPromises = [
        searchElectrical(query),
        searchSafety(query),
        searchHVAC(query),
        searchWiFi(query),
        searchUtility(query),
        searchRooms(query)
      ];

      const results = await Promise.allSettled(searchPromises);
      const combinedResults = results
        .filter(result => result.status === 'fulfilled')
        .flatMap(result => result.value)
        .slice(0, 20); // Limit to 20 results

      setSearchResults(combinedResults);

      // Save to search history
      saveToSearchHistory(query);

    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  const searchElectrical = async (query) => {
    try {
      const [outlets, panels] = await Promise.all([
        electricalService.searchOutlets({ buildingId, floorId, search: query }),
        electricalService.searchPanels({ buildingId, floorId, search: query })
      ]);

      const results = [];
      
      outlets.forEach(outlet => {
        results.push({
          id: `outlet_${outlet._id}`,
          type: 'electrical_outlet',
          title: outlet.label,
          subtitle: `Outlet in ${outlet.room}`,
          icon: <ElectricalIcon />,
          data: outlet,
          action: () => onItemSelect(outlet, 'electrical_outlet')
        });
      });

      panels.forEach(panel => {
        results.push({
          id: `panel_${panel._id}`,
          type: 'electrical_panel',
          title: panel.name,
          subtitle: `Panel ${panel.code} in ${panel.room}`,
          icon: <ElectricalIcon />,
          data: panel,
          action: () => onItemSelect(panel, 'electrical_panel')
        });
      });

      return results;
    } catch (error) {
      console.warn('Electrical search failed:', error);
      return [];
    }
  };

  const searchSafety = async (query) => {
    try {
      const assets = await safetyService.searchSafetyAssets({ buildingId, floorId, search: query });
      
      return assets.map(asset => ({
        id: `safety_${asset._id}`,
        type: 'safety_asset',
        title: `${asset.assetType.replace('_', ' ')} - ${asset.name}`,
        subtitle: `Located in ${asset.room}`,
        icon: <SafetyIcon />,
        data: asset,
        action: () => onItemSelect(asset, 'safety_asset')
      }));
    } catch (error) {
      console.warn('Safety search failed:', error);
      return [];
    }
  };

  const searchHVAC = async (query) => {
    try {
      const units = await hvacService.searchUnits({ buildingId, floorId, search: query });
      
      return units.map(unit => ({
        id: `hvac_${unit._id}`,
        type: 'hvac_unit',
        title: `HVAC Unit ${unit.name}`,
        subtitle: `${unit.type} in ${unit.room}`,
        icon: <HVACIcon />,
        data: unit,
        action: () => onItemSelect(unit, 'hvac_unit')
      }));
    } catch (error) {
      console.warn('HVAC search failed:', error);
      return [];
    }
  };

  const searchWiFi = async (query) => {
    try {
      const accessPoints = await wifiService.searchAccessPoints({ buildingId, floorId, search: query });
      
      return accessPoints.map(ap => ({
        id: `wifi_${ap._id}`,
        type: 'wifi_ap',
        title: `WiFi AP ${ap.name}`,
        subtitle: `SSID: ${ap.ssid}`,
        icon: <WiFiIcon />,
        data: ap,
        action: () => onItemSelect(ap, 'wifi_ap')
      }));
    } catch (error) {
      console.warn('WiFi search failed:', error);
      return [];
    }
  };

  const searchUtility = async (query) => {
    try {
      const shutoffs = await utilityShutoffService.searchShutoffs({ buildingId, floorId, search: query });
      
      return shutoffs.map(shutoff => ({
        id: `utility_${shutoff._id}`,
        type: 'utility_shutoff',
        title: `${shutoff.utilityType} Shutoff`,
        subtitle: `${shutoff.name} in ${shutoff.room}`,
        icon: <UtilityIcon />,
        data: shutoff,
        action: () => onItemSelect(shutoff, 'utility_shutoff')
      }));
    } catch (error) {
      console.warn('Utility search failed:', error);
      return [];
    }
  };

  const searchRooms = async (query) => {
    try {
      const building = await buildingManagementService.getBuilding(buildingId);
      const rooms = building.floors?.find(f => f._id === floorId)?.rooms || [];
      
      const matchingRooms = rooms.filter(room => 
        room.name.toLowerCase().includes(query.toLowerCase()) ||
        room.number?.toLowerCase().includes(query.toLowerCase())
      );

      return matchingRooms.map(room => ({
        id: `room_${room._id}`,
        type: 'room',
        title: room.name,
        subtitle: `Room ${room.number}`,
        icon: <RoomIcon />,
        data: room,
        action: () => onItemSelect(room, 'room')
      }));
    } catch (error) {
      console.warn('Room search failed:', error);
      return [];
    }
  };

  const saveToSearchHistory = (query) => {
    const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('bms_search_history', JSON.stringify(newHistory));
  };

  const handleSectionToggle = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getLayerCount = (layerId) => {
    // This would normally come from props or API
    const mockCounts = {
      electrical: 45,
      doors: 12,
      cameras: 8,
      safety: 15,
      hvac: 6,
      wifi: 18,
      utility: 4,
      events: 3
    };
    return mockCounts[layerId] || 0;
  };

  const getLayerAlerts = (layerId) => {
    // Mock alert counts
    const mockAlerts = {
      safety: 2,
      hvac: 1,
      electrical: 0
    };
    return mockAlerts[layerId] || 0;
  };

  if (collapsed) {
    return (
      <Paper 
        sx={{ 
          width: 64, 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center',
          py: 2
        }}
      >
        <IconButton onClick={onCollapse} sx={{ mb: 2 }}>
          <ExpandMoreIcon />
        </IconButton>
        <IconButton sx={{ mb: 1 }}>
          <SearchIcon />
        </IconButton>
        <IconButton sx={{ mb: 1 }}>
          <FilterIcon />
        </IconButton>
      </Paper>
    );
  }

  return (
    <Paper 
      sx={{ 
        width, 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        overflow: 'hidden'
      }}
    >
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Building Navigator
          </Typography>
          <IconButton onClick={onCollapse} size="small">
            <ExpandLessIcon />
          </IconButton>
        </Box>

        {/* Search */}
        <TextField
          fullWidth
          size="small"
          placeholder="Search rooms, equipment, systems..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleSearch(searchQuery);
            }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <IconButton 
                  size="small" 
                  onClick={() => {
                    setSearchQuery('');
                    setSearchResults([]);
                  }}
                >
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            )
          }}
        />

        <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => handleSearch(searchQuery)}
            disabled={!searchQuery.trim() || searchLoading}
          >
            {searchLoading ? <CircularProgress size={16} /> : 'Search'}
          </Button>
          <Button
            size="small"
            variant="text"
            startIcon={<SaveIcon />}
            disabled={!searchQuery.trim()}
          >
            Save
          </Button>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {/* Search Results */}
        {searchQuery && (
          <Accordion 
            expanded={expandedSections.search}
            onChange={() => handleSectionToggle('search')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2">
                Search Results ({searchResults.length})
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              {searchResults.length > 0 ? (
                <List dense>
                  {searchResults.map((result) => (
                    <ListItemButton
                      key={result.id}
                      onClick={result.action}
                      sx={{ pl: 2 }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {result.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={result.title}
                        secondary={result.subtitle}
                        primaryTypographyProps={{ variant: 'body2' }}
                        secondaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItemButton>
                  ))}
                </List>
              ) : searchQuery && !searchLoading ? (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    No results found for "{searchQuery}"
                  </Typography>
                </Box>
              ) : null}
            </AccordionDetails>
          </Accordion>
        )}

        {/* Search History */}
        {searchHistory.length > 0 && !searchQuery && (
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <HistoryIcon fontSize="small" />
                Recent Searches
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              <List dense>
                {searchHistory.map((query, index) => (
                  <ListItemButton
                    key={index}
                    onClick={() => {
                      setSearchQuery(query);
                      handleSearch(query);
                    }}
                    sx={{ pl: 2 }}
                  >
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <HistoryIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={query} />
                  </ListItemButton>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Layer Controls */}
        <Accordion 
          expanded={expandedSections.layers}
          onChange={() => handleSectionToggle('layers')}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">
              Layer Controls
            </Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 0 }}>
            {layerCategories.map((category) => (
              <Box key={category.id} sx={{ mb: 1 }}>
                <ListItem sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    {category.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={category.name} 
                    primaryTypographyProps={{ variant: 'body2', fontWeight: 600 }}
                  />
                </ListItem>
                
                <List dense sx={{ pl: 2 }}>
                  {category.layers.map((layerId) => {
                    const count = getLayerCount(layerId);
                    const alerts = getLayerAlerts(layerId);
                    const isActive = selectedLayers[layerId];
                    
                    return (
                      <ListItemButton
                        key={layerId}
                        onClick={() => onLayerToggle(layerId, !isActive)}
                        sx={{ py: 0.5 }}
                      >
                        <ListItemText
                          primary={layerId.replace('_', ' ').toUpperCase()}
                          primaryTypographyProps={{ 
                            variant: 'caption',
                            fontWeight: isActive ? 600 : 400
                          }}
                        />
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          {count > 0 && (
                            <Chip 
                              label={count} 
                              size="small" 
                              variant={isActive ? "filled" : "outlined"}
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          )}
                          {alerts > 0 && (
                            <Badge 
                              badgeContent={alerts} 
                              color="error" 
                              sx={{ '& .MuiBadge-badge': { fontSize: '0.6rem', minWidth: 16, height: 16 } }}
                            >
                              <Box sx={{ width: 16, height: 16 }} />
                            </Badge>
                          )}
                        </Box>
                      </ListItemButton>
                    );
                  })}
                </List>
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>

        {/* Quick Actions */}
        <Accordion 
          expanded={expandedSections.quickActions}
          onChange={() => handleSectionToggle('quickActions')}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle2">
              Quick Actions
            </Typography>
          </AccordionSummary>
          <AccordionDetails sx={{ p: 1 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="outlined"
                  color={action.color}
                  startIcon={action.icon}
                  onClick={action.action}
                  size="small"
                  sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                >
                  <Box>
                    <Typography variant="caption" display="block">
                      {action.label}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" display="block">
                      {action.description}
                    </Typography>
                  </Box>
                </Button>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Paper>
  );
};

export default LeftSidebar;