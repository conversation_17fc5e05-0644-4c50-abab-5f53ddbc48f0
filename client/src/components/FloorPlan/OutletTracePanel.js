import React, { useEffect, useMemo, useState } from 'react';
import { Box, Typography, TextField, List, ListItemButton, ListItemText, Divider, Button } from '@mui/material';
import electricalService from '../../services/electricalService';

/**
 * OutletTracePanel
 * Minimal search panel to select an outlet and trigger a trace path.
 *
 * Props:
 *  - onTrace: function(traceResult)
 *  - onClearTrace: function()
 *  - floorId: string
 *  - buildingId: string (currently unused, reserved for future filtering)
 */
const OutletTracePanel = ({ onTrace, onClearTrace, floorId, buildingId }) => {
  const [outlets, setOutlets] = useState([]);
  const [panels, setPanels] = useState([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    let cancelled = false;
    const load = async () => {
      if (!floorId) return;
      setLoading(true);
      try {
        const [o, p] = await Promise.all([
          electricalService.getOutlets({ floorId }),
          electricalService.getPanels({ floorId })
        ]);
        if (!cancelled) {
          setOutlets(o || []);
          setPanels(p || []);
        }
      } catch (e) {
        console.error('OutletTracePanel: failed to load electrical data', e);
        if (!cancelled) {
          setOutlets([]);
          setPanels([]);
        }
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    load();
    return () => { cancelled = true; };
  }, [floorId]);

  const filteredOutlets = useMemo(() => {
    const term = (search || '').toLowerCase();
    if (!term) return outlets;
    return outlets.filter(o => {
      const label = (o.label || o.name || o.metadata?.label || '').toLowerCase();
      const room = (o.room || o.metadata?.room || '').toLowerCase();
      return label.includes(term) || room.includes(term);
    });
  }, [outlets, search]);

  const handleTrace = async (outletIcon) => {
    try {
      const result = await electricalService.traceOutlet(outletIcon, panels);
      onTrace && onTrace(result);
    } catch (e) {
      console.error('OutletTracePanel: trace failed', e);
    }
  };

  return (
    <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Typography variant="h6" sx={{ mb: 1 }}>Outlet Trace</Typography>
      <TextField
        size="small"
        placeholder="Search by outlet label or room"
        value={search}
        onChange={e => setSearch(e.target.value)}
        fullWidth
      />
      <Divider sx={{ my: 1 }} />
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Typography variant="body2">Loading…</Typography>
        ) : (
          <List dense>
            {filteredOutlets.map((o) => (
              <ListItemButton key={o._id} onClick={() => handleTrace(o)}>
                <ListItemText
                  primary={o.label || o.name || 'Outlet'}
                  secondary={o.room || o.metadata?.room || o.metadata?.description || ''}
                />
              </ListItemButton>
            ))}
            {!filteredOutlets.length && (
              <Typography variant="body2" sx={{ px: 2, py: 1 }}>No outlets found on this floor.</Typography>
            )}
          </List>
        )}
      </Box>
      <Divider sx={{ my: 1 }} />
      <Button onClick={() => onClearTrace && onClearTrace()} variant="outlined" fullWidth>
        Clear Trace
      </Button>
    </Box>
  );
};

export default OutletTracePanel;
