import React, { useState, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Switch,
  FormControlLabel,
  Slider,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  GridOn as GridIcon,
  PhotoCamera as PhotoIcon,
  Attachment as AttachmentIcon,
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  CenterFocusStrong as CenterIcon,
  Straighten as RulerIcon,
  Architecture as ArchitectureIcon
} from '@mui/icons-material';

/**
 * EditorEnhancements Component
 * Provides advanced editing features for FloorPlanEditor including:
 * - Snap-to-grid functionality
 * - Photo attachments for items
 * - Measurement tools
 * - Architectural guides
 */
const EditorEnhancements = ({
  enabled = false,
  onSnapToGridChange,
  onPhotoAttachment,
  onMeasurementTool,
  gridSize = 20,
  onGridSizeChange,
  showGrid = false,
  onShowGridChange,
  selectedItem = null,
  onItemUpdate
}) => {
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [measurementMode, setMeasurementMode] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  const [editorSettings, setEditorSettings] = useState({
    snapToGrid: true,
    showGuides: true,
    showMeasurements: false,
    magneticSnap: true,
    gridOpacity: 0.3,
    snapDistance: 10
  });

  const handleSnapToGridChange = (event) => {
    const enabled = event.target.checked;
    setEditorSettings(prev => ({ ...prev, snapToGrid: enabled }));
    onSnapToGridChange?.(enabled);
  };

  const handleGridSizeChange = (event, value) => {
    onGridSizeChange?.(value);
  };

  const handleShowGridChange = (event) => {
    const show = event.target.checked;
    setEditorSettings(prev => ({ ...prev, showGrid: show }));
    onShowGridChange?.(show);
  };

  const handlePhotoUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setUploadProgress(0);
      const uploadedPhotos = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Create form data for upload
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('itemId', selectedItem?.id || '');
        formData.append('itemType', selectedItem?.type || '');
        formData.append('description', `Photo for ${selectedItem?.name || 'item'}`);

        // Mock upload - in production this would go to your file upload endpoint
        const uploadedPhoto = await mockPhotoUpload(formData, (progress) => {
          setUploadProgress(((i + progress) / files.length) * 100);
        });

        uploadedPhotos.push(uploadedPhoto);
      }

      // Update attachments
      const newAttachments = [...attachments, ...uploadedPhotos];
      setAttachments(newAttachments);

      // Notify parent component
      if (onPhotoAttachment && selectedItem) {
        onPhotoAttachment(selectedItem.id, uploadedPhotos);
      }

      // Update item with photos
      if (onItemUpdate && selectedItem) {
        onItemUpdate(selectedItem.id, {
          ...selectedItem,
          photos: newAttachments
        });
      }

      setPhotoDialogOpen(false);
      setUploadProgress(0);
    } catch (error) {
      console.error('Error uploading photos:', error);
      setUploadProgress(0);
    }
  };

  const mockPhotoUpload = (formData, onProgress) => {
    return new Promise((resolve) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        onProgress(progress / 100);
        
        if (progress >= 100) {
          clearInterval(interval);
          resolve({
            id: `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            filename: formData.get('photo').name,
            url: URL.createObjectURL(formData.get('photo')),
            size: formData.get('photo').size,
            type: formData.get('photo').type,
            uploadedAt: new Date(),
            description: formData.get('description')
          });
        }
      }, 100);
    });
  };

  const handleDeletePhoto = (photoId) => {
    const updatedAttachments = attachments.filter(photo => photo.id !== photoId);
    setAttachments(updatedAttachments);
    
    if (onItemUpdate && selectedItem) {
      onItemUpdate(selectedItem.id, {
        ...selectedItem,
        photos: updatedAttachments
      });
    }
  };

  const handleMeasurementToggle = () => {
    const newMode = !measurementMode;
    setMeasurementMode(newMode);
    setEditorSettings(prev => ({ ...prev, showMeasurements: newMode }));
    onMeasurementTool?.(newMode);
  };

  const renderGridControls = () => (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Grid & Snapping
      </Typography>
      
      <FormControlLabel
        control={
          <Switch
            checked={editorSettings.snapToGrid}
            onChange={handleSnapToGridChange}
            size="small"
          />
        }
        label="Snap to Grid"
      />
      
      <FormControlLabel
        control={
          <Switch
            checked={showGrid}
            onChange={handleShowGridChange}
            size="small"
          />
        }
        label="Show Grid"
      />
      
      <FormControlLabel
        control={
          <Switch
            checked={editorSettings.magneticSnap}
            onChange={(e) => setEditorSettings(prev => ({ 
              ...prev, 
              magneticSnap: e.target.checked 
            }))}
            size="small"
          />
        }
        label="Magnetic Snap"
      />

      <Box sx={{ mt: 2 }}>
        <Typography variant="caption" gutterBottom>
          Grid Size: {gridSize}px
        </Typography>
        <Slider
          value={gridSize}
          onChange={handleGridSizeChange}
          min={10}
          max={50}
          step={5}
          marks
          valueLabelDisplay="auto"
          size="small"
        />
      </Box>

      <Box sx={{ mt: 2 }}>
        <Typography variant="caption" gutterBottom>
          Snap Distance: {editorSettings.snapDistance}px
        </Typography>
        <Slider
          value={editorSettings.snapDistance}
          onChange={(e, value) => setEditorSettings(prev => ({ 
            ...prev, 
            snapDistance: value 
          }))}
          min={5}
          max={25}
          step={5}
          marks
          valueLabelDisplay="auto"
          size="small"
        />
      </Box>
    </Box>
  );

  const renderMeasurementTools = () => (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Measurement Tools
      </Typography>
      
      <Button
        variant={measurementMode ? "contained" : "outlined"}
        onClick={handleMeasurementToggle}
        startIcon={<RulerIcon />}
        size="small"
        fullWidth
        sx={{ mb: 1 }}
      >
        {measurementMode ? 'Exit Measurement' : 'Start Measurement'}
      </Button>
      
      <FormControlLabel
        control={
          <Switch
            checked={editorSettings.showGuides}
            onChange={(e) => setEditorSettings(prev => ({ 
              ...prev, 
              showGuides: e.target.checked 
            }))}
            size="small"
          />
        }
        label="Show Alignment Guides"
      />
    </Box>
  );

  const renderPhotoAttachments = () => (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Photo Attachments
      </Typography>
      
      <Button
        variant="outlined"
        onClick={() => setPhotoDialogOpen(true)}
        startIcon={<PhotoIcon />}
        size="small"
        fullWidth
        disabled={!selectedItem}
        sx={{ mb: 1 }}
      >
        Add Photos
      </Button>

      {attachments.length > 0 && (
        <List dense>
          {attachments.map((photo) => (
            <ListItem key={photo.id}>
              <ListItemIcon>
                <PhotoIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary={photo.filename}
                secondary={`${(photo.size / 1024).toFixed(1)} KB`}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  onClick={() => handleDeletePhoto(photo.id)}
                  size="small"
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );

  const renderArchitecturalTools = () => (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Architectural Tools
      </Typography>
      
      <Grid container spacing={1}>
        <Grid item xs={6}>
          <Button
            variant="outlined"
            startIcon={<ArchitectureIcon />}
            size="small"
            fullWidth
          >
            Room Outline
          </Button>
        </Grid>
        <Grid item xs={6}>
          <Button
            variant="outlined"
            startIcon={<CenterIcon />}
            size="small"
            fullWidth
          >
            Center View
          </Button>
        </Grid>
      </Grid>
    </Box>
  );

  if (!enabled) return null;

  return (
    <>
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Editor Tools
        </Typography>
        
        {renderGridControls()}
        <Divider sx={{ my: 2 }} />
        
        {renderMeasurementTools()}
        <Divider sx={{ my: 2 }} />
        
        {renderPhotoAttachments()}
        <Divider sx={{ my: 2 }} />
        
        {renderArchitecturalTools()}
      </Paper>

      {/* Photo Upload Dialog */}
      <Dialog
        open={photoDialogOpen}
        onClose={() => setPhotoDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Photos to {selectedItem?.name || 'Item'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<UploadIcon />}
              fullWidth
              sx={{ mb: 2 }}
            >
              Choose Photos
              <input
                ref={fileInputRef}
                type="file"
                hidden
                multiple
                accept="image/*"
                onChange={handlePhotoUpload}
              />
            </Button>

            {uploadProgress > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption">
                  Uploading... {Math.round(uploadProgress)}%
                </Typography>
                <Slider
                  value={uploadProgress}
                  min={0}
                  max={100}
                  disabled
                  size="small"
                />
              </Box>
            )}

            <Typography variant="body2" color="text.secondary">
              Supported formats: JPG, PNG, GIF (max 5MB each)
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPhotoDialogOpen(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default EditorEnhancements;