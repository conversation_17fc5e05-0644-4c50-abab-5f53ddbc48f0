import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Card,
  CardContent,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  TextField,
  InputAdornment,
  Tooltip,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Slide,
  Collapse,
  Alert
} from '@mui/material';
import {
  Kiosk as KioskIcon,
  ExitToApp as ExitIcon,
  Search as SearchIcon,
  DirectionsWalk as DirectionsIcon,
  LocalFireDepartment as FireIcon,
  LocalHospital as MedicalIcon,
  Power as ElectricalIcon,
  Thermostat as TemperatureIcon,
  Wifi as WiFiIcon,
  Info as InfoIcon,
  Print as PrintIcon,
  QrCode as QRIcon,
  ReportProblem as EmergencyIcon,
  Help as HelpIcon,
  Translate as LanguageIcon,
  AccessibilityNew as AccessibilityIcon,
  VolumeUp as VolumeIcon,
  Fullscreen as FullscreenIcon
} from '@mui/icons-material';

// Import existing services for kiosk functionality
import wayfindingService from '../../services/wayfindingService';
import safetyService from '../../services/safetyService';
import electricalService from '../../services/electricalService';
import hvacService from '../../services/hvacService';
import wifiService from '../../services/wifiService';

/**
 * Kiosk Mode Interface
 * Touch-friendly interface for lobby tablets and public access
 * Provides simplified navigation and information access
 */
const KioskMode = ({
  buildingId,
  floorId,
  onExit,
  onShowRoute,
  onHighlightItem,
  onShowLayer,
  language = 'en'
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [selectedService, setSelectedService] = useState(null);
  const [showDirections, setShowDirections] = useState(false);
  const [currentRoute, setCurrentRoute] = useState(null);
  const [lastActivity, setLastActivity] = useState(Date.now());
  const [showInactivityWarning, setShowInactivityWarning] = useState(false);
  const [accessibilityMode, setAccessibilityMode] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(language);

  // Auto-logout after inactivity
  const INACTIVITY_TIMEOUT = 300000; // 5 minutes
  const WARNING_TIMEOUT = 240000; // 4 minutes

  useEffect(() => {
    const resetInactivityTimer = () => {
      setLastActivity(Date.now());
      setShowInactivityWarning(false);
    };

    const handleActivity = () => resetInactivityTimer();

    // Track user activity
    document.addEventListener('click', handleActivity);
    document.addEventListener('touchstart', handleActivity);
    document.addEventListener('keypress', handleActivity);

    return () => {
      document.removeEventListener('click', handleActivity);
      document.removeEventListener('touchstart', handleActivity);
      document.removeEventListener('keypress', handleActivity);
    };
  }, []);

  useEffect(() => {
    const checkInactivity = setInterval(() => {
      const now = Date.now();
      const timeSinceActivity = now - lastActivity;

      if (timeSinceActivity >= INACTIVITY_TIMEOUT) {
        onExit(); // Auto-logout
      } else if (timeSinceActivity >= WARNING_TIMEOUT) {
        setShowInactivityWarning(true);
      }
    }, 10000); // Check every 10 seconds

    return () => clearInterval(checkInactivity);
  }, [lastActivity, onExit]);

  const kioskServices = [
    {
      id: 'wayfinding',
      name: getText('wayfinding', 'Wayfinding & Directions'),
      description: getText('wayfinding_desc', 'Find your way around the building'),
      icon: <DirectionsIcon sx={{ fontSize: 40 }} />,
      color: 'primary.main',
      popular: true
    },
    {
      id: 'safety',
      name: getText('safety', 'Safety & Emergency'),
      description: getText('safety_desc', 'Locate safety equipment and emergency exits'),
      icon: <FireIcon sx={{ fontSize: 40 }} />,
      color: 'error.main',
      popular: true
    },
    {
      id: 'electrical',
      name: getText('electrical', 'Electrical Information'),
      description: getText('electrical_desc', 'Find electrical panels and outlets'),
      icon: <ElectricalIcon sx={{ fontSize: 40 }} />,
      color: 'warning.main'
    },
    {
      id: 'temperature',
      name: getText('temperature', 'Climate Information'),
      description: getText('temperature_desc', 'View building temperature and HVAC'),
      icon: <TemperatureIcon sx={{ fontSize: 40 }} />,
      color: 'info.main'
    },
    {
      id: 'wifi',
      name: getText('wifi', 'WiFi Information'),
      description: getText('wifi_desc', 'Find WiFi access points and coverage'),
      icon: <WiFiIcon sx={{ fontSize: 40 }} />,
      color: 'success.main'
    },
    {
      id: 'information',
      name: getText('information', 'Building Information'),
      description: getText('information_desc', 'General building information and contacts'),
      icon: <InfoIcon sx={{ fontSize: 40 }} />,
      color: 'grey.600'
    }
  ];

  const getText = (key, defaultText) => {
    // Enhanced translation system for better accessibility
    const translations = {
      en: {
        wayfinding: 'Wayfinding & Directions',
        wayfinding_desc: 'Find your way around the building',
        safety: 'Safety & Emergency',
        safety_desc: 'Locate safety equipment and emergency exits',
        electrical: 'Electrical Information',
        electrical_desc: 'Find electrical panels and outlets',
        temperature: 'Climate Information',
        temperature_desc: 'View building temperature and HVAC',
        wifi: 'WiFi Information',
        wifi_desc: 'Find WiFi access points and coverage',
        information: 'Building Information',
        information_desc: 'General building information and contacts',
        search_placeholder: 'Search for rooms, equipment, or services...',
        inactivity_warning: 'Kiosk will reset in 1 minute due to inactivity',
        tap_to_continue: 'Tap anywhere to continue',
        voice_instructions: 'Voice instructions available',
        accessibility_mode: 'High contrast accessibility mode',
        print_directions: 'Print directions',
        emergency_help: 'Emergency Help',
        language_settings: 'Language Settings'
      },
      es: {
        wayfinding: 'Navegación y Direcciones',
        wayfinding_desc: 'Encuentra tu camino por el edificio',
        safety: 'Seguridad y Emergencia',
        safety_desc: 'Localiza equipos de seguridad y salidas de emergencia',
        electrical: 'Información Eléctrica',
        electrical_desc: 'Encuentra paneles eléctricos y enchufes',
        temperature: 'Información Climática',
        temperature_desc: 'Ver temperatura del edificio y HVAC',
        wifi: 'Información WiFi',
        wifi_desc: 'Encuentra puntos de acceso WiFi y cobertura',
        information: 'Información del Edificio',
        information_desc: 'Información general del edificio y contactos',
        search_placeholder: 'Buscar salas, equipos o servicios...',
        inactivity_warning: 'El kiosco se reiniciará en 1 minuto por inactividad',
        tap_to_continue: 'Toca cualquier lugar para continuar',
        voice_instructions: 'Instrucciones de voz disponibles',
        accessibility_mode: 'Modo de accesibilidad de alto contraste',
        print_directions: 'Imprimir direcciones',
        emergency_help: 'Ayuda de Emergencia',
        language_settings: 'Configuración de Idioma'
      },
      fr: {
        wayfinding: 'Orientation et Directions',
        wayfinding_desc: 'Trouvez votre chemin dans le bâtiment',
        safety: 'Sécurité et Urgence',
        safety_desc: 'Localisez les équipements de sécurité et les sorties de secours',
        electrical: 'Informations Électriques',
        electrical_desc: 'Trouvez les panneaux électriques et les prises',
        temperature: 'Informations Climatiques',
        temperature_desc: 'Voir la température du bâtiment et la CVC',
        wifi: 'Informations WiFi',
        wifi_desc: 'Trouvez les points d\'accès WiFi et la couverture',
        information: 'Informations du Bâtiment',
        information_desc: 'Informations générales du bâtiment et contacts',
        search_placeholder: 'Rechercher des salles, équipements ou services...',
        inactivity_warning: 'Le kiosque se réinitialisera dans 1 minute par inactivité',
        tap_to_continue: 'Touchez n\'importe où pour continuer',
        voice_instructions: 'Instructions vocales disponibles',
        accessibility_mode: 'Mode d\'accessibilité à contraste élevé',
        print_directions: 'Imprimer les directions',
        emergency_help: 'Aide d\'Urgence',
        language_settings: 'Paramètres de Langue'
      }
    };
    
    return translations[currentLanguage]?.[key] || defaultText;
  };

  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' }
  ];

  const speakText = (text) => {
    if ('speechSynthesis' in window && accessibilityMode) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = currentLanguage === 'es' ? 'es-ES' : currentLanguage === 'fr' ? 'fr-FR' : 'en-US';
      utterance.rate = 0.8;
      utterance.pitch = 1.0;
      window.speechSynthesis.speak(utterance);
    }
  };

  const toggleAccessibilityMode = () => {
    const newMode = !accessibilityMode;
    setAccessibilityMode(newMode);
    
    if (newMode) {
      speakText(getText('accessibility_mode', 'High contrast accessibility mode activated'));
    }
  };

  const changeLanguage = (languageCode) => {
    setCurrentLanguage(languageCode);
    
    // Announce language change
    const languageName = availableLanguages.find(lang => lang.code === languageCode)?.name;
    if (languageName) {
      speakText(`Language changed to ${languageName}`);
    }
  };

  const handleSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      // Search across all systems using existing services
      const [wayfindingResults, safetyResults, electricalResults] = await Promise.allSettled([
        wayfindingService.searchRoutes({ buildingId, floorId, search: query }),
        safetyService.searchSafetyAssets({ buildingId, floorId, search: query }),
        electricalService.searchOutlets({ buildingId, floorId, search: query })
      ]);

      const results = [];

      // Process wayfinding results
      if (wayfindingResults.status === 'fulfilled') {
        wayfindingResults.value.forEach(route => {
          results.push({
            id: `route_${route._id}`,
            type: 'route',
            title: route.name,
            description: route.description,
            icon: <DirectionsIcon />,
            action: () => showRoute(route)
          });
        });
      }

      // Process safety results
      if (safetyResults.status === 'fulfilled') {
        safetyResults.value.forEach(asset => {
          results.push({
            id: `safety_${asset._id}`,
            type: 'safety',
            title: `${asset.assetType} - ${asset.name}`,
            description: `Located in ${asset.room}`,
            icon: <FireIcon />,
            action: () => highlightSafetyAsset(asset)
          });
        });
      }

      // Process electrical results
      if (electricalResults.status === 'fulfilled') {
        electricalResults.value.forEach(outlet => {
          results.push({
            id: `outlet_${outlet._id}`,
            type: 'electrical',
            title: outlet.label,
            description: `Room: ${outlet.room}`,
            icon: <ElectricalIcon />,
            action: () => highlightElectricalItem(outlet)
          });
        });
      }

      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    }
  };

  const handleServiceSelect = async (service) => {
    setSelectedService(service);
    
    // Show relevant layer on map
    const layerMap = {
      wayfinding: 'wayfinding',
      safety: 'safety',
      electrical: 'electrical',
      temperature: 'temperature',
      wifi: 'wifi'
    };
    
    if (layerMap[service.id]) {
      onShowLayer(layerMap[service.id], true);
    }

    // Load service-specific data
    switch (service.id) {
      case 'wayfinding':
        await loadWayfindingOptions();
        break;
      case 'safety':
        await loadSafetyInformation();
        break;
      case 'electrical':
        await loadElectricalInformation();
        break;
      case 'temperature':
        await loadTemperatureInformation();
        break;
      case 'wifi':
        await loadWiFiInformation();
        break;
    }
  };

  const loadWayfindingOptions = async () => {
    try {
      const routes = await wayfindingService.getWayfindingRoutes({ buildingId, floorId });
      const popularDestinations = routes.filter(route => route.popular);
      
      setSearchResults(popularDestinations.map(route => ({
        id: `route_${route._id}`,
        type: 'route',
        title: route.name,
        description: route.description,
        icon: <DirectionsIcon />,
        action: () => showRoute(route)
      })));
    } catch (error) {
      console.error('Failed to load wayfinding options:', error);
    }
  };

  const loadSafetyInformation = async () => {
    try {
      const assets = await safetyService.getSafetyAssets({ buildingId, floorId });
      const grouped = groupSafetyAssets(assets);
      
      setSearchResults(Object.entries(grouped).map(([type, items]) => ({
        id: `safety_${type}`,
        type: 'safety_group',
        title: `${type.replace('_', ' ').toUpperCase()} (${items.length})`,
        description: `${items.length} units available`,
        icon: <FireIcon />,
        action: () => highlightSafetyGroup(type, items)
      })));
    } catch (error) {
      console.error('Failed to load safety information:', error);
    }
  };

  const loadElectricalInformation = async () => {
    try {
      const panels = await electricalService.getPanels({ buildingId, floorId });
      
      setSearchResults(panels.map(panel => ({
        id: `panel_${panel._id}`,
        type: 'electrical',
        title: panel.name,
        description: `Located in ${panel.room}`,
        icon: <ElectricalIcon />,
        action: () => highlightElectricalPanel(panel)
      })));
    } catch (error) {
      console.error('Failed to load electrical information:', error);
    }
  };

  const loadTemperatureInformation = async () => {
    try {
      const tempData = await hvacService.getTemperatureData(buildingId, floorId);
      const analysis = hvacService.analyzeTemperatureData(tempData);
      
      setSearchResults([
        {
          id: 'temp_overview',
          type: 'temperature',
          title: `Average Temperature: ${analysis.averageTemperature}°F`,
          description: `${analysis.dataPoints} sensors active`,
          icon: <TemperatureIcon />,
          action: () => showTemperatureOverlay()
        }
      ]);
    } catch (error) {
      console.error('Failed to load temperature information:', error);
    }
  };

  const loadWiFiInformation = async () => {
    try {
      const accessPoints = await wifiService.getAccessPoints({ buildingId, floorId });
      
      setSearchResults(accessPoints.map(ap => ({
        id: `wifi_${ap._id}`,
        type: 'wifi',
        title: ap.name,
        description: `Channel: ${ap.channel} | Clients: ${ap.clientCount}`,
        icon: <WiFiIcon />,
        action: () => highlightWiFiAP(ap)
      })));
    } catch (error) {
      console.error('Failed to load WiFi information:', error);
    }
  };

  const showRoute = async (route) => {
    try {
      const routeData = await wayfindingService.getRoute(route._id);
      setCurrentRoute(routeData);
      setShowDirections(true);
      onShowRoute(routeData);
    } catch (error) {
      console.error('Failed to show route:', error);
    }
  };

  const highlightSafetyAsset = (asset) => {
    onHighlightItem(asset._id, 'kiosk_highlight');
    onShowLayer('safety', true);
  };

  const highlightSafetyGroup = (type, assets) => {
    assets.forEach(asset => {
      onHighlightItem(asset._id, 'kiosk_group');
    });
    onShowLayer('safety', true);
  };

  const highlightElectricalItem = (item) => {
    onHighlightItem(item._id, 'kiosk_highlight');
    onShowLayer('electrical', true);
  };

  const highlightElectricalPanel = (panel) => {
    onHighlightItem(panel._id, 'kiosk_highlight');
    onShowLayer('electrical', true);
  };

  const showTemperatureOverlay = () => {
    onShowLayer('temperature', true);
  };

  const highlightWiFiAP = (ap) => {
    onHighlightItem(ap._id, 'kiosk_highlight');
    onShowLayer('wifi', true);
  };

  const groupSafetyAssets = (assets) => {
    return assets.reduce((groups, asset) => {
      const type = asset.assetType;
      if (!groups[type]) groups[type] = [];
      groups[type].push(asset);
      return groups;
    }, {});
  };

  const printDirections = () => {
    if (currentRoute) {
      // Generate printable directions
      window.print();
    }
  };

  const resetToHome = () => {
    setSelectedService(null);
    setSearchResults([]);
    setSearchQuery('');
    setShowDirections(false);
    setCurrentRoute(null);
  };

  const toggleAccessibility = () => {
    setAccessibilityMode(!accessibilityMode);
    // Apply accessibility enhancements
    document.body.style.fontSize = accessibilityMode ? 'initial' : '1.2em';
  };

  return (
    <Box 
      sx={{ 
        height: '100vh', 
        bgcolor: 'background.default',
        overflow: 'hidden',
        fontSize: accessibilityMode ? '1.2em' : 'inherit'
      }}
    >
      {/* Kiosk Header */}
      <Paper 
        sx={{ 
          p: 2, 
          bgcolor: 'primary.main', 
          color: 'primary.contrastText',
          borderRadius: 0,
          position: 'relative'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <KioskIcon sx={{ fontSize: 40 }} />
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 700 }}>
                {getText('building_directory', 'Building Directory')}
              </Typography>
              <Typography variant="subtitle1">
                {getText('touch_to_navigate', 'Touch screen to navigate')}
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title={getText('accessibility', 'Accessibility')}>
              <IconButton 
                onClick={toggleAccessibility}
                sx={{ color: 'inherit' }}
                size="large"
              >
                <AccessibilityIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={getText('language', 'Language')}>
              <IconButton sx={{ color: 'inherit' }} size="large">
                <LanguageIcon />
              </IconButton>
            </Tooltip>
            
            <Button
              variant="outlined"
              startIcon={<ExitIcon />}
              onClick={onExit}
              sx={{ 
                borderColor: 'white', 
                color: 'white',
                '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }
              }}
            >
              {getText('exit', 'Exit')}
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Search Bar */}
      <Box sx={{ p: 2, bgcolor: 'grey.100' }}>
        <TextField
          fullWidth
          placeholder={getText('search_placeholder', 'Search for rooms, equipment, or services...')}
          value={searchQuery}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            handleSearch(e.target.value);
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            sx: { fontSize: '1.2em', height: 60 }
          }}
        />
      </Box>

      {/* Main Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {!selectedService && searchResults.length === 0 && (
          /* Service Grid */
          <Grid container spacing={3}>
            {kioskServices.map((service) => (
              <Grid item xs={12} sm={6} md={4} key={service.id}>
                <Card 
                  sx={{ 
                    height: 200,
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': { transform: 'scale(1.02)' },
                    bgcolor: service.popular ? 'primary.light' : 'background.paper'
                  }}
                  onClick={() => handleServiceSelect(service)}
                >
                  <CardContent sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%',
                    textAlign: 'center'
                  }}>
                    <Box sx={{ color: service.color, mb: 2 }}>
                      {service.icon}
                    </Box>
                    
                    <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                      {service.name}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">
                      {service.description}
                    </Typography>
                    
                    {service.popular && (
                      <Chip 
                        label={getText('popular', 'Popular')} 
                        size="small" 
                        color="primary" 
                        sx={{ mt: 1 }}
                      />
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Box>
            <Typography variant="h5" gutterBottom>
              {selectedService ? selectedService.name : getText('search_results', 'Search Results')}
            </Typography>
            
            <List>
              {searchResults.map((result) => (
                <ListItem
                  key={result.id}
                  onClick={result.action}
                  sx={{ 
                    cursor: 'pointer',
                    borderRadius: 1,
                    mb: 1,
                    bgcolor: 'background.paper',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                >
                  <ListItemIcon sx={{ fontSize: 40 }}>
                    {result.icon}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={result.title}
                    secondary={result.description}
                    primaryTypographyProps={{ variant: 'h6' }}
                  />
                </ListItem>
              ))}
            </List>
            
            <Button
              variant="outlined"
              startIcon={<InfoIcon />}
              onClick={resetToHome}
              sx={{ mt: 2 }}
            >
              {getText('back_to_home', 'Back to Home')}
            </Button>
          </Box>
        )}
      </Box>

      {/* Directions Dialog */}
      <Dialog
        open={showDirections}
        onClose={() => setShowDirections(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {getText('directions', 'Directions')}
          <Box>
            <IconButton onClick={printDirections}>
              <PrintIcon />
            </IconButton>
            <IconButton onClick={() => setShowDirections(false)}>
              <ExitIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {currentRoute && (
            <Box>
              <Typography variant="h6" gutterBottom>
                {currentRoute.name}
              </Typography>
              <Typography variant="body1">
                {currentRoute.description}
              </Typography>
              {/* Route steps would be rendered here */}
            </Box>
          )}
        </DialogContent>
      </Dialog>

      {/* Inactivity Warning */}
      <Dialog
        open={showInactivityWarning}
        onClose={() => setShowInactivityWarning(false)}
      >
        <DialogTitle>
          {getText('session_timeout', 'Session Timeout Warning')}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {getText('timeout_message', 'Your session will end soon due to inactivity. Touch anywhere to continue.')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setShowInactivityWarning(false)}
            variant="contained"
            size="large"
          >
            {getText('continue', 'Continue Session')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Emergency Access Fab */}
      <Fab
        color="error"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => {
          // Switch to emergency mode
          window.location.hash = '#emergency';
        }}
      >
        <EmergencyIcon />
      </Fab>
    </Box>
  );
};

export default KioskMode;