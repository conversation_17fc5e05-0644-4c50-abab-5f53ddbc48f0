import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  ButtonGroup,
  Button,
  Collapse,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Slider,
  FormControlLabel,
  Switch,
  Badge
} from '@mui/material';
import {
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Fullscreen as FullscreenIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  CenterFocusStrong as CenterIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  Map as MapIcon,
  Legend as LegendIcon,
  Power as ElectricalIcon,
  Thermostat as TemperatureIcon,
  Door as DoorIcon,
  Videocam as CameraIcon,
  Air as HVACIcon,
  LocalFireDepartment as SafetyIcon,
  Wifi as WiFiIcon,
  Water as UtilityIcon,
  Event as EventIcon,
  Audiotrack as AVIcon
} from '@mui/icons-material';

/**
 * BottomTray Component
 * Provides legend, mini-map, and navigation controls for the floor plan viewer
 * Supports layer legend, zoom controls, and overview navigation
 */
const BottomTray = ({
  selectedLayers = {},
  layerCounts = {},
  layerAlerts = {},
  onLayerToggle,
  onZoomChange,
  onCenterView,
  currentZoom = 1,
  floorPlanImage,
  viewport = { x: 0, y: 0, width: 800, height: 600 },
  onViewportChange,
  collapsed = true,
  onCollapse,
  height = 200
}) => {
  const miniMapRef = useRef(null);
  const [miniMapLoaded, setMiniMapLoaded] = useState(false);

  // Layer configuration with icons and colors
  const layerConfig = {
    electrical: {
      name: 'Electrical',
      icon: <ElectricalIcon />,
      color: '#ff9800',
      description: 'Outlets, panels, circuits'
    },
    doors: {
      name: 'Doors',
      icon: <DoorIcon />,
      color: '#2196f3',
      description: 'Access control, lock status'
    },
    cameras: {
      name: 'Cameras',
      icon: <CameraIcon />,
      color: '#9c27b0',
      description: 'Security cameras, FOV'
    },
    safety: {
      name: 'Safety',
      icon: <SafetyIcon />,
      color: '#f44336',
      description: 'Fire extinguishers, AEDs'
    },
    hvac: {
      name: 'HVAC',
      icon: <HVACIcon />,
      color: '#4caf50',
      description: 'Units, filters, temperature'
    },
    wifi: {
      name: 'WiFi',
      icon: <WiFiIcon />,
      color: '#00bcd4',
      description: 'Access points, coverage'
    },
    utility: {
      name: 'Utilities',
      icon: <UtilityIcon />,
      color: '#795548',
      description: 'Water, gas shutoffs'
    },
    events: {
      name: 'Events',
      icon: <EventIcon />,
      color: '#673ab7',
      description: 'Room usage, schedules'
    },
    av_equipment: {
      name: 'AV Equipment',
      icon: <AVIcon />,
      color: '#e91e63',
      description: 'Audio/visual equipment'
    }
  };

  const zoomLevels = [
    { value: 0.25, label: '25%' },
    { value: 0.5, label: '50%' },
    { value: 0.75, label: '75%' },
    { value: 1, label: '100%' },
    { value: 1.25, label: '125%' },
    { value: 1.5, label: '150%' },
    { value: 2, label: '200%' },
    { value: 3, label: '300%' }
  ];

  useEffect(() => {
    if (floorPlanImage && miniMapRef.current) {
      drawMiniMap();
    }
  }, [floorPlanImage, viewport, selectedLayers]);

  const drawMiniMap = () => {
    if (!miniMapRef.current || !floorPlanImage) return;

    const canvas = miniMapRef.current;
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Draw floor plan image scaled to fit
      const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const offsetX = (canvas.width - scaledWidth) / 2;
      const offsetY = (canvas.height - scaledHeight) / 2;
      
      ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);
      
      // Draw viewport indicator
      const viewportScale = scale;
      const viewportX = offsetX + (viewport.x * viewportScale);
      const viewportY = offsetY + (viewport.y * viewportScale);
      const viewportWidth = viewport.width * viewportScale * currentZoom;
      const viewportHeight = viewport.height * viewportScale * currentZoom;
      
      ctx.strokeStyle = '#2196f3';
      ctx.lineWidth = 2;
      ctx.setLineDash([5, 5]);
      ctx.strokeRect(viewportX, viewportY, viewportWidth, viewportHeight);
      
      // Fill viewport with semi-transparent overlay
      ctx.fillStyle = 'rgba(33, 150, 243, 0.2)';
      ctx.fillRect(viewportX, viewportY, viewportWidth, viewportHeight);
      
      setMiniMapLoaded(true);
    };
    
    img.src = floorPlanImage;
  };

  const handleMiniMapClick = (event) => {
    if (!miniMapRef.current || !onViewportChange) return;

    const canvas = miniMapRef.current;
    const rect = canvas.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickY = event.clientY - rect.top;
    
    // Convert click coordinates to floor plan coordinates
    const img = new Image();
    img.onload = () => {
      const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
      const scaledWidth = img.width * scale;
      const scaledHeight = img.height * scale;
      const offsetX = (canvas.width - scaledWidth) / 2;
      const offsetY = (canvas.height - scaledHeight) / 2;
      
      const floorX = (clickX - offsetX) / scale;
      const floorY = (clickY - offsetY) / scale;
      
      onViewportChange({ x: floorX, y: floorY });
    };
    img.src = floorPlanImage;
  };

  const getActiveLayers = () => {
    return Object.keys(selectedLayers).filter(layerId => selectedLayers[layerId]);
  };

  const getTotalItems = () => {
    return Object.values(layerCounts).reduce((sum, count) => sum + count, 0);
  };

  const getTotalAlerts = () => {
    return Object.values(layerAlerts).reduce((sum, count) => sum + count, 0);
  };

  const handleZoomIn = () => {
    const currentIndex = zoomLevels.findIndex(level => level.value === currentZoom);
    const nextIndex = Math.min(currentIndex + 1, zoomLevels.length - 1);
    onZoomChange?.(zoomLevels[nextIndex].value);
  };

  const handleZoomOut = () => {
    const currentIndex = zoomLevels.findIndex(level => level.value === currentZoom);
    const nextIndex = Math.max(currentIndex - 1, 0);
    onZoomChange?.(zoomLevels[nextIndex].value);
  };

  const handlePrint = () => {
    // Print current floor plan view
    window.print();
  };

  const handleShare = () => {
    // Share current view
    if (navigator.share) {
      navigator.share({
        title: 'Building Floor Plan',
        text: 'Check out this building floor plan view',
        url: window.location.href
      });
    } else {
      // Fallback - copy URL to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  if (collapsed) {
    return (
      <Paper 
        sx={{ 
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: 2,
          zIndex: 1000,
          borderRadius: '16px 16px 0 0'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={onCollapse} size="small">
            <ExpandLessIcon />
          </IconButton>
          
          <Typography variant="body2" color="text.secondary">
            {getActiveLayers().length} layers active • {getTotalItems()} items
          </Typography>
          
          {getTotalAlerts() > 0 && (
            <Badge badgeContent={getTotalAlerts()} color="error">
              <Chip label="Alerts" size="small" color="error" variant="outlined" />
            </Badge>
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Zoom: {Math.round(currentZoom * 100)}%
          </Typography>
          
          <ButtonGroup size="small" variant="outlined">
            <IconButton onClick={handleZoomOut} disabled={currentZoom <= zoomLevels[0].value}>
              <ZoomOutIcon />
            </IconButton>
            <IconButton onClick={onCenterView}>
              <CenterIcon />
            </IconButton>
            <IconButton onClick={handleZoomIn} disabled={currentZoom >= zoomLevels[zoomLevels.length - 1].value}>
              <ZoomInIcon />
            </IconButton>
          </ButtonGroup>
          
          <IconButton onClick={handlePrint} size="small">
            <PrintIcon />
          </IconButton>
          
          <IconButton onClick={handleShare} size="small">
            <ShareIcon />
          </IconButton>
        </Box>
      </Paper>
    );
  }

  return (
    <Paper 
      sx={{ 
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        height,
        display: 'flex',
        flexDirection: 'column',
        zIndex: 1000,
        borderRadius: '16px 16px 0 0'
      }}
    >
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        px: 2, 
        py: 1,
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton onClick={onCollapse} size="small">
            <ExpandMoreIcon />
          </IconButton>
          
          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
            Floor Plan Controls
          </Typography>
          
          <Typography variant="body2" color="text.secondary">
            {getActiveLayers().length} layers • {getTotalItems()} items
            {getTotalAlerts() > 0 && ` • ${getTotalAlerts()} alerts`}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button startIcon={<PrintIcon />} size="small" onClick={handlePrint}>
            Print
          </Button>
          <Button startIcon={<ShareIcon />} size="small" onClick={handleShare}>
            Share
          </Button>
          <IconButton onClick={() => {}}>
            <FullscreenIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Layer Legend */}
        <Box sx={{ flex: 1, p: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            <LegendIcon />
            Layer Legend
          </Typography>
          
          <Grid container spacing={1}>
            {Object.entries(layerConfig).map(([layerId, config]) => {
              const isActive = selectedLayers[layerId];
              const count = layerCounts[layerId] || 0;
              const alerts = layerAlerts[layerId] || 0;
              
              return (
                <Grid item xs={6} sm={4} md={3} key={layerId}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      p: 1,
                      borderRadius: 1,
                      border: 1,
                      borderColor: isActive ? config.color : 'divider',
                      bgcolor: isActive ? `${config.color}15` : 'transparent',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        bgcolor: `${config.color}20`
                      }
                    }}
                    onClick={() => onLayerToggle(layerId, !isActive)}
                  >
                    <Box sx={{ color: config.color }}>
                      {config.icon}
                    </Box>
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Typography variant="caption" sx={{ fontWeight: 600, display: 'block' }}>
                        {config.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                        {config.description}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                        {count > 0 && (
                          <Chip label={count} size="small" sx={{ height: 16, fontSize: '0.6rem' }} />
                        )}
                        {alerts > 0 && (
                          <Chip label={`${alerts} alerts`} size="small" color="error" sx={{ height: 16, fontSize: '0.6rem' }} />
                        )}
                      </Box>
                    </Box>
                    <Box>
                      {isActive ? <VisibilityIcon /> : <VisibilityOffIcon />}
                    </Box>
                  </Box>
                </Grid>
              );
            })}
          </Grid>
        </Box>

        {/* Navigation Controls */}
        <Box sx={{ width: 280, borderLeft: 1, borderColor: 'divider', p: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            <MapIcon />
            Navigation
          </Typography>
          
          {/* Mini Map */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="text.secondary" gutterBottom>
              Floor Plan Overview
            </Typography>
            <Box
              sx={{
                width: '100%',
                height: 80,
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                overflow: 'hidden',
                position: 'relative',
                cursor: 'pointer'
              }}
              onClick={handleMiniMapClick}
            >
              <canvas
                ref={miniMapRef}
                width={248}
                height={80}
                style={{ width: '100%', height: '100%' }}
              />
              {!miniMapLoaded && (
                <Box sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'grey.100'
                }}>
                  <Typography variant="caption" color="text.secondary">
                    Loading map...
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>

          {/* Zoom Controls */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="text.secondary" gutterBottom>
              Zoom Level: {Math.round(currentZoom * 100)}%
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton 
                size="small" 
                onClick={handleZoomOut}
                disabled={currentZoom <= zoomLevels[0].value}
              >
                <ZoomOutIcon />
              </IconButton>
              <Slider
                value={currentZoom}
                onChange={(_, value) => onZoomChange?.(value)}
                min={zoomLevels[0].value}
                max={zoomLevels[zoomLevels.length - 1].value}
                step={0.25}
                size="small"
                sx={{ flex: 1 }}
              />
              <IconButton 
                size="small" 
                onClick={handleZoomIn}
                disabled={currentZoom >= zoomLevels[zoomLevels.length - 1].value}
              >
                <ZoomInIcon />
              </IconButton>
            </Box>
            <ButtonGroup size="small" fullWidth variant="outlined" sx={{ mt: 1 }}>
              <Button onClick={() => onZoomChange?.(0.5)}>50%</Button>
              <Button onClick={() => onZoomChange?.(1)}>100%</Button>
              <Button onClick={() => onZoomChange?.(2)}>200%</Button>
              <Button onClick={onCenterView}>
                <CenterIcon />
              </Button>
            </ButtonGroup>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default BottomTray;