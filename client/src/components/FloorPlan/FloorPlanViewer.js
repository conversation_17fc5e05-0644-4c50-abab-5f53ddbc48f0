import React, { useState, useEffect, useRef } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress, 
  Paper, 
  IconButton, 
  Tooltip, 
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Badge,
  Chip,
  Menu,
  ClickAwayListener,
  ToggleButtonGroup,
  ToggleButton,
  Divider,
  Drawer
} from '@mui/material';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import RefreshIcon from '@mui/icons-material/Refresh';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import VideocamIcon from '@mui/icons-material/Videocam';
import RouterIcon from '@mui/icons-material/Router';
import WifiIcon from '@mui/icons-material/Wifi';
import AcUnitIcon from '@mui/icons-material/AcUnit';
import SecurityIcon from '@mui/icons-material/Security';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import SensorsIcon from '@mui/icons-material/Sensors';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import MeetingRoomOutlinedIcon from '@mui/icons-material/MeetingRoomOutlined';
import ElectricalServicesIcon from '@mui/icons-material/ElectricalServices';
import PowerIcon from '@mui/icons-material/Power';
import LayersIcon from '@mui/icons-material/Layers';
import SearchIcon from '@mui/icons-material/Search';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import HealingIcon from '@mui/icons-material/Healing';
import OpacityIcon from '@mui/icons-material/Opacity';
import EventIcon from '@mui/icons-material/Event';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AssignmentIcon from '@mui/icons-material/Assignment';
import AudiotrackIcon from '@mui/icons-material/Audiotrack';
import CampaignIcon from '@mui/icons-material/Campaign';
import SupportAgentIcon from '@mui/icons-material/SupportAgent';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import VolunteerActivismIcon from '@mui/icons-material/VolunteerActivism';
import ChecklistIcon from '@mui/icons-material/Checklist';
import DirectionsIcon from '@mui/icons-material/Directions';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import PhoneIcon from '@mui/icons-material/Phone';
import EmergencyIcon from '@mui/icons-material/Report';
import WarningIcon from '@mui/icons-material/Warning';
import BuildIcon from '@mui/icons-material/Build';
import buildingManagementService from '../../services/buildingManagementService';
import websocketService from '../../services/websocketService';
import electricalService from '../../services/electricalService';
import safetyService from '../../services/safetyService';
import hvacService from '../../services/hvacService';
import wifiService from '../../services/wifiService';
import utilityShutoffService from '../../services/utilityShutoffService';
import churchEventService from '../../services/churchEventService';
import avEquipmentService from '../../services/avEquipmentService';
import eventModePresetService from '../../services/eventModePresetService';
import volunteerResourceService from '../../services/volunteerResourceService';
import avChecklistService from '../../services/avChecklistService';
import wayfindingService from '../../services/wayfindingService';
import OutletTracePanel from './OutletTracePanel';
import TemperatureHeatmap from '../Climate/TemperatureHeatmap';
import HVACServiceTracker from '../HVAC/HVACServiceTracker';
import WiFiCoverageMap from '../WiFi/WiFiCoverageMap';
import WiFiManagement from '../WiFi/WiFiManagement';
import EmergencyUtilityLocator from '../UtilityShutoffs/EmergencyUtilityLocator';
import UtilityShutoffManagement from '../UtilityShutoffs/UtilityShutoffManagement';
import CameraModal from '../Camera/CameraModal';
import DoorBulkManagement from '../Door/DoorBulkManagement';
import ChurchEventCalendar from '../ChurchEvents/ChurchEventCalendar';
import EventPreparationTracker from '../ChurchEvents/EventPreparationTracker';
import EventRoomScheduler from '../ChurchEvents/EventRoomScheduler';
import AVEquipmentManager from '../AVEquipment/AVEquipmentManager';
import AVTroubleshootingGuide from '../AVEquipment/AVTroubleshootingGuide';

/**
 * FloorPlanViewer component
 * Displays a floor plan with interactive icons that show device status
 */
const FloorPlanViewer = ({ 
  editMode = false, 
  onSave = null, 
  onCancel = null,
  refreshInterval = 30000 // 30 seconds default refresh interval
}) => {
  const [buildings, setBuildings] = useState([]);
  const [floors, setFloors] = useState([]);
  const [selectedBuilding, setSelectedBuilding] = useState('');
  const [selectedFloor, setSelectedFloor] = useState('');
  const [floorPlanUrl, setFloorPlanUrl] = useState('');
  const [icons, setIcons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [selectedIcon, setSelectedIcon] = useState(null);
  const [iconDetailsOpen, setIconDetailsOpen] = useState(false);
  const [doors, setDoors] = useState([]);
  const [climateDevices, setClimateDevices] = useState([]);
  const [cameras, setCameras] = useState([]);
  const [networkDevices, setNetworkDevices] = useState([]);
  const [isEditing, setIsEditing] = useState(editMode);
  const [newIcon, setNewIcon] = useState(null);
  const [iconToAdd, setIconToAdd] = useState(null);
  const [integrationDialogOpen, setIntegrationDialogOpen] = useState(false);
  const [availableDevices, setAvailableDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState('');
  const [doorStatusUpdates, setDoorStatusUpdates] = useState(0);
  const [contextMenu, setContextMenu] = useState(null);
  const [contextMenuIcon, setContextMenuIcon] = useState(null);
  
  // Layer toggles and electrical features
  const [layerToggles, setLayerToggles] = useState({
    temperature: true,
    doors: true,
    cameras: true,
    network: true,
    electrical: true,
    hvac: true,
    safety: true,
    wifi: true,
    utility: true,
    events: true,
    av_equipment: true
  });
  const [layerPanelOpen, setLayerPanelOpen] = useState(false);
  const [traceMode, setTraceMode] = useState(null); // 'outlet' when tracing
  const [tracePanelOpen, setTracePanelOpen] = useState(false);
  const [tracePath, setTracePath] = useState(null);
  const [tracedIconIds, setTracedIconIds] = useState(new Set());
  const [electricalOutlets, setElectricalOutlets] = useState([]);
  const [electricalPanels, setElectricalPanels] = useState([]);
  const [safetyAssets, setSafetyAssets] = useState([]);
  
  // Phase 3 - Camera and Door enhancements
  const [cameraModalOpen, setCameraModalOpen] = useState(false);
  const [selectedCamera, setSelectedCamera] = useState(null);
  const [doorBulkManagementOpen, setDoorBulkManagementOpen] = useState(false);
  const [cameraThumbnails, setCameraThumbnails] = useState(new Map());
  
  // Phase 4 - Climate heatmap and HVAC service tracking
  const [temperatureHeatmapOpen, setTemperatureHeatmapOpen] = useState(false);
  const [hvacServiceTrackerOpen, setHvacServiceTrackerOpen] = useState(false);
  const [hvacUnits, setHvacUnits] = useState([]);
  const [temperatureDataOverlay, setTemperatureDataOverlay] = useState(false);
  
  // Phase 5 - WiFi coverage and access points
  const [wifiCoverageMapOpen, setWifiCoverageMapOpen] = useState(false);
  const [wifiManagementOpen, setWifiManagementOpen] = useState(false);
  const [wifiAccessPoints, setWifiAccessPoints] = useState([]);
  const [wifiCoverageAreas, setWifiCoverageAreas] = useState([]);
  
  // Phase 6 - Water & Gas Shutoffs
  const [emergencyUtilityLocatorOpen, setEmergencyUtilityLocatorOpen] = useState(false);
  const [utilityShutoffManagementOpen, setUtilityShutoffManagementOpen] = useState(false);
  const [utilityShutoffs, setUtilityShutoffs] = useState([]);
  const [emergencyMode, setEmergencyMode] = useState(false);
  
  // Phase 7 - Church Event Management & Room Usage
  const [churchEventCalendarOpen, setChurchEventCalendarOpen] = useState(false);
  const [eventPreparationTrackerOpen, setEventPreparationTrackerOpen] = useState(false);
  const [eventRoomSchedulerOpen, setEventRoomSchedulerOpen] = useState(false);
  const [churchEvents, setChurchEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [eventOverlayMode, setEventOverlayMode] = useState(false);
  
  // Phase 8 - AV Equipment Management & Technical Support
  const [avEquipmentManagerOpen, setAvEquipmentManagerOpen] = useState(false);
  const [avTroubleshootingGuideOpen, setAvTroubleshootingGuideOpen] = useState(false);
  const [avEquipment, setAvEquipment] = useState([]);
  const [selectedAVEquipment, setSelectedAVEquipment] = useState(null);
  const [avSupportMode, setAvSupportMode] = useState(false);
  
  // Phase 9 - Church-Specific Conveniences
  const [eventModePresetsOpen, setEventModePresetsOpen] = useState(false);
  const [volunteerResourcesOpen, setVolunteerResourcesOpen] = useState(false);
  const [avChecklistsOpen, setAvChecklistsOpen] = useState(false);
  const [wayfindingGuidesOpen, setWayfindingGuidesOpen] = useState(false);
  const [eventModePresets, setEventModePresets] = useState([]);
  const [volunteerResources, setVolunteerResources] = useState([]);
  const [avChecklists, setAvChecklists] = useState([]);
  const [wayfindingRoutes, setWayfindingRoutes] = useState([]);
  const [currentEventMode, setCurrentEventMode] = useState(null);
  const [kioskMode, setKioskMode] = useState(false);
  const [sanctuaryChecklistActive, setSanctuaryChecklistActive] = useState(false);
  
  const containerRef = useRef(null);
  const floorPlanRef = useRef(null);
  const refreshTimerRef = useRef(null);

  // Handle door status updates from WebSocket
  const handleDoorStatusUpdate = (doorStatuses) => {
    if (!doorStatuses || !Array.isArray(doorStatuses) || doorStatuses.length === 0) return;
    
    console.log('Received door status update:', doorStatuses);
    
    // Increment the update counter to trigger a re-render
    setDoorStatusUpdates(prev => prev + 1);
    
    // Update the icons with the new door status
    setIcons(prevIcons => {
      // Create a copy of the icons array
      const updatedIcons = [...prevIcons];
      
      // Update door icons with matching deviceId
      doorStatuses.forEach(doorStatus => {
        const doorIndex = updatedIcons.findIndex(icon => 
          icon.type === 'door' && 
          icon.deviceId === doorStatus.doorId
        );
        
        if (doorIndex !== -1) {
          // Update the icon data with the new status
          updatedIcons[doorIndex] = {
            ...updatedIcons[doorIndex],
            data: {
              value: doorStatus.status || doorStatus.state || 'unknown',
              lastUpdated: new Date().toISOString()
            },
            status: getDoorStatusCategory(doorStatus)
          };
        }
      });
      
      return updatedIcons;
    });
  };
  
  // Determine door status category (normal, warning, alert, inactive)
  const getDoorStatusCategory = (doorStatus) => {
    const status = doorStatus.status || doorStatus.state || '';
    
    if (status.toLowerCase().includes('alarm') || status.toLowerCase().includes('error')) {
      return 'alert';
    } else if (status.toLowerCase().includes('unlocked') || status.toLowerCase().includes('open')) {
      return 'warning';
    } else if (status.toLowerCase().includes('locked') || status.toLowerCase().includes('closed')) {
      return 'normal';
    } else {
      return 'inactive';
    }
  };

  // Set up WebSocket connection for real-time door status updates
  useEffect(() => {
    // Connect to WebSocket server
    websocketService.connect().then(() => {
      console.log('WebSocket connected for door status updates');
      
      // Subscribe to door status events
      websocketService.subscribe('door-status');
      
      // Add event listener for door status updates
      websocketService.addEventListener('door-status', handleDoorStatusUpdate);
    }).catch(error => {
      console.warn('WebSocket connection failed, falling back to polling:', error);
    });
    
    // Cleanup function
    return () => {
      // Remove event listener
      websocketService.removeEventListener('door-status', handleDoorStatusUpdate);
      
      // Clear refresh timer
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []);
  
  // Load buildings on component mount
  useEffect(() => {
    const fetchBuildings = async () => {
      try {
        setLoading(true);
        const data = await buildingManagementService.getBuildings();
        setBuildings(data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching buildings:', err);
        setError('Failed to load buildings. Please try again later.');
        setLoading(false);
      }
    };

    fetchBuildings();

    // Cleanup function
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, []);

  // Load floors when a building is selected
  useEffect(() => {
    const fetchFloors = async () => {
      if (!selectedBuilding) {
        setFloors([]);
        setSelectedFloor('');
        return;
      }

      try {
        setLoading(true);
        const data = await buildingManagementService.getBuildingFloors(selectedBuilding);
        setFloors(data);
        setLoading(false);
        
        // Auto-select the first floor if available
        if (data.length > 0 && !selectedFloor) {
          setSelectedFloor(data[0]._id);
        } else {
          setSelectedFloor('');
        }
      } catch (err) {
        console.error('Error fetching floors:', err);
        setError('Failed to load floors. Please try again later.');
        setLoading(false);
      }
    };

    fetchFloors();
  }, [selectedBuilding]);

  // Load floor plan and icons when a floor is selected
  useEffect(() => {
    const fetchFloorPlanAndIcons = async () => {
      if (!selectedFloor) {
        setFloorPlanUrl('');
        setIcons([]);
        return;
      }

      try {
        setLoading(true);
        
        // Get floor plan URL
        const floorPlanUrl = buildingManagementService.getFloorplanUrl(selectedFloor);
        setFloorPlanUrl(floorPlanUrl);
        
        // Get floor icons
        const iconsData = await buildingManagementService.getFloorIcons(selectedFloor);
        setIcons(iconsData);
        
        setLoading(false);
        
        // Start the refresh timer
        startRefreshTimer();
      } catch (err) {
        console.error('Error fetching floor plan and icons:', err);
        setError('Failed to load floor plan. Please try again later.');
        setLoading(false);
      }
    };

    fetchFloorPlanAndIcons();
    
    // Fetch device data for integration selection
    const fetchDeviceData = async () => {
      try {
        const [doorsData, climateData, camerasData, networkData] = await Promise.all([
          buildingManagementService.getDoors(),
          buildingManagementService.getClimateDevices(),
          buildingManagementService.getCameras(),
          buildingManagementService.getNetworkDevices()
        ]);
        
        setDoors(doorsData);
        setClimateDevices(climateData);
        setCameras(camerasData);
        setNetworkDevices(networkData);
      } catch (err) {
        console.error('Error fetching device data:', err);
      }
    };
    
    // Fetch electrical data for the selected floor
    const fetchElectricalData = async () => {
      if (!selectedFloor) return;
      
      try {
        const [outletsData, panelsData] = await Promise.all([
          electricalService.getOutlets({ floorId: selectedFloor }),
          electricalService.getPanels({ floorId: selectedFloor })
        ]);
        
        setElectricalOutlets(outletsData);
        setElectricalPanels(panelsData);
      } catch (err) {
        console.error('Error fetching electrical data:', err);
      }
    };

    const fetchSafetyData = async () => {
      if (!selectedFloor) return;
      
      try {
        const safetyData = await safetyService.getAssetsByLocation(selectedBuilding, selectedFloor);
        setSafetyAssets(safetyData);
      } catch (err) {
        console.error('Error fetching safety data:', err);
        setSafetyAssets([]); // Set empty array on error
      }
    };

    // Fetch camera data and preload thumbnails for the selected floor
    const fetchCameraData = async () => {
      if (!selectedFloor) return;
      
      try {
        const allCameras = await buildingManagementService.getCameras();
        
        // Filter cameras for this floor (if they have floor association)
        const floorCameras = allCameras.filter(camera => 
          camera.floorId === selectedFloor || camera.metadata?.floorId === selectedFloor
        );
        
        // Preload thumbnails for camera icons on this floor
        const thumbnailMap = new Map();
        for (const camera of floorCameras) {
          try {
            // Only preload a few thumbnails to avoid overwhelming the API
            if (thumbnailMap.size < 5) {
              const thumbnailBlob = await fetch(camera.snapshotUrl || `/api/unifi-protect/cameras/${camera.id}/snapshot`);
              if (thumbnailBlob.ok) {
                const blob = await thumbnailBlob.blob();
                const imageUrl = URL.createObjectURL(blob);
                thumbnailMap.set(camera.id, imageUrl);
              }
            }
          } catch (err) {
            console.log(`Failed to preload thumbnail for camera ${camera.id}:`, err.message);
          }
        }
        
        setCameraThumbnails(thumbnailMap);
      } catch (err) {
        console.error('Error fetching camera data:', err);
      }
    };

    // Fetch HVAC data for the selected floor
    const fetchHvacData = async () => {
      if (!selectedFloor) return;
      
      try {
        const hvacData = await hvacService.getUnitsByLocation(selectedBuilding, selectedFloor);
        setHvacUnits(hvacData);
      } catch (err) {
        console.error('Error fetching HVAC data:', err);
        setHvacUnits([]); // Set empty array on error
      }
    };

    // Fetch WiFi data for the selected floor
    const fetchWifiData = async () => {
      if (!selectedFloor) return;
      
      try {
        const [accessPointsData, coverageData] = await Promise.all([
          wifiService.getAccessPointsByLocation(selectedBuilding, selectedFloor, { includeOffline: true }),
          wifiService.getCoverageAreasByLocation(selectedBuilding, selectedFloor)
        ]);
        setWifiAccessPoints(accessPointsData);
        setWifiCoverageAreas(coverageData);
      } catch (err) {
        console.error('Error fetching WiFi data:', err);
        setWifiAccessPoints([]);
        setWifiCoverageAreas([]);
      }
    };
    
    // Fetch utility shutoff data for the selected floor
    const fetchUtilityShutoffData = async () => {
      if (!selectedFloor) return;
      
      try {
        const shutoffData = await utilityShutoffService.getShutoffsByLocation(
          selectedBuilding, 
          selectedFloor, 
          true // includePositioned
        );
        setUtilityShutoffs(shutoffData);
      } catch (err) {
        console.error('Error fetching utility shutoff data:', err);
        setUtilityShutoffs([]);
      }
    };
    
    // Phase 7 - Fetch church events data for the selected floor
    const fetchChurchEventsData = async () => {
      if (!selectedFloor || !selectedBuilding) return;
      
      try {
        const eventsData = await churchEventService.getEventsByLocation(
          selectedBuilding, 
          selectedFloor, 
          {
            startDate: new Date().toISOString(),
            endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // Next 7 days
            includePreparation: true
          }
        );
        setChurchEvents(eventsData);
      } catch (err) {
        console.error('Error fetching church events data:', err);
        setChurchEvents([]);
      }
    };
    
    // Phase 8 - Fetch AV equipment data for the selected floor
    const fetchAVEquipmentData = async () => {
      if (!selectedFloor || !selectedBuilding) return;
      
      try {
        const equipmentData = await avEquipmentService.getEquipmentByLocation(
          selectedBuilding, 
          selectedFloor, 
          {
            includePortable: true,
            status: 'active'
          }
        );
        setAvEquipment(equipmentData);
      } catch (err) {
        console.error('Error fetching AV equipment data:', err);
        setAvEquipment([]);
      }
    };
    
    // Phase 9 - Fetch church convenience data
    const fetchEventModePresetsData = async () => {
      try {
        const presetsData = await eventModePresetService.getPresets({
          sortBy: 'popularity',
          sortOrder: 'desc',
          limit: 20
        });
        setEventModePresets(presetsData.presets || []);
      } catch (err) {
        console.error('Error fetching event mode presets:', err);
        setEventModePresets([]);
      }
    };
    
    const fetchVolunteerResourcesData = async () => {
      if (!selectedFloor || !selectedBuilding) return;
      
      try {
        const resourcesData = await volunteerResourceService.getAvailableForFloor(selectedFloor);
        setVolunteerResources(resourcesData.resources || []);
      } catch (err) {
        console.error('Error fetching volunteer resources:', err);
        setVolunteerResources([]);
      }
    };
    
    const fetchAVChecklistsData = async () => {
      try {
        const checklistsData = await avChecklistService.getChecklists({
          checklistType: 'pre_service',
          sortBy: 'name',
          limit: 10
        });
        setAvChecklists(checklistsData.checklists || []);
      } catch (err) {
        console.error('Error fetching AV checklists:', err);
        setAvChecklists([]);
      }
    };
    
    const fetchWayfindingRoutesData = async () => {
      if (!selectedBuilding) return;
      
      try {
        const routesData = await wayfindingService.getRoutes({
          buildingId: selectedBuilding,
          routeType: 'volunteer_guide',
          priority: 'high',
          limit: 15
        });
        setWayfindingRoutes(routesData.routes || []);
      } catch (err) {
        console.error('Error fetching wayfinding routes:', err);
        setWayfindingRoutes([]);
      }
    };
    
    fetchDeviceData();
    fetchElectricalData();
    fetchSafetyData();
    fetchCameraData();
    fetchHvacData();
    fetchWifiData();
    fetchUtilityShutoffData();
    fetchChurchEventsData();
    fetchAVEquipmentData();
    fetchEventModePresetsData();
    fetchVolunteerResourcesData();
    fetchAVChecklistsData();
    fetchWayfindingRoutesData();
    
    // Cleanup function
    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
      }
    };
  }, [selectedFloor]);

  // Start the refresh timer
  const startRefreshTimer = () => {
    // Clear any existing timer
    if (refreshTimerRef.current) {
      clearInterval(refreshTimerRef.current);
    }
    
    // Set up a new timer
    refreshTimerRef.current = setInterval(() => {
      refreshIconData();
    }, refreshInterval);
  };

  // Refresh icon data
  const refreshIconData = async () => {
    if (!selectedFloor) return;
    
    try {
      const iconsData = await buildingManagementService.getFloorIcons(selectedFloor);
      setIcons(iconsData);
    } catch (err) {
      console.error('Error refreshing icon data:', err);
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    refreshIconData();
    
    // Reset the refresh timer
    startRefreshTimer();
  };

  // Handle building selection
  const handleBuildingChange = (event) => {
    setSelectedBuilding(event.target.value);
  };

  // Handle floor selection
  const handleFloorChange = (event) => {
    setSelectedFloor(event.target.value);
  };

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.1, 2));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.1, 0.5));
  };

  // Handle icon click
  const handleIconClick = (icon, event) => {
    // Close any open context menu
    setContextMenu(null);
    
    if (isEditing) {
      // In edit mode, select the icon for editing
      setSelectedIcon(icon);
    } else {
      // In view mode
      if (event && event.type === 'contextmenu' && icon.type === 'door') {
        // Prevent the default context menu
        event.preventDefault();
        
        // Show our custom context menu for doors
        setContextMenuIcon(icon);
        setContextMenu(
          event.currentTarget ? {
            mouseX: event.clientX,
            mouseY: event.clientY,
          } : null,
        );
      } else {
        // Regular click - handle different icon types
        if (icon.type === 'camera') {
          // Camera click - open camera modal for thumbnail/live view
          setSelectedCamera(icon);
          setCameraModalOpen(true);
        } else {
          // Regular click - show icon details
          setSelectedIcon(icon);
          setIconDetailsOpen(true);
        }
      }
    }
  };
  
  // Handle context menu close
  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  // Handle floor plan click in edit mode
  const handleFloorPlanClick = (event) => {
    if (!isEditing || !floorPlanRef.current || iconToAdd === null) return;
    
    // Get click coordinates relative to the floor plan
    const rect = floorPlanRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / zoom;
    const y = (event.clientY - rect.top) / zoom;
    
    // Create a new icon at the clicked position
    const newIconData = {
      type: iconToAdd,
      position: { x, y },
      name: `New ${iconToAdd}`,
      floorId: selectedFloor
    };
    
    setNewIcon(newIconData);
    
    // Show integration selection dialog
    setIntegrationDialogOpen(true);
    
    // Set available devices based on icon type
    switch (iconToAdd) {
      case 'door':
        setAvailableDevices(doors);
        break;
      case 'temperature':
      case 'hvac':
        setAvailableDevices(climateDevices);
        break;
      case 'camera':
        setAvailableDevices(cameras);
        break;
      case 'network':
        setAvailableDevices(networkDevices);
        break;
      default:
        setAvailableDevices([]);
    }
  };

  // Handle adding a new icon
  const handleAddIcon = async () => {
    if (!newIcon) return;
    
    try {
      // Add device ID and integration source to the new icon
      const iconData = {
        ...newIcon,
        deviceId: selectedDevice,
        integrationSource: getIntegrationSource(newIcon.type, selectedDevice)
      };
      
      // Create the icon
      const createdIcon = await buildingManagementService.createFloorplanIcon(iconData);
      
      // Add the new icon to the list
      setIcons(prevIcons => [...prevIcons, createdIcon]);
      
      // Reset state
      setNewIcon(null);
      setIconToAdd(null);
      setSelectedDevice('');
      setIntegrationDialogOpen(false);
    } catch (err) {
      console.error('Error creating icon:', err);
      setError('Failed to create icon. Please try again.');
    }
  };

  // Get integration source based on icon type and device ID
  const getIntegrationSource = (type, deviceId) => {
    // This is a simplified implementation
    // In a real application, you would determine the integration source based on the device ID
    switch (type) {
      case 'door':
        return 'lenelS2NetBox';
      case 'temperature':
      case 'hvac':
        return 'dreo';
      case 'camera':
        return 'unifiProtect';
      case 'network':
        return 'unifiNetwork';
      default:
        return '';
    }
  };

  // Handle icon deletion
  const handleDeleteIcon = async () => {
    if (!selectedIcon) return;
    
    try {
      await buildingManagementService.deleteFloorplanIcon(selectedIcon._id);
      
      // Remove the icon from the list
      setIcons(prevIcons => prevIcons.filter(icon => icon._id !== selectedIcon._id));
      
      // Reset state
      setSelectedIcon(null);
      setIconDetailsOpen(false);
    } catch (err) {
      console.error('Error deleting icon:', err);
      setError('Failed to delete icon. Please try again.');
    }
  };

  // Handle door unlock
  const handleDoorUnlock = async (doorIcon = null) => {
    // Use the provided doorIcon, or contextMenuIcon, or selectedIcon
    const icon = doorIcon || contextMenuIcon || selectedIcon;
    
    if (!icon || icon.type !== 'door') return;
    
    try {
      // Close context menu if it was used
      if (contextMenu) {
        handleContextMenuClose();
      }
      
      // Call the API to unlock the door
      const response = await buildingManagementService.unlockDoor(
        icon.deviceId,
        icon.integrationSource
      );
      
      // Update the icon data with the new status
      const updatedData = {
        ...icon.data,
        value: 'unlocked',
        lastUpdated: new Date().toISOString()
      };
      
      await buildingManagementService.updateFloorplanIconData(icon._id, updatedData);
      
      // Update the icon in the list
      setIcons(prevIcons => prevIcons.map(i => 
        i._id === icon._id 
          ? { ...i, data: updatedData, status: 'warning' } 
          : i
      ));
      
      // Update the selected icon if it's the same as the one we just updated
      if (selectedIcon && selectedIcon._id === icon._id) {
        setSelectedIcon({ 
          ...selectedIcon, 
          data: updatedData,
          status: 'warning'
        });
      }
      
      console.log('Door unlocked successfully:', response);
    } catch (err) {
      console.error('Error unlocking door:', err);
      setError('Failed to unlock door. Please try again.');
    }
  };
  
  // Handle door lock
  const handleDoorLock = async (doorIcon = null) => {
    // Use the provided doorIcon, or contextMenuIcon, or selectedIcon
    const icon = doorIcon || contextMenuIcon || selectedIcon;
    
    if (!icon || icon.type !== 'door') return;
    
    try {
      // Close context menu if it was used
      if (contextMenu) {
        handleContextMenuClose();
      }
      
      // Call the API to lock the door
      const response = await buildingManagementService.lockDoor(
        icon.deviceId,
        icon.integrationSource
      );
      
      // Update the icon data with the new status
      const updatedData = {
        ...icon.data,
        value: 'locked',
        lastUpdated: new Date().toISOString()
      };
      
      await buildingManagementService.updateFloorplanIconData(icon._id, updatedData);
      
      // Update the icon in the list
      setIcons(prevIcons => prevIcons.map(i => 
        i._id === icon._id 
          ? { ...i, data: updatedData, status: 'normal' } 
          : i
      ));
      
      // Update the selected icon if it's the same as the one we just updated
      if (selectedIcon && selectedIcon._id === icon._id) {
        setSelectedIcon({ 
          ...selectedIcon, 
          data: updatedData,
          status: 'normal'
        });
      }
      
      console.log('Door locked successfully:', response);
    } catch (err) {
      console.error('Error locking door:', err);
      setError('Failed to lock door. Please try again.');
    }
  };
  
  // Handle door passage mode
  const handleDoorPassageMode = async (doorIcon = null) => {
    // Use the provided doorIcon, or contextMenuIcon, or selectedIcon
    const icon = doorIcon || contextMenuIcon || selectedIcon;
    
    if (!icon || icon.type !== 'door') return;
    
    try {
      // Close context menu if it was used
      if (contextMenu) {
        handleContextMenuClose();
      }
      
      // Call the API to set the door to passage mode
      const response = await buildingManagementService.setDoorPassageMode(
        icon.deviceId,
        icon.integrationSource
      );
      
      // Update the icon data with the new status
      const updatedData = {
        ...icon.data,
        value: 'passage',
        lastUpdated: new Date().toISOString()
      };
      
      await buildingManagementService.updateFloorplanIconData(icon._id, updatedData);
      
      // Update the icon in the list
      setIcons(prevIcons => prevIcons.map(i => 
        i._id === icon._id 
          ? { ...i, data: updatedData, status: 'warning' } 
          : i
      ));
      
      // Update the selected icon if it's the same as the one we just updated
      if (selectedIcon && selectedIcon._id === icon._id) {
        setSelectedIcon({ 
          ...selectedIcon, 
          data: updatedData,
          status: 'warning'
        });
      }
      
      console.log('Door set to passage mode successfully:', response);
    } catch (err) {
      console.error('Error setting door to passage mode:', err);
      setError('Failed to set door to passage mode. Please try again.');
    }
  };

  // Get icon component based on type
  const getIconComponent = (type, customType = null) => {
    switch (type) {
      case 'temperature':
        return <ThermostatIcon />;
      case 'door':
        return <MeetingRoomIcon />;
      case 'camera':
        return <VideocamIcon />;
      case 'network':
        return <RouterIcon />;
      case 'wifi':
        return <WifiIcon />;
      case 'hvac':
        return <AcUnitIcon />;
      case 'security':
        return <SecurityIcon />;
      case 'light':
        return <LightbulbIcon />;
      case 'motion':
        return <SensorsIcon />;
      case 'power':
        if (customType === 'panel') {
          return <ElectricalServicesIcon />;
        }
        return <PowerIcon />;
      case 'fire':
        if (customType === 'fire_extinguisher') {
          return <LocalFireDepartmentIcon />;
        }
        return <SecurityIcon />;
      case 'medical':
        if (customType === 'aed') {
          return <LocalHospitalIcon />;
        }
        if (customType === 'first_aid_kit') {
          return <HealingIcon />;
        }
        return <LocalHospitalIcon />;
      case 'water':
        if (customType === 'eyewash_station' || customType === 'emergency_shower') {
          return <OpacityIcon />;
        }
        return <SecurityIcon />;
      default:
        return <SensorsIcon />;
    }
  };

  // Get icon color based on status
  const getIconColor = (icon) => {
    if (!icon.data || !icon.data.value) return 'inherit';
    
    switch (icon.status) {
      case 'alert':
        return 'error.main';
      case 'warning':
        return 'warning.main';
      case 'normal':
        return 'success.main';
      case 'inactive':
        return 'text.disabled';
      default:
        return 'inherit';
    }
  };

  // Get icon tooltip text
  const getIconTooltip = (icon) => {
    let tooltipText = icon.name;
    
    if (icon.data && icon.data.value) {
      tooltipText += `: ${icon.data.value}`;
      if (icon.data.unit) {
        tooltipText += ` ${icon.data.unit}`;
      }
    }
    
    return tooltipText;
  };

  // Handle layer toggle changes
  const handleLayerToggle = (layer) => {
    setLayerToggles(prev => ({
      ...prev,
      [layer]: !prev[layer]
    }));
  };

  // Convert safety assets to icon-like objects for rendering
  const getSafetyAssetIcons = () => {
    if (!layerToggles.safety) return [];
    
    return safetyAssets
      .filter(asset => asset.iconId || (asset.x !== undefined && asset.y !== undefined))
      .map(asset => ({
        _id: `safety-${asset._id}`,
        assetId: asset._id,
        type: asset.assetType === 'fire_extinguisher' ? 'fire' : 
              asset.assetType === 'aed' || asset.assetType === 'first_aid_kit' ? 'medical' :
              asset.assetType === 'eyewash_station' || asset.assetType === 'emergency_shower' ? 'water' : 'fire',
        customType: asset.assetType,
        x: asset.x || (asset.iconId ? undefined : 50), // Default position if no coordinates
        y: asset.y || (asset.iconId ? undefined : 50),
        name: asset.name,
        data: {
          value: asset.inspection?.status || 'unknown',
          lastUpdated: asset.inspection?.lastInspection || asset.updatedAt
        },
        status: asset.inspection?.status === 'current' ? 'normal' :
                asset.inspection?.status === 'due' ? 'warning' :
                asset.inspection?.status === 'overdue' ? 'alert' : 'inactive',
        metadata: {
          assetId: asset.assetId,
          room: asset.room,
          specifications: asset.specifications,
          inspection: asset.inspection,
          isSafetyAsset: true
        }
      }));
  };

  // Convert HVAC units to icon-like objects for rendering
  const getHvacUnitIcons = () => {
    if (!layerToggles.hvac) return [];
    
    return hvacUnits
      .filter(unit => unit.iconId || (unit.x !== undefined && unit.y !== undefined))
      .map(unit => {
        // Determine status based on filter conditions and maintenance
        let status = 'normal';
        if (unit.status === 'needs_repair' || unit.status === 'out_of_service') {
          status = 'alert';
        } else if (unit.status === 'maintenance') {
          status = 'warning';
        } else if (unit.filters?.some(filter => hvacService.isFilterOverdue(filter))) {
          status = 'alert';
        } else if (unit.filters?.some(filter => hvacService.isFilterDue(filter))) {
          status = 'warning';
        }

        return {
          _id: `hvac-${unit._id}`,
          unitId: unit._id,
          type: 'hvac',
          customType: unit.unitType,
          position: { 
            x: unit.x || 50, 
            y: unit.y || 50,
            rotation: unit.rotation || 0
          },
          name: unit.name,
          data: {
            value: unit.status,
            unit: unit.unitType,
            lastUpdated: unit.updatedAt
          },
          status,
          metadata: {
            unitId: unit.unitId,
            room: unit.location?.room,
            filters: unit.filters,
            serviceHistory: unit.serviceHistory,
            isHvacUnit: true
          }
        };
      });
  };

  // Convert WiFi access points to icon-like objects for rendering
  const getWifiAccessPointIcons = () => {
    if (!layerToggles.wifi) return [];
    
    return wifiAccessPoints
      .filter(ap => ap.position && (ap.position.x !== undefined && ap.position.y !== undefined))
      .map(ap => {
        // Determine status based on health and connection state
        let status = 'normal';
        if (ap.status?.state !== 'connected') {
          status = 'inactive';
        } else {
          const health = ap.healthScore || 75;
          if (health < 40) status = 'alert';
          else if (health < 75) status = 'warning';
        }

        return {
          _id: `wifi-${ap._id}`,
          apId: ap._id,
          type: 'network',
          customType: 'wifi_ap',
          position: { 
            x: ap.position.x || 50, 
            y: ap.position.y || 50,
            rotation: ap.position.rotation || 0
          },
          name: ap.deviceInfo?.name || `AP-${ap.deviceId?.slice(-6)}`,
          data: {
            value: ap.status?.state || 'unknown',
            unit: `${ap.wireless?.clientStats?.total || 0} clients`,
            lastUpdated: ap.deviceInfo?.lastSeen || ap.updatedAt
          },
          status,
          metadata: {
            deviceId: ap.deviceId,
            room: ap.room,
            healthScore: ap.healthScore,
            clientCount: ap.wireless?.clientStats?.total || 0,
            signalBands: ap.wireless?.radios?.filter(r => r.enabled).map(r => r.band) || [],
            isWifiAP: true
          }
        };
      });
  };

  // Convert utility shutoffs to icon-like objects for rendering
  const getUtilityShutoffIcons = () => {
    if (!layerToggles.utility) return [];
    
    return utilityShutoffs
      .filter(shutoff => shutoff.position && (shutoff.position.x !== undefined && shutoff.position.y !== undefined))
      .map(shutoff => {
        // Determine status based on condition, state, and inspection status
        let status = 'normal';
        
        if (shutoff.isInspectionOverdue) {
          status = 'alert';
        } else if (shutoff.status?.condition === 'poor' || shutoff.status?.condition === 'needs_replacement') {
          status = 'alert';
        } else if (shutoff.status?.currentState === 'unknown' || !shutoff.status?.isAccessible) {
          status = 'warning';
        } else if (shutoff.status?.currentState === 'maintenance') {
          status = 'warning';
        }

        // Determine icon type based on shutoff type
        let iconType = 'water';
        if (shutoff.shutoffType?.startsWith('gas')) {
          iconType = 'fire';
        } else if (shutoff.shutoffType?.startsWith('steam')) {
          iconType = 'power';
        } else if (shutoff.shutoffType?.startsWith('compressed_air')) {
          iconType = 'network';
        }

        const formatted = utilityShutoffService.formatShutoffDisplay(shutoff);

        return {
          _id: `utility-${shutoff._id}`,
          shutoffId: shutoff._id,
          type: iconType,
          customType: shutoff.shutoffType,
          position: { 
            x: shutoff.position.x || 50, 
            y: shutoff.position.y || 50,
            rotation: shutoff.position.rotation || 0
          },
          name: shutoff.name,
          data: {
            value: shutoff.status?.currentState || 'unknown',
            unit: shutoff.coverage?.priority || 'medium',
            lastUpdated: shutoff.status?.lastChecked || shutoff.updatedAt
          },
          status,
          metadata: {
            shutoffId: shutoff.shutoffId,
            utilityType: formatted.utilityType,
            priority: shutoff.coverage?.priority || 'medium',
            condition: shutoff.status?.condition || 'good',
            isEmergency: formatted.isEmergency,
            isCritical: formatted.isCritical,
            affectedAreas: shutoff.coverage?.affectedAreas?.join(', ') || '',
            emergencyContact: shutoff.procedures?.emergencyContact || '',
            isUtilityShutoff: true
          }
        };
      });
  };

  // Filter icons based on active layers
  const getFilteredIcons = () => {
    const floorplanIcons = icons.filter(icon => {
      switch (icon.type) {
        case 'temperature':
          return layerToggles.temperature;
        case 'door':
          return layerToggles.doors;
        case 'camera':
          return layerToggles.cameras;
        case 'network':
        case 'wifi':
          return layerToggles.network;
        case 'power':
          return layerToggles.electrical;
        case 'hvac':
          return layerToggles.hvac;
        case 'fire':
        case 'water':
        case 'medical':
          return layerToggles.safety;
        default:
          return true;
      }
    });
    
    // Combine floorplan icons with safety asset icons, HVAC unit icons, WiFi AP icons, and utility shutoff icons
    const safetyAssetIcons = getSafetyAssetIcons();
    const hvacUnitIcons = getHvacUnitIcons();
    const wifiApIcons = getWifiAccessPointIcons();
    const utilityShutoffIcons = getUtilityShutoffIcons();
    return [...floorplanIcons, ...safetyAssetIcons, ...hvacUnitIcons, ...wifiApIcons, ...utilityShutoffIcons];
  };

  // Handle electrical trace
  const handleElectricalTrace = (traceResult) => {
    if (traceResult) {
      setTracePath(traceResult.path);
      setTraceMode('outlet');
      
      // Focus on the outlet and panel icons if they exist
      const filteredIcons = getFilteredIcons();
      if (traceResult.outlet?.iconId) {
        const outletIcon = filteredIcons.find(icon => icon._id === traceResult.outlet.iconId);
        if (outletIcon) {
          // Could scroll to icon or highlight it
          console.log('Found outlet icon:', outletIcon);
        }
      }
    }
  };

  // Clear electrical trace
  const handleClearTrace = () => {
    setTracePath(null);
    setTraceMode(null);
  };

  // Get icon count by layer for badges
  const getLayerIconCounts = () => {
    const counts = {
      temperature: 0,
      doors: 0,
      cameras: 0,
      network: 0,
      electrical: 0,
      hvac: 0,
      safety: 0,
      wifi: 0,
      utility: 0,
      events: 0,
      av_equipment: 0
    };

    // Count FloorplanIcons
    icons.forEach(icon => {
      switch (icon.type) {
        case 'temperature':
          counts.temperature++;
          break;
        case 'door':
          counts.doors++;
          break;
        case 'camera':
          counts.cameras++;
          break;
        case 'network':
        case 'wifi':
          counts.network++;
          break;
        case 'power':
          counts.electrical++;
          break;
        case 'hvac':
          counts.hvac++;
          break;
        case 'fire':
        case 'water':
        case 'medical':
          counts.safety++;
          break;
        default:
          break;
      }
    });
    
    // Add safety assets that are positioned on the floor (have iconId or manual coordinates)
    safetyAssets.forEach(asset => {
      if (asset.iconId || (asset.x !== undefined && asset.y !== undefined)) {
        counts.safety++;
      }
    });

    // Add HVAC units that are positioned on the floor (have iconId or manual coordinates)
    hvacUnits.forEach(unit => {
      if (unit.iconId || (unit.x !== undefined && unit.y !== undefined)) {
        counts.hvac++;
      }
    });

    // Add WiFi access points that are positioned on the floor
    wifiAccessPoints.forEach(ap => {
      if (ap.position && (ap.position.x !== undefined && ap.position.y !== undefined)) {
        counts.wifi++;
      }
    });

    // Add utility shutoffs that are positioned on the floor
    utilityShutoffs.forEach(shutoff => {
      if (shutoff.position && (shutoff.position.x !== undefined && shutoff.position.y !== undefined)) {
        counts.utility++;
      }
    });

    // Add church events that are active for this location and timeframe
    churchEvents.forEach(event => {
      if (event.location && (event.location.floorId === selectedFloor || event.location.buildingId === selectedBuilding)) {
        counts.events++;
      }
    });

    // Add AV equipment that is positioned on the floor
    avEquipment.forEach(equipment => {
      if (equipment.location && (equipment.location.floorId === selectedFloor || equipment.location.buildingId === selectedBuilding)) {
        counts.av_equipment++;
      }
    });

    return counts;
  };

  // Render icon details dialog
  const renderIconDetailsDialog = () => {
    if (!selectedIcon) return null;
    
    const isSafetyAsset = selectedIcon.metadata?.isSafetyAsset;
    const isHvacUnit = selectedIcon.metadata?.isHvacUnit;
    const isWifiAP = selectedIcon.metadata?.isWifiAP;
    const isUtilityShutoff = selectedIcon.metadata?.isUtilityShutoff;
    
    return (
      <Dialog open={iconDetailsOpen} onClose={() => setIconDetailsOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getIconComponent(selectedIcon.type, selectedIcon.customType)}
            {selectedIcon.name}
            {isSafetyAsset && (
              <Chip 
                label="Safety Asset" 
                size="small" 
                color="secondary"
                icon={<SecurityIcon />}
              />
            )}
            {isHvacUnit && (
              <Chip 
                label="HVAC Unit" 
                size="small" 
                color="info"
                icon={<AcUnitIcon />}
              />
            )}
            {isWifiAP && (
              <Chip 
                label="WiFi Access Point" 
                size="small" 
                color="primary"
                icon={<WifiIcon />}
              />
            )}
            {isUtilityShutoff && (
              <Chip 
                label="Utility Shutoff" 
                size="small" 
                color="warning"
                icon={<PowerIcon />}
              />
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1">Type: {selectedIcon.type}</Typography>
              {selectedIcon.customType && (
                <Typography variant="body2" color="text.secondary">
                  Subtype: {selectedIcon.customType.replace(/_/g, ' ')}
                </Typography>
              )}
            </Grid>
            
            {/* Standard device information */}
            {selectedIcon.deviceId && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">Device ID: {selectedIcon.deviceId}</Typography>
              </Grid>
            )}
            {selectedIcon.integrationSource && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">Integration: {selectedIcon.integrationSource}</Typography>
              </Grid>
            )}
            
            {/* Safety asset specific information */}
            {isSafetyAsset && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }}>Safety Asset Details</Divider>
                </Grid>
                
                {selectedIcon.metadata.assetId && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Asset ID: {selectedIcon.metadata.assetId}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.room && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Location: {selectedIcon.metadata.room}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.specifications && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Specifications:</Typography>
                    {selectedIcon.metadata.specifications.manufacturer && (
                      <Typography variant="body2">• Manufacturer: {selectedIcon.metadata.specifications.manufacturer}</Typography>
                    )}
                    {selectedIcon.metadata.specifications.model && (
                      <Typography variant="body2">• Model: {selectedIcon.metadata.specifications.model}</Typography>
                    )}
                    {selectedIcon.metadata.specifications.capacity && (
                      <Typography variant="body2">• Capacity: {selectedIcon.metadata.specifications.capacity}</Typography>
                    )}
                    {selectedIcon.metadata.specifications.agent && (
                      <Typography variant="body2">• Agent: {selectedIcon.metadata.specifications.agent}</Typography>
                    )}
                  </Grid>
                )}
                
                {selectedIcon.metadata.inspection && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Inspection Status:</Typography>
                    <Chip
                      label={safetyService.getInspectionStatuses().find(s => s.value === selectedIcon.metadata.inspection.status)?.label || 'Unknown'}
                      color={safetyService.getInspectionStatusColor(selectedIcon.metadata.inspection.status)}
                      sx={{ mr: 1, mb: 1 }}
                    />
                    {selectedIcon.metadata.inspection.lastInspection && (
                      <Typography variant="body2">
                        Last Inspection: {new Date(selectedIcon.metadata.inspection.lastInspection).toLocaleDateString()}
                      </Typography>
                    )}
                    {selectedIcon.metadata.inspection.nextInspection && (
                      <Typography variant="body2">
                        Next Inspection: {new Date(selectedIcon.metadata.inspection.nextInspection).toLocaleDateString()}
                      </Typography>
                    )}
                  </Grid>
                )}
              </>
            )}
            
            {/* HVAC unit specific information */}
            {isHvacUnit && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }}>HVAC Unit Details</Divider>
                </Grid>
                
                {selectedIcon.metadata.unitId && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Unit ID: {selectedIcon.metadata.unitId}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.room && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Location: {selectedIcon.metadata.room}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.customType && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Unit Type: {selectedIcon.customType.replace(/_/g, ' ')}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.filters && selectedIcon.metadata.filters.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Filter Status:</Typography>
                    {selectedIcon.metadata.filters.map((filter, index) => (
                      <Box key={index} sx={{ mb: 1 }}>
                        <Chip
                          label={`${filter.position}: ${filter.filterType} - ${hvacService.isFilterOverdue(filter) ? 'Overdue' : hvacService.isFilterDue(filter) ? 'Due Soon' : 'Good'}`}
                          color={hvacService.getFilterConditionColor(
                            hvacService.isFilterOverdue(filter) ? 'overdue' : 
                            hvacService.isFilterDue(filter) ? 'needs_replacement' : 'good'
                          )}
                          size="small"
                          sx={{ mr: 1, mb: 0.5 }}
                        />
                      </Box>
                    ))}
                  </Grid>
                )}
                
                {selectedIcon.data?.value && (
                  <Grid item xs={12}>
                    <Chip
                      label={hvacService.getUnitStatuses().find(s => s.value === selectedIcon.data.value)?.label || selectedIcon.data.value}
                      color={hvacService.getUnitStatuses().find(s => s.value === selectedIcon.data.value)?.color || 'default'}
                      sx={{ mr: 1, mb: 1 }}
                    />
                  </Grid>
                )}
                
                <Grid item xs={12}>
                  <Button 
                    variant="outlined" 
                    onClick={() => {
                      setIconDetailsOpen(false);
                      setHvacServiceTrackerOpen(true);
                    }}
                    startIcon={<AcUnitIcon />}
                  >
                    Open HVAC Service Tracker
                  </Button>
                </Grid>
              </>
            )}
            
            {/* WiFi access point specific information */}
            {isWifiAP && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }}>WiFi Access Point Details</Divider>
                </Grid>
                
                {selectedIcon.metadata.deviceId && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Device ID: {selectedIcon.metadata.deviceId}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.room && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Location: {selectedIcon.metadata.room}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.healthScore !== undefined && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Health Score: {selectedIcon.metadata.healthScore}%</Typography>
                    <Chip
                      label={
                        selectedIcon.metadata.healthScore >= 90 ? 'Excellent' :
                        selectedIcon.metadata.healthScore >= 75 ? 'Good' :
                        selectedIcon.metadata.healthScore >= 60 ? 'Fair' : 'Poor'
                      }
                      color={
                        selectedIcon.metadata.healthScore >= 90 ? 'success' :
                        selectedIcon.metadata.healthScore >= 75 ? 'primary' :
                        selectedIcon.metadata.healthScore >= 60 ? 'warning' : 'error'
                      }
                      size="small"
                      sx={{ mt: 0.5 }}
                    />
                  </Grid>
                )}
                
                {selectedIcon.metadata.clientCount !== undefined && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Connected Clients: {selectedIcon.metadata.clientCount}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.signalBands && selectedIcon.metadata.signalBands.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Frequency Bands:</Typography>
                    <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                      {selectedIcon.metadata.signalBands.map((band, index) => (
                        <Chip
                          key={index}
                          label={band}
                          size="small"
                          color={band === '6GHz' ? 'secondary' : band === '5GHz' ? 'primary' : 'default'}
                        />
                      ))}
                    </Box>
                  </Grid>
                )}
                
                <Grid item xs={12}>
                  <Button 
                    variant="outlined" 
                    onClick={() => {
                      setIconDetailsOpen(false);
                      setWifiManagementOpen(true);
                    }}
                    startIcon={<WifiIcon />}
                  >
                    Open WiFi Management
                  </Button>
                </Grid>
              </>
            )}
            
            {/* Utility shutoff specific information */}
            {isUtilityShutoff && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }}>Utility Shutoff Details</Divider>
                </Grid>
                
                {selectedIcon.metadata.shutoffId && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Shutoff ID: {selectedIcon.metadata.shutoffId}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.utilityType && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Utility Type: {selectedIcon.metadata.utilityType}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.priority && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Priority: </Typography>
                    <Chip
                      label={selectedIcon.metadata.priority}
                      size="small"
                      sx={{ 
                        backgroundColor: utilityShutoffService.getPriorityColor(selectedIcon.metadata.priority),
                        color: 'white',
                        ml: 1
                      }}
                    />
                  </Grid>
                )}
                
                {selectedIcon.metadata.condition && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle1">Condition: </Typography>
                    <Chip
                      label={selectedIcon.metadata.condition}
                      size="small"
                      color={selectedIcon.metadata.condition === 'poor' ? 'error' : 'default'}
                      sx={{ ml: 1 }}
                    />
                  </Grid>
                )}
                
                {selectedIcon.metadata.affectedAreas && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Affected Areas: {selectedIcon.metadata.affectedAreas}</Typography>
                  </Grid>
                )}
                
                {selectedIcon.metadata.emergencyContact && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Emergency Contact: {selectedIcon.metadata.emergencyContact}</Typography>
                    <Button 
                      variant="outlined" 
                      size="small"
                      href={`tel:${selectedIcon.metadata.emergencyContact}`}
                      startIcon={<PhoneIcon />}
                      sx={{ ml: 1 }}
                    >
                      Call
                    </Button>
                  </Grid>
                )}
                
                {(selectedIcon.metadata.isCritical || selectedIcon.metadata.isEmergency) && (
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {selectedIcon.metadata.isCritical && (
                        <Chip 
                          label="CRITICAL" 
                          size="small" 
                          color="error"
                          icon={<EmergencyIcon />}
                        />
                      )}
                      {selectedIcon.metadata.isEmergency && (
                        <Chip 
                          label="EMERGENCY" 
                          size="small" 
                          color="error"
                          icon={<WarningIcon />}
                        />
                      )}
                    </Box>
                  </Grid>
                )}
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button 
                      variant="outlined" 
                      onClick={() => {
                        setIconDetailsOpen(false);
                        setUtilityShutoffManagementOpen(true);
                      }}
                      startIcon={<BuildIcon />}
                    >
                      Open Utility Management
                    </Button>
                    <Button 
                      variant="contained" 
                      color="error"
                      onClick={() => {
                        setIconDetailsOpen(false);
                        setEmergencyUtilityLocatorOpen(true);
                        setEmergencyMode(true);
                      }}
                      startIcon={<EmergencyIcon />}
                    >
                      Emergency Locator
                    </Button>
                  </Box>
                </Grid>
              </>
            )}
            
            {/* Standard data display */}
            {selectedIcon.data && selectedIcon.data.value && !isSafetyAsset && !isHvacUnit && !isWifiAP && !isUtilityShutoff && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">
                  Value: {selectedIcon.data.value} {selectedIcon.data.unit || ''}
                </Typography>
              </Grid>
            )}
            {selectedIcon.data && selectedIcon.data.lastUpdated && (
              <Grid item xs={12}>
                <Typography variant="subtitle1">
                  Last Updated: {new Date(selectedIcon.data.lastUpdated).toLocaleString()}
                </Typography>
              </Grid>
            )}
            
            {/* Door controls */}
            {selectedIcon.type === 'door' && (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    onClick={handleDoorUnlock}
                    disabled={selectedIcon.data && selectedIcon.data.value === 'unlocked'}
                    startIcon={<MeetingRoomIcon />}
                  >
                    Unlock
                  </Button>
                  <Button 
                    variant="contained" 
                    color="secondary" 
                    onClick={handleDoorLock}
                    disabled={selectedIcon.data && selectedIcon.data.value === 'locked'}
                    startIcon={<LockIcon />}
                  >
                    Lock
                  </Button>
                  <Button 
                    variant="contained" 
                    color="info" 
                    onClick={handleDoorPassageMode}
                    disabled={selectedIcon.data && selectedIcon.data.value === 'passage'}
                    startIcon={<MeetingRoomIcon />}
                  >
                    Passage Mode
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          {isEditing && !isSafetyAsset && !isHvacUnit && !isWifiAP && (
            <Button onClick={handleDeleteIcon} color="error">
              Delete
            </Button>
          )}
          <Button onClick={() => setIconDetailsOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render integration selection dialog
  const renderIntegrationDialog = () => {
    return (
      <Dialog open={integrationDialogOpen} onClose={() => setIntegrationDialogOpen(false)}>
        <DialogTitle>Select Integration</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle1">
                Select a device to link to this {iconToAdd} icon:
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Device</InputLabel>
                <Select
                  value={selectedDevice}
                  onChange={(e) => setSelectedDevice(e.target.value)}
                  label="Device"
                >
                  {availableDevices.map(device => (
                    <MenuItem key={device.id} value={device.id}>
                      {device.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIntegrationDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddIcon} 
            color="primary" 
            disabled={!selectedDevice}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Render edit mode toolbar
  const renderEditToolbar = () => {
    if (!isEditing) return null;
    
    return (
      <Paper sx={{ p: 1, mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        <Typography variant="subtitle1" sx={{ flexGrow: 1, alignSelf: 'center' }}>
          Add Icon:
        </Typography>
        <Tooltip title="Add Temperature Sensor">
          <IconButton 
            color={iconToAdd === 'temperature' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('temperature')}
          >
            <ThermostatIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Door">
          <IconButton 
            color={iconToAdd === 'door' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('door')}
          >
            <MeetingRoomIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Camera">
          <IconButton 
            color={iconToAdd === 'camera' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('camera')}
          >
            <VideocamIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add HVAC">
          <IconButton 
            color={iconToAdd === 'hvac' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('hvac')}
          >
            <AcUnitIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add WiFi">
          <IconButton 
            color={iconToAdd === 'wifi' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('wifi')}
          >
            <WifiIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Add Network Device">
          <IconButton 
            color={iconToAdd === 'network' ? 'primary' : 'default'} 
            onClick={() => setIconToAdd('network')}
          >
            <RouterIcon />
          </IconButton>
        </Tooltip>
        <Box sx={{ flexGrow: 1 }} />
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<SaveIcon />}
          onClick={() => {
            setIsEditing(false);
            if (onSave) onSave();
          }}
        >
          Save
        </Button>
        <Button 
          variant="outlined" 
          startIcon={<CancelIcon />}
          onClick={() => {
            setIsEditing(false);
            if (onCancel) onCancel();
          }}
        >
          Cancel
        </Button>
      </Paper>
    );
  };

  return (
    <Box>
      <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Building</InputLabel>
          <Select
            value={selectedBuilding}
            onChange={handleBuildingChange}
            label="Building"
          >
            {buildings.map(building => (
              <MenuItem key={building._id} value={building._id}>
                {building.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Floor</InputLabel>
          <Select
            value={selectedFloor}
            onChange={handleFloorChange}
            label="Floor"
            disabled={!selectedBuilding || floors.length === 0}
          >
            {floors.map(floor => (
              <MenuItem key={floor._id} value={floor._id}>
                {floor.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <Box sx={{ flexGrow: 1 }} />
        
        <Tooltip title="Zoom In">
          <IconButton onClick={handleZoomIn}>
            <ZoomInIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Zoom Out">
          <IconButton onClick={handleZoomOut}>
            <ZoomOutIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Refresh">
          <IconButton onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Layer Controls">
          <IconButton onClick={() => setLayerPanelOpen(!layerPanelOpen)}>
            <LayersIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Electrical Trace">
          <IconButton 
            color={tracePanelOpen ? "primary" : "default"}
            onClick={() => setTracePanelOpen(!tracePanelOpen)}
          >
            <ElectricalServicesIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Door Management">
          <IconButton 
            color={doorBulkManagementOpen ? "primary" : "default"}
            onClick={() => setDoorBulkManagementOpen(true)}
          >
            <SecurityIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Temperature Heatmap">
          <IconButton 
            color={temperatureHeatmapOpen ? "primary" : "default"}
            onClick={() => setTemperatureHeatmapOpen(!temperatureHeatmapOpen)}
          >
            <ThermostatIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="HVAC Service Tracker">
          <IconButton 
            color={hvacServiceTrackerOpen ? "primary" : "default"}
            onClick={() => setHvacServiceTrackerOpen(!hvacServiceTrackerOpen)}
          >
            <AcUnitIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="WiFi Coverage Map">
          <IconButton 
            color={wifiCoverageMapOpen ? "primary" : "default"}
            onClick={() => setWifiCoverageMapOpen(!wifiCoverageMapOpen)}
          >
            <WifiIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="WiFi Management">
          <IconButton 
            color={wifiManagementOpen ? "primary" : "default"}
            onClick={() => setWifiManagementOpen(!wifiManagementOpen)}
          >
            <RouterIcon />
          </IconButton>
        </Tooltip>
        
        {/* Phase 7 - Church Event Management Buttons */}
        <Tooltip title="Church Event Calendar">
          <IconButton 
            color={churchEventCalendarOpen ? "primary" : "default"}
            onClick={() => setChurchEventCalendarOpen(!churchEventCalendarOpen)}
          >
            <CalendarTodayIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Event Room Scheduler">
          <IconButton 
            color={eventRoomSchedulerOpen ? "primary" : "default"}
            onClick={() => setEventRoomSchedulerOpen(!eventRoomSchedulerOpen)}
          >
            <EventIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Event Preparation Tracker">
          <IconButton 
            color={eventPreparationTrackerOpen ? "primary" : "default"}
            onClick={() => setEventPreparationTrackerOpen(!eventPreparationTrackerOpen)}
            disabled={!selectedEvent}
          >
            <AssignmentIcon />
          </IconButton>
        </Tooltip>
        
        {/* Phase 8 - AV Equipment Management Buttons */}
        <Tooltip title="AV Equipment Manager">
          <IconButton 
            color={avEquipmentManagerOpen ? "primary" : "default"}
            onClick={() => setAvEquipmentManagerOpen(!avEquipmentManagerOpen)}
          >
            <AudiotrackIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="AV Troubleshooting Guide">
          <IconButton 
            color={avTroubleshootingGuideOpen ? "primary" : "default"}
            onClick={() => setAvTroubleshootingGuideOpen(!avTroubleshootingGuideOpen)}
          >
            <SupportAgentIcon />
          </IconButton>
        </Tooltip>
        
        {/* Phase 9 - Church-Specific Conveniences Buttons */}
        <Tooltip title="Event Mode Presets">
          <IconButton 
            color={eventModePresetsOpen ? "primary" : "default"}
            onClick={() => setEventModePresetsOpen(!eventModePresetsOpen)}
          >
            <AutoAwesomeIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Volunteer Resource Locator">
          <IconButton 
            color={volunteerResourcesOpen ? "primary" : "default"}
            onClick={() => setVolunteerResourcesOpen(!volunteerResourcesOpen)}
          >
            <VolunteerActivismIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Sanctuary AV Checklist">
          <IconButton 
            color={avChecklistsOpen ? "primary" : "default"}
            onClick={() => setAvChecklistsOpen(!avChecklistsOpen)}
          >
            <ChecklistIcon />
          </IconButton>
        </Tooltip>
        
        <Tooltip title="Wayfinding Guides">
          <IconButton 
            color={wayfindingGuidesOpen ? "primary" : "default"}
            onClick={() => setWayfindingGuidesOpen(!wayfindingGuidesOpen)}
          >
            <DirectionsIcon />
          </IconButton>
        </Tooltip>
        
        {!isEditing && (
          <Tooltip title="Edit Floor Plan">
            <IconButton 
              color="primary" 
              onClick={() => setIsEditing(true)}
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      
      {renderEditToolbar()}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Typography color="error">{error}</Typography>
      ) : !selectedFloor ? (
        <Typography>Please select a building and floor to view the floor plan.</Typography>
      ) : (
        <Box 
          ref={containerRef}
          sx={{ 
            position: 'relative', 
            overflow: 'auto',
            border: '1px solid #ccc',
            borderRadius: 1,
            height: 'calc(100vh - 250px)',
            minHeight: 400
          }}
        >
          <Box 
            ref={floorPlanRef}
            onClick={handleFloorPlanClick}
            sx={{ 
              position: 'relative',
              transform: `scale(${zoom})`,
              transformOrigin: '0 0',
              backgroundImage: `url(${floorPlanUrl})`,
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center top',
              width: '100%',
              height: '100%',
              cursor: isEditing && iconToAdd !== null ? 'crosshair' : 'default'
            }}
          >
            {/* Temperature Heatmap Overlay */}
            {temperatureHeatmapOpen && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  zIndex: 0,
                  pointerEvents: 'none'
                }}
              >
                <TemperatureHeatmap
                  buildingId={selectedBuilding}
                  floorId={selectedFloor}
                  width={800}
                  height={600}
                  showControls={false}
                  autoRefresh={true}
                />
              </Box>
            )}
            {getFilteredIcons().map(icon => {
              const isTraced = traceMode === 'outlet' && tracePath && 
                (tracePath.some(p => p.fromIconId === icon._id || p.toIconId === icon._id));
              
              return (
                <Tooltip key={icon._id} title={getIconTooltip(icon)}>
                  <IconButton
                    onClick={(e) => handleIconClick(icon, e)}
                    onContextMenu={(e) => handleIconClick(icon, e)}
                    sx={{
                      position: 'absolute',
                      left: `${icon.position.x}px`,
                      top: `${icon.position.y}px`,
                      transform: `rotate(${icon.position.rotation || 0}deg)`,
                      color: getIconColor(icon),
                      backgroundColor: isTraced ? 'rgba(255, 193, 7, 0.8)' : 'rgba(255, 255, 255, 0.7)',
                      border: isTraced ? '2px solid #ff6f00' : 'none',
                      boxShadow: isTraced ? '0 0 10px rgba(255, 111, 0, 0.5)' : 'none',
                      '&:hover': {
                        backgroundColor: isTraced ? 'rgba(255, 193, 7, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                      },
                      width: icon.size?.width || 32,
                      height: icon.size?.height || 32,
                      p: 0.5,
                      zIndex: isTraced ? 10 : 1
                    }}
                  >
                    {getIconComponent(icon.type, icon.customType)}
                  </IconButton>
                </Tooltip>
              );
            })}
          </Box>
        </Box>
      )}
      
      {renderIconDetailsDialog()}
      {renderIntegrationDialog()}
      
      {/* Layer Controls Drawer */}
      <Drawer
        anchor="left"
        open={layerPanelOpen}
        onClose={() => setLayerPanelOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 280,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Layer Controls
        </Typography>
        
        {Object.entries(getLayerIconCounts()).map(([layer, count]) => (
          <Box key={layer} sx={{ mb: 2 }}>
            <ToggleButton
              value={layer}
              selected={layerToggles[layer]}
              onChange={() => handleLayerToggle(layer)}
              fullWidth
              sx={{ justifyContent: 'space-between' }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {layer === 'electrical' && <ElectricalServicesIcon />}
                {layer === 'doors' && <MeetingRoomIcon />}
                {layer === 'cameras' && <VideocamIcon />}
                {layer === 'temperature' && <ThermostatIcon />}
                {layer === 'network' && <RouterIcon />}
                {layer === 'hvac' && <AcUnitIcon />}
                {layer === 'safety' && <SecurityIcon />}
                {layer === 'wifi' && <WifiIcon />}
                {layer === 'utility' && <OpacityIcon />}
                {layer === 'events' && <EventIcon />}
                {layer === 'av_equipment' && <AudiotrackIcon />}
                <Typography sx={{ textTransform: 'capitalize' }}>
                  {layer === 'av_equipment' ? 'AV Equipment' : layer}
                </Typography>
              </Box>
              <Badge badgeContent={count} color="primary" />
            </ToggleButton>
          </Box>
        ))}
        
        <Divider sx={{ my: 2 }} />
        
        <Button
          variant="outlined"
          fullWidth
          onClick={() => {
            setLayerToggles({
              temperature: true,
              doors: true,
              cameras: true,
              network: true,
              electrical: true,
              hvac: true,
              safety: true,
              wifi: true,
              utility: true,
              events: true,
              av_equipment: true
            });
          }}
        >
          Show All Layers
        </Button>
        
        <Button
          variant="outlined"
          fullWidth
          sx={{ mt: 1 }}
          onClick={() => {
            setLayerToggles({
              temperature: false,
              doors: false,
              cameras: false,
              network: false,
              electrical: false,
              hvac: false,
              safety: false,
              wifi: false
            });
          }}
        >
          Hide All Layers
        </Button>
      </Drawer>
      
      {/* Electrical Trace Panel Drawer */}
      <Drawer
        anchor="right"
        open={tracePanelOpen}
        onClose={() => setTracePanelOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 320,
          },
        }}
      >
        <OutletTracePanel
          onTrace={handleElectricalTrace}
          onClearTrace={handleClearTrace}
          floorId={selectedFloor}
          buildingId={selectedBuilding}
        />
      </Drawer>
      
      {/* Door Control Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <Box sx={{ p: 1, minWidth: 200 }}>
          {contextMenuIcon && (
            <>
              <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 'bold' }}>
                {contextMenuIcon.name}
              </Typography>
              
              <Typography variant="body2" sx={{ mb: 2 }}>
                Status: {contextMenuIcon.data?.value || 'Unknown'}
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<LockOpenIcon />}
                  onClick={() => handleDoorUnlock()}
                  disabled={contextMenuIcon.data && contextMenuIcon.data.value === 'unlocked'}
                  fullWidth
                >
                  Unlock Door
                </Button>
                
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<LockIcon />}
                  onClick={() => handleDoorLock()}
                  disabled={contextMenuIcon.data && contextMenuIcon.data.value === 'locked'}
                  fullWidth
                >
                  Lock Door
                </Button>
                
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<MeetingRoomOutlinedIcon />}
                  onClick={() => handleDoorPassageMode()}
                  disabled={contextMenuIcon.data && contextMenuIcon.data.value === 'passage'}
                  fullWidth
                >
                  Passage Mode
                </Button>
              </Box>
            </>
          )}
        </Box>
      </Menu>
      
      {/* Phase 3 - Camera Modal */}
      <CameraModal
        open={cameraModalOpen}
        onClose={() => setCameraModalOpen(false)}
        camera={selectedCamera}
        initialView="snapshot"
      />
      
      {/* Phase 3 - Door Bulk Management */}
      <DoorBulkManagement
        open={doorBulkManagementOpen}
        onClose={() => setDoorBulkManagementOpen(false)}
        buildingId={selectedBuilding}
        floorId={selectedFloor}
      />
      
      {/* Phase 4 - HVAC Service Tracker */}
      <Drawer
        anchor="right"
        open={hvacServiceTrackerOpen}
        onClose={() => setHvacServiceTrackerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 480,
          },
        }}
      >
        <HVACServiceTracker
          buildingId={selectedBuilding}
          floorId={selectedFloor}
        />
      </Drawer>
      
      {/* Phase 4 - Temperature Heatmap Controls */}
      <Drawer
        anchor="left"
        open={temperatureHeatmapOpen}
        onClose={() => setTemperatureHeatmapOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 360,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Temperature Heatmap
        </Typography>
        <TemperatureHeatmap
          buildingId={selectedBuilding}
          floorId={selectedFloor}
          width={320}
          height={240}
          showControls={true}
          autoRefresh={true}
        />
      </Drawer>
      
      {/* Phase 5 - WiFi Coverage Map */}
      <Drawer
        anchor="left"
        open={wifiCoverageMapOpen}
        onClose={() => setWifiCoverageMapOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 400,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          WiFi Coverage Map
        </Typography>
        <WiFiCoverageMap
          buildingId={selectedBuilding}
          floorId={selectedFloor}
          showControls={true}
          autoRefresh={true}
        />
      </Drawer>
      
      {/* Phase 5 - WiFi Management */}
      <Drawer
        anchor="right"
        open={wifiManagementOpen}
        onClose={() => setWifiManagementOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 600,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          WiFi Management
        </Typography>
        <WiFiManagement
          buildingId={selectedBuilding}
          floorId={selectedFloor}
        />
      </Drawer>
      
      {/* Phase 7 - Church Event Calendar */}
      <Drawer
        anchor="left"
        open={churchEventCalendarOpen}
        onClose={() => setChurchEventCalendarOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 600,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Church Event Calendar
        </Typography>
        <ChurchEventCalendar
          buildingId={selectedBuilding}
          floorId={selectedFloor}
          view="week"
          onEventSelect={(event) => {
            setSelectedEvent(event);
            setEventPreparationTrackerOpen(true);
          }}
          showPreparationStatus={true}
          compactView={false}
        />
      </Drawer>
      
      {/* Phase 7 - Event Room Scheduler */}
      <Drawer
        anchor="right"
        open={eventRoomSchedulerOpen}
        onClose={() => setEventRoomSchedulerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 800,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Event Room Scheduler
        </Typography>
        <EventRoomScheduler
          buildingId={selectedBuilding}
          selectedDate={new Date()}
          view="day"
          onEventSelect={(event) => {
            setSelectedEvent(event);
            setEventPreparationTrackerOpen(true);
          }}
          onCreateEvent={() => {
            // Handle create event - could open a form or navigate elsewhere
            console.log('Create new event');
          }}
          showCapacityUtilization={true}
          compactView={false}
        />
      </Drawer>
      
      {/* Phase 7 - Event Preparation Tracker */}
      <Drawer
        anchor="right"
        open={eventPreparationTrackerOpen}
        onClose={() => setEventPreparationTrackerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 700,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Event Preparation Tracker
        </Typography>
        {selectedEvent ? (
          <EventPreparationTracker
            eventId={selectedEvent._id}
            onEventUpdate={() => {
              // Refresh event data when updated
              if (selectedFloor && selectedBuilding) {
                churchEventService.getEventsByLocation(
                  selectedBuilding, 
                  selectedFloor, 
                  {
                    startDate: new Date().toISOString(),
                    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    includePreparation: true
                  }
                ).then(setChurchEvents).catch(console.error);
              }
            }}
            showSystemsIntegration={true}
            compactView={false}
          />
        ) : (
          <Typography color="text.secondary">
            Please select an event from the calendar or scheduler to track its preparation.
          </Typography>
        )}
      </Drawer>
      
      {/* Phase 8 - AV Equipment Manager */}
      <Drawer
        anchor="left"
        open={avEquipmentManagerOpen}
        onClose={() => setAvEquipmentManagerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 700,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          AV Equipment Manager
        </Typography>
        <AVEquipmentManager
          buildingId={selectedBuilding}
          floorId={selectedFloor}
          selectedCategory={null}
          onEquipmentSelect={(equipment) => {
            setSelectedAVEquipment(equipment);
            setAvTroubleshootingGuideOpen(true);
          }}
          showTechnicalSupport={true}
          compactView={false}
        />
      </Drawer>
      
      {/* Phase 8 - AV Troubleshooting Guide */}
      <Drawer
        anchor="right"
        open={avTroubleshootingGuideOpen}
        onClose={() => setAvTroubleshootingGuideOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 600,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          AV Troubleshooting Guide
        </Typography>
        <AVTroubleshootingGuide
          selectedEquipment={selectedAVEquipment}
          onCreateTicket={(ticketData) => {
            // Handle create ticket - could integrate with existing ticketing system
            console.log('Create AV support ticket:', ticketData);
          }}
          showEmergencyContacts={true}
          compactView={false}
        />
      </Drawer>
      
      {/* Phase 9 - Event Mode Presets */}
      <Drawer
        anchor="left"
        open={eventModePresetsOpen}
        onClose={() => setEventModePresetsOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 500,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Event Mode Presets
        </Typography>
        <Box sx={{ mt: 2 }}>
          {eventModePresets.length > 0 ? (
            eventModePresets.map((preset) => (
              <Paper key={preset._id} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {preset.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {preset.description}
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<AutoAwesomeIcon />}
                  onClick={async () => {
                    try {
                      const result = await eventModePresetService.applyPreset(preset._id, {
                        layers: layerToggles
                      });
                      setLayerToggles(result.config.layers);
                      setCurrentEventMode(preset);
                    } catch (error) {
                      console.error('Error applying preset:', error);
                    }
                  }}
                >
                  Apply Preset
                </Button>
              </Paper>
            ))
          ) : (
            <Typography color="text.secondary">
              No event mode presets available.
            </Typography>
          )}
        </Box>
      </Drawer>
      
      {/* Phase 9 - Volunteer Resource Locator */}
      <Drawer
        anchor="right"
        open={volunteerResourcesOpen}
        onClose={() => setVolunteerResourcesOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 600,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Volunteer Resource Locator
        </Typography>
        <Box sx={{ mt: 2 }}>
          {volunteerResources.length > 0 ? (
            volunteerResources.map((resource) => (
              <Paper key={resource._id} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {resource.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Location: {resource.room}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Category: {resource.category.replace(/_/g, ' ').toUpperCase()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Available: {resource.quantity?.available || 1} {resource.quantity?.unit || 'items'}
                </Typography>
              </Paper>
            ))
          ) : (
            <Typography color="text.secondary">
              No volunteer resources found for this floor.
            </Typography>
          )}
        </Box>
      </Drawer>
      
      {/* Phase 9 - Sanctuary AV Checklist */}
      <Drawer
        anchor="left"
        open={avChecklistsOpen}
        onClose={() => setAvChecklistsOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 700,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Sanctuary AV Checklist
        </Typography>
        <Box sx={{ mt: 2 }}>
          {avChecklists.length > 0 ? (
            avChecklists.map((checklist) => (
              <Paper key={checklist._id} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {checklist.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Type: {checklist.checklistType.replace(/_/g, ' ').toUpperCase()}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Items: {checklist.items?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Estimated Time: {checklist.estimatedTotalTime || 'Unknown'} minutes
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<ChecklistIcon />}
                  onClick={async () => {
                    try {
                      await avChecklistService.startExecution(checklist._id);
                      setSanctuaryChecklistActive(true);
                    } catch (error) {
                      console.error('Error starting checklist:', error);
                    }
                  }}
                >
                  Start Checklist
                </Button>
              </Paper>
            ))
          ) : (
            <Typography color="text.secondary">
              No AV checklists available.
            </Typography>
          )}
        </Box>
      </Drawer>
      
      {/* Phase 9 - Wayfinding Guides */}
      <Drawer
        anchor="right"
        open={wayfindingGuidesOpen}
        onClose={() => setWayfindingGuidesOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: 600,
            p: 2,
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Wayfinding Guides
        </Typography>
        <Box sx={{ mt: 2 }}>
          {wayfindingRoutes.length > 0 ? (
            wayfindingRoutes.map((route) => (
              <Paper key={route._id} sx={{ p: 2, mb: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {route.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  From: {route.startPoint?.description}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  To: {route.endPoint?.description}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Type: {route.routeType.replace(/_/g, ' ').toUpperCase()}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Difficulty: {route.difficulty.charAt(0).toUpperCase() + route.difficulty.slice(1)}
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<PictureAsPdfIcon />}
                  onClick={async () => {
                    try {
                      await wayfindingService.generatePDF(route._id);
                    } catch (error) {
                      console.error('Error generating PDF:', error);
                    }
                  }}
                  sx={{ mr: 1 }}
                >
                  Download PDF
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<DirectionsIcon />}
                  onClick={() => {
                    // Could implement route visualization on floor plan
                    console.log('Show route on floor plan:', route);
                  }}
                >
                  Show Route
                </Button>
              </Paper>
            ))
          ) : (
            <Typography color="text.secondary">
              No wayfinding routes available for this building.
            </Typography>
          )}
        </Box>
      </Drawer>
    </Box>
  );
};

export default FloorPlanViewer;