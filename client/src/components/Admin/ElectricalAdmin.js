import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Button,
  Card,
  CardContent,
  CardActions,
  Grid,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  ElectricalServices as ElectricalServicesIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Add as AddIcon
} from '@mui/icons-material';
import electricalService from '../../services/electricalService';

/**
 * ElectricalAdmin Component
 * Administrative interface for electrical system management
 */
const ElectricalAdmin = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState(null);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importType, setImportType] = useState('panels');
  const [selectedFile, setSelectedFile] = useState(null);
  const [seedDialogOpen, setSeedDialogOpen] = useState(false);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSeedDemoData = async () => {
    try {
      setLoading(true);
      setMessage(null);
      
      const result = await electricalService.seedDemoData();
      setMessage({
        type: 'success',
        text: `Demo data created successfully! Created ${result.created.panels} panels, ${result.created.circuits} circuits, and ${result.created.outlets} outlets.`
      });
      setSeedDialogOpen(false);
    } catch (error) {
      setMessage({
        type: 'error',
        text: error.response?.data?.message || 'Failed to create demo data'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type === 'text/csv') {
      setSelectedFile(file);
    } else {
      setMessage({
        type: 'error',
        text: 'Please select a valid CSV file'
      });
    }
  };

  const handleImport = async (dryRun = false) => {
    if (!selectedFile) {
      setMessage({
        type: 'error',
        text: 'Please select a file first'
      });
      return;
    }

    try {
      setLoading(true);
      setMessage(null);

      let result;
      if (importType === 'panels') {
        result = await electricalService.importPanels(selectedFile, dryRun);
      } else {
        result = await electricalService.importOutlets(selectedFile, dryRun, true);
      }

      const action = dryRun ? 'Preview' : 'Import';
      setMessage({
        type: 'success',
        text: `${action} completed! Created: ${result.created}, Updated: ${result.updated}, Errors: ${result.errors.length}`
      });

      if (!dryRun) {
        setImportDialogOpen(false);
        setSelectedFile(null);
      }
    } catch (error) {
      setMessage({
        type: 'error',
        text: error.response?.data?.message || 'Import failed'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportPanels = async () => {
    try {
      setLoading(true);
      setMessage(null);
      
      await electricalService.exportPanels();
      setMessage({
        type: 'success',
        text: 'Panels exported successfully! Check your downloads folder.'
      });
    } catch (error) {
      setMessage({
        type: 'error',
        text: error.response?.data?.message || 'Export failed'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportOutlets = async () => {
    try {
      setLoading(true);
      setMessage(null);
      
      await electricalService.exportOutlets();
      setMessage({
        type: 'success',
        text: 'Outlets exported successfully! Check your downloads folder.'
      });
    } catch (error) {
      setMessage({
        type: 'error',
        text: error.response?.data?.message || 'Export failed'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportReport = async () => {
    try {
      setLoading(true);
      setMessage(null);
      
      await electricalService.exportReport();
      setMessage({
        type: 'success',
        text: 'System report exported successfully! Check your downloads folder.'
      });
    } catch (error) {
      setMessage({
        type: 'error',
        text: error.response?.data?.message || 'Export failed'
      });
    } finally {
      setLoading(false);
    }
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <ElectricalServicesIcon />
        Electrical System Administration
      </Typography>

      {message && (
        <Alert severity={message.type} sx={{ mb: 2 }}>
          {message.text}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Quick Actions" />
          <Tab label="Data Import" />
          <Tab label="System Status" />
        </Tabs>

        {/* Quick Actions Tab */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Demo Data Setup
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Create sample electrical data for testing and demonstration purposes.
                    Includes panels, circuits, and outlets with realistic church building layout.
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setSeedDialogOpen(true)}
                    disabled={loading}
                  >
                    Create Demo Data
                  </Button>
                </CardActions>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Data Export
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Export electrical system data for backup, reporting, or migration purposes.
                    Available formats: CSV reports.
                  </Typography>
                </CardContent>
                <CardActions sx={{ flexWrap: 'wrap', gap: 1 }}>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={handleExportPanels}
                    disabled={loading}
                  >
                    Export Panels
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={handleExportOutlets}
                    disabled={loading}
                  >
                    Export Outlets
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={handleExportReport}
                    disabled={loading}
                  >
                    Export Report
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Data Import Tab */}
        <TabPanel value={activeTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Import Panels
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Import electrical panels from CSV file. Required columns:
                    buildingCode, floorCode, name, code, room, notes
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => {
                      setImportType('panels');
                      setImportDialogOpen(true);
                    }}
                  >
                    Import Panels
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Import Outlets
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Import electrical outlets from CSV file. Required columns:
                    buildingCode, floorCode, label, room, panelCode, circuitNumber
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => {
                      setImportType('outlets');
                      setImportDialogOpen(true);
                    }}
                  >
                    Import Outlets
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* System Status Tab */}
        <TabPanel value={activeTab} index={2}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Electrical system status and health information will be displayed here.
                This section is planned for future development.
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                disabled
              >
                Refresh Status
              </Button>
            </CardActions>
          </Card>
        </TabPanel>
      </Paper>

      {/* Seed Confirmation Dialog */}
      <Dialog open={seedDialogOpen} onClose={() => setSeedDialogOpen(false)}>
        <DialogTitle>Create Demo Data</DialogTitle>
        <DialogContent>
          <Typography>
            This will create sample electrical data including panels, circuits, and outlets.
            Are you sure you want to proceed?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSeedDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSeedDemoData} variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={20} /> : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={importDialogOpen} onClose={() => setImportDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Import {importType === 'panels' ? 'Panels' : 'Outlets'}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <input
              accept=".csv"
              type="file"
              onChange={handleFileSelect}
              style={{ marginBottom: '16px' }}
            />
            
            {selectedFile && (
              <Alert severity="info" sx={{ mb: 2 }}>
                Selected file: {selectedFile.name}
              </Alert>
            )}

            <Typography variant="body2" color="text.secondary">
              {importType === 'panels' 
                ? 'CSV should contain: buildingCode, floorCode, name, code, room, notes'
                : 'CSV should contain: buildingCode, floorCode, label, room, panelCode, circuitNumber, breakerNumber, notes'
              }
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImportDialogOpen(false)}>Cancel</Button>
          <Button onClick={() => handleImport(true)} disabled={!selectedFile || loading}>
            {loading ? <CircularProgress size={20} /> : 'Preview'}
          </Button>
          <Button onClick={() => handleImport(false)} variant="contained" disabled={!selectedFile || loading}>
            {loading ? <CircularProgress size={20} /> : 'Import'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ElectricalAdmin;