import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ColorLens as ColorLensIcon
} from '@mui/icons-material';
import * as menuCategoryService from '../../services/menuCategoryService';

const MenuCategoryManagement = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: 'folder',
    color: '#1976d2',
    order: 0
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch categories
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const data = await menuCategoryService.getCategories();
      setCategories(data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError('Failed to load categories');
      setLoading(false);
    }
  };

  // Open dialog for new category
  const handleAddCategory = () => {
    setSelectedCategory(null);
    setFormData({
      name: '',
      description: '',
      icon: 'folder',
      color: '#1976d2',
      order: 0
    });
    setDialogOpen(true);
  };

  // Open dialog for editing category
  const handleEditCategory = (category) => {
    setSelectedCategory(category);
    setFormData({
      name: category.name,
      description: category.description || '',
      icon: category.icon || 'folder',
      color: category.color || '#1976d2',
      order: category.order || 0
    });
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle form input changes
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save category
  const handleSaveCategory = async () => {
    try {
      if (selectedCategory) {
        // Update existing category
        await menuCategoryService.updateCategory(selectedCategory._id, formData);
        setSnackbar({
          open: true,
          message: 'Category updated successfully',
          severity: 'success'
        });
      } else {
        // Create new category
        await menuCategoryService.createCategory(formData);
        setSnackbar({
          open: true,
          message: 'Category created successfully',
          severity: 'success'
        });
      }

      // Refresh categories
      await fetchCategories();
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving category:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save category',
        severity: 'error'
      });
    }
  };

  // Delete category
  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category? This may affect menu items using this category.')) {
      try {
        await menuCategoryService.deleteCategory(categoryId);
        
        // Refresh categories
        await fetchCategories();
        
        setSnackbar({
          open: true,
          message: 'Category deleted successfully',
          severity: 'success'
        });
      } catch (error) {
        console.error('Error deleting category:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete category',
          severity: 'error'
        });
      }
    }
  };

  // Initialize default categories
  const handleInitializeDefaultCategories = async () => {
    if (window.confirm('This will create default categories if they don\'t exist. Continue?')) {
      try {
        await menuCategoryService.initializeDefaultCategories();
        
        // Refresh categories
        await fetchCategories();
        
        setSnackbar({
          open: true,
          message: 'Default categories initialized',
          severity: 'success'
        });
      } catch (error) {
        console.error('Error initializing default categories:', error);
        setSnackbar({
          open: true,
          message: 'Failed to initialize default categories',
          severity: 'error'
        });
      }
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Box>
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Menu Categories</Typography>
          <Box>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={handleInitializeDefaultCategories}
              sx={{ mr: 1 }}
            >
              Initialize Default Categories
            </Button>
            <Button 
              variant="contained" 
              size="small" 
              startIcon={<AddIcon />} 
              onClick={handleAddCategory}
            >
              Add Category
            </Button>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={24} />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        ) : categories.length === 0 ? (
          <Alert severity="info">No categories found. Click "Initialize Default Categories" to create default categories.</Alert>
        ) : (
          <List>
            {categories.map((category) => (
              <React.Fragment key={category._id}>
                <ListItem>
                  <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                    <Box 
                      sx={{ 
                        width: 24, 
                        height: 24, 
                        borderRadius: '50%', 
                        bgcolor: category.color || '#1976d2',
                        mr: 1
                      }} 
                    />
                  </Box>
                  <ListItemText 
                    primary={category.name} 
                    secondary={category.description || 'No description'} 
                  />
                  <ListItemSecondaryAction>
                    <IconButton 
                      edge="end" 
                      aria-label="edit" 
                      onClick={() => handleEditCategory(category)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      edge="end" 
                      aria-label="delete" 
                      onClick={() => handleDeleteCategory(category._id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        )}
      </Paper>

      {/* Category Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedCategory ? 'Edit Category' : 'Add Category'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              margin="normal"
              multiline
              rows={2}
            />
            <TextField
              fullWidth
              label="Icon (Material-UI icon name)"
              name="icon"
              value={formData.icon}
              onChange={handleInputChange}
              margin="normal"
              helperText="Enter a Material-UI icon name, e.g., 'folder', 'link', 'dashboard'"
            />
            <TextField
              fullWidth
              label="Color"
              name="color"
              value={formData.color}
              onChange={handleInputChange}
              margin="normal"
              type="color"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <ColorLensIcon />
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              fullWidth
              label="Order"
              name="order"
              type="number"
              value={formData.order}
              onChange={handleInputChange}
              margin="normal"
              helperText="Lower numbers appear first"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveCategory} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MenuCategoryManagement;