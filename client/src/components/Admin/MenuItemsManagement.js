import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Link as LinkIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import * as menuItemService from '../../services/menuItemService';
import * as menuCategoryService from '../../services/menuCategoryService';

const MenuItemsManagement = () => {
  // State for menu items list
  const [menuItems, setMenuItems] = useState([]);
  const [filteredMenuItems, setFilteredMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  
  // State for categories
  const [categories, setCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  
  // State for dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    originalTitle: '',
    friendlyName: '',
    description: '',
    path: '',
    icon: 'link',
    customIcon: '',
    categories: ['Core Features'],
    requiredRoles: ['user'],
    requiredPermission: '',
    isActive: true,
    order: 0,
    type: 'regular',
    parent: ''
  });
  
  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch menu items and categories on component mount
  useEffect(() => {
    fetchMenuItems();
    fetchCategories();
  }, []);

  // Filter menu items when search term, filter category, or filter type changes
  useEffect(() => {
    filterMenuItems();
  }, [menuItems, searchTerm, filterCategory, filterType]);

  // Fetch menu items
  const fetchMenuItems = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await menuItemService.getMenuItems();
      setMenuItems(data);
    } catch (err) {
      console.error('Error fetching menu items:', err);
      setError('Failed to load menu items. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      const data = await menuCategoryService.getCategories();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching categories:', err);
    } finally {
      setLoadingCategories(false);
    }
  };

  // Filter menu items based on search term, category, and type
  const filterMenuItems = () => {
    let filtered = [...menuItems];
    
    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(term) ||
        item.description?.toLowerCase().includes(term) ||
        item.path.toLowerCase().includes(term)
      );
    }
    
    // Filter by category
    if (filterCategory !== 'all') {
      filtered = filtered.filter(item => 
        item.categories.includes(filterCategory)
      );
    }
    
    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(item => 
        item.type === filterType
      );
    }
    
    setFilteredMenuItems(filtered);
    setPage(0);
  };

  // Handle refresh button click
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchMenuItems();
    setRefreshing(false);
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle category filter change
  const handleCategoryFilterChange = (event) => {
    setFilterCategory(event.target.value);
  };

  // Handle type filter change
  const handleTypeFilterChange = (event) => {
    setFilterType(event.target.value);
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Open dialog for new menu item
  const handleAddMenuItem = () => {
    setSelectedMenuItem(null);
    setFormData({
      title: '',
      originalTitle: '',
      friendlyName: '',
      description: '',
      path: '',
      icon: 'link',
      customIcon: '',
      categories: ['Core Features'],
      requiredRoles: ['user'],
      requiredPermission: '',
      isActive: true,
      order: 0,
      type: 'regular',
      parent: ''
    });
    setDialogOpen(true);
  };

  // Open dialog for editing menu item
  const handleEditMenuItem = (menuItem) => {
    setSelectedMenuItem(menuItem);
    setFormData({
      title: menuItem.title,
      originalTitle: menuItem.originalTitle || menuItem.title,
      friendlyName: menuItem.friendlyName || '',
      description: menuItem.description || '',
      path: menuItem.path,
      icon: menuItem.icon || 'link',
      customIcon: menuItem.customIcon || '',
      categories: menuItem.categories || ['Core Features'],
      requiredRoles: menuItem.requiredRoles || ['user'],
      requiredPermission: menuItem.requiredPermission || '',
      isActive: menuItem.isActive !== undefined ? menuItem.isActive : true,
      order: menuItem.order || 0,
      type: menuItem.type || 'regular',
      parent: typeof menuItem.parent === 'object' ? menuItem.parent?._id || '' : (menuItem.parent || '')
    });
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle form input changes
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle switch changes
  const handleSwitchChange = (event) => {
    const { name, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save menu item
  const handleSaveMenuItem = async () => {
    try {
      // Ensure originalTitle is set if not provided
      if (!formData.originalTitle) {
        formData.originalTitle = formData.title;
      }
      
      if (selectedMenuItem) {
        // Update existing menu item
        await menuItemService.updateMenuItem(selectedMenuItem._id, formData);
        setSnackbar({
          open: true,
          message: 'Menu item updated successfully',
          severity: 'success'
        });
      } else {
        // Create new menu item
        await menuItemService.createMenuItem(formData);
        setSnackbar({
          open: true,
          message: 'Menu item created successfully',
          severity: 'success'
        });
      }

      // Refresh menu items
      await fetchMenuItems();
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving menu item:', error);
      setSnackbar({
        open: true,
        message: 'Failed to save menu item',
        severity: 'error'
      });
    }
  };

  // Delete menu item
  const handleDeleteMenuItem = async (menuItemId) => {
    if (window.confirm('Are you sure you want to delete this menu item?')) {
      try {
        await menuItemService.deleteMenuItem(menuItemId);
        
        // Refresh menu items
        await fetchMenuItems();
        
        setSnackbar({
          open: true,
          message: 'Menu item deleted successfully',
          severity: 'success'
        });
      } catch (error) {
        console.error('Error deleting menu item:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete menu item',
          severity: 'error'
        });
      }
    }
  };

  // Initialize default menu items
  const handleInitializeDefaultMenuItems = async () => {
    if (window.confirm('This will create default menu items if they don\'t exist. Continue?')) {
      try {
        await menuItemService.initializeDefaultMenuItems();
        
        // Refresh menu items
        await fetchMenuItems();
        
        setSnackbar({
          open: true,
          message: 'Default menu items initialized',
          severity: 'success'
        });
      } catch (error) {
        console.error('Error initializing default menu items:', error);
        setSnackbar({
          open: true,
          message: 'Failed to initialize default menu items',
          severity: 'error'
        });
      }
    }
  };

  // Sync new default menu items (non-destructive)
  const handleSyncNewMenuItems = async () => {
    if (window.confirm('This will add any missing default menu items without changing existing ones. Continue?')) {
      try {
        const result = await menuItemService.syncNewMenuItems();
        
        // Refresh menu items
        await fetchMenuItems();
        
        setSnackbar({
          open: true,
          message: result?.createdCount ? `${result.createdCount} new menu item(s) added` : 'No new menu items to add',
          severity: 'success'
        });
      } catch (error) {
        console.error('Error syncing new default menu items:', error);
        setSnackbar({
          open: true,
          message: 'Failed to sync new default menu items',
          severity: 'error'
        });
      }
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Calculate pagination
  const paginatedMenuItems = filteredMenuItems.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Box>
      <Paper sx={{ mb: 3, p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Menu Items</Typography>
          <Box>
            <Button 
              variant="outlined" 
              size="small" 
              startIcon={<RefreshIcon />} 
              onClick={handleSyncNewMenuItems}
              sx={{ mr: 1 }}
            >
              Sync New Menu Items
            </Button>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={handleInitializeDefaultMenuItems}
              sx={{ mr: 1 }}
            >
              Initialize Default Menu Items
            </Button>
            <Button 
              variant="contained" 
              size="small" 
              startIcon={<AddIcon />} 
              onClick={handleAddMenuItem}
            >
              Add Menu Item
            </Button>
          </Box>
        </Box>

        <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              variant="outlined"
              label="Search Menu Items"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={filterCategory}
                onChange={handleCategoryFilterChange}
                label="Category"
              >
                <MenuItem value="all">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category._id} value={category.name}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                onChange={handleTypeFilterChange}
                label="Type"
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="regular">Regular</MenuItem>
                <MenuItem value="integration">Integration</MenuItem>
                <MenuItem value="admin">Admin</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={2} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Tooltip title="Refresh">
              <IconButton onClick={handleRefresh} disabled={refreshing}>
                {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Path</TableCell>
                <TableCell>Categories</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Order</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : paginatedMenuItems.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Typography variant="body2" color="text.secondary">
                      {searchTerm || filterCategory !== 'all' || filterType !== 'all'
                        ? 'No menu items match your search criteria.'
                        : 'No menu items available. Click "Add Menu Item" to create one or "Initialize Default Menu Items" to create default items.'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedMenuItems.map((item) => (
                  <TableRow key={item._id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {item.isActive ? (
                          <VisibilityIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
                        ) : (
                          <VisibilityOffIcon fontSize="small" sx={{ mr: 1, color: 'text.disabled' }} />
                        )}
                        <Typography variant="body2">{item.title}</Typography>
                      </Box>
                      {item.description && (
                        <Typography variant="caption" color="text.secondary">
                          {item.description}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Tooltip title={item.path}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LinkIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography variant="body2" sx={{ 
                            maxWidth: 150, 
                            overflow: 'hidden', 
                            textOverflow: 'ellipsis', 
                            whiteSpace: 'nowrap' 
                          }}>
                            {item.path}
                          </Typography>
                        </Box>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {item.categories.map((category, index) => (
                          <Chip 
                            key={index} 
                            label={category} 
                            size="small" 
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={item.type} 
                        size="small" 
                        color={
                          item.type === 'admin' 
                            ? 'error' 
                            : item.type === 'integration' 
                              ? 'info' 
                              : 'default'
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={item.isActive ? 'Active' : 'Inactive'} 
                        size="small" 
                        color={item.isActive ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>{item.order}</TableCell>
                    <TableCell align="right">
                      <Tooltip title="Edit">
                        <IconButton size="small" onClick={() => handleEditMenuItem(item)}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton size="small" onClick={() => handleDeleteMenuItem(item._id)} color="error">
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredMenuItems.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Menu Item Edit Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedMenuItem ? 'Edit Menu Item' : 'Add Menu Item'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                  helperText="Display name in the menu"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Friendly Name (Optional)"
                  name="friendlyName"
                  value={formData.friendlyName}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="Alternative display name"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  margin="normal"
                  multiline
                  rows={2}
                  helperText="Brief description of this menu item"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Path"
                  name="path"
                  value={formData.path}
                  onChange={handleInputChange}
                  margin="normal"
                  required
                  helperText="URL path, e.g., /dashboard"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Icon (Material-UI icon name)"
                  name="icon"
                  value={formData.icon}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="Enter a Material-UI icon name, e.g., 'dashboard', 'link'"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Custom Icon URL (Optional)"
                  name="customIcon"
                  value={formData.customIcon}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="URL to a custom icon image"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Categories</InputLabel>
                  <Select
                    multiple
                    name="categories"
                    value={formData.categories}
                    onChange={handleMultiSelectChange}
                    label="Categories"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {loadingCategories ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} />
                      </MenuItem>
                    ) : (
                      categories.map((category) => (
                        <MenuItem key={category._id} value={category.name}>
                          {category.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Required Roles</InputLabel>
                  <Select
                    multiple
                    name="requiredRoles"
                    value={formData.requiredRoles}
                    onChange={handleMultiSelectChange}
                    label="Required Roles"
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    <MenuItem value="user">User</MenuItem>
                    <MenuItem value="admin">Admin</MenuItem>
                    <MenuItem value="manager">Manager</MenuItem>
                    <MenuItem value="staff">Staff</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Required Permission (Optional)"
                  name="requiredPermission"
                  value={formData.requiredPermission}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="Permission required to see this menu item, e.g., 'dashboard:read'"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Type</InputLabel>
                  <Select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    label="Type"
                  >
                    <MenuItem value="regular">Regular</MenuItem>
                    <MenuItem value="integration">Integration</MenuItem>
                    <MenuItem value="admin">Admin</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Parent Menu (Optional)</InputLabel>
                  <Select
                    name="parent"
                    value={formData.parent}
                    onChange={handleInputChange}
                    label="Parent Menu (Optional)"
                    displayEmpty
                  >
                    <MenuItem value="">
                      <em>None (Top-level)</em>
                    </MenuItem>
                    {menuItems
                      .filter(mi => mi._id !== (selectedMenuItem?._id || ''))
                      .sort((a,b) => (a.title || '').localeCompare(b.title || ''))
                      .map(mi => (
                        <MenuItem key={mi._id} value={mi._id}>
                          {mi.title}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Order"
                  name="order"
                  type="number"
                  value={formData.order}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="Display order (lower numbers appear first)"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isActive}
                      onChange={handleSwitchChange}
                      name="isActive"
                      color="primary"
                    />
                  }
                  label="Active"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveMenuItem} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MenuItemsManagement;