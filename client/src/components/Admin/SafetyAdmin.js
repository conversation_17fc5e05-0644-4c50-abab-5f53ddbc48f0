import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Security as SecurityIcon,
  LocalFireDepartment as FireIcon,
  LocalHospital as MedicalIcon,
  Healing as FirstAidIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import safetyService from '../../services/safetyService';
import buildingManagementService from '../../services/buildingManagementService';

/**
 * SafetyAdmin Component
 * Administrative interface for managing safety assets, inspections, and compliance
 */
const SafetyAdmin = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [assets, setAssets] = useState([]);
  const [buildings, setBuildings] = useState([]);
  const [floors, setFloors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // Form dialogs
  const [assetDialog, setAssetDialog] = useState(false);
  const [editingAsset, setEditingAsset] = useState(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [deletingAsset, setDeletingAsset] = useState(null);
  
  // Form state
  const [assetForm, setAssetForm] = useState({
    assetType: 'fire_extinguisher',
    assetId: '',
    name: '',
    buildingId: '',
    floorId: '',
    room: '',
    specifications: {
      manufacturer: '',
      model: '',
      capacity: '',
      agent: '',
      serialNumber: ''
    },
    inspection: {
      frequency: 30,
      lastInspection: '',
      inspector: '',
      notes: ''
    },
    status: 'active'
  });

  // Load data on mount
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadAssets(),
        loadBuildings()
      ]);
    } catch (err) {
      setError('Failed to load initial data');
      console.error('Error loading initial data:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAssets = async () => {
    try {
      const response = await safetyService.getAssets();
      setAssets(response.assets || response || []);
    } catch (err) {
      console.error('Error loading assets:', err);
      throw err;
    }
  };

  const loadBuildings = async () => {
    try {
      const buildingsData = await buildingManagementService.getBuildings();
      setBuildings(buildingsData);
    } catch (err) {
      console.error('Error loading buildings:', err);
      throw err;
    }
  };

  const loadFloors = async (buildingId) => {
    if (!buildingId) {
      setFloors([]);
      return;
    }

    try {
      const floorsData = await buildingManagementService.getFloorsByBuilding(buildingId);
      setFloors(floorsData);
    } catch (err) {
      console.error('Error loading floors:', err);
      setFloors([]);
    }
  };

  // Handle building selection in form
  useEffect(() => {
    if (assetForm.buildingId) {
      loadFloors(assetForm.buildingId);
    } else {
      setFloors([]);
      setAssetForm(prev => ({ ...prev, floorId: '' }));
    }
  }, [assetForm.buildingId]);

  const handleCreateAsset = () => {
    setEditingAsset(null);
    setAssetForm({
      assetType: 'fire_extinguisher',
      assetId: '',
      name: '',
      buildingId: '',
      floorId: '',
      room: '',
      specifications: {
        manufacturer: '',
        model: '',
        capacity: '',
        agent: '',
        serialNumber: ''
      },
      inspection: {
        frequency: 30,
        lastInspection: '',
        inspector: '',
        notes: ''
      },
      status: 'active'
    });
    setAssetDialog(true);
  };

  const handleEditAsset = (asset) => {
    setEditingAsset(asset);
    setAssetForm({
      assetType: asset.assetType || 'fire_extinguisher',
      assetId: asset.assetId || '',
      name: asset.name || '',
      buildingId: asset.buildingId?._id || asset.buildingId || '',
      floorId: asset.floorId?._id || asset.floorId || '',
      room: asset.room || '',
      specifications: {
        manufacturer: asset.specifications?.manufacturer || '',
        model: asset.specifications?.model || '',
        capacity: asset.specifications?.capacity || '',
        agent: asset.specifications?.agent || '',
        serialNumber: asset.specifications?.serialNumber || ''
      },
      inspection: {
        frequency: asset.inspection?.frequency || 30,
        lastInspection: asset.inspection?.lastInspection ? new Date(asset.inspection.lastInspection).toISOString().split('T')[0] : '',
        inspector: asset.inspection?.inspector || '',
        notes: asset.inspection?.notes || ''
      },
      status: asset.status || 'active'
    });
    setAssetDialog(true);
  };

  const handleSaveAsset = async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepare form data
      const assetData = {
        ...assetForm,
        inspection: {
          ...assetForm.inspection,
          lastInspection: assetForm.inspection.lastInspection ? new Date(assetForm.inspection.lastInspection) : null
        }
      };

      if (editingAsset) {
        await safetyService.updateAsset(editingAsset._id, assetData);
        setSuccess('Asset updated successfully');
      } else {
        await safetyService.createAsset(assetData);
        setSuccess('Asset created successfully');
      }

      setAssetDialog(false);
      await loadAssets();
    } catch (err) {
      console.error('Error saving asset:', err);
      setError(err.response?.data?.message || 'Failed to save asset');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAsset = (asset) => {
    setDeletingAsset(asset);
    setDeleteDialog(true);
  };

  const confirmDeleteAsset = async () => {
    try {
      setLoading(true);
      await safetyService.deleteAsset(deletingAsset._id);
      setSuccess('Asset deleted successfully');
      setDeleteDialog(false);
      await loadAssets();
    } catch (err) {
      console.error('Error deleting asset:', err);
      setError('Failed to delete asset');
    } finally {
      setLoading(false);
    }
  };

  const handleSeedDemoData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await safetyService.seedDemoData();
      setSuccess(`Created ${result.created} demo safety assets`);
      await loadAssets();
    } catch (err) {
      console.error('Error seeding demo data:', err);
      setError('Failed to create demo data');
    } finally {
      setLoading(false);
    }
  };

  const getAssetTypeIcon = (assetType) => {
    switch (assetType) {
      case 'fire_extinguisher': return <FireIcon color="error" />;
      case 'aed': return <MedicalIcon color="warning" />;
      case 'first_aid_kit': return <FirstAidIcon color="info" />;
      default: return <SecurityIcon />;
    }
  };

  const getBuildingName = (asset) => {
    if (asset.buildingId?.name) return asset.buildingId.name;
    if (typeof asset.buildingId === 'string') {
      const building = buildings.find(b => b._id === asset.buildingId);
      return building?.name || 'Unknown Building';
    }
    return 'No Building';
  };

  const getFloorName = (asset) => {
    if (asset.floorId?.name) return asset.floorId.name;
    return asset.room || 'Unknown Location';
  };

  const renderAssetsTab = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">Safety Assets</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadAssets}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={handleSeedDemoData}
            disabled={loading}
          >
            Seed Demo Data
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateAsset}
          >
            Add Asset
          </Button>
        </Box>
      </Box>

      {assets.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <SecurityIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>No Safety Assets Found</Typography>
            <Typography color="text.secondary" sx={{ mb: 3 }}>
              Get started by creating your first safety asset or loading demo data.
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button variant="outlined" onClick={handleSeedDemoData}>
                Load Demo Data
              </Button>
              <Button variant="contained" onClick={handleCreateAsset}>
                Create Asset
              </Button>
            </Box>
          </CardContent>
        </Card>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Asset ID</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Inspection Status</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {assets.map((asset) => (
                <TableRow key={asset._id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getAssetTypeIcon(asset.assetType)}
                      {safetyService.getAssetTypes().find(t => t.value === asset.assetType)?.label || asset.assetType}
                    </Box>
                  </TableCell>
                  <TableCell>{asset.name}</TableCell>
                  <TableCell>
                    <Chip label={asset.assetId} variant="outlined" size="small" />
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">{getBuildingName(asset)}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getFloorName(asset)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={safetyService.getInspectionStatuses().find(s => s.value === asset.inspection?.status)?.label || 'Unknown'}
                      color={safetyService.getInspectionStatusColor(asset.inspection?.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={safetyService.getAssetStatuses().find(s => s.value === asset.status)?.label || asset.status}
                      color={safetyService.getAssetStatuses().find(s => s.value === asset.status)?.color || 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditAsset(asset)} size="small">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleDeleteAsset(asset)} size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  const renderInspectionsTab = () => (
    <Box>
      <Typography variant="h5" sx={{ mb: 3 }}>Inspection Management</Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Current Inspections</Typography>
              <List>
                {assets.filter(a => a.inspection?.status === 'current').map((asset) => (
                  <ListItem key={asset._id}>
                    <ListItemIcon>{getAssetTypeIcon(asset.assetType)}</ListItemIcon>
                    <ListItemText 
                      primary={asset.name}
                      secondary={`Next: ${asset.inspection?.nextInspection ? new Date(asset.inspection.nextInspection).toLocaleDateString() : 'Not scheduled'}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="error">Overdue Inspections</Typography>
              <List>
                {assets.filter(a => a.inspection?.status === 'overdue').map((asset) => (
                  <ListItem key={asset._id}>
                    <ListItemIcon>{getAssetTypeIcon(asset.assetType)}</ListItemIcon>
                    <ListItemText 
                      primary={asset.name}
                      secondary={`Last: ${asset.inspection?.lastInspection ? new Date(asset.inspection.lastInspection).toLocaleDateString() : 'Never'}`}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, v) => setActiveTab(v)}>
          <Tab label="Assets" />
          <Tab label="Inspections" />
        </Tabs>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {activeTab === 0 && renderAssetsTab()}
      {activeTab === 1 && renderInspectionsTab()}

      {/* Asset Form Dialog */}
      <Dialog open={assetDialog} onClose={() => setAssetDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingAsset ? 'Edit Safety Asset' : 'Create Safety Asset'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Asset Type"
                select
                value={assetForm.assetType}
                onChange={(e) => setAssetForm({...assetForm, assetType: e.target.value})}
              >
                {safetyService.getAssetTypes().map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Asset ID"
                value={assetForm.assetId}
                onChange={(e) => setAssetForm({...assetForm, assetId: e.target.value})}
                helperText="Unique identifier (e.g., FE-001)"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Name"
                value={assetForm.name}
                onChange={(e) => setAssetForm({...assetForm, name: e.target.value})}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Building"
                select
                value={assetForm.buildingId}
                onChange={(e) => setAssetForm({...assetForm, buildingId: e.target.value})}
              >
                {buildings.map((building) => (
                  <MenuItem key={building._id} value={building._id}>
                    {building.name}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Floor"
                select
                value={assetForm.floorId}
                onChange={(e) => setAssetForm({...assetForm, floorId: e.target.value})}
                disabled={!assetForm.buildingId}
              >
                {floors.map((floor) => (
                  <MenuItem key={floor._id} value={floor._id}>
                    {floor.name}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Room/Location"
                value={assetForm.room}
                onChange={(e) => setAssetForm({...assetForm, room: e.target.value})}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>Specifications</Divider>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Manufacturer"
                value={assetForm.specifications.manufacturer}
                onChange={(e) => setAssetForm({
                  ...assetForm, 
                  specifications: {...assetForm.specifications, manufacturer: e.target.value}
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Model"
                value={assetForm.specifications.model}
                onChange={(e) => setAssetForm({
                  ...assetForm, 
                  specifications: {...assetForm.specifications, model: e.target.value}
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Capacity"
                value={assetForm.specifications.capacity}
                onChange={(e) => setAssetForm({
                  ...assetForm, 
                  specifications: {...assetForm.specifications, capacity: e.target.value}
                })}
                helperText="e.g., 5lbs, Adult/Pediatric"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Agent/Type"
                value={assetForm.specifications.agent}
                onChange={(e) => setAssetForm({
                  ...assetForm, 
                  specifications: {...assetForm.specifications, agent: e.target.value}
                })}
                helperText="e.g., ABC Dry Chemical"
              />
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>Inspection</Divider>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Inspection Frequency (days)"
                type="number"
                value={assetForm.inspection.frequency}
                onChange={(e) => setAssetForm({
                  ...assetForm, 
                  inspection: {...assetForm.inspection, frequency: parseInt(e.target.value)}
                })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Inspection"
                type="date"
                value={assetForm.inspection.lastInspection}
                onChange={(e) => setAssetForm({
                  ...assetForm, 
                  inspection: {...assetForm.inspection, lastInspection: e.target.value}
                })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssetDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveAsset} variant="contained" disabled={loading}>
            {editingAsset ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
        <DialogTitle>Delete Safety Asset</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{deletingAsset?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog(false)}>Cancel</Button>
          <Button onClick={confirmDeleteAsset} color="error" disabled={loading}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SafetyAdmin;