import React from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  CardContent, 
  <PERSON>Actions, 
  Button, 
  Typography, 
  Box
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Link as LinkIcon,
  Folder as FolderIcon,
  Brush as BrushIcon,
  Computer as ComputerIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  Security as SecurityIcon,
  Router as RouterIcon,
  Videocam as VideocamIcon,
  AcUnit as AcUnitIcon,
  Business as BusinessIcon,
  Lock as LockIcon,
  Star as StarIcon,
  Event as EventIcon,
  Description as DescriptionIcon,
  Apple as AppleIcon,
  Apartment as ApartmentIcon,
  AdminPanelSettings as AdminPanelSettingsIcon,
  Assignment as AssignmentIcon,
  AcUnit as LGThinqIcon,
  ShoppingCart as MarketplaceIcon,
  Group as GroupIcon,
  Wifi as RadiusIcon,
  MeetingRoom as RoomBookingIcon,
  ContactPhone as StaffDirectoryIcon,
  CheckCircle as TasksIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import Widget from './Widget';

const QuickActionsWidget = ({ id, title = 'Quick Actions', onRemove, onEdit, settings = {} }) => {
  const { user, hasIntegration } = useAuth();

  // Get enabled actions from settings, or use all if not specified
  const enabledActions = settings.enabledActions || [
    'shortcuts', 'drive', 'canva', 'glpi', 'planningCenter', 'synology', 
    'unifiAccess', 'unifiNetwork', 'unifiProtect', 'dreo', 'mosyleBusiness', 
    'lenelS2NetBox', 'googleCalendar', 'googleDrive'
  ];

  // Define all possible actions
  const allActions = [
    {
      id: 'shortcuts',
      title: 'Shortcuts',
      icon: <LinkIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/shortcuts'
    },
    {
      id: 'drive',
      title: 'Drive Files',
      icon: <FolderIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/drive'
    },
    {
      id: 'canva',
      title: 'Canva',
      icon: <BrushIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/canva'
    },
    {
      id: 'glpi',
      title: 'GLPI',
      icon: <ComputerIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/glpi'
    },
    {
      id: 'planningCenter',
      title: 'Planning Center',
      icon: <PeopleIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/planning-center'
    },
    {
      id: 'synology',
      title: 'Synology',
      icon: <StorageIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/synology'
    },
    {
      id: 'unifiAccess',
      title: 'Unifi Access',
      icon: <SecurityIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/unifi-access'
    },
    {
      id: 'unifiNetwork',
      title: 'Unifi Network',
      icon: <RouterIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/unifi-network'
    },
    {
      id: 'unifiProtect',
      title: 'Unifi Protect',
      icon: <VideocamIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/unifi-protect'
    },
    {
      id: 'dreo',
      title: 'Dreo',
      icon: <AcUnitIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/dreo'
    },
    {
      id: 'mosyleBusiness',
      title: 'Mosyle Business',
      icon: <BusinessIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/mosyle-business'
    },
    {
      id: 'lenelS2NetBox',
      title: 'Lenel S2 NetBox',
      icon: <LockIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/lenel-s2-netbox'
    },
    {
      id: 'googleCalendar',
      title: 'Google Calendar',
      icon: <EventIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/google-calendar'
    },
    {
      id: 'googleDrive',
      title: 'Google Drive',
      icon: <DescriptionIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/google-drive'
    },
    {
      id: 'appleBusinessManager',
      title: 'Apple Business',
      icon: <AppleIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/apple-business-manager'
    },
    {
      id: 'buildingManagement',
      title: 'Building Mgmt',
      icon: <ApartmentIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/building-management'
    },
    {
      id: 'googleAdmin',
      title: 'Google Admin',
      icon: <AdminPanelSettingsIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/google-admin'
    },
    {
      id: 'googleForms',
      title: 'Google Forms',
      icon: <AssignmentIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/google-forms'
    },
    {
      id: 'lgThinq',
      title: 'LG ThinQ',
      icon: <LGThinqIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/lg-thinq'
    },
    {
      id: 'marketplace',
      title: 'Marketplace',
      icon: <MarketplaceIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/marketplace'
    },
    {
      id: 'people',
      title: 'People',
      icon: <GroupIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/people'
    },
    {
      id: 'radius',
      title: 'Radius',
      icon: <RadiusIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/radius'
    },
    {
      id: 'roomBooking',
      title: 'Room Booking',
      icon: <RoomBookingIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/room-booking'
    },
    {
      id: 'staffDirectory',
      title: 'Staff Directory',
      icon: <StaffDirectoryIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/staff-directory'
    },
    {
      id: 'tasks',
      title: 'Tasks',
      icon: <TasksIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/tasks'
    }
  ];

  // Filter actions based on enabled actions in settings
  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead
  const actions = allActions.filter(action => {
    // Only check if the action is enabled in settings
    return enabledActions.includes(action.id);
  });

  // Add admin action if user has admin role
  if (user?.roles.includes('admin')) {
    actions.push({
      id: 'admin',
      title: 'Admin',
      icon: <StarIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />,
      path: '/admin/users'
    });
  }

  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
    >
      <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
        <Grid container spacing={2}>
          {actions.map((action) => (
            <Grid item xs={4} sm={3} md={2} key={action.id}>
              <Card className="shortcut-card">
                <CardContent>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    {action.icon}
                    <Typography variant="h6" component="h3" align="center">
                      {action.title}
                    </Typography>
                  </Box>
                </CardContent>
                <CardActions>
                  <Button 
                    fullWidth 
                    component={RouterLink} 
                    to={action.path}
                  >
                    View
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Widget>
  );
};

export default QuickActionsWidget;
