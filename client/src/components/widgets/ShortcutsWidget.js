import React, { useState, useEffect } from 'react';
import { 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  CircularProgress, 
  Alert,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Link as LinkIcon,
  Description as FormIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import axios from 'axios';
import Widget from './Widget';
import GoogleFormsViewer from '../GoogleForms/GoogleFormsViewer';

const ShortcutsWidget = ({ id, title = 'Popular Shortcuts', onRemove, onEdit, settings = {}, widgetSize = null }) => {
  const [shortcuts, setShortcuts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState(null);
  
  // Number of shortcuts to display, default to 5 if not specified in settings
  const limit = settings.limit || 5;

  useEffect(() => {
    const fetchShortcuts = async () => {
      try {
        const res = await axios.get('/api/shortcuts');
        // Sort by click count to get most popular
        const sortedShortcuts = res.data.sort((a, b) => b.clickCount - a.clickCount);
        setShortcuts(sortedShortcuts.slice(0, limit)); // Get top N
        setLoading(false);
      } catch (err) {
        console.error('Error fetching shortcuts:', err);
        setError('Failed to load shortcuts');
        setLoading(false);
      }
    };

    fetchShortcuts();
  }, [limit]);

  // Check if a URL is a Google Form
  const isGoogleForm = (url) => {
    return url && (
      url.includes('docs.google.com/forms') || 
      url.includes('forms.gle')
    );
  };
  
  // Handle form dialog close
  const handleFormDialogClose = () => {
    setFormDialogOpen(false);
    setSelectedForm(null);
  };
  
  // Track shortcut clicks and handle Google Forms
  const handleShortcutClick = async (id, shortcut) => {
    try {
      // Track the click
      await axios.post(`/api/shortcuts/${id}/click`);
      
      // Check if this is a Google Form
      if (isGoogleForm(shortcut.url)) {
        // Open the form in the embedded viewer
        setSelectedForm({
          formUrl: shortcut.url,
          title: shortcut.title
        });
        setFormDialogOpen(true);
        return true; // Indicate that we're handling this click
      }
      
      // Not a Google Form, let the default behavior happen
      return false;
    } catch (err) {
      console.error('Error tracking shortcut click:', err);
      return false;
    }
  };

  // Determine if we're in compact mode based on widget size
  const isCompact = widgetSize && (widgetSize.xs || widgetSize.sm);
  const displayLimit = isCompact ? Math.min(limit, 3) : limit;
  
  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
      widgetSize={widgetSize}
      isCompact={isCompact}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : shortcuts.length === 0 ? (
        <Alert severity="info">No shortcuts available</Alert>
      ) : (
        <>
          <List dense={isCompact} sx={{ py: 0 }}>
            {shortcuts.slice(0, displayLimit).map((shortcut) => (
              <ListItem 
                key={shortcut._id}
                component="a"
                href={shortcut.url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={(e) => {
                  // If it's a Google Form, prevent default behavior and handle it
                  if (handleShortcutClick(shortcut._id, shortcut)) {
                    e.preventDefault();
                  }
                }}
                button
                divider={!isCompact}
                sx={{
                  py: isCompact ? 0.5 : 1,
                  px: isCompact ? 1 : 2
                }}
              >
                {!isCompact && (
                  <ListItemIcon>
                    {isGoogleForm(shortcut.url) ? <FormIcon /> : <LinkIcon />}
                  </ListItemIcon>
                )}
                <ListItemText 
                  primary={shortcut.title} 
                  secondary={!isCompact ? shortcut.description : null}
                  primaryTypographyProps={{
                    variant: isCompact ? 'body2' : 'body1',
                    noWrap: isCompact
                  }}
                />
              </ListItem>
            ))}
          </List>
          {!isCompact && (
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button 
              component={RouterLink} 
              to="/shortcuts" 
              size="small"
            >
              View All
            </Button>
            </Box>
          )}
        </>
      )}
      
      {/* Google Form Dialog */}
      <Dialog
        open={formDialogOpen}
        onClose={handleFormDialogClose}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {selectedForm?.title || 'Google Form'}
          <IconButton onClick={handleFormDialogClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {selectedForm && (
            <GoogleFormsViewer
              formUrl={selectedForm.formUrl}
              title={selectedForm.title}
              fullWidth={true}
              fullHeight={true}
            />
          )}
        </DialogContent>
      </Dialog>
    </Widget>
  );
};

export default ShortcutsWidget;