import React, { useState, useEffect } from 'react';
import { 
  List, 
  ListItem, 
  ListItemText, 
  CircularProgress, 
  Alert,
  Box,
  Button,
  Typography,
  Divider,
  Paper,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import { 
  Article as ArticleIcon,
  OpenInNew as OpenInNewIcon,
  Star as StarIcon
} from '@mui/icons-material';
import newsService from '../../services/newsService';
import Widget from './Widget';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../context/AuthContext';

const NewsWidget = ({ id, title = 'Latest News', onRemove, onEdit, settings = {} }) => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  
  // Number of posts to display, default to 5 if not specified in settings
  const limit = settings.limit || 5;
  // Show category, default to true
  const showCategory = settings.showCategory !== undefined ? settings.showCategory : true;
  // Show author, default to true
  const showAuthor = settings.showAuthor !== undefined ? settings.showAuthor : true;

  // Fetch posts on component mount and when settings change
  useEffect(() => {
    fetchPosts();
  }, [limit, showCategory, showAuthor]);

  // Fetch posts from the API
  const fetchPosts = async () => {
    try {
      setLoading(true);
      const fetchedPosts = await newsService.getLatestPosts({ limit });
      setPosts(fetchedPosts);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching news posts:', err);
      setError('Failed to load news posts');
      setLoading(false);
    }
  };

  // Handle opening a post
  const handleOpenPost = (postId) => {
    navigate(`/news/posts/${postId}`);
  };

  // Handle navigating to news management page
  const handleManageNews = () => {
    navigate('/news');
  };

  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <>
          <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>
            {posts.length === 0 ? (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No news posts available.
                  {hasPermission('news:write') && (
                    <Box mt={1}>
                      <Button 
                        variant="outlined" 
                        size="small" 
                        onClick={handleManageNews}
                      >
                        Create News Post
                      </Button>
                    </Box>
                  )}
                </Typography>
              </Box>
            ) : (
              posts.map((post) => (
                <Paper 
                  key={post._id} 
                  elevation={1} 
                  sx={{ 
                    mb: 1,
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {post.featured && (
                    <Box 
                      sx={{ 
                        position: 'absolute', 
                        top: 0, 
                        right: 0,
                        bgcolor: 'warning.main',
                        color: 'warning.contrastText',
                        p: 0.5,
                        borderBottomLeftRadius: 4
                      }}
                    >
                      <StarIcon fontSize="small" />
                    </Box>
                  )}
                  <ListItem 
                    alignItems="flex-start"
                    secondaryAction={
                      <Tooltip title="Open post">
                        <IconButton 
                          edge="end" 
                          aria-label="open" 
                          onClick={() => handleOpenPost(post._id)}
                          size="small"
                        >
                          <OpenInNewIcon />
                        </IconButton>
                      </Tooltip>
                    }
                    sx={{ pr: 6 }}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <ArticleIcon fontSize="small" sx={{ mr: 1 }} />
                          <Typography 
                            variant="subtitle1"
                            sx={{ 
                              fontWeight: post.featured ? 'bold' : 'normal'
                            }}
                          >
                            {post.title}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <>
                          {post.summary && (
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{ 
                                mt: 1,
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {post.summary}
                            </Typography>
                          )}
                          <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {showCategory && post.category && (
                              <Chip 
                                label={post.category.name} 
                                size="small" 
                                color="primary" 
                                variant="outlined"
                              />
                            )}
                            {post.publishedAt && (
                              <Typography variant="caption" color="text.secondary">
                                {formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })}
                              </Typography>
                            )}
                            {showAuthor && post.author && (
                              <Typography variant="caption" color="text.secondary">
                                by {post.author.name}
                              </Typography>
                            )}
                          </Box>
                        </>
                      }
                    />
                  </ListItem>
                </Paper>
              ))
            )}
          </List>
          
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            {posts.length > 0 && (
              <Button 
                variant="outlined" 
                size="small"
                onClick={fetchPosts}
              >
                Refresh
              </Button>
            )}
          </Box>
        </>
      )}
    </Widget>
  );
};

export default NewsWidget;