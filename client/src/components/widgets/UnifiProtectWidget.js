import React, { useState, useEffect } from 'react';
import { 
  <PERSON>rid, 
  CircularProgress, 
  Alert,
  Box,
  Button,
  Card,
  CardMedia,
  CardContent,
  Typography,
  CardActions
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { Videocam as VideocamIcon } from '@mui/icons-material';
import axios from 'axios';
import Widget from './Widget';

const UnifiProtectWidget = ({ id, title = 'Camera Feeds', onRemove, onEdit, settings = {} }) => {
  const [cameras, setCameras] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Number of cameras to display, default to 4 if not specified in settings
  const limit = settings.limit || 4;
  // Camera IDs to display, if specified
  const cameraIds = settings.cameraIds || [];

  useEffect(() => {
    const fetchCameras = async () => {
      try {
        const res = await axios.get('/api/unifi-protect/cameras');
        let filteredCameras = res.data;
        
        // Filter by camera IDs if specified
        if (cameraIds.length > 0) {
          filteredCameras = filteredCameras.filter(camera => cameraIds.includes(camera.id));
        }
        
        setCameras(filteredCameras.slice(0, limit));
        setLoading(false);
      } catch (err) {
        console.error('Error fetching cameras:', err);
        setError('Failed to load camera feeds');
        setLoading(false);
      }
    };

    fetchCameras();
  }, [limit, cameraIds]);

  // Refresh snapshot every 30 seconds
  useEffect(() => {
    if (cameras.length === 0) return;
    
    const interval = setInterval(() => {
      setCameras(prevCameras => 
        prevCameras.map(camera => ({
          ...camera,
          snapshotUrl: `${camera.snapshotUrl}?t=${Date.now()}`
        }))
      );
    }, 30000);
    
    return () => clearInterval(interval);
  }, [cameras]);

  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : cameras.length === 0 ? (
        <Alert severity="info">No cameras available</Alert>
      ) : (
        <>
          <Grid container spacing={2}>
            {cameras.map((camera) => (
              <Grid item xs={12} sm={6} key={camera.id}>
                <Card>
                  <CardMedia
                    component="img"
                    height="140"
                    image={camera.snapshotUrl}
                    alt={camera.name}
                    sx={{ objectFit: 'cover' }}
                  />
                  <CardContent sx={{ pt: 1, pb: 1 }}>
                    <Typography variant="subtitle1" component="div">
                      {camera.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {camera.state === 'CONNECTED' ? 'Online' : 'Offline'}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button 
                      size="small" 
                      component={RouterLink} 
                      to={`/unifi-protect/cameras/${camera.id}`}
                      startIcon={<VideocamIcon />}
                    >
                      View Live
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button 
              component={RouterLink} 
              to="/unifi-protect" 
              size="small"
            >
              View All Cameras
            </Button>
          </Box>
        </>
      )}
    </Widget>
  );
};

export default UnifiProtectWidget;