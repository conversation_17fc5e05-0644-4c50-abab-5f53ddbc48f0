import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardContent, 
  IconButton, 
  Menu, 
  MenuItem, 
  Typography,
  Box
} from '@mui/material';
import { 
  MoreVert as MoreVertIcon,
  Close as CloseIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

const Widget = ({ 
  id, 
  title, 
  onRemove, 
  onEdit, 
  children, 
  settings = {},
  showSettings = false,
  widgetSize = null,
  isCompact = false
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRemove = () => {
    handleClose();
    if (onRemove) onRemove(id);
  };

  const handleEdit = () => {
    handleClose();
    if (onEdit) onEdit(id);
  };

  // Determine if widget should be in compact mode based on size
  const compact = isCompact || (widgetSize && (widgetSize.xs || widgetSize.sm));
  
  return (
    <Card sx={{ 
      width: '100%', 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      <CardHeader
        action={
          <Box>
            <IconButton
              aria-label="widget menu"
              aria-controls={open ? 'widget-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleClick}
            >
              <MoreVertIcon />
            </IconButton>
            <Menu
              id="widget-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              MenuListProps={{
                'aria-labelledby': 'widget-menu-button',
              }}
            >
              {showSettings && (
                <MenuItem onClick={handleEdit}>
                  <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="inherit">Settings</Typography>
                </MenuItem>
              )}
              <MenuItem onClick={handleRemove}>
                <CloseIcon fontSize="small" sx={{ mr: 1 }} />
                <Typography variant="inherit">Remove</Typography>
              </MenuItem>
            </Menu>
          </Box>
        }
        title={title}
        titleTypographyProps={{ 
          variant: compact ? 'subtitle1' : 'h6',
          sx: { fontSize: compact ? '0.95rem' : '1.25rem' }
        }}
        sx={{ 
          pb: compact ? 1 : 2,
          '& .MuiCardHeader-action': {
            alignSelf: 'center',
            marginTop: 0,
            marginRight: 0
          }
        }}
      />
      <CardContent sx={{ 
        flexGrow: 1, 
        overflow: 'auto',
        p: compact ? 1.5 : 2,
        '&:last-child': {
          pb: compact ? 1.5 : 2
        }
      }}>
        {children}
      </CardContent>
    </Card>
  );
};

export default Widget;
