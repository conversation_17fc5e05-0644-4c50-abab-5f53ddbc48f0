import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  IconButton,
  Tooltip,
  Chip,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  ElectricalServices as ElectricalServicesIcon,
  Power as PowerOutletIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import electricalService from '../../services/electricalService';

/**
 * ElectricalWidget Component
 * Dashboard widget showing electrical system overview
 */
const ElectricalWidget = ({ 
  settings = {},
  onNavigate,
  isSmall = false 
}) => {
  const [data, setData] = useState({
    panels: 0,
    circuits: 0,
    outlets: 0,
    recentOutlets: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchElectricalData();
  }, []);

  const fetchElectricalData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [panels, outlets] = await Promise.all([
        electricalService.getPanels(),
        electricalService.getOutlets()
      ]);

      // Calculate total circuits from panels
      let totalCircuits = 0;
      for (const panel of panels) {
        try {
          const circuits = await electricalService.getCircuits(panel._id);
          totalCircuits += circuits.length;
        } catch (err) {
          console.warn('Failed to fetch circuits for panel', panel._id);
        }
      }

      setData({
        panels: panels.length,
        circuits: totalCircuits,
        outlets: outlets.length,
        recentOutlets: outlets.slice(0, 5) // Show first 5 outlets
      });

    } catch (err) {
      console.error('Error fetching electrical data:', err);
      setError('Failed to load electrical data');
    } finally {
      setLoading(false);
    }
  };

  const handleOutletSearch = () => {
    if (onNavigate) {
      onNavigate('/floor-plan?electrical=true');
    }
  };

  const formatStatValue = (value) => {
    if (value === 0) return '0';
    if (value > 999) return `${(value / 1000).toFixed(1)}k`;
    return value.toString();
  };

  if (loading) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <ElectricalServicesIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Electrical System
          </Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <ElectricalServicesIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Electrical System
          </Typography>
          <Alert severity="error" size="small">
            {error}
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isSmall) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle2">
              <ElectricalServicesIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
              Electrical
            </Typography>
            <Tooltip title="Find Outlet">
              <IconButton size="small" onClick={handleOutletSearch}>
                <SearchIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          
          <Grid container spacing={1}>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  {formatStatValue(data.panels)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Panels
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="success.main">
                  {formatStatValue(data.circuits)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Circuits
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="info.main">
                  {formatStatValue(data.outlets)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Outlets
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            <ElectricalServicesIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Electrical System
          </Typography>
          <Box>
            <Tooltip title="Find Outlet">
              <IconButton onClick={handleOutletSearch}>
                <SearchIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Refresh">
              <IconButton onClick={fetchElectricalData}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Statistics */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={4}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {data.panels}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Panels
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {data.circuits}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Circuits
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {data.outlets}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Outlets
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Quick Access */}
        {data.recentOutlets.length > 0 && (
          <>
            <Typography variant="subtitle2" gutterBottom>
              Quick Outlet Search
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {data.recentOutlets.map((outlet) => (
                <Chip
                  key={outlet._id}
                  label={outlet.label}
                  size="small"
                  icon={<PowerOutletIcon />}
                  onClick={handleOutletSearch}
                  clickable
                  variant="outlined"
                />
              ))}
            </Box>
          </>
        )}

        {data.outlets === 0 && (
          <Alert severity="info" size="small">
            No electrical data found. Consider importing data or creating demo data.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default ElectricalWidget;