import React, { useState, useEffect } from 'react';
import { 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  CircularProgress, 
  Alert,
  Box,
  Button,
  Typography,
  Chip
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Event as EventIcon,
  Schedule as ScheduleIcon,
  CalendarMonth as CalendarIcon
} from '@mui/icons-material';
import axios from 'axios';
import Widget from './Widget';
import googleCalendarService from '../../services/googleCalendarService';

const GoogleCalendarWidget = ({ id, title = 'Upcoming Events', onRemove, onEdit, settings = {} }) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Number of events to display, default to 5 if not specified in settings
  const limit = settings.limit || 5;
  // Calendar IDs to fetch events from, default to ['primary'] if not specified in settings
  const calendarIds = settings.calendarIds || ['primary'];
  // For backward compatibility with old widget settings
  if (settings.calendarId && !settings.calendarIds) {
    calendarIds.push(settings.calendarId);
  }

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        // Create an array to hold all events
        let allEvents = [];
        
        // Fetch calendar details first
        const calendarsMap = {};
        try {
          const calendars = await googleCalendarService.listCalendars();
          // Create a map of calendar ID to calendar details
          calendars.forEach(calendar => {
            calendarsMap[calendar.id] = calendar;
          });
        } catch (err) {
          console.error('Error fetching calendars:', err);
          // Continue even if we can't fetch calendar details
        }
        
        // Fetch events from each selected calendar
        for (const calendarId of calendarIds) {
          try {
            // Get events for the next 30 days
            const timeMin = new Date().toISOString();
            const timeMax = new Date();
            timeMax.setDate(timeMax.getDate() + 30);
            
            const events = await googleCalendarService.listEvents(calendarId, {
              timeMin,
              timeMax: timeMax.toISOString(),
              maxResults: 50,
              singleEvents: true,
              orderBy: 'startTime'
            });
            
            // Add calendar ID and calendar details to each event for reference
            const eventsWithCalendarInfo = events.map(event => ({
              ...event,
              calendarId,
              calendar: calendarsMap[calendarId] || null
            }));
            
            // Add events to the combined array
            allEvents = [...allEvents, ...eventsWithCalendarInfo];
          } catch (err) {
            console.error(`Error fetching events for calendar ${calendarId}:`, err);
            // Continue with other calendars even if one fails
          }
        }
        
        // Sort by start time
        const sortedEvents = allEvents.sort((a, b) => {
          const dateA = new Date(a.start.dateTime || a.start.date);
          const dateB = new Date(b.start.dateTime || b.start.date);
          return dateA - dateB;
        });
        
        // Filter out past events
        const now = new Date();
        const upcomingEvents = sortedEvents.filter(event => {
          const eventDate = new Date(event.start.dateTime || event.start.date);
          return eventDate >= now;
        });
        
        setEvents(upcomingEvents.slice(0, limit));
        setLoading(false);
      } catch (err) {
        console.error('Error fetching calendar events:', err);
        setError('Failed to load calendar events');
        setLoading(false);
      }
    };

    fetchEvents();
  }, [limit, calendarIds]);

  // Format date and time for display
  const formatDateTime = (dateTimeStr, allDay = false) => {
    if (allDay) {
      return new Date(dateTimeStr).toLocaleDateString();
    }
    
    const date = new Date(dateTimeStr);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  };

  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : events.length === 0 ? (
        <Alert severity="info">No upcoming events</Alert>
      ) : (
        <>
          <List>
            {events.map((event) => {
              const isAllDay = !event.start.dateTime;
              const startTime = formatDateTime(event.start.dateTime || event.start.date, isAllDay);
              const endTime = event.end ? formatDateTime(event.end.dateTime || event.end.date, isAllDay) : '';
              
              return (
                <ListItem 
                  key={event.id}
                  component="a"
                  href={event.htmlLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  button
                  divider
                >
                  <ListItemIcon>
                    {isAllDay ? <EventIcon /> : <ScheduleIcon />}
                  </ListItemIcon>
                  <ListItemText 
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography component="span">
                          {event.summary}
                        </Typography>
                        {event.calendarId && event.calendarId !== 'primary' && (
                          <Chip 
                            icon={<CalendarIcon />} 
                            label={event.calendar?.summary || event.calendarId}
                            size="small"
                            sx={{ 
                              backgroundColor: event.calendar?.backgroundColor || 'primary.main',
                              color: event.calendar?.foregroundColor || 'white',
                              '& .MuiChip-icon': { 
                                color: event.calendar?.foregroundColor || 'white' 
                              }
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <>
                        <Typography component="span" variant="body2">
                          {isAllDay ? 'All day: ' : 'Time: '}
                          {startTime}
                          {!isAllDay && endTime && ` - ${endTime}`}
                        </Typography>
                        {event.location && (
                          <Typography component="div" variant="body2">
                            Location: {event.location}
                          </Typography>
                        )}
                      </>
                    }
                  />
                </ListItem>
              );
            })}
          </List>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button 
              component={RouterLink} 
              to="/google-calendar" 
              size="small"
            >
              View Calendar
            </Button>
          </Box>
        </>
      )}
    </Widget>
  );
};

export default GoogleCalendarWidget;