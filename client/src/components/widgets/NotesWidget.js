import React, { useState, useEffect } from 'react';
import { 
  List, 
  ListItem, 
  ListItemText, 
  CircularProgress, 
  Alert,
  Box,
  Button,
  TextField,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  IconButton,
  Typography,
  Checkbox,
  FormControlLabel,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
  Divider,
  Paper,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PushPin as PinIcon,
  PushPinOutlined as UnpinIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as UncheckedIcon,
  Note as NoteIcon,
  Assignment as TodoIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import notesService from '../../services/notesService';
import Widget from './Widget';

const NotesWidget = ({ id, title = 'Notes', onRemove, onEdit, settings = {} }) => {
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState(null);
  const [noteFormData, setNoteFormData] = useState({
    title: '',
    content: '',
    type: 'note',
    color: '#ffffff',
    pinned: false,
    tags: [],
    categories: []
  });
  
  // Number of notes to display, default to 5 if not specified in settings
  const limit = settings.limit || 5;
  // Filter type (all, notes, todos), default to all
  const filterType = settings.filterType || 'all';
  // Show completed todos, default to true
  const showCompleted = settings.showCompleted !== undefined ? settings.showCompleted : true;

  // Fetch notes on component mount and when settings change
  useEffect(() => {
    fetchNotes();
  }, [limit, filterType, showCompleted]);

  // Fetch notes from the API
  const fetchNotes = async () => {
    try {
      setLoading(true);
      const fetchedNotes = await notesService.getUserNotes();
      
      // Apply filters
      let filteredNotes = fetchedNotes;
      
      // Filter by type
      if (filterType === 'note') {
        filteredNotes = filteredNotes.filter(note => note.type === 'note');
      } else if (filterType === 'todo') {
        filteredNotes = filteredNotes.filter(note => note.type === 'todo');
      }
      
      // Filter by completion status
      if (filterType === 'todo' && !showCompleted) {
        filteredNotes = filteredNotes.filter(note => !note.completed);
      }
      
      // Sort by pinned status and update time
      filteredNotes.sort((a, b) => {
        if (a.pinned && !b.pinned) return -1;
        if (!a.pinned && b.pinned) return 1;
        return new Date(b.updatedAt) - new Date(a.updatedAt);
      });
      
      // Limit the number of notes
      setNotes(filteredNotes.slice(0, limit));
      setLoading(false);
    } catch (err) {
      console.error('Error fetching notes:', err);
      setError('Failed to load notes');
      setLoading(false);
    }
  };

  // Handle opening the note dialog for creating a new note
  const handleAddNoteOpen = () => {
    setCurrentNote(null);
    setNoteFormData({
      title: '',
      content: '',
      type: 'note',
      color: '#ffffff',
      pinned: false,
      tags: [],
      categories: []
    });
    setNoteDialogOpen(true);
  };

  // Handle opening the note dialog for editing an existing note
  const handleEditNoteOpen = (note) => {
    setCurrentNote(note);
    setNoteFormData({
      title: note.title,
      content: note.content || '',
      type: note.type,
      color: note.color,
      pinned: note.pinned
    });
    setNoteDialogOpen(true);
  };

  // Handle closing the note dialog
  const handleNoteDialogClose = () => {
    setNoteDialogOpen(false);
    setCurrentNote(null);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;
    setNoteFormData({
      ...noteFormData,
      [name]: name === 'pinned' ? checked : value
    });
  };

  // Handle form submission
  const handleNoteSubmit = async () => {
    try {
      if (currentNote) {
        // Update existing note
        await notesService.updateNote(currentNote._id, noteFormData);
      } else {
        // Create new note
        await notesService.createNote(noteFormData);
      }
      
      // Refresh notes
      fetchNotes();
      handleNoteDialogClose();
    } catch (err) {
      console.error('Error saving note:', err);
      setError('Failed to save note');
    }
  };

  // Handle note deletion
  const handleDeleteNote = async (noteId) => {
    try {
      await notesService.deleteNote(noteId);
      fetchNotes();
    } catch (err) {
      console.error('Error deleting note:', err);
      setError('Failed to delete note');
    }
  };

  // Handle toggling note completion status
  const handleToggleCompletion = async (noteId) => {
    try {
      await notesService.toggleNoteCompletion(noteId);
      fetchNotes();
    } catch (err) {
      console.error('Error toggling note completion:', err);
      setError('Failed to update note');
    }
  };

  // Handle toggling note pinned status
  const handleTogglePinned = async (noteId) => {
    try {
      await notesService.toggleNotePinned(noteId);
      fetchNotes();
    } catch (err) {
      console.error('Error toggling note pinned status:', err);
      setError('Failed to update note');
    }
  };

  return (
    <Widget 
      id={id} 
      title={title} 
      onRemove={onRemove} 
      onEdit={onEdit}
      settings={settings}
      showSettings={true}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <>
          <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>
            {notes.length === 0 ? (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No notes available. Click the "+" button to create one.
                </Typography>
              </Box>
            ) : (
              notes.map((note) => (
                <Paper 
                  key={note._id} 
                  elevation={1} 
                  sx={{ 
                    mb: 1, 
                    backgroundColor: note.color,
                    border: note.pinned ? '1px solid #aaa' : 'none'
                  }}
                >
                  <ListItem 
                    alignItems="flex-start"
                    secondaryAction={
                      <Box>
                        {note.type === 'todo' && (
                          <Tooltip title={note.completed ? "Mark as incomplete" : "Mark as complete"}>
                            <IconButton 
                              edge="end" 
                              aria-label="toggle completion" 
                              onClick={() => handleToggleCompletion(note._id)}
                              size="small"
                            >
                              {note.completed ? <CheckCircleIcon color="success" /> : <UncheckedIcon />}
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title={note.pinned ? "Unpin note" : "Pin note"}>
                          <IconButton 
                            edge="end" 
                            aria-label="toggle pinned" 
                            onClick={() => handleTogglePinned(note._id)}
                            size="small"
                          >
                            {note.pinned ? <PinIcon /> : <UnpinIcon />}
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit note">
                          <IconButton 
                            edge="end" 
                            aria-label="edit" 
                            onClick={() => handleEditNoteOpen(note)}
                            size="small"
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete note">
                          <IconButton 
                            edge="end" 
                            aria-label="delete" 
                            onClick={() => handleDeleteNote(note._id)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {note.type === 'todo' ? <TodoIcon fontSize="small" sx={{ mr: 1 }} /> : <NoteIcon fontSize="small" sx={{ mr: 1 }} />}
                          <Typography 
                            variant="subtitle1"
                            sx={{ 
                              textDecoration: note.type === 'todo' && note.completed ? 'line-through' : 'none',
                              fontWeight: note.pinned ? 'bold' : 'normal'
                            }}
                          >
                            {note.title}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        note.content && (
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ 
                              mt: 1,
                              textDecoration: note.type === 'todo' && note.completed ? 'line-through' : 'none',
                              display: '-webkit-box',
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden'
                            }}
                          >
                            {note.content}
                          </Typography>
                        )
                      }
                    />
                  </ListItem>
                </Paper>
              ))
            )}
          </List>
          
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Button 
              variant="contained" 
              color="primary" 
              startIcon={<AddIcon />}
              onClick={handleAddNoteOpen}
              size="small"
            >
              Add Note
            </Button>
            
            {notes.length > 0 && (
              <Button 
                variant="outlined" 
                size="small"
                onClick={fetchNotes}
              >
                Refresh
              </Button>
            )}
          </Box>
          
          {/* Note Dialog */}
          <Dialog
            open={noteDialogOpen}
            onClose={handleNoteDialogClose}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {currentNote ? 'Edit Note' : 'New Note'}
              <IconButton
                aria-label="close"
                onClick={handleNoteDialogClose}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8
                }}
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent>
              <Box component="form" sx={{ mt: 1 }}>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="title"
                  label="Title"
                  name="title"
                  value={noteFormData.title}
                  onChange={handleInputChange}
                />
                
                <TextField
                  margin="normal"
                  fullWidth
                  id="content"
                  label="Content"
                  name="content"
                  multiline
                  rows={4}
                  value={noteFormData.content}
                  onChange={handleInputChange}
                />
                
                <Box sx={{ display: 'flex', mt: 2, mb: 2 }}>
                  <FormControl sx={{ mr: 2, minWidth: 120 }}>
                    <InputLabel id="type-label">Type</InputLabel>
                    <Select
                      labelId="type-label"
                      id="type"
                      name="type"
                      value={noteFormData.type}
                      label="Type"
                      onChange={handleInputChange}
                    >
                      <MenuItem value="note">Note</MenuItem>
                      <MenuItem value="todo">To-Do</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl sx={{ mr: 2, minWidth: 120 }}>
                    <InputLabel id="color-label">Color</InputLabel>
                    <Select
                      labelId="color-label"
                      id="color"
                      name="color"
                      value={noteFormData.color}
                      label="Color"
                      onChange={handleInputChange}
                    >
                      <MenuItem value="#ffffff">White</MenuItem>
                      <MenuItem value="#f8f9fa">Light Gray</MenuItem>
                      <MenuItem value="#fff8e1">Yellow</MenuItem>
                      <MenuItem value="#e8f5e9">Green</MenuItem>
                      <MenuItem value="#e3f2fd">Blue</MenuItem>
                      <MenuItem value="#fce4ec">Pink</MenuItem>
                      <MenuItem value="#f3e5f5">Purple</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={noteFormData.pinned}
                        onChange={handleInputChange}
                        name="pinned"
                      />
                    }
                    label="Pin note"
                  />
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleNoteDialogClose}>Cancel</Button>
              <Button 
                onClick={handleNoteSubmit} 
                variant="contained"
                disabled={!noteFormData.title}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}
    </Widget>
  );
};

export default NotesWidget;