import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  Typography,
  Divider,
  Slider,
  Chip,
  OutlinedInput,
  ListItemText,
  CircularProgress
} from '@mui/material';
import axios from 'axios';
import { widgetTypes } from './WidgetRegistry';

const WidgetSettingsForm = ({ widgetType, initialSettings, onChange }) => {
  const [settings, setSettings] = useState(initialSettings || {});
  const [errors, setErrors] = useState({});
  const [asyncOptions, setAsyncOptions] = useState({});
  const [loadingOptions, setLoadingOptions] = useState({});

  // Find the widget type definition
  const widgetTypeDefinition = widgetTypes.find(w => w.type === widgetType);
  const settingsSchema = widgetTypeDefinition?.settingsSchema || {};

  useEffect(() => {
    // Initialize settings with default values if not provided
    if (!initialSettings && widgetTypeDefinition) {
      setSettings(widgetTypeDefinition.defaultSettings || {});
    }
  }, [initialSettings, widgetTypeDefinition]);

  // Fetch async options for fields that have async: true
  useEffect(() => {
    const fetchAsyncOptions = async () => {
      // Find all fields with async: true
      const asyncFields = Object.entries(settingsSchema)
        .filter(([_, schema]) => schema.async === true && schema.asyncOptionsUrl);
      
      // For each async field, fetch options
      for (const [key, schema] of asyncFields) {
        try {
          setLoadingOptions(prev => ({ ...prev, [key]: true }));
          
          // Fetch options from the specified URL
          const response = await axios.get(schema.asyncOptionsUrl);
          
          // Map the response data to options format if mapping is provided
          let options = response.data;
          if (schema.asyncOptionMapping) {
            options = options.map(item => ({
              value: item[schema.asyncOptionMapping.value],
              label: item[schema.asyncOptionMapping.label]
            }));
          }
          
          // Store the options in state
          setAsyncOptions(prev => ({ ...prev, [key]: options }));
        } catch (error) {
          console.error(`Error fetching options for ${key}:`, error);
        } finally {
          setLoadingOptions(prev => ({ ...prev, [key]: false }));
        }
      }
    };

    if (widgetTypeDefinition) {
      fetchAsyncOptions();
    }
  }, [settingsSchema, widgetTypeDefinition]);

  // Handle changes to settings
  const handleSettingChange = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    
    // Validate the new value
    validateSetting(key, value);
    
    // Notify parent component of changes
    if (onChange) {
      onChange(newSettings);
    }
  };

  // Validate a setting value
  const validateSetting = (key, value) => {
    const schema = settingsSchema[key];
    let error = null;

    if (schema) {
      if (schema.type === 'number') {
        if (schema.min !== undefined && value < schema.min) {
          error = `Minimum value is ${schema.min}`;
        } else if (schema.max !== undefined && value > schema.max) {
          error = `Maximum value is ${schema.max}`;
        }
      } else if (schema.type === 'text' && schema.required && !value) {
        error = 'This field is required';
      }
    }

    setErrors(prev => ({ ...prev, [key]: error }));
    return !error;
  };

  // Render a form field based on its schema
  const renderField = (key, schema) => {
    const value = settings[key] !== undefined ? settings[key] : '';
    const error = errors[key];

    switch (schema.type) {
      case 'text':
        return (
          <TextField
            key={key}
            fullWidth
            margin="normal"
            id={`setting-${key}`}
            label={schema.label}
            value={value}
            onChange={(e) => handleSettingChange(key, e.target.value)}
            error={!!error}
            helperText={error || schema.helperText}
            placeholder={schema.placeholder}
          />
        );

      case 'number':
        return (
          <TextField
            key={key}
            fullWidth
            margin="normal"
            id={`setting-${key}`}
            label={schema.label}
            type="number"
            value={value}
            onChange={(e) => {
              const numValue = e.target.value === '' ? '' : Number(e.target.value);
              handleSettingChange(key, numValue);
            }}
            error={!!error}
            helperText={error || schema.helperText}
            InputProps={{
              inputProps: { 
                min: schema.min, 
                max: schema.max,
                step: schema.step || 1
              }
            }}
          />
        );

      case 'boolean':
        return (
          <FormGroup key={key}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={!!value}
                  onChange={(e) => handleSettingChange(key, e.target.checked)}
                  name={key}
                />
              }
              label={schema.label}
            />
            {schema.helperText && (
              <FormHelperText>{schema.helperText}</FormHelperText>
            )}
          </FormGroup>
        );

      case 'select':
        return (
          <FormControl key={key} fullWidth margin="normal" error={!!error}>
            <InputLabel id={`setting-${key}-label`}>{schema.label}</InputLabel>
            <Select
              labelId={`setting-${key}-label`}
              id={`setting-${key}`}
              value={value}
              label={schema.label}
              onChange={(e) => handleSettingChange(key, e.target.value)}
            >
              {schema.options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(error || schema.helperText) && (
              <FormHelperText>{error || schema.helperText}</FormHelperText>
            )}
          </FormControl>
        );

      case 'multiselect':
        // Determine which options to use - async options if available, otherwise schema options
        const isAsyncField = schema.async === true && schema.asyncOptionsUrl;
        const isLoading = loadingOptions[key];
        const options = isAsyncField ? (asyncOptions[key] || []) : schema.options;
        
        return (
          <FormControl key={key} fullWidth margin="normal" error={!!error}>
            <InputLabel id={`setting-${key}-label`}>{schema.label}</InputLabel>
            <Select
              labelId={`setting-${key}-label`}
              id={`setting-${key}`}
              multiple
              value={Array.isArray(value) ? value : []}
              onChange={(e) => handleSettingChange(key, e.target.value)}
              input={<OutlinedInput label={schema.label} />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const option = options.find(opt => opt.value === value);
                    return (
                      <Chip key={value} label={option ? option.label : value} />
                    );
                  })}
                </Box>
              )}
              disabled={isLoading}
            >
              {isLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                  <CircularProgress size={24} />
                </Box>
              ) : options.length === 0 ? (
                <MenuItem disabled>
                  <ListItemText primary="No options available" />
                </MenuItem>
              ) : (
                options.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Checkbox checked={Array.isArray(value) && value.indexOf(option.value) > -1} />
                    <ListItemText primary={option.label} />
                  </MenuItem>
                ))
              )}
            </Select>
            {(error || schema.helperText) && (
              <FormHelperText>{error || schema.helperText}</FormHelperText>
            )}
          </FormControl>
        );

      case 'slider':
        return (
          <Box key={key} sx={{ width: '100%', mt: 3, mb: 1 }}>
            <Typography id={`setting-${key}-label`} gutterBottom>
              {schema.label}
            </Typography>
            <Slider
              value={typeof value === 'number' ? value : schema.min || 0}
              onChange={(e, newValue) => handleSettingChange(key, newValue)}
              aria-labelledby={`setting-${key}-label`}
              valueLabelDisplay="auto"
              step={schema.step || 1}
              marks={schema.marks}
              min={schema.min}
              max={schema.max}
            />
            {(error || schema.helperText) && (
              <FormHelperText error={!!error}>{error || schema.helperText}</FormHelperText>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  if (!widgetTypeDefinition) {
    return (
      <Typography color="error">
        Widget type '{widgetType}' not found
      </Typography>
    );
  }

  return (
    <Box>
      {Object.entries(settingsSchema).map(([key, schema]) => (
        <React.Fragment key={key}>
          {renderField(key, schema)}
        </React.Fragment>
      ))}
    </Box>
  );
};

export default WidgetSettingsForm;