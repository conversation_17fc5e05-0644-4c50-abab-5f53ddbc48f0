import React, { useState, useEffect, useRef } from 'react';
import { 
  Typography, 
  Box, 
  IconButton, 
  CircularProgress,
  Divider,
  CardMedia,
  CardActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  useTheme,
  useMediaQuery
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import wiimService from '../../services/wiimService';
import { Link } from 'react-router-dom';
import Widget from './Widget';

const WiimWidget = ({ id, title, settings, onRemove, onEdit, widgetBreakpoints }) => {
  const theme = useTheme();
  // Always evaluate media queries via hooks; override with widgetBreakpoints when provided
  const screenIsSmall = useMediaQuery(theme.breakpoints.down('sm'));
  const screenIsMedium = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const screenIsDesktop = useMediaQuery(theme.breakpoints.up('md'));

  const isSmall = widgetBreakpoints ? !!(widgetBreakpoints.xs || widgetBreakpoints.sm) : screenIsSmall;
  const isMedium = widgetBreakpoints ? !!widgetBreakpoints.md : screenIsMedium;
  const isDesktop = widgetBreakpoints ? !!(widgetBreakpoints.lg || widgetBreakpoints.xl) : screenIsDesktop;
  const innerContainerRef = useRef(null);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [playbackStatus, setPlaybackStatus] = useState(null);
  const [spotifyQueue, setSpotifyQueue] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  // Playlist state
  const [playlist, setPlaylist] = useState(null);
  const [playlistLoading, setPlaylistLoading] = useState(false);

  // Store the configuration status
  const [configStatus, setConfigStatus] = useState(null);
  
  // No longer need to measure container width - using MUI breakpoints instead

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status (only once during initialization)
        const config = await wiimService.getConfig();
        setConfigStatus(config);
        
        if (config) {
          // Get playback status
          const playbackData = await wiimService.getPlaybackStatus();
          setPlaybackStatus(playbackData);
          
          // Get Spotify queue
          try {
            const queueData = await wiimService.getSpotifyQueue();
            setSpotifyQueue(queueData);
          } catch (spotifyErr) {
            console.error('Error fetching Spotify queue:', spotifyErr);
            // Don't set an error, just log it - we'll fall back to playbackStatus
          }
          
          // Set UI state based on playback status
          if (playbackData) {
            setIsPlaying(playbackData.status === 'play');
          }
          
          // Always load playlist data
          fetchPlaylist();
        }
      } catch (err) {
        console.error('Error fetching WiiM data:', err);
        setError('Failed to load WiiM data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Set up polling for playback status updates
    const intervalId = setInterval(async () => {
      try {
        // Only fetch playback data if we have a valid config
        if (configStatus) {
          const playbackData = await wiimService.getPlaybackStatus();
          setPlaybackStatus(playbackData);
          
          // Get Spotify queue
          try {
            const queueData = await wiimService.getSpotifyQueue();
            setSpotifyQueue(queueData);
          } catch (spotifyErr) {
            console.error('Error updating Spotify queue:', spotifyErr);
            // Don't set an error, just log it - we'll fall back to playbackStatus
          }
          
          // Update UI state based on playback status
          if (playbackData) {
            setIsPlaying(playbackData.status === 'play');
          }
        }
      } catch (err) {
        console.error('Error updating playback status:', err);
      }
    }, 10000); // Update every 10 seconds

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  const handlePlayPause = async () => {
    try {
      if (isPlaying) {
        await wiimService.pause();
        setIsPlaying(false);
      } else {
        await wiimService.play();
        setIsPlaying(true);
      }
    } catch (err) {
      console.error('Error controlling playback:', err);
      setError('Failed to control playback');
    }
  };

  const handleNext = async () => {
    try {
      await wiimService.next();
    } catch (err) {
      console.error('Error skipping to next track:', err);
      setError('Failed to skip to next track');
    }
  };

  const handlePrevious = async () => {
    try {
      await wiimService.previous();
    } catch (err) {
      console.error('Error going to previous track:', err);
      setError('Failed to go to previous track');
    }
  };
  
  // Fetch current playlist
  const fetchPlaylist = async () => {
    try {
      setPlaylistLoading(true);
      const playlistData = await wiimService.getPlaylist();
      setPlaylist(playlistData);
    } catch (err) {
      console.error('Error fetching playlist:', err);
    } finally {
      setPlaylistLoading(false);
    }
  };
  
  // Fetch current queue
  const fetchQueue = async () => {
    try {
      setPlaylistLoading(true); // Reuse the loading state
      const queueData = await wiimService.getSpotifyQueue();
      setSpotifyQueue(queueData);
    } catch (err) {
      console.error('Error fetching queue:', err);
    } finally {
      setPlaylistLoading(false);
    }
  };
  
  // Play a specific track from the playlist
  const handlePlayTrack = async (index) => {
    try {
      await wiimService.playTrack(index);
      setIsPlaying(true);
    } catch (err) {
      console.error(`Error playing track at index ${index}:`, err);
    }
  };


  if (loading) {
    return (
      <Widget
        id={id}
        title={title || 'CSF Building Music'}
        onRemove={onRemove}
        onEdit={onEdit}
        settings={settings}
        showSettings={!!onEdit}
      >
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      </Widget>
    );
  }

  if (error) {
    return (
      <Widget
        id={id}
        title={title || 'CSF Building Music'}
        onRemove={onRemove}
        onEdit={onEdit}
        settings={settings}
        showSettings={!!onEdit}
      >
        <Typography color="error">{error}</Typography>
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2">
            <Link to="/wiim/setup">Configure WiiM</Link>
          </Typography>
        </Box>
      </Widget>
    );
  }

  if (!playbackStatus) {
    return (
      <Widget
        id={id}
        title={title || 'CSF Building Music'}
        onRemove={onRemove}
        onEdit={onEdit}
        settings={settings}
        showSettings={!!onEdit}
      >
        <Typography>No playback information available</Typography>
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2">
            <Link to="/wiim">Open WiiM Control</Link>
          </Typography>
        </Box>
      </Widget>
    );
  }

  // Determine layout based on MUI breakpoints - show side by side on desktop width
  const showSideBySide = isDesktop; // Using the isDesktop media query (md breakpoint in MUI)
  
  // Render the player section
  const renderPlayer = () => (
    <Box sx={{ 
      width: showSideBySide ? '50%' : '100%', 
      pr: showSideBySide ? 2 : 0,
      mb: 0
    }}>
      {(spotifyQueue?.currently_playing || (playbackStatus?.title && playbackStatus?.title !== 'Unknown')) ? (
        <Box sx={{ display: 'flex', mb: 2 }}>
          <CardMedia
            component="img"
            sx={{ width: 80, height: 80 }}
            image={spotifyQueue?.currently_playing?.album?.images?.[0]?.url || playbackStatus?.albumart || 'https://via.placeholder.com/80'}
            alt={spotifyQueue?.currently_playing?.name || playbackStatus?.title || 'Album Art'}
          />
          <Box sx={{ ml: 2, flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center', overflow: 'hidden' }}>
            <Typography variant="subtitle1" sx={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
              {spotifyQueue?.currently_playing?.name || playbackStatus?.title}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
              {(spotifyQueue?.currently_playing?.artists && Array.isArray(spotifyQueue.currently_playing.artists) ? 
                spotifyQueue.currently_playing.artists.map(a => a?.name || 'Unknown Artist').join(', ') : 
                null) || playbackStatus?.artist || 'Unknown Artist'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>
              {spotifyQueue?.currently_playing?.album?.name || playbackStatus?.album || 'Unknown Album'}
            </Typography>
          </Box>
        </Box>
      ) : (
        <Box sx={{ display: 'flex', mb: 2, justifyContent: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Not currently playing anything
          </Typography>
        </Box>
      )}

      <Divider sx={{ my: 1 }} />

      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 1 }}>
        <IconButton aria-label="previous" onClick={handlePrevious}>
          <SkipPreviousIcon />
        </IconButton>
        <IconButton aria-label="play/pause" onClick={handlePlayPause}>
          {isPlaying ? <PauseIcon sx={{ height: 38, width: 38 }} /> : <PlayArrowIcon sx={{ height: 38, width: 38 }} />}
        </IconButton>
        <IconButton aria-label="next" onClick={handleNext}>
          <SkipNextIcon />
        </IconButton>
      </Box>
    </Box>
  );
  
  // Render the queue section
  const renderQueue = () => (
    <Box sx={{ 
      width: showSideBySide ? '50%' : '100%',
      pl: showSideBySide ? 2 : 0,
      borderLeft: showSideBySide ? 1 : 0,
      borderColor: 'divider',
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column'
    }}>
      <Typography variant="h6" sx={{ mb: 2 }}>Current Queue</Typography>
      
      {playlistLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : spotifyQueue && spotifyQueue.queue && Array.isArray(spotifyQueue.queue) && spotifyQueue.queue.length > 0 ? (
        <List dense sx={{ maxHeight: 200, overflow: 'auto', flex: 1 }}>
          {spotifyQueue.queue.map((track, index) => (
            <ListItem 
              key={index}
              selected={playbackStatus?.index === index}
            >
              <ListItemIcon>
                <MusicNoteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText 
                primary={track.name || 'Unknown Track'} 
                secondary={(track.artists && Array.isArray(track.artists) ? 
                  track.artists.map(a => a?.name || 'Unknown Artist').join(', ') : 
                  'Unknown Artist')} 
                primaryTypographyProps={{ noWrap: true }}
                secondaryTypographyProps={{ noWrap: true }}
              />
            </ListItem>
          ))}
        </List>
      ) : (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No tracks in current queue
          </Typography>
        </Box>
      )}
      <Box sx={{ mt: 'auto', p: 1, textAlign: 'center' }}>
        <Button size="small" onClick={fetchQueue} disabled={playlistLoading}>
          Refresh Queue
        </Button>
      </Box>
    </Box>
  );
  
  return (
    <Widget
      id={id}
      title={title || 'CSF Building Music'}
      onRemove={onRemove}
      onEdit={onEdit}
      settings={settings}
      showSettings={!!onEdit}
    >
      <Box ref={innerContainerRef} sx={{ width: '100%' }}>
        <Box sx={{ display: 'flex', flexDirection: 'row' }}>
          {renderPlayer()}
          {showSideBySide && renderQueue()}
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <Typography variant="body2">
            <Link to="/wiim">Open WiiM Control</Link>
          </Typography>
        </Box>
      </Box>
    </Widget>
  );
};

export default WiimWidget;