import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Fab,
  Badge,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  ReportProblem as EmergencyIcon,
  Build as BuildIcon,
  Analytics as AnalyticsIcon,
  GetApp as ExportIcon,
  Sync as SyncIcon,
  Map as MapIcon,
  Phone as PhoneIcon,
  Info as InfoIcon,
  Assignment as InspectionIcon
} from '@mui/icons-material';
import utilityShutoffService from '../../services/utilityShutoffService';

/**
 * UtilityShutoffManagement - Administrative interface for utility shutoff management
 * Provides comprehensive CRUD operations, inspection tracking, and emergency management
 */
const UtilityShutoffManagement = ({ 
  buildingId, 
  floorId, 
  compactView = false,
  onShutoffSelect = null 
}) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [shutoffs, setShutoffs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  
  // Filters
  const [filters, setFilters] = useState({
    search: '',
    shutoffType: '',
    utilityType: '',
    priority: '',
    status: '',
    condition: ''
  });

  // Dialog states
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [inspectionDialogOpen, setInspectionDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedShutoff, setSelectedShutoff] = useState(null);
  const [editingShutoff, setEditingShutoff] = useState(null);

  // Analytics data
  const [analytics, setAnalytics] = useState(null);
  const [inspectionsDue, setInspectionsDue] = useState(null);

  // Get shutoff types and utility types for filters
  const shutoffTypes = utilityShutoffService.getShutoffTypes();
  const utilityTypes = utilityShutoffService.getUtilityTypes();
  const priorityLevels = utilityShutoffService.getPriorityLevels();
  const shutoffStates = utilityShutoffService.getShutoffStates();
  const conditionLevels = utilityShutoffService.getConditionLevels();

  // Load data on component mount and when filters change
  useEffect(() => {
    loadShutoffs();
  }, [buildingId, floorId, page, rowsPerPage, filters]);

  // Load analytics data when tab changes to analytics
  useEffect(() => {
    if (currentTab === 2) {
      loadAnalytics();
      loadInspectionsDue();
    }
  }, [currentTab, buildingId]);

  const loadShutoffs = async () => {
    setLoading(true);
    setError(null);
    try {
      const params = {
        buildingId,
        floorId,
        page: page + 1,
        limit: rowsPerPage,
        ...filters
      };

      const data = await utilityShutoffService.getUtilityShutoffs(params);
      setShutoffs(data.shutoffs || []);
      setTotalCount(data.pagination?.total || 0);
    } catch (err) {
      console.error('Error loading shutoffs:', err);
      setError('Failed to load utility shutoffs');
    } finally {
      setLoading(false);
    }
  };

  const loadAnalytics = async () => {
    try {
      const data = await utilityShutoffService.getAnalyticsSummary({ buildingId });
      setAnalytics(data);
    } catch (err) {
      console.error('Error loading analytics:', err);
    }
  };

  const loadInspectionsDue = async () => {
    try {
      const data = await utilityShutoffService.getInspectionsDue(30);
      setInspectionsDue(data);
    } catch (err) {
      console.error('Error loading inspections due:', err);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0); // Reset to first page when filter changes
  };

  const handleEdit = (shutoff) => {
    setEditingShutoff({ ...shutoff });
    setEditDialogOpen(true);
  };

  const handleDelete = (shutoff) => {
    setSelectedShutoff(shutoff);
    setDeleteDialogOpen(true);
  };

  const handleView = (shutoff) => {
    setSelectedShutoff(shutoff);
    setDetailsDialogOpen(true);
  };

  const handleInspection = (shutoff) => {
    setSelectedShutoff(shutoff);
    setInspectionDialogOpen(true);
  };

  const handleSave = async () => {
    if (!editingShutoff) return;

    try {
      if (editingShutoff._id) {
        await utilityShutoffService.updateUtilityShutoff(editingShutoff._id, editingShutoff);
      } else {
        await utilityShutoffService.createUtilityShutoff(editingShutoff);
      }
      setEditDialogOpen(false);
      setEditingShutoff(null);
      await loadShutoffs();
    } catch (err) {
      console.error('Error saving shutoff:', err);
      setError('Failed to save utility shutoff');
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedShutoff) return;

    try {
      await utilityShutoffService.deleteUtilityShutoff(selectedShutoff._id);
      setDeleteDialogOpen(false);
      setSelectedShutoff(null);
      await loadShutoffs();
    } catch (err) {
      console.error('Error deleting shutoff:', err);
      setError('Failed to delete utility shutoff');
    }
  };

  const handleInspectionSubmit = async (inspectionData) => {
    if (!selectedShutoff) return;

    try {
      await utilityShutoffService.recordInspection(selectedShutoff._id, inspectionData);
      setInspectionDialogOpen(false);
      setSelectedShutoff(null);
      await loadShutoffs();
    } catch (err) {
      console.error('Error recording inspection:', err);
      setError('Failed to record inspection');
    }
  };

  const handleSeedDemoData = async () => {
    try {
      await utilityShutoffService.seedDemoData(buildingId, floorId, false);
      await loadShutoffs();
    } catch (err) {
      console.error('Error seeding demo data:', err);
      setError('Failed to create demo data');
    }
  };

  const formatShutoffForDisplay = (shutoff) => {
    return utilityShutoffService.formatShutoffDisplay(shutoff);
  };

  const renderFilters = () => (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={6} md={3}>
          <TextField
            fullWidth
            size="small"
            label="Search"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Name, ID, room..."
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Utility Type</InputLabel>
            <Select
              value={filters.utilityType}
              onChange={(e) => handleFilterChange('utilityType', e.target.value)}
              label="Utility Type"
            >
              <MenuItem value="">All</MenuItem>
              {utilityTypes.map(type => (
                <MenuItem key={type.value} value={type.value}>
                  {type.icon} {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Priority</InputLabel>
            <Select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              label="Priority"
            >
              <MenuItem value="">All</MenuItem>
              {priorityLevels.map(level => (
                <MenuItem key={level.value} value={level.value}>
                  {level.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              label="Status"
            >
              <MenuItem value="">All</MenuItem>
              {shutoffStates.map(state => (
                <MenuItem key={state.value} value={state.value}>
                  {state.icon} {state.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              size="small"
              onClick={() => setFilters({
                search: '',
                shutoffType: '',
                utilityType: '',
                priority: '',
                status: '',
                condition: ''
              })}
            >
              Clear
            </Button>
            <Button
              variant="contained"
              size="small"
              startIcon={<SyncIcon />}
              onClick={loadShutoffs}
            >
              Refresh
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );

  const renderShutoffTable = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Shutoff</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Location</TableCell>
            <TableCell>Priority</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Condition</TableCell>
            <TableCell>Next Inspection</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {shutoffs.map((shutoff) => {
            const formatted = formatShutoffForDisplay(shutoff);
            const isInspectionOverdue = shutoff.isInspectionOverdue;
            
            return (
              <TableRow key={shutoff._id} hover>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {formatted.typeIcon} {shutoff.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {shutoff.shutoffId}
                    </Typography>
                  </Box>
                </TableCell>
                
                <TableCell>
                  <Chip 
                    label={formatted.typeLabel}
                    size="small"
                    sx={{ backgroundColor: formatted.utilityColor, color: 'white' }}
                  />
                </TableCell>
                
                <TableCell>
                  <Typography variant="body2">
                    {shutoff.room || 'Not specified'}
                  </Typography>
                </TableCell>
                
                <TableCell>
                  <Chip 
                    label={shutoff.coverage?.priority || 'medium'}
                    size="small"
                    sx={{ 
                      backgroundColor: formatted.priorityColor,
                      color: 'white'
                    }}
                  />
                </TableCell>
                
                <TableCell>
                  <Chip 
                    label={shutoff.status?.currentState || 'unknown'}
                    size="small"
                    sx={{ 
                      backgroundColor: formatted.stateColor,
                      color: 'white'
                    }}
                  />
                </TableCell>
                
                <TableCell>
                  <Chip 
                    label={shutoff.status?.condition || 'good'}
                    size="small"
                    color={shutoff.status?.condition === 'poor' ? 'error' : 'default'}
                  />
                </TableCell>
                
                <TableCell>
                  {shutoff.maintenance?.nextInspection ? (
                    <Box>
                      <Typography 
                        variant="caption" 
                        color={isInspectionOverdue ? 'error' : 'text.secondary'}
                      >
                        {new Date(shutoff.maintenance.nextInspection).toLocaleDateString()}
                      </Typography>
                      {isInspectionOverdue && (
                        <Chip 
                          label="OVERDUE" 
                          size="small" 
                          color="error"
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  ) : (
                    <Typography variant="caption" color="text.secondary">
                      Not scheduled
                    </Typography>
                  )}
                </TableCell>
                
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <Tooltip title="View Details">
                      <IconButton size="small" onClick={() => handleView(shutoff)}>
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton size="small" onClick={() => handleEdit(shutoff)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Record Inspection">
                      <IconButton 
                        size="small" 
                        onClick={() => handleInspection(shutoff)}
                        color={isInspectionOverdue ? 'error' : 'default'}
                      >
                        <InspectionIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton size="small" onClick={() => handleDelete(shutoff)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={(event, newPage) => setPage(newPage)}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
        rowsPerPageOptions={[5, 10, 25, 50]}
      />
    </TableContainer>
  );

  const renderAnalytics = () => (
    <Grid container spacing={3}>
      {/* Summary Cards */}
      {analytics && (
        <>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="primary">
                  {analytics.summary.totalShutoffs}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Shutoffs
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="error">
                  {analytics.summary.overdueInspections}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Overdue Inspections
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="warning.main">
                  {analytics.summary.needsAttention}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Needs Attention
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h4" color="success.main">
                  {Object.values(analytics.activity.recent).reduce((sum, count) => sum + count, 0)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Recent Activity ({analytics.activity.timeframe})
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </>
      )}

      {/* Inspection Schedule */}
      {inspectionsDue && (
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <ScheduleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Inspection Schedule
              </Typography>
              
              {inspectionsDue.overdue.length > 0 && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">
                    {inspectionsDue.overdue.length} Overdue Inspections
                  </Typography>
                  <List dense>
                    {inspectionsDue.overdue.slice(0, 5).map(shutoff => (
                      <ListItem key={shutoff._id} sx={{ py: 0.5 }}>
                        <ListItemIcon>
                          <WarningIcon color="error" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={shutoff.name}
                          secondary={`Due: ${new Date(shutoff.maintenance.nextInspection).toLocaleDateString()}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Alert>
              )}
              
              {inspectionsDue.upcoming.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Upcoming Inspections (Next 30 Days)
                  </Typography>
                  <List dense>
                    {inspectionsDue.upcoming.slice(0, 5).map(shutoff => (
                      <ListItem key={shutoff._id} sx={{ py: 0.5 }}>
                        <ListItemIcon>
                          <CheckIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={shutoff.name}
                          secondary={`Due: ${new Date(shutoff.maintenance.nextInspection).toLocaleDateString()}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      )}

      {/* Distribution Charts - simplified for this implementation */}
      {analytics && (
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Distribution by Type
              </Typography>
              <Grid container spacing={2}>
                {Object.entries(analytics.distribution.byType).map(([type, count]) => {
                  const typeInfo = shutoffTypes.find(t => t.value === type);
                  return (
                    <Grid item xs={6} key={type}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ mr: 1 }}>
                          {typeInfo?.icon || '⚙️'} {typeInfo?.label || type}
                        </Typography>
                        <Chip label={count} size="small" />
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      )}
    </Grid>
  );

  const renderEditDialog = () => (
    <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
      <DialogTitle>
        {editingShutoff?._id ? 'Edit Utility Shutoff' : 'Create New Utility Shutoff'}
      </DialogTitle>
      <DialogContent>
        {editingShutoff && (
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Shutoff ID"
                value={editingShutoff.shutoffId || ''}
                onChange={(e) => setEditingShutoff(prev => ({ 
                  ...prev, 
                  shutoffId: e.target.value 
                }))}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={editingShutoff.name || ''}
                onChange={(e) => setEditingShutoff(prev => ({ 
                  ...prev, 
                  name: e.target.value 
                }))}
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Shutoff Type</InputLabel>
                <Select
                  value={editingShutoff.shutoffType || ''}
                  onChange={(e) => setEditingShutoff(prev => ({ 
                    ...prev, 
                    shutoffType: e.target.value 
                  }))}
                  label="Shutoff Type"
                  required
                >
                  {shutoffTypes.map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.icon} {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Room/Location"
                value={editingShutoff.room || ''}
                onChange={(e) => setEditingShutoff(prev => ({ 
                  ...prev, 
                  room: e.target.value 
                }))}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Coverage Description"
                value={editingShutoff.coverage?.description || ''}
                onChange={(e) => setEditingShutoff(prev => ({ 
                  ...prev, 
                  coverage: { ...prev.coverage, description: e.target.value }
                }))}
                multiline
                rows={2}
                placeholder="What areas/systems does this shutoff control?"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={editingShutoff.coverage?.priority || 'medium'}
                  onChange={(e) => setEditingShutoff(prev => ({ 
                    ...prev, 
                    coverage: { ...prev.coverage, priority: e.target.value }
                  }))}
                  label="Priority"
                >
                  {priorityLevels.map(level => (
                    <MenuItem key={level.value} value={level.value}>
                      {level.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Current State</InputLabel>
                <Select
                  value={editingShutoff.status?.currentState || 'unknown'}
                  onChange={(e) => setEditingShutoff(prev => ({ 
                    ...prev, 
                    status: { ...prev.status, currentState: e.target.value }
                  }))}
                  label="Current State"
                >
                  {shutoffStates.map(state => (
                    <MenuItem key={state.value} value={state.value}>
                      {state.icon} {state.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label={
            <Badge badgeContent={shutoffs.length} color="primary">
              Shutoffs
            </Badge>
          } />
          <Tab label={
            inspectionsDue ? (
              <Badge badgeContent={inspectionsDue.summary.overdueCount} color="error">
                Inspections
              </Badge>
            ) : 'Inspections'
          } />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {currentTab === 0 && (
        <Box>
          {renderFilters()}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderShutoffTable()
          )}
        </Box>
      )}

      {currentTab === 1 && (
        <Box>
          {/* Inspection management would go here */}
          <Typography variant="h6" gutterBottom>
            Inspection Management
          </Typography>
          <Alert severity="info">
            Inspection management interface coming soon. Use the table view to record individual inspections.
          </Alert>
        </Box>
      )}

      {currentTab === 2 && renderAnalytics()}

      {/* Add New Shutoff FAB */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => {
          setEditingShutoff({
            buildingId,
            floorId,
            shutoffType: 'water_main',
            coverage: { priority: 'medium' },
            status: { currentState: 'unknown', condition: 'good' }
          });
          setEditDialogOpen(true);
        }}
      >
        <AddIcon />
      </Fab>

      {/* Quick Actions */}
      {!compactView && (
        <Box sx={{ position: 'fixed', bottom: 80, right: 16, display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Tooltip title="Seed Demo Data">
            <Fab size="small" onClick={handleSeedDemoData}>
              <BuildIcon />
            </Fab>
          </Tooltip>
        </Box>
      )}

      {/* Dialogs */}
      {renderEditDialog()}
    </Box>
  );
};

export default UtilityShutoffManagement;