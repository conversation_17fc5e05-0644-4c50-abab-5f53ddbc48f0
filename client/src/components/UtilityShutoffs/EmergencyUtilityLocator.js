import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip,
  Badge,
  ButtonGroup,
  Paper
} from '@mui/material';
import {
  ReportProblem as EmergencyIcon,
  Water as WaterIcon,
  LocalGasStation as GasIcon,
  PowerOff as ShutoffIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Build as ToolIcon,
  DirectionsRun as EvacuateIcon,
  Map as MapIcon,
  Timer as TimerIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import utilityShutoffService from '../../services/utilityShutoffService';

/**
 * EmergencyUtilityLocator - Crisis-optimized interface for emergency shutoff location
 * Large, clear interface designed for emergency situations with minimal cognitive load
 */
const EmergencyUtilityLocator = ({ 
  buildingId, 
  floorId, 
  currentLocation, 
  emergencyMode = false,
  onShutoffSelect = null,
  onClose = null 
}) => {
  const [selectedUtilityType, setSelectedUtilityType] = useState(null);
  const [shutoffs, setShutoffs] = useState([]);
  const [nearestShutoffs, setNearestShutoffs] = useState([]);
  const [selectedShutoff, setSelectedShutoff] = useState(null);
  const [proceduresOpen, setProceduresOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [emergencyProcedures, setEmergencyProcedures] = useState(null);

  // Emergency utility types for quick selection
  const emergencyUtilityTypes = useMemo(() => [
    {
      type: 'water',
      label: 'WATER EMERGENCY',
      icon: <WaterIcon sx={{ fontSize: 48 }} />,
      color: '#2196F3',
      description: 'Flooding, burst pipes, water leak',
      priority: 'critical'
    },
    {
      type: 'gas',
      label: 'GAS EMERGENCY', 
      icon: <GasIcon sx={{ fontSize: 48 }} />,
      color: '#FF5722',
      description: 'Gas leak, gas smell, heating emergency',
      priority: 'critical'
    }
  ], []);

  // Load shutoffs for selected utility type
  useEffect(() => {
    if (selectedUtilityType && buildingId && floorId) {
      loadShutoffs();
    }
  }, [selectedUtilityType, buildingId, floorId]);

  // Find nearest shutoffs when location is available
  useEffect(() => {
    if (selectedUtilityType && currentLocation && buildingId && floorId) {
      findNearestShutoffs();
    }
  }, [selectedUtilityType, currentLocation, buildingId, floorId]);

  const loadShutoffs = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await utilityShutoffService.getShutoffsByLocation(
        buildingId, 
        floorId, 
        true // includePositioned
      );
      
      // Filter by utility type and prioritize emergency/main shutoffs
      const filtered = data
        .filter(shutoff => shutoff.shutoffType.startsWith(selectedUtilityType))
        .map(shutoff => utilityShutoffService.formatShutoffDisplay(shutoff))
        .sort((a, b) => {
          // Emergency priority calculation
          const priorityA = utilityShutoffService.calculateEmergencyPriority(a);
          const priorityB = utilityShutoffService.calculateEmergencyPriority(b);
          return priorityB - priorityA;
        });

      setShutoffs(filtered);
    } catch (err) {
      console.error('Error loading shutoffs:', err);
      setError('Failed to load emergency shutoffs. Please contact facilities immediately.');
    } finally {
      setLoading(false);
    }
  };

  const findNearestShutoffs = async () => {
    try {
      const params = {
        buildingId,
        floorId,
        x: currentLocation.x,
        y: currentLocation.y,
        utilityType: selectedUtilityType,
        maxDistance: 100
      };

      const result = await utilityShutoffService.findNearestShutoffs(params);
      const formatted = result.shutoffs.map(shutoff => 
        utilityShutoffService.formatShutoffDisplay(shutoff)
      );
      
      setNearestShutoffs(formatted);
    } catch (err) {
      console.error('Error finding nearest shutoffs:', err);
    }
  };

  const handleShutoffSelect = async (shutoff) => {
    setSelectedShutoff(shutoff);
    
    // Load emergency procedures
    try {
      const procedures = await utilityShutoffService.getEmergencyProcedures(shutoff._id);
      setEmergencyProcedures(procedures);
    } catch (err) {
      console.error('Error loading procedures:', err);
    }

    if (onShutoffSelect) {
      onShutoffSelect(shutoff);
    }
  };

  const handleViewProcedures = () => {
    setProceduresOpen(true);
  };

  const handleRecordEmergencyUse = async (reason) => {
    if (!selectedShutoff) return;
    
    try {
      await utilityShutoffService.recordEmergencyUse(selectedShutoff._id, { reason });
      
      // Reload shutoff data
      await loadShutoffs();
      
      // Show success
      setError(null);
    } catch (err) {
      console.error('Error recording emergency use:', err);
      setError('Failed to record emergency shutoff use');
    }
  };

  const getEmergencyContacts = () => {
    const contacts = [];
    
    if (selectedUtilityType === 'gas') {
      contacts.push({
        label: 'Gas Company Emergency',
        number: '1-800-GAS-LEAK',
        description: 'Report gas leaks immediately'
      });
    }
    
    if (selectedUtilityType === 'water') {
      contacts.push({
        label: 'Facilities Emergency',
        number: '(*************',
        description: 'Building maintenance emergency'
      });
    }

    contacts.push({
      label: 'Emergency Services',
      number: '911',
      description: 'Life-threatening emergencies'
    });

    return contacts;
  };

  const renderUtilityTypeSelector = () => (
    <Box sx={{ mb: 4 }}>
      <Typography 
        variant="h4" 
        component="h1" 
        align="center" 
        sx={{ 
          mb: 3, 
          color: emergencyMode ? '#F44336' : 'primary.main',
          fontWeight: 'bold'
        }}
      >
        {emergencyMode ? '🚨 EMERGENCY SHUTOFF LOCATOR' : 'Utility Shutoff Locator'}
      </Typography>
      
      {emergencyMode && (
        <Alert 
          severity="error" 
          sx={{ mb: 3, fontSize: '1.1rem' }}
          icon={<EmergencyIcon />}
        >
          <Typography variant="h6" component="div">
            Emergency Mode Active
          </Typography>
          Select the type of utility emergency below to locate nearest shutoffs
        </Alert>
      )}

      <Grid container spacing={3} justifyContent="center">
        {emergencyUtilityTypes.map((utilityType) => (
          <Grid item xs={12} sm={6} md={4} key={utilityType.type}>
            <Card
              sx={{
                height: '100%',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                border: selectedUtilityType === utilityType.type ? 3 : 1,
                borderColor: selectedUtilityType === utilityType.type 
                  ? utilityType.color 
                  : 'divider',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 6,
                  borderColor: utilityType.color
                }
              }}
              onClick={() => setSelectedUtilityType(utilityType.type)}
            >
              <CardContent sx={{ textAlign: 'center', py: 4 }}>
                <Box sx={{ color: utilityType.color, mb: 2 }}>
                  {utilityType.icon}
                </Box>
                <Typography 
                  variant="h5" 
                  component="h2" 
                  gutterBottom
                  sx={{ fontWeight: 'bold', color: utilityType.color }}
                >
                  {utilityType.label}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {utilityType.description}
                </Typography>
                {utilityType.priority === 'critical' && (
                  <Chip 
                    label="CRITICAL" 
                    color="error" 
                    size="small" 
                    sx={{ mt: 1, fontWeight: 'bold' }}
                  />
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  const renderShutoffCard = (shutoff, isNearest = false) => (
    <Card
      key={shutoff._id}
      sx={{
        mb: 2,
        border: shutoff.isCritical ? 2 : 1,
        borderColor: shutoff.isCritical ? '#F44336' : 'divider',
        backgroundColor: isNearest ? 'action.hover' : 'background.paper'
      }}
    >
      <CardContent>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={8}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography 
                variant="h6" 
                component="h3"
                sx={{ fontWeight: 'bold' }}
              >
                {shutoff.typeIcon} {shutoff.displayName}
              </Typography>
              {shutoff.isCritical && (
                <Chip 
                  label="CRITICAL" 
                  color="error" 
                  size="small" 
                  sx={{ ml: 1, fontWeight: 'bold' }}
                />
              )}
              {isNearest && shutoff.distance !== undefined && (
                <Chip 
                  label={`${shutoff.distance}% away`}
                  color="primary" 
                  size="small" 
                  sx={{ ml: 1 }}
                />
              )}
            </Box>
            
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {shutoff.typeLabel} • {shutoff.room || 'Location not specified'}
            </Typography>
            
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>Controls:</strong> {shutoff.coverage?.description || 'Not specified'}
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip 
                label={shutoff.status?.currentState || 'Unknown'}
                size="small"
                sx={{ 
                  backgroundColor: shutoff.stateColor,
                  color: 'white',
                  fontWeight: 'bold'
                }}
              />
              <Chip 
                label={shutoff.coverage?.priority || 'Medium'}
                size="small"
                sx={{ 
                  backgroundColor: shutoff.priorityColor,
                  color: 'white'
                }}
              />
              {!shutoff.status?.isAccessible && (
                <Chip 
                  label="Limited Access"
                  size="small"
                  color="warning"
                  icon={<WarningIcon />}
                />
              )}
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<LocationIcon />}
                onClick={() => handleShutoffSelect(shutoff)}
                fullWidth
              >
                SELECT THIS SHUTOFF
              </Button>
              
              {shutoff.procedures?.emergencyContact && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<PhoneIcon />}
                  href={`tel:${shutoff.procedures.emergencyContact}`}
                  fullWidth
                >
                  Call Emergency Contact
                </Button>
              )}
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderShutoffList = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          {selectedUtilityType?.toUpperCase()} Emergency Shutoffs
        </Typography>
        <Button
          variant="outlined"
          onClick={() => setSelectedUtilityType(null)}
        >
          Back to Selection
        </Button>
      </Box>

      {emergencyMode && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" component="div" gutterBottom>
            Emergency Safety Reminder
          </Typography>
          {selectedUtilityType === 'gas' && (
            <Typography>
              • If you smell gas: EVACUATE IMMEDIATELY and call gas company
              • Do not use electrical switches or create sparks
              • Shut off gas only if safe to access
            </Typography>
          )}
          {selectedUtilityType === 'water' && (
            <Typography>
              • Shut off electricity to affected areas if water is present
              • Main shutoffs affect the entire building
              • Contact facilities management after shutting off
            </Typography>
          )}
        </Alert>
      )}

      {/* Emergency Contacts */}
      <Paper sx={{ p: 2, mb: 3, backgroundColor: 'error.light', color: 'error.contrastText' }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <PhoneIcon sx={{ mr: 1 }} />
          Emergency Contacts
        </Typography>
        <Grid container spacing={2}>
          {getEmergencyContacts().map((contact, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Button
                variant="contained"
                color="error"
                href={`tel:${contact.number}`}
                startIcon={<PhoneIcon />}
                fullWidth
                sx={{ mb: 1 }}
              >
                {contact.number}
              </Button>
              <Typography variant="caption" display="block">
                {contact.label}
              </Typography>
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Nearest Shutoffs (if location available) */}
      {nearestShutoffs.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ color: 'primary.main' }}>
            <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Nearest to Your Location
          </Typography>
          {nearestShutoffs.slice(0, 3).map(shutoff => renderShutoffCard(shutoff, true))}
        </Box>
      )}

      {/* All Available Shutoffs */}
      <Typography variant="h6" gutterBottom>
        All {selectedUtilityType?.toUpperCase()} Shutoffs on This Floor
      </Typography>
      {loading ? (
        <Typography>Loading shutoffs...</Typography>
      ) : shutoffs.length === 0 ? (
        <Alert severity="warning">
          No {selectedUtilityType} shutoffs found on this floor. Contact facilities management.
        </Alert>
      ) : (
        shutoffs.map(shutoff => renderShutoffCard(shutoff))
      )}
    </Box>
  );

  const renderProceduresDialog = () => (
    <Dialog 
      open={proceduresOpen} 
      onClose={() => setProceduresOpen(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle sx={{ backgroundColor: 'error.main', color: 'error.contrastText' }}>
        <EmergencyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        Emergency Shutoff Procedures
      </DialogTitle>
      <DialogContent sx={{ mt: 2 }}>
        {emergencyProcedures && (
          <Box>
            <Typography variant="h6" gutterBottom>
              {emergencyProcedures.shutoff.name}
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                  <ShutoffIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Shutoff Instructions
                </Typography>
                {emergencyProcedures.procedures.shutoffInstructions ? (
                  <List dense>
                    {emergencyProcedures.procedures.shutoffInstructions.split('\n').map((step, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <Typography variant="h6" color="primary">
                            {index + 1}
                          </Typography>
                        </ListItemIcon>
                        <ListItemText primary={step} />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography color="text.secondary">
                    No specific instructions available. Use standard procedures.
                  </Typography>
                )}
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                  <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Safety Warnings
                </Typography>
                {emergencyProcedures.warnings.length > 0 ? (
                  <List dense>
                    {emergencyProcedures.warnings.map((warning, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <WarningIcon color="warning" />
                        </ListItemIcon>
                        <ListItemText primary={warning} />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Typography color="text.secondary">
                    No specific warnings listed.
                  </Typography>
                )}
                
                {emergencyProcedures.requiredTools.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                      <ToolIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Required Tools
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {emergencyProcedures.requiredTools.map((tool, index) => (
                        <Chip key={index} label={tool} size="small" />
                      ))}
                    </Box>
                  </Box>
                )}
              </Grid>
            </Grid>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setProceduresOpen(false)}>
          Close
        </Button>
        <Button 
          variant="contained" 
          color="error"
          onClick={() => handleRecordEmergencyUse('Emergency shutoff activated via locator')}
        >
          Record Emergency Use
        </Button>
      </DialogActions>
    </Dialog>
  );

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        <Typography variant="h6">Emergency System Error</Typography>
        <Typography>{error}</Typography>
        <Typography sx={{ mt: 1 }}>
          <strong>Immediate Action Required:</strong> Contact facilities management or emergency services.
        </Typography>
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {!selectedUtilityType ? renderUtilityTypeSelector() : renderShutoffList()}
      
      {/* Selected Shutoff Actions */}
      {selectedShutoff && (
        <Paper sx={{ p: 3, mt: 3, backgroundColor: 'primary.light' }}>
          <Typography variant="h6" gutterBottom sx={{ color: 'primary.contrastText' }}>
            Selected: {selectedShutoff.displayName}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Button
                variant="contained"
                color="error"
                size="large"
                startIcon={<ShutoffIcon />}
                onClick={handleViewProcedures}
                fullWidth
              >
                View Procedures
              </Button>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Button
                variant="contained"
                color="secondary"
                size="large"
                startIcon={<MapIcon />}
                onClick={() => onShutoffSelect && onShutoffSelect(selectedShutoff)}
                fullWidth
              >
                Show on Map
              </Button>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Button
                variant="outlined"
                size="large"
                startIcon={<PhoneIcon />}
                href={`tel:${selectedShutoff.procedures?.emergencyContact || '911'}`}
                fullWidth
              >
                Call for Help
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {renderProceduresDialog()}
    </Box>
  );
};

export default EmergencyUtilityLocator;