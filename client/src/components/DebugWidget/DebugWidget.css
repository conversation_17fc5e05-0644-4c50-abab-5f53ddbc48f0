/* Debug Widget Enhanced Styles */
.debug-request-card {
  transition: all 0.2s ease-in-out;
}

.debug-request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.debug-status-chip {
  animation: fadeIn 0.3s ease-in;
}

.debug-search-field .MuiOutlinedInput-root {
  border-radius: 25px;
}

.debug-filter-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.debug-stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.debug-method-avatar {
  font-weight: bold;
  font-size: 0.75rem;
}

.debug-json-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  border: 1px solid #e9ecef;
}

.debug-domain-chip {
  background: linear-gradient(45deg, #21CBF3, #2196F3);
  color: white;
}

.debug-error-glow {
  box-shadow: 0 0 10px rgba(244, 67, 54, 0.3);
  border-left: 4px solid #f44336;
}

.debug-success-glow {
  border-left: 4px solid #4caf50;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.debug-request-enter {
  animation: slideIn 0.3s ease-out;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .debug-json-display {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  
  .debug-filter-card {
    background: rgba(45, 55, 72, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}