import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  TextField, 
  InputAdornment, 
  IconButton, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  Typography,
  Popper,
  ClickAwayListener,
  CircularProgress
} from '@mui/material';
import { 
  Search as SearchIcon, 
  Clear as ClearIcon,
  Description as DocumentIcon,
  Person as PersonIcon,
  Event as EventIcon,
  Folder as FolderIcon,
  Link as LinkIcon
} from '@mui/icons-material';
import searchService from '../services/searchService';

const SearchBar = ({ onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchInputRef = useRef(null);
  const navigate = useNavigate();

  // Auto-focus when component mounts (for popup mode)
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Get icon based on result type
  const getIconForType = (type) => {
    switch (type) {
      case 'person':
        return <PersonIcon />;
      case 'event':
        return <EventIcon />;
      case 'document':
        return <DocumentIcon />;
      case 'folder':
        return <FolderIcon />;
      default:
        return <LinkIcon />;
    }
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    const query = event.target.value;
    setSearchQuery(query);
    
    if (query.length > 2) {
      fetchSearchSuggestions(query);
    } else {
      setSearchResults([]);
      setShowResults(false);
    }
  };

  // Fetch search suggestions
  const fetchSearchSuggestions = async (query) => {
    setIsSearching(true);
    try {
      const suggestions = await searchService.getSuggestions(query);
      setSearchResults(suggestions);
      setShowResults(true);
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search submission
  const handleSearchSubmit = async (event) => {
    if (event) {
      event.preventDefault();
    }
    
    if (searchQuery.trim()) {
      setIsSearching(true);
      try {
        const results = await searchService.searchAll(searchQuery);
        setSearchResults(results);
        
        // If there are results, navigate to the first result
        if (results.length > 0) {
          navigate(results[0].url);
          setShowResults(false);
          setSearchQuery('');
          if (onClose) onClose(); // Close popup on submit
        }
      } catch (error) {
        console.error('Error performing search:', error);
      } finally {
        setIsSearching(false);
      }
    }
  };

  // Handle result click
  const handleResultClick = (url) => {
    navigate(url);
    setShowResults(false);
    setSearchQuery('');
    if (onClose) onClose(); // Close popup when result is selected
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowResults(false);
  };

  // Close results when clicking away
  const handleClickAway = () => {
    setShowResults(false);
  };

  // Close results on escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setShowResults(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, []);

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Box sx={{ position: 'relative', width: '100%' }}>
        <form onSubmit={handleSearchSubmit}>
          <TextField
            inputRef={searchInputRef}
            placeholder="Search across all pages..."
            value={searchQuery}
            onChange={handleSearchChange}
            variant="outlined"
            size="small"
            fullWidth
            sx={{
              backgroundColor: onClose ? 'transparent' : 'rgba(255, 255, 255, 0.15)',
              borderRadius: 1,
              '& .MuiOutlinedInput-root': {
                color: onClose ? 'inherit' : 'white',
                '& fieldset': {
                  borderColor: onClose ? 'rgba(0, 0, 0, 0.23)' : 'transparent',
                },
                '&:hover fieldset': {
                  borderColor: onClose ? 'rgba(0, 0, 0, 0.87)' : 'rgba(255, 255, 255, 0.3)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: onClose ? '#0066ff' : 'white',
                },
              },
              '& .MuiInputBase-input::placeholder': {
                color: onClose ? 'rgba(0, 0, 0, 0.6)' : 'rgba(255, 255, 255, 0.7)',
                opacity: 1,
              },
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: onClose ? 'inherit' : 'white' }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {isSearching ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : searchQuery ? (
                    <IconButton
                      size="small"
                      onClick={handleClearSearch}
                      sx={{ color: onClose ? 'inherit' : 'white' }}
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  ) : null}
                </InputAdornment>
              ),
            }}
          />
        </form>

        {/* Search Results Dropdown */}
        <Popper
          open={showResults && searchResults.length > 0}
          anchorEl={searchInputRef.current}
          placement="bottom-start"
          style={{ width: searchInputRef.current?.offsetWidth, zIndex: 1300 }}
        >
          <Paper elevation={3} sx={{ mt: 1, maxHeight: 400, overflow: 'auto' }}>
            <List>
              {searchResults.map((result, index) => (
                <ListItem
                  button
                  key={`${result.id}-${index}`}
                  onClick={() => handleResultClick(result.url)}
                  divider={index < searchResults.length - 1}
                >
                  <ListItemIcon>
                    {result.icon || getIconForType(result.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={result.title}
                    secondary={
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }}
                      >
                        {result.description}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Popper>
      </Box>
    </ClickAwayListener>
  );
};

export default SearchBar;