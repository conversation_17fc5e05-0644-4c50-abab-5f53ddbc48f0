import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  TextField,
  IconButton,
  Avatar,
  List,
  ListItem,
  Paper,
  Divider,
  CircularProgress,
  Chip,
  InputAdornment,
  Fade,
  Grow
} from '@mui/material';
import {
  Send as SendIcon,
  Close as CloseIcon,
  AttachFile as AttachFileIcon,
  EmojiEmotions as EmojiIcon,
  Delete as DeleteIcon,
  Reply as ReplyIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import messageService from '../services/messageService';
import websocketService from '../services/websocketService';

const MessageConversation = ({ conversation, open, onClose, onMessageSent }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [messageText, setMessageText] = useState('');
  const [typingUsers, setTypingUsers] = useState([]);
  const [replyingTo, setReplyingTo] = useState(null);
  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const lastTypingRef = useRef(false);

  // Load messages when conversation changes
  useEffect(() => {
    if (conversation && open) {
      loadMessages();
      markAsRead();
      
      // Subscribe to real-time events for this conversation
      websocketService.subscribe(`conversation:${conversation._id}`);
      websocketService.addEventListener('new_message', handleNewMessage);
      websocketService.addEventListener('message_deleted', handleMessageDeleted);
      websocketService.addEventListener('typing_status', handleTypingStatus);
      
      return () => {
        websocketService.unsubscribe(`conversation:${conversation._id}`);
        websocketService.removeEventListener('new_message', handleNewMessage);
        websocketService.removeEventListener('message_deleted', handleMessageDeleted);
        websocketService.removeEventListener('typing_status', handleTypingStatus);
      };
    }
  }, [conversation, open]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load messages
  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await messageService.getMessages(conversation._id);
      if (response.success) {
        setMessages(response.messages);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mark messages as read
  const markAsRead = async () => {
    try {
      await messageService.markAsRead(conversation._id);
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  };

  // Handle new message from WebSocket
  const handleNewMessage = (data) => {
    const message = data.data;
    if (message.conversation._id === conversation._id) {
      setMessages(prev => [...prev, message]);
      markAsRead();
    }
  };

  // Handle message deleted
  const handleMessageDeleted = (data) => {
    const { messageId, conversationId } = data.data;
    if (conversationId === conversation._id) {
      setMessages(prev => 
        prev.map(msg => 
          msg._id === messageId 
            ? { ...msg, content: '[Message deleted]', deletedAt: new Date() }
            : msg
        )
      );
    }
  };

  // Handle typing status
  const handleTypingStatus = (data) => {
    const { conversationId, userId, isTyping, userName } = data.data;
    if (conversationId === conversation._id && userId !== user._id) {
      setTypingUsers(prev => {
        if (isTyping) {
          if (!prev.find(u => u.userId === userId)) {
            return [...prev, { userId, userName }];
          }
          return prev;
        } else {
          return prev.filter(u => u.userId !== userId);
        }
      });
    }
  };

  // Send message
  const sendMessage = async () => {
    if (!messageText.trim() && !replyingTo) return;
    
    setSending(true);
    try {
      const response = await messageService.sendMessage(
        conversation._id,
        messageText.trim(),
        'text',
        replyingTo?._id
      );
      
      if (response.success) {
        setMessages(prev => [...prev, response.message]);
        setMessageText('');
        setReplyingTo(null);
        onMessageSent && onMessageSent();
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  // Handle typing
  const handleTyping = () => {
    if (!lastTypingRef.current) {
      messageService.setTypingStatus(conversation._id, true);
      lastTypingRef.current = true;
    }
    
    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      messageService.setTypingStatus(conversation._id, false);
      lastTypingRef.current = false;
    }, 2000);
  };

  // Delete message
  const deleteMessage = async (messageId) => {
    try {
      await messageService.deleteMessage(messageId);
      setMessages(prev => 
        prev.map(msg => 
          msg._id === messageId 
            ? { ...msg, content: '[Message deleted]', deletedAt: new Date() }
            : msg
        )
      );
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  };

  // Scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Get conversation display info
  const displayName = messageService.getConversationDisplayName(conversation, user._id);
  const avatar = messageService.getConversationAvatar(conversation, user._id);

  // Group messages by sender for better display
  const groupedMessages = messages.reduce((groups, message, index) => {
    const prevMessage = messages[index - 1];
    const isNewGroup = !prevMessage || 
      prevMessage.sender._id !== message.sender._id ||
      new Date(message.createdAt) - new Date(prevMessage.createdAt) > 60000; // 1 minute gap
    
    if (isNewGroup) {
      groups.push([message]);
    } else {
      groups[groups.length - 1].push(message);
    }
    
    return groups;
  }, []);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          height: '80vh',
          background: 'rgba(255, 255, 255, 0.98)',
          backdropFilter: 'blur(10px)',
          borderRadius: '12px'
        }
      }}
    >
      {/* Header */}
      <DialogTitle sx={{ 
        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
        background: 'linear-gradient(135deg, #2563eb, #1e40af)',
        color: 'white'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              src={avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(avatar)}` : undefined}
              sx={{ mr: 2, backgroundColor: '#1e40af' }}
            >
              {displayName[0]?.toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="h6">{displayName}</Typography>
              {typingUsers.length > 0 && (
                <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  {typingUsers.map(u => u.userName).join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
                </Typography>
              )}
            </Box>
          </Box>
          <IconButton onClick={onClose} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      {/* Messages */}
      <DialogContent sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3, flex: 1 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {groupedMessages.map((group, groupIndex) => {
              const isOwnMessage = group[0].sender._id === user._id;
              
              return (
                <Fade in key={groupIndex} timeout={300}>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: isOwnMessage ? 'row-reverse' : 'row',
                      mb: 2,
                      alignItems: 'flex-end'
                    }}
                  >
                    {!isOwnMessage && (
                      <Avatar
                        src={group[0].sender.avatar ? 
                          `/api/auth/avatar-proxy?url=${encodeURIComponent(group[0].sender.avatar)}` : 
                          undefined
                        }
                        sx={{ 
                          width: 32, 
                          height: 32, 
                          mr: 1,
                          mb: 0.5
                        }}
                      >
                        {group[0].sender.name[0]}
                      </Avatar>
                    )}
                    
                    <Box sx={{ maxWidth: '70%' }}>
                      {group.map((message, messageIndex) => (
                        <Grow in key={message._id} timeout={200}>
                          <Paper
                            sx={{
                              p: 1.5,
                              mb: 0.5,
                              borderRadius: '12px',
                              backgroundColor: isOwnMessage 
                                ? 'linear-gradient(135deg, #2563eb, #1e40af)'
                                : 'rgba(0, 0, 0, 0.05)',
                              background: isOwnMessage 
                                ? 'linear-gradient(135deg, #2563eb, #1e40af)'
                                : 'rgba(0, 0, 0, 0.05)',
                              color: isOwnMessage ? 'white' : 'inherit',
                              position: 'relative',
                              '&:hover .message-actions': {
                                opacity: 1
                              }
                            }}
                          >
                            {/* Reply reference */}
                            {message.replyTo && (
                              <Box sx={{ 
                                mb: 1, 
                                p: 1, 
                                borderLeft: '3px solid',
                                borderColor: isOwnMessage ? 'rgba(255, 255, 255, 0.5)' : 'rgba(37, 99, 235, 0.5)',
                                backgroundColor: isOwnMessage ? 'rgba(255, 255, 255, 0.1)' : 'rgba(37, 99, 235, 0.1)',
                                borderRadius: '4px'
                              }}>
                                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                                  Replying to {message.replyTo.sender.name}
                                </Typography>
                                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                                  {message.replyTo.content}
                                </Typography>
                              </Box>
                            )}
                            
                            {/* Message content */}
                            <Typography variant="body1">
                              {message.content}
                            </Typography>
                            
                            {/* Timestamp */}
                            {messageIndex === group.length - 1 && (
                              <Typography 
                                variant="caption" 
                                sx={{ 
                                  display: 'block',
                                  mt: 0.5,
                                  opacity: 0.7,
                                  fontSize: '0.75rem'
                                }}
                              >
                                {messageService.formatMessageTime(message.createdAt)}
                              </Typography>
                            )}
                            
                            {/* Message actions */}
                            <Box 
                              className="message-actions"
                              sx={{ 
                                position: 'absolute',
                                top: -20,
                                right: isOwnMessage ? 0 : 'auto',
                                left: isOwnMessage ? 'auto' : 0,
                                opacity: 0,
                                transition: 'opacity 0.2s',
                                display: 'flex',
                                backgroundColor: 'white',
                                borderRadius: '8px',
                                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                              }}
                            >
                              <IconButton
                                size="small"
                                onClick={() => setReplyingTo(message)}
                                sx={{ p: 0.5 }}
                              >
                                <ReplyIcon fontSize="small" />
                              </IconButton>
                              {isOwnMessage && !message.deletedAt && (
                                <IconButton
                                  size="small"
                                  onClick={() => deleteMessage(message._id)}
                                  sx={{ p: 0.5 }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              )}
                            </Box>
                          </Paper>
                        </Grow>
                      ))}
                    </Box>
                  </Box>
                </Fade>
              );
            })}
            <div ref={messagesEndRef} />
          </Box>
        )}

        {/* Reply indicator */}
        {replyingTo && (
          <Paper sx={{ 
            p: 1.5, 
            mb: 1,
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            borderLeft: '3px solid #2563eb'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="caption" color="primary">
                  Replying to {replyingTo.sender.name}
                </Typography>
                <Typography variant="body2">
                  {replyingTo.content}
                </Typography>
              </Box>
              <IconButton size="small" onClick={() => setReplyingTo(null)}>
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          </Paper>
        )}

        {/* Message input */}
        <Box sx={{ display: 'flex', alignItems: 'flex-end', mt: 2 }}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type a message..."
            value={messageText}
            onChange={(e) => {
              setMessageText(e.target.value);
              handleTyping();
            }}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
              }
            }}
            disabled={sending}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '12px',
                backgroundColor: 'rgba(0, 0, 0, 0.05)'
              }
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={sendMessage}
                    disabled={!messageText.trim() || sending}
                    sx={{
                      background: messageText.trim() ? 'linear-gradient(135deg, #2563eb, #1e40af)' : 'transparent',
                      color: messageText.trim() ? 'white' : 'inherit',
                      '&:hover': {
                        background: messageText.trim() ? 'linear-gradient(135deg, #1e40af, #1e3a8a)' : 'transparent'
                      }
                    }}
                  >
                    {sending ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default MessageConversation;