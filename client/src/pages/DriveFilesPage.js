import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Paper,
  Breadcrumbs,
  Link,
  IconButton,
  Tooltip,
  Tab,
  Tabs
} from '@mui/material';
import { 
  Search as SearchIcon,
  Folder as FolderIcon,
  Description as FileIcon,
  Image as ImageIcon,
  Movie as VideoIcon,
  AudioFile as AudioIcon,
  PictureAsPdf as PdfIcon,
  TableChart as SpreadsheetIcon,
  Slideshow as PresentationIcon,
  Code as CodeIcon,
  InsertDriveFile as GenericFileIcon,
  ArrowBack as BackIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import moment from 'moment';
import { useNavigate, useLocation } from 'react-router-dom';

const DriveFilesPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [files, setFiles] = useState([]);
  const [favorites, setFavorites] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [favoritesLoading, setFavoritesLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searching, setSearching] = useState(false);
  const [activeTab, setActiveTab] = useState(0); // 0 for all files, 1 for favorites

  // Check URL for tab parameter when component mounts
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    if (tabParam !== null) {
      const tabValue = parseInt(tabParam, 10);
      if (!isNaN(tabValue) && tabValue >= 0 && tabValue <= 1) { // 0 for all files, 1 for favorites
        setActiveTab(tabValue);
      }
    }
  }, [location.search]);

  // Fetch files from Google Drive
  useEffect(() => {
    const fetchFiles = async () => {
      try {
        setLoading(true);
        const res = await axios.get('/api/google/drive/files');
        setFiles(res.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching files:', err);
        setError('Failed to load files from Google Drive');
        setLoading(false);
      }
    };

    fetchFiles();
  }, []);

  // Fetch favorites
  useEffect(() => {
    const fetchFavorites = async () => {
      try {
        setFavoritesLoading(true);
        const res = await axios.get('/api/google/drive/favorites');
        setFavorites(res.data);
        setFavoritesLoading(false);
      } catch (err) {
        console.error('Error fetching favorites:', err);
        setFavoritesLoading(false);
      }
    };

    fetchFavorites();
  }, []);

  // Toggle favorite status
  const toggleFavorite = async (e, fileId) => {
    e.stopPropagation(); // Prevent navigating to file when clicking the star

    try {
      const isFavorite = favorites.some(fav => fav.fileId === fileId);

      if (isFavorite) {
        await axios.delete(`/api/google/drive/favorites/${fileId}`);
        setFavorites(favorites.filter(fav => fav.fileId !== fileId));
      } else {
        const fileToFavorite = files.find(file => file.id === fileId);
        if (fileToFavorite) {
          const res = await axios.post('/api/google/drive/favorites', { fileId });
          setFavorites([...favorites, res.data.favorite]);
        }
      }
    } catch (err) {
      console.error('Error toggling favorite status:', err);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Update URL with the selected tab
    navigate(`${location.pathname}?tab=${newValue}`, { replace: true });
  };

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle search submission
  const handleSearch = async (event) => {
    event.preventDefault();

    if (!searchTerm.trim()) {
      return;
    }

    try {
      setSearching(true);
      const res = await axios.get(`/api/google/drive/search?q=${encodeURIComponent(searchTerm)}`);
      setFiles(res.data);
      setSearching(false);
    } catch (err) {
      console.error('Error searching files:', err);
      setError('Failed to search Google Drive');
      setSearching(false);
    }
  };

  // Reset search and fetch all files
  const handleResetSearch = async () => {
    setSearchTerm('');
    try {
      setLoading(true);
      const res = await axios.get('/api/google/drive/files');
      setFiles(res.data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching files:', err);
      setError('Failed to load files from Google Drive');
      setLoading(false);
    }
  };

  // Get appropriate icon based on file mime type
  const getFileIcon = (mimeType) => {
    if (mimeType.includes('folder')) return <FolderIcon />;
    if (mimeType.includes('image')) return <ImageIcon />;
    if (mimeType.includes('video')) return <VideoIcon />;
    if (mimeType.includes('audio')) return <AudioIcon />;
    if (mimeType.includes('pdf')) return <PdfIcon />;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return <SpreadsheetIcon />;
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return <PresentationIcon />;
    if (mimeType.includes('document') || mimeType.includes('word')) return <FileIcon />;
    if (mimeType.includes('text') || mimeType.includes('code')) return <CodeIcon />;
    return <GenericFileIcon />;
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Google Drive Files
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Access and search your Google Drive files
        </Typography>
      </Box>

      {/* Search */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <form onSubmit={handleSearch}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs>
              <TextField
                fullWidth
                placeholder="Search files..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item>
              <Button 
                variant="contained" 
                color="primary" 
                type="submit"
                disabled={searching || !searchTerm.trim()}
              >
                {searching ? <CircularProgress size={24} /> : 'Search'}
              </Button>
            </Grid>
            {searchTerm && (
              <Grid item>
                <Button 
                  variant="outlined" 
                  onClick={handleResetSearch}
                  startIcon={<BackIcon />}
                >
                  Reset
                </Button>
              </Grid>
            )}
          </Grid>
        </form>
      </Paper>

      {/* Files list */}
      <Paper sx={{ p: 2 }}>
        <Box sx={{ mb: 2 }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange}
            aria-label="file tabs"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="All Files" />
            <Tab label="Favorites" />
          </Tabs>
        </Box>

        {activeTab === 0 ? (
          // All Files Tab
          loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error">{error}</Alert>
          ) : files.length === 0 ? (
            <Alert severity="info">
              {searchTerm ? 'No files found matching your search.' : 'No files found in your Google Drive.'}
            </Alert>
          ) : (
            <List>
              {files.map((file) => {
                const isFavorite = favorites.some(fav => fav.fileId === file.id);
                return (
                  <ListItem 
                    key={file.id}
                    disablePadding
                    divider
                    secondaryAction={
                      <Tooltip title={isFavorite ? "Remove from favorites" : "Add to favorites"}>
                        <IconButton 
                          edge="end" 
                          aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
                          onClick={(e) => toggleFavorite(e, file.id)}
                        >
                          {isFavorite ? <StarIcon color="primary" /> : <StarBorderIcon />}
                        </IconButton>
                      </Tooltip>
                    }
                  >
                    <ListItemButton
                      onClick={() => navigate(`/drive/view/${file.id}`)}
                    >
                      <ListItemIcon>
                        {getFileIcon(file.mimeType)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={file.name} 
                        secondary={
                          <>
                            <Typography component="span" variant="body2" color="text.secondary">
                              {file.mimeType.includes('folder') ? 'Folder' : file.mimeType.split('/')[1]}
                            </Typography>
                            {' • '}
                            <Typography component="span" variant="body2" color="text.secondary">
                              Modified: {moment(file.modifiedTime).format('MMM D, YYYY')}
                            </Typography>
                          </>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                );
              })}
            </List>
          )
        ) : (
          // Favorites Tab
          favoritesLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
              <CircularProgress />
            </Box>
          ) : favorites.length === 0 ? (
            <Alert severity="info">
              No favorite files yet. Star files to add them to your favorites.
            </Alert>
          ) : (
            <List>
              {favorites.map((favorite) => (
                <ListItem 
                  key={favorite.fileId}
                  disablePadding
                  divider
                  secondaryAction={
                    <Tooltip title="Remove from favorites">
                      <IconButton 
                        edge="end" 
                        aria-label="Remove from favorites"
                        onClick={(e) => toggleFavorite(e, favorite.fileId)}
                      >
                        <StarIcon color="primary" />
                      </IconButton>
                    </Tooltip>
                  }
                >
                  <ListItemButton
                    onClick={() => navigate(`/drive/view/${favorite.fileId}`)}
                  >
                    <ListItemIcon>
                      {getFileIcon(favorite.mimeType)}
                    </ListItemIcon>
                    <ListItemText 
                      primary={favorite.name} 
                      secondary={
                        <>
                          <Typography component="span" variant="body2" color="text.secondary">
                            {favorite.mimeType.includes('folder') ? 'Folder' : favorite.mimeType.split('/')[1]}
                          </Typography>
                          {' • '}
                          <Typography component="span" variant="body2" color="text.secondary">
                            Modified: {moment(favorite.modifiedTime).format('MMM D, YYYY')}
                          </Typography>
                        </>
                      }
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          )
        )}
      </Paper>
    </Container>
  );
};

export default DriveFilesPage;
