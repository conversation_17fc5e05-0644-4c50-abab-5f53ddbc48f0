import React, { useEffect, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Avatar,
  CircularProgress,
  Alert
} from '@mui/material';
import axios from 'axios';

const TeamHubsIndexPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [teams, setTeams] = useState([]);

  useEffect(() => {
    const fetchTeams = async () => {
      setLoading(true);
      try {
        // Get current user with teams
        const me = await axios.get('/api/users/me');
        const teamIds = Array.isArray(me.data.teams) ? me.data.teams : [];
        const teamDetails = [];
        for (const id of teamIds) {
          try {
            const t = await axios.get(`/api/staff-directory/teams/${id}`);
            teamDetails.push(t.data);
          } catch (e) {
            // ignore individual errors, continue
          }
        }
        setTeams(teamDetails);
        setError(null);
      } catch (err) {
        console.error('Error loading team hubs index:', err);
        setError('Failed to load your teams');
      } finally {
        setLoading(false);
      }
    };
    fetchTeams();
  }, []);

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Team Hubs
      </Typography>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      {teams.length === 0 ? (
        <Typography variant="body1" color="text.secondary">
          You are not a member of any teams yet.
        </Typography>
      ) : (
        <Grid container spacing={2}>
          {teams.map(team => (
            <Grid item xs={12} sm={6} md={4} key={team._id}>
              <Card>
                <CardHeader
                  avatar={<Avatar src={team.leader?.avatar} alt={team.name} />}
                  title={team.name}
                  subheader={team.description}
                />
                <CardContent>
                  <Button
                    variant="contained"
                    component={RouterLink}
                    to={`/teams/${team._id}/hub`}
                  >
                    Open Team Hub
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default TeamHubsIndexPage;
