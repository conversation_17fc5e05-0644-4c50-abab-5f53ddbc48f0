import React, { useEffect, useMemo, useState } from 'react';
import { useParams, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  PushPin as PinIcon,
  PushPinOutlined as UnpinIcon,
  Delete as DeleteIcon,
  Link as LinkIcon,
  Article as ArticleIcon
} from '@mui/icons-material';
import axios from 'axios';

const TeamHubPage = () => {
  const { teamId } = useParams();
  const [team, setTeam] = useState(null);
  const [hub, setHub] = useState({ links: [], helpPages: [] });
  const [canManage, setCanManage] = useState(false);
  const [newsPosts, setNewsPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [pageDialogOpen, setPageDialogOpen] = useState(false);
  const [linkForm, setLinkForm] = useState({ title: '', url: '', description: '', pinned: false });
  const [pageForm, setPageForm] = useState({ title: '', content: '', pinned: false });

  const pinnedLinks = useMemo(() => (hub.links || []).filter(l => l.pinned), [hub]);
  const otherLinks = useMemo(() => (hub.links || []).filter(l => !l.pinned), [hub]);
  const pinnedPages = useMemo(() => (hub.helpPages || []).filter(p => p.pinned), [hub]);
  const otherPages = useMemo(() => (hub.helpPages || []).filter(p => !p.pinned), [hub]);

  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true);
      try {
        const hubRes = await axios.get(`/api/team-hub/${teamId}`);
        setTeam(hubRes.data.team);
        setHub(hubRes.data.hub || { links: [], helpPages: [] });
        setCanManage(!!hubRes.data.canManage);

        // Load latest news posts for this team (published only)
        const newsRes = await axios.get('/api/news-posts', {
          params: { team: teamId, published: true, limit: 5, page: 1 }
        });
        setNewsPosts(newsRes.data.posts || []);
        setError(null);
      } catch (err) {
        console.error('Error loading team hub:', err);
        setError('Failed to load team hub.');
      } finally {
        setLoading(false);
      }
    };
    if (teamId) fetchAll();
  }, [teamId]);

  const submitLink = async () => {
    try {
      const res = await axios.post(`/api/team-hub/${teamId}/links`, linkForm);
      setHub(res.data);
      setLinkDialogOpen(false);
      setLinkForm({ title: '', url: '', description: '', pinned: false });
    } catch (err) {
      console.error('Error creating link:', err);
      setError('Failed to create link');
    }
  };

  const submitPage = async () => {
    try {
      const res = await axios.post(`/api/team-hub/${teamId}/pages`, pageForm);
      setHub(res.data);
      setPageDialogOpen(false);
      setPageForm({ title: '', content: '', pinned: false });
    } catch (err) {
      console.error('Error creating help page:', err);
      setError('Failed to create help page');
    }
  };

  const toggleLinkPin = async (link) => {
    try {
      const res = await axios.put(`/api/team-hub/${teamId}/links/${link._id}`, { ...link, pinned: !link.pinned });
      setHub(res.data);
    } catch (err) {
      console.error('Error toggling link pin:', err);
      setError('Failed to update link');
    }
  };

  const deleteLink = async (link) => {
    try {
      const res = await axios.delete(`/api/team-hub/${teamId}/links/${link._id}`);
      setHub(res.data);
    } catch (err) {
      console.error('Error deleting link:', err);
      setError('Failed to delete link');
    }
  };

  const togglePagePin = async (page) => {
    try {
      const res = await axios.put(`/api/team-hub/${teamId}/pages/${page._id}`, { ...page, pinned: !page.pinned });
      setHub(res.data);
    } catch (err) {
      console.error('Error toggling page pin:', err);
      setError('Failed to update page');
    }
  };

  const deletePage = async (page) => {
    try {
      const res = await axios.delete(`/api/team-hub/${teamId}/pages/${page._id}`);
      setHub(res.data);
    } catch (err) {
      console.error('Error deleting page:', err);
      setError('Failed to delete page');
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        {team ? `${team.name} — Team Hub` : 'Team Hub'}
      </Typography>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Grid container spacing={2}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="Announcements & News" subheader="Latest posts for this team" />
            <CardContent>
              {newsPosts.length === 0 ? (
                <Typography variant="body2" color="text.secondary">No posts yet.</Typography>
              ) : (
                <List>
                  {newsPosts.map(p => (
                    <ListItem key={p._id} button component={RouterLink} to={`/news/posts/${p._id}`}>
                      <ListItemText primary={p.title} secondary={p.summary || ''} />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>

          <Box sx={{ mt: 2 }}>
            <Card>
              <CardHeader
                title="Help Pages"
                action={canManage && (
                  <Button variant="outlined" startIcon={<AddIcon />} onClick={() => setPageDialogOpen(true)}>Add Help Page</Button>
                )}
              />
              <CardContent>
                {pinnedPages.length > 0 && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>Pinned</Typography>
                    <List>
                      {pinnedPages.map(page => (
                        <ListItem key={page._id}>
                          <ArticleIcon sx={{ mr: 1 }} />
                          <ListItemText primary={page.title} />
                          {canManage && (
                            <ListItemSecondaryAction>
                              <IconButton size="small" onClick={() => togglePagePin(page)} title={page.pinned ? 'Unpin' : 'Pin'}>
                                {page.pinned ? <PinIcon /> : <UnpinIcon />}
                              </IconButton>
                              <IconButton size="small" color="error" onClick={() => deletePage(page)} title="Delete">
                                <DeleteIcon />
                              </IconButton>
                            </ListItemSecondaryAction>
                          )}
                        </ListItem>
                      ))}
                    </List>
                    <Divider sx={{ my: 1 }} />
                  </Box>
                )}

                {(otherPages.length === 0 && pinnedPages.length === 0) ? (
                  <Typography variant="body2" color="text.secondary">No help pages yet.</Typography>
                ) : (
                  <List>
                    {otherPages.map(page => (
                      <ListItem key={page._id}>
                        <ArticleIcon sx={{ mr: 1 }} />
                        <ListItemText primary={page.title} />
                        {canManage && (
                          <ListItemSecondaryAction>
                            <IconButton size="small" onClick={() => togglePagePin(page)} title={page.pinned ? 'Unpin' : 'Pin'}>
                              {page.pinned ? <PinIcon /> : <UnpinIcon />}
                            </IconButton>
                            <IconButton size="small" color="error" onClick={() => deletePage(page)} title="Delete">
                              <DeleteIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        )}
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Box>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader
              title="Links & Files"
              action={canManage && (
                <Button variant="outlined" startIcon={<AddIcon />} onClick={() => setLinkDialogOpen(true)}>Add Link</Button>
              )}
            />
            <CardContent>
              {pinnedLinks.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Pinned</Typography>
                  <List>
                    {pinnedLinks.map(link => (
                      <ListItem key={link._id} component="a" href={link.url} target="_blank" rel="noreferrer">
                        <LinkIcon sx={{ mr: 1 }} />
                        <ListItemText primary={link.title} secondary={link.description} />
                        {canManage && (
                          <ListItemSecondaryAction>
                            <IconButton size="small" onClick={() => toggleLinkPin(link)} title={link.pinned ? 'Unpin' : 'Pin'}>
                              {link.pinned ? <PinIcon /> : <UnpinIcon />}
                            </IconButton>
                            <IconButton size="small" color="error" onClick={() => deleteLink(link)} title="Delete">
                              <DeleteIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        )}
                      </ListItem>
                    ))}
                  </List>
                  <Divider sx={{ my: 1 }} />
                </Box>
              )}

              {(otherLinks.length === 0 && pinnedLinks.length === 0) ? (
                <Typography variant="body2" color="text.secondary">No links yet.</Typography>
              ) : (
                <List>
                  {otherLinks.map(link => (
                    <ListItem key={link._id} component="a" href={link.url} target="_blank" rel="noreferrer">
                      <LinkIcon sx={{ mr: 1 }} />
                      <ListItemText primary={link.title} secondary={link.description} />
                      {canManage && (
                        <ListItemSecondaryAction>
                          <IconButton size="small" onClick={() => toggleLinkPin(link)} title={link.pinned ? 'Unpin' : 'Pin'}>
                            {link.pinned ? <PinIcon /> : <UnpinIcon />}
                          </IconButton>
                          <IconButton size="small" color="error" onClick={() => deleteLink(link)} title="Delete">
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      )}
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Add Link Dialog */}
      <Dialog open={linkDialogOpen} onClose={() => setLinkDialogOpen(false)} fullWidth maxWidth="sm">
        <DialogTitle>Add Link or File</DialogTitle>
        <DialogContent>
          <TextField fullWidth margin="dense" label="Title" value={linkForm.title} onChange={(e) => setLinkForm({ ...linkForm, title: e.target.value })} />
          <TextField fullWidth margin="dense" label="URL" value={linkForm.url} onChange={(e) => setLinkForm({ ...linkForm, url: e.target.value })} placeholder="https://... (Drive, Synology, website)" />
          <TextField fullWidth margin="dense" label="Description" value={linkForm.description} onChange={(e) => setLinkForm({ ...linkForm, description: e.target.value })} />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLinkDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={submitLink}>Add</Button>
        </DialogActions>
      </Dialog>

      {/* Add Help Page Dialog */}
      <Dialog open={pageDialogOpen} onClose={() => setPageDialogOpen(false)} fullWidth maxWidth="sm">
        <DialogTitle>Add Help Page</DialogTitle>
        <DialogContent>
          <TextField fullWidth margin="dense" label="Title" value={pageForm.title} onChange={(e) => setPageForm({ ...pageForm, title: e.target.value })} />
          <TextField fullWidth margin="dense" label="Content" value={pageForm.content} onChange={(e) => setPageForm({ ...pageForm, content: e.target.value })} multiline rows={6} />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPageDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={submitPage}>Add</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeamHubPage;
