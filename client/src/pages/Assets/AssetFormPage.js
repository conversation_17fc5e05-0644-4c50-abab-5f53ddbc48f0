import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Autocomplete,
  Chip,
  Stepper,
  Step,
  StepLabel,
  Alert,
  Card,
  CardContent,
  Divider,
  InputAdornment,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import assetService from '../../services/assetService';
import userService from '../../services/userService';

const AssetFormPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);

  // Form state
  const [formData, setFormData] = useState({
    // Basic Information
    name: '',
    assetTag: '',
    serialNumber: '',
    model: '',
    manufacturer: '',
    description: '',
    
    // Categorization
    category: '',
    tags: [],
    
    // Status and Location
    status: 'active',
    condition: 'good',
    location: '',
    assignedTo: '',
    department: '',
    
    // Financial Information
    purchasePrice: '',
    currentValue: '',
    depreciationRate: '',
    purchaseDate: null,
    warrantyExpiration: null,
    vendor: '',
    poNumber: '',
    
    // Technical Specifications
    specifications: {
      cpu: '',
      ram: '',
      storage: '',
      os: '',
      ipAddress: '',
      macAddress: '',
      hostname: '',
      customFields: []
    },
    
    // Maintenance
    maintenanceSchedule: 'annual',
    maintenanceNotes: '',
    
    // Insurance and Compliance
    insuranceValue: '',
    complianceNotes: '',
    riskLevel: 'low'
  });

  // Supporting data
  const [categories, setCategories] = useState([]);
  const [locations, setLocations] = useState([]);
  const [users, setUsers] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [autoGenerate, setAutoGenerate] = useState(!isEditing);

  const steps = [
    'Basic Information',
    'Categorization & Location',
    'Financial Details',
    'Technical Specifications',
    'Maintenance & Compliance'
  ];

  const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'in_maintenance', label: 'In Maintenance' },
    { value: 'retired', label: 'Retired' },
    { value: 'disposed', label: 'Disposed' },
    { value: 'lost', label: 'Lost' },
    { value: 'stolen', label: 'Stolen' },
    { value: 'on_loan', label: 'On Loan' },
    { value: 'reserved', label: 'Reserved' }
  ];

  const conditionOptions = [
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
    { value: 'needs_repair', label: 'Needs Repair' }
  ];

  const maintenanceScheduleOptions = [
    { value: 'none', label: 'None' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'semi_annual', label: 'Semi-Annual' },
    { value: 'annual', label: 'Annual' },
    { value: 'custom', label: 'Custom' }
  ];

  const riskLevelOptions = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' }
  ];

  useEffect(() => {
    loadSupportingData();
    if (isEditing) {
      loadAsset();
    }
  }, [id, isEditing]);

  const loadSupportingData = async () => {
    try {
      const [categoriesData, locationsData, usersData] = await Promise.all([
        assetService.getCategories(),
        assetService.getLocations(),
        userService.getAllUsers()
      ]);

      setCategories(categoriesData.categories || []);
      setLocations(locationsData.locations || []);
      // usersData may be an array or an object with a users field
      const resolvedUsers = Array.isArray(usersData) ? usersData : (usersData?.users || []);
      setUsers(resolvedUsers);
    } catch (error) {
      console.error('Error loading supporting data:', error);
      setError('Failed to load form data');
    }
  };

  const loadAsset = async () => {
    try {
      setLoading(true);
      const asset = await assetService.getAsset(id);
      
      setFormData({
        ...asset,
        purchaseDate: asset.purchaseDate ? new Date(asset.purchaseDate) : null,
        warrantyExpiration: asset.warrantyExpiration ? new Date(asset.warrantyExpiration) : null,
        category: asset.category?._id || '',
        location: asset.location?._id || '',
        assignedTo: asset.assignedTo?._id || '',
        specifications: asset.specifications || {
          cpu: '',
          ram: '',
          storage: '',
          os: '',
          ipAddress: '',
          macAddress: '',
          hostname: '',
          customFields: []
        }
      });
    } catch (error) {
      console.error('Error loading asset:', error);
      setError('Failed to load asset');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const submitData = {
        ...formData,
        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : null,
        currentValue: formData.currentValue ? parseFloat(formData.currentValue) : null,
        depreciationRate: formData.depreciationRate ? parseFloat(formData.depreciationRate) : null,
        insuranceValue: formData.insuranceValue ? parseFloat(formData.insuranceValue) : null
      };

      if (isEditing) {
        await assetService.updateAsset(id, submitData);
      } else {
        await assetService.createAsset(submitData);
      }

      setSuccess(true);
      setTimeout(() => {
        navigate('/assets');
      }, 2000);
    } catch (error) {
      console.error('Error saving asset:', error);
      setError(error.response?.data?.message || 'Failed to save asset');
    } finally {
      setLoading(false);
    }
  };

  const renderBasicInformation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Asset Name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Asset Tag"
          value={formData.assetTag}
          onChange={(e) => handleInputChange('assetTag', e.target.value)}
          required
          InputProps={{
            endAdornment: !isEditing && (
              <InputAdornment position="end">
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoGenerate}
                      onChange={(e) => setAutoGenerate(e.target.checked)}
                      size="small"
                    />
                  }
                  label="Auto"
                  sx={{ ml: 1 }}
                />
              </InputAdornment>
            )
          }}
          disabled={autoGenerate && !isEditing}
          helperText={autoGenerate && !isEditing ? "Will be auto-generated based on category" : ""}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Serial Number"
          value={formData.serialNumber}
          onChange={(e) => handleInputChange('serialNumber', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Model"
          value={formData.model}
          onChange={(e) => handleInputChange('model', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Manufacturer"
          value={formData.manufacturer}
          onChange={(e) => handleInputChange('manufacturer', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Status</InputLabel>
          <Select
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            label="Status"
          >
            {statusOptions.map(option => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          multiline
          rows={3}
        />
      </Grid>
    </Grid>
  );

  const renderCategorizationLocation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth required>
          <InputLabel>Category</InputLabel>
          <Select
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
            label="Category"
          >
            {categories.map(category => (
              <MenuItem key={category._id} value={category._id}>
                {category.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Condition</InputLabel>
          <Select
            value={formData.condition}
            onChange={(e) => handleInputChange('condition', e.target.value)}
            label="Condition"
          >
            {conditionOptions.map(option => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Location</InputLabel>
          <Select
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            label="Location"
          >
            {locations.map(location => (
              <MenuItem key={location._id} value={location._id}>
                {location.name} ({location.locationType})
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Assigned To</InputLabel>
          <Select
            value={formData.assignedTo}
            onChange={(e) => handleInputChange('assignedTo', e.target.value)}
            label="Assigned To"
          >
            <MenuItem value="">Unassigned</MenuItem>
            {users.map(user => (
              <MenuItem key={user._id} value={user._id}>
                {user.name} - {user.email}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Department"
          value={formData.department}
          onChange={(e) => handleInputChange('department', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Autocomplete
          multiple
          options={availableTags}
          freeSolo
          value={formData.tags}
          onChange={(event, newValue) => handleInputChange('tags', newValue)}
          renderTags={(value, getTagProps) =>
            value.map((option, index) => (
              <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />
            ))
          }
          renderInput={(params) => (
            <TextField
              {...params}
              label="Tags"
              placeholder="Add tags..."
              helperText="Press Enter to add custom tags"
            />
          )}
        />
      </Grid>
    </Grid>
  );

  const renderFinancialDetails = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Purchase Price"
          type="number"
          value={formData.purchasePrice}
          onChange={(e) => handleInputChange('purchasePrice', e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>
          }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Current Value"
          type="number"
          value={formData.currentValue}
          onChange={(e) => handleInputChange('currentValue', e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>
          }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <DatePicker
          label="Purchase Date"
          value={formData.purchaseDate}
          onChange={(newValue) => handleInputChange('purchaseDate', newValue)}
          slotProps={{ textField: { fullWidth: true } }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <DatePicker
          label="Warranty Expiration"
          value={formData.warrantyExpiration}
          onChange={(newValue) => handleInputChange('warrantyExpiration', newValue)}
          slotProps={{ textField: { fullWidth: true } }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Depreciation Rate"
          type="number"
          value={formData.depreciationRate}
          onChange={(e) => handleInputChange('depreciationRate', e.target.value)}
          InputProps={{
            endAdornment: <InputAdornment position="end">%</InputAdornment>
          }}
          inputProps={{ min: 0, max: 100 }}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Vendor"
          value={formData.vendor}
          onChange={(e) => handleInputChange('vendor', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="PO Number"
          value={formData.poNumber}
          onChange={(e) => handleInputChange('poNumber', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Insurance Value"
          type="number"
          value={formData.insuranceValue}
          onChange={(e) => handleInputChange('insuranceValue', e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>
          }}
        />
      </Grid>
    </Grid>
  );

  const renderTechnicalSpecifications = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="CPU"
          value={formData.specifications.cpu}
          onChange={(e) => handleInputChange('specifications.cpu', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="RAM"
          value={formData.specifications.ram}
          onChange={(e) => handleInputChange('specifications.ram', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Storage"
          value={formData.specifications.storage}
          onChange={(e) => handleInputChange('specifications.storage', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Operating System"
          value={formData.specifications.os}
          onChange={(e) => handleInputChange('specifications.os', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="IP Address"
          value={formData.specifications.ipAddress}
          onChange={(e) => handleInputChange('specifications.ipAddress', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="MAC Address"
          value={formData.specifications.macAddress}
          onChange={(e) => handleInputChange('specifications.macAddress', e.target.value)}
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Hostname"
          value={formData.specifications.hostname}
          onChange={(e) => handleInputChange('specifications.hostname', e.target.value)}
        />
      </Grid>
    </Grid>
  );

  const renderMaintenanceCompliance = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Maintenance Schedule</InputLabel>
          <Select
            value={formData.maintenanceSchedule}
            onChange={(e) => handleInputChange('maintenanceSchedule', e.target.value)}
            label="Maintenance Schedule"
          >
            {maintenanceScheduleOptions.map(option => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Risk Level</InputLabel>
          <Select
            value={formData.riskLevel}
            onChange={(e) => handleInputChange('riskLevel', e.target.value)}
            label="Risk Level"
          >
            {riskLevelOptions.map(option => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Maintenance Notes"
          value={formData.maintenanceNotes}
          onChange={(e) => handleInputChange('maintenanceNotes', e.target.value)}
          multiline
          rows={3}
        />
      </Grid>
      
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Compliance Notes"
          value={formData.complianceNotes}
          onChange={(e) => handleInputChange('complianceNotes', e.target.value)}
          multiline
          rows={3}
        />
      </Grid>
    </Grid>
  );

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return renderBasicInformation();
      case 1:
        return renderCategorizationLocation();
      case 2:
        return renderFinancialDetails();
      case 3:
        return renderTechnicalSpecifications();
      case 4:
        return renderMaintenanceCompliance();
      default:
        return null;
    }
  };

  if (loading && isEditing) {
    return (
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Container maxWidth="lg">
          <Box sx={{ py: 3, textAlign: 'center' }}>
            <Typography>Loading asset...</Typography>
          </Box>
        </Container>
      </LocalizationProvider>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <Box display="flex" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/assets')}
            sx={{ mr: 2 }}
          >
            Back to Assets
          </Button>
          <Typography variant="h4" component="h1">
            {isEditing ? 'Edit Asset' : 'Add New Asset'}
          </Typography>
        </Box>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Asset {isEditing ? 'updated' : 'created'} successfully! Redirecting...
          </Alert>
        )}

        {/* Stepper */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Stepper activeStep={activeStep} alternativeLabel>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </CardContent>
        </Card>

        {/* Form */}
        <Paper elevation={3}>
          <Box component="form" onSubmit={handleSubmit} sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {steps[activeStep]}
            </Typography>
            
            <Divider sx={{ mb: 3 }} />
            
            {renderStepContent(activeStep)}
            
            {/* Navigation Buttons */}
            <Box display="flex" justifyContent="space-between" mt={4}>
              <Button
                disabled={activeStep === 0}
                onClick={() => setActiveStep(activeStep - 1)}
              >
                Previous
              </Button>
              
              <Box display="flex" gap={2}>
                <Button
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={() => navigate('/assets')}
                >
                  Cancel
                </Button>
                
                {activeStep === steps.length - 1 ? (
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SaveIcon />}
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : (isEditing ? 'Update Asset' : 'Create Asset')}
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(activeStep + 1)}
                  >
                    Next
                  </Button>
                )}
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
    </LocalizationProvider>
  );
};

export default AssetFormPage;