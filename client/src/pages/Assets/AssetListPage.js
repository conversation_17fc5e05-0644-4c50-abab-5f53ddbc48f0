import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  QrCode as QrCodeIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  Print as PrintIcon,
  GetApp as ExportIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import assetService from '../../services/assetService';

const AssetListPage = () => {
  const navigate = useNavigate();
  
  // State management
  const [assets, setAssets] = useState([]);
  const [categories, setCategories] = useState([]);
  const [locations, setLocations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Pagination
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  // View mode
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'
  
  // Dialog states
  const [qrDialogOpen, setQrDialogOpen] = useState(false);
  const [selectedAssetForQR, setSelectedAssetForQR] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState(null);

  const statusColors = {
    active: 'success',
    inactive: 'default',
    in_maintenance: 'warning',
    retired: 'secondary',
    disposed: 'error',
    lost: 'error',
    stolen: 'error',
    on_loan: 'info',
    reserved: 'primary'
  };

  const conditionColors = {
    excellent: 'success',
    good: 'primary',
    fair: 'warning',
    poor: 'error',
    needs_repair: 'error'
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    loadAssets();
  }, [page, limit, searchTerm, selectedCategory, selectedStatus, selectedLocation]);

  const loadInitialData = async () => {
    try {
      const [categoriesData, locationsData] = await Promise.all([
        assetService.getCategories(),
        assetService.getLocations()
      ]);
      
      setCategories(categoriesData.categories || []);
      setLocations(locationsData.locations || []);
    } catch (error) {
      console.error('Error loading initial data:', error);
      setError('Failed to load categories and locations');
    }
  };

  const loadAssets = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit,
        ...(searchTerm && { search: searchTerm }),
        ...(selectedCategory && { category: selectedCategory }),
        ...(selectedStatus && { status: selectedStatus }),
        ...(selectedLocation && { location: selectedLocation })
      };

      const data = await assetService.getAssets(params);
      setAssets(data.assets);
      setTotalItems(data.pagination.totalItems);
    } catch (error) {
      console.error('Error loading assets:', error);
      setError('Failed to load assets');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleFilterChange = (filterType, value) => {
    setPage(0);
    switch (filterType) {
      case 'category':
        setSelectedCategory(value);
        break;
      case 'status':
        setSelectedStatus(value);
        break;
      case 'location':
        setSelectedLocation(value);
        break;
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedStatus('');
    setSelectedLocation('');
    setPage(0);
  };

  const handleDeleteAsset = async () => {
    if (!assetToDelete) return;
    
    try {
      await assetService.deleteAsset(assetToDelete.id);
      setDeleteDialogOpen(false);
      setAssetToDelete(null);
      loadAssets();
    } catch (error) {
      console.error('Error deleting asset:', error);
      setError('Failed to delete asset');
    }
  };

  const handleShowQRCode = (asset) => {
    setSelectedAssetForQR(asset);
    setQrDialogOpen(true);
  };

  const renderAssetCard = (asset) => (
    <Grid item xs={12} sm={6} md={4} key={asset._id}>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Typography variant="h6" component="h3" gutterBottom>
              {asset.name}
            </Typography>
            <Box>
              <Chip 
                label={asset.status} 
                color={statusColors[asset.status]} 
                size="small"
              />
            </Box>
          </Box>
          
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Asset Tag: {asset.assetTag}
          </Typography>
          
          {asset.serialNumber && (
            <Typography variant="body2" color="text.secondary" gutterBottom>
              S/N: {asset.serialNumber}
            </Typography>
          )}
          
          {asset.category && (
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Category: {asset.category.name}
            </Typography>
          )}
          
          {asset.location && (
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Location: {asset.location.name}
            </Typography>
          )}
          
          {asset.assignedTo && (
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Assigned to: {asset.assignedTo.name}
            </Typography>
          )}
          
          <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
            <Chip 
              label={asset.condition} 
              color={conditionColors[asset.condition]} 
              size="small"
            />
            
            <Box>
              <Tooltip title="View QR Code">
                <IconButton size="small" onClick={() => handleShowQRCode(asset)}>
                  <QrCodeIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit Asset">
                <IconButton size="small" onClick={() => navigate(`/assets/${asset._id}/edit`)}>
                  <EditIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="View History">
                <IconButton size="small" onClick={() => navigate(`/assets/${asset._id}/history`)}>
                  <HistoryIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Grid>
  );

  const renderAssetTable = () => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Asset Tag</TableCell>
            <TableCell>Name</TableCell>
            <TableCell>Category</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Condition</TableCell>
            <TableCell>Location</TableCell>
            <TableCell>Assigned To</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {assets.map((asset) => (
            <TableRow key={asset._id} hover>
              <TableCell>
                <Typography variant="body2" fontFamily="monospace">
                  {asset.assetTag}
                </Typography>
              </TableCell>
              <TableCell>
                <Typography variant="body2" fontWeight="medium">
                  {asset.name}
                </Typography>
                {asset.serialNumber && (
                  <Typography variant="caption" color="text.secondary" display="block">
                    S/N: {asset.serialNumber}
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                {asset.category?.name || 'Uncategorized'}
              </TableCell>
              <TableCell>
                <Chip 
                  label={asset.status} 
                  color={statusColors[asset.status]} 
                  size="small"
                />
              </TableCell>
              <TableCell>
                <Chip 
                  label={asset.condition} 
                  color={conditionColors[asset.condition]} 
                  size="small"
                />
              </TableCell>
              <TableCell>
                {asset.location?.name || 'Unassigned'}
              </TableCell>
              <TableCell>
                {asset.assignedTo?.name || 'Unassigned'}
              </TableCell>
              <TableCell>
                <Box display="flex" gap={1}>
                  <Tooltip title="View QR Code">
                    <IconButton size="small" onClick={() => handleShowQRCode(asset)}>
                      <QrCodeIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit Asset">
                    <IconButton size="small" onClick={() => navigate(`/assets/${asset._id}/edit`)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="View History">
                    <IconButton size="small" onClick={() => navigate(`/assets/${asset._id}/history`)}>
                      <HistoryIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete Asset">
                    <IconButton 
                      size="small" 
                      onClick={() => {
                        setAssetToDelete(asset);
                        setDeleteDialogOpen(true);
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <TablePagination
        rowsPerPageOptions={[10, 20, 50, 100]}
        component="div"
        count={totalItems}
        rowsPerPage={limit}
        page={page}
        onPageChange={(event, newPage) => setPage(newPage)}
        onRowsPerPageChange={(event) => {
          setLimit(parseInt(event.target.value, 10));
          setPage(0);
        }}
      />
    </TableContainer>
  );

  if (error) {
    return (
      <Container maxWidth="lg">
        <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
          <Typography color="error">{error}</Typography>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Asset Management
          </Typography>
          
          <Box display="flex" gap={2} alignItems="center">
            <Button
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={() => navigate('/assets/export')}
            >
              Export
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/assets/new')}
            >
              Add Asset
            </Button>
          </Box>
        </Box>

        {/* Search and Filters */}
        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Box display="flex" gap={2} alignItems="center" mb={2}>
            <TextField
              placeholder="Search assets..."
              value={searchTerm}
              onChange={handleSearch}
              InputProps={{
                startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
              }}
              size="small"
              sx={{ flexGrow: 1, maxWidth: 400 }}
            />
            
            <Button
              variant="outlined"
              startIcon={<FilterIcon />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filters
            </Button>
            
            <Box>
              <IconButton 
                onClick={() => setViewMode('table')}
                color={viewMode === 'table' ? 'primary' : 'default'}
              >
                <ViewListIcon />
              </IconButton>
              <IconButton 
                onClick={() => setViewMode('cards')}
                color={viewMode === 'cards' ? 'primary' : 'default'}
              >
                <ViewModuleIcon />
              </IconButton>
            </Box>
          </Box>

          {showFilters && (
            <Accordion expanded={showFilters}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Filters</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={selectedCategory}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                        label="Category"
                      >
                        <MenuItem value="">All Categories</MenuItem>
                        {categories.map(category => (
                          <MenuItem key={category._id} value={category._id}>
                            {category.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={selectedStatus}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                        label="Status"
                      >
                        <MenuItem value="">All Statuses</MenuItem>
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                        <MenuItem value="in_maintenance">In Maintenance</MenuItem>
                        <MenuItem value="retired">Retired</MenuItem>
                        <MenuItem value="disposed">Disposed</MenuItem>
                        <MenuItem value="lost">Lost</MenuItem>
                        <MenuItem value="stolen">Stolen</MenuItem>
                        <MenuItem value="on_loan">On Loan</MenuItem>
                        <MenuItem value="reserved">Reserved</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Location</InputLabel>
                      <Select
                        value={selectedLocation}
                        onChange={(e) => handleFilterChange('location', e.target.value)}
                        label="Location"
                      >
                        <MenuItem value="">All Locations</MenuItem>
                        {locations.map(location => (
                          <MenuItem key={location._id} value={location._id}>
                            {location.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      onClick={clearFilters}
                      fullWidth
                    >
                      Clear Filters
                    </Button>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          )}
        </Paper>

        {/* Asset List */}
        {loading ? (
          <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
            <Typography>Loading assets...</Typography>
          </Paper>
        ) : assets.length === 0 ? (
          <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
            <Typography color="text.secondary" gutterBottom>
              No assets found
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/assets/new')}
            >
              Add Your First Asset
            </Button>
          </Paper>
        ) : viewMode === 'cards' ? (
          <Grid container spacing={2}>
            {assets.map(renderAssetCard)}
          </Grid>
        ) : (
          renderAssetTable()
        )}

        {/* QR Code Dialog */}
        <Dialog open={qrDialogOpen} onClose={() => setQrDialogOpen(false)}>
          <DialogTitle>QR Code for {selectedAssetForQR?.name}</DialogTitle>
          <DialogContent>
            {selectedAssetForQR && (
              <Box textAlign="center">
                <img
                  src={`/api/assets/${selectedAssetForQR._id}/qrcode?size=300`}
                  alt={`QR Code for ${selectedAssetForQR.name}`}
                  style={{ maxWidth: '100%' }}
                />
                <Typography variant="body2" color="text.secondary" mt={2}>
                  Asset Tag: {selectedAssetForQR.assetTag}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setQrDialogOpen(false)}>Close</Button>
            <Button
              variant="contained"
              startIcon={<PrintIcon />}
              onClick={() => window.print()}
            >
              Print
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Asset</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete "{assetToDelete?.name}"? This action can be undone later.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button
              variant="contained"
              color="error"
              onClick={handleDeleteAsset}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default AssetListPage;