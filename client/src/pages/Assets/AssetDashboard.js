import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Alert
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Assignment as AssignmentIcon,
  LocationOn as LocationIcon,
  Category as CategoryIcon,
  AttachMoney as MoneyIcon,
  Build as BuildIcon,
  Inventory as InventoryIcon,
  Assessment as ReportIcon,
  Add as AddIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as ChartTooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import assetService from '../../services/assetService';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const AssetDashboard = () => {
  const navigate = useNavigate();

  // State for dashboard data
  const [dashboardData, setDashboardData] = useState({
    totalAssets: 0,
    totalValue: 0,
    avgValue: 0,
    assetsNeedingMaintenance: 0,
    overdueMaintenanceCount: 0,
    upcomingMaintenanceCount: 0,
    assetsByStatus: [],
    assetsByCategory: [],
    assetsByLocation: [],
    assetsByCondition: [],
    recentAssets: [],
    overdueMaintenanceItems: [],
    upcomingMaintenanceItems: [],
    valueOverTime: []
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load main asset data
      const [
        assetsResponse,
        categoriesResponse,
        locationsResponse,
        overdueMaintenanceResponse,
        upcomingMaintenanceResponse
      ] = await Promise.all([
        assetService.getAssets({ limit: 1000 }), // Get all assets for calculations
        assetService.getCategoryStats(),
        assetService.getLocationStats(),
        assetService.getOverdueMaintenanceRecords({ limit: 10 }),
        assetService.getUpcomingMaintenanceRecords({ limit: 10 })
      ]);

      const assets = assetsResponse.assets || [];
      
      // Calculate dashboard statistics
      const totalAssets = assets.length;
      const totalValue = assets.reduce((sum, asset) => sum + (asset.currentValue || asset.purchasePrice || 0), 0);
      const avgValue = totalAssets > 0 ? totalValue / totalAssets : 0;
      
      // Group by status
      const statusGroups = assets.reduce((acc, asset) => {
        acc[asset.status] = (acc[asset.status] || 0) + 1;
        return acc;
      }, {});
      
      const assetsByStatus = Object.entries(statusGroups).map(([status, count]) => ({
        name: status,
        value: count,
        percentage: ((count / totalAssets) * 100).toFixed(1)
      }));

      // Group by condition
      const conditionGroups = assets.reduce((acc, asset) => {
        acc[asset.condition] = (acc[asset.condition] || 0) + 1;
        return acc;
      }, {});
      
      const assetsByCondition = Object.entries(conditionGroups).map(([condition, count]) => ({
        name: condition,
        value: count,
        percentage: ((count / totalAssets) * 100).toFixed(1)
      }));

      // Recent assets (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentAssets = assets
        .filter(asset => new Date(asset.createdAt) > thirtyDaysAgo)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);

      setDashboardData({
        totalAssets,
        totalValue,
        avgValue,
        assetsNeedingMaintenance: assets.filter(a => a.condition === 'needs_repair' || a.status === 'in_maintenance').length,
        overdueMaintenanceCount: overdueMaintenanceResponse.overdueRecords?.length || 0,
        upcomingMaintenanceCount: upcomingMaintenanceResponse.upcomingRecords?.length || 0,
        assetsByStatus,
        assetsByCategory: categoriesResponse.topCategories || [],
        assetsByLocation: locationsResponse.topLocations || [],
        assetsByCondition,
        recentAssets,
        overdueMaintenanceItems: overdueMaintenanceResponse.overdueRecords || [],
        upcomingMaintenanceItems: upcomingMaintenanceResponse.upcomingRecords || []
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'success',
      inactive: 'default',
      in_maintenance: 'warning',
      retired: 'secondary',
      disposed: 'error',
      lost: 'error',
      stolen: 'error',
      on_loan: 'info',
      reserved: 'primary'
    };
    return colors[status] || 'default';
  };

  const getConditionColor = (condition) => {
    const colors = {
      excellent: 'success',
      good: 'primary',
      fair: 'warning',
      poor: 'error',
      needs_repair: 'error'
    };
    return colors[condition] || 'default';
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ py: 3, textAlign: 'center' }}>
          <LinearProgress sx={{ mb: 2 }} />
          <Typography>Loading dashboard...</Typography>
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Asset Dashboard
          </Typography>
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<ReportIcon />}
              onClick={() => navigate('/assets/reports')}
            >
              Reports
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/assets/new')}
            >
              Add Asset
            </Button>
          </Box>
        </Box>

        {/* Key Metrics Cards */}
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <InventoryIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {dashboardData.totalAssets}
                    </Typography>
                    <Typography color="text.secondary">
                      Total Assets
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <MoneyIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {formatCurrency(dashboardData.totalValue)}
                    </Typography>
                    <Typography color="text.secondary">
                      Total Value
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <BuildIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {dashboardData.assetsNeedingMaintenance}
                    </Typography>
                    <Typography color="text.secondary">
                      Need Maintenance
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {formatCurrency(dashboardData.avgValue)}
                    </Typography>
                    <Typography color="text.secondary">
                      Avg. Asset Value
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Charts Row */}
        <Grid container spacing={3} mb={3}>
          {/* Asset Status Distribution */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Assets by Status
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={dashboardData.assetsByStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} (${percentage}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dashboardData.assetsByStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <ChartTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Asset Condition Distribution */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Assets by Condition
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={dashboardData.assetsByCondition}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <ChartTooltip />
                    <Bar dataKey="value" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tables Row */}
        <Grid container spacing={3} mb={3}>
          {/* Top Categories */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    Top Categories
                  </Typography>
                  <IconButton onClick={() => navigate('/assets/categories')}>
                    <ViewIcon />
                  </IconButton>
                </Box>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Category</TableCell>
                        <TableCell align="right">Assets</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboardData.assetsByCategory.slice(0, 5).map((category) => (
                        <TableRow key={category._id}>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <CategoryIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              {category.name}
                            </Box>
                          </TableCell>
                          <TableCell align="right">{category.assetCount}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Top Locations */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    Top Locations
                  </Typography>
                  <IconButton onClick={() => navigate('/assets/locations')}>
                    <ViewIcon />
                  </IconButton>
                </Box>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Location</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell align="right">Assets</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboardData.assetsByLocation.slice(0, 5).map((location) => (
                        <TableRow key={location._id}>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              {location.name}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={location.locationType} 
                              size="small" 
                              variant="outlined" 
                            />
                          </TableCell>
                          <TableCell align="right">{location.assetCount}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Maintenance and Recent Activity Row */}
        <Grid container spacing={3}>
          {/* Overdue Maintenance */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '400px' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" color="error">
                    Overdue Maintenance
                  </Typography>
                  <Chip 
                    label={dashboardData.overdueMaintenanceCount} 
                    color="error" 
                    size="small"
                  />
                </Box>
                <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {dashboardData.overdueMaintenanceItems.length === 0 ? (
                    <ListItem>
                      <ListItemText 
                        primary="No overdue maintenance" 
                        secondary="All maintenance is up to date"
                      />
                    </ListItem>
                  ) : (
                    dashboardData.overdueMaintenanceItems.map((item) => (
                      <ListItem key={item._id}>
                        <ListItemIcon>
                          <WarningIcon color="error" />
                        </ListItemIcon>
                        <ListItemText
                          primary={item.asset.name}
                          secondary={`Due: ${assetService.formatDate(item.scheduledDate)}`}
                        />
                      </ListItem>
                    ))
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Upcoming Maintenance */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '400px' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" color="warning.main">
                    Upcoming Maintenance
                  </Typography>
                  <Chip 
                    label={dashboardData.upcomingMaintenanceCount} 
                    color="warning" 
                    size="small"
                  />
                </Box>
                <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {dashboardData.upcomingMaintenanceItems.length === 0 ? (
                    <ListItem>
                      <ListItemText 
                        primary="No upcoming maintenance" 
                        secondary="No maintenance scheduled for the next 30 days"
                      />
                    </ListItem>
                  ) : (
                    dashboardData.upcomingMaintenanceItems.map((item) => (
                      <ListItem key={item._id}>
                        <ListItemIcon>
                          <ScheduleIcon color="warning" />
                        </ListItemIcon>
                        <ListItemText
                          primary={item.asset.name}
                          secondary={`Scheduled: ${assetService.formatDate(item.scheduledDate)}`}
                        />
                      </ListItem>
                    ))
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Assets */}
          <Grid item xs={12} md={4}>
            <Card sx={{ height: '400px' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    Recently Added
                  </Typography>
                  <IconButton onClick={() => navigate('/assets')}>
                    <ViewIcon />
                  </IconButton>
                </Box>
                <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                  {dashboardData.recentAssets.length === 0 ? (
                    <ListItem>
                      <ListItemText 
                        primary="No recent assets" 
                        secondary="No assets added in the last 30 days"
                      />
                    </ListItem>
                  ) : (
                    dashboardData.recentAssets.map((asset) => (
                      <ListItem 
                        key={asset._id}
                        button
                        onClick={() => navigate(`/assets/${asset._id}`)}
                      >
                        <ListItemIcon>
                          <AssignmentIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary={asset.name}
                          secondary={
                            <Box>
                              <Typography variant="caption" display="block">
                                {asset.assetTag}
                              </Typography>
                              <Chip 
                                label={asset.status} 
                                color={getStatusColor(asset.status)}
                                size="small"
                              />
                            </Box>
                          }
                        />
                      </ListItem>
                    ))
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default AssetDashboard;