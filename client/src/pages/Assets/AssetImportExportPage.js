import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Stepper,
  Step,
  StepLabel,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CloudDownload as DownloadIcon,
  GetApp as GetTemplateIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import assetService from '../../services/assetService';

const AssetImportExportPage = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef(null);

  // State management
  const [activeTab, setActiveTab] = useState('export'); // 'export' or 'import'
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Export states
  const [exportFilters, setExportFilters] = useState({
    format: 'csv',
    includeDeleted: false,
    category: '',
    location: '',
    status: '',
    fields: 'all',
    startDate: '',
    endDate: ''
  });

  // Import states
  const [importFile, setImportFile] = useState(null);
  const [importOptions, setImportOptions] = useState({
    updateExisting: false,
    skipErrors: true,
    defaultCategory: '',
    defaultLocation: '',
    defaultStatus: 'active'
  });
  const [validationResult, setValidationResult] = useState(null);
  const [importResult, setImportResult] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);

  // Supporting data
  const [categories, setCategories] = useState([]);
  const [locations, setLocations] = useState([]);

  const importSteps = ['Upload File', 'Validate Data', 'Configure Import', 'Complete Import'];

  useEffect(() => {
    loadSupportingData();
  }, []);

  const loadSupportingData = async () => {
    try {
      const [categoriesData, locationsData] = await Promise.all([
        assetService.getCategories(),
        assetService.getLocations()
      ]);

      setCategories(categoriesData.categories || []);
      setLocations(locationsData.locations || []);
    } catch (error) {
      console.error('Error loading supporting data:', error);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build export parameters
      const params = new URLSearchParams();
      Object.keys(exportFilters).forEach(key => {
        if (exportFilters[key]) {
          params.append(key, exportFilters[key]);
        }
      });

      // Make export request
      const response = await fetch(`/api/assets/export?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Export failed');
      }

      if (exportFilters.format === 'json') {
        const data = await response.json();
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `assets-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `assets-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

      setSuccess('Assets exported successfully!');
    } catch (error) {
      console.error('Error exporting assets:', error);
      setError('Failed to export assets');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = async (includeExamples = false) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({ includeExamples });
      
      const response = await fetch(`/api/assets/import/template?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Template download failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `asset-import-template-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setSuccess('Import template downloaded successfully!');
    } catch (error) {
      console.error('Error downloading template:', error);
      setError('Failed to download template');
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setImportFile(file);
      setCurrentStep(1);
    }
  };

  const validateImportFile = async () => {
    if (!importFile) return;

    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();
      formData.append('file', importFile);

      const response = await fetch('/api/assets/import/validate', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Validation failed');
      }

      const result = await response.json();
      setValidationResult(result);

      if (result.isValid) {
        setCurrentStep(2);
      } else {
        setError('File validation failed. Please fix the errors and try again.');
      }
    } catch (error) {
      console.error('Error validating file:', error);
      setError('Failed to validate import file');
    } finally {
      setLoading(false);
    }
  };

  const executeImport = async () => {
    if (!importFile) return;

    try {
      setLoading(true);
      setError(null);

      const formData = new FormData();
      formData.append('file', importFile);
      
      Object.keys(importOptions).forEach(key => {
        formData.append(key, importOptions[key]);
      });

      const response = await fetch('/api/assets/import', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Import failed');
      }

      const result = await response.json();
      setImportResult(result);
      setCurrentStep(3);

      if (result.successful > 0) {
        setSuccess(`Import completed! ${result.successful}/${result.total} assets processed successfully.`);
      }
    } catch (error) {
      console.error('Error importing assets:', error);
      setError('Failed to import assets');
    } finally {
      setLoading(false);
    }
  };

  const resetImport = () => {
    setImportFile(null);
    setValidationResult(null);
    setImportResult(null);
    setCurrentStep(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const renderExportTab = () => (
    <Grid container spacing={3}>
      {/* Export Configuration */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardHeader title="Export Configuration" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Format</InputLabel>
                  <Select
                    value={exportFilters.format}
                    onChange={(e) => setExportFilters(prev => ({ ...prev, format: e.target.value }))}
                    label="Format"
                  >
                    <MenuItem value="csv">CSV</MenuItem>
                    <MenuItem value="json">JSON</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={exportFilters.category}
                    onChange={(e) => setExportFilters(prev => ({ ...prev, category: e.target.value }))}
                    label="Category"
                  >
                    <MenuItem value="">All Categories</MenuItem>
                    {categories.map(category => (
                      <MenuItem key={category._id} value={category._id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Location</InputLabel>
                  <Select
                    value={exportFilters.location}
                    onChange={(e) => setExportFilters(prev => ({ ...prev, location: e.target.value }))}
                    label="Location"
                  >
                    <MenuItem value="">All Locations</MenuItem>
                    {locations.map(location => (
                      <MenuItem key={location._id} value={location._id}>
                        {location.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={exportFilters.status}
                    onChange={(e) => setExportFilters(prev => ({ ...prev, status: e.target.value }))}
                    label="Status"
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="in_maintenance">In Maintenance</MenuItem>
                    <MenuItem value="retired">Retired</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Start Date"
                  type="date"
                  value={exportFilters.startDate}
                  onChange={(e) => setExportFilters(prev => ({ ...prev, startDate: e.target.value }))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="End Date"
                  type="date"
                  value={exportFilters.endDate}
                  onChange={(e) => setExportFilters(prev => ({ ...prev, endDate: e.target.value }))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={exportFilters.includeDeleted}
                      onChange={(e) => setExportFilters(prev => ({ ...prev, includeDeleted: e.target.checked }))}
                    />
                  }
                  label="Include Deleted Assets"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Export Actions */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardHeader title="Export Actions" />
          <CardContent>
            <Box display="flex" flexDirection="column" gap={2}>
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={handleExport}
                disabled={loading}
                fullWidth
              >
                Export Assets
              </Button>

              <Button
                variant="outlined"
                startIcon={<GetTemplateIcon />}
                onClick={() => handleDownloadTemplate(false)}
                disabled={loading}
                fullWidth
              >
                Download Template
              </Button>

              <Button
                variant="outlined"
                startIcon={<GetTemplateIcon />}
                onClick={() => handleDownloadTemplate(true)}
                disabled={loading}
                fullWidth
              >
                Download Template with Examples
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Export Help */}
        <Card sx={{ mt: 2 }}>
          <CardHeader title="Export Help" />
          <CardContent>
            <Typography variant="body2" paragraph>
              Use the export feature to download your asset data in CSV or JSON format for backup, analysis, or migration purposes.
            </Typography>
            <Typography variant="body2" paragraph>
              You can filter the export by category, location, status, or date range to get exactly the data you need.
            </Typography>
            <Typography variant="body2">
              The CSV format is compatible with Excel and other spreadsheet applications.
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderImportTab = () => (
    <Box>
      {/* Import Stepper */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stepper activeStep={currentStep} alternativeLabel>
            {importSteps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Step Content */}
      {currentStep === 0 && (
        <Card>
          <CardHeader title="Upload Import File" />
          <CardContent>
            <Box textAlign="center" py={4}>
              <input
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
                ref={fileInputRef}
              />
              <Button
                variant="outlined"
                startIcon={<UploadIcon />}
                onClick={() => fileInputRef.current?.click()}
                size="large"
                sx={{ mb: 2 }}
              >
                Choose File
              </Button>
              <Typography variant="body2" color="text.secondary">
                Upload a CSV or Excel file containing asset data
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {currentStep === 1 && importFile && (
        <Card>
          <CardHeader title="File Validation" />
          <CardContent>
            <Box mb={3}>
              <Typography variant="body1" gutterBottom>
                Selected File: <strong>{importFile.name}</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Size: {(importFile.size / 1024 / 1024).toFixed(2)} MB
              </Typography>
            </Box>

            {!validationResult ? (
              <Box textAlign="center">
                <Button
                  variant="contained"
                  onClick={validateImportFile}
                  disabled={loading}
                >
                  {loading ? 'Validating...' : 'Validate File'}
                </Button>
              </Box>
            ) : (
              <Box>
                <Alert 
                  severity={validationResult.isValid ? 'success' : 'error'} 
                  sx={{ mb: 2 }}
                >
                  {validationResult.isValid ? 
                    `File is valid and contains ${validationResult.rowCount} rows` :
                    'File validation failed'
                  }
                </Alert>

                {validationResult.errors.length > 0 && (
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography color="error">
                        Errors ({validationResult.errors.length})
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List>
                        {validationResult.errors.map((error, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <ErrorIcon color="error" />
                            </ListItemIcon>
                            <ListItemText primary={error} />
                          </ListItem>
                        ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                )}

                {validationResult.warnings.length > 0 && (
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography color="warning.main">
                        Warnings ({validationResult.warnings.length})
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List>
                        {validationResult.warnings.map((warning, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <WarningIcon color="warning" />
                            </ListItemIcon>
                            <ListItemText primary={warning} />
                          </ListItem>
                        ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                )}

                {validationResult.preview.length > 0 && (
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Data Preview</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              {Object.keys(validationResult.preview[0] || {}).map(key => (
                                <TableCell key={key}>{key}</TableCell>
                              ))}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {validationResult.preview.map((row, index) => (
                              <TableRow key={index}>
                                {Object.values(row).map((value, valueIndex) => (
                                  <TableCell key={valueIndex}>
                                    {String(value).substring(0, 50)}
                                  </TableCell>
                                ))}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </AccordionDetails>
                  </Accordion>
                )}

                <Box display="flex" gap={2} mt={3}>
                  <Button onClick={resetImport} variant="outlined">
                    Start Over
                  </Button>
                  {validationResult.isValid && (
                    <Button 
                      variant="contained" 
                      onClick={() => setCurrentStep(2)}
                    >
                      Configure Import
                    </Button>
                  )}
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      {currentStep === 2 && (
        <Card>
          <CardHeader title="Import Configuration" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Default Category</InputLabel>
                  <Select
                    value={importOptions.defaultCategory}
                    onChange={(e) => setImportOptions(prev => ({ ...prev, defaultCategory: e.target.value }))}
                    label="Default Category"
                  >
                    <MenuItem value="">None</MenuItem>
                    {categories.map(category => (
                      <MenuItem key={category._id} value={category._id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Default Location</InputLabel>
                  <Select
                    value={importOptions.defaultLocation}
                    onChange={(e) => setImportOptions(prev => ({ ...prev, defaultLocation: e.target.value }))}
                    label="Default Location"
                  >
                    <MenuItem value="">None</MenuItem>
                    {locations.map(location => (
                      <MenuItem key={location._id} value={location._id}>
                        {location.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Default Status</InputLabel>
                  <Select
                    value={importOptions.defaultStatus}
                    onChange={(e) => setImportOptions(prev => ({ ...prev, defaultStatus: e.target.value }))}
                    label="Default Status"
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={importOptions.updateExisting}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, updateExisting: e.target.checked }))}
                    />
                  }
                  label="Update existing assets with matching asset tags"
                />
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={importOptions.skipErrors}
                      onChange={(e) => setImportOptions(prev => ({ ...prev, skipErrors: e.target.checked }))}
                    />
                  }
                  label="Skip rows with errors and continue import"
                />
              </Grid>
            </Grid>

            <Box display="flex" gap={2} mt={3}>
              <Button onClick={() => setCurrentStep(1)} variant="outlined">
                Back
              </Button>
              <Button 
                variant="contained" 
                onClick={executeImport}
                disabled={loading}
              >
                {loading ? 'Importing...' : 'Start Import'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {currentStep === 3 && importResult && (
        <Card>
          <CardHeader title="Import Results" />
          <CardContent>
            <Alert 
              severity={importResult.failed > 0 ? 'warning' : 'success'} 
              sx={{ mb: 3 }}
            >
              Import completed: {importResult.successful} successful, {importResult.failed} failed out of {importResult.total} total rows
            </Alert>

            {importResult.created.length > 0 && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography color="success.main">
                    Created Assets ({importResult.created.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <List>
                    {importResult.created.map((asset, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <SuccessIcon color="success" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={asset.name}
                          secondary={asset.assetTag}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            )}

            {importResult.updated.length > 0 && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography color="info.main">
                    Updated Assets ({importResult.updated.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <List>
                    {importResult.updated.map((asset, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <InfoIcon color="info" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={asset.name}
                          secondary={asset.assetTag}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            )}

            {importResult.errors.length > 0 && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography color="error.main">
                    Errors ({importResult.errors.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <List>
                    {importResult.errors.map((error, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <ErrorIcon color="error" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={`Row ${error.row}: ${error.error}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            )}

            <Box display="flex" gap={2} mt={3}>
              <Button onClick={resetImport} variant="outlined">
                Import More
              </Button>
              <Button 
                variant="contained" 
                onClick={() => navigate('/assets')}
              >
                View Assets
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );

  return (
    <Container maxWidth="lg">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <Typography variant="h4" component="h1" gutterBottom>
          Asset Import / Export
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Import assets from CSV files or export your existing asset data for backup and analysis.
        </Typography>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        {/* Loading */}
        {loading && <LinearProgress sx={{ mb: 3 }} />}

        {/* Tab Navigation */}
        <Paper elevation={1} sx={{ mb: 3 }}>
          <Box display="flex" borderBottom={1} borderColor="divider">
            <Button
              variant={activeTab === 'export' ? 'contained' : 'text'}
              startIcon={<DownloadIcon />}
              onClick={() => setActiveTab('export')}
              sx={{ mr: 1 }}
            >
              Export Assets
            </Button>
            <Button
              variant={activeTab === 'import' ? 'contained' : 'text'}
              startIcon={<UploadIcon />}
              onClick={() => setActiveTab('import')}
            >
              Import Assets
            </Button>
          </Box>
        </Paper>

        {/* Tab Content */}
        {activeTab === 'export' ? renderExportTab() : renderImportTab()}
      </Box>
    </Container>
  );
};

export default AssetImportExportPage;