import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Button,
  Paper,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider
} from '@mui/material';
import {
  GetApp as DownloadIcon,
  Assessment as ReportIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Build as MaintenanceIcon,
  BarChart as UtilizationIcon,
  History as AuditIcon,
  ExpandMore as ExpandMoreIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import assetService from '../../services/assetService';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658'];

const AssetReportsPage = () => {
  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Date filters
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  
  // Report data
  const [overviewData, setOverviewData] = useState(null);
  const [financialData, setFinancialData] = useState(null);
  const [maintenanceData, setMaintenanceData] = useState(null);
  const [utilizationData, setUtilizationData] = useState(null);
  const [auditData, setAuditData] = useState(null);

  const tabs = [
    { label: 'Overview', icon: <ReportIcon />, value: 'overview' },
    { label: 'Financial', icon: <MoneyIcon />, value: 'financial' },
    { label: 'Maintenance', icon: <MaintenanceIcon />, value: 'maintenance' },
    { label: 'Utilization', icon: <UtilizationIcon />, value: 'utilization' },
    { label: 'Audit Trail', icon: <AuditIcon />, value: 'audit' }
  ];

  useEffect(() => {
    loadReportData();
  }, [activeTab, startDate, endDate]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {};
      if (startDate) params.startDate = startDate.toISOString().split('T')[0];
      if (endDate) params.endDate = endDate.toISOString().split('T')[0];

      switch (activeTab) {
        case 0: // Overview
          const overview = await fetch(`/api/assets/reports/overview?${new URLSearchParams(params)}`);
          setOverviewData(await overview.json());
          break;
        case 1: // Financial
          const financial = await fetch(`/api/assets/reports/financial?${new URLSearchParams(params)}`);
          setFinancialData(await financial.json());
          break;
        case 2: // Maintenance
          const maintenance = await fetch(`/api/assets/reports/maintenance?${new URLSearchParams(params)}`);
          setMaintenanceData(await maintenance.json());
          break;
        case 3: // Utilization
          const utilization = await fetch(`/api/assets/reports/utilization?${new URLSearchParams(params)}`);
          setUtilizationData(await utilization.json());
          break;
        case 4: // Audit
          const audit = await fetch(`/api/assets/reports/audit?${new URLSearchParams(params)}`);
          setAuditData(await audit.json());
          break;
      }
    } catch (error) {
      console.error('Error loading report data:', error);
      setError('Failed to load report data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num) => {
    if (!num && num !== 0) return 'N/A';
    return new Intl.NumberFormat('en-US').format(num);
  };

  const exportReport = async (reportType, format = 'csv') => {
    try {
      const params = { reportType, format };
      if (startDate) params.startDate = startDate.toISOString().split('T')[0];
      if (endDate) params.endDate = endDate.toISOString().split('T')[0];

      const response = await fetch(`/api/assets/reports/export?${new URLSearchParams(params)}`);
      
      if (format === 'csv') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const data = await response.json();
        console.log('Report data:', data);
      }
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const renderOverviewReport = () => {
    if (!overviewData) return <CircularProgress />;

    return (
      <Grid container spacing={3}>
        {/* Key Metrics */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Asset Overview" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="primary">
                      {formatNumber(overviewData.overview.totalAssets)}
                    </Typography>
                    <Typography variant="subtitle1">Total Assets</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="success.main">
                      {formatCurrency(overviewData.overview.totalValue)}
                    </Typography>
                    <Typography variant="subtitle1">Total Value</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="info.main">
                      {formatCurrency(overviewData.overview.averageValue)}
                    </Typography>
                    <Typography variant="subtitle1">Average Value</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="warning.main">
                      {formatNumber(overviewData.overview.assetsNeedingMaintenance)}
                    </Typography>
                    <Typography variant="subtitle1">Need Maintenance</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Status Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Assets by Status" 
              action={
                <Button
                  startIcon={<DownloadIcon />}
                  onClick={() => exportReport('overview', 'csv')}
                  size="small"
                >
                  Export
                </Button>
              }
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={overviewData.statusDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ _id, count }) => `${_id}: ${count}`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {overviewData.statusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Condition Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Assets by Condition" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={overviewData.conditionDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="_id" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Acquisition Trend */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Asset Acquisition Trend" />
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={overviewData.acquisitionTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="count" stroke="#8884d8" name="Assets Added" />
                  <Line type="monotone" dataKey="value" stroke="#82ca9d" name="Value Added" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  const renderFinancialReport = () => {
    if (!financialData) return <CircularProgress />;

    return (
      <Grid container spacing={3}>
        {/* Financial Summary by Category */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Financial Summary by Category" 
              action={
                <Button
                  startIcon={<DownloadIcon />}
                  onClick={() => exportReport('financial', 'csv')}
                  size="small"
                >
                  Export
                </Button>
              }
            />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Category</TableCell>
                      <TableCell align="right">Assets</TableCell>
                      <TableCell align="right">Purchase Value</TableCell>
                      <TableCell align="right">Current Value</TableCell>
                      <TableCell align="right">Depreciation</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {financialData.financialByGroup.map((row) => (
                      <TableRow key={row._id}>
                        <TableCell>
                          {row.categoryInfo?.[0]?.name || row.locationInfo?.[0]?.name || row._id}
                        </TableCell>
                        <TableCell align="right">{row.totalAssets}</TableCell>
                        <TableCell align="right">{formatCurrency(row.totalPurchaseValue)}</TableCell>
                        <TableCell align="right">{formatCurrency(row.totalCurrentValue)}</TableCell>
                        <TableCell align="right">{formatCurrency(row.totalDepreciation)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Depreciation Analysis */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Depreciation Analysis" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="body1">Average Depreciation Rate:</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {financialData.depreciationAnalysis.avgDepreciationRate?.toFixed(1)}%
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="body1">Total Depreciation:</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {formatCurrency(financialData.depreciationAnalysis.totalDepreciation)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="body1">Assets Analyzed:</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {financialData.depreciationAnalysis.assetsCount}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Value Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Value Distribution" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={financialData.financialByGroup.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="_id" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatCurrency(value)} />
                  <Bar dataKey="totalCurrentValue" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  const renderMaintenanceReport = () => {
    if (!maintenanceData) return <CircularProgress />;

    return (
      <Grid container spacing={3}>
        {/* Maintenance Summary */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Maintenance Summary" 
              action={
                <Button
                  startIcon={<DownloadIcon />}
                  onClick={() => exportReport('maintenance', 'csv')}
                  size="small"
                >
                  Export
                </Button>
              }
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={6} md={2}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="primary">
                      {formatNumber(maintenanceData.summary.totalRecords)}
                    </Typography>
                    <Typography variant="subtitle1">Total Records</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={2}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="success.main">
                      {formatNumber(maintenanceData.summary.completedRecords)}
                    </Typography>
                    <Typography variant="subtitle1">Completed</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={2}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="info.main">
                      {formatCurrency(maintenanceData.summary.totalCost)}
                    </Typography>
                    <Typography variant="subtitle1">Total Cost</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={2}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="warning.main">
                      {formatNumber(Math.round(maintenanceData.summary.totalDowntime / 60))}h
                    </Typography>
                    <Typography variant="subtitle1">Total Downtime</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={2}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="error.main">
                      {formatNumber(maintenanceData.summary.preventiveCount)}
                    </Typography>
                    <Typography variant="subtitle1">Preventive</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={2}>
                  <Box textAlign="center">
                    <Typography variant="h3" color="secondary.main">
                      {formatNumber(maintenanceData.summary.correctiveCount)}
                    </Typography>
                    <Typography variant="subtitle1">Corrective</Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Maintenance by Category */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="Maintenance by Category" />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Category</TableCell>
                      <TableCell align="right">Records</TableCell>
                      <TableCell align="right">Total Cost</TableCell>
                      <TableCell align="right">Avg Cost</TableCell>
                      <TableCell align="right">Downtime (hrs)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {maintenanceData.byCategory.map((row) => (
                      <TableRow key={row._id}>
                        <TableCell>{row.categoryName}</TableCell>
                        <TableCell align="right">{row.recordCount}</TableCell>
                        <TableCell align="right">{formatCurrency(row.totalCost)}</TableCell>
                        <TableCell align="right">{formatCurrency(row.averageCost)}</TableCell>
                        <TableCell align="right">{Math.round(row.totalDowntime / 60)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Overdue Items */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader 
              title={`Overdue Maintenance (${maintenanceData.overdue.length})`}
              titleTypographyProps={{ color: 'error.main' }}
            />
            <CardContent>
              {maintenanceData.overdue.length === 0 ? (
                <Typography color="success.main">No overdue maintenance</Typography>
              ) : (
                maintenanceData.overdue.map((item) => (
                  <Box key={item._id} mb={2}>
                    <Typography variant="body2" fontWeight="bold">
                      {item.asset.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Due: {new Date(item.scheduledDate).toLocaleDateString()}
                    </Typography>
                  </Box>
                ))
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Maintenance Trend */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Maintenance Trend" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={maintenanceData.trend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="count" stroke="#8884d8" name="Total Records" />
                  <Line type="monotone" dataKey="preventiveCount" stroke="#82ca9d" name="Preventive" />
                  <Line type="monotone" dataKey="correctiveCount" stroke="#ff7c7c" name="Corrective" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  const renderUtilizationReport = () => {
    if (!utilizationData) return <CircularProgress />;

    return (
      <Grid container spacing={3}>
        {/* Assignment Statistics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Asset Assignment" />
            <CardContent>
              <Box mb={2}>
                <Typography variant="h4" color="primary">
                  {typeof utilizationData.assignmentStats?.assignmentRate === 'number' 
                    ? `${utilizationData.assignmentStats.assignmentRate.toFixed(1)}%` 
                    : 'N/A'}
                </Typography>
                <Typography variant="subtitle1">Assignment Rate</Typography>
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2">Assigned:</Typography>
                  <Typography variant="h6">{utilizationData.assignmentStats?.assignedAssets ?? 0}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">Unassigned:</Typography>
                  <Typography variant="h6">{utilizationData.assignmentStats?.unassignedAssets ?? 0}</Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Status Utilization */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Status Distribution" />
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={utilizationData.utilizationByStatus || []}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ status, percentage }) => `${status}: ${typeof percentage === 'number' ? percentage : 'N/A'}%`}
                  >
                    {(utilizationData.utilizationByStatus || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Location Utilization */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Top Locations by Asset Count" />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Location</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell align="right">Current Assets</TableCell>
                      <TableCell align="right">Max Capacity</TableCell>
                      <TableCell align="right">Utilization</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(utilizationData.locationUtilization || []).map((row) => (
                      <TableRow key={row._id}>
                        <TableCell>{row.name}</TableCell>
                        <TableCell>
                          <Chip label={row.locationType} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell align="right">{row.currentAssets}</TableCell>
                        <TableCell align="right">{row.maxAssets || 'Unlimited'}</TableCell>
                        <TableCell align="right">
                          {typeof row.utilizationRate === 'number' ? `${row.utilizationRate.toFixed(1)}%` : 'N/A'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Age Distribution */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Asset Age Distribution" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={utilizationData.ageDistribution || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="ageRange" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  const renderAuditReport = () => {
    if (!auditData) return <CircularProgress />;

    return (
      <Grid container spacing={3}>
        {/* Activity Summary */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Activity Summary" />
            <CardContent>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Action</TableCell>
                      <TableCell align="right">Count</TableCell>
                      <TableCell>Last Activity</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auditData.summary.map((row) => (
                      <TableRow key={row._id}>
                        <TableCell>
                          <Chip label={row._id} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell align="right">{row.count}</TableCell>
                        <TableCell>
                          {new Date(row.lastActivity).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Users */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Most Active Users" />
            <CardContent>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>User</TableCell>
                      <TableCell align="right">Activities</TableCell>
                      <TableCell>Last Active</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auditData.byUser.slice(0, 10).map((row) => (
                      <TableRow key={row._id}>
                        <TableCell>{row.performedByName}</TableCell>
                        <TableCell align="right">{row.activityCount}</TableCell>
                        <TableCell>
                          {new Date(row.lastActivity).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Activity Trend */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Daily Activity Trend" />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={auditData.trend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="_id" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="count" stroke="#8884d8" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Recent Activities" />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>User</TableCell>
                      <TableCell>Action</TableCell>
                      <TableCell>Asset</TableCell>
                      <TableCell>Description</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auditData.activities.slice(0, 20).map((activity) => (
                      <TableRow key={activity._id}>
                        <TableCell>
                          {new Date(activity.createdAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{activity.performedBy?.name}</TableCell>
                        <TableCell>
                          <Chip label={activity.action} size="small" />
                        </TableCell>
                        <TableCell>{activity.asset?.name || activity.assetTag}</TableCell>
                        <TableCell>{activity.actionDescription}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  const renderTabContent = () => {
    if (loading) {
      return (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      );
    }

    switch (activeTab) {
      case 0: return renderOverviewReport();
      case 1: return renderFinancialReport();
      case 2: return renderMaintenanceReport();
      case 3: return renderUtilizationReport();
      case 4: return renderAuditReport();
      default: return null;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Asset Reports & Analytics
          </Typography>
          
          {/* Date Filters */}
          <Box display="flex" gap={2} alignItems="center">
            <DatePicker
              label="Start Date"
              value={startDate}
              onChange={(newValue) => setStartDate(newValue)}
              slotProps={{ textField: { size: 'small' } }}
            />
            <DatePicker
              label="End Date"
              value={endDate}
              onChange={(newValue) => setEndDate(newValue)}
              slotProps={{ textField: { size: 'small' } }}
            />
            <Button
              variant="outlined"
              startIcon={<DateRangeIcon />}
              onClick={() => {
                setStartDate(null);
                setEndDate(null);
              }}
            >
              Clear
            </Button>
          </Box>
        </Box>

        {/* Report Tabs */}
        <Paper elevation={1} sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={(event, newValue) => setActiveTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                icon={tab.icon}
                label={tab.label}
                iconPosition="start"
              />
            ))}
          </Tabs>
        </Paper>

        {/* Report Content */}
        {renderTabContent()}
      </Box>
    </Container>
    </LocalizationProvider>
  );
};

export default AssetReportsPage;