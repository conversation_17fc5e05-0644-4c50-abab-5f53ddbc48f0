import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  CircularProgress,
  Alert,
  Button,
  Grid,
  Divider,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Computer as ComputerIcon,
  Print as PrinterIcon,
  Phone as PhoneIcon,
  Router as NetworkIcon,
  Storage as ServerIcon,
  Devices as DeviceIcon
} from '@mui/icons-material';
import glpiService from '../../services/glpiService';
import { useParams, useNavigate } from 'react-router-dom';

const GLPIAssetDetail = () => {
  const [asset, setAsset] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    fetchAsset();
  }, [id]);

  const fetchAsset = async () => {
    setLoading(true);
    setError(null);

    try {
      const assetData = await glpiService.getAsset(id);
      setAsset(assetData);
    } catch (err) {
      console.error(`Error fetching asset with ID ${id}:`, err);
      setError('Failed to load asset details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = () => {
    navigate(`/glpi/assets/${id}/edit`);
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await glpiService.deleteAsset(id);
      setDeleteDialogOpen(false);
      navigate('/glpi/assets');
    } catch (err) {
      console.error(`Error deleting asset with ID ${id}:`, err);
      setError('Failed to delete asset. Please try again.');
      setDeleteDialogOpen(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  const handleBackClick = () => {
    navigate('/glpi/assets');
  };

  // Get icon based on asset type
  const getAssetIcon = (assetType) => {
    if (!assetType) return <DeviceIcon fontSize="large" />;
    
    switch (assetType.toLowerCase()) {
      case 'computer':
        return <ComputerIcon fontSize="large" />;
      case 'printer':
        return <PrinterIcon fontSize="large" />;
      case 'phone':
      case 'mobile':
        return <PhoneIcon fontSize="large" />;
      case 'network':
      case 'networkequipment':
        return <NetworkIcon fontSize="large" />;
      case 'server':
        return <ServerIcon fontSize="large" />;
      default:
        return <DeviceIcon fontSize="large" />;
    }
  };

  // Format date string
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Asset Details
          </Typography>
          <Alert severity="error">{error}</Alert>
          <Button
            startIcon={<BackIcon />}
            onClick={handleBackClick}
            sx={{ mt: 2 }}
          >
            Back to Assets
          </Button>
        </Box>
      </Container>
    );
  }

  if (!asset) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Asset Details
          </Typography>
          <Alert severity="warning">Asset not found</Alert>
          <Button
            startIcon={<BackIcon />}
            onClick={handleBackClick}
            sx={{ mt: 2 }}
          >
            Back to Assets
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Button
            startIcon={<BackIcon />}
            onClick={handleBackClick}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
            Asset Details
          </Typography>
          <Tooltip title="Edit Asset">
            <IconButton onClick={handleEditClick} sx={{ mr: 1 }}>
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete Asset">
            <IconButton onClick={handleDeleteClick} color="error">
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
        
        <Paper sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                {getAssetIcon(asset.type)}
              </Box>
              <Typography variant="h5" align="center" gutterBottom>
                {asset.name}
              </Typography>
              <Chip 
                label={asset.status || 'Unknown Status'} 
                color={asset.status === 'Active' ? 'success' : 'default'}
                sx={{ mt: 1 }}
              />
            </Grid>
            
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                General Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Type</Typography>
                  <Typography variant="body1" gutterBottom>{asset.type || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Manufacturer</Typography>
                  <Typography variant="body1" gutterBottom>{asset.manufacturer || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Model</Typography>
                  <Typography variant="body1" gutterBottom>{asset.model || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Serial Number</Typography>
                  <Typography variant="body1" gutterBottom>{asset.serial || 'N/A'}</Typography>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Location & Ownership
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Location</Typography>
                  <Typography variant="body1" gutterBottom>{asset.location || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">User</Typography>
                  <Typography variant="body1" gutterBottom>{asset.user || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Department</Typography>
                  <Typography variant="body1" gutterBottom>{asset.department || 'N/A'}</Typography>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Dates
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Purchase Date</Typography>
                  <Typography variant="body1" gutterBottom>{formatDate(asset.purchaseDate)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Warranty Expiration</Typography>
                  <Typography variant="body1" gutterBottom>{formatDate(asset.warrantyDate)}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Last Update</Typography>
                  <Typography variant="body1" gutterBottom>{formatDate(asset.lastUpdate)}</Typography>
                </Grid>
              </Grid>
              
              {asset.notes && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Notes
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {asset.notes}
                  </Typography>
                </>
              )}
            </Grid>
          </Grid>
        </Paper>
      </Box>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the asset "{asset?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default GLPIAssetDetail;