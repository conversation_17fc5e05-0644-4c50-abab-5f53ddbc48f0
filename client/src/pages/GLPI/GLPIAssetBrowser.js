import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  ListItemButton,
  Breadcrumbs,
  Link,
  Button,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Grid
} from '@mui/material';
import {
  Computer as ComputerIcon,
  Print as PrinterIcon,
  Phone as PhoneIcon,
  Router as NetworkIcon,
  Storage as ServerIcon,
  Devices as DeviceIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import glpiService from '../../services/glpiService';
import { useNavigate } from 'react-router-dom';

const GLPIAssetBrowser = () => {
  const [assets, setAssets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [assetTagSearch, setAssetTagSearch] = useState('');
  const [assetTypes, setAssetTypes] = useState([]);
  const [selectedType, setSelectedType] = useState('all');
  const [manufacturers, setManufacturers] = useState([]);
  const [selectedManufacturer, setSelectedManufacturer] = useState('all');
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loadingTypes, setLoadingTypes] = useState(false);
  const [loadingManufacturers, setLoadingManufacturers] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(false);
  
  const navigate = useNavigate();

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await glpiService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Fetch assets when config is loaded
  useEffect(() => {
    if (configStatus) {
      fetchAssets();
      fetchAssetTypes();
      fetchManufacturers();
      fetchCategories();
    }
  }, [configStatus]);

  const fetchAssets = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = {};
      if (searchTerm) params.search = searchTerm;
      if (assetTagSearch) params.asset_tag = assetTagSearch;
      if (selectedType !== 'all') params.itemtype = selectedType;
      if (selectedManufacturer !== 'all') params.manufacturer = selectedManufacturer;
      if (selectedCategory !== 'all') params.category = selectedCategory;
      
      // If we're searching by asset tag, use the search endpoint which has specific handling for asset tags
      if (assetTagSearch) {
        const searchResults = await glpiService.searchAssets(params);
        setAssets(searchResults.data || []);
      } else {
        const assetList = await glpiService.getAssets(params);
        setAssets(assetList);
      }
    } catch (err) {
      console.error('Error fetching assets:', err);
      setError('Failed to load assets. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAssetTypes = async () => {
    setLoadingTypes(true);
    try {
      const types = await glpiService.getAssetTypes();
      setAssetTypes(types);
    } catch (err) {
      console.error('Error fetching asset types:', err);
    } finally {
      setLoadingTypes(false);
    }
  };

  const fetchManufacturers = async () => {
    setLoadingManufacturers(true);
    try {
      const manufacturers = await glpiService.getAssetManufacturers();
      setManufacturers(manufacturers);
    } catch (err) {
      console.error('Error fetching manufacturers:', err);
    } finally {
      setLoadingManufacturers(false);
    }
  };
  
  const fetchCategories = async () => {
    setLoadingCategories(true);
    try {
      const categories = await glpiService.getAssetCategories();
      setCategories(categories);
    } catch (err) {
      console.error('Error fetching categories:', err);
    } finally {
      setLoadingCategories(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    fetchAssets();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  const handleAssetTagChange = (e) => {
    setAssetTagSearch(e.target.value);
  };

  const handleTypeChange = (e) => {
    setSelectedType(e.target.value);
  };

  const handleManufacturerChange = (e) => {
    setSelectedManufacturer(e.target.value);
  };
  
  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
  };

  const handleRefreshClick = () => {
    fetchAssets();
  };

  const handleAssetClick = (asset) => {
    navigate(`/glpi/assets/${asset.id}`);
  };

  const handleAddAsset = () => {
    navigate('/glpi/assets/new');
  };

  const handleEditAsset = (e, asset) => {
    e.stopPropagation();
    navigate(`/glpi/assets/${asset.id}/edit`);
  };

  const handleDeleteAsset = async (e, asset) => {
    e.stopPropagation();
    if (window.confirm(`Are you sure you want to delete ${asset.name}?`)) {
      try {
        await glpiService.deleteAsset(asset.id);
        fetchAssets(); // Refresh the list
      } catch (err) {
        console.error('Error deleting asset:', err);
        setError('Failed to delete asset. Please try again.');
      }
    }
  };

  // Get icon based on asset type
  const getAssetIcon = (assetType) => {
    // Add null/undefined check before calling toLowerCase
    if (!assetType) {
      return <DeviceIcon />;
    }
    
    switch (assetType.toLowerCase()) {
      case 'computer':
        return <ComputerIcon />;
      case 'printer':
        return <PrinterIcon />;
      case 'phone':
      case 'mobile':
        return <PhoneIcon />;
      case 'network':
      case 'networkequipment':
        return <NetworkIcon />;
      case 'server':
        return <ServerIcon />;
      default:
        return <DeviceIcon />;
    }
  };

  if (loadingConfig) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!configStatus) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            GLPI Asset Browser
          </Typography>
          <Alert severity="warning">
            GLPI is not configured yet. Please go to the GLPI Setup page to configure your connection.
          </Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          GLPI Asset Browser
        </Typography>
        
        <Paper sx={{ p: 2, mb: 2 }}>
          <Box component="form" onSubmit={handleSearch} sx={{ mb: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Search Assets"
                  variant="outlined"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton edge="end" type="submit">
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  label="Search by Asset Tag"
                  variant="outlined"
                  value={assetTagSearch}
                  onChange={handleAssetTagChange}
                  placeholder="Enter asset tag number"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton edge="end" type="submit">
                          <SearchIcon />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel id="asset-type-label">Asset Type</InputLabel>
                  <Select
                    labelId="asset-type-label"
                    id="asset-type"
                    value={selectedType}
                    label="Asset Type"
                    onChange={handleTypeChange}
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="Computer">Computers</MenuItem>
                    <MenuItem value="Monitor">Monitors</MenuItem>
                    <MenuItem value="Printer">Printers</MenuItem>
                    <MenuItem value="NetworkEquipment">Network Equipment</MenuItem>
                    <MenuItem value="Phone">Phones</MenuItem>
                    <MenuItem value="Peripheral">Peripherals</MenuItem>
                    {assetTypes.map((type) => (
                      <MenuItem key={type.id} value={type.id}>
                        {type.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel id="category-label">Category</InputLabel>
                  <Select
                    labelId="category-label"
                    id="category"
                    value={selectedCategory}
                    label="Category"
                    onChange={handleCategoryChange}
                  >
                    <MenuItem value="all">All Categories</MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth>
                  <InputLabel id="manufacturer-label">Manufacturer</InputLabel>
                  <Select
                    labelId="manufacturer-label"
                    id="manufacturer"
                    value={selectedManufacturer}
                    label="Manufacturer"
                    onChange={handleManufacturerChange}
                  >
                    <MenuItem value="all">All Manufacturers</MenuItem>
                    {manufacturers.map((manufacturer) => (
                      <MenuItem key={manufacturer.id} value={manufacturer.id}>
                        {manufacturer.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={12} md={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddAsset}
                    sx={{ mr: 1 }}
                  >
                    Add Asset
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={handleRefreshClick}
                  >
                    Refresh
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List>
              {assets.length === 0 ? (
                <ListItem>
                  <ListItemText primary="No assets found matching your criteria" />
                </ListItem>
              ) : (
                assets.map((asset) => (
                  <React.Fragment key={asset.id}>
                    <ListItem
                      disablePadding
                      secondaryAction={
                        <Box>
                          <Tooltip title="View Details">
                            <IconButton 
                              edge="end" 
                              aria-label="details"
                              onClick={() => handleAssetClick(asset)}
                            >
                              <InfoIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit">
                            <IconButton 
                              edge="end" 
                              aria-label="edit"
                              onClick={(e) => handleEditAsset(e, asset)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton 
                              edge="end" 
                              aria-label="delete"
                              onClick={(e) => handleDeleteAsset(e, asset)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      }
                    >
                      <ListItemButton onClick={() => handleAssetClick(asset)}>
                        <ListItemIcon>
                          {getAssetIcon(asset.type)}
                        </ListItemIcon>
                        <ListItemText 
                          primary={asset.name} 
                          secondary={`${asset.type} - ${asset.manufacturer || 'Unknown'} ${asset.model || ''} - ${asset.serial || 'No Serial'}`} 
                        />
                      </ListItemButton>
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))
              )}
            </List>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default GLPIAssetBrowser;