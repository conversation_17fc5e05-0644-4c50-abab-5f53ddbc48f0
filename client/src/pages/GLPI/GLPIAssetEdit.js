import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  CircularProgress,
  Alert,
  Button,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import glpiService from '../../services/glpiService';
import { useParams, useNavigate } from 'react-router-dom';

const GLPIAssetEdit = () => {
  const [asset, setAsset] = useState({
    name: '',
    type: '',
    manufacturer: '',
    model: '',
    serial: '',
    location: '',
    user: '',
    department: '',
    purchaseDate: '',
    warrantyDate: '',
    notes: '',
    status: 'Active'
  });
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [assetTypes, setAssetTypes] = useState([]);
  const [manufacturers, setManufacturers] = useState([]);
  const [models, setModels] = useState([]);
  
  const { id } = useParams();
  const navigate = useNavigate();
  const isNewAsset = id === 'new';

  useEffect(() => {
    fetchAssetTypes();
    fetchManufacturers();
    fetchModels();
    
    if (!isNewAsset) {
      fetchAsset();
    } else {
      setLoading(false);
    }
  }, [id]);

  const fetchAsset = async () => {
    setLoading(true);
    setError(null);

    try {
      const assetData = await glpiService.getAsset(id);
      setAsset(assetData);
    } catch (err) {
      console.error(`Error fetching asset with ID ${id}:`, err);
      setError('Failed to load asset details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAssetTypes = async () => {
    try {
      const types = await glpiService.getAssetTypes();
      setAssetTypes(types);
    } catch (err) {
      console.error('Error fetching asset types:', err);
    }
  };

  const fetchManufacturers = async () => {
    try {
      const manufacturers = await glpiService.getAssetManufacturers();
      setManufacturers(manufacturers);
    } catch (err) {
      console.error('Error fetching manufacturers:', err);
    }
  };

  const fetchModels = async () => {
    try {
      const models = await glpiService.getAssetModels();
      setModels(models);
    } catch (err) {
      console.error('Error fetching models:', err);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setAsset(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      if (isNewAsset) {
        await glpiService.createAsset(asset);
        setSuccess(true);
        // Navigate back to asset browser after a short delay
        setTimeout(() => {
          navigate('/glpi/assets');
        }, 1500);
      } else {
        await glpiService.updateAsset(id, asset);
        setSuccess(true);
        // Navigate back to asset detail after a short delay
        setTimeout(() => {
          navigate(`/glpi/assets/${id}`);
        }, 1500);
      }
    } catch (err) {
      console.error(`Error ${isNewAsset ? 'creating' : 'updating'} asset:`, err);
      setError(`Failed to ${isNewAsset ? 'create' : 'update'} asset. Please try again.`);
    } finally {
      setSaving(false);
    }
  };

  const handleBackClick = () => {
    if (isNewAsset) {
      navigate('/glpi/assets');
    } else {
      navigate(`/glpi/assets/${id}`);
    }
  };

  const handleSnackbarClose = () => {
    setSuccess(false);
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Button
            startIcon={<BackIcon />}
            onClick={handleBackClick}
            sx={{ mr: 2 }}
          >
            Back
          </Button>
          <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
            {isNewAsset ? 'Create New Asset' : 'Edit Asset'}
          </Typography>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        <Paper sx={{ p: 3 }}>
          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  label="Asset Name"
                  name="name"
                  value={asset.name}
                  onChange={handleInputChange}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel id="type-label">Asset Type</InputLabel>
                  <Select
                    labelId="type-label"
                    name="type"
                    value={asset.type || ''}
                    label="Asset Type"
                    onChange={handleInputChange}
                  >
                    {assetTypes.map((type) => (
                      <MenuItem key={type.id} value={type.id}>
                        {type.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="manufacturer-label">Manufacturer</InputLabel>
                  <Select
                    labelId="manufacturer-label"
                    name="manufacturer"
                    value={asset.manufacturer || ''}
                    label="Manufacturer"
                    onChange={handleInputChange}
                  >
                    {manufacturers.map((manufacturer) => (
                      <MenuItem key={manufacturer.id} value={manufacturer.id}>
                        {manufacturer.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="model-label">Model</InputLabel>
                  <Select
                    labelId="model-label"
                    name="model"
                    value={asset.model || ''}
                    label="Model"
                    onChange={handleInputChange}
                  >
                    {models.map((model) => (
                      <MenuItem key={model.id} value={model.id}>
                        {model.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Serial Number"
                  name="serial"
                  value={asset.serial || ''}
                  onChange={handleInputChange}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Location"
                  name="location"
                  value={asset.location || ''}
                  onChange={handleInputChange}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="User"
                  name="user"
                  value={asset.user || ''}
                  onChange={handleInputChange}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Department"
                  name="department"
                  value={asset.department || ''}
                  onChange={handleInputChange}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Purchase Date"
                  name="purchaseDate"
                  type="date"
                  value={asset.purchaseDate ? asset.purchaseDate.split('T')[0] : ''}
                  onChange={handleInputChange}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Warranty Expiration"
                  name="warrantyDate"
                  type="date"
                  value={asset.warrantyDate ? asset.warrantyDate.split('T')[0] : ''}
                  onChange={handleInputChange}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="status-label">Status</InputLabel>
                  <Select
                    labelId="status-label"
                    name="status"
                    value={asset.status || 'Active'}
                    label="Status"
                    onChange={handleInputChange}
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Inactive">Inactive</MenuItem>
                    <MenuItem value="Maintenance">Maintenance</MenuItem>
                    <MenuItem value="Retired">Retired</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  name="notes"
                  value={asset.notes || ''}
                  onChange={handleInputChange}
                  multiline
                  rows={4}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    startIcon={<SaveIcon />}
                    disabled={saving}
                  >
                    {saving ? 'Saving...' : (isNewAsset ? 'Create Asset' : 'Save Changes')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Box>
      
      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={handleSnackbarClose}
        message={isNewAsset ? "Asset created successfully!" : "Asset updated successfully!"}
      />
    </Container>
  );
};

export default GLPIAssetEdit;