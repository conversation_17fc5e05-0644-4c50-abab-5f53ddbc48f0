import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import FolderIcon from '@mui/icons-material/Folder';
import DescriptionIcon from '@mui/icons-material/Description';
import PersonIcon from '@mui/icons-material/Person';
import BrushIcon from '@mui/icons-material/Brush';
import canvaService from '../../services/canvaService';
import CanvaAuthButton from '../../components/Canva/CanvaAuthButton';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`canva-tabpanel-${index}`}
      aria-labelledby={`canva-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const CanvaPage = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [designs, setDesigns] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedDesign, setSelectedDesign] = useState(null);
  const [designFiles, setDesignFiles] = useState([]);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setCheckingAuth(true);
        const response = await canvaService.checkAuth();
        setIsAuthenticated(response.authenticated);
      } catch (error) {
        console.error('Error checking Canva authentication:', error);
        setIsAuthenticated(false);
      } finally {
        setCheckingAuth(false);
      }
    };

    checkAuthStatus();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle design selection for file browsing
  const handleDesignSelect = async (design) => {
    setSelectedDesign(design);
    setLoadingFiles(true);
    setError(null);
    
    try {
      const files = await canvaService.getDesignFiles(design.id);
      setDesignFiles(files);
    } catch (err) {
      setError(`Failed to load files for design ${design.name}. Please try again.`);
      console.error('Error loading design files:', err);
    } finally {
      setLoadingFiles(false);
    }
  };

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        switch (tabValue) {
          case 0: // Designs
            if (designs.length === 0) {
              const designsData = await canvaService.getDesigns();
              setDesigns(designsData);
            }
            break;
          case 1: // Templates
            if (templates.length === 0) {
              const templatesData = await canvaService.getTemplates();
              setTemplates(templatesData);
            }
            break;
          case 2: // Files
            if (designs.length === 0) {
              const designsData = await canvaService.getDesigns();
              setDesigns(designsData);
            }
            break;
          case 3: // Users
            if (users.length === 0) {
              const usersData = await canvaService.getUsers();
              setUsers(usersData);
            }
            break;
          default:
            break;
        }
      } catch (err) {
        setError('Failed to load data from Canva. Please check your connection and try again.');
        console.error('Error loading Canva data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, designs.length, templates.length, users.length]);

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Canva Design Service
        </Typography>
        
        <Paper sx={{ width: '100%', mb: 2, p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Authentication Status
          </Typography>
          <CanvaAuthButton onAuthStatusChange={(status) => setIsAuthenticated(status)} />
        </Paper>
        
        {checkingAuth ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : isAuthenticated ? (
          <Paper sx={{ width: '100%', mb: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              centered
            >
              <Tab label="Designs" />
              <Tab label="Templates" />
              <Tab label="Files" />
              <Tab label="Users" />
            </Tabs>

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TabPanel value={tabValue} index={0}>
                  <Typography variant="h6">Designs</Typography>
                  {designs.length > 0 ? (
                    <List>
                      {designs.map((design) => (
                        <ListItem key={design.id}>
                          <ListItemIcon>
                            <BrushIcon />
                          </ListItemIcon>
                          <ListItemText 
                            primary={design.name} 
                            secondary={design.type} 
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography>No designs found</Typography>
                  )}
                </TabPanel>
                
                <TabPanel value={tabValue} index={1}>
                  <Typography variant="h6">Templates</Typography>
                  {templates.length > 0 ? (
                    <List>
                      {templates.map((template) => (
                        <ListItem key={template.id}>
                          <ListItemIcon>
                            <BrushIcon />
                          </ListItemIcon>
                          <ListItemText 
                            primary={template.name} 
                            secondary={template.category} 
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography>No templates found</Typography>
                  )}
                </TabPanel>
                
                <TabPanel value={tabValue} index={2}>
                  <Typography variant="h6">Files</Typography>
                  {designs.length > 0 ? (
                    <Box sx={{ display: 'flex' }}>
                      <Box sx={{ width: '30%', borderRight: 1, borderColor: 'divider', pr: 2 }}>
                        <Typography variant="subtitle1" gutterBottom>Select a Design</Typography>
                        <List>
                          {designs.map((design) => (
                            <ListItem 
                              button 
                              key={design.id}
                              onClick={() => handleDesignSelect(design)}
                              selected={selectedDesign && selectedDesign.id === design.id}
                            >
                              <ListItemIcon>
                                <FolderIcon />
                              </ListItemIcon>
                              <ListItemText primary={design.name} />
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                      <Box sx={{ width: '70%', pl: 2 }}>
                        {selectedDesign ? (
                          <>
                            <Typography variant="subtitle1" gutterBottom>
                              Files for {selectedDesign.name}
                            </Typography>
                            {loadingFiles ? (
                              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                                <CircularProgress />
                              </Box>
                            ) : (
                              <>
                                {designFiles.length > 0 ? (
                                  <List>
                                    {designFiles.map((file) => (
                                      <ListItem key={file.id}>
                                        <ListItemIcon>
                                          <DescriptionIcon />
                                        </ListItemIcon>
                                        <ListItemText 
                                          primary={file.display_name} 
                                          secondary={`Size: ${(file.size / 1024).toFixed(2)} KB`} 
                                        />
                                      </ListItem>
                                    ))}
                                  </List>
                                ) : (
                                  <Typography>No files found for this design</Typography>
                                )}
                              </>
                            )}
                          </>
                        ) : (
                          <Typography>Select a design to view files</Typography>
                        )}
                      </Box>
                    </Box>
                  ) : (
                    <Typography>No designs found</Typography>
                  )}
                </TabPanel>
                
                <TabPanel value={tabValue} index={3}>
                  <Typography variant="h6">Users</Typography>
                  {users.length > 0 ? (
                    <List>
                      {users.map((user) => (
                        <ListItem key={user.id}>
                          <ListItemIcon>
                            <PersonIcon />
                          </ListItemIcon>
                          <ListItemText 
                            primary={`${user.name}`} 
                            secondary={user.email} 
                          />
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography>No users found</Typography>
                  )}
                </TabPanel>
              </>
            )}
          </Paper>
        ) : (
          <Paper sx={{ width: '100%', mb: 2, p: 3 }}>
            <Typography variant="body1" align="center" sx={{ py: 4 }}>
              Please authenticate with Canva using the button above to access your designs, templates, files, and users.
            </Typography>
          </Paper>
        )}
      </Box>
    </Container>
  );
};

export default CanvaPage;