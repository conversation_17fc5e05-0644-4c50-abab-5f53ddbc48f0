import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button
} from '@mui/material';
import { 
  HelpOutline as HelpIcon,
  QuestionAnswer as FAQIcon,
  ArrowBack as BackIcon
} from '@mui/icons-material';

const HelpTopicTemplate = ({ title, description, content, icon }) => {
  return (
    <Box>
      {/* Header section */}
      <Box 
        sx={{ 
          bgcolor: 'primary.main', 
          color: 'white',
          py: 4,
          mb: 4
        }}
      >
        <Container maxWidth="lg">
          <Typography
            component="h1"
            variant="h3"
            align="center"
            color="inherit"
            gutterBottom
          >
            {title}
          </Typography>
          <Typography
            variant="h6"
            align="center"
            color="inherit"
            paragraph
          >
            {description}
          </Typography>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              component={RouterLink}
              to="/help"
              startIcon={<BackIcon />}
            >
              Back to Help Center
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Help content */}
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Left sidebar with quick links */}
          <Grid item xs={12} md={3}>
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Quick Links
              </Typography>
              <List>
                <ListItem button component={RouterLink} to="/help">
                  <ListItemIcon><HelpIcon /></ListItemIcon>
                  <ListItemText primary="Help Home" />
                </ListItem>
                <ListItem button component={RouterLink} to="/help/faq">
                  <ListItemIcon><FAQIcon /></ListItemIcon>
                  <ListItemText primary="FAQs" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Main content area */}
          <Grid item xs={12} md={9}>
            <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                {icon}
                <Typography variant="h4" component="h2" sx={{ ml: 1 }}>
                  {title}
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
              
              {/* Content goes here */}
              {content}
            </Paper>

            <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="h5" gutterBottom>
                Need More Help?
              </Typography>
              <Typography paragraph>
                If you can't find the information you need, please contact the IT department for assistance.
              </Typography>
              <Button variant="contained" color="primary">
                Contact Support
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default HelpTopicTemplate;