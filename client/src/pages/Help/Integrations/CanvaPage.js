import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Brush as CanvaIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const CanvaPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Using the Canva Integration
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Canva to provide easy access to design tools and templates 
        directly within the portal. This guide will help you understand how to use the Canva integration 
        effectively for your design needs.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Canva?
      </Typography>
      <Typography paragraph>
        Canva is a graphic design platform that allows users to create social media graphics, presentations, 
        posters, documents, and other visual content. The platform offers a wide range of templates, images, 
        fonts, and design elements that make it easy to create professional-looking designs without advanced 
        design skills.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Canva in the Portal
      </Typography>
      <Typography paragraph>
        To access the Canva integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Canva" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If you haven't connected your Canva account yet, you'll be prompted to authorize the connection.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Once connected, you'll see the Canva dashboard within the portal.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Canva Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Canva integration in the portal provides access to most of Canva's features:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Design Dashboard
            </Typography>
            <Typography variant="body2">
              View your recent designs, access templates, and start new projects. The dashboard shows 
              thumbnails of your designs organized by recency or folders.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Templates Library
            </Typography>
            <Typography variant="body2">
              Browse and search through thousands of pre-designed templates for various purposes, including 
              social media posts, presentations, flyers, posters, and more.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Design Editor
            </Typography>
            <Typography variant="body2">
              Create and edit designs using Canva's drag-and-drop editor, which includes tools for adding 
              text, images, shapes, icons, and other design elements.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Brand Kit
            </Typography>
            <Typography variant="body2">
              Access your organization's brand assets, including logos, colors, and fonts, to ensure 
              consistent branding across all designs.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating a New Design
      </Typography>
      <Typography paragraph>
        To create a new design using the Canva integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click the "Create a Design" button in the top-right corner of the Canva dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the type of design you want to create from the dropdown menu (e.g., Presentation, Social Media Post, Flyer).
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Alternatively, browse the templates library and select a template to start with.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Canva editor will open, allowing you to customize your design.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the tools in the left sidebar to add elements, text, images, and other content to your design.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Your design will be automatically saved as you work.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing and Managing Your Designs
      </Typography>
      <Typography paragraph>
        To access and manage your existing Canva designs:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Recent Designs:</strong> Your most recent designs appear on the Canva dashboard when you first access the integration.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>All Designs:</strong> Click "All your designs" to see a complete list of your designs.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Folders:</strong> Click on a folder to view designs organized within that folder.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Search:</strong> Use the search bar to find specific designs by name or content.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Sharing and Collaborating
      </Typography>
      <Typography paragraph>
        The Canva integration allows you to share designs and collaborate with others:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Sharing Designs:</strong> Open a design and click the "Share" button in the top-right corner to share it with others via email or link.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Collaboration:</strong> Invite team members to edit a design by adding them as collaborators through the Share menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Comments:</strong> Leave comments on designs to provide feedback or suggestions to collaborators.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Version History:</strong> View and restore previous versions of a design using the version history feature.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Downloading and Publishing
      </Typography>
      <Typography paragraph>
        When your design is complete, you can download or publish it in various formats:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Open the design you want to download or publish.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Download" button in the top-right corner.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the file format you want to download (PNG, JPG, PDF, etc.).
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Adjust any format-specific settings if needed.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Download" to save the file to your computer.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Alternatively, use the "Share" menu to publish directly to social media platforms or schedule posts.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Canva Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use the CSF brand kit to ensure all your designs follow the organization's branding guidelines.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Save frequently used design elements as templates to speed up future design work.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Organize your designs into folders to keep them well-organized and easy to find.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Take advantage of Canva's resize feature to quickly adapt a design for different platforms or purposes.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the collaboration features to get feedback from colleagues before finalizing designs.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Canva"
      description="Learn how to use the Canva design integration"
      content={content}
      icon={<CanvaIcon color="primary" fontSize="large" />}
    />
  );
};

export default CanvaPage;