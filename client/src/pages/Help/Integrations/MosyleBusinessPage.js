import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { PhoneIphone as MosyleBusinessIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const MosyleBusinessPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Mobile Devices with Mosyle Business
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Mosyle Business to provide easy access to your organization's 
        mobile device management (MDM) system directly within the portal. This guide will help you understand how to use 
        the Mosyle Business integration to manage Apple devices, deploy apps, and enforce security policies.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Mosyle Business?
      </Typography>
      <Typography paragraph>
        Mosyle Business is a mobile device management (MDM) platform specifically designed for Apple devices 
        in business environments. It allows organizations to remotely manage, configure, and secure iPhones, 
        iPads, Macs, and Apple TVs. The integration in the CSF Staff Portal allows administrators to manage 
        these devices without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Mosyle Business in the Portal
      </Typography>
      <Typography paragraph>
        To access the Mosyle Business integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Mosyle Business" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Mosyle Business interface will open, displaying the device management dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the Mosyle Business integration, you may need to authorize 
            the application with administrator credentials.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Note that access to certain features may be restricted based on your user permissions.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Mosyle Business Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Mosyle Business integration in the portal provides a user-friendly interface for managing Apple devices:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body2">
              The main screen provides an overview of your device fleet, including 
              device counts by type, compliance status, and recent management actions.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Device Management
            </Typography>
            <Typography variant="body2">
              View and manage all enrolled devices, including detailed information about 
              each device's status, installed apps, and configuration profiles.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              App Management
            </Typography>
            <Typography variant="body2">
              Deploy and manage applications on enrolled devices, including 
              App Store apps, custom enterprise apps, and web clips.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Policy Management
            </Typography>
            <Typography variant="body2">
              Create and deploy configuration profiles that define device settings, 
              restrictions, and security policies for different device groups.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Devices
      </Typography>
      <Typography paragraph>
        To manage devices in Mosyle Business:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Devices" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all enrolled devices in your organization, with options to filter by device type, status, or other criteria.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To view details for a specific device, click on its name in the list to see:
            <ul>
              <li><strong>Device Information:</strong> Model, serial number, OS version, etc.</li>
              <li><strong>User Assignment:</strong> Who the device is assigned to</li>
              <li><strong>Installed Apps:</strong> What applications are on the device</li>
              <li><strong>Profiles:</strong> What configuration profiles are applied</li>
              <li><strong>Compliance Status:</strong> Whether the device meets security requirements</li>
              <li><strong>Location:</strong> Last known location (if enabled)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To perform actions on a device, select it and choose from available commands:
            <ul>
              <li><strong>Lock Device:</strong> Remotely lock the device</li>
              <li><strong>Clear Passcode:</strong> Remove the device passcode</li>
              <li><strong>Wipe Device:</strong> Erase all content and settings</li>
              <li><strong>Update OS:</strong> Trigger an operating system update</li>
              <li><strong>Restart/Shutdown:</strong> Remotely restart or power off the device</li>
              <li><strong>Lost Mode:</strong> Enable Lost Mode for missing devices</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Enrolling New Devices
      </Typography>
      <Typography paragraph>
        To enroll new devices in Mosyle Business:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Enrollment" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Choose the enrollment method that best fits your needs:
            <ul>
              <li><strong>User Enrollment:</strong> Send enrollment invitations to users via email</li>
              <li><strong>Manual Enrollment:</strong> Enroll devices one by one using a QR code or URL</li>
              <li><strong>Automated Enrollment:</strong> Use Apple Business Manager for zero-touch deployment</li>
              <li><strong>Bulk Enrollment:</strong> Enroll multiple devices simultaneously</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For user enrollment:
            <ul>
              <li>Enter the user's email address</li>
              <li>Select the device type they will be enrolling</li>
              <li>Choose which device group to assign them to</li>
              <li>Click "Send Invitation"</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For manual enrollment:
            <ul>
              <li>Generate an enrollment profile</li>
              <li>Display the QR code or URL to the user</li>
              <li>Have the user scan the code or visit the URL on their device</li>
              <li>Follow the on-screen instructions to complete enrollment</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Apps
      </Typography>
      <Typography paragraph>
        To manage applications in Mosyle Business:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Apps" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View all available applications in your app catalog.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To add a new app to your catalog:
            <ul>
              <li>Click "Add App"</li>
              <li>Choose the app type (App Store, Custom, Web Clip)</li>
              <li>For App Store apps, search for the app by name</li>
              <li>For custom apps, upload the IPA or PKG file</li>
              <li>For web clips, enter the URL and icon</li>
              <li>Configure deployment options (automatic/on-demand, VPP licensing, etc.)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To deploy an app to devices:
            <ul>
              <li>Select the app from your catalog</li>
              <li>Click "Deploy"</li>
              <li>Choose the target devices or groups</li>
              <li>Set deployment options (required/optional, removal policy, etc.)</li>
              <li>Click "Deploy" to push the app to selected devices</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To update or remove an app:
            <ul>
              <li>Select the app from your catalog</li>
              <li>Choose "Update" to push the latest version</li>
              <li>Choose "Remove" to uninstall from devices</li>
              <li>Select the target devices or groups</li>
              <li>Confirm the action</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating and Deploying Profiles
      </Typography>
      <Typography paragraph>
        To create and deploy configuration profiles:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Profiles" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Create Profile" to start a new configuration profile.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure the profile settings:
            <ul>
              <li><strong>General:</strong> Name, description, and platform (iOS, macOS, tvOS)</li>
              <li><strong>Payload:</strong> The specific settings to configure (Wi-Fi, restrictions, passcode policy, etc.)</li>
              <li><strong>Scope:</strong> Which devices or groups will receive this profile</li>
              <li><strong>Schedule:</strong> When the profile should be deployed</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Common profile types include:
            <ul>
              <li><strong>Wi-Fi Configuration:</strong> Automatically connect to your organization's networks</li>
              <li><strong>Email Configuration:</strong> Set up corporate email accounts</li>
              <li><strong>Security Restrictions:</strong> Disable features like camera or App Store</li>
              <li><strong>Passcode Policy:</strong> Enforce strong passcodes on devices</li>
              <li><strong>VPN Configuration:</strong> Set up VPN connections</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to create the profile, then "Deploy" to push it to the selected devices.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Mosyle Business Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use device groups to organize devices by department, function, or location for easier management.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Create different configuration profiles for different user roles to balance security with usability.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly review device compliance reports to identify devices that may need attention.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use automated enrollment through Apple Business Manager for the smoothest deployment experience.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Take advantage of Mosyle's automated actions to handle routine tasks like OS updates without manual intervention.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Mosyle Business"
      description="Learn how to manage Apple devices with Mosyle Business"
      content={content}
      icon={<MosyleBusinessIcon color="primary" fontSize="large" />}
    />
  );
};

export default MosyleBusinessPage;