import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { AdminPanelSettings as GoogleAdminIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const GoogleAdminPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Google Workspace with Google Admin
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Google Admin to provide easy access to your organization's 
        Google Workspace administration tools directly within the portal. This guide will help you understand 
        how to use the Google Admin integration to manage users, groups, devices, and security settings.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Google Admin?
      </Typography>
      <Typography paragraph>
        Google Admin (formerly G Suite Admin) is the administration console for Google Workspace that allows 
        administrators to manage users, security settings, devices, and other aspects of their organization's 
        Google Workspace account. The integration in the CSF Staff Portal allows administrators to perform 
        common management tasks without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Google Admin in the Portal
      </Typography>
      <Typography paragraph>
        To access the Google Admin integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Google Admin" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Google Admin interface will open, displaying the administration dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the Google Admin integration, you may need to authorize 
            the application with administrator credentials.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Note that access to Google Admin features requires administrator privileges in your Google Workspace account.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Google Admin Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Google Admin integration in the portal provides a user-friendly interface for managing your Google Workspace:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body2">
              The main screen provides an overview of your Google Workspace account, including 
              user counts, recent alerts, and quick access to common administrative tasks.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              User Management
            </Typography>
            <Typography variant="body2">
              Create, view, edit, and delete user accounts. Manage user permissions, reset passwords, 
              and configure user settings such as 2-step verification and app access.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Group Management
            </Typography>
            <Typography variant="body2">
              Create and manage groups for email distribution lists and access control. 
              Add or remove members, configure group settings, and manage group permissions.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Security Settings
            </Typography>
            <Typography variant="body2">
              Configure security policies, review security alerts, manage 2-step verification, 
              and control access to Google services and third-party applications.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Users
      </Typography>
      <Typography paragraph>
        To manage users in Google Admin:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Users" section in the Google Admin dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all users in your organization, with options to filter and search.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To create a new user, click the "Add New User" button and fill in the required information:
            <ul>
              <li><strong>First and Last Name:</strong> The user's full name</li>
              <li><strong>Email Address:</strong> The user's email address (<EMAIL>)</li>
              <li><strong>Password:</strong> Initial password for the account</li>
              <li><strong>Organizational Unit:</strong> Where to place the user in your organization's structure</li>
              <li><strong>Secondary Email:</strong> Optional recovery email address</li>
              <li><strong>Phone Number:</strong> Optional recovery phone number</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To edit a user, click on their name in the user list and modify their details as needed.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Common user management tasks include:
            <ul>
              <li><strong>Reset Password:</strong> Generate a new password for a user</li>
              <li><strong>Suspend User:</strong> Temporarily disable a user account</li>
              <li><strong>Delete User:</strong> Permanently remove a user account</li>
              <li><strong>Transfer Data:</strong> Move a user's data to another account</li>
              <li><strong>Manage Licenses:</strong> Assign or remove product licenses</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Groups
      </Typography>
      <Typography paragraph>
        To manage groups in Google Admin:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Groups" section in the Google Admin dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all groups in your organization, with options to filter and search.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To create a new group, click the "Create Group" button and fill in the required information:
            <ul>
              <li><strong>Name:</strong> The display name for the group</li>
              <li><strong>Email Address:</strong> The email address for the group</li>
              <li><strong>Description:</strong> Optional information about the group's purpose</li>
              <li><strong>Access Type:</strong> Who can join or view the group</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To manage group members, click on a group name and then select the "Members" tab:
            <ul>
              <li><strong>Add Members:</strong> Add users or other groups to the group</li>
              <li><strong>Remove Members:</strong> Remove users from the group</li>
              <li><strong>Change Role:</strong> Modify a member's role (Member, Manager, Owner)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure group settings such as:
            <ul>
              <li><strong>Email Access:</strong> Who can send emails to the group</li>
              <li><strong>Join Requests:</strong> How new membership requests are handled</li>
              <li><strong>Message Moderation:</strong> Whether messages require approval</li>
              <li><strong>Email Footer:</strong> Text to append to all group emails</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Organizational Units
      </Typography>
      <Typography paragraph>
        To manage organizational units (OUs) in Google Admin:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Organizational Units" section in the Google Admin dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View your organization's OU structure in a hierarchical tree view.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To create a new OU, select the parent OU and click "Create Organizational Unit":
            <ul>
              <li><strong>Name:</strong> The name for the new OU</li>
              <li><strong>Description:</strong> Optional information about the OU's purpose</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To move users between OUs:
            <ul>
              <li>Go to the "Users" section</li>
              <li>Select the users you want to move</li>
              <li>Click "Move" and select the destination OU</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure settings at the OU level to apply policies to specific groups of users:
            <ul>
              <li><strong>Services:</strong> Enable or disable Google services for the OU</li>
              <li><strong>Security:</strong> Apply security policies to the OU</li>
              <li><strong>Device Management:</strong> Configure device settings for the OU</li>
              <li><strong>Apps:</strong> Control access to applications for the OU</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Security Settings
      </Typography>
      <Typography paragraph>
        To manage security settings in Google Admin:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Security" section in the Google Admin dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure security policies for your organization:
            <ul>
              <li><strong>Password Policy:</strong> Set requirements for password strength and expiration</li>
              <li><strong>2-Step Verification:</strong> Require additional authentication for users</li>
              <li><strong>Single Sign-On (SSO):</strong> Configure SSO with third-party identity providers</li>
              <li><strong>API Access:</strong> Control which applications can access your Google Workspace data</li>
              <li><strong>Data Protection:</strong> Configure data loss prevention rules</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Review security reports and alerts to identify potential issues:
            <ul>
              <li><strong>Audit Log:</strong> View administrator actions and user activities</li>
              <li><strong>Security Dashboard:</strong> Get an overview of your security posture</li>
              <li><strong>Alert Center:</strong> Review and respond to security alerts</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Google Admin Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use organizational units to apply different policies to different departments or types of users.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Create user templates or use bulk upload features when adding multiple users at once.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly review the security dashboard and alerts to identify potential security issues.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Implement 2-step verification for all users, especially administrators, to enhance security.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use groups for both email distribution and access control to simplify administration.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Google Admin"
      description="Learn how to manage Google Workspace with Google Admin"
      content={content}
      icon={<GoogleAdminIcon color="primary" fontSize="large" />}
    />
  );
};

export default GoogleAdminPage;