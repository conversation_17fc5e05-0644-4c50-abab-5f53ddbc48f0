import React from 'react';
import { Box, Typography, Paper, List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';
import { Settings as SettingsIcon, Security as SecurityIcon, Router as RouterIcon, Check as CheckIcon } from '@mui/icons-material';
import PageHeader from '../../../components/PageHeader';
import HelpSection from '../../../components/HelpSection';

const RadiusPage = () => {
  return (
    <Box sx={{ p: 3 }}>
      <PageHeader 
        title="RADIUS Server Help" 
        subtitle="Learn how to use and configure the RADIUS server integration"
        icon={<SettingsIcon fontSize="large" />}
      />
      
      <HelpSection title="What is RADIUS?">
        <Typography paragraph>
          RADIUS (Remote Authentication Dial-In User Service) is a networking protocol that provides centralized 
          Authentication, Authorization, and Accounting (AAA) management for users who connect to and use network 
          services. The RADIUS server integration in this portal allows you to:
        </Typography>
        
        <List>
          <ListItem>
            <ListItemIcon>
              <SecurityIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Authenticate users through Google SAML or Google API" 
              secondary="Leverage your existing Google authentication for network access"
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <RouterIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Manage RADIUS clients" 
              secondary="Configure network devices that will authenticate against your RADIUS server"
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <CheckIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Monitor authentication logs" 
              secondary="Track successful and failed authentication attempts"
            />
          </ListItem>
        </List>
      </HelpSection>
      
      <Divider sx={{ my: 3 }} />
      
      <HelpSection title="Getting Started">
        <Typography paragraph>
          To start using the RADIUS server integration, you need to:
        </Typography>
        
        <List sx={{ listStyleType: 'decimal', pl: 4 }}>
          <ListItem sx={{ display: 'list-item' }}>
            <Typography>
              <strong>Configure the RADIUS server</strong> - Go to the RADIUS Setup page (admin only) and configure 
              the server with either Google SAML or Google API authentication.
            </Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item' }}>
            <Typography>
              <strong>Add RADIUS clients</strong> - Add network devices (like wireless access points, switches, or VPN servers) 
              that will authenticate against your RADIUS server.
            </Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item' }}>
            <Typography>
              <strong>Configure network devices</strong> - Configure your network devices to use the RADIUS server 
              for authentication.
            </Typography>
          </ListItem>
        </List>
      </HelpSection>
      
      <Divider sx={{ my: 3 }} />
      
      <HelpSection title="Authentication Methods">
        <Typography paragraph>
          The RADIUS server integration supports two authentication methods:
        </Typography>
        
        <Typography variant="h6" gutterBottom>Google SAML</Typography>
        <Typography paragraph>
          Google SAML (Security Assertion Markup Language) allows you to authenticate users against Google's identity 
          provider using SAML. This method requires:
        </Typography>
        <List>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>SAML Entry Point URL from Google</Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>SAML Issuer (Entity ID) for your application</Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>X.509 Certificate from Google</Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>Private Key for SAML signing</Typography>
          </ListItem>
        </List>
        
        <Typography variant="h6" gutterBottom>Google API</Typography>
        <Typography paragraph>
          Google API authentication uses OAuth 2.0 to authenticate users against Google's API. This method requires:
        </Typography>
        <List>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>OAuth Client ID from Google Cloud Console</Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>OAuth Client Secret from Google Cloud Console</Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>OAuth Redirect URI</Typography>
          </ListItem>
        </List>
      </HelpSection>
      
      <Divider sx={{ my: 3 }} />
      
      <HelpSection title="Managing RADIUS Clients">
        <Typography paragraph>
          RADIUS clients are network devices that authenticate users against your RADIUS server. To manage RADIUS clients:
        </Typography>
        
        <List sx={{ listStyleType: 'decimal', pl: 4 }}>
          <ListItem sx={{ display: 'list-item' }}>
            <Typography>
              <strong>Add a client</strong> - Click the "Add Client" button on the RADIUS page and enter the client details:
            </Typography>
            <List>
              <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 4 }}>
                <Typography>Client Name - A descriptive name for the client</Typography>
              </ListItem>
              <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 4 }}>
                <Typography>IP Address - The IP address of the client</Typography>
              </ListItem>
              <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 4 }}>
                <Typography>Shared Secret - A password shared between the client and the RADIUS server</Typography>
              </ListItem>
              <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 4 }}>
                <Typography>Description - Optional description of the client</Typography>
              </ListItem>
            </List>
          </ListItem>
          <ListItem sx={{ display: 'list-item' }}>
            <Typography>
              <strong>Edit a client</strong> - Click the edit icon next to a client to modify its details.
            </Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item' }}>
            <Typography>
              <strong>Delete a client</strong> - Click the delete icon next to a client to remove it.
            </Typography>
          </ListItem>
        </List>
      </HelpSection>
      
      <Divider sx={{ my: 3 }} />
      
      <HelpSection title="Troubleshooting">
        <Typography paragraph>
          If you encounter issues with the RADIUS server integration, try these troubleshooting steps:
        </Typography>
        
        <List>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>
              <strong>Test the connection</strong> - Use the "Test Connection" button on the RADIUS page to verify 
              that the server is running and accessible.
            </Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>
              <strong>Check authentication logs</strong> - Review the authentication logs on the RADIUS page to see 
              if there are any failed authentication attempts and why they failed.
            </Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>
              <strong>Verify client configuration</strong> - Make sure the client IP address and shared secret match 
              what's configured on the network device.
            </Typography>
          </ListItem>
          <ListItem sx={{ display: 'list-item', listStyleType: 'disc', pl: 2 }}>
            <Typography>
              <strong>Check Google authentication</strong> - Verify that your Google SAML or Google API configuration 
              is correct and that you have the necessary permissions.
            </Typography>
          </ListItem>
        </List>
      </HelpSection>
    </Box>
  );
};

export default RadiusPage;