import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Storage as SynologyIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const SynologyPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Accessing Synology Files
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Synology to provide easy access to shared network storage 
        directly within the portal. This guide will help you understand how to use the Synology integration 
        to access, manage, and share files stored on the organization's Synology NAS (Network Attached Storage).
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Synology?
      </Typography>
      <Typography paragraph>
        Synology is a Network Attached Storage (NAS) solution that provides centralized file storage and 
        sharing capabilities for organizations. It allows multiple users to access, store, and share files 
        on a central server, with proper permissions and access controls. The CSF Staff Portal integrates 
        with Synology to provide seamless access to these shared files without requiring separate login 
        credentials or additional software.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Synology in the Portal
      </Typography>
      <Typography paragraph>
        To access the Synology integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Synology" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Synology File Browser will open, displaying the shared folders and files you have permission to access.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing Synology through the portal, you may need to authenticate with your Synology credentials.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Synology Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Synology integration in the portal provides a file browser interface with several key components:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Navigation Panel
            </Typography>
            <Typography variant="body2">
              Located on the left side, this panel allows you to navigate between different shared folders, 
              personal space, and recently accessed files. It provides a hierarchical view of the available 
              storage locations.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              File List
            </Typography>
            <Typography variant="body2">
              The main area displays files and folders in the current location. For each item, you can see 
              the name, size, type, modification date, and owner. You can sort the list by clicking on column headers.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Search Bar
            </Typography>
            <Typography variant="body2">
              Located at the top, the search bar allows you to find files and folders by name or content. 
              You can specify the search location and use filters to narrow down results.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Action Toolbar
            </Typography>
            <Typography variant="body2">
              Provides access to common file operations such as upload, download, create folder, rename, 
              delete, and share. The available actions depend on your permissions for the current folder.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Browsing and Navigating Files
      </Typography>
      <Typography paragraph>
        To navigate through the Synology file system:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Opening Folders:</strong> Double-click on a folder to view its contents.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Navigating Up:</strong> Use the breadcrumb trail at the top of the file list or the "Up" button to navigate to parent folders.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Quick Navigation:</strong> Use the navigation panel on the left to jump to specific shared folders or locations.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Recent Files:</strong> Click on "Recent" in the navigation panel to see files you've recently accessed.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing and Editing Files
      </Typography>
      <Typography paragraph>
        To view or edit files stored on Synology:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on a file to select it.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "View" or "Open" button in the toolbar, or double-click the file.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For supported file types (documents, images, PDFs, etc.), the file will open in the built-in viewer within the portal.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To edit a file, click the "Edit" button in the viewer. Depending on the file type, it may open in an appropriate editor.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For unsupported file types, you'll be prompted to download the file to your computer.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            After editing, save the file to upload the changes back to Synology.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        File Management
      </Typography>
      <Typography paragraph>
        You can perform various file management tasks directly in the Synology integration:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Uploading Files:</strong> Click the "Upload" button and select files from your computer, or drag and drop files into the browser window.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Downloading Files:</strong> Select one or more files and click the "Download" button, or right-click and select "Download."
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Creating Folders:</strong> Click the "Create Folder" button to create a new folder in the current location.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Moving Files:</strong> Select files, then use cut and paste operations or drag and drop to move them between folders.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Copying Files:</strong> Select files, then use copy and paste operations to create duplicates in other locations.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Renaming Files:</strong> Select a file, click the "Rename" button or right-click and select "Rename," then enter the new name.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Deleting Files:</strong> Select files and click the "Delete" button, or right-click and select "Delete."
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Sharing Files
      </Typography>
      <Typography paragraph>
        To share files with others:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Select the file or folder you want to share.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Share" button in the toolbar, or right-click and select "Share."
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            In the sharing dialog, you can:
            <ul>
              <li><strong>Create a Share Link:</strong> Generate a URL that can be sent to others to access the file.</li>
              <li><strong>Set Permissions:</strong> Specify whether recipients can view, edit, or download the shared file.</li>
              <li><strong>Set Expiration:</strong> Specify when the share link will expire.</li>
              <li><strong>Password Protection:</strong> Add a password to protect the shared file.</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Create" to generate the share link.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Copy the link and share it with others via email, messaging, or other communication channels.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Synology Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use the search function to quickly find files instead of browsing through multiple folders.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Create bookmarks for frequently accessed folders by clicking the star icon next to the folder name.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Recent" view to quickly access files you've been working on.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              When uploading large files, keep the browser window open until the upload completes.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Version History" feature (if enabled) to recover previous versions of files.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Remember that your actions in the Synology integration affect the actual files on the server, so be careful when deleting or moving files.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Synology"
      description="Learn how to access and manage Synology files"
      content={content}
      icon={<SynologyIcon color="primary" fontSize="large" />}
    />
  );
};

export default SynologyPage;