import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Lock as UnifiAccessIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const UnifiAccessPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Access Control
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with UniFi Access to provide easy management of door access control 
        directly within the portal. This guide will help you understand how to use the UniFi Access integration 
        to manage access permissions, monitor door activity, and control building security.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is UniFi Access?
      </Typography>
      <Typography paragraph>
        UniFi Access is Ubiquiti's access control system that manages electronic door locks, card readers, 
        keypads, and other access points throughout your facility. It allows authorized administrators to 
        control who can access different areas of the building, when they can access them, and provides 
        detailed logs of all access events. The CSF Staff Portal integration brings these capabilities 
        directly into the portal interface for seamless management.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing UniFi Access in the Portal
      </Typography>
      <Typography paragraph>
        To access the UniFi Access integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "UniFi Access" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The UniFi Access dashboard will open, displaying an overview of your access control system.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If you don't have the necessary permissions to access certain features, they will be hidden or disabled.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        UniFi Access Interface in the Portal
      </Typography>
      <Typography paragraph>
        The UniFi Access integration in the portal provides several key components:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body2">
              Provides an overview of the access control system, including recent access events, 
              door status, and any alerts or notifications. The dashboard gives you a quick snapshot 
              of system health and activity.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Door Management
            </Typography>
            <Typography variant="body2">
              View and control all access points in the facility. See real-time status of doors 
              (locked, unlocked, open, closed), remotely lock or unlock doors, and configure 
              door settings such as auto-lock timers.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              User Access
            </Typography>
            <Typography variant="body2">
              Manage access permissions for staff members and visitors. Assign access cards or 
              PIN codes, set access levels determining which doors users can access, and configure 
              time-based restrictions.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Access Logs
            </Typography>
            <Typography variant="body2">
              View detailed logs of all access events, including successful entries, denied access 
              attempts, door held open alerts, and forced entry alarms. Filter and search logs by 
              user, door, time, or event type.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Monitoring Door Status
      </Typography>
      <Typography paragraph>
        The Door Management section allows you to monitor the status of all access points:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Door Status Indicators:</strong> Visual indicators show whether each door is locked/unlocked and open/closed.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Real-time Updates:</strong> Door status updates in real-time as conditions change.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Filtering and Sorting:</strong> Filter doors by building, floor, status, or other criteria to focus on specific areas.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Door Details:</strong> Click on a door to view detailed information, including hardware model, firmware version, and recent access events.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Controlling Doors
      </Typography>
      <Typography paragraph>
        Authorized users can remotely control doors through the portal:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Locate the door you want to control in the Door Management section.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on the door to select it, or use the checkbox to select multiple doors.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the action buttons to perform operations:
            <ul>
              <li><strong>Lock:</strong> Immediately lock the selected door(s)</li>
              <li><strong>Unlock:</strong> Temporarily unlock the door(s) to allow access</li>
              <li><strong>Hold Open:</strong> Keep the door unlocked for an extended period</li>
              <li><strong>Resume Schedule:</strong> Return the door to its programmed schedule</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For temporary unlocks, you can specify a duration after which the door will automatically re-lock.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing User Access
      </Typography>
      <Typography paragraph>
        The User Access section allows you to manage who can access which doors:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Viewing Users:</strong> Browse or search for users in the system, including staff members and visitors.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Assigning Credentials:</strong> Issue access cards, fobs, or PIN codes to users.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Setting Access Levels:</strong> Define which doors each user can access.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Time Restrictions:</strong> Set time-based access rules, such as allowing access only during business hours.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Temporary Access:</strong> Grant temporary access to visitors or contractors with automatic expiration.
          </Typography>
        </li>
      </ul>
      
      <Typography paragraph>
        To assign access to a user:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the User Access section.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the user you want to modify, or click "Add User" to create a new user.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            In the user details page, go to the "Access" tab.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the access level to assign, or individually select which doors the user can access.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Set any time restrictions or expiration date for the access.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to apply the changes.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Reviewing Access Logs
      </Typography>
      <Typography paragraph>
        The Access Logs section provides detailed information about all access events:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Event Types:</strong> View different types of events, including successful access, denied access, door held open, and forced entry.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Filtering:</strong> Filter logs by user, door, time range, or event type to find specific information.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Exporting:</strong> Export logs to CSV or PDF format for record-keeping or analysis.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Real-time Monitoring:</strong> View access events as they happen with the live log view.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Setting Up Schedules
      </Typography>
      <Typography paragraph>
        Create automated schedules for doors to lock and unlock at specific times:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Schedules" section in the UniFi Access interface.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Create Schedule" to set up a new schedule.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Define the schedule parameters:
            <ul>
              <li><strong>Name:</strong> Give the schedule a descriptive name</li>
              <li><strong>Days:</strong> Select which days of the week the schedule applies to</li>
              <li><strong>Times:</strong> Set the times when doors should unlock and lock</li>
              <li><strong>Doors:</strong> Select which doors the schedule applies to</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to activate the schedule.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          UniFi Access Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Regularly review access logs to identify unusual patterns or potential security issues.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Create different access levels for different types of staff (e.g., administrators, regular staff, maintenance) to simplify permission management.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              When an employee leaves the organization, immediately deactivate their access credentials to maintain security.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Hold Open" function for special events where many people need to enter, but remember to resume the normal schedule afterward.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Set up email or mobile notifications for critical events, such as forced entry attempts or doors left open for extended periods.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="UniFi Access"
      description="Learn how to manage access control with UniFi Access"
      content={content}
      icon={<UnifiAccessIcon color="primary" fontSize="large" />}
    />
  );
};

export default UnifiAccessPage;