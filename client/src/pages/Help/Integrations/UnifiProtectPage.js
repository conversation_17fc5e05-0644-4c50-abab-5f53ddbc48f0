import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Videocam as UnifiProtectIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const UnifiProtectPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Video Security with UniFi Protect
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with UniFi Protect to provide easy access to your organization's 
        video security system directly within the portal. This guide will help you understand how to use 
        the UniFi Protect integration to monitor cameras, view recordings, and manage security events.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is UniFi Protect?
      </Typography>
      <Typography paragraph>
        UniFi Protect is a video security platform developed by Ubiquiti that provides centralized management 
        of security cameras, video recording, and motion detection. It allows you to monitor and secure your 
        facilities with enterprise-grade video surveillance. The integration in the CSF Staff Portal allows 
        authorized staff to access the security system without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing UniFi Protect in the Portal
      </Typography>
      <Typography paragraph>
        To access the UniFi Protect integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "UniFi Protect" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The UniFi Protect interface will open, displaying the video security dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the UniFi Protect integration, you may need to authorize 
            the application with administrator credentials.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Note that access to certain features may be restricted based on your user permissions.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        UniFi Protect Interface in the Portal
      </Typography>
      <Typography paragraph>
        The UniFi Protect integration in the portal provides a user-friendly interface for managing your video security system:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Live View
            </Typography>
            <Typography variant="body2">
              The main screen displays live feeds from your cameras, allowing you to 
              monitor multiple areas simultaneously in a customizable grid layout.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Recordings
            </Typography>
            <Typography variant="body2">
              Access recorded footage from your cameras, with filtering options 
              by date, time, camera, and event type (motion, continuous, etc.).
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Camera Management
            </Typography>
            <Typography variant="body2">
              View and manage your camera devices, including status monitoring, 
              configuration settings, and firmware updates.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Events
            </Typography>
            <Typography variant="body2">
              Review security events such as motion detection, with thumbnail previews 
              and quick access to the associated video footage.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing Live Camera Feeds
      </Typography>
      <Typography paragraph>
        To view live camera feeds in UniFi Protect:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Live View" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The default view shows all cameras in a grid layout. You can:
            <ul>
              <li><strong>Change Layout:</strong> Adjust the grid size to show more or fewer cameras at once</li>
              <li><strong>Focus on Camera:</strong> Click on a camera feed to expand it to full view</li>
              <li><strong>Filter Cameras:</strong> Show only cameras from specific locations or groups</li>
              <li><strong>Create Custom Views:</strong> Save specific camera arrangements for quick access</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            When viewing a specific camera, you can:
            <ul>
              <li><strong>Pan/Tilt/Zoom:</strong> Control PTZ cameras remotely (if supported by the camera)</li>
              <li><strong>Take Snapshot:</strong> Capture a still image from the current view</li>
              <li><strong>Toggle Audio:</strong> Listen to audio from cameras with microphones</li>
              <li><strong>Adjust Quality:</strong> Change the video quality to optimize for your connection</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the timeline at the bottom of the view to quickly jump to recent recordings from the selected camera.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Recorded Footage
      </Typography>
      <Typography paragraph>
        To access and review recorded footage in UniFi Protect:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Recordings" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the calendar and time controls to select the date and time period you want to review.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the camera(s) whose footage you want to view from the camera list.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The timeline will display color-coded segments indicating different types of recordings:
            <ul>
              <li><strong>Blue:</strong> Continuous recording</li>
              <li><strong>Red:</strong> Motion events</li>
              <li><strong>Yellow:</strong> Smart detection events (if supported)</li>
              <li><strong>Gray:</strong> No recording available</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on a segment in the timeline to begin playback from that point.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            While viewing recordings, you can:
            <ul>
              <li><strong>Play/Pause:</strong> Control video playback</li>
              <li><strong>Speed Control:</strong> Adjust playback speed (0.5x to 16x)</li>
              <li><strong>Export:</strong> Save a clip to your local device</li>
              <li><strong>Take Snapshot:</strong> Capture a still image from the recording</li>
              <li><strong>Jump to Event:</strong> Navigate between detected motion events</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Cameras
      </Typography>
      <Typography paragraph>
        To manage cameras in UniFi Protect:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Cameras" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all cameras in your system, with status indicators showing which are online or offline.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To view details for a specific camera, click on it in the list to see:
            <ul>
              <li><strong>Camera Information:</strong> Model, MAC address, IP address, firmware version</li>
              <li><strong>Status:</strong> Online/offline, recording mode, storage usage</li>
              <li><strong>Preview:</strong> Current camera view</li>
              <li><strong>Settings:</strong> Configuration options for the camera</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To configure a camera, click on its settings to access options such as:
            <ul>
              <li><strong>Name and Location:</strong> Identify where the camera is installed</li>
              <li><strong>Recording Mode:</strong> Always, never, or motion-activated recording</li>
              <li><strong>Video Quality:</strong> Resolution, frame rate, and bitrate settings</li>
              <li><strong>Motion Detection:</strong> Sensitivity and zones for motion triggers</li>
              <li><strong>Infrared:</strong> Night vision settings</li>
              <li><strong>Notifications:</strong> Alert settings for motion events</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To perform actions on a camera, select it and choose from available commands:
            <ul>
              <li><strong>Restart Camera:</strong> Reboot the camera remotely</li>
              <li><strong>Upgrade Firmware:</strong> Update the camera's firmware</li>
              <li><strong>Locate:</strong> Flash the camera's LED to help physically locate it</li>
              <li><strong>Remove:</strong> Delete the camera from the system</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Events and Notifications
      </Typography>
      <Typography paragraph>
        To manage events and notifications in UniFi Protect:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Events" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a chronological list of all security events, such as motion detections or camera disconnections.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Filter events by:
            <ul>
              <li><strong>Event Type:</strong> Motion, smart detection, system events</li>
              <li><strong>Camera:</strong> Events from specific cameras</li>
              <li><strong>Time Range:</strong> Events within a specific period</li>
              <li><strong>Severity:</strong> Important or routine events</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on an event to:
            <ul>
              <li><strong>View Details:</strong> See information about when and where the event occurred</li>
              <li><strong>Watch Footage:</strong> Jump directly to the video recording of the event</li>
              <li><strong>Mark as Reviewed:</strong> Indicate that you've addressed the event</li>
              <li><strong>Add Notes:</strong> Document any actions taken in response</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To configure notification settings, go to the "Settings" section and select "Notifications" to:
            <ul>
              <li><strong>Enable/Disable:</strong> Turn notifications on or off for different event types</li>
              <li><strong>Set Recipients:</strong> Choose who receives notifications</li>
              <li><strong>Configure Delivery:</strong> Select notification methods (email, push, etc.)</li>
              <li><strong>Schedule:</strong> Set times when notifications should be active</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          UniFi Protect Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Create camera groups based on location (building, floor, department) for easier navigation and management.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Configure motion zones to focus on areas of interest and reduce false alerts from irrelevant movement.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use smart detection features (if available) to distinguish between people, vehicles, and other motion sources.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Set up custom recording schedules to balance security needs with storage capacity.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly export important footage for long-term storage, as the system may automatically delete older recordings to make space for new ones.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="UniFi Protect"
      description="Learn how to manage video security with UniFi Protect"
      content={content}
      icon={<UnifiProtectIcon color="primary" fontSize="large" />}
    />
  );
};

export default UnifiProtectPage;