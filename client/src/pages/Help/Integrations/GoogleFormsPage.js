import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Assignment as GoogleFormsIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const GoogleFormsPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Creating and Managing Surveys with Google Forms
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Google Forms to provide easy access to your organization's 
        surveys and forms directly within the portal. This guide will help you understand how to use 
        the Google Forms integration to create, distribute, and analyze surveys.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Google Forms?
      </Typography>
      <Typography paragraph>
        Google Forms is a survey administration software that allows you to create custom forms for surveys, 
        quizzes, and data collection. The integration in the CSF Staff Portal allows you to access and manage 
        your organization's forms without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Google Forms in the Portal
      </Typography>
      <Typography paragraph>
        To access the Google Forms integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Google Forms" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Google Forms interface will open, displaying your forms and surveys.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the Google Forms integration, you may need to authorize 
            the application to access your Google account.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Google Forms Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Google Forms integration in the portal provides a user-friendly interface for managing surveys:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Forms Dashboard
            </Typography>
            <Typography variant="body2">
              The main screen displays all your forms in a grid or list view. 
              You can see form titles, creation dates, and response counts at a glance.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Form Editor
            </Typography>
            <Typography variant="body2">
              Create and edit forms with a drag-and-drop interface. Add various question types, 
              sections, images, and videos to create engaging surveys.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Response Analysis
            </Typography>
            <Typography variant="body2">
              View and analyze responses with built-in charts and graphs. Export response data 
              to spreadsheets for more detailed analysis.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Sharing Options
            </Typography>
            <Typography variant="body2">
              Share forms via email, link, or embed them in websites. Control who can access 
              and respond to your forms with permission settings.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating a New Form
      </Typography>
      <Typography paragraph>
        To create a new form:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click the "Create" or "+" button in the Forms dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Choose a template or start with a blank form.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Add a title and description for your form.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Add questions by clicking the "+" button. You can choose from various question types:
            <ul>
              <li><strong>Short Answer:</strong> For brief text responses</li>
              <li><strong>Paragraph:</strong> For longer text responses</li>
              <li><strong>Multiple Choice:</strong> For selecting one option from a list</li>
              <li><strong>Checkboxes:</strong> For selecting multiple options from a list</li>
              <li><strong>Dropdown:</strong> For selecting one option from a dropdown menu</li>
              <li><strong>Linear Scale:</strong> For rating on a numeric scale</li>
              <li><strong>Multiple Choice Grid:</strong> For rating multiple items on the same scale</li>
              <li><strong>Date:</strong> For collecting date information</li>
              <li><strong>Time:</strong> For collecting time information</li>
              <li><strong>File Upload:</strong> For collecting files from respondents</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure each question with options, make questions required if needed, and add descriptions.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Organize your form by adding sections and page breaks for longer surveys.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Customize the form's appearance by changing the theme, colors, and fonts.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to save your form.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Distributing Your Form
      </Typography>
      <Typography paragraph>
        To share your form with respondents:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Open the form you want to distribute.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Send" button in the top-right corner.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Choose your preferred distribution method:
            <ul>
              <li><strong>Email:</strong> Send the form directly to specific email addresses</li>
              <li><strong>Link:</strong> Copy a URL that you can share via messaging apps, social media, etc.</li>
              <li><strong>Embed HTML:</strong> Get code to embed the form in a website</li>
              <li><strong>Social Media:</strong> Share directly to platforms like Facebook or Twitter</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure sharing settings if needed, such as limiting to people in your organization.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Send" to distribute your form.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing and Analyzing Responses
      </Typography>
      <Typography paragraph>
        To view and analyze form responses:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Open the form whose responses you want to view.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Responses" tab at the top of the form editor.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View responses in different formats:
            <ul>
              <li><strong>Summary:</strong> See aggregated data with charts and graphs</li>
              <li><strong>Question:</strong> View responses organized by question</li>
              <li><strong>Individual:</strong> See complete responses from each respondent</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Create Spreadsheet" to export responses to Google Sheets for more detailed analysis.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the "More" menu to download responses as a CSV file or print the response summary.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating Quizzes
      </Typography>
      <Typography paragraph>
        To create a quiz with Google Forms:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Create a new form or open an existing one.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the Settings gear icon in the top-right corner.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Quizzes" tab and toggle "Make this a quiz" to ON.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Configure quiz settings:
            <ul>
              <li><strong>Release Grade:</strong> Choose when to show grades to respondents</li>
              <li><strong>Respondent Can See:</strong> Select what information respondents can view after submission</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Add questions to your quiz as you would for a regular form.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For each question, click "Answer key" to set the correct answer and point value.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Optionally, add answer feedback that respondents will see after submission.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Save and distribute your quiz as you would a regular form.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Google Forms Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use form sections to organize longer surveys into logical groups of questions.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Enable response validation for questions that require specific formats (email addresses, numbers within a range, etc.).
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use conditional logic (Go to section based on answer) to create dynamic forms that show different questions based on previous responses.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly check the "Responses" tab to monitor incoming submissions and identify trends.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Remember to close forms when you're no longer accepting responses to prevent additional submissions.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Google Forms"
      description="Learn how to create and manage surveys with Google Forms"
      content={content}
      icon={<GoogleFormsIcon color="primary" fontSize="large" />}
    />
  );
};

export default GoogleFormsPage;