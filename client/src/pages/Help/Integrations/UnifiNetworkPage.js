import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Router as UnifiNetworkIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const UnifiNetworkPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Network Infrastructure with UniFi Network
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with UniFi Network to provide easy access to your organization's 
        network management system directly within the portal. This guide will help you understand how to use 
        the UniFi Network integration to monitor and manage your network devices, clients, and settings.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is UniFi Network?
      </Typography>
      <Typography paragraph>
        UniFi Network is a software-defined networking platform developed by Ubiquiti that allows you to manage 
        your network infrastructure through a unified controller. It provides centralized management of UniFi 
        devices such as access points, switches, gateways, and security gateways. The integration in the CSF Staff 
        Portal allows administrators to monitor and manage the network without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing UniFi Network in the Portal
      </Typography>
      <Typography paragraph>
        To access the UniFi Network integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "UniFi Network" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The UniFi Network interface will open, displaying the network management dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the UniFi Network integration, you may need to authorize 
            the application with administrator credentials.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Note that access to certain features may be restricted based on your user permissions.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        UniFi Network Interface in the Portal
      </Typography>
      <Typography paragraph>
        The UniFi Network integration in the portal provides a user-friendly interface for managing your network:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body2">
              The main screen provides an overview of your network, including 
              device status, client counts, traffic statistics, and alerts.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Device Management
            </Typography>
            <Typography variant="body2">
              View and manage all UniFi devices on your network, including access points, 
              switches, gateways, and security cameras.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Client Management
            </Typography>
            <Typography variant="body2">
              Monitor and manage client devices connected to your network, 
              view their statistics, and apply policies.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Network Configuration
            </Typography>
            <Typography variant="body2">
              Configure network settings such as wireless networks (SSIDs), 
              VLANs, routing, and security policies.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Monitoring Network Status
      </Typography>
      <Typography paragraph>
        To monitor your network status in UniFi Network:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Dashboard" section, which provides a real-time overview of your network.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Review key metrics and status indicators:
            <ul>
              <li><strong>Device Status:</strong> See which devices are online, offline, or experiencing issues</li>
              <li><strong>Client Count:</strong> View how many clients are connected to your network</li>
              <li><strong>Traffic Statistics:</strong> Monitor bandwidth usage across your network</li>
              <li><strong>Alerts:</strong> View any active alerts or warnings that require attention</li>
              <li><strong>Network Health:</strong> Check the overall health score of your network</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the "Statistics" section to view detailed historical data about your network performance:
            <ul>
              <li><strong>Traffic Analysis:</strong> View bandwidth usage over time</li>
              <li><strong>Client History:</strong> See when clients connect and disconnect</li>
              <li><strong>AP Utilization:</strong> Monitor access point usage and capacity</li>
              <li><strong>Deep Packet Inspection:</strong> Analyze traffic by application type (if enabled)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Check the "Alerts" section for any notifications about network issues that need attention.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Network Devices
      </Typography>
      <Typography paragraph>
        To manage network devices in UniFi Network:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Devices" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all UniFi devices on your network, with status indicators showing which are online or offline.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To view details for a specific device, click on it in the list to see:
            <ul>
              <li><strong>Device Information:</strong> Model, MAC address, IP address, firmware version</li>
              <li><strong>Performance Metrics:</strong> CPU usage, memory usage, temperature</li>
              <li><strong>Port Status:</strong> For switches, view the status of each port</li>
              <li><strong>Connected Clients:</strong> See which clients are connected to this device</li>
              <li><strong>Configuration:</strong> View and modify device-specific settings</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To perform actions on a device, select it and choose from available commands:
            <ul>
              <li><strong>Restart Device:</strong> Reboot the device remotely</li>
              <li><strong>Upgrade Firmware:</strong> Update the device's firmware</li>
              <li><strong>Locate:</strong> Flash the device's LED to help physically locate it</li>
              <li><strong>Forget:</strong> Remove the device from the controller</li>
              <li><strong>Configure:</strong> Modify device settings</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For access points, you can also:
            <ul>
              <li><strong>Adjust Radio Settings:</strong> Change channel, power, and band settings</li>
              <li><strong>Enable/Disable WLAN Groups:</strong> Control which wireless networks are broadcast</li>
              <li><strong>View RF Environment:</strong> Check for interference and neighboring networks</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Network Clients
      </Typography>
      <Typography paragraph>
        To manage client devices in UniFi Network:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Clients" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all client devices currently or recently connected to your network.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To view details for a specific client, click on it in the list to see:
            <ul>
              <li><strong>Client Information:</strong> Name, MAC address, IP address, device type</li>
              <li><strong>Connection Details:</strong> Which access point or switch port they're connected to</li>
              <li><strong>Traffic Statistics:</strong> Bandwidth usage, data transferred</li>
              <li><strong>Connection History:</strong> When they've connected and disconnected</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To perform actions on a client, select it and choose from available options:
            <ul>
              <li><strong>Block Client:</strong> Prevent the client from accessing the network</li>
              <li><strong>Reconnect:</strong> Force the client to reconnect to the network</li>
              <li><strong>Apply Fixed IP:</strong> Assign a static IP address</li>
              <li><strong>Create/Edit Alias:</strong> Give the client a recognizable name</li>
              <li><strong>Apply Bandwidth Limits:</strong> Restrict upload/download speeds</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Configuring Wireless Networks
      </Typography>
      <Typography paragraph>
        To configure wireless networks (SSIDs) in UniFi Network:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Settings" section, then select "Wireless Networks" or "WiFi".
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all configured wireless networks.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To create a new wireless network, click "Add New Wireless Network" and configure:
            <ul>
              <li><strong>Name (SSID):</strong> The name that will be visible to users</li>
              <li><strong>Security:</strong> Authentication type (Open, WPA Personal, WPA Enterprise)</li>
              <li><strong>Password:</strong> For WPA Personal networks</li>
              <li><strong>Advanced Options:</strong>
                <ul>
                  <li><strong>VLAN:</strong> Assign the network to a specific VLAN</li>
                  <li><strong>Band:</strong> 2.4GHz, 5GHz, or both</li>
                  <li><strong>Guest Policy:</strong> Enable guest portal, isolation, bandwidth limits</li>
                  <li><strong>Schedule:</strong> Set times when the network is available</li>
                </ul>
              </li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To edit an existing wireless network, click on it in the list and modify its settings.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to apply your changes to the network.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          UniFi Network Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use the map feature to create a visual representation of your network layout for easier troubleshooting.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Create separate wireless networks (SSIDs) for different purposes (staff, guests, IoT devices) to improve security.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly check for firmware updates to ensure your devices have the latest security patches and features.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use VLANs to segment your network traffic for better performance and security.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Set up alerts for important events like device disconnections or high bandwidth usage to proactively manage your network.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="UniFi Network"
      description="Learn how to manage network infrastructure with UniFi Network"
      content={content}
      icon={<UnifiNetworkIcon color="primary" fontSize="large" />}
    />
  );
};

export default UnifiNetworkPage;