import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { AcUnit as DreoIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const DreoPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Controlling Dreo Devices
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Dreo to provide easy control of Dreo smart devices 
        directly within the portal. This guide will help you understand how to use the Dreo integration 
        to monitor and control fans, heaters, and other Dreo devices throughout your facility.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Dreo?
      </Typography>
      <Typography paragraph>
        Dreo is a manufacturer of smart home devices, including fans, heaters, air purifiers, and other 
        climate control equipment. These devices can be connected to the internet and controlled remotely 
        through apps or integrations like the one in the CSF Staff Portal. The integration allows authorized 
        staff members to monitor and control Dreo devices throughout the facility without needing separate 
        apps or accounts.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing <PERSON>eo in the Portal
      </Typography>
      <Typography paragraph>
        To access the Dreo integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Dreo" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Dreo dashboard will open, displaying all the Dreo devices you have permission to control.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the Dreo integration, you may see a brief introduction or tutorial.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Dreo Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Dreo integration in the portal provides a user-friendly interface for controlling devices:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Device Dashboard
            </Typography>
            <Typography variant="body2">
              The main screen displays all available Dreo devices organized by location or type. 
              Each device card shows the device name, type, current status, and basic controls.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Device Controls
            </Typography>
            <Typography variant="body2">
              Each device has specific controls based on its type. For example, fans have speed and 
              oscillation controls, while heaters have temperature and mode settings. These controls 
              are accessible directly from the device cards.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Location Filters
            </Typography>
            <Typography variant="body2">
              Filter devices by location (room, floor, building) to quickly find and control 
              devices in specific areas. This is especially useful in larger facilities with 
              many devices.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Status Indicators
            </Typography>
            <Typography variant="body2">
              Visual indicators show the current status of each device, including power state, 
              operating mode, temperature settings, and connectivity status. These indicators 
              update in real-time as device states change.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Controlling Dreo Fans
      </Typography>
      <Typography paragraph>
        To control a Dreo fan:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Locate the fan in the device dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on the fan's card to expand the detailed controls, or use the quick controls on the card itself.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the following controls to adjust the fan's settings:
            <ul>
              <li><strong>Power:</strong> Turn the fan on or off</li>
              <li><strong>Speed:</strong> Adjust the fan speed (typically levels 1-9)</li>
              <li><strong>Mode:</strong> Select the operating mode (Normal, Natural, Sleep)</li>
              <li><strong>Oscillation:</strong> Toggle oscillation on or off</li>
              <li><strong>Timer:</strong> Set an auto-off timer (1-12 hours)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Changes take effect immediately and are reflected in the status indicators.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Controlling Dreo Heaters
      </Typography>
      <Typography paragraph>
        To control a Dreo heater:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Locate the heater in the device dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on the heater's card to expand the detailed controls, or use the quick controls on the card itself.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the following controls to adjust the heater's settings:
            <ul>
              <li><strong>Power:</strong> Turn the heater on or off</li>
              <li><strong>Temperature:</strong> Set the target temperature (40-95°F / 4-35°C)</li>
              <li><strong>Mode:</strong> Select the operating mode (High Heat, Low Heat, ECO, Fan Only)</li>
              <li><strong>Oscillation:</strong> Toggle oscillation on or off (if supported by the model)</li>
              <li><strong>Timer:</strong> Set an auto-off timer (1-12 hours)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Changes take effect immediately and are reflected in the status indicators.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Scheduling and Automation
      </Typography>
      <Typography paragraph>
        The Dreo integration allows you to create schedules and automations for devices:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Creating Schedules:</strong> Set devices to turn on or off, or change settings, at specific times or days of the week.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Temperature-Based Rules:</strong> Configure heaters to adjust based on room temperature readings.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Occupancy-Based Controls:</strong> If integrated with occupancy sensors, devices can be set to respond to room occupancy.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Group Control:</strong> Create device groups to control multiple devices simultaneously with a single command.
          </Typography>
        </li>
      </ul>
      
      <Typography paragraph>
        To create a schedule:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click the "Schedules" tab in the Dreo dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Create Schedule" or "Add New Schedule."
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the device(s) to include in the schedule.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Set the schedule parameters (time, days, actions).
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to activate the schedule.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Monitoring Device Status
      </Typography>
      <Typography paragraph>
        The Dreo integration provides monitoring capabilities:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Current Status:</strong> View the real-time status of all devices, including power state, settings, and room temperature.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Usage History:</strong> Access historical data on device usage, including operating hours and settings changes.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Energy Consumption:</strong> For supported models, view estimated energy consumption to help manage energy usage.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Alerts:</strong> Receive notifications for device issues, such as connectivity problems or filter replacement needs.
          </Typography>
        </li>
      </ul>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Dreo Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use the "All Off" feature to quickly turn off all devices in a specific area when the space is unoccupied.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Create schedules that align with your facility's operating hours to ensure devices are not running unnecessarily.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              For heaters, use the ECO mode when possible to reduce energy consumption while maintaining comfort.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Check the device status regularly to ensure all devices are functioning properly and to identify any that may need maintenance.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Remember that changes made through the portal affect the physical devices, so be considerate of others who may be in the spaces where devices are located.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Dreo"
      description="Learn how to control Dreo devices"
      content={content}
      icon={<DreoIcon color="primary" fontSize="large" />}
    />
  );
};

export default DreoPage;