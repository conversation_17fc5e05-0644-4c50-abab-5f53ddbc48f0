import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Inventory as GLPIIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const GLPIPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Using the GLPI Asset Management
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with GLPI (Gestionnaire Libre de Parc Informatique) to provide 
        easy access to IT asset management directly within the portal. This guide will help you understand 
        how to use the GLPI integration to view, track, and manage IT assets.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is GLPI?
      </Typography>
      <Typography paragraph>
        GLPI is an open-source IT Asset Management, issue tracking system, and service desk solution. 
        It helps organizations inventory and manage their IT assets, including computers, software, 
        network equipment, and other devices. GLPI also provides tools for managing support tickets, 
        service requests, and IT documentation.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing GLPI in the Portal
      </Typography>
      <Typography paragraph>
        To access the GLPI integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "GLPI" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The GLPI Asset Browser will open, displaying a list of IT assets based on your access permissions.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If you haven't used GLPI before, you may see a brief introduction or tutorial.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        GLPI Interface in the Portal
      </Typography>
      <Typography paragraph>
        The GLPI integration in the portal provides a streamlined interface for accessing key GLPI features:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Asset Browser
            </Typography>
            <Typography variant="body2">
              View and search for IT assets by type, location, status, or other criteria. The browser displays 
              key information about each asset, such as name, type, status, and location.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Asset Details
            </Typography>
            <Typography variant="body2">
              View detailed information about specific assets, including technical specifications, 
              maintenance history, associated software, and related documentation.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Ticket Management
            </Typography>
            <Typography variant="body2">
              Create, view, and update support tickets related to IT assets. Track the status of your 
              requests and communicate with IT support staff.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Reports and Dashboards
            </Typography>
            <Typography variant="body2">
              Access reports and dashboards that provide insights into asset inventory, status, 
              and usage patterns. View statistics on asset types, locations, and support issues.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Browsing and Searching for Assets
      </Typography>
      <Typography paragraph>
        The GLPI Asset Browser allows you to find assets in several ways:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Asset Categories:</strong> Browse assets by category (computers, monitors, printers, network devices, etc.) using the category tabs or dropdown menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Search Bar:</strong> Use the search bar to find assets by name, serial number, inventory number, or other identifiers.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Advanced Search:</strong> Click the "Advanced Search" button to access more detailed search options, including filters for location, status, user assignment, and technical specifications.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Saved Searches:</strong> Access previously saved searches to quickly find commonly needed assets or groups of assets.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing Asset Details
      </Typography>
      <Typography paragraph>
        To view detailed information about an asset:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Find the asset in the Asset Browser using the search or browsing methods described above.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on the asset's name or the "View Details" button to open the asset's detailed information page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The asset details page includes several tabs with different types of information:
            <ul>
              <li><strong>General:</strong> Basic information about the asset, including name, type, status, and location</li>
              <li><strong>Technical:</strong> Technical specifications and configuration details</li>
              <li><strong>Components:</strong> Information about the asset's components (for computers and other complex devices)</li>
              <li><strong>Software:</strong> List of software installed on the asset (for computers)</li>
              <li><strong>History:</strong> Record of changes, maintenance, and other events related to the asset</li>
              <li><strong>Documents:</strong> Related documentation, manuals, and other files</li>
              <li><strong>Tickets:</strong> Support tickets associated with the asset</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating and Managing Tickets
      </Typography>
      <Typography paragraph>
        To create a support ticket for an IT asset:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the asset's details page as described above.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Create Ticket" button in the asset details page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Alternatively, click on the "Tickets" tab in the main navigation and then click "Create Ticket."
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Fill out the ticket form with the following information:
            <ul>
              <li><strong>Title:</strong> A brief description of the issue or request</li>
              <li><strong>Description:</strong> Detailed information about the issue or request</li>
              <li><strong>Category:</strong> The type of issue or request</li>
              <li><strong>Priority:</strong> The urgency of the issue or request</li>
              <li><strong>Associated Asset:</strong> The asset related to the ticket (if creating from the Tickets tab)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Submit" to create the ticket.
          </Typography>
        </li>
      </ol>
      
      <Typography paragraph>
        To view and manage your tickets:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>My Tickets:</strong> Click on the "Tickets" tab and select "My Tickets" to see tickets you've created.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Ticket Details:</strong> Click on a ticket to view its details, including status updates and communications.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Adding Comments:</strong> Add comments to a ticket to provide additional information or respond to questions.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Closing Tickets:</strong> Mark a ticket as resolved when the issue has been addressed.
          </Typography>
        </li>
      </ul>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          GLPI Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use the "My Assets" filter to quickly see IT assets assigned to you.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              When creating a ticket, provide as much detail as possible to help IT staff resolve the issue efficiently.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Check the status of your tickets regularly to stay informed about progress on your requests.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the search function to find assets by serial number when reporting issues with specific equipment.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Save common searches for quick access to frequently needed asset information.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="GLPI Asset Management"
      description="Learn how to use the GLPI asset management system"
      content={content}
      icon={<GLPIIcon color="primary" fontSize="large" />}
    />
  );
};

export default GLPIPage;