import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Storage as GoogleDriveIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const GoogleDrivePage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Files with Google Drive
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Google Drive to provide easy access to your organization's 
        files and documents directly within the portal. This guide will help you understand how to use 
        the Google Drive integration to view, manage, and share files.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Google Drive?
      </Typography>
      <Typography paragraph>
        Google Drive is a cloud storage service that allows you to store, access, and share files from 
        any device with an internet connection. The integration in the CSF Staff Portal allows you to 
        access your organization's Google Drive files without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Google Drive in the Portal
      </Typography>
      <Typography paragraph>
        To access the Google Drive integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Google Drive" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Google Drive interface will open, displaying your files and folders.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the Google Drive integration, you may need to authorize 
            the application to access your Google Drive account.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Google Drive Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Google Drive integration in the portal provides a user-friendly interface for managing files:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              File Browser
            </Typography>
            <Typography variant="body2">
              The main screen displays your files and folders in a familiar file browser layout. 
              You can navigate through folders, view file details, and perform actions on files.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Search Functionality
            </Typography>
            <Typography variant="body2">
              Use the search bar to quickly find files by name, content, or other criteria. 
              The search function works across all your accessible Google Drive files.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              File Preview
            </Typography>
            <Typography variant="body2">
              Preview files directly in the portal without downloading them. This works for 
              many file types including documents, spreadsheets, presentations, PDFs, and images.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              File Actions
            </Typography>
            <Typography variant="body2">
              Perform common file operations such as download, share, rename, move, and delete. 
              These actions are available through the context menu or action buttons.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing and Managing Files
      </Typography>
      <Typography paragraph>
        To view and manage files in Google Drive:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the folder containing your files, or use the search function to find specific files.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click on a file to preview it, or right-click to see available actions.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Use the following actions to manage your files:
            <ul>
              <li><strong>Preview:</strong> View the file content directly in the portal</li>
              <li><strong>Download:</strong> Save the file to your local device</li>
              <li><strong>Share:</strong> Grant access to other users</li>
              <li><strong>Rename:</strong> Change the file name</li>
              <li><strong>Move:</strong> Relocate the file to a different folder</li>
              <li><strong>Delete:</strong> Remove the file (moves to trash)</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Uploading Files
      </Typography>
      <Typography paragraph>
        To upload files to Google Drive:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the folder where you want to upload the file.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Upload" button in the toolbar.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the file from your device that you want to upload.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Wait for the upload to complete. The file will appear in the current folder.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Sharing Files
      </Typography>
      <Typography paragraph>
        To share files with others:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Right-click on the file you want to share and select "Share" from the context menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            In the sharing dialog, enter the email addresses of the people you want to share with.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Set the appropriate permission level (Viewer, Commenter, or Editor).
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Optionally, add a message to include with the sharing notification.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Share" to grant access to the specified users.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating New Files and Folders
      </Typography>
      <Typography paragraph>
        To create new files or folders:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the location where you want to create the new item.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "New" button in the toolbar.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select the type of item you want to create (Folder, Google Document, Google Spreadsheet, etc.).
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For folders, enter a name and click "Create".
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            For documents, the new file will be created and opened in the appropriate editor.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Google Drive Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use folders to organize your files logically, making them easier to find later.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Take advantage of the search functionality to quickly locate files, even if you don't remember where they're stored.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Starred" feature to mark important files for quick access.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Remember that files shared with you are accessible from the "Shared with me" section.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Be mindful of your organization's file sharing policies when granting access to sensitive documents.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Google Drive"
      description="Learn how to manage files with Google Drive"
      content={content}
      icon={<GoogleDriveIcon color="primary" fontSize="large" />}
    />
  );
};

export default GoogleDrivePage;