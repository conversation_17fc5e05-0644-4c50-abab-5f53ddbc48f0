import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Security as LenelS2NetBoxIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const LenelS2NetBoxPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Access Control with Lenel S2 NetBox
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Lenel S2 NetBox to provide easy access to your organization's 
        physical access control system directly within the portal. This guide will help you understand how to use 
        the Lenel S2 NetBox integration to manage access credentials, monitor door status, and control building security.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What is Lenel S2 NetBox?
      </Typography>
      <Typography paragraph>
        Lenel S2 NetBox is a web-based access control and security management system that allows organizations 
        to monitor and control physical access to facilities. The integration in the CSF Staff Portal allows 
        authorized staff to manage access control without having to switch between applications.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Lenel S2 NetBox in the Portal
      </Typography>
      <Typography paragraph>
        To access the Lenel S2 NetBox integration:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Lenel S2 NetBox" in the Integrations section of the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Lenel S2 NetBox interface will open, displaying the security management dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            If this is your first time accessing the Lenel S2 NetBox integration, you may need to authorize 
            the application with administrator credentials.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Note that access to certain features may be restricted based on your user permissions.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Lenel S2 NetBox Interface in the Portal
      </Typography>
      <Typography paragraph>
        The Lenel S2 NetBox integration in the portal provides a user-friendly interface for managing physical access control:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body2">
              The main screen provides an overview of your access control system, including 
              door status, recent access events, and any active alarms or notifications.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Access Management
            </Typography>
            <Typography variant="body2">
              Manage access credentials, assign access levels to individuals or groups, 
              and configure access schedules and restrictions.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Door Control
            </Typography>
            <Typography variant="body2">
              Monitor door status in real-time, remotely lock or unlock doors, 
              and view access history for specific entry points.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Alarm Monitoring
            </Typography>
            <Typography variant="body2">
              View and respond to security alarms, such as forced entry attempts, 
              doors held open, or other security breaches.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Access Credentials
      </Typography>
      <Typography paragraph>
        To manage access credentials in Lenel S2 NetBox:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "People" or "Cardholders" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all individuals with access credentials in your system.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To add a new person, click the "Add Person" or "New Cardholder" button and fill in the required information:
            <ul>
              <li><strong>Name:</strong> The person's full name</li>
              <li><strong>ID Number:</strong> Employee ID or other identifier</li>
              <li><strong>Access Level:</strong> The level of access to assign</li>
              <li><strong>Credential:</strong> Card number, PIN, or biometric data</li>
              <li><strong>Activation Date:</strong> When the credential becomes active</li>
              <li><strong>Expiration Date:</strong> When the credential expires (if applicable)</li>
              <li><strong>Photo:</strong> Optional photo for identification</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To edit an existing person, click on their name in the list and modify their details as needed.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Common credential management tasks include:
            <ul>
              <li><strong>Activate/Deactivate:</strong> Enable or disable a credential</li>
              <li><strong>Change Access Level:</strong> Modify what areas a person can access</li>
              <li><strong>Replace Credential:</strong> Issue a new card or credential</li>
              <li><strong>Set Temporary Access:</strong> Grant time-limited access</li>
              <li><strong>View Access History:</strong> See where and when a credential was used</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Access Levels
      </Typography>
      <Typography paragraph>
        To manage access levels in Lenel S2 NetBox:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Access Levels" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View a list of all defined access levels in your system.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To create a new access level, click the "Add Access Level" button and configure:
            <ul>
              <li><strong>Name:</strong> A descriptive name for the access level</li>
              <li><strong>Readers/Doors:</strong> Which doors this access level can use</li>
              <li><strong>Schedules:</strong> When access is permitted (e.g., business hours only)</li>
              <li><strong>Description:</strong> Optional details about the access level's purpose</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To modify an existing access level, select it from the list and adjust its settings.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To assign an access level to people:
            <ul>
              <li>Navigate to the "People" section</li>
              <li>Select the individuals to modify</li>
              <li>Click "Assign Access Level" and select the appropriate level</li>
              <li>Alternatively, modify access levels when editing individual records</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Door Monitoring and Control
      </Typography>
      <Typography paragraph>
        To monitor and control doors in Lenel S2 NetBox:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Doors" or "Monitoring" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View the status of all doors in your system, typically with color-coded indicators:
            <ul>
              <li><strong>Green:</strong> Door secured and closed</li>
              <li><strong>Red:</strong> Door unsecured or open</li>
              <li><strong>Yellow:</strong> Door in alarm state (forced open, held open, etc.)</li>
              <li><strong>Gray:</strong> Door status unknown or offline</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To control a door, select it from the list or floor plan and choose an action:
            <ul>
              <li><strong>Momentary Unlock:</strong> Briefly unlock the door for a single entry</li>
              <li><strong>Lock:</strong> Secure the door, overriding the normal schedule</li>
              <li><strong>Unlock:</strong> Unsecure the door, overriding the normal schedule</li>
              <li><strong>Return to Schedule:</strong> Resume normal scheduled operation</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To view door activity, click on a door and select "View Events" or "History" to see recent access attempts.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Alarm Monitoring and Response
      </Typography>
      <Typography paragraph>
        To monitor and respond to alarms in Lenel S2 NetBox:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the "Alarms" or "Events" section in the dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            View active alarms, which are typically sorted by priority and time.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To respond to an alarm:
            <ul>
              <li>Select the alarm from the list</li>
              <li>Review the details, including location, type, and time</li>
              <li>Choose an appropriate response action (Acknowledge, Investigate, Clear, etc.)</li>
              <li>Add notes documenting your response for the audit trail</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            To view historical alarm data, use the "Reports" or "Event History" section to generate reports on past security events.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Lenel S2 NetBox Integration Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Create logical access levels that align with your organization's security policies and physical layout.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly audit access credentials to ensure that former employees or visitors no longer have access.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use time-limited access for contractors and visitors rather than permanent credentials.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Monitor door forced open and door held open alarms, as these are common security issues.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Periodically review access logs to identify unusual patterns or potential security concerns.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Lenel S2 NetBox"
      description="Learn how to manage access control with Lenel S2 NetBox"
      content={content}
      icon={<LenelS2NetBoxIcon color="primary" fontSize="large" />}
    />
  );
};

export default LenelS2NetBoxPage;