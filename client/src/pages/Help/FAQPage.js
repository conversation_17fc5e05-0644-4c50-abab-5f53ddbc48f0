import React, { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  InputAdornment,
  Button
} from '@mui/material';
import { 
  HelpOutline as HelpIcon,
  QuestionAnswer as FAQIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon
} from '@mui/icons-material';

const FAQPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedPanel, setExpandedPanel] = useState(false);

  // FAQ categories and questions
  const faqCategories = [
    {
      category: 'General',
      faqs: [
        { 
          question: 'What is the CSF Staff Portal?', 
          answer: 'The CSF Staff Portal is a centralized platform for CSF staff members to access important information, resources, and services. It provides secure access to Google Drive, shortcuts to frequently used resources, and integrations with various services used by the organization.' 
        },
        { 
          question: 'How do I get access to the portal?', 
          answer: 'Access to the CSF Staff Portal is provided to all CSF staff members. You can log in using your Google account credentials. If you are having trouble accessing the portal, please contact the IT department for assistance.' 
        },
        { 
          question: 'Is my information secure?', 
          answer: 'Yes, the CSF Staff Portal uses secure authentication through Google and implements role-based access control to ensure that users can only access the information and features they are authorized to use.' 
        }
      ]
    },
    {
      category: 'Account & Login',
      faqs: [
        { 
          question: 'How do I log in to the portal?', 
          answer: 'You can log in to the CSF Staff Portal by clicking the "Login" button on the homepage and then selecting your Google account. The portal uses Google authentication for secure access.' 
        },
        { 
          question: 'I forgot my password. What should I do?', 
          answer: 'The CSF Staff Portal uses Google authentication, so if you forgot your Google account password, you will need to reset it through Google. Visit https://accounts.google.com/recovery to reset your password.' 
        },
        { 
          question: 'How do I update my profile information?', 
          answer: 'You can update your profile information by clicking on your avatar in the top-right corner of the portal and selecting "Edit Profile". From there, you can update your personal information, contact details, and profile picture.' 
        }
      ]
    },
    {
      category: 'Features & Functionality',
      faqs: [
        { 
          question: 'How do I access my Google Drive files?', 
          answer: 'You can access your Google Drive files by clicking on the "Drive Files" link in the navigation menu. This will take you to a page where you can browse and search for files in your Google Drive.' 
        },
        { 
          question: 'How do I create a shortcut?', 
          answer: 'To create a shortcut, go to the "Shortcuts" page and click the "Add Shortcut" button. Enter the name and URL for the shortcut, and optionally select an icon. Click "Save" to create the shortcut.' 
        },
        { 
          question: 'How do I use the Staff Directory?', 
          answer: 'The Staff Directory provides information about CSF staff members. You can access it by clicking on the "Staff Directory" link in the navigation menu. From there, you can search for staff members by name, department, or role.' 
        }
      ]
    },
    {
      category: 'Integrations',
      faqs: [
        { 
          question: 'How do I connect to Canva?', 
          answer: 'To connect to Canva, go to the "Canva" page and follow the instructions to authorize the connection. Once connected, you can access your Canva designs directly from the portal.' 
        },
        { 
          question: 'How do I use the GLPI asset management system?', 
          answer: 'The GLPI integration allows you to view and manage IT assets. Go to the "GLPI" page to access the asset browser. You can search for assets by type, location, or status.' 
        },
        { 
          question: 'How do I control Dreo devices?', 
          answer: 'The Dreo integration allows you to control Dreo devices such as fans and heaters. Go to the "Dreo" page to see a list of available devices and control their settings.' 
        }
      ]
    },
    {
      category: 'Troubleshooting',
      faqs: [
        { 
          question: 'The portal is loading slowly. What can I do?', 
          answer: 'If the portal is loading slowly, try clearing your browser cache and cookies, or try using a different browser. If the problem persists, please contact the IT department for assistance.' 
        },
        { 
          question: 'I\'m seeing an error message. What should I do?', 
          answer: 'If you\'re seeing an error message, try refreshing the page. If the error persists, take a screenshot of the error message and contact the IT department for assistance.' 
        },
        { 
          question: 'An integration is not working. How can I fix it?', 
          answer: 'If an integration is not working, first check if you have the necessary permissions to use that integration. If you do, try disconnecting and reconnecting the integration. If the problem persists, contact the IT department for assistance.' 
        }
      ]
    }
  ];

  // Handle accordion expansion
  const handleAccordionChange = (panel) => (event, isExpanded) => {
    setExpandedPanel(isExpanded ? panel : false);
  };

  // Filter FAQs based on search term
  const filteredFAQs = searchTerm 
    ? faqCategories.map(category => ({
        ...category,
        faqs: category.faqs.filter(faq => 
          faq.question.toLowerCase().includes(searchTerm.toLowerCase()) || 
          faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
        )
      })).filter(category => category.faqs.length > 0)
    : faqCategories;

  return (
    <Box>
      {/* Header section */}
      <Box 
        sx={{ 
          bgcolor: 'primary.main', 
          color: 'white',
          py: 4,
          mb: 4
        }}
      >
        <Container maxWidth="lg">
          <Typography
            component="h1"
            variant="h3"
            align="center"
            color="inherit"
            gutterBottom
          >
            Frequently Asked Questions
          </Typography>
          <Typography
            variant="h6"
            align="center"
            color="inherit"
            paragraph
          >
            Find answers to common questions about the CSF Staff Portal
          </Typography>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              component={RouterLink}
              to="/help"
              startIcon={<HelpIcon />}
            >
              Help Center
            </Button>
          </Box>
        </Container>
      </Box>

      {/* FAQ content */}
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Left sidebar with quick links */}
          <Grid item xs={12} md={3}>
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Quick Links
              </Typography>
              <List>
                <ListItem button component={RouterLink} to="/help">
                  <ListItemIcon><HelpIcon /></ListItemIcon>
                  <ListItemText primary="Help Home" />
                </ListItem>
                <ListItem button component={RouterLink} to="/help/faq">
                  <ListItemIcon><FAQIcon /></ListItemIcon>
                  <ListItemText primary="FAQs" />
                </ListItem>
              </List>
            </Paper>

            <Paper elevation={2} sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                FAQ Categories
              </Typography>
              <List>
                {faqCategories.map((category, index) => (
                  <ListItem 
                    button 
                    key={index}
                    onClick={() => {
                      const element = document.getElementById(`category-${index}`);
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                      }
                    }}
                  >
                    <ListItemText primary={category.category} />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>

          {/* Main content area */}
          <Grid item xs={12} md={9}>
            <Box sx={{ mb: 4 }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Search FAQs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            {filteredFAQs.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="h6">
                  No FAQs found matching your search.
                </Typography>
                <Button 
                  variant="text" 
                  color="primary"
                  onClick={() => setSearchTerm('')}
                  sx={{ mt: 2 }}
                >
                  Clear Search
                </Button>
              </Box>
            ) : (
              filteredFAQs.map((category, categoryIndex) => (
                <Box key={categoryIndex} id={`category-${categoryIndex}`} sx={{ mb: 4 }}>
                  <Typography variant="h5" gutterBottom sx={{ mt: 3 }}>
                    {category.category}
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  {category.faqs.map((faq, faqIndex) => (
                    <Accordion 
                      key={faqIndex}
                      expanded={expandedPanel === `${categoryIndex}-${faqIndex}`}
                      onChange={handleAccordionChange(`${categoryIndex}-${faqIndex}`)}
                      sx={{ mb: 1 }}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls={`panel${categoryIndex}-${faqIndex}-content`}
                        id={`panel${categoryIndex}-${faqIndex}-header`}
                      >
                        <Typography variant="subtitle1" fontWeight="medium">
                          {faq.question}
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography>
                          {faq.answer}
                        </Typography>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </Box>
              ))
            )}

            <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="h5" gutterBottom>
                Still Have Questions?
              </Typography>
              <Typography paragraph>
                If you can't find the answer to your question, please contact the IT department for assistance.
              </Typography>
              <Button variant="contained" color="primary">
                Contact Support
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default FAQPage;