import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Link as LinkIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const ShortcutsPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Your Shortcuts
      </Typography>
      
      <Typography paragraph>
        Shortcuts in the CSF Staff Portal provide quick access to your most frequently used resources, 
        both internal and external. This guide will help you understand how to create, organize, and 
        manage your shortcuts effectively.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        What Are Shortcuts?
      </Typography>
      <Typography paragraph>
        Shortcuts are customizable links that allow you to quickly access:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            External websites and web applications
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Internal portal pages and features
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Specific Google Drive files or folders
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Frequently used tools and resources
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Your Shortcuts
      </Typography>
      <Typography paragraph>
        There are two main ways to access your shortcuts:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Shortcuts Page
            </Typography>
            <Typography variant="body2">
              Access the dedicated Shortcuts page by clicking on "Shortcuts" in the main navigation menu. 
              This page displays all your shortcuts in a grid layout with icons and labels.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard Widget
            </Typography>
            <Typography variant="body2">
              Your most important shortcuts can also be displayed in the Quick Links widget on your Dashboard 
              for even faster access without navigating to the Shortcuts page.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating a New Shortcut
      </Typography>
      <Typography paragraph>
        To create a new shortcut:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to the Shortcuts page by clicking "Shortcuts" in the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Add Shortcut" button (+ icon) in the top-right corner of the page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            In the dialog that appears, enter the following information:
            <ul>
              <li><strong>Name:</strong> A descriptive name for your shortcut</li>
              <li><strong>URL:</strong> The web address of the resource</li>
              <li><strong>Icon:</strong> Select an icon to represent your shortcut (optional)</li>
              <li><strong>Color:</strong> Choose a color for the shortcut tile (optional)</li>
              <li><strong>Description:</strong> A brief description of the resource (optional)</li>
              <li><strong>Category:</strong> Assign the shortcut to a category for organization (optional)</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to create the shortcut.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Editing and Deleting Shortcuts
      </Typography>
      <Typography paragraph>
        To edit an existing shortcut:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Hover over the shortcut tile and click the "Edit" icon (pencil) that appears.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Modify the shortcut details in the dialog that appears.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to update the shortcut.
          </Typography>
        </li>
      </ol>
      
      <Typography paragraph>
        To delete a shortcut:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Hover over the shortcut tile and click the "Delete" icon (trash can) that appears.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Confirm the deletion in the confirmation dialog.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Organizing Shortcuts
      </Typography>
      <Typography paragraph>
        You can organize your shortcuts in several ways:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Categories:</strong> Assign shortcuts to categories to group related items together.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Rearranging:</strong> Drag and drop shortcuts to change their order on the Shortcuts page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Pinning:</strong> Pin your most important shortcuts to have them appear at the top of the list.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Dashboard Display:</strong> Choose which shortcuts appear in the Dashboard Quick Links widget.
          </Typography>
        </li>
      </ul>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Shortcut Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Choose distinctive icons and colors for your shortcuts to make them easier to identify at a glance.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use categories to organize shortcuts by function (e.g., "Communication Tools," "Documentation," "Reports").
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Regularly review and clean up your shortcuts to remove ones you no longer use.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              For Google Drive shortcuts, you can link directly to specific files or folders by copying the URL from your browser.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Shortcuts"
      description="Learn how to create and manage your shortcuts"
      content={content}
      icon={<LinkIcon color="primary" fontSize="large" />}
    />
  );
};

export default ShortcutsPage;