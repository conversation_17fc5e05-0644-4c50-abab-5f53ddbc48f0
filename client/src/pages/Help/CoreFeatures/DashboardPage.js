import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Dashboard as DashboardIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const DashboardPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Using the Dashboard
      </Typography>
      
      <Typography paragraph>
        The Dashboard is your personalized homepage in the CSF Staff Portal. It provides a centralized view of 
        important information, quick access to frequently used features, and real-time updates from various 
        integrated systems.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Dashboard Overview
      </Typography>
      <Typography paragraph>
        Your Dashboard is organized into several sections:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Announcements
            </Typography>
            <Typography variant="body2">
              Important organization-wide announcements and updates from administrators. These may include 
              upcoming events, policy changes, or important notices.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Quick Links
            </Typography>
            <Typography variant="body2">
              Your personalized shortcuts to frequently accessed resources, both internal and external. 
              These can be customized to suit your workflow.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Calendar Events
            </Typography>
            <Typography variant="body2">
              Upcoming events from your Google Calendar, including meetings, appointments, and deadlines.
              This widget shows events for the current day and upcoming week.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Recent Files
            </Typography>
            <Typography variant="body2">
              Recently accessed or modified files from your Google Drive, making it easy to pick up 
              where you left off on important documents.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Team Updates
            </Typography>
            <Typography variant="body2">
              Recent activities and updates from your team members, helping you stay informed about 
              collaborative projects and team news.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              System Status
            </Typography>
            <Typography variant="body2">
              Status information for integrated systems and services, alerting you to any outages or 
              maintenance activities that might affect your work.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Customizing Your Dashboard
      </Typography>
      <Typography paragraph>
        You can personalize your Dashboard to better suit your needs:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            <strong>Rearranging Widgets:</strong> Click and drag widgets to reposition them on your Dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Resizing Widgets:</strong> Use the resize handle in the bottom-right corner of each widget to adjust its size.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Adding Widgets:</strong> Click the "Add Widget" button in the top-right corner of the Dashboard to add new widgets.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Removing Widgets:</strong> Click the menu icon in the top-right corner of any widget and select "Remove" to remove it from your Dashboard.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Widget Settings:</strong> Click the menu icon in the top-right corner of any widget and select "Settings" to configure widget-specific options.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Dashboard Notifications
      </Typography>
      <Typography paragraph>
        The Dashboard displays notifications from various sources:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>System Notifications:</strong> Important alerts about the portal itself, such as scheduled maintenance or updates.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Integration Notifications:</strong> Alerts from integrated systems like Planning Center, GLPI, or Dreo.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Team Notifications:</strong> Updates from your team members or team-related activities.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Calendar Reminders:</strong> Notifications about upcoming meetings or events.
          </Typography>
        </li>
      </ul>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Dashboard Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Arrange your most frequently used widgets at the top for easy access.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the refresh button on individual widgets to update their content without reloading the entire page.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              If your Dashboard becomes cluttered, consider removing widgets you rarely use to improve loading times and usability.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Dashboard layouts are saved automatically, so your customizations will persist across sessions.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Dashboard"
      description="Learn how to use and customize your Dashboard"
      content={content}
      icon={<DashboardIcon color="primary" fontSize="large" />}
    />
  );
};

export default DashboardPage;