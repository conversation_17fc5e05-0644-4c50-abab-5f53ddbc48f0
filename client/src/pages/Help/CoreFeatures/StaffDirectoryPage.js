import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { ContactPage as DirectoryIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const StaffDirectoryPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Finding Staff Information
      </Typography>
      
      <Typography paragraph>
        The Staff Directory in the CSF Staff Portal provides a centralized location to find information about 
        all staff members, including contact details, roles, departments, and team affiliations. This guide 
        will help you understand how to use the Staff Directory effectively.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing the Staff Directory
      </Typography>
      <Typography paragraph>
        To access the Staff Directory:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on "Staff Directory" in the main navigation menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            The Staff Directory page will display a list of all staff members with basic information.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Directory Interface
      </Typography>
      <Typography paragraph>
        The Staff Directory interface includes several key components:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Search Bar
            </Typography>
            <Typography variant="body2">
              Located at the top of the page, the search bar allows you to quickly find staff members by name, 
              department, role, or other keywords. As you type, results will update in real-time.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Filters Panel
            </Typography>
            <Typography variant="body2">
              Located on the left side, this panel allows you to filter staff members by department, team, 
              location, or role. You can apply multiple filters to narrow down the results.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Staff List
            </Typography>
            <Typography variant="body2">
              The main area displays staff members in either a grid or list view. Each entry shows the 
              staff member's name, photo, job title, department, and basic contact information.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              View Options
            </Typography>
            <Typography variant="body2">
              Toggle between grid view (showing staff photos prominently) and list view (showing more 
              detailed information in a compact format). You can also sort the directory by different criteria.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Finding Staff Members
      </Typography>
      <Typography paragraph>
        There are several ways to find specific staff members:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Search:</strong> Enter a name, department, or role in the search bar at the top of the page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Filters:</strong> Use the filters panel to narrow down the list by department, team, location, or role.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Browsing:</strong> Scroll through the directory to browse all staff members.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Alphabetical Navigation:</strong> Use the alphabetical index to jump to staff members whose names start with a specific letter.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing Staff Profiles
      </Typography>
      <Typography paragraph>
        To view detailed information about a staff member:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on a staff member's card or list entry in the directory.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            This will open their detailed profile page, which includes:
            <ul>
              <li><strong>Contact Information:</strong> Email, phone number, office location</li>
              <li><strong>Professional Details:</strong> Job title, department, reporting structure</li>
              <li><strong>Team Affiliations:</strong> Teams they belong to and their roles within those teams</li>
              <li><strong>Skills and Expertise:</strong> Areas of specialization and expertise</li>
              <li><strong>Availability:</strong> Current status and working hours (if enabled)</li>
              <li><strong>Bio:</strong> A brief professional biography (if provided)</li>
            </ul>
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Contacting Staff Members
      </Typography>
      <Typography paragraph>
        From a staff member's profile, you can quickly contact them through various channels:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Email:</strong> Click the email address to open your default email client with a new message to that person.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Phone:</strong> On mobile devices, click the phone number to initiate a call.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Chat:</strong> If integrated with communication tools, you may see options to start a chat or video call.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Calendar:</strong> Schedule a meeting directly from their profile by clicking the "Schedule Meeting" button.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Organizational Structure
      </Typography>
      <Typography paragraph>
        The Staff Directory also provides ways to understand the organizational structure:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Department View:</strong> View staff members grouped by department to understand departmental composition.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Team View:</strong> See team compositions and understand cross-functional team structures.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Org Chart:</strong> Access the organizational chart to visualize reporting relationships and hierarchies.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Manager/Direct Reports:</strong> On individual profiles, see who a person reports to and who reports to them.
          </Typography>
        </li>
      </ul>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Staff Directory Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Save frequently contacted staff members as favorites for quick access by clicking the star icon on their profile.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Recently Viewed" section to quickly return to profiles you've recently accessed.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Export contact information to your address book or as a CSV file using the export options.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Check a staff member's availability status before contacting them to see if they're currently available.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Similar Roles" section on a profile to find other staff members with similar responsibilities.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Staff Directory"
      description="Learn how to find and use staff information"
      content={content}
      icon={<DirectoryIcon color="primary" fontSize="large" />}
    />
  );
};

export default StaffDirectoryPage;