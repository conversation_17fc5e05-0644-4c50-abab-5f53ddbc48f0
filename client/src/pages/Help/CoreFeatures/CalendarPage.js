import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { CalendarMonth as CalendarIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const CalendarPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Using the Google Calendar Integration
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal integrates with Google Calendar to provide easy access to your schedule, events, 
        and appointments directly within the portal. This guide will help you understand how to use the 
        calendar features effectively.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing the Calendar
      </Typography>
      <Typography paragraph>
        There are two main ways to access calendar information in the portal:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Calendar Page
            </Typography>
            <Typography variant="body2">
              Access the full calendar view by clicking on "Calendar" in the main navigation menu. 
              This provides a comprehensive view of your schedule with various display options.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard Widget
            </Typography>
            <Typography variant="body2">
              The Calendar Events widget on your Dashboard displays upcoming events for the current day 
              and week, providing a quick overview without navigating to the full calendar.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Calendar Interface
      </Typography>
      <Typography paragraph>
        The Calendar page offers a familiar interface with several key components:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>View Selector:</strong> Switch between Day, Week, Month, and Agenda views.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Date Navigator:</strong> Navigate to different dates using the date picker or previous/next buttons.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Calendar Selector:</strong> Choose which calendars to display (your personal calendar, team calendars, resource calendars, etc.).
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Event Display:</strong> Events are color-coded based on the calendar they belong to.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Action Buttons:</strong> Create new events, refresh the calendar, and access additional options.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Viewing Your Schedule
      </Typography>
      <Typography paragraph>
        The Calendar offers several different views to help you manage your schedule:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Day View
            </Typography>
            <Typography variant="body2">
              Shows a detailed hour-by-hour breakdown of a single day. This view is useful for seeing the exact timing of events and any gaps in your schedule.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Week View
            </Typography>
            <Typography variant="body2">
              Displays a 7-day view with hours shown vertically. This is the default view and provides a good balance between detail and overview.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Month View
            </Typography>
            <Typography variant="body2">
              Shows an entire month at a glance. Events are displayed as small bars or dots, with limited detail visible without clicking on them.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Agenda View
            </Typography>
            <Typography variant="body2">
              Lists upcoming events in chronological order without the calendar grid. This view is useful for seeing a simple list of what's coming up.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Creating Events
      </Typography>
      <Typography paragraph>
        To create a new calendar event:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click the "Create" or "+" button in the top-left corner of the Calendar page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Alternatively, click on the desired time slot in the calendar grid.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            In the event creation dialog, enter the following information:
            <ul>
              <li><strong>Title:</strong> A name for your event</li>
              <li><strong>Date and Time:</strong> When the event will occur</li>
              <li><strong>Duration:</strong> How long the event will last</li>
              <li><strong>Location:</strong> Where the event will take place (physical location or virtual meeting link)</li>
              <li><strong>Description:</strong> Details about the event</li>
              <li><strong>Calendar:</strong> Which calendar to add the event to</li>
              <li><strong>Guests:</strong> People to invite to the event</li>
              <li><strong>Notifications:</strong> When to receive reminders about the event</li>
            </ul>
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to create the event.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Managing Events
      </Typography>
      <Typography paragraph>
        Once events are created, you can manage them in several ways:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Viewing Event Details:</strong> Click on an event to see its full details, including description, attendees, and location.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Editing Events:</strong> Click on an event and select "Edit" to modify any of its details.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Deleting Events:</strong> Click on an event and select "Delete" to remove it from your calendar.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Responding to Invitations:</strong> For events you've been invited to, you can respond with Yes, No, or Maybe directly from the event details.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Moving Events:</strong> Drag and drop events to reschedule them to a different time or day.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Resizing Events:</strong> Click and drag the bottom edge of an event to change its duration.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Calendar Settings
      </Typography>
      <Typography paragraph>
        You can customize your calendar experience through the settings menu:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Display Options:</strong> Choose which days of the week to show, set working hours, and adjust the time zone.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Calendar Visibility:</strong> Select which calendars to display and assign custom colors to each calendar.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Notification Preferences:</strong> Set default notification times for events.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Sharing Settings:</strong> Control who can see your calendar and what level of detail they can access.
          </Typography>
        </li>
      </ul>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Calendar Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use different calendars for different aspects of your life (work, personal, team events) to keep things organized.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Set up notifications for important events to ensure you don't miss them.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the "Find a time" feature when scheduling meetings to identify when all participants are available.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Add video conferencing links (Google Meet, Zoom) directly to calendar events for easy access when the meeting starts.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Use the search function to quickly find past or future events by title, attendee, or location.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Calendar"
      description="Learn how to use the Google Calendar integration"
      content={content}
      icon={<CalendarIcon color="primary" fontSize="large" />}
    />
  );
};

export default CalendarPage;