import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { Navigation as NavigationIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const NavigationPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Understanding the CSF Staff Portal Navigation
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal features an intuitive navigation system designed to help you quickly access 
        the tools and information you need. This guide will help you understand the different navigation 
        elements and how to use them effectively.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Main Navigation Menu
      </Typography>
      <Typography paragraph>
        The main navigation menu is located on the left side of the screen and provides access to all 
        the primary sections of the portal:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Dashboard
            </Typography>
            <Typography variant="body2">
              Your personalized homepage with widgets, announcements, and quick access to frequently used features.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Shortcuts
            </Typography>
            <Typography variant="body2">
              Customizable links to your most frequently used resources, both internal and external.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Drive Files
            </Typography>
            <Typography variant="body2">
              Access to your Google Drive files and shared team drives directly within the portal.
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Staff Directory
            </Typography>
            <Typography variant="body2">
              A searchable directory of all CSF staff members with contact information and team details.
            </Typography>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Top Navigation Bar
      </Typography>
      <Typography paragraph>
        The top navigation bar contains:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Search:</strong> A global search function that allows you to find content across the portal.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Notifications:</strong> Alerts about important updates, announcements, and system messages.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>User Menu:</strong> Access to your profile, account settings, and logout option.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Integrations Menu
      </Typography>
      <Typography paragraph>
        The Integrations section in the main menu provides access to various third-party services that 
        are integrated with the CSF Staff Portal:
      </Typography>
      <ul>
        <li>
          <Typography paragraph>
            <strong>Canva:</strong> Design tools for creating graphics and presentations.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>GLPI:</strong> IT asset management system.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Planning Center:</strong> Church management software.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Synology:</strong> Access to shared network storage.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>Dreo:</strong> Control for Dreo smart devices.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            <strong>UniFi Access:</strong> Door access control system.
          </Typography>
        </li>
      </ul>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Breadcrumb Navigation
      </Typography>
      <Typography paragraph>
        Breadcrumb navigation appears at the top of content pages to show your current location within 
        the portal hierarchy. You can click on any part of the breadcrumb trail to navigate to that level.
      </Typography>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Navigation Tips
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Use the search function to quickly find specific content without navigating through menus.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Customize your shortcuts for one-click access to your most frequently used resources.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              On mobile devices, the main menu can be accessed by tapping the menu icon in the top-left corner.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Navigation"
      description="Learn how to navigate the CSF Staff Portal effectively"
      content={content}
      icon={<NavigationIcon color="primary" fontSize="large" />}
    />
  );
};

export default NavigationPage;