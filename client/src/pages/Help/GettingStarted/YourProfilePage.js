import React from 'react';
import { Typography, Box, Grid, Paper } from '@mui/material';
import { AccountCircle as ProfileIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const YourProfilePage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        Managing Your Profile in the CSF Staff Portal
      </Typography>
      
      <Typography paragraph>
        Your profile in the CSF Staff Portal contains your personal information, contact details, and preferences.
        This guide will help you understand how to view and edit your profile information.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Accessing Your Profile
      </Typography>
      <Typography paragraph>
        To access your profile:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Click on your avatar or profile picture in the top-right corner of any page in the portal.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Select "My Profile" from the dropdown menu.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            You will be taken to your profile page where you can view and edit your information.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Profile Information
      </Typography>
      <Typography paragraph>
        Your profile contains the following information:
      </Typography>
      
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Personal Information
            </Typography>
            <ul>
              <li><Typography variant="body2">Name</Typography></li>
              <li><Typography variant="body2">Job Title</Typography></li>
              <li><Typography variant="body2">Department</Typography></li>
              <li><Typography variant="body2">Profile Picture</Typography></li>
            </ul>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Contact Information
            </Typography>
            <ul>
              <li><Typography variant="body2">Email Address</Typography></li>
              <li><Typography variant="body2">Phone Number</Typography></li>
              <li><Typography variant="body2">Office Location</Typography></li>
            </ul>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Account Settings
            </Typography>
            <ul>
              <li><Typography variant="body2">Notification Preferences</Typography></li>
              <li><Typography variant="body2">Language Preferences</Typography></li>
              <li><Typography variant="body2">Theme Preferences</Typography></li>
            </ul>
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Team Information
            </Typography>
            <ul>
              <li><Typography variant="body2">Team Membership</Typography></li>
              <li><Typography variant="body2">Role within Teams</Typography></li>
              <li><Typography variant="body2">Team Leader Status</Typography></li>
            </ul>
          </Paper>
        </Grid>
      </Grid>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Editing Your Profile
      </Typography>
      <Typography paragraph>
        To edit your profile information:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Navigate to your profile page as described above.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click the "Edit Profile" button near the top of the page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Make the desired changes to your information.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save Changes" to update your profile.
          </Typography>
        </li>
      </ol>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Changing Your Profile Picture
      </Typography>
      <Typography paragraph>
        To change your profile picture:
      </Typography>
      <ol>
        <li>
          <Typography paragraph>
            Go to your profile page.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Hover over your current profile picture and click the "Change Picture" button that appears.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Upload a new image from your computer or select from the available options.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Crop and adjust the image as needed.
          </Typography>
        </li>
        <li>
          <Typography paragraph>
            Click "Save" to update your profile picture.
          </Typography>
        </li>
      </ol>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Important Notes
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Some profile information may be automatically synchronized with your Google account and cannot be changed directly in the portal.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Your profile visibility to other staff members is determined by your role and department settings.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              If you need to change information that you cannot edit, please contact the IT department or your administrator.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Your Profile"
      description="Learn how to view and edit your profile information"
      content={content}
      icon={<ProfileIcon color="primary" fontSize="large" />}
    />
  );
};

export default YourProfilePage;