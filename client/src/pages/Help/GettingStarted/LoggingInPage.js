import React from 'react';
import { Typography, Box } from '@mui/material';
import { Login as LoginIcon } from '@mui/icons-material';
import HelpTopicTemplate from '../HelpTopicTemplate';

const LoggingInPage = () => {
  const content = (
    <>
      <Typography variant="h6" gutterBottom>
        How to Log In to the CSF Staff Portal
      </Typography>
      
      <Typography paragraph>
        The CSF Staff Portal uses Google authentication to provide secure access to authorized staff members.
        Follow these simple steps to log in:
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Step 1: Navigate to the Portal
      </Typography>
      <Typography paragraph>
        Open your web browser and go to the CSF Staff Portal URL. If you're unsure of the URL, 
        please contact the IT department.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Step 2: Click the Login Button
      </Typography>
      <Typography paragraph>
        On the homepage, click the "Login" button located in the top-right corner of the screen.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Step 3: Select Your Google Account
      </Typography>
      <Typography paragraph>
        You'll be redirected to the Google sign-in page. Select your CSF Google account from the list 
        or enter your email address if prompted.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Step 4: Enter Your Password
      </Typography>
      <Typography paragraph>
        Enter your Google account password and complete any two-factor authentication if it's enabled 
        for your account.
      </Typography>
      
      <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
        Step 5: Access Granted
      </Typography>
      <Typography paragraph>
        After successful authentication, you'll be redirected to the CSF Staff Portal dashboard where 
        you can access all the features available to you based on your role.
      </Typography>
      
      <Box sx={{ mt: 4, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Troubleshooting Login Issues
        </Typography>
        <Typography paragraph>
          If you're having trouble logging in, try these steps:
        </Typography>
        <ul>
          <li>
            <Typography paragraph>
              Make sure you're using your CSF Google account, not a personal account.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              Clear your browser cache and cookies, then try again.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              If you've forgotten your password, visit the Google account recovery page at 
              https://accounts.google.com/recovery.
            </Typography>
          </li>
          <li>
            <Typography paragraph>
              If you still can't log in, contact the IT department for assistance.
            </Typography>
          </li>
        </ul>
      </Box>
    </>
  );

  return (
    <HelpTopicTemplate
      title="Logging In"
      description="Learn how to securely log in to the CSF Staff Portal"
      content={content}
      icon={<LoginIcon color="primary" fontSize="large" />}
    />
  );
};

export default LoggingInPage;