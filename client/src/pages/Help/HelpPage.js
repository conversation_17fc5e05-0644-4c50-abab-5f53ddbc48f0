import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import { 
  HelpOutline as HelpIcon,
  QuestionAnswer as FAQIcon,
  Dashboard as DashboardIcon,
  Link as ShortcutIcon,
  Folder as FolderIcon,
  CalendarMonth as CalendarIcon,
  ContactPage as DirectoryIcon
} from '@mui/icons-material';

const HelpPage = () => {
  // Help topics organized by category
  const helpTopics = [
    {
      category: 'Getting Started',
      topics: [
        { title: 'Logging In', description: 'How to log in to the CSF Staff Portal', path: 'logging-in' },
        { title: 'Navigation', description: 'Understanding the portal navigation', path: 'navigation' },
        { title: 'Your Profile', description: 'How to view and edit your profile', path: 'your-profile' }
      ]
    },
    {
      category: 'Core Features',
      topics: [
        { title: 'Dashboard', description: 'Using the dashboard', icon: <DashboardIcon />, path: 'dashboard' },
        { title: 'Shortcuts', description: 'Managing your shortcuts', icon: <ShortcutIcon />, path: 'shortcuts' },
        { title: 'Drive Files', description: 'Accessing and managing files', icon: <FolderIcon />, path: 'drive-files' },
        { title: 'Calendar', description: 'Using the Google Calendar integration', icon: <CalendarIcon />, path: 'calendar' },
        { title: 'Staff Directory', description: 'Finding staff information', icon: <DirectoryIcon />, path: 'staff-directory' }
      ]
    },
    {
      category: 'Integrations',
      topics: [
        { title: 'Canva', description: 'Using the Canva integration', path: 'canva' },
        { title: 'GLPI', description: 'Using the GLPI asset management', path: 'glpi' },
        { title: 'Planning Center', description: 'Using the Planning Center integration', path: 'planning-center' },
        { title: 'Synology', description: 'Accessing Synology files', path: 'synology' },
        { title: 'Dreo', description: 'Controlling Dreo devices', path: 'dreo' },
        { title: 'UniFi Access', description: 'Managing access control', path: 'unifi-access' }
      ]
    }
  ];

  return (
    <Box>
      {/* Header section */}
      <Box 
        sx={{ 
          bgcolor: 'primary.main', 
          color: 'white',
          py: 4,
          mb: 4
        }}
      >
        <Container maxWidth="lg">
          <Typography
            component="h1"
            variant="h3"
            align="center"
            color="inherit"
            gutterBottom
          >
            Help Center
          </Typography>
          <Typography
            variant="h6"
            align="center"
            color="inherit"
            paragraph
          >
            Find answers to your questions about using the CSF Staff Portal
          </Typography>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              color="secondary"
              size="large"
              component={RouterLink}
              to="/help/faq"
              startIcon={<FAQIcon />}
            >
              Frequently Asked Questions
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Help content */}
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Left sidebar with quick links */}
          <Grid item xs={12} md={3}>
            <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Quick Links
              </Typography>
              <List>
                <ListItem button component={RouterLink} to="/help">
                  <ListItemIcon><HelpIcon /></ListItemIcon>
                  <ListItemText primary="Help Home" />
                </ListItem>
                <ListItem button component={RouterLink} to="/help/faq">
                  <ListItemIcon><FAQIcon /></ListItemIcon>
                  <ListItemText primary="FAQs" />
                </ListItem>
              </List>
            </Paper>
          </Grid>

          {/* Main content area */}
          <Grid item xs={12} md={9}>
            <Typography variant="h4" gutterBottom>
              Help Topics
            </Typography>
            <Typography paragraph>
              Select a topic below to learn more about using the CSF Staff Portal.
            </Typography>

            {helpTopics.map((category, index) => (
              <Box key={index} sx={{ mb: 4 }}>
                <Typography variant="h5" gutterBottom sx={{ mt: 3 }}>
                  {category.category}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Grid container spacing={2}>
                  {category.topics.map((topic, topicIndex) => (
                    <Grid item xs={12} sm={6} md={4} key={topicIndex}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            {topic.icon || <HelpIcon color="primary" />}
                            <Typography variant="h6" component="h3" sx={{ ml: 1 }}>
                              {topic.title}
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            {topic.description}
                          </Typography>
                        </CardContent>
                        <CardActions>
                          <Button 
                            size="small" 
                            color="primary" 
                            component={RouterLink} 
                            to={`/help/${topic.path || topic.title.toLowerCase().replace(/\s+/g, '-')}`}
                          >
                            Learn More
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ))}

            <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="h5" gutterBottom>
                Need More Help?
              </Typography>
              <Typography paragraph>
                If you can't find the information you need, please contact the IT department for assistance.
              </Typography>
              <Button variant="contained" color="primary">
                Contact Support
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default HelpPage;
