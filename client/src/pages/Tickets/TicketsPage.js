import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  Button,
  Chip,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Grid,
  CircularProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import ticketService from '../../services/ticketService';
import ticketCategoryService from '../../services/ticketCategoryService';
import ticketTagService from '../../services/ticketTagService';
import { useAuth } from '../../context/AuthContext';
import { formatDistanceToNow } from 'date-fns';

const TicketsPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalTickets, setTotalTickets] = useState(0);
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    category: '',
    assignedTo: '',
    createdBy: '',
    searchQuery: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  // Status and priority options
  const statusOptions = ['Open', 'In Progress', 'Pending', 'Resolved', 'Closed'];
  const priorityOptions = ['Low', 'Medium', 'High', 'Urgent'];

  // Load tickets
  const loadTickets = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = {
        page: page + 1, // API uses 1-based indexing
        limit: rowsPerPage,
        ...filters
      };
      
      const response = await ticketService.getAllTickets(params);
      // Add null checks to prevent "n.length" TypeError
      if (response && response.tickets) {
        setTickets(response.tickets);
        setTotalTickets(response.pagination?.total || 0);
      } else {
        console.error('Invalid response format:', response);
        setTickets([]);
        setTotalTickets(0);
        setError('Received invalid data format from server.');
      }
    } catch (err) {
      console.error('Error loading tickets:', err);
      setError('Failed to load tickets. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load categories and tags for filters
  const loadCategoriesAndTags = async () => {
    try {
      const categoriesResponse = await ticketCategoryService.getAllCategories();
      setCategories(categoriesResponse.data);
      
      const tagsResponse = await ticketTagService.getAllTags();
      setTags(tagsResponse.data);
    } catch (err) {
      console.error('Error loading categories and tags:', err);
    }
  };

  // Initial data load
  useEffect(() => {
    loadTickets();
    loadCategoriesAndTags();
  }, [page, rowsPerPage]);

  // Handle filter changes
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Apply filters
  const applyFilters = () => {
    setPage(0); // Reset to first page when applying filters
    loadTickets();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      status: '',
      priority: '',
      category: '',
      assignedTo: '',
      createdBy: '',
      searchQuery: ''
    });
    setPage(0);
    loadTickets();
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'info';
      case 'in progress':
        return 'primary';
      case 'pending':
        return 'warning';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'default';
      default:
        return 'default';
    }
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'low':
        return 'success';
      case 'medium':
        return 'info';
      case 'high':
        return 'warning';
      case 'urgent':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Tickets
        </Typography>
        
        {/* Actions Bar */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />}
            onClick={() => navigate('/tickets/new')}
          >
            Create Ticket
          </Button>
          
          <Box>
            <Tooltip title="Refresh">
              <IconButton onClick={loadTickets}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Toggle Filters">
              <IconButton onClick={() => setShowFilters(!showFilters)}>
                <FilterIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        {/* Filters */}
        {showFilters && (
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>Filters</Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={filters.status}
                    label="Status"
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="">All</MenuItem>
                    {statusOptions.map(status => (
                      <MenuItem key={status} value={status}>{status}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Priority</InputLabel>
                  <Select
                    name="priority"
                    value={filters.priority}
                    label="Priority"
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="">All</MenuItem>
                    {priorityOptions.map(priority => (
                      <MenuItem key={priority} value={priority}>{priority}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Category</InputLabel>
                  <Select
                    name="category"
                    value={filters.category}
                    label="Category"
                    onChange={handleFilterChange}
                  >
                    <MenuItem value="">All</MenuItem>
                    {categories.map(category => (
                      <MenuItem key={category._id} value={category._id}>{category.name}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  fullWidth
                  size="small"
                  name="searchQuery"
                  label="Search"
                  value={filters.searchQuery}
                  onChange={handleFilterChange}
                  InputProps={{
                    endAdornment: <SearchIcon color="action" />
                  }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                  <Button variant="outlined" onClick={resetFilters}>Reset</Button>
                  <Button variant="contained" onClick={applyFilters}>Apply</Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        )}
        
        {/* Tickets Table */}
        <Paper>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography color="error">{error}</Typography>
              <Button 
                sx={{ mt: 2 }} 
                variant="outlined" 
                onClick={loadTickets}
              >
                Retry
              </Button>
            </Box>
          ) : tickets.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography>No tickets found</Typography>
            </Box>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Title</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Priority</TableCell>
                      <TableCell>Category</TableCell>
                      <TableCell>Assigned To</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell>Updated</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Array.isArray(tickets) && tickets.map((ticket) => {
                      if (!ticket || !ticket._id) return null;
                      return (
                        <TableRow 
                          key={ticket._id}
                          hover
                          onClick={() => navigate(`/tickets/${ticket._id}`)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell>{ticket.ticketNumber || (ticket._id && ticket._id.substring(0, 8)) || 'N/A'}</TableCell>
                          <TableCell>{ticket.title || 'No Title'}</TableCell>
                          <TableCell>
                            <Chip 
                              label={ticket.status || 'Unknown'} 
                              color={getStatusColor(ticket.status)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={ticket.priority || 'Unknown'} 
                              color={getPriorityColor(ticket.priority)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{ticket.category?.name || 'Uncategorized'}</TableCell>
                          <TableCell>{ticket.assignedTo?.name || 'Unassigned'}</TableCell>
                          <TableCell>{formatDate(ticket.createdAt)}</TableCell>
                          <TableCell>{formatDate(ticket.updatedAt)}</TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <TablePagination
                rowsPerPageOptions={[5, 10, 25, 50]}
                component="div"
                count={totalTickets}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default TicketsPage;