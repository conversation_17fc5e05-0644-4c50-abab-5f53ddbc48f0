import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import ticketTagService from '../../services/ticketTagService';
import { useAuth } from '../../context/AuthContext';

const TicketTagsPage = () => {
  const { isAdmin } = useAuth();
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [deletingTag, setDeletingTag] = useState(null);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#1976d2',
    active: true
  });

  useEffect(() => {
    loadTags();
  }, []);

  const loadTags = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ticketTagService.getAllTags();
      setTags(response.data || []);
    } catch (err) {
      console.error('Error loading tags:', err);
      setError('Failed to load tags. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (tag = null) => {
    if (tag) {
      setEditingTag(tag);
      setFormData({
        name: tag.name || '',
        description: tag.description || '',
        color: tag.color || '#1976d2',
        active: tag.active !== false
      });
    } else {
      setEditingTag(null);
      setFormData({
        name: '',
        description: '',
        color: '#1976d2',
        active: true
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingTag(null);
    setFormData({
      name: '',
      description: '',
      color: '#1976d2',
      active: true
    });
  };

  const handleInputChange = (event) => {
    const { name, value, type, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setSaving(true);
    setError(null);

    try {
      if (editingTag) {
        await ticketTagService.updateTag(editingTag._id, formData);
      } else {
        await ticketTagService.createTag(formData);
      }
      
      handleCloseDialog();
      await loadTags();
    } catch (err) {
      console.error('Error saving tag:', err);
      setError(err.response?.data?.message || 'Failed to save tag. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteClick = (tag) => {
    setDeletingTag(tag);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingTag) return;

    setSaving(true);
    setError(null);

    try {
      await ticketTagService.deleteTag(deletingTag._id);
      setDeleteDialogOpen(false);
      setDeletingTag(null);
      await loadTags();
    } catch (err) {
      console.error('Error deleting tag:', err);
      setError(err.response?.data?.message || 'Failed to delete tag. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (!isAdmin()) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Alert severity="warning">
            You don't have permission to access this page.
          </Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Ticket Tags
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Actions Bar */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Add Tag
          </Button>
          
          <IconButton onClick={loadTags} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        </Box>

        {/* Tags Table */}
        <Paper>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : tags.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography>No tags found</Typography>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Preview</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tags.map((tag) => (
                    <TableRow key={tag._id} hover>
                      <TableCell>{tag.name}</TableCell>
                      <TableCell>{tag.description || 'No description'}</TableCell>
                      <TableCell>
                        <Chip
                          label={tag.name}
                          size="small"
                          sx={{ 
                            backgroundColor: tag.color || '#1976d2',
                            color: 'white'
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={tag.active !== false ? 'Active' : 'Inactive'}
                          color={tag.active !== false ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {tag.createdAt ? new Date(tag.createdAt).toLocaleDateString() : 'N/A'}
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(tag)}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteClick(tag)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>

        {/* Add/Edit Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <form onSubmit={handleSubmit}>
            <DialogTitle>
              {editingTag ? 'Edit Tag' : 'Add New Tag'}
            </DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                name="name"
                label="Tag Name"
                fullWidth
                required
                value={formData.name}
                onChange={handleInputChange}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                name="description"
                label="Description"
                fullWidth
                multiline
                rows={3}
                value={formData.description}
                onChange={handleInputChange}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                name="color"
                label="Color"
                type="color"
                fullWidth
                value={formData.color}
                onChange={handleInputChange}
                sx={{ mb: 2 }}
              />
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <input
                  type="checkbox"
                  id="active"
                  name="active"
                  checked={formData.active}
                  onChange={handleInputChange}
                  style={{ marginRight: 8 }}
                />
                <label htmlFor="active">Active</label>
              </Box>
              
              {/* Preview */}
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Preview:
                </Typography>
                <Chip
                  label={formData.name || 'Tag Name'}
                  sx={{ 
                    backgroundColor: formData.color,
                    color: 'white'
                  }}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog} disabled={saving}>
                Cancel
              </Button>
              <Button type="submit" variant="contained" disabled={saving || !formData.name.trim()}>
                {saving ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    {editingTag ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  editingTag ? 'Update' : 'Create'
                )}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Tag</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete the tag "{deletingTag?.name}"? 
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)} disabled={saving}>
              Cancel
            </Button>
            <Button 
              onClick={handleDeleteConfirm} 
              color="error" 
              variant="contained"
              disabled={saving}
            >
              {saving ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default TicketTagsPage;