import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  Chip,
  Avatar,
  Divider,
  TextField,
  Card,
  CardHeader,
  CardContent,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Comment as CommentIcon,
  Send as SendIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Flag as FlagIcon,
  Category as CategoryIcon,
  LocalOffer as TagIcon
} from '@mui/icons-material';
import ticketService from '../../services/ticketService';
import { useAuth } from '../../context/AuthContext';
import { formatDistanceToNow, format } from 'date-fns';

const TicketDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const [ticket, setTicket] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [commentLoading, setCommentLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [statusUpdateOpen, setStatusUpdateOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');

  const statusOptions = ['open', 'pending', 'on_hold', 'resolved', 'closed'];
  const priorityOptions = ['low', 'normal', 'high', 'urgent'];

  useEffect(() => {
    loadTicket();
  }, [id]);

  const loadTicket = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await ticketService.getTicketById(id);
      setTicket(response.data);
      setComments(response.data.comments || []);
    } catch (err) {
      console.error('Error loading ticket:', err);
      setError('Failed to load ticket. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim()) return;
    
    setCommentLoading(true);
    try {
      await ticketService.addComment(id, {
        content: newComment,
        type: 'comment',
        isPublic: true
      });
      setNewComment('');
      await loadTicket(); // Reload to get updated comments
    } catch (err) {
      console.error('Error adding comment:', err);
      setError('Failed to add comment. Please try again.');
    } finally {
      setCommentLoading(false);
    }
  };

  const handleStatusUpdate = async () => {
    if (!newStatus) return;
    
    try {
      await ticketService.updateTicket(id, { status: newStatus });
      setStatusUpdateOpen(false);
      await loadTicket(); // Reload to get updated ticket
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update status. Please try again.');
    }
  };

  const handleDeleteTicket = async () => {
    try {
      await ticketService.deleteTicket(id);
      navigate('/tickets');
    } catch (err) {
      console.error('Error deleting ticket:', err);
      setError('Failed to delete ticket. Please try again.');
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'open':
        return 'info';
      case 'pending':
        return 'warning';
      case 'on_hold':
        return 'default';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'default';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'low':
        return 'success';
      case 'normal':
        return 'info';
      case 'high':
        return 'warning';
      case 'urgent':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return format(date, 'PPpp');
  };

  const formatRelativeDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true });
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error && !ticket) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          <Button variant="outlined" onClick={() => navigate('/tickets')}>
            Back to Tickets
          </Button>
        </Box>
      </Container>
    );
  }

  if (!ticket) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Ticket not found
          </Alert>
          <Button variant="outlined" onClick={() => navigate('/tickets')}>
            Back to Tickets
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton onClick={() => navigate('/tickets')} sx={{ mr: 2 }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h4" component="h1">
              Ticket #{ticket.ticketNumber || ticket._id.substring(0, 8)}
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => navigate(`/tickets/${id}/edit`)}
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => {
                setNewStatus(ticket.status);
                setStatusUpdateOpen(true);
              }}
            >
              Update Status
            </Button>
            {isAdmin() && (
              <IconButton
                color="error"
                onClick={() => setDeleteDialogOpen(true)}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Main Content */}
          <Grid item xs={12} md={8}>
            {/* Ticket Details */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h5" gutterBottom>
                {ticket.subject}
              </Typography>
              
              <Box sx={{ mb: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip 
                  label={ticket.status} 
                  color={getStatusColor(ticket.status)}
                  size="small"
                />
                <Chip 
                  label={ticket.priority} 
                  color={getPriorityColor(ticket.priority)}
                  size="small"
                />
                {ticket.type && (
                  <Chip 
                    label={ticket.type} 
                    variant="outlined"
                    size="small"
                  />
                )}
                {ticket.category && (
                  <Chip 
                    icon={<CategoryIcon />}
                    label={ticket.category.name} 
                    variant="outlined"
                    size="small"
                  />
                )}
              </Box>

              <Typography variant="body1" paragraph>
                {ticket.description}
              </Typography>

              {ticket.tags && ticket.tags.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Tags:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {ticket.tags.map((tag) => (
                      <Chip
                        key={tag._id}
                        icon={<TagIcon />}
                        label={tag.name}
                        size="small"
                        variant="outlined"
                        sx={{ backgroundColor: tag.color || 'default' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>

            {/* Comments Section */}
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <CommentIcon sx={{ mr: 1 }} />
                Comments ({comments.length})
              </Typography>

              {/* Add Comment */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  sx={{ mb: 2 }}
                />
                <Button
                  variant="contained"
                  startIcon={<SendIcon />}
                  onClick={handleAddComment}
                  disabled={commentLoading || !newComment.trim()}
                >
                  {commentLoading ? 'Adding...' : 'Add Comment'}
                </Button>
              </Box>

              <Divider sx={{ mb: 2 }} />

              {/* Comments List */}
              {comments.length === 0 ? (
                <Typography color="text.secondary">
                  No comments yet.
                </Typography>
              ) : (
                comments.map((comment) => (
                  <Card key={comment._id} sx={{ mb: 2 }}>
                    <CardHeader
                      avatar={
                        <Avatar>
                          {comment.author?.name?.[0] || 'U'}
                        </Avatar>
                      }
                      title={comment.author?.name || 'Unknown User'}
                      subheader={formatRelativeDate(comment.createdAt)}
                    />
                    <CardContent sx={{ pt: 0 }}>
                      <Typography variant="body1">
                        {comment.content}
                      </Typography>
                    </CardContent>
                  </Card>
                ))
              )}
            </Paper>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Ticket Information
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Ticket ID
                </Typography>
                <Typography variant="body2">
                  #{ticket.ticketNumber || ticket._id.substring(0, 8)}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1, fontSize: 16 }} />
                  Requester
                </Typography>
                <Typography variant="body2">
                  {ticket.requester?.name || 'Unknown'}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                  <PersonIcon sx={{ mr: 1, fontSize: 16 }} />
                  Assigned To
                </Typography>
                <Typography variant="body2">
                  {ticket.assignedTo?.name || 'Unassigned'}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarIcon sx={{ mr: 1, fontSize: 16 }} />
                  Created
                </Typography>
                <Typography variant="body2">
                  {formatDate(ticket.createdAt)}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarIcon sx={{ mr: 1, fontSize: 16 }} />
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {formatDate(ticket.updatedAt)}
                </Typography>
              </Box>

              {ticket.dueDate && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                    <FlagIcon sx={{ mr: 1, fontSize: 16 }} />
                    Due Date
                  </Typography>
                  <Typography variant="body2">
                    {formatDate(ticket.dueDate)}
                  </Typography>
                </Box>
              )}

              {ticket.followers && ticket.followers.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Followers
                  </Typography>
                  {ticket.followers.map((follower) => (
                    <Typography key={follower._id} variant="body2">
                      {follower.name}
                    </Typography>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Status Update Dialog */}
        <Dialog open={statusUpdateOpen} onClose={() => setStatusUpdateOpen(false)}>
          <DialogTitle>Update Ticket Status</DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={newStatus}
                label="Status"
                onChange={(e) => setNewStatus(e.target.value)}
              >
                {statusOptions.map((status) => (
                  <MenuItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStatusUpdateOpen(false)}>Cancel</Button>
            <Button onClick={handleStatusUpdate} variant="contained">
              Update
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Ticket</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete this ticket? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDeleteTicket} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default TicketDetailPage;