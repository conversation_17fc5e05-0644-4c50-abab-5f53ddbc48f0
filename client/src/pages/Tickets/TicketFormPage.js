import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Autocomplete,
  IconButton,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ticketService from '../../services/ticketService';
import ticketCategoryService from '../../services/ticketCategoryService';
import ticketTagService from '../../services/ticketTagService';
import userService from '../../services/userService';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

const TicketFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [users, setUsers] = useState([]);
  const [groups, setGroups] = useState([]);

  const [formData, setFormData] = useState({
    subject: '',
    description: '',
    priority: 'normal',
    type: 'incident',
    status: 'open',
    category: '',
    assignedTo: '',
    assignedGroup: '',
    requester: user?.id || '',
    requesterEmail: '',
    dueDate: null,
    followers: [],
    tags: []
  });

  const statusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'pending', label: 'Pending' },
    { value: 'on_hold', label: 'On Hold' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' }
  ];

  const priorityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'normal', label: 'Normal' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' }
  ];

  const typeOptions = [
    { value: 'incident', label: 'Incident' },
    { value: 'request', label: 'Request' },
    { value: 'problem', label: 'Problem' },
    { value: 'change', label: 'Change' }
  ];

  useEffect(() => {
    loadFormData();
  }, [id]);

  const loadFormData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load categories, tags, users, and groups
      const [categoriesResponse, tagsResponse, usersResponse] = await Promise.all([
        ticketCategoryService.getAllCategories(),
        ticketTagService.getAllTags(),
        userService.getAllUsers()
      ]);

      setCategories(categoriesResponse.data || []);
      setTags(tagsResponse.data || []);
      setUsers(usersResponse || []);

      // Try to load groups if the endpoint exists
      try {
        const groupsResponse = await axios.get('/api/groups');
        // If successful, update the groups state
        if (groupsResponse.data) {
          // Create a groups state if it doesn't exist
          if (typeof setGroups === 'function') {
            setGroups(groupsResponse.data);
          }
        }
      } catch (groupError) {
        console.warn('Could not load groups:', groupError);
        // Continue without groups, the form will still work
      }

      // If editing, load the ticket
      if (isEdit) {
        const ticketResponse = await ticketService.getTicketById(id);
        const ticket = ticketResponse.data;
        
        setFormData({
          subject: ticket.subject || '',
          description: ticket.description || '',
          priority: ticket.priority || 'normal',
          type: ticket.type || 'incident',
          status: ticket.status || 'open',
          category: ticket.category?._id || '',
          assignedTo: ticket.assignedTo?._id || '',
          assignedGroup: ticket.assignedGroup?._id || '',
          requester: ticket.requester?._id || user?.id || '',
          requesterEmail: ticket.requesterEmail || '',
          dueDate: ticket.dueDate ? new Date(ticket.dueDate) : null,
          followers: ticket.followers?.map(f => f._id) || [],
          tags: ticket.tags?.map(t => t._id) || []
        });
      }
    } catch (err) {
      console.error('Error loading form data:', err);
      setError('Failed to load form data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      dueDate: date
    }));
  };

  const handleTagsChange = (event, value) => {
    setFormData(prev => ({
      ...prev,
      tags: value.map(tag => tag._id || tag)
    }));
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setSaving(true);
    setError(null);

    try {
      const submitData = {
        ...formData,
        dueDate: formData.dueDate ? formData.dueDate.toISOString() : null
      };

      if (isEdit) {
        await ticketService.updateTicket(id, submitData);
      } else {
        await ticketService.createTicket(submitData);
      }

      navigate('/tickets');
    } catch (err) {
      console.error('Error saving ticket:', err);
      setError(err.response?.data?.message || 'Failed to save ticket. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (isEdit) {
      navigate(`/tickets/${id}`);
    } else {
      navigate('/tickets');
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          {/* Header */}
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
            <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="h4" component="h1">
              {isEdit ? 'Edit Ticket' : 'Create New Ticket'}
            </Typography>
          </Box>

          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Subject */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    required
                    name="subject"
                    label="Subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    error={!formData.subject.trim()}
                    helperText={!formData.subject.trim() ? 'Subject is required' : ''}
                  />
                </Grid>

                {/* Description */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    required
                    multiline
                    rows={4}
                    name="description"
                    label="Description"
                    value={formData.description}
                    onChange={handleInputChange}
                    error={!formData.description.trim()}
                    helperText={!formData.description.trim() ? 'Description is required' : ''}
                  />
                </Grid>

                {/* Requester Email */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    name="requesterEmail"
                    label="Requester Email (optional)"
                    type="email"
                    value={formData.requesterEmail}
                    onChange={handleInputChange}
                    helperText="If different from your email. This is where the initial ticket notification will be sent."
                  />
                </Grid>

                {/* Priority and Type */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Priority</InputLabel>
                    <Select
                      name="priority"
                      value={formData.priority}
                      label="Priority"
                      onChange={handleInputChange}
                    >
                      {priorityOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Type</InputLabel>
                    <Select
                      name="type"
                      value={formData.type}
                      label="Type"
                      onChange={handleInputChange}
                    >
                      {typeOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Status (only for edit) */}
                {isEdit && (
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={formData.status}
                        label="Status"
                        onChange={handleInputChange}
                      >
                        {statusOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                {/* Category */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Category</InputLabel>
                    <Select
                      name="category"
                      value={formData.category}
                      label="Category"
                      onChange={handleInputChange}
                    >
                      <MenuItem value="">
                        <em>No Category</em>
                      </MenuItem>
                      {categories.map((category) => (
                        <MenuItem key={category._id} value={category._id}>
                          {category.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Due Date */}
                <Grid item xs={12} sm={6}>
                  <DateTimePicker
                    label="Due Date"
                    value={formData.dueDate}
                    onChange={handleDateChange}
                    renderInput={(params) => <TextField fullWidth {...params} />}
                  />
                </Grid>

                {/* Tags */}
                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    id="tags"
                    options={tags}
                    getOptionLabel={(option) => option.name || ''}
                    value={tags.filter(tag => formData.tags.includes(tag._id))}
                    onChange={handleTagsChange}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          variant="outlined"
                          label={option.name}
                          {...getTagProps({ index })}
                          key={option._id}
                          sx={{ backgroundColor: option.color || 'default' }}
                        />
                      ))
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Tags"
                        placeholder="Select tags..."
                      />
                    )}
                  />
                </Grid>

                {/* Actions */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                    <Button
                      variant="outlined"
                      onClick={handleCancel}
                      startIcon={<CancelIcon />}
                      disabled={saving}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<SaveIcon />}
                      disabled={saving || !formData.subject.trim() || !formData.description.trim()}
                    >
                      {saving ? (
                        <>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          {isEdit ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        isEdit ? 'Update Ticket' : 'Create Ticket'
                      )}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Box>
      </Container>
    </LocalizationProvider>
  );
};

export default TicketFormPage;