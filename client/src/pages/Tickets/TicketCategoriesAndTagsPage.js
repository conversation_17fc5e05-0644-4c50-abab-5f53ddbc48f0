import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import ticketCategoryService from '../../services/ticketCategoryService';
import ticketTagService from '../../services/ticketTagService';
import { useAuth } from '../../context/AuthContext';

// TabPanel component to handle tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ticket-tabpanel-${index}`}
      aria-labelledby={`ticket-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const TicketCategoriesAndTagsPage = () => {
  const { isAdmin } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  
  // Categories state
  const [categories, setCategories] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [categoryError, setCategoryError] = useState(null);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [deleteCategoryDialogOpen, setDeleteCategoryDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [deletingCategory, setDeletingCategory] = useState(null);
  const [savingCategory, setSavingCategory] = useState(false);
  
  // Tags state
  const [tags, setTags] = useState([]);
  const [loadingTags, setLoadingTags] = useState(true);
  const [tagError, setTagError] = useState(null);
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [deleteTagDialogOpen, setDeleteTagDialogOpen] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [deletingTag, setDeletingTag] = useState(null);
  const [savingTag, setSavingTag] = useState(false);

  // Form data for categories and tags
  const [categoryFormData, setCategoryFormData] = useState({
    name: '',
    description: '',
    color: '#1976d2',
    active: true
  });
  
  const [tagFormData, setTagFormData] = useState({
    name: '',
    description: '',
    color: '#1976d2',
    active: true
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Load data on component mount
  useEffect(() => {
    loadCategories();
    loadTags();
  }, []);

  // Categories functions
  const loadCategories = async () => {
    setLoadingCategories(true);
    setCategoryError(null);
    
    try {
      const response = await ticketCategoryService.getAllCategories();
      setCategories(response.data || []);
    } catch (err) {
      console.error('Error loading categories:', err);
      setCategoryError('Failed to load categories. Please try again.');
    } finally {
      setLoadingCategories(false);
    }
  };

  const handleOpenCategoryDialog = (category = null) => {
    if (category) {
      setEditingCategory(category);
      setCategoryFormData({
        name: category.name || '',
        description: category.description || '',
        color: category.color || '#1976d2',
        active: category.active !== false
      });
    } else {
      setEditingCategory(null);
      setCategoryFormData({
        name: '',
        description: '',
        color: '#1976d2',
        active: true
      });
    }
    setCategoryDialogOpen(true);
  };

  const handleCloseCategoryDialog = () => {
    setCategoryDialogOpen(false);
    setEditingCategory(null);
    setCategoryFormData({
      name: '',
      description: '',
      color: '#1976d2',
      active: true
    });
  };

  const handleCategoryInputChange = (event) => {
    const { name, value, type, checked } = event.target;
    setCategoryFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleCategorySubmit = async (event) => {
    event.preventDefault();
    setSavingCategory(true);
    setCategoryError(null);

    try {
      if (editingCategory) {
        await ticketCategoryService.updateCategory(editingCategory._id, categoryFormData);
      } else {
        await ticketCategoryService.createCategory(categoryFormData);
      }
      
      handleCloseCategoryDialog();
      await loadCategories();
    } catch (err) {
      console.error('Error saving category:', err);
      setCategoryError(err.response?.data?.message || 'Failed to save category. Please try again.');
    } finally {
      setSavingCategory(false);
    }
  };

  const handleDeleteCategoryClick = (category) => {
    setDeletingCategory(category);
    setDeleteCategoryDialogOpen(true);
  };

  const handleDeleteCategoryConfirm = async () => {
    if (!deletingCategory) return;

    setSavingCategory(true);
    setCategoryError(null);

    try {
      await ticketCategoryService.deleteCategory(deletingCategory._id);
      setDeleteCategoryDialogOpen(false);
      setDeletingCategory(null);
      await loadCategories();
    } catch (err) {
      console.error('Error deleting category:', err);
      setCategoryError(err.response?.data?.message || 'Failed to delete category. Please try again.');
    } finally {
      setSavingCategory(false);
    }
  };

  // Tags functions
  const loadTags = async () => {
    setLoadingTags(true);
    setTagError(null);
    
    try {
      const response = await ticketTagService.getAllTags();
      setTags(response.data || []);
    } catch (err) {
      console.error('Error loading tags:', err);
      setTagError('Failed to load tags. Please try again.');
    } finally {
      setLoadingTags(false);
    }
  };

  const handleOpenTagDialog = (tag = null) => {
    if (tag) {
      setEditingTag(tag);
      setTagFormData({
        name: tag.name || '',
        description: tag.description || '',
        color: tag.color || '#1976d2',
        active: tag.active !== false
      });
    } else {
      setEditingTag(null);
      setTagFormData({
        name: '',
        description: '',
        color: '#1976d2',
        active: true
      });
    }
    setTagDialogOpen(true);
  };

  const handleCloseTagDialog = () => {
    setTagDialogOpen(false);
    setEditingTag(null);
    setTagFormData({
      name: '',
      description: '',
      color: '#1976d2',
      active: true
    });
  };

  const handleTagInputChange = (event) => {
    const { name, value, type, checked } = event.target;
    setTagFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleTagSubmit = async (event) => {
    event.preventDefault();
    setSavingTag(true);
    setTagError(null);

    try {
      if (editingTag) {
        await ticketTagService.updateTag(editingTag._id, tagFormData);
      } else {
        await ticketTagService.createTag(tagFormData);
      }
      
      handleCloseTagDialog();
      await loadTags();
    } catch (err) {
      console.error('Error saving tag:', err);
      setTagError(err.response?.data?.message || 'Failed to save tag. Please try again.');
    } finally {
      setSavingTag(false);
    }
  };

  const handleDeleteTagClick = (tag) => {
    setDeletingTag(tag);
    setDeleteTagDialogOpen(true);
  };

  const handleDeleteTagConfirm = async () => {
    if (!deletingTag) return;

    setSavingTag(true);
    setTagError(null);

    try {
      await ticketTagService.deleteTag(deletingTag._id);
      setDeleteTagDialogOpen(false);
      setDeletingTag(null);
      await loadTags();
    } catch (err) {
      console.error('Error deleting tag:', err);
      setTagError(err.response?.data?.message || 'Failed to delete tag. Please try again.');
    } finally {
      setSavingTag(false);
    }
  };

  if (!isAdmin()) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Alert severity="warning">
            You don't have permission to access this page.
          </Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Ticket Categories & Tags
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="ticket management tabs">
            <Tab label="Categories" id="ticket-tab-0" aria-controls="ticket-tabpanel-0" />
            <Tab label="Tags" id="ticket-tab-1" aria-controls="ticket-tabpanel-1" />
          </Tabs>
        </Box>

        {/* Categories Tab */}
        <TabPanel value={tabValue} index={0}>
          {categoryError && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setCategoryError(null)}>
              {categoryError}
            </Alert>
          )}

          {/* Actions Bar */}
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenCategoryDialog()}
            >
              Add Category
            </Button>
            
            <IconButton onClick={loadCategories} disabled={loadingCategories}>
              <RefreshIcon />
            </IconButton>
          </Box>

          {/* Categories Table */}
          <Paper>
            {loadingCategories ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : categories.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography>No categories found</Typography>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Color</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {categories.map((category) => (
                      <TableRow key={category._id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: '50%',
                                backgroundColor: category.color || '#1976d2',
                                mr: 1
                              }}
                            />
                            {category.name}
                          </Box>
                        </TableCell>
                        <TableCell>{category.description || 'No description'}</TableCell>
                        <TableCell>
                          <Chip
                            label={category.color || '#1976d2'}
                            size="small"
                            sx={{ backgroundColor: category.color || '#1976d2', color: 'white' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={category.active !== false ? 'Active' : 'Inactive'}
                            color={category.active !== false ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {category.createdAt ? new Date(category.createdAt).toLocaleDateString() : 'N/A'}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenCategoryDialog(category)}
                            sx={{ mr: 1 }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteCategoryClick(category)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </TabPanel>

        {/* Tags Tab */}
        <TabPanel value={tabValue} index={1}>
          {tagError && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setTagError(null)}>
              {tagError}
            </Alert>
          )}

          {/* Actions Bar */}
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenTagDialog()}
            >
              Add Tag
            </Button>
            
            <IconButton onClick={loadTags} disabled={loadingTags}>
              <RefreshIcon />
            </IconButton>
          </Box>

          {/* Tags Table */}
          <Paper>
            {loadingTags ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : tags.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography>No tags found</Typography>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Preview</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tags.map((tag) => (
                      <TableRow key={tag._id} hover>
                        <TableCell>{tag.name}</TableCell>
                        <TableCell>{tag.description || 'No description'}</TableCell>
                        <TableCell>
                          <Chip
                            label={tag.name}
                            size="small"
                            sx={{ 
                              backgroundColor: tag.color || '#1976d2',
                              color: 'white'
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={tag.active !== false ? 'Active' : 'Inactive'}
                            color={tag.active !== false ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {tag.createdAt ? new Date(tag.createdAt).toLocaleDateString() : 'N/A'}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenTagDialog(tag)}
                            sx={{ mr: 1 }}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteTagClick(tag)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </TabPanel>

        {/* Category Add/Edit Dialog */}
        <Dialog open={categoryDialogOpen} onClose={handleCloseCategoryDialog} maxWidth="sm" fullWidth>
          <form onSubmit={handleCategorySubmit}>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Add New Category'}
            </DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                name="name"
                label="Category Name"
                fullWidth
                required
                value={categoryFormData.name}
                onChange={handleCategoryInputChange}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                name="description"
                label="Description"
                fullWidth
                multiline
                rows={3}
                value={categoryFormData.description}
                onChange={handleCategoryInputChange}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                name="color"
                label="Color"
                type="color"
                fullWidth
                value={categoryFormData.color}
                onChange={handleCategoryInputChange}
                sx={{ mb: 2 }}
              />
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <input
                  type="checkbox"
                  id="category-active"
                  name="active"
                  checked={categoryFormData.active}
                  onChange={handleCategoryInputChange}
                  style={{ marginRight: 8 }}
                />
                <label htmlFor="category-active">Active</label>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseCategoryDialog} disabled={savingCategory}>
                Cancel
              </Button>
              <Button type="submit" variant="contained" disabled={savingCategory || !categoryFormData.name.trim()}>
                {savingCategory ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    {editingCategory ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  editingCategory ? 'Update' : 'Create'
                )}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Category Delete Confirmation Dialog */}
        <Dialog open={deleteCategoryDialogOpen} onClose={() => setDeleteCategoryDialogOpen(false)}>
          <DialogTitle>Delete Category</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete the category "{deletingCategory?.name}"? 
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteCategoryDialogOpen(false)} disabled={savingCategory}>
              Cancel
            </Button>
            <Button 
              onClick={handleDeleteCategoryConfirm} 
              color="error" 
              variant="contained"
              disabled={savingCategory}
            >
              {savingCategory ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Tag Add/Edit Dialog */}
        <Dialog open={tagDialogOpen} onClose={handleCloseTagDialog} maxWidth="sm" fullWidth>
          <form onSubmit={handleTagSubmit}>
            <DialogTitle>
              {editingTag ? 'Edit Tag' : 'Add New Tag'}
            </DialogTitle>
            <DialogContent>
              <TextField
                autoFocus
                margin="dense"
                name="name"
                label="Tag Name"
                fullWidth
                required
                value={tagFormData.name}
                onChange={handleTagInputChange}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                name="description"
                label="Description"
                fullWidth
                multiline
                rows={3}
                value={tagFormData.description}
                onChange={handleTagInputChange}
                sx={{ mb: 2 }}
              />
              <TextField
                margin="dense"
                name="color"
                label="Color"
                type="color"
                fullWidth
                value={tagFormData.color}
                onChange={handleTagInputChange}
                sx={{ mb: 2 }}
              />
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <input
                  type="checkbox"
                  id="tag-active"
                  name="active"
                  checked={tagFormData.active}
                  onChange={handleTagInputChange}
                  style={{ marginRight: 8 }}
                />
                <label htmlFor="tag-active">Active</label>
              </Box>
              
              {/* Preview */}
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Preview:
                </Typography>
                <Chip
                  label={tagFormData.name || 'Tag Name'}
                  sx={{ 
                    backgroundColor: tagFormData.color,
                    color: 'white'
                  }}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseTagDialog} disabled={savingTag}>
                Cancel
              </Button>
              <Button type="submit" variant="contained" disabled={savingTag || !tagFormData.name.trim()}>
                {savingTag ? (
                  <>
                    <CircularProgress size={20} sx={{ mr: 1 }} />
                    {editingTag ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  editingTag ? 'Update' : 'Create'
                )}
              </Button>
            </DialogActions>
          </form>
        </Dialog>

        {/* Tag Delete Confirmation Dialog */}
        <Dialog open={deleteTagDialogOpen} onClose={() => setDeleteTagDialogOpen(false)}>
          <DialogTitle>Delete Tag</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete the tag "{deletingTag?.name}"? 
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteTagDialogOpen(false)} disabled={savingTag}>
              Cancel
            </Button>
            <Button 
              onClick={handleDeleteTagConfirm} 
              color="error" 
              variant="contained"
              disabled={savingTag}
            >
              {savingTag ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default TicketCategoriesAndTagsPage;