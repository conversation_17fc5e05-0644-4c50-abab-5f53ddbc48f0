import React, { useState, useEffect } from 'react';
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  Link,
  TextField,
  Typography,
  Alert,
  Paper,
  Switch,
  FormControlLabel
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import rainBirdService from '../../services/rainBirdService';

/**
 * Rain Bird Setup Component
 * Allows users to configure the Rain Bird integration
 */
const RainBirdSetup = () => {
  const [formData, setFormData] = useState({
    host: '',
    password: '',
    port: '443',
    localNetwork: true,
    cloudHost: 'rdz-rbcloud.rainbird.com',
    controllerMac: ''
  });
  const [loading, setLoading] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [oneClickSuccess, setOneClickSuccess] = useState(false);

  // Load current configuration status
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const config = await rainBirdService.getConfig();
        setConfigStatus(config);
        
        // If config exists, populate the form
        if (config) {
          setFormData({
            host: config.host || '',
            password: '', // Don't populate password for security reasons
            port: config.port || '443',
            localNetwork: config.localNetwork !== undefined ? config.localNetwork : true,
            cloudHost: config.cloudHost || 'rdz-rbcloud.rainbird.com',
            controllerMac: config.controllerMac || ''
          });
        }
        
        setLoading(false);
      } catch (err) {
        setLoading(false);
        // Not showing error for config fetch as it might not be configured yet
      }
    };

    fetchConfig();
  }, []);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await rainBirdService.saveConfig(formData);
      
      // Refresh config status
      const config = await rainBirdService.getConfig();
      setConfigStatus(config);
      
      setSuccess('Rain Bird configuration saved successfully!');
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to save Rain Bird configuration. Please try again.');
      console.error('Error saving Rain Bird configuration:', err);
    }
  };

  // Handle one-click setup
  const handleOneClickSetup = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      setOneClickSuccess(false);

      const response = await rainBirdService.oneClickSetup();
      
      setConfigStatus({
        host: response.host,
        port: response.port,
        localNetwork: response.localNetwork,
        configuredAt: response.configuredAt
      });
      
      // Update form data with the new configuration
      setFormData({
        host: response.host || '',
        password: '', // Don't populate password for security reasons
        port: response.port || '443',
        localNetwork: response.localNetwork !== undefined ? response.localNetwork : true,
        cloudHost: response.cloudHost || 'rdz-rbcloud.rainbird.com',
        controllerMac: response.controllerMac || ''
      });
      
      setOneClickSuccess(true);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to set up Rain Bird with one click. Please try again or use manual setup.');
      console.error('Error setting up Rain Bird with one click:', err);
    }
  };

  return (
    <Container maxWidth="md">
      <Helmet>
        <title>Rain Bird Irrigation Setup | CSF Staff Portal</title>
      </Helmet>

      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Rain Bird Irrigation System Setup
        </Typography>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {configStatus && (
          <Alert severity="info" sx={{ mb: 3 }}>
            Rain Bird is configured with host: {configStatus.host}
            <br />
            Last configured: {new Date(configStatus.configuredAt).toLocaleString()}
          </Alert>
        )}

        {!configStatus && !loading && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            Rain Bird integration is not configured yet. Please provide your controller credentials below.
          </Alert>
        )}

        <Card sx={{ mb: 4 }}>
          <CardHeader title="One-Click Setup" />
          <CardContent>
            <Typography variant="body1" paragraph>
              Use our one-click setup to automatically configure Rain Bird integration with default settings.
              This is the easiest way to get started if your Rain Bird controller is on the local network.
            </Typography>

            <Button
              variant="contained"
              color="primary"
              onClick={handleOneClickSetup}
              disabled={loading}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Set Up with One Click'}
            </Button>

            {oneClickSuccess && (
              <Alert severity="success" sx={{ mt: 3 }}>
                Rain Bird has been configured successfully with one-click setup!
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="Manual Configuration" />
          <CardContent>
            <Typography variant="body1" paragraph>
              Alternatively, you can manually provide your Rain Bird controller information.
            </Typography>

            <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.default', mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                How to find your Rain Bird controller information:
              </Typography>
              <ol>
                <Typography component="li">
                  Locate your Rain Bird controller on your network
                </Typography>
                <Typography component="li">
                  Find the IP address of your controller (check your router's DHCP client list)
                </Typography>
                <Typography component="li">
                  Use the default password or the password you've set for your controller
                </Typography>
                <Typography component="li">
                  If using cloud control, you'll need the MAC address of your controller
                </Typography>
              </ol>

              <Typography variant="body2" sx={{ mt: 2 }}>
                For more information, refer to your Rain Bird controller's documentation:
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                component={Link}
                href="https://www.rainbird.com/support"
                target="_blank"
                rel="noopener noreferrer"
                sx={{ mt: 1 }}
              >
                Rain Bird Support
              </Button>
            </Paper>

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        name="localNetwork"
                        checked={formData.localNetwork}
                        onChange={handleChange}
                      />
                    }
                    label="Controller is on local network"
                  />
                </Grid>

                <Grid item xs={12} sm={8}>
                  <FormControl fullWidth>
                    <TextField
                      label="Host"
                      name="host"
                      value={formData.host}
                      onChange={handleChange}
                      required
                    />
                    <FormHelperText>Enter the IP address or hostname of your Rain Bird controller</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <TextField
                      label="Port"
                      name="port"
                      type="number"
                      value={formData.port}
                      onChange={handleChange}
                      required
                    />
                    <FormHelperText>Default is 443</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <TextField
                      label="Password"
                      name="password"
                      type="password"
                      value={formData.password}
                      onChange={handleChange}
                      required={!configStatus} // Only required for new configurations
                    />
                    <FormHelperText>Enter your Rain Bird controller password (used for encryption)</FormHelperText>
                  </FormControl>
                </Grid>

                {!formData.localNetwork && (
                  <>
                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <TextField
                          label="Cloud Host"
                          name="cloudHost"
                          value={formData.cloudHost}
                          onChange={handleChange}
                        />
                        <FormHelperText>Default is rdz-rbcloud.rainbird.com</FormHelperText>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <TextField
                          label="Controller MAC Address"
                          name="controllerMac"
                          value={formData.controllerMac}
                          onChange={handleChange}
                          required={!formData.localNetwork}
                        />
                        <FormHelperText>Required for cloud control (format: AABBCCDDEEFF)</FormHelperText>
                      </FormControl>
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default RainBirdSetup;