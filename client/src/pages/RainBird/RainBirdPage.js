import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  Slider,
  Switch,
  Tab,
  Tabs,
  Typography,
  Alert,
  Tooltip
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  Opacity as WaterIcon,
  Power as PowerIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  WaterDrop as RainIcon,
  Timer as TimerIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import rainBirdService from '../../services/rainBirdService';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rain-bird-tabpanel-${index}`}
      aria-labelledby={`rain-bird-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

/**
 * Rain Bird Page Component
 * Main interface for controlling Rain Bird irrigation system
 */
const RainBirdPage = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [zones, setZones] = useState([]);
  const [programs, setPrograms] = useState([]);
  const [selectedZone, setSelectedZone] = useState(null);
  const [systemStatus, setSystemStatus] = useState(null);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [duration, setDuration] = useState(5); // Default duration in minutes

  // Load configuration status, zones, programs, and system status on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if Rain Bird is configured
        const config = await rainBirdService.getConfig();
        setConfigStatus(config);

        if (config) {
          try {
            // Fetch zones
            const zonesResponse = await rainBirdService.getZones();
            setZones(zonesResponse);

            // Fetch programs
            const programsResponse = await rainBirdService.getPrograms();
            setPrograms(programsResponse);

            // Fetch system status
            const statusResponse = await rainBirdService.getSystemStatus();
            setSystemStatus(statusResponse);

            // Select the first zone by default if available
            if (zonesResponse.length > 0) {
              setSelectedZone(zonesResponse[0]);
            }
          } catch (dataErr) {
            console.error('Error fetching data from Rain Bird API:', dataErr);
            setError('Failed to fetch data from Rain Bird. Please check your connection and try again.');
          }
        }

        setLoading(false);
      } catch (err) {
        setLoading(false);
        setError('Failed to load data from Rain Bird. Please check your connection and try again.');
        console.error('Error loading Rain Bird data:', err);
      }
    };

    fetchData();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle zone selection
  const handleZoneSelect = (zone) => {
    setSelectedZone(zone);
  };

  // Handle duration change
  const handleDurationChange = (event, newValue) => {
    setDuration(newValue);
  };

  // Handle start zone
  const handleStartZone = async (zoneId) => {
    try {
      setLoading(true);
      await rainBirdService.startZone(zoneId, duration * 60); // Convert minutes to seconds
      
      // Refresh data
      await refreshData();
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to start zone ${zoneId}. Please try again.`);
      console.error(`Error starting zone ${zoneId}:`, err);
    }
  };

  // Handle stop zone
  const handleStopZone = async (zoneId) => {
    try {
      setLoading(true);
      await rainBirdService.stopZone(zoneId);
      
      // Refresh data
      await refreshData();
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to stop zone ${zoneId}. Please try again.`);
      console.error(`Error stopping zone ${zoneId}:`, err);
    }
  };

  // Handle stop all zones
  const handleStopAllZones = async () => {
    try {
      setLoading(true);
      await rainBirdService.stopAllZones();
      
      // Refresh data
      await refreshData();
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to stop all zones. Please try again.');
      console.error('Error stopping all zones:', err);
    }
  };

  // Handle start program
  const handleStartProgram = async (programId) => {
    try {
      setLoading(true);
      await rainBirdService.startProgram(programId);
      
      // Refresh data
      await refreshData();
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to start program ${programId}. Please try again.`);
      console.error(`Error starting program ${programId}:`, err);
    }
  };

  // Handle stop program
  const handleStopProgram = async (programId) => {
    try {
      setLoading(true);
      await rainBirdService.stopProgram(programId);
      
      // Refresh data
      await refreshData();
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to stop program ${programId}. Please try again.`);
      console.error(`Error stopping program ${programId}:`, err);
    }
  };

  // Handle set rain delay
  const handleSetRainDelay = async (hours) => {
    try {
      setLoading(true);
      await rainBirdService.setRainDelay(hours);
      
      // Refresh data
      await refreshData();
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to set rain delay. Please try again.`);
      console.error('Error setting rain delay:', err);
    }
  };

  // Refresh all data
  const refreshData = async () => {
    try {
      setLoading(true);
      
      // Fetch zones
      const zonesResponse = await rainBirdService.getZones();
      setZones(zonesResponse);

      // Fetch programs
      const programsResponse = await rainBirdService.getPrograms();
      setPrograms(programsResponse);

      // Fetch system status
      const statusResponse = await rainBirdService.getSystemStatus();
      setSystemStatus(statusResponse);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to refresh data. Please try again.');
      console.error('Error refreshing data:', err);
    }
  };

  return (
    <Container maxWidth="lg">
      <Helmet>
        <title>Rain Bird Irrigation Control | CSF Staff Portal</title>
      </Helmet>

      <Box sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Rain Bird Irrigation Control
          </Typography>
          
          <Box>
            <Tooltip title="Refresh">
              <IconButton onClick={refreshData} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Settings">
              <IconButton component={Link} to="/rain-bird/setup">
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {!configStatus && !loading && (
          <Alert 
            severity="warning" 
            sx={{ mb: 3 }}
            action={
              <Button color="inherit" size="small" component={Link} to="/rain-bird/setup">
                Configure Now
              </Button>
            }
          >
            Rain Bird integration is not configured yet. Please configure it to control your irrigation system.
          </Alert>
        )}

        {configStatus && zones.length === 0 && !loading && (
          <Alert severity="info" sx={{ mb: 3 }}>
            No irrigation zones found. Make sure your Rain Bird controller is properly connected and configured.
          </Alert>
        )}

        {configStatus && (
          <Grid container spacing={3}>
            {/* System Status Card */}
            <Grid item xs={12}>
              <Card sx={{ mb: 3 }}>
                <CardHeader title="System Status" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PowerIcon color={systemStatus?.activeZones?.length > 0 ? "primary" : "disabled"} sx={{ mr: 1 }} />
                          <Typography variant="body1">Irrigation Active</Typography>
                        </Box>
                        <Typography variant="body1">
                          {systemStatus?.activeZones?.length > 0 ? 'Yes' : 'No'}
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <RainIcon color={systemStatus?.rainSensor ? "primary" : "disabled"} sx={{ mr: 1 }} />
                          <Typography variant="body1">Rain Sensor</Typography>
                        </Box>
                        <Typography variant="body1">
                          {systemStatus?.rainSensor ? 'Active' : 'Inactive'}
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TimerIcon color={systemStatus?.rainDelay > 0 ? "primary" : "disabled"} sx={{ mr: 1 }} />
                          <Typography variant="body1">Rain Delay</Typography>
                        </Box>
                        <Typography variant="body1">
                          {systemStatus?.rainDelay > 0 ? `${systemStatus.rainDelay} hours` : 'None'}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  {systemStatus?.activeZones?.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Button
                        variant="contained"
                        color="secondary"
                        startIcon={<StopIcon />}
                        onClick={handleStopAllZones}
                        disabled={loading}
                      >
                        Stop All Zones
                      </Button>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Main Content */}
            <Grid item xs={12}>
              <Card>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs value={tabValue} onChange={handleTabChange} aria-label="irrigation control tabs">
                    <Tab label="Zones" />
                    <Tab label="Programs" />
                    <Tab label="Settings" />
                  </Tabs>
                </Box>

                {/* Zones Tab */}
                <TabPanel value={tabValue} index={0}>
                  <Grid container spacing={3}>
                    {zones.map((zone) => (
                      <Grid item xs={12} sm={6} md={4} key={zone.id}>
                        <Card>
                          <CardHeader 
                            title={zone.name} 
                            subheader={zone.active ? 'Running' : 'Idle'}
                            sx={{
                              backgroundColor: zone.active ? 'primary.light' : 'inherit',
                              color: zone.active ? 'primary.contrastText' : 'inherit'
                            }}
                          />
                          <CardContent>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" gutterBottom>
                                Duration (minutes):
                              </Typography>
                              <Slider
                                value={duration}
                                min={1}
                                max={60}
                                step={1}
                                marks={[
                                  { value: 1, label: '1m' },
                                  { value: 15, label: '15m' },
                                  { value: 30, label: '30m' },
                                  { value: 60, label: '60m' }
                                ]}
                                valueLabelDisplay="auto"
                                onChange={handleDurationChange}
                                disabled={loading || zone.active}
                              />
                            </Box>
                            
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Button
                                variant="contained"
                                color="primary"
                                startIcon={<PlayIcon />}
                                onClick={() => handleStartZone(zone.id)}
                                disabled={loading || zone.active}
                                sx={{ flex: 1, mr: 1 }}
                              >
                                Start
                              </Button>
                              <Button
                                variant="outlined"
                                color="secondary"
                                startIcon={<StopIcon />}
                                onClick={() => handleStopZone(zone.id)}
                                disabled={loading || !zone.active}
                                sx={{ flex: 1 }}
                              >
                                Stop
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                    
                    {zones.length === 0 && (
                      <Grid item xs={12}>
                        <Alert severity="info">
                          No irrigation zones found. Make sure your Rain Bird controller is properly connected and configured.
                        </Alert>
                      </Grid>
                    )}
                  </Grid>
                </TabPanel>

                {/* Programs Tab */}
                <TabPanel value={tabValue} index={1}>
                  <Grid container spacing={3}>
                    {programs.map((program) => (
                      <Grid item xs={12} sm={6} md={4} key={program.id}>
                        <Card>
                          <CardHeader 
                            title={program.name} 
                            subheader={program.enabled ? 'Enabled' : 'Disabled'}
                          />
                          <CardContent>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                              <Button
                                variant="contained"
                                color="primary"
                                startIcon={<PlayIcon />}
                                onClick={() => handleStartProgram(program.id)}
                                disabled={loading || systemStatus?.activeZones?.length > 0}
                                sx={{ flex: 1, mr: 1 }}
                              >
                                Run Now
                              </Button>
                              <Button
                                variant="outlined"
                                color="secondary"
                                startIcon={<StopIcon />}
                                onClick={() => handleStopProgram(program.id)}
                                disabled={loading || systemStatus?.activeZones?.length === 0}
                                sx={{ flex: 1 }}
                              >
                                Stop
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                    
                    {programs.length === 0 && (
                      <Grid item xs={12}>
                        <Alert severity="info">
                          No irrigation programs found. Programs are schedules configured on your Rain Bird controller.
                        </Alert>
                      </Grid>
                    )}
                  </Grid>
                </TabPanel>

                {/* Settings Tab */}
                <TabPanel value={tabValue} index={2}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardHeader title="Rain Delay" />
                        <CardContent>
                          <Typography variant="body2" gutterBottom>
                            Set a rain delay to pause irrigation for a specified number of hours:
                          </Typography>
                          
                          <Box sx={{ mt: 2 }}>
                            <FormControl fullWidth sx={{ mb: 2 }}>
                              <InputLabel id="rain-delay-label">Rain Delay Duration</InputLabel>
                              <Select
                                labelId="rain-delay-label"
                                value={systemStatus?.rainDelay || 0}
                                label="Rain Delay Duration"
                                onChange={(e) => handleSetRainDelay(e.target.value)}
                                disabled={loading}
                              >
                                <MenuItem value={0}>No Delay</MenuItem>
                                <MenuItem value={24}>24 Hours</MenuItem>
                                <MenuItem value={48}>48 Hours</MenuItem>
                                <MenuItem value={72}>72 Hours</MenuItem>
                                <MenuItem value={96}>96 Hours</MenuItem>
                                <MenuItem value={168}>1 Week</MenuItem>
                              </Select>
                            </FormControl>
                            
                            <Typography variant="body2">
                              Current Rain Delay: {systemStatus?.rainDelay > 0 ? `${systemStatus.rainDelay} hours` : 'None'}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardHeader title="System Information" />
                        <CardContent>
                          <List>
                            <ListItem>
                              <ListItemText primary="Controller Model" secondary={systemStatus?.model || 'Unknown'} />
                            </ListItem>
                            <ListItem>
                              <ListItemText primary="Firmware Version" secondary={systemStatus?.version || 'Unknown'} />
                            </ListItem>
                            <ListItem>
                              <ListItemText primary="Rain Sensor Status" secondary={systemStatus?.rainSensor ? 'Active' : 'Inactive'} />
                            </ListItem>
                            <ListItem>
                              <ListItemText primary="Last Updated" secondary={systemStatus?.timestamp ? new Date(systemStatus.timestamp).toLocaleString() : 'Unknown'} />
                            </ListItem>
                          </List>
                          
                          <Button
                            variant="outlined"
                            color="primary"
                            component={Link}
                            to="/rain-bird/setup"
                            sx={{ mt: 2 }}
                          >
                            Configure Rain Bird
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </TabPanel>
              </Card>
            </Grid>
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default RainBirdPage;