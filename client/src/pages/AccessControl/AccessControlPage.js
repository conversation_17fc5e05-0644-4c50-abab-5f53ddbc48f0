import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  Divider,
  Chip
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import DashboardIcon from '@mui/icons-material/Dashboard';
import PeopleIcon from '@mui/icons-material/People';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import ScheduleIcon from '@mui/icons-material/Schedule';
import EventIcon from '@mui/icons-material/Event';
import PolicyIcon from '@mui/icons-material/Policy';
import SettingsIcon from '@mui/icons-material/Settings';
import RefreshIcon from '@mui/icons-material/Refresh';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import SecurityIcon from '@mui/icons-material/Security';

import accessControlService from '../../services/accessControlService';
import { useAuth } from '../../context/AuthContext';

// Import components (to be created)
import AccessControlDashboard from '../../components/AccessControl/AccessControlDashboard';
import UserManagement from '../../components/AccessControl/UserManagement';
import DoorManagement from '../../components/AccessControl/DoorManagement';
import ScheduleManagement from '../../components/AccessControl/ScheduleManagement';
import HolidayManagement from '../../components/AccessControl/HolidayManagement';
import PolicyManagement from '../../components/AccessControl/PolicyManagement';
import AccessLevelManagement from '../../components/AccessControl/AccessLevelManagement';
import SystemSettings from '../../components/AccessControl/SystemSettings';

const AccessControlPage = () => {
  const { hasPermission } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState({
    unifiAccess: null,
    lenelS2NetBox: null
  });
  const [activeTab, setActiveTab] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch configuration status on component mount
  useEffect(() => {
    fetchConfigStatus();
  }, []);

  const fetchConfigStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      const config = await accessControlService.getConfigStatus();
      setConfigStatus(config);
    } catch (err) {
      console.error('Error fetching access control configuration status:', err);
      setError('Failed to load access control configuration status. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchConfigStatus();
    setRefreshing(false);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Check if at least one system is configured
  const isConfigured = configStatus.unifiAccess || configStatus.lenelS2NetBox;

  if (loading) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If no systems are configured, show configuration required message
  if (!isConfigured) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Access Control Management
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              No access control systems are configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the Access Control Management system, at least one of the following integrations needs to be configured:
            </Typography>
            <ul>
              <li>
                <Typography variant="body1">
                  <strong>Unifi Access</strong> - Configure environment variables for Unifi Access
                </Typography>
              </li>
              <li>
                <Typography variant="body1">
                  <strong>Lenel S2 NetBox</strong> - Configure environment variables for Lenel S2 NetBox
                </Typography>
              </li>
            </ul>
            <Typography variant="body1" paragraph>
              Please contact your system administrator to set up the required environment variables.
            </Typography>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Access Control Management
          </Typography>
          <Tooltip title="Refresh">
            <IconButton onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="access control tabs"
            >
              <Tab icon={<DashboardIcon />} label="Dashboard" />
              <Tab icon={<PeopleIcon />} label="Users" />
              <Tab icon={<MeetingRoomIcon />} label="Doors" />
              <Tab icon={<VpnKeyIcon />} label="Access Levels" />
              <Tab icon={<ScheduleIcon />} label="Schedules" />
              <Tab icon={<EventIcon />} label="Holidays" />
              <Tab icon={<PolicyIcon />} label="Policies" />
              <Tab icon={<SettingsIcon />} label="Settings" />
            </Tabs>
          </Box>
          <Box sx={{ p: 3 }}>
            {/* Dashboard Tab */}
            {activeTab === 0 && (
              <AccessControlDashboard 
                configStatus={configStatus}
                onRefresh={handleRefresh}
              />
            )}

            {/* Users Tab */}
            {activeTab === 1 && (
              <UserManagement 
                configStatus={configStatus}
              />
            )}

            {/* Doors Tab */}
            {activeTab === 2 && (
              <DoorManagement 
                configStatus={configStatus}
              />
            )}

            {/* Access Levels Tab */}
            {activeTab === 3 && (
              <AccessLevelManagement 
                configStatus={configStatus}
              />
            )}

            {/* Schedules Tab */}
            {activeTab === 4 && (
              <ScheduleManagement 
                configStatus={configStatus}
              />
            )}

            {/* Holidays Tab */}
            {activeTab === 5 && (
              <HolidayManagement 
                configStatus={configStatus}
              />
            )}

            {/* Policies Tab */}
            {activeTab === 6 && (
              <PolicyManagement 
                configStatus={configStatus}
              />
            )}

            {/* Settings Tab */}
            {activeTab === 7 && (
              <SystemSettings 
                configStatus={configStatus}
              />
            )}
          </Box>
        </Paper>

        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            System Status
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardHeader 
                  title="Unifi Access" 
                  avatar={<SecurityIcon color={configStatus.unifiAccess ? "primary" : "disabled"} />}
                />
                <CardContent>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    Status: {configStatus.unifiAccess ? (
                      <Chip label="Connected" color="success" size="small" />
                    ) : (
                      <Chip label="Not Configured" color="error" size="small" />
                    )}
                  </Typography>
                  {configStatus.unifiAccess && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Controller: {configStatus.unifiAccess.controller || 'N/A'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Version: {configStatus.unifiAccess.version || 'N/A'}
                      </Typography>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardHeader 
                  title="Lenel S2 NetBox" 
                  avatar={<SecurityIcon color={configStatus.lenelS2NetBox ? "primary" : "disabled"} />}
                />
                <CardContent>
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    Status: {configStatus.lenelS2NetBox ? (
                      <Chip label="Connected" color="success" size="small" />
                    ) : (
                      <Chip label="Not Configured" color="error" size="small" />
                    )}
                  </Typography>
                  {configStatus.lenelS2NetBox && (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Server: {configStatus.lenelS2NetBox.server || 'N/A'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Version: {configStatus.lenelS2NetBox.version || 'N/A'}
                      </Typography>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      </Box>
    </Container>
  );
};

export default AccessControlPage;