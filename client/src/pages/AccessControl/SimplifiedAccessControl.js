import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CardHeader,
  Button,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Door as DoorIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  History as HistoryIcon,
  AccessTime as TimeIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Notifications as AlertIcon,
  Shield as ShieldIcon,
  Badge as BadgeIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const SimplifiedAccessControl = () => {
  const { hasPermission, user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [grantAccessDialogOpen, setGrantAccessDialogOpen] = useState(false);
  const [selectedDoor, setSelectedDoor] = useState('');
  const [selectedUser, setSelectedUser] = useState('');

  // Mock data for access control
  const [doorStatus, setDoorStatus] = useState([
    { id: 1, name: 'Main Entrance', location: 'Front Lobby', status: 'secured', lastAccess: '2 minutes ago', user: 'John Smith', alerts: 0 },
    { id: 2, name: 'Server Room', location: '2nd Floor', status: 'secured', lastAccess: '1 hour ago', user: 'IT Admin', alerts: 0 },
    { id: 3, name: 'Conference Room A', location: '1st Floor', status: 'unlocked', lastAccess: '5 minutes ago', user: 'Jane Doe', alerts: 0 },
    { id: 4, name: 'Storage Room', location: 'Basement', status: 'secured', lastAccess: '3 hours ago', user: 'Maintenance', alerts: 1 },
    { id: 5, name: 'Executive Office', location: '3rd Floor', status: 'secured', lastAccess: '30 minutes ago', user: 'CEO', alerts: 0 },
    { id: 6, name: 'Emergency Exit', location: 'All Floors', status: 'monitoring', lastAccess: 'Never', user: 'System', alerts: 0 }
  ]);

  const [accessRequests, setAccessRequests] = useState([
    { id: 1, user: 'Mike Wilson', door: 'Server Room', requested: '10 minutes ago', reason: 'System maintenance', status: 'pending' },
    { id: 2, user: 'Sarah Johnson', door: 'Conference Room B', requested: '25 minutes ago', reason: 'Team meeting', status: 'approved' },
    { id: 3, user: 'Tom Brown', door: 'Storage Room', requested: '1 hour ago', reason: 'Inventory check', status: 'pending' }
  ]);

  const [recentActivity, setRecentActivity] = useState([
    { id: 1, user: 'John Smith', action: 'Access granted', door: 'Main Entrance', time: '2 minutes ago', status: 'success' },
    { id: 2, user: 'Jane Doe', action: 'Door unlocked', door: 'Conference Room A', time: '5 minutes ago', status: 'success' },
    { id: 3, user: 'System', action: 'Security alert', door: 'Storage Room', time: '15 minutes ago', status: 'warning' },
    { id: 4, user: 'IT Admin', action: 'Access granted', door: 'Server Room', time: '1 hour ago', status: 'success' },
    { id: 5, user: 'Security', action: 'Manual override', door: 'Emergency Exit', time: '2 hours ago', status: 'info' }
  ]);

  // Quick stats
  const stats = {
    totalDoors: doorStatus.length,
    securedDoors: doorStatus.filter(d => d.status === 'secured').length,
    unlockedDoors: doorStatus.filter(d => d.status === 'unlocked').length,
    activeAlerts: doorStatus.reduce((sum, d) => sum + d.alerts, 0),
    pendingRequests: accessRequests.filter(r => r.status === 'pending').length
  };

  const getDoorStatusColor = (status) => {
    switch (status) {
      case 'secured': return 'success';
      case 'unlocked': return 'warning';
      case 'monitoring': return 'info';
      default: return 'default';
    }
  };

  const getDoorStatusIcon = (status) => {
    switch (status) {
      case 'secured': return <LockIcon />;
      case 'unlocked': return <UnlockIcon />;
      case 'monitoring': return <SecurityIcon />;
      default: return <DoorIcon />;
    }
  };

  const getActivityStatusColor = (status) => {
    switch (status) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      case 'info': return 'info';
      default: return 'default';
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleDoorToggle = (doorId) => {
    setDoorStatus(prev => prev.map(door => 
      door.id === doorId 
        ? { ...door, status: door.status === 'secured' ? 'unlocked' : 'secured' }
        : door
    ));
  };

  const handleApproveRequest = (requestId) => {
    setAccessRequests(prev => prev.map(request => 
      request.id === requestId 
        ? { ...request, status: 'approved' }
        : request
    ));
  };

  const handleDenyRequest = (requestId) => {
    setAccessRequests(prev => prev.map(request => 
      request.id === requestId 
        ? { ...request, status: 'denied' }
        : request
    ));
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
          Access Control
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Monitor and manage building access, door security, and user permissions
        </Typography>
      </Box>

      {/* Status Overview */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="success.main">{stats.securedDoors}</Typography>
            <Typography variant="body2" color="text.secondary">Doors Secured</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="warning.main">{stats.unlockedDoors}</Typography>
            <Typography variant="body2" color="text.secondary">Doors Unlocked</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color={stats.activeAlerts > 0 ? 'error.main' : 'text.primary'}>
              {stats.activeAlerts}
            </Typography>
            <Typography variant="body2" color="text.secondary">Active Alerts</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="info.main">{stats.pendingRequests}</Typography>
            <Typography variant="body2" color="text.secondary">Pending Requests</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      {hasPermission('accessControl:write') && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
            Quick Actions
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setGrantAccessDialogOpen(true)}
            >
              Grant Access
            </Button>
            <Button
              variant="outlined"
              startIcon={<ShieldIcon />}
              onClick={() => {/* Handle emergency lockdown */}}
            >
              Emergency Lockdown
            </Button>
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => {/* Navigate to settings */}}
            >
              Access Settings
            </Button>
          </Box>
        </Box>
      )}

      {/* Main Content Tabs */}
      <Paper sx={{ mb: 4 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Door Status" icon={<DoorIcon />} iconPosition="start" />
          <Tab label="Access Requests" icon={<PersonIcon />} iconPosition="start" />
          <Tab label="Recent Activity" icon={<HistoryIcon />} iconPosition="start" />
        </Tabs>

        {/* Door Status Tab */}
        {activeTab === 0 && (
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {doorStatus.map((door) => (
                <Grid item xs={12} sm={6} md={4} key={door.id}>
                  <Card>
                    <CardHeader
                      avatar={
                        <Badge badgeContent={door.alerts} color="error" invisible={door.alerts === 0}>
                          <Avatar sx={{ bgcolor: getDoorStatusColor(door.status) + '.main' }}>
                            {getDoorStatusIcon(door.status)}
                          </Avatar>
                        </Badge>
                      }
                      action={
                        hasPermission('accessControl:write') && door.status !== 'monitoring' && (
                          <Tooltip title={door.status === 'secured' ? 'Unlock Door' : 'Secure Door'}>
                            <IconButton onClick={() => handleDoorToggle(door.id)}>
                              {door.status === 'secured' ? <UnlockIcon /> : <LockIcon />}
                            </IconButton>
                          </Tooltip>
                        )
                      }
                      title={door.name}
                      subheader={door.location}
                    />
                    <CardContent sx={{ pt: 0 }}>
                      <Box sx={{ mb: 2 }}>
                        <Chip
                          label={door.status.charAt(0).toUpperCase() + door.status.slice(1)}
                          color={getDoorStatusColor(door.status)}
                          size="small"
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Last Access: {door.lastAccess}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        By: {door.user}
                      </Typography>
                      {door.alerts > 0 && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                          {door.alerts} security alert(s) require attention
                        </Alert>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Access Requests Tab */}
        {activeTab === 1 && (
          <Box sx={{ p: 3 }}>
            <List>
              {accessRequests.map((request) => (
                <ListItem key={request.id} divider>
                  <ListItemIcon>
                    <Avatar>
                      <PersonIcon />
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1">{request.user}</Typography>
                        <Chip
                          label={request.status}
                          size="small"
                          color={
                            request.status === 'approved' ? 'success' :
                            request.status === 'denied' ? 'error' : 'warning'
                          }
                        />
                      </Box>
                    }
                    secondary={
                      <React.Fragment>
                        <Typography component="span" variant="body2" color="text.primary">
                          {request.door}
                        </Typography>
                        {` — ${request.reason} • ${request.requested}`}
                      </React.Fragment>
                    }
                  />
                  <ListItemSecondaryAction>
                    {request.status === 'pending' && hasPermission('accessControl:approve') && (
                      <Box>
                        <IconButton
                          edge="end"
                          color="success"
                          onClick={() => handleApproveRequest(request.id)}
                          sx={{ mr: 1 }}
                        >
                          <CheckIcon />
                        </IconButton>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => handleDenyRequest(request.id)}
                        >
                          <WarningIcon />
                        </IconButton>
                      </Box>
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
            {accessRequests.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography color="text.secondary">No access requests at this time</Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Recent Activity Tab */}
        {activeTab === 2 && (
          <Box sx={{ p: 3 }}>
            <List>
              {recentActivity.map((activity) => (
                <ListItem key={activity.id} divider>
                  <ListItemIcon>
                    <Chip
                      size="small"
                      color={getActivityStatusColor(activity.status)}
                      sx={{ width: 8, height: 8, minWidth: 'unset' }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={activity.action}
                    secondary={
                      <React.Fragment>
                        <Typography component="span" variant="body2" color="text.primary">
                          {activity.user} • {activity.door}
                        </Typography>
                        {` — ${activity.time}`}
                      </React.Fragment>
                    }
                  />
                </ListItem>
              ))}
            </List>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button variant="outlined">
                View Full Access Log
              </Button>
            </Box>
          </Box>
        )}
      </Paper>

      {/* Grant Access Dialog */}
      <Dialog open={grantAccessDialogOpen} onClose={() => setGrantAccessDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Grant Door Access</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Select Door</InputLabel>
              <Select
                value={selectedDoor}
                onChange={(e) => setSelectedDoor(e.target.value)}
                label="Select Door"
              >
                {doorStatus.map((door) => (
                  <MenuItem key={door.id} value={door.id}>
                    {door.name} - {door.location}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="User Name or ID"
              value={selectedUser}
              onChange={(e) => setSelectedUser(e.target.value)}
              sx={{ mb: 3 }}
            />
            <TextField
              fullWidth
              label="Reason for Access"
              multiline
              rows={3}
              placeholder="Enter reason for granting access..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setGrantAccessDialogOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => {
            // Handle grant access
            setGrantAccessDialogOpen(false);
            setSelectedDoor('');
            setSelectedUser('');
          }}>
            Grant Access
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default SimplifiedAccessControl;