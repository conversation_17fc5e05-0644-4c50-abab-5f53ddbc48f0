import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  TextField, 
  Button, 
  Paper, 
  Alert, 
  CircularProgress,
  Link,
  Divider
} from '@mui/material';
import { Save as SaveIcon, Check as CheckIcon } from '@mui/icons-material';
import googleFormsService from '../../services/googleFormsService';

/**
 * Google Forms Configuration Page
 * Allows administrators to configure the Google Forms integration
 */
const GoogleFormsConfigPage = () => {
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [redirectUri, setRedirectUri] = useState('');
  const [tokenPath, setTokenPath] = useState('./google-forms-token.json');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [authUrl, setAuthUrl] = useState('');
  const [usingEnvVars, setUsingEnvVars] = useState({
    clientId: false,
    clientSecret: false,
    redirectUri: false,
    tokenPath: false
  });

  // State for user authentication status
  const [userAuthStatus, setUserAuthStatus] = useState({
    userAuth: false,
    message: '',
    globalAuthUrl: ''
  });

  // Load existing configuration on component mount
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);

        // First check auth status which will tell us if user is already authenticated
        try {
          const authStatus = await googleFormsService.getAuthUrl();
          setUserAuthStatus(authStatus);

          // If there's a specific auth URL, set it
          if (authStatus.globalAuthUrl) {
            setAuthUrl(authStatus.globalAuthUrl);
          } else if (authStatus.authUrl) {
            setAuthUrl(authStatus.authUrl);
          }
        } catch (authErr) {
          console.error('Error getting auth status:', authErr);
          // Continue to load config even if auth status check fails
        }

        // Load global config if it exists
        const config = await googleFormsService.getConfig();

        if (config) {
          setClientId(config.clientId || '');
          setRedirectUri(config.redirectUri || '');
          setTokenPath(config.tokenPath || './google-forms-token.json');
          setConfigStatus(config);

          // Set environment variables usage information if available
          if (config.usingEnvVars) {
            setUsingEnvVars(config.usingEnvVars);
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading configuration:', err);
        setError('Failed to load Google Forms configuration');
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!clientId || !clientSecret || !redirectUri) {
      setError('Client ID, Client Secret, and Redirect URI are required');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      await googleFormsService.saveConfig({
        clientId,
        clientSecret,
        redirectUri,
        tokenPath
      });

      // Get the auth URL after saving config
      const authStatus = await googleFormsService.getAuthUrl();
      setUserAuthStatus(authStatus);

      // Set the appropriate auth URL
      if (authStatus.globalAuthUrl) {
        setAuthUrl(authStatus.globalAuthUrl);
      } else if (authStatus.authUrl) {
        setAuthUrl(authStatus.authUrl);
      }

      // Reload config status
      const config = await googleFormsService.getConfig();
      setConfigStatus(config);

      setSuccess('Google Forms configuration saved successfully');
      setClientSecret(''); // Clear client secret for security
      setSaving(false);
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save Google Forms configuration');
      setSaving(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Google Forms Configuration
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Configure the Google Forms integration by providing your Google API credentials.
          </Typography>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            {/* Show user authentication status if available */}
            {userAuthStatus.userAuth && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {userAuthStatus.message || 'You are authenticated with Google Forms using your Google account.'}
              </Alert>
            )}

            {/* Show global config status if available */}
            {configStatus && configStatus.isAuthenticated && (
              <Alert severity="success" sx={{ mb: 3 }}>
                Global Google Forms configuration is authenticated. Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
              </Alert>
            )}

            {configStatus && !configStatus.isAuthenticated && !userAuthStatus.userAuth && (
              <Alert severity="warning" sx={{ mb: 3 }}>
                Google Forms is configured but not authenticated. Please authenticate using the link below.
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit} sx={{ mb: 4 }}>
              <TextField
                fullWidth
                label="Client ID"
                value={clientId}
                onChange={(e) => setClientId(e.target.value)}
                margin="normal"
                required
                disabled={usingEnvVars.clientId}
                helperText={usingEnvVars.clientId 
                  ? "Client ID is provided via environment variable" 
                  : "Your Google API Client ID"}
              />

              <TextField
                fullWidth
                label="Client Secret"
                value={usingEnvVars.clientSecret ? '********' : clientSecret}
                onChange={(e) => setClientSecret(e.target.value)}
                margin="normal"
                required
                type="password"
                disabled={usingEnvVars.clientSecret}
                helperText={usingEnvVars.clientSecret 
                  ? "Client Secret is provided via environment variable" 
                  : "Your Google API Client Secret"}
              />

              <TextField
                fullWidth
                label="Redirect URI"
                value={redirectUri}
                onChange={(e) => setRedirectUri(e.target.value)}
                margin="normal"
                required
                disabled={usingEnvVars.redirectUri}
                helperText={usingEnvVars.redirectUri 
                  ? "Redirect URI is provided via environment variable" 
                  : "The redirect URI registered in your Google API project"}
              />

              <TextField
                fullWidth
                label="Token Path"
                value={tokenPath}
                onChange={(e) => setTokenPath(e.target.value)}
                margin="normal"
                disabled={usingEnvVars.tokenPath}
                helperText={usingEnvVars.tokenPath 
                  ? "Token Path is provided via environment variable" 
                  : "Path where the OAuth token will be stored (default: ./google-forms-token.json)"}
              />

              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={saving ? <CircularProgress size={24} /> : <SaveIcon />}
                disabled={saving}
                sx={{ mt: 3 }}
              >
                {saving ? 'Saving...' : 'Save Configuration'}
              </Button>
            </Box>

            <Divider sx={{ my: 4 }} />

            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Authentication
              </Typography>

              {userAuthStatus.userAuth ? (
                <>
                  <Alert severity="info" sx={{ mb: 3 }}>
                    You are already authenticated with Google Forms using your Google account.
                    No additional authentication is needed to use Google Forms.
                  </Alert>

                  <Typography variant="body1" paragraph>
                    Your Google account has the necessary permissions to access Google Forms.
                    You can start using Google Forms features right away.
                  </Typography>

                  {authUrl && (
                    <>
                      <Typography variant="body1" paragraph>
                        If you want to use a global configuration instead of your Google account,
                        you can authenticate the global configuration using the link below.
                      </Typography>
                      <Link 
                        href={authUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        sx={{ display: 'inline-block', mb: 2 }}
                      >
                        <Button variant="outlined" color="primary">
                          Authenticate Global Configuration
                        </Button>
                      </Link>
                    </>
                  )}
                </>
              ) : (
                <>
                  {userAuthStatus.message && (
                    <Alert severity="info" sx={{ mb: 3 }}>
                      {userAuthStatus.message}
                    </Alert>
                  )}

                  {userAuthStatus.authUrl ? (
                    <>
                      <Typography variant="body1" paragraph>
                        To use Google Forms, you need to authenticate with your Google account.
                        Please log out and log back in to grant the necessary permissions.
                      </Typography>
                      <Link 
                        href="/api/auth/logout" 
                        sx={{ display: 'inline-block', mb: 2 }}
                      >
                        <Button variant="outlined" color="primary">
                          Logout to Re-authenticate
                        </Button>
                      </Link>
                    </>
                  ) : authUrl ? (
                    <>
                      <Typography variant="body1" paragraph>
                        After saving your configuration, you need to authenticate with Google Forms.
                        Click the link below to authorize this application to access your Google Forms.
                      </Typography>
                      <Link 
                        href={authUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        sx={{ display: 'inline-block', mb: 2 }}
                      >
                        <Button variant="outlined" color="primary">
                          Authenticate with Google Forms
                        </Button>
                      </Link>
                      <Typography variant="body2" color="text.secondary">
                        Note: After authentication, you will be redirected to the configured redirect URI.
                        The page might show an error, but the authentication should be complete.
                        Refresh this page to check the authentication status.
                      </Typography>
                    </>
                  ) : (
                    <Typography variant="body1" paragraph>
                      Save your configuration first to generate an authentication link.
                    </Typography>
                  )}
                </>
              )}
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleFormsConfigPage;
