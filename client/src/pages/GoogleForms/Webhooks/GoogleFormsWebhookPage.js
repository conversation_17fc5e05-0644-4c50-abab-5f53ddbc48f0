import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Alert, 
  CircularProgress,
  Button,
  Grid,
  Divider,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { 
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as ProcessIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import googleFormsWebhookService from '../../../services/googleFormsWebhookService';
import WebhookForm from '../../../components/GoogleFormsWebhook/WebhookForm';
import moment from 'moment';

/**
 * Google Forms Webhook Page
 * Displays detailed information about a webhook and allows editing and processing
 */
const GoogleFormsWebhookPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  
  // State
  const [webhook, setWebhook] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  
  // Fetch webhook on component mount
  useEffect(() => {
    const fetchWebhook = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // If webhook is passed in location state, use it
        if (location.state && location.state.webhook) {
          setWebhook(location.state.webhook);
          setLoading(false);
          return;
        }
        
        // Otherwise fetch it from the API
        const webhookData = await googleFormsWebhookService.getWebhookById(id);
        setWebhook(webhookData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching webhook:', err);
        setError('Failed to load webhook. Please try again.');
        setLoading(false);
      }
    };
    
    fetchWebhook();
  }, [id, location.state]);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Handle edit button click
  const handleEditClick = () => {
    setEditMode(true);
  };
  
  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditMode(false);
  };
  
  // Handle save webhook
  const handleSaveWebhook = async (updatedWebhook) => {
    try {
      setWebhook(updatedWebhook);
      setEditMode(false);
    } catch (err) {
      console.error('Error saving webhook:', err);
      setError('Failed to save webhook. Please try again.');
    }
  };
  
  // Handle process webhook
  const handleProcessWebhook = async () => {
    try {
      setProcessing(true);
      setError(null);
      
      const result = await googleFormsWebhookService.processWebhook(webhook._id);
      
      // Refresh webhook data
      const updatedWebhook = await googleFormsWebhookService.getWebhookById(webhook._id);
      setWebhook(updatedWebhook);
      
      setProcessing(false);
    } catch (err) {
      console.error('Error processing webhook:', err);
      setError('Failed to process webhook. Please try again.');
      setProcessing(false);
    }
  };
  
  // Handle delete button click
  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };
  
  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      setDeleteLoading(true);
      
      await googleFormsWebhookService.deleteWebhook(webhook._id);
      
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
      
      // Navigate back to forms page
      navigate('/google-forms');
    } catch (err) {
      console.error('Error deleting webhook:', err);
      setError('Failed to delete webhook. Please try again.');
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
    }
  };
  
  // Handle back button click
  const handleBackClick = () => {
    navigate('/google-forms');
  };
  
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return moment(dateString).format('MMM D, YYYY h:mm A');
  };
  
  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBackClick}
            sx={{ mr: 2 }}
          >
            Back to Forms
          </Button>
          
          <Typography variant="h4" component="h1">
            {editMode ? 'Edit Webhook' : 'Webhook Details'}
          </Typography>
        </Box>
        
        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {/* Loading indicator */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
            <CircularProgress />
          </Box>
        ) : webhook ? (
          <>
            {editMode ? (
              <WebhookForm
                webhook={webhook}
                onSave={handleSaveWebhook}
                onCancel={handleCancelEdit}
              />
            ) : (
              <>
                {/* Webhook details */}
                <Box sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h5">
                      {webhook.formName}
                    </Typography>
                    
                    <Box>
                      <Tooltip title="Process Webhook">
                        <IconButton
                          color="primary"
                          onClick={handleProcessWebhook}
                          disabled={processing}
                          sx={{ mr: 1 }}
                        >
                          {processing ? <CircularProgress size={24} /> : <ProcessIcon />}
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Edit Webhook">
                        <IconButton
                          color="primary"
                          onClick={handleEditClick}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Delete Webhook">
                        <IconButton
                          color="error"
                          onClick={handleDeleteClick}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  
                  <Chip 
                    icon={webhook.active ? <ActiveIcon /> : <InactiveIcon />}
                    label={webhook.active ? 'Active' : 'Inactive'}
                    color={webhook.active ? 'success' : 'default'}
                    sx={{ mb: 2 }}
                  />
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1">Form ID</Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>{webhook.formId}</Typography>
                      
                      <Typography variant="subtitle1">Task Type</Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {webhook.taskType === 'maintenance' ? 'Maintenance Task' : 'General Task'}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle1">Created By</Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {webhook.createdBy ? `${webhook.createdBy.name} (${webhook.createdBy.email})` : 'Unknown'}
                      </Typography>
                      
                      <Typography variant="subtitle1">Last Checked</Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {formatDate(webhook.lastChecked)}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
                
                <Divider sx={{ mb: 3 }} />
                
                {/* Tabs for different sections */}
                <Box sx={{ width: '100%' }}>
                  <Tabs
                    value={tabValue}
                    onChange={handleTabChange}
                    sx={{ mb: 3 }}
                    variant="scrollable"
                    scrollButtons="auto"
                  >
                    <Tab label="Field Mappings" />
                    <Tab label="Assignment Rules" />
                    <Tab label="Processed Responses" />
                  </Tabs>
                  
                  {/* Field Mappings Tab */}
                  {tabValue === 0 && (
                    <Box>
                      <Typography variant="h6" gutterBottom>Field Mappings</Typography>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle1">Title Field</Typography>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {webhook.fieldMappings.titleField || 'Not set'}
                          </Typography>
                          
                          <Typography variant="subtitle1">Description Field</Typography>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {webhook.fieldMappings.descriptionField || 'Not set'}
                          </Typography>
                          
                          <Typography variant="subtitle1">Priority Field</Typography>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {webhook.fieldMappings.priorityField || 'Not set'}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle1">Due Date Field</Typography>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {webhook.fieldMappings.dueDateField || 'Not set'}
                          </Typography>
                          
                          <Typography variant="subtitle1">Tags Field</Typography>
                          <Typography variant="body1" sx={{ mb: 2 }}>
                            {webhook.fieldMappings.tagsField || 'Not set'}
                          </Typography>
                        </Grid>
                      </Grid>
                      
                      {webhook.fieldMappings.customFields && webhook.fieldMappings.customFields.length > 0 && (
                        <Box sx={{ mt: 3 }}>
                          <Typography variant="subtitle1" gutterBottom>Custom Field Mappings</Typography>
                          
                          <List>
                            {webhook.fieldMappings.customFields.map((field, index) => (
                              <ListItem key={index} divider>
                                <ListItemText
                                  primary={`Form Field: ${field.formField}`}
                                  secondary={`Task Field: ${field.taskField}`}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                    </Box>
                  )}
                  
                  {/* Assignment Rules Tab */}
                  {tabValue === 1 && (
                    <Box>
                      <Typography variant="h6" gutterBottom>Assignment Rules</Typography>
                      
                      {webhook.assignmentRules && webhook.assignmentRules.length > 0 ? (
                        <List>
                          {webhook.assignmentRules.map((rule, index) => (
                            <ListItem key={index} divider>
                              <ListItemText
                                primary={`Rule #${index + 1}: If ${rule.field} contains "${rule.value}"`}
                                secondary={`Assign to: ${rule.assignTo ? (rule.assignTo.name || rule.assignTo) : 'Not set'}`}
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body1" color="text.secondary">
                          No assignment rules configured.
                        </Typography>
                      )}
                      
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle1">Default Assignee</Typography>
                        <Typography variant="body1">
                          {webhook.defaultAssignee ? 
                            (webhook.defaultAssignee.name ? 
                              `${webhook.defaultAssignee.name} (${webhook.defaultAssignee.email})` : 
                              webhook.defaultAssignee) : 
                            'None (Tasks will be unassigned)'}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                  
                  {/* Processed Responses Tab */}
                  {tabValue === 2 && (
                    <Box>
                      <Typography variant="h6" gutterBottom>Processed Responses</Typography>
                      
                      {webhook.processedResponses && webhook.processedResponses.length > 0 ? (
                        <List>
                          {webhook.processedResponses.map((response, index) => (
                            <ListItem key={index} divider>
                              <ListItemText
                                primary={`Response ID: ${response.responseId}`}
                                secondary={
                                  <>
                                    <Typography variant="body2" component="span">
                                      Processed: {formatDate(response.processedAt)}
                                    </Typography>
                                    <br />
                                    <Typography variant="body2" component="span">
                                      Task ID: {response.taskId}
                                    </Typography>
                                  </>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body1" color="text.secondary">
                          No responses have been processed yet.
                        </Typography>
                      )}
                    </Box>
                  )}
                </Box>
              </>
            )}
          </>
        ) : (
          <Box sx={{ textAlign: 'center', my: 8 }}>
            <Typography variant="h6" color="text.secondary">
              Webhook not found
            </Typography>
          </Box>
        )}
      </Paper>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Webhook</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete this webhook for "{webhook?.formName}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDeleteDialogOpen(false)} 
            disabled={deleteLoading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={24} /> : null}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default GoogleFormsWebhookPage;