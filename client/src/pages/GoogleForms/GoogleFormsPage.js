import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Alert, 
  CircularProgress,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  ListItemSecondaryAction,
  Divider,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  InputAdornment,
  IconButton,
  Tabs,
  Tab,
  Chip,
  Link,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper as MuiPaper,
  Snackbar
} from '@mui/material';
import { 
  Settings as SettingsIcon,
  Description as FormIcon,
  Search as SearchIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon,
  Webhook as WebhookIcon,
  Visibility as VisibilityIcon,
  Shortcut as ShortcutIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import googleFormsService from '../../services/googleFormsService';
import googleFormsWebhookService from '../../services/googleFormsWebhookService';
import WebhookList from '../../components/GoogleFormsWebhook/WebhookList';
import WebhookForm from '../../components/GoogleFormsWebhook/WebhookForm';
import GoogleFormsViewer from '../../components/GoogleForms/GoogleFormsViewer';
import axios from 'axios';
import moment from 'moment';

/**
 * Google Forms Page
 * Displays forms from Google Forms
 */
const GoogleFormsPage = () => {
  const navigate = useNavigate();
  const [forms, setForms] = useState([]);
  const [selectedForm, setSelectedForm] = useState(null);
  const [formDetails, setFormDetails] = useState(null);
  const [formResponses, setFormResponses] = useState([]);
  const [webhooks, setWebhooks] = useState([]);
  const [loadingWebhooks, setLoadingWebhooks] = useState(false);
  const [webhookError, setWebhookError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [webhookDialogOpen, setWebhookDialogOpen] = useState(false);
  const [editingWebhook, setEditingWebhook] = useState(null);
  
  // State for response details dialog
  const [responseDialogOpen, setResponseDialogOpen] = useState(false);
  const [selectedResponse, setSelectedResponse] = useState(null);
  
  // State for embedded view
  const [showEmbeddedView, setShowEmbeddedView] = useState(false);
  
  // State for snackbar notifications
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Check configuration status and load forms
  useEffect(() => {
    const checkConfig = async () => {
      try {
        const config = await googleFormsService.getConfig();
        setConfigStatus(config);

        if (config && config.isAuthenticated) {
          await fetchForms();
        } else {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error checking configuration:', err);
        setError('Failed to check Google Forms configuration');
        setLoading(false);
      }
    };

    checkConfig();
  }, []);

  // State for storing the filter message
  const [filterMessage, setFilterMessage] = useState('');

  // Fetch forms from Google Forms
  const fetchForms = async () => {
    try {
      setLoading(true);
      const response = await googleFormsService.listForms();
      
      // Handle the new response format which includes a message and forms array
      if (response.forms) {
        setForms(response.forms);
        // Store the filter message if available
        if (response.message) {
          setFilterMessage(response.message);
        }
      } else {
        // Handle backward compatibility with old API response format
        setForms(response);
        setFilterMessage(''); // Clear filter message for old format
      }
      
      // Clear any previous errors
      setError(null);
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching forms:', err);
      setError('Failed to load forms from Google Forms');
      setFilterMessage(''); // Clear filter message on error
      setLoading(false);
    }
  };

  // Fetch form details
  const fetchFormDetails = async (formId) => {
    try {
      setLoadingDetails(true);
      const data = await googleFormsService.getForm(formId);
      setFormDetails(data);
      
      // Also fetch responses for this form
      try {
        const responses = await googleFormsService.getFormResponses(formId);
        setFormResponses(responses);
      } catch (responseErr) {
        console.error('Error fetching form responses:', responseErr);
        // Don't set error here, just log it
      }
      
      setLoadingDetails(false);
    } catch (err) {
      console.error('Error fetching form details:', err);
      setError('Failed to load form details from Google Forms');
      setLoadingDetails(false);
    }
  };

  // Fetch webhooks for a form
  const fetchWebhooks = async (formId) => {
    try {
      setLoadingWebhooks(true);
      setWebhookError(null);
      
      const webhookData = await googleFormsWebhookService.getWebhooksForForm(formId);
      setWebhooks(webhookData);
      
      setLoadingWebhooks(false);
    } catch (err) {
      console.error('Error fetching webhooks:', err);
      setWebhookError('Failed to load webhooks. Please try again.');
      setWebhooks([]);
      setLoadingWebhooks(false);
    }
  };

  // Handle form selection
  const handleFormSelect = async (form) => {
    setSelectedForm(form);
    await fetchFormDetails(form.id);
    await fetchWebhooks(form.id);
    // Reset embedded view when selecting a new form
    setShowEmbeddedView(false);
  };

  // Navigate to configuration page
  const handleGoToConfig = () => {
    navigate('/google-forms/config');
  };
  
  // Handle adding form to shortcuts
  const handleAddToShortcuts = async (form) => {
    try {
      // Create shortcut data
      const shortcutData = {
        title: form.title || selectedForm.name,
        description: `Google Form: ${form.title || selectedForm.name}`,
        url: form.formUrl || selectedForm.webViewLink,
        icon: 'description', // Material-UI icon name for forms
        categories: ['Google Forms'],
        instructions: 'Click to open this Google Form',
        isPublic: false
      };
      
      // Save as a private shortcut for the current user
      const response = await axios.post('/api/shortcuts/private', shortcutData);
      
      // Show success message
      setSnackbarMessage('Form added to shortcuts successfully');
      setSnackbarOpen(true);
    } catch (err) {
      console.error('Error adding form to shortcuts:', err);
      setSnackbarMessage('Failed to add form to shortcuts');
      setSnackbarOpen(true);
    }
  };
  
  // Toggle embedded view
  const handleToggleEmbeddedView = () => {
    setShowEmbeddedView(!showEmbeddedView);
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Format date
  const formatDate = (dateString) => {
    return moment(dateString).format('MMM D, YYYY h:mm A');
  };

  // Open form in new tab
  const openFormInNewTab = (url) => {
    window.open(url, '_blank');
  };
  
  // Handle create webhook button click
  const handleCreateWebhook = () => {
    setEditingWebhook(null);
    setWebhookDialogOpen(true);
  };
  
  // Handle edit webhook button click
  const handleEditWebhook = (webhook) => {
    setEditingWebhook(webhook);
    setWebhookDialogOpen(true);
  };
  
  // Handle webhook dialog close
  const handleWebhookDialogClose = () => {
    setWebhookDialogOpen(false);
    setEditingWebhook(null);
  };
  
  // Handle webhook save
  const handleWebhookSave = async (webhook) => {
    // Refresh webhooks after save
    await fetchWebhooks(selectedForm.id);
    setWebhookDialogOpen(false);
    setEditingWebhook(null);
  };
  
  // Handle opening the response details dialog
  const handleOpenResponseDialog = (response) => {
    setSelectedResponse(response);
    setResponseDialogOpen(true);
  };
  
  // Handle closing the response details dialog
  const handleCloseResponseDialog = () => {
    setResponseDialogOpen(false);
    setSelectedResponse(null);
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Google Forms
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and manage your Google Forms
          </Typography>
        </Box>

        {!configStatus || !configStatus.isAuthenticated ? (
          <Box sx={{ my: 4 }}>
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Google Forms Integration Status
              </Typography>
              <Typography variant="body1" paragraph>
                This page allows you to view and manage Google Forms that you have permission to access. 
                The forms are filtered based on your email address permissions.
              </Typography>
              <Typography variant="body1">
                {!configStatus ? 
                  "Checking authentication status..." : 
                  "The system is not properly authenticated with Google Forms. Your administrator needs to configure the Google Forms integration."}
              </Typography>
              {configStatus && configStatus.userAuth && !configStatus.userAuth.isAuthenticated && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  You need to authenticate with your Google account to access your forms.
                </Typography>
              )}
            </Alert>
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
              <CircularProgress />
            </Box>
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {/* Forms List */}
                <Grid item xs={12} md={4}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">
                      Your Forms
                    </Typography>
                    <Button
                      variant="outlined"
                      color="primary"
                      startIcon={<RefreshIcon />}
                      onClick={fetchForms}
                      disabled={loading}
                    >
                      Refresh
                    </Button>
                  </Box>
                  
                  {/* Display filter message if available */}
                  {filterMessage && (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      {filterMessage}
                    </Alert>
                  )}
                  
                  {forms.length === 0 ? (
                    <Box sx={{ textAlign: 'center', my: 4 }}>
                      <Typography variant="body1" color="text.secondary">
                        {filterMessage.includes('available to') 
                          ? 'No forms found that you have permission to access. Forms must be shared with your email address.'
                          : 'No forms found in your Google account'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {filterMessage.includes('available to') && 'To access a form, ask the form owner to share it with your email address.'}
                      </Typography>
                    </Box>
                  ) : (
                    <List>
                      {forms.map((form) => (
                        <ListItem 
                          key={form.id} 
                          disablePadding
                          divider
                        >
                          <ListItemButton 
                            selected={selectedForm && selectedForm.id === form.id}
                            onClick={() => handleFormSelect(form)}
                          >
                            <ListItemIcon>
                              <FormIcon />
                            </ListItemIcon>
                            <ListItemText 
                              primary={form.name} 
                              secondary={`Created: ${formatDate(form.createdTime)}`}
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Grid>

                {/* Form Details */}
                <Grid item xs={12} md={8}>
                  {selectedForm ? (
                    <>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">
                          {selectedForm.name}
                        </Typography>
                        <Box sx={{ 
                          display: 'flex', 
                          flexDirection: { xs: 'column', sm: 'row' },
                          gap: 1
                        }}>
                          <Button
                            variant="outlined"
                            color="primary"
                            startIcon={<ShortcutIcon />}
                            onClick={() => handleAddToShortcuts({ title: selectedForm.name, formUrl: selectedForm.webViewLink })}
                            size="small"
                          >
                            Add to Shortcuts
                          </Button>
                          <Button
                            variant="outlined"
                            color="primary"
                            startIcon={showEmbeddedView ? <OpenInNewIcon /> : <FormIcon />}
                            onClick={handleToggleEmbeddedView}
                            size="small"
                          >
                            {showEmbeddedView ? 'Show Details' : 'Embed Form'}
                          </Button>
                          <Button
                            variant="outlined"
                            color="primary"
                            startIcon={<OpenInNewIcon />}
                            onClick={() => openFormInNewTab(selectedForm.webViewLink)}
                            size="small"
                          >
                            Open in New Tab
                          </Button>
                        </Box>
                      </Box>

                      <Tabs 
                        value={tabValue} 
                        onChange={handleTabChange} 
                        sx={{ mb: 2 }}
                        variant="scrollable"
                        scrollButtons="auto"
                      >
                        <Tab label="Form Details" icon={<FormIcon />} iconPosition="start" />
                        <Tab label="Responses" icon={<ListItemIcon />} iconPosition="start" />
                        <Tab label="Webhooks" icon={<WebhookIcon />} iconPosition="start" />
                      </Tabs>

                      {loadingDetails ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                          <CircularProgress />
                        </Box>
                      ) : (
                        <>
                          {showEmbeddedView ? (
                            <GoogleFormsViewer
                              formUrl={selectedForm.webViewLink}
                              title={selectedForm.name}
                              fullWidth={true}
                              fullHeight={true}
                              onAddToShortcuts={handleAddToShortcuts}
                            />
                          ) : (
                            <>
                              {tabValue === 0 && (
                                <Card>
                                  <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                      {selectedForm.name}
                                    </Typography>
                                    
                                    {selectedForm.description && (
                                      <Typography variant="body1" paragraph>
                                        {selectedForm.description}
                                      </Typography>
                                    )}
                                    
                                    <Typography variant="body2" color="text.secondary">
                                      Created: {formatDate(selectedForm.createdTime)}
                                    </Typography>
                                    
                                    <Typography variant="body2" color="text.secondary">
                                      Last Modified: {formatDate(selectedForm.modifiedTime)}
                                    </Typography>
                                    
                                    <Box sx={{ mt: 3 }}>
                                      <Link 
                                        href={selectedForm.webViewLink} 
                                        target="_blank"
                                        rel="noopener noreferrer"
                                      >
                                        View Form
                                      </Link>
                                    </Box>
                                  </CardContent>
                                </Card>
                              )}
                            </>
                          )}
                          
                          {tabValue === 1 && (
                            <>
                              {formResponses && formResponses.responses && formResponses.responses.length > 0 ? (
                                <List>
                                  {formResponses.responses.map((response, index) => (
                                    <ListItem key={index} divider>
                                      <ListItemText
                                        primary={`Response #${index + 1}`}
                                        secondary={`Submitted: ${formatDate(response.createTime)}`}
                                      />
                                      <ListItemSecondaryAction>
                                        <IconButton 
                                          edge="end" 
                                          aria-label="view" 
                                          onClick={() => handleOpenResponseDialog(response)}
                                          color="primary"
                                        >
                                          <VisibilityIcon />
                                        </IconButton>
                                      </ListItemSecondaryAction>
                                    </ListItem>
                                  ))}
                                </List>
                              ) : (
                                <Box sx={{ textAlign: 'center', my: 4 }}>
                                  <Typography variant="body1" color="text.secondary">
                                    No responses found for this form
                                  </Typography>
                                </Box>
                              )}
                            </>
                          )}
                          
                          {tabValue === 2 && (
                            <>
                              {webhookError && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                  {webhookError}
                                </Alert>
                              )}
                              
                              {loadingWebhooks ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                                  <CircularProgress />
                                </Box>
                              ) : (
                                <WebhookList 
                                  formId={selectedForm.id}
                                  formName={selectedForm.name}
                                  onCreateWebhook={handleCreateWebhook}
                                  onEditWebhook={handleEditWebhook}
                                  embedded={true}
                                />
                              )}
                            </>
                          )}
                        </>
                      )}
                    </>
                  ) : (
                    <Box sx={{ textAlign: 'center', my: 8 }}>
                      <Typography variant="h6" color="text.secondary">
                        Select a form to view details
                      </Typography>
                    </Box>
                  )}
                </Grid>
              </Grid>
            )}
          </>
        )}
      </Paper>
      
      {/* Webhook Dialog */}
      <Dialog
        open={webhookDialogOpen}
        onClose={handleWebhookDialogClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingWebhook ? 'Edit Webhook' : 'Create Webhook'}
        </DialogTitle>
        <DialogContent>
          {selectedForm && (
            <WebhookForm
              formId={selectedForm.id}
              formName={selectedForm.name}
              webhook={editingWebhook}
              onSave={handleWebhookSave}
              onCancel={handleWebhookDialogClose}
            />
          )}
        </DialogContent>
      </Dialog>
      
      {/* Response Details Dialog */}
      <Dialog
        open={responseDialogOpen}
        onClose={handleCloseResponseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Response Details
        </DialogTitle>
        <DialogContent>
          {selectedResponse && (
            <>
              <DialogContentText sx={{ mb: 2 }}>
                Submitted: {formatDate(selectedResponse.createTime)}
              </DialogContentText>
              
              {selectedResponse.answers && Object.keys(selectedResponse.answers).length > 0 ? (
                <TableContainer component={MuiPaper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Question</strong></TableCell>
                        <TableCell><strong>Answer</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(selectedResponse.answers).map(([questionId, answer], index) => {
                        // Get the question text from formDetails if available
                        let questionText = `Question ${index + 1}`;
                        if (formDetails && formDetails.items) {
                          const question = formDetails.items.find(item => item.questionItem?.question?.questionId === questionId);
                          if (question && question.title) {
                            questionText = question.title;
                          }
                        }
                        
                        // Format the answer based on its type
                        let formattedAnswer = '';
                        if (answer.textAnswers) {
                          formattedAnswer = answer.textAnswers.answers.map(a => a.value).join(', ');
                        } else if (answer.fileUploadAnswers) {
                          formattedAnswer = answer.fileUploadAnswers.answers.map(a => a.fileName).join(', ');
                        } else if (answer.choiceAnswers) {
                          formattedAnswer = answer.choiceAnswers.answers.map(a => a.value).join(', ');
                        } else {
                          formattedAnswer = JSON.stringify(answer);
                        }
                        
                        return (
                          <TableRow key={questionId}>
                            <TableCell>{questionText}</TableCell>
                            <TableCell>{formattedAnswer}</TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  No answer details available for this response
                </Typography>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseResponseDialog}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />
    </Container>
  );
};

export default GoogleFormsPage;