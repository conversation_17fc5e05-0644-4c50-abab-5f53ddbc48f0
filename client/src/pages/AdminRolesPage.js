import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  IconButton,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormGroup,
  FormControlLabel,
  Radio,
  RadioGroup,
  Divider,
  Tabs,
  Tab,
  Checkbox
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import axios from 'axios';
import integrationService from '../services/integrationService';
import DefaultRoleSettings from '../components/roles/DefaultRoleSettings';

const AdminRolesPage = () => {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [availableIntegrations, setAvailableIntegrations] = useState([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: []
  });
  
  // Tab state
  const [activeTab, setActiveTab] = useState(0);
  
  // Store previous custom permissions when switching to administrator mode
  const [previousCustomPermissions, setPreviousCustomPermissions] = useState([]);

  // Platform management permissions
  const platformPermissions = [
    { id: 'users', name: 'User Management' },
    { id: 'roles', name: 'Role Management' },
    { id: 'shortcuts', name: 'Shortcuts' },
    { id: 'help', name: 'Help Content' }
  ];

  // Permission levels
  const permissionLevels = [
    { value: 'none', label: 'None' },
    { value: 'read', label: 'Read' },
    { value: 'write', label: 'Write' },
    { value: 'delete', label: 'Delete' },
    { value: 'admin', label: 'Admin' }
  ];

  useEffect(() => {
    fetchRoles();
    fetchIntegrations();
  }, []);
  
  // Fetch available integrations
  const fetchIntegrations = async () => {
    try {
      const integrations = await integrationService.getAvailableIntegrations();
      setAvailableIntegrations(integrations);
    } catch (err) {
      setError('Failed to fetch integrations');
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await axios.get('/api/roles');
      setRoles(response.data);
    } catch (err) {
      setError('Failed to fetch roles');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (role = null) => {
    if (role) {
      setEditingRole(role);
      const permissions = role.permissions || [];
      
      // If role has administrator permission, store other permissions if any
      if (permissions.includes('*')) {
        const customPermissions = permissions.filter(p => p !== '*');
        setPreviousCustomPermissions(customPermissions);
      } else {
        // Store current permissions as previous custom permissions
        setPreviousCustomPermissions(permissions);
      }
      
      setFormData({
        name: role.name,
        description: role.description || '',
        permissions: permissions
      });
    } else {
      setEditingRole(null);
      setFormData({
        name: '',
        description: '',
        permissions: []
      });
      // Reset previous custom permissions for new role
      setPreviousCustomPermissions([]);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingRole(null);
    setFormData({
      name: '',
      description: '',
      permissions: []
    });
    // Reset previous custom permissions when closing dialog
    setPreviousCustomPermissions([]);
  };
  
  // Check if a permission exists in the permissions array
  const hasPermission = (permissionName) => {
    return formData.permissions.includes(permissionName);
  };
  
  // Get the permission level for an integration
  const getPermissionLevel = (integration) => {
    if (hasPermission(`${integration}:admin`)) return 'admin';
    if (hasPermission(`${integration}:delete`)) return 'delete';
    if (hasPermission(`${integration}:write`)) return 'write';
    if (hasPermission(`${integration}:read`)) return 'read';
    return 'none';
  };
  
  // Handle permission level change
  const handlePermissionChange = (entity, level) => {
    // Remove any existing permissions for this entity
    const filteredPermissions = formData.permissions.filter(
      p => !p.startsWith(`${entity}:`)
    );
    
    // Add the new permission if it's not 'none'
    const newPermissions = level !== 'none' 
      ? [...filteredPermissions, `${entity}:${level}`]
      : filteredPermissions;
    
    setFormData({
      ...formData,
      permissions: newPermissions
    });
  };

  const handleSubmit = async () => {
    try {
      if (editingRole) {
        await axios.put(`/api/roles/${editingRole._id}`, formData);
        setSuccess('Role updated successfully');
      } else {
        await axios.post('/api/roles', formData);
        setSuccess('Role created successfully');
      }
      fetchRoles();
      handleCloseDialog();
    } catch (err) {
      setError(err.response?.data?.msg || 'Failed to save role');
    }
  };

  const handleDelete = async (roleId) => {
    if (window.confirm('Are you sure you want to delete this role?')) {
      try {
        await axios.delete(`/api/roles/${roleId}`);
        setSuccess('Role deleted successfully');
        fetchRoles();
      } catch (err) {
        setError(err.response?.data?.msg || 'Failed to delete role');
      }
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (loading) {
    return <Box sx={{ p: 3 }}>Loading...</Box>;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Role Management
      </Typography>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="role management tabs">
          <Tab label="Roles" icon={<EditIcon />} iconPosition="start" />
          <Tab label="Default Role Settings" icon={<SettingsIcon />} iconPosition="start" />
        </Tabs>
      </Box>
      
      {/* Roles Tab */}
      {activeTab === 0 && (
        <>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
            >
              Add Role
            </Button>
          </Box>
          
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Permissions</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {roles.map((role) => (
                  <TableRow key={role._id}>
                    <TableCell>{role.name}</TableCell>
                    <TableCell>{role.description}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {role.permissions?.includes('*') ? (
                          <Chip 
                            key="admin" 
                            label="Administrator (All Permissions)" 
                            size="small" 
                            color="primary"
                          />
                        ) : (
                          role.permissions?.map((permission, index) => {
                            // Parse the permission string
                            const [entity, level] = permission.split(':');
                            
                            // Determine color based on permission level
                            let color = 'default';
                            if (level === 'admin') color = 'error';
                            else if (level === 'write') color = 'warning';
                            else if (level === 'read') color = 'success';
                            
                            // Format the label
                            let label = permission;
                            if (entity && level) {
                              // Find platform permission name if it exists
                              const platformPerm = platformPermissions.find(p => p.id === entity);
                              const entityName = platformPerm ? platformPerm.name : entity;
                              label = `${entityName}: ${level.charAt(0).toUpperCase() + level.slice(1)}`;
                            }
                            
                            return (
                              <Chip 
                                key={index} 
                                label={label} 
                                size="small" 
                                color={color}
                              />
                            );
                          })
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => handleOpenDialog(role)}
                        color="primary"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        onClick={() => handleDelete(role._id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </>
      )}
      
      {/* Default Role Settings Tab */}
      {activeTab === 1 && (
        <DefaultRoleSettings roles={roles} />
      )}

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRole ? 'Edit Role' : 'Add New Role'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Role Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            sx={{ mb: 3 }}
          />
          
          <Typography variant="h6" gutterBottom>
            Permissions
          </Typography>
          
          {/* Special wildcard permission for admin */}
          <FormControlLabel
            control={
              <Checkbox
                checked={hasPermission('*')}
                onChange={(event) => {
                  if (!event.target.checked) {
                    // Switching from administrator to custom permissions
                    // Restore previous custom permissions if they exist, otherwise leave empty
                    setFormData({
                      ...formData,
                      permissions: previousCustomPermissions.length > 0 
                        ? previousCustomPermissions 
                        : formData.permissions.filter(p => p !== '*')
                    });
                  } else {
                    // Switching from custom permissions to administrator
                    // Save current custom permissions for later restoration
                    setPreviousCustomPermissions(formData.permissions);
                    setFormData({
                      ...formData,
                      permissions: ['*']
                    });
                  }
                }}
              />
            }
            label={
              <Box>
                <Typography variant="subtitle1">Administrator (All Permissions)</Typography>
                <Typography variant="body2" color="text.secondary">
                  Grants full access to all features and integrations
                </Typography>
              </Box>
            }
            sx={{ mb: 2, display: 'flex', alignItems: 'flex-start' }}
          />
          
          {!hasPermission('*') && (
            <>
              {/* Platform Management Permissions */}
              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1">Platform Management</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Feature</TableCell>
                          <TableCell>Permission Level</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {platformPermissions.map((platform) => (
                          <TableRow key={platform.id}>
                            <TableCell>{platform.name}</TableCell>
                            <TableCell>
                              <RadioGroup
                                row
                                value={getPermissionLevel(platform.id)}
                                onChange={(e) => handlePermissionChange(platform.id, e.target.value)}
                              >
                                {permissionLevels.map((level) => (
                                  <FormControlLabel
                                    key={level.value}
                                    value={level.value}
                                    control={<Radio size="small" />}
                                    label={level.label}
                                  />
                                ))}
                              </RadioGroup>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
              
              {/* Integrations Permissions */}
              <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1">Integrations</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Integration</TableCell>
                          <TableCell>Permission Level</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {availableIntegrations.map((integration) => (
                          <TableRow key={integration.id || integration}>
                            <TableCell>{integration.name || integration}</TableCell>
                            <TableCell>
                              <RadioGroup
                                row
                                value={getPermissionLevel(integration.id || integration)}
                                onChange={(e) => handlePermissionChange(integration.id || integration, e.target.value)}
                              >
                                {permissionLevels.map((level) => (
                                  <FormControlLabel
                                    key={level.value}
                                    value={level.value}
                                    control={<Radio size="small" />}
                                    label={level.label}
                                  />
                                ))}
                              </RadioGroup>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </AccordionDetails>
              </Accordion>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingRole ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
      >
        <Alert onClose={() => setError('')} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
      >
        <Alert onClose={() => setSuccess('')} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AdminRolesPage;
