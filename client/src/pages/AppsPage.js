import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  CardActionArea,
  TextField,
  InputAdornment,
  Avatar,
  Chip,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  Search as SearchIcon,
  Dashboard as DashboardIcon,
  Link as ShortcutIcon,
  Folder as FolderIcon,
  People as PeopleIcon,
  VpnKey as RoleIcon,
  Settings as SettingsIcon,
  Image as CanvaIcon,
  Computer as GLPIIcon,
  Event as PlanningCenterIcon,
  Storage as SynologyIcon,
  AcUnit as DreoIcon,
  Security as SecurityIcon,
  PhoneIphone as MosyleBusinessIcon,
  Lock as UnifiAccessIcon,
  Router as UnifiNetworkIcon,
  Videocam as UnifiProtectIcon,
  AdminPanelSettings as AdminIcon,
  ContactPage as StaffDirectoryIcon,
  CalendarMonth as GoogleCalendarIcon,
  Description as GoogleFormsIcon,
  Person as PersonIcon,
  HelpOutline as HelpIcon,
  QuestionAnswer as FAQIcon,
  NetworkCheck as RadiusIcon,
  MeetingRoom as RoomBookingIcon,
  Assignment as TaskManagementIcon,
  Build as MaintenanceSchedulingIcon,
  HomeWork as BuildingManagementIcon,
  Apple as AppleIcon,
  MusicNote as WiimIcon,
  Thermostat as SkyportCloudIcon,
  Speaker as QsysIcon,
  Lightbulb as ColoritIcon,
  Tv as ZeeVeeIcon,
  ConfirmationNumber as TicketIcon,
  Category as CategoryIcon,
  LocalOffer as TagIcon,
  Menu as MenuIcon,
  Apps as AppsIcon
} from '@mui/icons-material';
import * as menuItemService from '../services/menuItemService';
import * as menuCategoryService from '../services/menuCategoryService';
import { useAuth } from '../context/AuthContext';

const AppsPage = () => {
  const { hasPermission } = useAuth();
  const [menuItems, setMenuItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);

  // Fetch menu items and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [itemsResponse, categoriesResponse] = await Promise.all([
          menuItemService.getMenuItems(),
          menuCategoryService.getCategories()
        ]);
        
        setMenuItems(itemsResponse);
        setCategories(categoriesResponse);
        setFilteredItems(itemsResponse);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load apps');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter items based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredItems(menuItems);
      return;
    }

    const term = searchTerm.toLowerCase();
    const filtered = menuItems.filter(item => {
      const title = (item.friendlyName || item.title || '').toLowerCase();
      const description = (item.description || '').toLowerCase();
      const categories = (item.categories || []).join(' ').toLowerCase();
      
      return title.includes(term) || description.includes(term) || categories.includes(term);
    });
    
    setFilteredItems(filtered);
  }, [searchTerm, menuItems]);

  // Helper function to render icon based on icon name or use custom icon
  const renderIcon = (item) => {
    if (item.customIcon) {
      return <Avatar src={item.customIcon} sx={{ width: 40, height: 40 }} />;
    }
    
    // If using database items, the icon is a string name
    if (typeof item.icon === 'string') {
      // Map icon names to Material-UI icons
      const iconMap = {
        'dashboard': <DashboardIcon fontSize="large" />,
        'link': <ShortcutIcon fontSize="large" />,
        'folder': <FolderIcon fontSize="large" />,
        'people': <PeopleIcon fontSize="large" />,
        'vpn_key': <RoleIcon fontSize="large" />,
        'settings': <SettingsIcon fontSize="large" />,
        'image': <CanvaIcon fontSize="large" />,
        'computer': <GLPIIcon fontSize="large" />,
        'event': <PlanningCenterIcon fontSize="large" />,
        'storage': <SynologyIcon fontSize="large" />,
        'ac_unit': <DreoIcon fontSize="large" />,
        'security': <SecurityIcon fontSize="large" />,
        'phone_iphone': <MosyleBusinessIcon fontSize="large" />,
        'lock': <UnifiAccessIcon fontSize="large" />,
        'router': <UnifiNetworkIcon fontSize="large" />,
        'videocam': <UnifiProtectIcon fontSize="large" />,
        'admin_panel_settings': <AdminIcon fontSize="large" />,
        'contact_page': <StaffDirectoryIcon fontSize="large" />,
        'calendar_month': <GoogleCalendarIcon fontSize="large" />,
        'description': <GoogleFormsIcon fontSize="large" />,
        'person': <PersonIcon fontSize="large" />,
        'help_outline': <HelpIcon fontSize="large" />,
        'question_answer': <FAQIcon fontSize="large" />,
        'network_check': <RadiusIcon fontSize="large" />,
        'meeting_room': <RoomBookingIcon fontSize="large" />,
        'assignment': <TaskManagementIcon fontSize="large" />,
        'build': <MaintenanceSchedulingIcon fontSize="large" />,
        'home_work': <BuildingManagementIcon fontSize="large" />,
        'apple': <AppleIcon fontSize="large" />,
        'music_note': <WiimIcon fontSize="large" />,
        'thermostat': <SkyportCloudIcon fontSize="large" />,
        'speaker': <QsysIcon fontSize="large" />,
        'lightbulb': <ColoritIcon fontSize="large" />,
        'tv': <ZeeVeeIcon fontSize="large" />,
        'confirmation_number': <TicketIcon fontSize="large" />,
        'category': <CategoryIcon fontSize="large" />,
        'local_offer': <TagIcon fontSize="large" />,
        'menu': <MenuIcon fontSize="large" />,
        'apps': <AppsIcon fontSize="large" />
      };
      
      return iconMap[item.icon] || <ShortcutIcon fontSize="large" />;
    }
    
    // If icon is not provided, use default
    return <ShortcutIcon fontSize="large" />;
  };

  // Group items by category
  const getItemsByCategory = () => {
    const itemsByCategory = {};
    
    // Initialize categories
    categories.forEach(category => {
      itemsByCategory[category.name] = [];
    });
    
    // Add "Uncategorized" for items without a category
    itemsByCategory['Uncategorized'] = [];
    
    // Group items by category
    filteredItems.forEach(item => {
      const itemPermission = item.requiredPermission || item.permission;
      
      // Skip items that the user doesn't have permission to see
      if (itemPermission && !hasPermission(itemPermission)) {
        return;
      }
      
      if (item.categories && item.categories.length > 0) {
        item.categories.forEach(categoryName => {
          if (itemsByCategory[categoryName]) {
            itemsByCategory[categoryName].push(item);
          } else {
            // If category doesn't exist yet, create it
            itemsByCategory[categoryName] = [item];
          }
        });
      } else {
        itemsByCategory['Uncategorized'].push(item);
      }
    });
    
    // Remove empty categories
    Object.keys(itemsByCategory).forEach(key => {
      if (itemsByCategory[key].length === 0) {
        delete itemsByCategory[key];
      }
    });
    
    return itemsByCategory;
  };

  // Get category color
  const getCategoryColor = (categoryName) => {
    const category = categories.find(cat => cat.name === categoryName);
    return category ? category.color : '#1976d2';
  };

  const itemsByCategory = getItemsByCategory();

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Apps
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Browse all available applications and tools
        </Typography>
        
        {/* Search bar */}
        <TextField
          fullWidth
          placeholder="Search apps..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ mt: 2, mb: 4 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 4 }}>{error}</Alert>
      ) : (
        Object.keys(itemsByCategory).length === 0 ? (
          <Alert severity="info">No apps found matching your search criteria.</Alert>
        ) : (
          Object.entries(itemsByCategory).map(([categoryName, items]) => (
            <Box key={categoryName} sx={{ mb: 6 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Chip 
                  label={categoryName} 
                  sx={{ 
                    backgroundColor: getCategoryColor(categoryName),
                    color: 'white',
                    fontWeight: 'bold',
                    mr: 2
                  }} 
                />
                <Divider sx={{ flexGrow: 1 }} />
              </Box>
              
              <Grid container spacing={3}>
                {items.map((item) => {
                  const itemText = item.friendlyName || item.title || item.text;
                  const itemPath = item.path;
                  const itemDescription = item.description || '';
                  
                  return (
                    <Grid item xs={12} sm={6} md={4} lg={3} key={itemPath}>
                      <Card sx={{ height: '100%' }}>
                        <CardActionArea 
                          component={Link} 
                          to={itemPath}
                          sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}
                        >
                          <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
                            {renderIcon(item)}
                          </Box>
                          <CardContent sx={{ flexGrow: 1, width: '100%', textAlign: 'center' }}>
                            <Typography variant="h6" component="div" gutterBottom>
                              {itemText}
                            </Typography>
                            {itemDescription && (
                              <Typography variant="body2" color="text.secondary">
                                {itemDescription}
                              </Typography>
                            )}
                          </CardContent>
                        </CardActionArea>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            </Box>
          ))
        )
      )}
    </Container>
  );
};

export default AppsPage;