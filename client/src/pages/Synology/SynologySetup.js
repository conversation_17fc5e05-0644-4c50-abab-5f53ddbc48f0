import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip,
  FormControlLabel,
  Switch,
  Divider
} from '@mui/material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import synologyService from '../../services/synologyService';

const SynologySetup = () => {
  const [formData, setFormData] = useState({
    host: '',
    port: '',
    username: '',
    password: '',
    secure: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [oneClickLoading, setOneClickLoading] = useState(false);
  const [oneClickSuccess, setOneClickSuccess] = useState(false);
  const [oneClickFormData, setOneClickFormData] = useState({
    host: '',
    port: ''
  });
  const [showOneClickOptions, setShowOneClickOptions] = useState(false);

  const { host, port, username, password, secure } = formData;
  const { host: oneClickHost, port: oneClickPort } = oneClickFormData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await synologyService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setFormData({ ...formData, [e.target.name]: value });
  };

  const onOneClickChange = e => {
    const value = e.target.value;
    setOneClickFormData({ ...oneClickFormData, [e.target.name]: value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await synologyService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await synologyService.getConfig();
      setConfigStatus(config);

      // Clear the form
      setFormData({
        host: '',
        port: '',
        username: '',
        password: '',
        secure: true
      });
    } catch (err) {
      setError('Failed to save Synology configuration. Please try again.');
      console.error('Error saving Synology configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOneClickSetup = async () => {
    setOneClickLoading(true);
    setError(null);
    setOneClickSuccess(false);
    setSuccess(false);

    try {
      // Only pass host and port if they are provided
      const options = {};
      if (oneClickHost) options.host = oneClickHost;
      if (oneClickPort) options.port = oneClickPort;

      const response = await synologyService.oneClickSetup(options);
      setOneClickSuccess(true);

      // Update the config status with the new configuration
      setConfigStatus({
        host: response.host,
        port: response.port,
        username: response.username,
        secure: response.secure,
        configuredAt: response.configuredAt
      });

      // Reset the one-click form data
      setOneClickFormData({
        host: '',
        port: ''
      });
      setShowOneClickOptions(false);
      setSuccess(true);
    } catch (err) {
      setError(`One-click setup failed: ${err.message}`);
      console.error('Error setting up Synology with one click:', err);
    } finally {
      setOneClickLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Synology Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Synology is configured with host: {configStatus.host}:{configStatus.port}
                    <br />
                    Username: {configStatus.username}
                    <br />
                    Secure connection: {configStatus.secure ? 'Yes' : 'No'}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Synology integration is not configured yet. Please provide your Synology credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              One-Click Setup
            </Typography>
            <Typography variant="body1" paragraph>
              Use our one-click setup to automatically configure Synology integration with generated credentials.
              This is the easiest way to get started.
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Button
                variant="contained"
                color={oneClickSuccess ? "success" : "primary"}
                onClick={handleOneClickSetup}
                disabled={oneClickLoading}
                startIcon={oneClickLoading ? <CircularProgress size={20} color="inherit" /> : <AutoFixHighIcon />}
                sx={{ mr: 2 }}
              >
                {oneClickLoading ? 'Setting up...' : oneClickSuccess ? 'Setup Successful' : 'One-Click Setup'}
              </Button>

              <Button
                variant="outlined"
                onClick={() => setShowOneClickOptions(!showOneClickOptions)}
                disabled={oneClickLoading}
              >
                {showOneClickOptions ? 'Hide Options' : 'Show Options'}
              </Button>
            </Box>

            {showOneClickOptions && (
              <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Optionally, you can specify your Synology host and port for the one-click setup.
                  If left empty, default values will be used.
                </Typography>
                <TextField
                  margin="normal"
                  fullWidth
                  id="oneClickHost"
                  label="Synology Host (Optional)"
                  name="host"
                  value={oneClickHost}
                  onChange={onOneClickChange}
                  placeholder="e.g., diskstation.local or *************"
                  size="small"
                  sx={{ mb: 2 }}
                />
                <TextField
                  margin="normal"
                  fullWidth
                  id="oneClickPort"
                  label="Synology Port (Optional)"
                  name="port"
                  value={oneClickPort}
                  onChange={onOneClickChange}
                  placeholder="e.g., 5000 for HTTP or 5001 for HTTPS"
                  size="small"
                />
              </Box>
            )}

            {oneClickSuccess && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Synology has been configured successfully with one-click setup!
              </Alert>
            )}
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Manual Setup
          </Typography>

          <Typography variant="body1" paragraph>
            To integrate with Synology, you need to provide your Synology DiskStation credentials.
          </Typography>

          <Typography variant="body1" paragraph>
            Enter the following information:
          </Typography>

          <ul>
            <li>
              <Typography variant="body1" paragraph>
                Host: The hostname or IP address of your Synology DiskStation
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Port: The port number (usually 5000 for HTTP or 5001 for HTTPS)
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Username: Your Synology account username
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Password: Your Synology account password
              </Typography>
            </li>
          </ul>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Synology configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="host"
              label="Host"
              name="host"
              value={host}
              onChange={onChange}
              autoFocus
              placeholder="e.g., diskstation.local or *************"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="port"
              label="Port"
              name="port"
              value={port}
              onChange={onChange}
              placeholder="e.g., 5000 for HTTP or 5001 for HTTPS"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              value={username}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              value={password}
              onChange={onChange}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={secure}
                  onChange={onChange}
                  name="secure"
                  color="primary"
                />
              }
              label="Use secure connection (HTTPS)"
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default SynologySetup;
