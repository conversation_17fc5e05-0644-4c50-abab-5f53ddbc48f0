import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Typography,
  Divider,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Card,
  CardContent,
  Button
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Home as HomeIcon,
  Work as WorkIcon,
  Person as PersonIcon,
  Cake as CakeIcon,
  Event as EventIcon,
  Group as GroupIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  Notes as NotesIcon,
  CloudCircle as CloudIcon
} from '@mui/icons-material';
import * as peopleService from '../../services/peopleService';

/**
 * Component for displaying detailed information about a person
 * Supports both local people and Planning Center people
 */
const PersonDetails = ({ personId, isPlanningCenterPerson, onClose }) => {
  const [person, setPerson] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch person details when the component mounts
  useEffect(() => {
    const fetchPersonDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        let personData;
        if (isPlanningCenterPerson) {
          // Fetch Planning Center person with additional data
          personData = await peopleService.getPlanningCenterPersonById(personId, {
            include: 'emails,phone_numbers,addresses,households'
          });
        } else {
          // Fetch local person
          personData = await peopleService.getPersonById(personId);
        }

        setPerson(personData);
      } catch (error) {
        console.error('Error fetching person details:', error);
        setError('Failed to load person details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (personId) {
      fetchPersonDetails();
    }
  }, [personId, isPlanningCenterPerson]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error">{error}</Alert>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button onClick={onClose}>Close</Button>
        </Box>
      </Box>
    );
  }

  // Render empty state
  if (!person) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info">No person details available.</Alert>
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button onClick={onClose}>Close</Button>
        </Box>
      </Box>
    );
  }

  // Extract contact details for Planning Center people
  const contactDetails = isPlanningCenterPerson ? person.contactDetails || {} : {};
  const emails = contactDetails.emails || [];
  const phoneNumbers = contactDetails.phoneNumbers || [];
  const addresses = contactDetails.addresses || [];
  const households = isPlanningCenterPerson ? person.households || [] : [];

  return (
    <Box sx={{ p: 2 }}>
      <Grid container spacing={3}>
        {/* Header with avatar and name */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              src={person.avatar || person.profileImage}
              sx={{ width: 64, height: 64, mr: 2 }}
            >
              {person.name ? person.name.charAt(0) : <PersonIcon />}
            </Avatar>
            <Box>
              <Typography variant="h5" component="h2">
                {person.name}
                {isPlanningCenterPerson && (
                  <CloudIcon color="primary" fontSize="small" sx={{ ml: 1 }} />
                )}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                ID: {isPlanningCenterPerson ? person.id : person._id}
              </Typography>
            </Box>
          </Box>
        </Grid>

        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Basic Information
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary={
                      isPlanningCenterPerson
                        ? (emails.find(email => email.primary)?.address || 
                           (emails.length > 0 ? emails[0].address : 'N/A'))
                        : person.email || 'N/A'
                    }
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <PhoneIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Phone"
                    secondary={
                      isPlanningCenterPerson
                        ? (phoneNumbers.find(phone => phone.primary)?.number || 
                           (phoneNumbers.length > 0 ? phoneNumbers[0].number : 'N/A'))
                        : person.phoneNumber || 'N/A'
                    }
                  />
                </ListItem>
                {isPlanningCenterPerson && (
                  <>
                    <ListItem>
                      <ListItemIcon>
                        <CakeIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Birthdate"
                        secondary={formatDate(person.birthdate)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <EventIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary="Anniversary"
                        secondary={formatDate(person.anniversary)}
                      />
                    </ListItem>
                  </>
                )}
                {!isPlanningCenterPerson && (
                  <ListItem>
                    <ListItemIcon>
                      <CakeIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Date of Birth"
                      secondary={formatDate(person.dateOfBirth)}
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Categories and Associations */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Categories
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                {person.categories && person.categories.length > 0 ? (
                  person.categories.map((category) => (
                    <Chip
                      key={category}
                      label={category}
                      size="small"
                      color={category === 'Planning Center' ? 'primary' : 'default'}
                    />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No categories assigned
                  </Typography>
                )}
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                Ministry Associations
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {person.ministryAssociations && person.ministryAssociations.length > 0 ? (
                  person.ministryAssociations.map((association) => (
                    <Chip key={association} label={association} size="small" />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No ministry associations
                  </Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Planning Center Contact Details */}
        {isPlanningCenterPerson && (
          <>
            {/* Email Addresses */}
            {emails.length > 0 && (
              <Grid item xs={12}>
                <Accordion defaultExpanded>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Email Addresses</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {emails.map((email) => (
                        <ListItem key={email.id}>
                          <ListItemIcon>
                            <EmailIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={email.address}
                            secondary={`${email.location}${email.primary ? ' (Primary)' : ''}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}

            {/* Phone Numbers */}
            {phoneNumbers.length > 0 && (
              <Grid item xs={12}>
                <Accordion defaultExpanded>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Phone Numbers</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {phoneNumbers.map((phone) => (
                        <ListItem key={phone.id}>
                          <ListItemIcon>
                            <PhoneIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={phone.number}
                            secondary={`${phone.location}${phone.primary ? ' (Primary)' : ''}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}

            {/* Addresses */}
            {addresses.length > 0 && (
              <Grid item xs={12}>
                <Accordion defaultExpanded>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Addresses</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {addresses.map((address) => (
                        <ListItem key={address.id}>
                          <ListItemIcon>
                            {address.location === 'Home' ? <HomeIcon /> : <WorkIcon />}
                          </ListItemIcon>
                          <ListItemText
                            primary={`${address.street || ''}`}
                            secondary={`${address.city || ''}, ${address.state || ''} ${address.zip || ''}${address.primary ? ' (Primary)' : ''}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}

            {/* Households */}
            {households.length > 0 && (
              <Grid item xs={12}>
                <Accordion defaultExpanded>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Households</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {households.map((household) => (
                        <ListItem key={household.id}>
                          <ListItemIcon>
                            <GroupIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={household.name}
                            secondary={`Primary Contact: ${household.primaryContactName || 'N/A'}, Members: ${household.memberCount || 0}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}
          </>
        )}

        {/* Local Person Additional Details */}
        {!isPlanningCenterPerson && (
          <>
            {/* Address Information */}
            {person.address && (
              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Address Information</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {person.address.street && (
                        <ListItem>
                          <ListItemIcon>
                            <HomeIcon />
                          </ListItemIcon>
                          <ListItemText primary="Street" secondary={person.address.street} />
                        </ListItem>
                      )}
                      {person.address.city && (
                        <ListItem>
                          <ListItemIcon>
                            <HomeIcon />
                          </ListItemIcon>
                          <ListItemText primary="City" secondary={person.address.city} />
                        </ListItem>
                      )}
                      {person.address.state && (
                        <ListItem>
                          <ListItemIcon>
                            <HomeIcon />
                          </ListItemIcon>
                          <ListItemText primary="State/Province" secondary={person.address.state} />
                        </ListItem>
                      )}
                      {person.address.zipCode && (
                        <ListItem>
                          <ListItemIcon>
                            <HomeIcon />
                          </ListItemIcon>
                          <ListItemText primary="Zip/Postal Code" secondary={person.address.zipCode} />
                        </ListItem>
                      )}
                      {person.address.country && (
                        <ListItem>
                          <ListItemIcon>
                            <HomeIcon />
                          </ListItemIcon>
                          <ListItemText primary="Country" secondary={person.address.country} />
                        </ListItem>
                      )}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}

            {/* Social Media */}
            {person.socialMedia && (
              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Social Media</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {person.socialMedia.facebook && (
                        <ListItem>
                          <ListItemIcon>
                            <FacebookIcon />
                          </ListItemIcon>
                          <ListItemText primary="Facebook" secondary={person.socialMedia.facebook} />
                        </ListItem>
                      )}
                      {person.socialMedia.twitter && (
                        <ListItem>
                          <ListItemIcon>
                            <TwitterIcon />
                          </ListItemIcon>
                          <ListItemText primary="Twitter" secondary={person.socialMedia.twitter} />
                        </ListItem>
                      )}
                      {person.socialMedia.instagram && (
                        <ListItem>
                          <ListItemIcon>
                            <InstagramIcon />
                          </ListItemIcon>
                          <ListItemText primary="Instagram" secondary={person.socialMedia.instagram} />
                        </ListItem>
                      )}
                      {person.socialMedia.linkedin && (
                        <ListItem>
                          <ListItemIcon>
                            <LinkedInIcon />
                          </ListItemIcon>
                          <ListItemText primary="LinkedIn" secondary={person.socialMedia.linkedin} />
                        </ListItem>
                      )}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}

            {/* Emergency Contact */}
            {person.emergencyContact && (
              <Grid item xs={12}>
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Emergency Contact</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <List dense>
                      {person.emergencyContact.name && (
                        <ListItem>
                          <ListItemIcon>
                            <PersonIcon />
                          </ListItemIcon>
                          <ListItemText primary="Contact Name" secondary={person.emergencyContact.name} />
                        </ListItem>
                      )}
                      {person.emergencyContact.relationship && (
                        <ListItem>
                          <ListItemIcon>
                            <PersonIcon />
                          </ListItemIcon>
                          <ListItemText primary="Relationship" secondary={person.emergencyContact.relationship} />
                        </ListItem>
                      )}
                      {person.emergencyContact.phoneNumber && (
                        <ListItem>
                          <ListItemIcon>
                            <PhoneIcon />
                          </ListItemIcon>
                          <ListItemText primary="Phone Number" secondary={person.emergencyContact.phoneNumber} />
                        </ListItem>
                      )}
                    </List>
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}
          </>
        )}

        {/* Notes */}
        {person.notes && (
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Notes</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body1">{person.notes}</Typography>
              </AccordionDetails>
            </Accordion>
          </Grid>
        )}

        {/* Close Button */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <Button onClick={onClose} variant="contained">
              Close
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PersonDetails;