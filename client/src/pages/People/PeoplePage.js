import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Divider,
  Switch,
  FormControlLabel,
  Tooltip,
  Pagination
} from '@mui/material';
import { 
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Person as PersonIcon,
  FilterList as FilterIcon,
  Cloud as CloudIcon,
  Visibility as VisibilityIcon,
  Email as EmailIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import * as peopleService from '../../services/peopleService';
import PersonForm from './PersonForm';
import PersonDetails from './PersonDetails';

const PeoplePage = () => {
  const { user } = useAuth();
  const [people, setPeople] = useState([]);
  const [planningCenterPeople, setPlanningCenterPeople] = useState([]);
  const [filteredPeople, setFilteredPeople] = useState([]);
  const [categories, setCategories] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [includePlanningCenter, setIncludePlanningCenter] = useState(true);
  const [planningCenterLoading, setPlanningCenterLoading] = useState(false);
  
  // State for person details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedPersonForDetails, setSelectedPersonForDetails] = useState(null);
  
  // State for Planning Center pagination
  // This manages the pagination state for Planning Center people
  // - currentPage: The current page being displayed
  // - totalPages: Total number of pages available
  // - totalCount: Total number of people in Planning Center
  // - perPage: Number of items to display per page
  const [pcPagination, setPcPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    perPage: 25
  });

  /**
   * Fetch people and categories
   * This effect is responsible for:
   * 1. Loading local people from the database
   * 2. Loading Planning Center people with pagination and search
   * 3. Combining the results
   * 4. Updating the UI state
   * 
   * It runs when:
   * - The Planning Center toggle changes
   * - The pagination parameters change (page, perPage)
   * - The search term changes
   */
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch local people and categories in parallel
        const [peopleData, categoriesData] = await Promise.all([
          peopleService.getAllPeople(),
          peopleService.getAllCategories()
        ]);
        setPeople(peopleData);

        // Initialize with local people
        let allPeople = [...peopleData];

        // Fetch Planning Center people if toggle is on
        if (includePlanningCenter) {
          setPlanningCenterLoading(true);
          try {
            // Pass pagination parameters and search term to the API
            // This enables server-side pagination and search
            const pcResponse = await peopleService.getPlanningCenterPeople({
              page: pcPagination.currentPage,
              per_page: pcPagination.perPage,
              search: searchTerm
            });
            
            // Extract people and pagination data from response
            // The response format is: { people: [...], pagination: {...}, meta: {...} }
            const { people: pcPeople, pagination, meta } = pcResponse;
            
            // Update Planning Center people state
            setPlanningCenterPeople(pcPeople || []);
            
            // Update pagination state with values from the response
            // We use both pagination and meta objects for compatibility
            setPcPagination({
              currentPage: pagination?.currentPage || meta?.current_page || 1,
              totalPages: pagination?.totalPages || meta?.total_pages || 1,
              totalCount: pagination?.totalCount || meta?.total_count || 0,
              perPage: pcPagination.perPage
            });

            // Add Planning Center people to the list
            allPeople = [...peopleData, ...(pcPeople || [])];
          } catch (pcErr) {
            console.error('Error fetching Planning Center people:', pcErr);
            // Don't set error here to avoid blocking the whole page
          } finally {
            setPlanningCenterLoading(false);
          }
        }

        // Update the filtered people list with all people (local + Planning Center)
        setFilteredPeople(allPeople);
        setCategories(categoriesData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load people directory');
        setLoading(false);
      }
    };

    fetchData();
  }, [includePlanningCenter, pcPagination.currentPage, pcPagination.perPage, searchTerm]);

  // Filter people based on search term and category
  useEffect(() => {
    const filterPeople = async () => {
      try {
        let filteredLocalPeople = [];

        if (!searchTerm && !selectedCategory) {
          filteredLocalPeople = people;
        } else {
          filteredLocalPeople = await peopleService.searchPeople(searchTerm, selectedCategory);
        }

        // Combine with Planning Center people if toggle is on
        if (includePlanningCenter) {
          // For Planning Center, we're using server-side filtering via the API
          // The planningCenterPeople state already contains the filtered results
          // based on the search term passed in the fetch function
          
          // We still need to filter by category client-side if a category is selected
          const filteredPCPeople = planningCenterPeople.filter(person => {
            const matchesCategory = !selectedCategory || 
              (person.categories && person.categories.includes(selectedCategory));

            return matchesCategory;
          });

          setFilteredPeople([...filteredLocalPeople, ...filteredPCPeople]);
        } else {
          setFilteredPeople(filteredLocalPeople);
        }
      } catch (err) {
        console.error('Error filtering people:', err);
        setError('Failed to filter people');
      }
    };

    filterPeople();
  }, [searchTerm, selectedCategory, people, planningCenterPeople, includePlanningCenter]);

  // Handle Planning Center toggle
  const handlePlanningCenterToggle = (event) => {
    setIncludePlanningCenter(event.target.checked);
  };

  /**
   * Handle search input changes
   * Updates the search term state and resets pagination to first page
   * This triggers a new fetch of Planning Center people with the search term
   * via the useEffect dependency on searchTerm
   */
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    
    // Reset to first page when search term changes
    // This ensures we start from the first page of search results
    if (includePlanningCenter) {
      setPcPagination(prev => ({
        ...prev,
        currentPage: 1
      }));
    }
  };

  // Handle category filter change
  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  /**
   * Handle Planning Center pagination change
   * Updates the current page in the pagination state
   * This triggers a new fetch of Planning Center people for the selected page
   * via the useEffect dependency on pcPagination.currentPage
   * 
   * @param {Object} event - The event object (not used)
   * @param {number} page - The new page number to display
   */
  const handlePcPageChange = (event, page) => {
    setPcPagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  // Open form dialog for adding a new person
  const handleAddPerson = () => {
    setSelectedPerson(null);
    setFormDialogOpen(true);
  };

  // Open form dialog for editing a person
  const handleEditPerson = (person) => {
    setSelectedPerson(person);
    setFormDialogOpen(true);
  };

  // Close form dialog
  const handleCloseDialog = () => {
    setFormDialogOpen(false);
    setSelectedPerson(null);
  };

  // Open details dialog for viewing a person
  const handleViewPersonDetails = (person) => {
    setSelectedPersonForDetails(person);
    setDetailsDialogOpen(true);
  };

  // Close details dialog
  const handleCloseDetailsDialog = () => {
    setDetailsDialogOpen(false);
    setSelectedPersonForDetails(null);
  };

  // Save person (create or update)
  const handleSavePerson = async (personData) => {
    try {
      if (selectedPerson) {
        await peopleService.updatePerson(selectedPerson._id, personData);
      } else {
        await peopleService.createPerson(personData);
      }

      // Refresh people list
      const updatedPeople = await peopleService.getAllPeople();
      setPeople(updatedPeople);
      setFilteredPeople(updatedPeople);

      // Refresh categories
      const updatedCategories = await peopleService.getAllCategories();
      setCategories(updatedCategories);

      handleCloseDialog();
    } catch (err) {
      console.error('Error saving person:', err);
      setError('Failed to save person');
    }
  };

  // Delete person
  const handleDeletePerson = async (personId) => {
    if (window.confirm('Are you sure you want to delete this person?')) {
      try {
        await peopleService.deletePerson(personId);

        // Refresh people list
        const updatedPeople = await peopleService.getAllPeople();
        setPeople(updatedPeople);
        setFilteredPeople(updatedPeople);
      } catch (err) {
        console.error('Error deleting person:', err);
        setError('Failed to delete person');
      }
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          People Directory
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage ministry members, donors, students, and other people associated with the church
        </Typography>
      </Box>

      {/* Tabs for different views */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="people directory tabs">
          <Tab label="All People" />
          <Tab label="Donors" />
          <Tab label="Students" />
          <Tab label="Residents" />
        </Tabs>
      </Box>

      {/* Search and filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={5}>
            <TextField
              fullWidth
              placeholder="Search people..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel id="category-filter-label">Filter by Category</InputLabel>
              <Select
                labelId="category-filter-label"
                value={selectedCategory}
                onChange={handleCategoryChange}
                label="Filter by Category"
                startAdornment={
                  <InputAdornment position="start">
                    <FilterIcon />
                  </InputAdornment>
                }
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={includePlanningCenter}
                  onChange={handlePlanningCenterToggle}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CloudIcon sx={{ mr: 0.5 }} fontSize="small" />
                  <Typography variant="body2">Planning Center</Typography>
                  {planningCenterLoading && (
                    <CircularProgress size={16} sx={{ ml: 1 }} />
                  )}
                </Box>
              }
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddPerson}
            >
              Add Person
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* People table */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Categories</TableCell>
                  <TableCell>Ministry Associations</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredPeople.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      No people found. Add a new person to get started.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPeople.map((person) => (
                    <TableRow 
                      key={person._id}
                      sx={person.isPlanningCenterPerson ? { backgroundColor: 'rgba(144, 202, 249, 0.08)' } : {}}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {person.isPlanningCenterPerson && (
                            <Tooltip title="Planning Center">
                              <CloudIcon fontSize="small" color="primary" sx={{ mr: 1 }} />
                            </Tooltip>
                          )}
                          {person.name}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {person.isPlanningCenterPerson && person.contactDetails?.emails?.length > 0 ? (
                          <Box>
                            {person.contactDetails.emails.map((email, index) => (
                              <Typography key={email.id || index} variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                <EmailIcon fontSize="small" sx={{ mr: 0.5 }} />
                                {email.address}
                                {email.primary && (
                                  <Chip size="small" label="Primary" color="primary" sx={{ ml: 1, height: 16, fontSize: '0.6rem' }} />
                                )}
                              </Typography>
                            ))}
                          </Box>
                        ) : (
                          person.email || 'N/A'
                        )}
                      </TableCell>
                      <TableCell>
                        {person.isPlanningCenterPerson && person.contactDetails?.phoneNumbers?.length > 0 ? (
                          <Box>
                            {person.contactDetails.phoneNumbers.map((phone, index) => (
                              <Typography key={phone.id || index} variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                <PhoneIcon fontSize="small" sx={{ mr: 0.5 }} />
                                {phone.number}
                                {phone.primary && (
                                  <Chip size="small" label="Primary" color="primary" sx={{ ml: 1, height: 16, fontSize: '0.6rem' }} />
                                )}
                              </Typography>
                            ))}
                          </Box>
                        ) : (
                          person.phoneNumber || 'N/A'
                        )}
                      </TableCell>
                      <TableCell>
                        {person.categories?.map((category) => (
                          <Chip 
                            key={category} 
                            label={category} 
                            size="small" 
                            color={category === 'Planning Center' ? 'primary' : 'default'}
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                      </TableCell>
                      <TableCell>
                        {person.ministryAssociations?.map((association) => (
                          <Chip 
                            key={association} 
                            label={association} 
                            size="small" 
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                      </TableCell>
                      <TableCell>
                        {/* View Details button - available for all people */}
                        <IconButton
                          onClick={() => handleViewPersonDetails(person)}
                          size="small"
                          color="primary"
                          title="View Details"
                        >
                          <VisibilityIcon />
                        </IconButton>
                        
                        {person.isPlanningCenterPerson ? (
                          <Tooltip title="Planning Center records cannot be edited directly">
                            <span>
                              <IconButton 
                                size="small"
                                disabled
                              >
                                <EditIcon />
                              </IconButton>
                              <IconButton 
                                size="small"
                                disabled
                              >
                                <DeleteIcon />
                              </IconButton>
                            </span>
                          </Tooltip>
                        ) : (
                          <>
                            <IconButton 
                              onClick={() => handleEditPerson(person)}
                              size="small"
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton 
                              onClick={() => handleDeletePerson(person._id)}
                              size="small"
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          {/* Planning Center Pagination */}
          {includePlanningCenter && pcPagination.totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 2, mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                <CloudIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Planning Center: {pcPagination.totalCount} people total
                </Typography>
              </Box>
              <Pagination 
                count={pcPagination.totalPages} 
                page={pcPagination.currentPage}
                onChange={handlePcPageChange}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
        </>
      )}

      {/* Person Form Dialog */}
      <Dialog 
        open={formDialogOpen} 
        onClose={handleCloseDialog} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          {selectedPerson ? 'Edit Person' : 'Add New Person'}
        </DialogTitle>
        <DialogContent>
          <PersonForm 
            person={selectedPerson} 
            onSave={handleSavePerson} 
            onCancel={handleCloseDialog}
            categories={categories}
          />
        </DialogContent>
      </Dialog>

      {/* Person Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleCloseDetailsDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Person Details
          {selectedPersonForDetails?.isPlanningCenterPerson && (
            <Chip 
              icon={<CloudIcon />} 
              label="Planning Center" 
              size="small" 
              color="primary" 
              sx={{ ml: 2 }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          {selectedPersonForDetails && (
            <PersonDetails
              personId={selectedPersonForDetails.isPlanningCenterPerson ? 
                selectedPersonForDetails.planningCenterId : 
                selectedPersonForDetails._id}
              isPlanningCenterPerson={!!selectedPersonForDetails.isPlanningCenterPerson}
              onClose={handleCloseDetailsDialog}
            />
          )}
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default PeoplePage;
