import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  Slider,
  TextField,
  Switch,
  FormControlLabel,
  IconButton
} from '@mui/material';
import { 
  Info as InfoIcon,
  Settings as SettingsIcon,
  Lightbulb as LightbulbIcon,
  BrightnessHigh as BrightnessIcon,
  ColorLens as ColorIcon,
  Thermostat as TemperatureIcon,
  Save as SaveIcon,
  PowerSettingsNew as PowerIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { SketchPicker } from 'react-color';
import goveeService from '../../services/goveeService';
import { useAuth } from '../../context/AuthContext';
import { PermissionCheck } from '../../components/PermissionCheck';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`govee-tabpanel-${index}`}
      aria-labelledby={`govee-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const GoveePage = () => {
  const { hasPermission } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [deviceState, setDeviceState] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [configForm, setConfigForm] = useState({
    apiKey: ''
  });
  const [colorPickerOpen, setColorPickerOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState({ r: 255, g: 255, b: 255 });
  const [brightness, setBrightness] = useState(100);
  const [colorTemperature, setColorTemperature] = useState(5000);
  const [powerState, setPowerState] = useState(false);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await goveeService.getConfig();
        setConfigStatus(config);
        if (config && config.apiKey) {
          setConfigForm({
            apiKey: ''  // Don't show the actual API key for security
          });
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Initialize Govee API when configuration is loaded
  useEffect(() => {
    const initializeGovee = async () => {
      // Consider configured if configStatus exists (backend hides API key on purpose)
      if (!configStatus) return;

      try {
        await goveeService.initialize();
      } catch (err) {
        console.error('Error initializing Govee:', err);
        setError('Failed to initialize Govee. Please check your connection and try again.');
      }
    };

    initializeGovee();
  }, [configStatus]);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      // configured when config is available
      if (!configStatus) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Devices
            const devicesData = await goveeService.getDevices();
            setDevices(devicesData);
            break;
          case 1: // Device Control
            if (selectedDevice) {
              const stateData = await goveeService.getDeviceState(selectedDevice.device, selectedDevice.model);
              setDeviceState(stateData);
              
              // Update UI controls based on device state
              if (stateData) {
                setPowerState(stateData.powerState === 'on');
                if (stateData.brightness) {
                  setBrightness(stateData.brightness);
                }
                if (stateData.color) {
                  setSelectedColor(stateData.color);
                }
                if (stateData.colorTemperature) {
                  setColorTemperature(stateData.colorTemperature);
                }
              }
            }
            break;
          case 2: // Settings
            // No data to fetch for settings tab
            break;
          default:
            break;
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please check your connection and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus, selectedDevice]);

  // Handle device selection
  const handleDeviceSelect = async (device) => {
    setSelectedDevice(device);
    setTabValue(1); // Switch to Device Control tab
  };

  // Handle power toggle
  const handlePowerToggle = async () => {
    if (!selectedDevice) return;

    setLoading(true);
    try {
      await goveeService.turnDevice(selectedDevice.device, selectedDevice.model, !powerState);
      setPowerState(!powerState);
      setError(null);
    } catch (err) {
      console.error('Error toggling power:', err);
      setError('Failed to toggle power. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle brightness change
  const handleBrightnessChange = (event, value) => {
    setBrightness(value);
  };

  // Apply brightness change
  const applyBrightnessChange = async () => {
    if (!selectedDevice) return;

    setLoading(true);
    try {
      await goveeService.setBrightness(selectedDevice.device, selectedDevice.model, brightness);
      setError(null);
    } catch (err) {
      console.error('Error setting brightness:', err);
      setError('Failed to set brightness. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle color change
  const handleColorChange = (color) => {
    setSelectedColor(color.rgb);
  };

  // Apply color change
  const applyColorChange = async () => {
    if (!selectedDevice) return;

    setLoading(true);
    try {
      await goveeService.setColor(selectedDevice.device, selectedDevice.model, selectedColor);
      setError(null);
      setColorPickerOpen(false);
    } catch (err) {
      console.error('Error setting color:', err);
      setError('Failed to set color. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle color temperature change
  const handleColorTemperatureChange = (event, value) => {
    setColorTemperature(value);
  };

  // Apply color temperature change
  const applyColorTemperatureChange = async () => {
    if (!selectedDevice) return;

    setLoading(true);
    try {
      await goveeService.setColorTemperature(selectedDevice.device, selectedDevice.model, colorTemperature);
      setError(null);
    } catch (err) {
      console.error('Error setting color temperature:', err);
      setError('Failed to set color temperature. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Refresh device state
  const refreshDeviceState = async () => {
    if (!selectedDevice) return;

    setLoading(true);
    try {
      const stateData = await goveeService.getDeviceState(selectedDevice.device, selectedDevice.model);
      setDeviceState(stateData);
      
      // Update UI controls based on device state
      if (stateData) {
        setPowerState(stateData.powerState === 'on');
        if (stateData.brightness) {
          setBrightness(stateData.brightness);
        }
        if (stateData.color) {
          setSelectedColor(stateData.color);
        }
        if (stateData.colorTemperature) {
          setColorTemperature(stateData.colorTemperature);
        }
      }
      
      setError(null);
    } catch (err) {
      console.error('Error refreshing device state:', err);
      setError('Failed to refresh device state. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle config form change
  const handleConfigFormChange = (e) => {
    const { name, value } = e.target;
    setConfigForm({
      ...configForm,
      [name]: value
    });
  };

  // Handle config save
  const handleConfigSave = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would save the API key to the server
      // For now, we'll just show a success message
      setError(null);
      alert('API key would be saved in a real implementation. Please update the .env file with your Govee API key.');
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save configuration. Please check your inputs and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Render configuration form
  const renderConfigForm = () => (
    <Box component="form" sx={{ mt: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="API Key"
            name="apiKey"
            type="password"
            value={configForm.apiKey}
            onChange={handleConfigFormChange}
            margin="normal"
            required
            helperText="Enter your Govee API key from the Govee Developer Portal"
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleConfigSave}
              disabled={loading}
              startIcon={<SaveIcon />}
            >
              Save Configuration
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  // Render devices list
  const renderDevices = () => {
    if (!devices || devices.length === 0) return <Typography>No devices available.</Typography>;

    return (
      <Grid container spacing={2}>
        {devices.map((device) => (
          <Grid item xs={12} sm={6} md={4} key={device.device}>
            <Card>
              <CardContent>
                <Typography variant="h6">
                  {device.deviceName || `Device ${device.device}`}
                </Typography>
                <Typography color="textSecondary" gutterBottom>
                  Model: {device.model}
                </Typography>
                <Typography color="textSecondary" gutterBottom>
                  ID: {device.device}
                </Typography>
                <Typography color="textSecondary" gutterBottom>
                  Controllable: {device.controllable ? 'Yes' : 'No'}
                </Typography>
                <Typography color="textSecondary" gutterBottom>
                  Retrievable: {device.retrievable ? 'Yes' : 'No'}
                </Typography>
              </CardContent>
              <CardActions>
                <Button 
                  size="small" 
                  color="primary"
                  onClick={() => handleDeviceSelect(device)}
                  startIcon={<LightbulbIcon />}
                >
                  Control Device
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render device control
  const renderDeviceControl = () => {
    if (!selectedDevice) return <Typography>Please select a device from the Devices tab.</Typography>;

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Controlling: {selectedDevice.deviceName || `Device ${selectedDevice.device}`}
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={refreshDeviceState}
            disabled={loading}
            startIcon={<RefreshIcon />}
            sx={{ mr: 2 }}
          >
            Refresh State
          </Button>
        </Box>

        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ mb: 3 }}>
          <FormControlLabel
            control={
              <Switch
                checked={powerState}
                onChange={handlePowerToggle}
                disabled={loading}
                color="primary"
              />
            }
            label={powerState ? "Turn Off" : "Turn On"}
          />
        </Box>

        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ mb: 3 }}>
          <Typography id="brightness-slider" gutterBottom>
            Brightness
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs>
              <Slider
                aria-labelledby="brightness-slider"
                value={brightness}
                onChange={handleBrightnessChange}
                min={0}
                max={100}
                marks={[
                  { value: 0, label: '0%' },
                  { value: 50, label: '50%' },
                  { value: 100, label: '100%' }
                ]}
                valueLabelDisplay="auto"
                disabled={loading || !powerState}
              />
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                onClick={applyBrightnessChange}
                disabled={loading || !powerState}
                startIcon={<BrightnessIcon />}
              >
                Apply
              </Button>
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ mb: 3 }}>
          <Typography gutterBottom>
            Color
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: 1,
                mr: 2,
                bgcolor: `rgb(${selectedColor.r}, ${selectedColor.g}, ${selectedColor.b})`,
                border: '1px solid #ccc',
                cursor: 'pointer'
              }}
              onClick={() => setColorPickerOpen(!colorPickerOpen)}
            />
            <Button
              variant="contained"
              color="primary"
              onClick={applyColorChange}
              disabled={loading || !powerState}
              startIcon={<ColorIcon />}
            >
              Apply Color
            </Button>
          </Box>
          {colorPickerOpen && (
            <Box sx={{ mt: 2, position: 'relative', zIndex: 1 }}>
              <SketchPicker
                color={selectedColor}
                onChange={handleColorChange}
                disableAlpha
              />
            </Box>
          )}
        </Box>

        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ mb: 3 }}>
          <Typography id="color-temperature-slider" gutterBottom>
            Color Temperature
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs>
              <Slider
                aria-labelledby="color-temperature-slider"
                value={colorTemperature}
                onChange={handleColorTemperatureChange}
                min={2000}
                max={9000}
                marks={[
                  { value: 2000, label: 'Warm' },
                  { value: 5500, label: 'Neutral' },
                  { value: 9000, label: 'Cool' }
                ]}
                valueLabelDisplay="auto"
                disabled={loading || !powerState}
              />
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                onClick={applyColorTemperatureChange}
                disabled={loading || !powerState}
                startIcon={<TemperatureIcon />}
              >
                Apply
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    );
  };

  // Wrap the entire component with PermissionCheck
  return (
    <PermissionCheck requiredPermission="govee:read">
      {/* If configuration is not set up, show instructions (no API key input on frontend) */}
      {!loadingConfig && !configStatus ? (
        <Container maxWidth="lg">
          <Typography variant="h4" component="h1" gutterBottom>
            Govee Smart Devices
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" component="h2" gutterBottom>
              Configuration Required
            </Typography>
            <Typography paragraph>
              The Govee integration now uses a server-side environment variable. Please set GOVEE_API_KEY in your server environment (e.g., .env or deployment secrets) and reload the page.
            </Typography>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
          </Paper>
        </Container>
      ) : (
        <Container maxWidth="lg">
          <Typography variant="h4" component="h1" gutterBottom>
            Govee Smart Devices
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Paper sx={{ width: '100%', mb: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab label="Devices" icon={<LightbulbIcon />} />
              <Tab label="Device Control" icon={<PowerIcon />} />
              <Tab label="Settings" icon={<SettingsIcon />} />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                renderDevices()
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              {loading && !selectedDevice ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                renderDeviceControl()
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Govee Configuration
              </Typography>
              <Typography paragraph>
                This integration uses the server-side GOVEE_API_KEY. For security, the key is not displayed in the UI.
              </Typography>
              {configStatus?.fromEnv ? (
                <Alert severity="success">Configuration detected from environment variables.</Alert>
              ) : (
                <Alert severity="warning">Configuration not detected. Set GOVEE_API_KEY on the server and reload.</Alert>
              )}
            </TabPanel>
          </Paper>
        </Container>
      )}
    </PermissionCheck>
  );
};

export default GoveePage;