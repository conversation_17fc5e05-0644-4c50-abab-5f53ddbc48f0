import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  Switch,
  FormControlLabel,
  Slider,
  IconButton
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import AcUnitIcon from '@mui/icons-material/AcUnit';
import DevicesIcon from '@mui/icons-material/Devices';
import SettingsIcon from '@mui/icons-material/Settings';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import dreoService from '../../services/dreoService';
import { useAuth } from '../../context/AuthContext';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dreo-tabpanel-${index}`}
      aria-labelledby={`dreo-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const DreoPage = () => {
  const { hasIntegration } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [deviceStatus, setDeviceStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  
  // Helper function to get the correct device ID (sn or id)
  const getDeviceId = (device) => {
    return device.sn || device.id;
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await dreoService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Devices
            const devicesData = await dreoService.getDevices();
            setDevices(devicesData);
            break;
          default:
            break;
        }
      } catch (err) {
        setError('Failed to load data from Dreo. Please check your connection and try again.');
        console.error('Error loading Dreo data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus]);

  // Handle device selection
  const handleDeviceSelect = async (device) => {
    setSelectedDevice(device);
    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(device);
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
      
      // Register for real-time updates for this device
      dreoService.registerStatusListener(deviceId, (updatedStatus) => {
        console.log('Received real-time update for device:', deviceId, updatedStatus);
        setDeviceStatus(updatedStatus);
      });
    } catch (err) {
      setError(`Failed to load status for device ${device.name}. Please try again.`);
      console.error('Error loading device status:', err);
    } finally {
      setLoadingStatus(false);
    }
  };
  
  // Cleanup listeners when component unmounts or when selected device changes
  useEffect(() => {
    return () => {
      if (selectedDevice) {
        const deviceId = getDeviceId(selectedDevice);
        dreoService.unregisterStatusListener(deviceId);
      }
    };
  }, [selectedDevice]);

  // Handle device power toggle
  const handlePowerToggle = async () => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'power',
        value: !deviceStatus.power
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle temperature change
  const handleTemperatureChange = async (event, newValue) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'temperature',
        value: newValue
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle fan speed change
  const handleFanSpeedChange = async (event, newValue) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'fan-speed',
        value: newValue
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle mode change
  const handleModeChange = async (mode) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'mode',
        value: mode
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };
  
  // Handle oscillation toggle
  const handleOscillationToggle = async (event) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'oscillation',
        value: event.target.checked
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle oscillation angle change
  const handleOscillationAngleChange = async (event, newValue) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'oscillation-angle',
        value: newValue
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle child lock toggle
  const handleChildLockToggle = async (event) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'child-lock',
        value: event.target.checked
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle light toggle
  const handleLightToggle = async (event) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'light',
        value: event.target.checked
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle brightness change
  const handleBrightnessChange = async (event, newValue) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'brightness',
        value: newValue
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // Handle temperature unit change
  const handleTemperatureUnitChange = async (unit) => {
    if (!selectedDevice || !deviceStatus) return;

    setLoadingStatus(true);
    setError(null);

    try {
      const deviceId = getDeviceId(selectedDevice);
      const command = {
        command: 'temperature-unit',
        value: unit
      };

      await dreoService.controlDevice(deviceId, command);

      // Refresh device status
      const status = await dreoService.getDeviceStatus(deviceId);
      setDeviceStatus(status);
    } catch (err) {
      setError(`Failed to control device ${selectedDevice.name}. Please try again.`);
      console.error('Error controlling device:', err);
    } finally {
      setLoadingStatus(false);
    }
  };

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead

  if (loadingConfig) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!configStatus) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Dreo Portable AC Unit
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning">
              Dreo integration is not configured. Please contact your administrator to set up the required environment variables.
            </Alert>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Dreo Portable AC Unit
          </Typography>
        </Box>

        <Paper sx={{ width: '100%', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            centered
          >
            <Tab label="Devices" />
          </Tabs>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TabPanel value={tabValue} index={0}>
                {devices.length > 0 ? (
                  <Box sx={{ display: 'flex' }}>
                    <Box sx={{ width: '30%', borderRight: 1, borderColor: 'divider', pr: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Your Devices
                      </Typography>
                      <List>
                        {devices.map((device) => (
                          <ListItem 
                            button 
                            key={getDeviceId(device)}
                            onClick={() => handleDeviceSelect(device)}
                            selected={selectedDevice && getDeviceId(selectedDevice) === getDeviceId(device)}
                          >
                            <ListItemIcon>
                              <AcUnitIcon />
                            </ListItemIcon>
                            <ListItemText 
                              primary={device.name} 
                              secondary={device.model} 
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                    <Box sx={{ width: '70%', pl: 2 }}>
                      {selectedDevice ? (
                        <>
                          <Typography variant="h6" gutterBottom>
                            {selectedDevice.name}
                          </Typography>
                          {loadingStatus ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                              <CircularProgress />
                            </Box>
                          ) : deviceStatus ? (
                            <Card>
                              <CardContent>
                                <Grid container spacing={2}>
                                  <Grid item xs={12}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                      <Typography variant="h6">
                                        Power
                                      </Typography>
                                      <IconButton 
                                        color={deviceStatus.power ? "primary" : "default"} 
                                        onClick={handlePowerToggle}
                                      >
                                        <PowerSettingsNewIcon />
                                      </IconButton>
                                    </Box>
                                    <Divider sx={{ my: 1 }} />
                                  </Grid>

                                  {deviceStatus.power && (
                                    <>
                                      {/* Device Information */}
                                      <Grid item xs={12}>
                                        <Typography variant="h6" gutterBottom>
                                          Device Information
                                        </Typography>
                                        <Box sx={{ mb: 2 }}>
                                          {/* Basic Device Info */}
                                          <Typography variant="body2">
                                            <strong>Model:</strong> {deviceStatus.model || (deviceStatus.deviceInfo && deviceStatus.deviceInfo.model) || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Brand:</strong> {deviceStatus.brand || (deviceStatus.deviceInfo && deviceStatus.deviceInfo.brand) || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Product Name:</strong> {deviceStatus.productName || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Device Name:</strong> {deviceStatus.deviceName || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Series Name:</strong> {deviceStatus.seriesName || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Room:</strong> {deviceStatus.roomName || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Family:</strong> {deviceStatus.familyName || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Device ID:</strong> {deviceStatus.deviceId || deviceStatus.sn || 'N/A'}
                                          </Typography>
                                          <Typography variant="body2">
                                            <strong>Product ID:</strong> {deviceStatus.productId || 'N/A'}
                                          </Typography>
                                        </Box>
                                        
                                        {/* Connectivity Information */}
                                        {deviceStatus.mainConf && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              Connectivity
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>WiFi:</strong> {deviceStatus.mainConf.isWifi ? 'Yes' : 'No'}
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>Bluetooth:</strong> {deviceStatus.mainConf.isBluetooth ? 'Yes' : 'No'}
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>Smart Features:</strong> {deviceStatus.mainConf.isSmart ? 'Yes' : 'No'}
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>Voice Control:</strong> {deviceStatus.mainConf.isVoiceControl ? 'Yes' : 'No'}
                                            </Typography>
                                          </Box>
                                        )}
                                        
                                        {/* Resources */}
                                        {deviceStatus.resourcesConf && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              Resources
                                            </Typography>
                                            {deviceStatus.resourcesConf.imageSmallSrc && (
                                              <Typography variant="body2">
                                                <strong>Device Image:</strong> <a href={deviceStatus.resourcesConf.imageSmallSrc} target="_blank" rel="noopener noreferrer">View</a>
                                              </Typography>
                                            )}
                                          </Box>
                                        )}
                                        
                                        {/* User Manuals */}
                                        {deviceStatus.userManuals && deviceStatus.userManuals.length > 0 && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              User Manuals
                                            </Typography>
                                            {deviceStatus.userManuals.map((manual, index) => (
                                              <Typography key={index} variant="body2">
                                                <a href={manual.url} target="_blank" rel="noopener noreferrer">
                                                  {manual.desc || `User Manual ${index + 1}`}
                                                </a> ({manual.lang || 'en'})
                                              </Typography>
                                            ))}
                                          </Box>
                                        )}
                                        
                                        {/* Services */}
                                        {deviceStatus.servicesConf && deviceStatus.servicesConf.length > 0 && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              Services
                                            </Typography>
                                            {deviceStatus.servicesConf.map((service, index) => (
                                              <Typography key={index} variant="body2">
                                                <strong>{service.key}:</strong> {service.value.startsWith('http') ? 
                                                  <a href={service.value} target="_blank" rel="noopener noreferrer">Link</a> : 
                                                  service.value}
                                              </Typography>
                                            ))}
                                          </Box>
                                        )}
                                        
                                        {/* Advanced Device Info (from deviceInfo if available) */}
                                        {deviceStatus.deviceInfo && !deviceStatus.model && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              Additional Information
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>Type:</strong> {deviceStatus.deviceInfo.type || 'N/A'}
                                            </Typography>
                                          </Box>
                                        )}
                                        
                                        {/* Device State */}
                                        {deviceStatus.state && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              Current State
                                            </Typography>
                                            {Object.entries(deviceStatus.state).map(([key, value]) => (
                                              <Typography key={key} variant="body2">
                                                <strong>{key}:</strong> {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                                              </Typography>
                                            )).slice(0, 10)} {/* Limit to first 10 state properties to avoid overwhelming the UI */}
                                            {Object.keys(deviceStatus.state).length > 10 && (
                                              <Typography variant="body2" color="text.secondary">
                                                ...and {Object.keys(deviceStatus.state).length - 10} more properties
                                              </Typography>
                                            )}
                                          </Box>
                                        )}
                                        
                                        {/* Add a collapsible section for controlsConf if needed */}
                                        {deviceStatus.controlsConf && Object.keys(deviceStatus.controlsConf).length > 0 && (
                                          <Box sx={{ mb: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                              Control Configuration
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>Template:</strong> {deviceStatus.controlsConf.template || 'N/A'}
                                            </Typography>
                                            <Typography variant="body2">
                                              <strong>Category:</strong> {deviceStatus.controlsConf.category || 'N/A'}
                                            </Typography>
                                          </Box>
                                        )}
                                        <Divider sx={{ my: 1 }} />
                                      </Grid>

                                      {/* Temperature Control */}
                                      <Grid item xs={12}>
                                        <Typography id="temperature-slider" gutterBottom>
                                          Temperature: {deviceStatus.temperature}°F
                                        </Typography>
                                        <Slider
                                          value={deviceStatus.temperature}
                                          onChange={handleTemperatureChange}
                                          aria-labelledby="temperature-slider"
                                          valueLabelDisplay="auto"
                                          step={1}
                                          marks
                                          min={60}
                                          max={86}
                                        />
                                      </Grid>

                                      {/* Fan Speed Control */}
                                      <Grid item xs={12}>
                                        <Typography id="fan-speed-slider" gutterBottom>
                                          Fan Speed: {deviceStatus.fanSpeed}
                                        </Typography>
                                        <Slider
                                          value={deviceStatus.fanSpeed}
                                          onChange={handleFanSpeedChange}
                                          aria-labelledby="fan-speed-slider"
                                          valueLabelDisplay="auto"
                                          step={1}
                                          marks
                                          min={1}
                                          max={deviceStatus.deviceInfo?.capabilities?.maxSpeed || 4}
                                        />
                                      </Grid>

                                      {/* Mode Control */}
                                      <Grid item xs={12}>
                                        <Typography gutterBottom>
                                          Mode
                                        </Typography>
                                        <Box sx={{ display: 'flex', gap: 1 }}>
                                          <Button 
                                            variant={deviceStatus.mode === 'cool' ? 'contained' : 'outlined'}
                                            onClick={() => handleModeChange('cool')}
                                          >
                                            Cool
                                          </Button>
                                          <Button 
                                            variant={deviceStatus.mode === 'fan' ? 'contained' : 'outlined'}
                                            onClick={() => handleModeChange('fan')}
                                          >
                                            Fan
                                          </Button>
                                          <Button 
                                            variant={deviceStatus.mode === 'dry' ? 'contained' : 'outlined'}
                                            onClick={() => handleModeChange('dry')}
                                          >
                                            Dry
                                          </Button>
                                        </Box>
                                      </Grid>

                                      {/* Oscillation Control - Only show if supported */}
                                      {deviceStatus.deviceInfo?.capabilities?.oscillation && (
                                        <Grid item xs={12} sx={{ mt: 2 }}>
                                          <Typography gutterBottom>
                                            Oscillation
                                          </Typography>
                                          <FormControlLabel
                                            control={
                                              <Switch
                                                checked={!!deviceStatus.oscillation}
                                                onChange={handleOscillationToggle}
                                                color="primary"
                                              />
                                            }
                                            label={deviceStatus.oscillation ? "On" : "Off"}
                                          />
                                          
                                          {/* Oscillation Angle - Only show if supported */}
                                          {deviceStatus.deviceInfo?.capabilities?.oscillationAngle && deviceStatus.oscillation && (
                                            <Box sx={{ mt: 1 }}>
                                              <Typography id="oscillation-angle-slider" gutterBottom>
                                                Oscillation Angle: {deviceStatus.oscangle || 60}°
                                              </Typography>
                                              <Slider
                                                value={deviceStatus.oscangle || 60}
                                                onChange={handleOscillationAngleChange}
                                                aria-labelledby="oscillation-angle-slider"
                                                valueLabelDisplay="auto"
                                                step={30}
                                                marks
                                                min={60}
                                                max={120}
                                              />
                                            </Box>
                                          )}
                                        </Grid>
                                      )}

                                      {/* Child Lock Control - Only show if supported */}
                                      {deviceStatus.deviceInfo?.capabilities?.childLock && (
                                        <Grid item xs={12} sx={{ mt: 2 }}>
                                          <Typography gutterBottom>
                                            Child Lock
                                          </Typography>
                                          <FormControlLabel
                                            control={
                                              <Switch
                                                checked={!!deviceStatus.childlockon}
                                                onChange={handleChildLockToggle}
                                                color="primary"
                                              />
                                            }
                                            label={deviceStatus.childlockon ? "Enabled" : "Disabled"}
                                          />
                                        </Grid>
                                      )}

                                      {/* Light Control - Only show if supported */}
                                      {deviceStatus.deviceInfo?.capabilities?.light && (
                                        <Grid item xs={12} sx={{ mt: 2 }}>
                                          <Typography gutterBottom>
                                            Light
                                          </Typography>
                                          <FormControlLabel
                                            control={
                                              <Switch
                                                checked={!!deviceStatus.lighton}
                                                onChange={handleLightToggle}
                                                color="primary"
                                              />
                                            }
                                            label={deviceStatus.lighton ? "On" : "Off"}
                                          />
                                          
                                          {deviceStatus.lighton && (
                                            <Box sx={{ mt: 1 }}>
                                              <Typography id="brightness-slider" gutterBottom>
                                                Brightness: {deviceStatus.brightness || 100}%
                                              </Typography>
                                              <Slider
                                                value={deviceStatus.brightness || 100}
                                                onChange={handleBrightnessChange}
                                                aria-labelledby="brightness-slider"
                                                valueLabelDisplay="auto"
                                                step={1}
                                                marks
                                                min={1}
                                                max={100}
                                              />
                                            </Box>
                                          )}
                                        </Grid>
                                      )}

                                      {/* Temperature Unit Control - Only show if supported */}
                                      {deviceStatus.deviceInfo?.capabilities?.temperatureUnit && (
                                        <Grid item xs={12} sx={{ mt: 2 }}>
                                          <Typography gutterBottom>
                                            Temperature Unit
                                          </Typography>
                                          <Box sx={{ display: 'flex', gap: 1 }}>
                                            <Button 
                                              variant={deviceStatus.tempunit === 1 ? 'contained' : 'outlined'}
                                              onClick={() => handleTemperatureUnitChange(1)}
                                            >
                                              Fahrenheit (°F)
                                            </Button>
                                            <Button 
                                              variant={deviceStatus.tempunit === 2 ? 'contained' : 'outlined'}
                                              onClick={() => handleTemperatureUnitChange(2)}
                                            >
                                              Celsius (°C)
                                            </Button>
                                          </Box>
                                        </Grid>
                                      )}
                                    </>
                                  )}
                                </Grid>
                              </CardContent>
                            </Card>
                          ) : (
                            <Typography>Select a device to view its status</Typography>
                          )}
                        </>
                      ) : (
                        <Typography>Select a device to view its status</Typography>
                      )}
                    </Box>
                  </Box>
                ) : (
                  <Alert severity="info">No devices found. Make sure your Dreo devices are set up in the Dreo app.</Alert>
                )}
              </TabPanel>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default DreoPage;
