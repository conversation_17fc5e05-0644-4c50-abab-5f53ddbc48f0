import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip,
  Link,
  Divider
} from '@mui/material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import dreoService from '../../services/dreoService';

const DreoSetup = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    apiKey: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [oneClickLoading, setOneClickLoading] = useState(false);
  const [oneClickSuccess, setOneClickSuccess] = useState(false);

  const { username, password, apiKey } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await dreoService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await dreoService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await dreoService.getConfig();
      setConfigStatus(config);

      // Clear the form
      setFormData({
        username: '',
        password: '',
        apiKey: ''
      });
    } catch (err) {
      setError('Failed to save Dreo configuration. Please try again.');
      console.error('Error saving Dreo configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOneClickSetup = async () => {
    setOneClickLoading(true);
    setError(null);
    setOneClickSuccess(false);

    try {
      const response = await dreoService.oneClickSetup();
      setOneClickSuccess(true);

      // Update the config status with the new configuration
      setConfigStatus({
        username: response.username,
        configuredAt: response.configuredAt
      });

      setSuccess(true);
    } catch (err) {
      setError(`One-click setup failed: ${err.message}`);
      console.error('Error setting up Dreo with one click:', err);
    } finally {
      setOneClickLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Dreo Portable AC Unit Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Dreo is configured with username: {configStatus.username}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Dreo integration is not configured yet. Please provide your API credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              One-Click Setup
            </Typography>
            <Typography variant="body1" paragraph>
              Use our one-click setup to automatically configure Dreo integration with generated credentials.
              This is the easiest way to get started.
            </Typography>
            <Button
              variant="contained"
              color={oneClickSuccess ? "success" : "primary"}
              onClick={handleOneClickSetup}
              disabled={oneClickLoading}
              startIcon={oneClickLoading ? <CircularProgress size={20} color="inherit" /> : <AutoFixHighIcon />}
              sx={{ mb: 2 }}
            >
              {oneClickLoading ? 'Setting up...' : oneClickSuccess ? 'Setup Successful' : 'One-Click Setup'}
            </Button>
            {oneClickSuccess && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Dreo has been configured successfully with one-click setup!
              </Alert>
            )}
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Manual Setup
          </Typography>

          <Typography variant="body1" paragraph>
            Alternatively, you can manually provide your Dreo account credentials and API key. 
          </Typography>

          <Typography variant="body1" paragraph>
            Follow these steps to get your API key:
          </Typography>

          <ol>
            <li>
              <Typography variant="body1" paragraph>
                Log in to your Dreo account
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Go to Account Settings → API Access
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Generate a new API key
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Copy the generated API key
              </Typography>
            </li>
          </ol>

          <Box sx={{ mt: 2, mb: 3 }}>
            <Typography variant="body1" paragraph>
              Click the button below to go directly to the Dreo API Access page:
            </Typography>
            <Link 
              href="https://api.dreo.com/account/api-access" 
              target="_blank" 
              rel="noopener noreferrer"
              sx={{ display: 'inline-block', mb: 2 }}
            >
              <Button variant="outlined" color="primary">
                Go to Dreo API Access
              </Button>
            </Link>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Dreo configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Dreo Username"
              name="username"
              value={username}
              onChange={onChange}
              autoFocus
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Dreo Password"
              type="password"
              id="password"
              value={password}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="apiKey"
              label="API Key"
              type="password"
              id="apiKey"
              value={apiKey}
              onChange={onChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default DreoSetup;
