import React, { useState, useEffect } from 'react';
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  Link,
  TextField,
  Typography,
  Alert,
  Paper
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import lgThinqService from '../../services/lgThinqService';

/**
 * LG ThinQ Setup Component
 * Allows users to configure the LG ThinQ integration
 */
const LGThinqSetup = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [oneClickSuccess, setOneClickSuccess] = useState(false);

  // Load current configuration status
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const config = await lgThinqService.getConfig();
        setConfigStatus(config);
        setLoading(false);
      } catch (err) {
        setLoading(false);
        // Not showing error for config fetch as it might not be configured yet
      }
    };

    fetchConfig();
  }, []);

  // Handle form input changes
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      await lgThinqService.saveConfig(formData);
      
      // Refresh config status
      const config = await lgThinqService.getConfig();
      setConfigStatus(config);
      
      setSuccess('LG ThinQ configuration saved successfully!');
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to save LG ThinQ configuration. Please try again.');
      console.error('Error saving LG ThinQ configuration:', err);
    }
  };

  // Handle one-click setup
  const handleOneClickSetup = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      setOneClickSuccess(false);

      const response = await lgThinqService.oneClickSetup();
      
      setConfigStatus({
        username: response.username,
        configuredAt: response.configuredAt
      });
      
      setOneClickSuccess(true);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to set up LG ThinQ with one click. Please try again or use manual setup.');
      console.error('Error setting up LG ThinQ with one click:', err);
    }
  };

  return (
    <Container maxWidth="md">
      <Helmet>
        <title>LG ThinQ AC Setup | CSF Staff Portal</title>
      </Helmet>

      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          LG ThinQ AC Unit Setup
        </Typography>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {configStatus && (
          <Alert severity="info" sx={{ mb: 3 }}>
            LG ThinQ is configured with username: {configStatus.username}
            <br />
            Last configured: {new Date(configStatus.configuredAt).toLocaleString()}
          </Alert>
        )}

        {!configStatus && !loading && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            LG ThinQ integration is not configured yet. Please provide your API credentials below.
          </Alert>
        )}

        <Card sx={{ mb: 4 }}>
          <CardHeader title="One-Click Setup" />
          <CardContent>
            <Typography variant="body1" paragraph>
              Use our one-click setup to automatically configure LG ThinQ integration with generated credentials.
              This is the easiest way to get started.
            </Typography>

            <Button
              variant="contained"
              color="primary"
              onClick={handleOneClickSetup}
              disabled={loading}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Set Up with One Click'}
            </Button>

            {oneClickSuccess && (
              <Alert severity="success" sx={{ mt: 3 }}>
                LG ThinQ has been configured successfully with one-click setup!
              </Alert>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="Manual Configuration" />
          <CardContent>
            <Typography variant="body1" paragraph>
              Alternatively, you can manually provide your LG ThinQ account credentials and API key.
            </Typography>

            <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.default', mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                How to get your credentials:
              </Typography>
              <ol>
                <Typography component="li">
                  Log in to your LG ThinQ account
                </Typography>
                <Typography component="li">
                  Navigate to your account settings
                </Typography>
                <Typography component="li">
                  Find the API access section
                </Typography>
                <Typography component="li">
                  Generate or copy your API credentials
                </Typography>
              </ol>

              <Typography variant="body2" sx={{ mt: 2 }}>
                Click the button below to go directly to the LG ThinQ API Access page:
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                component={Link}
                href="https://developers.thinq.com/docs"
                target="_blank"
                rel="noopener noreferrer"
                sx={{ mt: 1 }}
              >
                LG ThinQ Developer Portal
              </Button>
            </Paper>

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <TextField
                      label="Username"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      required
                    />
                    <FormHelperText>Enter your LG ThinQ username</FormHelperText>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <TextField
                      label="Password"
                      name="password"
                      type="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                    />
                    <FormHelperText>Enter your LG ThinQ password</FormHelperText>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default LGThinqSetup;