import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  Slider,
  Switch,
  Tab,
  Tabs,
  Typography,
  Alert,
  Tooltip
} from '@mui/material';
import { Helmet } from 'react-helmet-async';
import {
  AcUnit as AcIcon,
  Power as PowerIcon,
  Thermostat as ThermostatIcon,
  Speed as FanSpeedIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import lgThinqService from '../../services/lgThinqService';

// Tab Panel Component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`lg-thinq-tabpanel-${index}`}
      aria-labelledby={`lg-thinq-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

/**
 * LG ThinQ Page Component
 * Main interface for controlling LG ThinQ AC units
 */
const LGThinqPage = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [deviceStatus, setDeviceStatus] = useState(null);
  const [deviceDetails, setDeviceDetails] = useState(null);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);

  // Load configuration status and devices on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check if LG ThinQ is configured
        const config = await lgThinqService.getConfig();
        setConfigStatus(config);

        if (config) {
          try {
            // Fetch devices
            const response = await lgThinqService.getDevices();
            
            // Handle case where response is null or undefined
            if (!response) {
              console.warn('Received null or undefined response from LG ThinQ API');
              setDevices([]);
              setLoading(false);
              return;
            }
            
            // Process the API response to match the expected format
            // Handle case where response.response is null, undefined, or not an array
            const deviceList = Array.isArray(response.response) ? response.response : [];
            
            if (deviceList.length === 0) {
              console.info('No devices found in LG ThinQ API response');
            }
            
            const processedDevices = deviceList.map(device => ({
              id: device.deviceId,
              name: device.deviceInfo?.alias || 'Unnamed Device',
              type: device.deviceInfo?.deviceType || 'AC Unit',
              model: device.deviceInfo?.modelName || 'Unknown Model',
              online: device.deviceInfo?.reportable || false,
              // Keep the original data for reference
              _raw: device
            }));
            
            setDevices(processedDevices);

            // Select the first device by default if available
            if (processedDevices.length > 0) {
              setSelectedDevice(processedDevices[0]);
              await fetchDeviceStatus(processedDevices[0]);
            }
          } catch (deviceErr) {
            console.error('Error fetching devices from LG ThinQ API:', deviceErr);
            setError('Failed to fetch devices from LG ThinQ. Please check your connection and try again.');
            setDevices([]);
          }
        }

        setLoading(false);
      } catch (err) {
        setLoading(false);
        setError('Failed to load data from LG ThinQ. Please check your connection and try again.');
        console.error('Error loading LG ThinQ data:', err);
      }
    };

    fetchData();
  }, []);

  // Fetch device details (profile)
  const fetchDeviceDetails = async (device) => {
    if (!device || !device.id) {
      console.error('Invalid device object provided to fetchDeviceDetails');
      setError('Invalid device selected. Please try again.');
      return;
    }
    
    try {
      setLoading(true);
      const response = await lgThinqService.getDevice(device.id);
      
      // Handle case where response is null or undefined
      if (!response) {
        console.warn(`Received null or undefined details response for device ${device.id}`);
        setError(`Failed to fetch details for device ${device.name}. Received empty response.`);
        setLoading(false);
        return;
      }
      
      // Store the full device details
      setDeviceDetails(response);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to fetch details for device ${device.name || 'Unknown'}`);
      console.error(`Error fetching details for device ${device.id}:`, err);
    }
  };

  // Fetch device status (state)
  const fetchDeviceStatus = async (device) => {
    if (!device || !device.id) {
      console.error('Invalid device object provided to fetchDeviceStatus');
      setError('Invalid device selected. Please try again.');
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      const response = await lgThinqService.getDeviceStatus(device.id);
      
      // Handle case where response is null or undefined
      if (!response) {
        console.warn(`Received null or undefined status response for device ${device.id}`);
        setError(`Failed to fetch status for device ${device.name}. Received empty response.`);
        setLoading(false);
        return;
      }
      
      // Process the device status response
      // Extract power status from operation.airConOperationMode field
      // The field contains values like "POWER_ON" or "POWER_OFF"
      // Default to false if the operation field is missing or not "POWER_ON"
      const isPowerOn = response.operation &&
          response.operation.airConOperationMode === "POWER_ON";
      
      // Extract temperature from the response
      let currentTemp = 22;
      let targetTemp = 22;
      if (response.temperature) {
        if (typeof response.temperature.currentTemperature === 'number') {
          currentTemp = response.temperature.currentTemperature;
        }
        if (typeof response.temperature.targetTemperature === 'number') {
          targetTemp = response.temperature.targetTemperature;
        }
      }
      
      // Extract other status information
      const processedStatus = {
        power: isPowerOn,
        currentTemperature: currentTemp,
        temperature: targetTemp,
        fanSpeed: typeof response.fanSpeed === 'number' ? response.fanSpeed : 1,
        mode: response.airConJobMode?.currentJobMode?.toLowerCase() || 'cool',
        swingMode: typeof response.swingMode === 'string' ? response.swingMode : 'off',
        // Keep the raw response for reference
        _raw: response
      };
      
      setDeviceStatus(processedStatus);
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(`Failed to fetch status for device ${device.name || 'Unknown'}`);
      console.error(`Error fetching status for device ${device.id}:`, err);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle device selection
  const handleDeviceSelect = async (device) => {
    setSelectedDevice(device);
    
    // Fetch both device status and details
    await Promise.all([
      fetchDeviceStatus(device),
      fetchDeviceDetails(device)
    ]);
  };

  // Handle power toggle
  const handlePowerToggle = async () => {
    try {
      setLoading(true);
      const command = {
        command: 'power',
        value: !deviceStatus.power
      };

      await lgThinqService.controlDevice(selectedDevice.id, command);
      
      // Refresh device status
      await fetchDeviceStatus(selectedDevice);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to toggle power. Please try again.');
      console.error('Error toggling power:', err);
    }
  };

  // Handle temperature change
  const handleTemperatureChange = async (event, newValue) => {
    try {
      setLoading(true);
      const command = {
        command: 'temperature',
        value: newValue
      };

      await lgThinqService.controlDevice(selectedDevice.id, command);
      
      // Refresh device status
      await fetchDeviceStatus(selectedDevice);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to set temperature. Please try again.');
      console.error('Error setting temperature:', err);
    }
  };

  // Handle fan speed change
  const handleFanSpeedChange = async (event) => {
    try {
      setLoading(true);
      const command = {
        command: 'fan-speed',
        value: event.target.value
      };

      await lgThinqService.controlDevice(selectedDevice.id, command);
      
      // Refresh device status
      await fetchDeviceStatus(selectedDevice);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to set fan speed. Please try again.');
      console.error('Error setting fan speed:', err);
    }
  };

  // Handle mode change
  const handleModeChange = async (event) => {
    try {
      setLoading(true);
      const command = {
        command: 'mode',
        value: event.target.value
      };

      await lgThinqService.controlDevice(selectedDevice.id, command);
      
      // Refresh device status
      await fetchDeviceStatus(selectedDevice);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to set mode. Please try again.');
      console.error('Error setting mode:', err);
    }
  };

  // Handle swing mode change
  const handleSwingModeChange = async (event) => {
    try {
      setLoading(true);
      const command = {
        command: 'swing-mode',
        value: event.target.value
      };

      await lgThinqService.controlDevice(selectedDevice.id, command);
      
      // Refresh device status
      await fetchDeviceStatus(selectedDevice);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError('Failed to set swing mode. Please try again.');
      console.error('Error setting swing mode:', err);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    if (selectedDevice) {
      // Refresh both device status and details
      await Promise.all([
        fetchDeviceStatus(selectedDevice),
        fetchDeviceDetails(selectedDevice)
      ]);
    }
  };

  return (
    <Container maxWidth="lg">
      <Helmet>
        <title>LG ThinQ AC Control | CSF Staff Portal</title>
      </Helmet>

      <Box sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            LG ThinQ AC Control
          </Typography>
          
          <Box>
            <Tooltip title="Refresh">
              <IconButton onClick={handleRefresh} disabled={loading || !selectedDevice}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {!configStatus && !loading && (
          <Alert 
            severity="warning" 
            sx={{ mb: 3 }}
          >
            LG ThinQ integration is not configured yet. Please set the required environment variables (LG_THINQ_PAT_TOKEN, LG_THINQ_REGION, LG_THINQ_COUNTRY) to control your AC units.
          </Alert>
        )}

        {configStatus && devices.length === 0 && !loading && (
          <Alert severity="info" sx={{ mb: 3 }}>
            No LG ThinQ devices found. Make sure your devices are connected to your LG ThinQ account.
          </Alert>
        )}

        {configStatus && devices.length > 0 && (
          <Grid container spacing={3}>
            {/* Device List */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader title="Your Devices" />
                <CardContent>
                  <List>
                    {devices.map((device) => (
                      <ListItem 
                        key={device.id}
                        button
                        selected={selectedDevice && selectedDevice.id === device.id}
                        onClick={() => handleDeviceSelect(device)}
                      >
                        <ListItemIcon>
                          <AcIcon color={device.online ? "primary" : "disabled"} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={device.name || 'Unnamed Device'} 
                          secondary={device.type || 'AC Unit'} 
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>

            {/* Device Controls */}
            <Grid item xs={12} md={8}>
              {selectedDevice && deviceStatus ? (
                <Card>
                  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={tabValue} onChange={handleTabChange} aria-label="device control tabs">
                      <Tab label="Basic Controls" />
                      <Tab label="Advanced Settings" />
                      <Tab label="Device Info" />
                    </Tabs>
                  </Box>

                  {/* Basic Controls Tab */}
                  <TabPanel value={tabValue} index={0}>
                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <PowerIcon color={deviceStatus.power ? "primary" : "disabled"} sx={{ mr: 1 }} />
                            <Typography variant="h6">Power</Typography>
                          </Box>
                          <Switch
                            checked={deviceStatus.power}
                            onChange={handlePowerToggle}
                            disabled={loading}
                          />
                        </Paper>
                      </Grid>

                      <Grid item xs={12}>
                        <Paper sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <ThermostatIcon color="primary" sx={{ mr: 1 }} />
                            <Typography variant="h6">Temperature</Typography>
                            <Typography variant="h6" sx={{ ml: 'auto' }}>
                              {deviceStatus.temperature}°C
                            </Typography>
                          </Box>
                          <Slider
                            value={deviceStatus.temperature}
                            min={16}
                            max={30}
                            step={1}
                            marks
                            valueLabelDisplay="auto"
                            onChange={handleTemperatureChange}
                            disabled={loading || !deviceStatus.power}
                          />
                        </Paper>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Paper sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <FanSpeedIcon color="primary" sx={{ mr: 1 }} />
                            <Typography variant="h6">Fan Speed</Typography>
                          </Box>
                          <FormControl fullWidth disabled={loading || !deviceStatus.power}>
                            <Select
                              value={deviceStatus.fanSpeed}
                              onChange={handleFanSpeedChange}
                            >
                              <MenuItem value={1}>Low</MenuItem>
                              <MenuItem value={2}>Medium</MenuItem>
                              <MenuItem value={3}>High</MenuItem>
                              <MenuItem value={4}>Auto</MenuItem>
                            </Select>
                          </FormControl>
                        </Paper>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Paper sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <AcIcon color="primary" sx={{ mr: 1 }} />
                            <Typography variant="h6">Mode</Typography>
                          </Box>
                          <FormControl fullWidth disabled={loading || !deviceStatus.power}>
                            <Select
                              value={deviceStatus.mode}
                              onChange={handleModeChange}
                            >
                              <MenuItem value="cool">Cool</MenuItem>
                              <MenuItem value="heat">Heat</MenuItem>
                              <MenuItem value="fan">Fan</MenuItem>
                              <MenuItem value="dry">Dry</MenuItem>
                              <MenuItem value="auto">Auto</MenuItem>
                            </Select>
                          </FormControl>
                        </Paper>
                      </Grid>
                    </Grid>
                  </TabPanel>

                  {/* Advanced Settings Tab */}
                  <TabPanel value={tabValue} index={1}>
                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <Paper sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6">Swing Mode</Typography>
                          </Box>
                          <FormControl fullWidth disabled={loading || !deviceStatus.power}>
                            <Select
                              value={deviceStatus.swingMode || 'off'}
                              onChange={handleSwingModeChange}
                            >
                              <MenuItem value="off">Off</MenuItem>
                              <MenuItem value="horizontal">Horizontal</MenuItem>
                              <MenuItem value="vertical">Vertical</MenuItem>
                              <MenuItem value="both">Both</MenuItem>
                            </Select>
                          </FormControl>
                        </Paper>
                      </Grid>
                    </Grid>
                  </TabPanel>

                  {/* Device Info Tab */}
                  <TabPanel value={tabValue} index={2}>
                    {deviceDetails ? (
                      <Box>
                        <Typography variant="h6" gutterBottom>Basic Information</Typography>
                        <List>
                          <ListItem>
                            <ListItemText primary="Device Name" secondary={selectedDevice.name} />
                          </ListItem>
                          <ListItem>
                            <ListItemText primary="Device ID" secondary={selectedDevice.id} />
                          </ListItem>
                          <ListItem>
                            <ListItemText primary="Model" secondary={deviceDetails.modelName || selectedDevice.model || 'Unknown'} />
                          </ListItem>
                          <ListItem>
                            <ListItemText primary="Type" secondary={deviceDetails.deviceType || selectedDevice.type || 'AC Unit'} />
                          </ListItem>
                          <ListItem>
                            <ListItemText primary="Status" secondary={deviceStatus.power ? 'On' : 'Off'} />
                          </ListItem>
                          <ListItem>
                            <ListItemText primary="Online" secondary={selectedDevice.online ? 'Yes' : 'No'} />
                          </ListItem>
                        </List>

                        {deviceDetails.deviceSpec && (
                          <>
                            <Divider sx={{ my: 2 }} />
                            <Typography variant="h6" gutterBottom>Device Specifications</Typography>
                            <List>
                              {deviceDetails.deviceSpec.modelName && (
                                <ListItem>
                                  <ListItemText primary="Model Name" secondary={deviceDetails.deviceSpec.modelName} />
                                </ListItem>
                              )}
                              {deviceDetails.deviceSpec.manufacturer && (
                                <ListItem>
                                  <ListItemText primary="Manufacturer" secondary={deviceDetails.deviceSpec.manufacturer} />
                                </ListItem>
                              )}
                              {deviceDetails.deviceSpec.networkType && (
                                <ListItem>
                                  <ListItemText primary="Network Type" secondary={deviceDetails.deviceSpec.networkType} />
                                </ListItem>
                              )}
                              {deviceDetails.deviceSpec.deviceType && (
                                <ListItem>
                                  <ListItemText primary="Device Type" secondary={deviceDetails.deviceSpec.deviceType} />
                                </ListItem>
                              )}
                            </List>
                          </>
                        )}

                        {deviceDetails.deviceState && (
                          <>
                            <Divider sx={{ my: 2 }} />
                            <Typography variant="h6" gutterBottom>Current State</Typography>
                            <List>
                              {Object.entries(deviceDetails.deviceState).map(([key, value]) => (
                                <ListItem key={key}>
                                  <ListItemText 
                                    primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')} 
                                    secondary={typeof value === 'object' ? JSON.stringify(value) : String(value)} 
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </>
                        )}

                        {deviceDetails.supportedFeatures && deviceDetails.supportedFeatures.length > 0 && (
                          <>
                            <Divider sx={{ my: 2 }} />
                            <Typography variant="h6" gutterBottom>Supported Features</Typography>
                            <List>
                              {deviceDetails.supportedFeatures.map((feature, index) => (
                                <ListItem key={index}>
                                  <ListItemText primary={feature} />
                                </ListItem>
                              ))}
                            </List>
                          </>
                        )}
                      </Box>
                    ) : (
                      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                        {loading ? (
                          <CircularProgress />
                        ) : (
                          <Alert severity="info">Loading device details...</Alert>
                        )}
                      </Box>
                    )}
                  </TabPanel>
                </Card>
              ) : (
                <Card>
                  <CardContent>
                    <Alert severity="info">
                      {devices.length > 0 
                        ? 'Select a device from the list to control it' 
                        : 'No devices available. Make sure your LG ThinQ account is properly configured and your devices are connected.'}
                    </Alert>
                  </CardContent>
                </Card>
              )}
            </Grid>
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default LGThinqPage;