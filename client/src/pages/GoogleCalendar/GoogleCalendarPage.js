import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Alert, 
  CircularProgress,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Divider,
  Grid,
  Card,
  CardContent,
  CardActions,
  TextField,
  InputAdornment,
  IconButton,
  Tabs,
  Tab,
  Chip
} from '@mui/material';
import { 
  Event as EventIcon,
  CalendarMonth as CalendarIcon,
  Search as SearchIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Today as TodayIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import googleCalendarService from '../../services/googleCalendarService';
import moment from 'moment';

/**
 * Google Calendar Page
 * Displays calendars and events from Google Calendar
 */
const GoogleCalendarPage = () => {
  const navigate = useNavigate();
  const [calendars, setCalendars] = useState([]);
  const [events, setEvents] = useState([]);
  const [selectedCalendar, setSelectedCalendar] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingEvents, setLoadingEvents] = useState(false);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [tabValue, setTabValue] = useState(0);

  // Load calendars directly using environment variables
  useEffect(() => {
    const loadCalendars = async () => {
      try {
        // Get configuration but don't block on authentication status
        const config = await googleCalendarService.getConfig();
        setConfigStatus(config);
        
        // Attempt to fetch calendars directly
        await fetchCalendars();
      } catch (err) {
        console.error('Error loading calendars:', err);
        
        // Handle specific error cases
        if (err.response) {
          const status = err.response.status;
          const errorData = err.response.data || {};
          
          if (status === 401) {
            setError({
              message: errorData.message || 'You must be logged in to view your calendars',
              authUrl: errorData.authUrl || '/api/auth/google',
              type: 'auth'
            });
          } else if (status === 403) {
            setError({
              message: errorData.message || 'You do not have permission to access these calendars',
              authUrl: errorData.authUrl || '/api/auth/google',
              type: 'permission'
            });
          } else {
            setError({
              message: 'Failed to load calendars from Google Calendar',
              type: 'error'
            });
          }
        } else {
          setError({
            message: 'Failed to load calendars from Google Calendar',
            type: 'error'
          });
        }
        
        setLoading(false);
      }
    };

    loadCalendars();
  }, []);

  // Fetch calendars from Google Calendar
  const fetchCalendars = async () => {
    try {
      setLoading(true);
      const data = await googleCalendarService.listCalendars();
      setCalendars(data);

      // Select the primary calendar by default if available
      const primaryCalendar = data.find(cal => cal.primary) || (data.length > 0 ? data[0] : null);
      if (primaryCalendar) {
        setSelectedCalendar(primaryCalendar);
        await fetchEvents(primaryCalendar.id);
      }

      setLoading(false);
    } catch (err) {
      console.error('Error fetching calendars:', err);
      
      // Handle specific error cases
      if (err.response) {
        const status = err.response.status;
        const errorData = err.response.data || {};
        
        if (status === 401) {
          setError({
            message: errorData.message || 'You must be logged in to view your calendars',
            authUrl: errorData.authUrl || '/api/auth/google',
            type: 'auth'
          });
        } else if (status === 403) {
          setError({
            message: errorData.message || 'You do not have permission to access these calendars',
            authUrl: errorData.authUrl || '/api/auth/google',
            type: 'permission'
          });
        } else {
          setError({
            message: errorData.message || 'Failed to load calendars from Google Calendar',
            type: 'error'
          });
        }
      } else {
        setError({
          message: 'Failed to load calendars from Google Calendar',
          type: 'error'
        });
      }
      
      setLoading(false);
    }
  };

  // Fetch events for a calendar
  const fetchEvents = async (calendarId) => {
    try {
      setLoadingEvents(true);

      // Get events for the next 30 days
      const timeMin = new Date().toISOString();
      const timeMax = new Date();
      timeMax.setDate(timeMax.getDate() + 30);

      const data = await googleCalendarService.listEvents(calendarId, {
        timeMin,
        timeMax: timeMax.toISOString(),
        maxResults: 50,
        singleEvents: true,
        orderBy: 'startTime'
      });

      setEvents(data);
      setLoadingEvents(false);
    } catch (err) {
      console.error('Error fetching events:', err);
      
      // Handle specific error cases
      if (err.response) {
        const status = err.response.status;
        const errorData = err.response.data || {};
        
        if (status === 401) {
          setError({
            message: errorData.message || 'You must be logged in to view calendar events',
            authUrl: errorData.authUrl || '/api/auth/google',
            type: 'auth'
          });
        } else if (status === 403) {
          setError({
            message: errorData.message || 'You do not have permission to access events from this calendar',
            authUrl: errorData.authUrl || '/api/auth/google',
            type: 'permission'
          });
        } else if (status === 404) {
          setError({
            message: errorData.message || 'Calendar not found or you do not have permission to access it',
            type: 'notFound'
          });
        } else {
          setError({
            message: errorData.message || 'Failed to load events from Google Calendar',
            type: 'error'
          });
        }
      } else {
        setError({
          message: 'Failed to load events from Google Calendar',
          type: 'error'
        });
      }
      
      setLoadingEvents(false);
    }
  };

  // Handle calendar selection
  const handleCalendarSelect = async (calendar) => {
    setSelectedCalendar(calendar);
    await fetchEvents(calendar.id);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Format event time
  const formatEventTime = (event) => {
    if (event.start.dateTime) {
      // Event with specific time
      const start = moment(event.start.dateTime);
      const end = moment(event.end.dateTime);

      if (start.isSame(end, 'day')) {
        // Same day event
        return `${start.format('MMM D, YYYY')} • ${start.format('h:mm A')} - ${end.format('h:mm A')}`;
      } else {
        // Multi-day event with time
        return `${start.format('MMM D, YYYY h:mm A')} - ${end.format('MMM D, YYYY h:mm A')}`;
      }
    } else if (event.start.date) {
      // All-day event
      const start = moment(event.start.date);
      const end = moment(event.end.date).subtract(1, 'day'); // End date is exclusive

      if (start.isSame(end, 'day')) {
        // Single day event
        return `${start.format('MMM D, YYYY')} • All day`;
      } else {
        // Multi-day event
        return `${start.format('MMM D, YYYY')} - ${end.format('MMM D, YYYY')} • All day`;
      }
    }

    return 'Time not specified';
  };

  // Get event status chip
  const getEventStatusChip = (event) => {
    if (event.status === 'cancelled') {
      return <Chip label="Cancelled" color="error" size="small" />;
    }

    if (event.start.dateTime) {
      const start = moment(event.start.dateTime);
      const now = moment();

      if (start.isBefore(now)) {
        return <Chip label="In Progress" color="primary" size="small" />;
      }

      if (start.isSame(now, 'day')) {
        return <Chip label="Today" color="success" size="small" />;
      }

      if (start.isBefore(moment().add(1, 'day').endOf('day'))) {
        return <Chip label="Tomorrow" color="warning" size="small" />;
      }
    } else if (event.start.date) {
      const start = moment(event.start.date);
      const now = moment();

      if (start.isSame(now, 'day')) {
        return <Chip label="Today" color="success" size="small" />;
      }

      if (start.isBefore(moment().add(1, 'day').endOf('day')) && start.isAfter(now)) {
        return <Chip label="Tomorrow" color="warning" size="small" />;
      }
    }

    return null;
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Google Calendar
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and manage your Google Calendar events
          </Typography>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {error && (
              <Alert 
                severity={error.type === 'auth' || error.type === 'permission' ? "warning" : "error"} 
                sx={{ mb: 3 }}
                action={
                  (error.type === 'auth' || error.type === 'permission') && error.authUrl ? (
                    <Button 
                      color="inherit" 
                      size="small" 
                      href={error.authUrl}
                      variant="outlined"
                    >
                      Log In with Google
                    </Button>
                  ) : null
                }
              >
                {error.message || error}
              </Alert>
            )}

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                {/* Calendar List */}
                <Grid item xs={12} md={3}>
                  <Typography variant="h6" gutterBottom>
                    Your Calendars
                  </Typography>
                  <List>
                    {calendars.map((calendar) => (
                      <ListItem 
                        key={calendar.id} 
                        disablePadding
                        divider
                      >
                        <ListItemButton 
                          selected={selectedCalendar && selectedCalendar.id === calendar.id}
                          onClick={() => handleCalendarSelect(calendar)}
                        >
                          <ListItemIcon>
                            <CalendarIcon sx={{ color: calendar.backgroundColor || 'inherit' }} />
                          </ListItemIcon>
                          <ListItemText 
                            primary={calendar.summary} 
                            secondary={calendar.primary ? 'Primary Calendar' : calendar.description}
                          />
                        </ListItemButton>
                      </ListItem>
                    ))}
                  </List>
                </Grid>

                {/* Events */}
                <Grid item xs={12} md={9}>
                  {selectedCalendar ? (
                    <>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">
                          {selectedCalendar.summary}
                        </Typography>
                        <Button
                          variant="outlined"
                          color="primary"
                          startIcon={<RefreshIcon />}
                          onClick={() => fetchEvents(selectedCalendar.id)}
                          disabled={loadingEvents}
                        >
                          Refresh
                        </Button>
                      </Box>

                      <Tabs 
                        value={tabValue} 
                        onChange={handleTabChange} 
                        sx={{ mb: 2 }}
                        variant="scrollable"
                        scrollButtons="auto"
                      >
                        <Tab label="Upcoming Events" icon={<TodayIcon />} iconPosition="start" />
                        <Tab label="All Events" icon={<EventIcon />} iconPosition="start" />
                      </Tabs>

                      {loadingEvents ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                          <CircularProgress />
                        </Box>
                      ) : (
                        <>
                          {events.length === 0 ? (
                            <Box sx={{ textAlign: 'center', my: 8 }}>
                              <Typography variant="h6" color="text.secondary">
                                No events found in this calendar
                              </Typography>
                            </Box>
                          ) : (
                            <List>
                              {events.map((event) => (
                                <ListItem 
                                  key={event.id} 
                                  disablePadding
                                  divider
                                  sx={{ mb: 2 }}
                                >
                                  <Card sx={{ width: '100%' }}>
                                    <CardContent>
                                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                        <Typography variant="h6" component="div">
                                          {event.summary}
                                        </Typography>
                                        {getEventStatusChip(event)}
                                      </Box>
                                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        {formatEventTime(event)}
                                      </Typography>
                                      {event.location && (
                                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                          Location: {event.location}
                                        </Typography>
                                      )}
                                      {event.description && (
                                        <Typography variant="body2" sx={{ mt: 2, whiteSpace: 'pre-line' }}>
                                          {event.description}
                                        </Typography>
                                      )}
                                    </CardContent>
                                  </Card>
                                </ListItem>
                              ))}
                            </List>
                          )}
                        </>
                      )}
                    </>
                  ) : (
                    <Box sx={{ textAlign: 'center', my: 8 }}>
                      <Typography variant="h6" color="text.secondary">
                        Select a calendar to view events
                      </Typography>
                    </Box>
                  )}
                </Grid>
              </Grid>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleCalendarPage;
