import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  Switch,
  FormControlLabel,
  Slider,
  IconButton,
  TextField
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import DevicesIcon from '@mui/icons-material/Devices';
import SettingsIcon from '@mui/icons-material/Settings';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import ColorLensIcon from '@mui/icons-material/ColorLens';
import BrightnessHighIcon from '@mui/icons-material/BrightnessHigh';
import SpeedIcon from '@mui/icons-material/Speed';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import coloritService from '../../services/coloritService';
import { useAuth } from '../../context/AuthContext';
import { SketchPicker } from 'react-color';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`colorlit-tabpanel-${index}`}
      aria-labelledby={`colorlit-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ColoritPage = () => {
  const { hasIntegration } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [status, setStatus] = useState(null);
  const [modes, setModes] = useState([]);
  const [zones, setZones] = useState([]);
  const [selectedZone, setSelectedZone] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [color, setColor] = useState({ r: 255, g: 255, b: 255, w: 0 });
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [brightness, setBrightness] = useState(100);
  const [speed, setSpeed] = useState(50);
  const [selectedMode, setSelectedMode] = useState('');
  const [power, setPower] = useState(false);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await coloritService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus || !configStatus.host) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Device
            const deviceInfoData = await coloritService.getDeviceInfo();
            setDeviceInfo(deviceInfoData);
            
            const statusData = await coloritService.getStatus();
            setStatus(statusData);
            
            // Update state based on status
            if (statusData) {
              setPower(statusData.power || false);
              setBrightness(statusData.brightness || 100);
              setSpeed(statusData.speed || 50);
              setSelectedMode(statusData.mode || '');
              if (statusData.color) {
                setColor({
                  r: statusData.color.r || 255,
                  g: statusData.color.g || 255,
                  b: statusData.color.b || 255,
                  w: statusData.color.w || 0
                });
              }
            }
            break;
          case 1: // Modes
            const modesData = await coloritService.getModes();
            setModes(modesData || []);
            break;
          case 2: // Zones
            const zonesData = await coloritService.getZones();
            setZones(zonesData || []);
            break;
          case 3: // Settings
            // Config already loaded
            break;
          default:
            break;
        }
      } catch (err) {
        setError('Failed to load data from Colorlit. Please check your connection and try again.');
        console.error('Error loading Colorlit data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus]);

  // Handle power toggle
  const handlePowerToggle = async () => {
    try {
      setLoading(true);
      await coloritService.setPower(!power);
      setPower(!power);
    } catch (err) {
      setError('Failed to toggle power. Please try again.');
      console.error('Error toggling power:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle color change
  const handleColorChange = (newColor) => {
    setColor({
      r: newColor.rgb.r,
      g: newColor.rgb.g,
      b: newColor.rgb.b,
      w: color.w
    });
  };

  // Handle white value change
  const handleWhiteChange = (event, newValue) => {
    setColor({
      ...color,
      w: newValue
    });
  };

  // Apply color
  const handleApplyColor = async () => {
    try {
      setLoading(true);
      await coloritService.setColor(color.r, color.g, color.b, color.w);
    } catch (err) {
      setError('Failed to set color. Please try again.');
      console.error('Error setting color:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle brightness change
  const handleBrightnessChange = async (event, newValue) => {
    setBrightness(newValue);
  };

  // Apply brightness
  const handleApplyBrightness = async () => {
    try {
      setLoading(true);
      await coloritService.setBrightness(brightness);
    } catch (err) {
      setError('Failed to set brightness. Please try again.');
      console.error('Error setting brightness:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle speed change
  const handleSpeedChange = async (event, newValue) => {
    setSpeed(newValue);
  };

  // Apply speed
  const handleApplySpeed = async () => {
    try {
      setLoading(true);
      await coloritService.setSpeed(speed);
    } catch (err) {
      setError('Failed to set speed. Please try again.');
      console.error('Error setting speed:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle mode selection
  const handleModeSelect = async (mode) => {
    try {
      setLoading(true);
      await coloritService.setMode(mode);
      setSelectedMode(mode);
    } catch (err) {
      setError('Failed to set mode. Please try again.');
      console.error('Error setting mode:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle zone selection
  const handleZoneSelect = async (zoneId) => {
    try {
      setLoading(true);
      await coloritService.selectZone(zoneId);
      setSelectedZone(zoneId);
      
      // Refresh status after zone selection
      const statusData = await coloritService.getStatus();
      setStatus(statusData);
      
      // Update state based on status
      if (statusData) {
        setPower(statusData.power || false);
        setBrightness(statusData.brightness || 100);
        setSpeed(statusData.speed || 50);
        setSelectedMode(statusData.mode || '');
        if (statusData.color) {
          setColor({
            r: statusData.color.r || 255,
            g: statusData.color.g || 255,
            b: statusData.color.b || 255,
            w: statusData.color.w || 0
          });
        }
      }
    } catch (err) {
      setError('Failed to select zone. Please try again.');
      console.error('Error selecting zone:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle configuration save
  const handleSaveConfig = async (event) => {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const config = {
      host: formData.get('host'),
      port: parseInt(formData.get('port'), 10),
      apiKey: formData.get('apiKey'),
      username: formData.get('username'),
      password: formData.get('password')
    };
    
    try {
      setLoading(true);
      const result = await coloritService.saveConfig(config);
      setConfigStatus(result.config);
      setError(null);
    } catch (err) {
      setError('Failed to save configuration. Please try again.');
      console.error('Error saving configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  // Render loading state
  if (loadingConfig) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Render not configured state
  if (!configStatus || !configStatus.host) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h4" gutterBottom>
            Colorlit Z4 Pro LED Controller
          </Typography>
          <Alert severity="info" sx={{ mb: 3 }}>
            The Colorlit Z4 Pro LED Controller integration is not configured. Please configure it below.
          </Alert>
          
          <Box component="form" onSubmit={handleSaveConfig} noValidate sx={{ mt: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  id="host"
                  label="Host"
                  name="host"
                  autoComplete="off"
                  helperText="IP address or hostname of the Colorlit controller"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  id="port"
                  label="Port"
                  name="port"
                  type="number"
                  defaultValue={80}
                  autoComplete="off"
                  helperText="Port number (default: 80)"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="apiKey"
                  label="API Key"
                  name="apiKey"
                  autoComplete="off"
                  helperText="API key (if required)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  id="username"
                  label="Username"
                  name="username"
                  autoComplete="off"
                  helperText="Username (if required)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  id="password"
                  label="Password"
                  name="password"
                  type="password"
                  autoComplete="off"
                  helperText="Password (if required)"
                />
              </Grid>
            </Grid>
            <Button
              type="submit"
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Box>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Colorlit Z4 Pro LED Controller
        </Typography>
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="colorlit tabs">
            <Tab label="Control" icon={<LightbulbIcon />} iconPosition="start" />
            <Tab label="Modes" icon={<ViewModuleIcon />} iconPosition="start" />
            <Tab label="Zones" icon={<DevicesIcon />} iconPosition="start" />
            <Tab label="Settings" icon={<SettingsIcon />} iconPosition="start" />
          </Tabs>
        </Box>
        
        {/* Control Tab */}
        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {/* Power Control */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Power
                    </Typography>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Typography>
                        {power ? 'ON' : 'OFF'}
                      </Typography>
                      <IconButton 
                        color={power ? 'primary' : 'default'} 
                        onClick={handlePowerToggle}
                        disabled={loading}
                        size="large"
                      >
                        <PowerSettingsNewIcon />
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              {/* Color Control */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Color
                    </Typography>
                    <Box 
                      sx={{ 
                        height: 40, 
                        width: '100%', 
                        backgroundColor: `rgb(${color.r}, ${color.g}, ${color.b})`,
                        borderRadius: 1,
                        mb: 2,
                        cursor: 'pointer',
                        border: '1px solid #ccc'
                      }}
                      onClick={() => setShowColorPicker(!showColorPicker)}
                    />
                    {showColorPicker && (
                      <Box sx={{ mb: 2 }}>
                        <SketchPicker 
                          color={{ r: color.r, g: color.g, b: color.b }}
                          onChange={handleColorChange}
                          width="100%"
                        />
                      </Box>
                    )}
                    <Typography gutterBottom>White: {color.w}</Typography>
                    <Slider
                      value={color.w}
                      onChange={handleWhiteChange}
                      min={0}
                      max={255}
                      valueLabelDisplay="auto"
                    />
                  </CardContent>
                  <CardActions>
                    <Button 
                      variant="contained" 
                      onClick={handleApplyColor}
                      disabled={loading}
                      startIcon={<ColorLensIcon />}
                    >
                      Apply Color
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
              
              {/* Brightness Control */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Brightness
                    </Typography>
                    <Typography gutterBottom>
                      {brightness}%
                    </Typography>
                    <Slider
                      value={brightness}
                      onChange={handleBrightnessChange}
                      min={0}
                      max={100}
                      valueLabelDisplay="auto"
                    />
                  </CardContent>
                  <CardActions>
                    <Button 
                      variant="contained" 
                      onClick={handleApplyBrightness}
                      disabled={loading}
                      startIcon={<BrightnessHighIcon />}
                    >
                      Apply Brightness
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
              
              {/* Speed Control */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Speed
                    </Typography>
                    <Typography gutterBottom>
                      {speed}%
                    </Typography>
                    <Slider
                      value={speed}
                      onChange={handleSpeedChange}
                      min={0}
                      max={100}
                      valueLabelDisplay="auto"
                    />
                  </CardContent>
                  <CardActions>
                    <Button 
                      variant="contained" 
                      onClick={handleApplySpeed}
                      disabled={loading}
                      startIcon={<SpeedIcon />}
                    >
                      Apply Speed
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
              
              {/* Device Info */}
              {deviceInfo && (
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Device Information
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2">Model</Typography>
                          <Typography variant="body2">{deviceInfo.model || 'Z4 Pro'}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2">Firmware Version</Typography>
                          <Typography variant="body2">{deviceInfo.firmwareVersion || 'Unknown'}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2">MAC Address</Typography>
                          <Typography variant="body2">{deviceInfo.macAddress || 'Unknown'}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2">IP Address</Typography>
                          <Typography variant="body2">{deviceInfo.ipAddress || configStatus.host}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          )}
        </TabPanel>
        
        {/* Modes Tab */}
        <TabPanel value={tabValue} index={1}>
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {modes.length > 0 ? (
                modes.map((mode, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card 
                      sx={{ 
                        cursor: 'pointer',
                        bgcolor: selectedMode === mode.name ? 'primary.light' : 'background.paper'
                      }}
                      onClick={() => handleModeSelect(mode.name)}
                    >
                      <CardContent>
                        <Typography variant="h6">{mode.name}</Typography>
                        {mode.description && (
                          <Typography variant="body2" color="text.secondary">
                            {mode.description}
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Alert severity="info">
                    No modes available or unable to retrieve modes from the device.
                  </Alert>
                </Grid>
              )}
            </Grid>
          )}
        </TabPanel>
        
        {/* Zones Tab */}
        <TabPanel value={tabValue} index={2}>
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {zones.length > 0 ? (
                zones.map((zone, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card 
                      sx={{ 
                        cursor: 'pointer',
                        bgcolor: selectedZone === zone.id ? 'primary.light' : 'background.paper'
                      }}
                      onClick={() => handleZoneSelect(zone.id)}
                    >
                      <CardContent>
                        <Typography variant="h6">{zone.name}</Typography>
                        {zone.description && (
                          <Typography variant="body2" color="text.secondary">
                            {zone.description}
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              ) : (
                <Grid item xs={12}>
                  <Alert severity="info">
                    No zones available or unable to retrieve zones from the device.
                  </Alert>
                </Grid>
              )}
            </Grid>
          )}
        </TabPanel>
        
        {/* Settings Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box component="form" onSubmit={handleSaveConfig} noValidate>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  required
                  fullWidth
                  id="host"
                  label="Host"
                  name="host"
                  autoComplete="off"
                  defaultValue={configStatus.host}
                  helperText="IP address or hostname of the Colorlit controller"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  id="port"
                  label="Port"
                  name="port"
                  type="number"
                  defaultValue={configStatus.port || 80}
                  autoComplete="off"
                  helperText="Port number (default: 80)"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="apiKey"
                  label="API Key"
                  name="apiKey"
                  autoComplete="off"
                  defaultValue={configStatus.apiKey ? '' : ''}
                  helperText="API key (if required)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  id="username"
                  label="Username"
                  name="username"
                  autoComplete="off"
                  defaultValue={configStatus.username || ''}
                  helperText="Username (if required)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  id="password"
                  label="Password"
                  name="password"
                  type="password"
                  autoComplete="off"
                  defaultValue=""
                  helperText="Password (if required)"
                />
              </Grid>
            </Grid>
            <Button
              type="submit"
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
            
            <Typography variant="subtitle2" sx={{ mt: 4 }}>
              Configuration Source: {configStatus.configuredWith === 'environment' ? 'Environment Variables' : 'Database'}
            </Typography>
            {configStatus.configuredWith === 'environment' && (
              <Alert severity="info" sx={{ mt: 2 }}>
                This integration is configured using environment variables. Changes made here will be saved to the database but will not take effect until the environment variables are removed or the application is restarted.
              </Alert>
            )}
          </Box>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default ColoritPage;