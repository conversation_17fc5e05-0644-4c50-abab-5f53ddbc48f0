import React from 'react';
import { Container, Paper, Typography, Box, Alert } from '@mui/material';
import SettingsIcon from '@mui/icons-material/Settings';

/**
 * A component that displays a message informing users that Google integrations
 * are now configured through environment variables by administrators.
 */
const GoogleConfigEnvMessage = ({ serviceName = 'Google' }) => {
  return (
    <Container maxWidth="md">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            {serviceName} Configuration
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6">Configuration Change</Typography>
          <Typography variant="body1">
            {serviceName} integration is now fully configured through environment variables by administrators.
            The setup screens have been removed as they are no longer needed.
          </Typography>
        </Alert>

        <Typography variant="body1" paragraph>
          All {serviceName} related integrations now use environment variables for authentication and access.
          If you need to make changes to the configuration, please contact your system administrator.
        </Typography>

        <Typography variant="body2" color="text.secondary">
          This change improves security and simplifies the deployment process.
        </Typography>
      </Paper>
    </Container>
  );
};

export default GoogleConfigEnvMessage;