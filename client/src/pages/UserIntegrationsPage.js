import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  Divider,
  CircularProgress,
  Alert,
  AlertTitle,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  IconButton,
  Backdrop
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  PersonAdd as PersonAddIcon,
  PersonRemove as PersonRemoveIcon,
  AccountCircle as AccountCircleIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import integrationService from '../services/integrationService';

// Import icons for integrations
import {
  Image as CanvaIcon,
  Computer as GLPIIcon,
  Event as PlanningCenterIcon,
  Storage as SynologyIcon,
  AcUnit as DreoIcon,
  Security as LenelS2NetBoxIcon,
  PhoneIphone as MosyleBusinessIcon,
  Lock as UnifiAccessIcon,
  Router as UnifiNetworkIcon,
  Videocam as UnifiProtectIcon,
  AdminPanelSettings as AdminIcon,
  CalendarMonth as GoogleCalendarIcon
} from '@mui/icons-material';

// Map of integration IDs to their icons
const integrationIcons = {
  'canva': <CanvaIcon />,
  'dreo': <DreoIcon />,
  'glpi': <GLPIIcon />,
  'google-admin': <AdminIcon />,
  'google-calendar': <GoogleCalendarIcon />,
  'lenel-s2-netbox': <LenelS2NetBoxIcon />,
  'mosyle-business': <MosyleBusinessIcon />,
  'planning-center': <PlanningCenterIcon />,
  'synology': <SynologyIcon />,
  'unifi-access': <UnifiAccessIcon />,
  'unifi-network': <UnifiNetworkIcon />,
  'unifi-protect': <UnifiProtectIcon />
};

const UserIntegrationsPage = () => {
  const { user, updateUser } = useAuth();
  const [integrations, setIntegrations] = useState([]);
  const [integrationStatus, setIntegrationStatus] = useState([]);
  const [integrationSettings, setIntegrationSettings] = useState({});
  const [userPreferences, setUserPreferences] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [configOptions, setConfigOptions] = useState([]);
  const [configLoading, setConfigLoading] = useState(false);
  const [configError, setConfigError] = useState(null);
  const [userConfig, setUserConfig] = useState({});
  
  // New state for user provisioning
  const [accountStatus, setAccountStatus] = useState({});
  const [provisioningLoading, setProvisioningLoading] = useState(false);
  const [provisioningIntegration, setProvisioningIntegration] = useState(null);
  const [provisioningSuccess, setProvisioningSuccess] = useState(null);
  const [provisioningError, setProvisioningError] = useState(null);

  // Fetch available integrations, their status, user preferences, integration settings, and account status
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [availableIntegrations, status, preferences, settings] = await Promise.all([
          integrationService.getAvailableIntegrations(),
          integrationService.getIntegrationStatus(),
          integrationService.getUserPreferences(),
          integrationService.getAllSettings()
        ]);
        setIntegrations(availableIntegrations);
        setIntegrationStatus(status);
        setUserPreferences(preferences);

        // Convert settings array to object for easier access
        const settingsObj = {};
        settings.forEach(setting => {
          settingsObj[setting.integrationId] = setting;
        });
        setIntegrationSettings(settingsObj);
        
        // Fetch user account status if user is available
        if (user && user._id) {
          fetchUserAccountStatus(user._id);
        }
      } catch (err) {
        console.error('Error fetching integration data:', err);
        setError('Failed to load integration data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);
  
  // Fetch user account status across all integrations
  const fetchUserAccountStatus = async (userId) => {
    try {
      const status = await integrationService.getUserAccountStatus(userId);
      setAccountStatus(status);
    } catch (err) {
      console.error('Error fetching user account status:', err);
      // Don't set an error message here to avoid disrupting the main UI
    }
  };
  
  // Provision user account in an integration
  const handleProvisionAccount = async (integrationId) => {
    try {
      setProvisioningLoading(true);
      setProvisioningIntegration(integrationId);
      setProvisioningError(null);
      setProvisioningSuccess(null);
      
      // Get user data from preferences if available
      const userData = userPreferences[integrationId]?.config || {};
      
      // Provision the account
      const result = await integrationService.provisionUserAccount(user._id, integrationId, userData);
      
      // Update account status
      setAccountStatus(prevStatus => ({
        ...prevStatus,
        [integrationId]: {
          exists: true,
          accountId: result.accountId,
          details: result.details
        }
      }));
      
      setProvisioningSuccess(`Account provisioned successfully in ${integrationId}`);
      
      // Refresh account status after a short delay
      setTimeout(() => {
        fetchUserAccountStatus(user._id);
      }, 2000);
    } catch (err) {
      console.error(`Error provisioning account in ${integrationId}:`, err);
      setProvisioningError(err.response?.data?.message || err.message);
    } finally {
      setProvisioningLoading(false);
      setProvisioningIntegration(null);
      
      // Clear success/error messages after a delay
      setTimeout(() => {
        setProvisioningSuccess(null);
        setProvisioningError(null);
      }, 5000);
    }
  };
  
  // Deprovision user account from an integration
  const handleDeprovisionAccount = async (integrationId) => {
    try {
      setProvisioningLoading(true);
      setProvisioningIntegration(integrationId);
      setProvisioningError(null);
      setProvisioningSuccess(null);
      
      // Deprovision the account
      await integrationService.deprovisionUserAccount(user._id, integrationId);
      
      // Update account status
      setAccountStatus(prevStatus => ({
        ...prevStatus,
        [integrationId]: {
          exists: false
        }
      }));
      
      setProvisioningSuccess(`Account deprovisioned successfully from ${integrationId}`);
      
      // Refresh account status after a short delay
      setTimeout(() => {
        fetchUserAccountStatus(user._id);
      }, 2000);
    } catch (err) {
      console.error(`Error deprovisioning account from ${integrationId}:`, err);
      setProvisioningError(err.response?.data?.message || err.message);
    } finally {
      setProvisioningLoading(false);
      setProvisioningIntegration(null);
      
      // Clear success/error messages after a delay
      setTimeout(() => {
        setProvisioningSuccess(null);
        setProvisioningError(null);
      }, 5000);
    }
  };
  
  // Check if an integration supports user provisioning
  const supportsProvisioning = (integrationId) => {
    // Get the integration settings from the state
    const settings = integrationSettings[integrationId];
    // Check if the integration supports provisioning and if it's enabled
    return settings?.supportsProvisioning && settings?.provisioningEnabled;
  };

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead
  // This function is kept for backward compatibility but doesn't toggle anything
  const handleToggleIntegration = async () => {
    // No-op function since all integrations are now active
    return;
  };

  // Handle toggling between global and personal configuration
  const handleToggleUseGlobalConfig = async (integrationId) => {
    try {
      const updatedPreferences = {
        ...userPreferences,
        [integrationId]: {
          ...userPreferences[integrationId],
          useGlobalConfig: !getIntegrationUseGlobalConfig(integrationId)
        }
      };

      await integrationService.updateUserPreferences(updatedPreferences);
      setUserPreferences(updatedPreferences);

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      console.error('Error updating integration preferences:', err);
      setError('Failed to update integration preferences. Please try again.');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Open configuration dialog for an integration
  const handleOpenConfigDialog = async (integration) => {
    setSelectedIntegration(integration);
    setConfigDialogOpen(true);
    setConfigLoading(true);
    setConfigError(null);

    try {
      // Fetch configuration options for the integration
      const response = await integrationService.getIntegrationConfig(integration.id);
      setConfigOptions(response.configOptions || []);

      // Initialize user config with current values or defaults
      const initialConfig = {};
      (response.configOptions || []).forEach(option => {
        if (option.fields) {
          option.fields.forEach(field => {
            initialConfig[field.id] = userPreferences[integration.id]?.config?.[field.id] || field.defaultValue || '';
          });
        }
      });

      setUserConfig(initialConfig);
    } catch (err) {
      console.error('Error fetching integration configuration:', err);
      setConfigError('Failed to load configuration options. Please try again.');
    } finally {
      setConfigLoading(false);
    }
  };

  // Close configuration dialog
  const handleCloseConfigDialog = () => {
    setConfigDialogOpen(false);
    setSelectedIntegration(null);
    setConfigOptions([]);
    setUserConfig({});
    setConfigError(null);
  };

  // Handle configuration field change
  const handleConfigFieldChange = (fieldId, value) => {
    setUserConfig(prevConfig => ({
      ...prevConfig,
      [fieldId]: value
    }));
  };

  // Save user configuration
  const handleSaveConfig = async () => {
    try {
      const updatedPreferences = {
        ...userPreferences,
        [selectedIntegration.id]: {
          ...userPreferences[selectedIntegration.id],
          config: userConfig
        }
      };

      await integrationService.updateUserPreferences(updatedPreferences);
      setUserPreferences(updatedPreferences);

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
      handleCloseConfigDialog();
    } catch (err) {
      console.error('Error updating integration configuration:', err);
      setConfigError('Failed to save configuration. Please try again.');
    }
  };

  // Helper to get integration enabled status
  // All integrations are now assumed to be active for all users
  const getIntegrationEnabled = () => {
    return true;
  };

  // Helper to get integration use global config status
  const getIntegrationUseGlobalConfig = (integrationId) => {
    // If the integration settings enforce global config, return true
    if (integrationSettings[integrationId]?.useGlobalConfig) {
      return true;
    }
    // Otherwise, use the user preference (default to true)
    return userPreferences[integrationId]?.useGlobalConfig !== false;
  };

  // Helper to check if an integration is required
  const isIntegrationRequired = (integrationId) => {
    return integrationSettings[integrationId]?.isRequired || false;
  };

  // Helper to check if an integration is read-only
  const isIntegrationReadOnly = (integrationId) => {
    return integrationSettings[integrationId]?.isReadOnly || false;
  };

  // Get status chip for an integration
  const getStatusChip = (integrationId) => {
    const status = (Array.isArray(integrationStatus) ? integrationStatus : []).find(s => s.integration.toLowerCase() === integrationId);

    if (!status) {
      return <Chip icon={<WarningIcon />} label="Unknown" color="default" size="small" />;
    }

    switch (status.status) {
      case 'active':
        return <Chip icon={<CheckCircleIcon />} label="Active" color="success" size="small" />;
      case 'needs_auth':
        return <Chip icon={<WarningIcon />} label="Needs Authentication" color="warning" size="small" />;
      case 'not_configured':
        return <Chip icon={<WarningIcon />} label="Not Configured" color="warning" size="small" />;
      case 'error':
        return <Chip icon={<ErrorIcon />} label="Error" color="error" size="small" />;
      default:
        return <Chip icon={<WarningIcon />} label={status.status} color="default" size="small" />;
    }
  };

  // Get account status chip for an integration
  const getAccountStatusChip = (integrationId) => {
    const status = accountStatus[integrationId];
    
    if (!supportsProvisioning(integrationId)) {
      return null;
    }
    
    if (!status) {
      return <Chip icon={<WarningIcon />} label="Unknown" color="default" size="small" sx={{ ml: 1 }} />;
    }
    
    if (status.exists) {
      return <Chip icon={<AccountCircleIcon />} label="Account Exists" color="info" size="small" sx={{ ml: 1 }} />;
    } else {
      return <Chip icon={<WarningIcon />} label="No Account" color="default" size="small" sx={{ ml: 1 }} />;
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Integrations
        </Typography>

        <Paper sx={{ p: 3, mb: 3 }}>
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>Integration Access Update</AlertTitle>
            <Typography variant="body2">
              All integrations are now available to users based on their roles and permissions. 
              You no longer need to activate integrations individually.
            </Typography>
          </Alert>
          
          <Typography variant="body1" paragraph>
            Configure your personal settings for the integrations you have access to.
          </Typography>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          ) : (
            <>
              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  Integration preferences updated successfully!
                </Alert>
              )}
              
              {provisioningSuccess && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {provisioningSuccess}
                </Alert>
              )}
              
              {provisioningError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {provisioningError}
                </Alert>
              )}

              <List>
                {(Array.isArray(integrations) ? integrations : []).map((integration) => (
                  <React.Fragment key={integration.id}>
                    <ListItem>
                      <ListItemIcon>
                        {integrationIcons[integration.id] || <SettingsIcon />}
                      </ListItemIcon>
                      <ListItemText 
                        primary={integration.name} 
                        secondary={
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            {getStatusChip(integration.id)}
                            {getAccountStatusChip(integration.id)}
                            <Box sx={{ ml: 1 }}>
                              {getIntegrationEnabled(integration.id) && (
                                <FormControlLabel
                                  control={
                                    <Switch
                                      size="small"
                                      checked={getIntegrationUseGlobalConfig(integration.id)}
                                      onChange={() => handleToggleUseGlobalConfig(integration.id)}
                                      disabled={!getIntegrationEnabled(integration.id) || integrationSettings[integration.id]?.useGlobalConfig}
                                    />
                                  }
                                  label="Use global config"
                                />
                              )}
                            </Box>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {/* User provisioning buttons */}
                          {getIntegrationEnabled(integration.id) && supportsProvisioning(integration.id) && (
                            <>
                              {accountStatus[integration.id]?.exists ? (
                                <Tooltip title="Deprovision Account">
                                  <IconButton
                                    color="error"
                                    onClick={() => handleDeprovisionAccount(integration.id)}
                                    disabled={provisioningLoading}
                                    sx={{ mr: 1 }}
                                  >
                                    {provisioningLoading && provisioningIntegration === integration.id ? (
                                      <CircularProgress size={24} />
                                    ) : (
                                      <PersonRemoveIcon />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              ) : (
                                <Tooltip title="Provision Account">
                                  <IconButton
                                    color="primary"
                                    onClick={() => handleProvisionAccount(integration.id)}
                                    disabled={provisioningLoading}
                                    sx={{ mr: 1 }}
                                  >
                                    {provisioningLoading && provisioningIntegration === integration.id ? (
                                      <CircularProgress size={24} />
                                    ) : (
                                      <PersonAddIcon />
                                    )}
                                  </IconButton>
                                </Tooltip>
                              )}
                            </>
                          )}
                          
                          {/* Configuration button */}
                          {getIntegrationEnabled(integration.id) && !getIntegrationUseGlobalConfig(integration.id) && (
                            <Button 
                              variant="outlined" 
                              size="small" 
                              onClick={() => handleOpenConfigDialog(integration)}
                              sx={{ mr: 2 }}
                              disabled={isIntegrationReadOnly(integration.id)}
                            >
                              Configure
                            </Button>
                          )}
                          
                          {/* Integration access is now controlled by roles and permissions */}
                          <Tooltip title="Access controlled by roles and permissions">
                            <Chip 
                              label="Active" 
                              color="success" 
                              size="small" 
                              icon={<CheckCircleIcon />} 
                            />
                          </Tooltip>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                ))}
              </List>
            </>
          )}
        </Paper>
      </Box>
      
      {/* Global loading backdrop for provisioning operations */}
      <Backdrop
        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={provisioningLoading}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <CircularProgress color="inherit" />
          <Typography variant="h6" sx={{ mt: 2 }}>
            {provisioningIntegration ? `Processing ${provisioningIntegration}...` : 'Processing...'}
          </Typography>
        </Box>
      </Backdrop>

      {/* Configuration Dialog */}
      <Dialog
        open={configDialogOpen}
        onClose={handleCloseConfigDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedIntegration?.name} Configuration
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure your personal settings for {selectedIntegration?.name}.
          </Typography>

          {configLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : configError ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              {configError}
            </Alert>
          ) : configOptions.length === 0 ? (
            <>
              <Alert severity="info" sx={{ mb: 2 }}>
                No configuration options are available for this integration.
              </Alert>
              <Button 
                variant="contained" 
                color="primary"
                href={`/${selectedIntegration?.id}`}
              >
                Go to {selectedIntegration?.name} Page
              </Button>
            </>
          ) : (
            <Box sx={{ mt: 2 }}>
              {configOptions.map((option, index) => (
                <Accordion key={option.id || index} defaultExpanded={index === 0}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">{option.title || 'Configuration'}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {option.description}
                    </Typography>

                    <Grid container spacing={2}>
                      {option.fields && option.fields.map((field) => (
                        <Grid item xs={12} sm={field.fullWidth ? 12 : 6} key={field.id}>
                          {field.type === 'select' ? (
                            <FormControl fullWidth variant="outlined" size="small">
                              <InputLabel id={`${field.id}-label`}>{field.label}</InputLabel>
                              <Select
                                labelId={`${field.id}-label`}
                                id={field.id}
                                value={userConfig[field.id] || ''}
                                onChange={(e) => handleConfigFieldChange(field.id, e.target.value)}
                                label={field.label}
                              >
                                {field.options && field.options.map((option) => (
                                  <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                  </MenuItem>
                                ))}
                              </Select>
                              {field.helperText && (
                                <Typography variant="caption" color="text.secondary">
                                  {field.helperText}
                                </Typography>
                              )}
                            </FormControl>
                          ) : field.type === 'boolean' ? (
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={Boolean(userConfig[field.id])}
                                  onChange={(e) => handleConfigFieldChange(field.id, e.target.checked)}
                                />
                              }
                              label={field.label}
                            />
                          ) : (
                            <TextField
                              id={field.id}
                              label={field.label}
                              type={field.type || 'text'}
                              value={userConfig[field.id] || ''}
                              onChange={(e) => handleConfigFieldChange(field.id, e.target.value)}
                              fullWidth
                              variant="outlined"
                              size="small"
                              helperText={field.helperText}
                              multiline={field.multiline}
                              rows={field.rows || 1}
                            />
                          )}
                        </Grid>
                      ))}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfigDialog}>Cancel</Button>
          {configOptions.length > 0 && !configLoading && !configError && (
            <Button onClick={handleSaveConfig} color="primary" variant="contained">
              Save Configuration
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default UserIntegrationsPage;
