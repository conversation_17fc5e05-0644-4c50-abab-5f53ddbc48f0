import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  AvatarGroup
} from '@mui/material';
import {
  Edit as EditIcon,
  Visibility as ViewIcon,
  Assignment as AssignmentIcon,
  Build as BuildIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status) {
    case 'open':
      return 'info';
    case 'in_progress':
      return 'warning';
    case 'on_hold':
      return 'default';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

// Helper function to get priority color
const getPriorityColor = (priority) => {
  switch (priority) {
    case 'low':
      return 'success';
    case 'medium':
      return 'info';
    case 'high':
      return 'warning';
    case 'critical':
      return 'error';
    default:
      return 'default';
  }
};

// Helper function to format date
const formatDate = (date) => {
  if (!date) return 'N/A';
  return format(new Date(date), 'MMM d, yyyy');
};

const TaskList = ({ tasks, onTaskUpdated }) => {
  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }}>
        <TableHead>
          <TableRow>
            <TableCell>Title</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Priority</TableCell>
            <TableCell>Assigned To</TableCell>
            <TableCell>Due Date</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {tasks.map((task) => (
            <TableRow key={task._id} hover>
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {task.taskType === 'maintenance' ? (
                    <BuildIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  ) : (
                    <AssignmentIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  )}
                  <Box>
                    <Typography variant="body1" component="div">
                      {task.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Created {formatDate(task.createdAt)} by {task.createdBy?.name || 'Unknown'}
                    </Typography>
                  </Box>
                </Box>
              </TableCell>
              <TableCell>
                <Chip
                  label={task.taskType === 'maintenance' ? 'Maintenance' : 'General'}
                  size="small"
                  color={task.taskType === 'maintenance' ? 'secondary' : 'primary'}
                  variant="outlined"
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={task.status.replace('_', ' ').split(' ').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)
                  ).join(' ')}
                  size="small"
                  color={getStatusColor(task.status)}
                />
              </TableCell>
              <TableCell>
                <Chip
                  label={task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                  size="small"
                  color={getPriorityColor(task.priority)}
                />
              </TableCell>
              <TableCell>
                {task.assignedTo ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      src={task.assignedTo.avatar}
                      alt={task.assignedTo.name}
                      sx={{ width: 24, height: 24, mr: 1 }}
                    />
                    <Typography variant="body2">
                      {task.assignedTo.name}
                    </Typography>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Unassigned
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                {task.dueDate ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ScheduleIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      {formatDate(task.dueDate)}
                    </Typography>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No due date
                  </Typography>
                )}
              </TableCell>
              <TableCell>
                <Tooltip title="View">
                  <IconButton
                    size="small"
                    component={RouterLink}
                    to={`/tasks/${task._id}`}
                  >
                    <ViewIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Edit">
                  <IconButton
                    size="small"
                    component={RouterLink}
                    to={`/tasks/${task._id}/edit`}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default TaskList;