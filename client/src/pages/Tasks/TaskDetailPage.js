import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Paper,
  Chip,
  Divider,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  TextField,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Send as SendIcon,
  Assignment as AssignmentIcon,
  Build as BuildIcon,
  AttachFile as AttachFileIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Comment as CommentIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import taskService from '../../services/taskService';
import userService from '../../services/userService';
import { useAuth } from '../../context/AuthContext';

// Helper function to format date
const formatDate = (date) => {
  if (!date) return 'N/A';
  return format(new Date(date), 'MMM d, yyyy h:mm a');
};

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status) {
    case 'open':
      return 'info';
    case 'in_progress':
      return 'warning';
    case 'on_hold':
      return 'default';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

// Helper function to get priority color
const getPriorityColor = (priority) => {
  switch (priority) {
    case 'low':
      return 'success';
    case 'medium':
      return 'info';
    case 'high':
      return 'warning';
    case 'critical':
      return 'error';
    default:
      return 'default';
  }
};

const TaskDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // State
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [comment, setComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [statusComment, setStatusComment] = useState('');
  const [submittingStatus, setSubmittingStatus] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState('');
  const [users, setUsers] = useState([]);
  const [submittingAssign, setSubmittingAssign] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [submittingDelete, setSubmittingDelete] = useState(false);
  const [attachmentDialogOpen, setAttachmentDialogOpen] = useState(false);
  const [attachment, setAttachment] = useState({
    name: '',
    url: '',
    type: ''
  });
  const [submittingAttachment, setSubmittingAttachment] = useState(false);

  // Fetch task data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch task
        const taskData = await taskService.getTaskById(id);
        setTask(taskData);
        
        // Fetch users for assignment
        const usersData = await userService.getAllUsers();
        setUsers(usersData);
      } catch (err) {
        console.error('Error fetching task:', err);
        setError('Failed to load task. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle comment input change
  const handleCommentChange = (e) => {
    setComment(e.target.value);
  };

  // Handle comment submission
  const handleSubmitComment = async () => {
    if (!comment.trim()) return;
    
    try {
      setSubmittingComment(true);
      
      await taskService.addComment(id, { text: comment });
      
      // Refresh task data
      const updatedTask = await taskService.getTaskById(id);
      setTask(updatedTask);
      
      // Clear comment field
      setComment('');
    } catch (err) {
      console.error('Error submitting comment:', err);
      setError('Failed to submit comment. Please try again.');
    } finally {
      setSubmittingComment(false);
    }
  };

  // Handle status dialog open
  const handleOpenStatusDialog = () => {
    setNewStatus(task.status);
    setStatusComment('');
    setStatusDialogOpen(true);
  };

  // Handle status dialog close
  const handleCloseStatusDialog = () => {
    setStatusDialogOpen(false);
  };

  // Handle status change
  const handleStatusChange = (e) => {
    setNewStatus(e.target.value);
  };

  // Handle status comment change
  const handleStatusCommentChange = (e) => {
    setStatusComment(e.target.value);
  };

  // Handle status update
  const handleUpdateStatus = async () => {
    try {
      setSubmittingStatus(true);
      
      await taskService.updateTaskStatus(id, newStatus, statusComment);
      
      // Refresh task data
      const updatedTask = await taskService.getTaskById(id);
      setTask(updatedTask);
      
      // Close dialog
      setStatusDialogOpen(false);
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update status. Please try again.');
    } finally {
      setSubmittingStatus(false);
    }
  };

  // Handle assign dialog open
  const handleOpenAssignDialog = () => {
    setSelectedUser(task.assignedTo?._id || '');
    setAssignDialogOpen(true);
  };

  // Handle assign dialog close
  const handleCloseAssignDialog = () => {
    setAssignDialogOpen(false);
  };

  // Handle user selection
  const handleUserChange = (e) => {
    setSelectedUser(e.target.value);
  };

  // Handle task assignment
  const handleAssignTask = async () => {
    try {
      setSubmittingAssign(true);
      
      await taskService.assignTask(id, selectedUser);
      
      // Refresh task data
      const updatedTask = await taskService.getTaskById(id);
      setTask(updatedTask);
      
      // Close dialog
      setAssignDialogOpen(false);
    } catch (err) {
      console.error('Error assigning task:', err);
      setError('Failed to assign task. Please try again.');
    } finally {
      setSubmittingAssign(false);
    }
  };

  // Handle delete dialog open
  const handleOpenDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };

  // Handle delete dialog close
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  // Handle task deletion
  const handleDeleteTask = async () => {
    try {
      setSubmittingDelete(true);
      
      await taskService.deleteTask(id);
      
      // Navigate back to tasks list
      navigate('/tasks');
    } catch (err) {
      console.error('Error deleting task:', err);
      setError('Failed to delete task. Please try again.');
      setSubmittingDelete(false);
      setDeleteDialogOpen(false);
    }
  };

  // Handle attachment dialog open
  const handleOpenAttachmentDialog = () => {
    setAttachment({
      name: '',
      url: '',
      type: ''
    });
    setAttachmentDialogOpen(true);
  };

  // Handle attachment dialog close
  const handleCloseAttachmentDialog = () => {
    setAttachmentDialogOpen(false);
  };

  // Handle attachment input change
  const handleAttachmentChange = (e) => {
    const { name, value } = e.target;
    setAttachment(prev => ({ ...prev, [name]: value }));
  };

  // Handle attachment submission
  const handleSubmitAttachment = async () => {
    if (!attachment.name.trim() || !attachment.url.trim()) return;
    
    try {
      setSubmittingAttachment(true);
      
      await taskService.addAttachment(id, attachment);
      
      // Refresh task data
      const updatedTask = await taskService.getTaskById(id);
      setTask(updatedTask);
      
      // Close dialog
      setAttachmentDialogOpen(false);
    } catch (err) {
      console.error('Error adding attachment:', err);
      setError('Failed to add attachment. Please try again.');
    } finally {
      setSubmittingAttachment(false);
    }
  };

  // Handle checklist item toggle
  const handleToggleChecklistItem = async (index) => {
    if (!task || task.taskType !== 'maintenance') return;
    
    try {
      const updatedChecklist = [...task.checklist];
      updatedChecklist[index] = {
        ...updatedChecklist[index],
        completed: !updatedChecklist[index].completed,
        completedBy: !updatedChecklist[index].completed ? user.id : null,
        completedAt: !updatedChecklist[index].completed ? new Date() : null
      };
      
      await taskService.updateTask(id, { checklist: updatedChecklist });
      
      // Refresh task data
      const updatedTask = await taskService.getTaskById(id);
      setTask(updatedTask);
    } catch (err) {
      console.error('Error updating checklist:', err);
      setError('Failed to update checklist. Please try again.');
    }
  };

  // Handle back button
  const handleBack = () => {
    navigate('/tasks');
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
        >
          Back to Tasks
        </Button>
      </Container>
    );
  }

  if (!task) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Alert severity="warning" sx={{ mb: 3 }}>
          Task not found
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
        >
          Back to Tasks
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          {task.taskType === 'maintenance' ? (
            <BuildIcon sx={{ mr: 1, color: 'text.secondary' }} />
          ) : (
            <AssignmentIcon sx={{ mr: 1, color: 'text.secondary' }} />
          )}
          <Typography variant="h5" component="h1" sx={{ flexGrow: 1 }}>
            {task.title}
          </Typography>
          <Box>
            <Tooltip title="Edit Task">
              <IconButton 
                component={RouterLink} 
                to={`/tasks/${id}/edit`}
                sx={{ mr: 1 }}
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete Task">
              <IconButton 
                color="error" 
                onClick={handleOpenDeleteDialog}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Typography variant="h6" gutterBottom>
              Description
            </Typography>
            <Typography variant="body1" paragraph>
              {task.description || 'No description provided.'}
            </Typography>

            <Box sx={{ mt: 3, mb: 2 }}>
              <Chip
                label={task.taskType === 'maintenance' ? 'Maintenance Task' : 'General Task'}
                color={task.taskType === 'maintenance' ? 'secondary' : 'primary'}
                variant="outlined"
                sx={{ mr: 1 }}
              />
              <Chip
                label={task.status.replace('_', ' ').split(' ').map(word => 
                  word.charAt(0).toUpperCase() + word.slice(1)
                ).join(' ')}
                color={getStatusColor(task.status)}
                sx={{ mr: 1 }}
              />
              <Chip
                label={task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                color={getPriorityColor(task.priority)}
                sx={{ mr: 1 }}
              />
              {task.description && task.description.includes('Form response from') && (
                <Tooltip title="This task was created from a Google Forms response">
                  <Chip
                    label="Google Forms"
                    color="info"
                    variant="outlined"
                    component={RouterLink}
                    to="/google-forms"
                    clickable
                  />
                </Tooltip>
              )}
            </Box>

            {task.tags && task.tags.length > 0 && (
              <Box sx={{ mt: 2 }}>
                {task.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    size="small"
                    sx={{ mr: 0.5, mb: 0.5 }}
                  />
                ))}
              </Box>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Status
                </Typography>
                <Chip
                  label={task.status.replace('_', ' ').split(' ').map(word => 
                    word.charAt(0).toUpperCase() + word.slice(1)
                  ).join(' ')}
                  color={getStatusColor(task.status)}
                  sx={{ mb: 2 }}
                />
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={handleOpenStatusDialog}
                >
                  Update Status
                </Button>
              </CardContent>
            </Card>

            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Assignment
                </Typography>
                {task.assignedTo ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ mr: 1 }}>
                      {task.assignedTo.name.charAt(0)}
                    </Avatar>
                    <Typography>
                      {task.assignedTo.name}
                    </Typography>
                  </Box>
                ) : (
                  <Typography color="text.secondary" sx={{ mb: 2 }}>
                    Unassigned
                  </Typography>
                )}
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={handleOpenAssignDialog}
                >
                  {task.assignedTo ? 'Reassign' : 'Assign'}
                </Button>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  Dates
                </Typography>
                <Box sx={{ mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Created:
                  </Typography>
                  <Typography variant="body2">
                    {formatDate(task.createdAt)}
                  </Typography>
                </Box>
                {task.dueDate && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      Due:
                    </Typography>
                    <Typography variant="body2">
                      {formatDate(task.dueDate)}
                    </Typography>
                  </Box>
                )}
                {task.completedAt && (
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Completed:
                    </Typography>
                    <Typography variant="body2">
                      {formatDate(task.completedAt)}
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Maintenance-specific details */}
        {task.taskType === 'maintenance' && (
          <Box sx={{ mt: 3 }}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="h6" gutterBottom>
              Maintenance Details
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Category & Type
                    </Typography>
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Category:
                      </Typography>
                      <Typography variant="body2">
                        {task.category ? task.category.charAt(0).toUpperCase() + task.category.slice(1) : 'N/A'}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Maintenance Type:
                      </Typography>
                      <Typography variant="body2">
                        {task.maintenanceType ? task.maintenanceType.charAt(0).toUpperCase() + task.maintenanceType.slice(1) : 'N/A'}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>
                      Location
                    </Typography>
                    {task.location && (task.location.building || task.location.floor || task.location.room || task.location.description) ? (
                      <>
                        {task.location.building && (
                          <Box sx={{ mb: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Building:
                            </Typography>
                            <Typography variant="body2">
                              {task.location.building}
                            </Typography>
                          </Box>
                        )}
                        {task.location.floor && (
                          <Box sx={{ mb: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Floor:
                            </Typography>
                            <Typography variant="body2">
                              {task.location.floor}
                            </Typography>
                          </Box>
                        )}
                        {task.location.room && (
                          <Box sx={{ mb: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Room:
                            </Typography>
                            <Typography variant="body2">
                              {task.location.room}
                            </Typography>
                          </Box>
                        )}
                        {task.location.description && (
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Description:
                            </Typography>
                            <Typography variant="body2">
                              {task.location.description}
                            </Typography>
                          </Box>
                        )}
                      </>
                    ) : (
                      <Typography color="text.secondary">
                        No location information provided
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
              
              {task.equipment && (task.equipment.name || task.equipment.model || task.equipment.serialNumber || task.equipment.assetTag) && (
                <Grid item xs={12}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>Equipment Information</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2}>
                        {task.equipment.name && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2" color="text.secondary">
                              Name:
                            </Typography>
                            <Typography variant="body2">
                              {task.equipment.name}
                            </Typography>
                          </Grid>
                        )}
                        {task.equipment.model && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2" color="text.secondary">
                              Model:
                            </Typography>
                            <Typography variant="body2">
                              {task.equipment.model}
                            </Typography>
                          </Grid>
                        )}
                        {task.equipment.serialNumber && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2" color="text.secondary">
                              Serial Number:
                            </Typography>
                            <Typography variant="body2">
                              {task.equipment.serialNumber}
                            </Typography>
                          </Grid>
                        )}
                        {task.equipment.assetTag && (
                          <Grid item xs={12} sm={6}>
                            <Typography variant="body2" color="text.secondary">
                              Asset Tag:
                            </Typography>
                            <Typography variant="body2">
                              {task.equipment.assetTag}
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              )}
              
              {task.checklist && task.checklist.length > 0 && (
                <Grid item xs={12}>
                  <Accordion>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>
                        Completion Checklist ({task.checklist.filter(item => item.completed).length}/{task.checklist.length} completed)
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <List>
                        {task.checklist.map((item, index) => (
                          <ListItem key={index} dense>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  edge="start"
                                  checked={item.completed}
                                  onChange={() => handleToggleChecklistItem(index)}
                                />
                              }
                              label={
                                <Box>
                                  <Typography variant="body2">
                                    {item.item}
                                  </Typography>
                                  {item.completed && (
                                    <Typography variant="caption" color="text.secondary">
                                      Completed {item.completedAt ? formatDate(item.completedAt) : 'N/A'}
                                      {item.completedBy ? ` by ${item.completedBy.name || 'Unknown'}` : ''}
                                    </Typography>
                                  )}
                                </Box>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                </Grid>
              )}
            </Grid>
          </Box>
        )}

        <Divider sx={{ my: 3 }} />

        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="task details tabs">
              <Tab label="Comments" icon={<CommentIcon />} iconPosition="start" />
              <Tab label="Attachments" icon={<AttachFileIcon />} iconPosition="start" />
              <Tab label="Activity" icon={<ScheduleIcon />} iconPosition="start" />
            </Tabs>
          </Box>
          
          {/* Comments Tab */}
          {tabValue === 0 && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Add a comment..."
                  value={comment}
                  onChange={handleCommentChange}
                  variant="outlined"
                  disabled={submittingComment}
                />
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<SendIcon />}
                    onClick={handleSubmitComment}
                    disabled={!comment.trim() || submittingComment}
                  >
                    {submittingComment ? <CircularProgress size={24} /> : 'Add Comment'}
                  </Button>
                </Box>
              </Box>
              
              {task.comments && task.comments.length > 0 ? (
                <List>
                  {task.comments.map((comment, index) => (
                    <ListItem key={index} alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar>
                          {comment.user?.name?.charAt(0) || 'U'}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="subtitle2">
                              {comment.user?.name || 'Unknown User'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatDate(comment.date)}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Typography
                            variant="body2"
                            color="text.primary"
                            sx={{ mt: 1, whiteSpace: 'pre-wrap' }}
                          >
                            {comment.text}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" align="center">
                  No comments yet
                </Typography>
              )}
            </Box>
          )}
          
          {/* Attachments Tab */}
          {tabValue === 1 && (
            <Box>
              <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AttachFileIcon />}
                  onClick={handleOpenAttachmentDialog}
                >
                  Add Attachment
                </Button>
              </Box>
              
              {task.attachments && task.attachments.length > 0 ? (
                <Grid container spacing={2}>
                  {task.attachments.map((attachment, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle1" gutterBottom>
                            {attachment.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block">
                            Uploaded by {attachment.uploadedBy?.name || 'Unknown'} on {formatDate(attachment.uploadedAt)}
                          </Typography>
                        </CardContent>
                        <CardActions>
                          <Button 
                            size="small" 
                            href={attachment.url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                          >
                            Open
                          </Button>
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography color="text.secondary" align="center">
                  No attachments yet
                </Typography>
              )}
            </Box>
          )}
          
          {/* Activity Tab */}
          {tabValue === 2 && (
            <Box>
              <List>
                {/* Created activity */}
                <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar>
                      {task.createdBy?.name?.charAt(0) || 'U'}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                        <Typography variant="subtitle2">
                          {task.createdBy?.name || 'Unknown User'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(task.createdAt)}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Typography variant="body2" color="text.primary" sx={{ mt: 1 }}>
                        {task.description && task.description.includes('Form response from') ? (
                          <>
                            Created this task from a Google Forms response
                            {task.description.includes('Form response from ') && (
                              <>
                                {' '}({task.description.split('Form response from ')[1]})
                              </>
                            )}
                            <Button
                              component={RouterLink}
                              to="/google-forms"
                              size="small"
                              sx={{ ml: 1, mt: 0.5 }}
                            >
                              View Forms
                            </Button>
                          </>
                        ) : (
                          'Created this task'
                        )}
                      </Typography>
                    }
                  />
                </ListItem>
                
                {/* Assigned activity */}
                {task.assignedTo && (
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="subtitle2">
                            System
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {/* Assuming the last update time */}
                            {formatDate(task.updatedAt)}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" color="text.primary" sx={{ mt: 1 }}>
                          Assigned to {task.assignedTo.name}
                        </Typography>
                      }
                    />
                  </ListItem>
                )}
                
                {/* Completed activity */}
                {task.completedAt && (
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar>
                        {task.completedBy?.name?.charAt(0) || 'U'}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="subtitle2">
                            {task.completedBy?.name || 'Unknown User'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatDate(task.completedAt)}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" color="text.primary" sx={{ mt: 1 }}>
                          Marked this task as completed
                        </Typography>
                      }
                    />
                  </ListItem>
                )}
              </List>
            </Box>
          )}
        </Box>
      </Paper>

      {/* Status Update Dialog */}
      <Dialog open={statusDialogOpen} onClose={handleCloseStatusDialog}>
        <DialogTitle>Update Task Status</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Change the status of this task and add a comment about the update.
          </DialogContentText>
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={newStatus}
              onChange={handleStatusChange}
              label="Status"
            >
              <MenuItem value="open">Open</MenuItem>
              <MenuItem value="in_progress">In Progress</MenuItem>
              <MenuItem value="on_hold">On Hold</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
              <MenuItem value="cancelled">Cancelled</MenuItem>
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="Comment"
            multiline
            rows={3}
            value={statusComment}
            onChange={handleStatusCommentChange}
            variant="outlined"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseStatusDialog}>Cancel</Button>
          <Button 
            onClick={handleUpdateStatus} 
            variant="contained" 
            color="primary"
            disabled={submittingStatus}
          >
            {submittingStatus ? <CircularProgress size={24} /> : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Assign Task Dialog */}
      <Dialog open={assignDialogOpen} onClose={handleCloseAssignDialog}>
        <DialogTitle>Assign Task</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Select a user to assign this task to.
          </DialogContentText>
          <FormControl fullWidth>
            <InputLabel>Assign To</InputLabel>
            <Select
              value={selectedUser}
              onChange={handleUserChange}
              label="Assign To"
            >
              <MenuItem value="">Unassigned</MenuItem>
              {users.map(user => (
                <MenuItem key={user._id} value={user._id}>
                  {user.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAssignDialog}>Cancel</Button>
          <Button 
            onClick={handleAssignTask} 
            variant="contained" 
            color="primary"
            disabled={submittingAssign}
          >
            {submittingAssign ? <CircularProgress size={24} /> : 'Assign'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Task Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Task</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this task? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button 
            onClick={handleDeleteTask} 
            variant="contained" 
            color="error"
            disabled={submittingDelete}
          >
            {submittingDelete ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Attachment Dialog */}
      <Dialog open={attachmentDialogOpen} onClose={handleCloseAttachmentDialog}>
        <DialogTitle>Add Attachment</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Add a link to an attachment for this task.
          </DialogContentText>
          <TextField
            fullWidth
            label="Name"
            name="name"
            value={attachment.name}
            onChange={handleAttachmentChange}
            variant="outlined"
            sx={{ mb: 2 }}
            required
          />
          <TextField
            fullWidth
            label="URL"
            name="url"
            value={attachment.url}
            onChange={handleAttachmentChange}
            variant="outlined"
            sx={{ mb: 2 }}
            required
          />
          <TextField
            fullWidth
            label="Type (optional)"
            name="type"
            value={attachment.type}
            onChange={handleAttachmentChange}
            variant="outlined"
            placeholder="e.g., PDF, Image, Document"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAttachmentDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitAttachment} 
            variant="contained" 
            color="primary"
            disabled={submittingAttachment || !attachment.name.trim() || !attachment.url.trim()}
          >
            {submittingAttachment ? <CircularProgress size={24} /> : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TaskDetailPage;