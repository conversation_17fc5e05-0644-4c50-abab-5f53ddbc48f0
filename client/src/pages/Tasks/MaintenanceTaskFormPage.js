import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Checkbox,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import taskService from '../../services/taskService';
import userService from '../../services/userService';
import { useAuth } from '../../context/AuthContext';

const MaintenanceTaskFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isEditMode = Boolean(id);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    status: 'open',
    dueDate: null,
    assignedTo: '',
    tags: [],
    // Maintenance specific fields
    location: {
      building: '',
      floor: '',
      room: '',
      description: ''
    },
    category: 'other',
    equipment: {
      name: '',
      model: '',
      serialNumber: '',
      assetTag: ''
    },
    maintenanceType: 'corrective',
    estimatedDuration: '',
    safetyRequirements: '',
    specialInstructions: '',
    approval: {
      isRequired: false,
      notes: ''
    },
    checklist: []
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [users, setUsers] = useState([]);
  const [priorities, setPriorities] = useState([]);
  const [statuses, setStatuses] = useState([]);
  const [newTag, setNewTag] = useState('');
  const [newChecklistItem, setNewChecklistItem] = useState('');
  const [buildings, setBuildings] = useState([]);
  const [floors, setFloors] = useState([]);
  const [rooms, setRooms] = useState([]);

  // Maintenance categories
  const maintenanceCategories = [
    'electrical', 'plumbing', 'hvac', 'structural', 'cleaning', 'equipment', 'other'
  ];

  // Maintenance types
  const maintenanceTypes = [
    'preventive', 'corrective', 'emergency', 'inspection'
  ];

  // Load task data if in edit mode
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch users for assignment
        const usersData = await userService.getAllUsers();
        setUsers(usersData);

        // Fetch task priorities and statuses
        const prioritiesData = await taskService.getTaskPriorities();
        const statusesData = await taskService.getTaskStatuses();
        setPriorities(prioritiesData);
        setStatuses(statusesData);

        // TODO: Fetch buildings, floors, rooms from API
        // For now, using placeholder data
        setBuildings([
          { _id: 'building1', name: 'Main Building' },
          { _id: 'building2', name: 'Annex' }
        ]);
        setFloors([
          { _id: 'floor1', name: '1st Floor', building: 'building1' },
          { _id: 'floor2', name: '2nd Floor', building: 'building1' },
          { _id: 'floor3', name: 'Basement', building: 'building2' }
        ]);
        setRooms([
          { _id: 'room1', name: 'Room 101', floor: 'floor1' },
          { _id: 'room2', name: 'Room 102', floor: 'floor1' },
          { _id: 'room3', name: 'Room 201', floor: 'floor2' }
        ]);

        // If in edit mode, fetch task data
        if (isEditMode) {
          const taskData = await taskService.getTaskById(id);

          // Only allow editing maintenance tasks in this form
          if (taskData.taskType !== 'maintenance') {
            setError('This form is only for maintenance tasks. Please use the appropriate form for this task type.');
            return;
          }

          setFormData({
            title: taskData.title || '',
            description: taskData.description || '',
            priority: taskData.priority || 'medium',
            status: taskData.status || 'open',
            dueDate: taskData.dueDate ? new Date(taskData.dueDate) : null,
            assignedTo: taskData.assignedTo?._id || '',
            tags: taskData.tags || [],
            // Maintenance specific fields
            location: {
              building: taskData.location?.building || '',
              floor: taskData.location?.floor || '',
              room: taskData.location?.room || '',
              description: taskData.location?.description || ''
            },
            category: taskData.category || 'other',
            equipment: {
              name: taskData.equipment?.name || '',
              model: taskData.equipment?.model || '',
              serialNumber: taskData.equipment?.serialNumber || '',
              assetTag: taskData.equipment?.assetTag || ''
            },
            maintenanceType: taskData.maintenanceType || 'corrective',
            estimatedDuration: taskData.estimatedDuration || '',
            safetyRequirements: taskData.safetyRequirements || '',
            specialInstructions: taskData.specialInstructions || '',
            approval: {
              isRequired: taskData.approval?.isRequired || false,
              notes: taskData.approval?.notes || ''
            },
            checklist: taskData.checklist || []
          });
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, isEditMode]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle nested fields
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;

    // Handle nested fields
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: checked
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: checked }));
    }
  };

  // Handle date change
  const handleDateChange = (date) => {
    setFormData(prev => ({ ...prev, dueDate: date }));
  };

  // Handle tag input
  const handleTagInputChange = (e) => {
    setNewTag(e.target.value);
  };

  // Add a new tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  // Remove a tag
  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  // Handle checklist item input
  const handleChecklistItemChange = (e) => {
    setNewChecklistItem(e.target.value);
  };

  // Add a new checklist item
  const handleAddChecklistItem = () => {
    if (newChecklistItem.trim()) {
      setFormData(prev => ({
        ...prev,
        checklist: [
          ...prev.checklist,
          {
            item: newChecklistItem.trim(),
            completed: false
          }
        ]
      }));
      setNewChecklistItem('');
    }
  };

  // Remove a checklist item
  const handleRemoveChecklistItem = (index) => {
    setFormData(prev => ({
      ...prev,
      checklist: prev.checklist.filter((_, i) => i !== index)
    }));
  };

  // Toggle checklist item completion
  const handleToggleChecklistItem = (index) => {
    setFormData(prev => ({
      ...prev,
      checklist: prev.checklist.map((item, i) => 
        i === index ? { ...item, completed: !item.completed } : item
      )
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      setError(null);
      setSuccess(false);

      const taskData = {
        ...formData,
        taskType: 'maintenance'
      };

      let result;
      if (isEditMode) {
        result = await taskService.updateTask(id, taskData);
      } else {
        result = await taskService.createTask(taskData);
      }

      setSuccess(true);

      // Navigate back to tasks list or task detail after short delay
      setTimeout(() => {
        navigate(isEditMode ? `/tasks/${id}` : '/tasks');
      }, 1500);
    } catch (err) {
      console.error('Error saving task:', err);
      setError('Failed to save task. Please check your input and try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(isEditMode ? `/tasks/${id}` : '/tasks');
  };

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <BuildIcon sx={{ mr: 1 }} />
          <Typography variant="h5" component="h1">
            {isEditMode ? 'Edit Maintenance Task' : 'Create Maintenance Task'}
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Maintenance task {isEditMode ? 'updated' : 'created'} successfully!
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Task Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <TextField
                name="title"
                label="Task Title"
                value={formData.title}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                error={formData.title.trim() === ''}
                helperText={formData.title.trim() === '' ? 'Title is required' : ''}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                name="description"
                label="Description"
                value={formData.description}
                onChange={handleChange}
                fullWidth
                multiline
                rows={4}
                variant="outlined"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Priority</InputLabel>
                <Select
                  name="priority"
                  value={formData.priority}
                  onChange={handleChange}
                  label="Priority"
                >
                  {priorities.map(priority => (
                    <MenuItem key={priority} value={priority}>
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label="Status"
                >
                  {statuses.map(status => (
                    <MenuItem key={status} value={status}>
                      {status.replace('_', ' ').split(' ').map(word => 
                        word.charAt(0).toUpperCase() + word.slice(1)
                      ).join(' ')}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Due Date"
                  value={formData.dueDate}
                  onChange={handleDateChange}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: 'outlined'
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Assigned To</InputLabel>
                <Select
                  name="assignedTo"
                  value={formData.assignedTo}
                  onChange={handleChange}
                  label="Assigned To"
                >
                  <MenuItem value="">Unassigned</MenuItem>
                  {users.map(user => (
                    <MenuItem key={user._id} value={user._id}>
                      {user.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Location Information */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Location Information
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Building</InputLabel>
                <Select
                  name="location.building"
                  value={formData.location.building}
                  onChange={handleChange}
                  label="Building"
                >
                  <MenuItem value="">Select Building</MenuItem>
                  {buildings.map(building => (
                    <MenuItem key={building._id} value={building._id}>
                      {building.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Floor</InputLabel>
                <Select
                  name="location.floor"
                  value={formData.location.floor}
                  onChange={handleChange}
                  label="Floor"
                  disabled={!formData.location.building}
                >
                  <MenuItem value="">Select Floor</MenuItem>
                  {floors
                    .filter(floor => !formData.location.building || floor.building === formData.location.building)
                    .map(floor => (
                      <MenuItem key={floor._id} value={floor._id}>
                        {floor.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Room</InputLabel>
                <Select
                  name="location.room"
                  value={formData.location.room}
                  onChange={handleChange}
                  label="Room"
                  disabled={!formData.location.floor}
                >
                  <MenuItem value="">Select Room</MenuItem>
                  {rooms
                    .filter(room => !formData.location.floor || room.floor === formData.location.floor)
                    .map(room => (
                      <MenuItem key={room._id} value={room._id}>
                        {room.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                name="location.description"
                label="Location Description"
                value={formData.location.description}
                onChange={handleChange}
                fullWidth
                variant="outlined"
                placeholder="Additional location details"
              />
            </Grid>

            {/* Maintenance Details */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Maintenance Details
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  label="Category"
                  required
                >
                  {maintenanceCategories.map(category => (
                    <MenuItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>Maintenance Type</InputLabel>
                <Select
                  name="maintenanceType"
                  value={formData.maintenanceType}
                  onChange={handleChange}
                  label="Maintenance Type"
                >
                  {maintenanceTypes.map(type => (
                    <MenuItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                name="estimatedDuration"
                label="Estimated Duration (minutes)"
                value={formData.estimatedDuration}
                onChange={handleChange}
                type="number"
                fullWidth
                variant="outlined"
                inputProps={{ min: 0 }}
              />
            </Grid>

            {/* Equipment Information */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>Equipment Information</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="equipment.name"
                        label="Equipment Name"
                        value={formData.equipment.name}
                        onChange={handleChange}
                        fullWidth
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="equipment.model"
                        label="Model"
                        value={formData.equipment.model}
                        onChange={handleChange}
                        fullWidth
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="equipment.serialNumber"
                        label="Serial Number"
                        value={formData.equipment.serialNumber}
                        onChange={handleChange}
                        fullWidth
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        name="equipment.assetTag"
                        label="Asset Tag"
                        value={formData.equipment.assetTag}
                        onChange={handleChange}
                        fullWidth
                        variant="outlined"
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Safety and Instructions */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>Safety & Instructions</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <TextField
                        name="safetyRequirements"
                        label="Safety Requirements"
                        value={formData.safetyRequirements}
                        onChange={handleChange}
                        fullWidth
                        multiline
                        rows={3}
                        variant="outlined"
                        placeholder="List any safety requirements or precautions"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        name="specialInstructions"
                        label="Special Instructions"
                        value={formData.specialInstructions}
                        onChange={handleChange}
                        fullWidth
                        multiline
                        rows={3}
                        variant="outlined"
                        placeholder="Any special instructions for completing this task"
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Approval */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>Approval Requirements</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={formData.approval.isRequired}
                            onChange={handleCheckboxChange}
                            name="approval.isRequired"
                          />
                        }
                        label="Requires Approval"
                      />
                    </Grid>
                    {formData.approval.isRequired && (
                      <Grid item xs={12}>
                        <TextField
                          name="approval.notes"
                          label="Approval Notes"
                          value={formData.approval.notes}
                          onChange={handleChange}
                          fullWidth
                          multiline
                          rows={2}
                          variant="outlined"
                          placeholder="Notes about approval requirements"
                        />
                      </Grid>
                    )}
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Checklist */}
            <Grid item xs={12}>
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>Completion Checklist</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Add items that need to be checked off when completing this task
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <TextField
                        value={newChecklistItem}
                        onChange={handleChecklistItemChange}
                        placeholder="Add checklist item"
                        size="small"
                        sx={{ mr: 1, flexGrow: 1 }}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddChecklistItem();
                          }
                        }}
                      />
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        onClick={handleAddChecklistItem}
                        startIcon={<AddIcon />}
                      >
                        Add
                      </Button>
                    </Box>
                  </Box>

                  {formData.checklist.length > 0 ? (
                    <List>
                      {formData.checklist.map((item, index) => (
                        <ListItem key={index} dense>
                          <FormControlLabel
                            control={
                              <Checkbox
                                edge="start"
                                checked={item.completed}
                                onChange={() => handleToggleChecklistItem(index)}
                              />
                            }
                            label={item.item}
                          />
                          <ListItemSecondaryAction>
                            <IconButton 
                              edge="end" 
                              onClick={() => handleRemoveChecklistItem(index)}
                              size="small"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body2" color="text.secondary" align="center">
                      No checklist items added
                    </Typography>
                  )}
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Tags */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" gutterBottom>
                Tags
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TextField
                  value={newTag}
                  onChange={handleTagInputChange}
                  placeholder="Add a tag"
                  size="small"
                  sx={{ mr: 1 }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={handleAddTag}
                  startIcon={<AddIcon />}
                >
                  Add
                </Button>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    onDelete={() => handleRemoveTag(tag)}
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Grid>

            {/* Submit Buttons */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button
                  variant="outlined"
                  onClick={handleCancel}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={submitting || formData.title.trim() === ''}
                >
                  {submitting ? (
                    <CircularProgress size={24} />
                  ) : (
                    isEditMode ? 'Update Task' : 'Create Task'
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Container>
  );
};

export default MaintenanceTaskFormPage;
