import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Paper,
  Tabs,
  Tab,
  Chip,
  IconButton,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  FilterList as FilterListIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Assignment as AssignmentIcon,
  Build as BuildIcon
} from '@mui/icons-material';
import taskService from '../../services/taskService';
import { useAuth } from '../../context/AuthContext';
import TaskList from './components/TaskList';

const TasksPage = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [tabValue, setTabValue] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    taskType: '',
    search: ''
  });
  const [priorities, setPriorities] = useState([]);
  const [statuses, setStatuses] = useState([]);

  // Fetch tasks and filter options on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch tasks
        const tasksData = await taskService.getAllTasks();
        setTasks(tasksData);
        
        // Fetch filter options
        const prioritiesData = await taskService.getTaskPriorities();
        const statusesData = await taskService.getTaskStatuses();
        
        setPriorities(prioritiesData);
        setStatuses(statusesData);
      } catch (err) {
        console.error('Error fetching tasks:', err);
        setError('Failed to load tasks. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    
    // Apply filters based on tab
    let newFilters = { ...filters };
    
    switch (newValue) {
      case 0: // All tasks
        newFilters.status = '';
        break;
      case 1: // Open tasks
        newFilters.status = 'open';
        break;
      case 2: // In Progress tasks
        newFilters.status = 'in_progress';
        break;
      case 3: // Completed tasks
        newFilters.status = 'completed';
        break;
      default:
        newFilters.status = '';
    }
    
    setFilters(newFilters);
    applyFilters(newFilters);
  };

  // Toggle filter panel
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Handle filter changes
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    const newFilters = { ...filters, [name]: value };
    setFilters(newFilters);
  };

  // Apply filters
  const applyFilters = async (filterValues = filters) => {
    try {
      setLoading(true);
      setError(null);
      
      // Remove empty filters
      const activeFilters = Object.entries(filterValues)
        .reduce((acc, [key, value]) => {
          if (value !== '') {
            acc[key] = value;
          }
          return acc;
        }, {});
      
      const filteredTasks = await taskService.getTasksByFilter(activeFilters);
      setTasks(filteredTasks);
    } catch (err) {
      console.error('Error applying filters:', err);
      setError('Failed to filter tasks. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Reset filters
  const resetFilters = () => {
    const newFilters = {
      status: '',
      priority: '',
      taskType: '',
      search: ''
    };
    setFilters(newFilters);
    setTabValue(0);
    applyFilters(newFilters);
  };

  // Refresh tasks
  const refreshTasks = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const tasksData = await taskService.getAllTasks();
      setTasks(tasksData);
    } catch (err) {
      console.error('Error refreshing tasks:', err);
      setError('Failed to refresh tasks. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Task Management
        </Typography>
        <Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to="/tasks/new"
            sx={{ mr: 1 }}
          >
            New Task
          </Button>
          <Button
            variant="contained"
            color="secondary"
            startIcon={<BuildIcon />}
            component={RouterLink}
            to="/tasks/maintenance/new"
          >
            New Maintenance Task
          </Button>
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="All Tasks" />
          <Tab label="Open" />
          <Tab label="In Progress" />
          <Tab label="Completed" />
        </Tabs>
      </Paper>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TextField
              name="search"
              label="Search Tasks"
              variant="outlined"
              size="small"
              value={filters.search}
              onChange={handleFilterChange}
              sx={{ mr: 1, width: 250 }}
              InputProps={{
                endAdornment: (
                  <IconButton size="small" onClick={() => applyFilters()}>
                    <SearchIcon />
                  </IconButton>
                )
              }}
            />
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={toggleFilters}
              sx={{ mr: 1 }}
            >
              Filters
            </Button>
            <IconButton onClick={refreshTasks} color="primary">
              <RefreshIcon />
            </IconButton>
          </Box>
          <Box>
            {Object.values(filters).some(value => value !== '') && (
              <Button variant="text" color="primary" onClick={resetFilters}>
                Clear Filters
              </Button>
            )}
          </Box>
        </Box>

        {showFilters && (
          <Box sx={{ mb: 2 }}>
            <Divider sx={{ my: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth size="small">
                  <InputLabel>Priority</InputLabel>
                  <Select
                    name="priority"
                    value={filters.priority}
                    onChange={handleFilterChange}
                    label="Priority"
                  >
                    <MenuItem value="">All Priorities</MenuItem>
                    {priorities.map(priority => (
                      <MenuItem key={priority} value={priority}>
                        {priority.charAt(0).toUpperCase() + priority.slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={filters.status}
                    onChange={handleFilterChange}
                    label="Status"
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    {statuses.map(status => (
                      <MenuItem key={status} value={status}>
                        {status.replace('_', ' ').split(' ').map(word => 
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ')}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth size="small">
                  <InputLabel>Task Type</InputLabel>
                  <Select
                    name="taskType"
                    value={filters.taskType}
                    onChange={handleFilterChange}
                    label="Task Type"
                  >
                    <MenuItem value="">All Types</MenuItem>
                    <MenuItem value="general">General</MenuItem>
                    <MenuItem value="maintenance">Maintenance</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={() => applyFilters()}
              >
                Apply Filters
              </Button>
            </Box>
          </Box>
        )}
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : tasks.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <AssignmentIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No tasks found
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            {Object.values(filters).some(value => value !== '') 
              ? 'Try adjusting your filters or create a new task.'
              : 'Get started by creating your first task.'}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to="/tasks/new"
          >
            Create Task
          </Button>
        </Paper>
      ) : (
        <TaskList tasks={tasks} onTaskUpdated={refreshTasks} />
      )}
    </Container>
  );
};

export default TasksPage;