import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  PersonAdd as PersonAddIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import integrationService from '../services/integrationService';

// Import icons for integrations
import {
  Image as CanvaIcon,
  Computer as GLPIIcon,
  Event as PlanningCenterIcon,
  Storage as SynologyIcon,
  AcUnit as DreoIcon,
  Security as LenelS2NetBoxIcon,
  PhoneIphone as MosyleBusinessIcon,
  Lock as UnifiAccessIcon,
  Router as UnifiNetworkIcon,
  Videocam as UnifiProtectIcon,
  AdminPanelSettings as AdminIcon,
  CalendarMonth as GoogleCalendarIcon
} from '@mui/icons-material';

// Map of integration IDs to their icons
const integrationIcons = {
  'canva': <CanvaIcon />,
  'dreo': <DreoIcon />,
  'glpi': <GLPIIcon />,
  'google-admin': <AdminIcon />,
  'google-calendar': <GoogleCalendarIcon />,
  'lenel-s2-netbox': <LenelS2NetBoxIcon />,
  'mosyle-business': <MosyleBusinessIcon />,
  'planning-center': <PlanningCenterIcon />,
  'synology': <SynologyIcon />,
  'unifi-access': <UnifiAccessIcon />,
  'unifi-network': <UnifiNetworkIcon />,
  'unifi-protect': <UnifiProtectIcon />
};

const AdminIntegrationsPage = () => {
  const { user } = useAuth();
  const [integrations, setIntegrations] = useState([]);
  const [integrationSettings, setIntegrationSettings] = useState([]);
  const [integrationStatus, setIntegrationStatus] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedIntegration, setSelectedIntegration] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    isRequired: false,
    isReadOnly: false,
    useGlobalConfig: false,
    supportsProvisioning: false,
    provisioningEnabled: false,
    provisioningConfig: {}
  });

  // Fetch integrations, settings, and status on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [availableIntegrations, settings, status] = await Promise.all([
          integrationService.getAvailableIntegrations(),
          integrationService.getAllSettings(),
          integrationService.getIntegrationStatus()
        ]);
        setIntegrations(availableIntegrations);
        setIntegrationSettings(settings);
        setIntegrationStatus(status);
      } catch (err) {
        console.error('Error fetching integration data:', err);
        setError('Failed to load integration data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Open dialog to edit integration settings
  const handleOpenDialog = (integration) => {
    setSelectedIntegration(integration);
    
    // Find settings for this integration
    const settings = integrationSettings.find(s => s.integrationId === integration.id) || {
      integrationId: integration.id,
      isRequired: false,
      isReadOnly: false,
      useGlobalConfig: false,
      supportsProvisioning: false,
      provisioningEnabled: false,
      provisioningConfig: {}
    };
    
    setFormData({
      isRequired: settings.isRequired || false,
      isReadOnly: settings.isReadOnly || false,
      useGlobalConfig: settings.useGlobalConfig || false,
      supportsProvisioning: settings.supportsProvisioning || false,
      provisioningEnabled: settings.provisioningEnabled || false,
      provisioningConfig: settings.provisioningConfig || {}
    });
    
    setDialogOpen(true);
  };

  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedIntegration(null);
  };

  // Handle form field changes
  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Save integration settings
  const handleSaveSettings = async () => {
    try {
      if (!selectedIntegration) return;
      
      await integrationService.updateIntegrationSettings(selectedIntegration.id, formData);
      
      // Update local state
      setIntegrationSettings(prev => {
        const index = prev.findIndex(s => s.integrationId === selectedIntegration.id);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = {
            ...updated[index],
            ...formData
          };
          return updated;
        } else {
          return [
            ...prev,
            {
              integrationId: selectedIntegration.id,
              ...formData
            }
          ];
        }
      });
      
      setSuccess(`Settings for ${selectedIntegration.name} updated successfully`);
      setTimeout(() => setSuccess(null), 3000);
      handleCloseDialog();
    } catch (err) {
      console.error('Error updating integration settings:', err);
      setError(`Failed to update settings for ${selectedIntegration.name}. Please try again.`);
      setTimeout(() => setError(null), 3000);
    }
  };

  // Get status chip for an integration
  const getStatusChip = (integrationId) => {
    const status = (Array.isArray(integrationStatus) ? integrationStatus : []).find(s => s.integration.toLowerCase() === integrationId);

    if (!status) {
      return <Chip icon={<WarningIcon />} label="Unknown" color="default" size="small" />;
    }

    switch (status.status) {
      case 'active':
        return <Chip icon={<CheckCircleIcon />} label="Active" color="success" size="small" />;
      case 'needs_auth':
        return <Chip icon={<WarningIcon />} label="Needs Authentication" color="warning" size="small" />;
      case 'not_configured':
        return <Chip icon={<WarningIcon />} label="Not Configured" color="warning" size="small" />;
      case 'error':
        return <Chip icon={<ErrorIcon />} label="Error" color="error" size="small" />;
      default:
        return <Chip icon={<WarningIcon />} label={status.status} color="default" size="small" />;
    }
  };

  // Get provisioning status chip for an integration
  const getProvisioningStatusChip = (integrationId) => {
    const settings = integrationSettings.find(s => s.integrationId === integrationId);
    
    if (!settings) {
      return <Chip icon={<WarningIcon />} label="Not Configured" color="default" size="small" />;
    }
    
    if (!settings.supportsProvisioning) {
      return <Chip icon={<WarningIcon />} label="Not Supported" color="default" size="small" />;
    }
    
    if (settings.provisioningEnabled) {
      return <Chip icon={<CheckCircleIcon />} label="Enabled" color="success" size="small" />;
    } else {
      return <Chip icon={<WarningIcon />} label="Disabled" color="warning" size="small" />;
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Integration Management
        </Typography>
        
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Configure Integration Settings
          </Typography>
          
          <Typography variant="body1" paragraph>
            Manage global settings for all integrations, including provisioning capabilities.
          </Typography>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          ) : (
            <>
              {success && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  {success}
                </Alert>
              )}
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Integration</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Required</TableCell>
                      <TableCell>Read Only</TableCell>
                      <TableCell>Global Config</TableCell>
                      <TableCell>Provisioning</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {integrations.map((integration) => {
                      const settings = integrationSettings.find(s => s.integrationId === integration.id) || {};
                      return (
                        <TableRow key={integration.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {integrationIcons[integration.id] || <SettingsIcon />}
                              <Typography sx={{ ml: 1 }}>{integration.name}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>{getStatusChip(integration.id)}</TableCell>
                          <TableCell>
                            <Switch
                              checked={settings.isRequired || false}
                              onChange={() => {
                                const updatedSettings = {
                                  ...settings,
                                  isRequired: !settings.isRequired
                                };
                                integrationService.updateIntegrationSettings(integration.id, updatedSettings)
                                  .then(() => {
                                    setIntegrationSettings(prev => {
                                      const index = prev.findIndex(s => s.integrationId === integration.id);
                                      if (index >= 0) {
                                        const updated = [...prev];
                                        updated[index] = {
                                          ...updated[index],
                                          isRequired: !settings.isRequired
                                        };
                                        return updated;
                                      } else {
                                        return [
                                          ...prev,
                                          {
                                            integrationId: integration.id,
                                            isRequired: !settings.isRequired
                                          }
                                        ];
                                      }
                                    });
                                    setSuccess(`Updated ${integration.name} settings`);
                                    setTimeout(() => setSuccess(null), 3000);
                                  })
                                  .catch(err => {
                                    console.error('Error updating integration settings:', err);
                                    setError(`Failed to update ${integration.name} settings`);
                                    setTimeout(() => setError(null), 3000);
                                  });
                              }}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Switch
                              checked={settings.isReadOnly || false}
                              onChange={() => {
                                const updatedSettings = {
                                  ...settings,
                                  isReadOnly: !settings.isReadOnly
                                };
                                integrationService.updateIntegrationSettings(integration.id, updatedSettings)
                                  .then(() => {
                                    setIntegrationSettings(prev => {
                                      const index = prev.findIndex(s => s.integrationId === integration.id);
                                      if (index >= 0) {
                                        const updated = [...prev];
                                        updated[index] = {
                                          ...updated[index],
                                          isReadOnly: !settings.isReadOnly
                                        };
                                        return updated;
                                      } else {
                                        return [
                                          ...prev,
                                          {
                                            integrationId: integration.id,
                                            isReadOnly: !settings.isReadOnly
                                          }
                                        ];
                                      }
                                    });
                                    setSuccess(`Updated ${integration.name} settings`);
                                    setTimeout(() => setSuccess(null), 3000);
                                  })
                                  .catch(err => {
                                    console.error('Error updating integration settings:', err);
                                    setError(`Failed to update ${integration.name} settings`);
                                    setTimeout(() => setError(null), 3000);
                                  });
                              }}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Switch
                              checked={settings.useGlobalConfig || false}
                              onChange={() => {
                                const updatedSettings = {
                                  ...settings,
                                  useGlobalConfig: !settings.useGlobalConfig
                                };
                                integrationService.updateIntegrationSettings(integration.id, updatedSettings)
                                  .then(() => {
                                    setIntegrationSettings(prev => {
                                      const index = prev.findIndex(s => s.integrationId === integration.id);
                                      if (index >= 0) {
                                        const updated = [...prev];
                                        updated[index] = {
                                          ...updated[index],
                                          useGlobalConfig: !settings.useGlobalConfig
                                        };
                                        return updated;
                                      } else {
                                        return [
                                          ...prev,
                                          {
                                            integrationId: integration.id,
                                            useGlobalConfig: !settings.useGlobalConfig
                                          }
                                        ];
                                      }
                                    });
                                    setSuccess(`Updated ${integration.name} settings`);
                                    setTimeout(() => setSuccess(null), 3000);
                                  })
                                  .catch(err => {
                                    console.error('Error updating integration settings:', err);
                                    setError(`Failed to update ${integration.name} settings`);
                                    setTimeout(() => setError(null), 3000);
                                  });
                              }}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>{getProvisioningStatusChip(integration.id)}</TableCell>
                          <TableCell>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleOpenDialog(integration)}
                            >
                              Configure
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </Paper>
      </Box>
      
      {/* Integration Settings Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedIntegration?.name} Settings
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Basic Settings
            </Typography>
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isRequired}
                    onChange={(e) => handleFormChange('isRequired', e.target.checked)}
                  />
                }
                label="Required"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                If enabled, all users will have this integration activated by default.
              </Typography>
            </Box>
            
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isReadOnly}
                    onChange={(e) => handleFormChange('isReadOnly', e.target.checked)}
                  />
                }
                label="Read Only"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                If enabled, users cannot modify their settings for this integration.
              </Typography>
            </Box>
            
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.useGlobalConfig}
                    onChange={(e) => handleFormChange('useGlobalConfig', e.target.checked)}
                  />
                }
                label="Use Global Config"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                If enabled, all users will use the global configuration for this integration.
              </Typography>
            </Box>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" gutterBottom>
              Provisioning Settings
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.supportsProvisioning}
                    onChange={(e) => handleFormChange('supportsProvisioning', e.target.checked)}
                  />
                }
                label="Supports Provisioning"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                Indicates whether this integration supports user account provisioning.
              </Typography>
            </Box>
            
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.provisioningEnabled}
                    onChange={(e) => handleFormChange('provisioningEnabled', e.target.checked)}
                    disabled={!formData.supportsProvisioning}
                  />
                }
                label="Enable Provisioning"
              />
              <Typography variant="caption" display="block" color="text.secondary">
                If enabled, users can provision and deprovision accounts for this integration.
              </Typography>
            </Box>
            
            {formData.supportsProvisioning && formData.provisioningEnabled && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>Advanced Provisioning Configuration</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Configure advanced settings for user provisioning in this integration.
                  </Typography>
                  
                  <TextField
                    label="Default Role"
                    fullWidth
                    margin="normal"
                    value={formData.provisioningConfig.defaultRole || ''}
                    onChange={(e) => handleFormChange('provisioningConfig', {
                      ...formData.provisioningConfig,
                      defaultRole: e.target.value
                    })}
                    helperText="Default role to assign to provisioned users"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.provisioningConfig.autoProvision || false}
                        onChange={(e) => handleFormChange('provisioningConfig', {
                          ...formData.provisioningConfig,
                          autoProvision: e.target.checked
                        })}
                      />
                    }
                    label="Auto-provision new users"
                  />
                </AccordionDetails>
              </Accordion>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveSettings} variant="contained" color="primary">
            Save Settings
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AdminIntegrationsPage;