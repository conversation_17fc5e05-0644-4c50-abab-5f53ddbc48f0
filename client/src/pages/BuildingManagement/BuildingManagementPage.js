import React, { useState, useEffect, useCallback } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardHeader, 
  Button, 
  Tabs, 
  Tab, 
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  FormGroup
} from '@mui/material';
import PhoneBook from '../../components/PhoneBook/PhoneBook';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { PermissionCheck } from '../../components/PermissionCheck';
import buildingManagementService from '../../services/buildingManagementService';
import buildingTrackingService from '../../services/buildingTrackingService';
import roomSchedulingService from '../../services/roomSchedulingService';
import FloorPlanViewer from '../../components/FloorPlan/FloorPlanViewer';
import TrackingItemsList from '../../components/BuildingTracking/TrackingItemsList';
import TrackingItemForm from '../../components/BuildingTracking/TrackingItemForm';
import TrackingItemDetails from '../../components/BuildingTracking/TrackingItemDetails';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import SecurityIcon from '@mui/icons-material/Security';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import RouterIcon from '@mui/icons-material/Router';
import VideocamIcon from '@mui/icons-material/Videocam';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SettingsIcon from '@mui/icons-material/Settings';
import AutomationIcon from '@mui/icons-material/SmartToy';
import ScheduleIcon from '@mui/icons-material/Schedule';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import EventIcon from '@mui/icons-material/Event';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import PeopleIcon from '@mui/icons-material/People';
import SearchIcon from '@mui/icons-material/Search';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import MapIcon from '@mui/icons-material/Map';
import ContactsIcon from '@mui/icons-material/Contacts';
import AssignmentIcon from '@mui/icons-material/Assignment';

// Define tab paths for URL navigation
const TAB_PATHS = {
  0: 'dashboard',
  1: 'access-control',
  2: 'climate',
  3: 'security',
  4: 'network',
  5: 'room-scheduling',
  6: 'floor-plans',
  7: 'automation',
  8: 'analytics',
  9: 'tracking',
  10: 'phone-book'
};

// Map URL paths back to tab indices
const PATH_TO_TAB = {
  'dashboard': 0,
  'access-control': 1,
  'climate': 2,
  'security': 3,
  'network': 4,
  'room-scheduling': 5,
  'floor-plans': 6,
  'automation': 7,
  'analytics': 8,
  'tracking': 9,
  'phone-book': 10
};


const BuildingManagementPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [systemStatus, setSystemStatus] = useState({
    accessControl: { status: 'unknown', message: 'Checking status...' },
    climate: { status: 'unknown', message: 'Checking status...' },
    security: { status: 'unknown', message: 'Checking status...' },
    network: { status: 'unknown', message: 'Checking status...' },
    roomScheduling: { status: 'unknown', message: 'Checking status...' }
  });
  const [integrations, setIntegrations] = useState({
    dreo: { active: true },
    lenelS2NetBox: { active: true },
    unifiAccess: { active: true },
    unifiNetwork: { active: true },
    unifiProtect: { active: true },
    googleCalendar: { active: true }
  });
  const [automationRules, setAutomationRules] = useState([]);
  const [historicalData, setHistoricalData] = useState(null);
  const [timeRange, setTimeRange] = useState('24h');

  // Room scheduling state
  const [rooms, setRooms] = useState([]);
  const [reservations, setReservations] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchParams, setSearchParams] = useState({
    capacity: '',
    features: [],
    buildingId: '',
    floorId: '',
    startTime: '',
    endTime: ''
  });
  
  // Tracking items state
  const [trackingItems, setTrackingItems] = useState([]);
  const [trackingItemsLoading, setTrackingItemsLoading] = useState(false);
  const [trackingItemsError, setTrackingItemsError] = useState(null);
  const [trackingItemFormOpen, setTrackingItemFormOpen] = useState(false);
  const [trackingItemDetailsOpen, setTrackingItemDetailsOpen] = useState(false);
  const [selectedTrackingItem, setSelectedTrackingItem] = useState(null);
  const [buildings, setBuildings] = useState([]);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);

  // Set active tab based on URL path on component mount
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const tabPath = pathParts[pathParts.length - 1];
    
    // If the URL has a valid tab path, set the active tab
    if (tabPath && PATH_TO_TAB.hasOwnProperty(tabPath)) {
      setActiveTab(PATH_TO_TAB[tabPath]);
    } else if (pathParts.length <= 2) {
      // If we're at the root building-management path with no tab specified, navigate to the default tab
      navigate(`/building-management/${TAB_PATHS[0]}`, { replace: true });
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get integration statuses
        const integrationsStatus = await buildingManagementService.getIntegrationsStatus();
        setIntegrations(integrationsStatus);

        // Get system status for each category
        const status = await buildingManagementService.getSystemStatus();
        setSystemStatus(status);

        // Get automation rules
        try {
          const rules = await buildingManagementService.getAutomationRules();
          setAutomationRules(Array.isArray(rules) ? rules : []);
        } catch (rulesErr) {
          console.error('Error fetching automation rules:', rulesErr);
          setAutomationRules([]);
        }

        // Get historical data for the default time range
        const data = await buildingManagementService.getHistoricalData({ timeRange });
        setHistoricalData(data);

        // Get room scheduling data
        try {
          // Get all rooms
          const roomsData = await roomSchedulingService.getAllRooms();
          setRooms(Array.isArray(roomsData) ? roomsData : []);

          // Get today's reservations
          const today = new Date();
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);

          const reservationsData = await roomSchedulingService.getAllReservations({
            startDate: today.toISOString(),
            endDate: tomorrow.toISOString()
          });
          setReservations(Array.isArray(reservationsData) ? reservationsData : []);

          // Update room scheduling status
          setSystemStatus(prevStatus => ({
            ...prevStatus,
            roomScheduling: { 
              status: 'active', 
              message: 'Room scheduling system is operational' 
            }
          }));
        } catch (roomErr) {
          console.error('Error fetching room scheduling data:', roomErr);
          setSystemStatus(prevStatus => ({
            ...prevStatus,
            roomScheduling: { 
              status: 'error', 
              message: 'Room scheduling system is experiencing issues' 
            }
          }));
        }
        
        // Get buildings for tracking items
        try {
          const buildingsData = await buildingManagementService.getBuildings();
          setBuildings(Array.isArray(buildingsData) ? buildingsData : []);
        } catch (buildingsErr) {
          console.error('Error fetching buildings:', buildingsErr);
          setBuildings([]);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching building management data:', err);
        setError('Failed to load building management data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);
  
  // Fetch tracking items
  const fetchTrackingItems = useCallback(async (filters = {}) => {
    try {
      setTrackingItemsLoading(true);
      setTrackingItemsError(null);
      
      let items;
      try {
        items = await buildingTrackingService.getTrackingItems(filters);
      } catch (err) {
        // If API endpoint doesn't exist yet, use mock data for development
        if (err.response && err.response.status === 404) {
          console.warn('Building Tracking API not found, returning mock data');
          items = buildingTrackingService.getMockTrackingItems();
        } else {
          throw err;
        }
      }
      
      setTrackingItems(Array.isArray(items) ? items : []);
      setTrackingItemsLoading(false);
    } catch (err) {
      console.error('Error fetching tracking items:', err);
      setTrackingItemsError('Failed to load tracking items. Please try again later.');
      setTrackingItemsLoading(false);
    }
  }, []);
  
  // Fetch tracking items when the tracking tab is selected
  useEffect(() => {
    if (activeTab === PATH_TO_TAB['tracking']) {
      fetchTrackingItems();
    }
  }, [activeTab, fetchTrackingItems]);
  
  // Handle tracking item form open
  const handleAddTrackingItem = () => {
    setSelectedTrackingItem(null);
    setTrackingItemFormOpen(true);
  };
  
  // Handle tracking item edit
  const handleEditTrackingItem = (item) => {
    setSelectedTrackingItem(item);
    setTrackingItemFormOpen(true);
    setTrackingItemDetailsOpen(false);
  };
  
  // Handle tracking item view
  const handleViewTrackingItem = (item) => {
    setSelectedTrackingItem(item);
    setTrackingItemDetailsOpen(true);
  };
  
  // Handle tracking item delete confirmation
  const handleDeleteTrackingItemConfirm = (item) => {
    setSelectedTrackingItem(item);
    setConfirmDeleteOpen(true);
  };
  
  // Handle tracking item delete
  const handleDeleteTrackingItem = async () => {
    if (!selectedTrackingItem) return;
    
    try {
      setTrackingItemsLoading(true);
      
      try {
        await buildingTrackingService.deleteTrackingItem(selectedTrackingItem._id);
      } catch (err) {
        // If API endpoint doesn't exist yet, just remove from local state for development
        if (err.response && err.response.status === 404) {
          console.warn('Building Tracking API not found, removing from local state only');
        } else {
          throw err;
        }
      }
      
      // Remove the item from the local state
      setTrackingItems(prevItems => 
        prevItems.filter(item => item._id !== selectedTrackingItem._id)
      );
      
      setConfirmDeleteOpen(false);
      setSelectedTrackingItem(null);
      setTrackingItemsLoading(false);
    } catch (err) {
      console.error('Error deleting tracking item:', err);
      setTrackingItemsError('Failed to delete tracking item. Please try again later.');
      setTrackingItemsLoading(false);
    }
  };
  
  // Handle tracking item save
  const handleSaveTrackingItem = async (formData) => {
    try {
      setTrackingItemsLoading(true);
      
      let savedItem;
      
      if (selectedTrackingItem) {
        // Update existing item
        try {
          savedItem = await buildingTrackingService.updateTrackingItem(
            selectedTrackingItem._id, 
            formData
          );
        } catch (err) {
          // If API endpoint doesn't exist yet, just update local state for development
          if (err.response && err.response.status === 404) {
            console.warn('Building Tracking API not found, updating local state only');
            savedItem = { ...selectedTrackingItem, ...formData };
          } else {
            throw err;
          }
        }
        
        // Update the item in the local state
        setTrackingItems(prevItems => 
          prevItems.map(item => 
            item._id === selectedTrackingItem._id ? savedItem : item
          )
        );
      } else {
        // Create new item
        try {
          savedItem = await buildingTrackingService.createTrackingItem(formData);
        } catch (err) {
          // If API endpoint doesn't exist yet, create mock item for development
          if (err.response && err.response.status === 404) {
            console.warn('Building Tracking API not found, creating mock item');
            savedItem = {
              _id: `mock-${Date.now()}`,
              ...formData,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };
          } else {
            throw err;
          }
        }
        
        // Add the new item to the local state
        setTrackingItems(prevItems => [...prevItems, savedItem]);
      }
      
      setTrackingItemFormOpen(false);
      setSelectedTrackingItem(null);
      setTrackingItemsLoading(false);
    } catch (err) {
      console.error('Error saving tracking item:', err);
      setTrackingItemsError('Failed to save tracking item. Please try again later.');
      setTrackingItemsLoading(false);
    }
  };
  
  // Handle tracking item filter change
  const handleTrackingItemFilterChange = useCallback((filters) => {
    fetchTrackingItems(filters);
  }, [fetchTrackingItems]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Update URL when tab changes
    navigate(`/building-management/${TAB_PATHS[newValue]}`);
  };

  const renderDashboard = () => (
    <Box sx={{ mt: 3 }}>
      <Grid container spacing={3}>
        {/* System Status Overview */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Building Systems Status" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                    <MeetingRoomIcon fontSize="large" color={getStatusColor(systemStatus.accessControl?.status)} />
                    <Typography variant="h6" sx={{ mt: 1 }}>Access Control</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {systemStatus.accessControl?.message || 'Status unavailable'}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                    <ThermostatIcon fontSize="large" color={getStatusColor(systemStatus.climate?.status)} />
                    <Typography variant="h6" sx={{ mt: 1 }}>Climate Control</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {systemStatus.climate?.message || 'Status unavailable'}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                    <SecurityIcon fontSize="large" color={getStatusColor(systemStatus.security?.status)} />
                    <Typography variant="h6" sx={{ mt: 1 }}>Security Systems</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {systemStatus.security?.message || 'Status unavailable'}
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper elevation={2} sx={{ p: 2, textAlign: 'center', height: '100%' }}>
                    <RouterIcon fontSize="large" color={getStatusColor(systemStatus.network?.status)} />
                    <Typography variant="h6" sx={{ mt: 1 }}>Network</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {systemStatus.network?.message || 'Status unavailable'}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Access */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardHeader title="Quick Access" />
            <CardContent>
              <Grid container spacing={2}>
                {/* Room Scheduling - Always show this */}
                <Grid item xs={6} sm={4}>
                  <Button 
                    onClick={() => {
                      setActiveTab(5); // Set to Room Scheduling tab
                      navigate(`/building-management/${TAB_PATHS[5]}`);
                    }}
                    variant="outlined" 
                    startIcon={<CalendarTodayIcon />}
                    fullWidth
                    sx={{ height: '100%', justifyContent: 'flex-start', textAlign: 'left' }}
                  >
                    Room Scheduling
                  </Button>
                </Grid>
                {integrations.dreo?.active && (
                  <Grid item xs={6} sm={4}>
                    <Button 
                      component={Link} 
                      to="/dreo" 
                      variant="outlined" 
                      startIcon={<ThermostatIcon />}
                      fullWidth
                      sx={{ height: '100%', justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Climate Control
                    </Button>
                  </Grid>
                )}
                {((integrations.lenelS2NetBox?.active || integrations.unifiAccess?.active)) && (
                  <Grid item xs={6} sm={4}>
                    <Button 
                      component={Link} 
                      to={integrations.lenelS2NetBox?.active ? "/lenel-s2-netbox" : "/unifi-access"} 
                      variant="outlined" 
                      startIcon={<MeetingRoomIcon />}
                      fullWidth
                      sx={{ height: '100%', justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Access Control
                    </Button>
                  </Grid>
                )}
                {integrations.unifiProtect?.active && (
                  <Grid item xs={6} sm={4}>
                    <Button 
                      component={Link} 
                      to="/unifi-protect" 
                      variant="outlined" 
                      startIcon={<VideocamIcon />}
                      fullWidth
                      sx={{ height: '100%', justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Security Cameras
                    </Button>
                  </Grid>
                )}
                {integrations.unifiNetwork?.active && (
                  <Grid item xs={6} sm={4}>
                    <Button 
                      component={Link} 
                      to="/unifi-network" 
                      variant="outlined" 
                      startIcon={<RouterIcon />}
                      fullWidth
                      sx={{ height: '100%', justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Network
                    </Button>
                  </Grid>
                )}
                {user.roles.includes('admin') && (
                  <Grid item xs={6} sm={4}>
                    <Button 
                      component={Link} 
                      to="/building-management/setup" 
                      variant="outlined" 
                      startIcon={<SettingsIcon />}
                      fullWidth
                      sx={{ height: '100%', justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Settings
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Available Integrations */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardHeader title="Building Management Integrations" />
            <CardContent>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <ThermostatIcon color={integrations.dreo?.active ? "primary" : "disabled"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Dreo Climate Control" 
                    secondary={integrations.dreo?.active ? "Connected" : "Not configured"} 
                  />
                  <Button 
                    component={Link} 
                    to="/dreo" 
                    variant="text"
                  >
                    {integrations.dreo?.active ? "Manage" : "View"}
                  </Button>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <MeetingRoomIcon color={integrations.lenelS2NetBox?.active ? "primary" : "disabled"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Lenel S2 NetBox Access Control" 
                    secondary={integrations.lenelS2NetBox?.active ? "Connected" : "Not configured"} 
                  />
                  <Button 
                    component={Link} 
                    to="/lenel-s2-netbox" 
                    variant="text"
                  >
                    {integrations.lenelS2NetBox?.active ? "Manage" : "View"}
                  </Button>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <MeetingRoomIcon color={integrations.unifiAccess?.active ? "primary" : "disabled"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="UniFi Access Control" 
                    secondary={integrations.unifiAccess?.active ? "Connected" : "Not configured"}
                  />
                  <Button 
                    component={Link} 
                    to="/unifi-access"
                    variant="text"
                  >
                    {integrations.unifiAccess?.active ? "Manage" : "View"}
                  </Button>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <VideocamIcon color={integrations.unifiProtect?.active ? "primary" : "disabled"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="UniFi Protect Security Cameras" 
                    secondary={integrations.unifiProtect?.active ? "Connected" : "Not configured"}
                  />
                  <Button 
                    component={Link} 
                    to="/unifi-protect"
                    variant="text"
                  >
                    {integrations.unifiProtect?.active ? "Manage" : "View"}
                  </Button>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <RouterIcon color={integrations.unifiNetwork?.active ? "primary" : "disabled"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="UniFi Network" 
                    secondary={integrations.unifiNetwork?.active ? "Connected" : "Not configured"}
                  />
                  <Button 
                    component={Link} 
                    to="/unifi-network"
                    variant="text"
                  >
                    {integrations.unifiNetwork?.active ? "Manage" : "View"}
                  </Button>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <CalendarTodayIcon color={integrations.googleCalendar?.active ? "primary" : "disabled"} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Google Calendar Integration" 
                    secondary={integrations.googleCalendar?.active ? "Connected" : "Not configured"}
                  />
                  <Button 
                    component={Link} 
                    to="/google-calendar"
                    variant="text"
                  >
                    {integrations.googleCalendar?.active ? "Manage" : "View"}
                  </Button>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderAccessControl = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Access Control Systems
      </Typography>
      <Grid container spacing={3}>
        {integrations.lenelS2NetBox?.active && (
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Lenel S2 NetBox" />
              <CardContent>
                <Button 
                  component={Link} 
                  to="/lenel-s2-netbox" 
                  variant="contained" 
                  color="primary"
                  startIcon={<MeetingRoomIcon />}
                >
                  Manage Access Control
                </Button>
              </CardContent>
            </Card>
          </Grid>
        )}
        {integrations.unifiAccess?.active && (
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="UniFi Access" />
              <CardContent>
                <Button 
                  component={Link} 
                  to="/unifi-access" 
                  variant="contained" 
                  color="primary"
                  startIcon={<MeetingRoomIcon />}
                >
                  Manage Access Control
                </Button>
              </CardContent>
            </Card>
          </Grid>
        )}
        {!integrations.lenelS2NetBox?.active && !integrations.unifiAccess?.active && (
          <Grid item xs={12}>
            <Alert severity="info">
              No access control systems are currently configured. Please set up Lenel S2 NetBox or UniFi Access to manage building access.
            </Alert>
          </Grid>
        )}
      </Grid>
    </Box>
  );

  const renderClimateControl = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Climate Control Systems
      </Typography>
      <Grid container spacing={3}>
        {integrations.dreo?.active && (
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Dreo Climate Control" />
              <CardContent>
                <Button 
                  component={Link} 
                  to="/dreo" 
                  variant="contained" 
                  color="primary"
                  startIcon={<ThermostatIcon />}
                >
                  Manage Climate Control
                </Button>
              </CardContent>
            </Card>
          </Grid>
        )}
        {!integrations.dreo?.active && (
          <Grid item xs={12}>
            <Alert severity="info">
              No climate control systems are currently configured. Please set up Dreo to manage building climate.
            </Alert>
          </Grid>
        )}
      </Grid>
    </Box>
  );

  const renderSecurity = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Security Systems
      </Typography>
      <Grid container spacing={3}>
        {integrations.unifiProtect?.active && (
          <Grid item xs={12}>
            <Card>
              <CardHeader title="UniFi Protect Security Cameras" />
              <CardContent>
                <Button 
                  component={Link} 
                  to="/unifi-protect" 
                  variant="contained" 
                  color="primary"
                  startIcon={<VideocamIcon />}
                >
                  Manage Security Cameras
                </Button>
              </CardContent>
            </Card>
          </Grid>
        )}
        {!integrations.unifiProtect?.active && (
          <Grid item xs={12}>
            <Alert severity="info">
              No security systems are currently configured. Please set up UniFi Protect to manage building security.
            </Alert>
          </Grid>
        )}
      </Grid>
    </Box>
  );

  const renderNetwork = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Network Systems
      </Typography>
      <Grid container spacing={3}>
        {integrations.unifiNetwork?.active && (
          <Grid item xs={12}>
            <Card>
              <CardHeader title="UniFi Network" />
              <CardContent>
                <Button 
                  component={Link} 
                  to="/unifi-network" 
                  variant="contained" 
                  color="primary"
                  startIcon={<RouterIcon />}
                >
                  Manage Network
                </Button>
              </CardContent>
            </Card>
          </Grid>
        )}
        {!integrations.unifiNetwork?.active && (
          <Grid item xs={12}>
            <Alert severity="info">
              No network systems are currently configured. Please set up UniFi Network to manage building network.
            </Alert>
          </Grid>
        )}
      </Grid>
    </Box>
  );

  const renderAutomation = () => {
    const handleToggleRule = (ruleId) => {
      // Ensure automationRules is an array
      if (!Array.isArray(automationRules)) return;

      // Find the rule
      const rule = automationRules.find(r => r.id === ruleId);
      if (!rule) return;

      // Toggle the enabled status
      const updatedRule = { ...rule, enabled: !rule.enabled };

      // In a real implementation, this would call the API to update the rule
      console.log(`Toggling rule ${ruleId} to ${updatedRule.enabled ? 'enabled' : 'disabled'}`);

      // Update the local state
      setAutomationRules(automationRules.map(r => r.id === ruleId ? updatedRule : r));
    };

    const formatCondition = (condition) => {
      if (condition.time) {
        if (condition.time.allDay) {
          return `All day on ${Array.isArray(condition.time.days) ? condition.time.days.map(d => d.charAt(0).toUpperCase() + d.slice(1)).join(', ') : 'selected days'}`;
        } else {
          return `${condition.time.startTime} - ${condition.time.endTime} on ${Array.isArray(condition.time.days) ? condition.time.days.map(d => d.charAt(0).toUpperCase() + d.slice(1)).join(', ') : 'selected days'}`;
        }
      }
      return 'Custom condition';
    };

    const formatAction = (action) => {
      if (action.climate) {
        if (action.climate.targetTemperature) {
          return `Set temperature to ${action.climate.targetTemperature}°F in ${action.climate.zones.join(', ')}`;
        } else if (action.climate.mode) {
          return `Set climate mode to ${action.climate.mode} in ${action.climate.zones.join(', ')}`;
        }
      } else if (action.access) {
        if (action.access.lockDoors) {
          return `Lock doors: ${action.access.lockDoors.join(', ')}`;
        } else if (action.access.unlockDoors) {
          return `Unlock doors: ${action.access.unlockDoors.join(', ')}`;
        }
      }
      return 'Custom action';
    };

    return (
      <Box sx={{ mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Automation Rules
          </Typography>
          <Button 
            variant="contained" 
            color="primary"
            startIcon={<AddIcon />}
            // In a real implementation, this would navigate to a rule creation page or open a dialog
            onClick={() => console.log('Create new rule')}
          >
            Create Rule
          </Button>
        </Box>

        <Grid container spacing={3}>
          {Array.isArray(automationRules) && automationRules.length > 0 ? (
            automationRules.map(rule => (
              <Grid item xs={12} key={rule.id}>
                <Card>
                  <CardHeader 
                    title={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ScheduleIcon sx={{ mr: 1 }} />
                        {rule.name}
                        {rule.enabled ? 
                          <ToggleOnIcon 
                            color="primary" 
                            sx={{ ml: 1, cursor: 'pointer' }} 
                            onClick={() => handleToggleRule(rule.id)}
                          /> : 
                          <ToggleOffIcon 
                            color="disabled" 
                            sx={{ ml: 1, cursor: 'pointer' }} 
                            onClick={() => handleToggleRule(rule.id)}
                          />
                        }
                      </Box>
                    }
                    subheader={rule.description}
                    action={
                      <Box>
                        <Button 
                          startIcon={<EditIcon />} 
                          // In a real implementation, this would navigate to a rule edit page or open a dialog
                          onClick={() => console.log(`Edit rule ${rule.id}`)}
                        >
                          Edit
                        </Button>
                        <Button 
                          startIcon={<DeleteIcon />} 
                          color="error"
                          // In a real implementation, this would show a confirmation dialog and then delete the rule
                          onClick={() => console.log(`Delete rule ${rule.id}`)}
                        >
                          Delete
                        </Button>
                      </Box>
                    }
                  />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Conditions:
                        </Typography>
                        <Typography variant="body2">
                          {formatCondition(rule.conditions)}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Actions:
                        </Typography>
                        <Typography variant="body2">
                          {formatAction(rule.actions)}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))
          ) : (
            <Grid item xs={12}>
              <Alert severity="info">
                No automation rules have been created yet. Click the "Create Rule" button to get started.
              </Alert>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // Room Scheduling Component
  const RoomScheduling = () => {
    const [viewMode, setViewMode] = useState('calendar'); // 'calendar', 'list', 'search'
    const [newReservation, setNewReservation] = useState({
      roomId: '',
      title: '',
      description: '',
      startTime: '',
      endTime: '',
      recurrence: {
        isRecurring: false,
        pattern: 'daily',
        daysOfWeek: [],
        interval: 1,
        endDate: null,
        count: null
      },
      attendees: [],
      metadata: {
        purpose: '',
        numberOfAttendees: 1,
        requiredFeatures: []
      }
    });
    const [showNewReservationForm, setShowNewReservationForm] = useState(false);
    const [pendingApprovals, setPendingApprovals] = useState([]);

    useEffect(() => {
      const fetchPendingApprovals = async () => {
        try {
          // Get reservations that require approval
          const pendingReservations = await roomSchedulingService.getAllReservations({
            status: 'pending',
            requiresApproval: true
          });
          setPendingApprovals(Array.isArray(pendingReservations) ? pendingReservations : []);
        } catch (error) {
          console.error('Error fetching pending approvals:', error);
          setPendingApprovals([]);
        }
      };

      if (activeTab === 5) { // Room Scheduling tab
        fetchPendingApprovals();
      }
    }, [activeTab]);

    const handleViewModeChange = (mode) => {
      setViewMode(mode);
    };

    const handleRoomSelect = (roomId) => {
      const room = rooms.find(r => r._id === roomId);
      setSelectedRoom(room);
      setNewReservation(prev => ({ ...prev, roomId }));
    };

    const handleDateChange = (date) => {
      setSelectedDate(date);
    };

    const handleSearchParamsChange = (e) => {
      const { name, value } = e.target;
      setSearchParams(prev => ({ ...prev, [name]: value }));
    };

    const handleFindAvailableRooms = async () => {
      try {
        const availableRooms = await roomSchedulingService.findAvailableRooms(searchParams);
        setRooms(availableRooms);
      } catch (error) {
        console.error('Error finding available rooms:', error);
      }
    };

    const handleNewReservationChange = (e) => {
      const { name, value } = e.target;
      setNewReservation(prev => ({ ...prev, [name]: value }));
    };

    const handleCreateReservation = async () => {
      try {
        await roomSchedulingService.createReservation(newReservation);
        setShowNewReservationForm(false);

        // Refresh reservations
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const reservationsData = await roomSchedulingService.getAllReservations({
          startDate: today.toISOString(),
          endDate: tomorrow.toISOString()
        });
        setReservations(reservationsData);
      } catch (error) {
        console.error('Error creating reservation:', error);
      }
    };

    const handleApproveReservation = async (id) => {
      try {
        await roomSchedulingService.approveReservation(id);

        // Refresh pending approvals
        const pendingReservations = await roomSchedulingService.getAllReservations({
          status: 'pending',
          requiresApproval: true
        });
        setPendingApprovals(Array.isArray(pendingReservations) ? pendingReservations : []);
      } catch (error) {
        console.error('Error approving reservation:', error);
      }
    };

    const handleRejectReservation = async (id, reason) => {
      try {
        await roomSchedulingService.rejectReservation(id, { rejectionReason: reason });

        // Refresh pending approvals
        const pendingReservations = await roomSchedulingService.getAllReservations({
          status: 'pending',
          requiresApproval: true
        });
        setPendingApprovals(Array.isArray(pendingReservations) ? pendingReservations : []);
      } catch (error) {
        console.error('Error rejecting reservation:', error);
      }
    };

    return (
      <Box sx={{ mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Room Scheduling
          </Typography>
          <Box>
            <Button 
              variant={viewMode === 'calendar' ? 'contained' : 'outlined'}
              onClick={() => handleViewModeChange('calendar')}
              startIcon={<CalendarTodayIcon />}
              sx={{ mr: 1 }}
            >
              Calendar
            </Button>
            <Button 
              variant={viewMode === 'list' ? 'contained' : 'outlined'}
              onClick={() => handleViewModeChange('list')}
              startIcon={<EventIcon />}
              sx={{ mr: 1 }}
            >
              Reservations
            </Button>
            <Button 
              variant={viewMode === 'search' ? 'contained' : 'outlined'}
              onClick={() => handleViewModeChange('search')}
              startIcon={<SearchIcon />}
              sx={{ mr: 1 }}
            >
              Find Room
            </Button>
            <Button 
              variant="contained"
              color="primary"
              onClick={() => setShowNewReservationForm(true)}
              startIcon={<AddIcon />}
            >
              New Reservation
            </Button>
          </Box>
        </Box>

        {/* Room Scheduling Content */}
        <Grid container spacing={3}>
          {/* Room List */}
          <Grid item xs={12} md={3}>
            <Card>
              <CardHeader title="Rooms" />
              <CardContent>
                <List>
                  {Array.isArray(rooms) && rooms.length > 0 ? (
                    rooms.map(room => (
                      <ListItem 
                        key={room._id}
                        button
                        selected={selectedRoom && selectedRoom._id === room._id}
                        onClick={() => handleRoomSelect(room._id)}
                      >
                        <ListItemIcon>
                          <MeetingRoomIcon color={room.status === 'available' ? 'success' : 'error'} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={room.name} 
                          secondary={`Capacity: ${room.capacity} | Floor: ${room.floorId.name}`} 
                        />
                      </ListItem>
                    ))
                  ) : (
                    <ListItem>
                      <ListItemText primary="No rooms available" />
                    </ListItem>
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Main Content Area */}
          <Grid item xs={12} md={9}>
            {viewMode === 'calendar' && (
              <Card>
                <CardHeader 
                  title="Room Calendar" 
                  subheader={selectedRoom ? `${selectedRoom.name} - Capacity: ${selectedRoom.capacity}` : 'Select a room to view its calendar'}
                />
                <CardContent>
                  {selectedRoom ? (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Reservations for {selectedDate.toLocaleDateString()}
                      </Typography>
                      <List>
                        {Array.isArray(reservations) && reservations.filter(res => res.roomId._id === selectedRoom._id).length > 0 ? (
                          reservations
                            .filter(res => res.roomId._id === selectedRoom._id)
                            .map(reservation => (
                              <ListItem key={reservation._id}>
                                <ListItemIcon>
                                  {reservation.status === 'approved' ? (
                                    <EventAvailableIcon color="success" />
                                  ) : reservation.status === 'pending' ? (
                                    <EventIcon color="warning" />
                                  ) : (
                                    <EventBusyIcon color="error" />
                                  )}
                                </ListItemIcon>
                                <ListItemText 
                                  primary={reservation.title} 
                                  secondary={`${new Date(reservation.startTime).toLocaleTimeString()} - ${new Date(reservation.endTime).toLocaleTimeString()} | ${reservation.userId.name}`} 
                                />
                              </ListItem>
                            ))
                        ) : (
                          <ListItem>
                            <ListItemText primary="No reservations for this room on the selected date" />
                          </ListItem>
                        )}
                      </List>
                    </Box>
                  ) : (
                    <Alert severity="info">
                      Please select a room from the list to view its calendar.
                    </Alert>
                  )}
                </CardContent>
              </Card>
            )}

            {viewMode === 'list' && (
              <Card>
                <CardHeader title="All Reservations" />
                <CardContent>
                  <List>
                    {Array.isArray(reservations) && reservations.length > 0 ? (
                      reservations.map(reservation => (
                        <ListItem key={reservation._id}>
                          <ListItemIcon>
                            {reservation.status === 'approved' ? (
                              <EventAvailableIcon color="success" />
                            ) : reservation.status === 'pending' ? (
                              <EventIcon color="warning" />
                            ) : (
                              <EventBusyIcon color="error" />
                            )}
                          </ListItemIcon>
                          <ListItemText 
                            primary={reservation.title} 
                            secondary={`${new Date(reservation.startTime).toLocaleTimeString()} - ${new Date(reservation.endTime).toLocaleTimeString()} | Room: ${reservation.roomId.name} | Status: ${reservation.status}`} 
                          />
                        </ListItem>
                      ))
                    ) : (
                      <ListItem>
                        <ListItemText primary="No reservations found" />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>
            )}

            {viewMode === 'search' && (
              <Card>
                <CardHeader title="Find Available Rooms" />
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Capacity
                      </Typography>
                      <input
                        type="number"
                        name="capacity"
                        value={searchParams.capacity}
                        onChange={handleSearchParamsChange}
                        placeholder="Minimum capacity"
                        min="1"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Features
                      </Typography>
                      <select
                        name="features"
                        multiple
                        value={searchParams.features}
                        onChange={handleSearchParamsChange}
                      >
                        <option value="tv">TV</option>
                        <option value="projector">Projector</option>
                        <option value="whiteboard">Whiteboard</option>
                        <option value="videoconference">Video Conference</option>
                        <option value="phone">Phone</option>
                        <option value="computer">Computer</option>
                      </select>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Start Time
                      </Typography>
                      <input
                        type="datetime-local"
                        name="startTime"
                        value={searchParams.startTime}
                        onChange={handleSearchParamsChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        End Time
                      </Typography>
                      <input
                        type="datetime-local"
                        name="endTime"
                        value={searchParams.endTime}
                        onChange={handleSearchParamsChange}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleFindAvailableRooms}
                        startIcon={<SearchIcon />}
                      >
                        Find Available Rooms
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}
          </Grid>

          {/* Pending Approvals */}
          {user.roles.includes('admin') && Array.isArray(pendingApprovals) && pendingApprovals.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <CardHeader title="Pending Approvals" />
                <CardContent>
                  <List>
                    {pendingApprovals.map(reservation => (
                      <ListItem key={reservation._id}>
                        <ListItemIcon>
                          <EventIcon color="warning" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={reservation.title} 
                          secondary={`${new Date(reservation.startTime).toLocaleTimeString()} - ${new Date(reservation.endTime).toLocaleTimeString()} | Room: ${reservation.roomId.name} | Requested by: ${reservation.userId.name}`} 
                        />
                        <Button
                          variant="contained"
                          color="success"
                          size="small"
                          onClick={() => handleApproveReservation(reservation._id)}
                          sx={{ mr: 1 }}
                        >
                          Approve
                        </Button>
                        <Button
                          variant="contained"
                          color="error"
                          size="small"
                          onClick={() => handleRejectReservation(reservation._id, 'Rejected by administrator')}
                        >
                          Reject
                        </Button>
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>

        {/* New Reservation Dialog */}
        <Dialog 
          open={showNewReservationForm} 
          onClose={() => setShowNewReservationForm(false)}
          fullWidth
          maxWidth="md"
        >
          <DialogTitle>Create New Reservation</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="room-select-label">Room</InputLabel>
                  <Select
                    labelId="room-select-label"
                    id="room-select"
                    name="roomId"
                    value={newReservation.roomId}
                    onChange={handleNewReservationChange}
                    required
                  >
                    <MenuItem value="">
                      <em>Select a room</em>
                    </MenuItem>
                    {Array.isArray(rooms) ? rooms.map(room => (
                      <MenuItem key={room._id} value={room._id}>
                        {room.name} (Capacity: {room.capacity})
                      </MenuItem>
                    )) : null}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title"
                  name="title"
                  value={newReservation.title}
                  onChange={handleNewReservationChange}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={newReservation.description}
                  onChange={handleNewReservationChange}
                  multiline
                  rows={3}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Start Time"
                  name="startTime"
                  type="datetime-local"
                  value={newReservation.startTime}
                  onChange={handleNewReservationChange}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="End Time"
                  name="endTime"
                  type="datetime-local"
                  value={newReservation.endTime}
                  onChange={handleNewReservationChange}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={newReservation.recurrence.isRecurring}
                      onChange={(e) => {
                        setNewReservation({
                          ...newReservation,
                          recurrence: {
                            ...newReservation.recurrence,
                            isRecurring: e.target.checked
                          }
                        });
                      }}
                      name="isRecurring"
                    />
                  }
                  label="Recurring Reservation"
                />
              </Grid>
              {newReservation.recurrence.isRecurring && (
                <>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel id="recurrence-pattern-label">Recurrence Pattern</InputLabel>
                      <Select
                        labelId="recurrence-pattern-label"
                        id="recurrence-pattern"
                        value={newReservation.recurrence.pattern}
                        onChange={(e) => {
                          setNewReservation({
                            ...newReservation,
                            recurrence: {
                              ...newReservation.recurrence,
                              pattern: e.target.value
                            }
                          });
                        }}
                      >
                        <MenuItem value="daily">Daily</MenuItem>
                        <MenuItem value="weekly">Weekly</MenuItem>
                        <MenuItem value="biweekly">Bi-weekly</MenuItem>
                        <MenuItem value="monthly">Monthly</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="End Date"
                      type="date"
                      value={newReservation.recurrence.endDate || ''}
                      onChange={(e) => {
                        setNewReservation({
                          ...newReservation,
                          recurrence: {
                            ...newReservation.recurrence,
                            endDate: e.target.value
                          }
                        });
                      }}
                      InputLabelProps={{
                        shrink: true,
                      }}
                    />
                  </Grid>
                </>
              )}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Number of Attendees"
                  name="numberOfAttendees"
                  type="number"
                  value={newReservation.metadata.numberOfAttendees}
                  onChange={(e) => {
                    setNewReservation({
                      ...newReservation,
                      metadata: {
                        ...newReservation.metadata,
                        numberOfAttendees: parseInt(e.target.value)
                      }
                    });
                  }}
                  InputProps={{ inputProps: { min: 1 } }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="required-features-label">Required Features</InputLabel>
                  <Select
                    labelId="required-features-label"
                    id="required-features"
                    multiple
                    value={newReservation.metadata.requiredFeatures}
                    onChange={(e) => {
                      setNewReservation({
                        ...newReservation,
                        metadata: {
                          ...newReservation.metadata,
                          requiredFeatures: e.target.value
                        }
                      });
                    }}
                  >
                    <MenuItem value="tv">TV</MenuItem>
                    <MenuItem value="projector">Projector</MenuItem>
                    <MenuItem value="whiteboard">Whiteboard</MenuItem>
                    <MenuItem value="videoconference">Video Conference</MenuItem>
                    <MenuItem value="phone">Phone</MenuItem>
                    <MenuItem value="computer">Computer</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Purpose"
                  name="purpose"
                  value={newReservation.metadata.purpose}
                  onChange={(e) => {
                    setNewReservation({
                      ...newReservation,
                      metadata: {
                        ...newReservation.metadata,
                        purpose: e.target.value
                      }
                    });
                  }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowNewReservationForm(false)}>Cancel</Button>
            <Button 
              onClick={handleCreateReservation} 
              variant="contained" 
              color="primary"
              disabled={!newReservation.roomId || !newReservation.title || !newReservation.startTime || !newReservation.endTime}
            >
              Create Reservation
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  };

  const renderAnalytics = () => {
    const handleTimeRangeChange = (newRange) => {
      setTimeRange(newRange);
    };

    return (
      <Box sx={{ mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            Building Analytics
          </Typography>
          <Box>
            <Button 
              variant={timeRange === '24h' ? 'contained' : 'outlined'}
              onClick={() => handleTimeRangeChange('24h')}
              sx={{ mr: 1 }}
            >
              24 Hours
            </Button>
            <Button 
              variant={timeRange === '7d' ? 'contained' : 'outlined'}
              onClick={() => handleTimeRangeChange('7d')}
              sx={{ mr: 1 }}
            >
              7 Days
            </Button>
            <Button 
              variant={timeRange === '30d' ? 'contained' : 'outlined'}
              onClick={() => handleTimeRangeChange('30d')}
            >
              30 Days
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Climate Analytics */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader 
                title="Climate Analytics" 
                subheader={`Data for the last ${timeRange === '24h' ? '24 hours' : timeRange === '7d' ? '7 days' : '30 days'}`}
                avatar={<ThermostatIcon />}
              />
              <CardContent>
                <Typography variant="body2" paragraph>
                  Average Temperature: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    `${Math.round(historicalData.dataPoints.reduce((sum, dp) => sum + (dp.climate?.temperature || 0), 0) / historicalData.dataPoints.length)}°F` :
                    'Loading...'}
                </Typography>
                <Typography variant="body2" paragraph>
                  Average Humidity: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    `${Math.round(historicalData.dataPoints.reduce((sum, dp) => sum + (dp.climate?.humidity || 0), 0) / historicalData.dataPoints.length)}%` :
                    'Loading...'}
                </Typography>
                <Typography variant="body2">
                  Total Energy Usage: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    `${Math.round(historicalData.dataPoints.reduce((sum, dp) => sum + (dp.climate?.energyUsage || 0), 0))} kWh` :
                    'Loading...'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Security Analytics */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader 
                title="Security Analytics" 
                subheader={`Data for the last ${timeRange === '24h' ? '24 hours' : timeRange === '7d' ? '7 days' : '30 days'}`}
                avatar={<SecurityIcon />}
              />
              <CardContent>
                <Typography variant="body2" paragraph>
                  Total Access Events: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    historicalData.dataPoints.reduce((sum, dp) => sum + (dp.access?.accessEvents || 0), 0) :
                    'Loading...'}
                </Typography>
                <Typography variant="body2" paragraph>
                  Total Motion Events: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    historicalData.dataPoints.reduce((sum, dp) => sum + (dp.security?.motionEvents || 0), 0) :
                    'Loading...'}
                </Typography>
                <Typography variant="body2">
                  Total Alarms: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    historicalData.dataPoints.reduce((sum, dp) => sum + (dp.security?.alarms || 0), 0) :
                    'Loading...'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Network Analytics */}
          <Grid item xs={12}>
            <Card>
              <CardHeader 
                title="Network Analytics" 
                subheader={`Data for the last ${timeRange === '24h' ? '24 hours' : timeRange === '7d' ? '7 days' : '30 days'}`}
                avatar={<RouterIcon />}
              />
              <CardContent>
                <Typography variant="body2" paragraph>
                  Average Bandwidth Usage: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    `${Math.round(historicalData.dataPoints.reduce((sum, dp) => sum + (dp.network?.bandwidth || 0), 0) / historicalData.dataPoints.length)} Mbps` :
                    'Loading...'}
                </Typography>
                <Typography variant="body2">
                  Average Active Devices: {historicalData && historicalData.dataPoints && Array.isArray(historicalData.dataPoints) && historicalData.dataPoints.length > 0 ?
                    Math.round(historicalData.dataPoints.reduce((sum, dp) => sum + (dp.network?.activeDevices || 0), 0) / historicalData.dataPoints.length) :
                    'Loading...'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Helper function to determine status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
      case 'online':
      case 'operational':
        return 'success';
      case 'warning':
      case 'partial':
        return 'warning';
      case 'error':
      case 'offline':
      case 'critical':
        return 'error';
      default:
        return 'disabled';
    }
  };

  // Render tracking tab content
  const renderTracking = () => {
    return (
      <Box sx={{ mt: 3 }}>
        <TrackingItemsList
          items={trackingItems}
          buildings={buildings}
          loading={trackingItemsLoading}
          error={trackingItemsError}
          onAddItem={handleAddTrackingItem}
          onEditItem={handleEditTrackingItem}
          onViewItem={handleViewTrackingItem}
          onDeleteItem={handleDeleteTrackingItemConfirm}
          onFilterChange={handleTrackingItemFilterChange}
        />
        
        {/* Tracking Item Form Dialog */}
        <TrackingItemForm
          open={trackingItemFormOpen}
          onClose={() => setTrackingItemFormOpen(false)}
          onSave={handleSaveTrackingItem}
          item={selectedTrackingItem}
          buildings={buildings}
          isLoading={trackingItemsLoading}
        />
        
        {/* Tracking Item Details Dialog */}
        <TrackingItemDetails
          open={trackingItemDetailsOpen}
          onClose={() => setTrackingItemDetailsOpen(false)}
          onEdit={handleEditTrackingItem}
          item={selectedTrackingItem}
        />
        
        {/* Confirm Delete Dialog */}
        <Dialog
          open={confirmDeleteOpen}
          onClose={() => setConfirmDeleteOpen(false)}
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete this tracking item?
              {selectedTrackingItem && (
                <Box component="span" fontWeight="bold">
                  {" "}{selectedTrackingItem.name}
                </Box>
              )}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setConfirmDeleteOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleDeleteTrackingItem} 
              color="error"
              variant="contained"
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 0:
        return renderDashboard();
      case 1:
        return renderAccessControl();
      case 2:
        return renderClimateControl();
      case 3:
        return renderSecurity();
      case 4:
        return renderNetwork();
      case 5:
        return <RoomScheduling />;
      case 6:
        return <FloorPlanViewer />;
      case 7:
        return renderAutomation();
      case 8:
        return renderAnalytics();
      case 9:
        return renderTracking();
      default:
        return renderDashboard();
    }
  };

  // Wrap all return statements with PermissionCheck
  if (loading) {
    return (
      <PermissionCheck requiredPermission="buildingManagement:read">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      </PermissionCheck>
    );
  }

  if (error) {
    return (
      <PermissionCheck requiredPermission="buildingManagement:read">
        <Box sx={{ mt: 3 }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      </PermissionCheck>
    );
  }

  return (
    <PermissionCheck requiredPermission="buildingManagement:read">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Building Management System
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Manage and monitor all building systems from a central dashboard.
        </Typography>

        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<DashboardIcon />} label="Dashboard" />
            <Tab icon={<MeetingRoomIcon />} label="Access Control" />
            <Tab icon={<ThermostatIcon />} label="Climate" />
            <Tab icon={<SecurityIcon />} label="Security" />
            <Tab icon={<RouterIcon />} label="Network" />
            <Tab icon={<CalendarTodayIcon />} label="Room Scheduling" />
            <Tab icon={<MapIcon />} label="Floor Plans" />
            <Tab icon={<AutomationIcon />} label="Automation" />
            <Tab icon={<AnalyticsIcon />} label="Analytics" />
            <Tab icon={<AssignmentIcon />} label="Tracking" />
            <Tab icon={<ContactsIcon />} label="Phone Book" />
          </Tabs>
        </Paper>

        {renderContent()}
      </Box>
    </PermissionCheck>
  );
};

export default BuildingManagementPage;
