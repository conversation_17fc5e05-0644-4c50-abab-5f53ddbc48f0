import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardHeader, 
  Button, 
  Switch, 
  FormControlLabel, 
  TextField, 
  Divider,
  Alert,
  CircularProgress,
  Snackbar,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import buildingManagementService from '../../services/buildingManagementService';

// Icons
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import SecurityIcon from '@mui/icons-material/Security';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import RouterIcon from '@mui/icons-material/Router';
import VideocamIcon from '@mui/icons-material/Videocam';
import SaveIcon from '@mui/icons-material/Save';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import NotificationsIcon from '@mui/icons-material/Notifications';
import SettingsIcon from '@mui/icons-material/Settings';
import AutomationIcon from '@mui/icons-material/SmartToy';
import ScheduleIcon from '@mui/icons-material/Schedule';
import AnalyticsIcon from '@mui/icons-material/Analytics';

const BuildingManagementSetup = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [settings, setSettings] = useState({
    dashboardRefreshInterval: 60,
    defaultView: 'dashboard',
    notifications: {
      email: true,
      push: true
    },
    automation: {
      enabled: true,
      defaultRuleSettings: {
        notifyOnTrigger: true,
        logEvents: true
      },
      scheduleSettings: {
        allowWeekendSchedules: true,
        allowHolidaySchedules: true,
        defaultStartTime: '08:00',
        defaultEndTime: '18:00'
      },
      analytics: {
        collectData: true,
        retentionPeriod: 90 // days
      }
    }
  });
  const [integrations, setIntegrations] = useState({
    dreo: { enabled: false },
    lenelS2NetBox: { enabled: false },
    unifiAccess: { enabled: false },
    unifiNetwork: { enabled: false },
    unifiProtect: { enabled: false }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get current settings
        const currentSettings = await buildingManagementService.getSettings();
        setSettings(currentSettings);

        // Get integration statuses
        const integrationsStatus = await buildingManagementService.getIntegrationsStatus();

        // Convert active status to enabled for the form
        // Start with a copy of the current state to ensure all integrations are preserved
        const formattedIntegrations = { ...integrations };

        // Update with data from API response
        Object.keys(integrationsStatus).forEach(key => {
          formattedIntegrations[key] = {
            enabled: integrationsStatus[key].active,
            status: integrationsStatus[key]
          };
        });

        // Ensure all integrations have a status property
        Object.keys(formattedIntegrations).forEach(key => {
          if (!formattedIntegrations[key].status) {
            formattedIntegrations[key].status = { message: "Status unavailable" };
          }
        });

        setIntegrations(formattedIntegrations);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching building management setup data:', err);
        setError('Failed to load building management setup data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleSettingsChange = (event) => {
    // Ensure event and event.target exist before destructuring
    if (!event || !event.target) {
      console.error('Invalid event object in handleSettingsChange');
      return;
    }

    const { name, value } = event.target;

    // Ensure settings exists before updating
    if (!settings) {
      console.error('Settings is undefined');
      return;
    }

    setSettings({
      ...settings,
      [name]: value
    });
  };

  const handleNotificationChange = (event) => {
    // Ensure event and event.target exist before destructuring
    if (!event || !event.target) {
      console.error('Invalid event object in handleNotificationChange');
      return;
    }

    const { name, checked } = event.target;

    // Ensure settings and settings.notifications exist before updating
    if (!settings || !settings.notifications) {
      console.error('Settings or settings.notifications is undefined');
      return;
    }

    setSettings({
      ...settings,
      notifications: {
        ...settings.notifications,
        [name]: checked
      }
    });
  };

  // Ensure any object E has notifications before accessing E.notifications.email
  const safeGetEmailNotification = (E) => {
    if (!E || !E.notifications) {
      console.warn('Object or notifications property is undefined');
      return false;
    }
    return E.notifications.email || false;
  };

  const handleAutomationToggle = (event) => {
    // Ensure event and event.target exist before destructuring
    if (!event || !event.target) {
      console.error('Invalid event object in handleAutomationToggle');
      return;
    }

    const { checked } = event.target;

    // Ensure settings and settings.automation exist before updating
    if (!settings || !settings.automation) {
      console.error('Settings or settings.automation is undefined');
      return;
    }

    setSettings({
      ...settings,
      automation: {
        ...settings.automation,
        enabled: checked
      }
    });
  };

  const handleAutomationSettingChange = (section, name, value) => {
    // Ensure all parameters exist
    if (!section || !name || value === undefined) {
      console.error('Missing parameters in handleAutomationSettingChange');
      return;
    }

    // Ensure settings and settings.automation exist before updating
    if (!settings || !settings.automation) {
      console.error('Settings or settings.automation is undefined');
      return;
    }

    // Ensure the section exists in settings.automation
    if (!settings.automation[section]) {
      console.error(`Section ${section} does not exist in settings.automation`);
      return;
    }

    setSettings({
      ...settings,
      automation: {
        ...settings.automation,
        [section]: {
          ...settings.automation[section],
          [name]: value
        }
      }
    });
  };

  const handleIntegrationChange = (integration, enabled) => {
    // Ensure parameters exist
    if (!integration || enabled === undefined) {
      console.error('Missing parameters in handleIntegrationChange');
      return;
    }

    // Ensure integrations exists before updating
    if (!integrations) {
      console.error('Integrations is undefined');
      return;
    }

    // Ensure the integration exists in integrations
    if (!integrations[integration]) {
      console.error(`Integration ${integration} does not exist in integrations`);
      return;
    }

    setIntegrations({
      ...integrations,
      [integration]: {
        ...integrations[integration],
        enabled
      }
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Save settings
      await buildingManagementService.saveSettings(settings);

      // Save integration settings
      // This would typically update which integrations are enabled for the building management system
      // For now, we'll just simulate success

      setSaving(false);
      setSnackbar({
        open: true,
        message: 'Building management settings saved successfully',
        severity: 'success'
      });

      // Navigate back to the main building management page after a short delay
      setTimeout(() => {
        navigate('/building-management');
      }, 2000);
    } catch (err) {
      console.error('Error saving building management settings:', err);
      setSaving(false);
      setSnackbar({
        open: true,
        message: 'Error saving settings. Please try again.',
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Building Management Setup
        </Typography>
        <Button 
          component={Link} 
          to="/building-management" 
          startIcon={<ArrowBackIcon />}
          variant="outlined"
        >
          Back to Dashboard
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* Integrations */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Building Management Integrations" 
              subheader="Integrations are auto-enabled when configured. Use their configuration pages to set them up."
            />
            <CardContent>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <ThermostatIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Dreo Climate Control" 
                    secondary={integrations.dreo?.status?.message || "Control HVAC and climate systems"}
                  />
                  <ListItemSecondaryAction>
                    <Typography variant="body2" color="text.secondary">Auto-enabled when configured</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <MeetingRoomIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Lenel S2 NetBox Access Control" 
                    secondary={integrations.lenelS2NetBox?.status?.message || "Manage building access and security"}
                  />
                  <ListItemSecondaryAction>
                    <Typography variant="body2" color="text.secondary">Auto-enabled when configured</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <MeetingRoomIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="UniFi Access Control" 
                    secondary={integrations.unifiAccess?.status?.message || "Manage building access with UniFi"}
                  />
                  <ListItemSecondaryAction>
                    <Typography variant="body2" color="text.secondary">Auto-enabled when configured</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <VideocamIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="UniFi Protect Security Cameras" 
                    secondary={integrations.unifiProtect?.status?.message || "Monitor building security cameras"}
                  />
                  <ListItemSecondaryAction>
                    <Typography variant="body2" color="text.secondary">Auto-enabled when configured</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <RouterIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="UniFi Network" 
                    secondary={integrations.unifiNetwork?.status?.message || "Manage building network infrastructure"}
                  />
                  <ListItemSecondaryAction>
                    <Typography variant="body2" color="text.secondary">Auto-enabled when configured</Typography>
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Dashboard Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Dashboard Settings" 
              subheader="Configure how the building management dashboard works"
              avatar={<SettingsIcon />}
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Dashboard Refresh Interval (seconds)"
                    name="dashboardRefreshInterval"
                    type="number"
                    value={settings.dashboardRefreshInterval}
                    onChange={handleSettingsChange}
                    helperText="How often the dashboard should refresh data"
                    InputProps={{ inputProps: { min: 10, max: 3600 } }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Default View"
                    name="defaultView"
                    select
                    SelectProps={{ native: true }}
                    value={settings.defaultView}
                    onChange={handleSettingsChange}
                    helperText="Default tab to show when opening the dashboard"
                  >
                    <option value="dashboard">Dashboard</option>
                    <option value="access">Access Control</option>
                    <option value="climate">Climate</option>
                    <option value="security">Security</option>
                    <option value="network">Network</option>
                    <option value="automation">Automation</option>
                    <option value="analytics">Analytics</option>
                  </TextField>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Notification Settings" 
              subheader="Configure how you receive alerts from the building management system"
              avatar={<NotificationsIcon />}
            />
            <CardContent>
              <List>
                <ListItem>
                  <ListItemText 
                    primary="Email Notifications" 
                    secondary="Receive alerts via email"
                  />
                  <ListItemSecondaryAction>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={safeGetEmailNotification(settings)}
                          onChange={handleNotificationChange}
                          name="email"
                          color="primary"
                        />
                      }
                      label={safeGetEmailNotification(settings) ? "Enabled" : "Disabled"}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText 
                    primary="Push Notifications" 
                    secondary="Receive alerts in your browser"
                  />
                  <ListItemSecondaryAction>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications && settings.notifications.push}
                          onChange={handleNotificationChange}
                          name="push"
                          color="primary"
                        />
                      }
                      label={settings.notifications && settings.notifications.push ? "Enabled" : "Disabled"}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Automation Settings */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Automation Settings" 
              subheader="Configure the building automation system"
              avatar={<AutomationIcon />}
            />
            <CardContent>
              <Box sx={{ mb: 3 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.automation && settings.automation.enabled}
                      onChange={handleAutomationToggle}
                      color="primary"
                    />
                  }
                  label={(settings.automation && settings.automation.enabled) ? "Automation System Enabled" : "Automation System Disabled"}
                />
              </Box>

              <Grid container spacing={3}>
                {/* Default Rule Settings */}
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle1" gutterBottom>
                    Default Rule Settings
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemText 
                        primary="Notify on Rule Trigger" 
                        secondary="Send notifications when rules are triggered"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={settings.automation && settings.automation.defaultRuleSettings && settings.automation.defaultRuleSettings.notifyOnTrigger}
                          onChange={(e) => handleAutomationSettingChange('defaultRuleSettings', 'notifyOnTrigger', e.target.checked)}
                          color="primary"
                          disabled={!settings.automation || !settings.automation.enabled}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                    <ListItem>
                      <ListItemText 
                        primary="Log Events" 
                        secondary="Record all automation events in the system log"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={settings.automation && settings.automation.defaultRuleSettings && settings.automation.defaultRuleSettings.logEvents}
                          onChange={(e) => handleAutomationSettingChange('defaultRuleSettings', 'logEvents', e.target.checked)}
                          color="primary"
                          disabled={!settings.automation || !settings.automation.enabled}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                </Grid>

                {/* Schedule Settings */}
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle1" gutterBottom>
                    Schedule Settings
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemText 
                        primary="Allow Weekend Schedules" 
                        secondary="Enable automation rules to run on weekends"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={settings.automation && settings.automation.scheduleSettings && settings.automation.scheduleSettings.allowWeekendSchedules}
                          onChange={(e) => handleAutomationSettingChange('scheduleSettings', 'allowWeekendSchedules', e.target.checked)}
                          color="primary"
                          disabled={!settings.automation || !settings.automation.enabled}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                    <ListItem>
                      <ListItemText 
                        primary="Allow Holiday Schedules" 
                        secondary="Enable automation rules to run on holidays"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={settings.automation && settings.automation.scheduleSettings && settings.automation.scheduleSettings.allowHolidaySchedules}
                          onChange={(e) => handleAutomationSettingChange('scheduleSettings', 'allowHolidaySchedules', e.target.checked)}
                          color="primary"
                          disabled={!settings.automation || !settings.automation.enabled}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                    <ListItem>
                      <TextField
                        label="Default Start Time"
                        type="time"
                        value={settings.automation && settings.automation.scheduleSettings ? settings.automation.scheduleSettings.defaultStartTime : '08:00'}
                        onChange={(e) => handleAutomationSettingChange('scheduleSettings', 'defaultStartTime', e.target.value)}
                        disabled={!settings.automation || !settings.automation.enabled}
                        fullWidth
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          step: 300, // 5 min
                        }}
                      />
                    </ListItem>
                    <ListItem>
                      <TextField
                        label="Default End Time"
                        type="time"
                        value={settings.automation && settings.automation.scheduleSettings ? settings.automation.scheduleSettings.defaultEndTime : '18:00'}
                        onChange={(e) => handleAutomationSettingChange('scheduleSettings', 'defaultEndTime', e.target.value)}
                        disabled={!settings.automation || !settings.automation.enabled}
                        fullWidth
                        InputLabelProps={{
                          shrink: true,
                        }}
                        inputProps={{
                          step: 300, // 5 min
                        }}
                      />
                    </ListItem>
                  </List>
                </Grid>

                {/* Analytics Settings */}
                <Grid item xs={12} md={4}>
                  <Typography variant="subtitle1" gutterBottom>
                    Analytics Settings
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemText 
                        primary="Collect Analytics Data" 
                        secondary="Gather data for building analytics"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          checked={settings.automation && settings.automation.analytics && settings.automation.analytics.collectData}
                          onChange={(e) => handleAutomationSettingChange('analytics', 'collectData', e.target.checked)}
                          color="primary"
                          disabled={!settings.automation || !settings.automation.enabled}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                    <ListItem>
                      <TextField
                        fullWidth
                        label="Data Retention Period (days)"
                        type="number"
                        value={settings.automation && settings.automation.analytics ? settings.automation.analytics.retentionPeriod : 90}
                        onChange={(e) => handleAutomationSettingChange('analytics', 'retentionPeriod', parseInt(e.target.value))}
                        disabled={!settings.automation || !settings.automation.enabled || !(settings.automation.analytics && settings.automation.analytics.collectData)}
                        InputProps={{ inputProps: { min: 1, max: 365 } }}
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Save Button */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </Button>
          </Box>
        </Grid>
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BuildingManagementSetup;
