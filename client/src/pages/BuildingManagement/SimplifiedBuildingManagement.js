import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  HomeWork as BuildingIcon,
  MeetingRoom as RoomIcon,
  Lock as AccessIcon,
  Map as FloorPlanIcon,
  Thermostat as HVACIcon,
  Lightbulb as LightingIcon,
  Security as SecurityIcon,
  CheckCircle as ActiveIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  ArrowForward as ArrowForwardIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  Notifications as AlertsIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const SimplifiedBuildingManagement = () => {
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  
  // Mock data for building status
  const [buildingStatus, setBuildingStatus] = useState({
    temperature: 72,
    humidity: 45,
    occupancy: 127,
    activeAlerts: 2,
    roomsInUse: 8,
    totalRooms: 15,
    doorsSecured: 42,
    totalDoors: 45,
    lightsOn: 34,
    totalLights: 89
  });

  // Quick access cards for main functions
  const quickAccessCards = [
    {
      id: 'room-booking',
      title: 'Room Booking',
      icon: <RoomIcon sx={{ fontSize: 40 }} />,
      description: 'Reserve rooms and manage bookings',
      path: '/room-booking',
      permission: 'roomBooking:read',
      status: 'active',
      stats: `${buildingStatus.roomsInUse}/${buildingStatus.totalRooms} rooms in use`
    },
    {
      id: 'access-control',
      title: 'Access Control',
      icon: <AccessIcon sx={{ fontSize: 40 }} />,
      description: 'Manage door access and security',
      path: '/access-control',
      permission: 'accessControl:read',
      status: 'warning',
      stats: `${buildingStatus.doorsSecured}/${buildingStatus.totalDoors} doors secured`
    },
    {
      id: 'floor-plans',
      title: 'Floor Plans',
      icon: <FloorPlanIcon sx={{ fontSize: 40 }} />,
      description: 'Interactive building floor plans',
      path: '/building-management/floor-plans',
      permission: 'buildingManagement:read',
      status: 'active',
      stats: '3 floors available'
    },
    {
      id: 'hvac',
      title: 'HVAC Control',
      icon: <HVACIcon sx={{ fontSize: 40 }} />,
      description: 'Temperature and climate control',
      path: '/building-management/hvac',
      permission: 'buildingManagement:write',
      status: 'active',
      stats: `${buildingStatus.temperature}°F / ${buildingStatus.humidity}% humidity`
    }
  ];

  // Recent activities
  const recentActivities = [
    { id: 1, type: 'room', action: 'Room 201 reserved', user: 'John Smith', time: '5 minutes ago', icon: <RoomIcon /> },
    { id: 2, type: 'access', action: 'Main door access granted', user: 'Jane Doe', time: '15 minutes ago', icon: <AccessIcon /> },
    { id: 3, type: 'alert', action: 'Temperature alert in Server Room', user: 'System', time: '1 hour ago', icon: <WarningIcon color="warning" /> },
    { id: 4, type: 'room', action: 'Conference Room A released', user: 'Bob Wilson', time: '2 hours ago', icon: <RoomIcon /> },
    { id: 5, type: 'security', action: 'Security check completed', user: 'Security Team', time: '3 hours ago', icon: <SecurityIcon /> }
  ];

  // Upcoming room bookings
  const upcomingBookings = [
    { id: 1, room: 'Conference Room A', time: '2:00 PM - 3:00 PM', organizer: 'Marketing Team', attendees: 8 },
    { id: 2, room: 'Board Room', time: '3:30 PM - 5:00 PM', organizer: 'Leadership', attendees: 12 },
    { id: 3, room: 'Training Room', time: '4:00 PM - 6:00 PM', organizer: 'HR Department', attendees: 20 }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <ActiveIcon color="success" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'error': return <ErrorIcon color="error" />;
      default: return null;
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
          Building Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Monitor and control building systems, room bookings, and access control
        </Typography>
      </Box>

      {/* Status Overview */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="primary">{buildingStatus.occupancy}</Typography>
            <Typography variant="body2" color="text.secondary">Current Occupancy</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="success.main">{buildingStatus.roomsInUse}</Typography>
            <Typography variant="body2" color="text.secondary">Rooms In Use</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color="info.main">{buildingStatus.temperature}°F</Typography>
            <Typography variant="body2" color="text.secondary">Avg Temperature</Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="h4" color={buildingStatus.activeAlerts > 0 ? 'warning.main' : 'text.primary'}>
              {buildingStatus.activeAlerts}
            </Typography>
            <Typography variant="body2" color="text.secondary">Active Alerts</Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Quick Access Cards */}
      <Typography variant="h5" gutterBottom sx={{ mb: 2, fontWeight: 600 }}>
        Quick Actions
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {quickAccessCards.filter(card => !card.permission || hasPermission(card.permission)).map((card) => (
          <Grid item xs={12} sm={6} md={3} key={card.id}>
            <Card 
              sx={{ 
                height: '100%',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
              onClick={() => navigate(card.path)}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box sx={{ color: 'primary.main' }}>
                    {card.icon}
                  </Box>
                  {getStatusIcon(card.status)}
                </Box>
                <Typography variant="h6" component="h3" gutterBottom>
                  {card.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {card.description}
                </Typography>
                <Chip 
                  label={card.stats}
                  size="small"
                  color={getStatusColor(card.status)}
                  variant="outlined"
                />
              </CardContent>
              <CardActions>
                <Button 
                  size="small" 
                  endIcon={<ArrowForwardIcon />}
                >
                  Open
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Tabbed Content Area */}
      <Paper sx={{ mb: 4 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Recent Activity" icon={<AlertsIcon />} iconPosition="start" />
          <Tab label="Upcoming Bookings" icon={<ScheduleIcon />} iconPosition="start" />
          <Tab label="Building Status" icon={<BuildingIcon />} iconPosition="start" />
        </Tabs>

        {/* Recent Activity Tab */}
        {activeTab === 0 && (
          <Box sx={{ p: 3 }}>
            <List>
              {recentActivities.map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem>
                    <ListItemIcon>
                      {activity.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.action}
                      secondary={`${activity.user} • ${activity.time}`}
                    />
                  </ListItem>
                  {index < recentActivities.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button variant="outlined" onClick={() => navigate('/building-management/activity')}>
                View All Activity
              </Button>
            </Box>
          </Box>
        )}

        {/* Upcoming Bookings Tab */}
        {activeTab === 1 && (
          <Box sx={{ p: 3 }}>
            <List>
              {upcomingBookings.map((booking, index) => (
                <React.Fragment key={booking.id}>
                  <ListItem>
                    <ListItemIcon>
                      <RoomIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={booking.room}
                      secondary={
                        <React.Fragment>
                          <Typography component="span" variant="body2" color="text.primary">
                            {booking.time}
                          </Typography>
                          {` — ${booking.organizer}`}
                        </React.Fragment>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Chip
                        icon={<PeopleIcon />}
                        label={`${booking.attendees} attendees`}
                        size="small"
                        variant="outlined"
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < upcomingBookings.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button variant="contained" onClick={() => navigate('/room-booking')}>
                Book a Room
              </Button>
            </Box>
          </Box>
        )}

        {/* Building Status Tab */}
        {activeTab === 2 && (
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>Environmental</Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon><HVACIcon /></ListItemIcon>
                    <ListItemText primary="Temperature" secondary={`${buildingStatus.temperature}°F`} />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><HVACIcon /></ListItemIcon>
                    <ListItemText primary="Humidity" secondary={`${buildingStatus.humidity}%`} />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><LightingIcon /></ListItemIcon>
                    <ListItemText primary="Lighting" secondary={`${buildingStatus.lightsOn}/${buildingStatus.totalLights} lights on`} />
                  </ListItem>
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>Security</Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon><SecurityIcon /></ListItemIcon>
                    <ListItemText primary="Door Security" secondary={`${buildingStatus.doorsSecured}/${buildingStatus.totalDoors} secured`} />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><PeopleIcon /></ListItemIcon>
                    <ListItemText primary="Occupancy" secondary={`${buildingStatus.occupancy} people in building`} />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon><AlertsIcon /></ListItemIcon>
                    <ListItemText 
                      primary="Active Alerts" 
                      secondary={buildingStatus.activeAlerts > 0 ? `${buildingStatus.activeAlerts} alerts require attention` : 'No active alerts'}
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button variant="outlined" sx={{ mr: 2 }} onClick={() => navigate('/building-management/reports')}>
                View Reports
              </Button>
              <Button variant="contained" onClick={() => navigate('/admin/building-management')}>
                Manage Settings
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default SimplifiedBuildingManagement;