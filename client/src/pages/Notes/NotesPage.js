import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  Tooltip,
  Chip,
  Menu,
  MenuItem,
  Divider,
  Stack,
  Autocomplete
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PushPin as PinIcon,
  PushPinOutlined as UnpinnedIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as UncheckedIcon,
  Sort as SortIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import notesService from '../../services/notesService';

/**
 * Notes Page
 * Allows users to create, view, and manage their notes
 */
const NotesPage = () => {
  const { user } = useAuth();
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openNoteDialog, setOpenNoteDialog] = useState(false);
  const [currentNote, setCurrentNote] = useState(null);
  const [noteFormData, setNoteFormData] = useState({
    title: '',
    content: '',
    isCompleted: false,
    isPinned: false,
    isTodo: false,
    tags: [],
    categories: []
  });
  const [sortMenuAnchor, setSortMenuAnchor] = useState(null);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);
  const [sortBy, setSortBy] = useState('updatedAt');
  const [filterBy, setFilterBy] = useState('all');

  // Fetch notes on component mount
  useEffect(() => {
    fetchNotes();
  }, []);

  // Fetch notes from API
  const fetchNotes = async () => {
    try {
      setLoading(true);
      const data = await notesService.getUserNotes();
      setNotes(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching notes:', err);
      setError('Failed to load notes. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the note dialog for creating a new note
  const handleOpenNewNoteDialog = () => {
    setCurrentNote(null);
    setNoteFormData({
      title: '',
      content: '',
      isCompleted: false,
      isPinned: false,
      isTodo: false,
      tags: [],
      categories: []
    });
    setOpenNoteDialog(true);
  };

  // Handle opening the note dialog for editing an existing note
  const handleOpenEditNoteDialog = (note) => {
    setCurrentNote(note);
    setNoteFormData({
      title: note.title,
      content: note.content || '',
      isCompleted: note.isCompleted || false,
      isPinned: note.isPinned || false,
      isTodo: note.isTodo || false,
      tags: note.tags || [],
      categories: note.categories || []
    });
    setOpenNoteDialog(true);
  };

  // Handle closing the note dialog
  const handleCloseNoteDialog = () => {
    setOpenNoteDialog(false);
  };

  // Handle input change for note form
  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;
    setNoteFormData(prev => ({
      ...prev,
      [name]: e.target.type === 'checkbox' ? checked : value
    }));
  };

  // Handle saving a note (create or update)
  const handleSaveNote = async () => {
    try {
      if (currentNote) {
        // Update existing note
        await notesService.updateNote(currentNote._id, noteFormData);
      } else {
        // Create new note
        await notesService.createNote(noteFormData);
      }
      handleCloseNoteDialog();
      fetchNotes();
    } catch (err) {
      console.error('Error saving note:', err);
      setError('Failed to save note. Please try again later.');
    }
  };

  // Handle deleting a note
  const handleDeleteNote = async (id) => {
    try {
      await notesService.deleteNote(id);
      fetchNotes();
    } catch (err) {
      console.error('Error deleting note:', err);
      setError('Failed to delete note. Please try again later.');
    }
  };

  // Handle toggling note completion status (for to-dos)
  const handleToggleCompletion = async (id, currentStatus) => {
    try {
      await notesService.toggleNoteCompletion(id);
      setNotes(notes.map(note => 
        note._id === id ? { ...note, isCompleted: !currentStatus } : note
      ));
    } catch (err) {
      console.error('Error toggling note completion:', err);
      setError('Failed to update note. Please try again later.');
    }
  };

  // Handle toggling note pinned status
  const handleTogglePinned = async (id, currentStatus) => {
    try {
      await notesService.toggleNotePinned(id);
      setNotes(notes.map(note => 
        note._id === id ? { ...note, isPinned: !currentStatus } : note
      ));
    } catch (err) {
      console.error('Error toggling note pinned status:', err);
      setError('Failed to update note. Please try again later.');
    }
  };

  // Handle opening sort menu
  const handleOpenSortMenu = (event) => {
    setSortMenuAnchor(event.currentTarget);
  };

  // Handle closing sort menu
  const handleCloseSortMenu = () => {
    setSortMenuAnchor(null);
  };

  // Handle opening filter menu
  const handleOpenFilterMenu = (event) => {
    setFilterMenuAnchor(event.currentTarget);
  };

  // Handle closing filter menu
  const handleCloseFilterMenu = () => {
    setFilterMenuAnchor(null);
  };

  // Handle changing sort option
  const handleChangeSortBy = (option) => {
    setSortBy(option);
    handleCloseSortMenu();
  };

  // Handle changing filter option
  const handleChangeFilterBy = (option) => {
    setFilterBy(option);
    handleCloseFilterMenu();
  };

  // Sort and filter notes
  const getSortedAndFilteredNotes = () => {
    // First filter notes
    let filteredNotes = [...notes];
    
    if (filterBy === 'pinned') {
      filteredNotes = filteredNotes.filter(note => note.isPinned);
    } else if (filterBy === 'todos') {
      filteredNotes = filteredNotes.filter(note => note.isTodo);
    } else if (filterBy === 'completed') {
      filteredNotes = filteredNotes.filter(note => note.isCompleted);
    } else if (filterBy === 'uncompleted') {
      filteredNotes = filteredNotes.filter(note => note.isTodo && !note.isCompleted);
    }
    
    // Then sort notes
    return filteredNotes.sort((a, b) => {
      // Always show pinned notes first
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      // Then sort by selected option
      if (sortBy === 'title') {
        return a.title.localeCompare(b.title);
      } else if (sortBy === 'createdAt') {
        return new Date(b.createdAt) - new Date(a.createdAt);
      } else {
        // Default: sort by updatedAt
        return new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt);
      }
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Render note card
  const renderNoteCard = (note) => (
    <Card 
      key={note._id} 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        position: 'relative',
        bgcolor: note.isPinned ? 'rgba(255, 235, 59, 0.1)' : 'background.paper'
      }}
    >
      {note.isPinned && (
        <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
          <PinIcon fontSize="small" color="warning" />
        </Box>
      )}
      
      <CardContent sx={{ flexGrow: 1, pt: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 1 }}>
          {note.isTodo && (
            <Checkbox
              checked={note.isCompleted}
              onChange={() => handleToggleCompletion(note._id, note.isCompleted)}
              icon={<UncheckedIcon />}
              checkedIcon={<CheckCircleIcon />}
              sx={{ ml: -1, mr: 1, mt: -0.5 }}
            />
          )}
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ 
              textDecoration: note.isCompleted ? 'line-through' : 'none',
              color: note.isCompleted ? 'text.secondary' : 'text.primary'
            }}
          >
            {note.title}
          </Typography>
        </Box>
        
        <Typography 
          variant="body2" 
          color="text.secondary" 
          sx={{ 
            whiteSpace: 'pre-line',
            textDecoration: note.isCompleted ? 'line-through' : 'none',
            opacity: note.isCompleted ? 0.7 : 1
          }}
        >
          {note.content}
        </Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            {formatDate(note.updatedAt || note.createdAt)}
          </Typography>
          
          {note.isTodo && (
            <Chip 
              label={note.isCompleted ? "Completed" : "To-Do"} 
              size="small" 
              color={note.isCompleted ? "success" : "primary"}
              variant="outlined"
            />
          )}
        </Box>
        
        {/* Display tags and categories */}
        {(note.tags && note.tags.length > 0) || (note.categories && note.categories.length > 0) ? (
          <Box sx={{ mt: 2 }}>
            {/* Tags */}
            {note.tags && note.tags.length > 0 && (
              <Box sx={{ mb: 1 }}>
                <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                  {note.tags.map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag}
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mb: 0.5 }}
                    />
                  ))}
                </Stack>
              </Box>
            )}
            
            {/* Categories */}
            {note.categories && note.categories.length > 0 && (
              <Box>
                <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                  {note.categories.map((category, index) => (
                    <Chip
                      key={index}
                      label={category}
                      size="small"
                      color="secondary"
                      variant="outlined"
                      sx={{ mb: 0.5 }}
                    />
                  ))}
                </Stack>
              </Box>
            )}
          </Box>
        ) : null}
      </CardContent>
      
      <CardActions sx={{ justifyContent: 'flex-end' }}>
        <IconButton 
          size="small" 
          onClick={() => handleTogglePinned(note._id, note.isPinned)}
          title={note.isPinned ? "Unpin note" : "Pin note"}
        >
          {note.isPinned ? <PinIcon fontSize="small" /> : <UnpinnedIcon fontSize="small" />}
        </IconButton>
        <IconButton 
          size="small" 
          onClick={() => handleOpenEditNoteDialog(note)}
          title="Edit note"
        >
          <EditIcon fontSize="small" />
        </IconButton>
        <IconButton 
          size="small" 
          onClick={() => handleDeleteNote(note._id)}
          title="Delete note"
          color="error"
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      </CardActions>
    </Card>
  );

  return (
    <Container maxWidth="lg">
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          My Notes
        </Typography>
        <Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenNewNoteDialog}
            sx={{ mr: 1 }}
          >
            New Note
          </Button>
          
          <IconButton onClick={handleOpenSortMenu} title="Sort notes">
            <SortIcon />
          </IconButton>
          <Menu
            anchorEl={sortMenuAnchor}
            open={Boolean(sortMenuAnchor)}
            onClose={handleCloseSortMenu}
          >
            <MenuItem 
              onClick={() => handleChangeSortBy('updatedAt')}
              selected={sortBy === 'updatedAt'}
            >
              Last Updated
            </MenuItem>
            <MenuItem 
              onClick={() => handleChangeSortBy('createdAt')}
              selected={sortBy === 'createdAt'}
            >
              Date Created
            </MenuItem>
            <MenuItem 
              onClick={() => handleChangeSortBy('title')}
              selected={sortBy === 'title'}
            >
              Title
            </MenuItem>
          </Menu>
          
          <IconButton onClick={handleOpenFilterMenu} title="Filter notes">
            <FilterIcon />
          </IconButton>
          <Menu
            anchorEl={filterMenuAnchor}
            open={Boolean(filterMenuAnchor)}
            onClose={handleCloseFilterMenu}
          >
            <MenuItem 
              onClick={() => handleChangeFilterBy('all')}
              selected={filterBy === 'all'}
            >
              All Notes
            </MenuItem>
            <MenuItem 
              onClick={() => handleChangeFilterBy('pinned')}
              selected={filterBy === 'pinned'}
            >
              Pinned Notes
            </MenuItem>
            <MenuItem 
              onClick={() => handleChangeFilterBy('todos')}
              selected={filterBy === 'todos'}
            >
              To-Do Notes
            </MenuItem>
            <MenuItem 
              onClick={() => handleChangeFilterBy('completed')}
              selected={filterBy === 'completed'}
            >
              Completed
            </MenuItem>
            <MenuItem 
              onClick={() => handleChangeFilterBy('uncompleted')}
              selected={filterBy === 'uncompleted'}
            >
              Uncompleted
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {notes.length === 0 ? (
            <Paper sx={{ p: 5, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No notes found
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Click the "New Note" button to create your first note.
              </Typography>
            </Paper>
          ) : (
            <Grid container spacing={3}>
              {getSortedAndFilteredNotes().map(note => (
                <Grid item xs={12} sm={6} md={4} key={note._id}>
                  {renderNoteCard(note)}
                </Grid>
              ))}
            </Grid>
          )}
        </>
      )}

      {/* Note Dialog (Create/Edit) */}
      <Dialog open={openNoteDialog} onClose={handleCloseNoteDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentNote ? 'Edit Note' : 'New Note'}
        </DialogTitle>
        <DialogContent>
          <TextField
            name="title"
            label="Title"
            fullWidth
            value={noteFormData.title}
            onChange={handleInputChange}
            required
            margin="normal"
          />
          <TextField
            name="content"
            label="Content"
            fullWidth
            multiline
            rows={6}
            value={noteFormData.content}
            onChange={handleInputChange}
            margin="normal"
          />
          <Box sx={{ mt: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  name="isTodo"
                  checked={noteFormData.isTodo}
                  onChange={handleInputChange}
                />
              }
              label="This is a to-do item"
            />
            
            {noteFormData.isTodo && (
              <FormControlLabel
                control={
                  <Checkbox
                    name="isCompleted"
                    checked={noteFormData.isCompleted}
                    onChange={handleInputChange}
                  />
                }
                label="Mark as completed"
              />
            )}
            
            <FormControlLabel
              control={
                <Checkbox
                  name="isPinned"
                  checked={noteFormData.isPinned}
                  onChange={handleInputChange}
                />
              }
              label="Pin this note"
            />
          </Box>
          
          <Stack spacing={2} sx={{ mt: 2 }}>
            <Autocomplete
              multiple
              freeSolo
              options={[]}
              value={noteFormData.tags}
              onChange={(event, newValue) => {
                setNoteFormData({
                  ...noteFormData,
                  tags: newValue
                });
              }}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={option}
                    {...getTagProps({ index })}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  placeholder="Add tags"
                  helperText="Press Enter to add a tag"
                />
              )}
            />
            
            <Autocomplete
              multiple
              freeSolo
              options={[]}
              value={noteFormData.categories}
              onChange={(event, newValue) => {
                setNoteFormData({
                  ...noteFormData,
                  categories: newValue
                });
              }}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={option}
                    {...getTagProps({ index })}
                    color="secondary"
                    variant="outlined"
                    size="small"
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Categories"
                  placeholder="Add categories"
                  helperText="Press Enter to add a category"
                />
              )}
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseNoteDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveNote} 
            variant="contained"
            disabled={!noteFormData.title}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default NotesPage;