import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider
} from '@mui/material';
import { 
  Info as InfoIcon,
  Settings as SettingsIcon,
  CheckCircle as HealthIcon,
  ViewModule as ComponentsIcon,
  Tune as ControlsIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import qsysService from '../../services/qsysService';
import { useAuth } from '../../context/AuthContext';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`qsys-tabpanel-${index}`}
      aria-labelledby={`qsys-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const QsysPage = () => {
  const { hasPermission } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [status, setStatus] = useState(null);
  const [healthStatus, setHealthStatus] = useState(null);
  const [components, setComponents] = useState([]);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [controls, setControls] = useState([]);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [configForm, setConfigForm] = useState({
    host: '',
    port: 1710,
    username: '',
    password: ''
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await qsysService.getConfig();
        setConfigStatus(config);
        if (config && config.host) {
          setConfigForm({
            host: config.host,
            port: config.port || 1710,
            username: config.username || '',
            password: ''
          });
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus || !configStatus.host) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Status
            const statusData = await qsysService.getStatus();
            setStatus(statusData);
            break;
          case 1: // Health
            const healthData = await qsysService.getHealthStatus();
            setHealthStatus(healthData);
            break;
          case 2: // Components
            const componentsData = await qsysService.getComponents();
            setComponents(componentsData);
            break;
          case 3: // Controls
            if (selectedComponent) {
              const controlsData = await qsysService.getControls(selectedComponent);
              setControls(controlsData);
            }
            break;
          default:
            break;
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please check your connection and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus, selectedComponent]);

  // Handle component selection
  const handleComponentSelect = async (componentName) => {
    setSelectedComponent(componentName);
    setLoading(true);
    try {
      const controlsData = await qsysService.getControls(componentName);
      setControls(controlsData);
    } catch (err) {
      console.error(`Error fetching controls for component ${componentName}:`, err);
      setError(`Failed to load controls for ${componentName}.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle control value change
  const handleControlValueChange = async (componentName, controlName, value) => {
    try {
      await qsysService.setControlValue(componentName, controlName, value);
      // Refresh controls after setting value
      const controlsData = await qsysService.getControls(componentName);
      setControls(controlsData);
    } catch (err) {
      console.error(`Error setting value for control ${controlName}:`, err);
      setError(`Failed to set value for ${controlName}.`);
    }
  };

  // Handle config form change
  const handleConfigFormChange = (e) => {
    const { name, value } = e.target;
    setConfigForm({
      ...configForm,
      [name]: value
    });
  };

  // Handle config save
  const handleConfigSave = async () => {
    setLoading(true);
    try {
      await qsysService.saveConfig(configForm);
      const config = await qsysService.getConfig();
      setConfigStatus(config);
      setError(null);
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save configuration. Please check your inputs and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle one-click setup
  const handleOneClickSetup = async () => {
    setLoading(true);
    try {
      await qsysService.oneClickSetup();
      const config = await qsysService.getConfig();
      setConfigStatus(config);
      setError(null);
    } catch (err) {
      console.error('Error setting up Q-sys with one click:', err);
      setError('Failed to set up Q-sys with one click. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Render configuration form
  const renderConfigForm = () => (
    <Box component="form" sx={{ mt: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Host"
            name="host"
            value={configForm.host}
            onChange={handleConfigFormChange}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Port"
            name="port"
            type="number"
            value={configForm.port}
            onChange={handleConfigFormChange}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Username"
            name="username"
            value={configForm.username}
            onChange={handleConfigFormChange}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Password"
            name="password"
            type="password"
            value={configForm.password}
            onChange={handleConfigFormChange}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleConfigSave}
              disabled={loading}
              startIcon={<SaveIcon />}
            >
              Save Configuration
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleOneClickSetup}
              disabled={loading}
            >
              One-Click Setup
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  // Render status information
  const renderStatus = () => {
    if (!status) return <Typography>No status information available.</Typography>;

    return (
      <List>
        {Object.entries(status).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render health status
  const renderHealthStatus = () => {
    if (!healthStatus) return <Typography>No health status information available.</Typography>;

    return (
      <List>
        {Object.entries(healthStatus).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <HealthIcon color={value === 'OK' ? 'success' : 'error'} />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={value}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render components list
  const renderComponents = () => {
    if (!components || components.length === 0) return <Typography>No components available.</Typography>;

    return (
      <List>
        {components.map((component) => (
          <ListItem 
            key={component.name} 
            button 
            onClick={() => handleComponentSelect(component.name)}
            selected={selectedComponent === component.name}
          >
            <ListItemIcon>
              <ComponentsIcon />
            </ListItemIcon>
            <ListItemText
              primary={component.name}
              secondary={component.type}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render controls for selected component
  const renderControls = () => {
    if (!selectedComponent) return <Typography>Please select a component to view its controls.</Typography>;
    if (!controls || controls.length === 0) return <Typography>No controls available for this component.</Typography>;

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Controls for {selectedComponent}
        </Typography>
        <List>
          {controls.map((control) => (
            <ListItem key={control.name}>
              <ListItemIcon>
                <ControlsIcon />
              </ListItemIcon>
              <ListItemText
                primary={control.name}
                secondary={`Type: ${control.type}, Value: ${control.value}`}
              />
              {control.type === 'boolean' && (
                <Switch
                  checked={control.value === true || control.value === 1}
                  onChange={(e) => handleControlValueChange(selectedComponent, control.name, e.target.checked ? 1 : 0)}
                />
              )}
              {control.type === 'number' && (
                <Box sx={{ width: 200 }}>
                  <Slider
                    value={Number(control.value)}
                    min={control.min || 0}
                    max={control.max || 100}
                    step={control.step || 1}
                    onChange={(e, newValue) => handleControlValueChange(selectedComponent, control.name, newValue)}
                  />
                </Box>
              )}
            </ListItem>
          ))}
        </List>
      </Box>
    );
  };

  // If configuration is not set up, show configuration form
  if (!loadingConfig && (!configStatus || !configStatus.host)) {
    return (
      <Container maxWidth="lg">
        <Typography variant="h4" component="h1" gutterBottom>
          Q-sys Core Manager
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom>
            Configuration Required
          </Typography>
          <Typography paragraph>
            Please configure your Q-sys Core Manager connection settings below.
          </Typography>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {renderConfigForm()}
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Q-sys Core Manager
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Status" icon={<InfoIcon />} />
          <Tab label="Health" icon={<HealthIcon />} />
          <Tab label="Components" icon={<ComponentsIcon />} />
          <Tab label="Controls" icon={<ControlsIcon />} />
          <Tab label="Settings" icon={<SettingsIcon />} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderStatus()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderHealthStatus()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderComponents()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderControls()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <Typography variant="h6" gutterBottom>
            Q-sys Core Manager Configuration
          </Typography>
          {renderConfigForm()}
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default QsysPage;