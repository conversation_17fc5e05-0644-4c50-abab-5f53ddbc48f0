import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import mosyleBusinessService from '../../services/mosyleBusinessService';

const MosyleBusinessSetup = () => {
  const [formData, setFormData] = useState({
    domain: '',
    apiKey: '',
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  const { domain, apiKey, username, password } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await mosyleBusinessService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await mosyleBusinessService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await mosyleBusinessService.getConfig();
      setConfigStatus(config);

      // Clear the form
      setFormData({
        domain: '',
        apiKey: '',
        username: '',
        password: ''
      });
    } catch (err) {
      setError('Failed to save Mosyle Business configuration. Please try again.');
      console.error('Error saving Mosyle Business configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Mosyle Business MDM Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Mosyle Business is configured with domain: {configStatus.domain}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Mosyle Business integration is not configured yet. Please provide your API credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Typography variant="body1" paragraph>
            To integrate with Mosyle Business MDM, you need to provide your domain and API credentials. 
          </Typography>

          <Typography variant="body1" paragraph>
            Follow these steps to get your API key:
          </Typography>

          <ol>
            <li>
              <Typography variant="body1" paragraph>
                Log in to your Mosyle Business admin portal
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Go to Settings → API Access
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Generate a new API key
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Copy the generated API key
              </Typography>
            </li>
          </ol>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Mosyle Business configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="domain"
              label="Mosyle Domain"
              name="domain"
              value={domain}
              onChange={onChange}
              autoFocus
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              value={username}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              value={password}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="apiKey"
              label="API Key"
              type="password"
              id="apiKey"
              value={apiKey}
              onChange={onChange}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default MosyleBusinessSetup;