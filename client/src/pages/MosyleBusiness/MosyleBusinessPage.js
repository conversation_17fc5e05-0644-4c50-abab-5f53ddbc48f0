import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Link,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Chip
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import mosyleBusinessService from '../../services/mosyleBusinessService';

// Tab panel component for different sections
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`mosyle-tabpanel-${index}`}
      aria-labelledby={`mosyle-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const MosyleBusinessPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [devices, setDevices] = useState([]);
  const [users, setUsers] = useState([]);
  const [groups, setGroups] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [deviceDetailsOpen, setDeviceDetailsOpen] = useState(false);
  const [deviceDetailsLoading, setDeviceDetailsLoading] = useState(false);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Fetch configuration status and devices on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await mosyleBusinessService.getConfig();
        setConfigStatus(config);

        // If configured, get devices
        if (config) {
          const deviceList = await mosyleBusinessService.getDevices();
          setDevices(deviceList);
        }
      } catch (err) {
        console.error('Error fetching Mosyle Business data:', err);
        setError('Failed to load Mosyle Business data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch users when the Users tab is selected
  useEffect(() => {
    if (activeTab === 1 && configStatus && users.length === 0) {
      const fetchUsers = async () => {
        try {
          setLoading(true);
          const userList = await mosyleBusinessService.getUsers();
          setUsers(userList);
          setError(null);
        } catch (err) {
          console.error('Error fetching Mosyle Business users:', err);
          setError('Failed to load Mosyle Business users. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchUsers();
    }
  }, [activeTab, configStatus, users.length]);

  // Fetch groups when the Groups tab is selected
  useEffect(() => {
    if (activeTab === 2 && configStatus && groups.length === 0) {
      const fetchGroups = async () => {
        try {
          setLoading(true);
          const groupList = await mosyleBusinessService.getGroups();
          setGroups(groupList);
          setError(null);
        } catch (err) {
          console.error('Error fetching Mosyle Business groups:', err);
          setError('Failed to load Mosyle Business groups. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchGroups();
    }
  }, [activeTab, configStatus, groups.length]);

  // Handle viewing device details
  const handleViewDeviceDetails = async (device) => {
    setSelectedDevice(device);
    setDeviceDetailsOpen(true);
    setDeviceDetailsLoading(true);
    
    try {
      const details = await mosyleBusinessService.getDevice(device.id);
      setSelectedDevice(prev => ({ ...prev, details }));
      setError(null);
    } catch (err) {
      console.error(`Error fetching details for device ${device.id}:`, err);
      setError(`Failed to load details for device ${device.name}. Please try again later.`);
    } finally {
      setDeviceDetailsLoading(false);
    }
  };

  // Handle closing device details dialog
  const handleCloseDeviceDetails = () => {
    setDeviceDetailsOpen(false);
    setSelectedDevice(null);
  };

  if (loading && !configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Mosyle Business MDM
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              Mosyle Business integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the Mosyle Business integration, an administrator needs to configure it first.
            </Typography>
            <Button 
              component={RouterLink} 
              to="/mosyle-business/setup" 
              variant="contained" 
              color="primary"
              sx={{ mt: 2 }}
            >
              Go to Setup
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Mosyle Business MDM
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ mb: 3 }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange} 
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab label="Devices" />
            <Tab label="Users" />
            <Tab label="Groups" />
          </Tabs>

          {/* Devices Tab */}
          <TabPanel value={activeTab} index={0}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : Array.isArray(devices) && devices.length > 0 ? (
              <List>
                {devices.map((device, index) => (
                  <React.Fragment key={device.id || index}>
                    <ListItem>
                      <ListItemText 
                        primary={device.name} 
                        secondary={`Type: ${device.type || 'Unknown'} | Status: ${device.status || 'Unknown'}`} 
                      />
                      <Button 
                        variant="outlined" 
                        size="small"
                        onClick={() => handleViewDeviceDetails(device)}
                      >
                        View Details
                      </Button>
                    </ListItem>
                    {index < devices.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body1" sx={{ p: 3 }}>
                No devices found.
              </Typography>
            )}
          </TabPanel>

          {/* Users Tab */}
          <TabPanel value={activeTab} index={1}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : Array.isArray(users) && users.length > 0 ? (
              <List>
                {users.map((user, index) => (
                  <React.Fragment key={user.id || index}>
                    <ListItem>
                      <ListItemText 
                        primary={user.name || user.email || 'Unnamed User'} 
                        secondary={`Email: ${user.email || 'N/A'} | Role: ${user.role || 'N/A'}`} 
                      />
                    </ListItem>
                    {index < users.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body1" sx={{ p: 3 }}>
                No users found.
              </Typography>
            )}
          </TabPanel>

          {/* Groups Tab */}
          <TabPanel value={activeTab} index={2}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : Array.isArray(groups) && groups.length > 0 ? (
              <List>
                {groups.map((group, index) => (
                  <React.Fragment key={group.id || index}>
                    <ListItem>
                      <ListItemText 
                        primary={group.name || 'Unnamed Group'} 
                        secondary={`Members: ${group.memberCount || 0} | Type: ${group.type || 'N/A'}`} 
                      />
                    </ListItem>
                    {index < groups.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Typography variant="body1" sx={{ p: 3 }}>
                No groups found.
              </Typography>
            )}
          </TabPanel>
        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button 
            component={RouterLink} 
            to="/mosyle-business/setup" 
            variant="outlined" 
            color="primary"
          >
            Configuration Settings
          </Button>
        </Box>

        {/* Device Details Dialog */}
        <Dialog 
          open={deviceDetailsOpen} 
          onClose={handleCloseDeviceDetails}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {selectedDevice?.name || 'Device Details'}
          </DialogTitle>
          <DialogContent dividers>
            {deviceDetailsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : selectedDevice ? (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Basic Information" />
                    <CardContent>
                      <Typography variant="body1">
                        <strong>Name:</strong> {selectedDevice.name || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Type:</strong> {selectedDevice.type || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Status:</strong> {selectedDevice.status || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Serial Number:</strong> {selectedDevice.serialNumber || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Model:</strong> {selectedDevice.model || 'N/A'}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="System Information" />
                    <CardContent>
                      <Typography variant="body1">
                        <strong>OS Version:</strong> {selectedDevice.osVersion || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Last Check-in:</strong> {selectedDevice.lastCheckIn || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Battery Level:</strong> {selectedDevice.batteryLevel || 'N/A'}
                      </Typography>
                      <Typography variant="body1">
                        <strong>Storage:</strong> {selectedDevice.storageUsed || 'N/A'} / {selectedDevice.storageTotal || 'N/A'}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                {selectedDevice.details && (
                  <Grid item xs={12}>
                    <Card>
                      <CardHeader title="Additional Details" />
                      <CardContent>
                        <pre style={{ whiteSpace: 'pre-wrap' }}>
                          {JSON.stringify(selectedDevice.details, null, 2)}
                        </pre>
                      </CardContent>
                    </Card>
                  </Grid>
                )}
              </Grid>
            ) : (
              <Typography variant="body1">
                No device details available.
              </Typography>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeviceDetails} color="primary">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default MosyleBusinessPage;