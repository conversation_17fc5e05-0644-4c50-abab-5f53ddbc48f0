import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardHeader, 
  Button, 
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  FormGroup,
  CircularProgress
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import roomSchedulingService from '../../services/roomSchedulingService';

// Icons
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import AddIcon from '@mui/icons-material/Add';
import EventIcon from '@mui/icons-material/Event';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import EventBusyIcon from '@mui/icons-material/EventBusy';
import PeopleIcon from '@mui/icons-material/People';
import SearchIcon from '@mui/icons-material/Search';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';

const RoomBookingPage = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Room scheduling state
  const [rooms, setRooms] = useState([]);
  const [reservations, setReservations] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchParams, setSearchParams] = useState({
    capacity: '',
    features: [],
    buildingId: '',
    floorId: '',
    startTime: '',
    endTime: ''
  });

  const [viewMode, setViewMode] = useState('calendar'); // 'calendar', 'list', 'search'
  const [newReservation, setNewReservation] = useState({
    roomId: '',
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    recurrence: {
      isRecurring: false,
      pattern: 'daily',
      daysOfWeek: [],
      interval: 1,
      endDate: null,
      count: null
    },
    attendees: [],
    metadata: {
      purpose: '',
      numberOfAttendees: 1,
      requiredFeatures: []
    }
  });
  const [showNewReservationForm, setShowNewReservationForm] = useState(false);
  const [pendingApprovals, setPendingApprovals] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Get all rooms
        const roomsData = await roomSchedulingService.getAllRooms();
        setRooms(roomsData);

        // Get today's reservations
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const reservationsData = await roomSchedulingService.getAllReservations({
          startDate: today.toISOString(),
          endDate: tomorrow.toISOString()
        });
        setReservations(reservationsData);

        // Get reservations that require approval
        const pendingReservations = await roomSchedulingService.getAllReservations({
          status: 'pending',
          requiresApproval: true
        });
        setPendingApprovals(pendingReservations);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching room booking data:', err);
        setError('Failed to load room booking data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  const handleRoomSelect = (roomId) => {
    const room = rooms.find(r => r._id === roomId);
    setSelectedRoom(room);
    setNewReservation(prev => ({ ...prev, roomId }));
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
  };

  const handleSearchParamsChange = (e) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({ ...prev, [name]: value }));
  };

  const handleFindAvailableRooms = async () => {
    try {
      const availableRooms = await roomSchedulingService.findAvailableRooms(searchParams);
      setRooms(availableRooms);
    } catch (error) {
      console.error('Error finding available rooms:', error);
    }
  };

  const handleNewReservationChange = (e) => {
    const { name, value } = e.target;
    setNewReservation(prev => ({ ...prev, [name]: value }));
  };

  const handleCreateReservation = async () => {
    try {
      await roomSchedulingService.createReservation(newReservation);
      setShowNewReservationForm(false);

      // Refresh reservations
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const reservationsData = await roomSchedulingService.getAllReservations({
        startDate: today.toISOString(),
        endDate: tomorrow.toISOString()
      });
      setReservations(reservationsData);
    } catch (error) {
      console.error('Error creating reservation:', error);
    }
  };

  const handleApproveReservation = async (id) => {
    try {
      await roomSchedulingService.approveReservation(id);

      // Refresh pending approvals
      const pendingReservations = await roomSchedulingService.getAllReservations({
        status: 'pending',
        requiresApproval: true
      });
      setPendingApprovals(pendingReservations);
    } catch (error) {
      console.error('Error approving reservation:', error);
    }
  };

  const handleRejectReservation = async (id, reason) => {
    try {
      await roomSchedulingService.rejectReservation(id, { rejectionReason: reason });

      // Refresh pending approvals
      const pendingReservations = await roomSchedulingService.getAllReservations({
        status: 'pending',
        requiresApproval: true
      });
      setPendingApprovals(pendingReservations);
    } catch (error) {
      console.error('Error rejecting reservation:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4">
          Room Booking
        </Typography>
        <Box>
          <Button 
            variant={viewMode === 'calendar' ? 'contained' : 'outlined'}
            onClick={() => handleViewModeChange('calendar')}
            startIcon={<CalendarTodayIcon />}
            sx={{ mr: 1 }}
          >
            Calendar
          </Button>
          <Button 
            variant={viewMode === 'list' ? 'contained' : 'outlined'}
            onClick={() => handleViewModeChange('list')}
            startIcon={<EventIcon />}
            sx={{ mr: 1 }}
          >
            Reservations
          </Button>
          <Button 
            variant={viewMode === 'search' ? 'contained' : 'outlined'}
            onClick={() => handleViewModeChange('search')}
            startIcon={<SearchIcon />}
            sx={{ mr: 1 }}
          >
            Find Room
          </Button>
          <Button 
            variant="contained"
            color="primary"
            onClick={() => setShowNewReservationForm(true)}
            startIcon={<AddIcon />}
          >
            New Reservation
          </Button>
        </Box>
      </Box>

      {/* Room Scheduling Content */}
      <Grid container spacing={3}>
        {/* Room List */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardHeader title="Rooms" />
            <CardContent>
              <List>
                {rooms.length > 0 ? (
                  rooms.map(room => (
                    <ListItem 
                      key={room._id}
                      button
                      selected={selectedRoom && selectedRoom._id === room._id}
                      onClick={() => handleRoomSelect(room._id)}
                    >
                      <ListItemIcon>
                        <MeetingRoomIcon color={room.status === 'available' ? 'success' : 'error'} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={room.name} 
                        secondary={`Capacity: ${room.capacity} | Floor: ${room.floorId?.name || 'N/A'}`} 
                      />
                    </ListItem>
                  ))
                ) : (
                  <ListItem>
                    <ListItemText primary="No rooms available" />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Main Content Area */}
        <Grid item xs={12} md={9}>
          {viewMode === 'calendar' && (
            <Card>
              <CardHeader 
                title="Room Calendar" 
                subheader={selectedRoom ? `${selectedRoom.name} - Capacity: ${selectedRoom.capacity}` : 'Select a room to view its calendar'}
              />
              <CardContent>
                {selectedRoom ? (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Reservations for {selectedDate.toLocaleDateString()}
                    </Typography>
                    <List>
                      {reservations.filter(res => res.roomId._id === selectedRoom._id).length > 0 ? (
                        reservations
                          .filter(res => res.roomId._id === selectedRoom._id)
                          .map(reservation => (
                            <ListItem key={reservation._id}>
                              <ListItemIcon>
                                {reservation.status === 'approved' ? (
                                  <EventAvailableIcon color="success" />
                                ) : reservation.status === 'pending' ? (
                                  <EventIcon color="warning" />
                                ) : (
                                  <EventBusyIcon color="error" />
                                )}
                              </ListItemIcon>
                              <ListItemText 
                                primary={reservation.title} 
                                secondary={`${new Date(reservation.startTime).toLocaleTimeString()} - ${new Date(reservation.endTime).toLocaleTimeString()} | ${reservation.userId?.name || 'Unknown'}`} 
                              />
                            </ListItem>
                          ))
                      ) : (
                        <ListItem>
                          <ListItemText primary="No reservations for this room on the selected date" />
                        </ListItem>
                      )}
                    </List>
                  </Box>
                ) : (
                  <Alert severity="info">
                    Please select a room from the list to view its calendar.
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}

          {viewMode === 'list' && (
            <Card>
              <CardHeader title="All Reservations" />
              <CardContent>
                <List>
                  {reservations.length > 0 ? (
                    reservations.map(reservation => (
                      <ListItem key={reservation._id}>
                        <ListItemIcon>
                          {reservation.status === 'approved' ? (
                            <EventAvailableIcon color="success" />
                          ) : reservation.status === 'pending' ? (
                            <EventIcon color="warning" />
                          ) : (
                            <EventBusyIcon color="error" />
                          )}
                        </ListItemIcon>
                        <ListItemText 
                          primary={reservation.title} 
                          secondary={`${new Date(reservation.startTime).toLocaleTimeString()} - ${new Date(reservation.endTime).toLocaleTimeString()} | Room: ${reservation.roomId?.name || 'Unknown'} | Status: ${reservation.status}`} 
                        />
                      </ListItem>
                    ))
                  ) : (
                    <ListItem>
                      <ListItemText primary="No reservations found" />
                    </ListItem>
                  )}
                </List>
              </CardContent>
            </Card>
          )}

          {viewMode === 'search' && (
            <Card>
              <CardHeader title="Find Available Rooms" />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Minimum Capacity"
                      type="number"
                      name="capacity"
                      value={searchParams.capacity}
                      onChange={handleSearchParamsChange}
                      placeholder="Minimum capacity"
                      InputProps={{ inputProps: { min: 1 } }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Start Time"
                      type="datetime-local"
                      name="startTime"
                      value={searchParams.startTime}
                      onChange={handleSearchParamsChange}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="End Time"
                      type="datetime-local"
                      name="endTime"
                      value={searchParams.endTime}
                      onChange={handleSearchParamsChange}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      onClick={handleFindAvailableRooms}
                      startIcon={<SearchIcon />}
                    >
                      Find Available Rooms
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>

      {/* New Reservation Dialog */}
      <Dialog open={showNewReservationForm} onClose={() => setShowNewReservationForm(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Reservation</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Title"
                name="title"
                value={newReservation.title}
                onChange={handleNewReservationChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={newReservation.description}
                onChange={handleNewReservationChange}
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Room</InputLabel>
                <Select
                  name="roomId"
                  value={newReservation.roomId}
                  onChange={handleNewReservationChange}
                  label="Room"
                  required
                >
                  <MenuItem value="">Select a Room</MenuItem>
                  {rooms.map(room => (
                    <MenuItem key={room._id} value={room._id}>{room.name}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Number of Attendees"
                type="number"
                name="metadata.numberOfAttendees"
                value={newReservation.metadata.numberOfAttendees}
                onChange={(e) => setNewReservation(prev => ({
                  ...prev,
                  metadata: {
                    ...prev.metadata,
                    numberOfAttendees: e.target.value
                  }
                }))}
                InputProps={{ inputProps: { min: 1 } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Start Time"
                type="datetime-local"
                name="startTime"
                value={newReservation.startTime}
                onChange={handleNewReservationChange}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="End Time"
                type="datetime-local"
                name="endTime"
                value={newReservation.endTime}
                onChange={handleNewReservationChange}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={newReservation.recurrence.isRecurring}
                    onChange={(e) => setNewReservation(prev => ({
                      ...prev,
                      recurrence: {
                        ...prev.recurrence,
                        isRecurring: e.target.checked
                      }
                    }))}
                  />
                }
                label="Recurring Reservation"
              />
            </Grid>
            {newReservation.recurrence.isRecurring && (
              <>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Recurrence Pattern</InputLabel>
                    <Select
                      value={newReservation.recurrence.pattern}
                      onChange={(e) => setNewReservation(prev => ({
                        ...prev,
                        recurrence: {
                          ...prev.recurrence,
                          pattern: e.target.value
                        }
                      }))}
                      label="Recurrence Pattern"
                    >
                      <MenuItem value="daily">Daily</MenuItem>
                      <MenuItem value="weekly">Weekly</MenuItem>
                      <MenuItem value="monthly">Monthly</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="End Date"
                    type="date"
                    value={newReservation.recurrence.endDate || ''}
                    onChange={(e) => setNewReservation(prev => ({
                      ...prev,
                      recurrence: {
                        ...prev.recurrence,
                        endDate: e.target.value
                      }
                    }))}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNewReservationForm(false)}>Cancel</Button>
          <Button onClick={handleCreateReservation} variant="contained" color="primary">Create Reservation</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoomBookingPage;
