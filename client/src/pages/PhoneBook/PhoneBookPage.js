import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Button, 
  TextField, 
  Grid, 
  IconButton, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  MenuItem, 
  Select, 
  FormControl, 
  InputLabel, 
  Tabs, 
  Tab, 
  Divider,
  Card,
  CardContent,
  CardActions,
  Chip,
  Tooltip,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon, 
  Search as SearchIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  Sync as SyncIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import buildingManagementService from '../../services/buildingManagementService';
import contactSyncService from '../../services/contactSyncService';
import { useAuth } from '../../context/AuthContext';

// Contact categories with colors
const CATEGORIES = [
  { value: 'business', label: 'Business', color: '#4caf50' },
  { value: 'maintenance', label: 'Maintenance', color: '#2196f3' },
  { value: 'support', label: 'Support', color: '#ff9800' },
  { value: 'emergency', label: 'Emergency', color: '#f44336' },
  { value: 'other', label: 'Other', color: '#9e9e9e' }
];

// Get category color
const getCategoryColor = (category) => {
  const found = CATEGORIES.find(c => c.value === category);
  return found ? found.color : '#9e9e9e';
};

// Get category label
const getCategoryLabel = (category) => {
  const found = CATEGORIES.find(c => c.value === category);
  return found ? found.label : 'Other';
};

const PhoneBookPage = () => {
  const { user, hasPermission } = useAuth();
  const isAdmin = user?.roles?.includes('admin');
  const canViewPhoneBook = hasPermission('contacts:read');
  
  // State for contacts
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for filtering and search
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  
  // State for contact form
  const [openContactForm, setOpenContactForm] = useState(false);
  const [currentContact, setCurrentContact] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    category: 'business',
    phone: '',
    email: '',
    company: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    },
    notes: ''
  });
  
  // State for sync
  const [syncTab, setSyncTab] = useState(0);
  const [openSyncDialog, setOpenSyncDialog] = useState(false);
  const [googleSyncStatus, setGoogleSyncStatus] = useState(null);
  const [appleSyncStatus, setAppleSyncStatus] = useState(null);
  const [syncLoading, setSyncLoading] = useState(false);
  
  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Load contacts
  useEffect(() => {
    fetchContacts();
  }, []);
  
  // Fetch contacts
  const fetchContacts = async () => {
    try {
      setLoading(true);
      const params = {};
      
      if (searchTerm) {
        params.search = searchTerm;
      }
      
      if (categoryFilter) {
        params.category = categoryFilter;
      }
      
      const data = await buildingManagementService.getContacts(params);
      setContacts(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching contacts:', err);
      setError('Failed to load contacts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle search
  const handleSearch = () => {
    fetchContacts();
  };
  
  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setCategoryFilter('');
    fetchContacts();
  };
  
  // Open contact form for creating a new contact
  const handleAddContact = () => {
    setCurrentContact(null);
    setFormData({
      name: '',
      category: 'business',
      phone: '',
      email: '',
      company: '',
      address: {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA'
      },
      notes: ''
    });
    setOpenContactForm(true);
  };
  
  // Open contact form for editing an existing contact
  const handleEditContact = (contact) => {
    setCurrentContact(contact);
    setFormData({
      name: contact.name || '',
      category: contact.category || 'business',
      phone: contact.phone || '',
      email: contact.email || '',
      company: contact.company || '',
      address: {
        street: contact.address?.street || '',
        city: contact.address?.city || '',
        state: contact.address?.state || '',
        zipCode: contact.address?.zipCode || '',
        country: contact.address?.country || 'USA'
      },
      notes: contact.notes || ''
    });
    setOpenContactForm(true);
  };
  
  // Handle form input changes
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };
  
  // Save contact (create or update)
  const handleSaveContact = async () => {
    try {
      if (currentContact) {
        // Update existing contact
        await buildingManagementService.updateContact(currentContact._id, formData);
        setSnackbar({
          open: true,
          message: 'Contact updated successfully',
          severity: 'success'
        });
      } else {
        // Create new contact
        await buildingManagementService.createContact(formData);
        setSnackbar({
          open: true,
          message: 'Contact created successfully',
          severity: 'success'
        });
      }
      
      setOpenContactForm(false);
      fetchContacts();
    } catch (err) {
      console.error('Error saving contact:', err);
      setSnackbar({
        open: true,
        message: 'Failed to save contact',
        severity: 'error'
      });
    }
  };
  
  // Delete contact
  const handleDeleteContact = async (contact) => {
    if (window.confirm(`Are you sure you want to delete ${contact.name}?`)) {
      try {
        await buildingManagementService.deleteContact(contact._id);
        setSnackbar({
          open: true,
          message: 'Contact deleted successfully',
          severity: 'success'
        });
        fetchContacts();
      } catch (err) {
        console.error('Error deleting contact:', err);
        setSnackbar({
          open: true,
          message: 'Failed to delete contact',
          severity: 'error'
        });
      }
    }
  };
  
  // Open sync dialog
  const handleOpenSyncDialog = async () => {
    setOpenSyncDialog(true);
    setSyncLoading(true);
    
    try {
      // Get Google sync status
      const googleStatus = await contactSyncService.getGoogleSyncStatus();
      setGoogleSyncStatus(googleStatus);
      
      // Get Apple sync status
      const appleStatus = await contactSyncService.getAppleSyncStatus();
      setAppleSyncStatus(appleStatus);
    } catch (err) {
      console.error('Error fetching sync status:', err);
    } finally {
      setSyncLoading(false);
    }
  };
  
  // Handle Google auth
  const handleGoogleAuth = async () => {
    try {
      const { authUrl } = await contactSyncService.getGoogleAuthUrl();
      window.location.href = authUrl;
    } catch (err) {
      console.error('Error getting Google auth URL:', err);
      setSnackbar({
        open: true,
        message: 'Failed to get Google authentication URL',
        severity: 'error'
      });
    }
  };
  
  // Handle Google sync now
  const handleGoogleSyncNow = async () => {
    try {
      setSyncLoading(true);
      await contactSyncService.syncNowGoogle();
      
      // Refresh status
      const googleStatus = await contactSyncService.getGoogleSyncStatus();
      setGoogleSyncStatus(googleStatus);
      
      setSnackbar({
        open: true,
        message: 'Google sync started',
        severity: 'info'
      });
    } catch (err) {
      console.error('Error starting Google sync:', err);
      setSnackbar({
        open: true,
        message: 'Failed to start Google sync',
        severity: 'error'
      });
    } finally {
      setSyncLoading(false);
    }
  };
  
  // Handle Apple vCard download
  const handleAppleVCardDownload = () => {
    const categories = appleSyncStatus?.syncCategories || [];
    const url = contactSyncService.getAppleVCardUrl(categories.length > 0 ? categories : null);
    window.location.href = url;
  };
  
  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  // If user doesn't have permission, show message
  if (!canViewPhoneBook) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>Phone Book</Typography>
        <Alert severity="warning">
          You don't have permission to access the Phone Book. Please contact your administrator.
        </Alert>
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Phone Book</Typography>
      
      {/* Search and filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Search"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                endAdornment: (
                  <IconButton onClick={handleSearch}>
                    <SearchIcon />
                  </IconButton>
                )
              }}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {CATEGORIES.map((category) => (
                  <MenuItem key={category.value} value={category.value}>
                    {category.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={5}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
              <Button
                variant="outlined"
                startIcon={<SyncIcon />}
                onClick={handleOpenSyncDialog}
              >
                Sync Options
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleAddContact}
              >
                Add Contact
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Contacts list */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : contacts.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">No contacts found</Typography>
          <Typography variant="body2" color="textSecondary">
            {searchTerm || categoryFilter ? 'Try changing your search or filter' : 'Add your first contact to get started'}
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={2}>
          {contacts.map((contact) => (
            <Grid item xs={12} sm={6} md={4} key={contact._id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="h6" component="div">
                      {contact.name}
                    </Typography>
                    <Chip 
                      label={getCategoryLabel(contact.category)} 
                      size="small" 
                      sx={{ 
                        backgroundColor: getCategoryColor(contact.category),
                        color: 'white'
                      }} 
                    />
                  </Box>
                  
                  {contact.company && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BusinessIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">{contact.company}</Typography>
                    </Box>
                  )}
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      <a href={`tel:${contact.phone}`} style={{ textDecoration: 'none', color: 'inherit' }}>
                        {contact.phone}
                      </a>
                    </Typography>
                  </Box>
                  
                  {contact.email && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        <a href={`mailto:${contact.email}`} style={{ textDecoration: 'none', color: 'inherit' }}>
                          {contact.email}
                        </a>
                      </Typography>
                    </Box>
                  )}
                  
                  {contact.notes && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {contact.notes}
                    </Typography>
                  )}
                </CardContent>
                <CardActions>
                  <Button 
                    size="small" 
                    startIcon={<EditIcon />}
                    onClick={() => handleEditContact(contact)}
                  >
                    Edit
                  </Button>
                  {isAdmin && (
                    <Button 
                      size="small" 
                      color="error" 
                      startIcon={<DeleteIcon />}
                      onClick={() => handleDeleteContact(contact)}
                    >
                      Delete
                    </Button>
                  )}
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
      
      {/* Contact form dialog */}
      <Dialog open={openContactForm} onClose={() => setOpenContactForm(false)} maxWidth="md" fullWidth>
        <DialogTitle>{currentContact ? 'Edit Contact' : 'Add Contact'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  onChange={handleFormChange}
                  label="Category"
                >
                  {CATEGORIES.map((category) => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleFormChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Company"
                name="company"
                value={formData.company}
                onChange={handleFormChange}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>Address</Typography>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Street"
                name="address.street"
                value={formData.address.street}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="City"
                name="address.city"
                value={formData.address.city}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="State"
                name="address.state"
                value={formData.address.state}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                label="Zip Code"
                name="address.zipCode"
                value={formData.address.zipCode}
                onChange={handleFormChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="notes"
                value={formData.notes}
                onChange={handleFormChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenContactForm(false)}>Cancel</Button>
          <Button 
            onClick={handleSaveContact} 
            variant="contained" 
            color="primary"
            disabled={!formData.name || !formData.phone}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Sync dialog */}
      <Dialog open={openSyncDialog} onClose={() => setOpenSyncDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Contact Synchronization</DialogTitle>
        <DialogContent>
          {syncLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Tabs 
                value={syncTab} 
                onChange={(e, newValue) => setSyncTab(newValue)}
                sx={{ mb: 2 }}
              >
                <Tab label="Google Contacts" />
                <Tab label="Apple Contacts" />
              </Tabs>
              
              {syncTab === 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>Google Contacts Sync</Typography>
                  
                  {googleSyncStatus?.configured ? (
                    <>
                      <Alert 
                        severity={googleSyncStatus.enabled ? "success" : "warning"}
                        sx={{ mb: 2 }}
                      >
                        {googleSyncStatus.enabled 
                          ? "Google Contacts sync is enabled" 
                          : "Google Contacts sync is disabled"}
                      </Alert>
                      
                      <Typography variant="body1" gutterBottom>
                        Last sync: {googleSyncStatus.lastSyncTime 
                          ? contactSyncService.formatDate(googleSyncStatus.lastSyncTime) 
                          : "Never"}
                      </Typography>
                      
                      <Typography variant="body1" gutterBottom>
                        Status: {contactSyncService.formatSyncStatus(googleSyncStatus.syncStatus).label}
                      </Typography>
                      
                      <Button 
                        variant="contained" 
                        color="primary"
                        onClick={handleGoogleSyncNow}
                        sx={{ mt: 2 }}
                      >
                        Sync Now
                      </Button>
                    </>
                  ) : (
                    <>
                      <Alert severity="info" sx={{ mb: 2 }}>
                        Connect to Google Contacts to sync your contacts
                      </Alert>
                      
                      <Button 
                        variant="contained" 
                        color="primary"
                        onClick={handleGoogleAuth}
                      >
                        Connect to Google
                      </Button>
                    </>
                  )}
                </Box>
              )}
              
              {syncTab === 1 && (
                <Box>
                  <Typography variant="h6" gutterBottom>Apple Contacts Export</Typography>
                  
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Export your contacts as a vCard file to import into Apple Contacts
                  </Alert>
                  
                  <Button 
                    variant="contained" 
                    color="primary"
                    onClick={handleAppleVCardDownload}
                  >
                    Download vCard
                  </Button>
                </Box>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenSyncDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PhoneBookPage;