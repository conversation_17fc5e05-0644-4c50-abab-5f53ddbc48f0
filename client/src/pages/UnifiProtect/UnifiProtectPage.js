import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Link
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import unifiProtectService from '../../services/unifiProtectService';

const UnifiProtectPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [cameras, setCameras] = useState([]);

  // Fetch configuration status and cameras on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await unifiProtectService.getConfig();
        setConfigStatus(config);

        // If configured, get cameras
        if (config) {
          const cameraList = await unifiProtectService.getCameras();
          setCameras(cameraList);
        }
      } catch (err) {
        console.error('Error fetching UniFi Protect data:', err);
        setError('Failed to load UniFi Protect data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            UniFi Protect
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              UniFi Protect integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the UniFi Protect integration, an administrator needs to configure it first.
            </Typography>
            <Typography variant="body1" paragraph>
              Please contact your system administrator to set up the required environment variables.
            </Typography>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          UniFi Protect Cameras
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Cameras
          </Typography>
          
          {cameras.length > 0 ? (
            <List>
              {cameras.map((camera, index) => (
                <React.Fragment key={camera.id}>
                  <ListItem>
                    <ListItemText 
                      primary={camera.name} 
                      secondary={`Status: ${camera.state} | Type: ${camera.type}`} 
                    />
                    <Button 
                      variant="outlined" 
                      size="small"
                      onClick={() => {/* Handle camera action */}}
                    >
                      View Details
                    </Button>
                  </ListItem>
                  {index < cameras.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body1">
              No cameras found.
            </Typography>
          )}
        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Typography variant="caption" color="textSecondary">
            Configuration is managed through environment variables
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default UnifiProtectPage;