import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Button, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  CircularProgress,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Select,
  FormControl,
  InputLabel,
  FormHelperText
} from '@mui/material';
import { 
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Error as ErrorIcon,
  Settings as SettingsIcon,
  Router as RouterIcon,
  Group as GroupIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import radiusService from '../../services/radiusService';
import googleAdminService from '../../services/googleAdminService';
import PageHeader from '../../components/PageHeader';
import LoadingSpinner from '../../components/LoadingSpinner';
import EmptyState from '../../components/EmptyState';

const RadiusPage = () => {
  const { user } = useAuth();
  const isAdmin = user && user.roles && user.roles.includes('admin');

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [status, setStatus] = useState(null);
  const [clients, setClients] = useState([]);
  const [logs, setLogs] = useState([]);
  const [openClientDialog, setOpenClientDialog] = useState(false);
  const [currentClient, setCurrentClient] = useState(null);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [tabValue, setTabValue] = useState(0);

  // VLAN related state
  const [vlans, setVlans] = useState([]);
  const [groupVlanMappings, setGroupVlanMappings] = useState([]);
  const [userVlanMappings, setUserVlanMappings] = useState([]);
  const [googleGroups, setGoogleGroups] = useState([]);
  const [openVlanDialog, setOpenVlanDialog] = useState(false);
  const [currentVlan, setCurrentVlan] = useState(null);
  const [openGroupVlanDialog, setOpenGroupVlanDialog] = useState(false);
  const [currentGroupVlan, setCurrentGroupVlan] = useState(null);
  const [openUserVlanDialog, setOpenUserVlanDialog] = useState(false);
  const [currentUserVlan, setCurrentUserVlan] = useState(null);

  // Form state for client dialog
  const [clientForm, setClientForm] = useState({
    name: '',
    ipAddress: '',
    secret: '',
    description: ''
  });

  // Form state for VLAN dialog
  const [vlanForm, setVlanForm] = useState({
    id: '',
    name: '',
    description: ''
  });

  // Form state for group-to-VLAN mapping dialog
  const [groupVlanForm, setGroupVlanForm] = useState({
    groupId: '',
    groupName: '',
    vlanId: ''
  });

  // Form state for user-to-VLAN mapping dialog
  const [userVlanForm, setUserVlanForm] = useState({
    username: '',
    vlanId: ''
  });

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get RADIUS server status
      const statusData = await radiusService.getStatus();
      setStatus(statusData);

      // Get RADIUS clients (admin only)
      if (isAdmin) {
        const clientsData = await radiusService.getClients();
        setClients(clientsData || []);

        // Get recent auth logs
        const logsData = await radiusService.getAuthLogs({ limit: 10 });
        setLogs(Array.isArray(logsData) ? logsData : []);

        // Get VLAN configurations
        const vlansData = await radiusService.getVlanConfigs();
        setVlans(Array.isArray(vlansData) ? vlansData : []);

        // Get group-to-VLAN mappings
        const groupVlanData = await radiusService.getGroupVlanMappings();
        setGroupVlanMappings(Array.isArray(groupVlanData) ? groupVlanData : []);

        // Get user-to-VLAN mappings
        const userVlanData = await radiusService.getUserVlanMappings();
        setUserVlanMappings(Array.isArray(userVlanData) ? userVlanData : []);

        // TODO: Fetch Google groups when needed
        // This would typically be done when opening the group-to-VLAN mapping dialog
      }
    } catch (err) {
      console.error('Error fetching RADIUS data:', err);
      setError(err.message || 'Failed to load RADIUS data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [isAdmin]);

  const handleRefresh = () => {
    fetchData();
  };

  const handleTestConnection = async () => {
    try {
      setLoading(true);
      const result = await radiusService.testConnection();

      setSnackbar({
        open: true,
        message: result.success 
          ? 'Successfully connected to RADIUS server' 
          : 'Failed to connect to RADIUS server',
        severity: result.success ? 'success' : 'error'
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Connection test failed: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenClientDialog = (client = null) => {
    if (client) {
      setCurrentClient(client);
      setClientForm({
        name: client.name,
        ipAddress: client.ipAddress,
        secret: client.secret,
        description: client.description || ''
      });
    } else {
      setCurrentClient(null);
      setClientForm({
        name: '',
        ipAddress: '',
        secret: '',
        description: ''
      });
    }
    setOpenClientDialog(true);
  };

  const handleCloseClientDialog = () => {
    setOpenClientDialog(false);
    setCurrentClient(null);
  };

  const handleClientFormChange = (e) => {
    const { name, value } = e.target;
    setClientForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // VLAN dialog handlers
  const handleOpenVlanDialog = (vlan = null) => {
    if (vlan) {
      setCurrentVlan(vlan);
      setVlanForm({
        id: vlan.id,
        name: vlan.name,
        description: vlan.description || ''
      });
    } else {
      setCurrentVlan(null);
      setVlanForm({
        id: '',
        name: '',
        description: ''
      });
    }
    setOpenVlanDialog(true);
  };

  const handleCloseVlanDialog = () => {
    setOpenVlanDialog(false);
    setCurrentVlan(null);
  };

  const handleVlanFormChange = (e) => {
    const { name, value } = e.target;
    setVlanForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveVlan = async () => {
    try {
      setLoading(true);

      if (currentVlan) {
        // Update existing VLAN
        await radiusService.updateVlanConfig(currentVlan.id, vlanForm);
        setSnackbar({
          open: true,
          message: 'VLAN configuration updated successfully',
          severity: 'success'
        });
      } else {
        // Add new VLAN
        await radiusService.addVlanConfig(vlanForm);
        setSnackbar({
          open: true,
          message: 'VLAN configuration added successfully',
          severity: 'success'
        });
      }

      handleCloseVlanDialog();
      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error saving VLAN configuration: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVlan = async (vlanId) => {
    if (!window.confirm('Are you sure you want to delete this VLAN configuration?')) {
      return;
    }

    try {
      setLoading(true);
      await radiusService.deleteVlanConfig(vlanId);

      setSnackbar({
        open: true,
        message: 'VLAN configuration deleted successfully',
        severity: 'success'
      });

      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error deleting VLAN configuration: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Group-to-VLAN mapping dialog handlers
  const handleOpenGroupVlanDialog = async (mapping = null) => {
    try {
      // Fetch Google groups
      const groups = await googleAdminService.listGroups();
      setGoogleGroups(groups || []);

      if (mapping) {
        setCurrentGroupVlan(mapping);
        setGroupVlanForm({
          groupId: mapping.groupId,
          groupName: mapping.groupName,
          vlanId: mapping.vlanId
        });
      } else {
        setCurrentGroupVlan(null);
        setGroupVlanForm({
          groupId: '',
          groupName: '',
          vlanId: ''
        });
      }
      setOpenGroupVlanDialog(true);
    } catch (err) {
      console.error('Error fetching Google groups:', err);
      setSnackbar({
        open: true,
        message: `Error fetching Google groups: ${err.message}`,
        severity: 'error'
      });

      // Still open the dialog even if we couldn't fetch groups
      if (mapping) {
        setCurrentGroupVlan(mapping);
        setGroupVlanForm({
          groupId: mapping.groupId,
          groupName: mapping.groupName,
          vlanId: mapping.vlanId
        });
      } else {
        setCurrentGroupVlan(null);
        setGroupVlanForm({
          groupId: '',
          groupName: '',
          vlanId: ''
        });
      }
      setOpenGroupVlanDialog(true);
    }
  };

  const handleCloseGroupVlanDialog = () => {
    setOpenGroupVlanDialog(false);
    setCurrentGroupVlan(null);
  };

  const handleGroupVlanFormChange = (e) => {
    const { name, value } = e.target;
    setGroupVlanForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveGroupVlan = async () => {
    try {
      setLoading(true);

      if (currentGroupVlan) {
        // Update existing mapping
        await radiusService.updateGroupVlanMapping(currentGroupVlan.id, groupVlanForm);
        setSnackbar({
          open: true,
          message: 'Group-to-VLAN mapping updated successfully',
          severity: 'success'
        });
      } else {
        // Add new mapping
        await radiusService.addGroupVlanMapping(groupVlanForm);
        setSnackbar({
          open: true,
          message: 'Group-to-VLAN mapping added successfully',
          severity: 'success'
        });
      }

      handleCloseGroupVlanDialog();
      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error saving Group-to-VLAN mapping: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteGroupVlan = async (mappingId) => {
    if (!window.confirm('Are you sure you want to delete this Group-to-VLAN mapping?')) {
      return;
    }

    try {
      setLoading(true);
      await radiusService.deleteGroupVlanMapping(mappingId);

      setSnackbar({
        open: true,
        message: 'Group-to-VLAN mapping deleted successfully',
        severity: 'success'
      });

      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error deleting Group-to-VLAN mapping: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // User-to-VLAN mapping dialog handlers
  const handleOpenUserVlanDialog = (mapping = null) => {
    if (mapping) {
      setCurrentUserVlan(mapping);
      setUserVlanForm({
        username: mapping.username,
        vlanId: mapping.vlanId
      });
    } else {
      setCurrentUserVlan(null);
      setUserVlanForm({
        username: '',
        vlanId: ''
      });
    }
    setOpenUserVlanDialog(true);
  };

  const handleCloseUserVlanDialog = () => {
    setOpenUserVlanDialog(false);
    setCurrentUserVlan(null);
  };

  const handleUserVlanFormChange = (e) => {
    const { name, value } = e.target;
    setUserVlanForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveUserVlan = async () => {
    try {
      setLoading(true);

      if (currentUserVlan) {
        // Update existing mapping
        await radiusService.updateUserVlanMapping(currentUserVlan.id, userVlanForm);
        setSnackbar({
          open: true,
          message: 'User-to-VLAN mapping updated successfully',
          severity: 'success'
        });
      } else {
        // Add new mapping
        await radiusService.addUserVlanMapping(userVlanForm);
        setSnackbar({
          open: true,
          message: 'User-to-VLAN mapping added successfully',
          severity: 'success'
        });
      }

      handleCloseUserVlanDialog();
      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error saving User-to-VLAN mapping: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUserVlan = async (mappingId) => {
    if (!window.confirm('Are you sure you want to delete this User-to-VLAN mapping?')) {
      return;
    }

    try {
      setLoading(true);
      await radiusService.deleteUserVlanMapping(mappingId);

      setSnackbar({
        open: true,
        message: 'User-to-VLAN mapping deleted successfully',
        severity: 'success'
      });

      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error deleting User-to-VLAN mapping: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveClient = async () => {
    try {
      setLoading(true);

      if (currentClient) {
        // Update existing client
        await radiusService.updateClient(currentClient.id, clientForm);
        setSnackbar({
          open: true,
          message: 'RADIUS client updated successfully',
          severity: 'success'
        });
      } else {
        // Add new client
        await radiusService.addClient(clientForm);
        setSnackbar({
          open: true,
          message: 'RADIUS client added successfully',
          severity: 'success'
        });
      }

      handleCloseClientDialog();
      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error saving RADIUS client: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClient = async (clientId) => {
    if (!window.confirm('Are you sure you want to delete this RADIUS client?')) {
      return;
    }

    try {
      setLoading(true);
      await radiusService.deleteClient(clientId);

      setSnackbar({
        open: true,
        message: 'RADIUS client deleted successfully',
        severity: 'success'
      });

      fetchData();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Error deleting RADIUS client: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading && !status) {
    return <LoadingSpinner />;
  }

  return (
    <Box sx={{ p: 3 }}>
      <PageHeader 
        title="RADIUS Server" 
        subtitle="Manage your RADIUS server with Google SAML or Google API authentication"
        icon={<SettingsIcon fontSize="large" />}
      />

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button 
          variant="outlined" 
          startIcon={<RefreshIcon />} 
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>

        <Button 
          variant="outlined" 
          onClick={handleTestConnection}
          disabled={loading}
        >
          Test Connection
        </Button>

        {isAdmin && (
          <Button 
            variant="contained" 
            component={Link} 
            to="/radius/setup"
            startIcon={<SettingsIcon />}
          >
            Setup
          </Button>
        )}
      </Box>

      {/* Server Status Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Server Status
          </Typography>

          {status ? (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="subtitle2">Status</Typography>
                <Chip 
                  label={status.running ? "Running" : "Stopped"} 
                  color={status.running ? "success" : "error"} 
                  size="small"
                  icon={status.running ? <CheckIcon /> : <ErrorIcon />}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="subtitle2">Authentication Method</Typography>
                <Typography>{status.authType === 'google_saml' ? 'Google SAML' : 'Google API'}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="subtitle2">Active Clients</Typography>
                <Typography>{status.activeClients || 0}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="subtitle2">Uptime</Typography>
                <Typography>{status.uptime || 'N/A'}</Typography>
              </Grid>
            </Grid>
          ) : (
            <Typography color="text.secondary">
              Server status information not available
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Tabs for different sections */}
      {isAdmin && (
        <Box sx={{ mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="RADIUS management tabs">
            <Tab label="Clients" icon={<RouterIcon />} iconPosition="start" />
            <Tab label="VLANs" icon={<SettingsIcon />} iconPosition="start" />
            <Tab label="Group VLAN Mappings" icon={<GroupIcon />} iconPosition="start" />
            <Tab label="User VLAN Mappings" icon={<PersonIcon />} iconPosition="start" />
            <Tab label="Logs" icon={<RefreshIcon />} iconPosition="start" />
          </Tabs>
        </Box>
      )}

      {/* RADIUS Clients Section (Admin Only) */}
      {isAdmin && tabValue === 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                RADIUS Clients
              </Typography>
              <Button 
                startIcon={<AddIcon />} 
                variant="outlined" 
                size="small"
                onClick={() => handleOpenClientDialog()}
              >
                Add Client
              </Button>
            </Box>

            {clients.length > 0 ? (
              <List>
                {clients.map((client) => (
                  <React.Fragment key={client.id}>
                    <ListItem>
                      <ListItemText 
                        primary={client.name} 
                        secondary={
                          <>
                            <Typography component="span" variant="body2">
                              IP: {client.ipAddress}
                            </Typography>
                            <br />
                            <Typography component="span" variant="body2" color="text.secondary">
                              {client.description}
                            </Typography>
                          </>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton edge="end" onClick={() => handleOpenClientDialog(client)}>
                          <EditIcon />
                        </IconButton>
                        <IconButton edge="end" onClick={() => handleDeleteClient(client.id)}>
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <EmptyState 
                message="No RADIUS clients configured" 
                description="Add a RADIUS client to allow devices to authenticate"
              />
            )}
          </CardContent>
        </Card>
      )}

      {/* VLAN Configurations Section (Admin Only) */}
      {isAdmin && tabValue === 1 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                VLAN Configurations
              </Typography>
              <Button 
                startIcon={<AddIcon />} 
                variant="outlined" 
                size="small"
                onClick={() => handleOpenVlanDialog()}
              >
                Add VLAN
              </Button>
            </Box>

            {vlans.length > 0 ? (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {vlans.map((vlan) => (
                      <TableRow key={vlan.id}>
                        <TableCell>{vlan.id}</TableCell>
                        <TableCell>{vlan.name}</TableCell>
                        <TableCell>{vlan.description}</TableCell>
                        <TableCell align="right">
                          <IconButton size="small" onClick={() => handleOpenVlanDialog(vlan)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleDeleteVlan(vlan.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <EmptyState 
                message="No VLAN configurations" 
                description="Add a VLAN configuration to assign to users or groups"
              />
            )}
          </CardContent>
        </Card>
      )}

      {/* Group-to-VLAN Mappings Section (Admin Only) */}
      {isAdmin && tabValue === 2 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Group-to-VLAN Mappings
              </Typography>
              <Button 
                startIcon={<AddIcon />} 
                variant="outlined" 
                size="small"
                onClick={() => handleOpenGroupVlanDialog()}
              >
                Add Mapping
              </Button>
            </Box>

            {groupVlanMappings.length > 0 ? (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Group Name</TableCell>
                      <TableCell>VLAN ID</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {groupVlanMappings.map((mapping) => (
                      <TableRow key={mapping.id}>
                        <TableCell>{mapping.groupName}</TableCell>
                        <TableCell>{mapping.vlanId}</TableCell>
                        <TableCell align="right">
                          <IconButton size="small" onClick={() => handleOpenGroupVlanDialog(mapping)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleDeleteGroupVlan(mapping.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <EmptyState 
                message="No group-to-VLAN mappings" 
                description="Add mappings to assign VLANs based on Google user groups"
              />
            )}
          </CardContent>
        </Card>
      )}

      {/* User-to-VLAN Mappings Section (Admin Only) */}
      {isAdmin && tabValue === 3 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                User-to-VLAN Mappings
              </Typography>
              <Button 
                startIcon={<AddIcon />} 
                variant="outlined" 
                size="small"
                onClick={() => handleOpenUserVlanDialog()}
              >
                Add Mapping
              </Button>
            </Box>

            {userVlanMappings.length > 0 ? (
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Username</TableCell>
                      <TableCell>VLAN ID</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userVlanMappings.map((mapping) => (
                      <TableRow key={mapping.id}>
                        <TableCell>{mapping.username}</TableCell>
                        <TableCell>{mapping.vlanId}</TableCell>
                        <TableCell align="right">
                          <IconButton size="small" onClick={() => handleOpenUserVlanDialog(mapping)}>
                            <EditIcon />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleDeleteUserVlan(mapping.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <EmptyState 
                message="No user-to-VLAN mappings" 
                description="Add mappings to manually assign VLANs to specific users"
              />
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Authentication Logs (Admin Only) */}
      {isAdmin && tabValue === 4 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Authentication Logs
            </Typography>

            {logs.length > 0 ? (
              <List>
                {logs.map((log) => (
                  <React.Fragment key={log.id}>
                    <ListItem>
                      <ListItemText 
                        primary={`${log.username} from ${log.clientIp}`} 
                        secondary={
                          <>
                            <Typography component="span" variant="body2">
                              {new Date(log.timestamp).toLocaleString()}
                            </Typography>
                            <br />
                            <Typography component="span" variant="body2" color="text.secondary">
                              Status: {log.status}
                            </Typography>
                          </>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Chip 
                          label={log.success ? "Success" : "Failed"} 
                          color={log.success ? "success" : "error"} 
                          size="small"
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                    <Divider />
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <EmptyState 
                message="No authentication logs" 
                description="Authentication logs will appear here when users authenticate"
              />
            )}
          </CardContent>
        </Card>
      )}

      {/* Client Dialog */}
      <Dialog open={openClientDialog} onClose={handleCloseClientDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentClient ? 'Edit RADIUS Client' : 'Add RADIUS Client'}
        </DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            name="name"
            label="Client Name"
            fullWidth
            variant="outlined"
            value={clientForm.name}
            onChange={handleClientFormChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="ipAddress"
            label="IP Address"
            fullWidth
            variant="outlined"
            value={clientForm.ipAddress}
            onChange={handleClientFormChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="secret"
            label="Shared Secret"
            type="password"
            fullWidth
            variant="outlined"
            value={clientForm.secret}
            onChange={handleClientFormChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            fullWidth
            variant="outlined"
            value={clientForm.description}
            onChange={handleClientFormChange}
            multiline
            rows={2}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseClientDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveClient} 
            variant="contained" 
            disabled={!clientForm.name || !clientForm.ipAddress || !clientForm.secret}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* VLAN Dialog */}
      <Dialog open={openVlanDialog} onClose={handleCloseVlanDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentVlan ? 'Edit VLAN Configuration' : 'Add VLAN Configuration'}
        </DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            name="id"
            label="VLAN ID"
            fullWidth
            variant="outlined"
            value={vlanForm.id}
            onChange={handleVlanFormChange}
            sx={{ mb: 2 }}
            type="number"
            inputProps={{ min: 1, max: 4094 }}
            helperText="VLAN ID (1-4094)"
          />
          <TextField
            margin="dense"
            name="name"
            label="VLAN Name"
            fullWidth
            variant="outlined"
            value={vlanForm.name}
            onChange={handleVlanFormChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            fullWidth
            variant="outlined"
            value={vlanForm.description}
            onChange={handleVlanFormChange}
            multiline
            rows={2}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseVlanDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveVlan} 
            variant="contained" 
            disabled={!vlanForm.id || !vlanForm.name}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Group-to-VLAN Mapping Dialog */}
      <Dialog open={openGroupVlanDialog} onClose={handleCloseGroupVlanDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentGroupVlan ? 'Edit Group-to-VLAN Mapping' : 'Add Group-to-VLAN Mapping'}
        </DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="dense" sx={{ mb: 2 }}>
            <InputLabel id="group-select-label">Google Group</InputLabel>
            <Select
              labelId="group-select-label"
              name="groupId"
              value={groupVlanForm.groupId}
              onChange={handleGroupVlanFormChange}
              label="Google Group"
            >
              {googleGroups.map(group => (
                <MenuItem key={group.id} value={group.id}>
                  {group.name}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              Select a Google group to map to a VLAN
            </FormHelperText>
          </FormControl>

          <TextField
            margin="dense"
            name="groupName"
            label="Group Name (if not in list)"
            fullWidth
            variant="outlined"
            value={groupVlanForm.groupName}
            onChange={handleGroupVlanFormChange}
            sx={{ mb: 2 }}
            helperText="Enter group name manually if not in the dropdown"
          />

          <FormControl fullWidth margin="dense">
            <InputLabel id="vlan-select-label">VLAN</InputLabel>
            <Select
              labelId="vlan-select-label"
              name="vlanId"
              value={groupVlanForm.vlanId}
              onChange={handleGroupVlanFormChange}
              label="VLAN"
            >
              {vlans.map(vlan => (
                <MenuItem key={vlan.id} value={vlan.id}>
                  {vlan.name} (ID: {vlan.id})
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              Select the VLAN to assign to this group
            </FormHelperText>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseGroupVlanDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveGroupVlan} 
            variant="contained" 
            disabled={(!groupVlanForm.groupId && !groupVlanForm.groupName) || !groupVlanForm.vlanId}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* User-to-VLAN Mapping Dialog */}
      <Dialog open={openUserVlanDialog} onClose={handleCloseUserVlanDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {currentUserVlan ? 'Edit User-to-VLAN Mapping' : 'Add User-to-VLAN Mapping'}
        </DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            name="username"
            label="Username"
            fullWidth
            variant="outlined"
            value={userVlanForm.username}
            onChange={handleUserVlanFormChange}
            sx={{ mb: 2 }}
            helperText="Enter the username (email address)"
          />

          <FormControl fullWidth margin="dense">
            <InputLabel id="user-vlan-select-label">VLAN</InputLabel>
            <Select
              labelId="user-vlan-select-label"
              name="vlanId"
              value={userVlanForm.vlanId}
              onChange={handleUserVlanFormChange}
              label="VLAN"
            >
              {vlans.map(vlan => (
                <MenuItem key={vlan.id} value={vlan.id}>
                  {vlan.name} (ID: {vlan.id})
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              Select the VLAN to assign to this user
            </FormHelperText>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUserVlanDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveUserVlan} 
            variant="contained" 
            disabled={!userVlanForm.username || !userVlanForm.vlanId}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RadiusPage;
