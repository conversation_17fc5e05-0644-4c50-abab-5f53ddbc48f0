import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Button, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Divider,
  TextField,
  FormControl,
  FormControlLabel,
  FormLabel,
  RadioGroup,
  Radio,
  CircularProgress,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  Link as MuiLink
} from '@mui/material';
import { 
  Save as SaveIcon,
  Check as CheckIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import radiusService from '../../services/radiusService';
import PageHeader from '../../components/PageHeader';
import LoadingSpinner from '../../components/LoadingSpinner';

const RadiusSetup = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const isAdmin = user && user.roles && user.roles.includes('admin');

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [config, setConfig] = useState(null);
  const [authType, setAuthType] = useState('google_saml');
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [googleAuthUrl, setGoogleAuthUrl] = useState('');
  const [usingEnvVars, setUsingEnvVars] = useState({
    googleClientId: false,
    googleClientSecret: false,
    googleRedirectUri: false
  });

  // Form state for Google SAML configuration
  const [samlConfig, setSamlConfig] = useState({
    entryPoint: '',
    issuer: '',
    cert: '',
    privateKey: ''
  });

  // Form state for Google API configuration
  const [apiConfig, setApiConfig] = useState({
    clientId: '',
    clientSecret: '',
    redirectUri: ''
  });

  // Form state for RADIUS server configuration
  const [serverConfig, setServerConfig] = useState({
    port: 1812,
    authPort: 1812,
    acctPort: 1813,
    secret: '',
    nasIdentifier: 'RADIUS-Server'
  });

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Get RADIUS server configuration
      const configData = await radiusService.getConfig();

      if (configData) {
        setConfig(configData);
        setAuthType(configData.authType || 'google_saml');
        setTabValue(configData.authType === 'google_api' ? 1 : 0);

        // Set environment variables usage information if available
        if (configData.usingEnvVars) {
          setUsingEnvVars(configData.usingEnvVars);
        }

        // Set SAML config if available
        if (configData.samlConfig) {
          setSamlConfig({
            entryPoint: configData.samlConfig.entryPoint || '',
            issuer: configData.samlConfig.issuer || '',
            cert: configData.samlConfig.cert || '',
            privateKey: configData.samlConfig.privateKey || ''
          });
        }

        // Set API config if available
        if (configData.googleApiConfig) {
          setApiConfig({
            clientId: configData.googleApiConfig.clientId || '',
            clientSecret: configData.googleApiConfig.clientSecret || '',
            redirectUri: configData.googleApiConfig.redirectUri || ''
          });
        }

        // Set server config
        setServerConfig({
          port: configData.port || 1812,
          authPort: configData.authPort || 1812,
          acctPort: configData.acctPort || 1813,
          secret: configData.secret || '',
          nasIdentifier: configData.nasIdentifier || 'RADIUS-Server'
        });
      }

      // Get Google auth URL if using Google API
      if (authType === 'google_api') {
        try {
          const authUrlData = await radiusService.getGoogleAuthUrl();
          if (authUrlData && authUrlData.authUrl) {
            setGoogleAuthUrl(authUrlData.authUrl);
          }
        } catch (err) {
          console.error('Error getting Google auth URL:', err);
          // Non-critical error, don't set main error state
        }
      }
    } catch (err) {
      console.error('Error fetching RADIUS configuration:', err);
      setError(err.message || 'Failed to load RADIUS configuration');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Redirect non-admin users
    if (!isAdmin) {
      navigate('/radius');
      return;
    }

    fetchData();
  }, [isAdmin, navigate]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setAuthType(newValue === 0 ? 'google_saml' : 'google_api');
  };

  // Handle SAML config changes
  const handleSamlConfigChange = (e) => {
    const { name, value } = e.target;
    setSamlConfig(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle API config changes
  const handleApiConfigChange = (e) => {
    const { name, value } = e.target;
    setApiConfig(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle server config changes
  const handleServerConfigChange = (e) => {
    const { name, value } = e.target;
    setServerConfig(prev => ({
      ...prev,
      [name]: name === 'port' || name === 'authPort' || name === 'acctPort' 
        ? parseInt(value, 10) || '' 
        : value
    }));
  };

  // Save configuration
  const handleSaveConfig = async () => {
    setSaving(true);
    setError(null);

    try {
      const configToSave = {
        authType,
        ...serverConfig,
        samlConfig: authType === 'google_saml' ? samlConfig : undefined,
        googleApiConfig: authType === 'google_api' ? apiConfig : undefined
      };

      await radiusService.updateConfig(configToSave);

      setSnackbar({
        open: true,
        message: 'RADIUS configuration saved successfully',
        severity: 'success'
      });

      // Refresh data
      fetchData();
    } catch (err) {
      console.error('Error saving RADIUS configuration:', err);
      setError(err.message || 'Failed to save RADIUS configuration');

      setSnackbar({
        open: true,
        message: `Error saving configuration: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle Google auth callback
  const handleGoogleAuthCallback = async (code) => {
    try {
      setSaving(true);
      await radiusService.handleGoogleAuthCallback(code);

      setSnackbar({
        open: true,
        message: 'Successfully authenticated with Google',
        severity: 'success'
      });

      // Refresh data
      fetchData();
    } catch (err) {
      console.error('Error handling Google auth callback:', err);
      setError(err.message || 'Failed to authenticate with Google');

      setSnackbar({
        open: true,
        message: `Error authenticating with Google: ${err.message}`,
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle Google auth redirect
  const handleGoogleAuth = () => {
    if (googleAuthUrl) {
      window.open(googleAuthUrl, '_blank');
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Box sx={{ p: 3 }}>
      <PageHeader 
        title="RADIUS Server Setup" 
        subtitle="Configure your RADIUS server with Google SAML or Google API authentication"
      />

      <Box sx={{ mb: 3 }}>
        <Button 
          component={Link} 
          to="/radius" 
          startIcon={<ArrowBackIcon />}
          sx={{ mb: 2 }}
        >
          Back to RADIUS Server
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 3, p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Authentication Method
        </Typography>

        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
          <Tab label="Google SAML" />
          <Tab label="Google API" />
        </Tabs>

        {/* Google SAML Configuration */}
        {tabValue === 0 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Google SAML Configuration
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Configure RADIUS to authenticate users through Google SAML. You'll need to set up SAML in your Google Admin console.
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="SAML Entry Point"
                  name="entryPoint"
                  value={samlConfig.entryPoint}
                  onChange={handleSamlConfigChange}
                  fullWidth
                  margin="normal"
                  helperText="The SAML IdP entry point URL from Google"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="SAML Issuer"
                  name="issuer"
                  value={samlConfig.issuer}
                  onChange={handleSamlConfigChange}
                  fullWidth
                  margin="normal"
                  helperText="The SAML issuer (entity ID) for your application"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="SAML Certificate"
                  name="cert"
                  value={samlConfig.cert}
                  onChange={handleSamlConfigChange}
                  fullWidth
                  margin="normal"
                  multiline
                  rows={4}
                  helperText="The X.509 certificate from Google"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="Private Key"
                  name="privateKey"
                  value={samlConfig.privateKey}
                  onChange={handleSamlConfigChange}
                  fullWidth
                  margin="normal"
                  multiline
                  rows={4}
                  helperText="Your private key for SAML signing"
                />
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Google API Configuration */}
        {tabValue === 1 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Google API Configuration
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Configure RADIUS to authenticate users through Google API. You'll need to create OAuth credentials in the Google Cloud Console.
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Client ID"
                  name="clientId"
                  value={apiConfig.clientId}
                  onChange={handleApiConfigChange}
                  fullWidth
                  margin="normal"
                  disabled={usingEnvVars.googleClientId}
                  helperText={usingEnvVars.googleClientId 
                    ? "Client ID is provided via environment variable" 
                    : "OAuth Client ID from Google Cloud Console"}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  label="Client Secret"
                  name="clientSecret"
                  value={usingEnvVars.googleClientSecret ? '********' : apiConfig.clientSecret}
                  onChange={handleApiConfigChange}
                  fullWidth
                  margin="normal"
                  disabled={usingEnvVars.googleClientSecret}
                  helperText={usingEnvVars.googleClientSecret 
                    ? "Client Secret is provided via environment variable" 
                    : "OAuth Client Secret from Google Cloud Console"}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="Redirect URI"
                  name="redirectUri"
                  value={apiConfig.redirectUri}
                  onChange={handleApiConfigChange}
                  fullWidth
                  margin="normal"
                  disabled={usingEnvVars.googleRedirectUri}
                  helperText={usingEnvVars.googleRedirectUri 
                    ? "Redirect URI is provided via environment variable" 
                    : "OAuth Redirect URI (must match one configured in Google Cloud Console)"}
                />
              </Grid>

              {googleAuthUrl && (
                <Grid item xs={12}>
                  <Box sx={{ mt: 2 }}>
                    <Button 
                      variant="contained" 
                      color="primary" 
                      onClick={handleGoogleAuth}
                    >
                      Authenticate with Google
                    </Button>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      After authenticating, you'll receive a code. Use this code to complete the setup.
                    </Typography>
                  </Box>
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  label="Authorization Code"
                  name="authCode"
                  fullWidth
                  margin="normal"
                  helperText="Enter the authorization code from Google"
                  onChange={(e) => {
                    if (e.target.value) {
                      handleGoogleAuthCallback(e.target.value);
                    }
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>

      {/* RADIUS Server Configuration */}
      <Paper sx={{ mb: 3, p: 2 }}>
        <Typography variant="h6" gutterBottom>
          RADIUS Server Configuration
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <TextField
              label="RADIUS Port"
              name="port"
              type="number"
              value={serverConfig.port}
              onChange={handleServerConfigChange}
              fullWidth
              margin="normal"
              helperText="Main RADIUS server port"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              label="Authentication Port"
              name="authPort"
              type="number"
              value={serverConfig.authPort}
              onChange={handleServerConfigChange}
              fullWidth
              margin="normal"
              helperText="RADIUS authentication port"
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              label="Accounting Port"
              name="acctPort"
              type="number"
              value={serverConfig.acctPort}
              onChange={handleServerConfigChange}
              fullWidth
              margin="normal"
              helperText="RADIUS accounting port"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="Shared Secret"
              name="secret"
              value={serverConfig.secret}
              onChange={handleServerConfigChange}
              fullWidth
              margin="normal"
              helperText="Default shared secret for RADIUS clients"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              label="NAS Identifier"
              name="nasIdentifier"
              value={serverConfig.nasIdentifier}
              onChange={handleServerConfigChange}
              fullWidth
              margin="normal"
              helperText="Network Access Server identifier"
            />
          </Grid>
        </Grid>
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={saving ? <CircularProgress size={24} color="inherit" /> : <SaveIcon />}
          onClick={handleSaveConfig}
          disabled={saving}
        >
          Save Configuration
        </Button>
      </Box>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default RadiusSetup;
