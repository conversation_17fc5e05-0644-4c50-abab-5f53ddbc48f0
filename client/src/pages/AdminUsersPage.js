import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { 
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const AdminUsersPage = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    roles: [],
    isActive: true,
    jobTitle: '',
    department: '',
    phoneNumber: '',
    location: ''
  });


  // Fetch users and roles
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usersRes, rolesRes] = await Promise.all([
          axios.get('/api/users'),
          axios.get('/api/roles')
        ]);
        setUsers(usersRes.data);
        setFilteredUsers(usersRes.data);
        setRoles(rolesRes.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load users and roles');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter users based on search term
  useEffect(() => {
    const filtered = users.filter(user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [users, searchTerm]);

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Open edit dialog
  const handleEditUser = (user) => {
    setSelectedUser(user);

    setFormData({
      name: user.name,
      email: user.email,
      roles: user.roles || [],
      isActive: user.isActive !== false,
      jobTitle: user.jobTitle || '',
      department: user.department || '',
      phoneNumber: user.phoneNumber || '',
      location: user.location || ''
    });

    setEditDialogOpen(true);
  };

  // Open create dialog
  const handleOpenCreateDialog = () => {
    // Reset form data
    setFormData({
      name: '',
      email: '',
      roles: [],
      isActive: true,
      jobTitle: '',
      department: '',
      phoneNumber: '',
      location: ''
    });
    setCreateDialogOpen(true);
  };

  // Close create dialog
  const handleCloseCreateDialog = () => {
    setCreateDialogOpen(false);
    setFormData({
      name: '',
      email: '',
      roles: [],
      isActive: true,
      jobTitle: '',
      department: '',
      phoneNumber: '',
      location: ''
    });
  };

  // Close edit dialog
  const handleCloseDialog = () => {
    setEditDialogOpen(false);
    setSelectedUser(null);
    setFormData({
      name: '',
      email: '',
      roles: [],
      isActive: true,
      jobTitle: '',
      department: '',
      phoneNumber: '',
      location: ''
    });
  };

  // Handle form input changes
  const handleInputChange = (event) => {
    const { name, value, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'isActive' ? checked : value
    }));
  };

  // Handle role selection
  const handleRoleChange = (event) => {
    setFormData(prev => ({
      ...prev,
      roles: event.target.value
    }));
  };


  // Save user changes
  const handleSaveUser = async () => {
    try {
      if (selectedUser) {
        const userData = {
          ...formData
        };

        await axios.put(`/api/users/${selectedUser._id}`, userData);
        // Refresh users list
        const usersRes = await axios.get('/api/users');
        setUsers(usersRes.data);
        setFilteredUsers(usersRes.data);
      }
      handleCloseDialog();
    } catch (err) {
      console.error('Error saving user:', err);
      setError('Failed to save user changes');
    }
  };

  // Create new local user
  const handleCreateUser = async () => {
    try {
      // Validate required fields
      if (!formData.name || !formData.email) {
        setError('Name and email are required');
        return;
      }

      // Prepare user data for API
      const userData = {
        name: formData.name,
        email: formData.email,
        roles: formData.roles,
        jobTitle: formData.jobTitle,
        department: formData.department,
        phoneNumber: formData.phoneNumber,
        location: formData.location
      };

      // Call the API to create a new local user
      const response = await axios.post('/api/users/create-local', userData);
      
      // Show success message
      setError(null);
      alert(`User ${response.data.user.name} created successfully. A welcome email has been sent to ${response.data.user.email}.`);
      
      // Refresh users list
      const usersRes = await axios.get('/api/users');
      setUsers(usersRes.data);
      setFilteredUsers(usersRes.data);
      
      // Close the dialog
      handleCloseCreateDialog();
    } catch (err) {
      console.error('Error creating user:', err);
      setError(err.response?.data?.msg || 'Failed to create user');
    }
  };

  // Delete user
  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await axios.delete(`/api/users/${userId}`);
        // Refresh users list
        const usersRes = await axios.get('/api/users');
        setUsers(usersRes.data);
        setFilteredUsers(usersRes.data);
      } catch (err) {
        console.error('Error deleting user:', err);
        setError('Failed to delete user');
      }
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          User Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage user accounts and permissions
        </Typography>
      </Box>

      {/* Search and filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search users..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => handleOpenCreateDialog()}
            >
              Create Local User
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Users table */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Roles</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user._id}>
                  <TableCell>{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    {user.roles?.map((role) => (
                      <Chip 
                        key={role} 
                        label={role} 
                        size="small" 
                        sx={{ mr: 0.5 }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={user.isActive !== false ? 'Active' : 'Inactive'}
                      color={user.isActive !== false ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                  </TableCell>
                  <TableCell>
                    <IconButton 
                      onClick={() => handleEditUser(user)}
                      size="small"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleDeleteUser(user._id)}
                      size="small"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Edit User Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Edit User</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              margin="normal"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Roles</InputLabel>
              <Select
                multiple
                value={formData.roles}
                onChange={handleRoleChange}
                renderValue={(selected) => selected.join(', ')}
              >
                {roles.map((role) => (
                  <MenuItem key={role.name} value={role.name}>
                    <Checkbox checked={formData.roles.indexOf(role.name) > -1} />
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  name="isActive"
                />
              }
              label="Active"
            />

          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveUser} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Create Local User Dialog */}
      <Dialog open={createDialogOpen} onClose={handleCloseCreateDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Create Local User</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Name *"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Email *"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Roles</InputLabel>
              <Select
                multiple
                value={formData.roles}
                onChange={handleRoleChange}
                renderValue={(selected) => selected.join(', ')}
              >
                {roles.map((role) => (
                  <MenuItem key={role.name} value={role.name}>
                    <Checkbox checked={formData.roles.indexOf(role.name) > -1} />
                    {role.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            {/* Additional User Information */}
            <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
              Additional Information
            </Typography>
            <TextField
              fullWidth
              label="Job Title"
              name="jobTitle"
              value={formData.jobTitle}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Department"
              name="department"
              value={formData.department}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Phone Number"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleInputChange}
              margin="normal"
            />
            <TextField
              fullWidth
              label="Location"
              name="location"
              value={formData.location}
              onChange={handleInputChange}
              margin="normal"
            />
            
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              * Required fields
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Note: A welcome email with setup instructions will be sent to the user's email address.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCreateDialog}>Cancel</Button>
          <Button onClick={handleCreateUser} variant="contained" color="primary">
            Create User
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AdminUsersPage;
