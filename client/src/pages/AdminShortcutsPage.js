import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Snackbar
} from '@mui/material';
import { 
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Link as LinkIcon,
  Category as CategoryIcon,
  Folder as FolderIcon,
  ColorLens as ColorLensIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import * as shortcutCategoryService from '../services/shortcutCategoryService';

const AdminShortcutsPage = () => {
  const { user } = useAuth();
  const [shortcuts, setShortcuts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filteredShortcuts, setFilteredShortcuts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedShortcut, setSelectedShortcut] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    url: '',
    description: '',
    categories: [],
    instructions: '',
    isActive: true,
    isGlobal: true
  });

  // Category management state
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [categoryFormData, setCategoryFormData] = useState({
    name: '',
    description: '',
    icon: 'folder',
    color: '#1976d2'
  });
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [categoryList, setCategoryList] = useState([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Fetch shortcuts and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch shortcuts
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        setFilteredShortcuts(shortcutsRes.data);

        // Fetch categories from the old endpoint for backward compatibility
        const categoriesRes = await axios.get('/api/shortcuts/categories');
        setCategories(categoriesRes.data);

        // Fetch categories from the new endpoint
        await fetchCategoryList();

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load shortcuts');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch category list from the new API
  const fetchCategoryList = async () => {
    try {
      setLoadingCategories(true);
      const categories = await shortcutCategoryService.getCategories();
      setCategoryList(categories);
      setLoadingCategories(false);
    } catch (error) {
      console.error('Error fetching category list:', error);
      setLoadingCategories(false);
    }
  };

  // Filter shortcuts based on search term
  useEffect(() => {
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      const filtered = shortcuts.filter(shortcut => 
        shortcut.title.toLowerCase().includes(term) || 
        (shortcut.description && shortcut.description.toLowerCase().includes(term))
      );
      setFilteredShortcuts(filtered);
    } else {
      setFilteredShortcuts(shortcuts);
    }
  }, [shortcuts, searchTerm]);

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Open edit dialog for new shortcut
  const handleAddShortcut = () => {
    setSelectedShortcut(null);
    setFormData({
      title: '',
      url: '',
      description: '',
      categories: [],
      instructions: '',
      isActive: true,
      isGlobal: true
    });
    setEditDialogOpen(true);
  };

  // Open edit dialog for existing shortcut
  const handleEditShortcut = (shortcut) => {
    setSelectedShortcut(shortcut);
    setFormData({
      title: shortcut.title,
      url: shortcut.url,
      description: shortcut.description || '',
      categories: shortcut.categories || [],
      instructions: shortcut.instructions || '',
      isActive: shortcut.isActive !== false,
      isGlobal: shortcut.isGlobal !== false
    });
    setEditDialogOpen(true);
  };

  // Close edit dialog
  const handleCloseDialog = () => {
    setEditDialogOpen(false);
    setSelectedShortcut(null);
  };

  // Handle form input changes
  const handleInputChange = (event) => {
    const { name, value, checked } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'isActive' ? checked : value
    }));
  };

  // Handle category selection
  const handleCategoryChange = (event) => {
    setFormData(prev => ({
      ...prev,
      categories: event.target.value
    }));
  };

  // Save shortcut changes
  const handleSaveShortcut = async () => {
    try {
      if (selectedShortcut) {
        // Update existing shortcut
        await axios.put(`/api/shortcuts/${selectedShortcut._id}`, formData);
      } else {
        // Create new shortcut
        await axios.post('/api/shortcuts', formData);
      }

      // Refresh shortcuts list
      const shortcutsRes = await axios.get('/api/shortcuts');
      setShortcuts(shortcutsRes.data);
      setFilteredShortcuts(shortcutsRes.data);

      handleCloseDialog();
    } catch (err) {
      console.error('Error saving shortcut:', err);
      setError('Failed to save shortcut');
    }
  };

  // Delete shortcut
  const handleDeleteShortcut = async (shortcutId) => {
    if (window.confirm('Are you sure you want to delete this shortcut?')) {
      try {
        await axios.delete(`/api/shortcuts/${shortcutId}`);

        // Refresh shortcuts list
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        setFilteredShortcuts(shortcutsRes.data);
      } catch (err) {
        console.error('Error deleting shortcut:', err);
        setError('Failed to delete shortcut');
      }
    }
  };

  // Category management functions

  // Open category management dialog
  const handleOpenCategoryDialog = () => {
    setCategoryDialogOpen(true);
  };

  // Close category management dialog
  const handleCloseCategoryDialog = () => {
    setCategoryDialogOpen(false);
    setSelectedCategory(null);
    setCategoryFormData({
      name: '',
      description: '',
      icon: 'folder',
      color: '#1976d2'
    });
  };

  // Open add category dialog
  const handleAddCategory = () => {
    setSelectedCategory(null);
    setCategoryFormData({
      name: '',
      description: '',
      icon: 'folder',
      color: '#1976d2'
    });
    setCategoryDialogOpen(true);
  };

  // Open edit category dialog
  const handleEditCategory = (category) => {
    setSelectedCategory(category);
    setCategoryFormData({
      name: category.name,
      description: category.description || '',
      icon: category.icon || 'folder',
      color: category.color || '#1976d2'
    });
    setCategoryDialogOpen(true);
  };

  // Handle category form input changes
  const handleCategoryInputChange = (event) => {
    const { name, value } = event.target;
    setCategoryFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save category
  const handleSaveCategory = async () => {
    try {
      if (selectedCategory) {
        // Update existing category
        await shortcutCategoryService.updateCategory(selectedCategory._id, categoryFormData);
        setSnackbarMessage('Category updated successfully');
      } else {
        // Create new category
        await shortcutCategoryService.createCategory(categoryFormData);
        setSnackbarMessage('Category created successfully');
      }

      // Refresh category lists
      await fetchCategoryList();
      const categoriesRes = await axios.get('/api/shortcuts/categories');
      setCategories(categoriesRes.data);

      setSnackbarOpen(true);
      handleCloseCategoryDialog();
    } catch (err) {
      console.error('Error saving category:', err);
      setSnackbarMessage('Failed to save category');
      setSnackbarOpen(true);
    }
  };

  // Delete category
  const handleDeleteCategory = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category? This may affect shortcuts using this category.')) {
      try {
        await shortcutCategoryService.deleteCategory(categoryId);

        // Refresh category lists
        await fetchCategoryList();
        const categoriesRes = await axios.get('/api/shortcuts/categories');
        setCategories(categoriesRes.data);

        setSnackbarMessage('Category deleted successfully');
        setSnackbarOpen(true);
      } catch (err) {
        console.error('Error deleting category:', err);
        setSnackbarMessage('Failed to delete category');
        setSnackbarOpen(true);
      }
    }
  };

  // Initialize default categories
  const handleInitializeDefaultCategories = async () => {
    if (window.confirm('This will create default categories if they don\'t exist. Continue?')) {
      try {
        await shortcutCategoryService.initializeDefaultCategories();

        // Refresh category lists
        await fetchCategoryList();
        const categoriesRes = await axios.get('/api/shortcuts/categories');
        setCategories(categoriesRes.data);

        setSnackbarMessage('Default categories initialized');
        setSnackbarOpen(true);
      } catch (err) {
        console.error('Error initializing default categories:', err);
        setSnackbarMessage('Failed to initialize default categories');
        setSnackbarOpen(true);
      }
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Shortcuts Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage shortcuts for all users
        </Typography>
      </Box>

      {/* Search and Add buttons */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search shortcuts..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<CategoryIcon />}
              onClick={handleOpenCategoryDialog}
            >
              Manage Categories
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddShortcut}
            >
              Add Shortcut
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Shortcuts table */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>URL</TableCell>
                <TableCell>Categories</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredShortcuts.map((shortcut) => (
                <TableRow key={shortcut._id}>
                  <TableCell>{shortcut.title}</TableCell>
                  <TableCell>
                    <Box sx={{ maxWidth: 250, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {shortcut.url}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {shortcut.categories?.map((category) => (
                      <Chip 
                        key={category} 
                        label={category} 
                        size="small" 
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={shortcut.isActive !== false ? 'Active' : 'Inactive'}
                      color={shortcut.isActive !== false ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={shortcut.isGlobal !== false ? 'Global' : 'Private'}
                      color={shortcut.isGlobal !== false ? 'primary' : 'secondary'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton 
                      onClick={() => handleEditShortcut(shortcut)}
                      size="small"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleDeleteShortcut(shortcut._id)}
                      size="small"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Edit Shortcut Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedShortcut ? 'Edit Shortcut' : 'Add Shortcut'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="URL"
              name="url"
              value={formData.url}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              margin="normal"
              multiline
              rows={2}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Categories</InputLabel>
              <Select
                multiple
                value={formData.categories}
                onChange={handleCategoryChange}
                renderValue={(selected) => selected.join(', ')}
              >
                {categoryList.map((category) => (
                  <MenuItem key={category._id} value={category.name}>
                    <Checkbox checked={formData.categories.indexOf(category.name) > -1} />
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Instructions"
              name="instructions"
              value={formData.instructions}
              onChange={handleInputChange}
              margin="normal"
              multiline
              rows={4}
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  name="isActive"
                />
              }
              label="Active"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSaveShortcut} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>

      {/* Category Management Dialog */}
      <Dialog open={categoryDialogOpen} onClose={handleCloseCategoryDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          Manage Shortcut Categories
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {/* Category List */}
            <Paper sx={{ mb: 3 }}>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6">Categories</Typography>
                <Box>
                  <Button 
                    variant="outlined" 
                    size="small" 
                    onClick={handleInitializeDefaultCategories}
                    sx={{ mr: 1 }}
                  >
                    Initialize Default Categories
                  </Button>
                  <Button 
                    variant="contained" 
                    size="small" 
                    startIcon={<AddIcon />} 
                    onClick={handleAddCategory}
                  >
                    Add Category
                  </Button>
                </Box>
              </Box>

              {loadingCategories ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress size={24} />
                </Box>
              ) : categoryList.length === 0 ? (
                <Box sx={{ p: 2, textAlign: 'center' }}>
                  <Typography color="textSecondary">No categories found</Typography>
                </Box>
              ) : (
                <List>
                  {categoryList.map((category) => (
                    <React.Fragment key={category._id}>
                      <ListItem>
                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                          <Box 
                            sx={{ 
                              width: 24, 
                              height: 24, 
                              borderRadius: '50%', 
                              bgcolor: category.color || '#1976d2',
                              mr: 1
                            }} 
                          />
                        </Box>
                        <ListItemText 
                          primary={category.name} 
                          secondary={category.description || 'No description'} 
                        />
                        <ListItemSecondaryAction>
                          <IconButton 
                            edge="end" 
                            aria-label="edit" 
                            onClick={() => handleEditCategory(category)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton 
                            edge="end" 
                            aria-label="delete" 
                            onClick={() => handleDeleteCategory(category._id)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                      <Divider />
                    </React.Fragment>
                  ))}
                </List>
              )}
            </Paper>

            {/* Category Form */}
            {categoryDialogOpen && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {selectedCategory ? 'Edit Category' : 'Add Category'}
                </Typography>
                <TextField
                  fullWidth
                  label="Name"
                  name="name"
                  value={categoryFormData.name}
                  onChange={handleCategoryInputChange}
                  margin="normal"
                  required
                />
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={categoryFormData.description}
                  onChange={handleCategoryInputChange}
                  margin="normal"
                  multiline
                  rows={2}
                />
                <TextField
                  fullWidth
                  label="Icon (Material-UI icon name)"
                  name="icon"
                  value={categoryFormData.icon}
                  onChange={handleCategoryInputChange}
                  margin="normal"
                  helperText="Enter a Material-UI icon name, e.g., 'folder', 'link', 'cloud'"
                />
                <TextField
                  fullWidth
                  label="Color"
                  name="color"
                  value={categoryFormData.color}
                  onChange={handleCategoryInputChange}
                  margin="normal"
                  type="color"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <ColorLensIcon />
                      </InputAdornment>
                    ),
                  }}
                />
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    onClick={handleSaveCategory}
                  >
                    Save Category
                  </Button>
                </Box>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCategoryDialog}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default AdminShortcutsPage;
