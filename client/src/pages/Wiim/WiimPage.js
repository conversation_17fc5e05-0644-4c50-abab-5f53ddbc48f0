import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Tabs,
  Tab,
  IconButton,
  Slider,
  Card,
  CardMedia,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  ListItemAvatar,
  Avatar,
  ListItemSecondaryAction,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tooltip
} from '@mui/material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import PauseIcon from '@mui/icons-material/Pause';
import SkipPreviousIcon from '@mui/icons-material/SkipPrevious';
import SkipNextIcon from '@mui/icons-material/SkipNext';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import VolumeDownIcon from '@mui/icons-material/VolumeDown';
import RepeatIcon from '@mui/icons-material/Repeat';
import RepeatOneIcon from '@mui/icons-material/RepeatOne';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import SearchIcon from '@mui/icons-material/Search';
import AlbumIcon from '@mui/icons-material/Album';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import PersonIcon from '@mui/icons-material/Person';
import PlaylistPlayIcon from '@mui/icons-material/PlaylistPlay';
import DevicesIcon from '@mui/icons-material/Devices';
import RefreshIcon from '@mui/icons-material/Refresh';
import QueueMusicIcon from '@mui/icons-material/QueueMusic';
import AddIcon from '@mui/icons-material/Add';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import wiimService from '../../services/wiimService';
import integrationService from '../../services/integrationService';

// Define tab paths for URL navigation
const TAB_PATHS = {
  0: 'queue',
  1: 'current-playlist',
  2: 'playlists',
  3: 'device-info'
};

// Map URL paths back to tab indices
const PATH_TO_TAB = {
  'queue': 0,
  'current-playlist': 1,
  'playlists': 2,
  'device-info': 3
};

const WiimPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [integrationStatus, setIntegrationStatus] = useState(null);
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [playbackStatus, setPlaybackStatus] = useState(null);
  const [playlist, setPlaylist] = useState([]);
  const [playlists, setPlaylists] = useState([]);
  const [currentPlaylistTitle, setCurrentPlaylistTitle] = useState('');
  const [spotifyPlaylists, setSpotifyPlaylists] = useState([]);
  const [volume, setVolume] = useState(30);
  const [activeTab, setActiveTab] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [repeatMode, setRepeatMode] = useState('off');
  const [shuffleEnabled, setShuffleEnabled] = useState(false);
  
  // Spotify search state
  const [spotifySearchQuery, setSpotifySearchQuery] = useState('');
  const [spotifySearchResults, setSpotifySearchResults] = useState(null);
  const [spotifySearchLoading, setSpotifySearchLoading] = useState(false);
  const [spotifyActiveSection, setSpotifyActiveSection] = useState('playlists'); // 'playlists', 'search'
  
  // Spotify Connect state
  const [spotifyDevices, setSpotifyDevices] = useState([]);
  const [selectedSpotifyDevice, setSelectedSpotifyDevice] = useState('');
  const [useSpotifyConnect, setUseSpotifyConnect] = useState(true);
  const [spotifyPlaybackState, setSpotifyPlaybackState] = useState(null);
  const [loadingSpotifyDevices, setLoadingSpotifyDevices] = useState(false);
  
  // Spotify Queue state
  const [spotifyQueue, setSpotifyQueue] = useState(null);
  const [loadingSpotifyQueue, setLoadingSpotifyQueue] = useState(false);
  
  // Track currently playing Spotify playlist
  const [currentSpotifyPlaylistId, setCurrentSpotifyPlaylistId] = useState(null);
  
  // Track selected playlist for viewing
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [selectedPlaylistSongs, setSelectedPlaylistSongs] = useState([]);
  const [loadingPlaylistSongs, setLoadingPlaylistSongs] = useState(false);
  
  // Function to refresh data silently in the background
  const refreshData = async () => {
    if (!configStatus) return;
    
    try {
      // Get playback status and current playlist in parallel
      const [playbackData, playlistData, playlistsData] = await Promise.all([
        wiimService.getPlaybackStatus(),
        wiimService.getPlaylist(),
        wiimService.getPlaylists()
      ]);
      
      setPlaybackStatus(playbackData);
      
      // Update playlist if we have data
      if (playlistData?.songs) {
        setPlaylist(playlistData.songs);
      }
      
      // Update playlists if we have data
      if (playlistsData?.playlists) {
        setPlaylists(playlistsData.playlists);
        
        // Find the current playlist title
        if (playbackData && playbackData.playlistId) {
          const currentPlaylist = playlistsData.playlists.find(p => p.id === playbackData.playlistId);
          if (currentPlaylist) {
            setCurrentPlaylistTitle(currentPlaylist.name);
          }
        } else if (playbackData && playbackData.vendor && playbackData.vendor.startsWith('spotify:playlist:')) {
          // If it's a Spotify playlist in the vendor field
          const playlistId = playbackData.vendor.split(':')[2];
          // If we have spotifyData with the playlist name, use it
          if (playbackData.spotifyData && playbackData.spotifyData.name) {
            setCurrentPlaylistTitle(playbackData.spotifyData.name);
          } else if (currentSpotifyPlaylistId) {
            // Otherwise try to find it in the spotifyPlaylists
            const currentSpotifyPlaylist = spotifyPlaylists.find(p => p.id === playlistId || p.id === currentSpotifyPlaylistId);
            if (currentSpotifyPlaylist) {
              setCurrentPlaylistTitle(currentSpotifyPlaylist.name);
            }
          }
        } else if (currentSpotifyPlaylistId) {
          // If it's a Spotify playlist from currentSpotifyPlaylistId
          const currentSpotifyPlaylist = spotifyPlaylists.find(p => p.id === currentSpotifyPlaylistId);
          if (currentSpotifyPlaylist) {
            setCurrentPlaylistTitle(currentSpotifyPlaylist.name);
          }
        } else {
          // If no playlist is found, clear the current playlist title
          setCurrentPlaylistTitle('');
        }
      }
      
      // Update UI state based on playback status
      if (playbackData) {
        setIsPlaying(playbackData.status === 'play');
        setVolume(playbackData.vol || 30);
        setRepeatMode(playbackData.repeat === 0 ? 'off' : playbackData.repeat === 1 ? 'one' : 'all');
        setShuffleEnabled(playbackData.shuffle === 1);
      }
    } catch (err) {
      console.error('Error updating playback status and playlist:', err);
    }
  };

  // Function to fetch Spotify devices
  const fetchSpotifyDevices = async () => {
    if (!configStatus) return;
    
    setLoadingSpotifyDevices(true);
    try {
      const devices = await wiimService.getSpotifyDevices();
      setSpotifyDevices(devices || []);
      
      // If we have devices and no device is selected, select the first active one or the first one
      if (devices && devices.length > 0 && !selectedSpotifyDevice) {
        const activeDevice = devices.find(device => device.is_active);
        if (activeDevice) {
          setSelectedSpotifyDevice(activeDevice.id);
        } else {
          setSelectedSpotifyDevice(devices[0].id);
        }
      }
      
      // Clear any previous errors
      setError(null);
    } catch (err) {
      console.error('Error fetching Spotify devices:', err);
      
      // Set a user-friendly error message based on the error
      if (err.status === 401) {
        setError(`Spotify authentication failed: ${err.message}. Please check the Spotify credentials in the server configuration.`);
      } else if (err.status === 403) {
        setError(`Spotify permission denied: ${err.message}. Your Spotify account may not have the required permissions.`);
      } else if (err.message && err.message.includes('refresh token')) {
        setError(`Spotify refresh token issue: ${err.message}. Please generate a new refresh token.`);
      } else {
        // Only set a generic error if it's not an authentication issue (which might be expected if Spotify is not configured)
        setError(`Error fetching Spotify devices: ${err.message}`);
      }
    } finally {
      setLoadingSpotifyDevices(false);
    }
  };

  // Function to fetch Spotify playback state
  const fetchSpotifyPlaybackState = async () => {
    if (!configStatus || !useSpotifyConnect) return;
    
    try {
      const playbackState = await wiimService.getSpotifyPlaybackState();
      setSpotifyPlaybackState(playbackState);
      
      // If there's an active device, select it
      if (playbackState && playbackState.device && playbackState.device.id) {
        setSelectedSpotifyDevice(playbackState.device.id);
      }
    } catch (err) {
      console.error('Error fetching Spotify playback state:', err);
      // Not setting an error as Spotify Connect might not be configured
    }
  };
  
  // Function to fetch Spotify queue with loading indicator
  const fetchSpotifyQueue = async () => {
    if (!configStatus || !useSpotifyConnect) return;
    
    setLoadingSpotifyQueue(true);
    try {
      const queue = await wiimService.getSpotifyQueue();
      setSpotifyQueue(queue);
    } catch (err) {
      console.error('Error fetching Spotify queue:', err);
      // Not setting an error as Spotify Connect might not be configured
    } finally {
      setLoadingSpotifyQueue(false);
    }
  };
  
  // Function to fetch Spotify queue silently in the background without showing loading indicator
  const fetchSpotifyQueueSilently = async () => {
    if (!configStatus || !useSpotifyConnect) return;
    
    try {
      const queue = await wiimService.getSpotifyQueue();
      setSpotifyQueue(queue);
    } catch (err) {
      console.error('Error fetching Spotify queue silently:', err);
      // Not setting an error as Spotify Connect might not be configured
    }
  };
  
  // Function to add item to Spotify queue
  const handleAddToQueue = async (uri) => {
    if (!configStatus || !useSpotifyConnect) return;
    
    try {
      await wiimService.addToSpotifyQueue(uri, selectedSpotifyDevice);
      // Refresh the queue after adding an item
      fetchSpotifyQueue();
      setError(null);
    } catch (err) {
      console.error('Error adding item to Spotify queue:', err);
      setError('Failed to add item to queue. Please try again.');
    }
  };
  
  // Function to skip to next track
  const handleSkipToNext = async () => {
    if (!configStatus || !useSpotifyConnect) return;
    
    try {
      await wiimService.skipToNextTrack(selectedSpotifyDevice);
      // Refresh playback state and queue after skipping
      setTimeout(() => {
        fetchSpotifyPlaybackState();
        fetchSpotifyQueue();
      }, 1000);
      setError(null);
    } catch (err) {
      console.error('Error skipping to next track:', err);
      setError('Failed to skip to next track. Please try again.');
    }
  };
  
  // Function to skip to previous track
  const handleSkipToPrevious = async () => {
    if (!configStatus || !useSpotifyConnect) return;
    
    try {
      await wiimService.skipToPreviousTrack(selectedSpotifyDevice);
      // Refresh playback state and queue after skipping
      setTimeout(() => {
        fetchSpotifyPlaybackState();
        fetchSpotifyQueue();
      }, 1000);
      setError(null);
    } catch (err) {
      console.error('Error skipping to previous track:', err);
      setError('Failed to skip to previous track. Please try again.');
    }
  };
  
  // Function to transfer playback to WiiM device
  const handleTransferPlaybackToWiim = async (play = true) => {
    if (!configStatus || !useSpotifyConnect) return;
    
    try {
      await wiimService.transferPlaybackToWiim(play);
      // Refresh playback state after transferring
      setTimeout(fetchSpotifyPlaybackState, 1000);
      setError(null);
    } catch (err) {
      console.error('Error transferring playback to WiiM device:', err);
      setError('Failed to transfer playback to WiiM device. Please try again.');
    }
  };

  // Fetch configuration status and data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get integration status
        const integrationStatuses = await integrationService.getIntegrationStatus();
        const wiimStatus = integrationStatuses.find(status => status.integration === 'WiiM');
        setIntegrationStatus(wiimStatus);
        
        // Get configuration status
        const config = await wiimService.getConfig();
        setConfigStatus(config);

        // If configured, get playback status, and playlists
        if (config) {
          // Set device info from the enhanced config response
          if (config.deviceInfo) {
            setDeviceInfo(config.deviceInfo);
          }
          
          const [playbackData, playlistData, playlistsData] = await Promise.all([
            wiimService.getPlaybackStatus(),
            wiimService.getPlaylist(),
            wiimService.getPlaylists()
          ]);
          
          setPlaybackStatus(playbackData);
          setPlaylist(playlistData?.songs || []);
          setPlaylists(playlistsData?.playlists || []);
          
          // Find the current playlist title
          if (playbackData && playbackData.playlistId && playlistsData?.playlists) {
            const currentPlaylist = playlistsData.playlists.find(p => p.id === playbackData.playlistId);
            if (currentPlaylist) {
              setCurrentPlaylistTitle(currentPlaylist.name);
            }
          } else if (playbackData && playbackData.vendor && playbackData.vendor.startsWith('spotify:playlist:')) {
            // If it's a Spotify playlist in the vendor field
            const playlistId = playbackData.vendor.split(':')[2];
            // If we have spotifyData with the playlist name, use it
            if (playbackData.spotifyData && playbackData.spotifyData.name) {
              setCurrentPlaylistTitle(playbackData.spotifyData.name);
            } else if (spotifyPlaylists && spotifyPlaylists.length > 0) {
              // Otherwise try to find it in the spotifyPlaylists
              const currentSpotifyPlaylist = spotifyPlaylists.find(p => p.id === playlistId);
              if (currentSpotifyPlaylist) {
                setCurrentPlaylistTitle(currentSpotifyPlaylist.name);
              }
            }
          } else {
            // If no playlist is found, clear the current playlist title
            setCurrentPlaylistTitle('');
          }
          
          // Set UI state based on playback status
          if (playbackData) {
            setIsPlaying(playbackData.status === 'play');
            setVolume(playbackData.vol || 30);
            setRepeatMode(playbackData.repeat === 0 ? 'off' : playbackData.repeat === 1 ? 'one' : 'all');
            setShuffleEnabled(playbackData.shuffle === 1);
          }

          // Try to get Spotify playlists if available
          try {
            const spotifyData = await wiimService.getSpotifyPlaylists();
            setSpotifyPlaylists(spotifyData?.playlists || []);
            
            // Fetch Spotify devices if playlists are available
            await fetchSpotifyDevices();
            
            // Fetch Spotify playback state if using Spotify Connect
            if (useSpotifyConnect) {
              await fetchSpotifyPlaybackState();
            }
          } catch (spotifyErr) {
            console.log('Spotify playlists not available:', spotifyErr);
            // Not setting an error as Spotify might not be configured
          }
        }
      } catch (err) {
        console.error('Error fetching WiiM data:', err);
        setError('Failed to load WiiM data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Set up polling for playback status and playlist updates
    const intervalId = setInterval(async () => {
      if (configStatus) {
        try {
          // Get playback status and current playlist in parallel
          const [playbackData, playlistData] = await Promise.all([
            wiimService.getPlaybackStatus(),
            wiimService.getPlaylist()
          ]);
          
          setPlaybackStatus(playbackData);
          
          // Update playlist if we have data
          if (playlistData?.songs) {
            setPlaylist(playlistData.songs);
          }
          
          // Update UI state based on playback status
          if (playbackData) {
            setIsPlaying(playbackData.status === 'play');
            setVolume(playbackData.vol || 30);
            setRepeatMode(playbackData.repeat === 0 ? 'off' : playbackData.repeat === 1 ? 'one' : 'all');
            setShuffleEnabled(playbackData.shuffle === 1);
          }
          
          // Check if the current playlist has ended and advance to the next playlist if needed
          // This will only advance if repeat is off and the playlist has ended
          try {
            const advanceResult = await wiimService.checkAndAdvancePlaylist();
            if (advanceResult.advanced) {
              console.log('Advanced to next playlist:', advanceResult);
              // Refresh playlists if we advanced to a new playlist
              const playlistsData = await wiimService.getPlaylists();
              if (playlistsData?.playlists) {
                setPlaylists(playlistsData.playlists);
              }
            }
          } catch (advanceErr) {
            console.error('Error checking and advancing playlist:', advanceErr);
            // Don't set an error message for the user, as this is a background operation
          }
        } catch (err) {
          console.error('Error updating playback status and playlist:', err);
        }
      }
    }, 10000); // Update every 10 seconds

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []); // Empty dependency array to ensure this effect only runs once on mount
  
  // Set up polling for Spotify devices, playback state, queue, and playlists if using Spotify Connect
  useEffect(() => {
    if (!configStatus || !useSpotifyConnect) return;
    
    // Fetch devices, playback state, queue, and playlists immediately
    fetchSpotifyDevices();
    fetchSpotifyPlaybackState();
    fetchSpotifyQueue();
    
    // Fetch Spotify playlists
    const fetchSpotifyPlaylistsData = async () => {
      try {
        const spotifyData = await wiimService.getSpotifyPlaylists();
        setSpotifyPlaylists(spotifyData?.playlists || []);
      } catch (err) {
        console.error('Error fetching Spotify playlists:', err);
      }
    };
    
    fetchSpotifyPlaylistsData();
    
    // Set up polling for Spotify devices, playback state, queue, and playlists
    const intervalId = setInterval(() => {
      fetchSpotifyDevices();
      fetchSpotifyPlaybackState();
      fetchSpotifyQueueSilently();
      fetchSpotifyPlaylistsData();
    }, 10000); // Update every 10 seconds
    
    // Clean up interval on component unmount or when useSpotifyConnect changes
    return () => clearInterval(intervalId);
  }, [configStatus, useSpotifyConnect]);
  
  // Set active tab based on URL path on component mount
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const tabPath = pathParts[pathParts.length - 1];
    
    // If the URL has a valid tab path, set the active tab
    if (tabPath && PATH_TO_TAB.hasOwnProperty(tabPath)) {
      setActiveTab(PATH_TO_TAB[tabPath]);
    } else if (pathParts.length <= 2) {
      // If we're at the root wiim path with no tab specified, navigate to the default tab
      navigate(`/wiim/${TAB_PATHS[0]}`, { replace: true });
    }
  }, [location.pathname, navigate]);

  // Fetch Spotify queue silently when the Spotify Queue tab is active
  useEffect(() => {
    if (activeTab === 0 && configStatus && useSpotifyConnect) {
      fetchSpotifyQueueSilently();
    }
  }, [activeTab, configStatus, useSpotifyConnect]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Update URL when tab changes
    navigate(`/wiim/${TAB_PATHS[newValue]}`);
  };

  const handlePlayPause = async () => {
    try {
      if (isPlaying) {
        await wiimService.pause();
        setIsPlaying(false);
      } else {
        await wiimService.play();
        setIsPlaying(true);
      }
      // Refresh data immediately after play/pause action
      refreshData();
    } catch (err) {
      console.error('Error controlling playback:', err);
      setError('Failed to control playback. Please try again.');
    }
  };

  const handleNext = async () => {
    try {
      await wiimService.next();
      
      // Refresh data immediately after next track action
      refreshData();
    } catch (err) {
      console.error('Error skipping to next track:', err);
      setError('Failed to skip to next track. Please try again.');
    }
  };

  const handlePrevious = async () => {
    try {
      await wiimService.previous();
      
      // Refresh data immediately after previous track action
      refreshData();
    } catch (err) {
      console.error('Error going to previous track:', err);
      setError('Failed to go to previous track. Please try again.');
    }
  };

  const handleVolumeChange = (event, newValue) => {
    setVolume(newValue);
  };

  const handleVolumeChangeCommitted = async (event, newValue) => {
    try {
      await wiimService.setVolume(newValue);
    } catch (err) {
      console.error('Error setting volume:', err);
      setError('Failed to set volume. Please try again.');
    }
  };

  const handleRepeatToggle = async () => {
    try {
      let newMode;
      switch (repeatMode) {
        case 'off':
          newMode = 'all';
          break;
        case 'all':
          newMode = 'one';
          break;
        case 'one':
        default:
          newMode = 'off';
          break;
      }
      
      await wiimService.setRepeat(newMode);
      setRepeatMode(newMode);
    } catch (err) {
      console.error('Error setting repeat mode:', err);
      setError('Failed to set repeat mode. Please try again.');
    }
  };

  const handleShuffleToggle = async () => {
    try {
      const newShuffleState = !shuffleEnabled;
      await wiimService.setShuffle(newShuffleState);
      setShuffleEnabled(newShuffleState);
    } catch (err) {
      console.error('Error setting shuffle mode:', err);
      setError('Failed to set shuffle mode. Please try again.');
    }
  };

  const handlePlayTrack = async (index) => {
    try {
      await wiimService.playTrack(index);
      setIsPlaying(true);
      
      // Refresh data immediately after playing a track
      refreshData();
    } catch (err) {
      console.error('Error playing track:', err);
      setError('Failed to play track. Please try again.');
    }
  };

  const handlePlayPlaylist = async (playlistId) => {
    try {
      await wiimService.playPlaylist(playlistId);
      setIsPlaying(true);
      
      // Refresh data immediately after playing a playlist
      refreshData();
      
      // Also refresh playlists
      try {
        const playlistsData = await wiimService.getPlaylists();
        if (playlistsData?.playlists) {
          setPlaylists(playlistsData.playlists);
          
          // Update current playlist title
          const currentPlaylist = playlistsData.playlists.find(p => p.id === playlistId);
          if (currentPlaylist) {
            setCurrentPlaylistTitle(currentPlaylist.name);
          }
        }
      } catch (err) {
        console.error('Error updating playlists:', err);
      }
    } catch (err) {
      console.error('Error playing playlist:', err);
      setError('Failed to play playlist. Please try again.');
    }
  };
  
  // Function to view a playlist's songs without playing it
  const handleViewPlaylist = async (playlist) => {
    // Set the selected playlist
    setSelectedPlaylist(playlist);
    setSelectedPlaylistSongs([]);
    setLoadingPlaylistSongs(true);
    
    try {
      // Get the songs in the playlist without playing it
      const playlistData = await wiimService.getPlaylistSongs(playlist.id);
      
      // Update the state with the songs
      if (playlistData && playlistData.songs) {
        setSelectedPlaylistSongs(playlistData.songs);
      } else {
        // If no songs were returned, set an empty array
        setSelectedPlaylistSongs([]);
      }
      
      // Clear any previous errors
      setError(null);
    } catch (err) {
      console.error('Error viewing playlist:', err);
      setError('Failed to view playlist. Please try again.');
      setSelectedPlaylistSongs([]);
    } finally {
      setLoadingPlaylistSongs(false);
    }
  };

  const handlePlaySpotifyPlaylist = async (playlistId) => {
    try {
      // Always use Spotify Connect method for better reliability
      // First transfer playback to WiiM device if device ID is not explicitly provided
      if (!selectedSpotifyDevice) {
        await handleTransferPlaybackToWiim(false);
      }
      await wiimService.playSpotifyPlaylistConnect(playlistId, selectedSpotifyDevice);
      
      setIsPlaying(true);
      
      // Set the currently playing Spotify playlist ID
      setCurrentSpotifyPlaylistId(playlistId);
      
      // Refresh data immediately after playing a Spotify playlist
      refreshData();
      
      // Also refresh Spotify-specific data
      fetchSpotifyPlaybackState();
      fetchSpotifyQueue();
      
      // Also refresh Spotify playlists to update the playlist tab
      try {
        const spotifyData = await wiimService.getSpotifyPlaylists();
        setSpotifyPlaylists(spotifyData?.playlists || []);
        
        // Update current playlist title
        const currentSpotifyPlaylist = spotifyData?.playlists?.find(p => p.id === playlistId);
        if (currentSpotifyPlaylist) {
          setCurrentPlaylistTitle(currentSpotifyPlaylist.name);
        }
      } catch (err) {
        console.error('Error updating Spotify playlists:', err);
      }
    } catch (err) {
      console.error('Error playing Spotify playlist:', err);
      setError('Failed to play Spotify playlist. Please try again.');
    }
  };
  
  // Spotify search function
  const handleSpotifySearch = async () => {
    if (!spotifySearchQuery.trim()) return;
    
    setSpotifySearchLoading(true);
    setSpotifySearchResults(null);
    setError(null);
    
    try {
      const results = await wiimService.searchSpotify(spotifySearchQuery);
      setSpotifySearchResults(results);
      setSpotifyActiveSection('search');
    } catch (err) {
      console.error('Error searching Spotify:', err);
      setError('Failed to search Spotify. Please try again.');
    } finally {
      setSpotifySearchLoading(false);
    }
  };
  
  // Handle search input change
  const handleSearchInputChange = (e) => {
    setSpotifySearchQuery(e.target.value);
  };
  
  // Handle search input key press (search on Enter)
  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSpotifySearch();
    }
  };
  
  // Handle Spotify device selection
  const handleSpotifyDeviceChange = (e) => {
    setSelectedSpotifyDevice(e.target.value);
  };
  
  // Handle Spotify Connect toggle
  const handleSpotifyConnectToggle = (e) => {
    setUseSpotifyConnect(e.target.checked);
    
    // Refresh devices and playback state if enabling Spotify Connect
    if (e.target.checked) {
      fetchSpotifyDevices();
      fetchSpotifyPlaybackState();
    }
  };
  
  // Refresh Spotify devices
  const handleRefreshSpotifyDevices = () => {
    fetchSpotifyDevices();
  };
  
  // Play Spotify track
  const handlePlaySpotifyTrack = async (trackId) => {
    try {
      if (useSpotifyConnect) {
        // First transfer playback to WiiM device if device ID is not explicitly provided
        if (!selectedSpotifyDevice) {
          await handleTransferPlaybackToWiim(false);
        }
        await wiimService.playSpotifyTrackConnect(trackId, selectedSpotifyDevice);
      } else {
        await wiimService.playSpotifyTrack(trackId);
      }
      setIsPlaying(true);
      
      // Refresh data immediately after playing a Spotify track
      refreshData();
      
      // Also refresh Spotify-specific data if using Spotify Connect
      if (useSpotifyConnect) {
        fetchSpotifyPlaybackState();
        fetchSpotifyQueue();
        
        // Also refresh Spotify playlists to update the playlist tab
        try {
          const spotifyData = await wiimService.getSpotifyPlaylists();
          setSpotifyPlaylists(spotifyData?.playlists || []);
        } catch (err) {
          console.error('Error updating Spotify playlists:', err);
        }
      }
    } catch (err) {
      console.error('Error playing Spotify track:', err);
      setError('Failed to play Spotify track. Please try again.');
    }
  };
  
  // Play Spotify album
  const handlePlaySpotifyAlbum = async (albumId) => {
    try {
      if (useSpotifyConnect) {
        // First transfer playback to WiiM device if device ID is not explicitly provided
        if (!selectedSpotifyDevice) {
          await handleTransferPlaybackToWiim(false);
        }
        await wiimService.playSpotifyAlbumConnect(albumId, selectedSpotifyDevice);
      } else {
        await wiimService.playSpotifyAlbum(albumId);
      }
      setIsPlaying(true);
      
      // Refresh data immediately after playing a Spotify album
      refreshData();
      
      // Also refresh Spotify-specific data if using Spotify Connect
      if (useSpotifyConnect) {
        fetchSpotifyPlaybackState();
        fetchSpotifyQueue();
      }
    } catch (err) {
      console.error('Error playing Spotify album:', err);
      setError('Failed to play Spotify album. Please try again.');
    }
  };
  
  // Play Spotify artist
  const handlePlaySpotifyArtist = async (artistId) => {
    try {
      if (useSpotifyConnect) {
        // First transfer playback to WiiM device if device ID is not explicitly provided
        if (!selectedSpotifyDevice) {
          await handleTransferPlaybackToWiim(false);
        }
        await wiimService.playSpotifyArtistConnect(artistId, selectedSpotifyDevice);
      } else {
        await wiimService.playSpotifyArtist(artistId);
      }
      setIsPlaying(true);
      
      // Refresh data immediately after playing a Spotify artist
      refreshData();
      
      // Also refresh Spotify-specific data if using Spotify Connect
      if (useSpotifyConnect) {
        fetchSpotifyPlaybackState();
        fetchSpotifyQueue();
      }
    } catch (err) {
      console.error('Error playing Spotify artist:', err);
      setError('Failed to play Spotify artist. Please try again.');
    }
  };
  
  // Switch between Spotify sections (playlists/search)
  const handleSpotifySectionChange = (section) => {
    setSpotifyActiveSection(section);
  };

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            WiiM Media Hub
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              WiiM integration is not configured yet.
            </Alert>
            
            {integrationStatus && (
              <Alert 
                severity={integrationStatus.status === 'active' ? 'info' : 
                         integrationStatus.status === 'error' ? 'error' : 'warning'} 
                sx={{ mb: 2 }}
              >
                <Typography variant="body2">
                  Integration Status: <strong>{integrationStatus.status}</strong>
                </Typography>
                {integrationStatus.notes && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    {integrationStatus.notes}
                  </Typography>
                )}
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Last Checked: {integrationStatus.lastChecked ? new Date(integrationStatus.lastChecked).toLocaleString() : 'Never'}
                </Typography>
              </Alert>
            )}
            
            <Typography variant="body1" paragraph>
              To use the WiiM integration, you need to configure it first.
            </Typography>
            <Button 
              component={RouterLink} 
              to="/wiim/setup" 
              variant="contained" 
              color="primary"
              sx={{ mt: 2 }}
            >
              Go to Setup
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          WiiM Media Hub
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {integrationStatus && integrationStatus.status !== 'active' && (
          <Alert 
            severity={integrationStatus.status === 'error' ? 'error' : 'warning'} 
            sx={{ mb: 2 }}
          >
            <Typography variant="body2">
              Integration Status: <strong>{integrationStatus.status}</strong>
            </Typography>
            {integrationStatus.notes && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                {integrationStatus.notes}
              </Typography>
            )}
            <Typography variant="body2" sx={{ mt: 1 }}>
              Last Checked: {integrationStatus.lastChecked ? new Date(integrationStatus.lastChecked).toLocaleString() : 'Never'}
            </Typography>
          </Alert>
        )}

        {/* Now Playing Card */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            mb: 2,
            pb: 2,
            borderBottom: '1px solid rgba(0, 0, 0, 0.12)'
          }}>
            <Typography variant="h6" sx={{ flexGrow: 1 }}>
              Now Playing
            </Typography>
            {currentPlaylistTitle && (
              <Chip 
                color="primary" 
                icon={<PlaylistPlayIcon />}
                label={`Playlist: ${currentPlaylistTitle}`}
                sx={{ 
                  fontWeight: 'bold',
                  fontSize: '0.9rem',
                  height: 32
                }}
              />
            )}
          </Box>
          
          {(spotifyQueue?.currently_playing || 
            (playbackStatus?.position !== undefined && playlist[playbackStatus.position]?.title) || 
            (playbackStatus?.title && playbackStatus?.title !== 'Unknown')) ? (
            <Card sx={{ display: 'flex', mb: 3 }}>
              <CardMedia
                component="img"
                sx={{ width: 151 }}
                image={spotifyQueue?.currently_playing?.album?.images?.[0]?.url || (playbackStatus?.position !== undefined && playlist[playbackStatus.position]?.albumart) || playbackStatus?.albumart || 'https://via.placeholder.com/151'}
                alt={spotifyQueue?.currently_playing?.name || (playbackStatus?.position !== undefined && playlist[playbackStatus.position]?.title) || playbackStatus?.title || 'Album Art'}
              />
              <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                <CardContent sx={{ flex: '1 0 auto' }}>
                  <Typography component="div" variant="h5">
                    {spotifyQueue?.currently_playing?.name || (playbackStatus?.position !== undefined && playlist[playbackStatus.position]?.title) || playbackStatus?.title}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary" component="div">
                    {(spotifyQueue?.currently_playing?.artists && Array.isArray(spotifyQueue?.currently_playing?.artists) ? 
                      spotifyQueue.currently_playing.artists.map(a => a?.name || 'Unknown Artist').join(', ') : 
                      null) || (playbackStatus?.position !== undefined && playlist[playbackStatus.position]?.artist) || playbackStatus?.artist || 'Unknown Artist'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {spotifyQueue?.currently_playing?.album?.name || (playbackStatus?.position !== undefined && playlist[playbackStatus.position]?.album) || playbackStatus?.album || 'Unknown Album'}
                  </Typography>
                </CardContent>
                
                {/* Playback Controls */}
                <Box sx={{ display: 'flex', alignItems: 'center', pl: 1, pb: 1 }}>
                  <IconButton aria-label="previous" onClick={handlePrevious}>
                    <SkipPreviousIcon />
                  </IconButton>
                  <IconButton aria-label="play/pause" onClick={handlePlayPause}>
                    {isPlaying ? <PauseIcon sx={{ height: 38, width: 38 }} /> : <PlayArrowIcon sx={{ height: 38, width: 38 }} />}
                  </IconButton>
                  <IconButton aria-label="next" onClick={handleNext}>
                    <SkipNextIcon />
                  </IconButton>
                  <IconButton 
                    aria-label="repeat" 
                    onClick={handleRepeatToggle}
                    color={repeatMode !== 'off' ? 'primary' : 'default'}
                  >
                    {repeatMode === 'one' ? <RepeatOneIcon /> : <RepeatIcon />}
                  </IconButton>
                  <IconButton 
                    aria-label="shuffle" 
                    onClick={handleShuffleToggle}
                    color={shuffleEnabled ? 'primary' : 'default'}
                  >
                    <ShuffleIcon />
                  </IconButton>
                </Box>
                
                {/* Volume Control */}
                <Box sx={{ display: 'flex', alignItems: 'center', pl: 1, pr: 1, pb: 2 }}>
                  <VolumeDownIcon />
                  <Slider
                    aria-label="Volume"
                    value={volume}
                    onChange={handleVolumeChange}
                    onChangeCommitted={handleVolumeChangeCommitted}
                    min={0}
                    max={100}
                    sx={{ mx: 2 }}
                  />
                  <VolumeUpIcon />
                </Box>
              </Box>
            </Card>
          ) : (
            <Card sx={{ mb: 3, p: 3, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">
                Not currently playing anything
              </Typography>
            </Card>
          )}
          
          {/* Tabs for different content sections */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="wiim content tabs">
              <Tab label="Spotify Queue" />
              <Tab label="Current Playlist" />
              <Tab label="Playlists" />
              <Tab label="Device Info" />
            </Tabs>
          </Box>

          {/* Current Playlist Tab */}
          {activeTab === 1 && (
            <Box>
              <Box sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                mb: 2,
                p: 2,
                bgcolor: 'rgba(25, 118, 210, 0.08)',
                borderRadius: 1,
                border: '1px solid rgba(25, 118, 210, 0.2)'
              }}>
                <Typography variant="h6" sx={{ flexGrow: 1 }}>
                  Current Playlist
                </Typography>
                {currentPlaylistTitle && (
                  <Chip 
                    color="primary" 
                    label={currentPlaylistTitle}
                    sx={{ 
                      fontWeight: 'bold',
                      fontSize: '0.9rem',
                      height: 32
                    }}
                  />
                )}
              </Box>
              
              {playlist.length > 0 ? (
                <List>
                  {playlist.map((track, index) => (
                    <React.Fragment key={index}>
                      <ListItem 
                        button 
                        onClick={() => handlePlayTrack(index)}
                        selected={playbackStatus?.position === index}
                        sx={{ 
                          bgcolor: playbackStatus?.position === index ? 'rgba(25, 118, 210, 0.15)' : 'transparent',
                          borderLeft: playbackStatus?.position === index ? '4px solid #1976d2' : 'none',
                          paddingLeft: playbackStatus?.position === index ? '12px' : '16px',
                          '&:hover': {
                            bgcolor: playbackStatus?.position === index ? 'rgba(25, 118, 210, 0.2)' : 'rgba(0, 0, 0, 0.04)'
                          }
                        }}
                      >
                        {playbackStatus?.position === index && (
                          <PlayArrowIcon color="primary" sx={{ mr: 1 }} />
                        )}
                        <ListItemText 
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {playbackStatus?.position === index && (
                                <Chip 
                                  size="small" 
                                  color="primary" 
                                  label="Now Playing" 
                                  sx={{ mr: 1, height: 24, fontWeight: 'bold' }}
                                />
                              )}
                              {track.title || 'Unknown Track'}
                            </Box>
                          }
                          secondary={`${track.artist || 'Unknown Artist'} | ${track.album || 'Unknown Album'}`} 
                        />
                        <IconButton edge="end" aria-label="play" onClick={(e) => { e.stopPropagation(); handlePlayTrack(index); }}>
                          <PlayArrowIcon />
                        </IconButton>
                        <IconButton edge="end" aria-label="add to queue" onClick={(e) => { e.stopPropagation(); handleAddToQueue(`spotify:track:${track.id}`); }}>
                          <QueueMusicIcon />
                        </IconButton>
                      </ListItem>
                      {index < playlist.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography variant="body1">
                  No tracks in current playlist.
                </Typography>
              )}
            </Box>
          )}


          {/* Playlists Tab (formerly Spotify) */}
          {activeTab === 2 && (
            <Box>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Playlists
                </Typography>
                
                {/* Spotify Connect area is now hidden as it's handled behind the scenes */}
                
                {/* Search input */}
                <TextField
                  fullWidth
                  variant="outlined"
                  placeholder="Search for songs, albums, artists..."
                  value={spotifySearchQuery}
                  onChange={handleSearchInputChange}
                  onKeyPress={handleSearchKeyPress}
                  sx={{ mb: 2 }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton 
                          edge="end" 
                          onClick={handleSpotifySearch}
                          disabled={spotifySearchLoading || !spotifySearchQuery.trim()}
                        >
                          {spotifySearchLoading ? <CircularProgress size={24} /> : <SearchIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                
                {/* Section tabs */}
                <Box sx={{ display: 'flex', mb: 2 }}>
                  <Button 
                    variant={spotifyActiveSection === 'playlists' ? 'contained' : 'outlined'}
                    onClick={() => handleSpotifySectionChange('playlists')}
                    startIcon={<PlaylistPlayIcon />}
                    sx={{ mr: 1 }}
                  >
                    Playlists
                  </Button>
                  <Button 
                    variant={spotifyActiveSection === 'search' ? 'contained' : 'outlined'}
                    onClick={() => handleSpotifySectionChange('search')}
                    startIcon={<SearchIcon />}
                    disabled={!spotifySearchResults}
                  >
                    Search Results
                  </Button>
                </Box>
              </Box>
              
              {/* Regular Playlists section */}
              {spotifyActiveSection === 'playlists' && (
                <Box>
                  
                  <Typography variant="subtitle1" gutterBottom>
                    Your Spotify Playlists
                  </Typography>
                  
                  {spotifyPlaylists.length > 0 ? (
                    <List>
                      {spotifyPlaylists.map((playlist, index) => (
                        <React.Fragment key={playlist.id || index}>
                          <ListItem 
                            button 
                            onClick={() => handleViewPlaylist(playlist)}
                            selected={selectedPlaylist && selectedPlaylist.id === playlist.id}
                            sx={{ 
                              bgcolor: currentSpotifyPlaylistId === playlist.id ? 'rgba(25, 118, 210, 0.15)' : 'transparent',
                              borderLeft: currentSpotifyPlaylistId === playlist.id ? '4px solid #1976d2' : 'none',
                              paddingLeft: currentSpotifyPlaylistId === playlist.id ? '12px' : '16px',
                              '&:hover': {
                                bgcolor: currentSpotifyPlaylistId === playlist.id ? 'rgba(25, 118, 210, 0.2)' : 'rgba(0, 0, 0, 0.04)'
                              }
                            }}
                          >
                            <ListItemAvatar>
                              <Avatar>
                                <PlaylistPlayIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText 
                              primary={playlist.name || 'Unnamed Playlist'} 
                              secondary={`${playlist.trackCount || '?'} tracks`} 
                            />
                            <ListItemSecondaryAction>
                              {currentSpotifyPlaylistId === playlist.id && playbackStatus && (
                                <Chip 
                                  size="small" 
                                  color="primary" 
                                  label="Now Playing" 
                                  sx={{ mr: 1, fontWeight: 'bold' }}
                                />
                              )}
                              <Tooltip title="View Songs">
                                <IconButton edge="end" aria-label="view songs" onClick={(e) => { e.stopPropagation(); handleViewPlaylist(playlist); }} sx={{ mr: 1 }}>
                                  <QueueMusicIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Play Playlist">
                                <IconButton edge="end" aria-label="play" onClick={(e) => { e.stopPropagation(); handlePlaySpotifyPlaylist(playlist.id); }}>
                                  <PlayArrowIcon />
                                </IconButton>
                              </Tooltip>
                            </ListItemSecondaryAction>
                          </ListItem>
                          
                          {/* Display songs directly under this playlist if it's selected */}
                          {selectedPlaylist && selectedPlaylist.id === playlist.id && (
                            <Box sx={{ pl: 4, pr: 2, pb: 2, pt: 1, bgcolor: 'rgba(0, 0, 0, 0.02)' }}>
                              {loadingPlaylistSongs ? (
                                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
                                  <CircularProgress size={24} />
                                </Box>
                              ) : selectedPlaylistSongs.length > 0 ? (
                                <List dense>
                                  {selectedPlaylistSongs.map((song, songIndex) => (
                                    <ListItem key={song.id || songIndex} sx={{ pl: 2, borderLeft: '2px solid rgba(25, 118, 210, 0.3)' }}>
                                      <ListItemAvatar>
                                        <Avatar sx={{ width: 30, height: 30 }}>
                                          <MusicNoteIcon fontSize="small" />
                                        </Avatar>
                                      </ListItemAvatar>
                                      <ListItemText 
                                        primary={song.title || song.name || 'Unknown Track'} 
                                        secondary={`${song.artist || (song.artists && Array.isArray(song.artists) ? song.artists.map(a => a.name).join(', ') : 'Unknown Artist') || 'Unknown Artist'} • ${song.album || (song.album && song.album.name) || 'Unknown Album'}`} 
                                      />
                                      <IconButton edge="end" aria-label="play" onClick={() => handlePlaySpotifyTrack(song.id)} size="small">
                                        <PlayArrowIcon fontSize="small" />
                                      </IconButton>
                                    </ListItem>
                                  ))}
                                </List>
                              ) : (
                                <Typography variant="body2" sx={{ py: 2, pl: 2 }}>
                                  No songs found in this playlist.
                                </Typography>
                              )}
                            </Box>
                          )}
                          
                          {index < spotifyPlaylists.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  ) : (
                    <Typography variant="body1">
                      No Spotify playlists found. Make sure Spotify is connected to your WiiM device.
                    </Typography>
                  )}
                  
                </Box>
              )}
              
              {/* Search results section */}
              {spotifyActiveSection === 'search' && spotifySearchResults && (
                <Box>
                  {/* Tracks */}
                  {spotifySearchResults.tracks && spotifySearchResults.tracks.items.length > 0 && (
                    <Box sx={{ mb: 4 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Tracks
                      </Typography>
                      <List>
                        {spotifySearchResults.tracks.items.slice(0, 5).map((track) => (
                          <React.Fragment key={track.id}>
                            <ListItem button onClick={() => handlePlaySpotifyTrack(track.id)}>
                              <ListItemAvatar>
                                <Avatar src={track.album?.images?.[0]?.url}>
                                  <MusicNoteIcon />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText 
                                primary={track.name || 'Unnamed Track'} 
                                secondary={`${(track.artists && Array.isArray(track.artists) ? track.artists.map(a => a?.name || 'Unknown Artist').join(', ') : 'Unknown Artist') || 'Unknown Artist'} • ${track.album?.name || 'Unknown Album'}`} 
                              />
                              <IconButton edge="end" aria-label="play" onClick={(e) => { e.stopPropagation(); handlePlaySpotifyTrack(track.id); }}>
                                <PlayArrowIcon />
                              </IconButton>
                            </ListItem>
                            <Divider />
                          </React.Fragment>
                        ))}
                      </List>
                    </Box>
                  )}
                  
                  {/* Albums */}
                  {spotifySearchResults.albums && spotifySearchResults.albums.items.length > 0 && (
                    <Box sx={{ mb: 4 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Albums
                      </Typography>
                      <List>
                        {spotifySearchResults.albums.items.slice(0, 5).map((album) => (
                          <React.Fragment key={album.id}>
                            <ListItem button onClick={() => handlePlaySpotifyAlbum(album.id)}>
                              <ListItemAvatar>
                                <Avatar src={album.images?.[0]?.url}>
                                  <AlbumIcon />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText 
                                primary={album.name || 'Unnamed Album'} 
                                secondary={`${(album.artists && Array.isArray(album.artists) ? album.artists.map(a => a?.name || 'Unknown Artist').join(', ') : 'Unknown Artist') || 'Unknown Artist'} • ${album.total_tracks || 0} tracks`} 
                              />
                              <IconButton edge="end" aria-label="play" onClick={(e) => { e.stopPropagation(); handlePlaySpotifyAlbum(album.id); }}>
                                <PlayArrowIcon />
                              </IconButton>
                            </ListItem>
                            <Divider />
                          </React.Fragment>
                        ))}
                      </List>
                    </Box>
                  )}
                  
                  {/* Artists */}
                  {spotifySearchResults.artists && spotifySearchResults.artists.items.length > 0 && (
                    <Box sx={{ mb: 4 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Artists
                      </Typography>
                      <List>
                        {spotifySearchResults.artists.items.slice(0, 5).map((artist) => (
                          <React.Fragment key={artist.id}>
                            <ListItem button onClick={() => handlePlaySpotifyArtist(artist.id)}>
                              <ListItemAvatar>
                                <Avatar src={artist.images?.[0]?.url}>
                                  <PersonIcon />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText 
                                primary={artist.name || 'Unnamed Artist'} 
                                secondary={`${artist.followers?.total?.toLocaleString() || 'Unknown'} followers`} 
                              />
                              <IconButton edge="end" aria-label="play" onClick={(e) => { e.stopPropagation(); handlePlaySpotifyArtist(artist.id); }}>
                                <PlayArrowIcon />
                              </IconButton>
                            </ListItem>
                            <Divider />
                          </React.Fragment>
                        ))}
                      </List>
                    </Box>
                  )}
                  
                  {/* Playlists from search */}
                  {!selectedPlaylist && spotifySearchResults.playlists && spotifySearchResults.playlists.items.length > 0 && (
                    <Box>
                      <Typography variant="subtitle1" gutterBottom>
                        Playlists
                      </Typography>
                      <List>
                        {spotifySearchResults.playlists.items.slice(0, 5).map((playlist) => (
                          <React.Fragment key={playlist.id}>
                            <ListItem button onClick={() => handleViewPlaylist(playlist)}>
                              <ListItemAvatar>
                                <Avatar src={playlist.images?.[0]?.url}>
                                  <PlaylistPlayIcon />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText 
                                primary={playlist.name || 'Unnamed Playlist'} 
                                secondary={`${playlist.owner?.display_name || 'Unknown'} • ${playlist.tracks?.total || 0} tracks`} 
                              />
                              <IconButton edge="end" aria-label="play" onClick={(e) => { e.stopPropagation(); handlePlaySpotifyPlaylist(playlist.id); }}>
                                <PlayArrowIcon />
                              </IconButton>
                            </ListItem>
                            <Divider />
                          </React.Fragment>
                        ))}
                      </List>
                    </Box>
                  )}
                  
                  {/* Selected Playlist Songs (Search Results) */}
                  {selectedPlaylist && spotifyActiveSection === 'search' && (
                    <Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <IconButton 
                          onClick={() => setSelectedPlaylist(null)} 
                          sx={{ mr: 1 }}
                          size="small"
                        >
                          <ArrowBackIcon />
                        </IconButton>
                        <Typography variant="subtitle1">
                          {selectedPlaylist.name || 'Playlist'} Songs
                        </Typography>
                      </Box>
                      
                      {loadingPlaylistSongs ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                          <CircularProgress />
                        </Box>
                      ) : selectedPlaylistSongs.length > 0 ? (
                        <List>
                          {selectedPlaylistSongs.map((song, index) => (
                            <React.Fragment key={song.id || index}>
                              <ListItem>
                                <ListItemAvatar>
                                  <Avatar>
                                    <MusicNoteIcon />
                                  </Avatar>
                                </ListItemAvatar>
                                <ListItemText 
                                  primary={song.title || 'Unknown Track'} 
                                  secondary={`${song.artist || 'Unknown Artist'} • ${song.album || 'Unknown Album'}`} 
                                />
                                <IconButton edge="end" aria-label="play" onClick={() => handlePlaySpotifyTrack(song.id)}>
                                  <PlayArrowIcon />
                                </IconButton>
                              </ListItem>
                              {index < selectedPlaylistSongs.length - 1 && <Divider />}
                            </React.Fragment>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body1">
                          No songs found in this playlist.
                        </Typography>
                      )}
                    </Box>
                  )}
                  
                  {/* No results message */}
                  {(!spotifySearchResults.tracks?.items.length && 
                    !spotifySearchResults.albums?.items.length && 
                    !spotifySearchResults.artists?.items.length && 
                    !spotifySearchResults.playlists?.items.length) && (
                    <Typography variant="body1">
                      No results found for "{spotifySearchQuery}". Try a different search term.
                    </Typography>
                  )}
                </Box>
              )}
              
              
              {/* Loading indicator */}
              {spotifySearchLoading && (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          )}

          {/* Spotify Queue Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Spotify Queue
              </Typography>
              
              {loadingSpotifyQueue ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                  <CircularProgress />
                </Box>
              ) : spotifyQueue && spotifyQueue.queue && Array.isArray(spotifyQueue.queue) ? (
                <List>
                  {/* Currently playing track */}
                  {spotifyQueue.currently_playing && (
                    <>
                      <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                        Now Playing
                      </Typography>
                      <ListItem>
                        <ListItemAvatar>
                          <Avatar src={spotifyQueue.currently_playing.album?.images?.[0]?.url}>
                            <MusicNoteIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={spotifyQueue.currently_playing.name || 'Unknown Track'} 
                          secondary={`${spotifyQueue.currently_playing.artists?.map(a => a?.name || 'Unknown Artist').join(', ') || 'Unknown Artist'} • ${spotifyQueue.currently_playing.album?.name || 'Unknown Album'}`} 
                        />
                      </ListItem>
                      <Divider />
                    </>
                  )}
                  
                  {/* Queue items */}
                  {Array.isArray(spotifyQueue.queue) && spotifyQueue.queue.length > 0 ? (
                    <>
                      <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                        Up Next
                      </Typography>
                      {spotifyQueue.queue.map((track, index) => (
                        <React.Fragment key={track.id || index}>
                          <ListItem>
                            <ListItemAvatar>
                              <Avatar src={track.album?.images?.[0]?.url}>
                                <MusicNoteIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText 
                              primary={track.name || 'Unknown Track'} 
                              secondary={`${track.artists?.map(a => a?.name || 'Unknown Artist').join(', ') || 'Unknown Artist'} • ${track.album?.name || 'Unknown Album'}`} 
                            />
                          </ListItem>
                          {index < spotifyQueue.queue.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </>
                  ) : (
                    <Typography variant="body1" sx={{ mt: 2 }}>
                      No tracks in queue.
                    </Typography>
                  )}
                </List>
              ) : (
                <Typography variant="body1">
                  Queue information not available. Make sure Spotify is connected and playing.
                </Typography>
              )}
              
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={fetchSpotifyQueue}
                >
                  Refresh Queue
                </Button>
              </Box>
            </Box>
          )}

          {/* Device Info Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Device Information
              </Typography>
              
              {deviceInfo ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>Device Name:</strong> {deviceInfo.name || 'Unknown'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>Model:</strong> {deviceInfo.model || 'Unknown'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>IP Address:</strong> {configStatus.host}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>Port:</strong> {configStatus.port}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="body1">
                      <strong>Firmware:</strong> {deviceInfo.firmware || 'Unknown'}
                    </Typography>
                  </Grid>

                </Grid>
              ) : (
                <Typography variant="body1">
                  No device information available.
                </Typography>
              )}
            </Box>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default WiimPage;