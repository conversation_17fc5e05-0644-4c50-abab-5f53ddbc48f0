import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip,
  Divider
} from '@mui/material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import wiimService from '../../services/wiimService';

const WiimSetup = () => {
  const [formData, setFormData] = useState({
    host: '',
    port: '80'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [oneClickLoading, setOneClickLoading] = useState(false);
  const [oneClickSuccess, setOneClickSuccess] = useState(false);
  const [oneClickFormData, setOneClickFormData] = useState({
    host: '',
    port: '80'
  });
  const [showOneClickOptions, setShowOneClickOptions] = useState(false);

  const { host, port } = formData;
  const { host: oneClickHost, port: oneClickPort } = oneClickFormData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await wiimService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onOneClickChange = e => {
    const value = e.target.value;
    setOneClickFormData({ ...oneClickFormData, [e.target.name]: value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await wiimService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await wiimService.getConfig();
      setConfigStatus(config);

      // Update the form with the saved values
      setFormData({
        ...formData,
        host: config.host || formData.host,
        port: config.port || formData.port
      });
    } catch (err) {
      setError('Failed to save WiiM configuration. Please try again.');
      console.error('Error saving WiiM configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOneClickSetup = async () => {
    setOneClickLoading(true);
    setError(null);
    setOneClickSuccess(false);
    setSuccess(false);

    try {
      // Only pass host and port if they are provided
      const options = {};
      if (oneClickHost) options.host = oneClickHost;
      if (oneClickPort) options.port = oneClickPort;

      const response = await wiimService.oneClickSetup(options);
      setOneClickSuccess(true);

      // Update the config status with the new configuration
      setConfigStatus({
        host: response.host,
        port: response.port,
        configuredAt: response.configuredAt
      });

      // Reset the one-click form data
      setOneClickFormData({
        host: '',
        port: '80'
      });
      setShowOneClickOptions(false);
      setSuccess(true);
    } catch (err) {
      setError(`One-click setup failed: ${err.message}`);
      console.error('Error setting up WiiM with one click:', err);
    } finally {
      setOneClickLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          WiiM Media Hub Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    WiiM is configured with device IP: {configStatus.host}:{configStatus.port}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    WiiM integration is not configured yet. Please provide your WiiM device details below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              One-Click Setup
            </Typography>
            <Typography variant="body1" paragraph>
              Use our one-click setup to automatically discover and configure your WiiM device on the local network.
              This is the easiest way to get started.
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Button
                variant="contained"
                color={oneClickSuccess ? "success" : "primary"}
                onClick={handleOneClickSetup}
                disabled={oneClickLoading}
                startIcon={oneClickLoading ? <CircularProgress size={20} color="inherit" /> : <AutoFixHighIcon />}
                sx={{ mr: 2 }}
              >
                {oneClickLoading ? 'Setting up...' : oneClickSuccess ? 'Setup Successful' : 'One-Click Setup'}
              </Button>

              <Button
                variant="outlined"
                onClick={() => setShowOneClickOptions(!showOneClickOptions)}
                disabled={oneClickLoading}
              >
                {showOneClickOptions ? 'Hide Options' : 'Show Options'}
              </Button>
            </Box>

            {showOneClickOptions && (
              <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Optionally, you can specify your WiiM device IP address and port for the one-click setup.
                  If left empty, the system will attempt to discover your WiiM device automatically.
                </Typography>
                <TextField
                  margin="normal"
                  fullWidth
                  id="oneClickHost"
                  label="WiiM IP Address (Optional)"
                  name="host"
                  value={oneClickHost}
                  onChange={onOneClickChange}
                  placeholder="e.g., *************"
                  size="small"
                  sx={{ mb: 2 }}
                />
                <TextField
                  margin="normal"
                  fullWidth
                  id="oneClickPort"
                  label="Port (Optional)"
                  name="port"
                  value={oneClickPort}
                  onChange={onOneClickChange}
                  placeholder="e.g., 80"
                  size="small"
                />
              </Box>
            )}

            {oneClickSuccess && (
              <Alert severity="success" sx={{ mb: 2 }}>
                WiiM has been configured successfully with one-click setup!
              </Alert>
            )}
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Manual Setup
          </Typography>

          <Typography variant="body1" paragraph>
            To integrate with your WiiM device, you need to provide its IP address and port. 
          </Typography>

          <Typography variant="body1" paragraph>
            Follow these steps to set up the integration:
          </Typography>

          <ol>
            <li>
              <Typography variant="body1" paragraph>
                Make sure your WiiM device is connected to the same network as this server
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Find the IP address of your WiiM device (check your router's DHCP client list or the WiiM app)
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Enter the IP address and port (default is 80) below
              </Typography>
            </li>
          </ol>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              WiiM configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="host"
              label="WiiM IP Address"
              name="host"
              value={host}
              onChange={onChange}
              autoFocus
              placeholder="e.g., *************"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="port"
              label="Port"
              name="port"
              value={port}
              onChange={onChange}
              placeholder="80"
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default WiimSetup;