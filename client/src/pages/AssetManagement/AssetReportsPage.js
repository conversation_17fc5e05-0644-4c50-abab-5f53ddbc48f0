import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  Grid, 
  Card, 
  CardContent,
  IconButton,
  CircularProgress,
  Divider,
  Tab,
  Tabs
} from '@mui/material';
import { 
  ArrowBack as ArrowBackIcon,
  <PERSON>rendingUp as <PERSON>rendingUpIcon,
  <PERSON><PERSON>hart as PieChartI<PERSON>,
  <PERSON><PERSON>hart as BarChartIcon,
  BugReport as BugReportIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import assetReportsService from '../../services/assetReportsService';

// Import chart components from a charting library like recharts
// For this example, we'll use placeholders for the charts
const SummaryCard = ({ title, value, icon, color }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          bgcolor: `${color}.light`, 
          color: `${color}.main`,
          borderRadius: '50%',
          p: 1,
          mr: 2
        }}>
          {icon}
        </Box>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </Box>
      <Typography variant="h4" component="div" align="center" sx={{ fontWeight: 'bold' }}>
        {value}
      </Typography>
    </CardContent>
  </Card>
);

// Placeholder for charts
const ChartPlaceholder = ({ title, height = 300 }) => (
  <Paper sx={{ p: 2, height }}>
    <Typography variant="h6" gutterBottom>{title}</Typography>
    <Box 
      sx={{ 
        height: 'calc(100% - 40px)', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        bgcolor: 'action.hover',
        borderRadius: 1
      }}
    >
      <Typography variant="body2" color="text.secondary">
        Chart visualization would appear here
      </Typography>
    </Box>
  </Paper>
);

/**
 * Asset Reports Page
 * Displays reports and statistics for asset management
 */
const AssetReportsPage = () => {
  const { user } = useAuth();
  const [summary, setSummary] = useState(null);
  const [monthlyRequestStats, setMonthlyRequestStats] = useState([]);
  const [monthlyIssueStats, setMonthlyIssueStats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);

  // Check if user has admin or asset_manager role
  const isAdminOrManager = user && (user.roles.includes('admin') || user.roles.includes('asset_manager'));

  // Fetch report data on component mount
  useEffect(() => {
    if (!isAdminOrManager) {
      setError('You do not have permission to view asset reports.');
      setLoading(false);
      return;
    }

    fetchReportData();
  }, [isAdminOrManager]);

  // Fetch all report data
  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Fetch summary statistics
      const summaryData = await assetReportsService.getSummary();
      setSummary(summaryData);
      
      // Fetch monthly request statistics
      const requestStatsData = await assetReportsService.getMonthlyRequestStats();
      setMonthlyRequestStats(requestStatsData);
      
      // Fetch monthly issue statistics
      const issueStatsData = await assetReportsService.getMonthlyIssueStats();
      setMonthlyIssueStats(issueStatsData);
      
      setError(null);
    } catch (err) {
      console.error('Error fetching report data:', err);
      setError('Failed to load report data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Render summary statistics
  const renderSummary = () => {
    if (!summary) return null;

    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h5" gutterBottom>Summary Statistics</Typography>
        </Grid>
        
        {/* Request Statistics */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>Asset Requests</Typography>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <SummaryCard 
            title="Total Requests" 
            value={summary.requests.total} 
            icon={<InventoryIcon />} 
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <SummaryCard 
            title="Pending Requests" 
            value={summary.requests.pending} 
            icon={<InventoryIcon />} 
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <SummaryCard 
            title="Approved Requests" 
            value={summary.requests.approved} 
            icon={<InventoryIcon />} 
            color="success"
          />
        </Grid>
        
        {/* Issue Statistics */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>Asset Issues</Typography>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard 
            title="Total Issues" 
            value={summary.issues.total} 
            icon={<BugReportIcon />} 
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard 
            title="Open Issues" 
            value={summary.issues.byStatus.open} 
            icon={<BugReportIcon />} 
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard 
            title="In Progress" 
            value={summary.issues.byStatus.inProgress} 
            icon={<BugReportIcon />} 
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard 
            title="Resolved Issues" 
            value={summary.issues.byStatus.resolved} 
            icon={<BugReportIcon />} 
            color="success"
          />
        </Grid>
        
        {/* Issue Types */}
        <Grid item xs={12} md={6}>
          <ChartPlaceholder title="Issues by Type" />
        </Grid>
        
        {/* Issue Priorities */}
        <Grid item xs={12} md={6}>
          <ChartPlaceholder title="Issues by Priority" />
        </Grid>
      </Grid>
    );
  };

  // Render trends tab
  const renderTrends = () => {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h5" gutterBottom>Trends</Typography>
        </Grid>
        
        {/* Monthly Request Trends */}
        <Grid item xs={12}>
          <ChartPlaceholder title="Monthly Asset Requests" />
        </Grid>
        
        {/* Monthly Issue Trends */}
        <Grid item xs={12}>
          <ChartPlaceholder title="Monthly Asset Issues" />
        </Grid>
        
        {/* Resolution Time Trends */}
        <Grid item xs={12} md={6}>
          <ChartPlaceholder title="Average Resolution Time" />
        </Grid>
        
        {/* Approval Time Trends */}
        <Grid item xs={12} md={6}>
          <ChartPlaceholder title="Average Approval Time" />
        </Grid>
      </Grid>
    );
  };

  // Render detailed reports tab
  const renderDetailedReports = () => {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h5" gutterBottom>Detailed Reports</Typography>
        </Grid>
        
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Asset Request Status Distribution</Typography>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'action.hover', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Detailed request status distribution chart would appear here
              </Typography>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Asset Issue Status Distribution</Typography>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'action.hover', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Detailed issue status distribution chart would appear here
              </Typography>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Asset Type Distribution</Typography>
            <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'action.hover', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Asset type distribution chart would appear here
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    );
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton component={Link} to="/asset-management" sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1">
          Asset Reports
        </Typography>
      </Box>

      {error ? (
        <Paper sx={{ p: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      ) : loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Paper sx={{ mb: 3 }}>
            <Tabs 
              value={activeTab} 
              onChange={handleTabChange}
              variant="fullWidth"
              indicatorColor="primary"
              textColor="primary"
            >
              <Tab icon={<PieChartIcon />} label="Summary" />
              <Tab icon={<TrendingUpIcon />} label="Trends" />
              <Tab icon={<BarChartIcon />} label="Detailed Reports" />
            </Tabs>
          </Paper>

          <Box sx={{ mt: 3 }}>
            {activeTab === 0 && renderSummary()}
            {activeTab === 1 && renderTrends()}
            {activeTab === 2 && renderDetailedReports()}
          </Box>
        </>
      )}
    </Container>
  );
};

export default AssetReportsPage;