import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions, 
  Button,
  Container,
  Paper
} from '@mui/material';
import { 
  Inventory as InventoryIcon,
  BugReport as IssuesIcon,
  Assessment as ReportsIcon
} from '@mui/icons-material';

/**
 * Asset Management Page
 * Serves as a dashboard for accessing asset management features
 */
const AssetManagementPage = () => {
  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Asset Management
        </Typography>
        <Typography variant="body1" paragraph>
          Manage your organization's assets, track issues, and generate reports.
        </Typography>
      </Paper>

      <Grid container spacing={4}>
        {/* Asset Requests Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <InventoryIcon fontSize="large" color="primary" />
              </Box>
              <Typography variant="h5" component="h2" gutterBottom align="center">
                Asset Requests
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Request new assets or equipment for your team. View and manage your existing requests.
                Administrators can approve, deny, or manage all asset requests.
              </Typography>
            </CardContent>
            <CardActions>
              <Button 
                component={Link} 
                to="/asset-management/requests" 
                variant="contained" 
                fullWidth
              >
                View Requests
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Asset Issues Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <IssuesIcon fontSize="large" color="error" />
              </Box>
              <Typography variant="h5" component="h2" gutterBottom align="center">
                Asset Issues
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Report problems with assets or equipment. Track the status of reported issues.
                Support staff can manage, assign, and resolve issues.
              </Typography>
            </CardContent>
            <CardActions>
              <Button 
                component={Link} 
                to="/asset-management/issues" 
                variant="contained" 
                fullWidth
              >
                View Issues
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Asset Reports Card */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <ReportsIcon fontSize="large" color="success" />
              </Box>
              <Typography variant="h5" component="h2" gutterBottom align="center">
                Asset Reports
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Generate and view reports on asset usage, issues, and requests.
                Analyze trends and statistics to make informed decisions.
              </Typography>
            </CardContent>
            <CardActions>
              <Button 
                component={Link} 
                to="/asset-management/reports" 
                variant="contained" 
                fullWidth
              >
                View Reports
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AssetManagementPage;