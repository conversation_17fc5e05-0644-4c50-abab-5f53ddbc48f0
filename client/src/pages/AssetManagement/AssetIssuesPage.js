import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Button, 
  Container, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  CircularProgress,
  Divider
} from '@mui/material';
import { 
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import assetIssuesService from '../../services/assetIssuesService';

/**
 * Asset Issues Page
 * Allows users to view and manage asset issues
 */
const AssetIssuesPage = () => {
  const { user } = useAuth();
  const [issues, setIssues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openNewIssueDialog, setOpenNewIssueDialog] = useState(false);
  const [openViewIssueDialog, setOpenViewIssueDialog] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [newComment, setNewComment] = useState('');
  const [newIssue, setNewIssue] = useState({
    assetId: '',
    assetType: '',
    assetName: '',
    issueType: '',
    issueDescription: '',
    priority: 'medium'
  });

  // Asset types for dropdown
  const assetTypes = [
    'Laptop',
    'Desktop',
    'Monitor',
    'Tablet',
    'Phone',
    'Projector',
    'Camera',
    'Audio Equipment',
    'Other'
  ];

  // Issue types for dropdown
  const issueTypes = [
    'Hardware',
    'Software',
    'Connectivity',
    'Damage',
    'Performance',
    'Other'
  ];

  // Priority levels for dropdown
  const priorityLevels = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' }
  ];

  // Status options for dropdown
  const statusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' }
  ];

  // Fetch asset issues on component mount
  useEffect(() => {
    fetchIssues();
  }, []);

  // Fetch asset issues from API
  const fetchIssues = async () => {
    try {
      setLoading(true);
      const data = await assetIssuesService.getAssetIssues();
      setIssues(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching asset issues:', err);
      setError('Failed to load asset issues. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the new issue dialog
  const handleOpenNewIssueDialog = () => {
    setOpenNewIssueDialog(true);
  };

  // Handle closing the new issue dialog
  const handleCloseNewIssueDialog = () => {
    setOpenNewIssueDialog(false);
    setNewIssue({
      assetId: '',
      assetType: '',
      assetName: '',
      issueType: '',
      issueDescription: '',
      priority: 'medium'
    });
  };

  // Handle opening the view issue dialog
  const handleOpenViewIssueDialog = (issue) => {
    setSelectedIssue(issue);
    setOpenViewIssueDialog(true);
  };

  // Handle closing the view issue dialog
  const handleCloseViewIssueDialog = () => {
    setOpenViewIssueDialog(false);
    setSelectedIssue(null);
    setNewComment('');
  };

  // Handle input change for new issue form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewIssue(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle submitting a new issue
  const handleSubmitIssue = async () => {
    try {
      await assetIssuesService.createAssetIssue(newIssue);
      handleCloseNewIssueDialog();
      fetchIssues();
    } catch (err) {
      console.error('Error creating asset issue:', err);
      setError('Failed to create asset issue. Please try again later.');
    }
  };

  // Handle updating issue status
  const handleUpdateStatus = async (id, newStatus) => {
    try {
      await assetIssuesService.updateStatus(id, { status: newStatus });
      fetchIssues();
      if (selectedIssue && selectedIssue._id === id) {
        setSelectedIssue(prev => ({ ...prev, status: newStatus }));
      }
    } catch (err) {
      console.error('Error updating issue status:', err);
      setError('Failed to update issue status. Please try again later.');
    }
  };

  // Handle assigning issue to current user
  const handleAssignToMe = async (id) => {
    try {
      await assetIssuesService.assignIssue(id, { assignedTo: user.id });
      fetchIssues();
      if (selectedIssue && selectedIssue._id === id) {
        setSelectedIssue(prev => ({ ...prev, assignedTo: { _id: user.id, name: user.name } }));
      }
    } catch (err) {
      console.error('Error assigning issue:', err);
      setError('Failed to assign issue. Please try again later.');
    }
  };

  // Handle adding a comment to an issue
  const handleAddComment = async (id) => {
    if (!newComment.trim()) return;
    
    try {
      const updatedIssue = await assetIssuesService.addComment(id, { 
        text: newComment,
        user: user.id
      });
      setSelectedIssue(updatedIssue);
      setNewComment('');
      fetchIssues();
    } catch (err) {
      console.error('Error adding comment:', err);
      setError('Failed to add comment. Please try again later.');
    }
  };

  // Get status chip color based on status
  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'error';
      case 'in_progress':
        return 'warning';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'default';
      case 'reopened':
        return 'secondary';
      default:
        return 'default';
    }
  };

  // Get priority chip color based on priority
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'low':
        return 'success';
      case 'medium':
        return 'info';
      case 'high':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'default';
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Format date with time for display
  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Check if user is admin or support staff
  const isAdminOrSupport = user && (user.roles.includes('admin') || user.roles.includes('support'));

  return (
    <Container maxWidth="lg">
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton component={Link} to="/asset-management" sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1">
          Asset Issues
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleOpenNewIssueDialog}
        >
          Report Issue
        </Button>
      </Box>

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Asset Name</TableCell>
                <TableCell>Issue Type</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Reported Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {issues.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No asset issues found. Report a new issue to get started.
                  </TableCell>
                </TableRow>
              ) : (
                issues.map((issue) => (
                  <TableRow key={issue._id}>
                    <TableCell>{issue.assetName}</TableCell>
                    <TableCell>{issue.issueType}</TableCell>
                    <TableCell>
                      <Chip 
                        label={issue.priority.charAt(0).toUpperCase() + issue.priority.slice(1)} 
                        color={getPriorityColor(issue.priority)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{formatDate(issue.createdAt)}</TableCell>
                    <TableCell>
                      <Chip 
                        label={issue.status.replace('_', ' ').charAt(0).toUpperCase() + issue.status.replace('_', ' ').slice(1)} 
                        color={getStatusColor(issue.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton 
                        size="small" 
                        onClick={() => handleOpenViewIssueDialog(issue)}
                        title="View Details"
                      >
                        <ViewIcon />
                      </IconButton>
                      
                      {/* Admin/Support actions */}
                      {isAdminOrSupport && issue.status !== 'closed' && (
                        <IconButton 
                          size="small" 
                          onClick={() => handleAssignToMe(issue._id)}
                          title="Assign to Me"
                          color="primary"
                          disabled={issue.assignedTo && issue.assignedTo._id === user.id}
                        >
                          <CheckIcon />
                        </IconButton>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* New Issue Dialog */}
      <Dialog open={openNewIssueDialog} onClose={handleCloseNewIssueDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Report Asset Issue</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="assetType"
                label="Asset Type"
                select
                fullWidth
                value={newIssue.assetType}
                onChange={handleInputChange}
                required
              >
                {assetTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="assetName"
                label="Asset Name/Description"
                fullWidth
                value={newIssue.assetName}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="assetId"
                label="Asset ID (if known)"
                fullWidth
                value={newIssue.assetId}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="issueType"
                label="Issue Type"
                select
                fullWidth
                value={newIssue.issueType}
                onChange={handleInputChange}
                required
              >
                {issueTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="priority"
                label="Priority"
                select
                fullWidth
                value={newIssue.priority}
                onChange={handleInputChange}
                required
              >
                {priorityLevels.map((priority) => (
                  <MenuItem key={priority.value} value={priority.value}>
                    {priority.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="issueDescription"
                label="Issue Description"
                fullWidth
                multiline
                rows={4}
                value={newIssue.issueDescription}
                onChange={handleInputChange}
                required
                placeholder="Please describe the issue in detail. Include any error messages, when the issue started, and steps to reproduce if applicable."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseNewIssueDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitIssue} 
            variant="contained"
            disabled={
              !newIssue.assetType || 
              !newIssue.assetName || 
              !newIssue.issueType || 
              !newIssue.issueDescription || 
              !newIssue.priority
            }
          >
            Submit Issue
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Issue Dialog */}
      {selectedIssue && (
        <Dialog open={openViewIssueDialog} onClose={handleCloseViewIssueDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            Issue Details
            <Chip 
              label={selectedIssue.status.replace('_', ' ').charAt(0).toUpperCase() + selectedIssue.status.replace('_', ' ').slice(1)} 
              color={getStatusColor(selectedIssue.status)}
              size="small"
              sx={{ ml: 2 }}
            />
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Asset Name</Typography>
                <Typography variant="body1">{selectedIssue.assetName}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Asset Type</Typography>
                <Typography variant="body1">{selectedIssue.assetType}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Asset ID</Typography>
                <Typography variant="body1">{selectedIssue.assetId || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Priority</Typography>
                <Chip 
                  label={selectedIssue.priority.charAt(0).toUpperCase() + selectedIssue.priority.slice(1)} 
                  color={getPriorityColor(selectedIssue.priority)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Issue Type</Typography>
                <Typography variant="body1">{selectedIssue.issueType}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Reported By</Typography>
                <Typography variant="body1">{selectedIssue.reportedBy?.name || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Reported Date</Typography>
                <Typography variant="body1">{formatDateTime(selectedIssue.createdAt)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Assigned To</Typography>
                <Typography variant="body1">
                  {selectedIssue.assignedTo?.name || 'Not Assigned'}
                  {isAdminOrSupport && !selectedIssue.assignedTo && selectedIssue.status !== 'closed' && (
                    <Button 
                      size="small" 
                      onClick={() => handleAssignToMe(selectedIssue._id)}
                      sx={{ ml: 1 }}
                    >
                      Assign to Me
                    </Button>
                  )}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">Issue Description</Typography>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                  {selectedIssue.issueDescription}
                </Typography>
              </Grid>
              
              {isAdminOrSupport && selectedIssue.status !== 'closed' && (
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ mr: 2 }}>Update Status:</Typography>
                    {statusOptions.map((option) => (
                      <Button 
                        key={option.value}
                        variant={selectedIssue.status === option.value ? "contained" : "outlined"}
                        size="small"
                        color={getStatusColor(option.value) !== 'default' ? getStatusColor(option.value) : 'primary'}
                        onClick={() => handleUpdateStatus(selectedIssue._id, option.value)}
                        sx={{ mr: 1 }}
                        disabled={selectedIssue.status === option.value}
                      >
                        {option.label}
                      </Button>
                    ))}
                  </Box>
                </Grid>
              )}
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6">Comments</Typography>
                
                {selectedIssue.comments && selectedIssue.comments.length > 0 ? (
                  selectedIssue.comments.map((comment, index) => (
                    <Paper key={index} sx={{ p: 2, my: 1, bgcolor: 'background.default' }}>
                      <Typography variant="subtitle2">
                        {comment.user?.name || 'Unknown'} - {formatDateTime(comment.date)}
                      </Typography>
                      <Typography variant="body2">{comment.text}</Typography>
                    </Paper>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No comments yet.
                  </Typography>
                )}
                
                {selectedIssue.status !== 'closed' && (
                  <Box sx={{ mt: 2 }}>
                    <TextField
                      label="Add a comment"
                      fullWidth
                      multiline
                      rows={2}
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Enter your comment here..."
                    />
                    <Button 
                      variant="contained" 
                      sx={{ mt: 1 }}
                      disabled={!newComment.trim()}
                      onClick={() => handleAddComment(selectedIssue._id)}
                    >
                      Add Comment
                    </Button>
                  </Box>
                )}
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseViewIssueDialog}>Close</Button>
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default AssetIssuesPage;