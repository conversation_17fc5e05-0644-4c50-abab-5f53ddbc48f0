import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Button, 
  Container, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  CircularProgress
} from '@mui/material';
import { 
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import assetRequestsService from '../../services/assetRequestsService';

/**
 * Asset Requests Page
 * Allows users to view and manage asset requests
 */
const AssetRequestsPage = () => {
  const { user } = useAuth();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openNewRequestDialog, setOpenNewRequestDialog] = useState(false);
  const [openViewRequestDialog, setOpenViewRequestDialog] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [newRequest, setNewRequest] = useState({
    assetId: '',
    assetType: '',
    assetName: '',
    requestReason: '',
    startDate: '',
    endDate: ''
  });

  // Asset types for dropdown
  const assetTypes = [
    'Laptop',
    'Desktop',
    'Monitor',
    'Tablet',
    'Phone',
    'Projector',
    'Camera',
    'Audio Equipment',
    'Other'
  ];

  // Fetch asset requests on component mount
  useEffect(() => {
    fetchRequests();
  }, []);

  // Fetch asset requests from API
  const fetchRequests = async () => {
    try {
      setLoading(true);
      const data = await assetRequestsService.getAssetRequests();
      setRequests(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching asset requests:', err);
      setError('Failed to load asset requests. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle opening the new request dialog
  const handleOpenNewRequestDialog = () => {
    setOpenNewRequestDialog(true);
  };

  // Handle closing the new request dialog
  const handleCloseNewRequestDialog = () => {
    setOpenNewRequestDialog(false);
    setNewRequest({
      assetId: '',
      assetType: '',
      assetName: '',
      requestReason: '',
      startDate: '',
      endDate: ''
    });
  };

  // Handle opening the view request dialog
  const handleOpenViewRequestDialog = (request) => {
    setSelectedRequest(request);
    setOpenViewRequestDialog(true);
  };

  // Handle closing the view request dialog
  const handleCloseViewRequestDialog = () => {
    setOpenViewRequestDialog(false);
    setSelectedRequest(null);
  };

  // Handle input change for new request form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewRequest(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle submitting a new request
  const handleSubmitRequest = async () => {
    try {
      await assetRequestsService.createAssetRequest(newRequest);
      handleCloseNewRequestDialog();
      fetchRequests();
    } catch (err) {
      console.error('Error creating asset request:', err);
      setError('Failed to create asset request. Please try again later.');
    }
  };

  // Handle approving a request (admin only)
  const handleApproveRequest = async (id) => {
    try {
      await assetRequestsService.approveAssetRequest(id, { approvedBy: user.id });
      fetchRequests();
    } catch (err) {
      console.error('Error approving asset request:', err);
      setError('Failed to approve asset request. Please try again later.');
    }
  };

  // Handle denying a request (admin only)
  const handleDenyRequest = async (id) => {
    try {
      await assetRequestsService.denyAssetRequest(id, { denialReason: 'Request denied by administrator' });
      fetchRequests();
    } catch (err) {
      console.error('Error denying asset request:', err);
      setError('Failed to deny asset request. Please try again later.');
    }
  };

  // Handle canceling a request
  const handleCancelRequest = async (id) => {
    try {
      await assetRequestsService.cancelAssetRequest(id);
      fetchRequests();
    } catch (err) {
      console.error('Error canceling asset request:', err);
      setError('Failed to cancel asset request. Please try again later.');
    }
  };

  // Get status chip color based on status
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'denied':
        return 'error';
      case 'completed':
        return 'info';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  // Check if user is admin or asset manager
  const isAdminOrManager = user && (user.roles.includes('admin') || user.roles.includes('asset_manager'));

  return (
    <Container maxWidth="lg">
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton component={Link} to="/asset-management" sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1">
          Asset Requests
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleOpenNewRequestDialog}
        >
          New Request
        </Button>
      </Box>

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Asset Name</TableCell>
                <TableCell>Asset Type</TableCell>
                <TableCell>Request Date</TableCell>
                <TableCell>Start Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {requests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No asset requests found. Create a new request to get started.
                  </TableCell>
                </TableRow>
              ) : (
                requests.map((request) => (
                  <TableRow key={request._id}>
                    <TableCell>{request.assetName}</TableCell>
                    <TableCell>{request.assetType}</TableCell>
                    <TableCell>{formatDate(request.createdAt)}</TableCell>
                    <TableCell>{formatDate(request.startDate)}</TableCell>
                    <TableCell>
                      <Chip 
                        label={request.status.charAt(0).toUpperCase() + request.status.slice(1)} 
                        color={getStatusColor(request.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton 
                        size="small" 
                        onClick={() => handleOpenViewRequestDialog(request)}
                        title="View Details"
                      >
                        <ViewIcon />
                      </IconButton>
                      
                      {/* Only show cancel button for pending requests by the user */}
                      {request.status === 'pending' && 
                       request.requestedBy._id === user.id && (
                        <IconButton 
                          size="small" 
                          onClick={() => handleCancelRequest(request._id)}
                          title="Cancel Request"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      )}
                      
                      {/* Admin/Manager actions for pending requests */}
                      {isAdminOrManager && request.status === 'pending' && (
                        <>
                          <IconButton 
                            size="small" 
                            onClick={() => handleApproveRequest(request._id)}
                            title="Approve Request"
                            color="success"
                          >
                            <CheckIcon />
                          </IconButton>
                          <IconButton 
                            size="small" 
                            onClick={() => handleDenyRequest(request._id)}
                            title="Deny Request"
                            color="error"
                          >
                            <CloseIcon />
                          </IconButton>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* New Request Dialog */}
      <Dialog open={openNewRequestDialog} onClose={handleCloseNewRequestDialog} maxWidth="sm" fullWidth>
        <DialogTitle>New Asset Request</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                name="assetType"
                label="Asset Type"
                select
                fullWidth
                value={newRequest.assetType}
                onChange={handleInputChange}
                required
              >
                {assetTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="assetName"
                label="Asset Name/Description"
                fullWidth
                value={newRequest.assetName}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="assetId"
                label="Asset ID (if known)"
                fullWidth
                value={newRequest.assetId}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="requestReason"
                label="Reason for Request"
                fullWidth
                multiline
                rows={3}
                value={newRequest.requestReason}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="startDate"
                label="Start Date"
                type="date"
                fullWidth
                value={newRequest.startDate}
                onChange={handleInputChange}
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="endDate"
                label="End Date (if applicable)"
                type="date"
                fullWidth
                value={newRequest.endDate}
                onChange={handleInputChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseNewRequestDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitRequest} 
            variant="contained"
            disabled={
              !newRequest.assetType || 
              !newRequest.assetName || 
              !newRequest.requestReason || 
              !newRequest.startDate
            }
          >
            Submit Request
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Request Dialog */}
      {selectedRequest && (
        <Dialog open={openViewRequestDialog} onClose={handleCloseViewRequestDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Asset Request Details</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Asset Name</Typography>
                <Typography variant="body1">{selectedRequest.assetName}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Asset Type</Typography>
                <Typography variant="body1">{selectedRequest.assetType}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Asset ID</Typography>
                <Typography variant="body1">{selectedRequest.assetId || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Status</Typography>
                <Chip 
                  label={selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1)} 
                  color={getStatusColor(selectedRequest.status)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2">Request Reason</Typography>
                <Typography variant="body1">{selectedRequest.requestReason}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Requested By</Typography>
                <Typography variant="body1">{selectedRequest.requestedBy?.name || 'Unknown'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Request Date</Typography>
                <Typography variant="body1">{formatDate(selectedRequest.createdAt)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">Start Date</Typography>
                <Typography variant="body1">{formatDate(selectedRequest.startDate)}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2">End Date</Typography>
                <Typography variant="body1">{formatDate(selectedRequest.endDate)}</Typography>
              </Grid>
              {selectedRequest.status === 'approved' && (
                <>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Approved By</Typography>
                    <Typography variant="body1">{selectedRequest.approvedBy?.name || 'Unknown'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2">Approval Date</Typography>
                    <Typography variant="body1">{formatDate(selectedRequest.approvalDate)}</Typography>
                  </Grid>
                </>
              )}
              {selectedRequest.status === 'denied' && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Denial Reason</Typography>
                  <Typography variant="body1">{selectedRequest.denialReason || 'No reason provided'}</Typography>
                </Grid>
              )}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseViewRequestDialog}>Close</Button>
            
            {/* Only show cancel button for pending requests by the user */}
            {selectedRequest.status === 'pending' && 
             selectedRequest.requestedBy._id === user.id && (
              <Button 
                onClick={() => {
                  handleCancelRequest(selectedRequest._id);
                  handleCloseViewRequestDialog();
                }} 
                color="error"
              >
                Cancel Request
              </Button>
            )}
            
            {/* Admin/Manager actions for pending requests */}
            {isAdminOrManager && selectedRequest.status === 'pending' && (
              <>
                <Button 
                  onClick={() => {
                    handleApproveRequest(selectedRequest._id);
                    handleCloseViewRequestDialog();
                  }} 
                  color="success"
                  variant="contained"
                >
                  Approve
                </Button>
                <Button 
                  onClick={() => {
                    handleDenyRequest(selectedRequest._id);
                    handleCloseViewRequestDialog();
                  }} 
                  color="error"
                  variant="contained"
                >
                  Deny
                </Button>
              </>
            )}
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default AssetRequestsPage;