import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Tabs,
  Tab,
  Link
} from '@mui/material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import unifiNetworkService from '../../services/unifiNetworkService';

const UnifiNetworkPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [devices, setDevices] = useState([]);
  const [clients, setClients] = useState([]);
  const [activeTab, setActiveTab] = useState(0);

  // Fetch configuration status and data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await unifiNetworkService.getConfig();
        setConfigStatus(config);

        // If configured, get devices and clients
        if (config) {
          const [deviceList, clientList] = await Promise.allSettled([
            unifiNetworkService.getDevices(),
            unifiNetworkService.getClients()
          ]);
          
          // Handle successful responses
          if (deviceList.status === 'fulfilled') setDevices(deviceList.value || []);
          if (clientList.status === 'fulfilled') setClients(clientList.value || []);
          
          // Log any failures for debugging
          if (deviceList.status === 'rejected') console.warn('Failed to load devices:', deviceList.reason);
          if (clientList.status === 'rejected') console.warn('Failed to load clients:', clientList.reason);
        }
      } catch (err) {
        console.error('Error fetching UniFi Network data:', err);
        setError('Failed to load UniFi Network data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            UniFi Network
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              UniFi Network integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the UniFi Network integration, an administrator needs to configure it first.
            </Typography>
            <Typography variant="body1" paragraph>
              Please contact your system administrator to set up the required environment variables.
            </Typography>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          UniFi Network
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="unifi network tabs">
              <Tab label="Devices" />
              <Tab label="Clients" />
            </Tabs>
          </Box>

          {/* Devices Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Network Devices
              </Typography>

              {devices.length > 0 ? (
                <List>
                  {devices.map((device, index) => (
                    <React.Fragment key={device.id}>
                      <ListItem>
                        <ListItemText 
                          primary={device.name || device.model || device.mac} 
                          secondary={`${device.model ? `Model: ${device.model}` : ''} ${device.state ? `| Status: ${device.state}` : ''} ${device.ip ? `| IP: ${device.ip}` : ''}`} 
                        />
                        <Button 
                          variant="outlined" 
                          size="small"
                          onClick={() => {/* Handle device action */}}
                        >
                          View Details
                        </Button>
                      </ListItem>
                      {index < devices.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography variant="body1">
                  No devices found.
                </Typography>
              )}
            </Box>
          )}

          {/* Clients Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Connected Clients
              </Typography>

              {clients.length > 0 ? (
                <List>
                  {clients.map((client, index) => (
                    <React.Fragment key={client.id}>
                      <ListItem>
                        <ListItemText 
                          primary={client.name || client.hostname || client.mac} 
                          secondary={`${client.ip ? `IP: ${client.ip}` : ''} ${client.mac ? `| MAC: ${client.mac}` : ''} ${client.status ? `| Status: ${client.status}` : ''}`} 
                        />
                        <Button 
                          variant="outlined" 
                          size="small"
                          onClick={() => {/* Handle client action */}}
                        >
                          View Details
                        </Button>
                      </ListItem>
                      {index < clients.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography variant="body1">
                  No clients found.
                </Typography>
              )}
            </Box>
          )}

        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="caption" color="textSecondary">
            Configuration is managed through environment variables
          </Typography>
          <Button 
            variant="outlined" 
            size="small"
            onClick={() => window.location.reload()}
          >
            Refresh Data
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default UnifiNetworkPage;
