import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Grid,
  Card,
  CardHeader,
  CardContent,
  Button,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  ElectricBolt as ElectricalIcon,
  HealthAndSafety as SafetyIcon,
  Thermostat as HVACIcon,
  Wifi as WiFiIcon,
  PowerOff as UtilitiesIcon,
  Event as EventsIcon,
  Audiotrack as AVIcon,
  ChecklistRtl as ChecklistIcon,
  Explore as WayfindingIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { PermissionCheck } from '../../components/PermissionCheck';

// Import BMS components
import OutletTracePanel from '../../components/BMS/OutletTracePanel';
import SafetyAssetsPanel from '../../components/BMS/SafetyAssetsPanel';
import UtilityShutoffPanel from '../../components/BMS/UtilityShutoffPanel';

// Import services
import electricalService from '../../services/electricalService';
import safetyService from '../../services/safetyService';
import hvacService from '../../services/hvacService';
import wifiService from '../../services/wifiService';

const TAB_PATHS = {
  0: 'dashboard',
  1: 'electrical',
  2: 'safety',
  3: 'hvac',
  4: 'wifi',
  5: 'utilities',
  6: 'events',
  7: 'av-equipment',
  8: 'checklists',
  9: 'wayfinding'
};

const PATH_TO_TAB = Object.fromEntries(
  Object.entries(TAB_PATHS).map(([key, value]) => [value, parseInt(key)])
);

const BMSPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [systemStatus, setSystemStatus] = useState({});
  const [highlightedPath, setHighlightedPath] = useState(null);

  // Set active tab based on URL path
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const tabPath = pathParts[pathParts.length - 1];
    
    if (tabPath && PATH_TO_TAB.hasOwnProperty(tabPath)) {
      setActiveTab(PATH_TO_TAB[tabPath]);
    } else if (pathParts.length <= 2) {
      navigate(`/bms/${TAB_PATHS[0]}`, { replace: true });
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    fetchSystemStatus();
  }, []);

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      
      // Fetch status from all systems
      const [electrical, safety, hvac, wifi] = await Promise.allSettled([
        electricalService.getSystemStatus(),
        safetyService.getSystemStatus(),
        hvacService.getSystemStatus(),
        wifiService.getSystemStatus()
      ]);

      setSystemStatus({
        electrical: electrical.status === 'fulfilled' ? electrical.value : { status: 'error', message: 'Failed to load' },
        safety: safety.status === 'fulfilled' ? safety.value : { status: 'error', message: 'Failed to load' },
        hvac: hvac.status === 'fulfilled' ? hvac.value : { status: 'error', message: 'Failed to load' },
        wifi: wifi.status === 'fulfilled' ? wifi.value : { status: 'error', message: 'Failed to load' }
      });
      
    } catch (err) {
      console.error('Error fetching system status:', err);
      setError('Failed to load system status');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    navigate(`/bms/${TAB_PATHS[newValue]}`);
  };

  const handleHighlightPath = (path) => {
    setHighlightedPath(path);
  };

  const handleClearHighlight = () => {
    setHighlightedPath(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'operational':
      case 'active':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
      case 'critical':
        return 'error';
      default:
        return 'info';
    }
  };

  const renderDashboard = () => (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h5" gutterBottom>
        Building Management System Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        {/* System Status Overview */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="System Status Overview" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper 
                    elevation={2} 
                    sx={{ 
                      p: 2, 
                      textAlign: 'center',
                      backgroundColor: getStatusColor(systemStatus.electrical?.status) === 'success' ? 'success.light' : 
                                     getStatusColor(systemStatus.electrical?.status) === 'warning' ? 'warning.light' : 'error.light',
                      color: 'white'
                    }}
                  >
                    <ElectricalIcon fontSize="large" />
                    <Typography variant="h6" sx={{ mt: 1 }}>Electrical Systems</Typography>
                    <Typography variant="body2">
                      {systemStatus.electrical?.message || 'Loading...'}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Paper 
                    elevation={2} 
                    sx={{ 
                      p: 2, 
                      textAlign: 'center',
                      backgroundColor: getStatusColor(systemStatus.safety?.status) === 'success' ? 'success.light' : 
                                     getStatusColor(systemStatus.safety?.status) === 'warning' ? 'warning.light' : 'error.light',
                      color: 'white'
                    }}
                  >
                    <SafetyIcon fontSize="large" />
                    <Typography variant="h6" sx={{ mt: 1 }}>Safety Systems</Typography>
                    <Typography variant="body2">
                      {systemStatus.safety?.message || 'Loading...'}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Paper 
                    elevation={2} 
                    sx={{ 
                      p: 2, 
                      textAlign: 'center',
                      backgroundColor: getStatusColor(systemStatus.hvac?.status) === 'success' ? 'success.light' : 
                                     getStatusColor(systemStatus.hvac?.status) === 'warning' ? 'warning.light' : 'error.light',
                      color: 'white'
                    }}
                  >
                    <HVACIcon fontSize="large" />
                    <Typography variant="h6" sx={{ mt: 1 }}>HVAC Systems</Typography>
                    <Typography variant="body2">
                      {systemStatus.hvac?.message || 'Loading...'}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Paper 
                    elevation={2} 
                    sx={{ 
                      p: 2, 
                      textAlign: 'center',
                      backgroundColor: getStatusColor(systemStatus.wifi?.status) === 'success' ? 'success.light' : 
                                     getStatusColor(systemStatus.wifi?.status) === 'warning' ? 'warning.light' : 'error.light',
                      color: 'white'
                    }}
                  >
                    <WiFiIcon fontSize="large" />
                    <Typography variant="h6" sx={{ mt: 1 }}>WiFi Systems</Typography>
                    <Typography variant="body2">
                      {systemStatus.wifi?.message || 'Loading...'}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Quick Actions" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ElectricalIcon />}
                    onClick={() => handleTabChange(null, 1)}
                  >
                    Electrical Systems
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<SafetyIcon />}
                    onClick={() => handleTabChange(null, 2)}
                  >
                    Safety Assets
                  </Button>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<UtilitiesIcon />}
                    onClick={() => handleTabChange(null, 5)}
                  >
                    Utility Shutoffs
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderElectrical = () => (
    <Box sx={{ mt: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <OutletTracePanel 
            onHighlightPath={handleHighlightPath}
            onClearHighlight={handleClearHighlight}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Electrical System Status" />
            <CardContent>
              <Typography variant="body1" paragraph>
                Current Status: {systemStatus.electrical?.status || 'Loading...'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {systemStatus.electrical?.message || 'Checking electrical systems...'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderSafety = () => (
    <Box sx={{ mt: 3 }}>
      <SafetyAssetsPanel 
        onHighlightAsset={handleHighlightPath}
        onClearHighlight={handleClearHighlight}
      />
    </Box>
  );

  const renderUtilities = () => (
    <Box sx={{ mt: 3 }}>
      <UtilityShutoffPanel 
        onHighlightShutoff={handleHighlightPath}
        onClearHighlight={handleClearHighlight}
      />
    </Box>
  );

  const renderPlaceholder = (title, description) => (
    <Box sx={{ mt: 3 }}>
      <Card>
        <CardHeader title={title} />
        <CardContent>
          <Alert severity="info">
            {description} This feature is planned for future implementation.
          </Alert>
        </CardContent>
      </Card>
    </Box>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 0:
        return renderDashboard();
      case 1:
        return renderElectrical();
      case 2:
        return renderSafety();
      case 3:
        return renderPlaceholder('HVAC Management', 'Monitor and control building HVAC systems.');
      case 4:
        return renderPlaceholder('WiFi Management', 'Monitor and manage WiFi access points and networks.');
      case 5:
        return renderUtilities();
      case 6:
        return renderPlaceholder('Church Events Management', 'Coordinate building systems for church events.');
      case 7:
        return renderPlaceholder('AV Equipment Management', 'Control and monitor audiovisual equipment.');
      case 8:
        return renderPlaceholder('Maintenance Checklists', 'Building maintenance and inspection checklists.');
      case 9:
        return renderPlaceholder('Digital Wayfinding', 'Interactive building navigation system.');
      default:
        return renderDashboard();
    }
  };

  if (loading) {
    return (
      <PermissionCheck requiredPermission="bms:read">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Loading BMS...</Typography>
        </Box>
      </PermissionCheck>
    );
  }

  if (error) {
    return (
      <PermissionCheck requiredPermission="bms:read">
        <Box sx={{ mt: 3 }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      </PermissionCheck>
    );
  }

  return (
    <PermissionCheck requiredPermission="bms:read">
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Building Management System
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Comprehensive building systems management and monitoring platform.
        </Typography>

        <Paper sx={{ width: '100%', mt: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<DashboardIcon />} label="Dashboard" />
            <Tab icon={<ElectricalIcon />} label="Electrical" />
            <Tab icon={<SafetyIcon />} label="Safety Assets" />
            <Tab icon={<HVACIcon />} label="HVAC" />
            <Tab icon={<WiFiIcon />} label="WiFi" />
            <Tab icon={<UtilitiesIcon />} label="Utilities" />
            <Tab icon={<EventsIcon />} label="Events" />
            <Tab icon={<AVIcon />} label="AV Equipment" />
            <Tab icon={<ChecklistIcon />} label="Checklists" />
            <Tab icon={<WayfindingIcon />} label="Wayfinding" />
          </Tabs>
        </Paper>

        {renderContent()}
      </Box>
    </PermissionCheck>
  );
};

export default BMSPage;