import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  TextField,
  Chip,
  Box,
  Typography,
  Divider,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import newsService from '../../services/newsService';

/**
 * Category Management Dialog
 * Dialog for managing news categories
 */
const CategoryManagementDialog = ({ open, onClose }) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editCategoryId, setEditCategoryId] = useState(null);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryRoles, setNewCategoryRoles] = useState(['user']);
  const [availableRoles] = useState(['admin', 'user', 'guest']);
  
  // Fetch categories on component mount and when dialog opens
  useEffect(() => {
    if (open) {
      fetchCategories();
    }
  }, [open]);
  
  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const data = await newsService.getAllCategories();
      setCategories(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching news categories:', err);
      setError('Failed to load news categories. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle creating a new category
  const handleCreateCategory = async () => {
    if (!newCategoryName.trim()) {
      setError('Category name cannot be empty');
      return;
    }
    
    try {
      setLoading(true);
      
      const categoryData = {
        name: newCategoryName.trim(),
        accessRoles: newCategoryRoles
      };
      
      await newsService.createCategory(categoryData);
      
      // Reset form and fetch updated categories
      setNewCategoryName('');
      setNewCategoryRoles(['user']);
      fetchCategories();
    } catch (err) {
      console.error('Error creating news category:', err);
      setError('Failed to create news category. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle editing a category
  const handleEditCategory = (category) => {
    setEditMode(true);
    setEditCategoryId(category._id);
    setNewCategoryName(category.name);
    setNewCategoryRoles(category.accessRoles || ['user']);
  };
  
  // Handle updating a category
  const handleUpdateCategory = async () => {
    if (!newCategoryName.trim()) {
      setError('Category name cannot be empty');
      return;
    }
    
    try {
      setLoading(true);
      
      const categoryData = {
        name: newCategoryName.trim(),
        accessRoles: newCategoryRoles
      };
      
      await newsService.updateCategory(editCategoryId, categoryData);
      
      // Reset form and fetch updated categories
      setEditMode(false);
      setEditCategoryId(null);
      setNewCategoryName('');
      setNewCategoryRoles(['user']);
      fetchCategories();
    } catch (err) {
      console.error('Error updating news category:', err);
      setError('Failed to update news category. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle canceling edit mode
  const handleCancelEdit = () => {
    setEditMode(false);
    setEditCategoryId(null);
    setNewCategoryName('');
    setNewCategoryRoles(['user']);
  };
  
  // Handle deleting a category
  const handleDeleteCategory = async (categoryId) => {
    try {
      setLoading(true);
      await newsService.deleteCategory(categoryId);
      fetchCategories();
    } catch (err) {
      console.error('Error deleting news category:', err);
      setError('Failed to delete news category. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle role selection change
  const handleRoleChange = (event) => {
    setNewCategoryRoles(event.target.value);
  };
  
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="category-management-dialog-title"
    >
      <DialogTitle id="category-management-dialog-title">
        Manage News Categories
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            {editMode ? 'Edit Category' : 'Create New Category'}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start' }}>
            <TextField
              label="Category Name"
              value={newCategoryName}
              onChange={(e) => setNewCategoryName(e.target.value)}
              fullWidth
              variant="outlined"
              size="small"
            />
            
            <FormControl sx={{ minWidth: 200 }} size="small">
              <InputLabel id="roles-select-label">Access Roles</InputLabel>
              <Select
                labelId="roles-select-label"
                multiple
                value={newCategoryRoles}
                onChange={handleRoleChange}
                label="Access Roles"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {availableRoles.map((role) => (
                  <MenuItem key={role} value={role}>
                    {role}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            {editMode ? (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={handleUpdateCategory}
                  disabled={loading}
                >
                  Update
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<CancelIcon />}
                  onClick={handleCancelEdit}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </>
            ) : (
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleCreateCategory}
                disabled={loading}
              >
                Add
              </Button>
            )}
          </Box>
        </Box>
        
        <Divider sx={{ my: 2 }} />
        
        <Typography variant="subtitle1" gutterBottom>
          Existing Categories
        </Typography>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : categories.length === 0 ? (
          <Typography variant="body2" color="text.secondary" sx={{ p: 2 }}>
            No categories found. Create one to get started.
          </Typography>
        ) : (
          <List>
            {categories.map((category) => (
              <ListItem key={category._id} divider>
                <ListItemText
                  primary={category.name}
                  secondary={
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                      {(category.accessRoles || ['user']).map((role) => (
                        <Chip key={role} label={role} size="small" variant="outlined" />
                      ))}
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    aria-label="edit"
                    onClick={() => handleEditCategory(category)}
                    disabled={loading || editMode}
                  >
                    <EditIcon />
                  </IconButton>
                  
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={() => handleDeleteCategory(category._id)}
                    disabled={loading || editMode}
                    color="error"
                    sx={{ ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CategoryManagementDialog;