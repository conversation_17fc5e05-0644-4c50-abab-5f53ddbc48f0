import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  CircularProgress,
  Snackbar,
  Alert,
  Grid,
  Breadcrumbs,
  Link
} from '@mui/material';
import axios from 'axios';
import { 
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import newsService from '../../services/newsService';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

/**
 * News Post Form
 * Form for creating and editing news posts
 */
const NewsPostForm = () => {
  const { id } = useParams();
  const isEditMode = Boolean(id);
  const { user, hasPermission } = useAuth();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    title: '',
    summary: '',
    content: '',
    category: '',
    imageUrl: '',
    published: false,
    featured: false,
    allowComments: false,
    team: ''
  });
  
  const [categories, setCategories] = useState([]);
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  
  // Check if user has permission to create/edit posts
  useEffect(() => {
    if (!hasPermission('news:write')) {
      navigate('/news');
    }
  }, [hasPermission, navigate]);
  
  // Fetch categories on component mount
  useEffect(() => {
    fetchCategories();
    fetchTeams();
    
    // If in edit mode, fetch the post data
    if (isEditMode) {
      fetchPost();
    }
  }, [id]);
  
  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const data = await newsService.getAllCategories();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching news categories:', err);
      setError('Failed to load news categories. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch teams for selector
  const fetchTeams = async () => {
    try {
      const res = await axios.get('/api/staff-directory/teams', { params: { page: 1, limit: 1000 } });
      setTeams(res.data.teams || []);
    } catch (err) {
      console.error('Error fetching teams:', err);
      // Non-fatal
    }
  };
  
  // Fetch post data if in edit mode
  const fetchPost = async () => {
    try {
      setLoading(true);
      const post = await newsService.getPostById(id);
      
      setFormData({
        title: post.title || '',
        summary: post.summary || '',
        content: post.content || '',
        category: post.category?._id || post.category || '',
        imageUrl: post.imageUrl || '',
        published: post.published || false,
        featured: post.featured || false,
        allowComments: post.allowComments || false,
        team: post.team?._id || post.team || ''
      });
    } catch (err) {
      console.error('Error fetching news post:', err);
      setError('Failed to load news post. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: name === 'published' || name === 'featured' ? checked : value
    }));
  };
  
  // Handle rich text editor changes
  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      // Prepare post data
      const postData = {
        ...formData,
        author: user._id
      };
      
      // Create or update post
      if (isEditMode) {
        await newsService.updatePost(id, postData);
      } else {
        await newsService.createPost(postData);
      }
      
      setSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/news');
      }, 1500);
    } catch (err) {
      console.error('Error saving news post:', err);
      setError('Failed to save news post. Please try again later.');
    } finally {
      setSaving(false);
    }
  };
  
  // Handle cancel button
  const handleCancel = () => {
    navigate('/news');
  };
  
  // Handle closing the success snackbar
  const handleCloseSnackbar = () => {
    setSuccess(false);
  };
  
  // Handle closing the error alert
  const handleCloseError = () => {
    setError(null);
  };
  
  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to="/dashboard" color="inherit">
            Dashboard
          </Link>
          <Link component={RouterLink} to="/news" color="inherit">
            News
          </Link>
          <Typography color="text.primary">
            {isEditMode ? 'Edit Post' : 'Create Post'}
          </Typography>
        </Breadcrumbs>
      </Box>
      
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {isEditMode ? 'Edit News Post' : 'Create News Post'}
        </Typography>
      </Box>
      
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }} 
          onClose={handleCloseError}
        >
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Paper sx={{ p: 3 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  name="title"
                  label="Title"
                  value={formData.title}
                  onChange={handleChange}
                  fullWidth
                  required
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  name="summary"
                  label="Summary"
                  value={formData.summary}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={2}
                  variant="outlined"
                  helperText="A brief summary of the post (optional)"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel id="category-label">Category</InputLabel>
                  <Select
                    labelId="category-label"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    label="Category"
                  >
                    {categories.map(category => (
                      <MenuItem key={category._id} value={category._id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  name="imageUrl"
                  label="Image URL"
                  value={formData.imageUrl}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  helperText="URL for the post image (optional)"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Content
                </Typography>
                <ReactQuill
                  value={formData.content}
                  onChange={handleContentChange}
                  theme="snow"
                  style={{ height: '300px', marginBottom: '50px' }}
                  modules={{
                    toolbar: [
                      [{ 'header': [1, 2, 3, false] }],
                      ['bold', 'italic', 'underline', 'strike', 'blockquote'],
                      [{'list': 'ordered'}, {'list': 'bullet'}, {'indent': '-1'}, {'indent': '+1'}],
                      ['link', 'image'],
                      ['clean']
                    ],
                  }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      name="published"
                      checked={formData.published}
                      onChange={handleChange}
                      color="primary"
                    />
                  }
                  label="Publish immediately"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      name="featured"
                      checked={formData.featured}
                      onChange={handleChange}
                      color="warning"
                    />
                  }
                  label="Mark as featured"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<CancelIcon />}
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                  
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SaveIcon />}
                    disabled={saving}
                  >
                    {saving ? 'Saving...' : 'Save Post'}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>
      )}
      
      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Post {isEditMode ? 'updated' : 'created'} successfully!
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default NewsPostForm;