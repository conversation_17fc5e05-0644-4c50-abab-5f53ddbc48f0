import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  IconButton,
  TextField,
  CircularProgress,
  Chip,
  Menu,
  MenuItem,
  Divider,
  CardMedia,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from '@mui/material';
import { 
  Article as ArticleIcon,
  OpenInNew as OpenInNewIcon,
  Star as StarIcon,
  Sort as SortIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import newsService from '../../services/newsService';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import CategoryManagementDialog from './CategoryManagementDialog';
import axios from 'axios';

/**
 * News Page
 * Displays a list of news posts with filtering and sorting options
 */
const NewsPage = () => {
  const { user, hasPermission } = useAuth();
  const [posts, setPosts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortMenuAnchor, setSortMenuAnchor] = useState(null);
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);
  const [sortBy, setSortBy] = useState('publishedAt');
  const [filterByCategory, setFilterByCategory] = useState('all');
  const [filterByTeam, setFilterByTeam] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [postsPerPage] = useState(9);
  const navigate = useNavigate();

  // Fetch posts and categories on component mount
  useEffect(() => {
    fetchCategories();
    fetchTeams();
    fetchPosts();
  }, [page, sortBy, filterByCategory, filterByTeam, searchTerm]);

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      const data = await newsService.getAccessibleCategories();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching news categories:', err);
      setError('Failed to load news categories. Please try again later.');
    }
  };

  // Fetch teams from API (for filtering)
  const fetchTeams = async () => {
    try {
      const res = await axios.get('/api/staff-directory/teams', { params: { page: 1, limit: 1000 } });
      setTeams(res.data.teams || []);
    } catch (err) {
      console.error('Error fetching teams:', err);
      // Non-fatal: team filtering will be hidden if no teams
    }
  };

  // Fetch posts from API
  const fetchPosts = async () => {
    try {
      setLoading(true);
      
      // Prepare query parameters
      const params = {
        page,
        limit: postsPerPage,
        published: true,
        sort: sortBy
      };
      
      // Add team filter if selected
      if (filterByTeam !== 'all') {
        params.team = filterByTeam === 'global' ? 'null' : filterByTeam;
      }
      
      // Add category filter if selected
      if (filterByCategory !== 'all') {
        params.category = filterByCategory;
      }
      
      // Add search term if provided
      if (searchTerm) {
        params.search = searchTerm;
      }
      
      const response = await newsService.getAllPosts(params);
      setPosts(response.posts);
      setTotalPages(Math.ceil(response.total / postsPerPage));
      setError(null);
    } catch (err) {
      console.error('Error fetching news posts:', err);
      setError('Failed to load news posts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle opening a post
  const handleOpenPost = (postId) => {
    navigate(`/news/posts/${postId}`);
  };

  // Handle page change
  const handlePageChange = (event, value) => {
    setPage(value);
    window.scrollTo(0, 0);
  };

  // Handle opening sort menu
  const handleOpenSortMenu = (event) => {
    setSortMenuAnchor(event.currentTarget);
  };

  // Handle closing sort menu
  const handleCloseSortMenu = () => {
    setSortMenuAnchor(null);
  };

  // Handle opening filter menu
  const handleOpenFilterMenu = (event) => {
    setFilterMenuAnchor(event.currentTarget);
  };

  // Handle closing filter menu
  const handleCloseFilterMenu = () => {
    setFilterMenuAnchor(null);
  };

  // Handle changing sort option
  const handleChangeSortBy = (option) => {
    setSortBy(option);
    setPage(1);
    handleCloseSortMenu();
  };

  // Handle changing category filter
  const handleChangeCategory = (event) => {
    setFilterByCategory(event.target.value);
    setPage(1);
  };
  
  // Handle changing team filter
  const handleChangeTeam = (event) => {
    setFilterByTeam(event.target.value);
    setPage(1);
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(1);
  };

  // Handle search submit
  const handleSearchSubmit = (event) => {
    event.preventDefault();
    fetchPosts();
  };

  // Handle navigating to create post page
  const handleCreatePost = () => {
    navigate('/news/posts/new');
  };
  
  // Handle navigating to edit post page
  const handleEditPost = (postId, event) => {
    event.stopPropagation();
    navigate(`/news/posts/${postId}/edit`);
  };
  
  // Handle deleting a post
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [postToDelete, setPostToDelete] = useState(null);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  
  // Handle opening and closing category management dialog
  const handleOpenCategoryDialog = () => {
    setCategoryDialogOpen(true);
  };
  
  const handleCloseCategoryDialog = () => {
    setCategoryDialogOpen(false);
    // Refresh categories after dialog closes
    fetchCategories();
  };
  
  const handleOpenDeleteDialog = (post, event) => {
    event.stopPropagation();
    setPostToDelete(post);
    setDeleteDialogOpen(true);
  };
  
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setPostToDelete(null);
  };
  
  const handleDeletePost = async () => {
    if (!postToDelete) return;
    
    try {
      await newsService.deletePost(postToDelete._id);
      fetchPosts();
      handleCloseDeleteDialog();
    } catch (err) {
      console.error('Error deleting news post:', err);
      setError('Failed to delete news post. Please try again later.');
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Render news post card
  const renderNewsCard = (post) => (
    <Card 
      key={post._id} 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        position: 'relative',
        cursor: 'pointer',
        '&:hover': {
          boxShadow: 6
        }
      }}
      onClick={() => handleOpenPost(post._id)}
    >
      {post.featured && (
        <Box sx={{ 
          position: 'absolute', 
          top: 8, 
          right: 8,
          bgcolor: 'warning.main',
          color: 'warning.contrastText',
          p: 0.5,
          borderRadius: 1,
          zIndex: 1
        }}>
          <StarIcon fontSize="small" />
        </Box>
      )}
      
      {post.imageUrl && (
        <CardMedia
          component="img"
          height="140"
          image={post.imageUrl}
          alt={post.title}
        />
      )}
      
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography 
          variant="h6" 
          component="div" 
          gutterBottom
          sx={{ 
            fontWeight: post.featured ? 'bold' : 'normal',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
        >
          {post.title}
        </Typography>
        
        {post.summary && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ 
              mt: 1,
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {post.summary}
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
          <Typography variant="caption" color="text.secondary">
            {post.publishedAt && formatDistanceToNow(new Date(post.publishedAt), { addSuffix: true })}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {post.category && (
              <Chip 
                label={post.category.name} 
                size="small" 
                color="primary" 
                variant="outlined"
              />
            )}
            {post.team && (
              <Chip 
                label={post.team.name}
                size="small"
                color="success"
                variant="outlined"
              />
            )}
          </Box>
        </Box>
      </CardContent>
      
      <CardActions sx={{ justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {post.author && (
            <Typography variant="caption" color="text.secondary">
              By {post.author.name}
            </Typography>
          )}
        </Box>
        
        <Box sx={{ display: 'flex' }}>
          {hasPermission('news:admin') && (
            <>
              <IconButton 
                size="small" 
                onClick={(e) => handleEditPost(post._id, e)}
                title="Edit post"
                color="primary"
              >
                <EditIcon fontSize="small" />
              </IconButton>
              
              <IconButton 
                size="small" 
                onClick={(e) => handleOpenDeleteDialog(post, e)}
                title="Delete post"
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </>
          )}
          
          <IconButton 
            size="small" 
            onClick={(e) => {
              e.stopPropagation();
              handleOpenPost(post._id);
            }}
            title="Read more"
          >
            <OpenInNewIcon fontSize="small" />
          </IconButton>
        </Box>
      </CardActions>
    </Card>
  );

  return (
    <Container maxWidth="lg">
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          News
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          {hasPermission('news:admin') && (
            <Button
              variant="outlined"
              startIcon={<CategoryIcon />}
              onClick={handleOpenCategoryDialog}
            >
              Manage Categories
            </Button>
          )}
          {hasPermission('news:write') && (
            <Button
              variant="contained"
              startIcon={<ArticleIcon />}
              onClick={handleCreatePost}
            >
              Create Post
            </Button>
          )}
        </Box>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <form onSubmit={handleSearchSubmit}>
              <TextField
                fullWidth
                placeholder="Search news..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  endAdornment: (
                    <IconButton type="submit" edge="end">
                      <SearchIcon />
                    </IconButton>
                  )
                }}
              />
            </form>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel id="category-filter-label">Category</InputLabel>
              <Select
                labelId="category-filter-label"
                value={filterByCategory}
                label="Category"
                onChange={handleChangeCategory}
              >
                <MenuItem value="all">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category._id} value={category._id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel id="team-filter-label">Team</InputLabel>
              <Select
                labelId="team-filter-label"
                value={filterByTeam}
                label="Team"
                onChange={handleChangeTeam}
              >
                <MenuItem value="all">All Teams</MenuItem>
                <MenuItem value="global">Global (All)</MenuItem>
                {teams.map(team => (
                  <MenuItem key={team._id} value={team._id}>
                    {team.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                startIcon={<SortIcon />}
                onClick={handleOpenSortMenu}
                variant="outlined"
              >
                Sort By
              </Button>
              <Menu
                anchorEl={sortMenuAnchor}
                open={Boolean(sortMenuAnchor)}
                onClose={handleCloseSortMenu}
              >
                <MenuItem 
                  onClick={() => handleChangeSortBy('publishedAt')}
                  selected={sortBy === 'publishedAt'}
                >
                  Most Recent
                </MenuItem>
                <MenuItem 
                  onClick={() => handleChangeSortBy('title')}
                  selected={sortBy === 'title'}
                >
                  Title
                </MenuItem>
                <MenuItem 
                  onClick={() => handleChangeSortBy('viewCount')}
                  selected={sortBy === 'viewCount'}
                >
                  Most Viewed
                </MenuItem>
              </Menu>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {posts.length === 0 ? (
            <Paper sx={{ p: 5, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No news posts found
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {searchTerm || filterByCategory !== 'all' 
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Check back later for news updates.'}
              </Typography>
            </Paper>
          ) : (
            <>
              <Grid container spacing={3}>
                {posts.map(post => (
                  <Grid item xs={12} sm={6} md={4} key={post._id}>
                    {renderNewsCard(post)}
                  </Grid>
                ))}
              </Grid>
              
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                <Pagination 
                  count={totalPages} 
                  page={page} 
                  onChange={handlePageChange} 
                  color="primary" 
                />
              </Box>
            </>
          )}
        </>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete News Post
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete the post "{postToDelete?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeletePost} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Category Management Dialog */}
      <CategoryManagementDialog
        open={categoryDialogOpen}
        onClose={handleCloseCategoryDialog}
      />
    </Container>
  );
};

export default NewsPage;