import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  Breadcrumbs,
  Link,
  Chip,
  Divider,
  CircularProgress,
  Button,
  Avatar,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import { 
  Article as ArticleIcon,
  ArrowBack as ArrowBackIcon,
  Star as StarIcon,
  Person as PersonIcon,
  Category as CategoryIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import newsService from '../../services/newsService';
import { format } from 'date-fns';
import ReactMarkdown from 'react-markdown';

/**
 * News Post Page
 * Displays a single news post with its full content
 */
const NewsPostPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAuth();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [relatedPosts, setRelatedPosts] = useState([]);

  // Fetch post on component mount
  useEffect(() => {
    fetchPost();
  }, [id]);

  // Fetch post from API
  const fetchPost = async () => {
    try {
      setLoading(true);
      const fetchedPost = await newsService.getPostById(id);
      setPost(fetchedPost);
      
      // Increment view count
      try {
        await newsService.incrementViewCount(id);
      } catch (viewErr) {
        console.error('Error incrementing view count:', viewErr);
        // Non-critical error, don't show to user
      }
      
      // Fetch related posts from the same category
      if (fetchedPost.category) {
        fetchRelatedPosts(fetchedPost.category._id, fetchedPost._id);
      }
      
      setError(null);
    } catch (err) {
      console.error('Error fetching news post:', err);
      setError('Failed to load news post. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch related posts from the same category
  const fetchRelatedPosts = async (categoryId, currentPostId) => {
    try {
      const params = {
        category: categoryId,
        limit: 3,
        published: true,
        exclude: currentPostId
      };
      
      const response = await newsService.getAllPosts(params);
      setRelatedPosts(response.posts);
    } catch (err) {
      console.error('Error fetching related posts:', err);
      // Non-critical error, don't show to user
    }
  };

  // Handle navigating back to news list
  const handleBackToNews = () => {
    navigate('/news');
  };

  // Handle navigating to edit post page
  const handleEditPost = () => {
    navigate(`/news/posts/${id}/edit`);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  // Render related post card
  const renderRelatedPostCard = (post) => (
    <Card 
      key={post._id} 
      sx={{ 
        mb: 2,
        cursor: 'pointer',
        '&:hover': {
          boxShadow: 3
        }
      }}
      onClick={() => navigate(`/news/posts/${post._id}`)}
    >
      <CardContent>
        <Typography variant="subtitle1" gutterBottom>
          {post.title}
        </Typography>
        {post.summary && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ 
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}
          >
            {post.summary}
          </Typography>
        )}
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          {formatDate(post.publishedAt)}
        </Typography>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 3 }}>
        <Button 
          startIcon={<ArrowBackIcon />} 
          onClick={handleBackToNews}
          sx={{ mb: 2 }}
        >
          Back to News
        </Button>
        
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to="/dashboard" color="inherit">
            Dashboard
          </Link>
          <Link component={RouterLink} to="/news" color="inherit">
            News
          </Link>
          <Typography color="text.primary">
            {loading ? 'Loading...' : post?.title || 'Post not found'}
          </Typography>
        </Breadcrumbs>
      </Box>

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : post ? (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="h4" component="h1" gutterBottom>
                  {post.title}
                  {post.featured && (
                    <StarIcon 
                      color="warning" 
                      fontSize="small" 
                      sx={{ ml: 1, verticalAlign: 'middle' }} 
                    />
                  )}
                </Typography>
                
                {hasPermission('news:admin') && (
                  <Button
                    startIcon={<EditIcon />}
                    variant="outlined"
                    onClick={handleEditPost}
                  >
                    Edit
                  </Button>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', mb: 3, gap: 2 }}>
                {post.category && (
                  <Chip 
                    icon={<CategoryIcon />}
                    label={post.category.name} 
                    color="primary" 
                    variant="outlined"
                  />
                )}
                
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <VisibilityIcon fontSize="small" sx={{ mr: 0.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    {post.viewCount || 0} views
                  </Typography>
                </Box>
                
                <Typography variant="body2" color="text.secondary">
                  Published: {formatDate(post.publishedAt)}
                </Typography>
              </Box>
              
              {post.summary && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', fontStyle: 'italic' }}>
                    {post.summary}
                  </Typography>
                </Box>
              )}
              
              <Divider sx={{ mb: 3 }} />
              
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" component="div" sx={{ lineHeight: 1.7 }}>
                  <ReactMarkdown>{post.content}</ReactMarkdown>
                </Typography>
              </Box>
              
              <Divider sx={{ mb: 3 }} />
              
              {post.author && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ mr: 2 }}>
                    {post.author.avatar ? (
                      <img src={post.author.avatar} alt={post.author.name} />
                    ) : (
                      <PersonIcon />
                    )}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2">
                      {post.author.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {post.author.title || 'Author'}
                    </Typography>
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            {relatedPosts.length > 0 && (
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Related News
                </Typography>
                <Divider sx={{ mb: 2 }} />
                {relatedPosts.map(relatedPost => renderRelatedPostCard(relatedPost))}
              </Paper>
            )}
          </Grid>
        </Grid>
      ) : (
        <Paper sx={{ p: 5, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary" gutterBottom>
            News post not found
          </Typography>
          <Typography variant="body1" color="text.secondary">
            The news post you're looking for doesn't exist or has been removed.
          </Typography>
          <Button 
            variant="contained" 
            onClick={handleBackToNews}
            sx={{ mt: 3 }}
          >
            Back to News
          </Button>
        </Paper>
      )}
    </Container>
  );
};

export default NewsPostPage;