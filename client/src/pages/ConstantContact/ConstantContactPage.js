import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip,
  MenuItem,
  Select,
  FormControl,
  InputLabel
} from '@mui/material';
import { 
  Info as InfoIcon,
  Email as EmailIcon,
  ContactMail as ContactIcon,
  List as ListIcon,
  Campaign as CampaignIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import constantContactService from '../../services/constantContactService';
import { useAuth } from '../../context/AuthContext';
import { PermissionCheck } from '../../components/PermissionCheck';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`constant-contact-tabpanel-${index}`}
      aria-labelledby={`constant-contact-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ConstantContactPage = () => {
  const { hasPermission } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [accountInfo, setAccountInfo] = useState(null);
  const [contactLists, setContactLists] = useState([]);
  const [contacts, setContacts] = useState({ contacts: [] });
  const [campaigns, setCampaigns] = useState({ campaigns: [] });
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [campaignTracking, setCampaignTracking] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [configForm, setConfigForm] = useState({
    clientId: '',
    clientSecret: '',
    accessToken: '',
    refreshToken: ''
  });
  const [contactDialogOpen, setContactDialogOpen] = useState(false);
  const [contactForm, setContactForm] = useState({
    email_address: '',
    first_name: '',
    last_name: '',
    list_memberships: []
  });
  const [editingContact, setEditingContact] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState(null);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await constantContactService.getConfig();
        setConfigStatus(config);
        if (config && config.clientId) {
          setConfigForm({
            clientId: '',
            clientSecret: '',
            accessToken: '',
            refreshToken: ''
          });
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Initialize Constant Contact API when configuration is loaded
  useEffect(() => {
    const initializeConstantContact = async () => {
      if (!configStatus || !configStatus.clientId) return;

      try {
        await constantContactService.initialize();
      } catch (err) {
        console.error('Error initializing Constant Contact:', err);
        setError('Failed to initialize Constant Contact. Please check your connection and try again.');
      }
    };

    initializeConstantContact();
  }, [configStatus]);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus || !configStatus.clientId) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Account Info
            const accountData = await constantContactService.getAccountInfo();
            setAccountInfo(accountData);
            break;
          case 1: // Contact Lists
            const listsData = await constantContactService.getContactLists();
            setContactLists(listsData || []);
            break;
          case 2: // Contacts
            const contactsData = await constantContactService.getContacts();
            setContacts(contactsData || { contacts: [] });
            break;
          case 3: // Campaigns
            const campaignsData = await constantContactService.getCampaigns();
            setCampaigns(campaignsData || { campaigns: [] });
            break;
          default:
            break;
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please check your connection and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus]);

  // Handle config form change
  const handleConfigFormChange = (e) => {
    const { name, value } = e.target;
    setConfigForm({
      ...configForm,
      [name]: value
    });
  };

  // Handle config save
  const handleConfigSave = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would save the API credentials to the server
      // For now, we'll just show a success message
      setError(null);
      alert('API credentials would be saved in a real implementation. Please update the .env file with your Constant Contact credentials.');
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save configuration. Please check your inputs and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle contact form change
  const handleContactFormChange = (e) => {
    const { name, value } = e.target;
    setContactForm({
      ...contactForm,
      [name]: value
    });
  };

  // Handle contact list selection
  const handleContactListChange = (event) => {
    setContactForm({
      ...contactForm,
      list_memberships: event.target.value
    });
  };

  // Open contact dialog for creating a new contact
  const handleOpenContactDialog = () => {
    setContactForm({
      email_address: '',
      first_name: '',
      last_name: '',
      list_memberships: []
    });
    setEditingContact(null);
    setContactDialogOpen(true);
  };

  // Open contact dialog for editing an existing contact
  const handleEditContact = (contact) => {
    setContactForm({
      email_address: contact.email_address,
      first_name: contact.first_name || '',
      last_name: contact.last_name || '',
      list_memberships: contact.list_memberships || []
    });
    setEditingContact(contact);
    setContactDialogOpen(true);
  };

  // Close contact dialog
  const handleCloseContactDialog = () => {
    setContactDialogOpen(false);
  };

  // Save contact (create or update)
  const handleSaveContact = async () => {
    setLoading(true);
    try {
      if (editingContact) {
        // Update existing contact
        await constantContactService.updateContact(editingContact.id, contactForm);
      } else {
        // Create new contact
        await constantContactService.createContact(contactForm);
      }
      
      // Refresh contacts list
      const contactsData = await constantContactService.getContacts();
      setContacts(contactsData || { contacts: [] });
      
      setContactDialogOpen(false);
      setError(null);
    } catch (err) {
      console.error('Error saving contact:', err);
      setError('Failed to save contact. Please check your inputs and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Open delete confirmation dialog
  const handleOpenDeleteDialog = (contact) => {
    setContactToDelete(contact);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  // Delete contact
  const handleDeleteContact = async () => {
    if (!contactToDelete) return;
    
    setLoading(true);
    try {
      await constantContactService.deleteContact(contactToDelete.id);
      
      // Refresh contacts list
      const contactsData = await constantContactService.getContacts();
      setContacts(contactsData || { contacts: [] });
      
      setDeleteDialogOpen(false);
      setError(null);
    } catch (err) {
      console.error('Error deleting contact:', err);
      setError('Failed to delete contact. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // View campaign tracking data
  const handleViewCampaignTracking = async (campaign) => {
    setSelectedCampaign(campaign);
    setLoading(true);
    
    try {
      const trackingData = await constantContactService.getCampaignTracking(campaign.campaign_id);
      setCampaignTracking(trackingData);
      setError(null);
    } catch (err) {
      console.error('Error fetching campaign tracking:', err);
      setError('Failed to fetch campaign tracking data. Please try again.');
      setCampaignTracking(null);
    } finally {
      setLoading(false);
    }
  };

  // Render configuration form
  const renderConfigForm = () => (
    <Box component="form" sx={{ mt: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Client ID"
            name="clientId"
            value={configForm.clientId}
            onChange={handleConfigFormChange}
            margin="normal"
            required
            helperText="Enter your Constant Contact Client ID"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Client Secret"
            name="clientSecret"
            type="password"
            value={configForm.clientSecret}
            onChange={handleConfigFormChange}
            margin="normal"
            required
            helperText="Enter your Constant Contact Client Secret"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Access Token"
            name="accessToken"
            type="password"
            value={configForm.accessToken}
            onChange={handleConfigFormChange}
            margin="normal"
            required
            helperText="Enter your Constant Contact Access Token"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Refresh Token"
            name="refreshToken"
            type="password"
            value={configForm.refreshToken}
            onChange={handleConfigFormChange}
            margin="normal"
            helperText="Enter your Constant Contact Refresh Token (optional)"
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleConfigSave}
              disabled={loading}
              startIcon={<SaveIcon />}
            >
              Save Configuration
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  // Render account information
  const renderAccountInfo = () => {
    if (!accountInfo) return <Typography>No account information available.</Typography>;

    return (
      <List>
        {Object.entries(accountInfo).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render contact lists
  const renderContactLists = () => {
    if (!contactLists || contactLists.length === 0) return <Typography>No contact lists available.</Typography>;

    return (
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>List Name</TableCell>
              <TableCell>List ID</TableCell>
              <TableCell>Contact Count</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created At</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {contactLists.map((list) => (
              <TableRow key={list.list_id}>
                <TableCell>{list.name}</TableCell>
                <TableCell>{list.list_id}</TableCell>
                <TableCell>{list.contact_count}</TableCell>
                <TableCell>{list.status}</TableCell>
                <TableCell>{new Date(list.created_at).toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  // Render contacts
  const renderContacts = () => {
    if (!contacts || !contacts.contacts || contacts.contacts.length === 0) {
      return (
        <Box>
          <Button
            variant="contained"
            color="primary"
            onClick={handleOpenContactDialog}
            startIcon={<AddIcon />}
            sx={{ mb: 2 }}
          >
            Add Contact
          </Button>
          <Typography>No contacts available.</Typography>
        </Box>
      );
    }

    return (
      <Box>
        <Button
          variant="contained"
          color="primary"
          onClick={handleOpenContactDialog}
          startIcon={<AddIcon />}
          sx={{ mb: 2 }}
        >
          Add Contact
        </Button>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Email</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Lists</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {contacts.contacts.map((contact) => (
                <TableRow key={contact.id}>
                  <TableCell>{contact.email_address}</TableCell>
                  <TableCell>{`${contact.first_name || ''} ${contact.last_name || ''}`}</TableCell>
                  <TableCell>{contact.status}</TableCell>
                  <TableCell>
                    {contact.list_memberships && contact.list_memberships.map((listId) => {
                      const list = contactLists.find(l => l.list_id === listId);
                      return (
                        <Chip 
                          key={listId} 
                          label={list ? list.name : listId} 
                          size="small" 
                          sx={{ mr: 0.5, mb: 0.5 }} 
                        />
                      );
                    })}
                  </TableCell>
                  <TableCell>
                    <IconButton onClick={() => handleEditContact(contact)} size="small" color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton onClick={() => handleOpenDeleteDialog(contact)} size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
    );
  };

  // Render campaigns
  const renderCampaigns = () => {
    if (!campaigns || !campaigns.campaigns || campaigns.campaigns.length === 0) {
      return <Typography>No campaigns available.</Typography>;
    }

    return (
      <Box>
        {selectedCampaign && campaignTracking ? (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Campaign Tracking: {selectedCampaign.name}
            </Typography>
            <Button
              variant="outlined"
              onClick={() => setSelectedCampaign(null)}
              sx={{ mb: 2 }}
            >
              Back to Campaigns
            </Button>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Sends
                    </Typography>
                    <Typography variant="h4">
                      {campaignTracking.sends || 0}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Opens
                    </Typography>
                    <Typography variant="h4">
                      {campaignTracking.opens || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Rate: {campaignTracking.opens_rate || '0%'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Clicks
                    </Typography>
                    <Typography variant="h4">
                      {campaignTracking.clicks || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Rate: {campaignTracking.clicks_rate || '0%'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Bounces
                    </Typography>
                    <Typography variant="h4">
                      {campaignTracking.bounces || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Rate: {campaignTracking.bounces_rate || '0%'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Unsubscribes
                    </Typography>
                    <Typography variant="h4">
                      {campaignTracking.unsubscribes || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Rate: {campaignTracking.unsubscribes_rate || '0%'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      Forwards
                    </Typography>
                    <Typography variant="h4">
                      {campaignTracking.forwards || 0}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created At</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {campaigns.campaigns.map((campaign) => (
                  <TableRow key={campaign.campaign_id}>
                    <TableCell>{campaign.name}</TableCell>
                    <TableCell>{campaign.status}</TableCell>
                    <TableCell>{new Date(campaign.created_at).toLocaleString()}</TableCell>
                    <TableCell>{new Date(campaign.updated_at).toLocaleString()}</TableCell>
                    <TableCell>
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<AssessmentIcon />}
                        onClick={() => handleViewCampaignTracking(campaign)}
                      >
                        View Stats
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
    );
  };

  // Wrap the entire component with PermissionCheck
  return (
    <PermissionCheck requiredPermission="constant-contact:read">
      {/* If configuration is not set up, show configuration form */}
      {!loadingConfig && (!configStatus || !configStatus.clientId) ? (
        <Container maxWidth="lg">
          <Typography variant="h4" component="h1" gutterBottom>
            Constant Contact
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" component="h2" gutterBottom>
              Configuration Required
            </Typography>
            <Typography paragraph>
              Please configure your Constant Contact API credentials below. You can get these from the Constant Contact Developer Portal.
            </Typography>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            {renderConfigForm()}
          </Paper>
        </Container>
      ) : (
        <Container maxWidth="lg">
          <Typography variant="h4" component="h1" gutterBottom>
            Constant Contact
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Paper sx={{ width: '100%', mb: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab label="Account Info" icon={<InfoIcon />} />
              <Tab label="Contact Lists" icon={<ListIcon />} />
              <Tab label="Contacts" icon={<ContactIcon />} />
              <Tab label="Campaigns" icon={<CampaignIcon />} />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                renderAccountInfo()
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                renderContactLists()
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                renderContacts()
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={3}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                renderCampaigns()
              )}
            </TabPanel>

          </Paper>
        </Container>
      )}

      {/* Contact Dialog */}
      <Dialog open={contactDialogOpen} onClose={handleCloseContactDialog} maxWidth="md" fullWidth>
        <DialogTitle>{editingContact ? 'Edit Contact' : 'Add Contact'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email Address"
                name="email_address"
                value={contactForm.email_address}
                onChange={handleContactFormChange}
                margin="normal"
                required
                disabled={!!editingContact}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="First Name"
                name="first_name"
                value={contactForm.first_name}
                onChange={handleContactFormChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Last Name"
                name="last_name"
                value={contactForm.last_name}
                onChange={handleContactFormChange}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="list-memberships-label">Contact Lists</InputLabel>
                <Select
                  labelId="list-memberships-label"
                  multiple
                  value={contactForm.list_memberships}
                  onChange={handleContactListChange}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((listId) => {
                        const list = contactLists.find(l => l.list_id === listId);
                        return (
                          <Chip key={listId} label={list ? list.name : listId} />
                        );
                      })}
                    </Box>
                  )}
                >
                  {contactLists.map((list) => (
                    <MenuItem key={list.list_id} value={list.list_id}>
                      {list.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseContactDialog}>Cancel</Button>
          <Button onClick={handleSaveContact} variant="contained" color="primary" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Delete Contact</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the contact {contactToDelete?.email_address}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteContact} color="error" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </PermissionCheck>
  );
};

export default ConstantContactPage;