import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  TextField, 
  Button, 
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';

const LenelS2NetBoxSetup = () => {
  const [formData, setFormData] = useState({
    host: '',
    username: '',
    password: '',
    port: 443
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [oneClickLoading, setOneClickLoading] = useState(false);
  const [oneClickSuccess, setOneClickSuccess] = useState(false);

  const { host, username, password, port } = formData;

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await lenelS2NetBoxService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      await lenelS2NetBoxService.saveConfig(formData);
      setSuccess(true);

      // Refresh the config status
      const config = await lenelS2NetBoxService.getConfig();
      setConfigStatus(config);

      // Clear the form
      setFormData({
        host: '',
        username: '',
        password: '',
        port: 443
      });
    } catch (err) {
      setError('Failed to save Lenel S2 NetBox configuration. Please try again.');
      console.error('Error saving Lenel S2 NetBox configuration:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOneClickSetup = async () => {
    setOneClickLoading(true);
    setError(null);
    setOneClickSuccess(false);
    setSuccess(false);

    try {
      const response = await lenelS2NetBoxService.oneClickSetup();
      setOneClickSuccess(true);

      // Update the config status with the new configuration
      setConfigStatus({
        host: response.host,
        username: response.username,
        port: response.port,
        localNetwork: response.localNetwork,
        configuredAt: response.configuredAt
      });

      setSuccess(true);
    } catch (err) {
      setError(`One-click setup failed: ${err.message}`);
      console.error('Error setting up Lenel S2 NetBox with one click:', err);
    } finally {
      setOneClickLoading(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Lenel S2 NetBox Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Configured" 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Lenel S2 NetBox is configured with server: {configStatus.host}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    Lenel S2 NetBox integration is not configured yet. Please provide your API credentials below.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              One-Click Setup
            </Typography>
            <Typography variant="body1" paragraph>
              Use our one-click setup to automatically configure the integration with generated credentials.
              This is the easiest way to get started.
            </Typography>
            <Button
              variant="contained"
              color={oneClickSuccess ? "success" : "primary"}
              onClick={handleOneClickSetup}
              disabled={oneClickLoading}
              startIcon={oneClickLoading ? <CircularProgress size={20} color="inherit" /> : <AutoFixHighIcon />}
              sx={{ mb: 2 }}
            >
              {oneClickLoading ? 'Setting up...' : oneClickSuccess ? 'Setup Successful' : 'One-Click Setup'}
            </Button>
            {oneClickSuccess && (
              <Alert severity="success" sx={{ mb: 2 }}>
                Integration has been configured successfully with one-click setup!
              </Alert>
            )}
          </Box>

          <Typography variant="h6" gutterBottom>
            Manual Configuration
          </Typography>

          <Typography variant="body1" paragraph>
            To integrate with Lenel S2 NetBox, you need to provide your server URL and API credentials. 
          </Typography>

          <Typography variant="body1" paragraph>
            Follow these steps to configure your Lenel S2 NetBox integration:
          </Typography>

          <ol>
            <li>
              <Typography variant="body1" paragraph>
                Log in to your Lenel S2 NetBox admin portal
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Go to System Settings → User Management
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Create a new API user with appropriate permissions
              </Typography>
            </li>
            <li>
              <Typography variant="body1" paragraph>
                Enter the host URL, username, and password below
              </Typography>
            </li>
          </ol>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Lenel S2 NetBox configuration saved successfully!
            </Alert>
          )}

          <Box component="form" onSubmit={onSubmit} noValidate sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="host"
              label="Host"
              name="host"
              value={host}
              onChange={onChange}
              autoFocus
              helperText="Example: https://netbox.example.com"
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              value={username}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              value={password}
              onChange={onChange}
            />
            <TextField
              margin="normal"
              fullWidth
              name="port"
              label="Port"
              type="number"
              id="port"
              value={port}
              onChange={onChange}
              helperText="Default: 443"
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Save Configuration'}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default LenelS2NetBoxSetup;
