import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Link,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Tabs,
  Tab,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Snackbar
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import LockIcon from '@mui/icons-material/Lock';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import GroupIcon from '@mui/icons-material/Group';
import BadgeIcon from '@mui/icons-material/Badge';
import ScheduleIcon from '@mui/icons-material/Schedule';
import MeetingRoomIcon from '@mui/icons-material/MeetingRoom';
import PersonIcon from '@mui/icons-material/Person';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AssignmentIndIcon from '@mui/icons-material/AssignmentInd';
import HistoryIcon from '@mui/icons-material/History';
import ActivityIcon from '@mui/icons-material/Timeline';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import ElevatorIcon from '@mui/icons-material/Elevator';
import SensorsIcon from '@mui/icons-material/Sensors';
import WarningIcon from '@mui/icons-material/Warning';
import lenelS2NetBoxService from '../../services/lenelS2NetBoxService';
import LiveActivityLog from '../../components/LenelS2NetBox/LiveActivityLog';
import EvacuationManagement from '../../components/LenelS2NetBox/EvacuationManagement';
import CardManagement from '../../components/LenelS2NetBox/CardManagement';
import ElevatorControl from '../../components/LenelS2NetBox/ElevatorControl';
import ReaderManagement from '../../components/LenelS2NetBox/ReaderManagement';
import FeatureComingSoonDialog from '../../components/LenelS2NetBox/FeatureComingSoonDialog';

const LenelS2NetBoxPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [portals, setPortals] = useState([]);
  const [selectedPortal, setSelectedPortal] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [detailsLoading, setDetailsLoading] = useState(false);
  
  // New state variables for access control features
  const [accessLevels, setAccessLevels] = useState([]);
  const [accessGroups, setAccessGroups] = useState([]);
  const [badges, setBadges] = useState([]);
  const [doorSchedules, setDoorSchedules] = useState([]);
  const [doors, setDoors] = useState([]);
  const [accessControlLoading, setAccessControlLoading] = useState(false);
  const [mainPageTab, setMainPageTab] = useState(0);
  
  // New state variables for user management
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [userDialogMode, setUserDialogMode] = useState('create'); // 'create', 'edit', or 'view'
  const [userFormData, setUserFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    department: '',
    title: '',
    employeeId: ''
  });
  const [userCredentials, setUserCredentials] = useState([]);
  const [userLogs, setUserLogs] = useState([]);
  
  // State for Feature Coming Soon dialog
  const [featureDialogOpen, setFeatureDialogOpen] = useState(false);
  const [featureDialogProps, setFeatureDialogProps] = useState({
    title: "Feature Coming Soon",
    featureName: "",
    entityId: null
  });
  
  // Handler to open the Feature Coming Soon dialog
  const handleFeatureDialog = (featureName, entityId = null) => {
    setFeatureDialogProps({
      title: "Feature Coming Soon",
      featureName,
      entityId
    });
    setFeatureDialogOpen(true);
  };
  const [credentialDialogOpen, setCredentialDialogOpen] = useState(false);
  const [credentialFormData, setCredentialFormData] = useState({
    type: 'card',
    value: '',
    expirationDate: '',
    status: 'active'
  });
  const [selectedCredential, setSelectedCredential] = useState(null);
  const [userLogsLoading, setUserLogsLoading] = useState(false);
  const [userCredentialsLoading, setUserCredentialsLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // New state variables for enhanced features
  const [readers, setReaders] = useState([]);
  const [readerGroups, setReaderGroups] = useState([]);
  const [portalGroups, setPortalGroups] = useState([]);
  const [elevators, setElevators] = useState([]);

  // Card management dialog state
  const [cardManagementOpen, setCardManagementOpen] = useState(false);
  const [cardManagementCredential, setCardManagementCredential] = useState(null);

  // Elevator control dialog state
  const [elevatorControlOpen, setElevatorControlOpen] = useState(false);
  const [selectedElevator, setSelectedElevator] = useState(null);

  // Fetch configuration status and portals on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await lenelS2NetBoxService.getConfig();
        setConfigStatus(config);

        // If configured, get portals
        if (config) {
          const points = await lenelS2NetBoxService.getPortals();
          setPortals(points);
        }
      } catch (err) {
        console.error('Error fetching Lenel S2 NetBox data:', err);
        setError('Failed to load Lenel S2 NetBox data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  
  // Fetch access control data when component mounts and config is available
  useEffect(() => {
    const fetchAccessControlData = async () => {
      if (!configStatus) return;
      
      setAccessControlLoading(true);
      try {
        // Fetch all access control data in parallel
        const [
          accessLevelsData,
          accessGroupsData,
          badgesData,
          doorSchedulesData,
          doorsData,
          usersData,
          readersData,
          readerGroupsData,
          portalGroupsData,
          elevatorsData
        ] = await Promise.all([
          lenelS2NetBoxService.getAccessLevels(),
          lenelS2NetBoxService.getAccessGroups(),
          lenelS2NetBoxService.getBadges(),
          lenelS2NetBoxService.getDoorSchedules(),
          lenelS2NetBoxService.getDoors(),
          lenelS2NetBoxService.getUsers(),
          lenelS2NetBoxService.getReaders(),
          lenelS2NetBoxService.getReaderGroups(),
          lenelS2NetBoxService.getPortalGroups(),
          lenelS2NetBoxService.getElevators()
        ]);
        
        setAccessLevels(accessLevelsData);
        setAccessGroups(accessGroupsData);
        setBadges(badgesData);
        setDoorSchedules(doorSchedulesData);
        setDoors(doorsData);
        setUsers(usersData);
        setReaders(readersData);
        setReaderGroups(readerGroupsData);
        setPortalGroups(portalGroupsData);
        setElevators(elevatorsData);
      } catch (err) {
        console.error('Error fetching access control data:', err);
        setError('Failed to load access control data. Some features may not be available.');
      } finally {
        setAccessControlLoading(false);
      }
    };
    
    fetchAccessControlData();
  }, [configStatus]);
  
  // Handler functions for user management
  
  // Open user dialog in create mode
  const handleCreateUser = () => {
    setUserFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      department: '',
      title: '',
      employeeId: ''
    });
    setUserDialogMode('create');
    setUserDialogOpen(true);
  };
  
  // Open user dialog in edit mode
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setUserFormData({
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email || '',
      phone: user.phone || '',
      department: user.department || '',
      title: user.title || '',
      employeeId: user.employeeId || ''
    });
    setUserDialogMode('edit');
    setUserDialogOpen(true);
  };
  
  // Open user dialog in view mode
  const handleViewUser = async (user) => {
    setSelectedUser(user);
    setUserDialogMode('view');
    setUserDialogOpen(true);
    
    // Fetch user credentials
    try {
      setUserCredentialsLoading(true);
      const credentials = await lenelS2NetBoxService.getUserCredentials(user.id);
      setUserCredentials(credentials);
    } catch (err) {
      console.error(`Error fetching credentials for user ${user.id}:`, err);
      setError('Failed to load user credentials.');
    } finally {
      setUserCredentialsLoading(false);
    }
    
    // Fetch user logs
    try {
      setUserLogsLoading(true);
      const logs = await lenelS2NetBoxService.getUserLogs(user.id);
      setUserLogs(logs);
    } catch (err) {
      console.error(`Error fetching logs for user ${user.id}:`, err);
      setError('Failed to load user activity logs.');
    } finally {
      setUserLogsLoading(false);
    }
  };
  
  // Handle user form input changes
  const handleUserFormChange = (e) => {
    const { name, value } = e.target;
    setUserFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle user form submission
  const handleUserFormSubmit = async () => {
    try {
      if (userDialogMode === 'create') {
        await lenelS2NetBoxService.createUser(userFormData);
        setSnackbarMessage('User created successfully');
      } else if (userDialogMode === 'edit') {
        await lenelS2NetBoxService.updateUser(selectedUser.id, userFormData);
        setSnackbarMessage('User updated successfully');
      }
      
      // Refresh users list
      const updatedUsers = await lenelS2NetBoxService.getUsers();
      setUsers(updatedUsers);
      
      setSnackbarOpen(true);
      setUserDialogOpen(false);
    } catch (err) {
      console.error('Error saving user:', err);
      setError('Failed to save user. Please try again.');
    }
  };
  
  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }
    
    try {
      await lenelS2NetBoxService.deleteUser(userId);
      
      // Refresh users list
      const updatedUsers = await lenelS2NetBoxService.getUsers();
      setUsers(updatedUsers);
      
      setSnackbarMessage('User deleted successfully');
      setSnackbarOpen(true);
      
      // Close dialog if open
      if (userDialogOpen && selectedUser && selectedUser.id === userId) {
        setUserDialogOpen(false);
      }
    } catch (err) {
      console.error(`Error deleting user ${userId}:`, err);
      setError('Failed to delete user. Please try again.');
    }
  };
  
  // Open credential dialog in create mode
  const handleCreateCredential = () => {
    setCredentialFormData({
      type: 'card',
      value: '',
      expirationDate: '',
      status: 'active'
    });
    setSelectedCredential(null);
    setCredentialDialogOpen(true);
  };
  
  // Open credential dialog in edit mode
  const handleEditCredential = (credential) => {
    setCredentialFormData({
      type: credential.type || 'card',
      value: credential.value || '',
      expirationDate: credential.expirationDate || '',
      status: credential.status || 'active'
    });
    setSelectedCredential(credential);
    setCredentialDialogOpen(true);
  };
  
  // Handle credential form input changes
  const handleCredentialFormChange = (e) => {
    const { name, value } = e.target;
    setCredentialFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle credential form submission
  const handleCredentialFormSubmit = async () => {
    try {
      if (!selectedCredential) {
        // Create new credential
        await lenelS2NetBoxService.createUserCredential(selectedUser.id, credentialFormData);
        setSnackbarMessage('Credential created successfully');
      } else {
        // Update existing credential
        await lenelS2NetBoxService.updateUserCredential(
          selectedUser.id, 
          selectedCredential.id, 
          credentialFormData
        );
        setSnackbarMessage('Credential updated successfully');
      }
      
      // Refresh credentials list
      const updatedCredentials = await lenelS2NetBoxService.getUserCredentials(selectedUser.id);
      setUserCredentials(updatedCredentials);
      
      setSnackbarOpen(true);
      setCredentialDialogOpen(false);
    } catch (err) {
      console.error('Error saving credential:', err);
      setError('Failed to save credential. Please try again.');
    }
  };
  
  // Handle credential deletion
  const handleDeleteCredential = async (credentialId) => {
    if (!window.confirm('Are you sure you want to delete this credential?')) {
      return;
    }
    
    try {
      await lenelS2NetBoxService.deleteUserCredential(selectedUser.id, credentialId);
      
      // Refresh credentials list
      const updatedCredentials = await lenelS2NetBoxService.getUserCredentials(selectedUser.id);
      setUserCredentials(updatedCredentials);
      
      setSnackbarMessage('Credential deleted successfully');
      setSnackbarOpen(true);
    } catch (err) {
      console.error(`Error deleting credential ${credentialId}:`, err);
      setError('Failed to delete credential. Please try again.');
    }
  };
  
  // Handle assigning access level to user
  const handleAssignAccessLevel = async (accessLevelId) => {
    try {
      await lenelS2NetBoxService.assignAccessLevelToCardholder(selectedUser.id, accessLevelId);
      setSnackbarMessage('Access level assigned successfully');
      setSnackbarOpen(true);
      
      // Refresh user details
      const updatedUser = await lenelS2NetBoxService.getUser(selectedUser.id);
      setSelectedUser(updatedUser);
    } catch (err) {
      console.error(`Error assigning access level ${accessLevelId} to user ${selectedUser.id}:`, err);
      setError('Failed to assign access level. Please try again.');
    }
  };
  
  // Handle removing access level from user
  const handleRemoveAccessLevel = async (accessLevelId) => {
    try {
      await lenelS2NetBoxService.removeAccessLevelFromCardholder(selectedUser.id, accessLevelId);
      setSnackbarMessage('Access level removed successfully');
      setSnackbarOpen(true);
      
      // Refresh user details
      const updatedUser = await lenelS2NetBoxService.getUser(selectedUser.id);
      setSelectedUser(updatedUser);
    } catch (err) {
      console.error(`Error removing access level ${accessLevelId} from user ${selectedUser.id}:`, err);
      setError('Failed to remove access level. Please try again.');
    }
  };
  
  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };
  
  // Handle view details button click
  const handleViewDetails = async (portalId) => {
    try {
      setDetailsLoading(true);
      setDetailsOpen(true);
      
      // Find the portal in the current list to show basic info immediately
      const portal = portals.find(p => p.id === portalId);
      setSelectedPortal(portal);
      
      // Fetch detailed information
      const details = await lenelS2NetBoxService.getPortal(portalId);
      
      // Update with full details
      setSelectedPortal(prev => ({
        ...prev,
        ...details
      }));
    } catch (err) {
      console.error(`Error fetching portal details for ${portalId}:`, err);
      setError('Failed to load portal details. Please try again later.');
    } finally {
      setDetailsLoading(false);
    }
  };
  
  // Handle close details dialog
  const handleCloseDetails = () => {
    setDetailsOpen(false);
    setSelectedPortal(null);
  };
  
  // Handle tab change for main page
  const handleMainPageTabChange = (event, newValue) => {
    setMainPageTab(newValue);
  };
  
  // Handle lock door
  const handleLockDoor = async (doorId) => {
    try {
      setError(null);
      await lenelS2NetBoxService.lockDoor(doorId);
      // Refresh door status if we're on the doors tab
      if (mainPageTab === 5) {
        const updatedDoors = await lenelS2NetBoxService.getDoors();
        setDoors(updatedDoors);
      }
    } catch (err) {
      console.error(`Error locking door ${doorId}:`, err);
      setError('Failed to lock door. Please try again later.');
    }
  };
  
  // Handle unlock door
  const handleUnlockDoor = async (doorId) => {
    try {
      setError(null);
      await lenelS2NetBoxService.unlockDoor(doorId);
      // Refresh door status if we're on the doors tab
      if (mainPageTab === 5) {
        const updatedDoors = await lenelS2NetBoxService.getDoors();
        setDoors(updatedDoors);
      }
    } catch (err) {
      console.error(`Error unlocking door ${doorId}:`, err);
      setError('Failed to unlock door. Please try again later.');
    }
  };

  // Card management handlers
  const handleCardManagement = (credential) => {
    setCardManagementCredential(credential);
    setCardManagementOpen(true);
  };

  const handleCardStatusChanged = (credentialId, newStatus) => {
    // Update the credential status in the users list
    setUsers(prevUsers =>
      prevUsers.map(user => ({
        ...user,
        credentials: user.credentials?.map(cred =>
          cred.id === credentialId
            ? { ...cred, status: newStatus, disabled: newStatus === 'Lost' }
            : cred
        ) || []
      }))
    );

    // Show success message
    setSnackbarMessage(`Card ${credentialId} status updated to ${newStatus}`);
    setSnackbarOpen(true);
  };

  // Elevator control handlers
  const handleElevatorControl = (elevator) => {
    setSelectedElevator(elevator);
    setElevatorControlOpen(true);
  };

  const handleElevatorControlled = (elevatorId, controlData) => {
    // Refresh elevators list if needed
    setSnackbarMessage(`Elevator ${elevatorId} control action completed successfully`);
    setSnackbarOpen(true);
  };

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Lenel S2 NetBox
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              Lenel S2 NetBox integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the Lenel S2 NetBox integration, an administrator needs to configure it first.
            </Typography>
            <Button 
              component={RouterLink} 
              to="/help/lenel-s2-netbox" 
              variant="contained" 
              color="primary"
              sx={{ mt: 2 }}
            >
              View Setup Help
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Lenel S2 NetBox Portal Control
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs 
              value={mainPageTab} 
              onChange={handleMainPageTabChange} 
              aria-label="lenel s2 netbox tabs"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab 
                icon={<MeetingRoomIcon />} 
                iconPosition="start" 
                label="Portals" 
              />
              <Tab 
                icon={<VpnKeyIcon />} 
                iconPosition="start" 
                label="Access Levels" 
              />
              <Tab 
                icon={<GroupIcon />} 
                iconPosition="start" 
                label="Access Groups" 
              />
              <Tab 
                icon={<BadgeIcon />} 
                iconPosition="start" 
                label="Badges" 
              />
              <Tab 
                icon={<ScheduleIcon />} 
                iconPosition="start" 
                label="Door Schedules" 
              />
              <Tab 
                icon={<MeetingRoomIcon />}
                iconPosition="start" 
                label="Doors" 
              />
              <Tab
                icon={<PersonIcon />}
                iconPosition="start"
                label="Users"
              />
              <Tab
                icon={<ActivityIcon />}
                iconPosition="start"
                label="Activity Log"
              />
              <Tab
                icon={<ReportProblemIcon />}
                iconPosition="start"
                label="Evacuations"
              />
              <Tab
                icon={<SensorsIcon />}
                iconPosition="start"
                label="Readers"
              />
              <Tab
                icon={<GroupIcon />}
                iconPosition="start"
                label="Reader Groups"
              />
              <Tab
                icon={<GroupIcon />}
                iconPosition="start"
                label="Portal Groups"
              />
              <Tab
                icon={<ElevatorIcon />}
                iconPosition="start"
                label="Elevators"
              />
            </Tabs>
          </Box>
          
          {/* Portals Tab */}
          {mainPageTab === 0 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Portals</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Portal functionality")}
                >
                  Add Portal
                </Button>
              </Box>
              
              {portals.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {portals.map((portal) => (
                        <TableRow key={portal.id}>
                          <TableCell>{portal.name}</TableCell>
                          <TableCell>{portal.status}</TableCell>
                          <TableCell>{portal.location}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleViewDetails(portal.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Portal">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Portal", portal.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Portal">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Portal", portal.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">
                  No portals found.
                </Typography>
              )}
            </Paper>
          )}
          
          {/* Access Levels Tab */}
          {mainPageTab === 1 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Access Levels</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Access Level functionality")}
                >
                  Add Access Level
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : accessLevels.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>ID</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {accessLevels.map((level) => (
                        <TableRow key={level.id}>
                          <TableCell>{level.name}</TableCell>
                          <TableCell>{level.id}</TableCell>
                          <TableCell>{level.description || 'N/A'}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("View Access Level details", level.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Access Level">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Access Level", level.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Access Level">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Access Level", level.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No access levels found.</Typography>
              )}
            </Paper>
          )}
          
          {/* Access Groups Tab */}
          {mainPageTab === 2 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Access Groups</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Access Group functionality")}
                >
                  Add Access Group
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : accessGroups.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>ID</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {accessGroups.map((group) => (
                        <TableRow key={group.id}>
                          <TableCell>{group.name}</TableCell>
                          <TableCell>{group.id}</TableCell>
                          <TableCell>{group.description || 'N/A'}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("View Access Group details", group.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Access Group">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Access Group", group.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Access Group">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Access Group", group.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No access groups found.</Typography>
              )}
            </Paper>
          )}
          
          {/* Badges Tab */}
          {mainPageTab === 3 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Badges</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Badge functionality")}
                >
                  Add Badge
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : badges.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name/Number</TableCell>
                        <TableCell>Cardholder</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {badges.map((badge) => (
                        <TableRow key={badge.id}>
                          <TableCell>{badge.name || badge.badgeNumber}</TableCell>
                          <TableCell>{badge.cardholderName || 'Unknown'}</TableCell>
                          <TableCell>{badge.status || 'Unknown'}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("View Badge details", badge.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Badge">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Badge", badge.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Badge">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Badge", badge.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No badges found.</Typography>
              )}
            </Paper>
          )}
          
          {/* Door Schedules Tab */}
          {mainPageTab === 4 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Door Schedules</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Door Schedule functionality")}
                >
                  Add Door Schedule
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : doorSchedules.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {doorSchedules.map((schedule) => (
                        <TableRow key={schedule.id}>
                          <TableCell>{schedule.name}</TableCell>
                          <TableCell>{schedule.type || 'Standard'}</TableCell>
                          <TableCell>{schedule.description || 'N/A'}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("View Door Schedule details", schedule.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Door Schedule">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Door Schedule", schedule.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Door Schedule">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Door Schedule", schedule.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No door schedules found.</Typography>
              )}
            </Paper>
          )}
          
          {/* Doors Tab */}
          {mainPageTab === 5 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Doors</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Door functionality")}
                >
                  Add Door
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : doors.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {doors.map((door) => (
                        <TableRow key={door.id}>
                          <TableCell>{door.name}</TableCell>
                          <TableCell>{door.status || 'Unknown'}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("View Door details", door.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Door">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Door", door.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Door">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Door", door.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Lock Door">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  aria-label="lock door"
                                  onClick={() => handleLockDoor(door.id)}
                                >
                                  <LockIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Unlock Door">
                                <IconButton 
                                  size="small" 
                                  color="secondary" 
                                  aria-label="unlock door"
                                  onClick={() => handleUnlockDoor(door.id)}
                                >
                                  <LockOpenIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No doors found.</Typography>
              )}
            </Paper>
          )}
          
          {/* Users Tab */}
          {mainPageTab === 6 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Users</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={handleCreateUser}
                >
                  Add User
                </Button>
              </Box>
              
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : users.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Email</TableCell>
                        <TableCell>Department</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            {user.firstName} {user.lastName}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>{user.department}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View User">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleViewUser(user)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit User">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleEditUser(user)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete User">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleDeleteUser(user.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No users found.</Typography>
              )}
            </Paper>
          )}

          {/* Activity Log Tab */}
          {mainPageTab === 7 && (
            <LiveActivityLog />
          )}

          {/* Evacuations Tab */}
          {mainPageTab === 8 && (
            <EvacuationManagement />
          )}

          {/* Readers Tab */}
          {mainPageTab === 9 && (
            <ReaderManagement />
          )}

          {/* Reader Groups Tab */}
          {mainPageTab === 10 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>Reader Groups</Typography>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : readerGroups.length > 0 ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Reader Count</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {readerGroups.map((group) => (
                        <TableRow key={group.id}>
                          <TableCell>{group.name}</TableCell>
                          <TableCell>{group.description || 'N/A'}</TableCell>
                          <TableCell>{group.readerCount}</TableCell>
                          <TableCell>
                            <Chip
                              label={group.status}
                              color="success"
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No reader groups found.</Typography>
              )}
            </Paper>
          )}

          {/* Portal Groups Tab */}
          {mainPageTab === 11 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Portal Groups</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Portal Group functionality")}
                >
                  Add Portal Group
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : portalGroups.length > 0 ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Portal Count</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {portalGroups.map((group) => (
                        <TableRow key={group.id}>
                          <TableCell>{group.name}</TableCell>
                          <TableCell>{group.description || 'N/A'}</TableCell>
                          <TableCell>{group.portalCount}</TableCell>
                          <TableCell>
                            <Chip
                              label={group.status}
                              color="success"
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("View Portal Group details", group.id)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Portal Group">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Portal Group", group.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Portal Group">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Portal Group", group.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No portal groups found.</Typography>
              )}
            </Paper>
          )}

          {/* Elevators Tab */}
          {mainPageTab === 12 && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">Elevators</Typography>
                <Button 
                  variant="contained" 
                  color="primary" 
                  startIcon={<AddIcon />}
                  onClick={() => handleFeatureDialog("Create Elevator functionality")}
                >
                  Add Elevator
                </Button>
              </Box>
              {accessControlLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : elevators.length > 0 ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Name</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Current Floor</TableCell>
                        <TableCell>Total Floors</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {elevators.map((elevator) => (
                        <TableRow key={elevator.id}>
                          <TableCell>{elevator.name}</TableCell>
                          <TableCell>{elevator.location || 'N/A'}</TableCell>
                          <TableCell>{elevator.currentFloor || 'N/A'}</TableCell>
                          <TableCell>{elevator.totalFloors || 'N/A'}</TableCell>
                          <TableCell>
                            <Chip
                              label={elevator.status}
                              color={elevator.online ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => handleElevatorControl(elevator)}
                                >
                                  <VisibilityIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit Elevator">
                                <IconButton 
                                  size="small" 
                                  color="primary" 
                                  onClick={() => handleFeatureDialog("Edit Elevator", elevator.id)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Elevator">
                                <IconButton 
                                  size="small" 
                                  color="error" 
                                  onClick={() => handleFeatureDialog("Delete Elevator", elevator.id)}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Control Elevator">
                                <IconButton
                                  size="small"
                                  color="secondary"
                                  onClick={() => handleElevatorControl(elevator)}
                                >
                                  <ElevatorIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography variant="body1">No elevators found.</Typography>
              )}
            </Paper>
          )}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button 
            component={RouterLink} 
            to="/help/lenel-s2-netbox" 
            variant="outlined" 
            color="primary"
          >
            Setup Help
          </Button>
        </Box>
      </Box>
      
      {/* Simplified Portal Details Dialog */}
      <Dialog 
        open={detailsOpen} 
        onClose={handleCloseDetails}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {selectedPortal ? selectedPortal.name : 'Portal Details'}
          {detailsLoading && (
            <CircularProgress 
              size={24} 
              sx={{ ml: 2, verticalAlign: 'middle' }} 
            />
          )}
        </DialogTitle>
        <DialogContent dividers>
          {selectedPortal ? (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">Name</Typography>
                <Typography variant="body1" gutterBottom>{selectedPortal.name}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">Status</Typography>
                <Typography variant="body1" gutterBottom>{selectedPortal.status}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">Location</Typography>
                <Typography variant="body1" gutterBottom>{selectedPortal.location}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">Type</Typography>
                <Typography variant="body1" gutterBottom>{selectedPortal.type}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" fontWeight="bold">Portal ID</Typography>
                <Typography variant="body1" gutterBottom>{selectedPortal.id}</Typography>
              </Grid>
              {selectedPortal.portalKey && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Portal Key</Typography>
                  <Typography variant="body1" gutterBottom>{selectedPortal.portalKey}</Typography>
                </Grid>
              )}
              {selectedPortal.readerKey && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Reader Key</Typography>
                  <Typography variant="body1" gutterBottom>{selectedPortal.readerKey}</Typography>
                </Grid>
              )}
              <Grid item xs={12}>
                <Typography variant="subtitle1" fontWeight="bold">Additional Information</Typography>
                <Typography variant="body2" gutterBottom>
                  For access levels, users, and other features, please use the tabs on the main page.
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                  <Tooltip title="Lock Door">
                    <IconButton 
                      color="primary" 
                      aria-label="lock door"
                      onClick={() => handleLockDoor(selectedPortal.id)}
                    >
                      <LockIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Unlock Door">
                    <IconButton 
                      color="secondary" 
                      aria-label="unlock door"
                      onClick={() => handleUnlockDoor(selectedPortal.id)}
                    >
                      <LockOpenIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Grid>
            </Grid>
          ) : (
            <Typography variant="body1">Loading portal details...</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* User Form Dialog */}
      <Dialog
        open={userDialogOpen}
        onClose={() => setUserDialogOpen(false)}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {userDialogMode === 'create' ? 'Create User' : 
           userDialogMode === 'edit' ? 'Edit User' : 
           selectedUser ? `${selectedUser.firstName} ${selectedUser.lastName}` : 'User Details'}
        </DialogTitle>
        <DialogContent dividers>
          {userDialogMode === 'view' ? (
            <Box>
              {/* User Details View */}
              <Typography variant="h6" gutterBottom>User Information</Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Name</Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedUser?.firstName} {selectedUser?.lastName}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Email</Typography>
                  <Typography variant="body1" gutterBottom>{selectedUser?.email}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Phone</Typography>
                  <Typography variant="body1" gutterBottom>{selectedUser?.phone}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Department</Typography>
                  <Typography variant="body1" gutterBottom>{selectedUser?.department}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Title</Typography>
                  <Typography variant="body1" gutterBottom>{selectedUser?.title}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" fontWeight="bold">Employee ID</Typography>
                  <Typography variant="body1" gutterBottom>{selectedUser?.employeeId}</Typography>
                </Grid>
              </Grid>
              
              {/* User Credentials */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h6">Credentials</Typography>
                  <Button 
                    variant="outlined" 
                    size="small" 
                    startIcon={<AddIcon />}
                    onClick={handleCreateCredential}
                  >
                    Add Credential
                  </Button>
                </Box>
                
                {userCredentialsLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                    <CircularProgress size={24} />
                  </Box>
                ) : userCredentials.length > 0 ? (
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Type</TableCell>
                          <TableCell>Value</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Expiration</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {userCredentials.map((credential) => (
                          <TableRow key={credential.id}>
                            <TableCell>{credential.type}</TableCell>
                            <TableCell>{credential.value}</TableCell>
                            <TableCell>
                              <Chip 
                                label={credential.status} 
                                color={credential.status === 'active' ? 'success' : 'error'} 
                                size="small" 
                              />
                            </TableCell>
                            <TableCell>{credential.expirationDate}</TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleEditCredential(credential)}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                                <IconButton
                                  size="small"
                                  color="warning"
                                  onClick={() => handleCardManagement(credential)}
                                  title={credential.disabled || credential.status === 'Lost' ? 'Restore Card' : 'Mark as Lost'}
                                >
                                  <WarningIcon fontSize="small" />
                                </IconButton>
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => handleDeleteCredential(credential.id)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography variant="body2">No credentials found.</Typography>
                )}
              </Box>
              
              {/* User Access Levels */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>Access Levels</Typography>
                {selectedUser?.accessLevels?.length > 0 ? (
                  <Box>
                    {selectedUser.accessLevels.map((level) => (
                      <Chip 
                        key={level.id}
                        label={level.name} 
                        onDelete={() => handleRemoveAccessLevel(level.id)}
                        sx={{ m: 0.5 }}
                      />
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2">No access levels assigned.</Typography>
                )}
                
                <Box sx={{ mt: 2 }}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="access-level-select-label">Assign Access Level</InputLabel>
                    <Select
                      labelId="access-level-select-label"
                      id="access-level-select"
                      label="Assign Access Level"
                      value=""
                      onChange={(e) => handleAssignAccessLevel(e.target.value)}
                    >
                      {accessLevels.map((level) => (
                        <MenuItem key={level.id} value={level.id}>{level.name}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Box>
              
              {/* User Activity Logs */}
              <Box>
                <Typography variant="h6" gutterBottom>Activity Logs</Typography>
                {userLogsLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                    <CircularProgress size={24} />
                  </Box>
                ) : userLogs.length > 0 ? (
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Date</TableCell>
                          <TableCell>Event</TableCell>
                          <TableCell>Location</TableCell>
                          <TableCell>Details</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {userLogs.map((log) => (
                          <TableRow key={log.id}>
                            <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
                            <TableCell>{log.event}</TableCell>
                            <TableCell>{log.location}</TableCell>
                            <TableCell>{log.details}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography variant="body2">No activity logs found.</Typography>
                )}
              </Box>
            </Box>
          ) : (
            /* User Form for Create/Edit */
            <Box component="form" noValidate>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="firstName"
                    label="First Name"
                    value={userFormData.firstName}
                    onChange={handleUserFormChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="lastName"
                    label="Last Name"
                    value={userFormData.lastName}
                    onChange={handleUserFormChange}
                    fullWidth
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="email"
                    label="Email"
                    type="email"
                    value={userFormData.email}
                    onChange={handleUserFormChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="phone"
                    label="Phone"
                    value={userFormData.phone}
                    onChange={handleUserFormChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="department"
                    label="Department"
                    value={userFormData.department}
                    onChange={handleUserFormChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="title"
                    label="Title"
                    value={userFormData.title}
                    onChange={handleUserFormChange}
                    fullWidth
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    name="employeeId"
                    label="Employee ID"
                    value={userFormData.employeeId}
                    onChange={handleUserFormChange}
                    fullWidth
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          {userDialogMode === 'view' ? (
            <Button onClick={() => setUserDialogOpen(false)}>Close</Button>
          ) : (
            <>
              <Button onClick={() => setUserDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleUserFormSubmit} variant="contained" color="primary">
                {userDialogMode === 'create' ? 'Create' : 'Update'}
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
      
      {/* Credential Form Dialog */}
      <Dialog
        open={credentialDialogOpen}
        onClose={() => setCredentialDialogOpen(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {selectedCredential ? 'Edit Credential' : 'Add Credential'}
        </DialogTitle>
        <DialogContent dividers>
          <Box component="form" noValidate sx={{ mt: 1 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="credential-type-label">Type</InputLabel>
                  <Select
                    labelId="credential-type-label"
                    name="type"
                    value={credentialFormData.type}
                    onChange={handleCredentialFormChange}
                    label="Type"
                  >
                    <MenuItem value="card">Card</MenuItem>
                    <MenuItem value="pin">PIN</MenuItem>
                    <MenuItem value="biometric">Biometric</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="value"
                  label="Value"
                  value={credentialFormData.value}
                  onChange={handleCredentialFormChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="expirationDate"
                  label="Expiration Date"
                  type="date"
                  value={credentialFormData.expirationDate}
                  onChange={handleCredentialFormChange}
                  fullWidth
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="credential-status-label">Status</InputLabel>
                  <Select
                    labelId="credential-status-label"
                    name="status"
                    value={credentialFormData.status}
                    onChange={handleCredentialFormChange}
                    label="Status"
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="suspended">Suspended</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCredentialDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCredentialFormSubmit} variant="contained" color="primary">
            {selectedCredential ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Card Management Dialog */}
      <CardManagement
        open={cardManagementOpen}
        onClose={() => setCardManagementOpen(false)}
        credential={cardManagementCredential}
        onCardStatusChanged={handleCardStatusChanged}
      />
      
      {/* Feature Coming Soon Dialog */}
      <FeatureComingSoonDialog
        open={featureDialogOpen}
        onClose={() => setFeatureDialogOpen(false)}
        title={featureDialogProps.title}
        featureName={featureDialogProps.featureName}
        entityId={featureDialogProps.entityId}
      />

      {/* Elevator Control Dialog */}
      <ElevatorControl
        open={elevatorControlOpen}
        onClose={() => setElevatorControlOpen(false)}
        elevator={selectedElevator}
        onElevatorControlled={handleElevatorControlled}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        message={snackbarMessage}
      />
    </Container>
  );
};

export default LenelS2NetBoxPage;