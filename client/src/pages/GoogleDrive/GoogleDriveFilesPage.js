import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Paper,
  Breadcrumbs,
  Link,
  IconButton
} from '@mui/material';
import { 
  Search as SearchIcon,
  Folder as FolderIcon,
  Description as FileIcon,
  Image as ImageIcon,
  Movie as VideoIcon,
  AudioFile as AudioIcon,
  PictureAsPdf as PdfIcon,
  TableChart as SpreadsheetIcon,
  Slideshow as PresentationIcon,
  Code as CodeIcon,
  InsertDriveFile as GenericFileIcon,
  ArrowBack as BackIcon,
  Settings as SettingsIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import googleDriveService from '../../services/googleDriveService';
import moment from 'moment';

const GoogleDriveFilesPage = () => {
  const navigate = useNavigate();
  const [files, setFiles] = useState([]);
  const [favorites, setFavorites] = useState([]);
  const [showFavorites, setShowFavorites] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searching, setSearching] = useState(false);
  const [configStatus, setConfigStatus] = useState(null);
  const [favoriteLoading, setFavoriteLoading] = useState({});
  const [currentFolderId, setCurrentFolderId] = useState('root');
  const [folderPath, setFolderPath] = useState([{ id: 'root', name: 'My Drive' }]);

  // Check configuration status and load favorites
  useEffect(() => {
    const checkConfig = async () => {
      try {
        const config = await googleDriveService.getConfig();
        setConfigStatus(config);

        if (config && config.isAuthenticated) {
          await fetchFiles();
          await fetchFavorites();
        } else {
          setLoading(false);
        }
      } catch (err) {
        console.error('Error checking configuration:', err);
        setError('Failed to check Google Drive configuration');
        setLoading(false);
      }
    };

    checkConfig();
  }, []);

  // Fetch favorites from the server
  const fetchFavorites = async () => {
    try {
      const favorites = await googleDriveService.getFavorites();
      setFavorites(favorites);
    } catch (err) {
      console.error('Error fetching favorites:', err);
      // Don't set error state here to avoid blocking the main functionality
    }
  };

  // Fetch files from Google Drive
  const fetchFiles = async () => {
    try {
      setLoading(true);
      // Pass the current folder ID as a query parameter
      const options = { 
        q: currentFolderId === 'root' 
          ? "'root' in parents" 
          : `'${currentFolderId}' in parents` 
      };
      const data = await googleDriveService.listFiles(options);
      setFiles(data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching files:', err);
      setError('Failed to load files from Google Drive');
      setLoading(false);
    }
  };

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Handle search submission
  const handleSearch = async (event) => {
    event.preventDefault();

    if (!searchTerm.trim()) {
      return;
    }

    try {
      setSearching(true);
      const data = await googleDriveService.searchFiles(searchTerm);
      setFiles(data);
      setSearching(false);
    } catch (err) {
      console.error('Error searching files:', err);
      setError('Failed to search Google Drive');
      setSearching(false);
    }
  };

  // Reset search and fetch all files
  const handleResetSearch = async () => {
    setSearchTerm('');
    fetchFiles();
  };

  // Navigate to configuration page
  const handleGoToConfig = () => {
    navigate('/google-drive/config');
  };

  // Navigate to viewer page or enter folder
  const handleViewFile = (file) => {
    // If it's a folder, navigate into it
    if (file.mimeType === 'application/vnd.google-apps.folder') {
      navigateToFolder(file);
    } else {
      // Otherwise, open the file viewer
      navigate(`/google-drive/view/${file.id}`);
    }
  };

  // Navigate into a folder
  const navigateToFolder = (folder) => {
    // Update current folder ID
    setCurrentFolderId(folder.id);
    
    // Add folder to path
    setFolderPath([...folderPath, { id: folder.id, name: folder.name }]);
    
    // Reset search term
    setSearchTerm('');
    
    // Fetch files in the folder
    fetchFilesInFolder(folder.id);
  };

  // Fetch files in a specific folder
  const fetchFilesInFolder = async (folderId) => {
    try {
      setLoading(true);
      const options = { 
        q: `'${folderId}' in parents` 
      };
      const data = await googleDriveService.listFiles(options);
      setFiles(data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching files in folder:', err);
      setError('Failed to load files from Google Drive folder');
      setLoading(false);
    }
  };

  // Navigate to a specific folder in the path
  const navigateToPathFolder = (index) => {
    // Get the folder from the path
    const folder = folderPath[index];
    
    // Update current folder ID
    setCurrentFolderId(folder.id);
    
    // Update path to include only up to this folder
    setFolderPath(folderPath.slice(0, index + 1));
    
    // Reset search term
    setSearchTerm('');
    
    // Fetch files in the folder
    fetchFilesInFolder(folder.id);
  };

  // Check if a file is favorited
  const isFavorited = (fileId) => {
    return favorites.some(favorite => favorite.fileId === fileId);
  };

  // Toggle favorite status
  const toggleFavorite = async (event, fileId) => {
    event.stopPropagation(); // Prevent triggering the file click

    // Set loading state for this file
    setFavoriteLoading(prev => ({ ...prev, [fileId]: true }));

    try {
      if (isFavorited(fileId)) {
        // Remove from favorites
        await googleDriveService.removeFavorite(fileId);
        setFavorites(favorites.filter(favorite => favorite.fileId !== fileId));
      } else {
        // Add to favorites
        const result = await googleDriveService.addFavorite(fileId);
        setFavorites([...favorites, result.favorite]);
      }
    } catch (err) {
      console.error('Error toggling favorite status:', err);
      // Show a temporary error message
      setError('Failed to update favorite status');
      setTimeout(() => setError(null), 3000);
    } finally {
      // Clear loading state for this file
      setFavoriteLoading(prev => ({ ...prev, [fileId]: false }));
    }
  };

  // Toggle between showing all files and only favorites
  const toggleShowFavorites = () => {
    setShowFavorites(!showFavorites);
  };

  // Get appropriate icon based on file mime type
  const getFileIcon = (mimeType) => {
    if (mimeType.includes('folder')) return <FolderIcon />;
    if (mimeType.includes('image')) return <ImageIcon />;
    if (mimeType.includes('video')) return <VideoIcon />;
    if (mimeType.includes('audio')) return <AudioIcon />;
    if (mimeType.includes('pdf')) return <PdfIcon />;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return <SpreadsheetIcon />;
    if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return <PresentationIcon />;
    if (mimeType.includes('document') || mimeType.includes('word')) return <FileIcon />;
    if (mimeType.includes('text') || mimeType.includes('code')) return <CodeIcon />;
    return <GenericFileIcon />;
  };

  // Convert MIME type to user-friendly file type name
  const getFileTypeName = (mimeType) => {
    if (mimeType.includes('folder')) return 'Folder';
    if (mimeType.includes('image')) return 'Image';
    if (mimeType.includes('video')) return 'Video';
    if (mimeType.includes('audio')) return 'Audio';
    if (mimeType.includes('pdf')) return 'PDF';
    if (mimeType.includes('spreadsheet')) return 'Spreadsheet';
    if (mimeType.includes('excel')) return 'Excel';
    if (mimeType.includes('presentation')) return 'Presentation';
    if (mimeType.includes('powerpoint')) return 'PowerPoint';
    if (mimeType.includes('document')) return 'Document';
    if (mimeType.includes('word')) return 'Word';
    if (mimeType.includes('text')) return 'Text';
    if (mimeType.includes('code')) return 'Code';
    return 'File';
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Byte';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Google Drive Files
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Browse and search files in Google Drive
          </Typography>
        </Box>

        {!configStatus || !configStatus.isAuthenticated ? (
          <Box sx={{ my: 4 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SettingsIcon />}
              onClick={handleGoToConfig}
              fullWidth
              size="large"
              sx={{ py: 2 }}
            >
              Configure Google Drive
            </Button>
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSearch} sx={{ mb: 4 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={9}>
                  <TextField
                    fullWidth
                    label="Search Files"
                    value={searchTerm}
                    onChange={handleSearchChange}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                      endAdornment: searching && (
                        <InputAdornment position="end">
                          <CircularProgress size={24} />
                        </InputAdornment>
                      )
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ display: 'flex', gap: 1, height: '100%' }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={searching || !searchTerm.trim()}
                      sx={{ flex: 1 }}
                    >
                      Search
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleResetSearch}
                      disabled={searching || !searchTerm.trim()}
                      sx={{ flex: 1 }}
                    >
                      Reset
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Folder Path Breadcrumbs */}
            <Box sx={{ mb: 2 }}>
              <Breadcrumbs aria-label="folder path">
                {folderPath.map((folder, index) => (
                  <Link
                    key={folder.id}
                    color={index === folderPath.length - 1 ? "text.primary" : "inherit"}
                    sx={{ 
                      cursor: index === folderPath.length - 1 ? 'default' : 'pointer',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                    onClick={() => index < folderPath.length - 1 && navigateToPathFolder(index)}
                    underline={index === folderPath.length - 1 ? "none" : "hover"}
                  >
                    {index === 0 ? (
                      <>
                        <FolderIcon sx={{ mr: 0.5 }} fontSize="small" />
                        {folder.name}
                      </>
                    ) : folder.name}
                  </Link>
                ))}
              </Breadcrumbs>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {showFavorites ? 'Favorite Files' : 'All Files'}
              </Typography>
              <Button
                variant={showFavorites ? "contained" : "outlined"}
                color="primary"
                startIcon={<StarIcon />}
                onClick={toggleShowFavorites}
                sx={{ ml: 2 }}
              >
                {showFavorites ? 'Show All Files' : 'Show Favorites'}
              </Button>
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                {(() => {
                  // Filter files based on showFavorites state
                  const displayedFiles = showFavorites
                    ? files.filter(file => isFavorited(file.id))
                    : files;

                  if (displayedFiles.length === 0) {
                    return (
                      <Box sx={{ textAlign: 'center', my: 8 }}>
                        <Typography variant="h6" color="text.secondary">
                          {showFavorites 
                            ? 'No favorite files found. Star some files to add them to your favorites.'
                            : 'No files found'}
                        </Typography>
                      </Box>
                    );
                  }

                  return (
                    <List>
                      {displayedFiles.map((file) => (
                        <ListItem
                          key={file.id}
                          disablePadding
                          divider
                          secondaryAction={
                            <IconButton 
                              edge="end" 
                              aria-label="favorite"
                              onClick={(e) => toggleFavorite(e, file.id)}
                              disabled={favoriteLoading[file.id]}
                              color={isFavorited(file.id) ? "warning" : "default"}
                            >
                              {favoriteLoading[file.id] ? (
                                <CircularProgress size={24} />
                              ) : isFavorited(file.id) ? (
                                <StarIcon />
                              ) : (
                                <StarBorderIcon />
                              )}
                            </IconButton>
                          }
                        >
                          <ListItemButton onClick={() => handleViewFile(file)}>
                            <ListItemIcon>
                              {getFileIcon(file.mimeType)}
                            </ListItemIcon>
                            <ListItemText
                              primary={file.name}
                              secondary={
                                <>
                                  <Typography component="span" variant="body2" color="text.primary">
                                    {getFileTypeName(file.mimeType)}
                                  </Typography>
                                  {' • '}
                                  <Typography component="span" variant="body2" color="text.primary">
                                    {formatFileSize(file.size)}
                                  </Typography>
                                  {' • '}
                                  <Typography component="span" variant="body2" color="text.primary">
                                    Modified: {moment(file.modifiedTime).format('MMM D, YYYY')}
                                  </Typography>
                                  {file.owners && file.owners.length > 0 && (
                                    <>
                                      {' • '}
                                      <Typography component="span" variant="body2" color="text.primary">
                                        Owner: {file.owners[0].displayName}
                                      </Typography>
                                    </>
                                  )}
                                </>
                              }
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  );
                })()}
              </>
            )}
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleDriveFilesPage;
