import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Snackbar,
  Tab,
  Tabs,
  TextField,
  Typography,
  Alert
} from '@mui/material';
import { 
  Add as AddIcon,
  Save as SaveIcon,
  Preview as PreviewIcon,
  Settings as SettingsIcon,
  Publish as PublishIcon,
  ArrowBack as ArrowBackIcon,
  ContentCopy as DuplicateIcon
} from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import formService from '../../services/formService';
import FieldTypeSelector from '../../components/Forms/FieldTypeSelector';
import FieldEditor from '../../components/Forms/FieldEditor';
import FormPreview from '../../components/Forms/FormPreview';
import FormSettings from '../../components/Forms/FormSettings';
import FieldGroupEditor from '../../components/Forms/FieldGroupEditor';
import ConfirmDialog from '../../components/common/ConfirmDialog';

/**
 * Form Builder Page
 * Main interface for creating and editing forms
 */
const FormBuilderPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(id ? true : false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [confirmDialog, setConfirmDialog] = useState({ open: false, title: '', message: '', onConfirm: null });
  
  // Form state
  const [form, setForm] = useState({
    title: '',
    description: '',
    slug: '',
    status: 'draft',
    formType: 'standard',
    styling: {
      theme: 'default',
      primaryColor: '#3f51b5',
      backgroundColor: '#ffffff',
      fontFamily: 'Roboto, sans-serif'
    },
    fieldGroups: [{
      title: 'Default Group',
      description: '',
      order: 0,
      conditionalDisplay: {
        enabled: false,
        conditions: [],
        logicType: 'and'
      },
      fields: []
    }],
    submission: {
      successMessage: 'Thank you for your submission!',
      redirectUrl: '',
      notifications: [],
      createTicket: {
        enabled: false,
        fieldMappings: {},
        defaults: {}
      },
      createTask: {
        enabled: false,
        fieldMappings: {},
        defaults: {}
      }
    },
    permissions: {
      access: 'authenticated',
      accessRoles: [],
      accessUsers: [],
      accessGroups: [],
      manageRoles: [],
      manageUsers: [],
      manageGroups: [],
      viewSubmissionsRoles: [],
      viewSubmissionsUsers: [],
      viewSubmissionsGroups: []
    }
  });
  
  // Selected field and group state
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(0);
  const [selectedFieldIndex, setSelectedFieldIndex] = useState(-1);
  
  // Load form data if editing an existing form
  useEffect(() => {
    if (id) {
      loadForm();
    }
  }, [id]);
  
  // Load form data from API
  const loadForm = async () => {
    try {
      setLoading(true);
      const response = await formService.getForm(id);
      if (response.success) {
        setForm(response.data);
        // Select first group by default
        if (response.data.fieldGroups && response.data.fieldGroups.length > 0) {
          setSelectedGroupIndex(0);
        }
      } else {
        setSnackbar({
          open: true,
          message: 'Failed to load form',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error loading form:', error);
      setSnackbar({
        open: true,
        message: 'Error loading form: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Save form
  const saveForm = async () => {
    try {
      setSaving(true);
      
      // Validate form
      if (!form.title) {
        setSnackbar({
          open: true,
          message: 'Form title is required',
          severity: 'error'
        });
        setSaving(false);
        return;
      }
      
      // Create or update form
      let response;
      if (id) {
        response = await formService.updateForm(id, form);
      } else {
        response = await formService.createForm(form);
      }
      
      if (response.success) {
        setSnackbar({
          open: true,
          message: id ? 'Form updated successfully' : 'Form created successfully',
          severity: 'success'
        });
        
        // If creating a new form, redirect to edit page
        if (!id) {
          navigate(`/forms/edit/${response.data._id}`);
        } else {
          // Update form data with response
          setForm(response.data);
        }
      } else {
        setSnackbar({
          open: true,
          message: 'Failed to save form',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error saving form:', error);
      setSnackbar({
        open: true,
        message: 'Error saving form: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };
  
  // Publish form
  const publishForm = () => {
    setConfirmDialog({
      open: true,
      title: 'Publish Form',
      message: 'Are you sure you want to publish this form? It will be available to users based on the permission settings.',
      onConfirm: async () => {
        try {
          setSaving(true);
          const updatedForm = { ...form, status: 'published' };
          const response = await formService.updateForm(id, updatedForm);
          
          if (response.success) {
            setForm(response.data);
            setSnackbar({
              open: true,
              message: 'Form published successfully',
              severity: 'success'
            });
          } else {
            setSnackbar({
              open: true,
              message: 'Failed to publish form',
              severity: 'error'
            });
          }
        } catch (error) {
          console.error('Error publishing form:', error);
          setSnackbar({
            open: true,
            message: 'Error publishing form: ' + (error.response?.data?.message || error.message),
            severity: 'error'
          });
        } finally {
          setSaving(false);
        }
      }
    });
  };
  
  // Duplicate form
  const duplicateForm = async () => {
    if (!id) {
      setSnackbar({
        open: true,
        message: 'Please save the form before duplicating it',
        severity: 'warning'
      });
      return;
    }
    
    try {
      setSaving(true);
      const response = await formService.duplicateForm(id);
      
      if (response.success) {
        setSnackbar({
          open: true,
          message: 'Form duplicated successfully',
          severity: 'success'
        });
        
        // Navigate to the new form
        navigate(`/forms/edit/${response.data._id}`);
      } else {
        setSnackbar({
          open: true,
          message: 'Failed to duplicate form',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error duplicating form:', error);
      setSnackbar({
        open: true,
        message: 'Error duplicating form: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    } finally {
      setSaving(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle form field changes
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setForm(prevForm => ({
      ...prevForm,
      [name]: value
    }));
  };
  
  // Add a new field group
  const addFieldGroup = () => {
    const newGroup = {
      title: `Group ${form.fieldGroups.length + 1}`,
      description: '',
      order: form.fieldGroups.length,
      conditionalDisplay: {
        enabled: false,
        conditions: [],
        logicType: 'and'
      },
      fields: []
    };
    
    setForm(prevForm => ({
      ...prevForm,
      fieldGroups: [...prevForm.fieldGroups, newGroup]
    }));
    
    // Select the new group
    setSelectedGroupIndex(form.fieldGroups.length);
    setSelectedFieldIndex(-1);
  };
  
  // Update a field group
  const updateFieldGroup = (index, updatedGroup) => {
    setForm(prevForm => {
      const updatedGroups = [...prevForm.fieldGroups];
      updatedGroups[index] = updatedGroup;
      return {
        ...prevForm,
        fieldGroups: updatedGroups
      };
    });
  };
  
  // Delete a field group
  const deleteFieldGroup = (index) => {
    if (form.fieldGroups.length === 1) {
      setSnackbar({
        open: true,
        message: 'Cannot delete the only field group',
        severity: 'error'
      });
      return;
    }
    
    setConfirmDialog({
      open: true,
      title: 'Delete Field Group',
      message: 'Are you sure you want to delete this field group? All fields in this group will be deleted.',
      onConfirm: () => {
        setForm(prevForm => {
          const updatedGroups = prevForm.fieldGroups.filter((_, i) => i !== index);
          return {
            ...prevForm,
            fieldGroups: updatedGroups
          };
        });
        
        // Update selected group if needed
        if (selectedGroupIndex === index) {
          setSelectedGroupIndex(0);
          setSelectedFieldIndex(-1);
        } else if (selectedGroupIndex > index) {
          setSelectedGroupIndex(selectedGroupIndex - 1);
        }
      }
    });
  };
  
  // Add a new field to the selected group
  const addField = (fieldType) => {
    const newField = {
      type: fieldType,
      fieldId: `field_${uuidv4()}`,
      label: `New ${fieldType} field`,
      description: '',
      placeholder: '',
      required: false,
      order: form.fieldGroups[selectedGroupIndex].fields.length,
      options: {},
      validation: {},
      conditionalDisplay: {
        enabled: false,
        conditions: [],
        logicType: 'and'
      },
      styling: {
        width: 'full'
      }
    };
    
    // Add field type specific options
    switch (fieldType) {
      case 'select':
      case 'multiselect':
      case 'radio':
      case 'checkbox':
        newField.options.choices = [
          { label: 'Option 1', value: 'option_1' },
          { label: 'Option 2', value: 'option_2' }
        ];
        break;
      case 'number':
      case 'slider':
        newField.options.min = 0;
        newField.options.max = 100;
        newField.options.step = 1;
        break;
      case 'file':
        newField.options.maxFileSize = 5242880; // 5MB
        newField.options.allowedFileTypes = ['image/*', 'application/pdf'];
        newField.options.maxFiles = 1;
        break;
      case 'date':
        newField.options.dateFormat = 'YYYY-MM-DD';
        break;
      case 'time':
        newField.options.timeFormat = 'HH:mm';
        break;
      case 'html':
        newField.options.htmlContent = '<p>HTML content goes here</p>';
        break;
      default:
        break;
    }
    
    setForm(prevForm => {
      const updatedGroups = [...prevForm.fieldGroups];
      updatedGroups[selectedGroupIndex].fields.push(newField);
      return {
        ...prevForm,
        fieldGroups: updatedGroups
      };
    });
    
    // Select the new field
    setSelectedFieldIndex(form.fieldGroups[selectedGroupIndex].fields.length);
  };
  
  // Update a field in the selected group
  const updateField = (index, updatedField) => {
    setForm(prevForm => {
      const updatedGroups = [...prevForm.fieldGroups];
      updatedGroups[selectedGroupIndex].fields[index] = updatedField;
      return {
        ...prevForm,
        fieldGroups: updatedGroups
      };
    });
  };
  
  // Delete a field from the selected group
  const deleteField = (index) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Field',
      message: 'Are you sure you want to delete this field?',
      onConfirm: () => {
        setForm(prevForm => {
          const updatedGroups = [...prevForm.fieldGroups];
          updatedGroups[selectedGroupIndex].fields = updatedGroups[selectedGroupIndex].fields.filter((_, i) => i !== index);
          return {
            ...prevForm,
            fieldGroups: updatedGroups
          };
        });
        
        // Update selected field if needed
        if (selectedFieldIndex === index) {
          setSelectedFieldIndex(-1);
        } else if (selectedFieldIndex > index) {
          setSelectedFieldIndex(selectedFieldIndex - 1);
        }
      }
    });
  };
  
  // Move a field up or down in the selected group
  const moveField = (index, direction) => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === form.fieldGroups[selectedGroupIndex].fields.length - 1)
    ) {
      return;
    }
    
    setForm(prevForm => {
      const updatedGroups = [...prevForm.fieldGroups];
      const fields = [...updatedGroups[selectedGroupIndex].fields];
      const newIndex = direction === 'up' ? index - 1 : index + 1;
      
      // Swap fields
      [fields[index], fields[newIndex]] = [fields[newIndex], fields[index]];
      
      // Update order property
      fields[index].order = index;
      fields[newIndex].order = newIndex;
      
      updatedGroups[selectedGroupIndex].fields = fields;
      
      return {
        ...prevForm,
        fieldGroups: updatedGroups
      };
    });
    
    // Update selected field
    setSelectedFieldIndex(direction === 'up' ? index - 1 : index + 1);
  };
  
  // Update form settings
  const updateFormSettings = (updatedSettings) => {
    setForm(prevForm => ({
      ...prevForm,
      ...updatedSettings
    }));
  };
  
  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/forms')}
            sx={{ mr: 2 }}
          >
            Back to Forms
          </Button>
          <Typography variant="h4">
            {id ? 'Edit Form' : 'Create Form'}
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={saveForm}
            disabled={saving}
            sx={{ mr: 1 }}
          >
            Save
          </Button>
          {id && (
            <Button
              variant="outlined"
              startIcon={<DuplicateIcon />}
              onClick={duplicateForm}
              disabled={saving}
              sx={{ mr: 1 }}
            >
              Duplicate
            </Button>
          )}
          {id && form.status === 'draft' && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<PublishIcon />}
              onClick={publishForm}
              disabled={saving}
            >
              Publish
            </Button>
          )}
          {id && form.status === 'published' && (
            <Button
              variant="contained"
              color="secondary"
              startIcon={<PreviewIcon />}
              onClick={() => window.open(`/forms/view/${form.slug || id}`, '_blank')}
            >
              View Live Form
            </Button>
          )}
        </Box>
      </Box>
      
      {/* Form Title and Description */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Form Title"
              name="title"
              value={form.title}
              onChange={handleFormChange}
              required
              variant="outlined"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Form Slug (for custom URL)"
              name="slug"
              value={form.slug}
              onChange={handleFormChange}
              variant="outlined"
              helperText="Leave blank to auto-generate from title"
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Form Description"
              name="description"
              value={form.description}
              onChange={handleFormChange}
              variant="outlined"
              multiline
              rows={2}
            />
          </Grid>
        </Grid>
      </Paper>
      
      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="Form Builder" />
          <Tab label="Form Preview" />
          <Tab label="Settings" />
        </Tabs>
      </Paper>
      
      {/* Tab Content */}
      {activeTab === 0 && (
        <Grid container spacing={3}>
          {/* Field Groups Panel */}
          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 2, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Field Groups
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box mb={2}>
                {form.fieldGroups.map((group, index) => (
                  <Button
                    key={index}
                    variant={selectedGroupIndex === index ? 'contained' : 'outlined'}
                    fullWidth
                    sx={{ mb: 1, justifyContent: 'flex-start', textAlign: 'left' }}
                    onClick={() => {
                      setSelectedGroupIndex(index);
                      setSelectedFieldIndex(-1);
                    }}
                  >
                    {group.title}
                  </Button>
                ))}
              </Box>
              
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                fullWidth
                onClick={addFieldGroup}
              >
                Add Group
              </Button>
            </Paper>
          </Grid>
          
          {/* Field Editor Panel */}
          <Grid item xs={12} md={9}>
            <Paper sx={{ p: 2 }}>
              {/* Group Editor */}
              <FieldGroupEditor
                group={form.fieldGroups[selectedGroupIndex]}
                onUpdate={(updatedGroup) => updateFieldGroup(selectedGroupIndex, updatedGroup)}
                onDelete={() => deleteFieldGroup(selectedGroupIndex)}
              />
              
              <Divider sx={{ my: 2 }} />
              
              {/* Fields List */}
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Fields
                </Typography>
                <FieldTypeSelector onSelectFieldType={addField} />
              </Box>
              
              {form.fieldGroups[selectedGroupIndex].fields.length === 0 ? (
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  justifyContent="center"
                  p={4}
                  bgcolor="background.default"
                  borderRadius={1}
                >
                  <Typography variant="body1" color="textSecondary" gutterBottom>
                    No fields added yet
                  </Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Use the "Add Field" button to add fields to this group
                  </Typography>
                </Box>
              ) : (
                <Box>
                  {form.fieldGroups[selectedGroupIndex].fields.map((field, index) => (
                    <Box
                      key={field.fieldId}
                      sx={{
                        p: 2,
                        mb: 2,
                        border: '1px solid',
                        borderColor: selectedFieldIndex === index ? 'primary.main' : 'divider',
                        borderRadius: 1,
                        cursor: 'pointer',
                        '&:hover': {
                          borderColor: 'primary.light'
                        }
                      }}
                      onClick={() => setSelectedFieldIndex(index)}
                    >
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="subtitle1">
                          {field.label} ({field.type})
                        </Typography>
                        <Box>
                          <Button
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              moveField(index, 'up');
                            }}
                            disabled={index === 0}
                          >
                            Up
                          </Button>
                          <Button
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              moveField(index, 'down');
                            }}
                            disabled={index === form.fieldGroups[selectedGroupIndex].fields.length - 1}
                          >
                            Down
                          </Button>
                          <Button
                            size="small"
                            color="error"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteField(index);
                            }}
                          >
                            Delete
                          </Button>
                        </Box>
                      </Box>
                      
                      {selectedFieldIndex === index && (
                        <Box mt={2}>
                          <Divider sx={{ mb: 2 }} />
                          <FieldEditor
                            field={field}
                            allFields={form.fieldGroups.flatMap(g => g.fields)}
                            onUpdate={(updatedField) => updateField(index, updatedField)}
                          />
                        </Box>
                      )}
                    </Box>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 1 && (
        <Paper sx={{ p: 2 }}>
          <FormPreview form={form} />
        </Paper>
      )}
      
      {activeTab === 2 && (
        <Paper sx={{ p: 2 }}>
          <FormSettings
            form={form}
            onUpdate={updateFormSettings}
          />
        </Paper>
      )}
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
      
      {/* Confirmation Dialog */}
      <ConfirmDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={() => {
          confirmDialog.onConfirm();
          setConfirmDialog({ ...confirmDialog, open: false });
        }}
        onCancel={() => setConfirmDialog({ ...confirmDialog, open: false })}
      />
    </Container>
  );
};

export default FormBuilderPage;