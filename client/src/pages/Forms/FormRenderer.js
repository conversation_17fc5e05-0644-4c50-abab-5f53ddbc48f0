import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Paper,
  Snackbar,
  Typography,
  Alert
} from '@mui/material';
import formService from '../../services/formService';
import FormContainer from '../../components/Forms/FormContainer';

/**
 * Form Renderer Page
 * Displays a form for users to fill out and submit
 */
const FormRenderer = () => {
  const { id } = useParams(); // This can be either the form ID or slug
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [form, setForm] = useState(null);
  const [formData, setFormData] = useState({});
  const [files, setFiles] = useState([]);
  const [errors, setErrors] = useState({});
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  
  // Load form data
  useEffect(() => {
    loadForm();
  }, [id]);
  
  // Load form from API
  const loadForm = async () => {
    try {
      setLoading(true);
      
      // Determine if this is a public form or authenticated form
      const isPublic = window.location.pathname.includes('/public/');
      
      let response;
      if (isPublic) {
        response = await formService.getPublicForm(id);
      } else {
        response = await formService.getForm(id);
      }
      
      if (response.success) {
        setForm(response.data);
        
        // Initialize form data with default values
        const initialData = {};
        response.data.fieldGroups.forEach(group => {
          group.fields.forEach(field => {
            if (field.defaultValue !== undefined && field.defaultValue !== null) {
              initialData[field.fieldId] = field.defaultValue;
            } else if (['checkbox', 'multiselect'].includes(field.type)) {
              initialData[field.fieldId] = [];
            } else if (field.type === 'rating') {
              initialData[field.fieldId] = 0;
            } else {
              initialData[field.fieldId] = '';
            }
          });
        });
        
        setFormData(initialData);
      } else {
        setSnackbar({
          open: true,
          message: 'Failed to load form',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error loading form:', error);
      setSnackbar({
        open: true,
        message: 'Error loading form: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form data change
  const handleFormDataChange = (newFormData) => {
    setFormData(newFormData);
  };
  
  // Handle file change
  const handleFileChange = (newFiles) => {
    setFiles(newFiles);
  };
  
  // Handle form submission
  const handleSubmit = async (submittedData) => {
    try {
      setSubmitting(true);
      setErrors({});
      
      // Determine if this is a public form or authenticated form
      const isPublic = window.location.pathname.includes('/public/');
      
      let response;
      if (isPublic) {
        response = await formService.submitPublicForm(id, submittedData, files);
      } else {
        response = await formService.submitForm(form._id, submittedData, files);
      }
      
      if (response.success) {
        setSnackbar({
          open: true,
          message: form.submission?.successMessage || 'Form submitted successfully!',
          severity: 'success'
        });
        
        // Clear form data
        setFormData({});
        setFiles([]);
        
        // Redirect if specified
        if (form.submission?.redirectUrl) {
          setTimeout(() => {
            window.location.href = form.submission.redirectUrl;
          }, 2000);
        }
      } else {
        setSnackbar({
          open: true,
          message: 'Failed to submit form',
          severity: 'error'
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      
      // Handle validation errors
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
      }
      
      setSnackbar({
        open: true,
        message: 'Error submitting form: ' + (error.response?.data?.message || error.message),
        severity: 'error'
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }
  
  // Render error state if form not found
  if (!form) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" color="error" gutterBottom>
            Form Not Found
          </Typography>
          <Typography variant="body1" paragraph>
            The form you are looking for does not exist or you do not have permission to access it.
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={() => navigate('/')}
          >
            Return to Home
          </Button>
        </Paper>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <FormContainer
        form={form}
        formData={formData}
        onFormDataChange={handleFormDataChange}
        onFileChange={handleFileChange}
        onSubmit={handleSubmit}
        errors={errors}
        submitting={submitting}
      />
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default FormRenderer;