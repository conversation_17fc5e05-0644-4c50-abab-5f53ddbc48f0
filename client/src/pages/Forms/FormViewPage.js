import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON>ert,
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  Paper,
  Snackbar,
  Typography
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import formService from '../../services/formService';
import FormRenderer from './FormRenderer';

/**
 * Form View Page
 * Displays a form for users to fill out and submit
 */
const FormViewPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  
  // State
  const [form, setForm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({});
  const [files, setFiles] = useState([]);
  const [submitted, setSubmitted] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Load form data on component mount
  useEffect(() => {
    loadForm();
  }, [slug]);

  // Function to load form data
  const loadForm = async () => {
    try {
      setLoading(true);
      const response = await formService.getPublicForm(slug);
      setForm(response);
      setError(null);
      
      // Initialize form data with default values
      const initialData = {};
      if (response.fieldGroups) {
        response.fieldGroups.forEach(group => {
          if (group.fields) {
            group.fields.forEach(field => {
              if (field.defaultValue !== undefined) {
                initialData[field.id] = field.defaultValue;
              }
            });
          }
        });
      }
      setFormData(initialData);
    } catch (err) {
      console.error('Error loading form:', err);
      setError('The form you are looking for could not be found or is not available.');
      setForm(null);
    } finally {
      setLoading(false);
    }
  };

  // Handle form field changes
  const handleFieldChange = (fieldId, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  // Handle file uploads
  const handleFileUpload = (fieldId, file) => {
    // Remove any existing file for this field
    const updatedFiles = files.filter(f => f.fieldId !== fieldId);
    
    // Add the new file if it exists
    if (file) {
      updatedFiles.push({ fieldId, file });
    }
    
    setFiles(updatedFiles);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setError(null);
      
      await formService.submitPublicForm(slug, formData, files);
      
      // Show success message
      setSnackbar({
        open: true,
        message: form.submission?.successMessage || 'Form submitted successfully!',
        severity: 'success'
      });
      
      setSubmitted(true);
      
      // Redirect if a redirect URL is specified
      if (form.submission?.redirectUrl) {
        setTimeout(() => {
          window.location.href = form.submission.redirectUrl;
        }, 2000);
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      setError(err.response?.data?.message || 'Failed to submit form. Please try again.');
      setSnackbar({
        open: true,
        message: 'Failed to submit form. Please check your inputs and try again.',
        severity: 'error'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Handle back button click
  const handleBack = () => {
    navigate(-1);
  };

  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Render error state
  if (error) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" color="error" gutterBottom>
            Form Not Available
          </Typography>
          <Typography variant="body1" paragraph>
            {error}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Go Back
          </Button>
        </Paper>
      </Container>
    );
  }

  // Render success state after submission
  if (submitted && !form.submission?.redirectUrl) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h5" color="primary" gutterBottom>
            Thank You!
          </Typography>
          <Typography variant="body1" paragraph>
            {form.submission?.successMessage || 'Your form has been submitted successfully.'}
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/forms')}
            sx={{ mr: 2 }}
          >
            Back to Forms
          </Button>
          <Button
            variant="outlined"
            onClick={() => {
              setSubmitted(false);
              setFormData({});
              setFiles([]);
            }}
          >
            Submit Another Response
          </Button>
        </Paper>
      </Container>
    );
  }

  // Render form
  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: { xs: 2, sm: 4 } }}>
        {/* Form Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            {form.title}
          </Typography>
          {form.description && (
            <Typography variant="body1" color="textSecondary">
              {form.description}
            </Typography>
          )}
        </Box>
        
        <Divider sx={{ mb: 4 }} />
        
        {/* Form Renderer */}
        <FormRenderer
          form={form}
          formData={formData}
          onFieldChange={handleFieldChange}
          onFileUpload={handleFileUpload}
          readOnly={false}
        />
        
        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {/* Submit button */}
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <>
                <CircularProgress size={24} sx={{ mr: 1 }} />
                Submitting...
              </>
            ) : (
              'Submit'
            )}
          </Button>
        </Box>
      </Paper>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbar.severity}
          variant="filled"
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default FormViewPage;