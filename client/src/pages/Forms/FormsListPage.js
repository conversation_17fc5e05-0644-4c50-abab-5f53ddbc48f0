import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  Chip,
  CircularProgress,
  Container,
  Divider,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Pagination,
  Paper,
  Select,
  TextField,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  FileCopy as DuplicateIcon,
  Search as SearchIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import formService from '../../services/formService';
import ConfirmDialog from '../../components/common/ConfirmDialog';

/**
 * Forms List Page
 * Displays a list of all forms with filtering and pagination
 */
const FormsListPage = () => {
  // State for forms data
  const [forms, setForms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Pagination state
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit, setLimit] = useState(10);
  
  // Filtering and sorting state
  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });
  const [sort, setSort] = useState('createdAt');
  const [order, setOrder] = useState('desc');
  
  // Confirm dialog state
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    onConfirm: null
  });

  // Load forms on component mount and when filters/pagination change
  useEffect(() => {
    loadForms();
  }, [page, limit, filters, sort, order]);

  // Function to load forms from the API
  const loadForms = async () => {
    try {
      setLoading(true);
      const response = await formService.getForms(filters, page, limit, sort, order);
      setForms(response.forms);
      setTotalPages(Math.ceil(response.total / limit));
      setError(null);
    } catch (err) {
      console.error('Error loading forms:', err);
      setError('Failed to load forms. Please try again.');
      setForms([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setPage(1); // Reset to first page when filters change
  };

  // Handle search input
  const handleSearchChange = (event) => {
    const { value } = event.target;
    setFilters(prev => ({
      ...prev,
      search: value
    }));
    setPage(1); // Reset to first page when search changes
  };

  // Handle pagination change
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  // Handle form deletion
  const handleDeleteForm = async (id) => {
    try {
      await formService.deleteForm(id);
      loadForms(); // Reload forms after deletion
    } catch (err) {
      console.error('Error deleting form:', err);
      setError('Failed to delete form. Please try again.');
    }
  };

  // Handle form duplication
  const handleDuplicateForm = async (id) => {
    try {
      await formService.duplicateForm(id);
      loadForms(); // Reload forms after duplication
    } catch (err) {
      console.error('Error duplicating form:', err);
      setError('Failed to duplicate form. Please try again.');
    }
  };

  // Open confirm dialog for deletion
  const confirmDelete = (id, title) => {
    setConfirmDialog({
      open: true,
      title: 'Delete Form',
      message: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
      onConfirm: () => {
        handleDeleteForm(id);
        setConfirmDialog(prev => ({ ...prev, open: false }));
      }
    });
  };

  // Get status chip color based on form status
  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'draft':
        return 'default';
      case 'archived':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Forms
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          component={RouterLink}
          to="/forms/new"
        >
          Create Form
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              name="search"
              label="Search Forms"
              value={filters.search}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              fullWidth
              name="status"
              label="Status"
              value={filters.status}
              onChange={handleFilterChange}
            >
              <MenuItem value="">All Statuses</MenuItem>
              <MenuItem value="published">Published</MenuItem>
              <MenuItem value="draft">Draft</MenuItem>
              <MenuItem value="archived">Archived</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              fullWidth
              label="Sort By"
              value={sort}
              onChange={(e) => {
                setSort(e.target.value);
                setPage(1);
              }}
            >
              <MenuItem value="createdAt">Created Date</MenuItem>
              <MenuItem value="updatedAt">Updated Date</MenuItem>
              <MenuItem value="title">Title</MenuItem>
              <MenuItem value="status">Status</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <TextField
              select
              fullWidth
              label="Order"
              value={order}
              onChange={(e) => {
                setOrder(e.target.value);
                setPage(1);
              }}
            >
              <MenuItem value="asc">Ascending</MenuItem>
              <MenuItem value="desc">Descending</MenuItem>
            </TextField>
          </Grid>
        </Grid>
      </Paper>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography color="error">{error}</Typography>
          <Button 
            variant="outlined" 
            sx={{ mt: 2 }} 
            onClick={loadForms}
          >
            Try Again
          </Button>
        </Paper>
      ) : !forms || forms.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">No forms found</Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
            {filters.search || filters.status 
              ? 'Try adjusting your filters'
              : 'Create your first form to get started'}
          </Typography>
          {!filters.search && !filters.status && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to="/forms/new"
              sx={{ mt: 2 }}
            >
              Create Form
            </Button>
          )}
        </Paper>
      ) : (
        <>
          <Grid container spacing={3}>
            {forms.map((form) => (
              <Grid item xs={12} sm={6} md={4} key={form._id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Typography variant="h6" component="h2" noWrap sx={{ mb: 1 }}>
                        {form.title}
                      </Typography>
                      <Chip 
                        label={form.status.charAt(0).toUpperCase() + form.status.slice(1)} 
                        size="small" 
                        color={getStatusColor(form.status)}
                      />
                    </Box>
                    <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                      {form.description 
                        ? (form.description.length > 100 
                            ? `${form.description.substring(0, 100)}...` 
                            : form.description)
                        : 'No description'}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="textSecondary">
                        Created: {new Date(form.createdAt).toLocaleDateString()}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {form.submissions || 0} submissions
                      </Typography>
                    </Box>
                  </CardContent>
                  <Divider />
                  <CardActions>
                    <Tooltip title="View Form">
                      <IconButton 
                        component={RouterLink} 
                        to={`/forms/view/${form.slug}`}
                        size="small"
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Form">
                      <IconButton 
                        component={RouterLink} 
                        to={`/forms/edit/${form._id}`}
                        size="small"
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Duplicate Form">
                      <IconButton 
                        onClick={() => handleDuplicateForm(form._id)}
                        size="small"
                      >
                        <DuplicateIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Form">
                      <IconButton 
                        onClick={() => confirmDelete(form._id, form.title)}
                        size="small"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <Pagination 
              count={totalPages} 
              page={page} 
              onChange={handlePageChange} 
              color="primary" 
            />
          </Box>
        </>
      )}

      <ConfirmDialog
        open={confirmDialog.open}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={confirmDialog.onConfirm}
        onCancel={() => setConfirmDialog(prev => ({ ...prev, open: false }))}
      />
    </Container>
  );
};

export default FormsListPage;