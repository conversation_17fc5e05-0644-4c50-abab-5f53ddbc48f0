import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Tabs,
  Tab,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import * as menuItemService from '../../services/menuItemService';
import * as menuCategoryService from '../../services/menuCategoryService';
import MenuCategoryManagement from '../../components/Admin/MenuCategoryManagement';
import MenuItemsManagement from '../../components/Admin/MenuItemsManagement';

// This is a tabbed interface with two tabs:
// 1. Menu Categories - for managing menu categories
// 2. Menu Items - for managing menu items

const AdminMenuPage = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Menu Management
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Manage menu categories and items to organize the navigation menu and Apps page
        </Typography>
      </Box>

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="Menu Categories" />
          <Tab label="Menu Items" />
        </Tabs>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Tab content */}
          {tabValue === 0 && (
            <MenuCategoryManagement />
          )}

          {tabValue === 1 && (
            <MenuItemsManagement />
          )}
        </>
      )}
    </Container>
  );
};

export default AdminMenuPage;