import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  PersonAdd as PersonAddIcon,
  PersonRemove as PersonRemoveIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const ProvisioningManagementPage = () => {
  const { user } = useAuth();
  const [services, setServices] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    displayName: '',
    description: '',
    enabled: false,
    autoProvisionRoles: [],
    provisioningConfig: {}
  });

  // Fetch services and roles on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [servicesRes, rolesRes] = await Promise.all([
          axios.get('/api/provisioning/services'),
          axios.get('/api/roles')
        ]);
        setServices(servicesRes.data);
        setRoles(rolesRes.data);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load provisioning data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle opening the edit dialog
  const handleOpenDialog = (service) => {
    setSelectedService(service);
    setFormData({
      displayName: service.displayName || '',
      description: service.description || '',
      enabled: service.enabled || false,
      autoProvisionRoles: service.autoProvisionRoles || [],
      provisioningConfig: service.provisioningConfig || {}
    });
    setDialogOpen(true);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked
    });
  };

  // Handle role selection changes
  const handleRoleChange = (event) => {
    const {
      target: { value },
    } = event;
    setFormData({
      ...formData,
      autoProvisionRoles: typeof value === 'string' ? value.split(',') : value,
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const res = await axios.put(`/api/provisioning/services/${selectedService.id}`, formData);
      
      // Update the services list with the updated service
      setServices(prevServices => 
        prevServices.map(service => 
          service.id === selectedService.id ? res.data : service
        )
      );
      
      setSuccess('Service provisioning settings updated successfully');
      setDialogOpen(false);
    } catch (err) {
      console.error('Error updating service:', err);
      setError('Failed to update service provisioning settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle toggling service enabled status
  const handleToggleService = async (serviceId, enabled) => {
    try {
      setLoading(true);
      const res = await axios.put(`/api/provisioning/services/${serviceId}/toggle`, { enabled });
      
      // Update the services list with the updated service
      setServices(prevServices => 
        prevServices.map(service => 
          service.id === serviceId ? { ...service, enabled: res.data.enabled } : service
        )
      );
      
      setSuccess(`Service ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (err) {
      console.error('Error toggling service:', err);
      setError(`Failed to ${enabled ? 'enable' : 'disable'} service. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSuccess(null);
    setError(null);
  };

  if (loading && services.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Provisioning Management
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Service Provisioning Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Configure how provisioning and deprovisioning of each integration is handled and what roles get which provisioning automatically.
        </Typography>

        <TableContainer component={Paper} sx={{ mt: 3 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Service</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Auto-Provisioned Roles</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {services.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No services available
                  </TableCell>
                </TableRow>
              ) : (
                services.map((service) => (
                  <TableRow key={service.id}>
                    <TableCell>{service.displayName || service.id}</TableCell>
                    <TableCell>{service.description || 'No description'}</TableCell>
                    <TableCell>
                      {service.enabled ? (
                        <Chip 
                          icon={<CheckCircleIcon />} 
                          label="Enabled" 
                          color="success" 
                          size="small" 
                        />
                      ) : (
                        <Chip 
                          icon={<ErrorIcon />} 
                          label="Disabled" 
                          color="default" 
                          size="small" 
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      {service.autoProvisionRoles && service.autoProvisionRoles.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {service.autoProvisionRoles.map((role) => (
                            <Chip 
                              key={role} 
                              label={role} 
                              size="small" 
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          None
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => handleOpenDialog(service)}
                        >
                          Configure
                        </Button>
                        <Button
                          variant="outlined"
                          size="small"
                          color={service.enabled ? "error" : "primary"}
                          onClick={() => handleToggleService(service.id, !service.enabled)}
                        >
                          {service.enabled ? "Disable" : "Enable"}
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Edit Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Configure Service Provisioning: {selectedService?.displayName || selectedService?.id}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Display Name"
                  name="displayName"
                  value={formData.displayName}
                  onChange={handleInputChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.enabled}
                      onChange={handleCheckboxChange}
                      name="enabled"
                      color="primary"
                    />
                  }
                  label="Enabled"
                  sx={{ mt: 2 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  margin="normal"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="roles-select-label">Auto-Provision for Roles</InputLabel>
                  <Select
                    labelId="roles-select-label"
                    multiple
                    value={formData.autoProvisionRoles}
                    onChange={handleRoleChange}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {roles.map((role) => (
                      <MenuItem key={role.name} value={role.name}>
                        {role.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Typography variant="h6" sx={{ mt: 4, mb: 2 }}>
              Advanced Configuration
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Provisioning Configuration</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Configure advanced settings for this service's provisioning process.
                </Typography>
                
                {/* Add service-specific configuration options here */}
                <TextField
                  fullWidth
                  label="Configuration JSON"
                  name="provisioningConfigJson"
                  value={JSON.stringify(formData.provisioningConfig, null, 2)}
                  onChange={(e) => {
                    try {
                      const config = JSON.parse(e.target.value);
                      setFormData({
                        ...formData,
                        provisioningConfig: config
                      });
                    } catch (err) {
                      // Don't update if JSON is invalid
                      console.error('Invalid JSON:', err);
                    }
                  }}
                  margin="normal"
                  multiline
                  rows={8}
                />
              </AccordionDetails>
            </Accordion>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={Boolean(success) || Boolean(error)}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={success ? "success" : "error"} 
          sx={{ width: '100%' }}
        >
          {success || error}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ProvisioningManagementPage;