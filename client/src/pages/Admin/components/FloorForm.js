import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Grid, 
  TextField, 
  Button, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem,
  Typography,
  Divider,
  FormHelperText,
  Switch,
  FormControlLabel,
  Paper
} from '@mui/material';
import { CloudUpload as CloudUploadIcon } from '@mui/icons-material';
import buildingManagementService from '../../../services/buildingManagementService';

const FloorForm = ({ floor, buildingId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    buildingId: buildingId || '',
    name: '',
    level: '',
    description: '',
    dimensions: {
      width: '',
      length: '',
      squareFootage: ''
    },
    status: 'active',
    metadata: {
      occupancy: '',
      publicAccess: true,
      notes: ''
    }
  });
  
  const [floorplanFile, setFloorplanFile] = useState(null);
  const [floorplanPreview, setFloorplanPreview] = useState('');
  const [fileError, setFileError] = useState('');

  // Initialize form with floor data if editing
  useEffect(() => {
    if (floor) {
      setFormData({
        buildingId: floor.buildingId || buildingId || '',
        name: floor.name || '',
        level: floor.level || '',
        description: floor.description || '',
        dimensions: {
          width: floor.dimensions?.width || '',
          length: floor.dimensions?.length || '',
          squareFootage: floor.dimensions?.squareFootage || ''
        },
        status: floor.status || 'active',
        metadata: {
          occupancy: floor.metadata?.occupancy || '',
          publicAccess: floor.metadata?.publicAccess !== false,
          notes: floor.metadata?.notes || ''
        }
      });
      
      // Set floorplan preview if exists
      if (floor.floorplan && floor.floorplan.path) {
        setFloorplanPreview(buildingManagementService.getFloorplanUrl(floor._id));
      }
    }
  }, [floor, buildingId]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Handle nested objects (dimensions, metadata)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle file input changes
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) {
      setFloorplanFile(null);
      setFloorplanPreview('');
      return;
    }
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setFileError('Invalid file type. Only JPEG, PNG, GIF, SVG, and PDF files are allowed.');
      setFloorplanFile(null);
      setFloorplanPreview('');
      return;
    }
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      setFileError('File size exceeds 10MB limit.');
      setFloorplanFile(null);
      setFloorplanPreview('');
      return;
    }
    
    setFileError('');
    setFloorplanFile(file);
    
    // Create preview for image files
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFloorplanPreview(reader.result);
      };
      reader.readAsDataURL(file);
    } else {
      // For PDFs, just show a placeholder
      setFloorplanPreview('pdf');
    }
  };

  // Calculate square footage automatically if width and length are provided
  useEffect(() => {
    const { width, length } = formData.dimensions;
    if (width && length) {
      const squareFootage = parseFloat(width) * parseFloat(length);
      setFormData(prev => ({
        ...prev,
        dimensions: {
          ...prev.dimensions,
          squareFootage: squareFootage.toFixed(0)
        }
      }));
    }
  }, [formData.dimensions.width, formData.dimensions.length]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Convert numeric fields to numbers
    const processedData = {
      ...formData,
      level: parseInt(formData.level, 10),
      dimensions: {
        ...formData.dimensions,
        width: formData.dimensions.width ? parseFloat(formData.dimensions.width) : undefined,
        length: formData.dimensions.length ? parseFloat(formData.dimensions.length) : undefined,
        squareFootage: formData.dimensions.squareFootage ? parseInt(formData.dimensions.squareFootage, 10) : undefined
      },
      metadata: {
        ...formData.metadata,
        occupancy: formData.metadata.occupancy ? parseInt(formData.metadata.occupancy, 10) : undefined,
        publicAccess: formData.metadata.publicAccess
      }
    };
    
    onSave(processedData, floorplanFile);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="h6">Basic Information</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            required
            fullWidth
            label="Floor Name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="e.g., First Floor, Basement"
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            required
            fullWidth
            label="Level"
            name="level"
            type="number"
            value={formData.level}
            onChange={handleChange}
            placeholder="e.g., 1, 2, -1 for basement"
            helperText="Use positive numbers for above ground, negative for below ground"
          />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            multiline
            rows={2}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              name="status"
              value={formData.status}
              onChange={handleChange}
              label="Status"
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
              <MenuItem value="under_construction">Under Construction</MenuItem>
              <MenuItem value="maintenance">Maintenance</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        {/* Dimensions */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mt: 2 }}>Dimensions</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <TextField
            fullWidth
            label="Width (ft/m)"
            name="dimensions.width"
            type="number"
            value={formData.dimensions.width}
            onChange={handleChange}
            inputProps={{ min: 0, step: 0.1 }}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <TextField
            fullWidth
            label="Length (ft/m)"
            name="dimensions.length"
            type="number"
            value={formData.dimensions.length}
            onChange={handleChange}
            inputProps={{ min: 0, step: 0.1 }}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <TextField
            fullWidth
            label="Square Footage"
            name="dimensions.squareFootage"
            type="number"
            value={formData.dimensions.squareFootage}
            onChange={handleChange}
            inputProps={{ min: 0 }}
            disabled={formData.dimensions.width && formData.dimensions.length}
            helperText={formData.dimensions.width && formData.dimensions.length ? "Auto-calculated" : ""}
          />
        </Grid>
        
        {/* Additional Information */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mt: 2 }}>Additional Information</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Maximum Occupancy"
            name="metadata.occupancy"
            type="number"
            value={formData.metadata.occupancy}
            onChange={handleChange}
            inputProps={{ min: 0 }}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.metadata.publicAccess}
                onChange={handleChange}
                name="metadata.publicAccess"
              />
            }
            label="Public Access"
          />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Notes"
            name="metadata.notes"
            value={formData.metadata.notes}
            onChange={handleChange}
            multiline
            rows={2}
          />
        </Grid>
        
        {/* Floorplan Upload */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mt: 2 }}>Floorplan</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12}>
          <input
            accept="image/*,application/pdf"
            style={{ display: 'none' }}
            id="floorplan-upload"
            type="file"
            onChange={handleFileChange}
          />
          <label htmlFor="floorplan-upload">
            <Button
              variant="outlined"
              component="span"
              startIcon={<CloudUploadIcon />}
              sx={{ mb: 2 }}
            >
              {floor && floor.floorplan ? 'Replace Floorplan' : 'Upload Floorplan'}
            </Button>
          </label>
          
          {fileError && (
            <FormHelperText error>{fileError}</FormHelperText>
          )}
          
          {(floorplanPreview || (floor && floor.floorplan)) && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                {floorplanFile ? floorplanFile.name : floor?.floorplan?.originalFilename || floor?.floorplan?.filename}
              </Typography>
              
              {floorplanPreview && floorplanPreview !== 'pdf' ? (
                <Paper elevation={2} sx={{ p: 1, mt: 1, maxWidth: 400 }}>
                  <img 
                    src={floorplanPreview} 
                    alt="Floorplan preview" 
                    style={{ maxWidth: '100%', maxHeight: 300 }} 
                  />
                </Paper>
              ) : floorplanPreview === 'pdf' ? (
                <Typography variant="body2" color="text.secondary">
                  PDF file selected (preview not available)
                </Typography>
              ) : floor?.floorplan ? (
                <Paper elevation={2} sx={{ p: 1, mt: 1, maxWidth: 400 }}>
                  <img 
                    src={floorplanPreview} 
                    alt="Current floorplan" 
                    style={{ maxWidth: '100%', maxHeight: 300 }} 
                  />
                </Paper>
              ) : null}
            </Box>
          )}
        </Grid>
        
        {/* Form Actions */}
        <Grid item xs={12} sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button onClick={onCancel} sx={{ mr: 1 }}>
            Cancel
          </Button>
          <Button type="submit" variant="contained" color="primary">
            {floor ? 'Update Floor' : 'Create Floor'}
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FloorForm;