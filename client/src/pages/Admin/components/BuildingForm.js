import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Grid, 
  TextField, 
  Button, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem,
  Typography,
  Divider
} from '@mui/material';

const BuildingForm = ({ building, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    address: {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    },
    description: '',
    buildingType: 'office',
    status: 'active',
    contactInfo: {
      primaryContact: '',
      phone: '',
      email: ''
    },
    metadata: {
      yearBuilt: '',
      squareFootage: '',
      numberOfFloors: ''
    }
  });

  // Initialize form with building data if editing
  useEffect(() => {
    if (building) {
      setFormData({
        name: building.name || '',
        address: {
          street: building.address?.street || '',
          city: building.address?.city || '',
          state: building.address?.state || '',
          zipCode: building.address?.zipCode || '',
          country: building.address?.country || 'USA'
        },
        description: building.description || '',
        buildingType: building.buildingType || 'office',
        status: building.status || 'active',
        contactInfo: {
          primaryContact: building.contactInfo?.primaryContact || '',
          phone: building.contactInfo?.phone || '',
          email: building.contactInfo?.email || ''
        },
        metadata: {
          yearBuilt: building.metadata?.yearBuilt || '',
          squareFootage: building.metadata?.squareFootage || '',
          numberOfFloors: building.metadata?.numberOfFloors || ''
        }
      });
    }
  }, [building]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Handle nested objects (address, contactInfo, metadata)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Convert numeric fields to numbers
    const processedData = {
      ...formData,
      metadata: {
        ...formData.metadata,
        yearBuilt: formData.metadata.yearBuilt ? parseInt(formData.metadata.yearBuilt, 10) : undefined,
        squareFootage: formData.metadata.squareFootage ? parseInt(formData.metadata.squareFootage, 10) : undefined,
        numberOfFloors: formData.metadata.numberOfFloors ? parseInt(formData.metadata.numberOfFloors, 10) : undefined
      }
    };
    
    onSave(processedData);
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="h6">Basic Information</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            required
            fullWidth
            label="Building Name"
            name="name"
            value={formData.name}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>Building Type</InputLabel>
            <Select
              name="buildingType"
              value={formData.buildingType}
              onChange={handleChange}
              label="Building Type"
            >
              <MenuItem value="office">Office</MenuItem>
              <MenuItem value="residential">Residential</MenuItem>
              <MenuItem value="mixed-use">Mixed-Use</MenuItem>
              <MenuItem value="educational">Educational</MenuItem>
              <MenuItem value="industrial">Industrial</MenuItem>
              <MenuItem value="other">Other</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            multiline
            rows={3}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              name="status"
              value={formData.status}
              onChange={handleChange}
              label="Status"
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
              <MenuItem value="under_construction">Under Construction</MenuItem>
              <MenuItem value="maintenance">Maintenance</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        
        {/* Address Information */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mt: 2 }}>Address</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Street Address"
            name="address.street"
            value={formData.address.street}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="City"
            name="address.city"
            value={formData.address.city}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={3}>
          <TextField
            fullWidth
            label="State"
            name="address.state"
            value={formData.address.state}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={3}>
          <TextField
            fullWidth
            label="Zip Code"
            name="address.zipCode"
            value={formData.address.zipCode}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Country"
            name="address.country"
            value={formData.address.country}
            onChange={handleChange}
          />
        </Grid>
        
        {/* Contact Information */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mt: 2 }}>Contact Information</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Primary Contact"
            name="contactInfo.primaryContact"
            value={formData.contactInfo.primaryContact}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Phone"
            name="contactInfo.phone"
            value={formData.contactInfo.phone}
            onChange={handleChange}
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Email"
            name="contactInfo.email"
            type="email"
            value={formData.contactInfo.email}
            onChange={handleChange}
          />
        </Grid>
        
        {/* Metadata */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mt: 2 }}>Additional Information</Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <TextField
            fullWidth
            label="Year Built"
            name="metadata.yearBuilt"
            type="number"
            value={formData.metadata.yearBuilt}
            onChange={handleChange}
            inputProps={{ min: 1800, max: new Date().getFullYear() }}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <TextField
            fullWidth
            label="Square Footage"
            name="metadata.squareFootage"
            type="number"
            value={formData.metadata.squareFootage}
            onChange={handleChange}
            inputProps={{ min: 0 }}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <TextField
            fullWidth
            label="Number of Floors"
            name="metadata.numberOfFloors"
            type="number"
            value={formData.metadata.numberOfFloors}
            onChange={handleChange}
            inputProps={{ min: 1 }}
          />
        </Grid>
        
        {/* Form Actions */}
        <Grid item xs={12} sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button onClick={onCancel} sx={{ mr: 1 }}>
            Cancel
          </Button>
          <Button type="submit" variant="contained" color="primary">
            {building ? 'Update Building' : 'Create Building'}
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BuildingForm;