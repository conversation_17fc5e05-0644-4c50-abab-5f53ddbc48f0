import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Container, 
  Paper, 
  Tabs,
  Tab,
  Button,
  Breadcrumbs,
  Link
} from '@mui/material';
import { 
  Article as ArticleIcon,
  Category as CategoryIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import NewsPostsTab from './NewsPostsTab';
import NewsCategoriesTab from './NewsCategoriesTab';

/**
 * Admin News Page
 * Allows administrators to manage news posts and categories
 */
const AdminNewsPage = () => {
  const { hasPermission } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);

  // Check if user has admin permission
  if (!hasPermission('news:admin')) {
    navigate('/dashboard');
    return null;
  }

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // <PERSON>le creating a new post
  const handleCreatePost = () => {
    navigate('/admin/news/posts/new');
  };

  // <PERSON>le creating a new category
  const handleCreateCategory = () => {
    navigate('/admin/news/categories/new');
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link component={RouterLink} to="/dashboard" color="inherit">
            Dashboard
          </Link>
          <Link component={RouterLink} to="/news" color="inherit">
            News
          </Link>
          <Typography color="text.primary">Manage News</Typography>
        </Breadcrumbs>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Manage News
        </Typography>
        
        <Box>
          {activeTab === 0 && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreatePost}
            >
              New Post
            </Button>
          )}
          
          {activeTab === 1 && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateCategory}
            >
              New Category
            </Button>
          )}
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab 
            icon={<ArticleIcon />} 
            label="News Posts" 
            id="news-tab-0"
            aria-controls="news-tabpanel-0"
          />
          <Tab 
            icon={<CategoryIcon />} 
            label="Categories" 
            id="news-tab-1"
            aria-controls="news-tabpanel-1"
          />
        </Tabs>
      </Paper>

      <Box
        role="tabpanel"
        hidden={activeTab !== 0}
        id="news-tabpanel-0"
        aria-labelledby="news-tab-0"
      >
        {activeTab === 0 && <NewsPostsTab />}
      </Box>

      <Box
        role="tabpanel"
        hidden={activeTab !== 1}
        id="news-tabpanel-1"
        aria-labelledby="news-tab-1"
      >
        {activeTab === 1 && <NewsCategoriesTab />}
      </Box>
    </Container>
  );
};

export default AdminNewsPage;