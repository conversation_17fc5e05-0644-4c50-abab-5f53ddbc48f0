import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Tooltip,
  FormHelperText,
  Grid,
  Divider
} from '@mui/material';
import { 
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon
} from '@mui/icons-material';
import newsService from '../../../services/newsService';
import { format } from 'date-fns';

/**
 * News Categories Tab
 * Displays a list of news categories with options to create, edit, and delete categories
 */
const NewsCategoriesTab = () => {
  const [categories, setCategories] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editCategoryId, setEditCategoryId] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    accessRoles: []
  });
  const [formErrors, setFormErrors] = useState({});
  const [createMode, setCreateMode] = useState(false);

  // Fetch categories and roles on component mount
  useEffect(() => {
    fetchCategories();
    fetchRoles();
  }, []);

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      const data = await newsService.getAllCategories();
      setCategories(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching news categories:', err);
      setError('Failed to load news categories. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch roles from API
  const fetchRoles = async () => {
    try {
      // This would typically come from a roles service
      // For now, we'll use a placeholder
      setRoles([
        { _id: 'admin', name: 'Administrator' },
        { _id: 'user', name: 'User' },
        { _id: 'editor', name: 'Editor' },
        { _id: 'guest', name: 'Guest' }
      ]);
    } catch (err) {
      console.error('Error fetching roles:', err);
      // Non-critical error, don't show to user
    }
  };

  // Handle opening delete dialog
  const handleOpenDeleteDialog = (category) => {
    setCategoryToDelete(category);
    setDeleteDialogOpen(true);
  };

  // Handle closing delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setCategoryToDelete(null);
  };

  // Handle delete category
  const handleDeleteCategory = async () => {
    if (!categoryToDelete) return;
    
    try {
      await newsService.deleteCategory(categoryToDelete._id);
      fetchCategories();
      handleCloseDeleteDialog();
    } catch (err) {
      console.error('Error deleting news category:', err);
      setError('Failed to delete news category. Please try again later.');
    }
  };

  // Handle edit category
  const handleEditCategory = (category) => {
    setEditMode(true);
    setEditCategoryId(category._id);
    setFormData({
      name: category.name,
      description: category.description || '',
      accessRoles: category.accessRoles || []
    });
    setFormErrors({});
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditMode(false);
    setEditCategoryId(null);
    setFormData({
      name: '',
      description: '',
      accessRoles: []
    });
    setFormErrors({});
  };

  // Handle create category
  const handleCreateCategory = () => {
    setCreateMode(true);
    setFormData({
      name: '',
      description: '',
      accessRoles: []
    });
    setFormErrors({});
  };

  // Handle cancel create
  const handleCancelCreate = () => {
    setCreateMode(false);
    setFormData({
      name: '',
      description: '',
      accessRoles: []
    });
    setFormErrors({});
  };

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Handle roles change
  const handleRolesChange = (event) => {
    const { value } = event.target;
    setFormData(prev => ({
      ...prev,
      accessRoles: value
    }));
  };

  // Validate form
  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    
    return errors;
  };

  // Handle save category
  const handleSaveCategory = async () => {
    const errors = validateForm();
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    try {
      if (editMode) {
        await newsService.updateCategory(editCategoryId, formData);
      } else {
        await newsService.createCategory(formData);
      }
      
      fetchCategories();
      setEditMode(false);
      setCreateMode(false);
      setEditCategoryId(null);
      setFormData({
        name: '',
        description: '',
        accessRoles: []
      });
    } catch (err) {
      console.error('Error saving news category:', err);
      setError('Failed to save news category. Please try again later.');
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Render category form
  const renderCategoryForm = () => (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        {editMode ? 'Edit Category' : 'Create New Category'}
      </Typography>
      <Divider sx={{ mb: 3 }} />
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            name="name"
            label="Category Name"
            fullWidth
            value={formData.name}
            onChange={handleInputChange}
            error={!!formErrors.name}
            helperText={formErrors.name}
            required
          />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel id="access-roles-label">Access Roles</InputLabel>
            <Select
              labelId="access-roles-label"
              multiple
              value={formData.accessRoles}
              onChange={handleRolesChange}
              label="Access Roles"
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const role = roles.find(r => r._id === value);
                    return (
                      <Chip 
                        key={value} 
                        label={role ? role.name : value} 
                        size="small" 
                      />
                    );
                  })}
                </Box>
              )}
            >
              {roles.map((role) => (
                <MenuItem key={role._id} value={role._id}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              Select roles that can access this category. If none selected, all users can access.
            </FormHelperText>
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            name="description"
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={formData.description}
            onChange={handleInputChange}
          />
        </Grid>
      </Grid>
      
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>
        <Button 
          variant="outlined" 
          startIcon={<CancelIcon />}
          onClick={editMode ? handleCancelEdit : handleCancelCreate}
        >
          Cancel
        </Button>
        <Button 
          variant="contained" 
          startIcon={<SaveIcon />}
          onClick={handleSaveCategory}
        >
          Save
        </Button>
      </Box>
    </Paper>
  );

  return (
    <Box>
      {!editMode && !createMode && (
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateCategory}
          >
            New Category
          </Button>
        </Box>
      )}
      
      {(editMode || createMode) && renderCategoryForm()}

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="news categories table">
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Access Roles</TableCell>
              <TableCell>Created</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : categories.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No categories found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              categories.map((category) => (
                <TableRow
                  key={category._id}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    {category.name}
                  </TableCell>
                  <TableCell>
                    {category.description || '-'}
                  </TableCell>
                  <TableCell>
                    {category.accessRoles && category.accessRoles.length > 0 ? (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {category.accessRoles.map(roleId => {
                          const role = roles.find(r => r._id === roleId);
                          return (
                            <Chip 
                              key={roleId} 
                              label={role ? role.name : roleId} 
                              size="small" 
                              color="primary" 
                              variant="outlined"
                            />
                          );
                        })}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        All users
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(category.createdAt)}</TableCell>
                  <TableCell align="right">
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Tooltip title="Edit category">
                        <IconButton 
                          onClick={() => handleEditCategory(category)} 
                          size="small" 
                          color="primary"
                          disabled={editMode || createMode}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Delete category">
                        <IconButton 
                          onClick={() => handleOpenDeleteDialog(category)} 
                          size="small" 
                          color="error"
                          disabled={editMode || createMode}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete News Category
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete the category "{categoryToDelete?.name}"? This action cannot be undone.
            <Box sx={{ mt: 2, color: 'error.main' }}>
              Warning: Deleting this category will affect all news posts assigned to it.
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteCategory} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NewsCategoriesTab;