import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Tooltip
} from '@mui/material';
import { 
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import newsService from '../../../services/newsService';
import { format } from 'date-fns';

/**
 * News Posts Tab
 * Displays a list of news posts with options to create, edit, and delete posts
 */
const NewsPostsTab = () => {
  const navigate = useNavigate();
  const [posts, setPosts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalPosts, setTotalPosts] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterPublished, setFilterPublished] = useState('all');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [postToDelete, setPostToDelete] = useState(null);

  // Fetch posts and categories on component mount and when filters change
  useEffect(() => {
    fetchCategories();
    fetchPosts();
  }, [page, rowsPerPage, searchTerm, filterCategory, filterPublished]);

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      const data = await newsService.getAllCategories();
      setCategories(data);
    } catch (err) {
      console.error('Error fetching news categories:', err);
      setError('Failed to load news categories. Please try again later.');
    }
  };

  // Fetch posts from API
  const fetchPosts = async () => {
    try {
      setLoading(true);
      
      // Prepare query parameters
      const params = {
        page: page + 1, // API uses 1-based indexing
        limit: rowsPerPage
      };
      
      // Add search term if provided
      if (searchTerm) {
        params.search = searchTerm;
      }
      
      // Add category filter if selected
      if (filterCategory !== 'all') {
        params.category = filterCategory;
      }
      
      // Add published filter if selected
      if (filterPublished !== 'all') {
        params.published = filterPublished === 'published';
      }
      
      const response = await newsService.getAllPosts(params);
      setPosts(response.posts);
      setTotalPosts(response.total);
      setError(null);
    } catch (err) {
      console.error('Error fetching news posts:', err);
      setError('Failed to load news posts. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Handle search submit
  const handleSearchSubmit = (event) => {
    event.preventDefault();
    fetchPosts();
  };

  // Handle category filter change
  const handleCategoryFilterChange = (event) => {
    setFilterCategory(event.target.value);
    setPage(0);
  };

  // Handle published filter change
  const handlePublishedFilterChange = (event) => {
    setFilterPublished(event.target.value);
    setPage(0);
  };

  // Handle edit post
  const handleEditPost = (postId) => {
    navigate(`/admin/news/posts/${postId}/edit`);
  };

  // Handle view post
  const handleViewPost = (postId) => {
    navigate(`/news/posts/${postId}`);
  };

  // Handle create post
  const handleCreatePost = () => {
    navigate('/admin/news/posts/new');
  };

  // Handle opening delete dialog
  const handleOpenDeleteDialog = (post) => {
    setPostToDelete(post);
    setDeleteDialogOpen(true);
  };

  // Handle closing delete dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setPostToDelete(null);
  };

  // Handle delete post
  const handleDeletePost = async () => {
    if (!postToDelete) return;
    
    try {
      await newsService.deletePost(postToDelete._id);
      fetchPosts();
      handleCloseDeleteDialog();
    } catch (err) {
      console.error('Error deleting news post:', err);
      setError('Failed to delete news post. Please try again later.');
    }
  };

  // Handle toggle featured status
  const handleToggleFeatured = async (postId, currentStatus) => {
    try {
      await newsService.toggleFeatured(postId);
      setPosts(posts.map(post => 
        post._id === postId ? { ...post, featured: !currentStatus } : post
      ));
    } catch (err) {
      console.error('Error toggling featured status:', err);
      setError('Failed to update post. Please try again later.');
    }
  };

  // Handle toggle published status
  const handleTogglePublished = async (postId, currentStatus) => {
    try {
      await newsService.togglePublished(postId);
      setPosts(posts.map(post => 
        post._id === postId ? { ...post, published: !currentStatus } : post
      ));
    } catch (err) {
      console.error('Error toggling published status:', err);
      setError('Failed to update post. Please try again later.');
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Get category name by ID
  const getCategoryName = (categoryId) => {
    const category = categories.find(cat => cat._id === categoryId);
    return category ? category.name : 'Unknown';
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
        <Box sx={{ flex: 1 }}>
          <form onSubmit={handleSearchSubmit}>
            <TextField
              fullWidth
              placeholder="Search posts..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Button type="submit" variant="contained" size="small">
                      Search
                    </Button>
                  </InputAdornment>
                )
              }}
            />
          </form>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel id="category-filter-label">Category</InputLabel>
            <Select
              labelId="category-filter-label"
              value={filterCategory}
              label="Category"
              onChange={handleCategoryFilterChange}
              size="small"
            >
              <MenuItem value="all">All Categories</MenuItem>
              {categories.map(category => (
                <MenuItem key={category._id} value={category._id}>
                  {category.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel id="published-filter-label">Status</InputLabel>
            <Select
              labelId="published-filter-label"
              value={filterPublished}
              label="Status"
              onChange={handlePublishedFilterChange}
              size="small"
            >
              <MenuItem value="all">All Posts</MenuItem>
              <MenuItem value="published">Published</MenuItem>
              <MenuItem value="draft">Draft</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {error && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
          <Typography>{error}</Typography>
        </Paper>
      )}

      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="news posts table">
          <TableHead>
            <TableRow>
              <TableCell>Title</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Author</TableCell>
              <TableCell>Published</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Views</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : posts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No posts found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              posts.map((post) => (
                <TableRow
                  key={post._id}
                  sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                >
                  <TableCell component="th" scope="row">
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {post.title}
                      {post.featured && (
                        <StarIcon color="warning" fontSize="small" sx={{ ml: 1 }} />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {post.category ? (
                      <Chip 
                        label={typeof post.category === 'object' ? post.category.name : getCategoryName(post.category)} 
                        size="small" 
                        color="primary" 
                        variant="outlined"
                      />
                    ) : (
                      'None'
                    )}
                  </TableCell>
                  <TableCell>
                    {post.author ? (
                      typeof post.author === 'object' ? post.author.name : 'Unknown'
                    ) : (
                      'Unknown'
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={post.published ? 'Published' : 'Draft'} 
                      color={post.published ? 'success' : 'default'} 
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{formatDate(post.createdAt)}</TableCell>
                  <TableCell>{post.viewCount || 0}</TableCell>
                  <TableCell align="right">
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Tooltip title="View post">
                        <IconButton onClick={() => handleViewPost(post._id)} size="small">
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title={post.published ? "Unpublish" : "Publish"}>
                        <IconButton 
                          onClick={() => handleTogglePublished(post._id, post.published)} 
                          size="small"
                          color={post.published ? "success" : "default"}
                        >
                          {post.published ? (
                            <VisibilityIcon fontSize="small" />
                          ) : (
                            <VisibilityOffIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title={post.featured ? "Remove from featured" : "Mark as featured"}>
                        <IconButton 
                          onClick={() => handleToggleFeatured(post._id, post.featured)} 
                          size="small"
                          color={post.featured ? "warning" : "default"}
                        >
                          {post.featured ? (
                            <StarIcon fontSize="small" />
                          ) : (
                            <StarBorderIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Edit post">
                        <IconButton onClick={() => handleEditPost(post._id)} size="small" color="primary">
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      <Tooltip title="Delete post">
                        <IconButton onClick={() => handleOpenDeleteDialog(post)} size="small" color="error">
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalPosts}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          Delete News Post
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-dialog-description">
            Are you sure you want to delete the post "{postToDelete?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeletePost} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NewsPostsTab;