import React, { useEffect, useState, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Stack
} from '@mui/material';
import { Refresh as RefreshIcon, CheckCircle, Error as ErrorIcon, Warning as WarningIcon, HelpOutline } from '@mui/icons-material';
import integrationService from '../../services/integrationService';
import axios from 'axios';

const statusChip = (status) => {
  switch (status) {
    case 'active':
      return <Chip icon={<CheckCircle />} label="Active" color="success" size="small" />;
    case 'needs_auth':
      return <Chip icon={<WarningIcon />} label="Needs Authentication" color="warning" size="small" />;
    case 'not_configured':
      return <Chip icon={<WarningIcon />} label="Not Configured" color="warning" size="small" />;
    case 'error':
      return <Chip icon={<ErrorIcon />} label="Error" color="error" size="small" />;
    default:
      return <Chip icon={<HelpOutline />} label={status || 'Unknown'} color="default" size="small" />;
  }
};

const fmtDate = (value) => {
  if (!value || value === 'N/A') return 'N/A';
  try {
    const d = new Date(value);
    if (isNaN(d)) return 'N/A';
    return d.toLocaleString();
  } catch (e) {
    return 'N/A';
  }
};

export default function SystemStatusPage() {
  const [rows, setRows] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [success, setSuccess] = useState(null);

  const load = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const list = await integrationService.getIntegrationStatus();
      // Ensure deterministic ordering
      list.sort((a, b) => (a.integration || '').localeCompare(b.integration || ''));
      setRows(list);
    } catch (e) {
      console.error('Failed to load integration statuses', e);
      setError('Failed to load integration statuses');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    load();
  }, [load]);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await axios.post('/api/integration-status/refresh');
      await load();
      setSuccess('Status refreshed');
      setTimeout(() => setSuccess(null), 2000);
    } catch (e) {
      console.error('Failed to refresh status file', e);
      setError('Failed to refresh status file');
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
          <Typography variant="h4" component="h1">System Status</Typography>
          <Button variant="contained" startIcon={<RefreshIcon />} onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </Stack>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>
        )}

        <Paper>
          {loading ? (
            <Box sx={{ p: 3, display: 'flex', justifyContent: 'center' }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Integration</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Last Checked</TableCell>
                    <TableCell>Last Updated</TableCell>
                    <TableCell>Notes</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rows.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5}>
                        <Box sx={{ p: 3, textAlign: 'center', color: 'text.secondary' }}>
                          No integration status found.
                        </Box>
                      </TableCell>
                    </TableRow>
                  ) : rows.map((row) => (
                    <TableRow key={row.integration}>
                      <TableCell sx={{ fontWeight: 500 }}>{row.integration}</TableCell>
                      <TableCell>{statusChip(row.status)}</TableCell>
                      <TableCell>{fmtDate(row.lastChecked)}</TableCell>
                      <TableCell>{fmtDate(row.lastUpdated)}</TableCell>
                      <TableCell sx={{ maxWidth: 480, whiteSpace: 'pre-wrap' }}>{row.notes || ''}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Paper>
      </Box>
    </Container>
  );
}
