import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { 
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Business as BusinessIcon,
  Layers as LayersIcon,
  Map as MapIcon,
  LocationOn as LocationOnIcon,
  MeetingRoom as MeetingRoomIcon,
  People as PeopleIcon,
  EventAvailable as EventAvailableIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import buildingManagementService from '../../services/buildingManagementService';
import roomSchedulingService from '../../services/roomSchedulingService';
import BuildingForm from './components/BuildingForm';
import FloorForm from './components/FloorForm';
import FloorplanEditor from './components/FloorplanEditor';

const BuildingManagementAdminPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(0);
  const [buildings, setBuildings] = useState([]);
  const [floors, setFloors] = useState([]);
  const [rooms, setRooms] = useState([]);
  const [selectedBuilding, setSelectedBuilding] = useState(null);
  const [selectedFloor, setSelectedFloor] = useState(null);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [buildingDialogOpen, setBuildingDialogOpen] = useState(false);
  const [floorDialogOpen, setFloorDialogOpen] = useState(false);
  const [roomDialogOpen, setRoomDialogOpen] = useState(false);
  const [floorplanEditorOpen, setFloorplanEditorOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredBuildings, setFilteredBuildings] = useState([]);

  // Check URL for tab parameter when component mounts
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    if (tabParam !== null) {
      const tabValue = parseInt(tabParam, 10);
      if (!isNaN(tabValue) && tabValue >= 0 && tabValue <= 2) {
        setActiveTab(tabValue);
      }
    }
  }, [location.search]);

  // Fetch buildings and floors
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const buildingsData = await buildingManagementService.getBuildings();
        setBuildings(buildingsData);
        setFilteredBuildings(buildingsData);

        if (selectedBuilding) {
          const floorsData = await buildingManagementService.getBuildingFloors(selectedBuilding._id);
          setFloors(floorsData);
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching building management data:', err);
        setError('Failed to load building management data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedBuilding]);

  // Filter buildings based on search term
  useEffect(() => {
    if (!buildings) return;

    const filtered = buildings.filter(building =>
      building.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (building.address?.city && building.address.city.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (building.description && building.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    setFilteredBuildings(filtered);
  }, [buildings, searchTerm]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Update URL with the selected tab
    navigate(`${location.pathname}?tab=${newValue}`, { replace: true });
  };

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Open building dialog for creating/editing
  const handleOpenBuildingDialog = (building = null) => {
    setSelectedBuilding(building);
    setBuildingDialogOpen(true);
  };

  // Close building dialog
  const handleCloseBuildingDialog = () => {
    setBuildingDialogOpen(false);
    setSelectedBuilding(null);
  };

  // Open floor dialog for creating/editing
  const handleOpenFloorDialog = (floor = null) => {
    setSelectedFloor(floor);
    setFloorDialogOpen(true);
  };

  // Close floor dialog
  const handleCloseFloorDialog = () => {
    setFloorDialogOpen(false);
    setSelectedFloor(null);
  };

  // Open floorplan editor
  const handleOpenFloorplanEditor = (floor) => {
    setSelectedFloor(floor);
    setFloorplanEditorOpen(true);
  };

  // Close floorplan editor
  const handleCloseFloorplanEditor = () => {
    setFloorplanEditorOpen(false);
  };

  // Save building
  const handleSaveBuilding = async (buildingData) => {
    try {
      if (selectedBuilding) {
        // Update existing building
        await buildingManagementService.updateBuilding(selectedBuilding._id, buildingData);
      } else {
        // Create new building
        await buildingManagementService.createBuilding(buildingData);
      }

      // Refresh buildings list
      const buildingsData = await buildingManagementService.getBuildings();
      setBuildings(buildingsData);
      setFilteredBuildings(buildingsData);

      handleCloseBuildingDialog();
    } catch (err) {
      console.error('Error saving building:', err);
      setError('Failed to save building. Please try again later.');
    }
  };

  // Delete building
  const handleDeleteBuilding = async (buildingId) => {
    if (window.confirm('Are you sure you want to delete this building? This will also delete all floors and floorplan icons associated with this building.')) {
      try {
        await buildingManagementService.deleteBuilding(buildingId);

        // Refresh buildings list
        const buildingsData = await buildingManagementService.getBuildings();
        setBuildings(buildingsData);
        setFilteredBuildings(buildingsData);

        // If the deleted building was selected, clear selection
        if (selectedBuilding && selectedBuilding._id === buildingId) {
          setSelectedBuilding(null);
          setFloors([]);
        }
      } catch (err) {
        console.error('Error deleting building:', err);
        setError('Failed to delete building. Please try again later.');
      }
    }
  };

  // Save floor
  const handleSaveFloor = async (floorData, floorplanFile) => {
    try {
      if (selectedFloor) {
        // Update existing floor
        await buildingManagementService.updateFloor(selectedFloor._id, floorData, floorplanFile);
      } else {
        // Create new floor
        await buildingManagementService.createFloor(floorData, floorplanFile);
      }

      // Refresh floors list
      if (selectedBuilding) {
        const floorsData = await buildingManagementService.getBuildingFloors(selectedBuilding._id);
        setFloors(floorsData);
      }

      handleCloseFloorDialog();
    } catch (err) {
      console.error('Error saving floor:', err);
      setError('Failed to save floor. Please try again later.');
    }
  };

  // Delete floor
  const handleDeleteFloor = async (floorId) => {
    if (window.confirm('Are you sure you want to delete this floor? This will also delete all floorplan icons associated with this floor.')) {
      try {
        await buildingManagementService.deleteFloor(floorId);

        // Refresh floors list
        if (selectedBuilding) {
          const floorsData = await buildingManagementService.getBuildingFloors(selectedBuilding._id);
          setFloors(floorsData);
        }
      } catch (err) {
        console.error('Error deleting floor:', err);
        setError('Failed to delete floor. Please try again later.');
      }
    }
  };

  // Select a building to view its floors
  const handleSelectBuilding = async (building) => {
    setSelectedBuilding(building);
    setSelectedFloor(null);
    setRooms([]);
    setActiveTab(1); // Switch to Floors tab

    try {
      const floorsData = await buildingManagementService.getBuildingFloors(building._id);
      setFloors(floorsData);
    } catch (err) {
      console.error('Error fetching floors:', err);
      setError('Failed to load floors. Please try again later.');
    }
  };

  // Select a floor to view its rooms
  const handleSelectFloor = async (floor) => {
    setSelectedFloor(floor);
    setActiveTab(2); // Switch to Rooms tab

    try {
      setLoading(true);
      const roomsData = await roomSchedulingService.getAllRooms({ floorId: floor._id });
      setRooms(roomsData);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching rooms:', err);
      setError('Failed to load rooms. Please try again later.');
      setLoading(false);
    }
  };

  // Open room dialog for creating/editing
  const handleOpenRoomDialog = (room = null) => {
    setSelectedRoom(room);
    setRoomDialogOpen(true);
    setError(null); // Clear any previous errors
  };

  // Close room dialog
  const handleCloseRoomDialog = () => {
    setRoomDialogOpen(false);
    setSelectedRoom(null);
    setError(null); // Clear any errors when closing
  };

  // Save room
  const handleSaveRoom = async (roomData) => {
    try {
      if (selectedRoom) {
        // Update existing room
        await roomSchedulingService.updateRoom(selectedRoom._id, roomData);
      } else {
        // Create new room
        await roomSchedulingService.createRoom(roomData);
      }

      // Refresh rooms list
      if (selectedFloor) {
        const roomsData = await roomSchedulingService.getAllRooms({ floorId: selectedFloor._id });
        setRooms(roomsData);
      }

      handleCloseRoomDialog();
    } catch (err) {
      console.error('Error saving room:', err);
      setError('Failed to save room. Please try again later.');
    }
  };

  // Delete room
  const handleDeleteRoom = async (roomId) => {
    if (window.confirm('Are you sure you want to delete this room? This will also delete all reservations associated with this room.')) {
      try {
        await roomSchedulingService.deleteRoom(roomId);

        // Refresh rooms list
        if (selectedFloor) {
          const roomsData = await roomSchedulingService.getAllRooms({ floorId: selectedFloor._id });
          setRooms(roomsData);
        }
      } catch (err) {
        console.error('Error deleting room:', err);
        setError('Failed to delete room. Please try again later.');
      }
    }
  };

  // Render buildings tab
  const renderBuildingsTab = () => (
    <Box sx={{ mt: 3 }}>
      {/* Search and Add Building */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search buildings..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenBuildingDialog()}
            >
              Add Building
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Buildings Table */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : filteredBuildings.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>No buildings found. Add a building to get started.</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Address</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredBuildings.map((building) => (
                <TableRow key={building._id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <BusinessIcon sx={{ mr: 1 }} />
                      {building.name}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {building.address ? (
                      <>
                        {building.address.street && `${building.address.street}, `}
                        {building.address.city && `${building.address.city}, `}
                        {building.address.state && `${building.address.state} `}
                        {building.address.zipCode && building.address.zipCode}
                      </>
                    ) : (
                      'No address'
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={building.buildingType || 'office'} 
                      size="small" 
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={building.status || 'active'} 
                      color={building.status === 'active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton 
                      onClick={() => handleSelectBuilding(building)}
                      size="small"
                      title="View Floors"
                    >
                      <LayersIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleOpenBuildingDialog(building)}
                      size="small"
                      title="Edit Building"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleDeleteBuilding(building._id)}
                      size="small"
                      color="error"
                      title="Delete Building"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  // Render floors tab
  const renderFloorsTab = () => (
    <Box sx={{ mt: 3 }}>
      {/* Building Info and Add Floor */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            {selectedBuilding ? (
              <Box>
                <Typography variant="h6">{selectedBuilding.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedBuilding.address ? (
                    <>
                      {selectedBuilding.address.street && `${selectedBuilding.address.street}, `}
                      {selectedBuilding.address.city && `${selectedBuilding.address.city}, `}
                      {selectedBuilding.address.state && `${selectedBuilding.address.state} `}
                      {selectedBuilding.address.zipCode && selectedBuilding.address.zipCode}
                    </>
                  ) : (
                    'No address'
                  )}
                </Typography>
              </Box>
            ) : (
              <Typography>Select a building to manage its floors</Typography>
            )}
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            {selectedBuilding && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenFloorDialog()}
                disabled={!selectedBuilding}
              >
                Add Floor
              </Button>
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* Floors Table */}
      {!selectedBuilding ? (
        <Alert severity="info" sx={{ mb: 3 }}>Please select a building from the Buildings tab to manage its floors.</Alert>
      ) : loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : floors.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>No floors found for this building. Add a floor to get started.</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Level</TableCell>
                <TableCell>Floorplan</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {floors.map((floor) => (
                <TableRow key={floor._id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LayersIcon sx={{ mr: 1 }} />
                      {floor.name}
                    </Box>
                  </TableCell>
                  <TableCell>{floor.level}</TableCell>
                  <TableCell>
                    {floor.floorplan ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <MapIcon sx={{ mr: 1 }} />
                        <Typography variant="body2" noWrap sx={{ maxWidth: 150 }}>
                          {floor.floorplan.originalFilename || floor.floorplan.filename}
                        </Typography>
                      </Box>
                    ) : (
                      'No floorplan'
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={floor.status || 'active'} 
                      color={floor.status === 'active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton 
                      onClick={() => handleSelectFloor(floor)}
                      size="small"
                      title="View Rooms"
                    >
                      <MeetingRoomIcon />
                    </IconButton>
                    {floor.floorplan && (
                      <IconButton 
                        onClick={() => handleOpenFloorplanEditor(floor)}
                        size="small"
                        title="Edit Floorplan Icons"
                      >
                        <LocationOnIcon />
                      </IconButton>
                    )}
                    <IconButton 
                      onClick={() => handleOpenFloorDialog(floor)}
                      size="small"
                      title="Edit Floor"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleDeleteFloor(floor._id)}
                      size="small"
                      color="error"
                      title="Delete Floor"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  // Render rooms tab
  const renderRoomsTab = () => (
    <Box sx={{ mt: 3 }}>
      {/* Floor Info and Add Room */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            {selectedFloor ? (
              <Box>
                <Typography variant="h6">{selectedFloor.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Level {selectedFloor.level} - {selectedBuilding?.name || 'Building'}
                </Typography>
              </Box>
            ) : (
              <Typography>Select a floor to manage its rooms</Typography>
            )}
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            {selectedFloor && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenRoomDialog()}
                disabled={!selectedFloor || !selectedFloor._id}
              >
                Add Room
              </Button>
            )}
          </Grid>
        </Grid>
      </Paper>

      {/* Rooms Table */}
      {!selectedFloor ? (
        <Alert severity="info" sx={{ mb: 3 }}>Please select a floor from the Floors tab to manage its rooms.</Alert>
      ) : loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : rooms.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>No rooms found for this floor. Add a room to get started.</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Room Number</TableCell>
                <TableCell>Capacity</TableCell>
                <TableCell>Features</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rooms.map((room) => (
                <TableRow key={room._id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <MeetingRoomIcon sx={{ mr: 1 }} />
                      {room.name}
                    </Box>
                  </TableCell>
                  <TableCell>{room.roomNumber}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PeopleIcon sx={{ mr: 1, fontSize: 'small' }} />
                      {room.capacity || 'N/A'}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {room.features && room.features.length > 0 ? (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {room.features.slice(0, 3).map((feature, index) => (
                          <Chip key={index} label={feature} size="small" />
                        ))}
                        {room.features.length > 3 && (
                          <Chip label={`+${room.features.length - 3} more`} size="small" variant="outlined" />
                        )}
                      </Box>
                    ) : (
                      'None'
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={room.status || 'available'} 
                      color={room.status === 'available' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton 
                      onClick={() => handleOpenRoomDialog(room)}
                      size="small"
                      title="Edit Room"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      onClick={() => handleDeleteRoom(room._id)}
                      size="small"
                      color="error"
                      title="Delete Room"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Building Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage buildings, floors, and floorplans
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="building management tabs">
          <Tab label="Buildings" id="tab-0" aria-controls="tabpanel-0" />
          <Tab label="Floors" id="tab-1" aria-controls="tabpanel-1" />
          <Tab label="Rooms" id="tab-2" aria-controls="tabpanel-2" />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <div role="tabpanel" hidden={activeTab !== 0} id="tabpanel-0" aria-labelledby="tab-0">
        {activeTab === 0 && renderBuildingsTab()}
      </div>
      <div role="tabpanel" hidden={activeTab !== 1} id="tabpanel-1" aria-labelledby="tab-1">
        {activeTab === 1 && renderFloorsTab()}
      </div>
      <div role="tabpanel" hidden={activeTab !== 2} id="tabpanel-2" aria-labelledby="tab-2">
        {activeTab === 2 && renderRoomsTab()}
      </div>

      {/* Building Dialog */}
      <Dialog open={buildingDialogOpen} onClose={handleCloseBuildingDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedBuilding ? 'Edit Building' : 'Add Building'}</DialogTitle>
        <DialogContent>
          <BuildingForm 
            building={selectedBuilding} 
            onSave={handleSaveBuilding} 
            onCancel={handleCloseBuildingDialog} 
          />
        </DialogContent>
      </Dialog>

      {/* Floor Dialog */}
      <Dialog open={floorDialogOpen} onClose={handleCloseFloorDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedFloor ? 'Edit Floor' : 'Add Floor'}</DialogTitle>
        <DialogContent>
          <FloorForm 
            floor={selectedFloor} 
            buildingId={selectedBuilding?._id} 
            onSave={handleSaveFloor} 
            onCancel={handleCloseFloorDialog} 
          />
        </DialogContent>
      </Dialog>

      {/* Floorplan Editor Dialog */}
      {selectedFloor && (
        <Dialog open={floorplanEditorOpen} onClose={handleCloseFloorplanEditor} maxWidth="lg" fullWidth>
          <DialogTitle>Edit Floorplan Icons</DialogTitle>
          <DialogContent>
            <FloorplanEditor 
              floor={selectedFloor} 
              onClose={handleCloseFloorplanEditor} 
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Room Dialog */}
      <Dialog open={roomDialogOpen} onClose={handleCloseRoomDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedRoom ? 'Edit Room' : 'Add Room'}</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mt: 2, mb: 2 }} onClose={() => setError(null)}>
              {error}
            </Alert>
          )}
          <Box component="form" sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Room Name"
                  name="name"
                  required
                  defaultValue={selectedRoom?.name || ''}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Room Number"
                  name="roomNumber"
                  required
                  defaultValue={selectedRoom?.roomNumber || ''}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  multiline
                  rows={3}
                  defaultValue={selectedRoom?.description || ''}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Capacity"
                  name="capacity"
                  type="number"
                  InputProps={{ inputProps: { min: 1 } }}
                  defaultValue={selectedRoom?.capacity || ''}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="status-label">Status</InputLabel>
                  <Select
                    labelId="status-label"
                    name="status"
                    defaultValue={selectedRoom?.status || 'available'}
                  >
                    <MenuItem value="available">Available</MenuItem>
                    <MenuItem value="unavailable">Unavailable</MenuItem>
                    <MenuItem value="maintenance">Maintenance</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Features (comma separated)"
                  name="features"
                  helperText="e.g. projector, whiteboard, video conferencing"
                  defaultValue={selectedRoom?.features ? selectedRoom.features.join(', ') : ''}
                  margin="normal"
                />
              </Grid>
              {/* Always include floorId field, whether editing or creating */}
              <Grid item xs={12}>
                <input 
                  type="hidden" 
                  name="floorId" 
                  value={selectedRoom ? selectedRoom.floorId : (selectedFloor?._id || '')} 
                />
                {/* Show warning if no floor is selected for a new room */}
                {!selectedRoom && !selectedFloor?._id && (
                  <Typography color="error" variant="caption">
                    Please select a floor before creating a room.
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseRoomDialog}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={() => {
              const form = document.querySelector('form');
              const formData = new FormData(form);
              // Get floorId based on whether we're editing or creating
              const floorId = selectedRoom ? selectedRoom.floorId : formData.get('floorId');

              // Validate floorId
              if (!floorId) {
                setError('Floor ID is required. Please select a floor first.');
                return;
              }

              const roomData = {
                name: formData.get('name'),
                roomNumber: formData.get('roomNumber'),
                description: formData.get('description'),
                capacity: formData.get('capacity') ? parseInt(formData.get('capacity')) : undefined,
                status: formData.get('status'),
                features: formData.get('features') ? 
                  formData.get('features').split(',').map(feature => feature.trim()).filter(Boolean) : 
                  [],
                floorId: floorId
              };
              handleSaveRoom(roomData);
            }}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BuildingManagementAdminPage;
