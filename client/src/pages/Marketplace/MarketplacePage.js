import React from 'react';
import { 
  Box, 
  Typography, 
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert,
  AlertTitle
} from '@mui/material';
import { 
  Info as InfoIcon,
  Security as SecurityIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

const MarketplacePage = () => {
  const { hasRole } = useAuth();

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Integrations
      </Typography>

      <Alert severity="info" sx={{ mb: 4 }}>
        <AlertTitle>Integration Access Update</AlertTitle>
        <Typography variant="body1">
          The marketplace has been removed. All integrations are now available to users based on their roles and permissions.
        </Typography>
      </Alert>

      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h5" component="h2" gutterBottom>
          How Integration Access Works Now
        </Typography>
        
        <Typography variant="body1" paragraph>
          Access to integrations is now determined by your user roles and permissions. This means:
        </Typography>
        
        <List>
          <ListItem>
            <ListItemIcon>
              <InfoIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="No Activation Required" 
              secondary="You no longer need to activate integrations individually."
            />
          </ListItem>
          
          <Divider component="li" />
          
          <ListItem>
            <ListItemIcon>
              <SecurityIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Role-Based Access" 
              secondary="Your access to specific integrations is determined by your assigned roles."
            />
          </ListItem>
          
          <Divider component="li" />
          
          <ListItem>
            <ListItemIcon>
              <AdminIcon color="primary" />
            </ListItemIcon>
            <ListItemText 
              primary="Permission Management" 
              secondary="Administrators can manage which roles have access to which integrations."
            />
          </ListItem>
        </List>
        
        <Typography variant="body1" paragraph sx={{ mt: 2 }}>
          If you need access to a specific integration that doesn't appear in your navigation menu, 
          please contact your administrator to request the appropriate role or permission.
        </Typography>
      </Paper>

      {hasRole('admin') && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom>
            Administrator Information
          </Typography>
          
          <Typography variant="body1" paragraph>
            As an administrator, you can manage user roles and permissions to control access to integrations.
            Visit the User Management page to assign roles to users.
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default MarketplacePage;
