import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import { 
  Info as InfoIcon,
  Settings as SettingsIcon,
  CheckCircle as HealthIcon,
  Router as DeviceIcon,
  Input as EncoderIcon,
  Output as DecoderIcon,
  Tv as ChannelIcon,
  Save as SaveIcon,
  Refresh as RebootIcon,
  NetworkCheck as NetworkIcon
} from '@mui/icons-material';
import zeeveeService from '../../services/zeeveeService';
import { useAuth } from '../../context/AuthContext';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`zeevee-tabpanel-${index}`}
      aria-labelledby={`zeevee-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ZeeVeePage = () => {
  const { hasPermission } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [deviceInfo, setDeviceInfo] = useState(null);
  const [systemStatus, setSystemStatus] = useState(null);
  const [encoders, setEncoders] = useState([]);
  const [decoders, setDecoders] = useState([]);
  const [channels, setChannels] = useState([]);
  const [selectedEncoder, setSelectedEncoder] = useState(null);
  const [selectedDecoder, setSelectedDecoder] = useState(null);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [networkSettings, setNetworkSettings] = useState(null);
  const [healthStatus, setHealthStatus] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [configForm, setConfigForm] = useState({
    host: '',
    port: 80,
    username: '',
    password: ''
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await zeeveeService.getConfig();
        setConfigStatus(config);
        if (config && config.host) {
          setConfigForm({
            host: config.host,
            port: config.port || 80,
            username: config.username || '',
            password: ''
          });
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus || !configStatus.host) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Device Info
            const deviceInfoData = await zeeveeService.getDeviceInfo();
            setDeviceInfo(deviceInfoData);
            break;
          case 1: // System Status
            const systemStatusData = await zeeveeService.getSystemStatus();
            setSystemStatus(systemStatusData);
            break;
          case 2: // Encoders
            const encodersData = await zeeveeService.getEncoders();
            setEncoders(encodersData);
            break;
          case 3: // Decoders
            const decodersData = await zeeveeService.getDecoders();
            setDecoders(decodersData);
            break;
          case 4: // Channels
            const channelsData = await zeeveeService.getChannels();
            setChannels(channelsData);
            break;
          case 5: // Network
            const networkData = await zeeveeService.getNetworkSettings();
            setNetworkSettings(networkData);
            break;
          case 6: // Health
            const healthData = await zeeveeService.getHealthStatus();
            setHealthStatus(healthData);
            break;
          default:
            break;
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please check your connection and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus]);

  // Handle encoder selection
  const handleEncoderSelect = async (encoderId) => {
    setSelectedEncoder(encoderId);
    setLoading(true);
    try {
      const encoderDetails = await zeeveeService.getEncoderDetails(encoderId);
      // Update the encoder in the list with detailed information
      setEncoders(encoders.map(encoder => 
        encoder.id === encoderId ? { ...encoder, ...encoderDetails } : encoder
      ));
    } catch (err) {
      console.error(`Error fetching details for encoder ${encoderId}:`, err);
      setError(`Failed to load details for encoder ${encoderId}.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle decoder selection
  const handleDecoderSelect = async (decoderId) => {
    setSelectedDecoder(decoderId);
    setLoading(true);
    try {
      const decoderDetails = await zeeveeService.getDecoderDetails(decoderId);
      // Update the decoder in the list with detailed information
      setDecoders(decoders.map(decoder => 
        decoder.id === decoderId ? { ...decoder, ...decoderDetails } : decoder
      ));
    } catch (err) {
      console.error(`Error fetching details for decoder ${decoderId}:`, err);
      setError(`Failed to load details for decoder ${decoderId}.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle channel selection
  const handleChannelSelect = async (channelId) => {
    setSelectedChannel(channelId);
    setLoading(true);
    try {
      const channelDetails = await zeeveeService.getChannelDetails(channelId);
      // Update the channel in the list with detailed information
      setChannels(channels.map(channel => 
        channel.id === channelId ? { ...channel, ...channelDetails } : channel
      ));
    } catch (err) {
      console.error(`Error fetching details for channel ${channelId}:`, err);
      setError(`Failed to load details for channel ${channelId}.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle encoder input change
  const handleEncoderInputChange = async (encoderId, input) => {
    try {
      await zeeveeService.setEncoderInput(encoderId, input);
      // Refresh encoder details
      const encoderDetails = await zeeveeService.getEncoderDetails(encoderId);
      setEncoders(encoders.map(encoder => 
        encoder.id === encoderId ? { ...encoder, ...encoderDetails } : encoder
      ));
    } catch (err) {
      console.error(`Error setting input for encoder ${encoderId}:`, err);
      setError(`Failed to set input for encoder ${encoderId}.`);
    }
  };

  // Handle decoder output change
  const handleDecoderOutputChange = async (decoderId, output) => {
    try {
      await zeeveeService.setDecoderOutput(decoderId, output);
      // Refresh decoder details
      const decoderDetails = await zeeveeService.getDecoderDetails(decoderId);
      setDecoders(decoders.map(decoder => 
        decoder.id === decoderId ? { ...decoder, ...decoderDetails } : decoder
      ));
    } catch (err) {
      console.error(`Error setting output for decoder ${decoderId}:`, err);
      setError(`Failed to set output for decoder ${decoderId}.`);
    }
  };

  // Handle device reboot
  const handleReboot = async () => {
    if (!window.confirm('Are you sure you want to reboot the ZeeVee device?')) return;
    
    setLoading(true);
    try {
      await zeeveeService.rebootDevice();
      setError(null);
      alert('Reboot command sent successfully. The device will restart.');
    } catch (err) {
      console.error('Error rebooting ZeeVee device:', err);
      setError('Failed to reboot the device. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle config form change
  const handleConfigFormChange = (e) => {
    const { name, value } = e.target;
    setConfigForm({
      ...configForm,
      [name]: value
    });
  };

  // Handle config save
  const handleConfigSave = async () => {
    setLoading(true);
    try {
      await zeeveeService.saveConfig(configForm);
      const config = await zeeveeService.getConfig();
      setConfigStatus(config);
      setError(null);
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save configuration. Please check your inputs and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle one-click setup
  const handleOneClickSetup = async () => {
    setLoading(true);
    try {
      await zeeveeService.oneClickSetup();
      const config = await zeeveeService.getConfig();
      setConfigStatus(config);
      setError(null);
    } catch (err) {
      console.error('Error setting up ZeeVee with one click:', err);
      setError('Failed to set up ZeeVee with one click. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Render configuration form
  const renderConfigForm = () => (
    <Box component="form" sx={{ mt: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Host"
            name="host"
            value={configForm.host}
            onChange={handleConfigFormChange}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Port"
            name="port"
            type="number"
            value={configForm.port}
            onChange={handleConfigFormChange}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Username"
            name="username"
            value={configForm.username}
            onChange={handleConfigFormChange}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Password"
            name="password"
            type="password"
            value={configForm.password}
            onChange={handleConfigFormChange}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleConfigSave}
              disabled={loading}
              startIcon={<SaveIcon />}
            >
              Save Configuration
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              onClick={handleOneClickSetup}
              disabled={loading}
            >
              One-Click Setup
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  // Render device information
  const renderDeviceInfo = () => {
    if (!deviceInfo) return <Typography>No device information available.</Typography>;

    return (
      <List>
        {Object.entries(deviceInfo).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render system status
  const renderSystemStatus = () => {
    if (!systemStatus) return <Typography>No system status information available.</Typography>;

    return (
      <List>
        {Object.entries(systemStatus).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
        <ListItem>
          <Button
            variant="contained"
            color="warning"
            startIcon={<RebootIcon />}
            onClick={handleReboot}
            disabled={loading}
          >
            Reboot Device
          </Button>
        </ListItem>
      </List>
    );
  };

  // Render encoders list
  const renderEncoders = () => {
    if (!encoders || encoders.length === 0) return <Typography>No encoders available.</Typography>;

    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <Typography variant="h6" gutterBottom>
            Encoders
          </Typography>
          <List>
            {encoders.map((encoder) => (
              <ListItem 
                key={encoder.id} 
                button 
                onClick={() => handleEncoderSelect(encoder.id)}
                selected={selectedEncoder === encoder.id}
              >
                <ListItemIcon>
                  <EncoderIcon />
                </ListItemIcon>
                <ListItemText
                  primary={encoder.name || `Encoder ${encoder.id}`}
                  secondary={`ID: ${encoder.id}`}
                />
              </ListItem>
            ))}
          </List>
        </Grid>
        <Grid item xs={12} md={8}>
          {selectedEncoder && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Encoder Details
              </Typography>
              {encoders.find(e => e.id === selectedEncoder) && (
                <Card>
                  <CardContent>
                    <Typography variant="h6">
                      {encoders.find(e => e.id === selectedEncoder).name || `Encoder ${selectedEncoder}`}
                    </Typography>
                    <Typography color="textSecondary" gutterBottom>
                      ID: {selectedEncoder}
                    </Typography>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="body1" paragraph>
                      Status: {encoders.find(e => e.id === selectedEncoder).status || 'Unknown'}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Input Source: {encoders.find(e => e.id === selectedEncoder).input || 'Not set'}
                    </Typography>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <InputLabel>Input Source</InputLabel>
                      <Select
                        value={encoders.find(e => e.id === selectedEncoder).input || ''}
                        onChange={(e) => handleEncoderInputChange(selectedEncoder, e.target.value)}
                        label="Input Source"
                      >
                        <MenuItem value="hdmi1">HDMI 1</MenuItem>
                        <MenuItem value="hdmi2">HDMI 2</MenuItem>
                        <MenuItem value="component">Component</MenuItem>
                        <MenuItem value="composite">Composite</MenuItem>
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              )}
            </Box>
          )}
          {!selectedEncoder && (
            <Typography>Select an encoder to view details</Typography>
          )}
        </Grid>
      </Grid>
    );
  };

  // Render decoders list
  const renderDecoders = () => {
    if (!decoders || decoders.length === 0) return <Typography>No decoders available.</Typography>;

    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <Typography variant="h6" gutterBottom>
            Decoders
          </Typography>
          <List>
            {decoders.map((decoder) => (
              <ListItem 
                key={decoder.id} 
                button 
                onClick={() => handleDecoderSelect(decoder.id)}
                selected={selectedDecoder === decoder.id}
              >
                <ListItemIcon>
                  <DecoderIcon />
                </ListItemIcon>
                <ListItemText
                  primary={decoder.name || `Decoder ${decoder.id}`}
                  secondary={`ID: ${decoder.id}`}
                />
              </ListItem>
            ))}
          </List>
        </Grid>
        <Grid item xs={12} md={8}>
          {selectedDecoder && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Decoder Details
              </Typography>
              {decoders.find(d => d.id === selectedDecoder) && (
                <Card>
                  <CardContent>
                    <Typography variant="h6">
                      {decoders.find(d => d.id === selectedDecoder).name || `Decoder ${selectedDecoder}`}
                    </Typography>
                    <Typography color="textSecondary" gutterBottom>
                      ID: {selectedDecoder}
                    </Typography>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="body1" paragraph>
                      Status: {decoders.find(d => d.id === selectedDecoder).status || 'Unknown'}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Output Destination: {decoders.find(d => d.id === selectedDecoder).output || 'Not set'}
                    </Typography>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <InputLabel>Output Destination</InputLabel>
                      <Select
                        value={decoders.find(d => d.id === selectedDecoder).output || ''}
                        onChange={(e) => handleDecoderOutputChange(selectedDecoder, e.target.value)}
                        label="Output Destination"
                      >
                        {channels.map(channel => (
                          <MenuItem key={channel.id} value={channel.id}>
                            {channel.name || `Channel ${channel.id}`}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </CardContent>
                </Card>
              )}
            </Box>
          )}
          {!selectedDecoder && (
            <Typography>Select a decoder to view details</Typography>
          )}
        </Grid>
      </Grid>
    );
  };

  // Render channels list
  const renderChannels = () => {
    if (!channels || channels.length === 0) return <Typography>No channels available.</Typography>;

    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <Typography variant="h6" gutterBottom>
            Channels
          </Typography>
          <List>
            {channels.map((channel) => (
              <ListItem 
                key={channel.id} 
                button 
                onClick={() => handleChannelSelect(channel.id)}
                selected={selectedChannel === channel.id}
              >
                <ListItemIcon>
                  <ChannelIcon />
                </ListItemIcon>
                <ListItemText
                  primary={channel.name || `Channel ${channel.id}`}
                  secondary={`ID: ${channel.id}`}
                />
              </ListItem>
            ))}
          </List>
        </Grid>
        <Grid item xs={12} md={8}>
          {selectedChannel && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Channel Details
              </Typography>
              {channels.find(c => c.id === selectedChannel) && (
                <Card>
                  <CardContent>
                    <Typography variant="h6">
                      {channels.find(c => c.id === selectedChannel).name || `Channel ${selectedChannel}`}
                    </Typography>
                    <Typography color="textSecondary" gutterBottom>
                      ID: {selectedChannel}
                    </Typography>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="body1" paragraph>
                      Status: {channels.find(c => c.id === selectedChannel).status || 'Unknown'}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Source: {channels.find(c => c.id === selectedChannel).source || 'Not set'}
                    </Typography>
                    <Typography variant="body1" paragraph>
                      Destination: {channels.find(c => c.id === selectedChannel).destination || 'Not set'}
                    </Typography>
                  </CardContent>
                </Card>
              )}
            </Box>
          )}
          {!selectedChannel && (
            <Typography>Select a channel to view details</Typography>
          )}
        </Grid>
      </Grid>
    );
  };

  // Render network settings
  const renderNetworkSettings = () => {
    if (!networkSettings) return <Typography>No network settings available.</Typography>;

    return (
      <List>
        {Object.entries(networkSettings).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <NetworkIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render health status
  const renderHealthStatus = () => {
    if (!healthStatus) return <Typography>No health status information available.</Typography>;

    return (
      <List>
        {Object.entries(healthStatus).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <HealthIcon color={value === 'OK' ? 'success' : 'error'} />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={value}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // If configuration is not set up, show configuration form
  if (!loadingConfig && (!configStatus || !configStatus.host)) {
    return (
      <Container maxWidth="lg">
        <Typography variant="h4" component="h1" gutterBottom>
          ZeeVee HDbridge
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom>
            Configuration Required
          </Typography>
          <Typography paragraph>
            Please configure your ZeeVee HDbridge connection settings below.
          </Typography>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {renderConfigForm()}
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        ZeeVee HDbridge
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Device Info" icon={<DeviceIcon />} />
          <Tab label="System Status" icon={<InfoIcon />} />
          <Tab label="Encoders" icon={<EncoderIcon />} />
          <Tab label="Decoders" icon={<DecoderIcon />} />
          <Tab label="Channels" icon={<ChannelIcon />} />
          <Tab label="Network" icon={<NetworkIcon />} />
          <Tab label="Health" icon={<HealthIcon />} />
          <Tab label="Settings" icon={<SettingsIcon />} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderDeviceInfo()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderSystemStatus()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderEncoders()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderDecoders()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderChannels()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderNetworkSettings()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={6}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderHealthStatus()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={7}>
          <Typography variant="h6" gutterBottom>
            ZeeVee HDbridge Configuration
          </Typography>
          {renderConfigForm()}
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default ZeeVeePage;