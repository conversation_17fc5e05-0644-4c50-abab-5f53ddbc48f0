import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Link
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import unifiAccessService from '../../services/unifiAccessService';
import { useAuth } from '../../context/AuthContext';

const UnifiAccessPage = () => {
  const { hasIntegration } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [doors, setDoors] = useState([]);
  const [groups, setGroups] = useState([]);
  const [accessLevels, setAccessLevels] = useState([]);
  const [doorStatuses, setDoorStatuses] = useState({});

  // Fetch configuration status and doors on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await unifiAccessService.getConfig();
        setConfigStatus(config);

        // If configured, get doors, groups, and access levels
        if (config) {
          const doorList = await unifiAccessService.getDoors();
          setDoors(doorList);
          try {
            const [g, al] = await Promise.all([
              unifiAccessService.getGroups(),
              unifiAccessService.getAccessLevels()
            ]);
            setGroups(Array.isArray(g) ? g : []);
            setAccessLevels(Array.isArray(al) ? al : []);
          } catch (innerErr) {
            console.warn('Warning: Failed to load groups or access levels:', innerErr?.message || innerErr);
          }
        }
      } catch (err) {
        console.error('Error fetching UniFi Access data:', err);
        setError('Failed to load UniFi Access data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleUnlock = async (doorId) => {
    try {
      await unifiAccessService.controlDoor(doorId, { action: 'unlock' });
    } catch (e) {
      console.error('Failed to unlock door', e);
      setError('Failed to unlock door.');
    }
  };

  const handleCheckStatus = async (doorId) => {
    try {
      const status = await unifiAccessService.getDoorStatus(doorId);
      setDoorStatuses(prev => ({ ...prev, [doorId]: status }));
    } catch (e) {
      console.error('Failed to fetch door status', e);
      setError('Failed to fetch door status.');
    }
  };

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            UniFi Access
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              UniFi Access integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              To use the UniFi Access integration, an administrator needs to configure it first.
            </Typography>
            <Typography variant="body1" paragraph>
              Please contact your system administrator to set up the required environment variables.
            </Typography>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          UniFi Access Control
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Doors
          </Typography>

          {doors.length > 0 ? (
            <List>
              {doors.map((door, index) => (
                <React.Fragment key={door.id}>
                  <ListItem>
                    <ListItemText 
                      primary={door.name || door.full_name} 
                      secondary={(() => {
                        const s = doorStatuses[door.id];
                        const base = `Lock: ${door.door_lock_relay_status || 'unknown'} | Position: ${door.door_position_status || 'unknown'}${door.full_name ? ' | Location: ' + door.full_name : ''}`;
                        if (!s) return base;
                        const extra = ` | Live: ${s.status || (s.locked ? 'locked' : 'unknown')}${typeof s.open === 'boolean' ? `, open: ${s.open}` : ''}`;
                        return base + extra;
                      })()} 
                    />
                    <Button 
                      variant="contained" 
                      color="primary"
                      size="small"
                      onClick={() => handleUnlock(door.id)}
                      sx={{ mr: 1 }}
                    >
                      Unlock
                    </Button>
                    <Button 
                      variant="outlined" 
                      size="small"
                      onClick={() => handleCheckStatus(door.id)}
                    >
                      Check Status
                    </Button>
                  </ListItem>
                  {index < doors.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body1">
              No doors found.
            </Typography>
          )}
        </Paper>

        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Groups
          </Typography>
          {groups.length > 0 ? (
            <List>
              {groups.map((g, idx) => (
                <React.Fragment key={g.id || g._id || g.uuid || idx}>
                  <ListItem>
                    <ListItemText
                      primary={g.name || g.display_name || g.full_name || 'Unnamed Group'}
                      secondary={g.id || g._id || g.uuid || ''}
                    />
                  </ListItem>
                  {idx < groups.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body1">No groups found.</Typography>
          )}
        </Paper>

        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Access Levels
          </Typography>
          {accessLevels.length > 0 ? (
            <List>
              {accessLevels.map((al, idx) => (
                <React.Fragment key={al.id || idx}>
                  <ListItem>
                    <ListItemText
                      primary={al.name || 'Unnamed Access Level'}
                      secondary={al.type === 'policy' ? 'Policy' : (al.type || '')}
                    />
                  </ListItem>
                  {idx < accessLevels.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body1">No access levels found.</Typography>
          )}
        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Typography variant="caption" color="textSecondary">
            Configuration is managed through environment variables
          </Typography>
        </Box>
      </Box>
    </Container>
  );
};

export default UnifiAccessPage;
