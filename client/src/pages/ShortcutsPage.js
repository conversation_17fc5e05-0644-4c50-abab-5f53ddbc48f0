import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardActions,
  Button,
  TextField,
  InputAdornment,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox
} from '@mui/material';
import { 
  Search as SearchIcon,
  Link as LinkIcon,
  Info as InfoIcon,
  Launch as LaunchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const ShortcutsPage = () => {
  const { user } = useAuth();
  const [shortcuts, setShortcuts] = useState([]);
  const [filteredShortcuts, setFilteredShortcuts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedShortcut, setSelectedShortcut] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    url: '',
    description: '',
    categories: [],
    instructions: ''
  });

  // Fetch shortcuts and categories
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch shortcuts
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        setFilteredShortcuts(shortcutsRes.data);

        // Fetch categories from the dedicated ShortcutCategory model
        const categoriesRes = await axios.get('/api/shortcut-categories');
        // Extract category names from the response
        const categoryNames = categoriesRes.data.map(category => category.name);
        setCategories(['All', ...categoryNames]);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load shortcuts');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter shortcuts based on category and search term
  useEffect(() => {
    let filtered = shortcuts;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(shortcut => 
        shortcut.categories.includes(selectedCategory)
      );
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(shortcut => 
        shortcut.title.toLowerCase().includes(term) || 
        (shortcut.description && shortcut.description.toLowerCase().includes(term))
      );
    }

    setFilteredShortcuts(filtered);
  }, [selectedCategory, searchTerm, shortcuts]);

  // Handle category selection
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  // Handle search input
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Track shortcut clicks
  const handleShortcutClick = async (id) => {
    try {
      await axios.post(`/api/shortcuts/${id}/click`);
    } catch (err) {
      console.error('Error tracking shortcut click:', err);
    }
  };

  // Open instructions dialog
  const handleOpenDialog = (shortcut) => {
    setSelectedShortcut(shortcut);
    setDialogOpen(true);
  };

  // Close instructions dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Open edit dialog for new shortcut
  const handleAddShortcut = () => {
    setSelectedShortcut(null);
    setFormData({
      title: '',
      url: '',
      description: '',
      categories: [],
      instructions: ''
    });
    setEditDialogOpen(true);
  };

  // Open edit dialog for existing shortcut
  const handleEditShortcut = (shortcut) => {
    setSelectedShortcut(shortcut);
    setFormData({
      title: shortcut.title,
      url: shortcut.url,
      description: shortcut.description || '',
      categories: shortcut.categories || [],
      instructions: shortcut.instructions || ''
    });
    setEditDialogOpen(true);
  };

  // Close edit dialog
  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setSelectedShortcut(null);
  };

  // Handle form input changes
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle category selection
  const handleFormCategoryChange = (event) => {
    setFormData(prev => ({
      ...prev,
      categories: event.target.value
    }));
  };

  // Save shortcut changes
  const handleSaveShortcut = async () => {
    try {
      if (selectedShortcut && !selectedShortcut.isGlobal) {
        // Update existing private shortcut
        await axios.put(`/api/shortcuts/private/${selectedShortcut._id}`, formData);
      } else {
        // Create new private shortcut
        await axios.post('/api/shortcuts/private', formData);
      }

      // Refresh shortcuts list
      const shortcutsRes = await axios.get('/api/shortcuts');
      setShortcuts(shortcutsRes.data);
      setFilteredShortcuts(shortcutsRes.data);

      handleCloseEditDialog();
    } catch (err) {
      console.error('Error saving shortcut:', err);
      setError('Failed to save shortcut');
    }
  };

  // Delete shortcut
  const handleDeleteShortcut = async (shortcutId) => {
    if (window.confirm('Are you sure you want to delete this shortcut?')) {
      try {
        await axios.delete(`/api/shortcuts/private/${shortcutId}`);

        // Refresh shortcuts list
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        setFilteredShortcuts(shortcutsRes.data);
      } catch (err) {
        console.error('Error deleting shortcut:', err);
        setError('Failed to delete shortcut');
      }
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Shortcuts
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quick access to important resources and services
        </Typography>
      </Box>

      {/* Search and filter */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search shortcuts..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {categories.map((category) => (
                  <Chip
                    key={category}
                    label={category}
                    onClick={() => handleCategoryChange(category)}
                    color={selectedCategory === category ? 'primary' : 'default'}
                    variant={selectedCategory === category ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
              {user && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AddIcon />}
                  onClick={handleAddShortcut}
                  sx={{ ml: 2 }}
                >
                  Add Shortcut
                </Button>
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Shortcuts grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : filteredShortcuts.length === 0 ? (
        <Alert severity="info">
          No shortcuts found. Try changing your search or filter.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {filteredShortcuts.map((shortcut) => (
            <Grid item xs={12} sm={6} md={4} key={shortcut._id}>
              <Card className="shortcut-card">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ mr: 1 }}>
                      <LinkIcon color="primary" />
                    </Box>
                    <Typography variant="h6" component="h2" sx={{ flexGrow: 1 }}>
                      {shortcut.title}
                    </Typography>
                    {shortcut.instructions && (
                      <Tooltip title="View Instructions">
                        <IconButton 
                          size="small" 
                          onClick={() => handleOpenDialog(shortcut)}
                        >
                          <InfoIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                    {user && !shortcut.isGlobal && shortcut.createdBy === user._id && (
                      <>
                        <Tooltip title="Edit Shortcut">
                          <IconButton 
                            size="small" 
                            onClick={() => handleEditShortcut(shortcut)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Shortcut">
                          <IconButton 
                            size="small"
                            color="error"
                            onClick={() => handleDeleteShortcut(shortcut._id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </>
                    )}
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {shortcut.description}
                  </Typography>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
                    {shortcut.categories.map((category) => (
                      <Chip 
                        key={category} 
                        label={category} 
                        size="small" 
                        variant="outlined"
                        onClick={() => handleCategoryChange(category)}
                      />
                    ))}
                    {!shortcut.isGlobal && (
                      <Chip 
                        label="Private" 
                        size="small" 
                        color="secondary"
                      />
                    )}
                  </Box>
                </CardContent>
                <Divider />
                <CardActions>
                  <Button 
                    fullWidth
                    variant="contained"
                    color="primary"
                    startIcon={<LaunchIcon />}
                    component="a"
                    href={shortcut.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={() => handleShortcutClick(shortcut._id)}
                  >
                    Open
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Instructions Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        aria-labelledby="instructions-dialog-title"
      >
        {selectedShortcut && (
          <>
            <DialogTitle id="instructions-dialog-title">
              {selectedShortcut.title} - Instructions
            </DialogTitle>
            <DialogContent>
              <DialogContentText>
                {selectedShortcut.instructions || 'No instructions provided.'}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>Close</Button>
              <Button 
                variant="contained" 
                color="primary"
                component="a"
                href={selectedShortcut.url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => {
                  handleShortcutClick(selectedShortcut._id);
                  handleCloseDialog();
                }}
              >
                Open Link
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Edit Shortcut Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseEditDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedShortcut ? 'Edit Shortcut' : 'Add Shortcut'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="URL"
              name="url"
              value={formData.url}
              onChange={handleInputChange}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              margin="normal"
              multiline
              rows={2}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Categories</InputLabel>
              <Select
                multiple
                value={formData.categories}
                onChange={handleFormCategoryChange}
                renderValue={(selected) => selected.join(', ')}
              >
                {categories.filter(cat => cat !== 'All').map((category) => (
                  <MenuItem key={category} value={category}>
                    <Checkbox checked={formData.categories.indexOf(category) > -1} />
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Instructions"
              name="instructions"
              value={formData.instructions}
              onChange={handleInputChange}
              margin="normal"
              multiline
              rows={4}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Cancel</Button>
          <Button onClick={handleSaveShortcut} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ShortcutsPage;
