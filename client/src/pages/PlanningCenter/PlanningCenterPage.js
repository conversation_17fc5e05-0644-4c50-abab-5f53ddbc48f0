import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar,
  TextField,
  InputAdornment,
  Pagination
} from '@mui/material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import SettingsIcon from '@mui/icons-material/Settings';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import EventIcon from '@mui/icons-material/Event';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PersonIcon from '@mui/icons-material/Person';
import HomeIcon from '@mui/icons-material/Home';
import WorkIcon from '@mui/icons-material/Work';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CategoryIcon from '@mui/icons-material/Category';
import SearchIcon from '@mui/icons-material/Search';
import planningCenterService from '../../services/planningCenterService';
import { useAuth } from '../../context/AuthContext';

// Define tab paths for URL navigation
const TAB_PATHS = {
  0: 'events',
  1: 'people',
  2: 'resources'
};

// Map URL paths back to tab indices
const PATH_TO_TAB = {
  'events': 0,
  'people': 1,
  'resources': 2
};

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`planning-center-tabpanel-${index}`}
      aria-labelledby={`planning-center-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const PlanningCenterPage = () => {
  const { hasIntegration } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [events, setEvents] = useState({ data: [] });
  const [people, setPeople] = useState({ data: [] });
  const [resources, setResources] = useState({ data: [] });
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  // State for Planning Center people pagination
  // This manages the pagination state for Planning Center people
  // - currentPage: The current page being displayed
  // - totalPages: Total number of pages available
  // - totalCount: Total number of people in Planning Center
  // - perPage: Number of items to display per page
  const [peoplePagination, setPeoplePagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    perPage: 25
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    // Update URL when tab changes
    navigate(`/planning-center/${TAB_PATHS[newValue]}`);
  };
  
  /**
   * Handle search input changes
   * Updates the search term state and resets pagination to first page
   * This triggers a new fetch of Planning Center people with the search term
   * via the useEffect dependency on searchTerm
   */
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
    
    // Reset to first page when search term changes
    if (tabValue === 1) { // Only for People tab
      setPeoplePagination(prev => ({
        ...prev,
        currentPage: 1
      }));
    }
  };
  
  /**
   * Handle Planning Center people pagination change
   * Updates the current page in the pagination state
   * This triggers a new fetch of Planning Center people for the selected page
   * via the useEffect dependency on peoplePagination.currentPage
   * 
   * @param {Object} event - The event object (not used)
   * @param {number} page - The new page number to display
   */
  const handlePageChange = (event, page) => {
    setPeoplePagination(prev => ({
      ...prev,
      currentPage: page
    }));
  };

  // Set active tab based on URL path on component mount
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const tabPath = pathParts[pathParts.length - 1];
    
    // If the URL has a valid tab path, set the active tab
    if (tabPath && PATH_TO_TAB.hasOwnProperty(tabPath)) {
      setTabValue(PATH_TO_TAB[tabPath]);
    } else if (pathParts.length <= 2) {
      // If we're at the root planning-center path with no tab specified, navigate to the default tab
      navigate(`/planning-center/${TAB_PATHS[0]}`, { replace: true });
    }
  }, [location.pathname, navigate]);

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await planningCenterService.getConfig();
        setConfigStatus(config);
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Events
            if (!events.data || events.data.length === 0) {
              const eventsData = await planningCenterService.getEvents();
              setEvents(eventsData);
            }
            break;
          case 1: // People
            // Use the new getPeopleDirectory method with pagination and search
            const peopleResponse = await planningCenterService.getPeopleDirectory({
              page: peoplePagination.currentPage,
              per_page: peoplePagination.perPage,
              search: searchTerm
            });
            
            // Extract people and pagination data from response
            const { people: pcPeople, pagination, meta } = peopleResponse;
            
            // Update people state
            setPeople({ data: pcPeople || [] });
            
            // Update pagination state with values from the response
            setPeoplePagination({
              currentPage: pagination?.currentPage || meta?.current_page || 1,
              totalPages: pagination?.totalPages || meta?.total_pages || 1,
              totalCount: pagination?.totalCount || meta?.total_count || 0,
              perPage: peoplePagination.perPage
            });
            break;
          case 2: // Resources
            if (!resources.data || resources.data.length === 0) {
              const resourcesData = await planningCenterService.getResources();
              setResources(resourcesData);
            }
            break;
          default:
            break;
        }
      } catch (err) {
        setError('Failed to load data from Planning Center. Please check your connection and try again.');
        console.error('Error loading Planning Center data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, events.data, resources.data, configStatus, 
     // Add dependencies for People tab pagination and search
     tabValue === 1 && peoplePagination.currentPage,
     tabValue === 1 && peoplePagination.perPage,
     tabValue === 1 && searchTerm
  ]);

  // All integrations are now assumed to be active for all users
  // Access is controlled by roles and permissions instead

  if (loadingConfig) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!configStatus) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Planning Center
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              Planning Center integration is not configured yet. You need to set up your API credentials to use this integration.
            </Alert>
            <Typography variant="body1" paragraph>
              To integrate with Planning Center, you need to:
            </Typography>
            <ol>
              <li>
                <Typography variant="body1" paragraph>
                  Log in to your Planning Center account
                </Typography>
              </li>
              <li>
                <Typography variant="body1" paragraph>
                  Go to Developer API Access in your account settings
                </Typography>
              </li>
              <li>
                <Typography variant="body1" paragraph>
                  Create a new application or use an existing one
                </Typography>
              </li>
              <li>
                <Typography variant="body1" paragraph>
                  Configure the OAuth settings with the correct callback URL
                </Typography>
              </li>
              <li>
                <Typography variant="body1" paragraph>
                  Enter your Application ID and Secret in our setup page
                </Typography>
              </li>
            </ol>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Planning Center
          </Typography>
        </Box>

        <Paper sx={{ width: '100%', mb: 2 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            centered
          >
            <Tab label="Events" />
            <Tab label="People" />
            <Tab label="Resources" />
          </Tabs>

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TabPanel value={tabValue} index={0}>
                <Typography variant="h6" gutterBottom>Calendar Events</Typography>
                {events.data && events.data.length > 0 ? (
                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    {events.data.map((event) => {
                      const attributes = event.attributes || {};
                      
                      // Format dates
                      const startDate = attributes.starts_at ? new Date(attributes.starts_at) : null;
                      const endDate = attributes.ends_at ? new Date(attributes.ends_at) : null;
                      
                      const formatDate = (date) => {
                        if (!date) return 'N/A';
                        return date.toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        });
                      };
                      
                      const formatTime = (date) => {
                        if (!date) return '';
                        return date.toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit'
                        });
                      };
                      
                      return (
                        <Grid item xs={12} md={6} key={event.id}>
                          <Card variant="outlined">
                            <CardContent>
                              <Typography variant="h6" component="div" gutterBottom>
                                {attributes.name || 'Untitled Event'}
                              </Typography>
                              
                              <List dense>
                                <ListItem>
                                  <ListItemIcon>
                                    <CalendarTodayIcon />
                                  </ListItemIcon>
                                  <ListItemText 
                                    primary="Date" 
                                    secondary={formatDate(startDate)}
                                  />
                                </ListItem>
                                
                                <ListItem>
                                  <ListItemIcon>
                                    <AccessTimeIcon />
                                  </ListItemIcon>
                                  <ListItemText 
                                    primary="Time" 
                                    secondary={`${formatTime(startDate)} ${endDate ? '- ' + formatTime(endDate) : ''}`}
                                  />
                                </ListItem>
                                
                                {attributes.location && (
                                  <ListItem>
                                    <ListItemIcon>
                                      <LocationOnIcon />
                                    </ListItemIcon>
                                    <ListItemText 
                                      primary="Location" 
                                      secondary={attributes.location}
                                    />
                                  </ListItem>
                                )}
                              </List>
                              
                              {attributes.description && (
                                <>
                                  <Divider sx={{ my: 1 }} />
                                  <Typography variant="body2" color="text.secondary">
                                    {attributes.description}
                                  </Typography>
                                </>
                              )}
                              
                              {/* Display tags if available */}
                              {event.linked_data && event.linked_data.tags && event.linked_data.tags.length > 0 && (
                                <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                  {event.linked_data.tags.map(tag => (
                                    <Chip 
                                      key={tag.id}
                                      label={tag.attributes?.name || 'Tag'}
                                      size="small"
                                      sx={{ 
                                        backgroundColor: tag.attributes?.color || '#e0e0e0',
                                        color: '#fff'
                                      }}
                                    />
                                  ))}
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })}
                  </Grid>
                ) : (
                  <Typography>No events found</Typography>
                )}
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>People Directory</Typography>
                  
                  {/* Search input for People tab */}
                  <TextField
                    fullWidth
                    placeholder="Search people..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    sx={{ mb: 2 }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Box>
                
                {people.data && people.data.length > 0 ? (
                  <>
                    <TableContainer component={Paper} sx={{ mt: 2 }}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Name</TableCell>
                            <TableCell>Email</TableCell>
                            <TableCell>Phone</TableCell>
                            <TableCell>Details</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {people.data.map((person) => {
                            const attributes = person.attributes || {};
                            const contactDetails = person.contactDetails || {};
                            
                            return (
                              <TableRow key={person.id}>
                                <TableCell>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Avatar sx={{ mr: 1 }}>
                                      {attributes.first_name ? attributes.first_name.charAt(0) : <PersonIcon />}
                                    </Avatar>
                                    <Typography>
                                      {`${attributes.first_name || ''} ${attributes.last_name || ''}`.trim() || 'N/A'}
                                    </Typography>
                                  </Box>
                                </TableCell>
                                <TableCell>
                                  {contactDetails.emails && contactDetails.emails.length > 0 ? (
                                    <Box>
                                      {contactDetails.emails.map((email, index) => (
                                        <Typography key={email.id || index} variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                          <EmailIcon fontSize="small" sx={{ mr: 0.5 }} />
                                          {email.address}
                                          {email.primary && (
                                            <Chip size="small" label="Primary" color="primary" sx={{ ml: 1, height: 16, fontSize: '0.6rem' }} />
                                          )}
                                        </Typography>
                                      ))}
                                    </Box>
                                  ) : (
                                    attributes.email || 'N/A'
                                  )}
                                </TableCell>
                                <TableCell>
                                  {contactDetails.phoneNumbers && contactDetails.phoneNumbers.length > 0 ? (
                                    <Box>
                                      {contactDetails.phoneNumbers.map((phone, index) => (
                                        <Typography key={phone.id || index} variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                                          <PhoneIcon fontSize="small" sx={{ mr: 0.5 }} />
                                          {phone.number}
                                          {phone.primary && (
                                            <Chip size="small" label="Primary" color="primary" sx={{ ml: 1, height: 16, fontSize: '0.6rem' }} />
                                          )}
                                        </Typography>
                                      ))}
                                    </Box>
                                  ) : (
                                    attributes.phone_number || 'N/A'
                                  )}
                                </TableCell>
                                <TableCell>
                                  <Grid container spacing={1}>
                                    {attributes.birthdate && (
                                      <Grid item>
                                        <Chip 
                                          icon={<EventIcon />} 
                                          label={`Birthday: ${new Date(attributes.birthdate).toLocaleDateString()}`} 
                                          size="small" 
                                        />
                                      </Grid>
                                    )}
                                    {attributes.status && (
                                      <Grid item>
                                        <Chip 
                                          label={`Status: ${attributes.status}`} 
                                          size="small" 
                                        />
                                      </Grid>
                                    )}
                                  </Grid>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    
                    {/* Pagination controls */}
                    {peoplePagination.totalPages > 1 && (
                      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 2, mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            Total: {peoplePagination.totalCount} people
                          </Typography>
                        </Box>
                        <Pagination 
                          count={peoplePagination.totalPages} 
                          page={peoplePagination.currentPage}
                          onChange={handlePageChange}
                          color="primary"
                          showFirstButton
                          showLastButton
                        />
                      </Box>
                    )}
                  </>
                ) : (
                  <Typography>No people found</Typography>
                )}
              </TabPanel>

              <TabPanel value={tabValue} index={2}>
                <Typography variant="h6" gutterBottom>Resources</Typography>
                {resources.data && resources.data.length > 0 ? (
                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    {resources.data.map((resource) => {
                      const attributes = resource.attributes || {};
                      
                      return (
                        <Grid item xs={12} md={4} key={resource.id}>
                          <Card variant="outlined" sx={{ height: '100%' }}>
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="h6" component="div">
                                  {attributes.name || 'Unnamed Resource'}
                                </Typography>
                                {attributes.color && (
                                  <Box 
                                    sx={{ 
                                      width: 16, 
                                      height: 16, 
                                      borderRadius: '50%', 
                                      backgroundColor: attributes.color 
                                    }} 
                                  />
                                )}
                              </Box>
                              
                              {attributes.kind && (
                                <Chip 
                                  icon={<CategoryIcon />}
                                  label={attributes.kind}
                                  size="small"
                                  sx={{ mb: 2 }}
                                />
                              )}
                              
                              {attributes.description && (
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                  {attributes.description}
                                </Typography>
                              )}
                              
                              <Divider sx={{ my: 1 }} />
                              
                              <List dense>
                                {attributes.created_at && (
                                  <ListItem>
                                    <ListItemText 
                                      primary="Created" 
                                      secondary={new Date(attributes.created_at).toLocaleDateString()}
                                      primaryTypographyProps={{ variant: 'caption' }}
                                    />
                                  </ListItem>
                                )}
                                
                                {attributes.updated_at && (
                                  <ListItem>
                                    <ListItemText 
                                      primary="Updated" 
                                      secondary={new Date(attributes.updated_at).toLocaleDateString()}
                                      primaryTypographyProps={{ variant: 'caption' }}
                                    />
                                  </ListItem>
                                )}
                              </List>
                            </CardContent>
                          </Card>
                        </Grid>
                      );
                    })}
                  </Grid>
                ) : (
                  <Typography>No resources found</Typography>
                )}
              </TabPanel>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default PlanningCenterPage;
