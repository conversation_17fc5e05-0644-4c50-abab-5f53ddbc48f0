import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Grid,
  Alert,
  CircularProgress,
  Snackbar,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Group as GroupIcon,
  Refresh as RefreshIcon,
  PersonAdd as PersonAddIcon,
  PersonRemove as PersonRemoveIcon
} from '@mui/icons-material';
import googleAdminService from '../../services/googleAdminService';

const GoogleAdminGroupsPage = () => {
  const [groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openAddMemberDialog, setOpenAddMemberDialog] = useState(false);
  const [currentGroup, setCurrentGroup] = useState(null);
  const [userEmail, setUserEmail] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch groups on component mount
  useEffect(() => {
    fetchGroups();
  }, []);

  // Fetch groups from Google Admin
  const fetchGroups = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await googleAdminService.listGroups();
      setGroups(data || []);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching groups:', err);
      setError('Failed to load groups. Please check your Google Admin configuration and authentication.');
      setLoading(false);
    }
  };

  // Handle add member dialog
  const handleOpenAddMemberDialog = (group) => {
    setCurrentGroup(group);
    setUserEmail('');
    setOpenAddMemberDialog(true);
  };

  const handleCloseAddMemberDialog = () => {
    setOpenAddMemberDialog(false);
  };

  const handleAddMember = async () => {
    try {
      setLoading(true);
      await googleAdminService.addUserToGroup(currentGroup.email, userEmail);
      setOpenAddMemberDialog(false);
      setSnackbar({
        open: true,
        message: `User ${userEmail} added to group ${currentGroup.name} successfully`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Error adding user to group:', err);
      setSnackbar({
        open: true,
        message: `Error adding user to group: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle remove member
  const handleRemoveMember = async (groupKey, memberKey) => {
    try {
      setLoading(true);
      await googleAdminService.removeUserFromGroup(groupKey, memberKey);
      setSnackbar({
        open: true,
        message: `User removed from group successfully`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Error removing user from group:', err);
      setSnackbar({
        open: true,
        message: `Error removing user from group: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <GroupIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Google Admin Groups
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchGroups}
              disabled={loading}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {groups.length > 0 ? (
                  groups.map((group) => (
                    <TableRow key={group.id}>
                      <TableCell>{group.name}</TableCell>
                      <TableCell>{group.email}</TableCell>
                      <TableCell>{group.description || '-'}</TableCell>
                      <TableCell>
                        <Button
                          variant="outlined"
                          size="small"
                          startIcon={<PersonAddIcon />}
                          onClick={() => handleOpenAddMemberDialog(group)}
                        >
                          Add Member
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      No groups found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Add Member Dialog */}
      {currentGroup && (
        <Dialog open={openAddMemberDialog} onClose={handleCloseAddMemberDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Add Member to {currentGroup.name}</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  label="User Email"
                  value={userEmail}
                  onChange={(e) => setUserEmail(e.target.value)}
                  fullWidth
                  required
                  helperText="Enter the email address of the user to add to this group"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseAddMemberDialog}>Cancel</Button>
            <Button
              onClick={handleAddMember}
              variant="contained"
              color="primary"
              disabled={!userEmail}
            >
              Add
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default GoogleAdminGroupsPage;