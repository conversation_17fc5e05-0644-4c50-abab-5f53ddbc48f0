import React, { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';
import googleAdminService from '../../services/googleAdminService';

const GoogleAdminPage = () => {
  const [configStatus, setConfigStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        setLoading(true);
        const config = await googleAdminService.getConfig();
        setConfigStatus(config);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching Google Admin configuration:', err);
        setError('Failed to load Google Admin configuration status');
        setLoading(false);
      }
    };

    fetchConfigStatus();
  }, []);

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <AdminIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Google Admin
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your Google Workspace users, groups, and settings.
          </Typography>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {configStatus && !configStatus.isAuthenticated && (
              <Alert severity="warning" sx={{ mb: 3 }}>
                {configStatus.usingServiceAccount ? 
                  "Google Admin is configured to use service account authentication, but authentication failed. Please check your service account credentials and permissions." :
                  "Google Admin is not fully configured or authenticated. Please visit the configuration page to set up your credentials."}
              </Alert>
            )}

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <SettingsIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="h2">
                        Configuration
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Configure your Google Admin API credentials and authentication settings.
                    </Typography>
                    {configStatus && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          Status: {configStatus.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
                        </Typography>
                        {configStatus.usingServiceAccount && (
                          <Typography variant="body2" sx={{ color: 'info.main' }}>
                            Using Service Account Authentication
                          </Typography>
                        )}
                        {configStatus.configuredAt && (
                          <Typography variant="body2">
                            Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                          </Typography>
                        )}
                      </Box>
                    )}
                  </CardContent>
                  <CardActions>
                    <Button
                      component={RouterLink}
                      to="/google-admin/config"
                      size="small"
                      color="primary"
                    >
                      Manage Configuration
                    </Button>
                  </CardActions>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PersonIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="h2">
                        Users
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Manage users in your Google Workspace. Create, update, and delete user accounts.
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      component={RouterLink}
                      to="/google-admin/users"
                      size="small"
                      color="primary"
                      disabled={!configStatus || !configStatus.isAuthenticated}
                    >
                      Manage Users
                    </Button>
                  </CardActions>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <GroupIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="h2">
                        Groups
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Manage groups in your Google Workspace. Add and remove users from groups.
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      component={RouterLink}
                      to="/google-admin/groups"
                      size="small"
                      color="primary"
                      disabled={!configStatus || !configStatus.isAuthenticated}
                    >
                      Manage Groups
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            </Grid>

            <Divider sx={{ my: 4 }} />

            <Box>
              <Typography variant="h6" gutterBottom>
                About Google Admin Integration
              </Typography>
              <Typography variant="body2" paragraph>
                This integration allows you to manage your Google Workspace users and groups directly from this portal.
                You can create, update, and delete users, as well as manage group memberships.
              </Typography>
              <Typography variant="body2" paragraph>
                To use this integration, you need to configure it with your Google Cloud credentials and authenticate
                with a Google Workspace administrator account.
              </Typography>
              <Typography variant="body2" paragraph>
                For more information, visit the <RouterLink to="/google-admin/config">configuration page</RouterLink>.
              </Typography>
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default GoogleAdminPage;