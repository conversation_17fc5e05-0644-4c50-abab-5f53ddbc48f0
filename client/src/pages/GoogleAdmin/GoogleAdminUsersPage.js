import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  Grid,
  Alert,
  CircularProgress,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon,
  SyncAlt as SyncIcon
} from '@mui/icons-material';
import googleAdminService from '../../services/googleAdminService';

const GoogleAdminUsersPage = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [newUser, setNewUser] = useState({
    primaryEmail: '',
    name: {
      givenName: '',
      familyName: ''
    },
    password: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Fetch users from Google Admin
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await googleAdminService.listUsers();
      setUsers(data || []);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users. Please check your Google Admin configuration and authentication.');
      setLoading(false);
    }
  };

  // Handle create user dialog
  const handleOpenCreateDialog = () => {
    setNewUser({
      primaryEmail: '',
      name: {
        givenName: '',
        familyName: ''
      },
      password: ''
    });
    setOpenCreateDialog(true);
  };

  const handleCloseCreateDialog = () => {
    setOpenCreateDialog(false);
  };

  const handleCreateUser = async () => {
    try {
      setLoading(true);
      await googleAdminService.createUser(newUser);
      setOpenCreateDialog(false);
      await fetchUsers();
      setSnackbar({
        open: true,
        message: 'User created successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error creating user:', err);
      setSnackbar({
        open: true,
        message: `Error creating user: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle edit user dialog
  const handleOpenEditDialog = (user) => {
    setCurrentUser(user);
    setOpenEditDialog(true);
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const handleEditUser = async () => {
    try {
      setLoading(true);
      await googleAdminService.updateUser(currentUser.primaryEmail, {
        name: currentUser.name
      });
      setOpenEditDialog(false);
      await fetchUsers();
      setSnackbar({
        open: true,
        message: 'User updated successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error updating user:', err);
      setSnackbar({
        open: true,
        message: `Error updating user: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle delete user dialog
  const handleOpenDeleteDialog = (user) => {
    setCurrentUser(user);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleDeleteUser = async () => {
    try {
      setLoading(true);
      await googleAdminService.deleteUser(currentUser.primaryEmail);
      setOpenDeleteDialog(false);
      await fetchUsers();
      setSnackbar({
        open: true,
        message: 'User deleted successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error deleting user:', err);
      setSnackbar({
        open: true,
        message: `Error deleting user: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle syncing all users
  const handleSyncAllUsers = async () => {
    try {
      setLoading(true);
      const result = await googleAdminService.syncAllUsers();
      await fetchUsers(); // Refresh the user list after sync
      setSnackbar({
        open: true,
        message: `Sync completed: ${result.results.created} users created, ${result.results.existing} users already existed`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Error syncing users:', err);
      setSnackbar({
        open: true,
        message: `Error syncing users: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Handle input changes for new user
  const handleNewUserChange = (e) => {
    const { name, value } = e.target;
    if (name === 'givenName' || name === 'familyName') {
      setNewUser({
        ...newUser,
        name: {
          ...newUser.name,
          [name]: value
        }
      });
    } else {
      setNewUser({
        ...newUser,
        [name]: value
      });
    }
  };

  // Handle input changes for current user
  const handleCurrentUserChange = (e) => {
    const { name, value } = e.target;
    if (name === 'givenName' || name === 'familyName') {
      setCurrentUser({
        ...currentUser,
        name: {
          ...currentUser.name,
          [name]: value
        }
      });
    } else {
      setCurrentUser({
        ...currentUser,
        [name]: value
      });
    }
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 4, mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Google Admin Users
          </Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchUsers}
              sx={{ mr: 2 }}
              disabled={loading}
            >
              Refresh
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<SyncIcon />}
              onClick={handleSyncAllUsers}
              sx={{ mr: 2 }}
              disabled={loading}
            >
              Sync All Users
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateDialog}
              disabled={loading}
            >
              Add User
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Email</TableCell>
                  <TableCell>First Name</TableCell>
                  <TableCell>Last Name</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.length > 0 ? (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>{user.primaryEmail}</TableCell>
                      <TableCell>{user.name?.givenName || '-'}</TableCell>
                      <TableCell>{user.name?.familyName || '-'}</TableCell>
                      <TableCell>
                        <IconButton
                          color="primary"
                          onClick={() => handleOpenEditDialog(user)}
                          size="small"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => handleOpenDeleteDialog(user)}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      No users found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Paper>

      {/* Create User Dialog */}
      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Create New User</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                label="Email"
                name="primaryEmail"
                value={newUser.primaryEmail}
                onChange={handleNewUserChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="First Name"
                name="givenName"
                value={newUser.name.givenName}
                onChange={handleNewUserChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Last Name"
                name="familyName"
                value={newUser.name.familyName}
                onChange={handleNewUserChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Password"
                name="password"
                type="password"
                value={newUser.password}
                onChange={handleNewUserChange}
                fullWidth
                required
                helperText="Must be at least 8 characters"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCreateDialog}>Cancel</Button>
          <Button
            onClick={handleCreateUser}
            variant="contained"
            color="primary"
            disabled={
              !newUser.primaryEmail ||
              !newUser.name.givenName ||
              !newUser.name.familyName ||
              !newUser.password ||
              newUser.password.length < 8
            }
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      {currentUser && (
        <Dialog open={openEditDialog} onClose={handleCloseEditDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Edit User</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  label="Email"
                  value={currentUser.primaryEmail}
                  fullWidth
                  disabled
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  label="First Name"
                  name="givenName"
                  value={currentUser.name?.givenName || ''}
                  onChange={handleCurrentUserChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  label="Last Name"
                  name="familyName"
                  value={currentUser.name?.familyName || ''}
                  onChange={handleCurrentUserChange}
                  fullWidth
                  required
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseEditDialog}>Cancel</Button>
            <Button
              onClick={handleEditUser}
              variant="contained"
              color="primary"
              disabled={!currentUser.name?.givenName || !currentUser.name?.familyName}
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Delete User Dialog */}
      {currentUser && (
        <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
          <DialogTitle>Delete User</DialogTitle>
          <DialogContent>
            <DialogContentText>
              Are you sure you want to delete the user <strong>{currentUser.primaryEmail}</strong>?
              This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
            <Button onClick={handleDeleteUser} variant="contained" color="error">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default GoogleAdminUsersPage;
