import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  ToggleButton,
  ToggleButtonGroup,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import AcUnitIcon from '@mui/icons-material/AcUnit';
import LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';
import AutoModeIcon from '@mui/icons-material/AutoMode';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import AirIcon from '@mui/icons-material/Air';
import ScheduleIcon from '@mui/icons-material/Schedule';
import BarChartIcon from '@mui/icons-material/BarChart';
import HomeIcon from '@mui/icons-material/Home';
import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import skyportCloudService from '../../services/skyportCloudService';

const SkyportCloudPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [deviceStatus, setDeviceStatus] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [temperature, setTemperature] = useState(72);
  const [mode, setMode] = useState('off');
  const [fanMode, setFanMode] = useState('auto');
  const [holdEnabled, setHoldEnabled] = useState(false);
  const [awayEnabled, setAwayEnabled] = useState(false);
  const [schedule, setSchedule] = useState(null);
  const [energyUsage, setEnergyUsage] = useState(null);
  const [energyPeriod, setEnergyPeriod] = useState('month');
  const [zones, setZones] = useState([]);
  const [refreshInterval, setRefreshInterval] = useState(null);

  // Fetch configuration status and data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get configuration status
        const config = await skyportCloudService.getConfig();
        setConfigStatus(config);

        // If configured (API key or username/password), get devices
        if (config && (config.hasApiKey || config.hasUsernamePassword)) {
          const devicesData = await skyportCloudService.getDevices();
          setDevices(devicesData || []);

          // If there's a default device in the config, select it
          if (config.defaultDeviceId && devicesData) {
            const defaultDevice = devicesData.find(device => device.id === config.defaultDeviceId);
            if (defaultDevice) {
              setSelectedDevice(defaultDevice);
              await fetchDeviceData(defaultDevice.id);
            } else if (devicesData.length > 0) {
              // Otherwise select the first device
              setSelectedDevice(devicesData[0]);
              await fetchDeviceData(devicesData[0].id);
            }
          } else if (devicesData && devicesData.length > 0) {
            // If no default device, select the first one
            setSelectedDevice(devicesData[0]);
            await fetchDeviceData(devicesData[0].id);
          }
        }
      } catch (err) {
        console.error('Error fetching SkyportCloud data:', err);
        setError('Failed to load SkyportCloud data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Set up polling for device status updates
    const intervalId = setInterval(async () => {
      if (selectedDevice) {
        try {
          await fetchDeviceStatus(selectedDevice.id);
        } catch (err) {
          console.error('Error updating device status:', err);
        }
      }
    }, 30000); // Update every 30 seconds

    setRefreshInterval(intervalId);

    // Clean up interval on component unmount
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  // Clean up interval when component unmounts
  useEffect(() => {
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [refreshInterval]);

  const fetchDeviceData = async (deviceId) => {
    try {
      // Fetch device status, schedule, energy usage, and zones in parallel
      const [statusData, scheduleData, energyData, zonesData] = await Promise.all([
        skyportCloudService.getDeviceStatus(deviceId),
        skyportCloudService.getSchedule(deviceId),
        skyportCloudService.getEnergyUsage(deviceId, energyPeriod),
        skyportCloudService.getZones(deviceId)
      ]);

      setDeviceStatus(statusData);
      setSchedule(scheduleData);
      setEnergyUsage(energyData);
      setZones(zonesData || []);

      // Update UI state based on device status
      if (statusData) {
        setTemperature(statusData.temperature || 72);
        setMode(statusData.mode || 'off');
        setFanMode(statusData.fanMode || 'auto');
        setHoldEnabled(statusData.holdEnabled || false);
        setAwayEnabled(statusData.awayEnabled || false);
      }
    } catch (err) {
      console.error(`Error fetching data for device ${deviceId}:`, err);
      setError(`Failed to load device data. Please try again later.`);
    }
  };

  const fetchDeviceStatus = async (deviceId) => {
    try {
      const statusData = await skyportCloudService.getDeviceStatus(deviceId);
      setDeviceStatus(statusData);

      // Update UI state based on device status
      if (statusData) {
        setTemperature(statusData.temperature || 72);
        setMode(statusData.mode || 'off');
        setFanMode(statusData.fanMode || 'auto');
        setHoldEnabled(statusData.holdEnabled || false);
        setAwayEnabled(statusData.awayEnabled || false);
      }
    } catch (err) {
      console.error(`Error fetching status for device ${deviceId}:`, err);
    }
  };

  const handleDeviceChange = async (event) => {
    const deviceId = event.target.value;
    const device = devices.find(d => d.id === deviceId);
    setSelectedDevice(device);
    if (device) {
      await fetchDeviceData(device.id);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleTemperatureChange = (event, newValue) => {
    setTemperature(newValue);
  };

  const handleTemperatureChangeCommitted = async (event, newValue) => {
    try {
      await skyportCloudService.setTemperature(selectedDevice.id, newValue);
      await fetchDeviceStatus(selectedDevice.id);
    } catch (err) {
      console.error('Error setting temperature:', err);
      setError('Failed to set temperature. Please try again.');
    }
  };

  const handleModeChange = async (event, newMode) => {
    if (newMode !== null) {
      setMode(newMode);
      try {
        await skyportCloudService.setMode(selectedDevice.id, newMode);
        await fetchDeviceStatus(selectedDevice.id);
      } catch (err) {
        console.error('Error setting mode:', err);
        setError('Failed to set mode. Please try again.');
      }
    }
  };

  const handleFanModeChange = async (event) => {
    const newFanMode = event.target.value;
    setFanMode(newFanMode);
    try {
      await skyportCloudService.setFanMode(selectedDevice.id, newFanMode);
      await fetchDeviceStatus(selectedDevice.id);
    } catch (err) {
      console.error('Error setting fan mode:', err);
      setError('Failed to set fan mode. Please try again.');
    }
  };

  const handleHoldToggle = async (event) => {
    const newHoldEnabled = event.target.checked;
    setHoldEnabled(newHoldEnabled);
    try {
      await skyportCloudService.setHold(selectedDevice.id, newHoldEnabled);
      await fetchDeviceStatus(selectedDevice.id);
    } catch (err) {
      console.error('Error setting hold status:', err);
      setError('Failed to set hold status. Please try again.');
    }
  };

  const handleAwayToggle = async (event) => {
    const newAwayEnabled = event.target.checked;
    setAwayEnabled(newAwayEnabled);
    try {
      await skyportCloudService.setAwayMode(selectedDevice.id, newAwayEnabled);
      await fetchDeviceStatus(selectedDevice.id);
    } catch (err) {
      console.error('Error setting away mode:', err);
      setError('Failed to set away mode. Please try again.');
    }
  };

  const handleEnergyPeriodChange = async (event) => {
    const newPeriod = event.target.value;
    setEnergyPeriod(newPeriod);
    try {
      const energyData = await skyportCloudService.getEnergyUsage(selectedDevice.id, newPeriod);
      setEnergyUsage(energyData);
    } catch (err) {
      console.error('Error fetching energy usage:', err);
      setError('Failed to fetch energy usage. Please try again.');
    }
  };

  const refreshData = async () => {
    if (selectedDevice) {
      await fetchDeviceData(selectedDevice.id);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4, display: 'flex', justifyContent: 'center' }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // If not configured, show configuration required message
  if (!configStatus || !(configStatus.hasApiKey || configStatus.hasUsernamePassword)) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            SkyportCloud HVAC Controls
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              SkyportCloud integration is not configured yet.
            </Alert>
            <Typography variant="body1" paragraph>
              The SkyportCloud integration requires environment variables to be set on the server.
            </Typography>
            <Typography variant="body1" paragraph>
              Please contact your administrator to set up the required environment variables:
            </Typography>
            <Box component="ul" sx={{ pl: 4 }}>
              <Box component="li">
                <Typography variant="body2">
                  <strong>SKYPORTCLOUD_API_KEY</strong> (preferred) or
                </Typography>
              </Box>
              <Box component="li">
                <Typography variant="body2">
                  <strong>SKYPORTCLOUD_USERNAME</strong> and <strong>SKYPORTCLOUD_PASSWORD</strong>
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Box>
      </Container>
    );
  }

  // If no devices found
  if (devices.length === 0) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            SkyportCloud HVAC Controls
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              No HVAC devices found in your SkyportCloud account.
            </Alert>
            <Typography variant="body1" paragraph>
              Please make sure your devices are properly set up in your SkyportCloud account.
            </Typography>
            <Button 
              variant="contained" 
              color="primary"
              onClick={refreshData}
              sx={{ mt: 2 }}
            >
              Refresh
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          SkyportCloud HVAC Controls
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Device Selector */}
        {devices.length > 1 && (
          <Paper sx={{ p: 2, mb: 3 }}>
            <FormControl fullWidth>
              <InputLabel id="device-select-label">Device</InputLabel>
              <Select
                labelId="device-select-label"
                id="device-select"
                value={selectedDevice ? selectedDevice.id : ''}
                label="Device"
                onChange={handleDeviceChange}
              >
                {devices.map(device => (
                  <MenuItem key={device.id} value={device.id}>
                    {device.name || device.id}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Paper>
        )}

        {/* Main Control Panel */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {selectedDevice ? (selectedDevice.name || selectedDevice.id) : 'HVAC Controls'}
          </Typography>

          {/* Temperature and Mode Controls */}
          <Grid container spacing={3}>
            {/* Temperature Control */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Temperature
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
                    <ThermostatIcon sx={{ fontSize: 40, mr: 1 }} />
                    <Typography variant="h3">
                      {temperature}°
                    </Typography>
                  </Box>
                  <Slider
                    value={temperature}
                    onChange={handleTemperatureChange}
                    onChangeCommitted={handleTemperatureChangeCommitted}
                    min={50}
                    max={90}
                    step={1}
                    marks={[
                      { value: 50, label: '50°' },
                      { value: 70, label: '70°' },
                      { value: 90, label: '90°' }
                    ]}
                    disabled={mode === 'off'}
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* Mode Control */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Mode
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <ToggleButtonGroup
                      value={mode}
                      exclusive
                      onChange={handleModeChange}
                      aria-label="HVAC mode"
                    >
                      <ToggleButton value="cool" aria-label="cool">
                        <AcUnitIcon />
                        <Typography sx={{ ml: 1 }}>Cool</Typography>
                      </ToggleButton>
                      <ToggleButton value="heat" aria-label="heat">
                        <LocalFireDepartmentIcon />
                        <Typography sx={{ ml: 1 }}>Heat</Typography>
                      </ToggleButton>
                      <ToggleButton value="auto" aria-label="auto">
                        <AutoModeIcon />
                        <Typography sx={{ ml: 1 }}>Auto</Typography>
                      </ToggleButton>
                      <ToggleButton value="off" aria-label="off">
                        <PowerSettingsNewIcon />
                        <Typography sx={{ ml: 1 }}>Off</Typography>
                      </ToggleButton>
                    </ToggleButtonGroup>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Fan Control */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Fan
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <AirIcon sx={{ mr: 2 }} />
                    <FormControl fullWidth>
                      <InputLabel id="fan-mode-label">Fan Mode</InputLabel>
                      <Select
                        labelId="fan-mode-label"
                        id="fan-mode"
                        value={fanMode}
                        label="Fan Mode"
                        onChange={handleFanModeChange}
                        disabled={mode === 'off'}
                      >
                        <MenuItem value="auto">Auto</MenuItem>
                        <MenuItem value="on">On</MenuItem>
                        <MenuItem value="circulate">Circulate</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Hold and Away Controls */}
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Settings
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={holdEnabled}
                          onChange={handleHoldToggle}
                          disabled={mode === 'off'}
                        />
                      }
                      label="Hold Temperature"
                    />
                  </Box>
                  <Box>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={awayEnabled}
                          onChange={handleAwayToggle}
                        />
                      }
                      label="Away Mode"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Tabs for different content sections */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mt: 3, mb: 2 }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="skyportcloud content tabs">
              <Tab label="Schedule" icon={<ScheduleIcon />} iconPosition="start" />
              <Tab label="Energy Usage" icon={<BarChartIcon />} iconPosition="start" />
              {zones.length > 0 && <Tab label="Zones" icon={<HomeIcon />} iconPosition="start" />}
            </Tabs>
          </Box>

          {/* Schedule Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Schedule
              </Typography>
              
              {schedule ? (
                <Box>
                  {/* Display schedule information here */}
                  <Typography variant="body1">
                    Schedule information would be displayed here based on the API response format.
                  </Typography>
                </Box>
              ) : (
                <Typography variant="body1">
                  No schedule information available.
                </Typography>
              )}
            </Box>
          )}

          {/* Energy Usage Tab */}
          {activeTab === 1 && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Energy Usage
                </Typography>
                <FormControl sx={{ minWidth: 120 }}>
                  <InputLabel id="energy-period-label">Period</InputLabel>
                  <Select
                    labelId="energy-period-label"
                    id="energy-period"
                    value={energyPeriod}
                    label="Period"
                    onChange={handleEnergyPeriodChange}
                    size="small"
                  >
                    <MenuItem value="day">Day</MenuItem>
                    <MenuItem value="week">Week</MenuItem>
                    <MenuItem value="month">Month</MenuItem>
                    <MenuItem value="year">Year</MenuItem>
                  </Select>
                </FormControl>
              </Box>
              
              {energyUsage ? (
                <Box>
                  {/* Display energy usage information here */}
                  <Typography variant="body1">
                    Energy usage information would be displayed here based on the API response format.
                  </Typography>
                </Box>
              ) : (
                <Typography variant="body1">
                  No energy usage information available.
                </Typography>
              )}
            </Box>
          )}

          {/* Zones Tab */}
          {activeTab === 2 && zones.length > 0 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Zones
              </Typography>
              
              <List>
                {zones.map(zone => (
                  <ListItem key={zone.id}>
                    <ListItemText
                      primary={zone.name || `Zone ${zone.id}`}
                      secondary={`Temperature: ${zone.temperature}° | Mode: ${zone.mode}`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton edge="end" aria-label="edit">
                        <ThermostatIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </Paper>

        <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>
          <Button 
            variant="contained" 
            color="primary"
            onClick={refreshData}
          >
            Refresh
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default SkyportCloudPage;