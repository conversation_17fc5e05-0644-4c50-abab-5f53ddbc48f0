import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Alert,
  CircularProgress,
  Chip,
  Divider,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import VpnKeyIcon from '@mui/icons-material/VpnKey';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import InfoIcon from '@mui/icons-material/Info';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import skyportCloudService from '../../services/skyportCloudService';

const SkyportCloudSetup = () => {
  const [error, setError] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingStatus, setLoadingStatus] = useState(true);
  const [devices, setDevices] = useState([]);
  const [loadingDevices, setLoadingDevices] = useState(false);

  // Fetch current configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await skyportCloudService.getConfig();
        setConfigStatus(config);
        
        // If we have a configuration with credentials, try to fetch devices
        if (config && (config.hasApiKey || config.hasUsernamePassword)) {
          fetchDevices();
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
        setError('Error fetching configuration status. Please try again later.');
      } finally {
        setLoadingStatus(false);
      }
    };

    fetchConfigStatus();
  }, []);

  const fetchDevices = async () => {
    setLoadingDevices(true);
    try {
      const devicesData = await skyportCloudService.getDevices();
      setDevices(devicesData || []);
    } catch (err) {
      console.error('Error fetching devices:', err);
      setError('Error fetching devices. Please try again later.');
    } finally {
      setLoadingDevices(false);
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ my: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          SkyportCloud HVAC Controls Setup
        </Typography>

        <Paper sx={{ p: 3 }}>
          {loadingStatus ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" gutterBottom>
                Configuration Status
              </Typography>
              {configStatus ? (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label={configStatus.fromEnv ? "Configured via Environment" : "Configured via Database"} 
                    color="success" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    SkyportCloud is configured with API endpoint: {configStatus.baseUrl}
                    <br />
                    Authentication method: {configStatus.authMethod === 'apiKey' ? 'API Key' : 'Username/Password'}
                    <br />
                    Last updated: {new Date(configStatus.configuredAt).toLocaleString()}
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip 
                    label="Not Configured" 
                    color="warning" 
                    sx={{ mr: 2 }} 
                  />
                  <Typography variant="body2">
                    SkyportCloud integration is not configured yet. Please contact your administrator to set up the required environment variables.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>
            Environment Variable Configuration
          </Typography>

          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              The SkyportCloud integration is now configured using environment variables instead of the database.
              This change improves security by keeping sensitive credentials out of the database.
            </Typography>
          </Alert>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AdminPanelSettingsIcon sx={{ mr: 1 }} color="primary" />
                <Typography variant="h6">Administrator Instructions</Typography>
              </Box>
              
              <Typography variant="body1" paragraph>
                To configure the SkyportCloud integration, add the following environment variables to your server's environment:
              </Typography>

              <List>
                <ListItem>
                  <ListItemIcon>
                    <VpnKeyIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="SKYPORTCLOUD_API_KEY" 
                    secondary="Your SkyportCloud API key (preferred authentication method)" 
                  />
                </ListItem>
                <Typography variant="body2" sx={{ ml: 7, mb: 2 }}>
                  - OR -
                </Typography>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="SKYPORTCLOUD_USERNAME and SKYPORTCLOUD_PASSWORD" 
                    secondary="Your SkyportCloud username and password (alternative authentication method)" 
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <SettingsIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="SKYPORTCLOUD_BASE_URL (optional)" 
                    secondary="The base URL for the SkyportCloud API (defaults to https://api.skyportcloud.com)" 
                  />
                </ListItem>
              </List>

              <Typography variant="body1" paragraph>
                Add these variables to your .env file or your server's environment configuration.
              </Typography>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InfoIcon sx={{ mr: 1 }} color="primary" />
                <Typography variant="h6">How to Get Your SkyportCloud Credentials</Typography>
              </Box>
              
              <Typography variant="body1" paragraph>
                To get your SkyportCloud API key:
              </Typography>

              <ol>
                <li>
                  <Typography variant="body1" paragraph>
                    Log in to your SkyportCloud account at <a href="https://skyportcloud.com" target="_blank" rel="noopener noreferrer">skyportcloud.com</a>
                  </Typography>
                </li>
                <li>
                  <Typography variant="body1" paragraph>
                    Navigate to your account settings and find the API section
                  </Typography>
                </li>
                <li>
                  <Typography variant="body1" paragraph>
                    Generate a new API key or copy your existing API key
                  </Typography>
                </li>
                <li>
                  <Typography variant="body1" paragraph>
                    Add the API key to your environment variables as SKYPORTCLOUD_API_KEY
                  </Typography>
                </li>
              </ol>
            </CardContent>
          </Card>

          {devices.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Connected Devices
              </Typography>
              <List>
                {devices.map(device => (
                  <ListItem key={device.id}>
                    <ListItemText 
                      primary={device.name || device.id} 
                      secondary={`ID: ${device.id}`} 
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
          
          {loadingDevices && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <CircularProgress size={24} sx={{ mr: 1 }} />
              <Typography variant="body2">Loading devices...</Typography>
            </Box>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default SkyportCloudSetup;