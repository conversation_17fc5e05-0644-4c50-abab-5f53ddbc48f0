import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Avatar,
  Button,
  Card,
  CardContent,
  Grid,
  Divider,
  Chip,
  TextField,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Paper,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  LocalOffer as TagIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelCircleIcon,
  VpnKey as VpnKeyIcon,
  Security as SecurityIcon,
  Badge as BadgeIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import accessControlService from '../../services/accessControlService';

const UserProfilePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    avatar: '',
    jobTitle: '',
    department: '',
    phoneNumber: '',
    location: '',
    bio: '',
    skills: [],
    tags: []
  });
  const [newSkill, setNewSkill] = useState('');
  const [newTag, setNewTag] = useState('');
  const [skillDialogOpen, setSkillDialogOpen] = useState(false);
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [serviceProvisioning, setServiceProvisioning] = useState({});
  const [provisioningLoading, setProvisioningLoading] = useState(false);
  const [provisioningError, setProvisioningError] = useState(null);
  const [serviceDefinitions, setServiceDefinitions] = useState({});
  const [availableIntegrations, setAvailableIntegrations] = useState([]);
  const [userAccountStatus, setUserAccountStatus] = useState({});
  
  // State for user preferences
  const [userPreferences, setUserPreferences] = useState({});
  const [preferencesLoading, setPreferencesLoading] = useState(false);
  const [preferencesError, setPreferencesError] = useState(null);
  const [quickLinksToolbarEnabled, setQuickLinksToolbarEnabled] = useState(true);
  // Notification preferences
  const [notificationEmailEnabled, setNotificationEmailEnabled] = useState(true);
  const [notificationSystemEnabled, setNotificationSystemEnabled] = useState(true);
  
  // State for access control
  const [accessControlData, setAccessControlData] = useState(null);
  const [accessControlLoading, setAccessControlLoading] = useState(false);
  const [accessControlError, setAccessControlError] = useState(null);
  const [accessLevels, setAccessLevels] = useState([]);
  const [accessSummary, setAccessSummary] = useState(null);
  const [cardFormOpen, setCardFormOpen] = useState(false);
  const [cardFormData, setCardFormData] = useState({
    cardNumber: '',
    cardFormat: 'HID',
    description: ''
  });

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const res = await axios.get(`/api/staff-directory/users/${id}`);
        setUser(res.data);
        setFormData({
          name: res.data.name || '',
          avatar: res.data.avatar || '',
          jobTitle: res.data.jobTitle || '',
          department: res.data.department || '',
          phoneNumber: res.data.phoneNumber || '',
          location: res.data.location || '',
          bio: res.data.bio || '',
          skills: res.data.skills || [],
          tags: res.data.tags || []
        });
        
        // Fetch user account status across all integrations
        try {
          setProvisioningLoading(true);
          
          // Fetch user account status from userProvisioningController
          const statusRes = await axios.get(`/api/user-provisioning/${id}/status`);
          setUserAccountStatus(statusRes.data || {});
          
          // Create a map of service provisioning settings based on account status
          const provisioningMap = {};
          
          // Get available integrations from the controller
          const integrationsRes = await axios.get('/api/integrations');
          const integrations = integrationsRes.data.filter(integration => 
            integration.supportsProvisioning === true
          );
          
          setAvailableIntegrations(integrations);
          
          // Create service definitions map
          const servicesMap = {};
          integrations.forEach(integration => {
            servicesMap[integration.id] = {
              displayName: integration.name || integration.id,
              description: integration.description
            };
            
            // Set initial provisioning status based on account status
            provisioningMap[integration.id] = {
              enabled: statusRes.data[integration.id]?.exists || false,
              displayName: integration.name,
              description: integration.description
            };
          });
          
          setServiceDefinitions(servicesMap);
          setServiceProvisioning(provisioningMap);
          setProvisioningError(null);
        } catch (provErr) {
          console.error('Error fetching user account status:', provErr);
          // Don't set an error for this, as it's not critical
          setServiceProvisioning({});
          setProvisioningError('Failed to load integration status. Please try again later.');
        } finally {
          setProvisioningLoading(false);
        }
        
        // Fetch user widget preferences
        try {
          setPreferencesLoading(true);
          const preferencesRes = await axios.get('/api/users/me/widget-preferences');
          setUserPreferences(preferencesRes.data || {});
          
          // Set quick links toolbar enabled state
          if (preferencesRes.data && preferencesRes.data.floatingShortcut) {
            setQuickLinksToolbarEnabled(preferencesRes.data.floatingShortcut.enabled !== false);
          }
          
          // Fetch notification preferences for current user
          try {
            const notifRes = await axios.get('/api/users/me/notification-preferences');
            setNotificationEmailEnabled(notifRes.data?.emailEnabled !== false);
            setNotificationSystemEnabled(notifRes.data?.systemEnabled !== false);
          } catch (e) {
            console.warn('Error fetching notification preferences:', e);
          }
          
          setPreferencesError(null);
        } catch (prefErr) {
          console.error('Error fetching widget preferences:', prefErr);
          // Don't set an error for this, as it's not critical
          setPreferencesError(null);
        } finally {
          setPreferencesLoading(false);
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching user:', err);
        setError('Failed to load user profile. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [id]);
  
  // Fetch access control data
  useEffect(() => {
    const fetchAccessControlData = async () => {
      if (!user) return;
      
      setAccessControlLoading(true);
      try {
        // Lookup access control data for the user by email/phone
        const accessControlRes = await accessControlService.lookupUserByContact({
          email: user.email,
          phone: user.phoneNumber
        });
        setAccessControlData(accessControlRes);
        
        // Fetch available access levels
        const accessLevelsRes = await accessControlService.getAllAccessLevels();
        setAccessLevels(accessLevelsRes);
        
        setAccessControlError(null);
      } catch (err) {
        console.error('Error fetching access control data:', err);
        if (err?.response?.status === 404) {
          // Not found is acceptable; user may not exist in AC systems
          setAccessControlData(null);
          setAccessControlError(null);
        } else {
          setAccessControlError('Failed to load access control data. Please try again later.');
        }
      } finally {
        setAccessControlLoading(false);
      }
    };
    
    fetchAccessControlData();
  }, [id, user]);

  // Check if current user can edit this profile
  const canEdit = currentUser && (currentUser._id === id || currentUser.roles.includes('admin'));

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      let res;

      // Use the new /api/users/me endpoint when editing own profile
      if (currentUser._id === id) {
        res = await axios.put('/api/users/me', formData);
      } else {
        // For admins editing other users' profiles
        res = await axios.put(`/api/staff-directory/users/${id}/profile`, formData);
      }

      setUser(res.data);
      setEditing(false);
      setError(null);
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a skill
  const handleAddSkill = () => {
    if (newSkill.trim() && !formData.skills.includes(newSkill.trim())) {
      setFormData({
        ...formData,
        skills: [...formData.skills, newSkill.trim()]
      });
      setNewSkill('');
    }
    setSkillDialogOpen(false);
  };

  // Handle removing a skill
  const handleRemoveSkill = (skillToRemove) => {
    setFormData({
      ...formData,
      skills: formData.skills.filter(skill => skill !== skillToRemove)
    });
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
    setTagDialogOpen(false);
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Handle provisioning a user account in an integration
  const handleProvisionUser = async (integrationId) => {
    setProvisioningLoading(true);
    setProvisioningError(null);
    
    try {
      // Prepare user data for provisioning
      const userData = {
        firstName: user.firstName || user.name.split(' ')[0],
        lastName: user.lastName || user.name.split(' ').slice(1).join(' '),
        email: user.email
      };
      
      // Call the provisioning endpoint
      const res = await axios.post(`/api/user-provisioning/${id}/${integrationId}`, userData);
      
      // Update local state
      setUserAccountStatus(prev => ({
        ...prev,
        [integrationId]: {
          exists: true,
          accountId: res.data.accountId,
          details: res.data.details
        }
      }));
      
      setServiceProvisioning(prev => ({
        ...prev,
        [integrationId]: {
          ...prev[integrationId],
          enabled: true
        }
      }));
      
      setProvisioningError(null);
    } catch (err) {
      const serviceName = serviceDefinitions[integrationId]?.displayName || integrationId;
      console.error(`Error provisioning ${serviceName} account:`, err);
      setProvisioningError(`Failed to provision ${serviceName} account. Please try again.`);
    } finally {
      setProvisioningLoading(false);
    }
  };
  
  // Handle deprovisioning a user account from an integration
  const handleDeprovisionUser = async (integrationId) => {
    setProvisioningLoading(true);
    setProvisioningError(null);
    
    try {
      // Call the deprovisioning endpoint
      await axios.delete(`/api/user-provisioning/${id}/${integrationId}`);
      
      // Update local state
      setUserAccountStatus(prev => ({
        ...prev,
        [integrationId]: {
          exists: false
        }
      }));
      
      setServiceProvisioning(prev => ({
        ...prev,
        [integrationId]: {
          ...prev[integrationId],
          enabled: false
        }
      }));
      
      setProvisioningError(null);
    } catch (err) {
      const serviceName = serviceDefinitions[integrationId]?.displayName || integrationId;
      console.error(`Error deprovisioning ${serviceName} account:`, err);
      setProvisioningError(`Failed to deprovision ${serviceName} account. Please try again.`);
    } finally {
      setProvisioningLoading(false);
    }
  };
  
  // Handle disabling a user account in an integration
  const handleDisableUser = async (integrationId) => {
    setProvisioningLoading(true);
    setProvisioningError(null);
    
    try {
      // Call the disable endpoint
      await axios.put(`/api/user-provisioning/${id}/${integrationId}/disable`);
      
      // Update local state
      setUserAccountStatus(prev => ({
        ...prev,
        [integrationId]: {
          ...prev[integrationId],
          status: 'disabled'
        }
      }));
      
      setServiceProvisioning(prev => ({
        ...prev,
        [integrationId]: {
          ...prev[integrationId],
          enabled: false
        }
      }));
      
      setProvisioningError(null);
    } catch (err) {
      const serviceName = serviceDefinitions[integrationId]?.displayName || integrationId;
      console.error(`Error disabling ${serviceName} account:`, err);
      setProvisioningError(`Failed to disable ${serviceName} account. Please try again.`);
    } finally {
      setProvisioningLoading(false);
    }
  };
  
  // Handle toggling quick links toolbar
  const handleToggleQuickLinksToolbar = async (enabled) => {
    setPreferencesLoading(true);
    setPreferencesError(null);
    
    try {
      // Update local state
      setQuickLinksToolbarEnabled(enabled);
      
      // Save to server
      await axios.put('/api/users/me/widget-preferences', {
        widgetType: 'floatingShortcut',
        preferences: {
          ...userPreferences.floatingShortcut,
          enabled: enabled
        }
      });
      
      // Update local storage for immediate effect
      localStorage.setItem('floatingShortcutWidgetEnabled', enabled ? 'true' : 'false');
      
      setPreferencesError(null);
    } catch (err) {
      console.error('Error updating quick links toolbar preference:', err);
      setPreferencesError('Failed to update quick links toolbar preference. Please try again.');
      
      // Revert local state on error
      setQuickLinksToolbarEnabled(!enabled);
    } finally {
      setPreferencesLoading(false);
    }
  };
  
  // Handle toggling email notifications
  const handleToggleEmailNotifications = async (enabled) => {
    // Only allow current user to change their own preferences
    if (!currentUser || currentUser._id !== id) return;
    setPreferencesLoading(true);
    setPreferencesError(null);
    try {
      // Update local state
      setNotificationEmailEnabled(enabled);
      // Save to server
      await axios.put('/api/users/me/notification-preferences', {
        emailEnabled: enabled
      });
      setPreferencesError(null);
    } catch (err) {
      console.error('Error updating email notification preference:', err);
      setPreferencesError('Failed to update email notifications preference. Please try again.');
      // Revert on error
      setNotificationEmailEnabled(!enabled);
    } finally {
      setPreferencesLoading(false);
    }
  };
  
  // Handle toggling system notifications
  const handleToggleSystemNotifications = async (enabled) => {
    // Only allow current user to change their own preferences
    if (!currentUser || currentUser._id !== id) return;
    setPreferencesLoading(true);
    setPreferencesError(null);
    try {
      // Update local state
      setNotificationSystemEnabled(enabled);
      // Save to server
      await axios.put('/api/users/me/notification-preferences', {
        systemEnabled: enabled
      });
      setPreferencesError(null);
    } catch (err) {
      console.error('Error updating system notification preference:', err);
      setPreferencesError('Failed to update system notifications preference. Please try again.');
      // Revert on error
      setNotificationSystemEnabled(!enabled);
    } finally {
      setPreferencesLoading(false);
    }
  };
  
  // Handle card form input changes
  const handleCardFormInputChange = (e) => {
    const { name, value } = e.target;
    setCardFormData({
      ...cardFormData,
      [name]: value
    });
  };
  
  // Handle card form submission
  const handleCardFormSubmit = async (e) => {
    e.preventDefault();
    setAccessControlLoading(true);
    
    try {
      // Prepare the card data
      const newCard = {
        cardNumber: cardFormData.cardNumber,
        cardFormat: cardFormData.cardFormat,
        description: cardFormData.description
      };
      
      // Determine which systems to update
      const systems = [];
      if (accessControlData?.unifiAccessId) {
        systems.push('unifi-access');
      }
      if (accessControlData?.lenelS2NetBoxId) {
        systems.push('lenel-s2-netbox');
      }
      
      // Get existing cards or initialize empty array
      const existingCards = accessControlData?.lenelS2NetBoxData?.cards || [];
      
      // Update the user with the new card
      const acUserId = accessControlData?.id || id;
      await accessControlService.updateUser(acUserId, {
        firstName: user.firstName || user.name.split(' ')[0],
        lastName: user.lastName || user.name.split(' ').slice(1).join(' '),
        email: user.email,
        systems,
        cards: [...existingCards, newCard]
      });
      
      // Refresh access control data using contact lookup
      const updatedAccessControlData = await accessControlService.lookupUserByContact({
        email: user.email,
        phone: user.phoneNumber
      });
      setAccessControlData(updatedAccessControlData);
      
      // Reset form and close dialog
      setCardFormData({
        cardNumber: '',
        cardFormat: 'HID',
        description: ''
      });
      setCardFormOpen(false);
      
      setAccessControlError(null);
    } catch (err) {
      console.error('Error assigning card:', err);
      setAccessControlError('Failed to assign card. Please try again.');
    } finally {
      setAccessControlLoading(false);
    }
  };

  // Handle sync profile with Google
  const handleSyncProfile = async () => {
    if (currentUser._id !== id) return;

    setLoading(true);
    try {
      const res = await axios.post('/api/staff-directory/google/sync-profile');
      setUser(res.data);
      setFormData({
        jobTitle: res.data.jobTitle || '',
        department: res.data.department || '',
        phoneNumber: res.data.phoneNumber || '',
        location: res.data.location || '',
        bio: res.data.bio || '',
        skills: res.data.skills || [],
        tags: res.data.tags || []
      });
      setError(null);
    } catch (err) {
      console.error('Error syncing profile with Google:', err);
      setError('Failed to sync profile with Google. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !user) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !user) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
        <Button variant="contained" onClick={() => navigate('/staff-directory')} sx={{ mt: 2 }}>
          Back to Staff Directory
        </Button>
      </Box>
    );
  }

  if (!user) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>User not found</Typography>
        <Button variant="contained" onClick={() => navigate('/staff-directory')} sx={{ mt: 2 }}>
          Back to Staff Directory
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          User Profile
        </Typography>
        <Box>
          {currentUser._id === id && (
            <Button
              variant="outlined"
              onClick={handleSyncProfile}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              Sync with Google
            </Button>
          )}
          <Button
            variant="contained"
            onClick={() => navigate('/staff-directory')}
          >
            Back to Directory
          </Button>
        </Box>
      </Box>

      {error && (
        <Box sx={{ mb: 3 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<PersonIcon />} label="Profile" />
          <Tab icon={<GroupIcon />} label="Teams & Groups" />
          <Tab icon={<SettingsIcon />} label="Service Provisioning" />
          <Tab icon={<SecurityIcon />} label="Access Control" />
          <Tab icon={<SettingsIcon />} label="Settings" />
          <Tab icon={<NotificationsIcon />} label="Notifications" />
        </Tabs>
      </Paper>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Avatar
                  src={user.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(user.avatar)}` : undefined}
                  alt={user.name}
                  sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
                />
                <Typography variant="h5" gutterBottom>
                  {user.name}
                </Typography>
                {formData.jobTitle && !editing && (
                  <Typography variant="subtitle1" color="text.secondary">
                    {formData.jobTitle}
                  </Typography>
                )}
                {formData.department && !editing && (
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {formData.department}
                  </Typography>
                )}

                {!editing && canEdit && (
                  <Button
                    variant="contained"
                    startIcon={<EditIcon />}
                    onClick={() => setEditing(true)}
                    sx={{ mt: 2 }}
                  >
                    Edit Profile
                  </Button>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                {editing ? (
                  <form onSubmit={handleSubmit}>
                    <Typography variant="h6" gutterBottom>
                      Edit Profile
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Avatar URL"
                          name="avatar"
                          value={formData.avatar}
                          onChange={handleInputChange}
                          margin="normal"
                          helperText="Enter a URL for your profile picture"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Job Title"
                          name="jobTitle"
                          value={formData.jobTitle}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Department"
                          name="department"
                          value={formData.department}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Phone Number"
                          name="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Bio"
                          name="bio"
                          value={formData.bio}
                          onChange={handleInputChange}
                          margin="normal"
                          multiline
                          rows={4}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle1">Skills</Typography>
                          <Button
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={() => setSkillDialogOpen(true)}
                          >
                            Add Skill
                          </Button>
                        </Box>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {formData.skills.map((skill, index) => (
                            <Chip
                              key={index}
                              label={skill}
                              onDelete={() => handleRemoveSkill(skill)}
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Grid>

                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle1">Tags</Typography>
                          <Button
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={() => setTagDialogOpen(true)}
                          >
                            Add Tag
                          </Button>
                        </Box>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {formData.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              onDelete={() => handleRemoveTag(tag)}
                              color="primary"
                            />
                          ))}
                        </Box>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                      <Button
                        variant="outlined"
                        startIcon={<CancelIcon />}
                        onClick={() => {
                          setEditing(false);
                          setFormData({
                            name: user.name || '',
                            avatar: user.avatar || '',
                            jobTitle: user.jobTitle || '',
                            department: user.department || '',
                            phoneNumber: user.phoneNumber || '',
                            location: user.location || '',
                            bio: user.bio || '',
                            skills: user.skills || [],
                            tags: user.tags || []
                          });
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<SaveIcon />}
                        disabled={loading}
                      >
                        Save
                      </Button>
                    </Box>
                  </form>
                ) : (
                  <>
                    <Typography variant="h6" gutterBottom>
                      Contact Information
                    </Typography>

                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body1" component="a" href={`mailto:${user.email}`} sx={{ textDecoration: 'none', color: 'primary.main' }}>
                          {user.email}
                        </Typography>
                      </Box>

                      {formData.phoneNumber && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body1" component="a" href={`tel:${formData.phoneNumber}`} sx={{ textDecoration: 'none', color: 'primary.main' }}>
                            {formData.phoneNumber}
                          </Typography>
                        </Box>
                      )}

                      {formData.department && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body1">
                            {formData.department}
                          </Typography>
                        </Box>
                      )}

                      {formData.location && (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body1">
                            {formData.location}
                          </Typography>
                        </Box>
                      )}
                    </Box>

                    {formData.bio && (
                      <>
                        <Typography variant="h6" gutterBottom>
                          Bio
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {formData.bio}
                        </Typography>
                      </>
                    )}

                    {formData.skills && formData.skills.length > 0 && (
                      <>
                        <Typography variant="h6" gutterBottom>
                          Skills
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 3 }}>
                          {formData.skills.map((skill, index) => (
                            <Chip
                              key={index}
                              label={skill}
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </>
                    )}

                    {formData.tags && formData.tags.length > 0 && (
                      <>
                        <Typography variant="h6" gutterBottom>
                          Tags
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {formData.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={tag}
                              color="primary"
                            />
                          ))}
                        </Box>
                      </>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Teams
                </Typography>

                {user.teams && user.teams.length > 0 ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {user.teams.map((team) => (
                      <Card key={team._id} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle1" component="div">
                          {team.name}
                        </Typography>
                        {team.description && (
                          <Typography variant="body2" color="text.secondary">
                            {team.description}
                          </Typography>
                        )}
                      </Card>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Not a member of any teams
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Groups
                </Typography>

                {user.groups && user.groups.length > 0 ? (
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {user.groups.map((group) => (
                      <Card key={group._id} variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle1" component="div">
                          {group.name}
                        </Typography>
                        {group.description && (
                          <Typography variant="body2" color="text.secondary">
                            {group.description}
                          </Typography>
                        )}
                      </Card>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Not a member of any groups
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Service Provisioning
                  </Typography>
                  {canEdit && (
                    <Typography variant="body2" color="text.secondary">
                      Enable or disable automatic provisioning for various services
                    </Typography>
                  )}
                </Box>
                
                {provisioningError && (
                  <Box sx={{ mb: 3 }}>
                    <Typography color="error">{provisioningError}</Typography>
                  </Box>
                )}
                
                {provisioningLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Box>
                    {availableIntegrations.length === 0 ? (
                      <Typography variant="body2" color="text.secondary">
                        No external integrations available
                      </Typography>
                    ) : (
                      <Grid container spacing={2}>
                        {availableIntegrations.map((integration) => {
                          const integrationId = integration.id;
                          const settings = serviceProvisioning[integrationId] || {};
                          const accountStatus = userAccountStatus[integrationId] || { exists: false };
                          
                          return (
                            <Grid item xs={12} sm={6} md={4} key={integrationId}>
                              <Card variant="outlined" sx={{ p: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                  <Typography variant="subtitle1">
                                    {integration.name || integrationId}
                                  </Typography>
                                  {accountStatus.exists ? (
                                    <Chip 
                                      icon={<CheckCircleIcon />} 
                                      label="Account Exists" 
                                      color="success" 
                                      size="small" 
                                    />
                                  ) : (
                                    <Chip 
                                      icon={<CancelCircleIcon />} 
                                      label="No Account" 
                                      color="default" 
                                      size="small" 
                                    />
                                  )}
                                </Box>
                                
                                {integration.description && (
                                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {integration.description}
                                  </Typography>
                                )}
                                
                                {canEdit && (
                                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
                                    {!accountStatus.exists ? (
                                      // If account doesn't exist, show provision button
                                      <Button
                                        variant="contained"
                                        color="primary"
                                        size="small"
                                        onClick={() => handleProvisionUser(integrationId)}
                                        disabled={provisioningLoading}
                                        startIcon={<AddIcon />}
                                        fullWidth
                                      >
                                        Create Account
                                      </Button>
                                    ) : (
                                      // If account exists, show disable and delete buttons
                                      <>
                                        <Button
                                          variant="outlined"
                                          color="warning"
                                          size="small"
                                          onClick={() => handleDisableUser(integrationId)}
                                          disabled={provisioningLoading || !settings.enabled}
                                          startIcon={<CancelCircleIcon />}
                                          fullWidth
                                        >
                                          Disable Account
                                        </Button>
                                        <Button
                                          variant="outlined"
                                          color="error"
                                          size="small"
                                          onClick={() => handleDeprovisionUser(integrationId)}
                                          disabled={provisioningLoading}
                                          startIcon={<DeleteIcon />}
                                          fullWidth
                                        >
                                          Delete Account
                                        </Button>
                                      </>
                                    )}
                                  </Box>
                                )}
                                
                                {/* Display account details if available */}
                                {accountStatus.exists && accountStatus.details && (
                                  <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #eee' }}>
                                    <Typography variant="caption" color="text.secondary">
                                      Account Details:
                                    </Typography>
                                    <Typography variant="body2">
                                      ID: {accountStatus.accountId || 'N/A'}
                                    </Typography>
                                    {accountStatus.details.email && (
                                      <Typography variant="body2">
                                        Email: {accountStatus.details.email}
                                      </Typography>
                                    )}
                                  </Box>
                                )}
                              </Card>
                            </Grid>
                          );
                        })}
                      </Grid>
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 3 && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Access Control
                        </Typography>
                        {canEdit && (
                          <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={() => setCardFormOpen(true)}
                            disabled={accessControlLoading}
                          >
                            Assign Card
                          </Button>
                        )}
                      </Box>
                
                      {accessControlError && (
                        <Box sx={{ mb: 3 }}>
                          <Typography color="error">{accessControlError}</Typography>
                        </Box>
                      )}
                
                      {accessControlLoading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                          <CircularProgress />
                        </Box>
                      ) : (
                        <Box>
                          {!accessControlData ? (
                            <Typography variant="body2" color="text.secondary">
                              No access control data available for this user.
                            </Typography>
                          ) : (
                            <>
                              <Typography variant="subtitle1" gutterBottom>
                                Access Control Systems
                              </Typography>
                              <List>
                                {accessControlData.systems && accessControlData.systems.includes('unifi-access') && (
                                  <ListItem>
                                    <ListItemText 
                                      primary="UniFi Access" 
                                      secondary={accessControlData.unifiAccessData?.status === 'active' ? 'Active' : 'Inactive'} 
                                    />
                                    {accessControlData.unifiAccessData?.status === 'active' && (
                                      <Chip 
                                        label="Active" 
                                        color="success" 
                                        size="small" 
                                        sx={{ ml: 1 }} 
                                      />
                                    )}
                                  </ListItem>
                                )}
                                {accessControlData.systems && accessControlData.systems.includes('lenel-s2-netbox') && (
                                  <ListItem>
                                    <ListItemText 
                                      primary="Lenel S2 NetBox" 
                                      secondary={accessControlData.lenelS2NetBoxData?.status === 'active' ? 'Active' : 'Inactive'} 
                                    />
                                    {accessControlData.lenelS2NetBoxData?.status === 'active' && (
                                      <Chip 
                                        label="Active" 
                                        color="success" 
                                        size="small" 
                                        sx={{ ml: 1 }} 
                                      />
                                    )}
                                  </ListItem>
                                )}
                              </List>
                        
                              <Divider sx={{ my: 2 }} />
                        
                              <Typography variant="subtitle1" gutterBottom>
                                Access Levels
                              </Typography>
                              {accessControlData.unifiAccessData?.accessLevels?.length > 0 || 
                               accessControlData.lenelS2NetBoxData?.accessLevels?.length > 0 ? (
                                <List>
                                  {accessControlData.unifiAccessData?.accessLevels?.map((level) => (
                                    <ListItem key={`unifi-${level.id}`}>
                                      <ListItemText 
                                        primary={level.name} 
                                        secondary="UniFi Access" 
                                      />
                                    </ListItem>
                                  ))}
                                  {accessControlData.lenelS2NetBoxData?.accessLevels?.map((level) => (
                                    <ListItem key={`lenel-${level.id}`}>
                                      <ListItemText 
                                        primary={level.name} 
                                        secondary="Lenel S2 NetBox" 
                                      />
                                    </ListItem>
                                  ))}
                                </List>
                              ) : (
                                <Typography variant="body2" color="text.secondary">
                                  No access levels assigned.
                                </Typography>
                              )}
                        
                              <Divider sx={{ my: 2 }} />
                        
                              <Typography variant="subtitle1" gutterBottom>
                                Cards
                              </Typography>
                              {accessControlData.lenelS2NetBoxData?.cards?.length > 0 ? (
                                <List>
                                  {accessControlData.lenelS2NetBoxData.cards.map((card, index) => (
                                    <ListItem key={`card-${index}`}>
                                      <ListItemText 
                                        primary={`Card: ${card.cardNumber}`} 
                                        secondary={`Format: ${card.cardFormat}${card.description ? ` - ${card.description}` : ''}`} 
                                      />
                                    </ListItem>
                                  ))}
                                </List>
                              ) : (
                                <Typography variant="body2" color="text.secondary">
                                  No cards assigned.
                                </Typography>
                              )}
                            </>
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
      
            {tabValue === 4 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    User Interface Settings
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Customize your portal experience
                  </Typography>
                </Box>
                
                {preferencesError && (
                  <Box sx={{ mb: 3 }}>
                    <Typography color="error">{preferencesError}</Typography>
                  </Box>
                )}
                
                {preferencesLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Box>
                    <Card variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1">
                          Quick Links Toolbar
                        </Typography>
                        {quickLinksToolbarEnabled ? (
                          <Chip 
                            icon={<CheckCircleIcon />} 
                            label="Enabled" 
                            color="success" 
                            size="small" 
                          />
                        ) : (
                          <Chip 
                            icon={<CancelCircleIcon />} 
                            label="Disabled" 
                            color="default" 
                            size="small" 
                          />
                        )}
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Show the quick links toolbar at the bottom of the screen for easy access to your favorite links
                      </Typography>
                      
                      <Button
                        variant="outlined"
                        color={quickLinksToolbarEnabled ? "error" : "primary"}
                        size="small"
                        onClick={() => handleToggleQuickLinksToolbar(!quickLinksToolbarEnabled)}
                        disabled={preferencesLoading}
                        fullWidth
                      >
                        {quickLinksToolbarEnabled ? "Disable" : "Enable"} Quick Links Toolbar
                      </Button>
                    </Card>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 5 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Notifications
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Manage how you receive notifications
                  </Typography>
                </Box>
                
                {(!currentUser || currentUser._id !== id) && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      You can view this user's notification preferences, but only the owner can change them.
                    </Typography>
                  </Box>
                )}
                
                {preferencesError && (
                  <Box sx={{ mb: 3 }}>
                    <Typography color="error">{preferencesError}</Typography>
                  </Box>
                )}
                
                {preferencesLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <List>
                    <ListItem>
                      <ListItemText
                        primary="Email notifications"
                        secondary="Receive updates and alerts via email"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          edge="end"
                          color="primary"
                          checked={notificationEmailEnabled}
                          onChange={(e) => handleToggleEmailNotifications(e.target.checked)}
                          disabled={(!currentUser || currentUser._id !== id) || preferencesLoading}
                          inputProps={{ 'aria-label': 'toggle email notifications' }}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>

                    <ListItem>
                      <ListItemText
                        primary="System notifications"
                        secondary="Show in-app notifications in the portal"
                      />
                      <ListItemSecondaryAction>
                        <Switch
                          edge="end"
                          color="primary"
                          checked={notificationSystemEnabled}
                          onChange={(e) => handleToggleSystemNotifications(e.target.checked)}
                          disabled={(!currentUser || currentUser._id !== id) || preferencesLoading}
                          inputProps={{ 'aria-label': 'toggle system notifications' }}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Dialog for adding a skill */}
      <Dialog open={skillDialogOpen} onClose={() => setSkillDialogOpen(false)}>
        <DialogTitle>Add Skill</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Skill"
            fullWidth
            value={newSkill}
            onChange={(e) => setNewSkill(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSkillDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddSkill} color="primary">Add</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog for adding a tag */}
      <Dialog open={tagDialogOpen} onClose={() => setTagDialogOpen(false)}>
        <DialogTitle>Add Tag</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tag"
            fullWidth
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTagDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddTag} color="primary">Add</Button>
        </DialogActions>
      </Dialog>
      
      {/* Card Assignment Dialog */}
      <Dialog open={cardFormOpen} onClose={() => setCardFormOpen(false)}>
        <DialogTitle>Assign Card to User</DialogTitle>
        <form onSubmit={handleCardFormSubmit}>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              name="cardNumber"
              label="Card Number"
              fullWidth
              required
              value={cardFormData.cardNumber}
              onChange={handleCardFormInputChange}
            />
            <FormControl fullWidth margin="dense">
              <InputLabel id="card-format-label">Card Format</InputLabel>
              <Select
                labelId="card-format-label"
                name="cardFormat"
                value={cardFormData.cardFormat}
                onChange={handleCardFormInputChange}
                label="Card Format"
              >
                <MenuItem value="HID">HID</MenuItem>
                <MenuItem value="Wiegand">Wiegand</MenuItem>
                <MenuItem value="Magnetic">Magnetic</MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </Select>
            </FormControl>
            <TextField
              margin="dense"
              name="description"
              label="Description (Optional)"
              fullWidth
              value={cardFormData.description}
              onChange={handleCardFormInputChange}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCardFormOpen(false)}>Cancel</Button>
            <Button 
              type="submit" 
              color="primary"
              disabled={accessControlLoading || !cardFormData.cardNumber}
            >
              {accessControlLoading ? <CircularProgress size={24} /> : 'Assign Card'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </Box>
  );
};

export default UserProfilePage;
