import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  CircularProgress,
  Autocomplete,
  Chip,
  Avatar,
  Switch,
  FormControlLabel,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

const GroupManagementDialog = ({ open, onClose, groupId = null, onSave }) => {
  const { user: currentUser } = useAuth();
  const [group, setGroup] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: true,
    members: [],
    tags: []
  });
  const [newTag, setNewTag] = useState('');
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch group data and users when dialog opens
  useEffect(() => {
    if (open) {
      const fetchData = async () => {
        setLoading(true);
        try {
          // Fetch all users for member selection
          const usersRes = await axios.get('/api/staff-directory/users');
          setUsers(usersRes.data);

          // If editing an existing group, fetch its data
          if (groupId) {
            const groupRes = await axios.get(`/api/staff-directory/groups/${groupId}`);
            setGroup(groupRes.data);
            setFormData({
              name: groupRes.data.name || '',
              description: groupRes.data.description || '',
              isPublic: groupRes.data.isPublic !== undefined ? groupRes.data.isPublic : true,
              members: groupRes.data.members || [],
              tags: groupRes.data.tags || []
            });
          } else {
            // Reset form for new group
            setFormData({
              name: '',
              description: '',
              isPublic: true,
              members: [],
              tags: []
            });
            setGroup(null);
          }

          setError(null);
        } catch (err) {
          console.error('Error fetching data:', err);
          setError('Failed to load data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }
  }, [open, groupId]);

  // Check if current user is the owner or an admin
  const isOwner = group && currentUser && group.owner && group.owner._id === currentUser.id;
  const isAdmin = currentUser && currentUser.roles.includes('admin');
  const canEdit = isOwner || isAdmin;

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle switch change for isPublic
  const handleSwitchChange = (e) => {
    setFormData({
      ...formData,
      isPublic: e.target.checked
    });
  };

  // Handle members selection
  const handleMembersChange = (event, newValue) => {
    setFormData({
      ...formData,
      members: newValue
    });
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
    setTagDialogOpen(false);
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    setLoading(true);

    try {
      const groupData = {
        name: formData.name,
        description: formData.description,
        isPublic: formData.isPublic,
        members: formData.members.map(member => member._id),
        tags: formData.tags
      };

      let response;

      if (!groupId) {
        // Create new group
        response = await axios.post('/api/staff-directory/groups', groupData);
        setSnackbar({
          open: true,
          message: 'Group created successfully',
          severity: 'success'
        });
      } else {
        // Update existing group
        response = await axios.put(`/api/staff-directory/groups/${groupId}`, groupData);
        setSnackbar({
          open: true,
          message: 'Group updated successfully',
          severity: 'success'
        });
      }

      setError(null);
      
      // Call the onSave callback with the updated/created group
      if (onSave) {
        onSave(response.data);
      }
      
      // Close the dialog
      onClose();
    } catch (err) {
      console.error('Error saving group:', err);
      setError('Failed to save group. Please try again later.');
      setSnackbar({
        open: true,
        message: 'Error saving group',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  // Handle dialog close
  const handleClose = () => {
    // Reset form data when dialog closes
    setFormData({
      name: '',
      description: '',
      isPublic: true,
      members: [],
      tags: []
    });
    setError(null);
    onClose();
  };

  return (
    <>
      <Dialog 
        open={open} 
        onClose={handleClose}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {groupId ? 'Edit Group' : 'Create New Group'}
        </DialogTitle>
        
        <DialogContent dividers>
          {loading && !users.length ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {error && (
                <Box sx={{ mb: 2 }}>
                  <Typography color="error">{error}</Typography>
                </Box>
              )}

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Group Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    multiline
                    rows={3}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.isPublic}
                        onChange={handleSwitchChange}
                        name="isPublic"
                        color="primary"
                      />
                    }
                    label="Public Group"
                  />
                  <Typography variant="caption" color="text.secondary" display="block">
                    Public groups are visible to all users. Private groups are only visible to members.
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={users}
                    getOptionLabel={(option) => option.name}
                    value={formData.members}
                    onChange={handleMembersChange}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Group Members"
                        margin="normal"
                      />
                    )}
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            src={option.avatar}
                            alt={option.name}
                            sx={{ width: 24, height: 24, mr: 1 }}
                          />
                          <Typography>{option.name}</Typography>
                          {option.jobTitle && (
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              ({option.jobTitle})
                            </Typography>
                          )}
                        </Box>
                      </li>
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          avatar={<Avatar alt={option.name} src={option.avatar} />}
                          label={option.name}
                          {...getTagProps({ index })}
                        />
                      ))
                    }
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1">Tags</Typography>
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={() => setTagDialogOpen(true)}
                    >
                      Add Tag
                    </Button>
                  </Box>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {formData.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        onDelete={() => handleRemoveTag(tag)}
                        color="primary"
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button 
            onClick={handleClose}
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            variant="contained" 
            color="primary"
            startIcon={<SaveIcon />}
            disabled={loading || !formData.name}
          >
            {groupId ? 'Save Changes' : 'Create Group'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog for adding a tag */}
      <Dialog open={tagDialogOpen} onClose={() => setTagDialogOpen(false)}>
        <DialogTitle>Add Tag</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tag"
            fullWidth
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTagDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddTag} color="primary">Add</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default GroupManagementDialog;