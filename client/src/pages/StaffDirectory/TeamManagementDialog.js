import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  CircularProgress,
  Autocomplete,
  Chip,
  Avatar,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

// Utility function to proxy avatar URLs to avoid CORP issues
const getProxiedAvatarUrl = (avatarUrl) => {
  if (!avatarUrl) return undefined;
  return `/api/auth/avatar-proxy?url=${encodeURIComponent(avatarUrl)}`;
};

const TeamManagementDialog = ({ open, onClose, teamId = null, onSave }) => {
  const { user: currentUser } = useAuth();
  const [team, setTeam] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    leader: null,
    members: [],
    tags: []
  });
  const [newTag, setNewTag] = useState('');
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch team data and users when dialog opens
  useEffect(() => {
    if (open) {
      const fetchData = async () => {
        setLoading(true);
        try {
          // Fetch all users for member selection
          const usersRes = await axios.get('/api/staff-directory/users');
          setUsers(usersRes.data);

          // If editing an existing team, fetch its data
          if (teamId) {
            const teamRes = await axios.get(`/api/staff-directory/teams/${teamId}`);
            setTeam(teamRes.data);
            setFormData({
              name: teamRes.data.name || '',
              description: teamRes.data.description || '',
              leader: teamRes.data.leader || null,
              members: teamRes.data.members || [],
              tags: teamRes.data.tags || []
            });
          } else {
            // Reset form for new team
            setFormData({
              name: '',
              description: '',
              leader: null,
              members: [],
              tags: []
            });
            setTeam(null);
          }

          setError(null);
        } catch (err) {
          console.error('Error fetching data:', err);
          setError('Failed to load data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }
  }, [open, teamId]);

  // Check if current user is an admin
  const isAdmin = currentUser && currentUser.roles.includes('admin');

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle leader selection
  const handleLeaderChange = (event, newValue) => {
    setFormData({
      ...formData,
      leader: newValue
    });
  };

  // Handle members selection
  const handleMembersChange = (event, newValue) => {
    setFormData({
      ...formData,
      members: newValue
    });
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
    setTagDialogOpen(false);
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    setLoading(true);

    try {
      const teamData = {
        name: formData.name,
        description: formData.description,
        leader: formData.leader ? formData.leader._id : null,
        members: formData.members.map(member => member._id),
        tags: formData.tags
      };

      let response;

      if (!teamId) {
        // Create new team
        response = await axios.post('/api/staff-directory/teams', teamData);
        setSnackbar({
          open: true,
          message: 'Team created successfully',
          severity: 'success'
        });
      } else {
        // Update existing team
        response = await axios.put(`/api/staff-directory/teams/${teamId}`, teamData);
        setSnackbar({
          open: true,
          message: 'Team updated successfully',
          severity: 'success'
        });
      }

      setError(null);
      
      // Call the onSave callback with the updated/created team
      if (onSave) {
        onSave(response.data);
      }
      
      // Close the dialog
      onClose();
    } catch (err) {
      console.error('Error saving team:', err);
      setError('Failed to save team. Please try again later.');
      setSnackbar({
        open: true,
        message: 'Error saving team',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  // Handle dialog close
  const handleClose = () => {
    // Reset form data when dialog closes
    setFormData({
      name: '',
      description: '',
      leader: null,
      members: [],
      tags: []
    });
    setError(null);
    onClose();
  };

  // Check if user has permission to manage teams
  if (!isAdmin) {
    return (
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Permission Denied</DialogTitle>
        <DialogContent>
          <Typography>You don't have permission to manage teams.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <>
      <Dialog 
        open={open} 
        onClose={handleClose}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {teamId ? 'Edit Team' : 'Create New Team'}
        </DialogTitle>
        
        <DialogContent dividers>
          {loading && !users.length ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {error && (
                <Box sx={{ mb: 2 }}>
                  <Typography color="error">{error}</Typography>
                </Box>
              )}

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Team Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    multiline
                    rows={3}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Autocomplete
                    options={users}
                    getOptionLabel={(option) => option.name}
                    value={formData.leader}
                    onChange={handleLeaderChange}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Team Leader"
                        margin="normal"
                      />
                    )}
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            src={getProxiedAvatarUrl(option.avatar)}
                            alt={option.name}
                            sx={{ width: 24, height: 24, mr: 1 }}
                          />
                          <Typography>{option.name}</Typography>
                          {option.jobTitle && (
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              ({option.jobTitle})
                            </Typography>
                          )}
                        </Box>
                      </li>
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={users}
                    getOptionLabel={(option) => option.name}
                    value={formData.members}
                    onChange={handleMembersChange}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Team Members"
                        margin="normal"
                      />
                    )}
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            src={getProxiedAvatarUrl(option.avatar)}
                            alt={option.name}
                            sx={{ width: 24, height: 24, mr: 1 }}
                          />
                          <Typography>{option.name}</Typography>
                          {option.jobTitle && (
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              ({option.jobTitle})
                            </Typography>
                          )}
                        </Box>
                      </li>
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          avatar={<Avatar alt={option.name} src={getProxiedAvatarUrl(option.avatar)} />}
                          label={option.name}
                          {...getTagProps({ index })}
                        />
                      ))
                    }
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1">Tags</Typography>
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={() => setTagDialogOpen(true)}
                    >
                      Add Tag
                    </Button>
                  </Box>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {formData.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        onDelete={() => handleRemoveTag(tag)}
                        color="primary"
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>
            </>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button 
            onClick={handleClose}
            startIcon={<CancelIcon />}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            variant="contained" 
            color="primary"
            startIcon={<SaveIcon />}
            disabled={loading || !formData.name}
          >
            {teamId ? 'Save Changes' : 'Create Team'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog for adding a tag */}
      <Dialog open={tagDialogOpen} onClose={() => setTagDialogOpen(false)}>
        <DialogTitle>Add Tag</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tag"
            fullWidth
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTagDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddTag} color="primary">Add</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default TeamManagementDialog;