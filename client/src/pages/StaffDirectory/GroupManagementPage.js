import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  Chip,
  Avatar,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Snackbar,
  Alert,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';

const GroupManagementPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const [group, setGroup] = useState(null);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editing, setEditing] = useState(id === 'new');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: true,
    members: [],
    tags: []
  });
  const [newTag, setNewTag] = useState('');
  const [tagDialogOpen, setTagDialogOpen] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch group data and users
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch all users for member selection
        const usersRes = await axios.get('/api/staff-directory/users');
        setUsers(usersRes.data);

        // If editing an existing group, fetch its data
        if (id && id !== 'new') {
          const groupRes = await axios.get(`/api/staff-directory/groups/${id}`);
          setGroup(groupRes.data);
          setFormData({
            name: groupRes.data.name || '',
            description: groupRes.data.description || '',
            isPublic: groupRes.data.isPublic !== undefined ? groupRes.data.isPublic : true,
            members: groupRes.data.members || [],
            tags: groupRes.data.tags || []
          });
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Check if current user is the owner or an admin
  const isOwner = group && currentUser && group.owner && group.owner._id === currentUser.id;
  const isAdmin = currentUser && currentUser.roles.includes('admin');
  const canEdit = isOwner || isAdmin;

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle switch change for isPublic
  const handleSwitchChange = (e) => {
    setFormData({
      ...formData,
      isPublic: e.target.checked
    });
  };

  // Handle members selection
  const handleMembersChange = (event, newValue) => {
    setFormData({
      ...formData,
      members: newValue
    });
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
    setTagDialogOpen(false);
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const groupData = {
        name: formData.name,
        description: formData.description,
        isPublic: formData.isPublic,
        members: formData.members.map(member => member._id),
        tags: formData.tags
      };

      let response;

      if (id === 'new') {
        // Create new group
        response = await axios.post('/api/staff-directory/groups', groupData);
        setSnackbar({
          open: true,
          message: 'Group created successfully',
          severity: 'success'
        });

        // Navigate to the group page in edit mode
        navigate(`/staff-directory/groups/${response.data._id}`);
        // The page will reload with the new ID, so we don't need to set editing state here
      } else {
        // Update existing group
        response = await axios.put(`/api/staff-directory/groups/${id}`, groupData);
        setSnackbar({
          open: true,
          message: 'Group updated successfully',
          severity: 'success'
        });

        setGroup(response.data);
        setEditing(false);
      }

      setError(null);
    } catch (err) {
      console.error('Error saving group:', err);
      setError('Failed to save group. Please try again later.');
      setSnackbar({
        open: true,
        message: 'Error saving group',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle group deletion
  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this group?')) {
      return;
    }

    setLoading(true);
    try {
      await axios.delete(`/api/staff-directory/groups/${id}`);
      setSnackbar({
        open: true,
        message: 'Group deleted successfully',
        severity: 'success'
      });
      navigate('/staff-directory');
    } catch (err) {
      console.error('Error deleting group:', err);
      setError('Failed to delete group. Please try again later.');
      setSnackbar({
        open: true,
        message: 'Error deleting group',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading && !group && id !== 'new') {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !group && id !== 'new') {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">{error}</Typography>
        <Button variant="contained" onClick={() => navigate('/staff-directory')} sx={{ mt: 2 }}>
          Back to Staff Directory
        </Button>
      </Box>
    );
  }

  if (!canEdit && id !== 'new') {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>You don't have permission to manage this group.</Typography>
        <Button variant="contained" onClick={() => navigate('/staff-directory')} sx={{ mt: 2 }}>
          Back to Staff Directory
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {id === 'new' ? 'Create New Group' : 'Group Management'}
        </Typography>
        <Box>
          {!editing && id !== 'new' && canEdit && (
            <>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => setEditing(true)}
                sx={{ mr: 1 }}
              >
                Edit
              </Button>
              <Button
                variant="outlined"
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleDelete}
                sx={{ mr: 1 }}
              >
                Delete
              </Button>
            </>
          )}
          <Button
            variant="contained"
            onClick={() => navigate('/staff-directory')}
          >
            Back to Directory
          </Button>
        </Box>
      </Box>

      {error && (
        <Box sx={{ mb: 3 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}

      <Card>
        <CardContent>
          {editing ? (
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Group Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    multiline
                    rows={3}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.isPublic}
                        onChange={handleSwitchChange}
                        name="isPublic"
                        color="primary"
                      />
                    }
                    label="Public Group"
                  />
                  <Typography variant="caption" color="text.secondary" display="block">
                    Public groups are visible to all users. Private groups are only visible to members.
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <Autocomplete
                    multiple
                    options={users}
                    getOptionLabel={(option) => option.name}
                    value={formData.members}
                    onChange={handleMembersChange}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Group Members"
                        margin="normal"
                      />
                    )}
                    renderOption={(props, option) => (
                      <li {...props}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            src={option.avatar}
                            alt={option.name}
                            sx={{ width: 24, height: 24, mr: 1 }}
                          />
                          <Typography>{option.name}</Typography>
                          {option.jobTitle && (
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              ({option.jobTitle})
                            </Typography>
                          )}
                        </Box>
                      </li>
                    )}
                    renderTags={(value, getTagProps) =>
                      value.map((option, index) => (
                        <Chip
                          avatar={<Avatar alt={option.name} src={option.avatar} />}
                          label={option.name}
                          {...getTagProps({ index })}
                        />
                      ))
                    }
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle1">Tags</Typography>
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={() => setTagDialogOpen(true)}
                    >
                      Add Tag
                    </Button>
                  </Box>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {formData.tags.map((tag, index) => (
                      <Chip
                        key={index}
                        label={tag}
                        onDelete={() => handleRemoveTag(tag)}
                        color="primary"
                      />
                    ))}
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                {id !== 'new' && (
                  <Button
                    variant="outlined"
                    startIcon={<CancelIcon />}
                    onClick={() => {
                      setEditing(false);
                      if (group) {
                        setFormData({
                          name: group.name || '',
                          description: group.description || '',
                          isPublic: group.isPublic !== undefined ? group.isPublic : true,
                          members: group.members || [],
                          tags: group.tags || []
                        });
                      }
                    }}
                  >
                    Cancel
                  </Button>
                )}
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={loading}
                >
                  {id === 'new' ? 'Create Group' : 'Save Changes'}
                </Button>
              </Box>
            </form>
          ) : (
            <>
              {group && (
                <>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h5" gutterBottom>
                      {group.name}
                    </Typography>
                    {!group.isPublic && (
                      <Chip label="Private" size="small" />
                    )}
                  </Box>

                  {group.description && (
                    <Typography variant="body1" paragraph>
                      {group.description}
                    </Typography>
                  )}

                  <Divider sx={{ my: 2 }} />

                  {group.owner && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h6" gutterBottom>
                        Owner
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={group.owner.avatar}
                          alt={group.owner.name}
                          sx={{ width: 40, height: 40, mr: 2 }}
                        />
                        <Box>
                          <Typography variant="subtitle1">
                            {group.owner.name}
                          </Typography>
                          {group.owner.jobTitle && (
                            <Typography variant="body2" color="text.secondary">
                              {group.owner.jobTitle}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </Box>
                  )}

                  <Typography variant="h6" gutterBottom>
                    Group Members ({group.members ? group.members.length : 0})
                  </Typography>

                  {group.members && group.members.length > 0 ? (
                    <TableContainer component={Paper} sx={{ mb: 3 }}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Name</TableCell>
                            <TableCell>Job Title</TableCell>
                            <TableCell>Department</TableCell>
                            <TableCell>Email</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {group.members.map((member) => (
                            <TableRow key={member._id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar
                                    src={member.avatar}
                                    alt={member.name}
                                    sx={{ width: 24, height: 24, mr: 1 }}
                                  />
                                  <Typography>{member.name}</Typography>
                                </Box>
                              </TableCell>
                              <TableCell>{member.jobTitle || '-'}</TableCell>
                              <TableCell>{member.department || '-'}</TableCell>
                              <TableCell>{member.email}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      No members in this group
                    </Typography>
                  )}

                  {group.tags && group.tags.length > 0 && (
                    <>
                      <Typography variant="h6" gutterBottom>
                        Tags
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {group.tags.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            color="primary"
                          />
                        ))}
                      </Box>
                    </>
                  )}
                </>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog for adding a tag */}
      <Dialog open={tagDialogOpen} onClose={() => setTagDialogOpen(false)}>
        <DialogTitle>Add Tag</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tag"
            fullWidth
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTagDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddTag} color="primary">Add</Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default GroupManagementPage;