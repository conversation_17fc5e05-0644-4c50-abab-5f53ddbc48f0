import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Paper, 
  Tabs, 
  Tab, 
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid,
  Slider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { 
  Info as InfoIcon,
  Settings as SettingsIcon,
  Videocam as CameraIcon,
  PhotoCamera as SnapshotIcon,
  ControlCamera as PTZIcon,
  ZoomIn as ZoomIcon,
  Save as SaveIcon,
  Bookmark as PresetIcon
} from '@mui/icons-material';
import panasonicService from '../../services/panasonicService';
import { useAuth } from '../../context/AuthContext';

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`panasonic-tabpanel-${index}`}
      aria-labelledby={`panasonic-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const PanasonicPage = () => {
  const { hasPermission } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [cameraInfo, setCameraInfo] = useState(null);
  const [cameraStatus, setCameraStatus] = useState(null);
  const [presets, setPresets] = useState([]);
  const [snapshot, setSnapshot] = useState(null);
  const [configStatus, setConfigStatus] = useState(null);
  const [loadingConfig, setLoadingConfig] = useState(true);
  const [configForm, setConfigForm] = useState({
    host: '',
    port: 80,
    username: '',
    password: ''
  });
  const [ptzValues, setPtzValues] = useState({
    pan: 0,
    tilt: 0,
    zoom: 0
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Fetch configuration status on component mount
  useEffect(() => {
    const fetchConfigStatus = async () => {
      try {
        const config = await panasonicService.getConfig();
        setConfigStatus(config);
        if (config && config.host) {
          setConfigForm({
            host: config.host,
            port: config.port || 80,
            username: config.username || '',
            password: ''
          });
        }
      } catch (err) {
        console.error('Error fetching configuration status:', err);
      } finally {
        setLoadingConfig(false);
      }
    };

    fetchConfigStatus();
  }, []);

  // Initialize camera API when configuration is loaded
  useEffect(() => {
    const initializeCamera = async () => {
      if (!configStatus || !configStatus.host) return;

      try {
        await panasonicService.initialize();
      } catch (err) {
        console.error('Error initializing camera:', err);
        setError('Failed to initialize camera. Please check your connection and try again.');
      }
    };

    initializeCamera();
  }, [configStatus]);

  // Load data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      if (!configStatus || !configStatus.host) return;

      setLoading(true);
      setError(null);

      try {
        switch (tabValue) {
          case 0: // Camera Info
            const infoData = await panasonicService.getCameraInfo();
            setCameraInfo(infoData);
            break;
          case 1: // Camera Status
            const statusData = await panasonicService.getCameraStatus();
            setCameraStatus(statusData);
            break;
          case 2: // Presets
            const presetsData = await panasonicService.getCameraPresets();
            setPresets(presetsData);
            break;
          case 3: // PTZ Control
            // No data to fetch for PTZ control tab
            break;
          case 4: // Snapshot
            const snapshotData = await panasonicService.getSnapshot();
            setSnapshot(snapshotData);
            break;
          default:
            break;
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please check your connection and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [tabValue, configStatus]);

  // Handle preset selection
  const handlePresetSelect = async (presetId) => {
    setLoading(true);
    try {
      await panasonicService.moveToPreset(presetId);
      setError(null);
    } catch (err) {
      console.error(`Error moving to preset ${presetId}:`, err);
      setError(`Failed to move to preset ${presetId}.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle PTZ control
  const handlePTZChange = (axis, value) => {
    setPtzValues({
      ...ptzValues,
      [axis]: value
    });
  };

  // Apply PTZ changes
  const applyPTZChanges = async () => {
    setLoading(true);
    try {
      await panasonicService.controlPTZ(ptzValues);
      setError(null);
    } catch (err) {
      console.error('Error controlling PTZ:', err);
      setError('Failed to control PTZ. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle zoom control
  const handleZoomChange = async (value) => {
    setLoading(true);
    try {
      await panasonicService.controlZoom({ zoom: value });
      setPtzValues({
        ...ptzValues,
        zoom: value
      });
      setError(null);
    } catch (err) {
      console.error('Error controlling zoom:', err);
      setError('Failed to control zoom. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Refresh snapshot
  const refreshSnapshot = async () => {
    setLoading(true);
    try {
      const snapshotData = await panasonicService.getSnapshot();
      setSnapshot(snapshotData);
      setError(null);
    } catch (err) {
      console.error('Error fetching snapshot:', err);
      setError('Failed to fetch snapshot. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle config form change
  const handleConfigFormChange = (e) => {
    const { name, value } = e.target;
    setConfigForm({
      ...configForm,
      [name]: value
    });
  };

  // Handle config save
  const handleConfigSave = async () => {
    setLoading(true);
    try {
      await panasonicService.saveConfig(configForm);
      const config = await panasonicService.getConfig();
      setConfigStatus(config);
      setError(null);
    } catch (err) {
      console.error('Error saving configuration:', err);
      setError('Failed to save configuration. Please check your inputs and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Render configuration form
  const renderConfigForm = () => (
    <Box component="form" sx={{ mt: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Host"
            name="host"
            value={configForm.host}
            onChange={handleConfigFormChange}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Port"
            name="port"
            type="number"
            value={configForm.port}
            onChange={handleConfigFormChange}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Username"
            name="username"
            value={configForm.username}
            onChange={handleConfigFormChange}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Password"
            name="password"
            type="password"
            value={configForm.password}
            onChange={handleConfigFormChange}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleConfigSave}
              disabled={loading}
              startIcon={<SaveIcon />}
            >
              Save Configuration
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  // Render camera information
  const renderCameraInfo = () => {
    if (!cameraInfo) return <Typography>No camera information available.</Typography>;

    return (
      <List>
        {Object.entries(cameraInfo).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <InfoIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render camera status
  const renderCameraStatus = () => {
    if (!cameraStatus) return <Typography>No camera status information available.</Typography>;

    return (
      <List>
        {Object.entries(cameraStatus).map(([key, value]) => (
          <ListItem key={key}>
            <ListItemIcon>
              <CameraIcon />
            </ListItemIcon>
            <ListItemText
              primary={key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
              secondary={typeof value === 'object' ? JSON.stringify(value) : value.toString()}
            />
          </ListItem>
        ))}
      </List>
    );
  };

  // Render presets
  const renderPresets = () => {
    if (!presets || presets.length === 0) return <Typography>No presets available.</Typography>;

    return (
      <Grid container spacing={2}>
        {presets.map((preset) => (
          <Grid item xs={12} sm={6} md={4} key={preset.id}>
            <Card>
              <CardContent>
                <Typography variant="h6">
                  {preset.name || `Preset ${preset.id}`}
                </Typography>
                <Typography color="textSecondary" gutterBottom>
                  ID: {preset.id}
                </Typography>
              </CardContent>
              <CardActions>
                <Button 
                  size="small" 
                  color="primary"
                  onClick={() => handlePresetSelect(preset.id)}
                  startIcon={<PresetIcon />}
                >
                  Move to Preset
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render PTZ control
  const renderPTZControl = () => {
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Pan/Tilt/Zoom Control
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Typography id="pan-slider" gutterBottom>
              Pan
            </Typography>
            <Slider
              aria-labelledby="pan-slider"
              value={ptzValues.pan}
              onChange={(e, value) => handlePTZChange('pan', value)}
              min={-100}
              max={100}
              marks={[
                { value: -100, label: 'Left' },
                { value: 0, label: 'Center' },
                { value: 100, label: 'Right' }
              ]}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Typography id="tilt-slider" gutterBottom>
              Tilt
            </Typography>
            <Slider
              aria-labelledby="tilt-slider"
              value={ptzValues.tilt}
              onChange={(e, value) => handlePTZChange('tilt', value)}
              min={-100}
              max={100}
              marks={[
                { value: -100, label: 'Down' },
                { value: 0, label: 'Center' },
                { value: 100, label: 'Up' }
              ]}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={12}>
            <Typography id="zoom-slider" gutterBottom>
              Zoom
            </Typography>
            <Slider
              aria-labelledby="zoom-slider"
              value={ptzValues.zoom}
              onChange={(e, value) => handlePTZChange('zoom', value)}
              onChangeCommitted={(e, value) => handleZoomChange(value)}
              min={-100}
              max={100}
              marks={[
                { value: -100, label: 'Wide' },
                { value: 0, label: 'Middle' },
                { value: 100, label: 'Tele' }
              ]}
              valueLabelDisplay="auto"
            />
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="contained"
              color="primary"
              onClick={applyPTZChanges}
              disabled={loading}
              startIcon={<PTZIcon />}
            >
              Apply PTZ Changes
            </Button>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render snapshot
  const renderSnapshot = () => {
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Camera Snapshot
        </Typography>
        <Box sx={{ mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={refreshSnapshot}
            disabled={loading}
            startIcon={<SnapshotIcon />}
          >
            Refresh Snapshot
          </Button>
        </Box>
        {snapshot ? (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <img 
              src={`data:image/jpeg;base64,${snapshot.data}`} 
              alt="Camera Snapshot" 
              style={{ maxWidth: '100%', maxHeight: '500px' }}
            />
            <Typography variant="caption" display="block" sx={{ mt: 1 }}>
              Captured at: {snapshot.timestamp || new Date().toLocaleString()}
            </Typography>
          </Box>
        ) : (
          <Typography>No snapshot available. Click the button above to capture a snapshot.</Typography>
        )}
      </Box>
    );
  };

  // If configuration is not set up, show configuration form
  if (!loadingConfig && (!configStatus || !configStatus.host)) {
    return (
      <Container maxWidth="lg">
        <Typography variant="h4" component="h1" gutterBottom>
          Panasonic Pro AV Camera
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" component="h2" gutterBottom>
            Configuration Required
          </Typography>
          <Typography paragraph>
            Please configure your Panasonic Pro AV Camera connection settings below.
          </Typography>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {renderConfigForm()}
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Panasonic Pro AV Camera
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Camera Info" icon={<InfoIcon />} />
          <Tab label="Camera Status" icon={<CameraIcon />} />
          <Tab label="Presets" icon={<PresetIcon />} />
          <Tab label="PTZ Control" icon={<PTZIcon />} />
          <Tab label="Snapshot" icon={<SnapshotIcon />} />
          <Tab label="Settings" icon={<SettingsIcon />} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderCameraInfo()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderCameraStatus()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderPresets()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderPTZControl()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            renderSnapshot()
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          <Typography variant="h6" gutterBottom>
            Panasonic Pro AV Camera Configuration
          </Typography>
          {renderConfigForm()}
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default PanasonicPage;