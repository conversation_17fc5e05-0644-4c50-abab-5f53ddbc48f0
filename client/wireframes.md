# CSF Portal Wireframes

This document outlines the wireframes for all major sections of the CSF Portal, with a focus on responsive design considerations.

## Table of Contents
- [Dashboard](#dashboard)
- [Planning Center Integration](#planning-center-integration)
- [Synology Integration](#synology-integration)
- [Canva Integration](#canva-integration)
- [Google Drive Integration](#google-drive-integration)
- [GLPI Asset Management](#glpi-asset-management)
- [Mosyle Business Integration](#mosyle-business-integration)
- [Dreo Portable AC Unit Integration](#dreo-portable-ac-unit-integration)
- [UniFi Protect Integration](#unifi-protect-integration)
- [UniFi Access Integration](#unifi-access-integration)
- [UniFi Network Integration](#unifi-network-integration)
- [Lenel S2 NetBox Integration](#lenel-s2-netbox-integration)
- [Help & Documentation](#help--documentation)
- [User Profile](#user-profile)
- [Admin Panel](#admin-panel)

## Dashboard

### Desktop View (≥1024px)
- **Layout**: 3-column grid of shortcut cards
- **Navigation**: Left sidebar with full text labels
- **Header**: Full-width with logo, search, and user menu
- **Content**: Large cards with icons and text

### Tablet View (768px-1023px)
- **Layout**: 2-column grid of shortcut cards
- **Navigation**: Collapsible left sidebar with icons and tooltips
- **Header**: Full-width with condensed elements
- **Content**: Medium-sized cards

### Mobile View (≤767px)
- **Layout**: 1-column grid of shortcut cards
- **Navigation**: Bottom navigation bar or hamburger menu
- **Header**: Simplified with logo and menu button
- **Content**: Full-width cards with minimal padding

## Planning Center Integration

### Desktop View (≥1024px)
- **Layout**: Split view with navigation on left, content on right
- **Calendar**: Month view with detailed event information
- **People**: Table view with multiple columns
- **Resources**: Grid view with filtering options

### Tablet View (768px-1023px)
- **Layout**: Tabbed interface for different sections
- **Calendar**: Week view with collapsible event details
- **People**: Table with fewer visible columns, horizontal scrolling
- **Resources**: 2-column grid with filtering dropdown

### Mobile View (≤767px)
- **Layout**: Stacked sections with accordion expansion
- **Calendar**: List view of upcoming events
- **People**: Card view with essential information
- **Resources**: 1-column list with search at top

## Synology Integration

### Desktop View (≥1024px)
- **Layout**: File explorer with folder tree on left, file list on right
- **Actions**: Full toolbar with all file operations
- **Preview**: Inline file preview with details panel

### Tablet View (768px-1023px)
- **Layout**: Collapsible folder tree, file grid/list toggle
- **Actions**: Dropdown menus for less common operations
- **Preview**: Modal file preview

### Mobile View (≤767px)
- **Layout**: Breadcrumb navigation, file list only
- **Actions**: Context menu on long-press
- **Preview**: Full-screen file preview

## Canva Integration

### Desktop View (≥1024px)
- **Layout**: Gallery view with filtering sidebar
- **Design Browser**: 4-column grid of design thumbnails
- **User Management**: Table view with inline actions

### Tablet View (768px-1023px)
- **Layout**: Filtering options in dropdown/accordion
- **Design Browser**: 3-column grid
- **User Management**: Simplified table with expandable rows

### Mobile View (≤767px)
- **Layout**: Filter button opening modal
- **Design Browser**: 2-column grid
- **User Management**: Card view with action buttons

## Google Drive Integration

### Desktop View (≥1024px)
- **Layout**: Similar to Synology with folder/file structure
- **Document Viewer**: Embedded viewer with toolbar
- **Search**: Advanced search with multiple filters

### Tablet View (768px-1023px)
- **Layout**: Simplified navigation with breadcrumbs
- **Document Viewer**: Full-width viewer with minimal controls
- **Search**: Search bar with expandable advanced options

### Mobile View (≤767px)
- **Layout**: List view with folder navigation
- **Document Viewer**: Full-screen viewer with gesture controls
- **Search**: Simple search with type filters

## GLPI Asset Management

### Desktop View (≥1024px)
- **Layout**: Dashboard with statistics and quick access
- **Asset Browser**: Table view with filtering and sorting
- **Asset Details**: Two-column layout with specifications and history

### Tablet View (768px-1023px)
- **Layout**: Simplified dashboard with key metrics
- **Asset Browser**: Table with horizontal scroll or fewer columns
- **Asset Details**: Tabbed interface for different information sections

### Mobile View (≤767px)
- **Layout**: Card-based dashboard
- **Asset Browser**: List view with essential information
- **Asset Details**: Stacked sections with collapsible panels

## Mosyle Business Integration

### Desktop View (≥1024px)
- **Layout**: Dashboard with device status overview
- **Device List**: Table with detailed information and bulk actions
- **Device Details**: Comprehensive view with tabs for different settings

### Tablet View (768px-1023px)
- **Layout**: Status cards with key metrics
- **Device List**: Simplified table with essential columns
- **Device Details**: Scrollable interface with section navigation

### Mobile View (≤767px)
- **Layout**: Status summary cards
- **Device List**: Card view with device thumbnails
- **Device Details**: Accordion sections for different settings

## Dreo Portable AC Unit Integration

### Desktop View (≥1024px)
- **Layout**: Visual floor plan with unit locations
- **Controls**: Detailed control panel with all settings
- **Statistics**: Charts for temperature and usage history

### Tablet View (768px-1023px)
- **Layout**: List of units with status indicators
- **Controls**: Simplified control panel with common settings
- **Statistics**: Basic charts with toggle options

### Mobile View (≤767px)
- **Layout**: Card view of units with quick controls
- **Controls**: Essential controls with sliders
- **Statistics**: Summary statistics with option to view detailed charts

## UniFi Protect Integration

### Desktop View (≥1024px)
- **Layout**: Grid view of camera feeds with sidebar navigation
- **Camera View**: Large video feed with controls and information panel
- **Events**: Timeline view with filtering options

### Tablet View (768px-1023px)
- **Layout**: 2x2 grid of camera feeds with pagination
- **Camera View**: Full-width video with collapsible information
- **Events**: List view with date grouping

### Mobile View (≤767px)
- **Layout**: Single camera view with carousel navigation
- **Camera View**: Full-screen video with overlay controls
- **Events**: Chronological list with minimal details

## UniFi Access Integration

### Desktop View (≥1024px)
- **Layout**: Map view of access points with status sidebar
- **Door Controls**: Grid of doors with status and quick actions
- **Access Logs**: Detailed table with filtering and search

### Tablet View (768px-1023px)
- **Layout**: List view of access points with status indicators
- **Door Controls**: List of doors with status and actions
- **Access Logs**: Simplified table with essential information

### Mobile View (≤767px)
- **Layout**: Card view of access points
- **Door Controls**: Large button interface for each door
- **Access Logs**: Recent activity feed with load more option

## UniFi Network Integration

### Desktop View (≥1024px)
- **Layout**: Network topology map with device details sidebar
- **Device Management**: Table view with status and performance metrics
- **Client List**: Detailed table with connection information

### Tablet View (768px-1023px)
- **Layout**: Simplified network diagram or list view
- **Device Management**: Card view with key metrics
- **Client List**: Table with essential columns

### Mobile View (≤767px)
- **Layout**: List of network segments
- **Device Management**: Card view with status indicators
- **Client List**: Simple list with connection status

## Lenel S2 NetBox Integration

### Desktop View (≥1024px)
- **Layout**: Security dashboard with status overview
- **Access Control**: Interactive map of access points
- **Alarm Management**: Table view with priority indicators

### Tablet View (768px-1023px)
- **Layout**: Status cards for different security zones
- **Access Control**: List view of access points with status
- **Alarm Management**: Grouped list by priority

### Mobile View (≤767px)
- **Layout**: Critical status indicators at top
- **Access Control**: Simple list with essential controls
- **Alarm Management**: Notification-style list with actions

## Help & Documentation

### Desktop View (≥1024px)
- **Layout**: Sidebar navigation with content area
- **Search**: Prominent search bar with filters
- **Content**: Rich text with images and embedded videos

### Tablet View (768px-1023px)
- **Layout**: Collapsible navigation with content area
- **Search**: Full-width search at top
- **Content**: Responsive content with scaled images

### Mobile View (≤767px)
- **Layout**: Bottom navigation or hamburger menu
- **Search**: Search button opening full-screen search
- **Content**: Simplified content optimized for small screens

## User Profile

### Desktop View (≥1024px)
- **Layout**: Two-column layout with navigation and content
- **Profile Info**: Detailed information with inline editing
- **Preferences**: Grouped settings with descriptions

### Tablet View (768px-1023px)
- **Layout**: Tabbed interface for different sections
- **Profile Info**: Form layout with save button
- **Preferences**: Toggle switches with minimal descriptions

### Mobile View (≤767px)
- **Layout**: Stacked sections with accordion expansion
- **Profile Info**: Simple form with large input fields
- **Preferences**: Simplified options with icons

## Admin Panel

### Desktop View (≥1024px)
- **Layout**: Sidebar navigation with dashboard overview
- **User Management**: Table view with inline actions
- **System Settings**: Grouped settings with detailed options

### Tablet View (768px-1023px)
- **Layout**: Tabbed interface with icon navigation
- **User Management**: Table with fewer columns
- **System Settings**: Categorized settings in cards

### Mobile View (≤767px)
- **Layout**: Bottom navigation or hamburger menu
- **User Management**: Card view with action buttons
- **System Settings**: Single setting group per screen

## Responsive Design Principles

1. **Mobile-First Approach**: Design for mobile first, then enhance for larger screens
2. **Flexible Grids**: Use percentage-based widths instead of fixed pixels
3. **Media Queries**: Implement breakpoints at 768px and 1024px
4. **Touch-Friendly**: Ensure all interactive elements are at least 44x44px on mobile
5. **Progressive Disclosure**: Show less information on smaller screens, with options to expand
6. **Performance Optimization**: Lazy-load images and optimize assets for mobile networks
7. **Accessibility**: Maintain WCAG 2.1 AA compliance across all screen sizes
8. **Testing**: Test on actual devices, not just browser resizing