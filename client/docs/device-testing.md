# Device Testing Guide for CSF Portal

This document outlines the process for testing the CSF Portal on multiple devices and screen sizes to ensure a consistent and responsive user experience.

## Test Devices Matrix

| Device Category | Device Name | Screen Size | Resolution | Browser(s) |
|----------------|-------------|-------------|------------|------------|
| **Desktop**    | Large Monitor | 27"+ | 2560x1440 or higher | Chrome, Firefox, Safari, Edge |
|                | Standard Monitor | 22-24" | 1920x1080 | Chrome, Firefox, Safari, Edge |
|                | Laptop | 13-15" | 1366x768 | Chrome, Firefox, Safari, Edge |
| **Tablet**     | iPad Pro | 12.9" | 2732x2048 | Safari, Chrome |
|                | iPad | 10.2" | 2160x1620 | Safari, Chrome |
|                | Android Tablet | 10" | 1920x1200 | Chrome, Firefox |
| **Mobile**     | iPhone 13 Pro Max | 6.7" | 1284x2778 | Safari, Chrome |
|                | iPhone SE | 4.7" | 750x1334 | Safari |
|                | Samsung Galaxy S21 | 6.2" | 1080x2400 | Chrome, Samsung Internet |
|                | Google Pixel 6 | 6.4" | 1080x2400 | Chrome |

## Testing Checklist

For each device in the matrix, test the following aspects:

### Layout and Responsiveness

- [ ] All content is visible and properly aligned
- [ ] No horizontal scrolling is required (except for tables or other elements designed to scroll horizontally)
- [ ] Text is readable without zooming
- [ ] Touch targets (buttons, links, etc.) are at least 44x44px on mobile devices
- [ ] Spacing between elements is appropriate for the screen size
- [ ] Images scale appropriately and maintain aspect ratio
- [ ] Grid layouts adjust correctly based on screen size
- [ ] Navigation elements (sidebar, hamburger menu, bottom nav) display correctly for the device
- [ ] Forms and input fields are usable and properly sized

### Functionality

- [ ] All interactive elements are functional
- [ ] Touch gestures work as expected on touch devices
- [ ] Hover states are properly handled (desktop) or have touch equivalents (mobile/tablet)
- [ ] Keyboard navigation works correctly on desktop
- [ ] Form validation works correctly
- [ ] Modals and dialogs display correctly and can be dismissed
- [ ] Dropdowns and select menus work properly
- [ ] File uploads work correctly
- [ ] Search functionality works as expected

### Performance

- [ ] Page load times are acceptable (under 3 seconds)
- [ ] Scrolling is smooth without jank
- [ ] Animations and transitions are smooth
- [ ] No memory leaks or excessive CPU/GPU usage
- [ ] Battery usage is reasonable (for mobile devices)
- [ ] Network requests are optimized for mobile connections

### Integration-Specific Testing

For each integration, test the following:

#### Planning Center Integration
- [ ] Calendar views adapt appropriately to screen size
- [ ] Event details are accessible on all devices
- [ ] People management features work on all devices

#### Synology Integration
- [ ] File browser is usable on all devices
- [ ] File preview works correctly
- [ ] Upload/download functionality works on all devices

#### UniFi Protect Integration
- [ ] Camera feeds load and display correctly
- [ ] Camera controls are accessible and functional
- [ ] Event timeline is navigable on all devices

#### Lenel S2 NetBox Integration
- [ ] Security dashboard is readable on all devices
- [ ] Door controls are accessible and functional
- [ ] Alarm management works correctly

## Testing Process

1. **Setup Testing Environment**
   - Ensure all test devices are available and charged
   - Clear browser caches before testing
   - Use real devices whenever possible (not just browser emulation)

2. **Systematic Testing**
   - Start with the smallest screen size and work up to larger screens
   - Test each page of the application using the checklist above
   - Document any issues with screenshots and detailed descriptions

3. **Cross-Browser Testing**
   - Test on multiple browsers for each device category
   - Pay special attention to Safari on iOS and Chrome on Android

4. **Regression Testing**
   - After fixing issues, retest to ensure fixes don't introduce new problems
   - Perform regression testing on all affected devices

## Testing Tools

- **Browser Developer Tools**
  - Chrome DevTools Device Mode
  - Firefox Responsive Design Mode
  - Safari Responsive Design Mode

- **Cross-Browser Testing Services**
  - BrowserStack
  - Sauce Labs
  - LambdaTest

- **Performance Testing Tools**
  - Lighthouse (Chrome DevTools)
  - WebPageTest
  - GTmetrix

## Reporting Issues

When reporting issues, include the following information:

1. Device name and specifications
2. Browser name and version
3. Steps to reproduce the issue
4. Expected behavior
5. Actual behavior
6. Screenshots or screen recordings
7. Console errors (if applicable)

## Continuous Testing

- Schedule regular testing sessions, especially after major updates
- Incorporate device testing into the CI/CD pipeline when possible
- Consider implementing automated testing for critical user flows

## Accessibility Testing

In addition to responsive design testing, perform accessibility testing on each device:

- [ ] Screen reader compatibility (VoiceOver on iOS/macOS, TalkBack on Android, NVDA/JAWS on Windows)
- [ ] Keyboard navigation
- [ ] Color contrast
- [ ] Text scaling
- [ ] Focus indicators

## Documentation Updates

Update this document as new devices or testing requirements are identified.