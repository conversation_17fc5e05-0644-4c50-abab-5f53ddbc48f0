const mongoose = require('mongoose');
const fs = require('fs');
const Floor = require('./models/Floor');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

async function checkFloor() {
  try {
    // Check if the floor exists
    const floorId = '686dd139c7f14736e4e21bd2';
    const floor = await Floor.findById(floorId);
    
    if (!floor) {
      console.log(`Floor with ID ${floorId} does not exist in the database.`);
      return;
    }
    
    console.log('Floor found:', {
      id: floor._id,
      name: floor.name,
      buildingId: floor.buildingId,
      level: floor.level
    });
    
    // Check if the floor has a floorplan
    if (!floor.floorplan) {
      console.log('Floor does not have a floorplan.');
      return;
    }
    
    console.log('Floorplan details:', floor.floorplan);
    
    // Check if the floorplan file exists
    if (floor.floorplan.path && fs.existsSync(floor.floorplan.path)) {
      console.log('Floorplan file exists at path:', floor.floorplan.path);
    } else {
      console.log('Floorplan file does not exist at path:', floor.floorplan.path);
    }
  } catch (err) {
    console.error('Error checking floor:', err);
  } finally {
    mongoose.disconnect();
  }
}

checkFloor();