/**
 * Test script to verify error handling fixes
 * 
 * This script tests:
 * 1. UniFi Access controller's getDoorsInternal method
 * 2. UniFi Access controller's getDoorStatusInternal method
 * 3. LenelS2NetBoxAPI's transformElevatorStatusData method
 */

// Import required modules
const unifiAccessController = require('./server/controllers/unifiAccessController');
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Create a new instance of LenelS2NetBoxAPI
const lenelS2NetBoxAPI = new LenelS2NetBoxAPI();

// Test functions
async function testUnifiAccessControllerErrorHandling() {
  console.log('Testing UniFi Access controller error handling...');
  
  try {
    // Test getDoorsInternal method
    console.log('Testing getDoorsInternal method...');
    const doors = await unifiAccessController.getDoorsInternal();
    console.log('getDoorsInternal result:', doors ? 'Success' : 'Null or undefined');
    
    // Test getDoorStatusInternal method with a non-existent door ID
    console.log('Testing getDoorStatusInternal method with a non-existent door ID...');
    const doorStatus = await unifiAccessController.getDoorStatusInternal('non-existent-door-id');
    console.log('getDoorStatusInternal result:', doorStatus ? 'Success' : 'Null or undefined');
  } catch (error) {
    console.error('Error in UniFi Access controller test:', error);
    console.log('UniFi Access controller error handling test FAILED');
    return false;
  }
  
  console.log('UniFi Access controller error handling test PASSED');
  return true;
}

function testLenelS2NetBoxAPIErrorHandling() {
  console.log('Testing LenelS2NetBoxAPI error handling...');
  
  try {
    // Test transformElevatorStatusData method with undefined response
    console.log('Testing transformElevatorStatusData method with undefined response...');
    const undefinedResult = lenelS2NetBoxAPI.transformElevatorStatusData(undefined, 'test-elevator-id');
    console.log('transformElevatorStatusData with undefined result:', undefinedResult ? 'Success' : 'Null or undefined');
    
    // Test transformElevatorStatusData method with malformed response
    console.log('Testing transformElevatorStatusData method with malformed response...');
    const malformedResult = lenelS2NetBoxAPI.transformElevatorStatusData({ NETBOX: {} }, 'test-elevator-id');
    console.log('transformElevatorStatusData with malformed result:', malformedResult ? 'Success' : 'Null or undefined');
    
    // Test transformElevatorStatusData method with unsuccessful response
    console.log('Testing transformElevatorStatusData method with unsuccessful response...');
    const unsuccessfulResult = lenelS2NetBoxAPI.transformElevatorStatusData({
      NETBOX: {
        RESPONSE: [{ CODE: ['FAIL'] }]
      }
    }, 'test-elevator-id');
    console.log('transformElevatorStatusData with unsuccessful result:', unsuccessfulResult ? 'Success' : 'Null or undefined');
  } catch (error) {
    console.error('Error in LenelS2NetBoxAPI test:', error);
    console.log('LenelS2NetBoxAPI error handling test FAILED');
    return false;
  }
  
  console.log('LenelS2NetBoxAPI error handling test PASSED');
  return true;
}

// Run tests
async function runTests() {
  console.log('Starting error handling tests...');
  
  let unifiAccessResult = false;
  let lenelS2NetBoxResult = false;
  
  try {
    unifiAccessResult = await testUnifiAccessControllerErrorHandling();
  } catch (error) {
    console.error('UniFi Access controller test failed with uncaught error:', error);
  }
  
  try {
    lenelS2NetBoxResult = testLenelS2NetBoxAPIErrorHandling();
  } catch (error) {
    console.error('LenelS2NetBoxAPI test failed with uncaught error:', error);
  }
  
  console.log('\nTest Results:');
  console.log('UniFi Access controller error handling:', unifiAccessResult ? 'PASSED' : 'FAILED');
  console.log('LenelS2NetBoxAPI error handling:', lenelS2NetBoxResult ? 'PASSED' : 'FAILED');
  
  if (unifiAccessResult && lenelS2NetBoxResult) {
    console.log('\nAll tests PASSED! Error handling fixes are working correctly.');
  } else {
    console.log('\nSome tests FAILED. Error handling fixes need further improvement.');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Test script failed with uncaught error:', error);
});