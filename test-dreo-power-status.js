// Test script to check Dreo power status detection
require('dotenv').config();
const DreoAPI = require('./server/integrations/dreo/dreoAPI');

// Get credentials from environment variables
const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
const password = process.env.DREO_PASSWORD;

if (!email || !password) {
  console.error('Error: DREO_EMAIL/DREO_USERNAME and DREO_PASSWORD environment variables must be set');
  process.exit(1);
}

async function testDreoPowerStatus() {
  try {
    console.log('Initializing Dreo API with credentials...');
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    console.log('Authenticating with Dreo API...');
    await dreoAPI.authenticate();
    
    // Get devices
    console.log('Fetching devices...');
    const devices = await dreoAPI.getDevices();
    console.log(`Found ${devices.length} devices`);
    
    if (devices.length === 0) {
      console.log('No devices found. Please make sure your Dreo account has devices associated with it.');
      return;
    }
    
    // Get details for each device
    for (let i = 0; i < devices.length; i++) {
      const device = devices[i];
      const deviceId = device.id || device.sn;
      console.log(`\n--- Device ${i + 1}: ${device.name || device.deviceName} (ID: ${deviceId}) ---`);
      
      // Get device details
      console.log('Fetching device details...');
      const deviceDetails = await dreoAPI.getDeviceDetails(deviceId);
      
      // Log the raw device details
      console.log('\nRaw device details:');
      console.log(JSON.stringify(deviceDetails, null, 2));
      
      // Check for power status
      console.log('\nPower status detection:');
      
      // Check direct power property
      if (deviceDetails.power !== undefined) {
        console.log(`- Direct power property: ${deviceDetails.power} (${typeof deviceDetails.power})`);
        console.log(`- Interpreted as: ${deviceDetails.power ? 'ON' : 'OFF'}`);
      } else {
        console.log('- Direct power property: Not found');
      }
      
      // Check power in state property
      if (deviceDetails.state && deviceDetails.state.power !== undefined) {
        console.log(`- State.power property: ${deviceDetails.state.power} (${typeof deviceDetails.state.power})`);
        console.log(`- Interpreted as: ${deviceDetails.state.power ? 'ON' : 'OFF'}`);
      } else {
        console.log('- State.power property: Not found');
      }
      
      // Check poweron property (common in some Dreo devices)
      if (deviceDetails.poweron !== undefined) {
        console.log(`- Direct poweron property: ${deviceDetails.poweron} (${typeof deviceDetails.poweron})`);
        console.log(`- Interpreted as: ${deviceDetails.poweron ? 'ON' : 'OFF'}`);
      } else {
        console.log('- Direct poweron property: Not found');
      }
      
      // Check poweron in state property
      if (deviceDetails.state && deviceDetails.state.poweron !== undefined) {
        console.log(`- State.poweron property: ${deviceDetails.state.poweron} (${typeof deviceDetails.state.poweron})`);
        console.log(`- Interpreted as: ${deviceDetails.state.poweron ? 'ON' : 'OFF'}`);
      } else {
        console.log('- State.poweron property: Not found');
      }
      
      // Check for any property that might indicate power status
      console.log('\nOther potential power-related properties:');
      const powerRelatedProps = ['power', 'poweron', 'on', 'isOn', 'isPoweredOn', 'status'];
      
      // Check in root object
      for (const prop of powerRelatedProps) {
        if (deviceDetails[prop] !== undefined && prop !== 'power' && prop !== 'poweron') {
          console.log(`- ${prop}: ${deviceDetails[prop]} (${typeof deviceDetails[prop]})`);
        }
      }
      
      // Check in state object
      if (deviceDetails.state) {
        for (const prop of Object.keys(deviceDetails.state)) {
          if (prop.toLowerCase().includes('power') || prop.toLowerCase().includes('on')) {
            if (prop !== 'power' && prop !== 'poweron') {
              console.log(`- state.${prop}: ${deviceDetails.state[prop]} (${typeof deviceDetails.state[prop]})`);
            }
          }
        }
      }
    }
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing Dreo power status:', error);
  }
}

// Run the test
testDreoPowerStatus();