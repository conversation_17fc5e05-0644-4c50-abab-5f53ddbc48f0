// Simple verification script for /api/unifi-protect/config endpoint logic
// It calls the controller directly with mocked req/res and prints results for different env scenarios.

const controller = require('./server/controllers/unifiProtectController');

function makeMockRes(label) {
  return {
    _label: label,
    _status: 200,
    _json: null,
    status(code) {
      this._status = code;
      return this;
    },
    json(payload) {
      this._json = payload;
      console.log(`\n[${this._label}] status=${this._status}`);
      console.log(JSON.stringify(payload, null, 2));
    }
  };
}

async function run() {
  console.log('Verifying UniFi Protect config endpoint behavior...');

  // Scenario 1: Network credentials (A/B)
  process.env.UNIFI_PROTECT_HOST_A = 'protect-a.local';
  process.env.UNIFI_PROTECT_HOST_B = 'protect-b.local';
  process.env.UNIFI_PROTECT_PORT = '7443';
  process.env.UNIFI_NETWORK_USERNAME = 'netuser';
  process.env.UNIFI_NETWORK_PASSWORD = 'netpass';
  delete process.env.UNIFI_PROTECT_HOST;
  delete process.env.UNIFI_PROTECT_USERNAME;
  delete process.env.UNIFI_PROTECT_PASSWORD;

  await controller.getConfig({}, makeMockRes('Network Mode (A/B)'));

  // Scenario 2: Legacy env
  delete process.env.UNIFI_PROTECT_HOST_A;
  delete process.env.UNIFI_PROTECT_HOST_B;
  delete process.env.UNIFI_NETWORK_USERNAME;
  delete process.env.UNIFI_NETWORK_PASSWORD;
  process.env.UNIFI_PROTECT_HOST = 'legacy-host.local';
  process.env.UNIFI_PROTECT_USERNAME = 'admin';
  process.env.UNIFI_PROTECT_PASSWORD = 'secret';
  process.env.UNIFI_PROTECT_PORT = '443';

  await controller.getConfig({}, makeMockRes('Legacy Mode'));

  // Scenario 3: Missing envs
  delete process.env.UNIFI_PROTECT_HOST_A;
  delete process.env.UNIFI_PROTECT_HOST_B;
  delete process.env.UNIFI_NETWORK_USERNAME;
  delete process.env.UNIFI_NETWORK_PASSWORD;
  delete process.env.UNIFI_PROTECT_HOST;
  delete process.env.UNIFI_PROTECT_USERNAME;
  delete process.env.UNIFI_PROTECT_PASSWORD;
  delete process.env.UNIFI_PROTECT_PORT;

  await controller.getConfig({}, makeMockRes('Missing Env Vars'));

  console.log('\nVerification complete.');
}

run().catch(err => {
  console.error('Verification script error:', err);
});
