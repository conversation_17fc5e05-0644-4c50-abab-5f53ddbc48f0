const websocketServer = require('../websocket/websocketServer');
const LenelS2NetBoxAPI = require('../integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Environment variables for Lenel S2 NetBox API
const lenelHost = process.env.LENEL_S2_NETBOX_HOST || '';
const lenelUsername = process.env.LENEL_S2_NETBOX_USERNAME || '';
const lenelPassword = process.env.LENEL_S2_NETBOX_PASSWORD || '';
const lenelPort = process.env.LENEL_S2_NETBOX_PORT || 443;

// Use lazy initialization for the API
let lenelS2NetBoxAPI = null;
const UnifiAccessAPI = require('../integrations/unifiAccess/unifiAccessAPI');
const UnifiProtectAPI = require('../integrations/unifiProtect/unifiProtectAPI');
const unifiNetworkController = require('../controllers/unifiNetworkController');
const unifiAccessController = require('../controllers/unifiAccessController');
const unifiProtectController = require('../controllers/unifiProtectController');
const googleFormsWebhookController = require('../controllers/googleFormsWebhookController');
const GoogleFormsWebhook = require('../../models/GoogleFormsWebhook');

class RealtimeService {
  constructor() {
    this.isRunning = false;
    this.intervals = new Map();
    this.lastActivityCheck = null;
    this.lastEvacuationCheck = null;
    this.lastElevatorCheck = null;
    this.lastGoogleFormsWebhookCheck = null;
    this.lastDoorStatusCheck = null;
  }

  /**
   * Start real-time monitoring services
   */
  async start() {
    if (this.isRunning) {
      console.log('Realtime service is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting realtime service...');

    // Initialize UniFi integrations
    await this.initializeUnifiIntegrations();
    
    // Note: Lenel S2 NetBox API is now lazily initialized only when needed
    // This prevents unnecessary API calls on non-Lenel related pages

    // Start activity log monitoring
    this.startActivityLogMonitoring();
    
    // Start evacuation status monitoring
    this.startEvacuationMonitoring();
    
    // Start elevator status monitoring
    this.startElevatorMonitoring();
    
    // Start door status monitoring
    this.startDoorStatusMonitoring();
    
    // Start Google Forms webhook monitoring
    this.startGoogleFormsWebhookMonitoring();

    console.log('Realtime service started');
  }

  /**
   * Stop real-time monitoring services
   */
  stop() {
    if (!this.isRunning) {
      console.log('Realtime service is not running');
      return;
    }

    this.isRunning = false;
    
    // Clear all intervals
    this.intervals.forEach((interval, name) => {
      clearInterval(interval);
      console.log(`Stopped ${name} monitoring`);
    });
    
    this.intervals.clear();
    console.log('Realtime service stopped');
  }

  /**
   * Start monitoring activity log for new events
   */
  startActivityLogMonitoring() {
    const checkInterval = 10000; // Check every 10 seconds
    
    const checkActivityLog = async () => {
      try {
        // Skip if Lenel environment variables are not set
        if (!lenelHost || !lenelUsername || !lenelPassword) {
          return;
        }
        
        // Initialize Lenel API if not already initialized
        if (!lenelS2NetBoxAPI) {
          await this.initializeLenelS2NetBoxAPI();
          
          // If initialization failed, skip this check
          if (!lenelS2NetBoxAPI) {
            return;
          }
        }
        
        // Get recent activity since last check
        const filters = {
          limit: 50,
          startDate: this.lastActivityCheck || new Date(Date.now() - 60000).toISOString() // Last minute if no previous check
        };

        // Check if the method exists before calling it
        if (!lenelS2NetBoxAPI.getLiveActivityLog) {
          console.log('getLiveActivityLog method not implemented in Lenel S2 NetBox API');
          return;
        }
        
        const newActivity = await lenelS2NetBoxAPI.getLiveActivityLog(filters);
        
        if (newActivity && newActivity.length > 0) {
          // Filter out activities we've already seen
          const unseenActivity = this.lastActivityCheck 
            ? newActivity.filter(activity => new Date(activity.timestamp) > new Date(this.lastActivityCheck))
            : newActivity;

          if (unseenActivity.length > 0) {
            console.log(`Broadcasting ${unseenActivity.length} new activity events`);
            websocketServer.broadcast('activity-log', unseenActivity);
          }
        }

        this.lastActivityCheck = new Date().toISOString();
      } catch (error) {
        console.error('Error checking activity log:', error);
      }
    };

    // Initial check
    checkActivityLog();
    
    // Set up interval
    const interval = setInterval(checkActivityLog, checkInterval);
    this.intervals.set('activity-log', interval);
    
    console.log('Activity log monitoring started');
  }

  /**
   * Start monitoring evacuation status
   */
  startEvacuationMonitoring() {
    const checkInterval = 30000; // Check every 30 seconds
    
    const checkEvacuationStatus = async () => {
      try {
        // Skip if Lenel environment variables are not set
        if (!lenelHost || !lenelUsername || !lenelPassword) {
          return;
        }
        
        // Initialize Lenel API if not already initialized
        if (!lenelS2NetBoxAPI) {
          await this.initializeLenelS2NetBoxAPI();
          
          // If initialization failed, skip this check
          if (!lenelS2NetBoxAPI) {
            return;
          }
        }
        
        // Check if the method exists before calling it
        if (!lenelS2NetBoxAPI.getEvacuationStatus) {
          console.log('getEvacuationStatus method not implemented in Lenel S2 NetBox API');
          return;
        }
        
        const evacuationStatus = await lenelS2NetBoxAPI.getEvacuationStatus();
        
        // Check if evacuation status has changed
        if (this.lastEvacuationCheck) {
          const hasChanged = JSON.stringify(evacuationStatus) !== JSON.stringify(this.lastEvacuationCheck);
          
          if (hasChanged) {
            console.log('Broadcasting evacuation status update');
            websocketServer.broadcast('evacuation-status', evacuationStatus);
          }
        }

        this.lastEvacuationCheck = evacuationStatus;
      } catch (error) {
        console.error('Error checking evacuation status:', error);
      }
    };

    // Initial check
    checkEvacuationStatus();
    
    // Set up interval
    const interval = setInterval(checkEvacuationStatus, checkInterval);
    this.intervals.set('evacuation-status', interval);
    
    console.log('Evacuation monitoring started');
  }

  /**
   * Start monitoring elevator status
   */
  startElevatorMonitoring() {
    const checkInterval = 15000; // Check every 15 seconds
    
    const checkElevatorStatus = async () => {
      try {
        // Skip if Lenel environment variables are not set
        if (!lenelHost || !lenelUsername || !lenelPassword) {
          return;
        }
        
        // Initialize Lenel API if not already initialized
        if (!lenelS2NetBoxAPI) {
          await this.initializeLenelS2NetBoxAPI();
          
          // If initialization failed, skip this check
          if (!lenelS2NetBoxAPI) {
            return;
          }
        }
        
        // Check if the method exists before calling it
        if (!lenelS2NetBoxAPI.getElevators) {
          console.log('getElevators method not implemented in Lenel S2 NetBox API');
          return;
        }
        
        // Get all elevators first
        const elevators = await lenelS2NetBoxAPI.getElevators();
        
        if (elevators && elevators.length > 0) {
          // Check status for each elevator
          const elevatorStatuses = await Promise.all(
            elevators.map(async (elevator) => {
              try {
                // Check if the method exists before calling it
                if (!lenelS2NetBoxAPI.getElevatorStatus) {
                  console.log('getElevatorStatus method not implemented in Lenel S2 NetBox API');
                  return null;
                }
                
                const status = await lenelS2NetBoxAPI.getElevatorStatus(elevator.id);
                return {
                  elevatorId: elevator.id,
                  elevatorName: elevator.name,
                  ...status
                };
              } catch (error) {
                console.error(`Error getting status for elevator ${elevator.id}:`, error);
                return null;
              }
            })
          );

          const validStatuses = elevatorStatuses.filter(status => status !== null);
          
          // Check if any elevator status has changed
          if (this.lastElevatorCheck) {
            const hasChanged = JSON.stringify(validStatuses) !== JSON.stringify(this.lastElevatorCheck);
            
            if (hasChanged) {
              console.log('Broadcasting elevator status update');
              websocketServer.broadcast('elevator-status', validStatuses);
            }
          }

          this.lastElevatorCheck = validStatuses;
        }
      } catch (error) {
        console.error('Error checking elevator status:', error);
      }
    };

    // Initial check
    checkElevatorStatus();
    
    // Set up interval
    const interval = setInterval(checkElevatorStatus, checkInterval);
    this.intervals.set('elevator-status', interval);
    
    console.log('Elevator monitoring started');
  }

  /**
   * Manually trigger activity log broadcast
   * @param {Array} activities - Activities to broadcast
   */
  broadcastActivityLog(activities) {
    if (activities && activities.length > 0) {
      websocketServer.broadcast('activity-log', activities);
    }
  }

  /**
   * Manually trigger evacuation status broadcast
   * @param {Object} evacuationStatus - Evacuation status to broadcast
   */
  broadcastEvacuationStatus(evacuationStatus) {
    websocketServer.broadcast('evacuation-status', evacuationStatus);
  }

  /**
   * Manually trigger elevator status broadcast
   * @param {Array} elevatorStatuses - Elevator statuses to broadcast
   */
  broadcastElevatorStatus(elevatorStatuses) {
    websocketServer.broadcast('elevator-status', elevatorStatuses);
  }

  /**
   * Broadcast card status change
   * @param {Object} cardStatusChange - Card status change event
   */
  broadcastCardStatusChange(cardStatusChange) {
    websocketServer.broadcast('card-status-change', cardStatusChange);
  }
  
  /**
   * Broadcast access attempt event
   * @param {Object} accessAttempt - Access attempt event data
   */
  broadcastAccessAttempt(accessAttempt) {
    websocketServer.broadcast('access-attempt', accessAttempt);
  }
  
  /**
   * Broadcast policy change event
   * @param {Object} policyChange - Policy change event data
   */
  broadcastPolicyChange(policyChange) {
    websocketServer.broadcast('policy-change', policyChange);
  }
  
  /**
   * Broadcast access level change event
   * @param {Object} accessLevelChange - Access level change event data
   */
  broadcastAccessLevelChange(accessLevelChange) {
    websocketServer.broadcast('access-level-change', accessLevelChange);
  }
  
  /**
   * Broadcast user status change event
   * @param {Object} userStatusChange - User status change event data
   */
  broadcastUserStatusChange(userStatusChange) {
    websocketServer.broadcast('user-status-change', userStatusChange);
  }
  
  /**
   * Broadcast access control system status change
   * @param {Object} systemStatusChange - System status change event data
   */
  broadcastSystemStatusChange(systemStatusChange) {
    websocketServer.broadcast('system-status-change', systemStatusChange);
  }
  
  /**
   * Start monitoring door status
   */
  startDoorStatusMonitoring() {
    const checkInterval = 10000; // Check every 10 seconds
    
    const checkDoorStatus = async () => {
      try {
        // Check if Lenel environment variables are set
        const lenelConfigured = lenelHost && lenelUsername && lenelPassword;
        
        // Check if UniFi Access environment variables are set
        const unifiAccessConfigured = process.env.UNIFI_ACCESS_HOST && 
                                     process.env.UNIFI_ACCESS_API_KEY;
        
        // Skip if neither system is configured
        if (!lenelConfigured && !unifiAccessConfigured) {
          return;
        }
        
        let doors = [];
        
        // Get door status from Lenel S2 NetBox
        if (lenelConfigured) {
          // Initialize Lenel API if not already initialized
          if (!lenelS2NetBoxAPI) {
            await this.initializeLenelS2NetBoxAPI();
            
            // If initialization failed, skip this check
            if (!lenelS2NetBoxAPI) {
              console.log('Failed to initialize Lenel S2 NetBox API, skipping door status check');
            }
          }
          
          if (lenelS2NetBoxAPI) {
            // Check if the method exists before calling it
            if (lenelS2NetBoxAPI.getDoors) {
              const lenelDoors = await lenelS2NetBoxAPI.getDoors();
              
              // Get status for each door
              if (lenelDoors && lenelDoors.length > 0) {
                const lenelDoorStatuses = await Promise.all(
                  lenelDoors.map(async (door) => {
                    try {
                      // Check if the method exists before calling it
                      if (lenelS2NetBoxAPI.getDoorStatus) {
                        const status = await lenelS2NetBoxAPI.getDoorStatus(door.id);
                        return {
                          doorId: door.id,
                          doorName: door.name,
                          source: 'lenelS2NetBox',
                          ...status
                        };
                      }
                      return null;
                    } catch (error) {
                      console.error(`Error getting status for Lenel door ${door.id}:`, error);
                      return null;
                    }
                  })
                );
                
                // Add valid door statuses to the list
                doors = doors.concat(lenelDoorStatuses.filter(status => status !== null));
              }
            } else {
              console.log('getDoors method not implemented in Lenel S2 NetBox API');
            }
          }
        }
        
        // Get door status from UniFi Access
        if (unifiAccessConfigured) {
          // Get UniFi Access controller instance
          if (unifiAccessController && unifiAccessController.getDoors) {
            const unifiDoors = await unifiAccessController.getDoors();
            
            // Get status for each door
            if (unifiDoors && unifiDoors.length > 0) {
              const unifiDoorStatuses = await Promise.all(
                unifiDoors.map(async (door) => {
                  try {
                    // Check if the method exists before calling it
                    if (unifiAccessController.getDoorStatus) {
                      const status = await unifiAccessController.getDoorStatus(door.id);
                      return {
                        doorId: door.id,
                        doorName: door.name,
                        source: 'unifiAccess',
                        ...status
                      };
                    }
                    return null;
                  } catch (error) {
                    console.error(`Error getting status for UniFi door ${door.id}:`, error);
                    return null;
                  }
                })
              );
              
              // Add valid door statuses to the list
              doors = doors.concat(unifiDoorStatuses.filter(status => status !== null));
            }
          } else {
            console.log('getDoors method not implemented in UniFi Access controller');
          }
        }
        
        // Check if door status has changed
        if (doors.length > 0) {
          if (this.lastDoorStatusCheck) {
            const hasChanged = JSON.stringify(doors) !== JSON.stringify(this.lastDoorStatusCheck);
            
            if (hasChanged) {
              console.log(`Broadcasting door status update for ${doors.length} doors`);
              this.broadcastDoorStatus(doors);
            }
          } else {
            // First check, broadcast initial status
            console.log(`Broadcasting initial door status for ${doors.length} doors`);
            this.broadcastDoorStatus(doors);
          }
          
          this.lastDoorStatusCheck = doors;
        }
      } catch (error) {
        console.error('Error checking door status:', error);
      }
    };
    
    // Initial check
    checkDoorStatus();
    
    // Set up interval
    const interval = setInterval(checkDoorStatus, checkInterval);
    this.intervals.set('door-status', interval);
    
    console.log('Door status monitoring started');
  }
  
  /**
   * Manually trigger door status broadcast
   * @param {Array} doorStatuses - Door statuses to broadcast
   */
  broadcastDoorStatus(doorStatuses) {
    websocketServer.broadcast('door-status', doorStatuses);
  }

  /**
   * Initialize UniFi integrations
   */
  async initializeUnifiIntegrations() {
    try {
      console.log('Initializing UniFi integrations...');
      
      // Initialize UniFi Access API
      if (process.env.UNIFI_ACCESS_HOST && process.env.UNIFI_ACCESS_API_KEY) {
        console.log('Initializing UniFi Access API...');
        const unifiAccessAPI = new UnifiAccessAPI();
        await unifiAccessAPI.initialize();
        console.log('UniFi Access API initialized successfully');
      } else {
        console.log('UniFi Access environment variables not set, skipping initialization');
      }
      
      // Initialize UniFi Network API
      if (process.env.UNIFI_NETWORK_HOST && process.env.UNIFI_NETWORK_API_KEY) {
        console.log('Initializing UniFi Network API...');
        await unifiNetworkController.initializeAPI();
        console.log('UniFi Network API initialized successfully');
      } else {
        console.log('UniFi Network environment variables not set, skipping initialization');
      }
      
      // Initialize UniFi Protect API
      try {
        console.log('Initializing UniFi Protect API...');
        // Get the latest configuration from the database using the exported function
        const config = await unifiProtectController.getLatestConfig();
        if (config) {
          // Create a new instance with the configuration
          const unifiProtectAPI = new UnifiProtectAPI(config.host, config.username, config.password, config.port);
          // Initialize the API to update its status in the integrationTracker
          await unifiProtectAPI.initialize();
          console.log('UniFi Protect API initialized successfully');
        } else {
          console.log('UniFi Protect configuration not found, skipping initialization');
        }
      } catch (error) {
        console.error('Error initializing UniFi Protect API:', error);
      }
      
      console.log('UniFi integrations initialization completed');
    } catch (error) {
      console.error('Error initializing UniFi integrations:', error);
    }
  }

  /**
   * Initialize Lenel S2 NetBox API
   */
  async initializeLenelS2NetBoxAPI() {
    try {
      console.log('Initializing Lenel S2 NetBox API...');
      
      // Check if the necessary environment variables are set
      // Using module-level variables defined at the top of the file
      if (lenelHost && lenelUsername && lenelPassword) {
        console.log('Lenel S2 NetBox environment variables found, initializing API...');
        
        // Create and initialize the API if it hasn't been initialized yet
        if (!lenelS2NetBoxAPI) {
          console.log('Creating new Lenel S2 NetBox API instance');
          lenelS2NetBoxAPI = new LenelS2NetBoxAPI(lenelHost, lenelUsername, lenelPassword, lenelPort);
          await lenelS2NetBoxAPI.initialize();
        }
        
        console.log('Lenel S2 NetBox API initialized successfully');
      } else {
        console.log('Lenel S2 NetBox environment variables not set, skipping initialization');
      }
    } catch (error) {
      console.error('Error initializing Lenel S2 NetBox API:', error);
    }
  }

  /**
   * Start monitoring Google Forms webhooks for new responses
   */
  startGoogleFormsWebhookMonitoring() {
    const checkInterval = 5 * 60 * 1000; // Check every 5 minutes
    
    const checkGoogleFormsWebhooks = async () => {
      try {
        console.log('Checking Google Forms webhooks for new responses...');
        
        // Get all active webhooks
        const activeWebhooks = await GoogleFormsWebhook.find({ active: true })
          .populate('createdBy', 'name email')
          .populate('defaultAssignee', 'name email')
          .populate('assignmentRules.assignTo', 'name email');
        
        if (activeWebhooks.length === 0) {
          console.log('No active Google Forms webhooks found');
          return;
        }
        
        console.log(`Found ${activeWebhooks.length} active Google Forms webhooks`);
        
        // Process each webhook
        let totalProcessed = 0;
        let totalNewTasks = 0;
        
        for (const webhook of activeWebhooks) {
          try {
            // Use the controller method to process the webhook
            const result = await googleFormsWebhookController.processWebhookResponses(
              webhook, 
              webhook.createdBy._id
            );
            
            if (result.processed > 0) {
              console.log(`Processed ${result.processed} new responses for form "${webhook.formName}"`);
              totalProcessed += result.processed;
              totalNewTasks += result.newTasks.length;
              
              // Broadcast an event for the new tasks
              if (result.newTasks.length > 0) {
                websocketServer.broadcast('google-forms-webhook-tasks', {
                  webhookId: webhook._id,
                  formName: webhook.formName,
                  newTasks: result.newTasks
                });
              }
            }
          } catch (webhookError) {
            console.error(`Error processing webhook for form "${webhook.formName}":`, webhookError);
          }
        }
        
        if (totalProcessed > 0) {
          console.log(`Total: Processed ${totalProcessed} responses and created ${totalNewTasks} tasks`);
        } else {
          console.log('No new form responses to process');
        }
        
        this.lastGoogleFormsWebhookCheck = new Date();
      } catch (error) {
        console.error('Error checking Google Forms webhooks:', error);
      }
    };
    
    // Initial check
    checkGoogleFormsWebhooks();
    
    // Set up interval
    const interval = setInterval(checkGoogleFormsWebhooks, checkInterval);
    this.intervals.set('google-forms-webhooks', interval);
    
    console.log('Google Forms webhook monitoring started');
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      activeMonitors: Array.from(this.intervals.keys()),
      lastChecks: {
        activityLog: this.lastActivityCheck,
        evacuation: this.lastEvacuationCheck,
        elevator: this.lastElevatorCheck,
        doorStatus: this.lastDoorStatusCheck,
        googleFormsWebhooks: this.lastGoogleFormsWebhookCheck
      },
      websocketStats: websocketServer.getStats()
    };
  }
}

// Create singleton instance
const realtimeService = new RealtimeService();

module.exports = realtimeService;
