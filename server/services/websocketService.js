/**
 * WebSocket Service Wrapper for Server-Side
 * Provides easy access to websocket functionality for messaging
 */

let websocketServer = null;

// Initialize with websocket server instance
const initialize = (server) => {
  websocketServer = server;
};

// Send message to specific user
const sendToUser = (userId, eventType, data) => {
  if (!websocketServer) {
    console.error('WebSocket server not initialized');
    return;
  }
  websocketServer.sendToUser(userId, eventType, data);
};

// Send message to conversation participants
const sendToConversation = (conversationId, eventType, data) => {
  if (!websocketServer) {
    console.error('WebSocket server not initialized');
    return;
  }
  websocketServer.sendToConversation(conversationId, eventType, data);
};

// Broadcast to all clients
const broadcast = (eventType, data) => {
  if (!websocketServer) {
    console.error('WebSocket server not initialized');
    return;
  }
  websocketServer.broadcast(eventType, data);
};

module.exports = {
  initialize,
  sendToUser,
  sendToConversation,
  broadcast
};