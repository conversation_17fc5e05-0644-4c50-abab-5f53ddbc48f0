const nodemailer = require('nodemailer');
const User = require('../../models/User');

/**
 * Access Control Notification Service
 * Handles email/SMS notifications for critical access control events
 */
class AccessControlNotificationService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    if (process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS) {
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });
    } else {
      console.log('SMTP configuration not found, email notifications disabled');
    }
  }

  /**
   * Send notification for door forced open event
   * @param {Object} door - Door/portal object
   * @param {Object} event - Event details
   */
  async notifyDoorForcedOpen(door, event) {
    if (!this.transporter) return;

    try {
      const recipients = await this.getAccessControlNotificationRecipients('door_forced_open');
      
      if (recipients.length === 0) return;

      const subject = `ALERT: Door Forced Open - ${door.name}`;
      const content = await this.generateDoorForcedOpenEmail(door, event);

      await this.sendNotificationEmails(recipients, subject, content);
      
      console.log(`Door forced open notification sent for ${door.name} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending door forced open notification:', error);
    }
  }

  /**
   * Send notification for unauthorized access attempt
   * @param {Object} door - Door/portal object
   * @param {Object} event - Event details
   */
  async notifyUnauthorizedAccess(door, event) {
    if (!this.transporter) return;

    try {
      const recipients = await this.getAccessControlNotificationRecipients('unauthorized_access');
      
      if (recipients.length === 0) return;

      const subject = `ALERT: Unauthorized Access Attempt - ${door.name}`;
      const content = await this.generateUnauthorizedAccessEmail(door, event);

      await this.sendNotificationEmails(recipients, subject, content);
      
      console.log(`Unauthorized access notification sent for ${door.name} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending unauthorized access notification:', error);
    }
  }

  /**
   * Send notification for door held open event
   * @param {Object} door - Door/portal object
   * @param {Object} event - Event details
   */
  async notifyDoorHeldOpen(door, event) {
    if (!this.transporter) return;

    try {
      const recipients = await this.getAccessControlNotificationRecipients('door_held_open');
      
      if (recipients.length === 0) return;

      const subject = `ALERT: Door Held Open - ${door.name}`;
      const content = await this.generateDoorHeldOpenEmail(door, event);

      await this.sendNotificationEmails(recipients, subject, content);
      
      console.log(`Door held open notification sent for ${door.name} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending door held open notification:', error);
    }
  }

  /**
   * Send notification for system offline event
   * @param {string} system - System name ('unifi-access' or 'lenel-s2-netbox')
   * @param {Object} event - Event details
   */
  async notifySystemOffline(system, event) {
    if (!this.transporter) return;

    try {
      const recipients = await this.getAccessControlNotificationRecipients('system_offline');
      
      if (recipients.length === 0) return;

      const systemName = system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox';
      const subject = `ALERT: Access Control System Offline - ${systemName}`;
      const content = await this.generateSystemOfflineEmail(system, event);

      await this.sendNotificationEmails(recipients, subject, content);
      
      console.log(`System offline notification sent for ${systemName} to ${recipients.length} recipients`);
    } catch (error) {
      console.error('Error sending system offline notification:', error);
    }
  }

  /**
   * Get recipients for access control notifications
   * @param {string} eventType - Type of event
   * @returns {Array} Array of user objects
   */
  async getAccessControlNotificationRecipients(eventType) {
    const recipients = new Set();
    
    try {
      // Get users with security admin role or access control admin role
      const securityAdmins = await User.find({
        $or: [
          { roles: { $in: ['security_admin', 'access_control_admin'] } },
          { 'accessControlNotifications.all': true },
          { [`accessControlNotifications.${eventType}`]: true }
        ],
        isActive: true
      });
      
      securityAdmins.forEach(admin => {
        if (admin.email) {
          recipients.add(admin);
        }
      });
      
      return Array.from(recipients);
    } catch (error) {
      console.error('Error getting access control notification recipients:', error);
      return [];
    }
  }

  /**
   * Send notification emails
   * @param {Array} recipients - Array of user objects
   * @param {string} subject - Email subject
   * @param {string} content - Email content (HTML)
   */
  async sendNotificationEmails(recipients, subject, content) {
    if (!this.transporter || !Array.isArray(recipients) || recipients.length === 0) return;

    // Respect user email notification preferences (default to enabled)
    const filteredRecipients = recipients.filter(r => !!r?.email && (r.notificationPreferences?.emailEnabled !== false));
    if (filteredRecipients.length === 0) return;

    const emailPromises = filteredRecipients.map(recipient => {
      return this.transporter.sendMail({
        from: process.env.ACCESS_CONTROL_EMAIL_FROM || process.env.SMTP_USER,
        to: recipient.email,
        subject: subject,
        html: content
      });
    });

    try {
      await Promise.allSettled(emailPromises);
    } catch (error) {
      console.error('Error sending notification emails:', error);
    }
  }

  /**
   * Generate email content for door forced open event
   * @param {Object} door - Door/portal object
   * @param {Object} event - Event details
   * @returns {string} HTML email content
   */
  async generateDoorForcedOpenEmail(door, event) {
    const portalUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/access-control/doors`;
    const timestamp = new Date(event.timestamp).toLocaleString();
    const systemName = door.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #d32f2f; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .event-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ALERT: Door Forced Open</h1>
          </div>
          <div class="content">
            <p>A door has been forced open in your access control system.</p>
            
            <div class="event-info">
              <h3>Event Details</h3>
              <p><strong>Door Name:</strong> ${door.name}</p>
              <p><strong>Location:</strong> ${door.location || 'N/A'}</p>
              <p><strong>System:</strong> ${systemName}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
              ${event.description ? `<p><strong>Description:</strong> ${event.description}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${portalUrl}" class="button">View Access Control</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Access Control Management System</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for unauthorized access attempt
   * @param {Object} door - Door/portal object
   * @param {Object} event - Event details
   * @returns {string} HTML email content
   */
  async generateUnauthorizedAccessEmail(door, event) {
    const portalUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/access-control/doors`;
    const timestamp = new Date(event.timestamp).toLocaleString();
    const systemName = door.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #d32f2f; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .event-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ALERT: Unauthorized Access Attempt</h1>
          </div>
          <div class="content">
            <p>An unauthorized access attempt has been detected in your access control system.</p>
            
            <div class="event-info">
              <h3>Event Details</h3>
              <p><strong>Door Name:</strong> ${door.name}</p>
              <p><strong>Location:</strong> ${door.location || 'N/A'}</p>
              <p><strong>System:</strong> ${systemName}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
              ${event.user ? `<p><strong>User:</strong> ${event.user}</p>` : ''}
              ${event.credential ? `<p><strong>Credential:</strong> ${event.credential}</p>` : ''}
              ${event.description ? `<p><strong>Description:</strong> ${event.description}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${portalUrl}" class="button">View Access Control</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Access Control Management System</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for door held open event
   * @param {Object} door - Door/portal object
   * @param {Object} event - Event details
   * @returns {string} HTML email content
   */
  async generateDoorHeldOpenEmail(door, event) {
    const portalUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/access-control/doors`;
    const timestamp = new Date(event.timestamp).toLocaleString();
    const systemName = door.system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #ff9800; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .event-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ALERT: Door Held Open</h1>
          </div>
          <div class="content">
            <p>A door has been held open beyond the allowed time in your access control system.</p>
            
            <div class="event-info">
              <h3>Event Details</h3>
              <p><strong>Door Name:</strong> ${door.name}</p>
              <p><strong>Location:</strong> ${door.location || 'N/A'}</p>
              <p><strong>System:</strong> ${systemName}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
              ${event.duration ? `<p><strong>Duration:</strong> ${event.duration} seconds</p>` : ''}
              ${event.description ? `<p><strong>Description:</strong> ${event.description}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${portalUrl}" class="button">View Access Control</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Access Control Management System</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate email content for system offline event
   * @param {string} system - System name ('unifi-access' or 'lenel-s2-netbox')
   * @param {Object} event - Event details
   * @returns {string} HTML email content
   */
  async generateSystemOfflineEmail(system, event) {
    const portalUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/access-control`;
    const timestamp = new Date(event.timestamp).toLocaleString();
    const systemName = system === 'unifi-access' ? 'Unifi Access' : 'Lenel S2 NetBox';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #d32f2f; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .event-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
          .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>ALERT: Access Control System Offline</h1>
          </div>
          <div class="content">
            <p>An access control system is currently offline and requires attention.</p>
            
            <div class="event-info">
              <h3>Event Details</h3>
              <p><strong>System:</strong> ${systemName}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
              ${event.lastSeen ? `<p><strong>Last Seen:</strong> ${new Date(event.lastSeen).toLocaleString()}</p>` : ''}
              ${event.description ? `<p><strong>Description:</strong> ${event.description}</p>` : ''}
            </div>
            
            <p style="text-align: center;">
              <a href="${portalUrl}" class="button">View Access Control</a>
            </p>
          </div>
          <div class="footer">
            <p>CSF Portal - Access Control Management System</p>
            <p>This is an automated notification. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new AccessControlNotificationService();