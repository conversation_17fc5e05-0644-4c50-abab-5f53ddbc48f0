const Imap = require('imap');
const { simpleParser } = require('mailparser');
const ticketEmailController = require('../controllers/ticketEmailController');

/**
 * IMAP Email Service
 * Polls Gmail/Google Workspace via IMAP for new emails
 */
class ImapEmailService {
  constructor() {
    this.imap = null;
    this.isConnected = false;
    this.pollInterval = null;
    this.processedMessageIds = new Set();
  }

  /**
   * Initialize IMAP connection
   */
  initialize() {
    if (!process.env.IMAP_HOST || !process.env.IMAP_USER || !process.env.IMAP_PASS) {
      console.log('IMAP not configured, skipping email polling');
      return;
    }

    this.imap = new Imap({
      host: process.env.IMAP_HOST,
      port: process.env.IMAP_PORT || 993,
      tls: true,
      user: process.env.IMAP_USER,
      password: process.env.IMAP_PASS
    });

    this.imap.once('ready', () => {
      console.log('IMAP connection ready');
      this.isConnected = true;
      this.startPolling();
    });

    this.imap.once('error', (err) => {
      console.error('IMAP connection error:', err);
      this.isConnected = false;
    });

    this.imap.once('end', () => {
      console.log('IMAP connection ended');
      this.isConnected = false;
    });

    this.imap.connect();
  }

  /**
   * Start polling for new emails
   */
  startPolling() {
    // Poll every 2 minutes
    this.pollInterval = setInterval(() => {
      this.checkForNewEmails();
    }, 2 * 60 * 1000);

    // Initial check
    this.checkForNewEmails();
  }

  /**
   * Check for new emails
   */
  async checkForNewEmails() {
    if (!this.isConnected) return;

    try {
      this.imap.openBox('INBOX', false, (err, box) => {
        if (err) {
          console.error('Error opening inbox:', err);
          return;
        }

        // Search for unread emails
        this.imap.search(['UNSEEN'], (err, results) => {
          if (err) {
            console.error('Error searching emails:', err);
            return;
          }

          if (results && results.length > 0) {
            console.log(`Found ${results.length} unread emails`);
            this.processEmails(results);
          }
        });
      });
    } catch (error) {
      console.error('Error checking for new emails:', error);
    }
  }

  /**
   * Process found emails
   * @param {Array} messageIds - Array of message IDs
   */
  processEmails(messageIds) {
    const fetch = this.imap.fetch(messageIds, {
      bodies: '',
      markSeen: true
    });

    fetch.on('message', (msg, seqno) => {
      let buffer = '';

      msg.on('body', (stream, info) => {
        stream.on('data', (chunk) => {
          buffer += chunk.toString('utf8');
        });

        stream.once('end', async () => {
          try {
            const parsed = await simpleParser(buffer);
            
            // Skip if already processed
            if (this.processedMessageIds.has(parsed.messageId)) {
              return;
            }

            // Add to processed set
            this.processedMessageIds.add(parsed.messageId);

            // Convert to webhook format
            const emailData = {
              messageId: parsed.messageId,
              from: parsed.from?.text || parsed.from,
              to: parsed.to?.text || parsed.to,
              subject: parsed.subject,
              text: parsed.text,
              html: parsed.html,
              inReplyTo: parsed.inReplyTo,
              references: parsed.references,
              headers: parsed.headers,
              attachments: parsed.attachments || []
            };

            // Process through ticket controller
            await this.processEmailData(emailData);

          } catch (error) {
            console.error('Error parsing email:', error);
          }
        });
      });

      msg.once('attributes', (attrs) => {
        console.log(`Processing email ${seqno} with UID ${attrs.uid}`);
      });
    });

    fetch.once('error', (err) => {
      console.error('Fetch error:', err);
    });

    fetch.once('end', () => {
      console.log('Done fetching emails');
    });
  }

  /**
   * Process email data through ticket controller
   * @param {Object} emailData - Parsed email data
   */
  async processEmailData(emailData) {
    try {
      const mockReq = {
        body: emailData,
        headers: { 'user-agent': 'IMAP-Email-Processor' }
      };
      
      const mockRes = {
        status: (code) => ({ json: (data) => console.log(`IMAP email response ${code}:`, data) }),
        json: (data) => console.log('IMAP email response:', data)
      };

      await ticketEmailController.processIncomingEmail(mockReq, mockRes);
    } catch (error) {
      console.error('Error processing email data:', error);
    }
  }

  /**
   * Stop the email service
   */
  stop() {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }

    if (this.imap && this.isConnected) {
      this.imap.end();
    }

    console.log('IMAP email service stopped');
  }
}

module.exports = new ImapEmailService();