const TicketFormsWebhook = require('../../models/TicketFormsWebhook');
const Ticket = require('../../models/Ticket');
const User = require('../../models/User'); 
const GoogleFormsAPI = require('../integrations/googleForms/googleFormsAPI');
const { validationResult } = require('express-validator');

/**
 * Ticket Forms Webhook Controller
 * Handles ticket creation from Google Forms responses
 */
const ticketFormsWebhookController = {
  /**
   * Create a new ticket forms webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created webhook
   */
  createWebhook: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check permissions
    if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
      return res.status(403).json({ message: 'Not authorized to create ticket webhooks' });
    }

    try {
      const {
        formId,
        formName,
        fieldMappings,
        tokenMappings = [],
        assignmentRules = [],
        defaults = {},
        notifications = {}
      } = req.body;

      // Validate required fields
      if (!formId || !formName || !fieldMappings || !fieldMappings.subjectField) {
        return res.status(400).json({ 
          message: 'Form ID, form name, and subject field mapping are required' 
        });
      }

      // Check if webhook already exists for this form
      const existingWebhook = await TicketFormsWebhook.findOne({ formId });
      if (existingWebhook) {
        return res.status(400).json({ 
          message: 'A ticket webhook already exists for this form',
          webhookId: existingWebhook._id
        });
      }

      // Create new webhook
      const webhook = new TicketFormsWebhook({
        formId,
        formName,
        createdBy: req.user.id,
        fieldMappings,
        tokenMappings,
        assignmentRules,
        defaults,
        notifications
      });

      await webhook.save();

      // Populate references for response
      await webhook.populate('createdBy', 'name email');
      if (webhook.defaults.assignee) {
        await webhook.populate('defaults.assignee', 'name email');
      }
      if (webhook.defaults.group) {
        await webhook.populate('defaults.group', 'name');
      }
      if (webhook.assignmentRules && webhook.assignmentRules.length > 0) {
        await webhook.populate('assignmentRules.assignTo', 'name email');
        await webhook.populate('assignmentRules.assignToGroup', 'name');
      }

      res.status(201).json(webhook);
    } catch (error) {
      console.error('Error creating ticket webhook:', error);
      res.status(500).json({ message: 'Error creating ticket webhook', error: error.message });
    }
  },

  /**
   * Get all ticket webhooks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with webhooks
   */
  getAllWebhooks: async (req, res) => {
    try {
      // If user is admin or ticket manager, return all webhooks
      // Otherwise, return only the user's webhooks
      let webhooks;
      if (req.user.roles.includes('admin') || req.user.roles.includes('ticket_manager')) {
        webhooks = await TicketFormsWebhook.find()
          .sort({ createdAt: -1 })
          .populate('createdBy', 'name email')
          .populate('defaults.assignee', 'name email')
          .populate('defaults.group', 'name')
          .populate('assignmentRules.assignTo', 'name email')
          .populate('assignmentRules.assignToGroup', 'name')
          .populate('notifications.notifyFollowers', 'name email');
      } else {
        webhooks = await TicketFormsWebhook.find({ createdBy: req.user.id })
          .sort({ createdAt: -1 })
          .populate('createdBy', 'name email')
          .populate('defaults.assignee', 'name email')
          .populate('defaults.group', 'name')
          .populate('assignmentRules.assignTo', 'name email')
          .populate('assignmentRules.assignToGroup', 'name')
          .populate('notifications.notifyFollowers', 'name email');
      }

      res.json(webhooks);
    } catch (error) {
      console.error('Error getting ticket webhooks:', error);
      res.status(500).json({ message: 'Error getting ticket webhooks', error: error.message });
    }
  },

  /**
   * Process form responses to create tickets
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with processing results
   */
  processWebhook: async (req, res) => {
    try {
      const webhook = await TicketFormsWebhook.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('defaults.assignee', 'name email')
        .populate('defaults.group', 'name')
        .populate('assignmentRules.assignTo', 'name email')
        .populate('assignmentRules.assignToGroup', 'name')
        .populate('notifications.notifyFollowers', 'name email');
      
      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      // Check if user is authorized to process this webhook
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('ticket_manager') && 
          webhook.createdBy._id.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to process this webhook' });
      }

      // Process the webhook
      const result = await ticketFormsWebhookController.processWebhookResponses(webhook, req.user.id);
      
      res.json(result);
    } catch (error) {
      console.error('Error processing ticket webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error processing ticket webhook', error: error.message });
    }
  },

  /**
   * Process all active ticket webhooks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with processing results
   */
  processAllWebhooks: async (req, res) => {
    try {
      // Check if user is admin
      if (!req.user.roles.includes('admin')) {
        return res.status(403).json({ message: 'Not authorized to process all ticket webhooks' });
      }

      // Get all active webhooks
      const webhooks = await TicketFormsWebhook.find({ active: true })
        .populate('createdBy', 'name email')
        .populate('defaults.assignee', 'name email')
        .populate('defaults.group', 'name')
        .populate('assignmentRules.assignTo', 'name email')
        .populate('assignmentRules.assignToGroup', 'name')
        .populate('notifications.notifyFollowers', 'name email');

      // Process each webhook
      const results = [];
      for (const webhook of webhooks) {
        try {
          const result = await ticketFormsWebhookController.processWebhookResponses(webhook, req.user.id);
          results.push({
            webhookId: webhook._id,
            formName: webhook.formName,
            success: true,
            result
          });
        } catch (error) {
          results.push({
            webhookId: webhook._id,
            formName: webhook.formName,
            success: false,
            error: error.message
          });
        }
      }
      
      res.json({
        message: `Processed ${webhooks.length} ticket webhooks`,
        results
      });
    } catch (error) {
      console.error('Error processing all ticket webhooks:', error);
      res.status(500).json({ message: 'Error processing all ticket webhooks', error: error.message });
    }
  },

  /**
   * Process responses for a webhook (internal method)
   * @param {Object} webhook - Webhook document
   * @param {string} userId - User ID processing the webhook
   * @returns {Object} Processing results
   */
  processWebhookResponses: async (webhook, userId) => {
    try {
      // Get API instance (reuse existing Google Forms API setup)
      const api = await ticketFormsWebhookController.getApiWithUser(webhook.createdBy._id || userId);

      // Get form responses
      const formResponses = await api.getFormResponses(webhook.formId);
      
      if (!formResponses || !formResponses.responses) {
        return {
          message: 'No responses found',
          processed: 0,
          newTickets: []
        };
      }

      // Get processed response IDs
      const processedIds = new Set(webhook.processedResponses.map(pr => pr.responseId));
      
      // Filter out already processed responses
      const newResponses = formResponses.responses.filter(response => 
        !processedIds.has(response.responseId)
      );

      if (newResponses.length === 0) {
        return {
          message: 'No new responses to process',
          processed: 0,
          newTickets: []
        };
      }

      // Process each new response
      const newTickets = [];
      let successCount = 0;
      let errorCount = 0;

      for (const response of newResponses) {
        try {
          // Create a ticket from the response
          const ticket = await ticketFormsWebhookController.createTicketFromResponse(
            response, 
            webhook, 
            userId
          );

          // Add to processed responses
          webhook.processedResponses.push({
            responseId: response.responseId,
            processedAt: new Date(),
            ticketId: ticket._id,
            success: true
          });

          newTickets.push({
            responseId: response.responseId,
            ticketId: ticket._id,
            ticketNumber: ticket.ticketNumber,
            subject: ticket.subject
          });

          successCount++;
        } catch (error) {
          console.error(`Error processing response ${response.responseId}:`, error);
          
          // Add failed response to tracking
          webhook.processedResponses.push({
            responseId: response.responseId,
            processedAt: new Date(),
            success: false,
            error: error.message
          });

          errorCount++;
        }
      }

      // Update webhook stats and last checked time
      webhook.lastChecked = new Date();
      webhook.stats.totalProcessed += newResponses.length;
      webhook.stats.successCount += successCount;
      webhook.stats.errorCount += errorCount;
      
      await webhook.save();

      return {
        message: `Processed ${newResponses.length} new responses`,
        processed: newResponses.length,
        successful: successCount,
        failed: errorCount,
        newTickets
      };
    } catch (error) {
      console.error('Error processing webhook responses:', error);
      throw error;
    }
  },

  /**
   * Create a ticket from a form response
   * @param {Object} response - Form response
   * @param {Object} webhook - Webhook document
   * @param {string} userId - User ID creating the ticket
   * @returns {Object} Created ticket
   */
  createTicketFromResponse: async (response, webhook, userId) => {
    try {
      // Extract answer values from the response
      const answers = {};
      if (response.answers) {
        Object.entries(response.answers).forEach(([questionId, answer]) => {
          // Handle different answer types
          if (answer.textAnswers && answer.textAnswers.answers) {
            answers[questionId] = answer.textAnswers.answers.map(a => a.value).join(', ');
          } else if (answer.fileUploadAnswers) {
            answers[questionId] = answer.fileUploadAnswers.answers.map(a => a.fileId).join(', ');
          } else if (answer.dateAnswers) {
            answers[questionId] = answer.dateAnswers.answers.map(a => a.value).join(', ');
          } else if (answer.timeAnswers) {
            answers[questionId] = answer.timeAnswers.answers.map(a => a.value).join(', ');
          } else if (answer.scaleAnswers) {
            answers[questionId] = answer.scaleAnswers.answers.map(a => a.value).join(', ');
          } else {
            // Default fallback
            answers[questionId] = JSON.stringify(answer);
          }
        });
      }

      // Process tokens
      const processedTokens = {};
      if (webhook.tokenMappings && webhook.tokenMappings.length > 0) {
        for (const tokenMapping of webhook.tokenMappings) {
          processedTokens[tokenMapping.token] = await ticketFormsWebhookController.processToken(
            tokenMapping, 
            answers, 
            response
          );
        }
      }

      // Map form fields to ticket properties using token system
      const { fieldMappings } = webhook;
      
      // Required fields
      const subject = ticketFormsWebhookController.processFieldMapping(
        fieldMappings.subjectField, 
        answers, 
        processedTokens
      ) || 'Form Response';
      
      // Optional fields with defaults
      const description = fieldMappings.descriptionField ? 
        ticketFormsWebhookController.processFieldMapping(fieldMappings.descriptionField, answers, processedTokens) : 
        `Ticket created from form: ${webhook.formName}`;
      
      const priority = fieldMappings.priorityField ? 
        ticketFormsWebhookController.mapPriority(
          ticketFormsWebhookController.processFieldMapping(fieldMappings.priorityField, answers, processedTokens)
        ) : webhook.defaults.priority || 'normal';
      
      const type = fieldMappings.typeField ?
        ticketFormsWebhookController.mapType(
          ticketFormsWebhookController.processFieldMapping(fieldMappings.typeField, answers, processedTokens)
        ) : webhook.defaults.type || 'request';

      // Parse due date if provided
      let dueDate = null;
      if (fieldMappings.dueDateField) {
        const dueDateValue = ticketFormsWebhookController.processFieldMapping(
          fieldMappings.dueDateField, 
          answers, 
          processedTokens
        );
        if (dueDateValue) {
          try {
            dueDate = new Date(dueDateValue);
          } catch (error) {
            console.warn(`Invalid due date format: ${dueDateValue}`);
          }
        }
      }
      
      // Parse tags if provided
      let tags = [...(webhook.defaults.tags || [])];
      if (fieldMappings.tagsField) {
        const tagsValue = ticketFormsWebhookController.processFieldMapping(
          fieldMappings.tagsField, 
          answers, 
          processedTokens
        );
        if (tagsValue) {
          const formTags = tagsValue.split(',').map(tag => tag.trim()).filter(tag => tag);
          tags = [...new Set([...tags, ...formTags])];
        }
      }

      // Determine requester
      let requester = webhook.createdBy._id;
      if (fieldMappings.requesterEmailField) {
        const requesterEmail = ticketFormsWebhookController.processFieldMapping(
          fieldMappings.requesterEmailField, 
          answers, 
          processedTokens
        );
        if (requesterEmail) {
          const requesterUser = await User.findOne({ email: requesterEmail });
          if (requesterUser) {
            requester = requesterUser._id;
          } else {
            // Create external user
            const requesterName = fieldMappings.requesterNameField ?
              ticketFormsWebhookController.processFieldMapping(
                fieldMappings.requesterNameField, 
                answers, 
                processedTokens
              ) : requesterEmail;
            
            const newUser = new User({
              name: requesterName,
              email: requesterEmail,
              authType: 'local',
              roles: ['external_user'],
              isActive: false
            });
            await newUser.save();
            requester = newUser._id;
          }
        }
      }

      // Determine assignment based on rules
      let assignedTo = webhook.defaults.assignee;
      let assignedGroup = webhook.defaults.group;
      let category = webhook.defaults.category;
      let additionalTags = [];

      if (webhook.assignmentRules && webhook.assignmentRules.length > 0) {
        // Sort rules by priority
        const sortedRules = [...webhook.assignmentRules].sort((a, b) => a.priority - b.priority);
        
        // Find the first matching rule
        for (const rule of sortedRules) {
          const fieldValue = ticketFormsWebhookController.processFieldMapping(
            rule.field, 
            answers, 
            processedTokens
          );
          
          if (fieldValue && ticketFormsWebhookController.matchesRule(fieldValue, rule)) {
            if (rule.assignTo) assignedTo = rule.assignTo;
            if (rule.assignToGroup) assignedGroup = rule.assignToGroup;
            if (rule.category) category = rule.category;
            if (rule.tags && rule.tags.length > 0) {
              additionalTags = [...additionalTags, ...rule.tags];
            }
            break; // Use first matching rule
          }
        }
      }

      // Add rule-based tags
      if (additionalTags.length > 0) {
        tags = [...new Set([...tags, ...additionalTags])];
      }

      // Create ticket data
      const ticketData = {
        subject,
        description,
        priority,
        type,
        status: 'open',
        dueDate,
        tags,
        category,
        assignedTo,
        assignedGroup,
        requester,
        source: 'form',
        sourceMetadata: new Map({
          formId: webhook.formId,
          formName: webhook.formName,
          responseId: response.responseId,
          webhookId: webhook._id.toString()
        })
      };

      // Add followers
      const followers = [];
      if (webhook.notifications.notifyFollowers && webhook.notifications.notifyFollowers.length > 0) {
        followers.push(...webhook.notifications.notifyFollowers);
      }
      if (followers.length > 0) {
        ticketData.followers = followers;
      }

      // Process custom fields
      if (fieldMappings.customFields && fieldMappings.customFields.length > 0) {
        const customFields = {};
        fieldMappings.customFields.forEach(customField => {
          const value = ticketFormsWebhookController.processFieldMapping(
            customField.formField, 
            answers, 
            processedTokens
          );
          if (value) {
            let processedValue = value;
            
            // Apply transformations
            switch (customField.transform) {
              case 'email_to_user':
                // Try to find user by email
                User.findOne({ email: value }).then(user => {
                  if (user) processedValue = user._id;
                });
                break;
              case 'text_to_tags':
                processedValue = value.split(',').map(tag => tag.trim());
                break;
              case 'date_parse':
                try {
                  processedValue = new Date(value);
                } catch (e) {
                  processedValue = value;
                }
                break;
              case 'priority_map':
                processedValue = ticketFormsWebhookController.mapPriority(value);
                break;
            }
            
            customFields[customField.ticketField] = processedValue;
          }
        });
        
        if (Object.keys(customFields).length > 0) {
          ticketData.customFields = new Map(Object.entries(customFields));
        }
      }

      // Create the ticket
      const ticket = new Ticket(ticketData);
      await ticket.save();
      
      // Populate references
      await ticket.populate('requester', 'name email');
      if (assignedTo) {
        await ticket.populate('assignedTo', 'name email');
      }
      if (assignedGroup) {
        await ticket.populate('assignedGroup', 'name');
      }
      if (followers.length > 0) {
        await ticket.populate('followers', 'name email');
      }
      
      return ticket;
    } catch (error) {
      console.error('Error creating ticket from form response:', error);
      throw error;
    }
  },

  /**
   * Get webhook by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with webhook
   */
  getWebhookById: async (req, res) => {
    try {
      const webhook = await TicketFormsWebhook.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('defaults.assignee', 'name email')
        .populate('defaults.group', 'name')
        .populate('assignmentRules.assignTo', 'name email')
        .populate('assignmentRules.assignToGroup', 'name')
        .populate('notifications.notifyFollowers', 'name email')
        .populate('processedResponses.ticketId');

      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }

      // Check if user is authorized to view this webhook
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('ticket_manager') && 
          webhook.createdBy._id.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to view this webhook' });
      }

      res.json(webhook);
    } catch (error) {
      console.error('Error getting webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error getting webhook', error: error.message });
    }
  },

  /**
   * Update a webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated webhook
   */
  updateWebhook: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const webhook = await TicketFormsWebhook.findById(req.params.id);
      
      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      // Check if user is authorized to update this webhook
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('ticket_manager') && 
          webhook.createdBy.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to update this webhook' });
      }
      
      const {
        formName,
        active,
        fieldMappings,
        tokenMappings,
        assignmentRules,
        defaults,
        notifications
      } = req.body;

      // Update webhook fields
      if (formName) webhook.formName = formName;
      if (active !== undefined) webhook.active = active;
      if (fieldMappings) webhook.fieldMappings = fieldMappings;
      if (tokenMappings) webhook.tokenMappings = tokenMappings;
      if (assignmentRules) webhook.assignmentRules = assignmentRules;
      if (defaults) webhook.defaults = defaults;
      if (notifications) webhook.notifications = notifications;

      await webhook.save();
      
      // Populate references for response
      await webhook.populate('createdBy', 'name email');
      if (webhook.defaults.assignee) {
        await webhook.populate('defaults.assignee', 'name email');
      }
      if (webhook.defaults.group) {
        await webhook.populate('defaults.group', 'name');
      }
      if (webhook.assignmentRules && webhook.assignmentRules.length > 0) {
        await webhook.populate('assignmentRules.assignTo', 'name email');
        await webhook.populate('assignmentRules.assignToGroup', 'name');
      }
      if (webhook.notifications.notifyFollowers && webhook.notifications.notifyFollowers.length > 0) {
        await webhook.populate('notifications.notifyFollowers', 'name email');
      }
      
      res.json(webhook);
    } catch (error) {
      console.error('Error updating webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error updating webhook', error: error.message });
    }
  },

  /**
   * Delete a webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message
   */
  deleteWebhook: async (req, res) => {
    try {
      const webhook = await TicketFormsWebhook.findById(req.params.id);
      
      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      // Check if user is authorized to delete this webhook (admin only)
      if (!req.user.roles.includes('admin')) {
        return res.status(403).json({ message: 'Not authorized to delete webhooks' });
      }
      
      await webhook.deleteOne();
      
      res.json({ message: 'Webhook deleted successfully' });
    } catch (error) {
      console.error('Error deleting webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error deleting webhook', error: error.message });
    }
  },

  // Helper methods
  getApiWithUser: async (userId) => {
    // Reuse the existing Google Forms API helper from the original webhook controller
    const User = require('../../models/User');
    const GoogleFormsAPI = require('../integrations/googleForms/googleFormsAPI');
    
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
      const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
      const usingServiceAccount = !!(serviceAccountEmail && serviceAccountPrivateKey);

      if (!usingServiceAccount) {
        throw new Error('Service account credentials are required for webhook functionality');
      }

      if (!user.email) {
        throw new Error('User email is required for service account impersonation');
      }

      const api = new GoogleFormsAPI(
        process.env.GOOGLE_FORMS_CLIENT_ID || '',
        process.env.GOOGLE_FORMS_CLIENT_SECRET || '',
        process.env.GOOGLE_FORMS_REDIRECT_URI || '',
        process.env.GOOGLE_FORMS_TOKEN_PATH || './google-forms-token.json',
        null,
        userId,
        user.email
      );

      await api.initialize();
      return api;
    } catch (error) {
      console.error('Error creating API with user:', error);
      throw error;
    }
  },

  processToken: async (tokenMapping, answers, response) => {
    switch (tokenMapping.type) {
      case 'static':
        return tokenMapping.value;
      
      case 'form_field':
        return answers[tokenMapping.value] || '';
      
      case 'user_lookup':
        const email = answers[tokenMapping.value];
        if (email) {
          const user = await User.findOne({ email });
          return user ? user._id : null;
        }
        return null;
      
      case 'date_calc':
        const daysToAdd = parseInt(tokenMapping.value) || 0;
        const date = new Date();
        date.setDate(date.getDate() + daysToAdd);
        return date;
      
      default:
        return tokenMapping.value;
    }
  },

  processFieldMapping: (fieldMapping, answers, processedTokens) => {
    if (!fieldMapping) return null;
    
    // Check if it's a token (starts with $)
    if (fieldMapping.startsWith('$')) {
      const tokenName = fieldMapping.substring(1);
      return processedTokens[tokenName] || null;
    }
    
    // Otherwise it's a direct form field
    return answers[fieldMapping] || null;
  },

  matchesRule: (fieldValue, rule) => {
    const value = (fieldValue || '').toString().toLowerCase();
    const ruleValue = rule.value.toLowerCase();
    
    switch (rule.operator) {
      case 'equals':
        return value === ruleValue;
      case 'contains':
        return value.includes(ruleValue);
      case 'starts_with':
        return value.startsWith(ruleValue);
      case 'ends_with':
        return value.endsWith(ruleValue);
      case 'regex':
        try {
          const regex = new RegExp(rule.value, 'i');
          return regex.test(value);
        } catch (e) {
          console.error('Invalid regex in assignment rule:', rule.value);
          return false;
        }
      default:
        return false;
    }
  },

  mapPriority: (value) => {
    if (!value) return 'normal';
    
    const lowerValue = value.toLowerCase();
    
    if (lowerValue.includes('urgent') || lowerValue === '4') {
      return 'urgent';
    } else if (lowerValue.includes('high') || lowerValue === '3') {
      return 'high';
    } else if (lowerValue.includes('low') || lowerValue === '1') {
      return 'low';
    } else {
      return 'normal';
    }
  },

  mapType: (value) => {
    if (!value) return 'request';
    
    const lowerValue = value.toLowerCase();
    
    if (lowerValue.includes('incident')) {
      return 'incident';
    } else if (lowerValue.includes('problem')) {
      return 'problem';
    } else if (lowerValue.includes('change')) {
      return 'change';
    } else {
      return 'request';
    }
  }
};

module.exports = ticketFormsWebhookController;