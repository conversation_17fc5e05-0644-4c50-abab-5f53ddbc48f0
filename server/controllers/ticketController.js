const Ticket = require('../../models/Ticket');
const TicketComment = require('../../models/TicketComment');
const TicketCategory = require('../../models/TicketCategory');
const TicketTag = require('../../models/TicketTag');
const User = require('../../models/User');
const { validationResult } = require('express-validator');
const nodemailer = require('nodemailer');

/**
 * Ticket Controller
 * Handles API operations for tickets
 */
const ticketController = {
  /**
   * Send ticket creation email to requester email
   * @param {Object} ticket - Ticket object
   * @param {Object} creator - User who created the ticket
   */
  sendTicketCreationEmail: async (ticket, creator) => {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.log('SMTP configuration not found, email notification skipped');
      return;
    }

    try {
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });

      const ticketUrl = `${process.env.PORTAL_URL || 'http://localhost:3000'}/tickets/${ticket._id}`;
      
      const emailContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1976d2; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .ticket-info { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .button { display: inline-block; padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Support Ticket Created</h1>
            </div>
            <div class="content">
              <p>A new support ticket has been created in the CSF Portal.</p>
              
              <div class="ticket-info">
                <h3>Ticket Details</h3>
                <p><strong>Ticket Number:</strong> ${ticket.ticketNumber}</p>
                <p><strong>Subject:</strong> ${ticket.subject}</p>
                <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
                <p><strong>Type:</strong> ${ticket.type.charAt(0).toUpperCase() + ticket.type.slice(1)}</p>
                <p><strong>Status:</strong> ${ticket.status.replace('_', ' ').toUpperCase()}</p>
                <p><strong>Created by:</strong> ${creator.name} (${creator.email})</p>
                <p><strong>Created:</strong> ${ticket.createdAt.toLocaleString()}</p>
                ${ticket.category ? `<p><strong>Category:</strong> ${ticket.category}</p>` : ''}
                ${ticket.dueDate ? `<p><strong>Due Date:</strong> ${new Date(ticket.dueDate).toLocaleDateString()}</p>` : ''}
              </div>
              
              <div class="ticket-info">
                <h3>Description</h3>
                <p>${ticket.description.replace(/\n/g, '<br>')}</p>
              </div>
              
              <p style="text-align: center;">
                <a href="${ticketUrl}" class="button">View Ticket</a>
              </p>
            </div>
            <div class="footer">
              <p>CSF Portal - Ticket Management System</p>
              <p>Please do not reply to this email. Use the portal to add comments to the ticket.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      await transporter.sendMail({
        from: process.env.TICKET_EMAIL_FROM || process.env.SMTP_USER,
        to: ticket.requesterEmail,
        subject: `New Ticket Created: ${ticket.ticketNumber} - ${ticket.subject}`,
        html: emailContent,
        headers: {
          'References': ticket._id,
          'X-Ticket-ID': ticket.ticketNumber
        }
      });

      console.log(`Ticket creation email sent to ${ticket.requesterEmail}`);
    } catch (error) {
      console.error('Error sending ticket creation email:', error);
      throw error;
    }
  },
  /**
   * Get all tickets with filtering and pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with tickets
   */
  getAllTickets: async (req, res) => {
    try {
      const {
        status,
        priority,
        type,
        category,
        assignedTo,
        requester,
        tags,
        search,
        page = 1,
        limit = 50,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      // Build filter object
      const filter = {};
      
      // Status filter
      if (status) {
        filter.status = Array.isArray(status) ? { $in: status } : status;
      }
      
      // Priority filter
      if (priority) {
        filter.priority = Array.isArray(priority) ? { $in: priority } : priority;
      }
      
      // Type filter
      if (type) {
        filter.type = Array.isArray(type) ? { $in: type } : type;
      }
      
      // Category filter
      if (category) {
        filter.category = category;
      }
      
      // Assignment filters
      if (assignedTo) {
        filter.assignedTo = assignedTo;
      }
      
      if (requester) {
        filter.requester = requester;
      }
      
      // Tags filter
      if (tags) {
        const tagArray = Array.isArray(tags) ? tags : tags.split(',');
        filter.tags = { $in: tagArray };
      }
      
      // Text search
      if (search) {
        filter.$text = { $search: search };
      }
      
      // Role-based access control
      if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
        // Regular users can only see tickets they requested, are assigned to, or follow
        filter.$or = [
          { requester: req.user.id },
          { assignedTo: req.user.id },
          { followers: req.user.id }
        ];
      }
      
      // Build sort object
      const sort = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      
      // Execute query with pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      const [tickets, totalCount] = await Promise.all([
        Ticket.find(filter)
          .sort(sort)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('requester', 'name email')
          .populate('assignedTo', 'name email')
          .populate('assignedGroup', 'name')
          .populate('followers', 'name email'),
        Ticket.countDocuments(filter)
      ]);
      
      res.json({
        tickets,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          pages: Math.ceil(totalCount / parseInt(limit))
        }
      });
    } catch (err) {
      console.error('Error getting tickets:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Get ticket by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with ticket
   */
  getTicketById: async (req, res) => {
    try {
      const ticket = await Ticket.findById(req.params.id)
        .populate('requester', 'name email')
        .populate('assignedTo', 'name email')
        .populate('assignedGroup', 'name')
        .populate('followers', 'name email');
      
      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      // Check access permissions
      if (!ticketController.canAccessTicket(req.user, ticket)) {
        return res.status(403).json({ message: 'Not authorized to view this ticket' });
      }
      
      // Get ticket comments
      const comments = await TicketComment.find({ ticket: ticket._id })
        .sort({ createdAt: 1 })
        .populate('author', 'name email');
      
      res.json({
        ticket,
        comments
      });
    } catch (err) {
      console.error('Error getting ticket:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Create a new ticket
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created ticket
   */
  createTicket: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const {
        subject,
        description,
        priority = 'normal',
        type = 'incident',
        status = 'open',
        category,
        subcategory,
        tags = [],
        assignedTo,
        assignedGroup,
        requester,
        requesterEmail,
        dueDate,
        followers = [],
        customFields = {},
        source = 'portal',
        sourceMetadata = {}
      } = req.body;

      // Use current user as requester if not specified and no requesterEmail
      const ticketRequester = requester || (requesterEmail ? null : req.user.id);

      // Create ticket
      const ticket = new Ticket({
        subject,
        description,
        priority,
        type,
        status,
        category,
        subcategory,
        tags,
        assignedTo,
        assignedGroup,
        requester: ticketRequester,
        requesterEmail,
        dueDate,
        followers,
        customFields,
        source,
        sourceMetadata
      });

      // Auto-assign category based on rules if not provided
      if (!category) {
        const autoCategory = await ticketController.autoAssignCategory(subject, description);
        if (autoCategory) {
          ticket.category = autoCategory.name;
          if (!assignedTo && autoCategory.defaultAssignee) {
            ticket.assignedTo = autoCategory.defaultAssignee;
          }
          if (!assignedGroup && autoCategory.defaultGroup) {
            ticket.assignedGroup = autoCategory.defaultGroup;
          }
        }
      }

      // Auto-assign tags based on rules
      const autoTags = await ticketController.autoAssignTags(subject, description, category);
      if (autoTags.length > 0) {
        ticket.tags = [...new Set([...ticket.tags, ...autoTags])];
      }

      await ticket.save();
      
      // Populate references for response
      await ticket.populate('requester', 'name email');
      if (ticket.assignedTo) {
        await ticket.populate('assignedTo', 'name email');
      }
      if (ticket.assignedGroup) {
        await ticket.populate('assignedGroup', 'name');
      }
      if (ticket.followers.length > 0) {
        await ticket.populate('followers', 'name email');
      }
      
      // Send email directly to requesterEmail if provided
      if (ticket.requesterEmail) {
        try {
          await ticketController.sendTicketCreationEmail(ticket, req.user);
        } catch (emailError) {
          console.error('Error sending ticket creation email:', emailError);
          // Don't fail the request if email sending fails
        }
      }
      
      res.status(201).json(ticket);
    } catch (err) {
      console.error('Error creating ticket:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Update a ticket
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated ticket
   */
  updateTicket: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const ticket = await Ticket.findById(req.params.id);
      
      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      // Check permissions
      if (!ticketController.canEditTicket(req.user, ticket)) {
        return res.status(403).json({ message: 'Not authorized to update this ticket' });
      }
      
      const {
        subject,
        description,
        priority,
        status,
        type,
        category,
        subcategory,
        tags,
        assignedTo,
        assignedGroup,
        requester,
        dueDate,
        followers,
        customFields
      } = req.body;

      // Track changes for notifications
      const changes = [];
      
      // Update fields and track changes
      if (subject && subject !== ticket.subject) {
        changes.push({ field: 'subject', from: ticket.subject, to: subject });
        ticket.subject = subject;
      }
      
      if (description && description !== ticket.description) {
        changes.push({ field: 'description', from: ticket.description, to: description });
        ticket.description = description;
      }
      
      if (priority && priority !== ticket.priority) {
        changes.push({ field: 'priority', from: ticket.priority, to: priority });
        ticket.priority = priority;
      }
      
      if (status && status !== ticket.status) {
        changes.push({ field: 'status', from: ticket.status, to: status });
        ticket.status = status;
        
        // Set resolution/close timestamps
        if (status === 'resolved' && ticket.status !== 'resolved') {
          ticket.resolvedAt = new Date();
        }
        if (status === 'closed' && ticket.status !== 'closed') {
          ticket.closedAt = new Date();
        }
      }
      
      if (type && type !== ticket.type) {
        changes.push({ field: 'type', from: ticket.type, to: type });
        ticket.type = type;
      }
      
      if (category && category !== ticket.category) {
        changes.push({ field: 'category', from: ticket.category, to: category });
        ticket.category = category;
      }
      
      if (subcategory !== undefined && subcategory !== ticket.subcategory) {
        changes.push({ field: 'subcategory', from: ticket.subcategory, to: subcategory });
        ticket.subcategory = subcategory;
      }
      
      if (tags && JSON.stringify(tags) !== JSON.stringify(ticket.tags)) {
        changes.push({ field: 'tags', from: ticket.tags, to: tags });
        ticket.tags = tags;
      }
      
      if (assignedTo !== undefined && String(assignedTo) !== String(ticket.assignedTo)) {
        changes.push({ field: 'assignedTo', from: ticket.assignedTo, to: assignedTo });
        ticket.assignedTo = assignedTo;
      }
      
      if (assignedGroup !== undefined && String(assignedGroup) !== String(ticket.assignedGroup)) {
        changes.push({ field: 'assignedGroup', from: ticket.assignedGroup, to: assignedGroup });
        ticket.assignedGroup = assignedGroup;
      }
      
      if (requester !== undefined && String(requester) !== String(ticket.requester)) {
        changes.push({ field: 'requester', from: ticket.requester, to: requester });
        ticket.requester = requester;
      }
      
      if (dueDate !== undefined && String(dueDate) !== String(ticket.dueDate)) {
        changes.push({ field: 'dueDate', from: ticket.dueDate, to: dueDate });
        ticket.dueDate = dueDate;
      }
      
      if (followers && JSON.stringify(followers) !== JSON.stringify(ticket.followers)) {
        changes.push({ field: 'followers', from: ticket.followers, to: followers });
        ticket.followers = followers;
      }
      
      if (customFields) {
        ticket.customFields = new Map(Object.entries(customFields));
      }

      await ticket.save();
      
      // Create system comment for changes
      if (changes.length > 0) {
        const changeText = changes.map(change => 
          `${change.field} changed from "${change.from}" to "${change.to}"`
        ).join(', ');
        
        await TicketComment.create({
          ticket: ticket._id,
          author: req.user.id,
          content: `Ticket updated: ${changeText}`,
          type: 'system',
          isPublic: false
        });
      }
      
      // Populate references for response
      await ticket.populate('requester', 'name email');
      if (ticket.assignedTo) {
        await ticket.populate('assignedTo', 'name email');
      }
      if (ticket.assignedGroup) {
        await ticket.populate('assignedGroup', 'name');
      }
      if (ticket.followers.length > 0) {
        await ticket.populate('followers', 'name email');
      }
      
      res.json(ticket);
    } catch (err) {
      console.error('Error updating ticket:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Delete a ticket
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message
   */
  deleteTicket: async (req, res) => {
    try {
      const ticket = await Ticket.findById(req.params.id);
      
      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      // Check permissions (only admins can delete tickets)
      if (!req.user.roles.includes('admin')) {
        return res.status(403).json({ message: 'Not authorized to delete tickets' });
      }
      
      // Delete associated comments
      await TicketComment.deleteMany({ ticket: ticket._id });
      
      // Delete the ticket
      await ticket.deleteOne();
      
      res.json({ message: 'Ticket deleted successfully' });
    } catch (err) {
      console.error('Error deleting ticket:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Add a comment to a ticket
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created comment
   */
  addComment: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const ticket = await Ticket.findById(req.params.id);
      
      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      // Check permissions
      if (!ticketController.canAccessTicket(req.user, ticket)) {
        return res.status(403).json({ message: 'Not authorized to comment on this ticket' });
      }
      
      const {
        content,
        type = 'comment',
        isPublic = true,
        attachments = []
      } = req.body;
      
      // Check if this is the first response
      const existingComments = await TicketComment.countDocuments({ 
        ticket: ticket._id, 
        type: { $in: ['comment', 'email_inbound', 'email_outbound'] }
      });
      const isFirstResponse = existingComments === 0;
      
      // Create comment
      const comment = new TicketComment({
        ticket: ticket._id,
        content,
        type,
        isPublic,
        author: req.user.id,
        authorEmail: req.user.email,
        authorName: req.user.name,
        attachments,
        isFirstResponse
      });

      await comment.save();
      
      // Update ticket first response time
      if (isFirstResponse) {
        ticket.firstResponseAt = new Date();
        await ticket.save();
      }
      
      // Populate author for response
      await comment.populate('author', 'name email');
      
      res.status(201).json(comment);
    } catch (err) {
      console.error('Error adding comment:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Ticket not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Get ticket statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with statistics
   */
  getTicketStats: async (req, res) => {
    try {
      const { timeframe = '30d' } = req.query;
      
      // Calculate date range
      const now = new Date();
      let startDate;
      
      switch (timeframe) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }
      
      // Build base filter for user permissions
      const baseFilter = {};
      if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
        baseFilter.$or = [
          { requester: req.user.id },
          { assignedTo: req.user.id },
          { followers: req.user.id }
        ];
      }
      
      // Aggregate statistics
      const [
        totalTickets,
        openTickets,
        closedTickets,
        statusBreakdown,
        priorityBreakdown,
        categoryBreakdown,
        avgResolutionTime,
        recentTickets
      ] = await Promise.all([
        Ticket.countDocuments({ ...baseFilter, createdAt: { $gte: startDate } }),
        Ticket.countDocuments({ ...baseFilter, status: { $in: ['open', 'pending', 'on_hold'] } }),
        Ticket.countDocuments({ ...baseFilter, status: { $in: ['resolved', 'closed'] } }),
        Ticket.aggregate([
          { $match: { ...baseFilter, createdAt: { $gte: startDate } } },
          { $group: { _id: '$status', count: { $sum: 1 } } }
        ]),
        Ticket.aggregate([
          { $match: { ...baseFilter, createdAt: { $gte: startDate } } },
          { $group: { _id: '$priority', count: { $sum: 1 } } }
        ]),
        Ticket.aggregate([
          { $match: { ...baseFilter, createdAt: { $gte: startDate }, category: { $ne: null } } },
          { $group: { _id: '$category', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]),
        Ticket.aggregate([
          {
            $match: {
              ...baseFilter,
              resolvedAt: { $ne: null },
              createdAt: { $gte: startDate }
            }
          },
          {
            $project: {
              resolutionTime: {
                $divide: [
                  { $subtract: ['$resolvedAt', '$createdAt'] },
                  1000 * 60 * 60 // Convert to hours
                ]
              }
            }
          },
          {
            $group: {
              _id: null,
              avgResolutionTime: { $avg: '$resolutionTime' }
            }
          }
        ]),
        Ticket.find(baseFilter)
          .sort({ createdAt: -1 })
          .limit(5)
          .populate('requester', 'name email')
          .populate('assignedTo', 'name email')
      ]);
      
      res.json({
        summary: {
          total: totalTickets,
          open: openTickets,
          closed: closedTickets,
          avgResolutionTimeHours: avgResolutionTime[0]?.avgResolutionTime || 0
        },
        breakdowns: {
          status: statusBreakdown,
          priority: priorityBreakdown,
          category: categoryBreakdown
        },
        recentTickets,
        timeframe
      });
    } catch (err) {
      console.error('Error getting ticket stats:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  // Helper methods
  canAccessTicket: (user, ticket) => {
    if (user.roles.includes('admin') || user.roles.includes('ticket_manager')) {
      return true;
    }
    
    return (
      String(ticket.requester) === String(user.id) ||
      String(ticket.assignedTo) === String(user.id) ||
      ticket.followers.some(follower => String(follower) === String(user.id))
    );
  },

  canEditTicket: (user, ticket) => {
    if (user.roles.includes('admin') || user.roles.includes('ticket_manager')) {
      return true;
    }
    
    return (
      String(ticket.requester) === String(user.id) ||
      String(ticket.assignedTo) === String(user.id)
    );
  },

  autoAssignCategory: async (subject, description) => {
    try {
      const categories = await TicketCategory.find({ 
        isActive: true,
        'autoRules.0': { $exists: true }
      });
      
      const text = `${subject} ${description}`.toLowerCase();
      
      let bestMatch = null;
      let highestPriority = -1;
      
      for (const category of categories) {
        for (const rule of category.autoRules) {
          let matches = false;
          const value = rule.value.toLowerCase();
          
          switch (rule.operator) {
            case 'contains':
              matches = text.includes(value);
              break;
            case 'starts_with':
              matches = text.startsWith(value);
              break;
            case 'ends_with':
              matches = text.endsWith(value);
              break;
            case 'equals':
              matches = text === value;
              break;
            case 'regex':
              try {
                const regex = new RegExp(rule.value, 'i');
                matches = regex.test(text);
              } catch (e) {
                console.error('Invalid regex in category rule:', rule.value);
              }
              break;
          }
          
          if (matches && rule.priority > highestPriority) {
            bestMatch = category;
            highestPriority = rule.priority;
          }
        }
      }
      
      return bestMatch;
    } catch (error) {
      console.error('Error in auto-assign category:', error);
      return null;
    }
  },

  autoAssignTags: async (subject, description, category) => {
    try {
      const tags = await TicketTag.find({ 
        isActive: true,
        'autoRules.0': { $exists: true }
      });
      
      const text = `${subject} ${description}`.toLowerCase();
      const assignedTags = [];
      
      for (const tag of tags) {
        for (const rule of tag.autoRules) {
          let matches = false;
          let testValue = '';
          
          switch (rule.field) {
            case 'subject':
              testValue = subject.toLowerCase();
              break;
            case 'description':
              testValue = description.toLowerCase();
              break;
            case 'category':
              testValue = (category || '').toLowerCase();
              break;
            default:
              testValue = text;
          }
          
          const ruleValue = rule.value.toLowerCase();
          
          switch (rule.operator) {
            case 'contains':
              matches = testValue.includes(ruleValue);
              break;
            case 'starts_with':
              matches = testValue.startsWith(ruleValue);
              break;
            case 'ends_with':
              matches = testValue.endsWith(ruleValue);
              break;
            case 'equals':
              matches = testValue === ruleValue;
              break;
            case 'regex':
              try {
                const regex = new RegExp(rule.value, 'i');
                matches = regex.test(testValue);
              } catch (e) {
                console.error('Invalid regex in tag rule:', rule.value);
              }
              break;
          }
          
          if (matches) {
            assignedTags.push(tag.name);
            break; // Only need one rule to match per tag
          }
        }
      }
      
      return [...new Set(assignedTags)]; // Remove duplicates
    } catch (error) {
      console.error('Error in auto-assign tags:', error);
      return [];
    }
  }
};

module.exports = ticketController;