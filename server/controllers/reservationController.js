const Reservation = require('../../models/Reservation');
const Room = require('../../models/Room');
const User = require('../../models/User');
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Types;
const GoogleCalendarAPI = require('../integrations/googleCalendar/googleCalendarAPI');

/**
 * Reservation Controller
 * Handles all reservation-related operations
 */
const reservationController = {
  /**
   * Get all reservations
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllReservations: async (req, res) => {
    try {
      const { 
        roomId, 
        userId, 
        status, 
        startDate, 
        endDate, 
        isRecurring,
        requiresApproval,
        search
      } = req.query;
      
      // Build query
      const query = {};
      
      if (roomId) {
        query.roomId = roomId;
      }
      
      if (userId) {
        query.userId = userId;
      } else if (!req.user.isAdmin) {
        // If not admin, only show user's own reservations
        query.userId = req.user._id;
      }
      
      if (status) {
        query.status = status;
      }
      
      if (startDate) {
        query.startTime = { $gte: new Date(startDate) };
      }
      
      if (endDate) {
        query.endTime = { $lte: new Date(endDate) };
      }
      
      if (isRecurring !== undefined) {
        query['recurrence.isRecurring'] = isRecurring === 'true';
      }
      
      if (requiresApproval !== undefined) {
        query['approvalStatus.isApprovalRequired'] = requiresApproval === 'true';
      }
      
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      // Get reservations
      const reservations = await Reservation.find(query)
        .populate('roomId', 'name roomNumber floorId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email')
        .populate('approvalStatus.approvedBy', 'name email')
        .populate('approvalStatus.rejectedBy', 'name email')
        .sort({ startTime: 1 });
      
      res.json(reservations);
    } catch (error) {
      console.error('Error getting reservations:', error);
      res.status(500).json({ message: 'Error getting reservations', error: error.message });
    }
  },
  
  /**
   * Get reservation by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getReservationById: async (req, res) => {
    try {
      const { id } = req.params;
      
      const reservation = await Reservation.findById(id)
        .populate('roomId', 'name roomNumber floorId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email')
        .populate('approvalStatus.approvedBy', 'name email')
        .populate('approvalStatus.rejectedBy', 'name email')
        .populate('attendees.userId', 'name email');
      
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to view this reservation
      if (!req.user.isAdmin && reservation.userId._id.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'You do not have permission to view this reservation' });
      }
      
      res.json(reservation);
    } catch (error) {
      console.error('Error getting reservation:', error);
      res.status(500).json({ message: 'Error getting reservation', error: error.message });
    }
  },
  
  /**
   * Create a new reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createReservation: async (req, res) => {
    try {
      const { 
        roomId, 
        title, 
        description, 
        startTime, 
        endTime, 
        recurrence,
        attendees,
        metadata
      } = req.body;
      
      // Validate room exists
      const room = await Room.findById(roomId);
      if (!room) {
        return res.status(404).json({ message: 'Room not found' });
      }
      
      // Validate dates
      const start = new Date(startTime);
      const end = new Date(endTime);
      
      if (start >= end) {
        return res.status(400).json({ message: 'End time must be after start time' });
      }
      
      // Check if room is available during the requested time
      const isAvailable = await reservationController.checkRoomAvailability(roomId, start, end, null);
      if (!isAvailable) {
        return res.status(400).json({ message: 'Room is not available during the requested time' });
      }
      
      // Determine if approval is required
      const requiresApproval = room.metadata && room.metadata.requiresApproval;
      
      // Create reservation
      const reservation = new Reservation({
        roomId,
        userId: req.user._id,
        title,
        description,
        startTime: start,
        endTime: end,
        recurrence,
        attendees: attendees || [],
        status: requiresApproval ? 'pending' : 'approved',
        approvalStatus: {
          isApprovalRequired: requiresApproval,
        },
        metadata
      });
      
      await reservation.save();
      
      // If the room has a Google Calendar ID, create a calendar event
      if (room.calendarId) {
        try {
          await reservationController.createGoogleCalendarEvent(reservation, room);
        } catch (calendarError) {
          console.error('Error creating Google Calendar event:', calendarError);
          // Continue with the reservation creation even if calendar event creation fails
        }
      }
      
      // Populate the response
      const populatedReservation = await Reservation.findById(reservation._id)
        .populate('roomId', 'name roomNumber floorId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email');
      
      res.status(201).json(populatedReservation);
    } catch (error) {
      console.error('Error creating reservation:', error);
      res.status(500).json({ message: 'Error creating reservation', error: error.message });
    }
  },
  
  /**
   * Update a reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateReservation: async (req, res) => {
    try {
      const { id } = req.params;
      const { 
        roomId, 
        title, 
        description, 
        startTime, 
        endTime, 
        recurrence,
        attendees,
        status,
        metadata
      } = req.body;
      
      // Validate reservation exists
      const reservation = await Reservation.findById(id);
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to update this reservation
      if (!req.user.isAdmin && reservation.userId.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'You do not have permission to update this reservation' });
      }
      
      // If changing room, validate new room exists
      let room = null;
      if (roomId && roomId !== reservation.roomId.toString()) {
        room = await Room.findById(roomId);
        if (!room) {
          return res.status(404).json({ message: 'Room not found' });
        }
      } else {
        room = await Room.findById(reservation.roomId);
      }
      
      // If changing time, validate dates and check availability
      let start = reservation.startTime;
      let end = reservation.endTime;
      
      if (startTime || endTime) {
        start = startTime ? new Date(startTime) : reservation.startTime;
        end = endTime ? new Date(endTime) : reservation.endTime;
        
        if (start >= end) {
          return res.status(400).json({ message: 'End time must be after start time' });
        }
        
        // Check if room is available during the requested time (excluding this reservation)
        const isAvailable = await reservationController.checkRoomAvailability(
          roomId || reservation.roomId, 
          start, 
          end, 
          id
        );
        
        if (!isAvailable) {
          return res.status(400).json({ message: 'Room is not available during the requested time' });
        }
      }
      
      // Determine if approval is required for the new room
      const requiresApproval = room.metadata && room.metadata.requiresApproval;
      
      // If changing to a room that requires approval, set status to pending
      let newStatus = reservation.status;
      if (roomId && roomId !== reservation.roomId.toString() && requiresApproval) {
        newStatus = 'pending';
      } else if (status && req.user.isAdmin) {
        // Only admins can directly change status
        newStatus = status;
      }
      
      // Update reservation
      const updatedReservation = await Reservation.findByIdAndUpdate(
        id,
        {
          roomId: roomId || reservation.roomId,
          title: title || reservation.title,
          description: description !== undefined ? description : reservation.description,
          startTime: start,
          endTime: end,
          recurrence: recurrence || reservation.recurrence,
          attendees: attendees || reservation.attendees,
          status: newStatus,
          'approvalStatus.isApprovalRequired': requiresApproval,
          metadata: metadata || reservation.metadata
        },
        { new: true }
      )
        .populate('roomId', 'name roomNumber floorId calendarId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email');
      
      // If the room has a Google Calendar ID, update the calendar event
      if (updatedReservation.roomId.calendarId && updatedReservation.googleCalendarEventId) {
        try {
          await reservationController.updateGoogleCalendarEvent(updatedReservation);
        } catch (calendarError) {
          console.error('Error updating Google Calendar event:', calendarError);
          // Continue with the reservation update even if calendar event update fails
        }
      }
      
      res.json(updatedReservation);
    } catch (error) {
      console.error('Error updating reservation:', error);
      res.status(500).json({ message: 'Error updating reservation', error: error.message });
    }
  },
  
  /**
   * Delete a reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteReservation: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Validate reservation exists
      const reservation = await Reservation.findById(id)
        .populate('roomId', 'calendarId');
      
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to delete this reservation
      if (!req.user.isAdmin && reservation.userId.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'You do not have permission to delete this reservation' });
      }
      
      // If the reservation has a Google Calendar event, delete it
      if (reservation.roomId.calendarId && reservation.googleCalendarEventId) {
        try {
          await reservationController.deleteGoogleCalendarEvent(reservation);
        } catch (calendarError) {
          console.error('Error deleting Google Calendar event:', calendarError);
          // Continue with the reservation deletion even if calendar event deletion fails
        }
      }
      
      // Delete reservation
      await Reservation.findByIdAndDelete(id);
      
      res.json({ message: 'Reservation deleted successfully' });
    } catch (error) {
      console.error('Error deleting reservation:', error);
      res.status(500).json({ message: 'Error deleting reservation', error: error.message });
    }
  },
  
  /**
   * Approve a reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  approveReservation: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Validate reservation exists
      const reservation = await Reservation.findById(id)
        .populate('roomId', 'metadata calendarId');
      
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to approve this reservation
      const isApprover = reservation.roomId.metadata && 
                         reservation.roomId.metadata.approvers && 
                         reservation.roomId.metadata.approvers.some(
                           approver => approver.toString() === req.user._id.toString()
                         );
      
      if (!req.user.isAdmin && !isApprover) {
        return res.status(403).json({ message: 'You do not have permission to approve this reservation' });
      }
      
      // Check if reservation is pending
      if (reservation.status !== 'pending') {
        return res.status(400).json({ message: 'Only pending reservations can be approved' });
      }
      
      // Update reservation
      const updatedReservation = await Reservation.findByIdAndUpdate(
        id,
        {
          status: 'approved',
          'approvalStatus.approvedBy': req.user._id,
          'approvalStatus.approvedAt': new Date()
        },
        { new: true }
      )
        .populate('roomId', 'name roomNumber floorId calendarId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email')
        .populate('approvalStatus.approvedBy', 'name email');
      
      // If the room has a Google Calendar ID and the reservation doesn't have a calendar event yet,
      // create one now that it's approved
      if (updatedReservation.roomId.calendarId && !updatedReservation.googleCalendarEventId) {
        try {
          await reservationController.createGoogleCalendarEvent(updatedReservation, updatedReservation.roomId);
        } catch (calendarError) {
          console.error('Error creating Google Calendar event:', calendarError);
          // Continue with the reservation approval even if calendar event creation fails
        }
      }
      
      res.json(updatedReservation);
    } catch (error) {
      console.error('Error approving reservation:', error);
      res.status(500).json({ message: 'Error approving reservation', error: error.message });
    }
  },
  
  /**
   * Reject a reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  rejectReservation: async (req, res) => {
    try {
      const { id } = req.params;
      const { rejectionReason } = req.body;
      
      // Validate reservation exists
      const reservation = await Reservation.findById(id)
        .populate('roomId', 'metadata calendarId');
      
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to reject this reservation
      const isApprover = reservation.roomId.metadata && 
                         reservation.roomId.metadata.approvers && 
                         reservation.roomId.metadata.approvers.some(
                           approver => approver.toString() === req.user._id.toString()
                         );
      
      if (!req.user.isAdmin && !isApprover) {
        return res.status(403).json({ message: 'You do not have permission to reject this reservation' });
      }
      
      // Check if reservation is pending
      if (reservation.status !== 'pending') {
        return res.status(400).json({ message: 'Only pending reservations can be rejected' });
      }
      
      // Update reservation
      const updatedReservation = await Reservation.findByIdAndUpdate(
        id,
        {
          status: 'rejected',
          'approvalStatus.rejectedBy': req.user._id,
          'approvalStatus.rejectedAt': new Date(),
          'approvalStatus.rejectionReason': rejectionReason
        },
        { new: true }
      )
        .populate('roomId', 'name roomNumber floorId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email')
        .populate('approvalStatus.rejectedBy', 'name email');
      
      // If the reservation has a Google Calendar event, delete it since it's rejected
      if (reservation.roomId.calendarId && reservation.googleCalendarEventId) {
        try {
          await reservationController.deleteGoogleCalendarEvent(reservation);
        } catch (calendarError) {
          console.error('Error deleting Google Calendar event:', calendarError);
          // Continue with the reservation rejection even if calendar event deletion fails
        }
      }
      
      res.json(updatedReservation);
    } catch (error) {
      console.error('Error rejecting reservation:', error);
      res.status(500).json({ message: 'Error rejecting reservation', error: error.message });
    }
  },
  
  /**
   * Confirm a reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  confirmReservation: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Validate reservation exists
      const reservation = await Reservation.findById(id);
      
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to confirm this reservation
      if (!req.user.isAdmin && reservation.userId.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'You do not have permission to confirm this reservation' });
      }
      
      // Update reservation
      const updatedReservation = await Reservation.findByIdAndUpdate(
        id,
        {
          'confirmationStatus.isConfirmed': true,
          'confirmationStatus.confirmedAt': new Date()
        },
        { new: true }
      )
        .populate('roomId', 'name roomNumber floorId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email');
      
      res.json(updatedReservation);
    } catch (error) {
      console.error('Error confirming reservation:', error);
      res.status(500).json({ message: 'Error confirming reservation', error: error.message });
    }
  },
  
  /**
   * Cancel a reservation
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  cancelReservation: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Validate reservation exists
      const reservation = await Reservation.findById(id)
        .populate('roomId', 'calendarId');
      
      if (!reservation) {
        return res.status(404).json({ message: 'Reservation not found' });
      }
      
      // Check if user has permission to cancel this reservation
      if (!req.user.isAdmin && reservation.userId.toString() !== req.user._id.toString()) {
        return res.status(403).json({ message: 'You do not have permission to cancel this reservation' });
      }
      
      // Update reservation
      const updatedReservation = await Reservation.findByIdAndUpdate(
        id,
        {
          status: 'cancelled'
        },
        { new: true }
      )
        .populate('roomId', 'name roomNumber floorId')
        .populate({
          path: 'roomId',
          populate: {
            path: 'floorId',
            model: 'Floor',
            select: 'name level buildingId',
            populate: {
              path: 'buildingId',
              model: 'Building',
              select: 'name'
            }
          }
        })
        .populate('userId', 'name email');
      
      // If the reservation has a Google Calendar event, delete it since it's cancelled
      if (reservation.roomId.calendarId && reservation.googleCalendarEventId) {
        try {
          await reservationController.deleteGoogleCalendarEvent(reservation);
        } catch (calendarError) {
          console.error('Error deleting Google Calendar event:', calendarError);
          // Continue with the reservation cancellation even if calendar event deletion fails
        }
      }
      
      res.json(updatedReservation);
    } catch (error) {
      console.error('Error cancelling reservation:', error);
      res.status(500).json({ message: 'Error cancelling reservation', error: error.message });
    }
  },
  
  /**
   * Check if a room is available during a specific time period
   * @param {string} roomId - Room ID
   * @param {Date} startTime - Start time
   * @param {Date} endTime - End time
   * @param {string} excludeReservationId - Reservation ID to exclude from the check (for updates)
   * @returns {Promise<boolean>} Whether the room is available
   */
  checkRoomAvailability: async (roomId, startTime, endTime, excludeReservationId) => {
    // Find all reservations that overlap with the requested time period
    const query = {
      roomId,
      $or: [
        // Reservation starts during the requested period
        { startTime: { $gte: startTime, $lt: endTime } },
        // Reservation ends during the requested period
        { endTime: { $gt: startTime, $lte: endTime } },
        // Reservation spans the entire requested period
        { startTime: { $lte: startTime }, endTime: { $gte: endTime } }
      ],
      status: { $in: ['pending', 'approved'] }
    };
    
    // Exclude the current reservation if updating
    if (excludeReservationId) {
      query._id = { $ne: excludeReservationId };
    }
    
    const overlappingReservations = await Reservation.find(query);
    
    return overlappingReservations.length === 0;
  },
  
  /**
   * Create a Google Calendar event for a reservation
   * @param {Object} reservation - Reservation object
   * @param {Object} room - Room object
   * @returns {Promise<void>}
   */
  createGoogleCalendarEvent: async (reservation, room) => {
    try {
      // Get user details for service account impersonation and event data
      const user = await User.findById(reservation.userId);
      if (!user || !user.email) {
        throw new Error('User email is required for Google Calendar integration');
      }
      
      // Initialize Google Calendar API with service account
      const googleCalendar = new GoogleCalendarAPI(
        process.env.GOOGLE_CALENDAR_CLIENT_ID,
        process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
        process.env.GOOGLE_CALENDAR_REDIRECT_URI,
        process.env.GOOGLE_CALENDAR_TOKEN_PATH,
        null, // No user tokens
        reservation.userId, // User ID
        user.email // User email for service account impersonation
      );
      
      await googleCalendar.initialize();
      
      if (!googleCalendar.isAuthenticated()) {
        throw new Error('Google Calendar API not authenticated');
      }
      
      // Create event data
      const eventData = {
        summary: reservation.title,
        description: reservation.description || '',
        location: `${room.name} (${room.roomNumber})`,
        start: {
          dateTime: reservation.startTime.toISOString(),
          timeZone: 'America/New_York' // Use appropriate timezone
        },
        end: {
          dateTime: reservation.endTime.toISOString(),
          timeZone: 'America/New_York' // Use appropriate timezone
        },
        attendees: [
          { email: user.email, displayName: user.name }
        ],
        // Add additional attendees if they exist
        ...(reservation.attendees && reservation.attendees.length > 0 && {
          attendees: [
            { email: user.email, displayName: user.name },
            ...reservation.attendees.filter(a => a.email).map(a => ({
              email: a.email,
              displayName: a.name
            }))
          ]
        }),
        // Add recurrence if it's a recurring reservation
        ...(reservation.recurrence && reservation.recurrence.isRecurring && {
          recurrence: reservationController.buildRecurrenceRule(reservation.recurrence)
        })
      };
      
      // Create event
      const event = await googleCalendar.createEvent(room.calendarId, eventData);
      
      // Update reservation with Google Calendar event ID
      await Reservation.findByIdAndUpdate(reservation._id, {
        googleCalendarEventId: event.id
      });
    } catch (error) {
      console.error('Error creating Google Calendar event:', error);
      throw error;
    }
  },
  
  /**
   * Update a Google Calendar event for a reservation
   * @param {Object} reservation - Reservation object
   * @returns {Promise<void>}
   */
  updateGoogleCalendarEvent: async (reservation) => {
    try {
      // Get user details for service account impersonation and event data
      const user = await User.findById(reservation.userId);
      if (!user || !user.email) {
        throw new Error('User email is required for Google Calendar integration');
      }
      
      // Initialize Google Calendar API with service account
      const googleCalendar = new GoogleCalendarAPI(
        process.env.GOOGLE_CALENDAR_CLIENT_ID,
        process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
        process.env.GOOGLE_CALENDAR_REDIRECT_URI,
        process.env.GOOGLE_CALENDAR_TOKEN_PATH,
        null, // No user tokens
        reservation.userId, // User ID
        user.email // User email for service account impersonation
      );
      
      await googleCalendar.initialize();
      
      if (!googleCalendar.isAuthenticated()) {
        throw new Error('Google Calendar API not authenticated');
      }
      
      // Create event data
      const eventData = {
        summary: reservation.title,
        description: reservation.description || '',
        location: `${reservation.roomId.name} (${reservation.roomId.roomNumber})`,
        start: {
          dateTime: reservation.startTime.toISOString(),
          timeZone: 'America/New_York' // Use appropriate timezone
        },
        end: {
          dateTime: reservation.endTime.toISOString(),
          timeZone: 'America/New_York' // Use appropriate timezone
        },
        attendees: [
          { email: user.email, displayName: user.name }
        ],
        // Add additional attendees if they exist
        ...(reservation.attendees && reservation.attendees.length > 0 && {
          attendees: [
            { email: user.email, displayName: user.name },
            ...reservation.attendees.filter(a => a.email).map(a => ({
              email: a.email,
              displayName: a.name
            }))
          ]
        }),
        // Add recurrence if it's a recurring reservation
        ...(reservation.recurrence && reservation.recurrence.isRecurring && {
          recurrence: reservationController.buildRecurrenceRule(reservation.recurrence)
        })
      };
      
      // Update event
      await googleCalendar.updateEvent(
        reservation.roomId.calendarId,
        reservation.googleCalendarEventId,
        eventData
      );
    } catch (error) {
      console.error('Error updating Google Calendar event:', error);
      throw error;
    }
  },
  
  /**
   * Delete a Google Calendar event for a reservation
   * @param {Object} reservation - Reservation object
   * @returns {Promise<void>}
   */
  deleteGoogleCalendarEvent: async (reservation) => {
    try {
      // Get user details for service account impersonation
      const user = await User.findById(reservation.userId);
      if (!user || !user.email) {
        throw new Error('User email is required for Google Calendar integration');
      }
      
      // Initialize Google Calendar API with service account
      const googleCalendar = new GoogleCalendarAPI(
        process.env.GOOGLE_CALENDAR_CLIENT_ID,
        process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
        process.env.GOOGLE_CALENDAR_REDIRECT_URI,
        process.env.GOOGLE_CALENDAR_TOKEN_PATH,
        null, // No user tokens
        reservation.userId, // User ID
        user.email // User email for service account impersonation
      );
      
      await googleCalendar.initialize();
      
      if (!googleCalendar.isAuthenticated()) {
        throw new Error('Google Calendar API not authenticated');
      }
      
      // Delete event
      await googleCalendar.deleteEvent(
        reservation.roomId.calendarId,
        reservation.googleCalendarEventId
      );
    } catch (error) {
      console.error('Error deleting Google Calendar event:', error);
      throw error;
    }
  },
  
  /**
   * Build recurrence rule for Google Calendar
   * @param {Object} recurrence - Recurrence object from reservation
   * @returns {Array<string>} Recurrence rules
   */
  buildRecurrenceRule: (recurrence) => {
    if (!recurrence || !recurrence.isRecurring) {
      return [];
    }
    
    let rule = 'RRULE:FREQ=';
    
    // Set frequency
    switch (recurrence.pattern) {
      case 'daily':
        rule += 'DAILY';
        break;
      case 'weekly':
        rule += 'WEEKLY';
        break;
      case 'biweekly':
        rule += 'WEEKLY;INTERVAL=2';
        break;
      case 'monthly':
        rule += 'MONTHLY';
        break;
      default:
        rule += 'DAILY'; // Default to daily
    }
    
    // Add interval if not biweekly (already handled)
    if (recurrence.pattern !== 'biweekly' && recurrence.interval && recurrence.interval > 1) {
      rule += `;INTERVAL=${recurrence.interval}`;
    }
    
    // Add days of week for weekly/biweekly recurrence
    if ((recurrence.pattern === 'weekly' || recurrence.pattern === 'biweekly') && 
        recurrence.daysOfWeek && recurrence.daysOfWeek.length > 0) {
      const days = recurrence.daysOfWeek.map(day => {
        switch (day) {
          case 0: return 'SU';
          case 1: return 'MO';
          case 2: return 'TU';
          case 3: return 'WE';
          case 4: return 'TH';
          case 5: return 'FR';
          case 6: return 'SA';
          default: return '';
        }
      }).filter(day => day !== '');
      
      if (days.length > 0) {
        rule += `;BYDAY=${days.join(',')}`;
      }
    }
    
    // Add end date or count
    if (recurrence.endDate) {
      const endDate = new Date(recurrence.endDate);
      const year = endDate.getFullYear();
      const month = String(endDate.getMonth() + 1).padStart(2, '0');
      const day = String(endDate.getDate()).padStart(2, '0');
      rule += `;UNTIL=${year}${month}${day}T235959Z`;
    } else if (recurrence.count && recurrence.count > 0) {
      rule += `;COUNT=${recurrence.count}`;
    }
    
    return [rule];
  }
};

module.exports = reservationController;