const WiimAPI = require('../integrations/wiim/wiimAPI');
const WiimConfig = require('../../models/WiimConfig');

// Initialize WiiM API with default values
// Will be updated with actual configuration when available
let wiimAPI = new WiimAPI('', 80);

// Initialize WiiM API with configuration from environment variables
const initializeWiimAPI = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.WIIM_HOST || '';
    const envPort = process.env.WIIM_PORT || 80;
    
    if (envHost) {
      wiimAPI = new WiimAPI(envHost, envPort);
      console.log(`WiiM API initialized with environment variables: ${envHost}:${envPort}`);
      
      // Call the initialize method to update integration status
      try {
        await wiimAPI.initialize();
      } catch (initError) {
        console.error('Error updating WiiM integration status:', initError);
        // Continue even if status update fails
      }
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('WIIM_HOST environment variable not set. Falling back to database config (not recommended).');
    
    // Get the latest configuration from the database
    const config = await WiimConfig.findOne().sort({ updatedAt: -1 });
    
    if (config) {
      // Update the wiimAPI instance with the configuration
      wiimAPI = new WiimAPI(config.host, config.port);
      console.log(`WiiM API initialized with configuration from database: ${config.host}:${config.port}`);
      
      // Call the initialize method to update integration status
      try {
        await wiimAPI.initialize();
      } catch (initError) {
        console.error('Error updating WiiM integration status:', initError);
        // Continue even if status update fails
      }
    } else {
      console.log('No WiiM configuration found in environment variables or database');
      
      // Update integration status to not_configured
      try {
        await wiimAPI.initialize();
      } catch (initError) {
        console.error('Error updating WiiM integration status:', initError);
        // Continue even if status update fails
      }
    }
  } catch (error) {
    console.error('Error initializing WiiM API:', error);
  }
};

// Call the initialization function
initializeWiimAPI();

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (!wiimAPI.host) {
    throw new Error('WiiM configuration is missing. Please configure the WiiM integration.');
  }
  return true;
};

// Import the cache utility
const cacheUtil = require('../utils/cacheUtil');

/**
 * Get WiiM device information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceInfo = async (req, res) => {
  try {
    ensureApiInitialized();
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('wiim-device-info');
    
    // Try to get the device info from cache first
    let deviceInfo = cacheUtil.get(cacheKey);
    
    if (!deviceInfo) {
      console.log('WiiM device info cache miss, fetching from API');
      // If not in cache, fetch from API
      deviceInfo = await wiimAPI.getDeviceInfo();
      
      // Cache the result for 10 minutes (device info doesn't change often)
      cacheUtil.set(cacheKey, deviceInfo, 10 * 60 * 1000);
    } else {
      console.log('WiiM device info served from cache');
    }
    
    res.json(deviceInfo);
  } catch (error) {
    console.error('Controller error fetching WiiM device information:', error);
    res.status(500).json({ message: 'Error fetching WiiM device information', error: error.message });
  }
};

/**
 * Get current playback status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPlaybackStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    
    // Check if the request wants enriched data (default to true)
    const enriched = req.query.enriched !== 'false';
    
    if (enriched) {
      // Get enriched player status with Spotify data
      const enrichedStatus = await wiimAPI.getEnrichedPlayerStatus();
      res.json(enrichedStatus);
    } else {
      // Get basic player status without Spotify enrichment
      const status = await wiimAPI.getPlaybackStatus();
      res.json(status);
    }
  } catch (error) {
    console.error('Controller error fetching WiiM playback status:', error);
    res.status(500).json({ message: 'Error fetching WiiM playback status', error: error.message });
  }
};

/**
 * Play
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.play = async (req, res) => {
  try {
    ensureApiInitialized();
    const result = await wiimAPI.play();
    res.json(result);
  } catch (error) {
    console.error('Controller error sending play command to WiiM:', error);
    res.status(500).json({ message: 'Error sending play command to WiiM', error: error.message });
  }
};

/**
 * Pause
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.pause = async (req, res) => {
  try {
    ensureApiInitialized();
    const result = await wiimAPI.pause();
    res.json(result);
  } catch (error) {
    console.error('Controller error sending pause command to WiiM:', error);
    res.status(500).json({ message: 'Error sending pause command to WiiM', error: error.message });
  }
};

/**
 * Next track
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.next = async (req, res) => {
  try {
    ensureApiInitialized();
    const result = await wiimAPI.next();
    res.json(result);
  } catch (error) {
    console.error('Controller error sending next command to WiiM:', error);
    res.status(500).json({ message: 'Error sending next command to WiiM', error: error.message });
  }
};

/**
 * Previous track
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.previous = async (req, res) => {
  try {
    ensureApiInitialized();
    const result = await wiimAPI.previous();
    res.json(result);
  } catch (error) {
    console.error('Controller error sending previous command to WiiM:', error);
    res.status(500).json({ message: 'Error sending previous command to WiiM', error: error.message });
  }
};

/**
 * Set volume
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setVolume = async (req, res) => {
  try {
    ensureApiInitialized();
    const { volume } = req.body;
    if (volume === undefined || volume < 0 || volume > 100) {
      return res.status(400).json({ message: 'Volume must be a number between 0 and 100' });
    }
    const result = await wiimAPI.setVolume(volume);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting WiiM volume:', error);
    res.status(500).json({ message: 'Error setting WiiM volume', error: error.message });
  }
};

/**
 * Get current playlist
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPlaylist = async (req, res) => {
  try {
    ensureApiInitialized();
    
    // Check if the request wants enriched data (default to true)
    const enriched = req.query.enriched !== 'false';
    
    if (enriched) {
      // Get enriched playlist with Spotify data
      const enrichedPlaylist = await wiimAPI.getEnrichedPlaylist();
      res.json(enrichedPlaylist);
    } else {
      // Get basic playlist without Spotify enrichment
      const playlist = await wiimAPI.getPlaylist();
      res.json(playlist);
    }
  } catch (error) {
    console.error('Controller error fetching WiiM playlist:', error);
    res.status(500).json({ message: 'Error fetching WiiM playlist', error: error.message });
  }
};

/**
 * Play specific track from playlist
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playTrack = async (req, res) => {
  try {
    ensureApiInitialized();
    const { index } = req.params;
    const result = await wiimAPI.playTrack(index);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing track on WiiM:', error);
    res.status(500).json({ message: 'Error playing track on WiiM', error: error.message });
  }
};

/**
 * Set repeat mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setRepeat = async (req, res) => {
  try {
    ensureApiInitialized();
    const { mode } = req.body;
    if (!mode || !['all', 'one', 'off'].includes(mode)) {
      return res.status(400).json({ message: 'Mode must be one of: all, one, off' });
    }
    const result = await wiimAPI.setRepeat(mode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting WiiM repeat mode:', error);
    res.status(500).json({ message: 'Error setting WiiM repeat mode', error: error.message });
  }
};

/**
 * Set shuffle mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setShuffle = async (req, res) => {
  try {
    ensureApiInitialized();
    const { enabled } = req.body;
    if (enabled === undefined) {
      return res.status(400).json({ message: 'Enabled parameter is required' });
    }
    const result = await wiimAPI.setShuffle(enabled);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting WiiM shuffle mode:', error);
    res.status(500).json({ message: 'Error setting WiiM shuffle mode', error: error.message });
  }
};

/**
 * Get list of available playlists
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPlaylists = async (req, res) => {
  try {
    ensureApiInitialized();
    
    // Check if the request wants Spotify playlists (default to true)
    const useSpotify = req.query.spotify !== 'false';
    
    if (useSpotify) {
      try {
        // Try to get Spotify playlists first
        const spotifyPlaylists = await wiimAPI.getSpotifyPlaylists();
        res.json(spotifyPlaylists);
        return;
      } catch (spotifyError) {
        console.error('Error fetching Spotify playlists, falling back to WiiM playlists:', spotifyError);
        // Fall back to WiiM playlists if Spotify fails
      }
    }
    
    // Get playlists from WiiM device
    const playlists = await wiimAPI.getPlaylists();
    res.json(playlists);
  } catch (error) {
    console.error('Controller error fetching WiiM playlists:', error);
    res.status(500).json({ message: 'Error fetching WiiM playlists', error: error.message });
  }
};

/**
 * Play a specific playlist
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playPlaylist = async (req, res) => {
  try {
    ensureApiInitialized();
    const { playlistId } = req.params;
    const result = await wiimAPI.playPlaylist(playlistId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing playlist on WiiM:', error);
    res.status(500).json({ message: 'Error playing playlist on WiiM', error: error.message });
  }
};

/**
 * Get songs in a specific playlist without playing it
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPlaylistSongs = async (req, res) => {
  try {
    ensureApiInitialized();
    const { playlistId } = req.params;
    const songs = await wiimAPI.getPlaylistSongs(playlistId);
    res.json(songs);
  } catch (error) {
    console.error('Controller error fetching songs for playlist on WiiM:', error);
    res.status(500).json({ message: 'Error fetching songs for playlist on WiiM', error: error.message });
  }
};

/**
 * Search for music
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.search = async (req, res) => {
  try {
    ensureApiInitialized();
    const { query } = req.query;
    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }
    const results = await wiimAPI.search(query);
    res.json(results);
  } catch (error) {
    console.error('Controller error searching on WiiM:', error);
    res.status(500).json({ message: 'Error searching on WiiM', error: error.message });
  }
};

/**
 * Get Spotify playlists
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSpotifyPlaylists = async (req, res) => {
  try {
    ensureApiInitialized();
    const playlists = await wiimAPI.getSpotifyPlaylists();
    res.json(playlists);
  } catch (error) {
    console.error('Controller error fetching Spotify playlists from WiiM:', error);
    res.status(500).json({ message: 'Error fetching Spotify playlists from WiiM', error: error.message });
  }
};

/**
 * Play Spotify playlist
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyPlaylist = async (req, res) => {
  try {
    ensureApiInitialized();
    const { playlistId } = req.params;
    const result = await wiimAPI.playSpotifyPlaylist(playlistId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify playlist on WiiM:', error);
    res.status(500).json({ message: 'Error playing Spotify playlist on WiiM', error: error.message });
  }
};

/**
 * Search Spotify for tracks, albums, artists, or playlists
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchSpotify = async (req, res) => {
  try {
    ensureApiInitialized();
    const { query, type } = req.query;
    
    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }
    
    const results = await wiimAPI.searchSpotify(query, type);
    res.json(results);
  } catch (error) {
    console.error('Controller error searching Spotify:', error);
    res.status(500).json({ message: 'Error searching Spotify', error: error.message });
  }
};

/**
 * Play a Spotify track
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyTrack = async (req, res) => {
  try {
    ensureApiInitialized();
    const { trackId } = req.params;
    const result = await wiimAPI.playSpotifyTrack(trackId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify track on WiiM:', error);
    res.status(500).json({ message: 'Error playing Spotify track on WiiM', error: error.message });
  }
};

/**
 * Play a Spotify album
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyAlbum = async (req, res) => {
  try {
    ensureApiInitialized();
    const { albumId } = req.params;
    const result = await wiimAPI.playSpotifyAlbum(albumId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify album on WiiM:', error);
    res.status(500).json({ message: 'Error playing Spotify album on WiiM', error: error.message });
  }
};

/**
 * Play a Spotify artist's top tracks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyArtist = async (req, res) => {
  try {
    ensureApiInitialized();
    const { artistId } = req.params;
    const result = await wiimAPI.playSpotifyArtist(artistId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify artist on WiiM:', error);
    res.status(500).json({ message: 'Error playing Spotify artist on WiiM', error: error.message });
  }
};

/**
 * Get available Spotify devices for playback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSpotifyDevices = async (req, res) => {
  try {
    ensureApiInitialized();
    const devices = await wiimAPI.getSpotifyDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Spotify devices:', error);
    
    // Handle specific error types with appropriate status codes
    if (error.isAuthError) {
      return res.status(401).json({ 
        message: 'Error fetching Spotify devices', 
        error: error.message,
        details: 'Authentication failed. Please check your Spotify credentials in the environment variables.'
      });
    } else if (error.isPermissionError) {
      return res.status(403).json({ 
        message: 'Error fetching Spotify devices', 
        error: error.message,
        details: 'Permission denied. Your Spotify account may not have the required permissions.'
      });
    } else if (error.message && error.message.includes('refresh token is invalid or has been revoked')) {
      return res.status(401).json({ 
        message: 'Error fetching Spotify devices', 
        error: error.message,
        details: 'Spotify refresh token is invalid or has been revoked. Please generate a new refresh token.'
      });
    }
    
    // Default error response
    res.status(500).json({ message: 'Error fetching Spotify devices', error: error.message });
  }
};

/**
 * Get current Spotify playback state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSpotifyPlaybackState = async (req, res) => {
  try {
    const playbackState = await wiimAPI.getSpotifyPlaybackState();
    res.json(playbackState);
  } catch (error) {
    console.error('Controller error fetching Spotify playback state:', error);
    res.status(500).json({ message: 'Error fetching Spotify playback state', error: error.message });
  }
};

/**
 * Transfer playback to the WiiM device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.transferPlaybackToWiim = async (req, res) => {
  try {
    const { play = true } = req.body;
    const result = await wiimAPI.transferPlaybackToWiim(play);
    res.json(result);
  } catch (error) {
    console.error('Controller error transferring playback to WiiM device:', error);
    res.status(500).json({ message: 'Error transferring playback to WiiM device', error: error.message });
  }
};

/**
 * Get the current Spotify queue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSpotifyQueue = async (req, res) => {
  try {
    const queue = await wiimAPI.getSpotifyQueue();
    res.json(queue);
  } catch (error) {
    console.error('Controller error fetching Spotify queue:', error);
    res.status(500).json({ message: 'Error fetching Spotify queue', error: error.message });
  }
};

/**
 * Add an item to the Spotify queue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addToSpotifyQueue = async (req, res) => {
  try {
    const { uri, deviceId } = req.body;
    
    if (!uri) {
      return res.status(400).json({ message: 'URI is required' });
    }
    
    const result = await wiimAPI.addToSpotifyQueue(uri, deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error adding item to Spotify queue:', error);
    res.status(500).json({ message: 'Error adding item to Spotify queue', error: error.message });
  }
};

/**
 * Skip to the next track in the Spotify queue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.skipToNextTrack = async (req, res) => {
  try {
    const { deviceId } = req.body;
    const result = await wiimAPI.skipToNextTrack(deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error skipping to next track:', error);
    res.status(500).json({ message: 'Error skipping to next track', error: error.message });
  }
};

/**
 * Skip to the previous track in the Spotify queue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.skipToPreviousTrack = async (req, res) => {
  try {
    const { deviceId } = req.body;
    const result = await wiimAPI.skipToPreviousTrack(deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error skipping to previous track:', error);
    res.status(500).json({ message: 'Error skipping to previous track', error: error.message });
  }
};

/**
 * Play a Spotify track using Spotify Connect API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyTrackConnect = async (req, res) => {
  try {
    ensureApiInitialized();
    const { trackId } = req.params;
    const { deviceId } = req.body;
    const result = await wiimAPI.playSpotifyTrackConnect(trackId, deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify track using Connect API:', error);
    res.status(500).json({ message: 'Error playing Spotify track using Connect API', error: error.message });
  }
};

/**
 * Play a Spotify album using Spotify Connect API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyAlbumConnect = async (req, res) => {
  try {
    ensureApiInitialized();
    const { albumId } = req.params;
    const { deviceId } = req.body;
    const result = await wiimAPI.playSpotifyAlbumConnect(albumId, deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify album using Connect API:', error);
    res.status(500).json({ message: 'Error playing Spotify album using Connect API', error: error.message });
  }
};

/**
 * Play a Spotify artist using Spotify Connect API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyArtistConnect = async (req, res) => {
  try {
    ensureApiInitialized();
    const { artistId } = req.params;
    const { deviceId } = req.body;
    const result = await wiimAPI.playSpotifyArtistConnect(artistId, deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify artist using Connect API:', error);
    res.status(500).json({ message: 'Error playing Spotify artist using Connect API', error: error.message });
  }
};

/**
 * Play a Spotify playlist using Spotify Connect API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.playSpotifyPlaylistConnect = async (req, res) => {
  try {
    ensureApiInitialized();
    const { playlistId } = req.params;
    const { deviceId } = req.body;
    const result = await wiimAPI.playSpotifyPlaylistConnect(playlistId, deviceId);
    res.json(result);
  } catch (error) {
    console.error('Controller error playing Spotify playlist using Connect API:', error);
    res.status(500).json({ message: 'Error playing Spotify playlist using Connect API', error: error.message });
  }
};

/**
 * Get list of available inputs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getInputs = async (req, res) => {
  try {
    ensureApiInitialized();
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('wiim-inputs');
    
    // Try to get the inputs from cache first
    let inputs = cacheUtil.get(cacheKey);
    
    if (!inputs) {
      console.log('WiiM inputs cache miss, fetching from API');
      // If not in cache, fetch from API
      inputs = await wiimAPI.getInputs();
      
      // Cache the result for 30 minutes (inputs list rarely changes)
      cacheUtil.set(cacheKey, inputs, 30 * 60 * 1000);
    } else {
      console.log('WiiM inputs served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=1800'); // 30 minutes
    
    res.json(inputs);
  } catch (error) {
    console.error('Controller error fetching WiiM inputs:', error);
    res.status(500).json({ message: 'Error fetching WiiM inputs', error: error.message });
  }
};

/**
 * Switch to a specific input
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.switchInput = async (req, res) => {
  try {
    ensureApiInitialized();
    const { inputId } = req.params;
    const result = await wiimAPI.switchInput(inputId);
    res.json(result);
  } catch (error) {
    console.error('Controller error switching input on WiiM:', error);
    res.status(500).json({ message: 'Error switching input on WiiM', error: error.message });
  }
};

/**
 * Get WiiM health status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getHealthStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const healthStatus = await wiimAPI.getHealthStatus();
    res.json(healthStatus);
  } catch (error) {
    console.error('Controller error fetching WiiM health status:', error);
    res.status(500).json({
      message: 'Error fetching WiiM health status',
      error: error.message,
      status: 'unhealthy',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get WiiM configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('wiim-config');
    
    // Try to get the config from cache first
    let responseData = cacheUtil.get(cacheKey);
    
    if (!responseData) {
      console.log('WiiM config cache miss, fetching from sources');
      
      // Always prioritize environment variables for authentication
      const envHost = process.env.WIIM_HOST || '';
      const envPort = process.env.WIIM_PORT || 80;
      
      let configData = {};
      let deviceInfo = null;
      
      // If environment variables are set, use them directly
      if (envHost) {
        configData = {
          host: envHost,
          port: envPort,
          configuredAt: new Date(),
          fromEnv: true
        };
        
        // Try to get extended device information using environment variables
        try {
          // Ensure the API is using the correct configuration
          wiimAPI = new WiimAPI(envHost, envPort);
          
          // Get extended status information
          const statusExResponse = await wiimAPI.getStatusEx();
          
          // Create deviceInfo object with name from DeviceName and model from project
          deviceInfo = {
            ...statusExResponse,
            name: statusExResponse?.DeviceName || 'Unknown',
            model: statusExResponse?.project || 'Unknown'
          };
        } catch (deviceError) {
          console.error('Error fetching WiiM device information:', deviceError);
          // Continue without device info if there's an error
        }
      } else {
        // If environment variables are not set, fall back to database config (not recommended)
        console.warn('WIIM_HOST environment variable not set. Falling back to database config (not recommended).');
        
        // Get the latest configuration from the database
        const config = await WiimConfig.findOne().sort({ updatedAt: -1 });
        
        if (config) {
          configData = {
            host: config.host,
            port: config.port,
            configuredAt: config.updatedAt,
            fromEnv: false
          };
          
          // Try to get extended device information if we have a valid configuration
          if (config.host) {
            try {
              // Ensure the API is using the correct configuration
              wiimAPI = new WiimAPI(config.host, config.port);
              
              // Get extended status information
              const statusExResponse = await wiimAPI.getStatusEx();
              
              // Create deviceInfo object with name from DeviceName and model from project
              deviceInfo = {
                ...statusExResponse,
                name: statusExResponse?.DeviceName || 'Unknown',
                model: statusExResponse?.project || 'Unknown'
              };
            } catch (deviceError) {
              console.error('Error fetching WiiM device information:', deviceError);
              // Continue without device info if there's an error
            }
          }
        } else {
          return res.status(404).json({ message: 'WiiM configuration not found. Please set the required environment variables.' });
        }
      }
      
      // Combine configuration data with device information
      responseData = {
        ...configData,
        deviceInfo: deviceInfo
      };
      
      // Cache the result for 15 minutes (configuration doesn't change often)
      cacheUtil.set(cacheKey, responseData, 15 * 60 * 1000);
    } else {
      console.log('WiiM config served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=900'); // 15 minutes
    
    res.json(responseData);
  } catch (error) {
    console.error('Error fetching WiiM configuration:', error);
    res.status(500).json({ message: 'Error fetching WiiM configuration', error: error.message });
  }
};

/**
 * Save WiiM configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling WiiM configuration request:', error);
    res.status(500).json({ message: 'Error handling WiiM configuration request', error: error.message });
  }
};

/**
 * Set up WiiM with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling WiiM one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling WiiM one-click setup request', 
      error: error.message 
    });
  }
};

/**
 * Seek to a specific position in the current track
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.seek = async (req, res) => {
  try {
    ensureApiInitialized();
    const { position } = req.body;
    if (position === undefined || isNaN(position) || position < 0) {
      return res.status(400).json({ message: 'Position must be a non-negative number' });
    }
    const result = await wiimAPI.seek(position);
    res.json(result);
  } catch (error) {
    console.error('Controller error seeking on WiiM:', error);
    res.status(500).json({ message: 'Error seeking on WiiM', error: error.message });
  }
};

/**
 * Get detailed information about the current track
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTrackInfo = async (req, res) => {
  try {
    ensureApiInitialized();
    const trackInfo = await wiimAPI.getTrackInfo();
    res.json(trackInfo);
  } catch (error) {
    console.error('Controller error fetching track information from WiiM:', error);
    res.status(500).json({ message: 'Error fetching track information from WiiM', error: error.message });
  }
};

/**
 * Get equalizer settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEqualizer = async (req, res) => {
  try {
    ensureApiInitialized();
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('wiim-equalizer');
    
    // Try to get the equalizer settings from cache first
    let equalizer = cacheUtil.get(cacheKey);
    
    if (!equalizer) {
      console.log('WiiM equalizer settings cache miss, fetching from API');
      // If not in cache, fetch from API
      equalizer = await wiimAPI.getEqualizer();
      
      // Cache the result for 10 minutes (equalizer settings don't change unless modified)
      cacheUtil.set(cacheKey, equalizer, 10 * 60 * 1000);
    } else {
      console.log('WiiM equalizer settings served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=600'); // 10 minutes
    
    res.json(equalizer);
  } catch (error) {
    console.error('Controller error fetching equalizer settings from WiiM:', error);
    res.status(500).json({ message: 'Error fetching equalizer settings from WiiM', error: error.message });
  }
};

/**
 * Set equalizer settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setEqualizer = async (req, res) => {
  try {
    ensureApiInitialized();
    const { preset, bands } = req.body;
    
    if (!preset) {
      return res.status(400).json({ message: 'Preset is required' });
    }
    
    // If preset is 'custom', validate bands
    if (preset === 'custom' && (!bands || !Array.isArray(bands) || bands.length === 0)) {
      return res.status(400).json({ message: 'Bands array is required for custom preset' });
    }
    
    const result = await wiimAPI.setEqualizer(preset, bands);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting equalizer on WiiM:', error);
    res.status(500).json({ message: 'Error setting equalizer on WiiM', error: error.message });
  }
};

/**
 * Check if the current playlist has ended and advance to the next playlist if needed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkAndAdvancePlaylist = async (req, res) => {
  try {
    ensureApiInitialized();
    const result = await wiimAPI.checkAndAdvancePlaylist();
    res.json(result);
  } catch (error) {
    console.error('Controller error checking and advancing playlist on WiiM:', error);
    res.status(500).json({ message: 'Error checking and advancing playlist on WiiM', error: error.message });
  }
};