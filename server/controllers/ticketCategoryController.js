const TicketCategory = require('../../models/TicketCategory');
const { validationResult } = require('express-validator');

/**
 * Ticket Category Controller
 * Handles API operations for ticket categories
 */
const ticketCategoryController = {
  /**
   * Get all categories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with categories
   */
  getAllCategories: async (req, res) => {
    try {
      const { includeInactive = false } = req.query;
      
      const filter = includeInactive === 'true' ? {} : { isActive: true };
      
      const categories = await TicketCategory.find(filter)
        .sort({ level: 1, sortOrder: 1, name: 1 })
        .populate('parent', 'name')
        .populate('defaultAssignee', 'name email')
        .populate('defaultGroup', 'name')
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');
      
      res.json(categories);
    } catch (err) {
      console.error('Error getting categories:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Get category by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with category
   */
  getCategoryById: async (req, res) => {
    try {
      const category = await TicketCategory.findById(req.params.id)
        .populate('parent', 'name')
        .populate('defaultAssignee', 'name email')
        .populate('defaultGroup', 'name')
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');
      
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      
      res.json(category);
    } catch (err) {
      console.error('Error getting category:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Category not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Create a new category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created category
   */
  createCategory: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check permissions
    if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
      return res.status(403).json({ message: 'Not authorized to create categories' });
    }

    try {
      const {
        name,
        description,
        parent,
        color,
        icon,
        defaultAssignee,
        defaultGroup,
        slaSettings,
        autoRules,
        sortOrder
      } = req.body;

      // Check for duplicate name
      const existingCategory = await TicketCategory.findOne({ name });
      if (existingCategory) {
        return res.status(400).json({ message: 'Category name already exists' });
      }

      const category = new TicketCategory({
        name,
        description,
        parent,
        color,
        icon,
        defaultAssignee,
        defaultGroup,
        slaSettings,
        autoRules,
        sortOrder,
        createdBy: req.user.id
      });

      await category.save();
      
      // Populate references for response
      await category.populate('parent', 'name');
      if (category.defaultAssignee) {
        await category.populate('defaultAssignee', 'name email');
      }
      if (category.defaultGroup) {
        await category.populate('defaultGroup', 'name');
      }
      await category.populate('createdBy', 'name email');
      
      res.status(201).json(category);
    } catch (err) {
      console.error('Error creating category:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Update a category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated category
   */
  updateCategory: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check permissions
    if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
      return res.status(403).json({ message: 'Not authorized to update categories' });
    }

    try {
      const category = await TicketCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      
      const {
        name,
        description,
        parent,
        color,
        icon,
        defaultAssignee,
        defaultGroup,
        slaSettings,
        autoRules,
        isActive,
        sortOrder
      } = req.body;

      // Check for duplicate name (excluding current category)
      if (name && name !== category.name) {
        const existingCategory = await TicketCategory.findOne({ 
          name, 
          _id: { $ne: category._id } 
        });
        if (existingCategory) {
          return res.status(400).json({ message: 'Category name already exists' });
        }
        category.name = name;
      }

      // Update fields
      if (description !== undefined) category.description = description;
      if (parent !== undefined) category.parent = parent;
      if (color !== undefined) category.color = color;
      if (icon !== undefined) category.icon = icon;
      if (defaultAssignee !== undefined) category.defaultAssignee = defaultAssignee;
      if (defaultGroup !== undefined) category.defaultGroup = defaultGroup;
      if (slaSettings !== undefined) category.slaSettings = slaSettings;
      if (autoRules !== undefined) category.autoRules = autoRules;
      if (isActive !== undefined) category.isActive = isActive;
      if (sortOrder !== undefined) category.sortOrder = sortOrder;
      
      category.updatedBy = req.user.id;

      await category.save();
      
      // Populate references for response
      await category.populate('parent', 'name');
      if (category.defaultAssignee) {
        await category.populate('defaultAssignee', 'name email');
      }
      if (category.defaultGroup) {
        await category.populate('defaultGroup', 'name');
      }
      await category.populate('createdBy', 'name email');
      await category.populate('updatedBy', 'name email');
      
      res.json(category);
    } catch (err) {
      console.error('Error updating category:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Category not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Delete a category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message
   */
  deleteCategory: async (req, res) => {
    // Check permissions
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'Not authorized to delete categories' });
    }

    try {
      const category = await TicketCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ message: 'Category not found' });
      }
      
      // Check if category has child categories
      const childCategories = await TicketCategory.find({ parent: category._id });
      if (childCategories.length > 0) {
        return res.status(400).json({ 
          message: 'Cannot delete category with child categories. Please delete or reassign child categories first.' 
        });
      }
      
      // Check if category is used by tickets
      const Ticket = require('../../models/Ticket');
      const ticketCount = await Ticket.countDocuments({ category: category.name });
      if (ticketCount > 0) {
        return res.status(400).json({ 
          message: `Cannot delete category. It is used by ${ticketCount} ticket(s). Please reassign tickets first.` 
        });
      }
      
      await category.deleteOne();
      
      res.json({ message: 'Category deleted successfully' });
    } catch (err) {
      console.error('Error deleting category:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Category not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Get category hierarchy
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with hierarchical categories
   */
  getCategoryHierarchy: async (req, res) => {
    try {
      const categories = await TicketCategory.find({ isActive: true })
        .sort({ level: 1, sortOrder: 1, name: 1 })
        .populate('defaultAssignee', 'name email')
        .populate('defaultGroup', 'name');
      
      // Build hierarchy
      const hierarchy = ticketCategoryController.buildHierarchy(categories);
      
      res.json(hierarchy);
    } catch (err) {
      console.error('Error getting category hierarchy:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Build hierarchical structure from flat category list
   * @param {Array} categories - Flat list of categories
   * @returns {Array} Hierarchical category structure
   */
  buildHierarchy: (categories) => {
    const categoryMap = new Map();
    const rootCategories = [];
    
    // Create map for quick lookup
    categories.forEach(category => {
      categoryMap.set(category._id.toString(), {
        ...category.toObject(),
        children: []
      });
    });
    
    // Build hierarchy
    categories.forEach(category => {
      const categoryObj = categoryMap.get(category._id.toString());
      
      if (category.parent) {
        const parentObj = categoryMap.get(category.parent.toString());
        if (parentObj) {
          parentObj.children.push(categoryObj);
        } else {
          // Orphaned category, treat as root
          rootCategories.push(categoryObj);
        }
      } else {
        rootCategories.push(categoryObj);
      }
    });
    
    return rootCategories;
  }
};

module.exports = ticketCategoryController;