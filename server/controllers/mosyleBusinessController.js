const MosyleBusinessAPI = require('../integrations/mosyleBusiness/mosyleBusinessAPI');
const MosyleBusinessConfig = require('../../models/MosyleBusinessConfig');

// Initialize Mosyle Business API with empty credentials (will be updated when needed)
let mosyleBusinessAPI = new MosyleBusinessAPI('', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envApiKey = process.env.MOSYLE_BUSINESS_API_KEY || '';
    const envDomain = process.env.MOSYLE_BUSINESS_DOMAIN || '';
    
    // If environment variables are set, use them directly
    if (envApiKey && envDomain) {
      mosyleBusinessAPI = new MosyleBusinessAPI(envApiKey, envDomain);
      return {
        apiKey: envApiKey,
        domain: envDomain,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('MOSYLE_BUSINESS_API_KEY or MOSYLE_BUSINESS_DOMAIN environment variables not set. Falling back to database config (not recommended).');
    
    const config = await MosyleBusinessConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      mosyleBusinessAPI = new MosyleBusinessAPI(config.apiKey, config.domain);
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Mosyle Business configuration:', error);
    throw error;
  }
};

/**
 * Get all Mosyle Business devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig();
    const devices = await mosyleBusinessAPI.getDevices(req.query);
    // Set cache-control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    // Ensure we always return an array, even if the API returns something else
    res.json(Array.isArray(devices) ? devices : []);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business devices:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business devices', error: error.message });
  }
};

/**
 * Get Mosyle Business device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const deviceId = req.params.id;
    const deviceDetails = await mosyleBusinessAPI.getDeviceDetails(deviceId);
    // Set cache-control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    res.json(deviceDetails);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business device details:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business device details', error: error.message });
  }
};

/**
 * Get all Mosyle Business users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    await getLatestConfig();
    const users = await mosyleBusinessAPI.getUsers(req.query);
    // Set cache-control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business users:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business users', error: error.message });
  }
};

/**
 * Get all Mosyle Business groups
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getGroups = async (req, res) => {
  try {
    await getLatestConfig();
    const groups = await mosyleBusinessAPI.getGroups(req.query);
    // Set cache-control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    res.json(groups);
  } catch (error) {
    console.error('Controller error fetching Mosyle Business groups:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business groups', error: error.message });
  }
};

/**
 * Save Mosyle Business configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Mosyle Business configuration request:', error);
    res.status(500).json({ message: 'Error handling Mosyle Business configuration request', error: error.message });
  }
};

/**
 * Get Mosyle Business configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Set cache-control headers to prevent caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    
    // Always prioritize environment variables for authentication
    const envApiKey = process.env.MOSYLE_BUSINESS_API_KEY || '';
    const envDomain = process.env.MOSYLE_BUSINESS_DOMAIN || '';
    
    // If environment variables are set, use them directly
    if (envApiKey && envDomain) {
      // Don't send the actual API key back to the client for security
      res.json({
        domain: envDomain,
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('MOSYLE_BUSINESS_API_KEY or MOSYLE_BUSINESS_DOMAIN environment variables not set. Falling back to database config (not recommended).');
    
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Mosyle Business configuration not found. Please set the required environment variables.' });
    }

    // Don't send the actual API key back to the client for security
    res.json({
      domain: config.domain,
      configuredAt: config.updatedAt,
      fromEnv: !!config.fromEnv
    });
  } catch (error) {
    console.error('Error fetching Mosyle Business configuration:', error);
    res.status(500).json({ message: 'Error fetching Mosyle Business configuration', error: error.message });
  }
};