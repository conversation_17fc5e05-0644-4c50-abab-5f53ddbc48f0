const Note = require('../../models/Note');
const { validationResult } = require('express-validator');

/**
 * Note Controller
 * Handles API operations for user notes
 */
const noteController = {
  /**
   * Get all notes for the current user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with notes
   */
  getUserNotes: async (req, res) => {
    try {
      const notes = await Note.find({ userId: req.user.id })
        .sort({ pinned: -1, updatedAt: -1 });
      
      res.json(notes);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get note by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with note
   */
  getNoteById: async (req, res) => {
    try {
      const note = await Note.findById(req.params.id);
      
      if (!note) {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      // Check if user is authorized to view this note
      if (note.userId.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to view this note' });
      }
      
      res.json(note);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Create a new note
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created note
   */
  createNote: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        type,
        completed,
        color,
        pinned,
        tags,
        categories
      } = req.body;

      // Create new note
      const newNote = new Note({
        title,
        content,
        type: type || 'note',
        userId: req.user.id,
        completed: completed || false,
        color: color || '#ffffff',
        pinned: pinned || false,
        tags: tags || [],
        categories: categories || []
      });

      const note = await newNote.save();
      res.json(note);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Update a note
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated note
   */
  updateNote: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        type,
        completed,
        color,
        pinned,
        tags,
        categories
      } = req.body;

      // Find note by ID
      let note = await Note.findById(req.params.id);
      
      if (!note) {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      // Check if user is authorized to update this note
      if (note.userId.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this note' });
      }
      
      // Update note fields
      if (title) note.title = title;
      if (content !== undefined) note.content = content;
      if (type) note.type = type;
      if (completed !== undefined) note.completed = completed;
      if (color) note.color = color;
      if (pinned !== undefined) note.pinned = pinned;
      if (tags) note.tags = tags;
      if (categories) note.categories = categories;
      
      await note.save();
      res.json(note);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Delete a note
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response
   */
  deleteNote: async (req, res) => {
    try {
      // Find note by ID
      const note = await Note.findById(req.params.id);
      
      if (!note) {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      // Check if user is authorized to delete this note
      if (note.userId.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to delete this note' });
      }
      
      await note.remove();
      res.json({ msg: 'Note removed' });
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Toggle note completion status (for to-dos)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated note
   */
  toggleNoteCompletion: async (req, res) => {
    try {
      // Find note by ID
      const note = await Note.findById(req.params.id);
      
      if (!note) {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      // Check if user is authorized to update this note
      if (note.userId.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this note' });
      }
      
      // Toggle completed status
      note.completed = !note.completed;
      
      await note.save();
      res.json(note);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Toggle note pinned status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated note
   */
  toggleNotePinned: async (req, res) => {
    try {
      // Find note by ID
      const note = await Note.findById(req.params.id);
      
      if (!note) {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      // Check if user is authorized to update this note
      if (note.userId.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this note' });
      }
      
      // Toggle pinned status
      note.pinned = !note.pinned;
      
      await note.save();
      res.json(note);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Note not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
};

module.exports = noteController;