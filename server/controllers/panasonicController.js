const PanasonicAPI = require('../integrations/panasonic/panasonicAPI');

// Initialize Panasonic API with environment variables
let panasonicAPI = new PanasonicAPI();

/**
 * Get the latest Panasonic camera configuration
 * @returns {Promise<Object|null>} The latest configuration or null if not found
 */
const getLatestConfig = async () => {
  try {
    // Check if environment variables are set
    const envHost = process.env.PANASONIC_HOST || '';
    const envUsername = process.env.PANASONIC_USERNAME || '';
    const envPassword = process.env.PANASONIC_PASSWORD || '';
    const envPort = process.env.PANASONIC_PORT || 80;
    const envModel = process.env.PANASONIC_MODEL || 'AW-HE40';
    const envProtocol = process.env.PANASONIC_PROTOCOL || 'http';
    
    // If environment variables are set, use them directly
    if (envHost && envUsername && envPassword) {
      return {
        host: envHost,
        username: envUsername,
        password: envPassword,
        port: envPort,
        model: envModel,
        protocol: envProtocol,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If no environment variables are set, log a warning
    console.warn('Panasonic camera environment variables not set. Please configure PANASONIC_HOST, PANASONIC_USERNAME, and PANASONIC_PASSWORD in your environment.');
    
    return null;
  } catch (error) {
    console.error('Error fetching Panasonic camera configuration:', error);
    throw error;
  }
};

// Export the getLatestConfig function for use by other modules
exports.getLatestConfig = getLatestConfig;

/**
 * Initialize the Panasonic camera API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.initialize = async (req, res) => {
  try {
    await panasonicAPI.initialize();
    res.json({ message: 'Panasonic camera API initialized successfully' });
  } catch (error) {
    console.error('Error initializing Panasonic camera API:', error);
    res.status(500).json({ message: 'Error initializing Panasonic camera API', error: error.message });
  }
};

// Import the cache utility
const cacheUtil = require('../utils/cacheUtil');

/**
 * Get camera information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraInfo = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('panasonic-camera-info');
    
    // Try to get the camera info from cache first
    let cameraInfo = cacheUtil.get(cacheKey);
    
    if (!cameraInfo) {
      console.log('Panasonic camera info cache miss, fetching from API');
      // If not in cache, fetch from API
      cameraInfo = await panasonicAPI.getCameraInfo();
      
      if (!cameraInfo) {
        return res.status(500).json({ message: 'Error fetching camera information' });
      }
      
      // Cache the result for 15 minutes (camera info doesn't change often)
      cacheUtil.set(cacheKey, cameraInfo, 15 * 60 * 1000);
    } else {
      console.log('Panasonic camera info served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=900'); // 15 minutes
    
    res.json(cameraInfo);
  } catch (error) {
    console.error('Controller error fetching camera information:', error);
    res.status(500).json({ message: 'Error fetching camera information', error: error.message });
  }
};

/**
 * Get camera status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraStatus = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    const cameraStatus = await panasonicAPI.getCameraStatus();
    
    if (!cameraStatus) {
      return res.status(500).json({ message: 'Error fetching camera status' });
    }
    
    res.json(cameraStatus);
  } catch (error) {
    console.error('Controller error fetching camera status:', error);
    res.status(500).json({ message: 'Error fetching camera status', error: error.message });
  }
};

/**
 * Get camera presets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCameraPresets = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('panasonic-camera-presets');
    
    // Try to get the camera presets from cache first
    let presets = cacheUtil.get(cacheKey);
    
    if (!presets) {
      console.log('Panasonic camera presets cache miss, fetching from API');
      // If not in cache, fetch from API
      presets = await panasonicAPI.getCameraPresets();
      
      if (!presets) {
        return res.status(500).json({ message: 'Error fetching camera presets' });
      }
      
      // Cache the result for 30 minutes (presets rarely change)
      cacheUtil.set(cacheKey, presets, 30 * 60 * 1000);
    } else {
      console.log('Panasonic camera presets served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=1800'); // 30 minutes
    
    res.json(presets);
  } catch (error) {
    console.error('Controller error fetching camera presets:', error);
    res.status(500).json({ message: 'Error fetching camera presets', error: error.message });
  }
};

/**
 * Move camera to preset position
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.moveToPreset = async (req, res) => {
  try {
    const { presetId } = req.params;
    
    if (!presetId) {
      return res.status(400).json({ message: 'Preset ID is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    const success = await panasonicAPI.moveToPreset(presetId);
    
    if (!success) {
      return res.status(500).json({ message: 'Error moving camera to preset position' });
    }
    
    res.json({ message: 'Camera moved to preset position successfully' });
  } catch (error) {
    console.error('Controller error moving camera to preset position:', error);
    res.status(500).json({ message: 'Error moving camera to preset position', error: error.message });
  }
};

/**
 * Control camera PTZ (Pan, Tilt, Zoom)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlPTZ = async (req, res) => {
  try {
    const { direction, speed } = req.body;
    
    if (!direction) {
      return res.status(400).json({ message: 'Direction is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    const success = await panasonicAPI.controlPTZ(direction, speed);
    
    if (!success) {
      return res.status(500).json({ message: 'Error controlling camera PTZ' });
    }
    
    res.json({ message: 'Camera PTZ controlled successfully' });
  } catch (error) {
    console.error('Controller error controlling camera PTZ:', error);
    res.status(500).json({ message: 'Error controlling camera PTZ', error: error.message });
  }
};

/**
 * Control camera zoom
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlZoom = async (req, res) => {
  try {
    const { direction, speed } = req.body;
    
    if (!direction) {
      return res.status(400).json({ message: 'Direction is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    const success = await panasonicAPI.controlZoom(direction, speed);
    
    if (!success) {
      return res.status(500).json({ message: 'Error controlling camera zoom' });
    }
    
    res.json({ message: 'Camera zoom controlled successfully' });
  } catch (error) {
    console.error('Controller error controlling camera zoom:', error);
    res.status(500).json({ message: 'Error controlling camera zoom', error: error.message });
  }
};

/**
 * Get camera snapshot
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSnapshot = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
    }
    
    const snapshot = await panasonicAPI.getSnapshot();
    
    if (!snapshot) {
      return res.status(500).json({ message: 'Error fetching camera snapshot' });
    }
    
    res.set('Content-Type', 'image/jpeg');
    res.send(snapshot);
  } catch (error) {
    console.error('Controller error fetching camera snapshot:', error);
    res.status(500).json({ message: 'Error fetching camera snapshot', error: error.message });
  }
};

/**
 * Get configuration status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('panasonic-config');
    
    // Try to get the config from cache first
    let safeConfig = cacheUtil.get(cacheKey);
    
    if (!safeConfig) {
      console.log('Panasonic config cache miss, fetching from sources');
      
      const config = await getLatestConfig();
      
      if (!config) {
        return res.status(404).json({ message: 'Panasonic camera not configured. Please set the required environment variables.' });
      }
      
      // Remove sensitive information
      safeConfig = {
        ...config,
        password: undefined
      };
      
      // Cache the result for 15 minutes (configuration doesn't change often)
      cacheUtil.set(cacheKey, safeConfig, 15 * 60 * 1000);
    } else {
      console.log('Panasonic config served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=900'); // 15 minutes
    
    res.json(safeConfig);
  } catch (error) {
    console.error('Controller error fetching configuration:', error);
    res.status(500).json({ message: 'Error fetching configuration', error: error.message });
  }
};