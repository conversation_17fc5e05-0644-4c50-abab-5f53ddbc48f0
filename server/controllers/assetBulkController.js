const Asset = require('../../models/Asset');
const AssetCategory = require('../../models/AssetCategory');
const AssetLocation = require('../../models/AssetLocation');
const AssetAuditLog = require('../../models/AssetAuditLog');
const multer = require('multer');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const fs = require('fs');
const path = require('path');

/**
 * Asset Bulk Operations Controller
 * Handles bulk import/export operations for assets
 */

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/temp/',
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || 
        file.mimetype === 'application/vnd.ms-excel' ||
        file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      cb(null, true);
    } else {
      cb(new Error('Only CSV and Excel files are allowed'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

/**
 * Export assets to CSV
 */
exports.exportAssets = async (req, res) => {
  try {
    const {
      format = 'csv',
      includeDeleted = false,
      category,
      location,
      status,
      startDate,
      endDate,
      fields = 'all'
    } = req.query;

    // Build query
    const query = {};
    if (!includeDeleted || includeDeleted === 'false') {
      query.isDeleted = false;
    }
    if (category) query.category = category;
    if (location) query.location = location;
    if (status) query.status = status;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // Get assets with populated references
    const assets = await Asset.find(query)
      .populate('category', 'name')
      .populate('location', 'name locationType')
      .populate('assignedTo', 'name email')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 });

    // Define export fields
    const allFields = [
      { id: 'assetTag', title: 'Asset Tag' },
      { id: 'name', title: 'Name' },
      { id: 'serialNumber', title: 'Serial Number' },
      { id: 'model', title: 'Model' },
      { id: 'manufacturer', title: 'Manufacturer' },
      { id: 'category', title: 'Category' },
      { id: 'status', title: 'Status' },
      { id: 'condition', title: 'Condition' },
      { id: 'location', title: 'Location' },
      { id: 'locationType', title: 'Location Type' },
      { id: 'assignedTo', title: 'Assigned To' },
      { id: 'assignedToEmail', title: 'Assigned To Email' },
      { id: 'department', title: 'Department' },
      { id: 'purchasePrice', title: 'Purchase Price' },
      { id: 'currentValue', title: 'Current Value' },
      { id: 'purchaseDate', title: 'Purchase Date' },
      { id: 'warrantyExpiration', title: 'Warranty Expiration' },
      { id: 'vendor', title: 'Vendor' },
      { id: 'poNumber', title: 'PO Number' },
      { id: 'maintenanceSchedule', title: 'Maintenance Schedule' },
      { id: 'riskLevel', title: 'Risk Level' },
      { id: 'createdAt', title: 'Created Date' },
      { id: 'createdBy', title: 'Created By' }
    ];

    // Filter fields if specific fields requested
    let exportFields = allFields;
    if (fields !== 'all') {
      const requestedFields = fields.split(',');
      exportFields = allFields.filter(field => requestedFields.includes(field.id));
    }

    // Transform data for export
    const exportData = assets.map(asset => {
      const data = {
        assetTag: asset.assetTag,
        name: asset.name,
        serialNumber: asset.serialNumber || '',
        model: asset.model || '',
        manufacturer: asset.manufacturer || '',
        category: asset.category?.name || '',
        status: asset.status,
        condition: asset.condition,
        location: asset.location?.name || '',
        locationType: asset.location?.locationType || '',
        assignedTo: asset.assignedTo?.name || '',
        assignedToEmail: asset.assignedTo?.email || '',
        department: asset.department || '',
        purchasePrice: asset.purchasePrice || '',
        currentValue: asset.currentValue || '',
        purchaseDate: asset.purchaseDate ? asset.purchaseDate.toISOString().split('T')[0] : '',
        warrantyExpiration: asset.warrantyExpiration ? asset.warrantyExpiration.toISOString().split('T')[0] : '',
        vendor: asset.vendor || '',
        poNumber: asset.poNumber || '',
        maintenanceSchedule: asset.maintenanceSchedule || '',
        riskLevel: asset.riskLevel || '',
        createdAt: asset.createdAt.toISOString().split('T')[0],
        createdBy: asset.createdBy?.name || ''
      };

      // Add technical specifications
      if (asset.specifications) {
        data.cpu = asset.specifications.cpu || '';
        data.ram = asset.specifications.ram || '';
        data.storage = asset.specifications.storage || '';
        data.os = asset.specifications.os || '';
        data.ipAddress = asset.specifications.ipAddress || '';
        data.macAddress = asset.specifications.macAddress || '';
        data.hostname = asset.specifications.hostname || '';
      }

      return data;
    });

    if (format === 'json') {
      res.json({
        assets: exportData,
        count: exportData.length,
        exportDate: new Date(),
        filters: { category, location, status, startDate, endDate }
      });
      return;
    }

    // Generate CSV
    const filename = `assets-export-${new Date().toISOString().split('T')[0]}.csv`;
    const filepath = path.join('uploads/temp/', filename);

    const csvWriter = createCsvWriter({
      path: filepath,
      header: exportFields
    });

    await csvWriter.writeRecords(exportData);

    // Log the export
    await AssetAuditLog.logAction({
      asset: null,
      assetTag: 'BULK_EXPORT',
      action: 'exported',
      actionDescription: `Exported ${exportData.length} assets to CSV`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'asset_management',
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    // Send file
    res.download(filepath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      // Clean up temp file
      fs.unlink(filepath, (unlinkErr) => {
        if (unlinkErr) console.error('Error deleting temp file:', unlinkErr);
      });
    });

  } catch (error) {
    console.error('Error exporting assets:', error);
    res.status(500).json({ message: 'Error exporting assets', error: error.message });
  }
};

/**
 * Import assets from CSV
 */
exports.importAssets = [
  upload.single('file'),
  async (req, res) => {
    const results = {
      total: 0,
      successful: 0,
      failed: 0,
      errors: [],
      created: [],
      updated: []
    };

    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const {
        updateExisting = false,
        skipErrors = true,
        defaultCategory,
        defaultLocation,
        defaultStatus = 'active'
      } = req.body;

      // Parse CSV
      const csvData = [];
      const filepath = req.file.path;

      await new Promise((resolve, reject) => {
        fs.createReadStream(filepath)
          .pipe(csv())
          .on('data', (row) => csvData.push(row))
          .on('end', resolve)
          .on('error', reject);
      });

      results.total = csvData.length;

      // Validate and prepare data
      const categories = await AssetCategory.find({ isActive: true });
      const locations = await AssetLocation.find({ isActive: true });
      
      const categoryMap = new Map();
      categories.forEach(cat => {
        categoryMap.set(cat.name.toLowerCase(), cat._id);
      });

      const locationMap = new Map();
      locations.forEach(loc => {
        locationMap.set(loc.name.toLowerCase(), loc._id);
      });

      // Process each row
      for (let i = 0; i < csvData.length; i++) {
        const row = csvData[i];
        
        try {
          // Validate required fields
          if (!row.assetTag || !row.name) {
            results.errors.push({
              row: i + 1,
              error: 'Missing required fields: assetTag and name are required'
            });
            results.failed++;
            if (!skipErrors) break;
            continue;
          }

          // Check if asset exists
          const existingAsset = await Asset.findOne({ 
            assetTag: row.assetTag, 
            isDeleted: false 
          });

          if (existingAsset && !updateExisting) {
            results.errors.push({
              row: i + 1,
              error: `Asset tag '${row.assetTag}' already exists`
            });
            results.failed++;
            if (!skipErrors) break;
            continue;
          }

          // Prepare asset data
          const assetData = {
            assetTag: row.assetTag,
            name: row.name,
            serialNumber: row.serialNumber || '',
            model: row.model || '',
            manufacturer: row.manufacturer || '',
            description: row.description || '',
            status: row.status || defaultStatus,
            condition: row.condition || 'good',
            department: row.department || '',
            purchasePrice: row.purchasePrice ? parseFloat(row.purchasePrice) : null,
            currentValue: row.currentValue ? parseFloat(row.currentValue) : null,
            depreciationRate: row.depreciationRate ? parseFloat(row.depreciationRate) : null,
            vendor: row.vendor || '',
            poNumber: row.poNumber || '',
            maintenanceSchedule: row.maintenanceSchedule || 'annual',
            riskLevel: row.riskLevel || 'low',
            tags: row.tags ? row.tags.split(',').map(tag => tag.trim()) : [],
            createdBy: req.user._id
          };

          // Handle dates
          if (row.purchaseDate) {
            assetData.purchaseDate = new Date(row.purchaseDate);
          }
          if (row.warrantyExpiration) {
            assetData.warrantyExpiration = new Date(row.warrantyExpiration);
          }

          // Map category
          if (row.category) {
            const categoryId = categoryMap.get(row.category.toLowerCase());
            if (categoryId) {
              assetData.category = categoryId;
            } else if (defaultCategory) {
              assetData.category = defaultCategory;
            } else {
              results.errors.push({
                row: i + 1,
                error: `Category '${row.category}' not found`
              });
              results.failed++;
              if (!skipErrors) break;
              continue;
            }
          } else if (defaultCategory) {
            assetData.category = defaultCategory;
          }

          // Map location
          if (row.location) {
            const locationId = locationMap.get(row.location.toLowerCase());
            if (locationId) {
              assetData.location = locationId;
            } else if (defaultLocation) {
              assetData.location = defaultLocation;
            }
          } else if (defaultLocation) {
            assetData.location = defaultLocation;
          }

          // Handle specifications
          assetData.specifications = {
            cpu: row.cpu || '',
            ram: row.ram || '',
            storage: row.storage || '',
            os: row.os || '',
            ipAddress: row.ipAddress || '',
            macAddress: row.macAddress || '',
            hostname: row.hostname || '',
            customFields: []
          };

          // Create or update asset
          if (existingAsset && updateExisting) {
            Object.assign(existingAsset, assetData);
            existingAsset._modifiedBy = req.user._id;
            await existingAsset.save();
            
            results.updated.push({
              assetTag: existingAsset.assetTag,
              name: existingAsset.name
            });
          } else {
            const newAsset = new Asset(assetData);
            newAsset._modifiedBy = req.user._id;
            await newAsset.save();
            
            results.created.push({
              assetTag: newAsset.assetTag,
              name: newAsset.name
            });
          }

          results.successful++;

        } catch (rowError) {
          results.errors.push({
            row: i + 1,
            error: rowError.message
          });
          results.failed++;
          
          if (!skipErrors) break;
        }
      }

      // Log the import
      await AssetAuditLog.logAction({
        asset: null,
        assetTag: 'BULK_IMPORT',
        action: 'imported',
        actionDescription: `Imported ${results.successful}/${results.total} assets from CSV. ${results.failed} failed.`,
        performedBy: req.user._id,
        performedByName: req.user.name,
        performedByEmail: req.user.email,
        category: 'asset_management',
        severity: results.failed > 0 ? 'warning' : 'info',
        sessionInfo: {
          sessionId: req.session?.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      });

      // Clean up uploaded file
      fs.unlink(filepath, (err) => {
        if (err) console.error('Error deleting uploaded file:', err);
      });

      res.json(results);

    } catch (error) {
      console.error('Error importing assets:', error);
      
      // Clean up uploaded file on error
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('Error deleting uploaded file:', err);
        });
      }
      
      res.status(500).json({ 
        message: 'Error importing assets', 
        error: error.message,
        results
      });
    }
  }
];

/**
 * Get import template
 */
exports.getImportTemplate = async (req, res) => {
  try {
    const { includeExamples = false } = req.query;

    const headers = [
      'assetTag',
      'name',
      'serialNumber',
      'model',
      'manufacturer',
      'description',
      'category',
      'status',
      'condition',
      'location',
      'department',
      'purchasePrice',
      'currentValue',
      'purchaseDate',
      'warrantyExpiration',
      'vendor',
      'poNumber',
      'maintenanceSchedule',
      'riskLevel',
      'tags',
      'cpu',
      'ram',
      'storage',
      'os',
      'ipAddress',
      'macAddress',
      'hostname'
    ];

    const templateData = [];
    
    if (includeExamples === 'true') {
      templateData.push({
        assetTag: 'LAPTOP-2024-0001',
        name: 'Dell Latitude 7420',
        serialNumber: '*********',
        model: 'Latitude 7420',
        manufacturer: 'Dell',
        description: 'Business laptop for staff use',
        category: 'Laptops',
        status: 'active',
        condition: 'excellent',
        location: 'IT Office',
        department: 'Information Technology',
        purchasePrice: '1200.00',
        currentValue: '900.00',
        purchaseDate: '2024-01-15',
        warrantyExpiration: '2027-01-15',
        vendor: 'Dell Inc',
        poNumber: 'PO-2024-001',
        maintenanceSchedule: 'annual',
        riskLevel: 'medium',
        tags: 'portable,business,windows',
        cpu: 'Intel Core i7-1165G7',
        ram: '16GB DDR4',
        storage: '512GB NVMe SSD',
        os: 'Windows 11 Pro',
        ipAddress: '',
        macAddress: '00:14:22:01:23:45',
        hostname: 'STAFF-LAPTOP-001'
      });
      
      templateData.push({
        assetTag: 'PRINTER-2024-0001',
        name: 'HP LaserJet Pro 4025dn',
        serialNumber: '*********',
        model: 'LaserJet Pro 4025dn',
        manufacturer: 'HP',
        description: 'Network laser printer for office use',
        category: 'Printers',
        status: 'active',
        condition: 'good',
        location: 'Main Office',
        department: 'Administration',
        purchasePrice: '450.00',
        currentValue: '350.00',
        purchaseDate: '2024-02-01',
        warrantyExpiration: '2025-02-01',
        vendor: 'HP Inc',
        poNumber: 'PO-2024-002',
        maintenanceSchedule: 'semi_annual',
        riskLevel: 'low',
        tags: 'network,laser,office',
        cpu: '',
        ram: '',
        storage: '',
        os: '',
        ipAddress: '*************',
        macAddress: '00:1F:29:01:23:46',
        hostname: 'OFFICE-PRINTER-001'
      });
    }

    const filename = `asset-import-template-${new Date().toISOString().split('T')[0]}.csv`;
    const filepath = path.join('uploads/temp/', filename);

    const csvWriter = createCsvWriter({
      path: filepath,
      header: headers.map(header => ({ id: header, title: header }))
    });

    await csvWriter.writeRecords(templateData);

    res.download(filepath, filename, (err) => {
      if (err) {
        console.error('Error sending template file:', err);
      }
      // Clean up temp file
      fs.unlink(filepath, (unlinkErr) => {
        if (unlinkErr) console.error('Error deleting temp template file:', unlinkErr);
      });
    });

  } catch (error) {
    console.error('Error generating import template:', error);
    res.status(500).json({ message: 'Error generating import template', error: error.message });
  }
};

/**
 * Validate import file
 */
exports.validateImportFile = [
  upload.single('file'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const validation = {
        isValid: true,
        rowCount: 0,
        errors: [],
        warnings: [],
        preview: []
      };

      // Parse CSV for validation
      const csvData = [];
      const filepath = req.file.path;

      await new Promise((resolve, reject) => {
        fs.createReadStream(filepath)
          .pipe(csv())
          .on('data', (row) => csvData.push(row))
          .on('end', resolve)
          .on('error', reject);
      });

      validation.rowCount = csvData.length;

      // Check required columns
      const requiredColumns = ['assetTag', 'name'];
      const firstRow = csvData[0] || {};
      const missingColumns = requiredColumns.filter(col => !firstRow.hasOwnProperty(col));
      
      if (missingColumns.length > 0) {
        validation.isValid = false;
        validation.errors.push(`Missing required columns: ${missingColumns.join(', ')}`);
      }

      // Validate data (sample first 10 rows)
      const sampleSize = Math.min(10, csvData.length);
      for (let i = 0; i < sampleSize; i++) {
        const row = csvData[i];
        const rowErrors = [];
        
        if (!row.assetTag || row.assetTag.trim() === '') {
          rowErrors.push('Asset Tag is required');
        }
        
        if (!row.name || row.name.trim() === '') {
          rowErrors.push('Name is required');
        }
        
        if (row.purchasePrice && isNaN(parseFloat(row.purchasePrice))) {
          rowErrors.push('Purchase Price must be a number');
        }
        
        if (row.currentValue && isNaN(parseFloat(row.currentValue))) {
          rowErrors.push('Current Value must be a number');
        }
        
        if (rowErrors.length > 0) {
          validation.errors.push(`Row ${i + 1}: ${rowErrors.join(', ')}`);
          validation.isValid = false;
        }
        
        // Add to preview
        if (i < 5) {
          validation.preview.push(row);
        }
      }

      // Check for duplicate asset tags in file
      const assetTags = csvData.map(row => row.assetTag).filter(tag => tag);
      const duplicateTags = assetTags.filter((tag, index) => assetTags.indexOf(tag) !== index);
      
      if (duplicateTags.length > 0) {
        validation.warnings.push(`Duplicate asset tags in file: ${[...new Set(duplicateTags)].join(', ')}`);
      }

      // Check for existing asset tags in database
      const existingAssets = await Asset.find({ 
        assetTag: { $in: assetTags }, 
        isDeleted: false 
      }).select('assetTag');
      
      if (existingAssets.length > 0) {
        validation.warnings.push(`Asset tags already exist in database: ${existingAssets.map(a => a.assetTag).join(', ')}`);
      }

      // Clean up uploaded file
      fs.unlink(filepath, (err) => {
        if (err) console.error('Error deleting validation file:', err);
      });

      res.json(validation);

    } catch (error) {
      console.error('Error validating import file:', error);
      
      // Clean up uploaded file on error
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('Error deleting uploaded file:', err);
        });
      }
      
      res.status(500).json({ message: 'Error validating import file', error: error.message });
    }
  }
];