const GLPIAPI = require('../integrations/glpi/glpiAPI');
const GLPIConfig = require('../../models/GLPIConfig');

// Initialize GLPI API with empty credentials (will be updated when needed)
let glpiAPI = null;
let sessionInitialized = false;
let lastSessionInitTime = null;
const SESSION_TIMEOUT = 60 * 60 * 1000; // 1 hour in milliseconds

// Helper function to get the latest configuration and initialize the API
const getLatestConfig = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envUrl = process.env.GLPI_API_URL || '';
    const envAppToken = process.env.GLPI_APP_TOKEN || '';
    const envUserToken = process.env.GLPI_USER_TOKEN || '';
    
    // If environment variables are set, use them directly
    if (envUrl && envAppToken && envUserToken) {
      // Only create a new API instance if it doesn't exist
      if (!glpiAPI) {
        glpiAPI = new GLPIAPI(
          envUrl,
          envAppToken,
          envUserToken
        );
      }
      
      // Initialize the API session if not already initialized or if session has expired
      const currentTime = new Date().getTime();
      if (!sessionInitialized || !lastSessionInitTime || (currentTime - lastSessionInitTime > SESSION_TIMEOUT)) {
        await initializeSession();
      }
      
      return {
        url: envUrl,
        appToken: envAppToken,
        userToken: envUserToken,
        username: '',
        password: '',
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('GLPI environment variables not set. Falling back to database config (not recommended).');
    
    const config = await GLPIConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      // Only create a new API instance if it doesn't exist or if the config has changed
      if (!glpiAPI || 
          glpiAPI.url !== config.url || 
          glpiAPI.appToken !== config.appToken || 
          glpiAPI.userToken !== config.userToken) {
        glpiAPI = new GLPIAPI(
          config.url,
          config.appToken,
          config.userToken
        );
        // Reset session state when API instance changes
        sessionInitialized = false;
      }
      
      // Initialize the API session if not already initialized or if session has expired
      const currentTime = new Date().getTime();
      if (!sessionInitialized || !lastSessionInitTime || (currentTime - lastSessionInitTime > SESSION_TIMEOUT)) {
        await initializeSession();
      }
      
      return config;
    }

    // No configuration found in the database or environment variables
    return null;
  } catch (error) {
    console.error('Error fetching GLPI configuration:', error);
    sessionInitialized = false; // Reset session state on error
    throw error;
  }
};

// Helper function to initialize the session
const initializeSession = async () => {
  if (!glpiAPI) {
    throw new Error('GLPI API not initialized');
  }
  
  try {
    if (glpiAPI.userToken) {
      await glpiAPI.initSessionByUserToken();
    } else if (glpiAPI.username && glpiAPI.password) {
      await glpiAPI.initSessionByCredentials(glpiAPI.username, glpiAPI.password);
    } else {
      throw new Error('No authentication method available');
    }
    
    sessionInitialized = true;
    lastSessionInitTime = new Date().getTime();
    console.log('GLPI session initialized successfully');
  } catch (error) {
    console.error('Failed to initialize GLPI session:', error);
    sessionInitialized = false;
    throw error;
  }
};

/**
 * Save GLPI configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling GLPI configuration request:', error);
    res.status(500).json({ message: 'Error handling GLPI configuration request', error: error.message });
  }
};

/**
 * Get GLPI configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Always prioritize environment variables for authentication
    const envUrl = process.env.GLPI_API_URL || '';
    const envAppToken = process.env.GLPI_APP_TOKEN || '';
    const envUserToken = process.env.GLPI_USER_TOKEN || '';
    
    // If environment variables are set, use them directly
    if (envUrl && envAppToken && envUserToken) {
      // Don't send the actual tokens back to the client for security
      res.json({
        url: envUrl,
        hasAppToken: true,
        hasUserToken: true,
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('GLPI environment variables not set. Falling back to database config (not recommended).');
    
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found. Please set the required environment variables.' });
    }

    // Don't send the actual password or tokens back to the client for security
    res.json({
      url: config.url,
      username: config.username,
      hasPassword: !!config.password,
      hasUserToken: !!config.userToken,
      configuredAt: config.updatedAt,
      fromEnv: !!config.fromEnv
    });
  } catch (error) {
    console.error('Error fetching GLPI configuration:', error);
    res.status(500).json({ message: 'Error fetching GLPI configuration', error: error.message });
  }
};

/**
 * Test GLPI connection
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.testConnection = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()
    
    // Get profile information to verify connection
    const profileInfo = await glpiAPI.getMyProfiles();

    res.json({ 
      message: 'GLPI connection successful',
      profileInfo
    });
  } catch (error) {
    console.error('Error testing GLPI connection:', error);
    res.status(500).json({ message: 'Error testing GLPI connection', error: error.message });
  }
};

/**
 * Get assets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssets = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Get assets
    const assets = await glpiAPI.getAssets(req.query);

    res.json(assets);
  } catch (error) {
    console.error('Error getting GLPI assets:', error);
    res.status(500).json({ message: 'Error getting GLPI assets', error: error.message });
  }
};

/**
 * Get asset by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAsset = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ message: 'Asset ID is required' });
    }

    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Get asset
    const asset = await glpiAPI.getAsset(id);

    res.json(asset);
  } catch (error) {
    console.error('Error getting GLPI asset:', error);
    res.status(500).json({ message: 'Error getting GLPI asset', error: error.message });
  }
};

/**
 * Search assets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchAssets = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Search assets
    const results = await glpiAPI.searchAssets(req.query);

    res.json(results);
  } catch (error) {
    console.error('Error searching GLPI assets:', error);
    res.status(500).json({ message: 'Error searching GLPI assets', error: error.message });
  }
};

/**
 * Get asset types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetTypes = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Get asset types
    const types = await glpiAPI.getAssetTypes();

    res.json(types);
  } catch (error) {
    console.error('Error getting GLPI asset types:', error);
    res.status(500).json({ message: 'Error getting GLPI asset types', error: error.message });
  }
};

/**
 * Get asset models
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetModels = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Get asset models
    const models = await glpiAPI.getAssetModels();

    res.json(models);
  } catch (error) {
    console.error('Error getting GLPI asset models:', error);
    res.status(500).json({ message: 'Error getting GLPI asset models', error: error.message });
  }
};

/**
 * Get asset manufacturers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetManufacturers = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Get asset manufacturers
    const manufacturers = await glpiAPI.getAssetManufacturers();

    res.json(manufacturers);
  } catch (error) {
    console.error('Error getting GLPI asset manufacturers:', error);
    res.status(500).json({ message: 'Error getting GLPI asset manufacturers', error: error.message });
  }
};

/**
 * Get asset categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAssetCategories = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Get asset categories
    const categories = await glpiAPI.getAssetCategories();

    res.json(categories);
  } catch (error) {
    console.error('Error getting GLPI asset categories:', error);
    res.status(500).json({ message: 'Error getting GLPI asset categories', error: error.message });
  }
};

/**
 * Create asset
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createAsset = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Create asset
    const asset = await glpiAPI.createAsset(req.body);

    res.json(asset);
  } catch (error) {
    console.error('Error creating GLPI asset:', error);
    res.status(500).json({ message: 'Error creating GLPI asset', error: error.message });
  }
};

/**
 * Update asset
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateAsset = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ message: 'Asset ID is required' });
    }

    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Update asset
    const asset = await glpiAPI.updateAsset(id, req.body);

    res.json(asset);
  } catch (error) {
    console.error('Error updating GLPI asset:', error);
    res.status(500).json({ message: 'Error updating GLPI asset', error: error.message });
  }
};

/**
 * Delete asset
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteAsset = async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ message: 'Asset ID is required' });
    }

    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'GLPI configuration not found' });
    }

    // Session is already initialized by getLatestConfig()

    // Delete asset
    await glpiAPI.deleteAsset(id);

    res.json({ message: 'Asset deleted successfully' });
  } catch (error) {
    console.error('Error deleting GLPI asset:', error);
    res.status(500).json({ message: 'Error deleting GLPI asset', error: error.message });
  }
};
