const AssetMaintenanceRecord = require('../../models/AssetMaintenanceRecord');
const AssetWorkOrder = require('../../models/AssetWorkOrder');
const Asset = require('../../models/Asset');
const AssetAuditLog = require('../../models/AssetAuditLog');

/**
 * Asset Maintenance Controller
 * Manages asset maintenance records and scheduling
 */

/**
 * Get maintenance records with filtering and pagination
 */
exports.getMaintenanceRecords = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      assetId,
      status,
      maintenanceType,
      assignedTo,
      priority,
      startDate,
      endDate,
      overdue = false,
      sortBy = 'scheduledDate',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (assetId) query.asset = assetId;
    if (status) {
      if (Array.isArray(status)) {
        query.status = { $in: status };
      } else {
        query.status = status;
      }
    }
    if (maintenanceType) query.maintenanceType = maintenanceType;
    if (assignedTo) query.assignedTo = assignedTo;
    if (priority) query.priority = priority;
    
    if (startDate || endDate) {
      query.scheduledDate = {};
      if (startDate) query.scheduledDate.$gte = new Date(startDate);
      if (endDate) query.scheduledDate.$lte = new Date(endDate);
    }

    if (overdue === 'true') {
      query.status = 'scheduled';
      query.scheduledDate = { $lt: new Date() };
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [records, total] = await Promise.all([
      AssetMaintenanceRecord.find(query)
        .populate('asset', 'name assetTag category location')
        .populate('assignedTo', 'name email')
        .populate('performedBy.user', 'name email')
        .populate('createdBy', 'name email')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit)),
      AssetMaintenanceRecord.countDocuments(query)
    ]);

    res.json({
      records,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting maintenance records:', error);
    res.status(500).json({ message: 'Error getting maintenance records', error: error.message });
  }
};

/**
 * Get single maintenance record by ID
 */
exports.getMaintenanceRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    const record = await AssetMaintenanceRecord.findById(id)
      .populate('asset', 'name assetTag category location status condition')
      .populate('workOrder', 'workOrderNumber title status')
      .populate('assignedTo', 'name email')
      .populate('performedBy.user', 'name email')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    if (!record) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }

    res.json(record);
  } catch (error) {
    console.error('Error getting maintenance record:', error);
    res.status(500).json({ message: 'Error getting maintenance record', error: error.message });
  }
};

/**
 * Create new maintenance record
 */
exports.createMaintenanceRecord = async (req, res) => {
  try {
    const recordData = {
      ...req.body,
      createdBy: req.user._id
    };

    // Validate asset exists
    const asset = await Asset.findById(recordData.asset);
    if (!asset) {
      return res.status(400).json({ message: 'Asset not found' });
    }

    const record = new AssetMaintenanceRecord(recordData);
    record._modifiedBy = req.user._id;
    await record.save();

    // Log the maintenance record creation
    await AssetAuditLog.logAction({
      asset: asset._id,
      assetTag: asset.assetTag,
      action: 'maintenance_scheduled',
      actionDescription: `Maintenance record "${record.title}" scheduled for ${record.scheduledDate}`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'maintenance',
      relatedRecords: [{
        recordType: 'AssetMaintenanceRecord',
        recordId: record._id,
        recordDescription: record.title
      }],
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    // Populate references before sending response
    await record.populate([
      { path: 'asset', select: 'name assetTag category location' },
      { path: 'assignedTo', select: 'name email' },
      { path: 'createdBy', select: 'name email' }
    ]);

    res.status(201).json(record);
  } catch (error) {
    console.error('Error creating maintenance record:', error);
    res.status(500).json({ message: 'Error creating maintenance record', error: error.message });
  }
};

/**
 * Update maintenance record
 */
exports.updateMaintenanceRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const record = await AssetMaintenanceRecord.findById(id);
    if (!record) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }

    // Calculate actual duration if dates are being updated
    if (updates.startDate || updates.completedDate) {
      const startDate = updates.startDate ? new Date(updates.startDate) : record.startDate;
      const completedDate = updates.completedDate ? new Date(updates.completedDate) : record.completedDate;
      
      if (startDate && completedDate) {
        record.actualDuration = Math.round((completedDate - startDate) / (1000 * 60));
      }
    }

    Object.assign(record, updates);
    record._modifiedBy = req.user._id;
    await record.save();

    // Log significant status changes
    if (updates.status === 'completed' && record.status !== 'completed') {
      const asset = await Asset.findById(record.asset);
      if (asset) {
        await AssetAuditLog.logMaintenancePerformed(asset, record, req.user, {
          sessionId: req.session?.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        });

        // Update asset's last maintenance date
        asset.lastMaintenanceDate = record.completedDate || new Date();
        if (record.conditionAfter) {
          asset.condition = record.conditionAfter;
        }
        await asset.save();
      }
    }

    // Populate references
    await record.populate([
      { path: 'asset', select: 'name assetTag category location' },
      { path: 'assignedTo', select: 'name email' },
      { path: 'performedBy.user', select: 'name email' },
      { path: 'lastModifiedBy', select: 'name email' }
    ]);

    res.json(record);
  } catch (error) {
    console.error('Error updating maintenance record:', error);
    res.status(500).json({ message: 'Error updating maintenance record', error: error.message });
  }
};

/**
 * Delete maintenance record
 */
exports.deleteMaintenanceRecord = async (req, res) => {
  try {
    const { id } = req.params;
    
    const record = await AssetMaintenanceRecord.findById(id);
    if (!record) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }

    // Don't allow deletion of completed maintenance records
    if (record.status === 'completed') {
      return res.status(400).json({ 
        message: 'Cannot delete completed maintenance records for audit trail purposes' 
      });
    }

    await AssetMaintenanceRecord.findByIdAndDelete(id);

    res.json({ message: 'Maintenance record deleted successfully' });
  } catch (error) {
    console.error('Error deleting maintenance record:', error);
    res.status(500).json({ message: 'Error deleting maintenance record', error: error.message });
  }
};

/**
 * Get overdue maintenance records
 */
exports.getOverdueMaintenanceRecords = async (req, res) => {
  try {
    const { limit = 50, assignedTo } = req.query;

    const query = {
      status: 'scheduled',
      scheduledDate: { $lt: new Date() }
    };

    if (assignedTo) {
      query.assignedTo = assignedTo;
    }

    const overdueRecords = await AssetMaintenanceRecord.find(query)
      .populate('asset', 'name assetTag category location priority')
      .populate('assignedTo', 'name email')
      .sort({ scheduledDate: 1 })
      .limit(parseInt(limit));

    res.json({
      overdueRecords,
      count: overdueRecords.length
    });
  } catch (error) {
    console.error('Error getting overdue maintenance records:', error);
    res.status(500).json({ message: 'Error getting overdue maintenance records', error: error.message });
  }
};

/**
 * Get upcoming maintenance records
 */
exports.getUpcomingMaintenanceRecords = async (req, res) => {
  try {
    const { days = 30, limit = 50, assignedTo } = req.query;

    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + parseInt(days));

    const query = {
      status: 'scheduled',
      scheduledDate: { 
        $gte: new Date(),
        $lte: futureDate 
      }
    };

    if (assignedTo) {
      query.assignedTo = assignedTo;
    }

    const upcomingRecords = await AssetMaintenanceRecord.find(query)
      .populate('asset', 'name assetTag category location priority')
      .populate('assignedTo', 'name email')
      .sort({ scheduledDate: 1 })
      .limit(parseInt(limit));

    res.json({
      upcomingRecords,
      count: upcomingRecords.length,
      dateRange: {
        from: new Date(),
        to: futureDate
      }
    });
  } catch (error) {
    console.error('Error getting upcoming maintenance records:', error);
    res.status(500).json({ message: 'Error getting upcoming maintenance records', error: error.message });
  }
};

/**
 * Get maintenance statistics for an asset
 */
exports.getAssetMaintenanceStats = async (req, res) => {
  try {
    const { assetId } = req.params;
    const { startDate, endDate } = req.query;

    const asset = await Asset.findById(assetId);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const dateFilter = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const stats = await AssetMaintenanceRecord.aggregate([
      { 
        $match: { 
          asset: mongoose.Types.ObjectId(assetId),
          ...(Object.keys(dateFilter).length > 0 && { completedDate: dateFilter })
        }
      },
      {
        $group: {
          _id: null,
          totalRecords: { $sum: 1 },
          completedRecords: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          totalCost: { $sum: '$totalCost' },
          averageCost: { $avg: '$totalCost' },
          totalDowntime: { $sum: '$actualDuration' },
          averageDowntime: { $avg: '$actualDuration' },
          preventiveCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'preventive'] }, 1, 0] }
          },
          correctiveCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'corrective'] }, 1, 0] }
          },
          emergencyCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'emergency'] }, 1, 0] }
          }
        }
      }
    ]);

    const maintenanceHistory = await AssetMaintenanceRecord.find({ 
      asset: assetId,
      status: 'completed'
    })
    .sort({ completedDate: -1 })
    .limit(10)
    .select('title maintenanceType completedDate totalCost actualDuration');

    const nextScheduled = await AssetMaintenanceRecord.find({
      asset: assetId,
      status: 'scheduled',
      scheduledDate: { $gte: new Date() }
    })
    .sort({ scheduledDate: 1 })
    .limit(5)
    .select('title maintenanceType scheduledDate priority assignedTo')
    .populate('assignedTo', 'name email');

    res.json({
      asset: {
        id: asset._id,
        name: asset.name,
        assetTag: asset.assetTag
      },
      statistics: stats[0] || {
        totalRecords: 0,
        completedRecords: 0,
        totalCost: 0,
        averageCost: 0,
        totalDowntime: 0,
        averageDowntime: 0,
        preventiveCount: 0,
        correctiveCount: 0,
        emergencyCount: 0
      },
      recentHistory: maintenanceHistory,
      upcomingMaintenance: nextScheduled
    });
  } catch (error) {
    console.error('Error getting asset maintenance stats:', error);
    res.status(500).json({ message: 'Error getting asset maintenance stats', error: error.message });
  }
};

/**
 * Complete maintenance record
 */
exports.completeMaintenanceRecord = async (req, res) => {
  try {
    const { id } = req.params;
    const { workPerformed, conditionAfter, partsUsed, laborCost, partsCost, otherCosts } = req.body;

    const record = await AssetMaintenanceRecord.findById(id);
    if (!record) {
      return res.status(404).json({ message: 'Maintenance record not found' });
    }

    if (record.status === 'completed') {
      return res.status(400).json({ message: 'Maintenance record is already completed' });
    }

    // Mark as complete using instance method
    await record.markComplete(req.user._id, workPerformed, conditionAfter);

    // Update costs if provided
    if (laborCost !== undefined) record.laborCost = laborCost;
    if (partsCost !== undefined) record.partsCost = partsCost;
    if (otherCosts !== undefined) record.otherCosts = otherCosts;
    if (partsUsed && Array.isArray(partsUsed)) record.partsUsed = partsUsed;

    // Calculate actual duration
    record.calculateActualDuration();
    
    await record.save();

    // Update asset condition and last maintenance date
    const asset = await Asset.findById(record.asset);
    if (asset) {
      asset.lastMaintenanceDate = record.completedDate;
      if (conditionAfter) {
        asset.condition = conditionAfter;
      }
      asset._modifiedBy = req.user._id;
      await asset.save();

      // Log the completion
      await AssetAuditLog.logMaintenancePerformed(asset, record, req.user, {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
    }

    await record.populate([
      { path: 'asset', select: 'name assetTag category location' },
      { path: 'assignedTo', select: 'name email' },
      { path: 'performedBy.user', select: 'name email' }
    ]);

    res.json(record);
  } catch (error) {
    console.error('Error completing maintenance record:', error);
    res.status(500).json({ message: 'Error completing maintenance record', error: error.message });
  }
};

/**
 * Schedule recurring maintenance for an asset
 */
exports.scheduleRecurringMaintenance = async (req, res) => {
  try {
    const { assetId } = req.params;
    const { 
      maintenanceType,
      title,
      description,
      intervalDays,
      assignedTo,
      priority = 'medium',
      estimatedDuration,
      occurrences = 12 // Default to 1 year of monthly maintenance
    } = req.body;

    const asset = await Asset.findById(assetId);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const maintenanceRecords = [];
    const startDate = new Date();

    for (let i = 0; i < occurrences; i++) {
      const scheduledDate = new Date(startDate.getTime() + (i * intervalDays * 24 * 60 * 60 * 1000));
      
      const record = new AssetMaintenanceRecord({
        asset: assetId,
        maintenanceType,
        title: `${title} #${i + 1}`,
        description,
        scheduledDate,
        assignedTo,
        priority,
        estimatedDuration,
        createdBy: req.user._id
      });

      record._modifiedBy = req.user._id;
      await record.save();
      maintenanceRecords.push(record);
    }

    // Update asset maintenance schedule
    asset.maintenanceSchedule = 'custom';
    asset.nextMaintenanceDate = maintenanceRecords[0].scheduledDate;
    asset._modifiedBy = req.user._id;
    await asset.save();

    res.status(201).json({
      message: `Scheduled ${occurrences} maintenance records for ${asset.name}`,
      records: maintenanceRecords,
      nextMaintenance: maintenanceRecords[0].scheduledDate
    });
  } catch (error) {
    console.error('Error scheduling recurring maintenance:', error);
    res.status(500).json({ message: 'Error scheduling recurring maintenance', error: error.message });
  }
};

module.exports = exports;