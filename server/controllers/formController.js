const Form = require('../../models/Form');
const FormSubmission = require('../../models/FormSubmission');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Form Controller
 * Handles all form-related operations
 */
const formController = {
  /**
   * Duplicate an existing form
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  duplicateForm: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Find the original form
      const originalForm = await Form.findById(id);
      
      if (!originalForm) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if user has permission to view the original form
      const hasAccess = originalForm.hasAccessPermission(req.user);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this form'
        });
      }
      
      // Create a copy of the form data
      const formData = originalForm.toObject();
      
      // Remove _id and update metadata
      delete formData._id;
      delete formData.createdAt;
      delete formData.updatedAt;
      
      // Update title and slug
      formData.title = `Copy of ${formData.title}`;
      formData.slug = `${formData.slug}-copy-${Date.now().toString(36)}`;
      formData.status = 'draft'; // Always start as draft
      
      // Reset statistics
      formData.stats = {
        views: 0,
        submissions: 0,
        completionRate: 0,
        averageCompletionTime: 0
      };
      
      // Set creator and updater to current user
      formData.createdBy = req.user._id;
      formData.updatedBy = req.user._id;
      
      // Create new form
      const newForm = new Form(formData);
      await newForm.save();
      
      res.status(201).json({
        success: true,
        message: 'Form duplicated successfully',
        data: newForm
      });
    } catch (error) {
      console.error('Error duplicating form:', error);
      res.status(500).json({
        success: false,
        message: 'Error duplicating form',
        error: error.message
      });
    }
  },

  /**
   * Create a new form
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createForm: async (req, res) => {
    try {
      const { title, description, fieldGroups, styling, submission, permissions } = req.body;
      
      // Create slug from title if not provided
      let slug = req.body.slug;
      if (!slug && title) {
        slug = title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      }
      
      // Create new form
      const form = new Form({
        title,
        description,
        slug,
        fieldGroups: fieldGroups || [],
        styling: styling || {},
        submission: submission || {},
        permissions: permissions || {},
        createdBy: req.user._id,
        updatedBy: req.user._id
      });
      
      // Ensure each field has a unique fieldId
      form.fieldGroups.forEach(group => {
        group.fields.forEach(field => {
          if (!field.fieldId) {
            field.fieldId = `field_${uuidv4()}`;
          }
        });
      });
      
      // Save form
      await form.save();
      
      res.status(201).json({
        success: true,
        message: 'Form created successfully',
        data: form
      });
    } catch (error) {
      console.error('Error creating form:', error);
      res.status(500).json({
        success: false,
        message: 'Error creating form',
        error: error.message
      });
    }
  },
  
  /**
   * Get all forms (with filtering and pagination)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getForms: async (req, res) => {
    try {
      const { status, search, page = 1, limit = 10, sort = 'createdAt', order = 'desc' } = req.query;
      
      // Build query
      const query = {};
      
      // Filter by status
      if (status) {
        query.status = status;
      }
      
      // Search by title or description
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      // Filter by access permissions
      if (!req.user.roles.includes('admin')) {
        query.$or = [
          // Forms created by the user
          { createdBy: req.user._id },
          // Forms with public access
          { 'permissions.access': 'public' },
          // Forms with authenticated access
          { 'permissions.access': 'authenticated' },
          // Forms with specific roles access
          { 
            'permissions.access': 'specific_roles',
            'permissions.accessRoles': { $in: req.user.roles }
          },
          // Forms with specific users access
          {
            'permissions.access': 'specific_users',
            'permissions.accessUsers': req.user._id
          },
          // Forms with specific groups access
          {
            'permissions.access': 'specific_groups',
            'permissions.accessGroups': { $in: req.user.groups || [] }
          }
        ];
      }
      
      // Count total forms matching query
      const total = await Form.countDocuments(query);
      
      // Build sort object
      const sortObj = {};
      sortObj[sort] = order === 'desc' ? -1 : 1;
      
      // Get forms with pagination
      const forms = await Form.find(query)
        .sort(sortObj)
        .skip((page - 1) * limit)
        .limit(parseInt(limit))
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email');
      
      res.status(200).json({
        success: true,
        data: forms,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      console.error('Error getting forms:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting forms',
        error: error.message
      });
    }
  },
  
  /**
   * Get a single form by ID or slug
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getForm: async (req, res) => {
    try {
      const { id } = req.params;
      let form;
      
      // Check if id is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(id)) {
        form = await Form.findById(id)
          .populate('createdBy', 'name email')
          .populate('updatedBy', 'name email');
      } else {
        // Try to find by slug
        form = await Form.findOne({ slug: id })
          .populate('createdBy', 'name email')
          .populate('updatedBy', 'name email');
      }
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if user has access to this form
      const hasAccess = form.hasAccessPermission(req.user);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this form'
        });
      }
      
      // Increment view count
      form.stats.views += 1;
      await form.save();
      
      res.status(200).json({
        success: true,
        data: form
      });
    } catch (error) {
      console.error('Error getting form:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting form',
        error: error.message
      });
    }
  },
  
  /**
   * Update a form
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateForm: async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      // Find form
      const form = await Form.findById(id);
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if user has permission to manage this form
      const hasPermission = form.hasManagePermission(req.user);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this form'
        });
      }
      
      // Update form fields
      Object.keys(updateData).forEach(key => {
        // Don't update createdBy
        if (key !== 'createdBy' && key !== '_id') {
          form[key] = updateData[key];
        }
      });
      
      // Update updatedBy
      form.updatedBy = req.user._id;
      
      // Ensure each field has a unique fieldId
      form.fieldGroups.forEach(group => {
        group.fields.forEach(field => {
          if (!field.fieldId) {
            field.fieldId = `field_${uuidv4()}`;
          }
        });
      });
      
      // Save updated form
      await form.save();
      
      res.status(200).json({
        success: true,
        message: 'Form updated successfully',
        data: form
      });
    } catch (error) {
      console.error('Error updating form:', error);
      res.status(500).json({
        success: false,
        message: 'Error updating form',
        error: error.message
      });
    }
  },
  
  /**
   * Delete a form
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteForm: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Find form
      const form = await Form.findById(id);
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if user has permission to manage this form
      const hasPermission = form.hasManagePermission(req.user);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete this form'
        });
      }
      
      // Delete form
      await Form.findByIdAndDelete(id);
      
      res.status(200).json({
        success: true,
        message: 'Form deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting form:', error);
      res.status(500).json({
        success: false,
        message: 'Error deleting form',
        error: error.message
      });
    }
  },
  
  /**
   * Get form statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getFormStats: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Find form
      const form = await Form.findById(id);
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if user has permission to view submissions
      const hasPermission = form.hasViewSubmissionsPermission(req.user);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view form statistics'
        });
      }
      
      // Get submission count
      const submissionCount = await FormSubmission.countDocuments({ form: id });
      
      // Get submission status counts
      const statusCounts = await FormSubmission.aggregate([
        { $match: { form: mongoose.Types.ObjectId(id) } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);
      
      // Format status counts
      const statusCountsObj = {};
      statusCounts.forEach(status => {
        statusCountsObj[status._id] = status.count;
      });
      
      // Get average completion time
      const avgCompletionTime = await FormSubmission.aggregate([
        { $match: { form: mongoose.Types.ObjectId(id), completionTime: { $exists: true, $ne: null } } },
        { $group: { _id: null, avg: { $avg: '$completionTime' } } }
      ]);
      
      // Get submission trend (submissions per day for last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const submissionTrend = await FormSubmission.aggregate([
        { 
          $match: { 
            form: mongoose.Types.ObjectId(id),
            createdAt: { $gte: thirtyDaysAgo }
          }
        },
        {
          $group: {
            _id: { 
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);
      
      // Update form stats
      form.stats.submissions = submissionCount;
      form.stats.completionRate = submissionCount > 0 ? 
        (statusCountsObj.submitted || 0) / form.stats.views * 100 : 0;
      form.stats.averageCompletionTime = avgCompletionTime.length > 0 ? 
        avgCompletionTime[0].avg : 0;
      
      await form.save();
      
      res.status(200).json({
        success: true,
        data: {
          views: form.stats.views,
          submissions: submissionCount,
          completionRate: form.stats.completionRate,
          averageCompletionTime: form.stats.averageCompletionTime,
          statusCounts: statusCountsObj,
          submissionTrend
        }
      });
    } catch (error) {
      console.error('Error getting form statistics:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting form statistics',
        error: error.message
      });
    }
  },
  
  /**
   * Get public form (no authentication required)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getPublicForm: async (req, res) => {
    try {
      const { slug } = req.params;
      
      // Find form by slug
      const form = await Form.findOne({ 
        slug,
        status: 'published',
        'permissions.access': 'public'
      });
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found or not publicly accessible'
        });
      }
      
      // Increment view count
      form.stats.views += 1;
      await form.save();
      
      // Return sanitized form data (only what's needed for rendering)
      const sanitizedForm = {
        _id: form._id,
        title: form.title,
        description: form.description,
        slug: form.slug,
        formType: form.formType,
        styling: form.styling,
        fieldGroups: form.fieldGroups,
        submission: {
          successMessage: form.submission.successMessage,
          redirectUrl: form.submission.redirectUrl
        }
      };
      
      res.status(200).json({
        success: true,
        data: sanitizedForm
      });
    } catch (error) {
      console.error('Error getting public form:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting public form',
        error: error.message
      });
    }
  }
};

module.exports = formController;