const Asset = require('../../models/Asset');
const AssetCategory = require('../../models/AssetCategory');
const AssetLocation = require('../../models/AssetLocation');
const AssetMaintenanceRecord = require('../../models/AssetMaintenanceRecord');
const AssetAuditLog = require('../../models/AssetAuditLog');
const mongoose = require('mongoose');

/**
 * Asset Report Controller
 * Generates comprehensive reports and analytics for asset management
 */

/**
 * Get asset overview report
 */
exports.getAssetOverview = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const dateFilter = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const matchStage = { isDeleted: false };
    if (Object.keys(dateFilter).length > 0) {
      matchStage.createdAt = dateFilter;
    }

    const overview = await Asset.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalAssets: { $sum: 1 },
          totalValue: { $sum: { $ifNull: ['$currentValue', '$purchasePrice'] } },
          averageValue: { $avg: { $ifNull: ['$currentValue', '$purchasePrice'] } },
          totalPurchaseValue: { $sum: '$purchasePrice' },
          totalDepreciation: { 
            $sum: { 
              $subtract: [
                '$purchasePrice', 
                { $ifNull: ['$currentValue', '$purchasePrice'] }
              ]
            }
          },
          assetsWithWarranty: {
            $sum: {
              $cond: [
                { $and: [
                  { $ne: ['$warrantyExpiration', null] },
                  { $gt: ['$warrantyExpiration', new Date()] }
                ]},
                1,
                0
              ]
            }
          },
          assetsNeedingMaintenance: {
            $sum: {
              $cond: [
                { $or: [
                  { $eq: ['$condition', 'needs_repair'] },
                  { $eq: ['$status', 'in_maintenance'] }
                ]},
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    // Asset distribution by status
    const statusDistribution = await Asset.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalValue: { $sum: { $ifNull: ['$currentValue', '$purchasePrice'] } }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Asset distribution by condition
    const conditionDistribution = await Asset.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$condition',
          count: { $sum: 1 },
          totalValue: { $sum: { $ifNull: ['$currentValue', '$purchasePrice'] } }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Monthly asset acquisition trend
    const acquisitionTrend = await Asset.aggregate([
      { $match: { isDeleted: false, createdAt: { $gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) } } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 },
          value: { $sum: { $ifNull: ['$purchasePrice', 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: 1
            }
          },
          count: 1,
          value: 1
        }
      }
    ]);

    res.json({
      overview: overview[0] || {
        totalAssets: 0,
        totalValue: 0,
        averageValue: 0,
        totalPurchaseValue: 0,
        totalDepreciation: 0,
        assetsWithWarranty: 0,
        assetsNeedingMaintenance: 0
      },
      statusDistribution,
      conditionDistribution,
      acquisitionTrend
    });
  } catch (error) {
    console.error('Error generating asset overview report:', error);
    res.status(500).json({ message: 'Error generating asset overview report', error: error.message });
  }
};

/**
 * Get financial report
 */
exports.getFinancialReport = async (req, res) => {
  try {
    const { startDate, endDate, groupBy = 'category' } = req.query;
    
    const dateFilter = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const matchStage = { isDeleted: false };
    if (Object.keys(dateFilter).length > 0) {
      matchStage.purchaseDate = dateFilter;
    }

    // Financial summary by group
    let groupByField;
    let lookupStage;
    
    switch (groupBy) {
      case 'category':
        groupByField = '$category';
        lookupStage = {
          $lookup: {
            from: 'assetcategories',
            localField: '_id',
            foreignField: '_id',
            as: 'categoryInfo'
          }
        };
        break;
      case 'location':
        groupByField = '$location';
        lookupStage = {
          $lookup: {
            from: 'assetlocations',
            localField: '_id',
            foreignField: '_id',
            as: 'locationInfo'
          }
        };
        break;
      case 'status':
        groupByField = '$status';
        lookupStage = null;
        break;
      default:
        groupByField = '$category';
        lookupStage = {
          $lookup: {
            from: 'assetcategories',
            localField: '_id',
            foreignField: '_id',
            as: 'categoryInfo'
          }
        };
    }

    const pipeline = [
      { $match: matchStage },
      {
        $group: {
          _id: groupByField,
          totalAssets: { $sum: 1 },
          totalPurchaseValue: { $sum: { $ifNull: ['$purchasePrice', 0] } },
          totalCurrentValue: { $sum: { $ifNull: ['$currentValue', '$purchasePrice'] } },
          averagePurchasePrice: { $avg: { $ifNull: ['$purchasePrice', 0] } },
          averageCurrentValue: { $avg: { $ifNull: ['$currentValue', '$purchasePrice'] } },
          totalDepreciation: {
            $sum: {
              $subtract: [
                { $ifNull: ['$purchasePrice', 0] },
                { $ifNull: ['$currentValue', '$purchasePrice'] }
              ]
            }
          }
        }
      }
    ];

    if (lookupStage) {
      pipeline.push(lookupStage);
    }

    pipeline.push({ $sort: { totalCurrentValue: -1 } });

    const financialData = await Asset.aggregate(pipeline);

    // Depreciation analysis
    const depreciationAnalysis = await Asset.aggregate([
      { $match: { ...matchStage, purchasePrice: { $gt: 0 } } },
      {
        $project: {
          age: {
            $divide: [
              { $subtract: [new Date(), '$purchaseDate'] },
              1000 * 60 * 60 * 24 * 365 // Convert to years
            ]
          },
          purchasePrice: 1,
          currentValue: { $ifNull: ['$currentValue', '$purchasePrice'] },
          depreciation: {
            $subtract: [
              '$purchasePrice',
              { $ifNull: ['$currentValue', '$purchasePrice'] }
            ]
          },
          depreciationRate: {
            $multiply: [
              {
                $divide: [
                  { $subtract: [
                    '$purchasePrice',
                    { $ifNull: ['$currentValue', '$purchasePrice'] }
                  ]},
                  '$purchasePrice'
                ]
              },
              100
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          avgDepreciationRate: { $avg: '$depreciationRate' },
          totalDepreciation: { $sum: '$depreciation' },
          assetsCount: { $sum: 1 }
        }
      }
    ]);

    res.json({
      financialByGroup: financialData,
      depreciationAnalysis: depreciationAnalysis[0] || {
        avgDepreciationRate: 0,
        totalDepreciation: 0,
        assetsCount: 0
      },
      groupBy
    });
  } catch (error) {
    console.error('Error generating financial report:', error);
    res.status(500).json({ message: 'Error generating financial report', error: error.message });
  }
};

/**
 * Get maintenance report
 */
exports.getMaintenanceReport = async (req, res) => {
  try {
    const { startDate, endDate, includeScheduled = true } = req.query;
    
    const dateFilter = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const matchStage = {};
    if (Object.keys(dateFilter).length > 0) {
      matchStage.completedDate = dateFilter;
    }

    // Maintenance summary
    const maintenanceSummary = await AssetMaintenanceRecord.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: null,
          totalRecords: { $sum: 1 },
          completedRecords: {
            $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          },
          totalCost: { $sum: { $ifNull: ['$totalCost', 0] } },
          averageCost: { $avg: { $ifNull: ['$totalCost', 0] } },
          totalDowntime: { $sum: { $ifNull: ['$actualDuration', 0] } },
          averageDowntime: { $avg: { $ifNull: ['$actualDuration', 0] } },
          preventiveCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'preventive'] }, 1, 0] }
          },
          correctiveCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'corrective'] }, 1, 0] }
          },
          emergencyCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'emergency'] }, 1, 0] }
          }
        }
      }
    ]);

    // Maintenance by asset category
    const maintenanceByCategory = await AssetMaintenanceRecord.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'assets',
          localField: 'asset',
          foreignField: '_id',
          as: 'assetInfo'
        }
      },
      { $unwind: '$assetInfo' },
      {
        $lookup: {
          from: 'assetcategories',
          localField: 'assetInfo.category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      },
      { $unwind: '$categoryInfo' },
      {
        $group: {
          _id: '$categoryInfo._id',
          categoryName: { $first: '$categoryInfo.name' },
          recordCount: { $sum: 1 },
          totalCost: { $sum: { $ifNull: ['$totalCost', 0] } },
          averageCost: { $avg: { $ifNull: ['$totalCost', 0] } },
          totalDowntime: { $sum: { $ifNull: ['$actualDuration', 0] } }
        }
      },
      { $sort: { totalCost: -1 } }
    ]);

    // Monthly maintenance trend
    const maintenanceTrend = await AssetMaintenanceRecord.aggregate([
      { 
        $match: { 
          status: 'completed',
          completedDate: { $gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$completedDate' },
            month: { $month: '$completedDate' }
          },
          count: { $sum: 1 },
          totalCost: { $sum: { $ifNull: ['$totalCost', 0] } },
          preventiveCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'preventive'] }, 1, 0] }
          },
          correctiveCount: {
            $sum: { $cond: [{ $eq: ['$maintenanceType', 'corrective'] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: 1
            }
          },
          count: 1,
          totalCost: 1,
          preventiveCount: 1,
          correctiveCount: 1
        }
      }
    ]);

    // Overdue and upcoming maintenance
    const currentDate = new Date();
    const futureDate = new Date(currentDate.getTime() + 30 * 24 * 60 * 60 * 1000);

    const [overdueMaintenance, upcomingMaintenance] = await Promise.all([
      AssetMaintenanceRecord.find({
        status: 'scheduled',
        scheduledDate: { $lt: currentDate }
      })
        .populate('asset', 'name assetTag')
        .populate('assignedTo', 'name email')
        .sort({ scheduledDate: 1 })
        .limit(10),
      
      AssetMaintenanceRecord.find({
        status: 'scheduled',
        scheduledDate: { $gte: currentDate, $lte: futureDate }
      })
        .populate('asset', 'name assetTag')
        .populate('assignedTo', 'name email')
        .sort({ scheduledDate: 1 })
        .limit(10)
    ]);

    res.json({
      summary: maintenanceSummary[0] || {
        totalRecords: 0,
        completedRecords: 0,
        totalCost: 0,
        averageCost: 0,
        totalDowntime: 0,
        averageDowntime: 0,
        preventiveCount: 0,
        correctiveCount: 0,
        emergencyCount: 0
      },
      byCategory: maintenanceByCategory,
      trend: maintenanceTrend,
      overdue: overdueMaintenance,
      upcoming: upcomingMaintenance
    });
  } catch (error) {
    console.error('Error generating maintenance report:', error);
    res.status(500).json({ message: 'Error generating maintenance report', error: error.message });
  }
};

/**
 * Get utilization report
 */
exports.getUtilizationReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Asset utilization by status
    const utilizationByStatus = await Asset.aggregate([
      { $match: { isDeleted: false } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          percentage: { $multiply: [{ $divide: ['$count', '$totalAssets'] }, 100] }
        }
      },
      {
        $lookup: {
          from: 'assets',
          pipeline: [{ $match: { isDeleted: false } }, { $count: 'total' }],
          as: 'totalCount'
        }
      },
      {
        $addFields: {
          totalAssets: { $arrayElemAt: ['$totalCount.total', 0] }
        }
      },
      {
        $project: {
          status: '$_id',
          count: 1,
          percentage: { 
            $multiply: [
              { $divide: ['$count', '$totalAssets'] }, 
              100
            ]
          }
        }
      }
    ]);

    // Asset assignment rate
    const assignmentStats = await Asset.aggregate([
      { $match: { isDeleted: false } },
      {
        $group: {
          _id: null,
          totalAssets: { $sum: 1 },
          assignedAssets: {
            $sum: { $cond: [{ $ne: ['$assignedTo', null] }, 1, 0] }
          },
          unassignedAssets: {
            $sum: { $cond: [{ $eq: ['$assignedTo', null] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          totalAssets: 1,
          assignedAssets: 1,
          unassignedAssets: 1,
          assignmentRate: {
            $multiply: [
              { $divide: ['$assignedAssets', '$totalAssets'] },
              100
            ]
          }
        }
      }
    ]);

    // Location utilization
    const locationUtilization = await AssetLocation.aggregate([
      {
        $lookup: {
          from: 'assets',
          localField: '_id',
          foreignField: 'location',
          as: 'assets'
        }
      },
      {
        $project: {
          name: 1,
          locationType: 1,
          maxAssets: 1,
          currentAssets: { $size: '$assets' },
          utilizationRate: {
            $cond: [
              { $gt: ['$maxAssets', 0] },
              { $multiply: [{ $divide: [{ $size: '$assets' }, '$maxAssets'] }, 100] },
              null
            ]
          }
        }
      },
      { $match: { currentAssets: { $gt: 0 } } },
      { $sort: { currentAssets: -1 } },
      { $limit: 20 }
    ]);

    // Age distribution
    const ageDistribution = await Asset.aggregate([
      { 
        $match: { 
          isDeleted: false, 
          purchaseDate: { $ne: null } 
        } 
      },
      {
        $project: {
          ageInYears: {
            $divide: [
              { $subtract: [new Date(), '$purchaseDate'] },
              1000 * 60 * 60 * 24 * 365
            ]
          }
        }
      },
      {
        $bucket: {
          groupBy: '$ageInYears',
          boundaries: [0, 1, 2, 3, 5, 10, 100],
          default: 'Unknown',
          output: {
            count: { $sum: 1 },
            ageRange: {
              $switch: {
                branches: [
                  { case: { $lt: ['$ageInYears', 1] }, then: '< 1 year' },
                  { case: { $lt: ['$ageInYears', 2] }, then: '1-2 years' },
                  { case: { $lt: ['$ageInYears', 3] }, then: '2-3 years' },
                  { case: { $lt: ['$ageInYears', 5] }, then: '3-5 years' },
                  { case: { $lt: ['$ageInYears', 10] }, then: '5-10 years' }
                ],
                default: '> 10 years'
              }
            }
          }
        }
      }
    ]);

    res.json({
      utilizationByStatus,
      assignmentStats: assignmentStats[0] || {
        totalAssets: 0,
        assignedAssets: 0,
        unassignedAssets: 0,
        assignmentRate: 0
      },
      locationUtilization,
      ageDistribution
    });
  } catch (error) {
    console.error('Error generating utilization report:', error);
    res.status(500).json({ message: 'Error generating utilization report', error: error.message });
  }
};

/**
 * Get audit report
 */
exports.getAuditReport = async (req, res) => {
  try {
    const { startDate, endDate, action, userId, limit = 100 } = req.query;
    
    const matchStage = { status: 'active' };
    
    if (startDate || endDate) {
      matchStage.createdAt = {};
      if (startDate) matchStage.createdAt.$gte = new Date(startDate);
      if (endDate) matchStage.createdAt.$lte = new Date(endDate);
    }
    
    if (action) matchStage.action = action;
    if (userId) matchStage.performedBy = mongoose.Types.ObjectId(userId);

    // Recent audit activities
    const auditActivities = await AssetAuditLog.find(matchStage)
      .populate('asset', 'name assetTag')
      .populate('performedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

    // Activity summary by action
    const activitySummary = await AssetAuditLog.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 },
          lastActivity: { $max: '$createdAt' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Activity by user
    const activityByUser = await AssetAuditLog.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$performedBy',
          performedByName: { $first: '$performedByName' },
          activityCount: { $sum: 1 },
          lastActivity: { $max: '$createdAt' }
        }
      },
      { $sort: { activityCount: -1 } },
      { $limit: 10 }
    ]);

    // Daily activity trend
    const activityTrend = await AssetAuditLog.aggregate([
      { 
        $match: { 
          ...matchStage,
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.json({
      activities: auditActivities,
      summary: activitySummary,
      byUser: activityByUser,
      trend: activityTrend
    });
  } catch (error) {
    console.error('Error generating audit report:', error);
    res.status(500).json({ message: 'Error generating audit report', error: error.message });
  }
};

/**
 * Export report data as CSV
 */
exports.exportReport = async (req, res) => {
  try {
    const { reportType, format = 'csv', ...params } = req.query;
    
    let data;
    let filename;
    
    switch (reportType) {
      case 'overview':
        data = await this.getAssetOverview({ query: params }, { json: () => data });
        filename = `asset-overview-${new Date().toISOString().split('T')[0]}`;
        break;
      case 'financial':
        data = await this.getFinancialReport({ query: params }, { json: () => data });
        filename = `financial-report-${new Date().toISOString().split('T')[0]}`;
        break;
      case 'maintenance':
        data = await this.getMaintenanceReport({ query: params }, { json: () => data });
        filename = `maintenance-report-${new Date().toISOString().split('T')[0]}`;
        break;
      case 'utilization':
        data = await this.getUtilizationReport({ query: params }, { json: () => data });
        filename = `utilization-report-${new Date().toISOString().split('T')[0]}`;
        break;
      case 'audit':
        data = await this.getAuditReport({ query: params }, { json: () => data });
        filename = `audit-report-${new Date().toISOString().split('T')[0]}`;
        break;
      default:
        return res.status(400).json({ message: 'Invalid report type' });
    }

    if (format === 'csv') {
      // Convert to CSV (basic implementation)
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      
      // For now, return JSON - in production, implement proper CSV conversion
      res.json(data);
    } else {
      res.json(data);
    }
  } catch (error) {
    console.error('Error exporting report:', error);
    res.status(500).json({ message: 'Error exporting report', error: error.message });
  }
};

/**
 * Get custom report based on user-defined criteria
 */
exports.getCustomReport = async (req, res) => {
  try {
    const {
      fields = [],
      filters = {},
      groupBy,
      sortBy,
      sortOrder = 'desc',
      limit = 1000
    } = req.body;

    // Build aggregation pipeline
    const pipeline = [];
    
    // Match stage for filters
    const matchStage = { isDeleted: false };
    Object.keys(filters).forEach(key => {
      if (filters[key] !== null && filters[key] !== undefined) {
        matchStage[key] = filters[key];
      }
    });
    
    pipeline.push({ $match: matchStage });

    // Lookup stages for referenced fields
    if (fields.includes('category') || groupBy === 'category') {
      pipeline.push({
        $lookup: {
          from: 'assetcategories',
          localField: 'category',
          foreignField: '_id',
          as: 'categoryInfo'
        }
      });
    }

    if (fields.includes('location') || groupBy === 'location') {
      pipeline.push({
        $lookup: {
          from: 'assetlocations',
          localField: 'location',
          foreignField: '_id',
          as: 'locationInfo'
        }
      });
    }

    if (fields.includes('assignedTo') || groupBy === 'assignedTo') {
      pipeline.push({
        $lookup: {
          from: 'users',
          localField: 'assignedTo',
          foreignField: '_id',
          as: 'userInfo'
        }
      });
    }

    // Group stage if groupBy is specified
    if (groupBy) {
      const groupStage = {
        _id: `$${groupBy}`,
        count: { $sum: 1 },
        totalValue: { $sum: { $ifNull: ['$currentValue', '$purchasePrice'] } },
        averageValue: { $avg: { $ifNull: ['$currentValue', '$purchasePrice'] } }
      };

      pipeline.push({ $group: groupStage });
    }

    // Project stage for field selection
    if (fields.length > 0 && !groupBy) {
      const projectStage = {};
      fields.forEach(field => {
        projectStage[field] = 1;
      });
      pipeline.push({ $project: projectStage });
    }

    // Sort stage
    if (sortBy) {
      const sortStage = {};
      sortStage[sortBy] = sortOrder === 'asc' ? 1 : -1;
      pipeline.push({ $sort: sortStage });
    }

    // Limit stage
    pipeline.push({ $limit: parseInt(limit) });

    const results = await Asset.aggregate(pipeline);

    res.json({
      results,
      count: results.length,
      criteria: {
        fields,
        filters,
        groupBy,
        sortBy,
        sortOrder,
        limit
      }
    });
  } catch (error) {
    console.error('Error generating custom report:', error);
    res.status(500).json({ message: 'Error generating custom report', error: error.message });
  }
};