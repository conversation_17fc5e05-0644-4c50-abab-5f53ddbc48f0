const Ticket = require('../../models/Ticket');
const TicketComment = require('../../models/TicketComment');
const TicketCategory = require('../../models/TicketCategory');
const User = require('../../models/User');
const crypto = require('crypto');
const nodemailer = require('nodemailer');

/**
 * Ticket Email Controller
 * Handles email integration for tickets
 */
const ticketEmailController = {
  /**
   * Process incoming email webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with processing result
   */
  processIncomingEmail: async (req, res) => {
    try {
      console.log('Processing incoming email webhook:', req.body);
      
      const emailData = ticketEmailController.parseEmailWebhook(req.body);
      
      if (!emailData) {
        return res.status(400).json({ message: 'Invalid email webhook data' });
      }
      
      // Check if this is a reply to an existing ticket
      const existingTicket = await ticketEmailController.findTicketByEmail(emailData);
      
      if (existingTicket) {
        // This is a reply - add as comment
        await ticketEmailController.addEmailReply(existingTicket, emailData);
        res.json({ 
          message: 'Email reply processed successfully', 
          ticketId: existingTicket._id,
          action: 'reply_added'
        });
      } else {
        // This is a new ticket
        const ticket = await ticketEmailController.createTicketFromEmail(emailData);
        res.json({ 
          message: 'New ticket created from email', 
          ticketId: ticket._id,
          ticketNumber: ticket.ticketNumber,
          action: 'ticket_created'
        });
      }
    } catch (error) {
      console.error('Error processing incoming email:', error);
      res.status(500).json({ 
        message: 'Error processing email', 
        error: error.message 
      });
    }
  },

  /**
   * Parse email webhook data from different providers
   * @param {Object} webhookData - Raw webhook data
   * @returns {Object|null} Parsed email data
   */
  parseEmailWebhook: (webhookData) => {
    // Support for common email webhook formats (SendGrid, Mailgun, etc.)
    try {
      let emailData = {};
      
      // SendGrid format
      if (webhookData.to && webhookData.from && webhookData.subject) {
        emailData = {
          to: webhookData.to,
          from: webhookData.from,
          subject: webhookData.subject,
          html: webhookData.html || '',
          text: webhookData.text || '',
          messageId: webhookData['message-id'] || webhookData.messageId,
          inReplyTo: webhookData['in-reply-to'] || webhookData.inReplyTo,
          references: webhookData.references || '',
          headers: webhookData.headers || {},
          attachments: webhookData.attachments || []
        };
      }
      // Mailgun format
      else if (webhookData.recipient && webhookData.sender && webhookData.subject) {
        emailData = {
          to: webhookData.recipient,
          from: webhookData.sender,
          subject: webhookData.subject,
          html: webhookData['body-html'] || '',
          text: webhookData['body-plain'] || '',
          messageId: webhookData['Message-Id'],
          inReplyTo: webhookData['In-Reply-To'],
          references: webhookData['References'] || '',
          headers: webhookData,
          attachments: webhookData.attachments || []
        };
      }
      // Generic format
      else if (webhookData.email) {
        emailData = webhookData.email;
      } else {
        return null;
      }
      
      // Clean and validate required fields
      if (!emailData.to || !emailData.from || !emailData.subject) {
        return null;
      }
      
      // Extract email addresses from potentially formatted strings
      emailData.to = ticketEmailController.extractEmail(emailData.to);
      emailData.from = ticketEmailController.extractEmail(emailData.from);
      
      // Clean subject
      emailData.subject = emailData.subject.trim();
      
      // Use text content if HTML not available
      if (!emailData.text && emailData.html) {
        emailData.text = ticketEmailController.htmlToText(emailData.html);
      }
      
      return emailData;
    } catch (error) {
      console.error('Error parsing email webhook:', error);
      return null;
    }
  },

  /**
   * Find existing ticket by email data
   * @param {Object} emailData - Parsed email data
   * @returns {Object|null} Existing ticket if found
   */
  findTicketByEmail: async (emailData) => {
    try {
      // Look for ticket number in subject line
      const ticketNumberMatch = emailData.subject.match(/CSF-(\d{6})/);
      if (ticketNumberMatch) {
        const ticketNumber = `CSF-${ticketNumberMatch[1]}`;
        const ticket = await Ticket.findOne({ ticketNumber });
        if (ticket) return ticket;
      }
      
      // Look for existing email thread
      if (emailData.inReplyTo || emailData.references) {
        const searchIds = [];
        if (emailData.inReplyTo) searchIds.push(emailData.inReplyTo);
        if (emailData.references) {
          searchIds.push(...emailData.references.split(/\s+/));
        }
        
        for (const messageId of searchIds) {
          const ticket = await Ticket.findOne({
            $or: [
              { emailThreadId: messageId },
              { lastEmailMessageId: messageId }
            ]
          });
          if (ticket) return ticket;
          
          // Also check comments for message IDs
          const comment = await TicketComment.findOne({ emailMessageId: messageId });
          if (comment) {
            return await Ticket.findById(comment.ticket);
          }
        }
      }
      
      // Look for recent tickets from the same sender with similar subject
      const baseSubject = emailData.subject
        .replace(/^(re:|fwd?:)\s*/i, '')
        .trim()
        .toLowerCase();
      
      const recentTicket = await Ticket.findOne({
        'sourceMetadata.senderEmail': emailData.from,
        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
      }).sort({ createdAt: -1 });
      
      if (recentTicket && 
          recentTicket.subject.toLowerCase().includes(baseSubject) ||
          baseSubject.includes(recentTicket.subject.toLowerCase())) {
        return recentTicket;
      }
      
      return null;
    } catch (error) {
      console.error('Error finding ticket by email:', error);
      return null;
    }
  },

  /**
   * Create new ticket from email
   * @param {Object} emailData - Parsed email data
   * @returns {Object} Created ticket
   */
  createTicketFromEmail: async (emailData) => {
    try {
      // Find or create user from sender email
      let requester = await User.findOne({ email: emailData.from });
      if (!requester) {
        // Create a basic user record for external email
        const senderName = ticketEmailController.extractName(emailData.from) || emailData.from;
        requester = new User({
          name: senderName,
          email: emailData.from,
          authType: 'local',
          roles: ['external_user'],
          isActive: false // External users are inactive by default
        });
        await requester.save();
      }
      
      // Clean up subject (remove RE:, FWD: etc.)
      const cleanSubject = emailData.subject.replace(/^(re:|fwd?:)\s*/i, '').trim();
      
      // Determine content
      const description = emailData.text || emailData.html || 'No content';
      
      // Create ticket
      const ticket = new Ticket({
        subject: cleanSubject,
        description: description,
        requester: requester._id,
        source: 'email',
        sourceMetadata: new Map({
          senderEmail: emailData.from,
          recipientEmail: emailData.to,
          messageId: emailData.messageId,
          originalSubject: emailData.subject
        }),
        emailThreadId: emailData.messageId,
        lastEmailMessageId: emailData.messageId,
        incomingEmailAddress: emailData.to
      });
      
      // Auto-assign category, tags, etc.
      const autoCategory = await ticketEmailController.autoAssignCategory(cleanSubject, description, emailData.from);
      if (autoCategory) {
        ticket.category = autoCategory.name;
        if (autoCategory.defaultAssignee) {
          ticket.assignedTo = autoCategory.defaultAssignee;
        }
        if (autoCategory.defaultGroup) {
          ticket.assignedGroup = autoCategory.defaultGroup;
        }
      }
      
      await ticket.save();
      
      // Create initial comment with email content
      const comment = new TicketComment({
        ticket: ticket._id,
        content: description,
        contentType: emailData.html ? 'html' : 'text',
        author: requester._id,
        authorEmail: emailData.from,
        authorName: requester.name,
        type: 'email_inbound',
        emailMessageId: emailData.messageId,
        emailThreadId: emailData.messageId,
        emailHeaders: new Map(Object.entries(emailData.headers || {})),
        attachments: emailData.attachments || [],
        isFirstResponse: false
      });
      
      await comment.save();
      
      // Send auto-response email
      await ticketEmailController.sendAutoResponse(ticket, emailData);
      
      return ticket;
    } catch (error) {
      console.error('Error creating ticket from email:', error);
      throw error;
    }
  },

  /**
   * Add email reply to existing ticket
   * @param {Object} ticket - Existing ticket
   * @param {Object} emailData - Parsed email data
   * @returns {Object} Created comment
   */
  addEmailReply: async (ticket, emailData) => {
    try {
      // Find the user who sent the email
      let author = await User.findOne({ email: emailData.from });
      if (!author) {
        // Create external user if not found
        const senderName = ticketEmailController.extractName(emailData.from) || emailData.from;
        author = new User({
          name: senderName,
          email: emailData.from,
          authType: 'local',
          roles: ['external_user'],
          isActive: false
        });
        await author.save();
      }
      
      // Determine if this is from the requester or assignee
      const isFromRequester = String(ticket.requester) === String(author._id);
      const isFromAssignee = ticket.assignedTo && String(ticket.assignedTo) === String(author._id);
      const isInternal = author.roles.some(role => ['admin', 'ticket_manager', 'user'].includes(role));
      
      const content = emailData.text || emailData.html || 'No content';
      
      // Create comment
      const comment = new TicketComment({
        ticket: ticket._id,
        content: content,
        contentType: emailData.html ? 'html' : 'text',
        author: author._id,
        authorEmail: emailData.from,
        authorName: author.name,
        type: 'email_inbound',
        isPublic: !isInternal, // External emails are public, internal are based on context
        emailMessageId: emailData.messageId,
        emailThreadId: ticket.emailThreadId,
        emailInReplyTo: emailData.inReplyTo,
        emailHeaders: new Map(Object.entries(emailData.headers || {})),
        attachments: emailData.attachments || []
      });
      
      await comment.save();
      
      // Update ticket
      ticket.lastEmailMessageId = emailData.messageId;
      
      // If ticket was closed/resolved and requester replies, reopen it
      if (isFromRequester && ['resolved', 'closed'].includes(ticket.status)) {
        ticket.status = 'open';
      }
      
      // If this is from assignee and ticket is open, mark as pending
      if (isFromAssignee && ticket.status === 'open') {
        ticket.status = 'pending';
      }
      
      await ticket.save();
      
      // Send notification emails to relevant parties
      await ticketEmailController.sendReplyNotifications(ticket, comment, emailData);
      
      return comment;
    } catch (error) {
      console.error('Error adding email reply:', error);
      throw error;
    }
  },

  /**
   * Send auto-response email for new ticket
   * @param {Object} ticket - Created ticket
   * @param {Object} emailData - Original email data
   */
  sendAutoResponse: async (ticket, emailData) => {
    try {
      if (!process.env.SMTP_HOST || !process.env.TICKET_EMAIL_FROM) {
        console.log('SMTP not configured, skipping auto-response');
        return;
      }
      
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });
      
      const autoResponseSubject = `Ticket Created: ${ticket.ticketNumber} - ${ticket.subject}`;
      const autoResponseBody = `
        <p>Thank you for contacting us. Your support request has been received and assigned ticket number <strong>${ticket.ticketNumber}</strong>.</p>
        
        <p><strong>Ticket Details:</strong></p>
        <ul>
          <li>Ticket Number: ${ticket.ticketNumber}</li>
          <li>Subject: ${ticket.subject}</li>
          <li>Priority: ${ticket.priority}</li>
          <li>Status: ${ticket.status}</li>
        </ul>
        
        <p>We will review your request and respond as soon as possible. Please reference the ticket number ${ticket.ticketNumber} in any future correspondence regarding this issue.</p>
        
        <p>Thank you,<br>CSF Portal Support Team</p>
      `;
      
      await transporter.sendMail({
        from: process.env.TICKET_EMAIL_FROM,
        to: emailData.from,
        subject: autoResponseSubject,
        html: autoResponseBody,
        headers: {
          'References': emailData.messageId,
          'In-Reply-To': emailData.messageId
        }
      });
      
      console.log(`Auto-response sent for ticket ${ticket.ticketNumber}`);
    } catch (error) {
      console.error('Error sending auto-response:', error);
    }
  },

  /**
   * Send notification emails for ticket replies
   * @param {Object} ticket - Ticket object
   * @param {Object} comment - Comment object
   * @param {Object} emailData - Original email data
   */
  sendReplyNotifications: async (ticket, comment, emailData) => {
    try {
      if (!process.env.SMTP_HOST || !process.env.TICKET_EMAIL_FROM) {
        console.log('SMTP not configured, skipping reply notifications');
        return;
      }
      
      // Determine who should receive notifications
      const recipients = new Set();
      
      // Always notify the requester (unless they sent the reply)
      if (String(ticket.requester) !== String(comment.author)) {
        const requester = await User.findById(ticket.requester);
        if (requester && requester.email) {
          recipients.add(requester.email);
        }
      }
      
      // Notify assignee (unless they sent the reply)
      if (ticket.assignedTo && String(ticket.assignedTo) !== String(comment.author)) {
        const assignee = await User.findById(ticket.assignedTo);
        if (assignee && assignee.email) {
          recipients.add(assignee.email);
        }
      }
      
      // Notify followers (excluding the author)
      if (ticket.followers && ticket.followers.length > 0) {
        const followers = await User.find({ 
          _id: { $in: ticket.followers },
          _id: { $ne: comment.author }
        });
        followers.forEach(follower => {
          if (follower.email) {
            recipients.add(follower.email);
          }
        });
      }
      
      // Send notifications
      if (recipients.size > 0) {
        const transporter = nodemailer.createTransport({
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT || 587,
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
          }
        });
        
        const notificationSubject = `Ticket Updated: ${ticket.ticketNumber} - ${ticket.subject}`;
        const notificationBody = `
          <p>A new reply has been added to ticket <strong>${ticket.ticketNumber}</strong>.</p>
          
          <p><strong>From:</strong> ${comment.authorName} (${comment.authorEmail})</p>
          <p><strong>Date:</strong> ${comment.createdAt}</p>
          
          <div style="border-left: 3px solid #ccc; padding-left: 15px; margin: 15px 0;">
            ${comment.contentType === 'html' ? comment.content : comment.content.replace(/\n/g, '<br>')}
          </div>
          
          <p><strong>Ticket Details:</strong></p>
          <ul>
            <li>Ticket Number: ${ticket.ticketNumber}</li>
            <li>Subject: ${ticket.subject}</li>
            <li>Priority: ${ticket.priority}</li>
            <li>Status: ${ticket.status}</li>
          </ul>
          
          <p>View ticket: <a href="${process.env.PORTAL_URL}/tickets/${ticket._id}">Click here</a></p>
        `;
        
        for (const recipient of recipients) {
          await transporter.sendMail({
            from: process.env.TICKET_EMAIL_FROM,
            to: recipient,
            subject: notificationSubject,
            html: notificationBody,
            headers: {
              'References': ticket.emailThreadId,
              'In-Reply-To': emailData.messageId
            }
          });
        }
        
        console.log(`Reply notifications sent for ticket ${ticket.ticketNumber} to ${recipients.size} recipients`);
      }
    } catch (error) {
      console.error('Error sending reply notifications:', error);
    }
  },

  // Helper methods
  extractEmail: (emailString) => {
    const match = emailString.match(/<([^>]+)>/) || emailString.match(/([^\s<>]+@[^\s<>]+)/);
    return match ? match[1] : emailString;
  },

  extractName: (emailString) => {
    const match = emailString.match(/^([^<]+)<[^>]+>$/);
    return match ? match[1].trim().replace(/^["']|["']$/g, '') : null;
  },

  htmlToText: (html) => {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  },

  autoAssignCategory: async (subject, description, senderEmail) => {
    try {
      const categories = await TicketCategory.find({ 
        isActive: true,
        'autoRules.0': { $exists: true }
      });
      
      const text = `${subject} ${description}`.toLowerCase();
      const domain = senderEmail.split('@')[1] || '';
      
      let bestMatch = null;
      let highestPriority = -1;
      
      for (const category of categories) {
        for (const rule of category.autoRules) {
          let matches = false;
          let testValue = '';
          
          switch (rule.field) {
            case 'subject':
              testValue = subject.toLowerCase();
              break;
            case 'description':
              testValue = description.toLowerCase();
              break;
            case 'requesterEmail':
              testValue = senderEmail.toLowerCase();
              break;
            case 'senderDomain':
              testValue = domain.toLowerCase();
              break;
            default:
              testValue = text;
          }
          
          const ruleValue = rule.value.toLowerCase();
          
          switch (rule.operator) {
            case 'contains':
              matches = testValue.includes(ruleValue);
              break;
            case 'starts_with':
              matches = testValue.startsWith(ruleValue);
              break;
            case 'ends_with':
              matches = testValue.endsWith(ruleValue);
              break;
            case 'equals':
              matches = testValue === ruleValue;
              break;
            case 'regex':
              try {
                const regex = new RegExp(rule.value, 'i');
                matches = regex.test(testValue);
              } catch (e) {
                console.error('Invalid regex in category rule:', rule.value);
              }
              break;
          }
          
          if (matches && rule.priority > highestPriority) {
            bestMatch = category;
            highestPriority = rule.priority;
          }
        }
      }
      
      return bestMatch;
    } catch (error) {
      console.error('Error in auto-assign category for email:', error);
      return null;
    }
  }
};

module.exports = ticketEmailController;