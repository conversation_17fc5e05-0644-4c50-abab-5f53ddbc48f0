const LenelS2NetBoxAPI = require('../integrations/lenelS2NetBox/lenelS2NetBoxAPI');
const GoogleAdminAPI = require('../integrations/googleAdmin/googleAdminAPI');
const User = require('../../models/User');
const IntegrationSettings = require('../../models/IntegrationSettings');
const integrationTracker = require('../utils/integrationTracker');

// Initialize Lenel S2 NetBox API with environment variables
const lenelHost = process.env.LENEL_S2_NETBOX_HOST || '';
const lenelUsername = process.env.LENEL_S2_NETBOX_USERNAME || '';
const lenelPassword = process.env.LENEL_S2_NETBOX_PASSWORD || '';
const lenelPort = process.env.LENEL_S2_NETBOX_PORT || 443;

// Initialize Google Admin API with environment variables
const googleServiceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
const googleServiceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
const googleImpersonationEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;
const googleClientId = process.env.GOOGLE_ADMIN_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_ADMIN_CLIENT_SECRET || '';
const googleRedirectUri = process.env.GOOGLE_ADMIN_REDIRECT_URI || '';
const googleTokenPath = process.env.GOOGLE_ADMIN_TOKEN_PATH || './google-admin-token.json';

// Create API instances
let lenelS2NetBoxAPI = new LenelS2NetBoxAPI(lenelHost, lenelUsername, lenelPassword, lenelPort);
let googleAdminAPI = new GoogleAdminAPI(
  googleClientId,
  googleClientSecret,
  googleRedirectUri,
  googleTokenPath,
  null, // No user tokens
  null, // No user ID
  googleImpersonationEmail // Pass the impersonation email for service account authentication
);

/**
 * Get user account status across all integrations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserAccountStatus = async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Get all available integrations
    const integrations = await getAvailableIntegrations();
    
    // Check user account status for each integration
    const accountStatus = {};
    
    for (const integration of integrations) {
      try {
        const status = await checkUserAccountStatus(integration.id, user);
        accountStatus[integration.id] = status;
      } catch (error) {
        console.error(`Error checking account status for ${integration.id}:`, error);
        accountStatus[integration.id] = {
          exists: false,
          error: error.message
        };
      }
    }
    
    res.json(accountStatus);
  } catch (error) {
    console.error('Error getting user account status:', error);
    res.status(500).json({ message: 'Error getting user account status', error: error.message });
  }
};

/**
 * Provision user account in an integration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.provisionUserAccount = async (req, res) => {
  try {
    const { userId, integrationId } = req.params;
    const userData = req.body;
    
    if (!userId || !integrationId) {
      return res.status(400).json({ message: 'User ID and integration ID are required' });
    }
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Check if the integration supports user provisioning
    const supportsProvisioning = await checkProvisioningSupport(integrationId);
    if (!supportsProvisioning) {
      return res.status(400).json({ message: `Integration ${integrationId} does not support user provisioning` });
    }
    
    // Check if the user already has an account in this integration
    const accountStatus = await checkUserAccountStatus(integrationId, user);
    if (accountStatus.exists) {
      return res.status(400).json({ message: `User already has an account in ${integrationId}` });
    }
    
    // Provision the user account
    const result = await provisionUserAccount(integrationId, user, userData);
    
    // Update user's provisioned accounts
    if (!user.provisionedAccounts) {
      user.provisionedAccounts = {};
    }
    
    user.provisionedAccounts[integrationId] = {
      accountId: result.accountId,
      provisionedAt: new Date(),
      status: 'active'
    };
    
    await user.save();
    
    res.json({
      message: `User account provisioned successfully in ${integrationId}`,
      accountId: result.accountId,
      details: result.details
    });
  } catch (error) {
    console.error('Error provisioning user account:', error);
    res.status(500).json({ message: 'Error provisioning user account', error: error.message });
  }
};

/**
 * Deprovision user account from an integration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deprovisionUserAccount = async (req, res) => {
  try {
    const { userId, integrationId } = req.params;
    
    if (!userId || !integrationId) {
      return res.status(400).json({ message: 'User ID and integration ID are required' });
    }
    
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Check if the integration supports user deprovisioning
    const supportsDeprovisioning = await checkDeprovisioningSupport(integrationId);
    if (!supportsDeprovisioning) {
      return res.status(400).json({ message: `Integration ${integrationId} does not support user deprovisioning` });
    }
    
    // Check if the user has an account in this integration
    const accountStatus = await checkUserAccountStatus(integrationId, user);
    if (!accountStatus.exists) {
      return res.status(400).json({ message: `User does not have an account in ${integrationId}` });
    }
    
    // Deprovision the user account
    await deprovisionUserAccount(integrationId, user, accountStatus.accountId);
    
    // Update user's provisioned accounts
    if (user.provisionedAccounts && user.provisionedAccounts[integrationId]) {
      user.provisionedAccounts[integrationId] = {
        ...user.provisionedAccounts[integrationId],
        deprovisionedAt: new Date(),
        status: 'deprovisioned'
      };
    }
    
    await user.save();
    
    res.json({
      message: `User account deprovisioned successfully from ${integrationId}`
    });
  } catch (error) {
    console.error('Error deprovisioning user account:', error);
    res.status(500).json({ message: 'Error deprovisioning user account', error: error.message });
  }
};

/**
 * Get available integrations that support user provisioning
 * @returns {Promise<Array>} List of integrations that support user provisioning
 */
async function getAvailableIntegrations() {
  // This is a simplified version - in a real implementation, you would
  // dynamically discover integrations that support user provisioning
  return [
    {
      id: 'lenel-s2-netbox',
      name: 'Lenel S2 NetBox',
      supportsProvisioning: true,
      supportsDeprovisioning: true
    },
    {
      id: 'google-admin',
      name: 'Google Admin',
      supportsProvisioning: true,
      supportsDeprovisioning: true
    },
    {
      id: 'apple-business-manager',
      name: 'Apple Business Manager',
      supportsProvisioning: false,
      supportsDeprovisioning: false
    },
    {
      id: 'synology',
      name: 'Synology',
      supportsProvisioning: false,
      supportsDeprovisioning: false
    }
  ];
}

/**
 * Check if an integration supports user provisioning
 * @param {string} integrationId - Integration ID
 * @returns {Promise<boolean>} Whether the integration supports user provisioning
 */
async function checkProvisioningSupport(integrationId) {
  const integrations = await getAvailableIntegrations();
  const integration = integrations.find(i => i.id === integrationId);
  return integration ? integration.supportsProvisioning : false;
}

/**
 * Check if an integration supports user deprovisioning
 * @param {string} integrationId - Integration ID
 * @returns {Promise<boolean>} Whether the integration supports user deprovisioning
 */
async function checkDeprovisioningSupport(integrationId) {
  const integrations = await getAvailableIntegrations();
  const integration = integrations.find(i => i.id === integrationId);
  return integration ? integration.supportsDeprovisioning : false;
}

/**
 * Check if a user has an account in an integration
 * @param {string} integrationId - Integration ID
 * @param {Object} user - User object
 * @returns {Promise<Object>} Account status
 */
async function checkUserAccountStatus(integrationId, user) {
  switch (integrationId) {
    case 'lenel-s2-netbox':
      return await checkLenelS2NetBoxAccount(user);
    case 'google-admin':
      return await checkGoogleAdminAccount(user);
    case 'apple-business-manager':
      return { exists: false, message: 'Apple Business Manager does not support user provisioning' };
    case 'synology':
      return { exists: false, message: 'Synology does not support user provisioning' };
    default:
      return { exists: false, message: `Unknown integration: ${integrationId}` };
  }
}

/**
 * Check if a user has an account in Lenel S2 NetBox
 * @param {Object} user - User object
 * @returns {Promise<Object>} Account status
 */
async function checkLenelS2NetBoxAccount(user) {
  try {
    // Ensure API is initialized
    await lenelS2NetBoxAPI.initialize();
    
    // Get all cardholders
    const cardholders = await lenelS2NetBoxAPI.getCardholders();
    
    // Check if the user has a cardholder account
    // This is a simplified implementation - in a real system, you would
    // need a more robust way to match users to cardholders
    const cardholder = cardholders.find(c => 
      c.email === user.email || 
      (c.firstName === user.name.split(' ')[0] && c.lastName === user.name.split(' ').slice(1).join(' '))
    );
    
    if (cardholder) {
      return {
        exists: true,
        accountId: cardholder.id,
        details: cardholder
      };
    }
    
    return { exists: false };
  } catch (error) {
    console.error('Error checking Lenel S2 NetBox account:', error);
    return { exists: false, error: error.message };
  }
}

/**
 * Check if a user has an account in Google Admin
 * @param {Object} user - User object
 * @returns {Promise<Object>} Account status
 */
async function checkGoogleAdminAccount(user) {
  try {
    // Ensure API is initialized
    await googleAdminAPI.initialize();
    
    // Try to get the user by email
    try {
      const googleUser = await googleAdminAPI.getUser(user.email);
      
      if (googleUser) {
        return {
          exists: true,
          accountId: googleUser.id,
          details: googleUser
        };
      }
    } catch (error) {
      // If the user is not found, the API will throw an error
      if (error.message.includes('Resource Not Found')) {
        return { exists: false };
      }
      throw error;
    }
    
    return { exists: false };
  } catch (error) {
    console.error('Error checking Google Admin account:', error);
    return { exists: false, error: error.message };
  }
}

/**
 * Provision a user account in an integration
 * @param {string} integrationId - Integration ID
 * @param {Object} user - User object
 * @param {Object} userData - User data for provisioning
 * @returns {Promise<Object>} Provisioning result
 */
async function provisionUserAccount(integrationId, user, userData) {
  switch (integrationId) {
    case 'lenel-s2-netbox':
      return await provisionLenelS2NetBoxAccount(user, userData);
    case 'google-admin':
      return await provisionGoogleAdminAccount(user, userData);
    default:
      throw new Error(`Unknown integration: ${integrationId}`);
  }
}

/**
 * Provision a user account in Lenel S2 NetBox
 * @param {Object} user - User object
 * @param {Object} userData - User data for provisioning
 * @returns {Promise<Object>} Provisioning result
 */
async function provisionLenelS2NetBoxAccount(user, userData) {
  try {
    // Ensure API is initialized
    await lenelS2NetBoxAPI.initialize();
    
    // Prepare user data for Lenel S2 NetBox
    const lenelUserData = {
      firstName: userData.firstName || user.name.split(' ')[0],
      lastName: userData.lastName || user.name.split(' ').slice(1).join(' '),
      email: userData.email || user.email,
      notes: userData.notes || `Provisioned from CSF Portal on ${new Date().toISOString()}`,
      activationDate: userData.activationDate || new Date().toISOString(),
      expirationDate: userData.expirationDate,
      accessLevels: userData.accessLevels || []
    };
    
    // Create the user in Lenel S2 NetBox
    const result = await lenelS2NetBoxAPI.createUser(lenelUserData);
    
    return {
      accountId: result.id,
      details: result
    };
  } catch (error) {
    console.error('Error provisioning Lenel S2 NetBox account:', error);
    throw error;
  }
}

/**
 * Provision a user account in Google Admin
 * @param {Object} user - User object
 * @param {Object} userData - User data for provisioning
 * @returns {Promise<Object>} Provisioning result
 */
async function provisionGoogleAdminAccount(user, userData) {
  try {
    // Ensure API is initialized
    await googleAdminAPI.initialize();
    
    // Prepare user data for Google Admin
    const googleUserData = {
      primaryEmail: userData.email || user.email,
      name: {
        givenName: userData.firstName || user.name.split(' ')[0],
        familyName: userData.lastName || user.name.split(' ').slice(1).join(' '),
        fullName: userData.fullName || user.name
      },
      password: userData.password || Math.random().toString(36).slice(-10),
      changePasswordAtNextLogin: userData.changePasswordAtNextLogin !== false,
      orgUnitPath: userData.orgUnitPath || '/',
      isAdmin: userData.isAdmin || false
    };
    
    // Create the user in Google Admin
    const result = await googleAdminAPI.createUser(googleUserData);
    
    return {
      accountId: result.id,
      details: result
    };
  } catch (error) {
    console.error('Error provisioning Google Admin account:', error);
    throw error;
  }
}

/**
 * Deprovision a user account from an integration
 * @param {string} integrationId - Integration ID
 * @param {Object} user - User object
 * @param {string} accountId - Account ID in the integration
 * @returns {Promise<void>}
 */
async function deprovisionUserAccount(integrationId, user, accountId) {
  switch (integrationId) {
    case 'lenel-s2-netbox':
      return await deprovisionLenelS2NetBoxAccount(accountId);
    case 'google-admin':
      return await deprovisionGoogleAdminAccount(user.email);
    default:
      throw new Error(`Unknown integration: ${integrationId}`);
  }
}

/**
 * Deprovision a user account from Lenel S2 NetBox
 * @param {string} cardholderId - Cardholder ID
 * @returns {Promise<void>}
 */
async function deprovisionLenelS2NetBoxAccount(cardholderId) {
  try {
    // Ensure API is initialized
    await lenelS2NetBoxAPI.initialize();
    
    // Delete the user from Lenel S2 NetBox
    await lenelS2NetBoxAPI.deleteUser(cardholderId);
  } catch (error) {
    console.error('Error deprovisioning Lenel S2 NetBox account:', error);
    throw error;
  }
}

/**
 * Deprovision a user account from Google Admin
 * @param {string} userEmail - User email
 * @returns {Promise<void>}
 */
async function deprovisionGoogleAdminAccount(userEmail) {
  try {
    // Ensure API is initialized
    await googleAdminAPI.initialize();
    
    // Delete the user from Google Admin
    await googleAdminAPI.deleteUser(userEmail);
  } catch (error) {
    console.error('Error deprovisioning Google Admin account:', error);
    throw error;
  }
}