const ZeeVeeAPI = require('../integrations/zeeVee/zeeVeeAPI');
const ZeeVeeConfig = require('../../models/ZeeVeeConfig');

// Initialize ZeeVee API with empty credentials (will be updated when needed)
let zeeVeeAPI = new ZeeVeeAPI('', 80, '', '');

// Helper function to get the latest configuration
const getLatestConfig = async (req) => {
  try {
    // Always prioritize environment variables for authentication
    const host = process.env.ZEEVEE_HOST || '';
    const port = process.env.ZEEVEE_PORT || 80;
    const username = process.env.ZEEVEE_USERNAME || '';
    const password = process.env.ZEEVEE_PASSWORD || '';
    
    // If environment variables are set, use them directly
    if (host) {
      zeeVeeAPI = new ZeeVeeAPI(host, port, username, password);
      return {
        host,
        port,
        username,
        password: '********', // Mask password for security
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('ZEEVEE_HOST environment variable not set. Falling back to database config (not recommended).');
    
    // Get the latest configuration from the database
    const config = await ZeeVeeConfig.findOne().sort({ updatedAt: -1 });
    
    if (config) {
      zeeVeeAPI = new ZeeVeeAPI(config.host, config.port, config.username, config.password);
      return {
        host: config.host,
        port: config.port,
        username: config.username,
        password: '********', // Mask password for security
        updatedAt: config.updatedAt,
        fromEnv: false
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching ZeeVee configuration:', error);
    throw error;
  }
};

/**
 * Get device information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceInfo = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceInfo = await zeeVeeAPI.getDeviceInfo();
    res.json(deviceInfo);
  } catch (error) {
    console.error('Controller error fetching ZeeVee device information:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee device information', error: error.message });
  }
};

/**
 * Get system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    await getLatestConfig(req);
    const systemStatus = await zeeVeeAPI.getSystemStatus();
    res.json(systemStatus);
  } catch (error) {
    console.error('Controller error fetching ZeeVee system status:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee system status', error: error.message });
  }
};

/**
 * Get all encoders
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEncoders = async (req, res) => {
  try {
    await getLatestConfig(req);
    const encoders = await zeeVeeAPI.getEncoders();
    res.json(encoders);
  } catch (error) {
    console.error('Controller error fetching ZeeVee encoders:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee encoders', error: error.message });
  }
};

/**
 * Get encoder details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEncoderDetails = async (req, res) => {
  try {
    await getLatestConfig(req);
    const encoderId = req.params.id;
    const encoderDetails = await zeeVeeAPI.getEncoderDetails(encoderId);
    res.json(encoderDetails);
  } catch (error) {
    console.error('Controller error fetching ZeeVee encoder details:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee encoder details', error: error.message });
  }
};

/**
 * Get all decoders
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDecoders = async (req, res) => {
  try {
    await getLatestConfig(req);
    const decoders = await zeeVeeAPI.getDecoders();
    res.json(decoders);
  } catch (error) {
    console.error('Controller error fetching ZeeVee decoders:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee decoders', error: error.message });
  }
};

/**
 * Get decoder details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDecoderDetails = async (req, res) => {
  try {
    await getLatestConfig(req);
    const decoderId = req.params.id;
    const decoderDetails = await zeeVeeAPI.getDecoderDetails(decoderId);
    res.json(decoderDetails);
  } catch (error) {
    console.error('Controller error fetching ZeeVee decoder details:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee decoder details', error: error.message });
  }
};

/**
 * Get all video channels
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getChannels = async (req, res) => {
  try {
    await getLatestConfig(req);
    const channels = await zeeVeeAPI.getChannels();
    res.json(channels);
  } catch (error) {
    console.error('Controller error fetching ZeeVee channels:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee channels', error: error.message });
  }
};

/**
 * Get channel details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getChannelDetails = async (req, res) => {
  try {
    await getLatestConfig(req);
    const channelId = req.params.id;
    const channelDetails = await zeeVeeAPI.getChannelDetails(channelId);
    res.json(channelDetails);
  } catch (error) {
    console.error('Controller error fetching ZeeVee channel details:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee channel details', error: error.message });
  }
};

/**
 * Set encoder input
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setEncoderInput = async (req, res) => {
  try {
    await getLatestConfig(req);
    const encoderId = req.params.id;
    const { input } = req.body;
    
    if (!input) {
      return res.status(400).json({ message: 'Input source is required' });
    }
    
    const result = await zeeVeeAPI.setEncoderInput(encoderId, input);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting ZeeVee encoder input:', error);
    res.status(500).json({ message: 'Error setting ZeeVee encoder input', error: error.message });
  }
};

/**
 * Set decoder output
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setDecoderOutput = async (req, res) => {
  try {
    await getLatestConfig(req);
    const decoderId = req.params.id;
    const { channelId } = req.body;
    
    if (!channelId) {
      return res.status(400).json({ message: 'Channel ID is required' });
    }
    
    const result = await zeeVeeAPI.setDecoderOutput(decoderId, channelId);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting ZeeVee decoder output:', error);
    res.status(500).json({ message: 'Error setting ZeeVee decoder output', error: error.message });
  }
};

/**
 * Reboot device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.rebootDevice = async (req, res) => {
  try {
    await getLatestConfig(req);
    const result = await zeeVeeAPI.rebootDevice();
    res.json(result);
  } catch (error) {
    console.error('Controller error rebooting ZeeVee device:', error);
    res.status(500).json({ message: 'Error rebooting ZeeVee device', error: error.message });
  }
};

/**
 * Get network settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getNetworkSettings = async (req, res) => {
  try {
    await getLatestConfig(req);
    const networkSettings = await zeeVeeAPI.getNetworkSettings();
    res.json(networkSettings);
  } catch (error) {
    console.error('Controller error fetching ZeeVee network settings:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee network settings', error: error.message });
  }
};

/**
 * Get health status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getHealthStatus = async (req, res) => {
  try {
    await getLatestConfig(req);
    const healthStatus = await zeeVeeAPI.getHealthStatus();
    res.json(healthStatus);
  } catch (error) {
    console.error('Controller error fetching ZeeVee health status:', error);
    res.status(500).json({
      message: 'Error fetching ZeeVee health status',
      error: error.message,
      status: 'unhealthy',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get ZeeVee configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Set headers to prevent caching and ensure the resource is always considered modified
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // Always prioritize environment variables for authentication
    const host = process.env.ZEEVEE_HOST || '';
    const port = process.env.ZEEVEE_PORT || 80;
    const username = process.env.ZEEVEE_USERNAME || '';
    const password = process.env.ZEEVEE_PASSWORD || '';
    
    let configData = {};
    let deviceInfo = null;
    
    // If environment variables are set, use them directly
    if (host) {
      configData = {
        host,
        port,
        username,
        hasPassword: !!password,
        configuredAt: new Date(),
        fromEnv: true
      };
      
      // Try to get device information using environment variables
      try {
        // Ensure the API is using the correct configuration
        zeeVeeAPI = new ZeeVeeAPI(host, port, username, password);
        
        // Get device information
        deviceInfo = await zeeVeeAPI.getDeviceInfo();
      } catch (deviceError) {
        console.error('Error fetching ZeeVee device information:', deviceError);
        // Continue without device info if there's an error
      }
    } else {
      // If environment variables are not set, fall back to database config (not recommended)
      console.warn('ZEEVEE_HOST environment variable not set. Falling back to database config (not recommended).');
      
      // Get the latest configuration from the database
      const config = await ZeeVeeConfig.findOne().sort({ updatedAt: -1 });
      
      if (config) {
        configData = {
          host: config.host,
          port: config.port,
          username: config.username,
          hasPassword: !!config.password,
          configuredAt: config.updatedAt,
          fromEnv: false
        };
        
        // Try to get device information if we have a valid configuration
        if (config.host) {
          try {
            // Ensure the API is using the correct configuration
            zeeVeeAPI = new ZeeVeeAPI(config.host, config.port, config.username, config.password);
            
            // Get device information
            deviceInfo = await zeeVeeAPI.getDeviceInfo();
          } catch (deviceError) {
            console.error('Error fetching ZeeVee device information:', deviceError);
            // Continue without device info if there's an error
          }
        }
      } else {
        return res.status(404).json({ message: 'ZeeVee configuration not found. Please set the required environment variables.' });
      }
    }
    
    // Combine configuration data with device information
    const responseData = {
      ...configData,
      deviceInfo
    };
    
    res.json(responseData);
  } catch (error) {
    console.error('Error fetching ZeeVee configuration:', error);
    res.status(500).json({ message: 'Error fetching ZeeVee configuration', error: error.message });
  }
};

/**
 * Save ZeeVee configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling ZeeVee configuration request:', error);
    res.status(500).json({ message: 'Error handling ZeeVee configuration request', error: error.message });
  }
};

/**
 * Set up ZeeVee with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling ZeeVee one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling ZeeVee one-click setup request', 
      error: error.message 
    });
  }
};