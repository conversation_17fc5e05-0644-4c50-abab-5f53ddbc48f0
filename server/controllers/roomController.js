const Room = require('../../models/Room');
const Floor = require('../../models/Floor');
const Building = require('../../models/Building');
const Reservation = require('../../models/Reservation');
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Types;

/**
 * Room Controller
 * Handles all room-related operations
 */
const roomController = {
  /**
   * Get all rooms
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllRooms: async (req, res) => {
    try {
      const { 
        buildingId, 
        floorId, 
        capacity, 
        features, 
        status, 
        search 
      } = req.query;
      
      // Build query
      const query = {};
      
      if (floorId) {
        query.floorId = floorId;
      }
      
      if (capacity) {
        query.capacity = { $gte: parseInt(capacity) };
      }
      
      if (features) {
        const featureArray = features.split(',');
        query.features = { $all: featureArray };
      }
      
      if (status) {
        query.status = status;
      }
      
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { roomNumber: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      // If buildingId is provided, first get all floors in the building
      if (buildingId) {
        const floors = await Floor.find({ buildingId });
        const floorIds = floors.map(floor => floor._id);
        query.floorId = { $in: floorIds };
      }
      
      // Get rooms
      const rooms = await Room.find(query)
        .populate('floorId', 'name level buildingId')
        .populate({
          path: 'floorId',
          populate: {
            path: 'buildingId',
            model: 'Building',
            select: 'name'
          }
        });
      
      res.json(rooms);
    } catch (error) {
      console.error('Error getting rooms:', error);
      res.status(500).json({ message: 'Error getting rooms', error: error.message });
    }
  },
  
  /**
   * Get room by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getRoomById: async (req, res) => {
    try {
      const { id } = req.params;
      
      const room = await Room.findById(id)
        .populate('floorId', 'name level buildingId')
        .populate({
          path: 'floorId',
          populate: {
            path: 'buildingId',
            model: 'Building',
            select: 'name'
          }
        });
      
      if (!room) {
        return res.status(404).json({ message: 'Room not found' });
      }
      
      res.json(room);
    } catch (error) {
      console.error('Error getting room:', error);
      res.status(500).json({ message: 'Error getting room', error: error.message });
    }
  },
  
  /**
   * Create a new room
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createRoom: async (req, res) => {
    try {
      const { 
        floorId, 
        name, 
        roomNumber, 
        description, 
        capacity, 
        features, 
        otherFeatures,
        dimensions,
        status,
        calendarId,
        metadata
      } = req.body;
      
      // Validate floor exists
      const floor = await Floor.findById(floorId);
      if (!floor) {
        return res.status(404).json({ message: 'Floor not found' });
      }
      
      // Create room
      const room = new Room({
        floorId,
        name,
        roomNumber,
        description,
        capacity,
        features,
        otherFeatures,
        dimensions,
        status,
        calendarId,
        metadata,
        createdBy: req.user._id
      });
      
      await room.save();
      
      res.status(201).json(room);
    } catch (error) {
      console.error('Error creating room:', error);
      res.status(500).json({ message: 'Error creating room', error: error.message });
    }
  },
  
  /**
   * Update a room
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateRoom: async (req, res) => {
    try {
      const { id } = req.params;
      const { 
        floorId, 
        name, 
        roomNumber, 
        description, 
        capacity, 
        features, 
        otherFeatures,
        dimensions,
        status,
        calendarId,
        metadata
      } = req.body;
      
      // Validate room exists
      const room = await Room.findById(id);
      if (!room) {
        return res.status(404).json({ message: 'Room not found' });
      }
      
      // If floorId is changing, validate new floor exists
      if (floorId && floorId !== room.floorId.toString()) {
        const floor = await Floor.findById(floorId);
        if (!floor) {
          return res.status(404).json({ message: 'Floor not found' });
        }
      }
      
      // Update room
      const updatedRoom = await Room.findByIdAndUpdate(
        id,
        {
          floorId: floorId || room.floorId,
          name: name || room.name,
          roomNumber: roomNumber || room.roomNumber,
          description: description !== undefined ? description : room.description,
          capacity: capacity || room.capacity,
          features: features || room.features,
          otherFeatures: otherFeatures || room.otherFeatures,
          dimensions: dimensions || room.dimensions,
          status: status || room.status,
          calendarId: calendarId !== undefined ? calendarId : room.calendarId,
          metadata: metadata || room.metadata
        },
        { new: true }
      );
      
      res.json(updatedRoom);
    } catch (error) {
      console.error('Error updating room:', error);
      res.status(500).json({ message: 'Error updating room', error: error.message });
    }
  },
  
  /**
   * Delete a room
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteRoom: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Check if room exists
      const room = await Room.findById(id);
      if (!room) {
        return res.status(404).json({ message: 'Room not found' });
      }
      
      // Check if room has any reservations
      const reservations = await Reservation.find({ roomId: id });
      if (reservations.length > 0) {
        return res.status(400).json({ 
          message: 'Cannot delete room with existing reservations',
          reservationCount: reservations.length
        });
      }
      
      // Delete room
      await Room.findByIdAndDelete(id);
      
      res.json({ message: 'Room deleted successfully' });
    } catch (error) {
      console.error('Error deleting room:', error);
      res.status(500).json({ message: 'Error deleting room', error: error.message });
    }
  },
  
  /**
   * Find available rooms based on criteria
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  findAvailableRooms: async (req, res) => {
    try {
      const { 
        startTime, 
        endTime, 
        capacity, 
        features, 
        buildingId, 
        floorId 
      } = req.query;
      
      // Validate required parameters
      if (!startTime || !endTime) {
        return res.status(400).json({ message: 'Start time and end time are required' });
      }
      
      // Parse dates
      const start = new Date(startTime);
      const end = new Date(endTime);
      
      // Validate dates
      if (start >= end) {
        return res.status(400).json({ message: 'End time must be after start time' });
      }
      
      // Build query for rooms
      const roomQuery = {};
      
      if (capacity) {
        roomQuery.capacity = { $gte: parseInt(capacity) };
      }
      
      if (features) {
        const featureArray = features.split(',');
        roomQuery.features = { $all: featureArray };
      }
      
      if (floorId) {
        roomQuery.floorId = floorId;
      } else if (buildingId) {
        // If buildingId is provided, first get all floors in the building
        const floors = await Floor.find({ buildingId });
        const floorIds = floors.map(floor => floor._id);
        roomQuery.floorId = { $in: floorIds };
      }
      
      // Only include available rooms
      roomQuery.status = 'available';
      
      // Find all rooms that match the criteria
      const rooms = await Room.find(roomQuery)
        .populate('floorId', 'name level buildingId')
        .populate({
          path: 'floorId',
          populate: {
            path: 'buildingId',
            model: 'Building',
            select: 'name'
          }
        });
      
      // Find all reservations that overlap with the requested time period
      const overlappingReservations = await Reservation.find({
        $or: [
          // Reservation starts during the requested period
          { startTime: { $gte: start, $lt: end } },
          // Reservation ends during the requested period
          { endTime: { $gt: start, $lte: end } },
          // Reservation spans the entire requested period
          { startTime: { $lte: start }, endTime: { $gte: end } }
        ],
        status: { $in: ['pending', 'approved'] }
      });
      
      // Get the room IDs that are already reserved
      const reservedRoomIds = overlappingReservations.map(res => res.roomId.toString());
      
      // Filter out rooms that are already reserved
      const availableRooms = rooms.filter(room => !reservedRoomIds.includes(room._id.toString()));
      
      res.json(availableRooms);
    } catch (error) {
      console.error('Error finding available rooms:', error);
      res.status(500).json({ message: 'Error finding available rooms', error: error.message });
    }
  }
};

module.exports = roomController;