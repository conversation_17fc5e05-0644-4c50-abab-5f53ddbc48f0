const LGThinqAPI = require('../integrations/lgThinq/lgThinqAPI');
const LGThinqConfig = require('../../models/LGThinqConfig');

// Initialize LG ThinQ API with empty credentials (will be updated when needed)
let lgThinqAPI = new LGThinqAPI('', 'america', 'US');

// Helper function to get the latest configuration
const getLatestConfig = async (req) => {
  try {
    // Always prioritize environment variables for authentication
    const patToken = process.env.LG_THINQ_PAT_TOKEN;
    const region = process.env.LG_THINQ_REGION || 'america';
    const country = process.env.LG_THINQ_COUNTRY || 'US';
    const clientId = process.env.LG_THINQ_CLIENT_ID || null;
    
    // If environment variables are set, use them directly
    if (patToken) {
      lgThinqAPI = new LGThinqAPI(patToken, region, country, clientId);
      return {
        patToken,
        region,
        country,
        clientId,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('LG_THINQ_PAT_TOKEN environment variable not set. Falling back to database config (not recommended).');
    
    // Check if we should use global config or user-specific config
    const useGlobalConfig = req ? (req.useGlobalConfig !== false) : true;

    if (useGlobalConfig) {
      // Use global configuration
      const config = await LGThinqConfig.findOne().sort({ updatedAt: -1 });
      if (config) {
        lgThinqAPI = new LGThinqAPI(config.patToken || config.apiKey, config.region, config.country, config.clientId);
        return config;
      }
    } else if (req && req.user) {
      // Use user-specific configuration if available
      const User = require('../../models/User');
      const user = await User.findById(req.user.id);

      if (user && user.lgThinqConfig) {
        lgThinqAPI = new LGThinqAPI(
          user.lgThinqConfig.patToken || user.lgThinqConfig.apiKey, 
          user.lgThinqConfig.region || 'america', 
          user.lgThinqConfig.country || 'US',
          user.lgThinqConfig.clientId
        );
        return user.lgThinqConfig;
      }
    }

    return null;
  } catch (error) {
    console.error('Error fetching LG ThinQ configuration:', error);
    throw error;
  }
};

/**
 * Get all LG ThinQ devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig(req);
    const devices = await lgThinqAPI.getDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching LG ThinQ devices:', error);
    res.status(500).json({ message: 'Error fetching LG ThinQ devices', error: error.message });
  }
};

/**
 * Get LG ThinQ device profile (detailed information)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const deviceProfile = await lgThinqAPI.getDeviceDetails(deviceId);
    
    // Process the response to ensure it's in a consistent format
    // If the response has a 'result' property, return that
    // Otherwise, return the entire response
    const processedResponse = deviceProfile.result ? deviceProfile.result : deviceProfile;
    
    res.json(processedResponse);
  } catch (error) {
    console.error('Controller error fetching LG ThinQ device profile:', error);
    res.status(500).json({ message: 'Error fetching LG ThinQ device profile', error: error.message });
  }
};

/**
 * Get device state (current status)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceStatus = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const deviceState = await lgThinqAPI.getDeviceStatus(deviceId);
    
    // Process the response to ensure it's in a consistent format
    // If the response has a 'result' property, return that
    // Otherwise, return the entire response
    const processedResponse = deviceState.result ? deviceState.result : deviceState;
    
    res.json(processedResponse);
  } catch (error) {
    console.error('Controller error fetching LG ThinQ device state:', error);
    res.status(500).json({ message: 'Error fetching LG ThinQ device state', error: error.message });
  }
};

/**
 * Set device power state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setPower = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { power } = req.body;

    if (power === undefined) {
      return res.status(400).json({ message: 'Power state is required' });
    }

    const result = await lgThinqAPI.setPower(deviceId, power);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting LG ThinQ device power:', error);
    res.status(500).json({ message: 'Error setting LG ThinQ device power', error: error.message });
  }
};

/**
 * Set device temperature
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setTemperature = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { temperature } = req.body;

    if (temperature === undefined) {
      return res.status(400).json({ message: 'Temperature is required' });
    }

    const result = await lgThinqAPI.setTemperature(deviceId, temperature);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting LG ThinQ device temperature:', error);
    res.status(500).json({ message: 'Error setting LG ThinQ device temperature', error: error.message });
  }
};

/**
 * Set device fan speed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setFanSpeed = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { speed } = req.body;

    if (speed === undefined) {
      return res.status(400).json({ message: 'Fan speed is required' });
    }

    const result = await lgThinqAPI.setFanSpeed(deviceId, speed);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting LG ThinQ device fan speed:', error);
    res.status(500).json({ message: 'Error setting LG ThinQ device fan speed', error: error.message });
  }
};

/**
 * Set device mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setMode = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { mode } = req.body;

    if (mode === undefined) {
      return res.status(400).json({ message: 'Mode is required' });
    }

    const result = await lgThinqAPI.setMode(deviceId, mode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting LG ThinQ device mode:', error);
    res.status(500).json({ message: 'Error setting LG ThinQ device mode', error: error.message });
  }
};

/**
 * Set device swing mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setSwingMode = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { swingMode } = req.body;

    if (swingMode === undefined) {
      return res.status(400).json({ message: 'Swing mode is required' });
    }

    const result = await lgThinqAPI.setSwingMode(deviceId, swingMode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting LG ThinQ device swing mode:', error);
    res.status(500).json({ message: 'Error setting LG ThinQ device swing mode', error: error.message });
  }
};

/**
 * Save LG ThinQ configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling LG ThinQ configuration request:', error);
    res.status(500).json({ message: 'Error handling LG ThinQ configuration request', error: error.message });
  }
};

/**
 * Get LG ThinQ configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Always prioritize environment variables for authentication
    const patToken = process.env.LG_THINQ_PAT_TOKEN;
    const region = process.env.LG_THINQ_REGION || 'america';
    const country = process.env.LG_THINQ_COUNTRY || 'US';
    const clientId = process.env.LG_THINQ_CLIENT_ID || null;
    
    // If environment variables are set, use them directly
    if (patToken) {
      // Return configuration without sensitive information
      res.json({
        region,
        country,
        clientId: clientId ? 'configured' : 'auto-generated',
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('LG_THINQ_PAT_TOKEN environment variable not set. Falling back to database config (not recommended).');
    
    const config = await getLatestConfig(req);

    if (!config) {
      return res.status(404).json({ message: 'LG ThinQ configuration not found. Please set LG_THINQ_PAT_TOKEN environment variable.' });
    }

    // Return configuration without sensitive information
    res.json({
      region: config.region,
      country: config.country,
      clientId: config.clientId ? 'configured' : 'auto-generated',
      configuredAt: config.updatedAt,
      isUserSpecific: !req.useGlobalConfig,
      fromEnv: !!config.fromEnv
    });
  } catch (error) {
    console.error('Error fetching LG ThinQ configuration:', error);
    res.status(500).json({ message: 'Error fetching LG ThinQ configuration', error: error.message });
  }
};

/**
 * Set up LG ThinQ with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling LG ThinQ one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling LG ThinQ one-click setup request', 
      error: error.message 
    });
  }
};

/**
 * Control device with custom commands
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlDevice = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { command, value } = req.body;

    if (!command) {
      return res.status(400).json({ message: 'Command is required' });
    }

    // Here we would ideally have a more generic control method in the API
    // For now, we'll handle specific commands based on what's available in the API
    let result;
    switch (command) {
      case 'power':
        result = await lgThinqAPI.setPower(deviceId, value);
        break;
      case 'temperature':
        result = await lgThinqAPI.setTemperature(deviceId, value);
        break;
      case 'fan-speed':
        result = await lgThinqAPI.setFanSpeed(deviceId, value);
        break;
      case 'mode':
        result = await lgThinqAPI.setMode(deviceId, value);
        break;
      case 'swing-mode':
        result = await lgThinqAPI.setSwingMode(deviceId, value);
        break;
      default:
        return res.status(400).json({ message: `Unsupported command: ${command}` });
    }

    res.json(result);
  } catch (error) {
    console.error('Controller error controlling LG ThinQ device:', error);
    res.status(500).json({ message: 'Error controlling LG ThinQ device', error: error.message });
  }
};