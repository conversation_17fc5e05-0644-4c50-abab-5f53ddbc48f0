const GoogleDriveFavorite = require('../../models/GoogleDriveFavorite');
const GoogleDriveAPI = require('../integrations/googleDrive/googleDriveAPI');
const GoogleDriveConfig = require('../../models/GoogleDriveConfig');
const path = require('path');

// Helper function to get the latest configuration and initialize API
const getGoogleDriveAPI = async (userId = null) => {
  try {
    // Always prioritize environment variables for authentication
    const envClientId = process.env.GOOGLE_DRIVE_CLIENT_ID || '';
    const envClientSecret = process.env.GOOGLE_DRIVE_CLIENT_SECRET || '';
    const envRedirectUri = process.env.GOOGLE_DRIVE_REDIRECT_URI || '';
    const envTokenPath = process.env.GOOGLE_DRIVE_TOKEN_PATH || './google-drive-token.json';
    
    // If environment variables are set, use them directly
    if (envClientId && envClientSecret && envRedirectUri) {
      const tokenPath = path.resolve(process.cwd(), envTokenPath);
      const googleDriveAPI = new GoogleDriveAPI(
        envClientId,
        envClientSecret,
        envRedirectUri,
        tokenPath,
        null,
        userId
      );
      await googleDriveAPI.initialize();
      return googleDriveAPI;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('Google Drive environment variables not set. Falling back to database config (not recommended).');
    
    const config = await GoogleDriveConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      const tokenPath = path.resolve(process.cwd(), config.tokenPath || './google-drive-token.json');
      const googleDriveAPI = new GoogleDriveAPI(
        config.clientId,
        config.clientSecret,
        config.redirectUri,
        tokenPath,
        null,
        userId
      );
      await googleDriveAPI.initialize();
      return googleDriveAPI;
    }
    return null;
  } catch (error) {
    console.error('Error initializing Google Drive API:', error);
    throw error;
  }
};

/**
 * Add a Google Drive file to favorites
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addFavorite = async (req, res) => {
  try {
    const { fileId } = req.body;

    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    // Check if the file is already favorited by this user
    const existingFavorite = await GoogleDriveFavorite.findOne({
      user: req.user.id,
      fileId
    });

    if (existingFavorite) {
      return res.status(400).json({ message: 'File is already in favorites' });
    }

    // Get file details from Google Drive
    const googleDriveAPI = await getGoogleDriveAPI(req.user.id);
    if (!googleDriveAPI || !googleDriveAPI.isAuthenticated()) {
      return res.status(401).json({ message: 'Not authenticated with Google Drive' });
    }

    const fileDetails = await googleDriveAPI.getFile(fileId);

    // Create a new favorite
    const favorite = new GoogleDriveFavorite({
      user: req.user.id,
      fileId: fileDetails.id,
      name: fileDetails.name,
      mimeType: fileDetails.mimeType,
      iconLink: fileDetails.iconLink,
      webViewLink: fileDetails.webViewLink,
      size: fileDetails.size,
      modifiedTime: fileDetails.modifiedTime
    });

    await favorite.save();

    res.json({ message: 'File added to favorites', favorite });
  } catch (error) {
    console.error('Error adding file to favorites:', error);
    res.status(500).json({ message: 'Error adding file to favorites', error: error.message });
  }
};

/**
 * Remove a Google Drive file from favorites
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.removeFavorite = async (req, res) => {
  try {
    const { fileId } = req.params;

    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    // Find and remove the favorite
    const favorite = await GoogleDriveFavorite.findOneAndDelete({
      user: req.user.id,
      fileId
    });

    if (!favorite) {
      return res.status(404).json({ message: 'Favorite not found' });
    }

    res.json({ message: 'File removed from favorites' });
  } catch (error) {
    console.error('Error removing file from favorites:', error);
    res.status(500).json({ message: 'Error removing file from favorites', error: error.message });
  }
};

/**
 * Get all favorites for the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFavorites = async (req, res) => {
  try {
    const favorites = await GoogleDriveFavorite.find({ user: req.user.id })
      .sort({ createdAt: -1 });

    res.json(favorites);
  } catch (error) {
    console.error('Error fetching favorites:', error);
    res.status(500).json({ message: 'Error fetching favorites', error: error.message });
  }
};

/**
 * Check if a file is favorited by the current user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkFavorite = async (req, res) => {
  try {
    const { fileId } = req.params;

    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    const favorite = await GoogleDriveFavorite.findOne({
      user: req.user.id,
      fileId
    });

    res.json({ isFavorite: !!favorite });
  } catch (error) {
    console.error('Error checking favorite status:', error);
    res.status(500).json({ message: 'Error checking favorite status', error: error.message });
  }
};
