const UnifiAccessAPI = require('../integrations/unifiAccess/unifiAccessAPI');

// Initialize UniFi Access API with environment variables
let unifiAccessAPI = new UnifiAccessAPI();

/**
 * Get all UniFi Access doors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoors = async (req, res) => {
  try {
    const doors = await unifiAccessAPI.getDoors();
    res.json(doors);
  } catch (error) {
    console.error('Controller error fetching UniFi Access doors:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access doors', error: error.message });
  }
};

/**
 * Get all UniFi Access doors (direct method for internal use)
 * @returns {Promise<Array>} List of doors
 */
exports.getDoorsInternal = async () => {
  try {
    return await unifiAccessAPI.getDoors();
  } catch (error) {
    console.error('Controller error fetching UniFi Access doors:', error);
    // Return an empty array instead of throwing an error
    return [];
  }
};

/**
 * Get UniFi Access door details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoorDetails = async (req, res) => {
  try {
    const doorId = req.params.id;
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    res.json(doorDetails);
  } catch (error) {
    console.error('Controller error fetching UniFi Access door details:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access door details', error: error.message });
  }
};

/**
 * Unlock a door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unlockDoor = async (req, res) => {
  try {
    const doorId = req.params.id;
    const result = await unifiAccessAPI.unlockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unlocking UniFi Access door:', error);
    res.status(500).json({ message: 'Error unlocking UniFi Access door', error: error.message });
  }
};

/**
 * Lock a door - This method has been removed as the lockDoor function does not exist in the UniFi Access API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.lockDoor = async (req, res) => {
  console.error('The lockDoor function does not exist in the UniFi Access API');
  return res.status(400).json({ 
    message: 'The lock door functionality is not supported by the UniFi Access API',
    error: 'UNSUPPORTED_OPERATION'
  });
};

/**
 * Get all UniFi Access access points
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessPoints = async (req, res) => {
  try {
    const accessPoints = await unifiAccessAPI.getAccessPoints();
    res.json(accessPoints);
  } catch (error) {
    console.error('Controller error fetching UniFi Access access points:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access access points', error: error.message });
  }
};

/**
 * Get all UniFi Access users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    const users = await unifiAccessAPI.getUsers();
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching UniFi Access users:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access users', error: error.message });
  }
};

/**
 * Get UniFi Access events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    const events = await unifiAccessAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching UniFi Access events:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access events', error: error.message });
  }
};

/**
 * Get UniFi Access system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    const status = await unifiAccessAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching UniFi Access system status:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access system status', error: error.message });
  }
};

/**
 * Save UniFi Access configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  res.status(400).json({ 
    message: 'UniFi Access configuration is now managed through environment variables. Please set UNIFI_ACCESS_HOST, UNIFI_ACCESS_API_KEY, and optionally UNIFI_ACCESS_PORT in your environment.' 
  });
};

/**
 * Get UniFi Access configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  console.log('UniFi Access getConfig method called');
  
  // Check if environment variables are set
  const host = process.env.UNIFI_ACCESS_HOST;
  const apiKey = process.env.UNIFI_ACCESS_API_KEY;
  const port = process.env.UNIFI_ACCESS_PORT || 443;
  
  if (!host || !apiKey) {
    console.log('UniFi Access configuration not found - missing environment variables');
    return res.status(404).json({ 
      message: 'UniFi Access configuration not found. Please set UNIFI_ACCESS_HOST, UNIFI_ACCESS_API_KEY, and optionally UNIFI_ACCESS_PORT in your environment.',
      error: 'MISSING_ENV_VARS'
    });
  }
  
  // Return configuration status without sensitive information
  console.log('UniFi Access configuration found - returning data');
  res.json({
    host: host,
    port: port,
    configuredAt: new Date(),
    message: 'UniFi Access is configured using environment variables'
  });
};

/**
 * Set up UniFi Access with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  res.status(400).json({ 
    message: 'One-click setup is no longer available. UniFi Access configuration is now managed through environment variables. Please set UNIFI_ACCESS_HOST, UNIFI_ACCESS_API_KEY, and optionally UNIFI_ACCESS_PORT in your environment.',
    environmentVariables: {
      UNIFI_ACCESS_HOST: 'Your UniFi Access host or IP address',
      UNIFI_ACCESS_API_KEY: 'Your UniFi Access API key',
      UNIFI_ACCESS_PORT: 'Your UniFi Access port (default: 443)'
    }
  });
};

/**
 * Get door status
 * @param {string} doorId - Door ID
 * @returns {Promise<Object>} Door status
 */
exports.getDoorStatus = async (doorId) => {
  try {
    // Get door details which includes status information
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    
    // Extract and return relevant status information
    return {
      id: doorDetails.id,
      name: doorDetails.name,
      status: doorDetails.status,
      locked: doorDetails.locked,
      open: doorDetails.open,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Controller error fetching UniFi Access door status for ${doorId}:`, error);
    throw error;
  }
};

/**
 * Get door status (internal method with improved error handling)
 * @param {string} doorId - Door ID
 * @returns {Promise<Object>} Door status
 */
exports.getDoorStatusInternal = async (doorId) => {
  try {
    // Get door details which includes status information
    const doorDetails = await unifiAccessAPI.getDoorDetails(doorId);
    
    // Extract and return relevant status information
    return {
      id: doorDetails.id,
      name: doorDetails.name,
      status: doorDetails.status,
      locked: doorDetails.locked,
      open: doorDetails.open,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Controller error fetching UniFi Access door status for ${doorId}:`, error);
    // Return a default status object instead of throwing an error
    return {
      id: doorId,
      name: 'Unknown Door',
      status: 'Error',
      locked: false,
      open: false,
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
};

/**
 * Get UniFi Access user by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserById = async (req, res) => {
  try {
    const userId = req.params.id;
    
    // Get all users first
    const users = await unifiAccessAPI.getUsers();
    
    // Find the specific user by ID
    const user = users.find(user => user.id === userId);
    
    if (!user) {
      return res.status(404).json({ message: 'UniFi Access user not found', error: 'USER_NOT_FOUND' });
    }
    
    res.json(user);
  } catch (error) {
    console.error('Controller error fetching UniFi Access user by ID:', error);
    res.status(500).json({ message: 'Error fetching UniFi Access user by ID', error: error.message });
  }
};


/**
 * Additional UniFi Access controller endpoints for groups, policies, user details, and door status
 */

// Groups
exports.getGroups = async (req, res) => {
  try {
    const groups = await unifiAccessAPI.getGroups();
    return res.json(groups);
  } catch (error) {
    console.error('Controller error fetching UniFi Access groups:', error);
    return res.status(500).json({ message: 'Error fetching UniFi Access groups', error: error.message });
  }
};

exports.getGroupDetails = async (req, res) => {
  try {
    const groupId = req.params.id;
    const group = await unifiAccessAPI.getGroupDetails(groupId);
    return res.json(group);
  } catch (error) {
    console.error('Controller error fetching UniFi Access group details:', error);
    return res.status(500).json({ message: 'Error fetching UniFi Access group details', error: error.message });
  }
};

exports.createGroup = async (req, res) => {
  try {
    const groupData = req.body;
    if (!groupData || Object.keys(groupData).length === 0) {
      return res.status(400).json({ message: 'Group data is required', error: 'MISSING_BODY' });
    }
    const created = await unifiAccessAPI.createGroup(groupData);
    return res.json(created);
  } catch (error) {
    console.error('Controller error creating UniFi Access group:', error);
    return res.status(500).json({ message: 'Error creating UniFi Access group', error: error.message });
  }
};

exports.updateGroup = async (req, res) => {
  try {
    const groupId = req.params.id;
    const groupData = req.body;
    if (!groupId) {
      return res.status(400).json({ message: 'Group ID is required', error: 'MISSING_ID' });
    }
    const updated = await unifiAccessAPI.updateGroup(groupId, groupData || {});
    return res.json(updated);
  } catch (error) {
    console.error('Controller error updating UniFi Access group:', error);
    return res.status(500).json({ message: 'Error updating UniFi Access group', error: error.message });
  }
};

exports.deleteGroup = async (req, res) => {
  try {
    const groupId = req.params.id;
    if (!groupId) {
      return res.status(400).json({ message: 'Group ID is required', error: 'MISSING_ID' });
    }
    const deleted = await unifiAccessAPI.deleteGroup(groupId);
    return res.json(deleted);
  } catch (error) {
    console.error('Controller error deleting UniFi Access group:', error);
    return res.status(500).json({ message: 'Error deleting UniFi Access group', error: error.message });
  }
};

// Policies
exports.getPolicies = async (req, res) => {
  try {
    const policies = await unifiAccessAPI.getPolicies();
    return res.json(policies);
  } catch (error) {
    console.error('Controller error fetching UniFi Access policies:', error);
    return res.status(500).json({ message: 'Error fetching UniFi Access policies', error: error.message });
  }
};

exports.getPolicyDetails = async (req, res) => {
  try {
    const policyId = req.params.id;
    const policy = await unifiAccessAPI.getPolicyDetails(policyId);
    return res.json(policy);
  } catch (error) {
    console.error('Controller error fetching UniFi Access policy details:', error);
    return res.status(500).json({ message: 'Error fetching UniFi Access policy details', error: error.message });
  }
};

exports.createPolicy = async (req, res) => {
  try {
    const policyData = req.body;
    if (!policyData || Object.keys(policyData).length === 0) {
      return res.status(400).json({ message: 'Policy data is required', error: 'MISSING_BODY' });
    }
    const created = await unifiAccessAPI.createPolicy(policyData);
    return res.json(created);
  } catch (error) {
    console.error('Controller error creating UniFi Access policy:', error);
    return res.status(500).json({ message: 'Error creating UniFi Access policy', error: error.message });
  }
};

exports.updatePolicy = async (req, res) => {
  try {
    const policyId = req.params.id;
    const policyData = req.body;
    if (!policyId) {
      return res.status(400).json({ message: 'Policy ID is required', error: 'MISSING_ID' });
    }
    const updated = await unifiAccessAPI.updatePolicy(policyId, policyData || {});
    return res.json(updated);
  } catch (error) {
    console.error('Controller error updating UniFi Access policy:', error);
    return res.status(500).json({ message: 'Error updating UniFi Access policy', error: error.message });
  }
};

exports.deletePolicy = async (req, res) => {
  try {
    const policyId = req.params.id;
    if (!policyId) {
      return res.status(400).json({ message: 'Policy ID is required', error: 'MISSING_ID' });
    }
    const deleted = await unifiAccessAPI.deletePolicy(policyId);
    return res.json(deleted);
  } catch (error) {
    console.error('Controller error deleting UniFi Access policy:', error);
    return res.status(500).json({ message: 'Error deleting UniFi Access policy', error: error.message });
  }
};

exports.assignPolicyToDoor = async (req, res) => {
  try {
    const policyId = req.params.id;
    const doorId = req.params.doorId;
    if (!policyId || !doorId) {
      return res.status(400).json({ message: 'Policy ID and Door ID are required', error: 'MISSING_PARAMS' });
    }
    const result = await unifiAccessAPI.assignPolicyToDoor(policyId, doorId);
    return res.json(result);
  } catch (error) {
    console.error('Controller error assigning policy to door:', error);
    return res.status(500).json({ message: 'Error assigning policy to door', error: error.message });
  }
};

exports.removePolicyFromDoor = async (req, res) => {
  try {
    const policyId = req.params.id;
    const doorId = req.params.doorId;
    if (!policyId || !doorId) {
      return res.status(400).json({ message: 'Policy ID and Door ID are required', error: 'MISSING_PARAMS' });
    }
    const result = await unifiAccessAPI.removePolicyFromDoor(policyId, doorId);
    return res.json(result);
  } catch (error) {
    console.error('Controller error removing policy from door:', error);
    return res.status(500).json({ message: 'Error removing policy from door', error: error.message });
  }
};

// Door status (Express handler)
exports.getDoorStatusHandler = async (req, res) => {
  try {
    const doorId = req.params.id;
    const status = await exports.getDoorStatus(doorId);
    return res.json(status);
  } catch (error) {
    console.error('Controller error fetching UniFi Access door status:', error);
    return res.status(500).json({ message: 'Error fetching UniFi Access door status', error: error.message });
  }
};

// Schedules (not implemented in UniFi Access API wrapper yet) - return empty list for compatibility
exports.getSchedules = async (req, res) => {
  try {
    return res.json([]);
  } catch (error) {
    return res.json([]);
  }
};

// Access Levels (map to UniFi Access policies)
exports.getAccessLevels = async (req, res) => {
  try {
    // Map UniFi Access Policies to Access Levels for unified UI
    const policies = await unifiAccessAPI.getPolicies();
    // Normalize to a generic access-level shape while preserving original fields
    const accessLevels = Array.isArray(policies) ? policies.map((p) => ({
      id: p.id || p._id || p.uuid || p.policy_id || p.name,
      name: p.name || p.display_name || p.policyName || p.full_name || 'Unnamed Policy',
      type: 'policy',
      raw: p
    })) : [];
    return res.json(accessLevels);
  } catch (error) {
    console.error('Controller error fetching UniFi Access access levels:', error);
    return res.status(500).json({ message: 'Error fetching UniFi Access access levels', error: error.message });
  }
};

// Momentary unlock (not supported) - provide a clear response
exports.momentaryUnlockDoor = async (req, res) => {
  return res.status(400).json({ 
    message: 'The momentary unlock functionality is not supported by the UniFi Access API',
    error: 'UNSUPPORTED_OPERATION'
  });
};
