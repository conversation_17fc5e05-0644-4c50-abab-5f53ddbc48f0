const TicketTag = require('../../models/TicketTag');
const { validationResult } = require('express-validator');

/**
 * Ticket Tag Controller
 * Handles API operations for ticket tags
 */
const ticketTagController = {
  /**
   * Get all tags
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with tags
   */
  getAllTags: async (req, res) => {
    try {
      const { 
        includeInactive = false, 
        search,
        sortBy = 'ticketCount',
        sortOrder = 'desc',
        limit = 100
      } = req.query;
      
      const filter = {};
      
      // Filter by active status
      if (includeInactive !== 'true') {
        filter.isActive = true;
      }
      
      // Text search
      if (search) {
        filter.$text = { $search: search };
      }
      
      // Build sort object
      const sort = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      
      const tags = await TicketTag.find(filter)
        .sort(sort)
        .limit(parseInt(limit))
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email')
        .populate('relatedTags.tag', 'name displayName');
      
      res.json(tags);
    } catch (err) {
      console.error('Error getting tags:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Get tag by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with tag
   */
  getTagById: async (req, res) => {
    try {
      const tag = await TicketTag.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('updatedBy', 'name email')
        .populate('relatedTags.tag', 'name displayName');
      
      if (!tag) {
        return res.status(404).json({ message: 'Tag not found' });
      }
      
      res.json(tag);
    } catch (err) {
      console.error('Error getting tag:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Tag not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Create a new tag
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created tag
   */
  createTag: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check permissions
    if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
      return res.status(403).json({ message: 'Not authorized to create tags' });
    }

    try {
      const {
        name,
        displayName,
        description,
        color,
        backgroundColor,
        autoRules,
        relatedTags,
        isSystemTag = false
      } = req.body;

      // Convert name to lowercase and check for duplicates
      const normalizedName = name.toLowerCase().trim();
      const existingTag = await TicketTag.findOne({ name: normalizedName });
      
      if (existingTag) {
        return res.status(400).json({ message: 'Tag name already exists' });
      }

      const tag = new TicketTag({
        name: normalizedName,
        displayName: displayName || name,
        description,
        color,
        backgroundColor,
        autoRules,
        relatedTags,
        isSystemTag,
        createdBy: req.user.id
      });

      await tag.save();
      
      // Populate references for response
      await tag.populate('createdBy', 'name email');
      if (tag.relatedTags && tag.relatedTags.length > 0) {
        await tag.populate('relatedTags.tag', 'name displayName');
      }
      
      res.status(201).json(tag);
    } catch (err) {
      console.error('Error creating tag:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Update a tag
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated tag
   */
  updateTag: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check permissions
    if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
      return res.status(403).json({ message: 'Not authorized to update tags' });
    }

    try {
      const tag = await TicketTag.findById(req.params.id);
      
      if (!tag) {
        return res.status(404).json({ message: 'Tag not found' });
      }
      
      // Prevent editing system tags unless admin
      if (tag.isSystemTag && !req.user.roles.includes('admin')) {
        return res.status(403).json({ message: 'Cannot edit system tags' });
      }
      
      const {
        name,
        displayName,
        description,
        color,
        backgroundColor,
        autoRules,
        relatedTags,
        isActive
      } = req.body;

      // Check for duplicate name (excluding current tag)
      if (name && name.toLowerCase() !== tag.name) {
        const normalizedName = name.toLowerCase().trim();
        const existingTag = await TicketTag.findOne({ 
          name: normalizedName, 
          _id: { $ne: tag._id } 
        });
        if (existingTag) {
          return res.status(400).json({ message: 'Tag name already exists' });
        }
        tag.name = normalizedName;
      }

      // Update fields
      if (displayName !== undefined) tag.displayName = displayName;
      if (description !== undefined) tag.description = description;
      if (color !== undefined) tag.color = color;
      if (backgroundColor !== undefined) tag.backgroundColor = backgroundColor;
      if (autoRules !== undefined) tag.autoRules = autoRules;
      if (relatedTags !== undefined) tag.relatedTags = relatedTags;
      if (isActive !== undefined) tag.isActive = isActive;
      
      tag.updatedBy = req.user.id;

      await tag.save();
      
      // Populate references for response
      await tag.populate('createdBy', 'name email');
      await tag.populate('updatedBy', 'name email');
      if (tag.relatedTags && tag.relatedTags.length > 0) {
        await tag.populate('relatedTags.tag', 'name displayName');
      }
      
      res.json(tag);
    } catch (err) {
      console.error('Error updating tag:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Tag not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Delete a tag
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message
   */
  deleteTag: async (req, res) => {
    // Check permissions
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'Not authorized to delete tags' });
    }

    try {
      const tag = await TicketTag.findById(req.params.id);
      
      if (!tag) {
        return res.status(404).json({ message: 'Tag not found' });
      }
      
      // Prevent deleting system tags
      if (tag.isSystemTag) {
        return res.status(400).json({ message: 'Cannot delete system tags' });
      }
      
      // Check if tag is used by tickets
      if (tag.ticketCount > 0) {
        return res.status(400).json({ 
          message: `Cannot delete tag. It is used by ${tag.ticketCount} ticket(s). Please remove from tickets first.` 
        });
      }
      
      await tag.deleteOne();
      
      res.json({ message: 'Tag deleted successfully' });
    } catch (err) {
      console.error('Error deleting tag:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Tag not found' });
      }
      
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Get popular tags
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with popular tags
   */
  getPopularTags: async (req, res) => {
    try {
      const { limit = 20 } = req.query;
      
      const tags = await TicketTag.find({ 
        isActive: true,
        ticketCount: { $gt: 0 }
      })
        .sort({ ticketCount: -1 })
        .limit(parseInt(limit))
        .select('name displayName color backgroundColor ticketCount');
      
      res.json(tags);
    } catch (err) {
      console.error('Error getting popular tags:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Update tag usage counts
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with update results
   */
  updateTagCounts: async (req, res) => {
    // Check permissions
    if (!req.user.roles.includes('admin')) {
      return res.status(403).json({ message: 'Not authorized to update tag counts' });
    }

    try {
      const Ticket = require('../../models/Ticket');
      
      // Get all tags
      const tags = await TicketTag.find();
      let updatedCount = 0;
      
      // Update count for each tag
      for (const tag of tags) {
        const ticketCount = await Ticket.countDocuments({ tags: tag.name });
        if (tag.ticketCount !== ticketCount) {
          tag.ticketCount = ticketCount;
          await tag.save();
          updatedCount++;
        }
      }
      
      res.json({ 
        message: `Updated ${updatedCount} tag counts`,
        totalTags: tags.length,
        updatedTags: updatedCount
      });
    } catch (err) {
      console.error('Error updating tag counts:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  },

  /**
   * Search tags by name
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with matching tags
   */
  searchTags: async (req, res) => {
    try {
      const { q, limit = 10 } = req.query;
      
      if (!q || q.trim().length === 0) {
        return res.json([]);
      }
      
      const searchTerm = q.trim();
      const regex = new RegExp(searchTerm, 'i');
      
      const tags = await TicketTag.find({
        isActive: true,
        $or: [
          { name: regex },
          { displayName: regex }
        ]
      })
        .sort({ ticketCount: -1, name: 1 })
        .limit(parseInt(limit))
        .select('name displayName color backgroundColor ticketCount');
      
      res.json(tags);
    } catch (err) {
      console.error('Error searching tags:', err.message);
      res.status(500).json({ message: 'Server Error', error: err.message });
    }
  }
};

module.exports = ticketTagController;