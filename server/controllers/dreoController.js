const DreoAPI = require('../integrations/dreo/dreoAPI');
const DreoConfig = require('../../models/DreoConfig');
const jwt = require('jsonwebtoken');

// Initialize Dreo API with empty credentials (will be updated when needed)
let dreoAPI = new DreoAPI('', '');

// Store WebSocket connections and device listeners
const webSockets = new Set();
const deviceListeners = new Map(); // Map of deviceId -> Set of WebSocket connections

// Helper function to get the latest configuration
const getLatestConfig = async (req) => {
  try {
    // Only use environment variables for authentication
    const envEmail = process.env.DREO_EMAIL || process.env.DREO_USERNAME || ''; // Support both new and old env var names
    const envPassword = process.env.DREO_PASSWORD || '';
    
    // If environment variables are set, use them
    if (envEmail && envPassword) {
      dreoAPI = new DreoAPI(envEmail, envPassword);
      return {
        email: envEmail,
        password: envPassword,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, log an error
    console.error('Dreo environment variables not set. Please configure DREO_USERNAME and DREO_PASSWORD in environment variables.');
    return null;
  } catch (error) {
    console.error('Error fetching Dreo configuration:', error);
    throw error;
  }
};

/**
 * Get all Dreo devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    await getLatestConfig(req);
    const devices = await dreoAPI.getDevices();
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Dreo devices:', error);
    res.status(500).json({ message: 'Error fetching Dreo devices', error: error.message });
  }
};

/**
 * Get Dreo device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const deviceDetails = await dreoAPI.getDeviceDetails(deviceId);
    res.json(deviceDetails);
  } catch (error) {
    console.error('Controller error fetching Dreo device details:', error);
    res.status(500).json({ message: 'Error fetching Dreo device details', error: error.message });
  }
};

/**
 * Set device power state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setPower = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { power } = req.body;

    if (power === undefined) {
      return res.status(400).json({ message: 'Power state is required' });
    }

    const result = await dreoAPI.setPower(deviceId, power);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device power:', error);
    res.status(500).json({ message: 'Error setting Dreo device power', error: error.message });
  }
};

/**
 * Set device temperature
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setTemperature = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { temperature } = req.body;

    if (temperature === undefined) {
      return res.status(400).json({ message: 'Temperature is required' });
    }

    const result = await dreoAPI.setTemperature(deviceId, temperature);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device temperature:', error);
    res.status(500).json({ message: 'Error setting Dreo device temperature', error: error.message });
  }
};

/**
 * Set device fan speed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setFanSpeed = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { speed } = req.body;

    if (speed === undefined) {
      return res.status(400).json({ message: 'Fan speed is required' });
    }

    const result = await dreoAPI.setFanSpeed(deviceId, speed);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device fan speed:', error);
    res.status(500).json({ message: 'Error setting Dreo device fan speed', error: error.message });
  }
};

/**
 * Set device mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setMode = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { mode } = req.body;

    if (mode === undefined) {
      return res.status(400).json({ message: 'Mode is required' });
    }

    const result = await dreoAPI.setMode(deviceId, mode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Dreo device mode:', error);
    res.status(500).json({ message: 'Error setting Dreo device mode', error: error.message });
  }
};

/**
 * Save Dreo configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Dreo configuration request:', error);
    res.status(500).json({ message: 'Error handling Dreo configuration request', error: error.message });
  }
};

/**
 * Get Dreo configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Only use environment variables for authentication
    const envEmail = process.env.DREO_EMAIL || process.env.DREO_USERNAME || ''; // Support both new and old env var names
    const envPassword = process.env.DREO_PASSWORD || '';
    
    // If environment variables are set, use them
    if (envEmail && envPassword) {
      // Don't send the actual password back to the client for security
      res.json({
        username: envEmail,
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, return 404
    return res.status(404).json({ 
      message: 'Dreo configuration not found. Please contact your administrator to set up the required environment variables.' 
    });
  } catch (error) {
    console.error('Error fetching Dreo configuration:', error);
    res.status(500).json({ message: 'Error fetching Dreo configuration', error: error.message });
  }
};

/**
 * Set up Dreo with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Dreo one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Dreo one-click setup request', 
      error: error.message 
    });
  }
};

/**
 * Control device with custom commands
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlDevice = async (req, res) => {
  try {
    await getLatestConfig(req);
    const deviceId = req.params.id;
    const { command, value } = req.body;

    if (!command) {
      return res.status(400).json({ message: 'Command is required' });
    }

    // Handle specific commands based on what's available in the API
    let result;
    switch (command) {
      case 'power':
        result = await dreoAPI.setPower(deviceId, value);
        break;
      case 'temperature':
        result = await dreoAPI.setTemperature(deviceId, value);
        break;
      case 'fan-speed':
        result = await dreoAPI.setFanSpeed(deviceId, value);
        break;
      case 'mode':
        result = await dreoAPI.setMode(deviceId, value);
        break;
      case 'oscillation':
        result = await dreoAPI.setOscillation(deviceId, value);
        break;
      case 'oscillation-angle':
        result = await dreoAPI.setOscillationAngle(deviceId, value);
        break;
      case 'child-lock':
        result = await dreoAPI.setChildLock(deviceId, value);
        break;
      case 'light':
        result = await dreoAPI.setLight(deviceId, value);
        break;
      case 'brightness':
        result = await dreoAPI.setLightBrightness(deviceId, value);
        break;
      case 'temperature-unit':
        result = await dreoAPI.setTemperatureUnit(deviceId, value);
        break;
      default:
        return res.status(400).json({ message: `Unsupported command: ${command}` });
    }

    // After successful control, get the updated status and notify listeners
    try {
      const status = await dreoAPI.getDeviceDetails(deviceId);
      notifyDeviceListeners(deviceId, status);
    } catch (err) {
      console.error(`Error getting updated status for device ${deviceId}:`, err);
      // Continue even if we can't get the updated status
    }

    res.json(result);
  } catch (error) {
    console.error('Controller error controlling Dreo device:', error);
    res.status(500).json({ message: 'Error controlling Dreo device', error: error.message });
  }
};

/**
 * Register a client for real-time updates for a specific device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.registerDeviceListener = async (req, res) => {
  try {
    const deviceId = req.params.id;
    
    // Create a Set for this device if it doesn't exist
    if (!deviceListeners.has(deviceId)) {
      deviceListeners.set(deviceId, new Set());
      
      // Register a listener with the Dreo API for this device
      dreoAPI.registerDeviceListener(deviceId, (status) => {
        notifyDeviceListeners(deviceId, status);
      });
    }
    
    res.json({ success: true, message: `Registered for updates on device ${deviceId}` });
  } catch (error) {
    console.error(`Error registering device listener for ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error registering device listener', error: error.message });
  }
};

/**
 * Unregister a client from real-time updates for a specific device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unregisterDeviceListener = async (req, res) => {
  try {
    const deviceId = req.params.id;
    
    // If there are no more listeners for this device, unregister from the Dreo API
    if (deviceListeners.has(deviceId) && deviceListeners.get(deviceId).size === 0) {
      dreoAPI.unregisterDeviceListener(deviceId);
      deviceListeners.delete(deviceId);
    }
    
    res.json({ success: true, message: `Unregistered from updates on device ${deviceId}` });
  } catch (error) {
    console.error(`Error unregistering device listener for ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error unregistering device listener', error: error.message });
  }
};

/**
 * Handle WebSocket connection for real-time device updates
 * @param {WebSocket} ws - WebSocket connection
 * @param {Object} req - Express request object
 */
exports.handleWebSocket = async (ws, req) => {
  try {
    // Add the WebSocket to the set of active connections
    webSockets.add(ws);
    
    // Set up event handlers
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        
        // Handle authentication
        if (data.type === 'auth' && data.token) {
          // Verify the token
          jwt.verify(data.token, process.env.JWT_SECRET, (err, decoded) => {
            if (err) {
              ws.send(JSON.stringify({ type: 'error', message: 'Authentication failed' }));
              ws.close();
            } else {
              // Store user info on the WebSocket
              ws.userId = decoded.id;
              ws.send(JSON.stringify({ type: 'auth-success' }));
            }
          });
        }
        
        // Handle device registration
        if (data.type === 'register-device' && data.deviceId) {
          const deviceId = data.deviceId;
          
          // Create a Set for this device if it doesn't exist
          if (!deviceListeners.has(deviceId)) {
            deviceListeners.set(deviceId, new Set());
            
            // Register a listener with the Dreo API for this device
            dreoAPI.registerDeviceListener(deviceId, (status) => {
              notifyDeviceListeners(deviceId, status);
            });
          }
          
          // Add this WebSocket to the device's listeners
          deviceListeners.get(deviceId).add(ws);
          
          // Store the device ID on the WebSocket for cleanup
          if (!ws.devices) {
            ws.devices = new Set();
          }
          ws.devices.add(deviceId);
          
          ws.send(JSON.stringify({ type: 'register-success', deviceId }));
        }
        
        // Handle device unregistration
        if (data.type === 'unregister-device' && data.deviceId) {
          const deviceId = data.deviceId;
          
          // Remove this WebSocket from the device's listeners
          if (deviceListeners.has(deviceId)) {
            deviceListeners.get(deviceId).delete(ws);
            
            // If there are no more listeners for this device, unregister from the Dreo API
            if (deviceListeners.get(deviceId).size === 0) {
              dreoAPI.unregisterDeviceListener(deviceId);
              deviceListeners.delete(deviceId);
            }
          }
          
          // Remove the device ID from the WebSocket
          if (ws.devices) {
            ws.devices.delete(deviceId);
          }
          
          ws.send(JSON.stringify({ type: 'unregister-success', deviceId }));
        }
      } catch (err) {
        console.error('Error processing WebSocket message:', err);
      }
    });
    
    ws.on('close', () => {
      // Remove this WebSocket from all device listeners
      if (ws.devices) {
        for (const deviceId of ws.devices) {
          if (deviceListeners.has(deviceId)) {
            deviceListeners.get(deviceId).delete(ws);
            
            // If there are no more listeners for this device, unregister from the Dreo API
            if (deviceListeners.get(deviceId).size === 0) {
              dreoAPI.unregisterDeviceListener(deviceId);
              deviceListeners.delete(deviceId);
            }
          }
        }
      }
      
      // Remove the WebSocket from the set of active connections
      webSockets.delete(ws);
    });
    
    // Send a welcome message
    ws.send(JSON.stringify({ type: 'welcome', message: 'Connected to Dreo WebSocket server' }));
  } catch (error) {
    console.error('Error handling WebSocket connection:', error);
    ws.close();
  }
};

/**
 * Notify all listeners for a specific device about a status update
 * @param {string} deviceId - Device ID
 * @param {Object} status - Device status
 */
function notifyDeviceListeners(deviceId, status) {
  if (deviceListeners.has(deviceId)) {
    const listeners = deviceListeners.get(deviceId);
    const message = JSON.stringify({
      type: 'device-update',
      deviceId,
      status
    });
    
    for (const ws of listeners) {
      if (ws.readyState === 1) { // OPEN
        ws.send(message);
      }
    }
  }
}
