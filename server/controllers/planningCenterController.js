const PlanningCenterAPI = require('../integrations/planningCenter/planningCenterAPI');
const PlanningCenterConfig = require('../../models/PlanningCenterConfig');
require('dotenv').config();

// Initialize Planning Center API (will use environment variables by default)
let planningCenterAPI = new PlanningCenterAPI();

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    // We're now using environment variables for authentication,
    // so we don't need to fetch configuration from the database
    // This function is kept for backward compatibility with existing code
    return {
      isConfigured: true,
      updatedAt: new Date()
    };
  } catch (error) {
    console.error('Error with Planning Center configuration:', error);
    throw error;
  }
};

/**
 * Get all Planning Center applications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getApplications = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    const applications = await planningCenterAPI.getApplications();
    res.json(applications);
  } catch (error) {
    console.error('Controller error fetching Planning Center applications:', error);
    res.status(500).json({ message: 'Error fetching Planning Center applications', error: error.message });
  }
};

/**
 * Get Planning Center events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    // Process query parameters
    const queryParams = { ...req.query };
    
    // Handle common query parameters according to Planning Center API specs
    
    // Include related resources if not specified
    // This ensures we get related data like tags, resources, and rooms
    if (!queryParams.include) {
      queryParams.include = 'tags,resources,rooms';
    }
    
    // Handle date filtering
    // If specific date parameters are provided, use them
    // Otherwise, default behavior is handled in the API layer (events starting from today)
    
    // Handle pagination
    // Planning Center uses per_page and page parameters for pagination
    if (!queryParams.per_page) {
      queryParams.per_page = 25; // Default page size
    }
    
    // Handle sorting
    // Planning Center uses order parameter for sorting
    // Format: order=field_name or order=-field_name (for descending)
    if (!queryParams.order) {
      queryParams.order = 'starts_at'; // Default sort by start date
    }
    
    const eventsResponse = await planningCenterAPI.getEvents(queryParams);
    
    // Format the events data for human-readable output
    const formattedEvents = formatEventsData(eventsResponse);
    
    res.json(formattedEvents);
  } catch (error) {
    console.error('Controller error fetching Planning Center events:', error);
    res.status(500).json({ message: 'Error fetching Planning Center events', error: error.message });
  }
};

/**
 * Format Planning Center events data for human-readable output
 * @param {Object} eventsResponse - Raw response from Planning Center API
 * @returns {Object} Formatted events data
 */
const formatEventsData = (eventsResponse) => {
  // Check if we have valid data
  if (!eventsResponse || !eventsResponse.data || !Array.isArray(eventsResponse.data)) {
    return { events: [] };
  }

  // Map the raw data to a more readable format
  const formattedEvents = eventsResponse.data.map(event => {
    const attributes = event.attributes || {};
    const relationships = event.relationships || {};
    
    // Extract dates and format them
    const startDate = attributes.starts_at ? new Date(attributes.starts_at) : null;
    const endDate = attributes.ends_at ? new Date(attributes.ends_at) : null;
    
    // Format dates for display
    const formatDate = (date) => {
      if (!date) return '';
      return date.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };
    
    // Initialize additional data object for linked resources
    const additionalData = {};
    
    // Process linked data if it exists (from included resources)
    if (event.linked_data) {
      // Store the linked data in the additionalData object
      additionalData.linked_data = event.linked_data;
      
      // Extract specific data from linked resources if needed
      // For example, if event tags are included:
      if (event.linked_data.tags && Array.isArray(event.linked_data.tags)) {
        additionalData.tags = event.linked_data.tags.map(tag => ({
          id: tag.id,
          name: tag.attributes?.name,
          color: tag.attributes?.color
        })).filter(tag => tag.name);
      }
      
      // If event resources are included:
      if (event.linked_data.resources && Array.isArray(event.linked_data.resources)) {
        additionalData.resources = event.linked_data.resources.map(resource => ({
          id: resource.id,
          name: resource.attributes?.name,
          kind: resource.attributes?.kind,
          description: resource.attributes?.description
        })).filter(resource => resource.name);
      }
      
      // If event rooms are included:
      if (event.linked_data.rooms && Array.isArray(event.linked_data.rooms)) {
        additionalData.rooms = event.linked_data.rooms.map(room => ({
          id: room.id,
          name: room.attributes?.name,
          description: room.attributes?.description,
          capacity: room.attributes?.capacity
        })).filter(room => room.name);
      }
    }
    
    return {
      id: event.id,
      title: attributes.name || 'Untitled Event',
      description: attributes.description || '',
      location: attributes.location || '',
      startDate: startDate ? startDate.toISOString() : null,
      endDate: endDate ? endDate.toISOString() : null,
      formattedStartDate: formatDate(startDate),
      formattedEndDate: formatDate(endDate),
      status: attributes.status || '',
      visibility: attributes.visibility || '',
      createdAt: attributes.created_at || '',
      updatedAt: attributes.updated_at || '',
      url: attributes.url || '',
      details: {
        isRecurring: !!attributes.recurring,
        isAllDay: !!attributes.all_day,
        attendeesCount: attributes.attendees_count || 0,
        organizerName: attributes.organizer_name || '',
        organizerEmail: attributes.organizer_email || '',
      },
      // Include additional data from included resources if available
      ...(Object.keys(additionalData).length > 0 ? { additionalData } : {}),
      // Include the original data for reference if needed
      rawData: event
    };
  });

  return {
    events: formattedEvents,
    meta: eventsResponse.meta || {},
    links: eventsResponse.links || {}
  };
};

/**
 * Get Planning Center people
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPeople = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    // Pass query parameters to the API, which will handle the include parameter
    const people = await planningCenterAPI.getPeople(req.query);
    
    // The API now automatically processes included data if it exists
    res.json(people);
  } catch (error) {
    console.error('Controller error fetching Planning Center people:', error);
    res.status(500).json({ message: 'Error fetching Planning Center people', error: error.message });
  }
};

/**
 * Get a single Planning Center person by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPersonById = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    const personId = req.params.id;
    
    if (!personId) {
      return res.status(400).json({ message: 'Person ID is required' });
    }

    const personResponse = await planningCenterAPI.getPersonById(personId, req.query);
    
    // If the response contains a data object, format it for frontend consumption
    if (personResponse.data) {
      // For a single person (not in an array)
      if (!Array.isArray(personResponse.data)) {
        const attributes = personResponse.data.attributes || {};
        
        // Create a formatted person object with all the details
        const formattedPerson = {
          id: personResponse.data.id,
          firstName: attributes.first_name || '',
          lastName: attributes.last_name || '',
          name: `${attributes.first_name || ''} ${attributes.last_name || ''}`.trim(),
          email: attributes.email || '',
          phoneNumber: attributes.phone_number || '',
          avatar: attributes.avatar || '',
          gender: attributes.gender || '',
          birthdate: attributes.birthdate || '',
          anniversary: attributes.anniversary || '',
          status: attributes.status || '',
          createdAt: attributes.created_at || '',
          updatedAt: attributes.updated_at || '',
          
          // Include any linked data that was processed
          linkedData: personResponse.data.linked_data || {},
          
          // Extract specific data from linked resources for easier access
          contactDetails: {
            emails: [],
            phoneNumbers: [],
            addresses: []
          },
          
          // Include the original data for reference if needed
          rawData: personResponse.data
        };
        
        // Process linked data if it exists
        if (personResponse.data.linked_data) {
          // Process emails
          if (personResponse.data.linked_data.emails && Array.isArray(personResponse.data.linked_data.emails)) {
            formattedPerson.contactDetails.emails = personResponse.data.linked_data.emails.map(email => ({
              id: email.id,
              address: email.attributes?.address || '',
              location: email.attributes?.location || '',
              primary: email.attributes?.primary || false
            })).filter(email => email.address);
          }
          
          // Process phone numbers
          if (personResponse.data.linked_data.phone_numbers && Array.isArray(personResponse.data.linked_data.phone_numbers)) {
            formattedPerson.contactDetails.phoneNumbers = personResponse.data.linked_data.phone_numbers.map(phone => ({
              id: phone.id,
              number: phone.attributes?.number || '',
              location: phone.attributes?.location || '',
              primary: phone.attributes?.primary || false
            })).filter(phone => phone.number);
          }
          
          // Process addresses
          if (personResponse.data.linked_data.addresses && Array.isArray(personResponse.data.linked_data.addresses)) {
            formattedPerson.contactDetails.addresses = personResponse.data.linked_data.addresses.map(address => ({
              id: address.id,
              street: address.attributes?.street || '',
              city: address.attributes?.city || '',
              state: address.attributes?.state || '',
              zip: address.attributes?.zip || '',
              location: address.attributes?.location || '',
              primary: address.attributes?.primary || false
            })).filter(address => address.street || address.city);
          }
          
          // Process households if included
          if (personResponse.data.linked_data.households && Array.isArray(personResponse.data.linked_data.households)) {
            formattedPerson.households = personResponse.data.linked_data.households.map(household => ({
              id: household.id,
              name: household.attributes?.name || '',
              primaryContactName: household.attributes?.primary_contact_name || '',
              memberCount: household.attributes?.member_count || 0
            })).filter(household => household.name);
          }
        }
        
        return res.json({
          person: formattedPerson,
          meta: personResponse.meta || {},
          links: personResponse.links || {}
        });
      }
      
      // If it's an array of people (unlikely for a single person endpoint, but handling just in case)
      if (Array.isArray(personResponse.data) && personResponse.data.length > 0) {
        // Format the first person in the array
        const person = personResponse.data[0];
        const attributes = person.attributes || {};
        
        const formattedPerson = {
          id: person.id,
          firstName: attributes.first_name || '',
          lastName: attributes.last_name || '',
          name: `${attributes.first_name || ''} ${attributes.last_name || ''}`.trim(),
          email: attributes.email || '',
          phoneNumber: attributes.phone_number || '',
          // Include other attributes and linked data as needed
          linkedData: person.linked_data || {},
          rawData: person
        };
        
        return res.json({
          person: formattedPerson,
          meta: personResponse.meta || {},
          links: personResponse.links || {}
        });
      }
    }
    
    // If we get here, return the raw response
    res.json(personResponse);
  } catch (error) {
    console.error(`Controller error fetching Planning Center person with ID ${req.params.id}:`, error);
    res.status(500).json({ 
      message: `Error fetching Planning Center person with ID ${req.params.id}`, 
      error: error.message 
    });
  }
};

/**
 * Get Planning Center people formatted for the People Directory
 * This endpoint supports pagination and search functionality
 * 
 * Query parameters:
 * - page: Current page number (default: 1)
 * - per_page: Number of items per page (default: 25)
 * - search: Search term for filtering people by name
 * - include: Comma-separated list of related resources to include (default: 'emails,phone_numbers')
 * - order: Sorting order (default: 'last_name,first_name')
 * 
 * Response format:
 * {
 *   people: Array of formatted people objects,
 *   pagination: Pagination metadata (currentPage, totalPages, totalCount),
 *   meta: Original metadata from Planning Center API,
 *   links: Navigation links from Planning Center API
 * }
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPeopleForDirectory = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    // Ensure we're requesting any needed included data
    const query = { ...req.query };
    
    // If no include parameter is specified, add default includes for contact information
    // This ensures we get email and phone data for each person
    if (!query.include) {
      query.include = 'emails,phone_numbers';
    }

    // Handle pagination
    // Planning Center uses per_page and page parameters for pagination
    // These parameters are passed directly to the Planning Center API
    if (!query.per_page) {
      query.per_page = 25; // Default page size
    }
    
    // Handle search
    // Planning Center uses where[search_name] parameter for name search
    // We map our 'search' parameter to Planning Center's format
    if (query.search && !query['where[search_name]']) {
      query['where[search_name]'] = query.search;
      // Remove the search parameter as it's not used by the Planning Center API
      delete query.search;
    }
    
    // Handle sorting
    // Planning Center uses order parameter for sorting
    // Format: field_name or -field_name (for descending)
    if (!query.order) {
      query.order = 'last_name,first_name'; // Default sort by name
    }

    // Call the Planning Center API with the prepared query parameters
    const pcPeople = await planningCenterAPI.getPeople(query);

    // Transform Planning Center people to match Person model format
    const formattedPeople = pcPeople.data.map(person => {
      const attributes = person.attributes || {};
      
      // Initialize additional data objects
      const additionalData = {};
      
      // Process linked data if it exists (from included resources)
      if (person.linked_data) {
        // Extract specific data from linked resources in a user-friendly format
        // Process emails
        if (person.linked_data.emails && Array.isArray(person.linked_data.emails)) {
          additionalData.emails = person.linked_data.emails.map(email => ({
            id: email.id,
            address: email.attributes?.address || '',
            location: email.attributes?.location || '',
            primary: email.attributes?.primary || false
          })).filter(email => email.address);
        }
        
        // Process phone numbers
        if (person.linked_data.phone_numbers && Array.isArray(person.linked_data.phone_numbers)) {
          additionalData.phoneNumbers = person.linked_data.phone_numbers.map(phone => ({
            id: phone.id,
            number: phone.attributes?.number || '',
            location: phone.attributes?.location || '',
            primary: phone.attributes?.primary || false
          })).filter(phone => phone.number);
        }
        
        // Process addresses
        if (person.linked_data.addresses && Array.isArray(person.linked_data.addresses)) {
          additionalData.addresses = person.linked_data.addresses.map(address => ({
            id: address.id,
            street: address.attributes?.street || '',
            city: address.attributes?.city || '',
            state: address.attributes?.state || '',
            zip: address.attributes?.zip || '',
            location: address.attributes?.location || '',
            primary: address.attributes?.primary || false
          })).filter(address => address.street || address.city);
        }
        
        // Process households if included
        if (person.linked_data.households && Array.isArray(person.linked_data.households)) {
          additionalData.households = person.linked_data.households.map(household => ({
            id: household.id,
            name: household.attributes?.name || '',
            primaryContactName: household.attributes?.primary_contact_name || '',
            memberCount: household.attributes?.member_count || 0
          })).filter(household => household.name);
        }
      }

      // Use the full_name attribute if available, otherwise construct the name from first_name and last_name
      const personName = attributes.full_name || `${attributes.first_name || ''} ${attributes.last_name || ''}`.trim();
      
      // Log the person's name for debugging
      console.log(`Formatting person: ID=${person.id}, Name=${personName}`);

      return {
        _id: `pc_${person.id}`, // Prefix with 'pc_' to distinguish from local people
        name: personName,
        email: attributes.email || '',
        phoneNumber: attributes.phone_number || '',
        categories: ['Planning Center'], // Add Planning Center as a category
        ministryAssociations: [], // Could be populated if available in the API
        notes: '',
        isActive: true,
        isPlanningCenterPerson: true, // Flag to identify Planning Center people
        planningCenterId: person.id, // Store the original Planning Center ID
        // Format contact details in a consistent structure
        contactDetails: {
          emails: additionalData.emails || [],
          phoneNumbers: additionalData.phoneNumbers || [],
          addresses: additionalData.addresses || []
        },
        // Include households if available
        households: additionalData.households || [],
        // Add any other fields that are available in the Planning Center API
        // and relevant to the Person model
      };
    });

    // Return the formatted people along with pagination metadata
    // This structure allows the client to:
    // 1. Display the people in the table
    // 2. Render pagination controls based on pagination metadata
    // 3. Access original API metadata if needed
    res.json({
      people: formattedPeople,                // Array of formatted people objects
      pagination: pcPeople.pagination || {},  // Pagination info (currentPage, totalPages, totalCount)
      meta: pcPeople.meta || {},              // Original metadata from Planning Center API
      links: pcPeople.links || {}             // Navigation links from Planning Center API
    });
  } catch (error) {
    console.error('Controller error fetching Planning Center people for directory:', error);
    res.status(500).json({ 
      message: 'Error fetching Planning Center people for directory', 
      error: error.message 
    });
  }
};

/**
 * Format Planning Center resources data for human-readable output
 * @param {Object} resourcesResponse - Raw response from Planning Center API
 * @returns {Object} Formatted resources data
 */
const formatResourcesData = (resourcesResponse) => {
  // Check if we have valid data
  if (!resourcesResponse || !resourcesResponse.data || !Array.isArray(resourcesResponse.data)) {
    return { resources: [] };
  }

  // Map the raw data to a more readable format
  const formattedResources = resourcesResponse.data.map(resource => {
    const attributes = resource.attributes || {};
    const relationships = resource.relationships || {};
    
    return {
      id: resource.id,
      name: attributes.name || 'Unnamed Resource',
      description: attributes.description || '',
      kind: attributes.kind || '',
      color: attributes.color || '',
      createdAt: attributes.created_at || '',
      updatedAt: attributes.updated_at || '',
      // Include any linked data that was processed
      linkedData: resource.linked_data || {},
      // Include the original data for reference if needed
      rawData: resource
    };
  });

  return {
    resources: formattedResources,
    meta: resourcesResponse.meta || {},
    links: resourcesResponse.links || {},
    pagination: resourcesResponse.pagination || {}
  };
};

/**
 * Get Planning Center resources
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getResources = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    // Process query parameters
    const queryParams = { ...req.query };
    
    // Handle common query parameters according to Planning Center API specs
    
    // Include related resources if not specified
    // This ensures we get related data like events, rooms, etc.
    if (!queryParams.include) {
      queryParams.include = 'events,rooms';
    }
    
    // Handle pagination
    // Planning Center uses per_page and page parameters for pagination
    if (!queryParams.per_page) {
      queryParams.per_page = 25; // Default page size
    }
    
    // Handle sorting
    // Planning Center uses order parameter for sorting
    // Format: order=field_name or order=-field_name (for descending)
    if (!queryParams.order) {
      queryParams.order = 'name'; // Default sort by name
    }
    
    const resourcesResponse = await planningCenterAPI.getResources(queryParams);
    
    // Format the resources data for human-readable output
    const formattedResources = formatResourcesData(resourcesResponse);
    
    res.json(formattedResources);
  } catch (error) {
    console.error('Controller error fetching Planning Center resources:', error);
    res.status(500).json({ message: 'Error fetching Planning Center resources', error: error.message });
  }
};

/**
 * Get a single Planning Center resource by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getResourceById = async (req, res) => {
  try {
    // Ensure we have the latest configuration
    await getLatestConfig();

    const resourceId = req.params.id;
    
    if (!resourceId) {
      return res.status(400).json({ message: 'Resource ID is required' });
    }

    const resourceResponse = await planningCenterAPI.getResourceById(resourceId, req.query);
    
    // If the response contains a data array, format it
    if (resourceResponse.data && Array.isArray(resourceResponse.data)) {
      const formattedResources = formatResourcesData(resourceResponse);
      return res.json(formattedResources);
    }
    
    // If it's a single resource (not in an array)
    if (resourceResponse.data) {
      const attributes = resourceResponse.data.attributes || {};
      
      const formattedResource = {
        id: resourceResponse.data.id,
        name: attributes.name || 'Unnamed Resource',
        description: attributes.description || '',
        kind: attributes.kind || '',
        color: attributes.color || '',
        createdAt: attributes.created_at || '',
        updatedAt: attributes.updated_at || '',
        // Include any linked data that was processed
        linkedData: resourceResponse.data.linked_data || {},
        // Include the original data for reference if needed
        rawData: resourceResponse.data
      };
      
      return res.json({
        resource: formattedResource,
        meta: resourceResponse.meta || {},
        links: resourceResponse.links || {},
        pagination: resourceResponse.pagination || {}
      });
    }
    
    // If we get here, return the raw response
    res.json(resourceResponse);
  } catch (error) {
    console.error(`Controller error fetching Planning Center resource with ID ${req.params.id}:`, error);
    res.status(500).json({ 
      message: `Error fetching Planning Center resource with ID ${req.params.id}`, 
      error: error.message 
    });
  }
};

/**
 * Save Planning Center configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    // We're now using environment variables for authentication
    // This endpoint is kept for backward compatibility with existing code
    // but it will inform users about the new configuration method

    res.json({ 
      message: 'Planning Center now uses environment variables for authentication. Please update your .env file with PLANNING_CENTER_APPID and PLANNING_CENTER_TOKEN.',
      configMethod: 'environment_variables'
    });
  } catch (error) {
    console.error('Error with Planning Center configuration:', error);
    res.status(500).json({ message: 'Error with Planning Center configuration', error: error.message });
  }
};

/**
 * Get Planning Center configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Check if environment variables are set
    const username = process.env.PLANNING_CENTER_APPID;
    const password = process.env.PLANNING_CENTER_TOKEN;

    const isConfigured = !!(username && password);

    res.json({
      isConfigured,
      configMethod: 'environment_variables',
      message: isConfigured 
        ? 'Planning Center is configured using environment variables.' 
        : 'Planning Center is not configured. Please set PLANNING_CENTER_APPID and PLANNING_CENTER_TOKEN in your .env file.'
    });
  } catch (error) {
    console.error('Error checking Planning Center configuration:', error);
    res.status(500).json({ message: 'Error checking Planning Center configuration', error: error.message });
  }
};

/**
 * Verify Planning Center credentials
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.verifyToken = async (req, res) => {
  try {
    // Check if environment variables are set
    const username = process.env.PLANNING_CENTER_APPID;
    const password = process.env.PLANNING_CENTER_TOKEN;

    if (!username || !password) {
      return res.status(400).json({ 
        success: false,
        message: 'Planning Center configuration is incomplete. Please set PLANNING_CENTER_APPID and PLANNING_CENTER_TOKEN in your .env file.'
      });
    }

    // Try to make a simple API call to verify the credentials work
    await planningCenterAPI.getApplications();

    // If we get here, the credentials are valid
    res.json({ 
      success: true,
      message: 'Planning Center credentials verified successfully' 
    });
  } catch (error) {
    console.error('Error verifying Planning Center credentials:', error);
    res.status(500).json({ 
      success: false,
      message: 'Error verifying Planning Center credentials', 
      error: error.message 
    });
  }
};

/**
 * Set up Planning Center with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    // Planning Center requires username and password in environment variables
    return res.status(400).json({
      message: 'Planning Center integration requires environment variables configuration',
      instructions: [
        'To integrate with Planning Center, you need to:',
        '1. Log in to your Planning Center account',
        '2. Note your username (usually your email) and password',
        '3. Add the following to your .env file:',
        '   PLANNING_CENTER_APPID=your_username',
        '   PLANNING_CENTER_TOKEN=your_password',
        '4. Restart the application to apply the changes'
      ],
      configMethod: 'environment_variables'
    });
  } catch (error) {
    console.error('Error providing Planning Center setup instructions:', error);
    res.status(500).json({ 
      message: 'Error providing Planning Center setup instructions', 
      error: error.message 
    });
  }
};
