const Asset = require('../../models/Asset');
const AssetAuditLog = require('../../models/AssetAuditLog');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

/**
 * Asset Attachment Controller
 * Handles file attachments for assets (documents, images, manuals, etc.)
 */

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join('uploads', 'assets', req.params.id);
    
    // Create directory if it doesn't exist
    fs.mkdirSync(uploadPath, { recursive: true });
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename while preserving extension
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    // Allow common document and image types
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
      'application/zip',
      'application/x-zip-compressed'
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('File type not allowed'), false);
    }
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB limit
  }
});

/**
 * Upload attachment to asset
 */
exports.uploadAttachment = [
  upload.single('file'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { description, category = 'document' } = req.body;

      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      const asset = await Asset.findById(id);
      if (!asset) {
        // Clean up uploaded file if asset not found
        fs.unlink(req.file.path, () => {});
        return res.status(404).json({ message: 'Asset not found' });
      }

      if (asset.isDeleted) {
        // Clean up uploaded file if asset is deleted
        fs.unlink(req.file.path, () => {});
        return res.status(410).json({ message: 'Cannot add attachments to deleted asset' });
      }

      // Create attachment object
      const attachment = {
        name: req.file.originalname,
        filename: req.file.filename,
        url: `/uploads/assets/${id}/${req.file.filename}`,
        type: req.file.mimetype,
        size: req.file.size,
        category: category,
        description: description || '',
        uploadedBy: req.user._id,
        uploadedAt: new Date()
      };

      // Add attachment to asset
      asset.attachments.push(attachment);
      asset._modifiedBy = req.user._id;
      await asset.save();

      // Log the attachment upload
      await AssetAuditLog.logAction({
        asset: asset._id,
        assetTag: asset.assetTag,
        action: 'attachment_uploaded',
        actionDescription: `File "${req.file.originalname}" uploaded to asset`,
        performedBy: req.user._id,
        performedByName: req.user.name,
        performedByEmail: req.user.email,
        category: 'asset_management',
        sessionInfo: {
          sessionId: req.session?.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        },
        metadata: {
          attachmentName: req.file.originalname,
          attachmentSize: req.file.size,
          attachmentType: req.file.mimetype
        }
      });

      // Populate user info for response
      await asset.populate('attachments.uploadedBy', 'name email');
      
      const newAttachment = asset.attachments[asset.attachments.length - 1];
      res.status(201).json(newAttachment);

    } catch (error) {
      console.error('Error uploading attachment:', error);
      
      // Clean up uploaded file on error
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) console.error('Error deleting uploaded file:', err);
        });
      }
      
      res.status(500).json({ message: 'Error uploading attachment', error: error.message });
    }
  }
];

/**
 * Get all attachments for an asset
 */
exports.getAttachments = async (req, res) => {
  try {
    const { id } = req.params;
    const { category } = req.query;

    const asset = await Asset.findById(id)
      .populate('attachments.uploadedBy', 'name email')
      .select('attachments');

    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    let attachments = asset.attachments;
    
    // Filter by category if specified
    if (category) {
      attachments = attachments.filter(att => att.category === category);
    }

    res.json({
      attachments: attachments.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt)),
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments:', error);
    res.status(500).json({ message: 'Error getting attachments', error: error.message });
  }
};

/**
 * Download attachment
 */
exports.downloadAttachment = async (req, res) => {
  try {
    const { id, attachmentId } = req.params;

    const asset = await Asset.findById(id).select('attachments assetTag');
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const attachment = asset.attachments.id(attachmentId);
    if (!attachment) {
      return res.status(404).json({ message: 'Attachment not found' });
    }

    const filePath = path.join('uploads', 'assets', id, attachment.filename);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'File not found on disk' });
    }

    // Log the download
    await AssetAuditLog.logAction({
      asset: asset._id,
      assetTag: asset.assetTag,
      action: 'attachment_downloaded',
      actionDescription: `File "${attachment.name}" downloaded from asset`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'asset_management',
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      },
      metadata: {
        attachmentName: attachment.name,
        attachmentSize: attachment.size
      }
    });

    // Send file
    res.download(filePath, attachment.name);

  } catch (error) {
    console.error('Error downloading attachment:', error);
    res.status(500).json({ message: 'Error downloading attachment', error: error.message });
  }
};

/**
 * Delete attachment
 */
exports.deleteAttachment = async (req, res) => {
  try {
    const { id, attachmentId } = req.params;

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const attachment = asset.attachments.id(attachmentId);
    if (!attachment) {
      return res.status(404).json({ message: 'Attachment not found' });
    }

    // Store attachment info for logging
    const attachmentInfo = {
      name: attachment.name,
      filename: attachment.filename,
      size: attachment.size
    };

    // Remove from database
    asset.attachments.pull({ _id: attachmentId });
    asset._modifiedBy = req.user._id;
    await asset.save();

    // Delete physical file
    const filePath = path.join('uploads', 'assets', id, attachment.filename);
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('Error deleting physical file:', err);
      }
    });

    // Log the deletion
    await AssetAuditLog.logAction({
      asset: asset._id,
      assetTag: asset.assetTag,
      action: 'attachment_deleted',
      actionDescription: `File "${attachmentInfo.name}" deleted from asset`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'asset_management',
      severity: 'warning',
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      },
      metadata: {
        attachmentName: attachmentInfo.name,
        attachmentSize: attachmentInfo.size
      }
    });

    res.json({ message: 'Attachment deleted successfully' });

  } catch (error) {
    console.error('Error deleting attachment:', error);
    res.status(500).json({ message: 'Error deleting attachment', error: error.message });
  }
};

/**
 * Update attachment metadata
 */
exports.updateAttachment = async (req, res) => {
  try {
    const { id, attachmentId } = req.params;
    const { name, description, category } = req.body;

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const attachment = asset.attachments.id(attachmentId);
    if (!attachment) {
      return res.status(404).json({ message: 'Attachment not found' });
    }

    // Update attachment metadata
    if (name) attachment.name = name;
    if (description !== undefined) attachment.description = description;
    if (category) attachment.category = category;

    asset._modifiedBy = req.user._id;
    await asset.save();

    // Log the update
    await AssetAuditLog.logAction({
      asset: asset._id,
      assetTag: asset.assetTag,
      action: 'attachment_updated',
      actionDescription: `Attachment metadata updated for "${attachment.name}"`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'asset_management',
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    await asset.populate('attachments.uploadedBy', 'name email');
    res.json(attachment);

  } catch (error) {
    console.error('Error updating attachment:', error);
    res.status(500).json({ message: 'Error updating attachment', error: error.message });
  }
};

/**
 * Get attachment categories and statistics
 */
exports.getAttachmentStats = async (req, res) => {
  try {
    const { id } = req.params;

    const asset = await Asset.findById(id).select('attachments');
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const stats = {
      totalCount: asset.attachments.length,
      totalSize: 0,
      categories: {},
      fileTypes: {},
      recentUploads: []
    };

    asset.attachments.forEach(attachment => {
      // Calculate total size
      stats.totalSize += attachment.size || 0;

      // Count by category
      const category = attachment.category || 'uncategorized';
      stats.categories[category] = (stats.categories[category] || 0) + 1;

      // Count by file type
      const fileType = attachment.type || 'unknown';
      stats.fileTypes[fileType] = (stats.fileTypes[fileType] || 0) + 1;

      // Track recent uploads (last 7 days)
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      if (attachment.uploadedAt > weekAgo) {
        stats.recentUploads.push({
          name: attachment.name,
          uploadedAt: attachment.uploadedAt,
          size: attachment.size
        });
      }
    });

    // Sort recent uploads by date
    stats.recentUploads.sort((a, b) => new Date(b.uploadedAt) - new Date(a.uploadedAt));

    res.json(stats);

  } catch (error) {
    console.error('Error getting attachment stats:', error);
    res.status(500).json({ message: 'Error getting attachment stats', error: error.message });
  }
};

/**
 * Bulk upload attachments
 */
exports.bulkUpload = [
  upload.array('files', 10), // Allow up to 10 files
  async (req, res) => {
    const results = {
      successful: 0,
      failed: 0,
      errors: [],
      uploaded: []
    };

    try {
      const { id } = req.params;
      const { category = 'document', description = '' } = req.body;

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ message: 'No files uploaded' });
      }

      const asset = await Asset.findById(id);
      if (!asset) {
        // Clean up uploaded files if asset not found
        req.files.forEach(file => {
          fs.unlink(file.path, () => {});
        });
        return res.status(404).json({ message: 'Asset not found' });
      }

      if (asset.isDeleted) {
        // Clean up uploaded files if asset is deleted
        req.files.forEach(file => {
          fs.unlink(file.path, () => {});
        });
        return res.status(410).json({ message: 'Cannot add attachments to deleted asset' });
      }

      // Process each file
      for (const file of req.files) {
        try {
          const attachment = {
            name: file.originalname,
            filename: file.filename,
            url: `/uploads/assets/${id}/${file.filename}`,
            type: file.mimetype,
            size: file.size,
            category: category,
            description: description,
            uploadedBy: req.user._id,
            uploadedAt: new Date()
          };

          asset.attachments.push(attachment);
          results.uploaded.push({
            name: file.originalname,
            size: file.size
          });
          results.successful++;

        } catch (fileError) {
          results.errors.push({
            file: file.originalname,
            error: fileError.message
          });
          results.failed++;
          
          // Clean up failed file
          fs.unlink(file.path, () => {});
        }
      }

      asset._modifiedBy = req.user._id;
      await asset.save();

      // Log the bulk upload
      await AssetAuditLog.logAction({
        asset: asset._id,
        assetTag: asset.assetTag,
        action: 'bulk_upload',
        actionDescription: `Bulk uploaded ${results.successful} files to asset`,
        performedBy: req.user._id,
        performedByName: req.user.name,
        performedByEmail: req.user.email,
        category: 'asset_management',
        sessionInfo: {
          sessionId: req.session?.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        },
        metadata: {
          totalFiles: req.files.length,
          successfulUploads: results.successful,
          failedUploads: results.failed
        }
      });

      res.json(results);

    } catch (error) {
      console.error('Error in bulk upload:', error);
      
      // Clean up uploaded files on error
      if (req.files) {
        req.files.forEach(file => {
          fs.unlink(file.path, () => {});
        });
      }
      
      res.status(500).json({ 
        message: 'Error in bulk upload', 
        error: error.message,
        results
      });
    }
  }
];

/**
 * Create uploads directory structure if it doesn't exist
 */
exports.initializeStorage = () => {
  const uploadsPath = path.join('uploads', 'assets');
  if (!fs.existsSync(uploadsPath)) {
    fs.mkdirSync(uploadsPath, { recursive: true });
    console.log('Asset uploads directory created');
  }
};