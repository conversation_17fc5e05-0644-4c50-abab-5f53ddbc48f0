const SynologyAPI = require('../integrations/synology/synologyAPI');
const SynologyConfig = require('../../models/SynologyConfig');

// Initialize Synology API with empty credentials (will be updated when needed)
let synologyAPI = null;

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.SYNOLOGY_HOST || '';
    const envPort = process.env.SYNOLOGY_PORT || 5001;
    const envUsername = process.env.SYNOLOGY_USERNAME || '';
    const envPassword = process.env.SYNOLOGY_PASSWORD || '';
    const envSecure = process.env.SYNOLOGY_SECURE !== 'false';
    
    // If environment variables are set, use them directly
    if (envHost && envUsername && envPassword) {
      synologyAPI = new SynologyAPI(
        envHost,
        parseInt(envPort, 10),
        envUsername,
        envPassword,
        envSecure
      );
      return {
        host: envHost,
        port: parseInt(envPort, 10),
        username: envUsername,
        password: envPassword,
        secure: envSecure,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('Synology environment variables not set. Falling back to database config (not recommended).');
    
    const config = await SynologyConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      synologyAPI = new SynologyAPI(
        config.host,
        config.port,
        config.username,
        config.password,
        config.secure
      );
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Synology configuration:', error);
    throw error;
  }
};

/**
 * Save Synology configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Synology configuration request:', error);
    res.status(500).json({ message: 'Error handling Synology configuration request', error: error.message });
  }
};

/**
 * Get Synology configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.SYNOLOGY_HOST || '';
    const envPort = process.env.SYNOLOGY_PORT || 5001;
    const envUsername = process.env.SYNOLOGY_USERNAME || '';
    const envPassword = process.env.SYNOLOGY_PASSWORD || '';
    const envSecure = process.env.SYNOLOGY_SECURE !== 'false';
    
    // If environment variables are set, use them directly
    if (envHost && envUsername && envPassword) {
      // Don't send the actual password back to the client for security
      res.json({
        host: envHost,
        port: parseInt(envPort, 10),
        username: envUsername,
        secure: envSecure,
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('Synology environment variables not set. Falling back to database config (not recommended).');
    
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found. Please set the required environment variables.' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      port: config.port,
      username: config.username,
      secure: config.secure,
      configuredAt: config.updatedAt,
      fromEnv: !!config.fromEnv
    });
  } catch (error) {
    console.error('Error fetching Synology configuration:', error);
    res.status(500).json({ message: 'Error fetching Synology configuration', error: error.message });
  }
};

/**
 * List files in a directory with pagination support
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listFiles = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    const { path, page, pageSize, sortBy, sortDirection, ...otherOptions } = req.query;

    if (!path) {
      return res.status(400).json({ message: 'Path parameter is required' });
    }

    // Convert pagination parameters to the format expected by the Synology API
    const options = {
      ...otherOptions,
      // If page and pageSize are provided, convert them to offset and limit
      ...(page && pageSize ? {
        offset: (parseInt(page, 10) - 1) * parseInt(pageSize, 10),
        limit: Math.min(parseInt(pageSize, 10), 200)  // Cap at 200 to prevent timeouts
      } : {
        limit: 100  // Default to 100 if no pageSize specified
      }),
      // If sortBy and sortDirection are provided, use them
      ...(sortBy ? { sort_by: sortBy } : {}),
      ...(sortDirection ? { sort_direction: sortDirection.toUpperCase() } : {})
    };

    // Ensure synologyAPI is initialized
    if (!synologyAPI) {
      console.log('synologyAPI not initialized, initializing now...');
      synologyAPI = new SynologyAPI(
        config.host,
        config.port,
        config.username,
        config.password,
        config.secure
      );
      
      // Initialize the API
      await synologyAPI.initialize();
    }
    
    // Get files with pagination metadata
    const result = await synologyAPI.listFiles(path, options);
    
    // Format the response for the client
    // If the API returned the new format (object with files and pagination), use it
    // Otherwise, for backward compatibility, wrap the files array in an object
    const response = result.files ? {
      files: result.files,
      pagination: {
        ...result.pagination,
        // Convert offset/limit back to page/pageSize for the client
        page: Math.floor(result.pagination.offset / result.pagination.limit) + 1,
        pageSize: result.pagination.limit,
        // We don't have the total count from the API, but we can estimate if we have a full page
        hasMore: result.files.length >= result.pagination.limit
      },
      path: {
        original: result.originalPath,
        adjusted: result.adjustedPath
      }
    } : {
      // Backward compatibility for old API response format
      files: Array.isArray(result) ? result : [],
      pagination: {
        page: 1,
        pageSize: 100,
        hasMore: false
      },
      path: {
        original: path,
        adjusted: path
      }
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error listing files from Synology:', error);
    
    // Check if this is a 2FA error
    if (error.message && error.message.includes('Two-factor authentication required')) {
      return res.status(403).json({ 
        message: 'Error listing files from Synology', 
        error: '403',
        details: error.message,
        requires2FA: true
      });
    }
    
    // Check for specific Synology error codes
    if (error.message && error.message.includes('Error code 119')) {
      console.log('Handling error code 119 - trying with different path format...');
      
      // Try with a different path format
      try {
        let adjustedPath = path;
        
        // If path starts with /volume, try without it
        if (path.startsWith('/volume')) {
          adjustedPath = path.replace(/^\/volume\d+/, '');
          if (adjustedPath === '') adjustedPath = '/';
        } else {
          // Otherwise, try with /volume1 prefix
          adjustedPath = `/volume1${path === '/' ? '' : path}`;
        }
        
        console.log(`Retrying with adjusted path: ${adjustedPath}`);
        
        // Retry with the adjusted path
        const retryResult = await synologyAPI.listFiles(adjustedPath, options);
        
        // Format the response for the client
        const retryResponse = retryResult.files ? {
          files: retryResult.files,
          pagination: {
            ...retryResult.pagination,
            page: Math.floor(retryResult.pagination.offset / retryResult.pagination.limit) + 1,
            pageSize: retryResult.pagination.limit,
            hasMore: retryResult.files.length >= retryResult.pagination.limit
          },
          path: {
            original: path,
            adjusted: adjustedPath
          }
        } : {
          files: Array.isArray(retryResult) ? retryResult : [],
          pagination: {
            page: 1,
            pageSize: 20,
            hasMore: false
          },
          path: {
            original: path,
            adjusted: adjustedPath
          }
        };
        
        return res.json(retryResponse);
      } catch (retryError) {
        console.error('Error retrying with adjusted path:', retryError);
        return res.status(500).json({ 
          message: 'Error listing files from Synology', 
          error: retryError.message,
          originalError: error.message,
          code: 119
        });
      }
    }
    
    // Check for timeout errors
    if (error.message && error.message.includes('timeout')) {
      return res.status(408).json({ 
        message: 'Request timeout when listing files from Synology', 
        error: error.message,
        code: 408
      });
    }
    
    res.status(500).json({ message: 'Error listing files from Synology', error: error.message });
  }
};

/**
 * Download a file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.downloadFile = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    const { path } = req.query;

    if (!path) {
      return res.status(400).json({ message: 'Path parameter is required' });
    }

    const fileData = await synologyAPI.downloadFile(path);

    // Get the filename from the path
    const filename = path.split('/').pop();

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    res.send(fileData);
  } catch (error) {
    console.error('Error downloading file from Synology:', error);
    
    // Check if this is a 2FA error
    if (error.message && error.message.includes('Two-factor authentication required')) {
      return res.status(403).json({ 
        message: 'Error downloading file from Synology', 
        error: '403',
        details: error.message,
        requires2FA: true
      });
    }
    
    res.status(500).json({ message: 'Error downloading file from Synology', error: error.message });
  }
};

/**
 * Create a sharing link
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createSharingLink = async (req, res) => {
  try {
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Synology configuration not found' });
    }

    const { path, ...options } = req.body;

    if (!path) {
      return res.status(400).json({ message: 'Path parameter is required' });
    }

    const sharingInfo = await synologyAPI.createSharingLink(path, options);
    res.json(sharingInfo);
  } catch (error) {
    console.error('Error creating sharing link on Synology:', error);
    
    // Check if this is a 2FA error
    if (error.message && error.message.includes('Two-factor authentication required')) {
      return res.status(403).json({ 
        message: 'Error creating sharing link on Synology', 
        error: '403',
        details: error.message,
        requires2FA: true
      });
    }
    
    res.status(500).json({ message: 'Error creating sharing link on Synology', error: error.message });
  }
};

/**
 * Set up Synology with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Synology one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Synology one-click setup request', 
      error: error.message 
    });
  }
};
