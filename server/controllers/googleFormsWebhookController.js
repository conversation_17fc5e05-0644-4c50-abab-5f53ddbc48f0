const GoogleFormsWebhook = require('../../models/GoogleFormsWebhook');
const GoogleFormsAPI = require('../integrations/googleForms/googleFormsAPI');
const Task = require('../../models/Task');
const MaintenanceTask = require('../../models/MaintenanceTask');
const User = require('../../models/User');
const { validationResult } = require('express-validator');

// Helper function to get an API instance with user impersonation
const getApiWithUser = async (userId) => {
  try {
    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Check if service account credentials are available
    const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
    const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
    const usingServiceAccount = !!(serviceAccountEmail && serviceAccountPrivateKey);

    if (!usingServiceAccount) {
      throw new Error('Service account credentials are required for webhook functionality');
    }

    // Use service account with user impersonation
    if (!user.email) {
      throw new Error('User email is required for service account impersonation');
    }

    const api = new GoogleFormsAPI(
      process.env.GOOGLE_FORMS_CLIENT_ID || '',
      process.env.GOOGLE_FORMS_CLIENT_SECRET || '',
      process.env.GOOGLE_FORMS_REDIRECT_URI || '',
      process.env.GOOGLE_FORMS_TOKEN_PATH || './google-forms-token.json',
      null, // No user tokens
      userId,
      user.email // Pass the user email for service account impersonation
    );

    await api.initialize();
    return api;
  } catch (error) {
    console.error('Error creating API with user:', error);
    throw error;
  }
};

/**
 * Google Forms Webhook Controller
 * Handles API operations for Google Forms webhooks
 */
const googleFormsWebhookController = {
  /**
   * Create a new webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created webhook
   */
  createWebhook: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const {
        formId,
        formName,
        fieldMappings,
        assignmentRules,
        defaultAssignee,
        taskType
      } = req.body;

      // Validate required fields
      if (!formId || !formName || !fieldMappings || !fieldMappings.titleField) {
        return res.status(400).json({ 
          message: 'Form ID, form name, and title field mapping are required' 
        });
      }

      // Check if webhook already exists for this form
      const existingWebhook = await GoogleFormsWebhook.findOne({ formId });
      if (existingWebhook) {
        return res.status(400).json({ 
          message: 'A webhook already exists for this form',
          webhookId: existingWebhook._id
        });
      }

      // Create new webhook
      const webhook = new GoogleFormsWebhook({
        formId,
        formName,
        createdBy: req.user.id,
        fieldMappings,
        assignmentRules: assignmentRules || [],
        defaultAssignee,
        taskType: taskType || 'general'
      });

      await webhook.save();

      // Populate references for response
      await webhook.populate('createdBy', 'name email');
      if (webhook.defaultAssignee) {
        await webhook.populate('defaultAssignee', 'name email');
      }
      if (webhook.assignmentRules && webhook.assignmentRules.length > 0) {
        await webhook.populate('assignmentRules.assignTo', 'name email');
      }

      res.status(201).json(webhook);
    } catch (error) {
      console.error('Error creating webhook:', error);
      res.status(500).json({ message: 'Error creating webhook', error: error.message });
    }
  },

  /**
   * Get all webhooks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with webhooks
   */
  getAllWebhooks: async (req, res) => {
    try {
      // If user is admin, return all webhooks
      // Otherwise, return only the user's webhooks
      let webhooks;
      if (req.user.roles.includes('admin')) {
        webhooks = await GoogleFormsWebhook.find()
          .sort({ createdAt: -1 })
          .populate('createdBy', 'name email')
          .populate('defaultAssignee', 'name email')
          .populate('assignmentRules.assignTo', 'name email');
      } else {
        webhooks = await GoogleFormsWebhook.find({ createdBy: req.user.id })
          .sort({ createdAt: -1 })
          .populate('createdBy', 'name email')
          .populate('defaultAssignee', 'name email')
          .populate('assignmentRules.assignTo', 'name email');
      }

      res.json(webhooks);
    } catch (error) {
      console.error('Error getting webhooks:', error);
      res.status(500).json({ message: 'Error getting webhooks', error: error.message });
    }
  },

  /**
   * Get webhook by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with webhook
   */
  getWebhookById: async (req, res) => {
    try {
      const webhook = await GoogleFormsWebhook.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('defaultAssignee', 'name email')
        .populate('assignmentRules.assignTo', 'name email')
        .populate('processedResponses.taskId');

      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }

      // Check if user is authorized to view this webhook
      if (!req.user.roles.includes('admin') && webhook.createdBy._id.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to view this webhook' });
      }

      res.json(webhook);
    } catch (error) {
      console.error('Error getting webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error getting webhook', error: error.message });
    }
  },

  /**
   * Update a webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated webhook
   */
  updateWebhook: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const webhook = await GoogleFormsWebhook.findById(req.params.id);
      
      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      // Check if user is authorized to update this webhook
      if (!req.user.roles.includes('admin') && webhook.createdBy.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to update this webhook' });
      }
      
      const {
        formName,
        active,
        fieldMappings,
        assignmentRules,
        defaultAssignee,
        taskType
      } = req.body;

      // Update webhook fields
      if (formName) webhook.formName = formName;
      if (active !== undefined) webhook.active = active;
      if (fieldMappings) webhook.fieldMappings = fieldMappings;
      if (assignmentRules) webhook.assignmentRules = assignmentRules;
      if (defaultAssignee !== undefined) webhook.defaultAssignee = defaultAssignee;
      if (taskType) webhook.taskType = taskType;

      await webhook.save();
      
      // Populate references for response
      await webhook.populate('createdBy', 'name email');
      if (webhook.defaultAssignee) {
        await webhook.populate('defaultAssignee', 'name email');
      }
      if (webhook.assignmentRules && webhook.assignmentRules.length > 0) {
        await webhook.populate('assignmentRules.assignTo', 'name email');
      }
      
      res.json(webhook);
    } catch (error) {
      console.error('Error updating webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error updating webhook', error: error.message });
    }
  },

  /**
   * Delete a webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message
   */
  deleteWebhook: async (req, res) => {
    try {
      const webhook = await GoogleFormsWebhook.findById(req.params.id);
      
      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      // Check if user is authorized to delete this webhook
      if (!req.user.roles.includes('admin') && webhook.createdBy.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to delete this webhook' });
      }
      
      await webhook.remove();
      
      res.json({ message: 'Webhook removed' });
    } catch (error) {
      console.error('Error deleting webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error deleting webhook', error: error.message });
    }
  },

  /**
   * Process form responses for a specific webhook
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with processing results
   */
  processWebhook: async (req, res) => {
    try {
      const webhook = await GoogleFormsWebhook.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('defaultAssignee', 'name email')
        .populate('assignmentRules.assignTo', 'name email');
      
      if (!webhook) {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      // Check if user is authorized to process this webhook
      if (!req.user.roles.includes('admin') && webhook.createdBy._id.toString() !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to process this webhook' });
      }

      // Process the webhook
      const result = await googleFormsWebhookController.processWebhookResponses(webhook, req.user.id);
      
      res.json(result);
    } catch (error) {
      console.error('Error processing webhook:', error);
      
      if (error.kind === 'ObjectId') {
        return res.status(404).json({ message: 'Webhook not found' });
      }
      
      res.status(500).json({ message: 'Error processing webhook', error: error.message });
    }
  },

  /**
   * Process all active webhooks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with processing results
   */
  processAllWebhooks: async (req, res) => {
    try {
      // Check if user is admin
      if (!req.user.roles.includes('admin')) {
        return res.status(403).json({ message: 'Not authorized to process all webhooks' });
      }

      // Get all active webhooks
      const webhooks = await GoogleFormsWebhook.find({ active: true })
        .populate('createdBy', 'name email')
        .populate('defaultAssignee', 'name email')
        .populate('assignmentRules.assignTo', 'name email');

      // Process each webhook
      const results = [];
      for (const webhook of webhooks) {
        try {
          const result = await googleFormsWebhookController.processWebhookResponses(webhook, req.user.id);
          results.push({
            webhookId: webhook._id,
            formName: webhook.formName,
            success: true,
            result
          });
        } catch (error) {
          results.push({
            webhookId: webhook._id,
            formName: webhook.formName,
            success: false,
            error: error.message
          });
        }
      }
      
      res.json({
        message: `Processed ${webhooks.length} webhooks`,
        results
      });
    } catch (error) {
      console.error('Error processing all webhooks:', error);
      res.status(500).json({ message: 'Error processing all webhooks', error: error.message });
    }
  },

  /**
   * Process responses for a webhook (internal method)
   * @param {Object} webhook - Webhook document
   * @param {string} userId - User ID processing the webhook
   * @returns {Object} Processing results
   */
  processWebhookResponses: async (webhook, userId) => {
    try {
      // Get API instance with user impersonation
      const api = await getApiWithUser(webhook.createdBy._id || userId);

      // Get form responses
      const formResponses = await api.getFormResponses(webhook.formId);
      
      if (!formResponses || !formResponses.responses) {
        return {
          message: 'No responses found',
          processed: 0,
          newTasks: []
        };
      }

      // Get processed response IDs
      const processedIds = new Set(webhook.processedResponses.map(pr => pr.responseId));
      
      // Filter out already processed responses
      const newResponses = formResponses.responses.filter(response => 
        !processedIds.has(response.responseId)
      );

      if (newResponses.length === 0) {
        return {
          message: 'No new responses to process',
          processed: 0,
          newTasks: []
        };
      }

      // Process each new response
      const newTasks = [];
      for (const response of newResponses) {
        try {
          // Create a task from the response
          const task = await googleFormsWebhookController.createTaskFromResponse(
            response, 
            webhook, 
            userId
          );

          // Add to processed responses
          webhook.processedResponses.push({
            responseId: response.responseId,
            processedAt: new Date(),
            taskId: task._id
          });

          newTasks.push({
            responseId: response.responseId,
            taskId: task._id,
            taskTitle: task.title
          });
        } catch (error) {
          console.error(`Error processing response ${response.responseId}:`, error);
          // Continue with next response
        }
      }

      // Update last checked time
      webhook.lastChecked = new Date();
      await webhook.save();

      return {
        message: `Processed ${newResponses.length} new responses`,
        processed: newResponses.length,
        newTasks
      };
    } catch (error) {
      console.error('Error processing webhook responses:', error);
      throw error;
    }
  },

  /**
   * Create a task from a form response
   * @param {Object} response - Form response
   * @param {Object} webhook - Webhook document
   * @param {string} userId - User ID creating the task
   * @returns {Object} Created task
   */
  createTaskFromResponse: async (response, webhook, userId) => {
    try {
      // Extract answer values from the response
      const answers = {};
      if (response.answers) {
        Object.entries(response.answers).forEach(([questionId, answer]) => {
          // Handle different answer types
          if (answer.textAnswers && answer.textAnswers.answers) {
            answers[questionId] = answer.textAnswers.answers.map(a => a.value).join(', ');
          } else if (answer.fileUploadAnswers) {
            answers[questionId] = answer.fileUploadAnswers.answers.map(a => a.fileId).join(', ');
          } else if (answer.dateAnswers) {
            answers[questionId] = answer.dateAnswers.answers.map(a => a.value).join(', ');
          } else if (answer.timeAnswers) {
            answers[questionId] = answer.timeAnswers.answers.map(a => a.value).join(', ');
          } else if (answer.scaleAnswers) {
            answers[questionId] = answer.scaleAnswers.answers.map(a => a.value).join(', ');
          } else {
            // Default fallback
            answers[questionId] = JSON.stringify(answer);
          }
        });
      }

      // Map form fields to task properties
      const { fieldMappings } = webhook;
      
      // Required fields
      const title = answers[fieldMappings.titleField] || 'Form Response';
      
      // Optional fields with defaults
      const description = fieldMappings.descriptionField ? 
        answers[fieldMappings.descriptionField] : 
        `Form response from ${webhook.formName}`;
      
      const priority = fieldMappings.priorityField ? 
        mapPriority(answers[fieldMappings.priorityField]) : 
        'medium';
      
      // Parse due date if provided
      let dueDate = null;
      if (fieldMappings.dueDateField && answers[fieldMappings.dueDateField]) {
        try {
          dueDate = new Date(answers[fieldMappings.dueDateField]);
        } catch (error) {
          console.warn(`Invalid due date format: ${answers[fieldMappings.dueDateField]}`);
        }
      }
      
      // Parse tags if provided
      let tags = [];
      if (fieldMappings.tagsField && answers[fieldMappings.tagsField]) {
        tags = answers[fieldMappings.tagsField].split(',').map(tag => tag.trim());
      }

      // Determine assignee based on assignment rules
      let assignedTo = null;
      if (webhook.assignmentRules && webhook.assignmentRules.length > 0) {
        // Sort rules by priority
        const sortedRules = [...webhook.assignmentRules].sort((a, b) => a.priority - b.priority);
        
        // Find the first matching rule
        for (const rule of sortedRules) {
          if (answers[rule.field] && answers[rule.field].includes(rule.value)) {
            assignedTo = rule.assignTo;
            break;
          }
        }
      }
      
      // Use default assignee if no rules matched
      if (!assignedTo && webhook.defaultAssignee) {
        assignedTo = webhook.defaultAssignee;
      }

      // Create task data
      const taskData = {
        title,
        description,
        priority,
        status: 'open',
        dueDate,
        tags,
        assignedTo,
        createdBy: webhook.createdBy
      };

      // Add custom fields if defined
      if (fieldMappings.customFields && fieldMappings.customFields.length > 0) {
        fieldMappings.customFields.forEach(customField => {
          if (answers[customField.formField]) {
            taskData[customField.taskField] = answers[customField.formField];
          }
        });
      }

      // Create the appropriate task type
      let task;
      if (webhook.taskType === 'maintenance') {
        task = new MaintenanceTask(taskData);
      } else {
        task = new Task(taskData);
      }

      await task.save();
      
      // Populate references
      await task.populate('createdBy', 'name email');
      if (assignedTo) {
        await task.populate('assignedTo', 'name email');
      }
      
      return task;
    } catch (error) {
      console.error('Error creating task from response:', error);
      throw error;
    }
  }
};

/**
 * Map form priority value to task priority
 * @param {string} value - Priority value from form
 * @returns {string} Task priority value
 */
function mapPriority(value) {
  if (!value) return 'medium';
  
  const lowerValue = value.toLowerCase();
  
  if (lowerValue.includes('high') || lowerValue.includes('urgent') || lowerValue === '3') {
    return 'high';
  } else if (lowerValue.includes('critical') || lowerValue === '4') {
    return 'critical';
  } else if (lowerValue.includes('low') || lowerValue === '1') {
    return 'low';
  } else {
    return 'medium';
  }
}

module.exports = googleFormsWebhookController;