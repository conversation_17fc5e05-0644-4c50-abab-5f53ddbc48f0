const Asset = require('../../models/Asset');
const AssetCategory = require('../../models/AssetCategory');
const AssetLocation = require('../../models/AssetLocation');
const AssetAuditLog = require('../../models/AssetAuditLog');
const AssetTemplate = require('../../models/AssetTemplate');
const QRCode = require('qrcode');
const JsBarcode = require('jsbarcode');
const { createCanvas } = require('canvas');
const mongoose = require('mongoose');

/**
 * Asset Controller
 * Handles CRUD operations and advanced asset management functionality
 */

/**
 * Get all assets with filtering, sorting, and pagination
 */
exports.getAssets = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      category,
      status,
      assignedTo,
      location,
      tags,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      includeDeleted = false
    } = req.query;

    // Build query
    const query = {};
    
    if (!includeDeleted || includeDeleted === 'false') {
      query.isDeleted = false;
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { assetTag: { $regex: search, $options: 'i' } },
        { serialNumber: { $regex: search, $options: 'i' } },
        { model: { $regex: search, $options: 'i' } },
        { manufacturer: { $regex: search, $options: 'i' } }
      ];
    }

    if (category) query.category = category;
    if (status) {
      if (Array.isArray(status)) {
        query.status = { $in: status };
      } else {
        query.status = status;
      }
    }
    if (assignedTo) query.assignedTo = assignedTo;
    if (location) query.location = location;
    if (tags) {
      if (Array.isArray(tags)) {
        query.tags = { $in: tags };
      } else {
        query.tags = tags;
      }
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [assets, total] = await Promise.all([
      Asset.find(query)
        .populate('category', 'name description')
        .populate('location', 'name locationType fullPath')
        .populate('assignedTo', 'name email')
        .populate('createdBy', 'name email')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit)),
      Asset.countDocuments(query)
    ]);

    res.json({
      assets,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting assets:', error);
    res.status(500).json({ message: 'Error getting assets', error: error.message });
  }
};

/**
 * Get single asset by ID
 */
exports.getAsset = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }
    
    const asset = await Asset.findById(id)
      .populate('category', 'name description')
      .populate('location', 'name locationType fullPath')
      .populate('assignedTo', 'name email department')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    if (asset.isDeleted) {
      return res.status(410).json({ message: 'Asset has been deleted' });
    }

    res.json(asset);
  } catch (error) {
    console.error('Error getting asset:', error);
    res.status(500).json({ message: 'Error getting asset', error: error.message });
  }
};

/**
 * Create new asset
 */
exports.createAsset = async (req, res) => {
  try {
    const assetData = {
      ...req.body,
      createdBy: req.user._id
    };

    // Generate asset tag if not provided
    if (!assetData.assetTag) {
      assetData.assetTag = await generateAssetTag(assetData.category);
    }

    // Validate unique asset tag
    const existingAsset = await Asset.findByAssetTag(assetData.assetTag);
    if (existingAsset) {
      return res.status(400).json({ message: 'Asset tag already exists' });
    }

    const asset = new Asset(assetData);
    asset._modifiedBy = req.user._id;
    await asset.save();

    // Log the creation
    await AssetAuditLog.logAssetCreated(asset, req.user, {
      sessionId: req.session?.id,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Populate references before sending response
    await asset.populate([
      { path: 'category', select: 'name description' },
      { path: 'location', select: 'name locationType fullPath' },
      { path: 'assignedTo', select: 'name email' },
      { path: 'createdBy', select: 'name email' }
    ]);

    res.status(201).json(asset);
  } catch (error) {
    console.error('Error creating asset:', error);
    
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({ 
        message: `${field} already exists`,
        field: field,
        value: error.keyValue[field]
      });
    }
    
    res.status(500).json({ message: 'Error creating asset', error: error.message });
  }
};

/**
 * Update asset
 */
exports.updateAsset = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    if (asset.isDeleted) {
      return res.status(410).json({ message: 'Cannot update deleted asset' });
    }

    // Track changes for audit log
    const changedFields = [];
    const originalAsset = asset.toObject();
    
    Object.keys(updates).forEach(field => {
      if (JSON.stringify(originalAsset[field]) !== JSON.stringify(updates[field])) {
        changedFields.push({
          field,
          oldValue: originalAsset[field],
          newValue: updates[field],
          dataType: typeof updates[field]
        });
      }
    });

    // Apply updates
    Object.assign(asset, updates);
    asset._modifiedBy = req.user._id;
    await asset.save();

    // Log the update if there were changes
    if (changedFields.length > 0) {
      await AssetAuditLog.logAssetUpdated(asset, changedFields, req.user, {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      });
    }

    // Populate references
    await asset.populate([
      { path: 'category', select: 'name description' },
      { path: 'location', select: 'name locationType fullPath' },
      { path: 'assignedTo', select: 'name email' },
      { path: 'lastModifiedBy', select: 'name email' }
    ]);

    res.json(asset);
  } catch (error) {
    console.error('Error updating asset:', error);
    
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({ 
        message: `${field} already exists`,
        field: field,
        value: error.keyValue[field]
      });
    }
    
    res.status(500).json({ message: 'Error updating asset', error: error.message });
  }
};

/**
 * Soft delete asset
 */
exports.deleteAsset = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    if (asset.isDeleted) {
      return res.status(410).json({ message: 'Asset already deleted' });
    }

    await asset.softDelete(req.user._id);

    // Log the deletion
    await AssetAuditLog.logAction({
      asset: asset._id,
      assetTag: asset.assetTag,
      action: 'deleted',
      actionDescription: `Asset "${asset.name}" (${asset.assetTag}) was deleted`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'asset_management',
      severity: 'warning',
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    res.json({ message: 'Asset deleted successfully' });
  } catch (error) {
    console.error('Error deleting asset:', error);
    res.status(500).json({ message: 'Error deleting asset', error: error.message });
  }
};

/**
 * Restore deleted asset
 */
exports.restoreAsset = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    if (!asset.isDeleted) {
      return res.status(400).json({ message: 'Asset is not deleted' });
    }

    await asset.restore();

    // Log the restoration
    await AssetAuditLog.logAction({
      asset: asset._id,
      assetTag: asset.assetTag,
      action: 'restored',
      actionDescription: `Asset "${asset.name}" (${asset.assetTag}) was restored`,
      performedBy: req.user._id,
      performedByName: req.user.name,
      performedByEmail: req.user.email,
      category: 'asset_management',
      sessionInfo: {
        sessionId: req.session?.id,
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    });

    await asset.populate([
      { path: 'category', select: 'name description' },
      { path: 'location', select: 'name locationType fullPath' },
      { path: 'assignedTo', select: 'name email' }
    ]);

    res.json(asset);
  } catch (error) {
    console.error('Error restoring asset:', error);
    res.status(500).json({ message: 'Error restoring asset', error: error.message });
  }
};

/**
 * Generate barcode for asset
 */
exports.generateBarcode = async (req, res) => {
  try {
    const { id } = req.params;
    const { format = 'CODE128', width = 200, height = 50 } = req.query;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const canvas = createCanvas(width, height);
    JsBarcode(canvas, asset.assetTag, {
      format: format,
      width: 2,
      height: height - 20,
      displayValue: true,
      fontSize: 12
    });

    // Save barcode reference to asset if not already present
    if (!asset.barcode) {
      asset.barcode = asset.assetTag;
      await asset.save();
    }

    res.setHeader('Content-Type', 'image/png');
    res.send(canvas.toBuffer('image/png'));
  } catch (error) {
    console.error('Error generating barcode:', error);
    res.status(500).json({ message: 'Error generating barcode', error: error.message });
  }
};

/**
 * Generate QR code for asset
 */
exports.generateQRCode = async (req, res) => {
  try {
    const { id } = req.params;
    const { size = 200 } = req.query;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    // Create QR code data with asset information
    const qrData = {
      assetId: asset._id,
      assetTag: asset.assetTag,
      name: asset.name,
      serialNumber: asset.serialNumber,
      url: `${req.protocol}://${req.get('host')}/assets/${asset._id}`
    };

    const qrCodeBuffer = await QRCode.toBuffer(JSON.stringify(qrData), {
      width: size,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // Save QR code reference to asset if not already present
    if (!asset.qrCode) {
      asset.qrCode = asset.assetTag;
      await asset.save();
    }

    res.setHeader('Content-Type', 'image/png');
    res.send(qrCodeBuffer);
  } catch (error) {
    console.error('Error generating QR code:', error);
    res.status(500).json({ message: 'Error generating QR code', error: error.message });
  }
};

/**
 * Bulk operations
 */
exports.bulkUpdate = async (req, res) => {
  try {
    const { assetIds, updates } = req.body;

    if (!assetIds || !Array.isArray(assetIds) || assetIds.length === 0) {
      return res.status(400).json({ message: 'Asset IDs array is required' });
    }

    const results = [];
    
    for (const assetId of assetIds) {
      try {
        const asset = await Asset.findById(assetId);
        if (!asset || asset.isDeleted) {
          results.push({ assetId, status: 'error', message: 'Asset not found or deleted' });
          continue;
        }

        Object.assign(asset, updates);
        asset._modifiedBy = req.user._id;
        await asset.save();

        // Log bulk update
        await AssetAuditLog.logAction({
          asset: asset._id,
          assetTag: asset.assetTag,
          action: 'bulk_updated',
          actionDescription: `Asset bulk updated: ${Object.keys(updates).join(', ')}`,
          performedBy: req.user._id,
          performedByName: req.user.name,
          performedByEmail: req.user.email,
          category: 'asset_management',
          sessionInfo: {
            sessionId: req.session?.id,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
          }
        });

        results.push({ assetId, status: 'success' });
      } catch (error) {
        results.push({ assetId, status: 'error', message: error.message });
      }
    }

    res.json({
      message: `Bulk update completed`,
      results,
      summary: {
        total: assetIds.length,
        success: results.filter(r => r.status === 'success').length,
        errors: results.filter(r => r.status === 'error').length
      }
    });
  } catch (error) {
    console.error('Error in bulk update:', error);
    res.status(500).json({ message: 'Error in bulk update', error: error.message });
  }
};

/**
 * Get asset history/audit log
 */
exports.getAssetHistory = async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 50, page = 1, action, category, startDate, endDate } = req.query;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ message: 'Invalid asset ID' });
    }

    const asset = await Asset.findById(id);
    if (!asset) {
      return res.status(404).json({ message: 'Asset not found' });
    }

    const options = {
      limit: parseInt(limit),
      action,
      category,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined
    };

    const history = await AssetAuditLog.findByAsset(id, options);
    
    res.json({
      asset: {
        id: asset._id,
        name: asset.name,
        assetTag: asset.assetTag
      },
      history
    });
  } catch (error) {
    console.error('Error getting asset history:', error);
    res.status(500).json({ message: 'Error getting asset history', error: error.message });
  }
};

/**
 * Helper function to generate asset tag
 */
async function generateAssetTag(categoryId) {
  try {
    let prefix = 'ASSET';
    
    if (categoryId) {
      const category = await AssetCategory.findById(categoryId);
      if (category) {
        prefix = category.name.toUpperCase().substring(0, 3);
      }
    }

    const year = new Date().getFullYear();
    const baseTag = `${prefix}-${year}`;
    
    // Find the next sequential number
    const regex = new RegExp(`^${baseTag}-(\\d+)$`);
    const lastAsset = await Asset.findOne(
      { assetTag: { $regex: regex } },
      {},
      { sort: { assetTag: -1 } }
    );

    let nextNumber = 1;
    if (lastAsset) {
      const match = lastAsset.assetTag.match(regex);
      if (match && match[1]) {
        nextNumber = parseInt(match[1]) + 1;
      }
    }

    return `${baseTag}-${nextNumber.toString().padStart(4, '0')}`;
  } catch (error) {
    console.error('Error generating asset tag:', error);
    return `ASSET-${Date.now()}`;
  }
}