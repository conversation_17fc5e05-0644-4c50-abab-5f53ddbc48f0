const FormSubmission = require('../../models/FormSubmission');
const Form = require('../../models/Form');
const Ticket = require('../../models/Ticket');
const Task = require('../../models/Task');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const nodemailer = require('nodemailer');

// Configure file upload storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/form-submissions');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// Configure upload middleware
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check if file type is allowed
    const allowedTypes = req.form ? 
      getAllowedFileTypes(req.form) : 
      ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    
    if (allowedTypes.includes(file.mimetype) || allowedTypes.includes('*/*')) {
      cb(null, true);
    } else {
      cb(new Error('File type not allowed'), false);
    }
  }
});

// Helper function to get allowed file types from form
const getAllowedFileTypes = (form) => {
  const allowedTypes = [];
  
  form.fieldGroups.forEach(group => {
    group.fields.forEach(field => {
      if (field.type === 'file' && field.options && field.options.allowedFileTypes) {
        allowedTypes.push(...field.options.allowedFileTypes);
      }
    });
  });
  
  return allowedTypes.length > 0 ? allowedTypes : ['*/*'];
};

/**
 * Form Submission Controller
 * Handles all form submission operations
 */
const formSubmissionController = {
  /**
   * Submit a form
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  submitForm: async (req, res) => {
    try {
      const { formId } = req.params;
      const formData = req.body.data || {};
      
      // Find the form
      const form = await Form.findById(formId);
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if form is published
      if (form.status !== 'published') {
        return res.status(400).json({
          success: false,
          message: 'This form is not currently accepting submissions'
        });
      }
      
      // Check if user has access to submit this form
      const hasAccess = form.hasAccessPermission(req.user);
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to submit this form'
        });
      }
      
      // Validate required fields
      const validationErrors = validateFormData(form, formData);
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: validationErrors
        });
      }
      
      // Create a simplified version of the form for historical reference
      const formVersion = {
        title: form.title,
        fieldGroups: form.fieldGroups.map(group => ({
          title: group.title,
          fields: group.fields.map(field => ({
            fieldId: field.fieldId,
            type: field.type,
            label: field.label
          }))
        }))
      };
      
      // Create submission
      const submission = new FormSubmission({
        form: formId,
        formVersion,
        data: new Map(Object.entries(formData)),
        status: 'submitted',
        submittedBy: req.user ? req.user._id : null,
        submitterEmail: formData.email || req.body.email || (req.user ? req.user.email : null),
        submitterName: formData.name || req.body.name || (req.user ? req.user.name : null),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        completionTime: req.body.completionTime
      });
      
      // Process file uploads if any
      if (req.files && req.files.length > 0) {
        submission.files = req.files.map(file => ({
          fieldId: file.fieldname,
          filename: file.filename,
          originalFilename: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          url: `/api/forms/submissions/files/${file.filename}`
        }));
      }
      
      // Save submission
      await submission.save();
      
      // Update form statistics
      form.stats.submissions += 1;
      await form.save();
      
      // Process submission (create ticket/task, send notifications)
      await processSubmission(submission, form);
      
      res.status(201).json({
        success: true,
        message: 'Form submitted successfully',
        data: {
          submissionId: submission._id,
          redirectUrl: form.submission.redirectUrl,
          successMessage: form.submission.successMessage
        }
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      res.status(500).json({
        success: false,
        message: 'Error submitting form',
        error: error.message
      });
    }
  },
  
  /**
   * Submit a public form (no authentication required)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  submitPublicForm: async (req, res) => {
    try {
      const { slug } = req.params;
      const formData = req.body.data || {};
      
      // Find the form by slug
      const form = await Form.findOne({ 
        slug,
        status: 'published',
        'permissions.access': 'public'
      });
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found or not publicly accessible'
        });
      }
      
      // Validate required fields
      const validationErrors = validateFormData(form, formData);
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation errors',
          errors: validationErrors
        });
      }
      
      // Create a simplified version of the form for historical reference
      const formVersion = {
        title: form.title,
        fieldGroups: form.fieldGroups.map(group => ({
          title: group.title,
          fields: group.fields.map(field => ({
            fieldId: field.fieldId,
            type: field.type,
            label: field.label
          }))
        }))
      };
      
      // Create submission
      const submission = new FormSubmission({
        form: form._id,
        formVersion,
        data: new Map(Object.entries(formData)),
        status: 'submitted',
        submittedBy: null, // Anonymous submission
        submitterEmail: formData.email || req.body.email,
        submitterName: formData.name || req.body.name,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        completionTime: req.body.completionTime
      });
      
      // Process file uploads if any
      if (req.files && req.files.length > 0) {
        submission.files = req.files.map(file => ({
          fieldId: file.fieldname,
          filename: file.filename,
          originalFilename: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          url: `/api/forms/public/submissions/files/${file.filename}`
        }));
      }
      
      // Save submission
      await submission.save();
      
      // Update form statistics
      form.stats.submissions += 1;
      await form.save();
      
      // Process submission (create ticket/task, send notifications)
      await processSubmission(submission, form);
      
      res.status(201).json({
        success: true,
        message: 'Form submitted successfully',
        data: {
          submissionId: submission._id,
          redirectUrl: form.submission.redirectUrl,
          successMessage: form.submission.successMessage
        }
      });
    } catch (error) {
      console.error('Error submitting public form:', error);
      res.status(500).json({
        success: false,
        message: 'Error submitting form',
        error: error.message
      });
    }
  },
  
  /**
   * Get all submissions for a form
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getFormSubmissions: async (req, res) => {
    try {
      const { formId } = req.params;
      const { page = 1, limit = 10, status, sort = 'createdAt', order = 'desc' } = req.query;
      
      // Find the form
      const form = await Form.findById(formId);
      
      if (!form) {
        return res.status(404).json({
          success: false,
          message: 'Form not found'
        });
      }
      
      // Check if user has permission to view submissions
      const hasPermission = form.hasViewSubmissionsPermission(req.user);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view submissions for this form'
        });
      }
      
      // Build query
      const query = { form: formId };
      
      // Filter by status
      if (status) {
        query.status = status;
      }
      
      // Count total submissions
      const total = await FormSubmission.countDocuments(query);
      
      // Build sort object
      const sortObj = {};
      sortObj[sort] = order === 'desc' ? -1 : 1;
      
      // Get submissions with pagination
      const submissions = await FormSubmission.find(query)
        .sort(sortObj)
        .skip((page - 1) * limit)
        .limit(parseInt(limit))
        .populate('submittedBy', 'name email')
        .populate('review.reviewedBy', 'name email');
      
      res.status(200).json({
        success: true,
        data: submissions,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      console.error('Error getting form submissions:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting form submissions',
        error: error.message
      });
    }
  },
  
  /**
   * Get a single submission
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getSubmission: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Find submission
      const submission = await FormSubmission.findById(id)
        .populate('submittedBy', 'name email')
        .populate('review.reviewedBy', 'name email')
        .populate('form');
      
      if (!submission) {
        return res.status(404).json({
          success: false,
          message: 'Submission not found'
        });
      }
      
      // Check if user can view this submission
      const canView = await submission.canView(req.user);
      if (!canView) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to view this submission'
        });
      }
      
      res.status(200).json({
        success: true,
        data: submission
      });
    } catch (error) {
      console.error('Error getting submission:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting submission',
        error: error.message
      });
    }
  },
  
  /**
   * Update a submission (for draft submissions or admin review)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateSubmission: async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      // Find submission
      const submission = await FormSubmission.findById(id);
      
      if (!submission) {
        return res.status(404).json({
          success: false,
          message: 'Submission not found'
        });
      }
      
      // Check if user can edit this submission
      const canEdit = await submission.canEdit(req.user);
      if (!canEdit) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this submission'
        });
      }
      
      // Update submission data
      if (updateData.data) {
        // Convert object to Map
        submission.data = new Map(Object.entries(updateData.data));
      }
      
      // Update status
      if (updateData.status) {
        submission.status = updateData.status;
      }
      
      // Update review information
      if (updateData.review) {
        submission.review = {
          ...submission.review,
          ...updateData.review,
          reviewedBy: req.user._id,
          reviewedAt: Date.now()
        };
      }
      
      // Update notes
      if (updateData.notes && updateData.notes.text) {
        submission.notes.push({
          text: updateData.notes.text,
          createdBy: req.user._id,
          createdAt: Date.now()
        });
      }
      
      // Save updated submission
      await submission.save();
      
      // If status changed to submitted, process the submission
      if (updateData.status === 'submitted' && submission.status === 'draft') {
        const form = await Form.findById(submission.form);
        if (form) {
          await processSubmission(submission, form);
        }
      }
      
      res.status(200).json({
        success: true,
        message: 'Submission updated successfully',
        data: submission
      });
    } catch (error) {
      console.error('Error updating submission:', error);
      res.status(500).json({
        success: false,
        message: 'Error updating submission',
        error: error.message
      });
    }
  },
  
  /**
   * Delete a submission
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteSubmission: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Find submission
      const submission = await FormSubmission.findById(id);
      
      if (!submission) {
        return res.status(404).json({
          success: false,
          message: 'Submission not found'
        });
      }
      
      // Check if user has permission to manage the form
      const form = await Form.findById(submission.form);
      if (!form || !form.hasManagePermission(req.user)) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete this submission'
        });
      }
      
      // Delete any associated files
      if (submission.files && submission.files.length > 0) {
        submission.files.forEach(file => {
          if (file.path && fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });
      }
      
      // Delete submission
      await FormSubmission.findByIdAndDelete(id);
      
      // Update form statistics
      form.stats.submissions -= 1;
      await form.save();
      
      res.status(200).json({
        success: true,
        message: 'Submission deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting submission:', error);
      res.status(500).json({
        success: false,
        message: 'Error deleting submission',
        error: error.message
      });
    }
  },
  
  /**
   * Get a submission file
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getSubmissionFile: async (req, res) => {
    try {
      const { filename } = req.params;
      
      // Find submission with this file
      const submission = await FormSubmission.findOne({
        'files.filename': filename
      });
      
      if (!submission) {
        return res.status(404).json({
          success: false,
          message: 'File not found'
        });
      }
      
      // Check if user can view this submission
      const canView = await submission.canView(req.user);
      if (!canView) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this file'
        });
      }
      
      // Find the file in the submission
      const file = submission.files.find(f => f.filename === filename);
      
      if (!file || !file.path || !fs.existsSync(file.path)) {
        return res.status(404).json({
          success: false,
          message: 'File not found on server'
        });
      }
      
      // Set content type
      res.setHeader('Content-Type', file.mimetype);
      res.setHeader('Content-Disposition', `inline; filename="${file.originalFilename}"`);
      
      // Stream the file
      const fileStream = fs.createReadStream(file.path);
      fileStream.pipe(res);
    } catch (error) {
      console.error('Error getting submission file:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting submission file',
        error: error.message
      });
    }
  },
  
  /**
   * Get a public submission file (no authentication required)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getPublicSubmissionFile: async (req, res) => {
    try {
      const { filename } = req.params;
      
      // Find submission with this file
      const submission = await FormSubmission.findOne({
        'files.filename': filename
      }).populate('form');
      
      if (!submission || !submission.form) {
        return res.status(404).json({
          success: false,
          message: 'File not found'
        });
      }
      
      // Check if the form is public
      if (submission.form.permissions.access !== 'public') {
        return res.status(403).json({
          success: false,
          message: 'This file is not publicly accessible'
        });
      }
      
      // Find the file in the submission
      const file = submission.files.find(f => f.filename === filename);
      
      if (!file || !file.path || !fs.existsSync(file.path)) {
        return res.status(404).json({
          success: false,
          message: 'File not found on server'
        });
      }
      
      // Set content type
      res.setHeader('Content-Type', file.mimetype);
      res.setHeader('Content-Disposition', `inline; filename="${file.originalFilename}"`);
      
      // Stream the file
      const fileStream = fs.createReadStream(file.path);
      fileStream.pipe(res);
    } catch (error) {
      console.error('Error getting public submission file:', error);
      res.status(500).json({
        success: false,
        message: 'Error getting submission file',
        error: error.message
      });
    }
  },
  
  // Middleware to handle file uploads
  uploadFiles: (req, res, next) => {
    return (req, res, next) => {
      // First find the form to get allowed file types
      Form.findById(req.params.formId)
        .then(form => {
          if (!form) {
            return res.status(404).json({
              success: false,
              message: 'Form not found'
            });
          }
          
          // Store form in request for later use
          req.form = form;
          
          // Use multer upload
          upload.array('files')(req, res, (err) => {
            if (err) {
              return res.status(400).json({
                success: false,
                message: 'Error uploading files',
                error: err.message
              });
            }
            next();
          });
        })
        .catch(err => {
          res.status(500).json({
            success: false,
            message: 'Error finding form',
            error: err.message
          });
        });
    };
  }
};

/**
 * Helper function to validate form data
 * @param {Object} form - Form object
 * @param {Object} formData - Form data to validate
 * @returns {Array} - Array of validation errors
 */
const validateFormData = (form, formData) => {
  const errors = [];
  
  // Collect all required fields
  const requiredFields = [];
  form.fieldGroups.forEach(group => {
    group.fields.forEach(field => {
      if (field.required) {
        requiredFields.push({
          fieldId: field.fieldId,
          label: field.label,
          type: field.type
        });
      }
    });
  });
  
  // Check if all required fields are present and valid
  requiredFields.forEach(field => {
    const value = formData[field.fieldId];
    
    // Check if field is present
    if (value === undefined || value === null || value === '') {
      errors.push({
        fieldId: field.fieldId,
        message: `${field.label} is required`
      });
      return;
    }
    
    // Type-specific validation
    switch (field.type) {
      case 'email':
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          errors.push({
            fieldId: field.fieldId,
            message: `${field.label} must be a valid email address`
          });
        }
        break;
      case 'number':
        if (isNaN(Number(value))) {
          errors.push({
            fieldId: field.fieldId,
            message: `${field.label} must be a number`
          });
        }
        break;
      // Add more type-specific validation as needed
    }
  });
  
  return errors;
};

/**
 * Helper function to process a form submission
 * @param {Object} submission - FormSubmission object
 * @param {Object} form - Form object
 */
const processSubmission = async (submission, form) => {
  try {
    // Create ticket if enabled
    if (form.submission.createTicket && form.submission.createTicket.enabled) {
      await createTicketFromSubmission(submission, form);
    }
    
    // Create task if enabled
    if (form.submission.createTask && form.submission.createTask.enabled) {
      await createTaskFromSubmission(submission, form);
    }
    
    // Send notifications
    if (form.submission.notifications && form.submission.notifications.length > 0) {
      await sendNotifications(submission, form);
    }
    
    // Update submission processing status
    submission.processing.notificationsSent = true;
    await submission.save();
  } catch (error) {
    console.error('Error processing submission:', error);
    submission.processing.notificationErrors = error.message;
    await submission.save();
  }
};

/**
 * Helper function to create a ticket from a form submission
 * @param {Object} submission - FormSubmission object
 * @param {Object} form - Form object
 */
const createTicketFromSubmission = async (submission, form) => {
  try {
    const ticketData = {};
    const mappings = form.submission.createTicket.fieldMappings;
    const defaults = form.submission.createTicket.defaults;
    
    // Map form fields to ticket fields
    if (mappings.subject && submission.data.has(mappings.subject)) {
      ticketData.subject = submission.data.get(mappings.subject);
    }
    
    if (mappings.description && submission.data.has(mappings.description)) {
      ticketData.description = submission.data.get(mappings.description);
    }
    
    if (mappings.priority && submission.data.has(mappings.priority)) {
      ticketData.priority = submission.data.get(mappings.priority);
    }
    
    if (mappings.category && submission.data.has(mappings.category)) {
      ticketData.category = submission.data.get(mappings.category);
    }
    
    // Apply custom field mappings
    if (mappings.customFields && mappings.customFields.length > 0) {
      ticketData.customFields = {};
      
      mappings.customFields.forEach(mapping => {
        if (submission.data.has(mapping.formField)) {
          ticketData.customFields[mapping.ticketField] = submission.data.get(mapping.formField);
        }
      });
    }
    
    // Apply default values
    if (defaults) {
      if (defaults.assignee) ticketData.assignee = defaults.assignee;
      if (defaults.group) ticketData.group = defaults.group;
      if (defaults.priority && !ticketData.priority) ticketData.priority = defaults.priority;
      if (defaults.tags) ticketData.tags = defaults.tags;
    }
    
    // Set requester information
    if (submission.submittedBy) {
      ticketData.requester = submission.submittedBy;
    } else if (submission.submitterEmail) {
      ticketData.requesterEmail = submission.submitterEmail;
      ticketData.requesterName = submission.submitterName || submission.submitterEmail;
    }
    
    // Add reference to form submission
    ticketData.formSubmission = submission._id;
    
    // Create ticket
    const ticket = new Ticket(ticketData);
    await ticket.save();
    
    // Update submission with ticket reference
    submission.processing.ticketCreated = true;
    submission.processing.ticketId = ticket._id;
    await submission.save();
    
    return ticket;
  } catch (error) {
    console.error('Error creating ticket from submission:', error);
    throw error;
  }
};

/**
 * Helper function to create a task from a form submission
 * @param {Object} submission - FormSubmission object
 * @param {Object} form - Form object
 */
const createTaskFromSubmission = async (submission, form) => {
  try {
    const taskData = {};
    const mappings = form.submission.createTask.fieldMappings;
    const defaults = form.submission.createTask.defaults;
    
    // Map form fields to task fields
    if (mappings.title && submission.data.has(mappings.title)) {
      taskData.title = submission.data.get(mappings.title);
    }
    
    if (mappings.description && submission.data.has(mappings.description)) {
      taskData.description = submission.data.get(mappings.description);
    }
    
    if (mappings.dueDate && submission.data.has(mappings.dueDate)) {
      taskData.dueDate = new Date(submission.data.get(mappings.dueDate));
    }
    
    // Apply custom field mappings
    if (mappings.customFields && mappings.customFields.length > 0) {
      taskData.customFields = {};
      
      mappings.customFields.forEach(mapping => {
        if (submission.data.has(mapping.formField)) {
          taskData.customFields[mapping.taskField] = submission.data.get(mapping.formField);
        }
      });
    }
    
    // Apply default values
    if (defaults) {
      if (defaults.assignee) taskData.assignee = defaults.assignee;
      if (defaults.priority) taskData.priority = defaults.priority;
      if (defaults.tags) taskData.tags = defaults.tags;
    }
    
    // Set creator information
    if (submission.submittedBy) {
      taskData.createdBy = submission.submittedBy;
    }
    
    // Add reference to form submission
    taskData.formSubmission = submission._id;
    
    // Create task
    const task = new Task(taskData);
    await task.save();
    
    // Update submission with task reference
    submission.processing.taskCreated = true;
    submission.processing.taskId = task._id;
    await submission.save();
    
    return task;
  } catch (error) {
    console.error('Error creating task from submission:', error);
    throw error;
  }
};

/**
 * Helper function to send notifications for a form submission
 * @param {Object} submission - FormSubmission object
 * @param {Object} form - Form object
 */
const sendNotifications = async (submission, form) => {
  try {
    const notifications = form.submission.notifications;
    
    // Skip if no notifications
    if (!notifications || notifications.length === 0) {
      return;
    }
    
    // Get email transport
    const transporter = await getEmailTransporter();
    
    // Process each notification
    for (const notification of notifications) {
      // Check if notification should be sent based on trigger
      if (notification.trigger === 'on_submit') {
        // Always send for on_submit trigger
      } else if (notification.trigger === 'on_approval' && submission.status !== 'approved') {
        continue;
      } else if (notification.trigger === 'on_rejection' && submission.status !== 'rejected') {
        continue;
      } else if (notification.trigger === 'custom') {
        // Check custom trigger conditions
        let shouldSend = true;
        
        if (notification.triggerConditions && notification.triggerConditions.length > 0) {
          shouldSend = notification.triggerConditions.every(condition => {
            if (!submission.data.has(condition.fieldId)) {
              return false;
            }
            
            const value = submission.data.get(condition.fieldId);
            
            switch (condition.operator) {
              case 'equals':
                return value === condition.value;
              case 'not_equals':
                return value !== condition.value;
              case 'contains':
                return String(value).includes(String(condition.value));
              case 'not_contains':
                return !String(value).includes(String(condition.value));
              case 'greater_than':
                return Number(value) > Number(condition.value);
              case 'less_than':
                return Number(value) < Number(condition.value);
              default:
                return false;
            }
          });
        }
        
        if (!shouldSend) {
          continue;
        }
      }
      
      // Determine recipients
      let recipients = [];
      
      if (notification.type === 'admin') {
        // Send to form creator
        const creator = await User.findById(form.createdBy);
        if (creator && creator.email) {
          recipients.push(creator.email);
        }
      } else if (notification.type === 'user' && submission.submitterEmail) {
        // Send to form submitter
        recipients.push(submission.submitterEmail);
      } else if (notification.type === 'custom' && notification.recipients) {
        // Use custom recipients
        recipients = notification.recipients;
      }
      
      // Skip if no recipients
      if (recipients.length === 0) {
        continue;
      }
      
      // Prepare email content
      let subject = notification.subject || `Form Submission: ${form.title}`;
      let template = notification.template || 'Thank you for your submission.';
      
      // Replace variables in template
      template = replaceTemplateVariables(template, submission, form);
      
      // Send email
      await transporter.sendMail({
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: recipients.join(','),
        subject,
        html: template
      });
    }
  } catch (error) {
    console.error('Error sending notifications:', error);
    throw error;
  }
};

/**
 * Helper function to get email transporter
 * @returns {Object} - Nodemailer transporter
 */
const getEmailTransporter = async () => {
  // Create reusable transporter using SMTP transport
  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD
    }
  });
};

/**
 * Helper function to replace template variables
 * @param {String} template - Email template
 * @param {Object} submission - FormSubmission object
 * @param {Object} form - Form object
 * @returns {String} - Template with variables replaced
 */
const replaceTemplateVariables = (template, submission, form) => {
  // Replace form variables
  template = template.replace(/\{form\.title\}/g, form.title);
  template = template.replace(/\{form\.description\}/g, form.description || '');
  
  // Replace submission variables
  template = template.replace(/\{submission\.id\}/g, submission._id.toString());
  template = template.replace(/\{submission\.date\}/g, submission.createdAt.toLocaleString());
  
  // Replace submitter variables
  template = template.replace(/\{submitter\.name\}/g, submission.submitterName || 'Anonymous');
  template = template.replace(/\{submitter\.email\}/g, submission.submitterEmail || 'No email provided');
  
  // Replace field variables
  submission.data.forEach((value, key) => {
    template = template.replace(new RegExp(`\\{field\\.${key}\\}`, 'g'), value);
  });
  
  return template;
};

module.exports = formSubmissionController;