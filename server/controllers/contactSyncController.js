const GooglePeopleAPI = require('../integrations/googlePeople/googlePeopleAPI');
const AppleContactsAPI = require('../integrations/appleContacts/appleContactsAPI');
const ContactSyncConfig = require('../../models/ContactSyncConfig');
const ContactSyncLog = require('../../models/ContactSyncLog');
const Contact = require('../../models/Contact');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);

/**
 * Contact Sync Controller
 * Handles API endpoints for contacts synchronization with Google and Apple
 */
const contactSyncController = {
  /**
   * Get Google OAuth URL
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getGoogleAuthUrl: async (req, res) => {
    try {
      const googlePeopleAPI = new GooglePeopleAPI();
      const authUrl = googlePeopleAPI.generateAuthUrl();
      res.json({ authUrl });
    } catch (error) {
      console.error('Error generating Google auth URL:', error);
      res.status(500).json({ message: 'Error generating Google auth URL', error: error.message });
    }
  },

  /**
   * Handle Google OAuth callback
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  handleGoogleCallback: async (req, res) => {
    try {
      const { code } = req.query;
      const userId = req.user.id;

      if (!code) {
        return res.status(400).json({ message: 'Authorization code is required' });
      }

      // Get tokens from authorization code
      const googlePeopleAPI = new GooglePeopleAPI();
      const tokens = await googlePeopleAPI.getTokensFromCode(code);

      if (!tokens.refresh_token) {
        return res.status(400).json({ 
          message: 'No refresh token received. Please revoke access and try again.' 
        });
      }

      // Save or update sync configuration
      let syncConfig = await ContactSyncConfig.findOne({ user: userId, provider: 'google' });

      if (syncConfig) {
        syncConfig.googleRefreshToken = tokens.refresh_token;
        syncConfig.enabled = true;
        syncConfig.syncStatus = 'idle';
        syncConfig.syncError = null;
      } else {
        syncConfig = new ContactSyncConfig({
          user: userId,
          provider: 'google',
          googleRefreshToken: tokens.refresh_token,
          enabled: true
        });
      }

      await syncConfig.save();

      // Redirect to phone book page
      res.redirect('/phone-book?sync=google-connected');
    } catch (error) {
      console.error('Error handling Google callback:', error);
      res.status(500).json({ message: 'Error handling Google callback', error: error.message });
    }
  },

  /**
   * Configure Google sync settings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  configureGoogleSync: async (req, res) => {
    try {
      const userId = req.user.id;
      const { enabled, syncFrequency, syncCategories } = req.body;

      // Find or create sync configuration
      let syncConfig = await ContactSyncConfig.findOne({ user: userId, provider: 'google' });

      if (!syncConfig) {
        return res.status(404).json({ message: 'Google sync not configured. Please connect to Google first.' });
      }

      // Update configuration
      if (enabled !== undefined) syncConfig.enabled = enabled;
      if (syncFrequency !== undefined) syncConfig.syncFrequency = syncFrequency;
      if (syncCategories !== undefined) syncConfig.syncCategories = syncCategories;

      await syncConfig.save();

      res.json(syncConfig);
    } catch (error) {
      console.error('Error configuring Google sync:', error);
      res.status(500).json({ message: 'Error configuring Google sync', error: error.message });
    }
  },

  /**
   * Trigger immediate sync with Google
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  syncNowGoogle: async (req, res) => {
    try {
      const userId = req.user.id;

      // Find sync configuration
      const syncConfig = await ContactSyncConfig.findOne({ user: userId, provider: 'google' });

      if (!syncConfig) {
        return res.status(404).json({ message: 'Google sync not configured. Please connect to Google first.' });
      }

      if (!syncConfig.googleRefreshToken) {
        return res.status(400).json({ message: 'Google refresh token not found. Please reconnect to Google.' });
      }

      // Create sync log entry
      const syncLog = new ContactSyncLog({
        user: userId,
        provider: 'google',
        status: 'in_progress'
      });

      await syncLog.save();

      // Update sync status
      syncConfig.syncStatus = 'in_progress';
      syncConfig.lastSyncTime = new Date();
      await syncConfig.save();

      // Start sync process in background
      contactSyncController._performGoogleSync(userId, syncConfig, syncLog)
        .catch(error => {
          console.error('Error in background Google sync:', error);
        });

      res.json({ message: 'Sync started', syncLog });
    } catch (error) {
      console.error('Error starting Google sync:', error);
      res.status(500).json({ message: 'Error starting Google sync', error: error.message });
    }
  },

  /**
   * Get Google sync status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getGoogleSyncStatus: async (req, res) => {
    try {
      const userId = req.user.id;

      // Find sync configuration
      const syncConfig = await ContactSyncConfig.findOne({ user: userId, provider: 'google' });

      if (!syncConfig) {
        return res.json({ configured: false });
      }

      // Get latest sync log
      const latestLog = await ContactSyncLog.findOne({ 
        user: userId, 
        provider: 'google' 
      }).sort({ startTime: -1 });

      res.json({
        configured: true,
        enabled: syncConfig.enabled,
        syncFrequency: syncConfig.syncFrequency,
        syncCategories: syncConfig.syncCategories,
        syncStatus: syncConfig.syncStatus,
        lastSyncTime: syncConfig.lastSyncTime,
        latestLog
      });
    } catch (error) {
      console.error('Error getting Google sync status:', error);
      res.status(500).json({ message: 'Error getting Google sync status', error: error.message });
    }
  },

  /**
   * Generate vCard file for Apple contacts
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  generateAppleVCard: async (req, res) => {
    try {
      const userId = req.user.id;
      const categories = req.query.categories ? req.query.categories.split(',') : null;

      // Get contacts
      let contacts = await Contact.find({});

      // Filter by categories if specified
      if (categories && categories.length > 0) {
        contacts = contacts.filter(contact => categories.includes(contact.category));
      }

      if (contacts.length === 0) {
        return res.status(404).json({ message: 'No contacts found' });
      }

      // Generate vCard file
      const appleContactsAPI = new AppleContactsAPI();
      const filename = appleContactsAPI.generateUniqueFilename(userId);
      const filePath = await appleContactsAPI.generateVCardFile(contacts, filename);

      // Create sync log entry
      const syncLog = new ContactSyncLog({
        user: userId,
        provider: 'apple',
        status: 'success',
        contactsAdded: contacts.length,
        contactsUpdated: 0,
        contactsRemoved: 0,
        endTime: new Date(),
        details: `Generated vCard file with ${contacts.length} contacts`
      });

      await syncLog.save();

      // Schedule cleanup of temporary file
      appleContactsAPI.scheduleCleanup(filePath);

      // Send file
      res.download(filePath, 'contacts.vcf', (err) => {
        if (err) {
          console.error('Error sending vCard file:', err);
        }
      });
    } catch (error) {
      console.error('Error generating Apple vCard:', error);
      res.status(500).json({ message: 'Error generating Apple vCard', error: error.message });
    }
  },

  /**
   * Configure Apple sync settings
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  configureAppleSync: async (req, res) => {
    try {
      const userId = req.user.id;
      const { enabled, syncFrequency, syncCategories } = req.body;

      // Find or create sync configuration
      let syncConfig = await ContactSyncConfig.findOne({ user: userId, provider: 'apple' });

      if (!syncConfig) {
        syncConfig = new ContactSyncConfig({
          user: userId,
          provider: 'apple',
          enabled: true
        });
      }

      // Update configuration
      if (enabled !== undefined) syncConfig.enabled = enabled;
      if (syncFrequency !== undefined) syncConfig.syncFrequency = syncFrequency;
      if (syncCategories !== undefined) syncConfig.syncCategories = syncCategories;

      await syncConfig.save();

      res.json(syncConfig);
    } catch (error) {
      console.error('Error configuring Apple sync:', error);
      res.status(500).json({ message: 'Error configuring Apple sync', error: error.message });
    }
  },

  /**
   * Get Apple sync status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAppleSyncStatus: async (req, res) => {
    try {
      const userId = req.user.id;

      // Find sync configuration
      const syncConfig = await ContactSyncConfig.findOne({ user: userId, provider: 'apple' });

      if (!syncConfig) {
        return res.json({ configured: false });
      }

      // Get latest sync log
      const latestLog = await ContactSyncLog.findOne({ 
        user: userId, 
        provider: 'apple' 
      }).sort({ startTime: -1 });

      res.json({
        configured: true,
        enabled: syncConfig.enabled,
        syncFrequency: syncConfig.syncFrequency,
        syncCategories: syncConfig.syncCategories,
        syncStatus: syncConfig.syncStatus,
        lastSyncTime: syncConfig.lastSyncTime,
        latestLog
      });
    } catch (error) {
      console.error('Error getting Apple sync status:', error);
      res.status(500).json({ message: 'Error getting Apple sync status', error: error.message });
    }
  },

  /**
   * Get sync logs
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getSyncLogs: async (req, res) => {
    try {
      const userId = req.user.id;
      const { provider, limit = 10, offset = 0 } = req.query;

      // Build query
      const query = { user: userId };
      if (provider) query.provider = provider;

      // Get logs
      const logs = await ContactSyncLog.find(query)
        .sort({ startTime: -1 })
        .skip(parseInt(offset))
        .limit(parseInt(limit));

      // Get total count
      const total = await ContactSyncLog.countDocuments(query);

      res.json({
        logs,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      });
    } catch (error) {
      console.error('Error getting sync logs:', error);
      res.status(500).json({ message: 'Error getting sync logs', error: error.message });
    }
  },

  /**
   * Perform Google sync (internal method)
   * @param {string} userId - User ID
   * @param {Object} syncConfig - Sync configuration
   * @param {Object} syncLog - Sync log
   * @private
   */
  _performGoogleSync: async (userId, syncConfig, syncLog) => {
    try {
      // Initialize Google People API
      const googlePeopleAPI = new GooglePeopleAPI({
        refreshToken: syncConfig.googleRefreshToken
      });

      await googlePeopleAPI.initialize();

      // Get or create contact group
      const groupName = 'CSF Portal Contacts';
      const contactGroup = await googlePeopleAPI.getOrCreateContactGroup(groupName);
      
      // Update sync config with group resource name
      syncConfig.googleResourceName = contactGroup.resourceName;
      await syncConfig.save();

      // Get contacts from database
      let contacts = await Contact.find({});

      // Filter by categories if specified
      if (syncConfig.syncCategories && syncConfig.syncCategories.length > 0) {
        contacts = contacts.filter(contact => 
          syncConfig.syncCategories.includes(contact.category)
        );
      }

      // Get existing contacts from Google
      const googleContacts = await googlePeopleAPI.getAllContacts();

      // Initialize counters
      let added = 0;
      let updated = 0;
      let removed = 0;

      // Process each contact
      for (const contact of contacts) {
        // Check if contact already exists in Google
        const contactId = contact._id.toString();
        const resourceName = syncConfig.contactMappings && syncConfig.contactMappings.get(contactId);
        
        if (resourceName) {
          // Contact exists, update it
          const existingContact = googleContacts.find(c => c.resourceName === resourceName);
          
          if (existingContact) {
            await googlePeopleAPI.updateContact(resourceName, contact);
            updated++;
          } else {
            // Contact was deleted in Google, recreate it
            const newContact = await googlePeopleAPI.createContact(contact, contactGroup.resourceName);
            
            // Update mapping
            if (!syncConfig.contactMappings) {
              syncConfig.contactMappings = new Map();
            }
            syncConfig.contactMappings.set(contactId, newContact.resourceName);
            added++;
          }
        } else {
          // Contact doesn't exist, create it
          const newContact = await googlePeopleAPI.createContact(contact, contactGroup.resourceName);
          
          // Update mapping
          if (!syncConfig.contactMappings) {
            syncConfig.contactMappings = new Map();
          }
          syncConfig.contactMappings.set(contactId, newContact.resourceName);
          added++;
        }
      }

      // Update sync log
      syncLog.status = 'success';
      syncLog.contactsAdded = added;
      syncLog.contactsUpdated = updated;
      syncLog.contactsRemoved = removed;
      syncLog.endTime = new Date();
      syncLog.details = `Synced ${contacts.length} contacts with Google`;
      await syncLog.save();

      // Update sync config
      syncConfig.syncStatus = 'success';
      syncConfig.syncError = null;
      syncConfig.lastSyncTime = new Date();
      await syncConfig.save();
    } catch (error) {
      console.error('Error performing Google sync:', error);

      // Update sync log
      syncLog.status = 'error';
      syncLog.error = error.message;
      syncLog.endTime = new Date();
      await syncLog.save();

      // Update sync config
      syncConfig.syncStatus = 'error';
      syncConfig.syncError = error.message;
      await syncConfig.save();
    }
  }
};

module.exports = contactSyncController;