const GoogleFormsAPI = require('../integrations/googleForms/googleFormsAPI');
const User = require('../../models/User');
const path = require('path');
const fs = require('fs');

// Check if service account credentials are available (primary authentication method)
const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
const adminImpersonationEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;
const usingServiceAccount = !!(serviceAccountEmail && serviceAccountPrivateKey);

// OAuth fallback variables (for backwards compatibility only)
const clientId = process.env.GOOGLE_FORMS_CLIENT_ID || '';
const clientSecret = process.env.GOOGLE_FORMS_CLIENT_SECRET || '';
const redirectUri = process.env.GOOGLE_FORMS_REDIRECT_URI || '';
const tokenPath = path.resolve(process.cwd(), process.env.GOOGLE_FORMS_TOKEN_PATH || './google-forms-token.json');

// Create a global API instance with admin impersonation email for service account auth
let googleFormsAPI = new GoogleFormsAPI(
  clientId,
  clientSecret,
  redirectUri,
  tokenPath,
  null, // No user tokens
  null, // No user ID
  adminImpersonationEmail // Use admin impersonation email for service account
);

// Initialize the API immediately to ensure it's ready for use
(async () => {
  try {
    // Only initialize if we have the required credentials
    if ((usingServiceAccount && serviceAccountEmail && serviceAccountPrivateKey && adminImpersonationEmail) || 
        (!usingServiceAccount && clientId && clientSecret && redirectUri)) {
      console.log('Initializing Google Forms API with service account...');
      await googleFormsAPI.initialize();
      console.log('Google Forms API initialized successfully');
    } else {
      console.log('Skipping Google Forms API initialization due to missing credentials');
    }
  } catch (error) {
    console.error('Error initializing Google Forms API on startup:', error);
  }
})();

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (usingServiceAccount) {
    if (!serviceAccountEmail || !serviceAccountPrivateKey) {
      throw new Error('Google service account configuration is missing. Please check your environment variables.');
    }
    if (!adminImpersonationEmail) {
      throw new Error('Google admin impersonation email is missing. Please check your GOOGLE_ADMIN_IMPERSONATION_EMAIL environment variable.');
    }
  } else if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Google Forms configuration is missing. Please check your environment variables.');
  }
  return true;
};

// Helper function to get an API instance with user email for service account impersonation
// or fall back to user tokens if service account is not configured
const getApiWithUser = async (userId) => {
  try {
    // Ensure environment variables are set
    ensureApiInitialized();

    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (usingServiceAccount) {
      // Use service account with user impersonation
      if (!user.email) {
        throw new Error('User email is required for service account impersonation');
      }

      const api = new GoogleFormsAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        null, // No user tokens
        userId,
        user.email // Pass the user email for service account impersonation
      );

      await api.initialize();
      return api;
    } else {
      // Fall back to OAuth tokens if service account is not configured
      if (!user.googleAccessToken) {
        throw new Error('User missing Google tokens and service account not configured');
      }

      // Create a new API instance with the user's tokens
      const userTokens = {
        accessToken: user.googleAccessToken,
        refreshToken: user.googleRefreshToken
      };

      const api = new GoogleFormsAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        userTokens,
        userId
      );

      await api.initialize();
      return api;
    }
  } catch (error) {
    console.error('Error creating API with user:', error);
    throw error;
  }
};

// Helper function to get the best available API instance
const getApiInstance = async (req) => {
  let api;

  // Try to use the user's credentials if available
  if (req.user && req.user.id) {
    // Check if user is authenticated via Google
    if (req.user.authType !== 'google') {
      throw new Error('You are not logged in using Google authentication. Google Forms integration will not work. Please log in with your Google account.');
    }
    
    try {
      api = await getApiWithUser(req.user.id);
      return api;
    } catch (userError) {
      console.log('Could not use user credentials, falling back to global config:', userError.message);
      // Fall back to global config if user credentials don't work
    }
  }

  // No user in the request or user credentials failed, use global config
  // Ensure environment variables are set
  ensureApiInitialized();

  // Check if the global API is already authenticated
  if (!googleFormsAPI.isAuthenticated()) {
    console.log('Global Google Forms API not authenticated, initializing...');
    try {
      // Re-initialize with admin impersonation email to ensure it's using service account
      if (adminImpersonationEmail) {
        // Update the user email for service account impersonation if needed
        googleFormsAPI.userEmail = adminImpersonationEmail;
      }
      
      // Initialize the API
      await googleFormsAPI.initialize();
      console.log('Global Google Forms API initialized successfully');
    } catch (error) {
      console.error('Error initializing Google Forms API:', error);
      throw error; // Propagate the error to handle it in the route handler
    }
  }

  return googleFormsAPI;
};

/**
 * Save Google Forms configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Google Forms configuration request:', error);
    res.status(500).json({ message: 'Error handling Google Forms configuration request', error: error.message });
  }
};

/**
 * Get Google Forms configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // First check if the user has Google tokens
    let userHasTokens = false;
    let userAuthStatus = {
      isAuthenticated: false,
      message: ''
    };

    if (req.user && req.user.id) {
      const user = await User.findById(req.user.id);
      if (user && user.googleAccessToken && user.googleRefreshToken) {
        userHasTokens = true;
        userAuthStatus = {
          isAuthenticated: true,
          message: 'You are authenticated with Google Forms using your Google account'
        };
      }
    }

    // Ensure environment variables are set
    ensureApiInitialized();

    // Check if environment variables are being used
    const usingEnvVars = {
      serviceAccount: !!(process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && 
                        process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY),
      clientId: !!process.env.GOOGLE_FORMS_CLIENT_ID,
      clientSecret: !!process.env.GOOGLE_FORMS_CLIENT_SECRET,
      redirectUri: !!process.env.GOOGLE_FORMS_REDIRECT_URI,
      tokenPath: !!process.env.GOOGLE_FORMS_TOKEN_PATH
    };

    // Check if the global API is authenticated
    const globalAuthStatus = {
      isAuthenticated: googleFormsAPI ? googleFormsAPI.isAuthenticated() : false
    };

    // If user has tokens, they're authenticated regardless of global config
    if (userHasTokens) {
      return res.json({
        userAuth: userAuthStatus,
        globalAuth: globalAuthStatus,
        isAuthenticated: true,
        clientId: process.env.GOOGLE_FORMS_CLIENT_ID,
        redirectUri: process.env.GOOGLE_FORMS_REDIRECT_URI,
        tokenPath: process.env.GOOGLE_FORMS_TOKEN_PATH,
        configuredAt: new Date(),
        usingEnvVars
      });
    }

    // If no user tokens but we have a global config
    return res.json({
      userAuth: userAuthStatus,
      globalAuth: globalAuthStatus,
      isAuthenticated: globalAuthStatus.isAuthenticated,
      clientId: process.env.GOOGLE_FORMS_CLIENT_ID,
      redirectUri: process.env.GOOGLE_FORMS_REDIRECT_URI,
      tokenPath: process.env.GOOGLE_FORMS_TOKEN_PATH,
      configuredAt: new Date(),
      usingEnvVars
    });
  } catch (error) {
    console.error('Error fetching Google Forms configuration:', error);
    res.status(500).json({ message: 'Error fetching Google Forms configuration', error: error.message });
  }
};

/**
 * Get authentication URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAuthUrl = async (req, res) => {
  try {
    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Google Forms',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    // Check if user has Google tokens
    const user = await User.findById(req.user.id);
    if (!user.googleAccessToken || !user.googleRefreshToken) {
      return res.status(401).json({ 
        message: 'You need to authenticate with Google. Please log out and log back in.',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    // Ensure environment variables are set
    ensureApiInitialized();

    // Get the auth URL from the global API
    const authUrl = googleFormsAPI.getAuthUrl();
    return res.json({ 
      message: 'You can use your Google account or the global configuration',
      userAuth: true,
      globalAuthUrl: authUrl 
    });
  } catch (error) {
    console.error('Error generating Google Forms auth URL:', error);
    res.status(500).json({ message: 'Error generating Google Forms auth URL', error: error.message });
  }
};

/**
 * Handle OAuth2 callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code } = req.query;

    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Google Forms',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    // Ensure environment variables are set
    ensureApiInitialized();

    // Get tokens from Google
    await googleFormsAPI.getToken(code);

    // For user authentication, we don't need to handle the callback here
    // The main Google auth callback already stores the tokens in the user model

    res.json({ 
      message: 'Google Forms authentication successful',
      userAuth: true,
      globalAuth: true
    });
  } catch (error) {
    console.error('Error handling Google Forms callback:', error);
    res.status(500).json({ message: 'Error handling Google Forms callback', error: error.message });
  }
};

/**
 * List forms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listForms = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Forms',
        authUrl: api.getAuthUrl()
      });
    }

    // Get the user's email from the request
    let userEmail = null;
    if (req.user && req.user.email) {
      userEmail = req.user.email;
    }

    // If we have a user email, filter forms by permission
    // Otherwise, fall back to listing all forms the authenticated service can access
    const forms = userEmail 
      ? await api.listForms(userEmail) 
      : await api.listForms();

    // Return the forms with a message indicating how they were filtered
    res.json({
      message: userEmail 
        ? `Showing forms available to ${userEmail}` 
        : 'Showing all forms available to the authenticated service',
      forms: forms
    });
  } catch (error) {
    console.error('Error listing forms from Google Forms:', error);

    if (error.message === 'Google Forms configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error listing forms from Google Forms', error: error.message });
  }
};

/**
 * Get form details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getForm = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Forms',
        authUrl: api.getAuthUrl()
      });
    }

    const { formId } = req.params;

    if (!formId) {
      return res.status(400).json({ message: 'Form ID is required' });
    }

    const form = await api.getForm(formId);
    res.json(form);
  } catch (error) {
    console.error('Error getting form from Google Forms:', error);

    if (error.message === 'Google Forms configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting form from Google Forms', error: error.message });
  }
};

/**
 * Get form responses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFormResponses = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Forms',
        authUrl: api.getAuthUrl()
      });
    }

    const { formId } = req.params;

    if (!formId) {
      return res.status(400).json({ message: 'Form ID is required' });
    }

    const responses = await api.getFormResponses(formId);
    res.json(responses);
  } catch (error) {
    console.error('Error getting form responses from Google Forms:', error);

    if (error.message === 'Google Forms configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting form responses from Google Forms', error: error.message });
  }
};

/**
 * Create a new form
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createForm = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Forms',
        authUrl: api.getAuthUrl()
      });
    }

    const formData = req.body;

    if (!formData || !formData.info || !formData.info.title) {
      return res.status(400).json({ message: 'Form data with at least a title is required' });
    }

    const form = await api.createForm(formData);
    res.json(form);
  } catch (error) {
    console.error('Error creating form in Google Forms:', error);

    if (error.message === 'Google Forms configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error creating form in Google Forms', error: error.message });
  }
};

/**
 * Update an existing form
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateForm = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Forms',
        authUrl: api.getAuthUrl()
      });
    }

    const { formId } = req.params;
    const formData = req.body;

    if (!formId) {
      return res.status(400).json({ message: 'Form ID is required' });
    }

    if (!formData) {
      return res.status(400).json({ message: 'Form data is required' });
    }

    const form = await api.updateForm(formId, formData);
    res.json(form);
  } catch (error) {
    console.error('Error updating form in Google Forms:', error);

    if (error.message === 'Google Forms configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error updating form in Google Forms', error: error.message });
  }
};
