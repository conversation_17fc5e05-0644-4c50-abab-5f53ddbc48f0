const Task = require('../../models/Task');
const MaintenanceTask = require('../../models/MaintenanceTask');
const { validationResult } = require('express-validator');

/**
 * Task Controller
 * Handles API operations for tasks
 */
const taskController = {
  /**
   * Get all tasks
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with tasks
   */
  getAllTasks: async (req, res) => {
    try {
      // If user is admin or has task_manager role, return all tasks
      // Otherwise, return only the user's tasks or tasks assigned to them
      let tasks;
      if (req.user.roles.includes('admin') || req.user.roles.includes('task_manager')) {
        tasks = await Task.find()
          .sort({ createdAt: -1 })
          .populate('createdBy', 'name email')
          .populate('assignedTo', 'name email')
          .populate('completedBy', 'name email');
      } else {
        tasks = await Task.find({ 
          $or: [
            { createdBy: req.user.id },
            { assignedTo: req.user.id }
          ]
        })
          .sort({ createdAt: -1 })
          .populate('createdBy', 'name email')
          .populate('assignedTo', 'name email')
          .populate('completedBy', 'name email');
      }
      
      res.json(tasks);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get task by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with task
   */
  getTaskById: async (req, res) => {
    try {
      const task = await Task.findById(req.params.id)
        .populate('createdBy', 'name email')
        .populate('assignedTo', 'name email')
        .populate('completedBy', 'name email')
        .populate('comments.user', 'name email')
        .populate('attachments.uploadedBy', 'name email')
        .populate('dependencies.task', 'title status');
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to view this task
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy._id.toString() !== req.user.id &&
          (task.assignedTo && task.assignedTo._id.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to view this task' });
      }
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Create a new task
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created task
   */
  createTask: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        description, 
        priority, 
        status,
        dueDate,
        recurrence,
        assignedTo,
        tags,
        taskType,
        // Maintenance task specific fields
        location,
        category,
        equipment,
        maintenanceType,
        estimatedDuration,
        safetyRequirements,
        specialInstructions,
        approval,
        checklist
      } = req.body;

      // Create task object based on task type
      let taskData = {
        title,
        description,
        priority,
        status,
        dueDate,
        recurrence,
        assignedTo,
        tags,
        createdBy: req.user.id
      };

      let task;

      // Create the appropriate task type
      if (taskType === 'maintenance') {
        task = new MaintenanceTask({
          ...taskData,
          location,
          category,
          equipment,
          maintenanceType,
          estimatedDuration,
          safetyRequirements,
          specialInstructions,
          approval,
          checklist
        });
      } else {
        task = new Task(taskData);
      }

      await task.save();
      
      // Populate references for response
      await task.populate('createdBy', 'name email');
      if (assignedTo) {
        await task.populate('assignedTo', 'name email');
      }
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Update a task
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated task
   */
  updateTask: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const task = await Task.findById(req.params.id);
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to update this task
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy.toString() !== req.user.id &&
          (task.assignedTo && task.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to update this task' });
      }
      
      const { 
        title, 
        description, 
        priority, 
        status,
        dueDate,
        recurrence,
        assignedTo,
        tags,
        // Maintenance task specific fields
        location,
        category,
        equipment,
        maintenanceType,
        estimatedDuration,
        actualDuration,
        materials,
        safetyRequirements,
        specialInstructions,
        approval,
        checklist
      } = req.body;

      // Update basic task fields
      if (title) task.title = title;
      if (description !== undefined) task.description = description;
      if (priority) task.priority = priority;
      if (status) {
        // If status is changing to completed, set completedBy and completedAt
        if (status === 'completed' && task.status !== 'completed') {
          task.completedBy = req.user.id;
          task.completedAt = Date.now();
        }
        // If status is changing from completed to something else, clear completion data
        if (status !== 'completed' && task.status === 'completed') {
          task.completedBy = undefined;
          task.completedAt = undefined;
        }
        task.status = status;
      }
      if (dueDate !== undefined) task.dueDate = dueDate;
      if (recurrence !== undefined) task.recurrence = recurrence;
      if (assignedTo !== undefined) task.assignedTo = assignedTo;
      if (tags !== undefined) task.tags = tags;

      // Update maintenance task specific fields if applicable
      if (task.taskType === 'maintenance') {
        if (location !== undefined) task.location = location;
        if (category !== undefined) task.category = category;
        if (equipment !== undefined) task.equipment = equipment;
        if (maintenanceType !== undefined) task.maintenanceType = maintenanceType;
        if (estimatedDuration !== undefined) task.estimatedDuration = estimatedDuration;
        if (actualDuration !== undefined) task.actualDuration = actualDuration;
        if (materials !== undefined) task.materials = materials;
        if (safetyRequirements !== undefined) task.safetyRequirements = safetyRequirements;
        if (specialInstructions !== undefined) task.specialInstructions = specialInstructions;
        if (approval !== undefined) task.approval = approval;
        if (checklist !== undefined) task.checklist = checklist;
      }

      await task.save();
      
      // Populate references for response
      await task.populate('createdBy', 'name email');
      if (task.assignedTo) {
        await task.populate('assignedTo', 'name email');
      }
      if (task.completedBy) {
        await task.populate('completedBy', 'name email');
      }
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Delete a task
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message
   */
  deleteTask: async (req, res) => {
    try {
      const task = await Task.findById(req.params.id);
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to delete this task
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to delete this task' });
      }
      
      await task.remove();
      
      res.json({ msg: 'Task removed' });
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Assign a task to a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated task
   */
  assignTask: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const task = await Task.findById(req.params.id);
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to assign this task
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to assign this task' });
      }
      
      task.assignedTo = req.body.assignedTo;
      if (task.status === 'open') {
        task.status = 'in_progress';
      }

      // Add a comment about the assignment
      task.comments.push({
        user: req.user.id,
        text: `Task assigned to user ID: ${req.body.assignedTo}`,
        date: Date.now()
      });

      await task.save();
      
      // Populate references for response
      await task.populate('createdBy', 'name email');
      await task.populate('assignedTo', 'name email');
      await task.populate('comments.user', 'name email');
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Update task status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated task
   */
  updateStatus: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const task = await Task.findById(req.params.id);
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to update this task's status
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy.toString() !== req.user.id &&
          (task.assignedTo && task.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to update this task status' });
      }
      
      const { status, comment } = req.body;
      
      // Update status
      task.status = status;
      
      // If status is completed, set completedBy and completedAt
      if (status === 'completed') {
        task.completedBy = req.user.id;
        task.completedAt = Date.now();
      }
      
      // Add the comment
      task.comments.push({
        user: req.user.id,
        text: comment,
        date: Date.now()
      });

      await task.save();
      
      // Populate references for response
      await task.populate('createdBy', 'name email');
      if (task.assignedTo) {
        await task.populate('assignedTo', 'name email');
      }
      if (task.completedBy) {
        await task.populate('completedBy', 'name email');
      }
      await task.populate('comments.user', 'name email');
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Add a comment to a task
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated task
   */
  addComment: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const task = await Task.findById(req.params.id);
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to comment on this task
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy.toString() !== req.user.id &&
          (task.assignedTo && task.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to comment on this task' });
      }
      
      const { text, isInternal } = req.body;
      
      // Add the comment
      task.comments.push({
        user: req.user.id,
        text,
        date: Date.now(),
        isInternal: isInternal || false
      });

      await task.save();
      
      // Populate references for response
      await task.populate('comments.user', 'name email');
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Add an attachment to a task
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated task
   */
  addAttachment: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const task = await Task.findById(req.params.id);
      
      if (!task) {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      // Check if user is authorized to add attachments to this task
      if (!req.user.roles.includes('admin') && 
          !req.user.roles.includes('task_manager') && 
          task.createdBy.toString() !== req.user.id &&
          (task.assignedTo && task.assignedTo.toString() !== req.user.id)) {
        return res.status(403).json({ msg: 'Not authorized to add attachments to this task' });
      }
      
      const { name, url, type } = req.body;
      
      // Add the attachment
      task.attachments.push({
        name,
        url,
        type,
        uploadedBy: req.user.id,
        uploadedAt: Date.now()
      });

      // Add a comment about the attachment
      task.comments.push({
        user: req.user.id,
        text: `Added attachment: ${name}`,
        date: Date.now()
      });

      await task.save();
      
      // Populate references for response
      await task.populate('attachments.uploadedBy', 'name email');
      await task.populate('comments.user', 'name email');
      
      res.json(task);
    } catch (err) {
      console.error(err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'Task not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get tasks by filter
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with filtered tasks
   */
  getTasksByFilter: async (req, res) => {
    try {
      const { 
        status, 
        priority, 
        taskType, 
        assignedTo, 
        createdBy,
        dueDate,
        tags,
        search
      } = req.query;

      // Build filter object
      const filter = {};
      
      // Add filters based on query parameters
      if (status) filter.status = status;
      if (priority) filter.priority = priority;
      if (taskType) filter.taskType = taskType;
      if (assignedTo) filter.assignedTo = assignedTo;
      if (createdBy) filter.createdBy = createdBy;
      if (tags) {
        // Handle comma-separated tags
        const tagArray = tags.split(',');
        filter.tags = { $in: tagArray };
      }
      
      // Handle due date filtering
      if (dueDate) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (dueDate === 'overdue') {
          filter.dueDate = { $lt: today };
          filter.status = { $ne: 'completed' };
        } else if (dueDate === 'today') {
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          filter.dueDate = { $gte: today, $lt: tomorrow };
        } else if (dueDate === 'week') {
          const nextWeek = new Date(today);
          nextWeek.setDate(nextWeek.getDate() + 7);
          filter.dueDate = { $gte: today, $lt: nextWeek };
        }
      }
      
      // Handle text search
      if (search) {
        filter.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      // Apply role-based filtering
      if (!req.user.roles.includes('admin') && !req.user.roles.includes('task_manager')) {
        filter.$or = [
          { createdBy: req.user.id },
          { assignedTo: req.user.id }
        ];
      }
      
      // Execute query
      const tasks = await Task.find(filter)
        .sort({ createdAt: -1 })
        .populate('createdBy', 'name email')
        .populate('assignedTo', 'name email')
        .populate('completedBy', 'name email');
      
      res.json(tasks);
    } catch (err) {
      console.error(err.message);
      res.status(500).send('Server Error');
    }
  }
};

module.exports = taskController;