const NewsPost = require('../../models/NewsPost');
const NewsCategory = require('../../models/NewsCategory');
const User = require('../../models/User');
const Team = require('../../models/Team');
const { validationResult } = require('express-validator');

/**
 * News Post Controller
 * Handles API operations for news posts
 */
const newsPostController = {
  /**
   * Get all news posts
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with posts
   */
  getAllPosts: async (req, res) => {
    try {
      // Get query parameters
      const { limit = 10, page = 1, category, featured, published = true, search, sort, team, exclude } = req.query;
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      // Build base query
      const query = {};
      if (category) query.category = category;
      if (featured !== undefined) query.featured = featured === 'true';
      if (published !== undefined) query.published = published === 'true';
      if (exclude) query._id = { $ne: exclude };
      if (team) {
        // explicit team filter; use null for global if 'null' string provided
        if (team === 'null') query.team = null; else query.team = team;
      }
      
      if (search) {
        const regex = new RegExp(search, 'i');
        query.$or = [
          { title: regex },
          { summary: regex },
          { content: regex },
          { tags: regex }
        ];
      }
      
      // If user is not admin, restrict by accessible categories and teams
      if (!req.user.roles.includes('admin')) {
        // Categories by roles
        const accessibleCategories = await NewsCategory.find({
          $or: [
            { accessRoles: { $in: req.user.roles } },
            { accessRoles: { $size: 0 } }
          ]
        }).select('_id');
        const categoryIds = accessibleCategories.map(cat => cat._id);
        if (query.category) {
          // retain existing specific category and also require it to be within accessible categories
          query.$and = query.$and ? [...query.$and, { category: { $in: categoryIds } }] : [{ category: { $in: categoryIds } }];
        } else {
          query.category = { $in: categoryIds };
        }
        
        // Teams by membership
        const dbUser = await User.findById(req.user.id).select('teams');
        const allowedTeams = dbUser?.teams || [];
        const teamAccessClause = { $or: [ { team: null }, { team: { $in: allowedTeams } } ] };
        // Combine with existing query via $and
        query.$and = query.$and ? [...query.$and, teamAccessClause] : [teamAccessClause];
      }
      
      // Sorting
      let sortObj = { featured: -1, publishedAt: -1, createdAt: -1 };
      if (sort) {
        // Support simple fields; default desc for dates/counts
        const allowed = ['publishedAt', 'createdAt', 'title', 'viewCount'];
        if (allowed.includes(sort)) {
          sortObj = { featured: -1 };
          sortObj[sort] = sort === 'title' ? 1 : -1;
        }
      }
      
      // Get total count for pagination
      const total = await NewsPost.countDocuments(query);
      
      // Get posts
      const posts = await NewsPost.find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(parseInt(limit))
        .populate('category', 'name')
        .populate('team', 'name')
        .populate('author', 'name avatar');
      
      res.json({
        posts,
        total,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      });
    } catch (err) {
      console.error('Error fetching news posts:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get latest news posts for dashboard widget
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with latest posts
   */
  getLatestPosts: async (req, res) => {
    try {
      // Get query parameters
      const { limit = 5 } = req.query;
      
      // Build query
      const query = { published: true };
      
      // If user is not admin, only show posts from accessible categories and teams
      if (!req.user.roles.includes('admin')) {
        // Get categories accessible by user's roles
        const accessibleCategories = await NewsCategory.find({
          $or: [
            { accessRoles: { $in: req.user.roles } },
            { accessRoles: { $size: 0 } }
          ]
        }).select('_id');
        const categoryIds = accessibleCategories.map(cat => cat._id);
        query.category = { $in: categoryIds };
        
        // Team membership restriction: allow global (team=null) or teams user belongs to
        const dbUser = await User.findById(req.user.id).select('teams');
        const allowedTeams = dbUser?.teams || [];
        query.$or = [ { team: null }, { team: { $in: allowedTeams } } ];
      }
      
      // Get latest posts
      const posts = await NewsPost.find(query)
        .sort({ featured: -1, publishedAt: -1 })
        .limit(parseInt(limit))
        .populate('category', 'name')
        .populate('team', 'name')
        .populate('author', 'name avatar');
      
      res.json(posts);
    } catch (err) {
      console.error('Error fetching latest news posts:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get post by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with post
   */
  getPostById: async (req, res) => {
    try {
      const post = await NewsPost.findById(req.params.id)
        .populate('category', 'name')
        .populate('team', 'name')
        .populate('author', 'name avatar')
        .populate('comments.author', 'name avatar');
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user has access to this post's category and team
      if (!req.user.roles.includes('admin')) {
        const category = await NewsCategory.findById(post.category._id);
        
        // Check if user has access to this category
        const hasAccess = category.accessRoles.length === 0 || 
          category.accessRoles.some(role => req.user.roles.includes(role));
        
        if (!hasAccess) {
          return res.status(403).json({ msg: 'Not authorized to view this post' });
        }
        
        // Team-based access: allow if global or user belongs to the team
        if (post.team) {
          const dbUser = await User.findById(req.user.id).select('teams');
          const isMember = dbUser?.teams?.some(t => t.toString() === post.team._id.toString());
          if (!isMember) {
            return res.status(403).json({ msg: 'Not authorized to view this team post' });
          }
        }
      }
      
      // Increment view count
      post.viewCount += 1;
      await post.save();
      
      res.json(post);
    } catch (err) {
      console.error('Error fetching news post:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Create a new news post
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created post
   */
  createPost: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        summary,
        category,
        published,
        featured,
        tags,
        imageUrl,
        allowComments,
        team
      } = req.body;

      // Check if category exists
      const categoryExists = await NewsCategory.findById(category);
      if (!categoryExists) {
        return res.status(400).json({ msg: 'Invalid category' });
      }
      
      // Normalize team: treat empty string or undefined as null (global)
      let postTeam = null;
      if (team === null || team === 'null') {
        postTeam = null;
      } else if (team) {
        // Validate team exists
        const teamDoc = await Team.findById(team);
        if (!teamDoc) {
          return res.status(400).json({ msg: 'Invalid team' });
        }
        postTeam = teamDoc._id;
      }
      
      // Check if user has write access to this category and team
      if (!req.user.roles.includes('admin')) {
        const hasAccess = categoryExists.accessRoles.length === 0 || 
          categoryExists.accessRoles.some(role => req.user.roles.includes(role));
        
        if (!hasAccess) {
          return res.status(403).json({ msg: 'Not authorized to create posts in this category' });
        }
        
        // Team write permission: user must be member if targeting a team
        if (postTeam) {
          const dbUser = await User.findById(req.user.id).select('teams');
          const isMember = dbUser?.teams?.some(t => t.toString() === postTeam.toString());
          if (!isMember) {
            return res.status(403).json({ msg: 'Not authorized to create posts for this team' });
          }
        }
      }

      // Create new post
      const newPost = new NewsPost({
        title,
        content,
        summary: summary || title.substring(0, 100),
        category,
        author: req.user.id,
        published: published || false,
        featured: featured || false,
        tags: tags || [],
        imageUrl: imageUrl || undefined,
        allowComments: !!allowComments,
        team: postTeam
      });

      const post = await newPost.save();
      
      // Populate relations
      await post.populate('category', 'name');
      await post.populate('team', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error creating news post:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Update a news post
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated post
   */
  updatePost: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { 
        title, 
        content, 
        summary,
        category,
        published,
        featured,
        tags,
        imageUrl,
        allowComments,
        team
      } = req.body;

      // Find post by ID
      let post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to update this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this post' });
      }
      
      // If category is being changed, check if it exists and user has access
      if (category && category !== post.category.toString()) {
        const categoryExists = await NewsCategory.findById(category);
        if (!categoryExists) {
          return res.status(400).json({ msg: 'Invalid category' });
        }
        
        // Check if user has write access to this category
        if (!req.user.roles.includes('admin')) {
          const hasAccess = categoryExists.accessRoles.length === 0 || 
            categoryExists.accessRoles.some(role => req.user.roles.includes(role));
          
          if (!hasAccess) {
            return res.status(403).json({ msg: 'Not authorized to move posts to this category' });
          }
        }
      }
      
      // Handle team changes
      if (team !== undefined) {
        let newTeam = null;
        if (team === null || team === 'null' || team === '') {
          newTeam = null;
        } else {
          const teamDoc = await Team.findById(team);
          if (!teamDoc) {
            return res.status(400).json({ msg: 'Invalid team' });
          }
          newTeam = teamDoc._id;
        }
        // Non-admin must be a member of the target team (if any)
        if (!req.user.roles.includes('admin') && newTeam) {
          const dbUser = await User.findById(req.user.id).select('teams');
          const isMember = dbUser?.teams?.some(t => t.toString() === newTeam.toString());
          if (!isMember) {
            return res.status(403).json({ msg: 'Not authorized to assign this team' });
          }
        }
        post.team = newTeam;
      }
      
      // Update post fields
      if (title !== undefined) post.title = title;
      if (content !== undefined) post.content = content;
      if (summary !== undefined) post.summary = summary;
      if (category !== undefined) post.category = category;
      if (imageUrl !== undefined) post.imageUrl = imageUrl;
      if (allowComments !== undefined) post.allowComments = !!allowComments;
      if (published !== undefined) {
        post.published = published;
        if (published && !post.publishedAt) {
          post.publishedAt = Date.now();
        }
      }
      if (featured !== undefined) post.featured = featured;
      if (tags !== undefined) post.tags = tags;
      
      await post.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('team', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error updating news post:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Delete a news post
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response
   */
  deletePost: async (req, res) => {
    try {
      // Find post by ID
      const post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to delete this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to delete this post' });
      }
      
      await post.remove();
      res.json({ msg: 'News post removed' });
    } catch (err) {
      console.error('Error deleting news post:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Toggle post featured status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated post
   */
  toggleFeatured: async (req, res) => {
    try {
      // Find post by ID
      const post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to update this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this post' });
      }
      
      // Toggle featured status
      post.featured = !post.featured;
      
      await post.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error toggling news post featured status:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Toggle post published status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated post
   */
  togglePublished: async (req, res) => {
    try {
      // Find post by ID
      const post = await NewsPost.findById(req.params.id);
      
      if (!post) {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      // Check if user is authorized to update this post
      if (!req.user.roles.includes('admin') && post.author.toString() !== req.user.id) {
        return res.status(403).json({ msg: 'Not authorized to update this post' });
      }
      
      // Toggle published status
      post.published = !post.published;
      
      // Set publishedAt date if publishing for the first time
      if (post.published && !post.publishedAt) {
        post.publishedAt = Date.now();
      }
      
      await post.save();
      
      // Populate category and author
      await post.populate('category', 'name');
      await post.populate('author', 'name avatar');
      
      res.json(post);
    } catch (err) {
      console.error('Error toggling news post published status:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News post not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
};

module.exports = newsPostController;