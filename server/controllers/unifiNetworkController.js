const UnifiNetworkAPI = require('../integrations/unifiNetwork/unifiNetworkAPI');

// Initialize UniFi Network API with environment variables
const host = process.env.UNIFI_NETWORK_HOST || '';
const apiKey = process.env.UNIFI_NETWORK_API_KEY || '';
const username = process.env.UNIFI_NETWORK_USERNAME || 'admin';
const password = process.env.UNIFI_NETWORK_PASSWORD || apiKey;
const port = process.env.UNIFI_NETWORK_PORT || 443;
const site = process.env.UNIFI_NETWORK_SITEID || process.env.UNIFI_NETWORK_SITE || 'default';

let unifiNetworkAPI = new UnifiNetworkAPI(host, apiKey, port, site);

// Initialize the API only when needed, not on module load
// This prevents unnecessary connection attempts when just checking configuration
const initializeAPI = async () => {
  try {
    if (host && apiKey) {
      console.log(`[UniFi Network Controller] Initializing API with host: ${host}, port: ${port}, site: ${site}`);
      await unifiNetworkAPI.initialize();
      console.log('[UniFi Network Controller] UniFi Network API initialized successfully');
      return true;
    }
    console.log('[UniFi Network Controller] Skipping API initialization - missing host or apiKey');
    return false;
  } catch (error) {
    console.error('[UniFi Network Controller] Error initializing UniFi Network API:', error);
    // Log additional details about the error
    if (error.response) {
      // The request was made and the server responded with a status code outside the 2xx range
      console.error(`[UniFi Network Controller] Response status: ${error.response.status}`);
      console.error(`[UniFi Network Controller] Response headers:`, error.response.headers);
      console.error(`[UniFi Network Controller] Response data:`, error.response.data);
      
      if (error.response.status === 404) {
        console.error('[UniFi Network Controller] 404 Not Found: This may indicate an incorrect API endpoint or incompatible UniFi Network controller version');
      }
    } else if (error.request) {
      // The request was made but no response was received
      console.error('[UniFi Network Controller] No response received from server. This may indicate network connectivity issues or incorrect host/port configuration');
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error(`[UniFi Network Controller] Error setting up request: ${error.message}`);
    }
    
    return false;
  }
};

// Export the initializeAPI function so it can be called from other modules
exports.initializeAPI = initializeAPI;

// Helper function to ensure API is initialized
const ensureApiInitialized = async () => {
  if (!host || !apiKey) {
    throw new Error('UniFi Network configuration is missing. Please check your environment variables.');
  }
  
  // Initialize the API if needed
  await initializeAPI();
  return true;
};

/**
 * Get all UniFi Network devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    console.log('[UniFi Network Controller] Handling request to get all devices');
    await ensureApiInitialized();
    
    // Extract filter parameter from query if present
    const options = {};
    if (req.query.filter) {
      options.filter = req.query.filter;
      console.log(`[UniFi Network Controller] Using filter: ${req.query.filter}`);
    }
    
    const devices = await unifiNetworkAPI.getDevices(options);
    console.log(`[UniFi Network Controller] Successfully fetched ${devices.length} devices`);
    res.json(devices);
  } catch (error) {
    console.error('[UniFi Network Controller] Error fetching UniFi Network devices:', error);
    
    // Determine appropriate status code and message based on the error
    let statusCode = 500;
    let responseMessage = 'Error fetching UniFi Network devices';
    let errorDetails = error.message;
    
    // Check if this is a response error
    if (error.response) {
      statusCode = error.response.status;
      
      // Special handling for 404 errors
      if (statusCode === 404) {
        console.error('[UniFi Network Controller] 404 Not Found: The devices endpoint does not exist or is not accessible');
        responseMessage = 'UniFi Network devices endpoint not found';
        errorDetails = 'The requested endpoint does not exist or is not accessible. This may indicate an incompatible UniFi Network controller version.';
      }
    } else if (error.message.includes('configuration is missing')) {
      // Handle configuration errors
      statusCode = 400;
      responseMessage = 'UniFi Network configuration error';
      errorDetails = error.message;
    }
    
    res.status(statusCode).json({ 
      message: responseMessage, 
      error: errorDetails,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get UniFi Network device details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceDetails = async (req, res) => {
  try {
    const deviceId = req.params.id;
    console.log(`[UniFi Network Controller] Handling request to get device details for device ID: ${deviceId}`);
    
    await ensureApiInitialized();
    const deviceDetails = await unifiNetworkAPI.getDeviceDetails(deviceId);
    
    if (!deviceDetails) {
      console.log(`[UniFi Network Controller] Device with ID ${deviceId} not found`);
      return res.status(404).json({ 
        message: 'Device not found', 
        error: `Device with ID ${deviceId} not found`,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log(`[UniFi Network Controller] Successfully fetched details for device ID: ${deviceId}`);
    res.json(deviceDetails);
  } catch (error) {
    console.error(`[UniFi Network Controller] Error fetching UniFi Network device details for ${req.params.id}:`, error);
    
    // Determine appropriate status code and message based on the error
    let statusCode = 500;
    let responseMessage = 'Error fetching UniFi Network device details';
    let errorDetails = error.message;
    
    // Check if this is a response error
    if (error.response) {
      statusCode = error.response.status;
      
      // Special handling for 404 errors
      if (statusCode === 404) {
        console.error(`[UniFi Network Controller] 404 Not Found: The device endpoint for ${req.params.id} does not exist or is not accessible`);
        responseMessage = 'UniFi Network device endpoint not found';
        errorDetails = 'The requested endpoint does not exist or is not accessible. This may indicate an incompatible UniFi Network controller version.';
      }
    } else if (error.message.includes('configuration is missing')) {
      // Handle configuration errors
      statusCode = 400;
      responseMessage = 'UniFi Network configuration error';
      errorDetails = error.message;
    } else if (error.message.includes('not found')) {
      // Handle device not found errors
      statusCode = 404;
      responseMessage = 'Device not found';
      errorDetails = error.message;
    }
    
    res.status(statusCode).json({ 
      message: responseMessage, 
      error: errorDetails,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get all UniFi Network clients
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getClients = async (req, res) => {
  try {
    console.log('[UniFi Network Controller] Handling request to get all clients');
    await ensureApiInitialized();
    
    // Extract filter parameter from query if present
    const options = {};
    if (req.query.filter) {
      options.filter = req.query.filter;
      console.log(`[UniFi Network Controller] Using filter: ${req.query.filter}`);
    }
    
    const clients = await unifiNetworkAPI.getClients(options);
    console.log(`[UniFi Network Controller] Successfully fetched ${clients.length} clients`);
    res.json(clients);
  } catch (error) {
    console.error('[UniFi Network Controller] Error fetching UniFi Network clients:', error);
    
    // Determine appropriate status code and message based on the error
    let statusCode = 500;
    let responseMessage = 'Error fetching UniFi Network clients';
    let errorDetails = error.message;
    
    // Check if this is a response error
    if (error.response) {
      statusCode = error.response.status;
      
      // Special handling for 404 errors
      if (statusCode === 404) {
        console.error('[UniFi Network Controller] 404 Not Found: The clients endpoint does not exist or is not accessible');
        responseMessage = 'UniFi Network clients endpoint not found';
        errorDetails = 'The requested endpoint does not exist or is not accessible. This may indicate an incompatible UniFi Network controller version.';
      }
    } else if (error.message.includes('configuration is missing')) {
      // Handle configuration errors
      statusCode = 400;
      responseMessage = 'UniFi Network configuration error';
      errorDetails = error.message;
    }
    
    res.status(statusCode).json({ 
      message: responseMessage, 
      error: errorDetails,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get UniFi Network client details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getClientDetails = async (req, res) => {
  try {
    const clientId = req.params.id;
    console.log(`[UniFi Network Controller] Handling request to get client details for client ID: ${clientId}`);
    
    await ensureApiInitialized();
    const clientDetails = await unifiNetworkAPI.getClientDetails(clientId);
    
    if (!clientDetails) {
      console.log(`[UniFi Network Controller] Client with ID ${clientId} not found`);
      return res.status(404).json({ 
        message: 'Client not found', 
        error: `Client with ID ${clientId} not found`,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log(`[UniFi Network Controller] Successfully fetched details for client ID: ${clientId}`);
    res.json(clientDetails);
  } catch (error) {
    console.error(`[UniFi Network Controller] Error fetching UniFi Network client details for ${req.params.id}:`, error);
    
    // Determine appropriate status code and message based on the error
    let statusCode = 500;
    let responseMessage = 'Error fetching UniFi Network client details';
    let errorDetails = error.message;
    
    // Check if this is a response error
    if (error.response) {
      statusCode = error.response.status;
      
      // Special handling for 404 errors
      if (statusCode === 404) {
        console.error(`[UniFi Network Controller] 404 Not Found: The client endpoint for ${req.params.id} does not exist or is not accessible`);
        responseMessage = 'UniFi Network client endpoint not found';
        errorDetails = 'The requested endpoint does not exist or is not accessible. This may indicate an incompatible UniFi Network controller version.';
      }
    } else if (error.message.includes('configuration is missing')) {
      // Handle configuration errors
      statusCode = 400;
      responseMessage = 'UniFi Network configuration error';
      errorDetails = error.message;
    } else if (error.message.includes('not found')) {
      // Handle client not found errors
      statusCode = 404;
      responseMessage = 'Client not found';
      errorDetails = error.message;
    }
    
    res.status(statusCode).json({ 
      message: responseMessage, 
      error: errorDetails,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Block a UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.blockClient = async (req, res) => {
  try {
    await ensureApiInitialized();
    const clientId = req.params.id;
    const result = await unifiNetworkAPI.blockClient(clientId);
    res.json(result);
  } catch (error) {
    console.error('Controller error blocking UniFi Network client:', error);
    res.status(500).json({ message: 'Error blocking UniFi Network client', error: error.message });
  }
};

/**
 * Unblock a UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unblockClient = async (req, res) => {
  try {
    await ensureApiInitialized();
    const clientId = req.params.id;
    const result = await unifiNetworkAPI.unblockClient(clientId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unblocking UniFi Network client:', error);
    res.status(500).json({ message: 'Error unblocking UniFi Network client', error: error.message });
  }
};

/**
 * Reconnect (kick) a UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.reconnectClient = async (req, res) => {
  try {
    await ensureApiInitialized();
    const clientId = req.params.id;
    const result = await unifiNetworkAPI.reconnectClient(clientId);
    res.json(result);
  } catch (error) {
    console.error('Controller error reconnecting UniFi Network client:', error);
    res.status(500).json({ message: 'Error reconnecting UniFi Network client', error: error.message });
  }
};

/**
 * Authorize a guest UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.authorizeGuest = async (req, res) => {
  try {
    await ensureApiInitialized();
    const clientId = req.params.id;
    const minutes = req.body.minutes || 60; // Default to 60 minutes if not specified
    const result = await unifiNetworkAPI.authorizeGuest(clientId, minutes);
    res.json(result);
  } catch (error) {
    console.error('Controller error authorizing UniFi Network guest client:', error);
    res.status(500).json({ message: 'Error authorizing UniFi Network guest client', error: error.message });
  }
};

/**
 * Unauthorize a guest UniFi Network client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unauthorizeGuest = async (req, res) => {
  try {
    await ensureApiInitialized();
    const clientId = req.params.id;
    const result = await unifiNetworkAPI.unauthorizeGuest(clientId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unauthorizing UniFi Network guest client:', error);
    res.status(500).json({ message: 'Error unauthorizing UniFi Network guest client', error: error.message });
  }
};

/**
 * Get UniFi Network client statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getClientStatistics = async (req, res) => {
  try {
    await ensureApiInitialized();
    const clientId = req.params.id;
    const stats = await unifiNetworkAPI.getClientStatistics(clientId);
    res.json(stats);
  } catch (error) {
    console.error(`Controller error fetching UniFi Network client statistics for ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error fetching UniFi Network client statistics', error: error.message });
  }
};

// ===== Network Management Endpoints =====

/**
 * Get all UniFi Network networks
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getNetworks = async (req, res) => {
  try {
    await ensureApiInitialized();
    const networks = await unifiNetworkAPI.getNetworks();
    res.json(networks);
  } catch (error) {
    console.error('Controller error fetching UniFi Network networks:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network networks', error: error.message });
  }
};

/**
 * Create a new UniFi Network network
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createNetwork = async (req, res) => {
  try {
    await ensureApiInitialized();
    const networkData = req.body;
    
    // Validate required fields
    if (!networkData.name) {
      return res.status(400).json({ message: 'Network name is required' });
    }
    
    const network = await unifiNetworkAPI.createNetwork(networkData);
    res.status(201).json(network);
  } catch (error) {
    console.error('Controller error creating UniFi Network network:', error);
    res.status(500).json({ message: 'Error creating UniFi Network network', error: error.message });
  }
};

/**
 * Update an existing UniFi Network network
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateNetwork = async (req, res) => {
  try {
    await ensureApiInitialized();
    const networkId = req.params.id;
    const networkData = req.body;
    
    const network = await unifiNetworkAPI.updateNetwork(networkId, networkData);
    res.json(network);
  } catch (error) {
    console.error(`Controller error updating UniFi Network network ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error updating UniFi Network network', error: error.message });
  }
};

/**
 * Delete a UniFi Network network
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteNetwork = async (req, res) => {
  try {
    await ensureApiInitialized();
    const networkId = req.params.id;
    
    const result = await unifiNetworkAPI.deleteNetwork(networkId);
    res.json(result);
  } catch (error) {
    console.error(`Controller error deleting UniFi Network network ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error deleting UniFi Network network', error: error.message });
  }
};

// ===== Wireless Management Endpoints =====

/**
 * Get all UniFi Network wireless networks (WLANs)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWirelessNetworks = async (req, res) => {
  try {
    await ensureApiInitialized();
    const wlans = await unifiNetworkAPI.getWirelessNetworks();
    res.json(wlans);
  } catch (error) {
    console.error('Controller error fetching UniFi Network wireless networks:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network wireless networks', error: error.message });
  }
};

/**
 * Create a new UniFi Network wireless network (WLAN)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createWirelessNetwork = async (req, res) => {
  try {
    await ensureApiInitialized();
    const wlanData = req.body;
    
    // Validate required fields
    if (!wlanData.name) {
      return res.status(400).json({ message: 'Wireless network name is required' });
    }
    
    const wlan = await unifiNetworkAPI.createWirelessNetwork(wlanData);
    res.status(201).json(wlan);
  } catch (error) {
    console.error('Controller error creating UniFi Network wireless network:', error);
    res.status(500).json({ message: 'Error creating UniFi Network wireless network', error: error.message });
  }
};

/**
 * Update an existing UniFi Network wireless network (WLAN)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateWirelessNetwork = async (req, res) => {
  try {
    await ensureApiInitialized();
    const wlanId = req.params.id;
    const wlanData = req.body;
    
    const wlan = await unifiNetworkAPI.updateWirelessNetwork(wlanId, wlanData);
    res.json(wlan);
  } catch (error) {
    console.error(`Controller error updating UniFi Network wireless network ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error updating UniFi Network wireless network', error: error.message });
  }
};

/**
 * Delete a UniFi Network wireless network (WLAN)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteWirelessNetwork = async (req, res) => {
  try {
    await ensureApiInitialized();
    const wlanId = req.params.id;
    
    const result = await unifiNetworkAPI.deleteWirelessNetwork(wlanId);
    res.json(result);
  } catch (error) {
    console.error(`Controller error deleting UniFi Network wireless network ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error deleting UniFi Network wireless network', error: error.message });
  }
};

// ===== System Management Endpoints =====

/**
 * Get UniFi Network system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    await ensureApiInitialized();
    const status = await unifiNetworkAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching UniFi Network system status:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network system status', error: error.message });
  }
};

/**
 * Get UniFi Network system settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemSettings = async (req, res) => {
  try {
    await ensureApiInitialized();
    const settings = await unifiNetworkAPI.getSystemSettings();
    res.json(settings);
  } catch (error) {
    console.error('Controller error fetching UniFi Network system settings:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network system settings', error: error.message });
  }
};

/**
 * Update UniFi Network system settings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateSystemSettings = async (req, res) => {
  try {
    await ensureApiInitialized();
    const settingsData = req.body;
    
    const settings = await unifiNetworkAPI.updateSystemSettings(settingsData);
    res.json(settings);
  } catch (error) {
    console.error('Controller error updating UniFi Network system settings:', error);
    res.status(500).json({ message: 'Error updating UniFi Network system settings', error: error.message });
  }
};

/**
 * Get UniFi Network system logs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemLogs = async (req, res) => {
  try {
    await ensureApiInitialized();
    const limit = req.query.limit ? parseInt(req.query.limit, 10) : 100;
    
    const logs = await unifiNetworkAPI.getSystemLogs(limit);
    res.json(logs);
  } catch (error) {
    console.error('Controller error fetching UniFi Network system logs:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network system logs', error: error.message });
  }
};

/**
 * Get UniFi Network system alerts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemAlerts = async (req, res) => {
  try {
    await ensureApiInitialized();
    const alerts = await unifiNetworkAPI.getSystemAlerts();
    res.json(alerts);
  } catch (error) {
    console.error('Controller error fetching UniFi Network system alerts:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network system alerts', error: error.message });
  }
};

// ===== Device Management Endpoints =====

/**
 * Reboot a UniFi Network device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.rebootDevice = async (req, res) => {
  try {
    await ensureApiInitialized();
    const deviceId = req.params.id;
    
    const result = await unifiNetworkAPI.rebootDevice(deviceId);
    res.json(result);
  } catch (error) {
    console.error(`Controller error rebooting UniFi Network device ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error rebooting UniFi Network device', error: error.message });
  }
};

/**
 * Adopt a UniFi Network device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.adoptDevice = async (req, res) => {
  try {
    await ensureApiInitialized();
    const deviceId = req.params.id;
    
    const result = await unifiNetworkAPI.adoptDevice(deviceId);
    res.json(result);
  } catch (error) {
    console.error(`Controller error adopting UniFi Network device ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error adopting UniFi Network device', error: error.message });
  }
};

/**
 * Set name for a UniFi Network device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setDeviceName = async (req, res) => {
  try {
    await ensureApiInitialized();
    const deviceId = req.params.id;
    const { name } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Device name is required' });
    }
    
    const result = await unifiNetworkAPI.setDeviceName(deviceId, name);
    res.json(result);
  } catch (error) {
    console.error(`Controller error setting name for UniFi Network device ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error setting name for UniFi Network device', error: error.message });
  }
};

/**
 * Upgrade firmware for a UniFi Network device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.upgradeDeviceFirmware = async (req, res) => {
  try {
    await ensureApiInitialized();
    const deviceId = req.params.id;
    
    const result = await unifiNetworkAPI.upgradeDeviceFirmware(deviceId);
    res.json(result);
  } catch (error) {
    console.error(`Controller error upgrading firmware for UniFi Network device ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error upgrading firmware for UniFi Network device', error: error.message });
  }
};

// ===== Statistics Endpoints =====

/**
 * Get UniFi Network site statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSiteStatistics = async (req, res) => {
  try {
    await ensureApiInitialized();
    const stats = await unifiNetworkAPI.getSiteStatistics();
    res.json(stats);
  } catch (error) {
    console.error('Controller error fetching UniFi Network site statistics:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network site statistics', error: error.message });
  }
};

/**
 * Get UniFi Network traffic statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTrafficStatistics = async (req, res) => {
  try {
    await ensureApiInitialized();
    const stats = await unifiNetworkAPI.getTrafficStatistics();
    res.json(stats);
  } catch (error) {
    console.error('Controller error fetching UniFi Network traffic statistics:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network traffic statistics', error: error.message });
  }
};


/**
 * Get UniFi Network device statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceStatistics = async (req, res) => {
  try {
    const deviceId = req.params.id;
    console.log(`[UniFi Network Controller] Handling request to get device statistics for device ID: ${deviceId}`);
    
    await ensureApiInitialized();
    const deviceStats = await unifiNetworkAPI.getDeviceStatistics(deviceId);
    
    if (!deviceStats) {
      console.log(`[UniFi Network Controller] Statistics for device with ID ${deviceId} not found`);
      return res.status(404).json({ 
        message: 'Device statistics not found', 
        error: `Statistics for device with ID ${deviceId} not found`,
        timestamp: new Date().toISOString()
      });
    }
    
    console.log(`[UniFi Network Controller] Successfully fetched statistics for device ID: ${deviceId}`);
    res.json(deviceStats);
  } catch (error) {
    console.error(`[UniFi Network Controller] Error fetching UniFi Network device statistics for ${req.params.id}:`, error);
    
    // Determine appropriate status code and message based on the error
    let statusCode = 500;
    let responseMessage = 'Error fetching UniFi Network device statistics';
    let errorDetails = error.message;
    
    // Check if this is a response error
    if (error.response) {
      statusCode = error.response.status;
      
      // Special handling for 404 errors
      if (statusCode === 404) {
        console.error(`[UniFi Network Controller] 404 Not Found: The device statistics endpoint for ${req.params.id} does not exist or is not accessible`);
        responseMessage = 'UniFi Network devices endpoint not found';
        errorDetails = 'The requested endpoint does not exist or is not accessible. This may indicate an incompatible UniFi Network controller version.';
      }
    } else if (error.message.includes('configuration is missing')) {
      // Handle configuration errors
      statusCode = 400;
      responseMessage = 'UniFi Network configuration error';
      errorDetails = error.message;
    } else if (error.message.includes('not found')) {
      // Handle device not found errors
      statusCode = 404;
      responseMessage = 'Device not found';
      errorDetails = error.message;
    }
    
    res.status(statusCode).json({ 
      message: responseMessage, 
      error: errorDetails,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Save UniFi Network configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling UniFi Network configuration request:', error);
    res.status(500).json({ message: 'Error handling UniFi Network configuration request', error: error.message });
  }
};

/**
 * Get UniFi Network configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Check if the required environment variables are set
    if (!host || !apiKey) {
      // Return 404 to indicate configuration is missing
      return res.status(404).json({ message: 'UniFi Network configuration is missing' });
    }

    // Don't send the actual credentials back to the client for security
    res.json({
      host: process.env.UNIFI_NETWORK_HOST,
      apiKeyConfigured: !!process.env.UNIFI_NETWORK_API_KEY,
      port: process.env.UNIFI_NETWORK_PORT || 443,
      site: process.env.UNIFI_NETWORK_SITE || 'default',
      configuredAt: new Date()
    });
  } catch (error) {
    console.error('Error fetching UniFi Network configuration:', error);
    res.status(500).json({ message: 'Error fetching UniFi Network configuration', error: error.message });
  }
};

/**
 * Set up UniFi Network with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling UniFi Network one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling UniFi Network one-click setup request', 
      error: error.message 
    });
  }
};


