const Message = require('../../models/Message');
const Conversation = require('../../models/Conversation');
const User = require('../../models/User');
const websocketService = require('../services/websocketService');

// Get all conversations for a user
exports.getConversations = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 20 } = req.query;

    const conversations = await Conversation.find({
      participants: userId,
      isActive: true
    })
    .populate('participants', 'name email avatar presence')
    .populate('lastMessage')
    .sort({ lastMessageAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

    // Add unread count for current user
    const conversationsWithUnread = conversations.map(conv => {
      const convObj = conv.toObject();
      convObj.unreadCount = conv.unreadCounts.get(userId.toString()) || 0;
      return convObj;
    });

    res.json({
      success: true,
      conversations: conversationsWithUnread,
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch conversations' 
    });
  }
};

// Get or create a conversation
exports.getOrCreateConversation = async (req, res) => {
  try {
    const { participantIds, isGroup = false, groupName } = req.body;
    const userId = req.user._id;

    // Ensure current user is included in participants
    const allParticipants = [...new Set([userId.toString(), ...participantIds])];

    if (!isGroup && allParticipants.length !== 2) {
      return res.status(400).json({
        success: false,
        message: 'Direct conversations must have exactly 2 participants'
      });
    }

    // For direct conversations, check if one already exists
    if (!isGroup) {
      const existingConversation = await Conversation.findOne({
        participants: { $all: allParticipants, $size: 2 },
        isGroup: false
      }).populate('participants', 'name email avatar presence');

      if (existingConversation) {
        return res.json({
          success: true,
          conversation: existingConversation,
          isNew: false
        });
      }
    }

    // Create new conversation
    const conversation = new Conversation({
      participants: allParticipants,
      isGroup,
      groupName,
      createdBy: userId
    });

    await conversation.save();
    await conversation.populate('participants', 'name email avatar presence');

    res.json({
      success: true,
      conversation,
      isNew: true
    });
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to create conversation' 
    });
  }
};

// Get messages for a conversation
exports.getMessages = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const userId = req.user._id;

    // Check if user is participant
    const conversation = await Conversation.findById(conversationId);
    if (!conversation || !conversation.isParticipant(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You are not a participant in this conversation'
      });
    }

    const messages = await Message.find({
      conversation: conversationId,
      deletedAt: { $exists: false }
    })
    .populate('sender', 'name email avatar')
    .populate('replyTo')
    .sort({ createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

    // Mark conversation as read
    await conversation.markAsRead(userId);

    res.json({
      success: true,
      messages: messages.reverse(), // Return in chronological order
      page: parseInt(page),
      limit: parseInt(limit)
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch messages' 
    });
  }
};

// Send a message
exports.sendMessage = async (req, res) => {
  try {
    const { conversationId, content, messageType = 'text', replyTo } = req.body;
    const userId = req.user._id;

    // Check if user is participant
    const conversation = await Conversation.findById(conversationId)
      .populate('participants', 'name email avatar');
    
    if (!conversation || !conversation.isParticipant(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You are not a participant in this conversation'
      });
    }

    // Create message
    const message = new Message({
      conversation: conversationId,
      sender: userId,
      content,
      messageType,
      replyTo,
      readBy: [{ user: userId }]
    });

    await message.save();
    await message.populate('sender', 'name email avatar');
    
    if (replyTo) {
      await message.populate('replyTo');
    }

    // Update conversation
    conversation.lastMessage = message._id;
    conversation.lastMessageAt = new Date();
    await conversation.incrementUnreadForAll(userId);

    // Send real-time notification to all participants
    const messageData = {
      ...message.toObject(),
      conversation: conversation.toObject()
    };

    conversation.participants.forEach(participant => {
      if (participant._id.toString() !== userId.toString()) {
        websocketService.sendToUser(participant._id.toString(), 'new_message', messageData);
      }
    });

    res.json({
      success: true,
      message
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to send message' 
    });
  }
};

// Mark messages as read
exports.markAsRead = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user._id;

    // Check if user is participant
    const conversation = await Conversation.findById(conversationId);
    if (!conversation || !conversation.isParticipant(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You are not a participant in this conversation'
      });
    }

    // Mark all unread messages as read
    await Message.updateMany(
      {
        conversation: conversationId,
        'readBy.user': { $ne: userId }
      },
      {
        $addToSet: {
          readBy: {
            user: userId,
            readAt: new Date()
          }
        }
      }
    );

    // Reset unread count
    await conversation.markAsRead(userId);

    // Notify other participants about read status
    websocketService.sendToConversation(conversationId, 'messages_read', {
      conversationId,
      userId,
      readAt: new Date()
    });

    res.json({
      success: true,
      message: 'Messages marked as read'
    });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to mark messages as read' 
    });
  }
};

// Delete a message
exports.deleteMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user._id;

    const message = await Message.findById(messageId);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    // Check if user is sender
    if (message.sender.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete your own messages'
      });
    }

    // Soft delete
    await message.softDelete(userId);

    // Notify participants
    websocketService.sendToConversation(message.conversation, 'message_deleted', {
      messageId,
      conversationId: message.conversation
    });

    res.json({
      success: true,
      message: 'Message deleted'
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to delete message' 
    });
  }
};

// Add reaction to message
exports.addReaction = async (req, res) => {
  try {
    const { messageId } = req.params;
    const { emoji } = req.body;
    const userId = req.user._id;

    const message = await Message.findById(messageId);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    await message.addReaction(userId, emoji);

    // Notify participants
    websocketService.sendToConversation(message.conversation, 'reaction_added', {
      messageId,
      userId,
      emoji,
      conversationId: message.conversation
    });

    res.json({
      success: true,
      message: 'Reaction added'
    });
  } catch (error) {
    console.error('Error adding reaction:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to add reaction' 
    });
  }
};

// Search users for starting new conversations
exports.searchUsers = async (req, res) => {
  try {
    const { query } = req.query;
    const userId = req.user._id;

    const users = await User.find({
      _id: { $ne: userId },
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } }
      ],
      isActive: true
    })
    .select('name email avatar presence')
    .limit(10);

    res.json({
      success: true,
      users
    });
  } catch (error) {
    console.error('Error searching users:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to search users' 
    });
  }
};

// Get conversation details
exports.getConversation = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user._id;

    const conversation = await Conversation.findById(conversationId)
      .populate('participants', 'name email avatar presence')
      .populate('lastMessage');

    if (!conversation || !conversation.isParticipant(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You are not a participant in this conversation'
      });
    }

    const convObj = conversation.toObject();
    convObj.unreadCount = conversation.unreadCounts.get(userId.toString()) || 0;

    res.json({
      success: true,
      conversation: convObj
    });
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to fetch conversation' 
    });
  }
};

// Typing indicator
exports.setTypingStatus = async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { isTyping } = req.body;
    const userId = req.user._id;

    // Check if user is participant
    const conversation = await Conversation.findById(conversationId);
    if (!conversation || !conversation.isParticipant(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You are not a participant in this conversation'
      });
    }

    // Send typing status to other participants
    conversation.participants.forEach(participantId => {
      if (participantId.toString() !== userId.toString()) {
        websocketService.sendToUser(participantId.toString(), 'typing_status', {
          conversationId,
          userId,
          isTyping,
          userName: req.user.name
        });
      }
    });

    res.json({
      success: true,
      message: 'Typing status updated'
    });
  } catch (error) {
    console.error('Error updating typing status:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to update typing status' 
    });
  }
};