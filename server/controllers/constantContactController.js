const ConstantContactAPI = require('../integrations/constantContact/constantContactAPI');
const cacheUtil = require('../utils/cacheUtil');
const axios = require('axios');

// Initialize Constant Contact API with environment variables
let constantContactAPI = new ConstantContactAPI();

/**
 * Get the latest Constant Contact configuration
 * @returns {Promise<Object|null>} The latest configuration or null if not found
 */
const getLatestConfig = async () => {
  try {
    // Check if environment variables are set
    const clientId = process.env.CONSTANT_CONTACT_CLIENT_ID || '';
    const clientSecret = process.env.CONSTANT_CONTACT_CLIENT_SECRET || '';
    const accessToken = process.env.CONSTANT_CONTACT_ACCESS_TOKEN || '';
    const refreshToken = process.env.CONSTANT_CONTACT_REFRESH_TOKEN || '';
    const apiUrl = process.env.API_URL || process.env.PORTAL_URL || 'http://localhost:3000';
    const redirectUri = process.env.CONSTANT_CONTACT_REDIRECT_URI || `${apiUrl.replace(/\/$/, '')}/api/constant-contact/oauth/callback`;
    
    // If environment variables are set, use them directly
    if (clientId && clientSecret) {
      return {
        clientId,
        clientSecret,
        accessToken,
        refreshToken,
        redirectUri,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If no environment variables are set, log a warning
    console.warn('Constant Contact API credentials not set. Please configure CONSTANT_CONTACT_CLIENT_ID and CONSTANT_CONTACT_CLIENT_SECRET in your environment.');
    
    return {
      clientId: '',
      clientSecret: '',
      accessToken: '',
      refreshToken: '',
      redirectUri,
      updatedAt: new Date(),
      fromEnv: false
    };
  } catch (error) {
    console.error('Error fetching Constant Contact configuration:', error);
    throw error;
  }
};

// Export the getLatestConfig function for use by other modules
exports.getLatestConfig = getLatestConfig;

/**
 * Get Constant Contact OAuth authorization URL
 */
exports.getAuthUrl = async (req, res) => {
  try {
    const cfg = await getLatestConfig();
    if (!cfg || !cfg.clientId) {
      return res.status(400).json({ message: 'Constant Contact client ID is not configured' });
    }
    const scopes = process.env.CONSTANT_CONTACT_SCOPES || 'account_read contacts_read contacts_write campaign_read';
    const params = new URLSearchParams({
      client_id: cfg.clientId,
      response_type: 'code',
      redirect_uri: cfg.redirectUri,
      scope: scopes,
      state: req.query.state || 'cc_state'
    });
    const authUrl = `https://authz.constantcontact.com/oauth2/default/v1/authorize?${params.toString()}`;
    res.json({ authUrl, redirectUri: cfg.redirectUri });
  } catch (error) {
    console.error('Error building Constant Contact auth URL:', error);
    res.status(500).json({ message: 'Error building auth URL', error: error.message });
  }
};

/**
 * OAuth callback to exchange code for tokens and persist them
 */
exports.oauthCallback = async (req, res) => {
  try {
    const { code, error, error_description } = req.query;
    if (error) {
      return res.status(400).send(`Authorization error: ${error_description || error}`);
    }
    if (!code) {
      return res.status(400).send('Missing authorization code');
    }
    const cfg = await getLatestConfig();
    if (!cfg || !cfg.clientId || !cfg.clientSecret) {
      return res.status(500).send('Constant Contact credentials are not configured');
    }

    const tokenRes = await axios.post(
      'https://authz.constantcontact.com/oauth2/default/v1/token',
      new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: cfg.redirectUri
      }).toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${cfg.clientId}:${cfg.clientSecret}`).toString('base64')}`
        }
      }
    );

    const tokens = tokenRes.data || {};
    // Save tokens through API helper
    constantContactAPI.accessToken = tokens.access_token;
    constantContactAPI.refreshToken = tokens.refresh_token || constantContactAPI.refreshToken;
    constantContactAPI.saveTokensToFile({
      access_token: constantContactAPI.accessToken,
      refresh_token: constantContactAPI.refreshToken,
      token_type: tokens.token_type,
      expires_in: tokens.expires_in,
      updated_at: new Date().toISOString()
    });

    // Update axios header for immediate use
    constantContactAPI.axios.defaults.headers.common['Authorization'] = `Bearer ${constantContactAPI.accessToken}`;

    // Respond with a simple page or JSON
    if ((req.headers.accept || '').includes('text/html')) {
      return res.send('<html><body><h3>Constant Contact authorization successful.</h3><p>You can close this window.</p></body></html>');
    }
    return res.json({ success: true, message: 'Authorized successfully', expires_in: tokens.expires_in });
  } catch (err) {
    console.error('Error handling Constant Contact OAuth callback:', err.response?.data || err.message);
    res.status(500).send(`OAuth callback error: ${err.response?.data?.error_description || err.message}`);
  }
};

/**
 * Initialize the Constant Contact API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.initialize = async (req, res) => {
  try {
    await constantContactAPI.initialize();
    res.json({ message: 'Constant Contact API initialized successfully' });
  } catch (error) {
    console.error('Error initializing Constant Contact API:', error);
    res.status(500).json({ message: 'Error initializing Constant Contact API', error: error.message });
  }
};

/**
 * Get account information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccountInfo = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('constant-contact-account-info');
    
    // Try to get the account info from cache first
    let accountInfo = cacheUtil.get(cacheKey);
    
    if (!accountInfo) {
      console.log('Constant Contact account info cache miss, fetching from API');
      // If not in cache, fetch from API
      accountInfo = await constantContactAPI.getAccountInfo();
      
      if (!accountInfo) {
        return res.status(500).json({ message: 'Error fetching Constant Contact account information' });
      }
      
      // Cache the result for 15 minutes (account info doesn't change often)
      cacheUtil.set(cacheKey, accountInfo, 15 * 60 * 1000);
    } else {
      console.log('Constant Contact account info served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=900'); // 15 minutes
    
    res.json(accountInfo);
  } catch (error) {
    console.error('Controller error fetching Constant Contact account information:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact account information', error: error.message });
  }
};

/**
 * Get contact lists
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getContactLists = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('constant-contact-lists');
    
    // Try to get the contact lists from cache first
    let contactLists = cacheUtil.get(cacheKey);
    
    if (!contactLists) {
      console.log('Constant Contact lists cache miss, fetching from API');
      // If not in cache, fetch from API
      contactLists = await constantContactAPI.getContactLists();
      
      if (!contactLists) {
        return res.status(500).json({ message: 'Error fetching Constant Contact lists' });
      }
      
      // Cache the result for 5 minutes (lists don't change often)
      cacheUtil.set(cacheKey, contactLists, 5 * 60 * 1000);
    } else {
      console.log('Constant Contact lists served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=300'); // 5 minutes
    
    res.json(contactLists);
  } catch (error) {
    console.error('Controller error fetching Constant Contact lists:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact lists', error: error.message });
  }
};

/**
 * Get contacts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getContacts = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Get query parameters
    const { limit, status } = req.query;
    const params = {};
    if (limit) params.limit = limit;
    if (status) params.status = status;
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey(`constant-contact-contacts-${JSON.stringify(params)}`);
    
    // Try to get the contacts from cache first
    let contacts = cacheUtil.get(cacheKey);
    
    if (!contacts) {
      console.log('Constant Contact contacts cache miss, fetching from API');
      // If not in cache, fetch from API
      contacts = await constantContactAPI.getContacts(params);
      
      if (!contacts) {
        return res.status(500).json({ message: 'Error fetching Constant Contact contacts' });
      }
      
      // Cache the result for 2 minutes (contacts can change)
      cacheUtil.set(cacheKey, contacts, 2 * 60 * 1000);
    } else {
      console.log('Constant Contact contacts served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=120'); // 2 minutes
    
    res.json(contacts);
  } catch (error) {
    console.error('Controller error fetching Constant Contact contacts:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact contacts', error: error.message });
  }
};

/**
 * Get contact by email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getContactByEmail = async (req, res) => {
  try {
    const { email } = req.query;
    
    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey(`constant-contact-contact-${email}`);
    
    // Try to get the contact from cache first
    let contact = cacheUtil.get(cacheKey);
    
    if (!contact) {
      console.log(`Constant Contact contact cache miss for ${email}, fetching from API`);
      // If not in cache, fetch from API
      contact = await constantContactAPI.getContactByEmail(email);
      
      // Cache the result for 2 minutes (contact can change)
      if (contact) {
        cacheUtil.set(cacheKey, contact, 2 * 60 * 1000);
      }
    } else {
      console.log(`Constant Contact contact for ${email} served from cache`);
    }
    
    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=120'); // 2 minutes
    
    res.json(contact);
  } catch (error) {
    console.error('Controller error fetching Constant Contact contact by email:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact contact by email', error: error.message });
  }
};

/**
 * Create a new contact
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createContact = async (req, res) => {
  try {
    const contact = req.body;
    
    if (!contact || !contact.email_address) {
      return res.status(400).json({ message: 'Contact data with email_address is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    const createdContact = await constantContactAPI.createContact(contact);
    
    if (!createdContact) {
      return res.status(500).json({ message: 'Error creating Constant Contact contact' });
    }
    
    // Invalidate contacts cache
    cacheUtil.del(cacheUtil.createKey('constant-contact-contacts'));
    
    // Invalidate contact by email cache
    if (contact.email_address) {
      cacheUtil.del(cacheUtil.createKey(`constant-contact-contact-${contact.email_address}`));
    }
    
    res.status(201).json(createdContact);
  } catch (error) {
    console.error('Controller error creating Constant Contact contact:', error);
    res.status(500).json({ message: 'Error creating Constant Contact contact', error: error.message });
  }
};

/**
 * Update an existing contact
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateContact = async (req, res) => {
  try {
    const { contactId } = req.params;
    const contact = req.body;
    
    if (!contactId || !contact) {
      return res.status(400).json({ message: 'Contact ID and contact data are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    const updatedContact = await constantContactAPI.updateContact(contactId, contact);
    
    if (!updatedContact) {
      return res.status(500).json({ message: 'Error updating Constant Contact contact' });
    }
    
    // Invalidate contacts cache
    cacheUtil.del(cacheUtil.createKey('constant-contact-contacts'));
    
    // Invalidate contact by email cache
    if (contact.email_address) {
      cacheUtil.del(cacheUtil.createKey(`constant-contact-contact-${contact.email_address}`));
    }
    
    res.json(updatedContact);
  } catch (error) {
    console.error('Controller error updating Constant Contact contact:', error);
    res.status(500).json({ message: 'Error updating Constant Contact contact', error: error.message });
  }
};

/**
 * Delete a contact
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteContact = async (req, res) => {
  try {
    const { contactId } = req.params;
    
    if (!contactId) {
      return res.status(400).json({ message: 'Contact ID is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    const success = await constantContactAPI.deleteContact(contactId);
    
    if (!success) {
      return res.status(500).json({ message: 'Error deleting Constant Contact contact' });
    }
    
    // Invalidate contacts cache
    cacheUtil.del(cacheUtil.createKey('constant-contact-contacts'));
    
    res.json({ message: 'Contact deleted successfully' });
  } catch (error) {
    console.error('Controller error deleting Constant Contact contact:', error);
    res.status(500).json({ message: 'Error deleting Constant Contact contact', error: error.message });
  }
};

/**
 * Get campaigns
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCampaigns = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Get query parameters
    const { limit, status } = req.query;
    const params = {};
    if (limit) params.limit = limit;
    if (status) params.status = status;
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey(`constant-contact-campaigns-${JSON.stringify(params)}`);
    
    // Try to get the campaigns from cache first
    let campaigns = cacheUtil.get(cacheKey);
    
    if (!campaigns) {
      console.log('Constant Contact campaigns cache miss, fetching from API');
      // If not in cache, fetch from API
      campaigns = await constantContactAPI.getCampaigns(params);
      
      if (!campaigns) {
        return res.status(500).json({ message: 'Error fetching Constant Contact campaigns' });
      }
      
      // Cache the result for 5 minutes (campaigns don't change often)
      cacheUtil.set(cacheKey, campaigns, 5 * 60 * 1000);
    } else {
      console.log('Constant Contact campaigns served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=300'); // 5 minutes
    
    res.json(campaigns);
  } catch (error) {
    console.error('Controller error fetching Constant Contact campaigns:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact campaigns', error: error.message });
  }
};

/**
 * Get campaign by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCampaign = async (req, res) => {
  try {
    const { campaignId } = req.params;
    
    if (!campaignId) {
      return res.status(400).json({ message: 'Campaign ID is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey(`constant-contact-campaign-${campaignId}`);
    
    // Try to get the campaign from cache first
    let campaign = cacheUtil.get(cacheKey);
    
    if (!campaign) {
      console.log(`Constant Contact campaign cache miss for ${campaignId}, fetching from API`);
      // If not in cache, fetch from API
      campaign = await constantContactAPI.getCampaign(campaignId);
      
      if (!campaign) {
        return res.status(404).json({ message: 'Campaign not found' });
      }
      
      // Cache the result for 5 minutes (campaign details don't change often)
      cacheUtil.set(cacheKey, campaign, 5 * 60 * 1000);
    } else {
      console.log(`Constant Contact campaign for ${campaignId} served from cache`);
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=300'); // 5 minutes
    
    res.json(campaign);
  } catch (error) {
    console.error('Controller error fetching Constant Contact campaign:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact campaign', error: error.message });
  }
};

/**
 * Get campaign tracking data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCampaignTracking = async (req, res) => {
  try {
    const { campaignId } = req.params;
    
    if (!campaignId) {
      return res.status(400).json({ message: 'Campaign ID is required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Constant Contact API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey(`constant-contact-campaign-tracking-${campaignId}`);
    
    // Try to get the campaign tracking from cache first
    let campaignTracking = cacheUtil.get(cacheKey);
    
    if (!campaignTracking) {
      console.log(`Constant Contact campaign tracking cache miss for ${campaignId}, fetching from API`);
      // If not in cache, fetch from API
      campaignTracking = await constantContactAPI.getCampaignTracking(campaignId);
      
      if (!campaignTracking) {
        return res.status(404).json({ message: 'Campaign tracking data not found' });
      }
      
      // Cache the result for 5 minutes (tracking data can update)
      cacheUtil.set(cacheKey, campaignTracking, 5 * 60 * 1000);
    } else {
      console.log(`Constant Contact campaign tracking for ${campaignId} served from cache`);
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=300'); // 5 minutes
    
    res.json(campaignTracking);
  } catch (error) {
    console.error('Controller error fetching Constant Contact campaign tracking:', error);
    res.status(500).json({ message: 'Error fetching Constant Contact campaign tracking', error: error.message });
  }
};

/**
 * Get configuration status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Always recompute minimal config (so redirect URL stays current)
    const config = await getLatestConfig();
    if (!config || !config.clientId) {
      return res.json({ configured: false, clientId: '', redirectUri: config?.redirectUri });
    }
    const safeConfig = {
      configured: true,
      clientId: config.clientId,
      redirectUri: config.redirectUri,
      updatedAt: config.updatedAt
    };
    res.json(safeConfig);
  } catch (error) {
    console.error('Controller error fetching configuration:', error);
    res.status(500).json({ message: 'Error fetching configuration', error: error.message });
  }
};