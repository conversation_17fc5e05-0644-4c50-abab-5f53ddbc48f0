const ColoritAPI = require('../integrations/colorlit/coloritAPI');
const ColoritConfig = require('../../models/ColoritConfig');

// Initialize Colorlit API with default values
// Will be updated with actual configuration when available
let coloritAPI = new ColoritAPI('', 80);

// Initialize Colorlit API with configuration from environment variables
const initializeColoritAPI = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.COLORLIT_HOST || '';
    const envPort = process.env.COLORLIT_PORT || 80;
    const envApiKey = process.env.COLORLIT_API_KEY || '';
    const envUsername = process.env.COLORLIT_USERNAME || '';
    const envPassword = process.env.COLORLIT_PASSWORD || '';
    
    if (envHost) {
      coloritAPI = new ColoritAPI(envHost, envPort, envApiKey, envUsername, envPassword);
      console.log(`Colorlit API initialized with environment variables: ${envHost}:${envPort}`);
      
      // Call the initialize method to update integration status
      try {
        await coloritAPI.initialize();
      } catch (initError) {
        console.error('Error updating Colorlit integration status:', initError);
        // Continue even if status update fails
      }
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('COLORLIT_HOST environment variable not set. Falling back to database config (not recommended).');
    
    // Get the latest configuration from the database
    const config = await ColoritConfig.findOne().sort({ updatedAt: -1 });
    
    if (config) {
      // Update the coloritAPI instance with the configuration
      coloritAPI = new ColoritAPI(
        config.host, 
        config.port, 
        config.apiKey, 
        config.username, 
        config.password
      );
      console.log(`Colorlit API initialized with configuration from database: ${config.host}:${config.port}`);
      
      // Call the initialize method to update integration status
      try {
        await coloritAPI.initialize();
      } catch (initError) {
        console.error('Error updating Colorlit integration status:', initError);
        // Continue even if status update fails
      }
    } else {
      console.log('No Colorlit configuration found in environment variables or database');
      
      // Update integration status to not_configured
      try {
        await coloritAPI.initialize();
      } catch (initError) {
        console.error('Error updating Colorlit integration status:', initError);
        // Continue even if status update fails
      }
    }
  } catch (error) {
    console.error('Error initializing Colorlit API:', error);
  }
};

// Call the initialization function
initializeColoritAPI();

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (!coloritAPI.host) {
    throw new Error('Colorlit configuration is missing. Please configure the Colorlit integration.');
  }
  return true;
};

/**
 * Get Colorlit device information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceInfo = async (req, res) => {
  try {
    ensureApiInitialized();
    const deviceInfo = await coloritAPI.getDeviceInfo();
    res.json(deviceInfo);
  } catch (error) {
    console.error('Controller error fetching Colorlit device information:', error);
    res.status(500).json({ message: 'Error fetching Colorlit device information', error: error.message });
  }
};

/**
 * Get current status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const status = await coloritAPI.getStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching Colorlit status:', error);
    res.status(500).json({ message: 'Error fetching Colorlit status', error: error.message });
  }
};

/**
 * Set power state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setPower = async (req, res) => {
  try {
    ensureApiInitialized();
    const { power } = req.body;
    
    if (power === undefined) {
      return res.status(400).json({ message: 'Power state is required' });
    }
    
    const result = await coloritAPI.setPower(power);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Colorlit power state:', error);
    res.status(500).json({ message: 'Error setting Colorlit power state', error: error.message });
  }
};

/**
 * Set color
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setColor = async (req, res) => {
  try {
    ensureApiInitialized();
    const { r, g, b, w } = req.body;
    
    if (r === undefined || g === undefined || b === undefined) {
      return res.status(400).json({ message: 'RGB values are required' });
    }
    
    const result = await coloritAPI.setColor(r, g, b, w || 0);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Colorlit color:', error);
    res.status(500).json({ message: 'Error setting Colorlit color', error: error.message });
  }
};

/**
 * Set brightness
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setBrightness = async (req, res) => {
  try {
    ensureApiInitialized();
    const { brightness } = req.body;
    
    if (brightness === undefined) {
      return res.status(400).json({ message: 'Brightness value is required' });
    }
    
    const result = await coloritAPI.setBrightness(brightness);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Colorlit brightness:', error);
    res.status(500).json({ message: 'Error setting Colorlit brightness', error: error.message });
  }
};

/**
 * Set mode
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setMode = async (req, res) => {
  try {
    ensureApiInitialized();
    const { mode } = req.body;
    
    if (!mode) {
      return res.status(400).json({ message: 'Mode is required' });
    }
    
    const result = await coloritAPI.setMode(mode);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Colorlit mode:', error);
    res.status(500).json({ message: 'Error setting Colorlit mode', error: error.message });
  }
};

/**
 * Get available modes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getModes = async (req, res) => {
  try {
    ensureApiInitialized();
    const modes = await coloritAPI.getModes();
    res.json(modes);
  } catch (error) {
    console.error('Controller error fetching Colorlit modes:', error);
    res.status(500).json({ message: 'Error fetching Colorlit modes', error: error.message });
  }
};

/**
 * Set speed
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setSpeed = async (req, res) => {
  try {
    ensureApiInitialized();
    const { speed } = req.body;
    
    if (speed === undefined) {
      return res.status(400).json({ message: 'Speed value is required' });
    }
    
    const result = await coloritAPI.setSpeed(speed);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Colorlit speed:', error);
    res.status(500).json({ message: 'Error setting Colorlit speed', error: error.message });
  }
};

/**
 * Get zones
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getZones = async (req, res) => {
  try {
    ensureApiInitialized();
    const zones = await coloritAPI.getZones();
    res.json(zones);
  } catch (error) {
    console.error('Controller error fetching Colorlit zones:', error);
    res.status(500).json({ message: 'Error fetching Colorlit zones', error: error.message });
  }
};

/**
 * Select zone
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.selectZone = async (req, res) => {
  try {
    ensureApiInitialized();
    const { zoneId } = req.body;
    
    if (!zoneId) {
      return res.status(400).json({ message: 'Zone ID is required' });
    }
    
    const result = await coloritAPI.selectZone(zoneId);
    res.json(result);
  } catch (error) {
    console.error('Controller error selecting Colorlit zone:', error);
    res.status(500).json({ message: 'Error selecting Colorlit zone', error: error.message });
  }
};

/**
 * Get configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Check if environment variables are set
    const envConfig = {
      host: process.env.COLORLIT_HOST || '',
      port: process.env.COLORLIT_PORT || 80,
      apiKey: process.env.COLORLIT_API_KEY ? '********' : '',
      username: process.env.COLORLIT_USERNAME || '',
      password: process.env.COLORLIT_PASSWORD ? '********' : '',
      configuredWith: 'environment'
    };
    
    if (envConfig.host) {
      return res.json(envConfig);
    }
    
    // If environment variables are not set, get config from database
    const dbConfig = await ColoritConfig.findOne().sort({ updatedAt: -1 });
    
    if (dbConfig) {
      const config = {
        host: dbConfig.host,
        port: dbConfig.port,
        apiKey: dbConfig.apiKey ? '********' : '',
        username: dbConfig.username,
        password: dbConfig.password ? '********' : '',
        configuredWith: 'database',
        updatedAt: dbConfig.updatedAt
      };
      return res.json(config);
    }
    
    // If no configuration is found
    res.json({
      host: '',
      port: 80,
      apiKey: '',
      username: '',
      password: '',
      configuredWith: 'none'
    });
  } catch (error) {
    console.error('Controller error fetching Colorlit configuration:', error);
    res.status(500).json({ message: 'Error fetching Colorlit configuration', error: error.message });
  }
};

/**
 * Save configuration to database
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    const { host, port, apiKey, username, password } = req.body;
    
    if (!host) {
      return res.status(400).json({ message: 'Host is required' });
    }
    
    // Create new configuration
    const config = new ColoritConfig({
      host,
      port: port || 80,
      apiKey: apiKey || '',
      username: username || '',
      password: password || ''
    });
    
    // Save configuration to database
    await config.save();
    
    // Reinitialize API with new configuration
    coloritAPI = new ColoritAPI(
      config.host, 
      config.port, 
      config.apiKey, 
      config.username, 
      config.password
    );
    
    // Update integration status
    try {
      await coloritAPI.initialize();
    } catch (initError) {
      console.error('Error updating Colorlit integration status:', initError);
      // Continue even if status update fails
    }
    
    res.json({
      message: 'Configuration saved successfully',
      config: {
        host: config.host,
        port: config.port,
        apiKey: config.apiKey ? '********' : '',
        username: config.username,
        password: config.password ? '********' : '',
        configuredWith: 'database',
        updatedAt: config.updatedAt
      }
    });
  } catch (error) {
    console.error('Controller error saving Colorlit configuration:', error);
    res.status(500).json({ message: 'Error saving Colorlit configuration', error: error.message });
  }
};