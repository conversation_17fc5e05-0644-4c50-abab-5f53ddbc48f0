const QsysAPI = require('../integrations/qsys/qsysAPI');

// Initialize Q-sys API with default values
// Will be updated with actual configuration when available
let qsysAPI = new QsysAPI('', 0, '', '', 'tcp');

// Initialize Q-sys API with configuration from environment variables
const initializeQsysAPI = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.QSYS_HOST || '';
    const envPort = process.env.QSYS_PORT || (process.env.QSYS_PROTOCOL === 'https' ? 443 : 1710);
    const envUsername = process.env.QSYS_USERNAME || '';
    const envPassword = process.env.QSYS_PASSWORD || '';
    const envProtocol = process.env.QSYS_PROTOCOL || 'tcp';
    
    if (envHost) {
      qsysAPI = new QsysAPI(envHost, envPort, envUsername, envPassword, envProtocol);
      console.log(`Q-sys API initialized with environment variables: ${envHost}:${envPort} (${envProtocol})`);
      
      // Call the initialize method to update integration status
      try {
        await qsysAPI.initialize();
      } catch (initError) {
        console.error('Error updating Q-sys integration status:', initError);
        // Continue even if status update fails
      }
      return;
    }
    
    // If environment variables are not set, log a warning
    console.warn('QSYS_HOST environment variable not set. Q-sys integration will not be available.');
    
    // Update integration status to not_configured
    try {
      await qsysAPI.initialize();
    } catch (initError) {
      console.error('Error updating Q-sys integration status:', initError);
      // Continue even if status update fails
    }
  } catch (error) {
    console.error('Error initializing Q-sys API:', error);
  }
};

// Call the initialization function
initializeQsysAPI();

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (!qsysAPI.host) {
    throw new Error('Q-sys configuration is missing. Please configure the Q-sys integration.');
  }
  return true;
};

/**
 * Get Q-sys Core Manager status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const status = await qsysAPI.getStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching Q-sys status:', error);
    res.status(500).json({ message: 'Error fetching Q-sys status', error: error.message });
  }
};

/**
 * Get Q-sys Core Manager health status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getHealthStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const healthStatus = await qsysAPI.getHealthStatus();
    res.json(healthStatus);
  } catch (error) {
    console.error('Controller error fetching Q-sys health status:', error);
    res.status(500).json({
      message: 'Error fetching Q-sys health status',
      error: error.message,
      status: 'unhealthy',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get Q-sys Core Manager components
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getComponents = async (req, res) => {
  try {
    ensureApiInitialized();
    const components = await qsysAPI.getComponents();
    res.json(components);
  } catch (error) {
    console.error('Controller error fetching Q-sys components:', error);
    res.status(500).json({ message: 'Error fetching Q-sys components', error: error.message });
  }
};

/**
 * Get controls for a component
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getControls = async (req, res) => {
  try {
    ensureApiInitialized();
    const { componentName } = req.params;
    
    if (!componentName) {
      return res.status(400).json({ message: 'Component name is required' });
    }
    
    const controls = await qsysAPI.getControls(componentName);
    res.json(controls);
  } catch (error) {
    console.error('Controller error fetching Q-sys controls:', error);
    res.status(500).json({ message: 'Error fetching Q-sys controls', error: error.message });
  }
};

/**
 * Get the value of a control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getControlValue = async (req, res) => {
  try {
    ensureApiInitialized();
    const { componentName, controlName } = req.params;
    
    if (!componentName || !controlName) {
      return res.status(400).json({ message: 'Component name and control name are required' });
    }
    
    const value = await qsysAPI.getControlValue(componentName, controlName);
    res.json(value);
  } catch (error) {
    console.error('Controller error fetching Q-sys control value:', error);
    res.status(500).json({ message: 'Error fetching Q-sys control value', error: error.message });
  }
};

/**
 * Set the value of a control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setControlValue = async (req, res) => {
  try {
    ensureApiInitialized();
    const { componentName, controlName } = req.params;
    const { value } = req.body;
    
    if (!componentName || !controlName) {
      return res.status(400).json({ message: 'Component name and control name are required' });
    }
    
    if (value === undefined) {
      return res.status(400).json({ message: 'Value is required' });
    }
    
    const result = await qsysAPI.setControlValue(componentName, controlName, value);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Q-sys control value:', error);
    res.status(500).json({ message: 'Error setting Q-sys control value', error: error.message });
  }
};

/**
 * Get Q-sys configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Set headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    const config = await qsysAPI.getConfig();
    res.json(config);
  } catch (error) {
    console.error('Error fetching Q-sys configuration:', error);
    res.status(500).json({ message: 'Error fetching Q-sys configuration', error: error.message });
  }
};

/**
 * Save Q-sys configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Q-sys configuration request:', error);
    res.status(500).json({ message: 'Error handling Q-sys configuration request', error: error.message });
  }
};

/**
 * Set up Q-sys with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Q-sys one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Q-sys one-click setup request', 
      error: error.message 
    });
  }
};