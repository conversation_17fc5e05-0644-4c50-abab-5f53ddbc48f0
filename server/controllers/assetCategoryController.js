const AssetCategory = require('../../models/AssetCategory');
const Asset = require('../../models/Asset');

/**
 * Asset Category Controller
 * Manages asset categorization system
 */

/**
 * Get all asset categories
 */
exports.getCategories = async (req, res) => {
  try {
    const { includeInactive = false, includeAssetCount = false } = req.query;

    const query = includeInactive === 'true' ? {} : { isActive: true };
    
    let categoriesQuery = AssetCategory.find(query)
      .populate('parentCategory', 'name')
      .populate('createdBy', 'name email')
      .sort({ name: 1 });

    if (includeAssetCount === 'true') {
      categoriesQuery = categoriesQuery.populate('assetCount');
    }

    const categories = await categoriesQuery;

    // Build hierarchical structure
    const categoryMap = new Map();
    const rootCategories = [];

    // First pass: create map of all categories
    categories.forEach(category => {
      categoryMap.set(category._id.toString(), {
        ...category.toObject(),
        children: []
      });
    });

    // Second pass: build hierarchy
    categories.forEach(category => {
      const categoryObj = categoryMap.get(category._id.toString());
      
      if (category.parentCategory) {
        const parent = categoryMap.get(category.parentCategory._id.toString());
        if (parent) {
          parent.children.push(categoryObj);
        } else {
          rootCategories.push(categoryObj);
        }
      } else {
        rootCategories.push(categoryObj);
      }
    });

    res.json({
      categories: rootCategories,
      totalCount: categories.length
    });
  } catch (error) {
    console.error('Error getting categories:', error);
    res.status(500).json({ message: 'Error getting categories', error: error.message });
  }
};

/**
 * Get single category by ID
 */
exports.getCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    const category = await AssetCategory.findById(id)
      .populate('parentCategory', 'name description')
      .populate('children')
      .populate('assetCount')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    res.json(category);
  } catch (error) {
    console.error('Error getting category:', error);
    res.status(500).json({ message: 'Error getting category', error: error.message });
  }
};

/**
 * Create new category
 */
exports.createCategory = async (req, res) => {
  try {
    const categoryData = {
      ...req.body,
      createdBy: req.user._id
    };

    // Validate parent category if provided
    if (categoryData.parentCategory) {
      const parentCategory = await AssetCategory.findById(categoryData.parentCategory);
      if (!parentCategory) {
        return res.status(400).json({ message: 'Parent category not found' });
      }
    }

    const category = new AssetCategory(categoryData);
    category._modifiedBy = req.user._id;
    await category.save();

    await category.populate([
      { path: 'parentCategory', select: 'name description' },
      { path: 'createdBy', select: 'name email' }
    ]);

    res.status(201).json(category);
  } catch (error) {
    console.error('Error creating category:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({ 
        message: 'Category name already exists',
        field: 'name'
      });
    }
    
    res.status(500).json({ message: 'Error creating category', error: error.message });
  }
};

/**
 * Update category
 */
exports.updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const category = await AssetCategory.findById(id);
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    // Validate parent category if being updated
    if (updates.parentCategory && updates.parentCategory !== category.parentCategory?.toString()) {
      const parentCategory = await AssetCategory.findById(updates.parentCategory);
      if (!parentCategory) {
        return res.status(400).json({ message: 'Parent category not found' });
      }

      // Check for circular reference
      const wouldCreateCircle = await checkCircularReference(id, updates.parentCategory);
      if (wouldCreateCircle) {
        return res.status(400).json({ message: 'Cannot create circular reference in category hierarchy' });
      }
    }

    Object.assign(category, updates);
    category._modifiedBy = req.user._id;
    await category.save();

    await category.populate([
      { path: 'parentCategory', select: 'name description' },
      { path: 'lastModifiedBy', select: 'name email' }
    ]);

    res.json(category);
  } catch (error) {
    console.error('Error updating category:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({ 
        message: 'Category name already exists',
        field: 'name'
      });
    }
    
    res.status(500).json({ message: 'Error updating category', error: error.message });
  }
};

/**
 * Delete category (soft delete)
 */
exports.deleteCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { force = false } = req.query;
    
    const category = await AssetCategory.findById(id);
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    // Check if category has assets
    const assetCount = await Asset.countDocuments({ category: id, isDeleted: false });
    if (assetCount > 0 && !force) {
      return res.status(400).json({ 
        message: `Cannot delete category with ${assetCount} assets. Use force=true to proceed.`,
        assetCount 
      });
    }

    // Check if category has child categories
    const childCount = await AssetCategory.countDocuments({ parentCategory: id, isActive: true });
    if (childCount > 0 && !force) {
      return res.status(400).json({ 
        message: `Cannot delete category with ${childCount} child categories. Use force=true to proceed.`,
        childCount 
      });
    }

    if (force && assetCount > 0) {
      // Move assets to parent category or null
      await Asset.updateMany(
        { category: id },
        { 
          category: category.parentCategory || null,
          lastModifiedBy: req.user._id
        }
      );
    }

    if (force && childCount > 0) {
      // Move child categories to parent or root level
      await AssetCategory.updateMany(
        { parentCategory: id },
        { 
          parentCategory: category.parentCategory || null,
          lastModifiedBy: req.user._id
        }
      );
    }

    category.isActive = false;
    category._modifiedBy = req.user._id;
    await category.save();

    res.json({ 
      message: 'Category deactivated successfully',
      assetsRelocated: force ? assetCount : 0,
      childrenRelocated: force ? childCount : 0
    });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Error deleting category', error: error.message });
  }
};

/**
 * Get category tree (hierarchical view)
 */
exports.getCategoryTree = async (req, res) => {
  try {
    const rootCategories = await AssetCategory.findRootCategories();
    
    // Recursively populate the tree with asset counts
    const populateTree = async (categories) => {
      for (const category of categories) {
        // Get asset count for this category and all children
        const assetCount = await getAssetCountRecursive(category._id);
        category.assetCount = assetCount;
        
        if (category.children && category.children.length > 0) {
          await populateTree(category.children);
        }
      }
    };

    await populateTree(rootCategories);
    
    res.json(rootCategories);
  } catch (error) {
    console.error('Error getting category tree:', error);
    res.status(500).json({ message: 'Error getting category tree', error: error.message });
  }
};

/**
 * Get assets by category
 */
exports.getCategoryAssets = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      page = 1, 
      limit = 20, 
      includeChildren = false,
      status,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    const category = await AssetCategory.findById(id);
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }

    let categoryIds = [id];
    
    if (includeChildren === 'true') {
      const childCategories = await category.getAllChildren();
      categoryIds = categoryIds.concat(childCategories.map(c => c._id));
    }

    const query = { 
      category: { $in: categoryIds },
      isDeleted: false 
    };
    
    if (status) query.status = status;

    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;
    const [assets, total] = await Promise.all([
      Asset.find(query)
        .populate('category', 'name')
        .populate('location', 'name locationType')
        .populate('assignedTo', 'name email')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit)),
      Asset.countDocuments(query)
    ]);

    res.json({
      category: {
        id: category._id,
        name: category.name,
        description: category.description
      },
      assets,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting category assets:', error);
    res.status(500).json({ message: 'Error getting category assets', error: error.message });
  }
};

/**
 * Get category statistics
 */
exports.getCategoryStats = async (req, res) => {
  try {
    const stats = await AssetCategory.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'assets',
          localField: '_id',
          foreignField: 'category',
          as: 'assets'
        }
      },
      {
        $addFields: {
          assetCount: { $size: '$assets' },
          activeAssets: {
            $size: {
              $filter: {
                input: '$assets',
                cond: { 
                  $and: [
                    { $eq: ['$$this.isDeleted', false] },
                    { $eq: ['$$this.status', 'active'] }
                  ]
                }
              }
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalCategories: { $sum: 1 },
          totalAssets: { $sum: '$assetCount' },
          totalActiveAssets: { $sum: '$activeAssets' },
          categoriesWithAssets: {
            $sum: { $cond: [{ $gt: ['$assetCount', 0] }, 1, 0] }
          },
          avgAssetsPerCategory: { $avg: '$assetCount' }
        }
      }
    ]);

    const topCategories = await AssetCategory.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'assets',
          localField: '_id',
          foreignField: 'category',
          as: 'assets'
        }
      },
      {
        $addFields: {
          assetCount: { $size: '$assets' }
        }
      },
      { $sort: { assetCount: -1 } },
      { $limit: 10 },
      {
        $project: {
          name: 1,
          description: 1,
          assetCount: 1
        }
      }
    ]);

    res.json({
      overview: stats[0] || {
        totalCategories: 0,
        totalAssets: 0,
        totalActiveAssets: 0,
        categoriesWithAssets: 0,
        avgAssetsPerCategory: 0
      },
      topCategories
    });
  } catch (error) {
    console.error('Error getting category stats:', error);
    res.status(500).json({ message: 'Error getting category stats', error: error.message });
  }
};

/**
 * Helper function to check for circular references
 */
async function checkCircularReference(categoryId, parentCategoryId) {
  if (categoryId === parentCategoryId) {
    return true;
  }

  const parentCategory = await AssetCategory.findById(parentCategoryId);
  if (!parentCategory || !parentCategory.parentCategory) {
    return false;
  }

  return await checkCircularReference(categoryId, parentCategory.parentCategory.toString());
}

/**
 * Helper function to get asset count recursively
 */
async function getAssetCountRecursive(categoryId) {
  const category = await AssetCategory.findById(categoryId);
  if (!category) return 0;

  let count = await Asset.countDocuments({ 
    category: categoryId, 
    isDeleted: false 
  });

  const children = await AssetCategory.find({ parentCategory: categoryId, isActive: true });
  for (const child of children) {
    count += await getAssetCountRecursive(child._id);
  }

  return count;
}