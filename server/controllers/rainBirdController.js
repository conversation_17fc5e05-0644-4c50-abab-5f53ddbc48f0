const RainBirdAPI = require('../integrations/rainBird/rainBirdAPI');
const RainBirdConfig = require('../../models/RainBirdConfig');

// Initialize Rain Bird API with empty credentials (will be updated when needed)
let rainBirdAPI = new RainBirdAPI('', '', '');

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.RAIN_BIRD_HOST || '';
    const envUsername = process.env.RAIN_BIRD_USERNAME || '';
    const envPassword = process.env.RAIN_BIRD_PASSWORD || '';
    const envPort = process.env.RAIN_BIRD_PORT || 443;
    const envLocalNetwork = process.env.RAIN_BIRD_LOCAL_NETWORK === 'true';
    const envCloudHost = process.env.RAIN_BIRD_CLOUD_HOST || 'rdz-rbcloud.rainbird.com';
    const envControllerMac = process.env.RAIN_BIRD_CONTROLLER_MAC || '';
    
    // If environment variables are set, use them directly
    if (envHost && envPassword) {
      // Username is not actually used by the Rain Bird API, but kept for backward compatibility
      rainBirdAPI = new RainBirdAPI(
        envHost, 
        envUsername, 
        envPassword, 
        envPort, 
        envLocalNetwork, 
        envCloudHost, 
        envControllerMac
      );
      return {
        host: envHost,
        username: envUsername,
        password: envPassword,
        port: envPort,
        localNetwork: envLocalNetwork,
        cloudHost: envCloudHost,
        controllerMac: envControllerMac,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('RAIN_BIRD environment variables not set. Falling back to database config (not recommended).');
    
    const config = await RainBirdConfig.findOne().sort({ updatedAt: -1 });
    if (config) {
      // Username is not actually used by the Rain Bird API, but kept for backward compatibility
      rainBirdAPI = new RainBirdAPI(
        config.host, 
        config.username, 
        config.password, 
        config.port,
        config.localNetwork,
        config.cloudHost,
        config.controllerMac
      );
      return config;
    }
    return null;
  } catch (error) {
    console.error('Error fetching Rain Bird configuration:', error);
    throw error;
  }
};

/**
 * Get all Rain Bird zones
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getZones = async (req, res) => {
  try {
    await getLatestConfig();
    const zones = await rainBirdAPI.getZones();
    res.json(zones);
  } catch (error) {
    console.error('Controller error fetching Rain Bird zones:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird zones', error: error.message });
  }
};

/**
 * Get Rain Bird zone details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getZoneDetails = async (req, res) => {
  try {
    await getLatestConfig();
    const zoneId = req.params.id;
    const zoneDetails = await rainBirdAPI.getZoneDetails(zoneId);
    res.json(zoneDetails);
  } catch (error) {
    console.error('Controller error fetching Rain Bird zone details:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird zone details', error: error.message });
  }
};

/**
 * Start watering a zone
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.startZone = async (req, res) => {
  try {
    await getLatestConfig();
    const zoneId = req.params.id;
    const { duration } = req.body;
    const result = await rainBirdAPI.startZone(zoneId, duration);
    res.json(result);
  } catch (error) {
    console.error('Controller error starting Rain Bird zone:', error);
    res.status(500).json({ message: 'Error starting Rain Bird zone', error: error.message });
  }
};

/**
 * Stop watering a zone
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.stopZone = async (req, res) => {
  try {
    await getLatestConfig();
    const zoneId = req.params.id;
    const result = await rainBirdAPI.stopZone(zoneId);
    res.json(result);
  } catch (error) {
    console.error('Controller error stopping Rain Bird zone:', error);
    res.status(500).json({ message: 'Error stopping Rain Bird zone', error: error.message });
  }
};

/**
 * Get all Rain Bird programs (schedules)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPrograms = async (req, res) => {
  try {
    await getLatestConfig();
    const programs = await rainBirdAPI.getPrograms();
    res.json(programs);
  } catch (error) {
    console.error('Controller error fetching Rain Bird programs:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird programs', error: error.message });
  }
};

/**
 * Start a program
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.startProgram = async (req, res) => {
  try {
    await getLatestConfig();
    const programId = req.params.id;
    const result = await rainBirdAPI.startProgram(programId);
    res.json(result);
  } catch (error) {
    console.error('Controller error starting Rain Bird program:', error);
    res.status(500).json({ message: 'Error starting Rain Bird program', error: error.message });
  }
};

/**
 * Stop a program
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.stopProgram = async (req, res) => {
  try {
    await getLatestConfig();
    const programId = req.params.id;
    const result = await rainBirdAPI.stopProgram(programId);
    res.json(result);
  } catch (error) {
    console.error('Controller error stopping Rain Bird program:', error);
    res.status(500).json({ message: 'Error stopping Rain Bird program', error: error.message });
  }
};

/**
 * Get Rain Bird system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    await getLatestConfig();
    const status = await rainBirdAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching Rain Bird system status:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird system status', error: error.message });
  }
};

/**
 * Save Rain Bird configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Rain Bird configuration request:', error);
    res.status(500).json({ message: 'Error handling Rain Bird configuration request', error: error.message });
  }
};

/**
 * Get Rain Bird configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Always prioritize environment variables for authentication
    const envHost = process.env.RAIN_BIRD_HOST || '';
    const envUsername = process.env.RAIN_BIRD_USERNAME || '';
    const envPassword = process.env.RAIN_BIRD_PASSWORD || '';
    const envPort = process.env.RAIN_BIRD_PORT || 443;
    const envLocalNetwork = process.env.RAIN_BIRD_LOCAL_NETWORK === 'true';
    const envCloudHost = process.env.RAIN_BIRD_CLOUD_HOST || 'rdz-rbcloud.rainbird.com';
    const envControllerMac = process.env.RAIN_BIRD_CONTROLLER_MAC || '';
    
    // If environment variables are set, use them directly
    if (envHost && envPassword) {
      // Don't send the actual password back to the client for security
      res.json({
        host: envHost,
        username: envUsername, // Username is not actually used by the Rain Bird API, but kept for backward compatibility
        port: envPort,
        localNetwork: envLocalNetwork,
        cloudHost: envCloudHost,
        controllerMac: envControllerMac ? '**********' : '', // Mask the MAC address for security
        configuredAt: new Date(),
        fromEnv: true
      });
      return;
    }
    
    // If environment variables are not set, fall back to database config (not recommended)
    console.warn('RAIN_BIRD environment variables not set. Falling back to database config (not recommended).');
    
    const config = await getLatestConfig();

    if (!config) {
      return res.status(404).json({ message: 'Rain Bird configuration not found. Please set the required environment variables.' });
    }

    // Don't send the actual password back to the client for security
    res.json({
      host: config.host,
      username: config.username,
      port: config.port,
      localNetwork: config.localNetwork,
      configuredAt: config.updatedAt,
      fromEnv: !!config.fromEnv
    });
  } catch (error) {
    console.error('Error fetching Rain Bird configuration:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird configuration', error: error.message });
  }
};

/**
 * One-click setup for Rain Bird
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Rain Bird one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Rain Bird one-click setup request', 
      error: error.message 
    });
  }
};

/**
 * Stop all zones
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.stopAllZones = async (req, res) => {
  try {
    await getLatestConfig();
    const result = await rainBirdAPI.stopAllZones();
    res.json(result);
  } catch (error) {
    console.error('Controller error stopping all Rain Bird zones:', error);
    res.status(500).json({ message: 'Error stopping all Rain Bird zones', error: error.message });
  }
};

/**
 * Get rain delay
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getRainDelay = async (req, res) => {
  try {
    await getLatestConfig();
    const result = await rainBirdAPI.getRainDelay();
    res.json(result);
  } catch (error) {
    console.error('Controller error fetching Rain Bird rain delay:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird rain delay', error: error.message });
  }
};

/**
 * Set rain delay
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setRainDelay = async (req, res) => {
  try {
    await getLatestConfig();
    const { hours } = req.body;
    
    if (hours === undefined || isNaN(hours) || hours < 0) {
      return res.status(400).json({ message: 'Invalid hours parameter. Must be a non-negative number.' });
    }
    
    const result = await rainBirdAPI.setRainDelay(hours);
    res.json(result);
  } catch (error) {
    console.error('Controller error setting Rain Bird rain delay:', error);
    res.status(500).json({ message: 'Error setting Rain Bird rain delay', error: error.message });
  }
};

/**
 * Get rain sensor state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getRainSensorState = async (req, res) => {
  try {
    await getLatestConfig();
    const result = await rainBirdAPI.getRainSensorState();
    res.json(result);
  } catch (error) {
    console.error('Controller error fetching Rain Bird rain sensor state:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird rain sensor state', error: error.message });
  }
};

/**
 * Get irrigation state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getIrrigationState = async (req, res) => {
  try {
    await getLatestConfig();
    const result = await rainBirdAPI.getIrrigationState();
    res.json(result);
  } catch (error) {
    console.error('Controller error fetching Rain Bird irrigation state:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird irrigation state', error: error.message });
  }
};

/**
 * Get controller state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getControllerState = async (req, res) => {
  try {
    await getLatestConfig();
    const result = await rainBirdAPI.getControllerState();
    res.json(result);
  } catch (error) {
    console.error('Controller error fetching Rain Bird controller state:', error);
    res.status(500).json({ message: 'Error fetching Rain Bird controller state', error: error.message });
  }
};