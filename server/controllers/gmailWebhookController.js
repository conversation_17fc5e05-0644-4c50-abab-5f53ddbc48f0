const { PubSub } = require('@google-cloud/pubsub');
const { google } = require('googleapis');
const ticketEmailController = require('./ticketEmailController');

/**
 * Gmail Webhook Controller
 * Handles Gmail push notifications via Google Cloud Pub/Sub
 */
class GmailWebhookController {
  constructor() {
    this.pubsub = new PubSub({
      projectId: process.env.GOOGLE_GMAIL_PROJECT_ID,
      keyFilename: process.env.GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY
    });

    this.gmail = google.gmail({ version: 'v1' });
    this.subscription = null;
    this.isListening = false;
    this.processedMessageIds = new Set();
  }

  /**
   * Initialize Gmail webhook listener
   */
  async initialize() {
    try {
      if (!process.env.GOOGLE_GMAIL_PROJECT_ID || !process.env.GOOGLE_GMAIL_SUBSCRIPTION_NAME) {
        console.log('Gmail webhook not configured, skipping initialization');
        return;
      }

      // Set up Service Account JWT with domain-wide delegation to the monitored mailbox
      const jwtClient = new google.auth.JWT({
        keyFile: process.env.GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY,
        scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
        subject: process.env.GMAIL_MONITORED_EMAIL
      });
      await jwtClient.authorize();

      this.gmail = google.gmail({ version: 'v1', auth: jwtClient });

      // Set up Pub/Sub subscription
      this.subscription = this.pubsub.subscription(process.env.GOOGLE_GMAIL_SUBSCRIPTION_NAME);
      // Note: leave subscription options as defaults to ensure compatibility with library versions

      // Set up message handler
      this.subscription.on('message', this.handlePubSubMessage.bind(this));
      this.subscription.on('error', error => {
        console.error('Gmail Pub/Sub subscription error:', error);
      });

      console.log('Gmail webhook listener initialized');
      this.isListening = true;

      // Set up Gmail push notifications
      await this.setupGmailWatch();
      
    } catch (error) {
      console.error('Error initializing Gmail webhook:', error);
    }
  }

  /**
   * Set up Gmail push notifications
   */
  async setupGmailWatch() {
    try {
      const topicName = `projects/${process.env.GOOGLE_GMAIL_PROJECT_ID}/topics/${process.env.GOOGLE_GMAIL_TOPIC_NAME}`;
      
      const watchRequest = {
        userId: 'me',
        requestBody: {
          topicName: topicName,
          labelIds: ['INBOX'],
          labelFilterAction: 'include'
        }
      };

      const response = await this.gmail.users.watch(watchRequest);
      console.log('Gmail watch setup successful:', response.data);
      
      // Schedule renewal (Gmail watch expires after 7 days)
      setTimeout(() => {
        this.setupGmailWatch();
      }, 6 * 24 * 60 * 60 * 1000); // Renew after 6 days
      
    } catch (error) {
      console.error('Error setting up Gmail watch:', error);
    }
  }

  /**
   * Handle incoming Pub/Sub messages
   * @param {Object} message - Pub/Sub message
   */
  async handlePubSubMessage(message) {
    try {
      console.log('Received Gmail notification:', message.data.toString());
      
      // Parse the notification
      const notification = JSON.parse(message.data.toString());
      
      if (notification.emailAddress && notification.historyId) {
        // Get the new messages since the last history ID
        await this.processGmailHistory(notification);
      }
      
      // Acknowledge the message
      message.ack();
      
    } catch (error) {
      console.error('Error handling Gmail Pub/Sub message:', error);
      message.nack();
    }
  }

  /**
   * Process Gmail history to find new messages
   * @param {Object} notification - Gmail notification data
   */
  async processGmailHistory(notification) {
    try {
      const historyResponse = await this.gmail.users.history.list({
        userId: 'me',
        startHistoryId: notification.historyId,
        historyTypes: ['messageAdded']
      });

      const history = historyResponse.data.history || [];
      
      for (const historyItem of history) {
        if (historyItem.messagesAdded) {
          for (const messageAdded of historyItem.messagesAdded) {
            await this.processGmailMessage(messageAdded.message);
          }
        }
      }
      
    } catch (error) {
      console.error('Error processing Gmail history:', error);
    }
  }

  /**
   * Process individual Gmail message
   * @param {Object} message - Gmail message
   * @param {string} emailAddress - Email address
   */
  async processGmailMessage(message, emailAddress = 'me') {
    try {
      // Get full message details
      const messageResponse = await this.gmail.users.messages.get({
        userId: emailAddress || 'me',
        id: message.id,
        format: 'full'
      });

      const fullMessage = messageResponse.data;
      
      // Convert Gmail message to standard email format
      const emailData = this.convertGmailToEmailData(fullMessage);
      
      if (emailData) {
        // Process through existing email controller
        await this.simulateWebhookRequest(emailData);
      }
      
    } catch (error) {
      console.error('Error processing Gmail message:', error);
    }
  }

  /**
   * Convert Gmail message format to standard email webhook format
   * @param {Object} gmailMessage - Gmail API message
   * @returns {Object} Standard email data
   */
  convertGmailToEmailData(gmailMessage) {
    try {
      const headers = {};
      const payload = gmailMessage.payload;
      
      const decodeBase64Url = (data) => {
        if (!data) return '';
        let s = data.replace(/-/g, '+').replace(/_/g, '/');
        const pad = s.length % 4;
        if (pad) s += '='.repeat(4 - pad);
        return Buffer.from(s, 'base64').toString('utf-8');
      };
      
      // Extract headers
      if (payload.headers) {
        payload.headers.forEach(header => {
          headers[header.name.toLowerCase()] = header.value;
        });
      }

      // Extract body content
      let textContent = '';
      let htmlContent = '';
      
      const extractContent = (part) => {
        if (part.mimeType === 'text/plain' && part.body.data) {
          textContent += decodeBase64Url(part.body.data);
        } else if (part.mimeType === 'text/html' && part.body.data) {
          htmlContent += decodeBase64Url(part.body.data);
        } else if (part.parts) {
          part.parts.forEach(extractContent);
        }
      };

      if (payload.parts) {
        payload.parts.forEach(extractContent);
      } else if (payload.body.data) {
        if (payload.mimeType === 'text/plain') {
          textContent = decodeBase64Url(payload.body.data);
        } else if (payload.mimeType === 'text/html') {
          htmlContent = decodeBase64Url(payload.body.data);
        }
      }

      return {
        messageId: headers['message-id'],
        from: headers['from'],
        to: headers['to'],
        subject: headers['subject'],
        text: textContent,
        html: htmlContent,
        inReplyTo: headers['in-reply-to'],
        references: headers['references'],
        headers: headers,
        attachments: [] // TODO: Extract attachments if needed
      };
      
    } catch (error) {
      console.error('Error converting Gmail message:', error);
      return null;
    }
  }

  /**
   * Simulate webhook request for email processing
   * @param {Object} emailData - Email data
   */
  async simulateWebhookRequest(emailData) {
    try {
      // Create mock request/response objects
      const mockReq = {
        body: emailData,
        headers: { 'user-agent': 'Gmail-Webhook-Processor' }
      };
      
      const mockRes = {
        status: (code) => ({ json: (data) => console.log(`Gmail webhook response ${code}:`, data) }),
        json: (data) => console.log('Gmail webhook response:', data)
      };

      // Process through existing email controller
      await ticketEmailController.processIncomingEmail(mockReq, mockRes);
      
    } catch (error) {
      console.error('Error simulating webhook request:', error);
    }
  }

  /**
   * Stop listening to Gmail notifications
   */
  stop() {
    if (this.subscription && this.isListening) {
      this.subscription.removeAllListeners();
      this.isListening = false;
      console.log('Gmail webhook listener stopped');
    }
  }
}

module.exports = new GmailWebhookController();