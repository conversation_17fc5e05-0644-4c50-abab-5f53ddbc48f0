const GoogleCalendarAPI = require('../integrations/googleCalendar/googleCalendarAPI');
const User = require('../../models/User');
const path = require('path');
const fs = require('fs');

// Check if service account credentials are available (primary authentication method)
const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
const usingServiceAccount = !!(serviceAccountEmail && serviceAccountPrivateKey);

// OAuth fallback variables (for backwards compatibility only)
const clientId = process.env.GOOGLE_CALENDAR_CLIENT_ID || '';
const clientSecret = process.env.GOOGLE_CALENDAR_CLIENT_SECRET || '';
const redirectUri = process.env.GOOGLE_CALENDAR_REDIRECT_URI || '';
const tokenPath = path.resolve(process.cwd(), process.env.GOOGLE_CALENDAR_TOKEN_PATH || './google-calendar-token.json');

// Create a global API instance
// Use a default admin email for service account impersonation when no specific user is provided
const defaultServiceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || '<EMAIL>';

let googleCalendarAPI = new GoogleCalendarAPI(
  clientId,
  clientSecret,
  redirectUri,
  tokenPath,
  null, // No user tokens
  null, // No user ID
  defaultServiceAccountEmail // Use the service account email itself as the default impersonation email
);

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (usingServiceAccount) {
    if (!serviceAccountEmail || !serviceAccountPrivateKey) {
      throw new Error('Google service account configuration is missing. Please check your environment variables.');
    }
  } else if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Google Calendar configuration is missing. Please check your environment variables.');
  }
  return true;
};

// Helper function to get an API instance with user email for service account impersonation
// or fall back to user tokens if service account is not configured
const getApiWithUser = async (userId) => {
  try {
    // Ensure environment variables are set
    ensureApiInitialized();

    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (usingServiceAccount) {
      // Use service account with user impersonation
      if (!user.email) {
        throw new Error('User email is required for service account impersonation');
      }

      const api = new GoogleCalendarAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        null, // No user tokens
        userId,
        user.email // Pass the user email for service account impersonation
      );

      await api.initialize();
      return api;
    } else {
      // Fall back to OAuth tokens if service account is not configured
      if (!user.googleAccessToken) {
        throw new Error('User missing Google tokens and service account not configured');
      }

      // Create a new API instance with the user's tokens
      const userTokens = {
        accessToken: user.googleAccessToken,
        refreshToken: user.googleRefreshToken
      };

      const api = new GoogleCalendarAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        userTokens,
        userId
      );

      await api.initialize();
      return api;
    }
  } catch (error) {
    console.error('Error creating API with user:', error);
    throw error;
  }
};

// Helper function to get the best available API instance
const getApiInstance = async (req) => {
  let api;

  // Try to use the user's credentials if available
  if (req.user && req.user.id) {
    try {
      api = await getApiWithUser(req.user.id);
      return api;
    } catch (userError) {
      console.log('Could not use user credentials, falling back to global config:', userError.message);
      // Fall back to global config if user credentials don't work
    }
  }

  // No user in the request or user credentials failed, use global config
  // Ensure environment variables are set
  ensureApiInitialized();

  // Initialize the API if needed
  try {
    await googleCalendarAPI.initialize();
  } catch (error) {
    console.error('Error initializing Google Calendar API:', error);
  }

  return googleCalendarAPI;
};

// Helper function to get API instance using only user credentials
// This ensures only calendars the user has access to are shown
const getUserApiInstance = async (req) => {
  // Check if user is logged in
  if (!req.user || !req.user.id) {
    throw new Error('User authentication required to access calendars');
  }

  try {
    // Get API instance with user credentials
    const api = await getApiWithUser(req.user.id);
    return api;
  } catch (error) {
    console.error('Error getting user API instance:', error);
    throw new Error('Unable to access user calendars: ' + error.message);
  }
};

/**
 * Save Google Calendar configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Google Calendar configuration request:', error);
    res.status(500).json({ message: 'Error handling Google Calendar configuration request', error: error.message });
  }
};

/**
 * Get Google Calendar configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // First check if the user has Google tokens
    let userHasTokens = false;
    let userAuthStatus = {
      isAuthenticated: false,
      message: ''
    };

    if (req.user && req.user.id) {
      const user = await User.findById(req.user.id);
      if (user && user.googleAccessToken && user.googleRefreshToken) {
        userHasTokens = true;
        userAuthStatus = {
          isAuthenticated: true,
          message: 'You are authenticated with Google Calendar using your Google account'
        };
      }
    }

    // Ensure environment variables are set
    ensureApiInitialized();

    // Check if environment variables are being used
    const usingEnvVars = {
      serviceAccount: !!(process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && 
                        process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY),
      clientId: !!process.env.GOOGLE_CALENDAR_CLIENT_ID,
      clientSecret: !!process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
      redirectUri: !!process.env.GOOGLE_CALENDAR_REDIRECT_URI,
      tokenPath: !!process.env.GOOGLE_CALENDAR_TOKEN_PATH
    };

    // Check if the global API is authenticated
    const globalAuthStatus = {
      isAuthenticated: googleCalendarAPI ? googleCalendarAPI.isAuthenticated() : false
    };

    // If user has tokens, they're authenticated regardless of global config
    if (userHasTokens) {
      return res.json({
        userAuth: userAuthStatus,
        globalAuth: globalAuthStatus,
        isAuthenticated: true,
        clientId: process.env.GOOGLE_CALENDAR_CLIENT_ID,
        redirectUri: process.env.GOOGLE_CALENDAR_REDIRECT_URI,
        tokenPath: process.env.GOOGLE_CALENDAR_TOKEN_PATH,
        configuredAt: new Date(),
        usingEnvVars
      });
    }

    // If no user tokens but we have a global config
    return res.json({
      userAuth: userAuthStatus,
      globalAuth: globalAuthStatus,
      isAuthenticated: globalAuthStatus.isAuthenticated,
      clientId: process.env.GOOGLE_CALENDAR_CLIENT_ID,
      redirectUri: process.env.GOOGLE_CALENDAR_REDIRECT_URI,
      tokenPath: process.env.GOOGLE_CALENDAR_TOKEN_PATH,
      configuredAt: new Date(),
      usingEnvVars
    });
  } catch (error) {
    console.error('Error fetching Google Calendar configuration:', error);
    res.status(500).json({ message: 'Error fetching Google Calendar configuration', error: error.message });
  }
};

/**
 * Get authentication URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAuthUrl = async (req, res) => {
  try {
    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Google Calendar',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    // Check if user has Google tokens
    const user = await User.findById(req.user.id);
    if (!user.googleAccessToken || !user.googleRefreshToken) {
      return res.status(401).json({ 
        message: 'You need to authenticate with Google. Please log out and log back in.',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    // Ensure environment variables are set
    ensureApiInitialized();

    // Get the auth URL from the global API
    const authUrl = googleCalendarAPI.getAuthUrl();
    return res.json({ 
      message: 'You can use your Google account or the global configuration',
      userAuth: true,
      globalAuthUrl: authUrl 
    });
  } catch (error) {
    console.error('Error generating Google Calendar auth URL:', error);
    res.status(500).json({ message: 'Error generating Google Calendar auth URL', error: error.message });
  }
};

/**
 * Handle OAuth2 callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code } = req.query;

    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Google Calendar',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    // Ensure environment variables are set
    ensureApiInitialized();

    // Get tokens from Google
    await googleCalendarAPI.getToken(code);

    // For user authentication, we don't need to handle the callback here
    // The main Google auth callback already stores the tokens in the user model

    res.json({ 
      message: 'Google Calendar authentication successful',
      userAuth: true,
      globalAuth: true
    });
  } catch (error) {
    console.error('Error handling Google Calendar callback:', error);
    res.status(500).json({ message: 'Error handling Google Calendar callback', error: error.message });
  }
};

/**
 * List calendars
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listCalendars = async (req, res) => {
  try {
    // Use getUserApiInstance to ensure we only use user tokens
    // This ensures only calendars the user has access to are shown
    const api = await getUserApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'You must be authenticated with your Google account to view calendars',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    const calendars = await api.listCalendars();
    res.json(calendars);
  } catch (error) {
    console.error('Error listing calendars from Google Calendar:', error);

    if (error.message.includes('User authentication required')) {
      return res.status(401).json({ 
        message: 'You must be logged in to view your calendars',
        authUrl: '/api/auth/google'
      });
    }

    if (error.message.includes('Unable to access user calendars')) {
      return res.status(403).json({ 
        message: 'Unable to access your calendars. Please ensure you have granted the necessary permissions.',
        authUrl: '/api/auth/google'
      });
    }

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error listing calendars from Google Calendar', error: error.message });
  }
};

/**
 * Get calendar details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCalendar = async (req, res) => {
  try {
    // Use getUserApiInstance to ensure we only use user tokens
    // This ensures only calendars the user has access to are shown
    const api = await getUserApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'You must be authenticated with your Google account to view calendar details',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    const { calendarId } = req.params;

    if (!calendarId) {
      return res.status(400).json({ message: 'Calendar ID is required' });
    }

    const calendar = await api.getCalendar(calendarId);
    res.json(calendar);
  } catch (error) {
    console.error('Error getting calendar from Google Calendar:', error);

    if (error.message.includes('User authentication required')) {
      return res.status(401).json({ 
        message: 'You must be logged in to view calendar details',
        authUrl: '/api/auth/google'
      });
    }

    if (error.message.includes('Unable to access user calendars')) {
      return res.status(403).json({ 
        message: 'Unable to access this calendar. Please ensure you have the necessary permissions.',
        authUrl: '/api/auth/google'
      });
    }

    // Handle specific Google API errors
    if (error.response && error.response.status === 404) {
      return res.status(404).json({ 
        message: 'Calendar not found or you do not have permission to access it'
      });
    }

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting calendar from Google Calendar', error: error.message });
  }
};

/**
 * List events for a calendar
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listEvents = async (req, res) => {
  try {
    // Use getUserApiInstance to ensure we only use user tokens
    // This ensures only events from calendars the user has access to are shown
    const api = await getUserApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'You must be authenticated with your Google account to view calendar events',
        authUrl: '/api/auth/google' // Direct them to the main Google auth endpoint
      });
    }

    const { calendarId } = req.params;
    const options = req.query;

    if (!calendarId) {
      return res.status(400).json({ message: 'Calendar ID is required' });
    }

    const events = await api.listEvents(calendarId, options);
    res.json(events);
  } catch (error) {
    console.error('Error listing events from Google Calendar:', error);

    if (error.message.includes('User authentication required')) {
      return res.status(401).json({ 
        message: 'You must be logged in to view calendar events',
        authUrl: '/api/auth/google'
      });
    }

    if (error.message.includes('Unable to access user calendars')) {
      return res.status(403).json({ 
        message: 'Unable to access events from this calendar. Please ensure you have the necessary permissions.',
        authUrl: '/api/auth/google'
      });
    }

    // Handle specific Google API errors
    if (error.response && error.response.status === 404) {
      return res.status(404).json({ 
        message: 'Calendar not found or you do not have permission to access it'
      });
    }

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error listing events from Google Calendar', error: error.message });
  }
};

/**
 * Get event details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvent = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Calendar',
        authUrl: api.getAuthUrl()
      });
    }

    const { calendarId, eventId } = req.params;

    if (!calendarId || !eventId) {
      return res.status(400).json({ message: 'Calendar ID and Event ID are required' });
    }

    const event = await api.getEvent(calendarId, eventId);
    res.json(event);
  } catch (error) {
    console.error('Error getting event from Google Calendar:', error);

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting event from Google Calendar', error: error.message });
  }
};

/**
 * Create a new event
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createEvent = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Calendar',
        authUrl: api.getAuthUrl()
      });
    }

    const { calendarId } = req.params;
    const eventData = req.body;

    if (!calendarId) {
      return res.status(400).json({ message: 'Calendar ID is required' });
    }

    if (!eventData || !eventData.summary) {
      return res.status(400).json({ message: 'Event data with at least a summary is required' });
    }

    const event = await api.createEvent(calendarId, eventData);
    res.json(event);
  } catch (error) {
    console.error('Error creating event in Google Calendar:', error);

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error creating event in Google Calendar', error: error.message });
  }
};

/**
 * Update an existing event
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateEvent = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Calendar',
        authUrl: api.getAuthUrl()
      });
    }

    const { calendarId, eventId } = req.params;
    const eventData = req.body;

    if (!calendarId || !eventId) {
      return res.status(400).json({ message: 'Calendar ID and Event ID are required' });
    }

    if (!eventData) {
      return res.status(400).json({ message: 'Event data is required' });
    }

    const event = await api.updateEvent(calendarId, eventId, eventData);
    res.json(event);
  } catch (error) {
    console.error('Error updating event in Google Calendar:', error);

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error updating event in Google Calendar', error: error.message });
  }
};

/**
 * Delete an event
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteEvent = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Calendar',
        authUrl: api.getAuthUrl()
      });
    }

    const { calendarId, eventId } = req.params;

    if (!calendarId || !eventId) {
      return res.status(400).json({ message: 'Calendar ID and Event ID are required' });
    }

    await api.deleteEvent(calendarId, eventId);
    res.json({ message: 'Event deleted successfully' });
  } catch (error) {
    console.error('Error deleting event from Google Calendar:', error);

    if (error.message === 'Google Calendar configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error deleting event from Google Calendar', error: error.message });
  }
};

/**
 * Set up Google Calendar with one click
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Google Calendar one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Google Calendar one-click setup request', 
      error: error.message 
    });
  }
};
