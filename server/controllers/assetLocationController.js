const AssetLocation = require('../../models/AssetLocation');
const Asset = require('../../models/Asset');

/**
 * Asset Location Controller
 * Manages asset location hierarchy and tracking
 */

/**
 * Get all asset locations
 */
exports.getLocations = async (req, res) => {
  try {
    const { 
      includeInactive = false, 
      locationType,
      includeAssetCount = false,
      parentLocation 
    } = req.query;

    const query = {};
    
    if (includeInactive !== 'true') {
      query.isActive = true;
    }
    
    if (locationType) {
      query.locationType = locationType;
    }

    if (parentLocation) {
      query.parentLocation = parentLocation === 'null' ? null : parentLocation;
    }
    
    let locationsQuery = AssetLocation.find(query)
      .populate('parentLocation', 'name locationType')
      .populate('manager', 'name email')
      .populate('createdBy', 'name email')
      .sort({ name: 1 });

    if (includeAssetCount === 'true') {
      locationsQuery = locationsQuery.populate('assetCount');
    }

    const locations = await locationsQuery;

    // Build hierarchical structure if no specific parent is requested
    if (!parentLocation) {
      const locationMap = new Map();
      const rootLocations = [];

      // First pass: create map of all locations
      locations.forEach(location => {
        locationMap.set(location._id.toString(), {
          ...location.toObject(),
          children: []
        });
      });

      // Second pass: build hierarchy
      locations.forEach(location => {
        const locationObj = locationMap.get(location._id.toString());
        
        if (location.parentLocation) {
          const parent = locationMap.get(location.parentLocation._id.toString());
          if (parent) {
            parent.children.push(locationObj);
          } else {
            rootLocations.push(locationObj);
          }
        } else {
          rootLocations.push(locationObj);
        }
      });

      return res.json({
        locations: rootLocations,
        totalCount: locations.length
      });
    }

    res.json({
      locations,
      totalCount: locations.length
    });
  } catch (error) {
    console.error('Error getting locations:', error);
    res.status(500).json({ message: 'Error getting locations', error: error.message });
  }
};

/**
 * Get single location by ID
 */
exports.getLocation = async (req, res) => {
  try {
    const { id } = req.params;
    
    const location = await AssetLocation.findById(id)
      .populate('parentLocation', 'name locationType description')
      .populate('children')
      .populate('assets')
      .populate('manager', 'name email')
      .populate('contacts.user', 'name email')
      .populate('room', 'name description')
      .populate('building', 'name address')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');

    if (!location) {
      return res.status(404).json({ message: 'Location not found' });
    }

    res.json(location);
  } catch (error) {
    console.error('Error getting location:', error);
    res.status(500).json({ message: 'Error getting location', error: error.message });
  }
};

/**
 * Create new location
 */
exports.createLocation = async (req, res) => {
  try {
    const locationData = {
      ...req.body,
      createdBy: req.user._id
    };

    // Validate parent location if provided
    if (locationData.parentLocation) {
      const parentLocation = await AssetLocation.findById(locationData.parentLocation);
      if (!parentLocation) {
        return res.status(400).json({ message: 'Parent location not found' });
      }
    }

    const location = new AssetLocation(locationData);
    location._modifiedBy = req.user._id;
    await location.save();

    await location.populate([
      { path: 'parentLocation', select: 'name locationType' },
      { path: 'manager', select: 'name email' },
      { path: 'createdBy', select: 'name email' }
    ]);

    res.status(201).json(location);
  } catch (error) {
    console.error('Error creating location:', error);
    
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({ 
        message: `${field} already exists`,
        field: field,
        value: error.keyValue[field]
      });
    }
    
    res.status(500).json({ message: 'Error creating location', error: error.message });
  }
};

/**
 * Update location
 */
exports.updateLocation = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    const location = await AssetLocation.findById(id);
    if (!location) {
      return res.status(404).json({ message: 'Location not found' });
    }

    // Validate parent location if being updated
    if (updates.parentLocation && updates.parentLocation !== location.parentLocation?.toString()) {
      const parentLocation = await AssetLocation.findById(updates.parentLocation);
      if (!parentLocation) {
        return res.status(400).json({ message: 'Parent location not found' });
      }

      // Check for circular reference
      const wouldCreateCircle = await checkCircularReference(id, updates.parentLocation);
      if (wouldCreateCircle) {
        return res.status(400).json({ message: 'Cannot create circular reference in location hierarchy' });
      }
    }

    Object.assign(location, updates);
    location._modifiedBy = req.user._id;
    await location.save();

    await location.populate([
      { path: 'parentLocation', select: 'name locationType' },
      { path: 'manager', select: 'name email' },
      { path: 'lastModifiedBy', select: 'name email' }
    ]);

    res.json(location);
  } catch (error) {
    console.error('Error updating location:', error);
    
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({ 
        message: `${field} already exists`,
        field: field,
        value: error.keyValue[field]
      });
    }
    
    res.status(500).json({ message: 'Error updating location', error: error.message });
  }
};

/**
 * Delete location (soft delete)
 */
exports.deleteLocation = async (req, res) => {
  try {
    const { id } = req.params;
    const { force = false } = req.query;
    
    const location = await AssetLocation.findById(id);
    if (!location) {
      return res.status(404).json({ message: 'Location not found' });
    }

    // Check if location has assets
    const assetCount = await Asset.countDocuments({ location: id, isDeleted: false });
    if (assetCount > 0 && !force) {
      return res.status(400).json({ 
        message: `Cannot delete location with ${assetCount} assets. Use force=true to proceed.`,
        assetCount 
      });
    }

    // Check if location has child locations
    const childCount = await AssetLocation.countDocuments({ parentLocation: id, isActive: true });
    if (childCount > 0 && !force) {
      return res.status(400).json({ 
        message: `Cannot delete location with ${childCount} child locations. Use force=true to proceed.`,
        childCount 
      });
    }

    if (force && assetCount > 0) {
      // Move assets to parent location or null
      await Asset.updateMany(
        { location: id },
        { 
          location: location.parentLocation || null,
          lastModifiedBy: req.user._id
        }
      );
    }

    if (force && childCount > 0) {
      // Move child locations to parent or root level
      await AssetLocation.updateMany(
        { parentLocation: id },
        { 
          parentLocation: location.parentLocation || null,
          lastModifiedBy: req.user._id
        }
      );
    }

    location.isActive = false;
    location._modifiedBy = req.user._id;
    await location.save();

    res.json({ 
      message: 'Location deactivated successfully',
      assetsRelocated: force ? assetCount : 0,
      childrenRelocated: force ? childCount : 0
    });
  } catch (error) {
    console.error('Error deleting location:', error);
    res.status(500).json({ message: 'Error deleting location', error: error.message });
  }
};

/**
 * Get location tree (hierarchical view)
 */
exports.getLocationTree = async (req, res) => {
  try {
    const { locationType } = req.query;
    
    const query = { parentLocation: null, isActive: true };
    if (locationType) {
      query.locationType = locationType;
    }

    const rootLocations = await AssetLocation.find(query)
      .populate('children')
      .populate('manager', 'name email')
      .sort({ name: 1 });
    
    // Recursively populate the tree with asset counts
    const populateTree = async (locations) => {
      for (const location of locations) {
        // Get asset count for this location and all children
        const assetCount = await getAssetCountRecursive(location._id);
        location.assetCount = assetCount;
        
        if (location.children && location.children.length > 0) {
          await populateTree(location.children);
        }
      }
    };

    await populateTree(rootLocations);
    
    res.json(rootLocations);
  } catch (error) {
    console.error('Error getting location tree:', error);
    res.status(500).json({ message: 'Error getting location tree', error: error.message });
  }
};

/**
 * Get assets by location
 */
exports.getLocationAssets = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      page = 1, 
      limit = 20, 
      includeChildren = false,
      status,
      category,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    const location = await AssetLocation.findById(id);
    if (!location) {
      return res.status(404).json({ message: 'Location not found' });
    }

    let locationIds = [id];
    
    if (includeChildren === 'true') {
      const childLocations = await location.getAllChildren();
      locationIds = locationIds.concat(childLocations.map(l => l._id));
    }

    const query = { 
      location: { $in: locationIds },
      isDeleted: false 
    };
    
    if (status) query.status = status;
    if (category) query.category = category;

    const sort = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const skip = (page - 1) * limit;
    const [assets, total] = await Promise.all([
      Asset.find(query)
        .populate('category', 'name')
        .populate('location', 'name locationType')
        .populate('assignedTo', 'name email')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit)),
      Asset.countDocuments(query)
    ]);

    res.json({
      location: {
        id: location._id,
        name: location.name,
        locationType: location.locationType,
        description: location.description
      },
      assets,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting location assets:', error);
    res.status(500).json({ message: 'Error getting location assets', error: error.message });
  }
};

/**
 * Search locations
 */
exports.searchLocations = async (req, res) => {
  try {
    const { 
      query: searchQuery, 
      locationType,
      limit = 20,
      includeAssetCount = false 
    } = req.query;

    if (!searchQuery || searchQuery.trim().length === 0) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    const query = {
      isActive: true,
      $or: [
        { name: { $regex: searchQuery, $options: 'i' } },
        { code: { $regex: searchQuery, $options: 'i' } },
        { description: { $regex: searchQuery, $options: 'i' } },
        { 'address.street': { $regex: searchQuery, $options: 'i' } },
        { 'address.city': { $regex: searchQuery, $options: 'i' } }
      ]
    };

    if (locationType) {
      query.locationType = locationType;
    }

    let searchResults = AssetLocation.find(query)
      .populate('parentLocation', 'name locationType')
      .populate('manager', 'name email')
      .limit(parseInt(limit))
      .sort({ name: 1 });

    if (includeAssetCount === 'true') {
      searchResults = searchResults.populate('assetCount');
    }

    const locations = await searchResults;

    res.json({
      query: searchQuery,
      results: locations,
      count: locations.length
    });
  } catch (error) {
    console.error('Error searching locations:', error);
    res.status(500).json({ message: 'Error searching locations', error: error.message });
  }
};

/**
 * Get location statistics
 */
exports.getLocationStats = async (req, res) => {
  try {
    const stats = await AssetLocation.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'assets',
          localField: '_id',
          foreignField: 'location',
          as: 'assets'
        }
      },
      {
        $addFields: {
          assetCount: { $size: '$assets' },
          activeAssets: {
            $size: {
              $filter: {
                input: '$assets',
                cond: { 
                  $and: [
                    { $eq: ['$$this.isDeleted', false] },
                    { $eq: ['$$this.status', 'active'] }
                  ]
                }
              }
            }
          }
        }
      },
      {
        $group: {
          _id: '$locationType',
          count: { $sum: 1 },
          totalAssets: { $sum: '$assetCount' },
          activeAssets: { $sum: '$activeAssets' },
          avgAssetsPerLocation: { $avg: '$assetCount' }
        }
      },
      { $sort: { totalAssets: -1 } }
    ]);

    const topLocations = await AssetLocation.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'assets',
          localField: '_id',
          foreignField: 'location',
          as: 'assets'
        }
      },
      {
        $addFields: {
          assetCount: { $size: '$assets' }
        }
      },
      { $sort: { assetCount: -1 } },
      { $limit: 10 },
      {
        $project: {
          name: 1,
          locationType: 1,
          description: 1,
          assetCount: 1
        }
      }
    ]);

    const totalStats = await AssetLocation.aggregate([
      { $match: { isActive: true } },
      {
        $lookup: {
          from: 'assets',
          localField: '_id',
          foreignField: 'location',
          as: 'assets'
        }
      },
      {
        $addFields: {
          assetCount: { $size: '$assets' }
        }
      },
      {
        $group: {
          _id: null,
          totalLocations: { $sum: 1 },
          totalAssets: { $sum: '$assetCount' },
          locationsWithAssets: {
            $sum: { $cond: [{ $gt: ['$assetCount', 0] }, 1, 0] }
          },
          avgAssetsPerLocation: { $avg: '$assetCount' }
        }
      }
    ]);

    res.json({
      overview: totalStats[0] || {
        totalLocations: 0,
        totalAssets: 0,
        locationsWithAssets: 0,
        avgAssetsPerLocation: 0
      },
      byLocationType: stats,
      topLocations
    });
  } catch (error) {
    console.error('Error getting location stats:', error);
    res.status(500).json({ message: 'Error getting location stats', error: error.message });
  }
};

/**
 * Helper function to check for circular references
 */
async function checkCircularReference(locationId, parentLocationId) {
  if (locationId === parentLocationId) {
    return true;
  }

  const parentLocation = await AssetLocation.findById(parentLocationId);
  if (!parentLocation || !parentLocation.parentLocation) {
    return false;
  }

  return await checkCircularReference(locationId, parentLocation.parentLocation.toString());
}

/**
 * Helper function to get asset count recursively
 */
async function getAssetCountRecursive(locationId) {
  const location = await AssetLocation.findById(locationId);
  if (!location) return 0;

  let count = await Asset.countDocuments({ 
    location: locationId, 
    isDeleted: false 
  });

  const children = await AssetLocation.find({ parentLocation: locationId, isActive: true });
  for (const child of children) {
    count += await getAssetCountRecursive(child._id);
  }

  return count;
}