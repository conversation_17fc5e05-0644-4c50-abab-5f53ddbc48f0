const GoogleDriveAPI = require('../integrations/googleDrive/googleDriveAPI');
const User = require('../../models/User');
const path = require('path');
const fs = require('fs');

// Check if service account credentials are available (primary authentication method)
const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
const usingServiceAccount = !!(serviceAccountEmail && serviceAccountPrivateKey);

// OAuth fallback variables (for backwards compatibility only)
const clientId = process.env.GOOGLE_DRIVE_CLIENT_ID || '';
const clientSecret = process.env.GOOGLE_DRIVE_CLIENT_SECRET || '';
const redirectUri = process.env.GOOGLE_DRIVE_REDIRECT_URI || '';
const tokenPath = path.resolve(process.cwd(), process.env.GOOGLE_DRIVE_TOKEN_PATH || './google-drive-token.json');

// Get admin impersonation email for service account
const adminImpersonationEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;

// Create a global API instance with service account support
let googleDriveAPI = new GoogleDriveAPI(
  clientId,
  clientSecret,
  redirectUri,
  tokenPath,
  null, // No user tokens
  null, // No user ID
  adminImpersonationEmail // Use admin email for service account impersonation
);

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (usingServiceAccount) {
    if (!serviceAccountEmail || !serviceAccountPrivateKey) {
      throw new Error('Google service account configuration is missing. Please check your environment variables.');
    }
  } else if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Google Drive configuration is missing. Please check your environment variables.');
  }
  return true;
};

// Helper function to get an API instance with user email for service account impersonation
// or fall back to user tokens if service account is not configured
const getApiWithUser = async (userId) => {
  try {
    // Ensure environment variables are set
    ensureApiInitialized();

    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (usingServiceAccount) {
      // Use service account with user impersonation
      if (!user.email) {
        throw new Error('User email is required for service account impersonation');
      }

      const api = new GoogleDriveAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        null, // No user tokens
        userId,
        user.email // Pass the user email for service account impersonation
      );

      await api.initialize();
      return api;
    } else {
      // Fall back to OAuth tokens if service account is not configured
      if (!user.googleAccessToken) {
        throw new Error('User missing Google tokens and service account not configured');
      }

      // Create a new API instance with the user's tokens
      const userTokens = {
        accessToken: user.googleAccessToken,
        refreshToken: user.googleRefreshToken
      };

      const api = new GoogleDriveAPI(
        clientId,
        clientSecret,
        redirectUri,
        tokenPath,
        userTokens,
        userId
      );

      await api.initialize();
      return api;
    }
  } catch (error) {
    console.error('Error creating API with user:', error);
    throw error;
  }
};

// Helper function to get the best available API instance
const getApiInstance = async (req) => {
  let api;

  // Try to use the user's credentials if available
  if (req.user && req.user.id) {
    // Check if user is authenticated via Google
    if (req.user.authType !== 'google') {
      throw new Error('You are not logged in using Google authentication. Google Drive integration will not work. Please log in with your Google account.');
    }
    
    try {
      api = await getApiWithUser(req.user.id);
      return api;
    } catch (userError) {
      console.log('Could not use user credentials, falling back to global config:', userError.message);
      // Fall back to global config if user credentials don't work
    }
  }

  // No user in the request or user credentials failed, use global config
  // Ensure environment variables are set
  ensureApiInitialized();

  // Initialize the API if needed
  try {
    await googleDriveAPI.initialize();
  } catch (error) {
    console.error('Error initializing Google Drive API:', error);
  }

  return googleDriveAPI;
};

/**
 * Save Google Drive configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Google Drive configuration request:', error);
    res.status(500).json({ message: 'Error handling Google Drive configuration request', error: error.message });
  }
};

/**
 * Get Google Drive configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    ensureApiInitialized();

    // Check if environment variables are being used
    const usingEnvVars = {
      serviceAccount: !!(process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL && 
                        process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY),
      clientId: !!process.env.GOOGLE_DRIVE_CLIENT_ID,
      clientSecret: !!process.env.GOOGLE_DRIVE_CLIENT_SECRET,
      redirectUri: !!process.env.GOOGLE_DRIVE_REDIRECT_URI,
      tokenPath: !!process.env.GOOGLE_DRIVE_TOKEN_PATH
    };

    // Don't send the actual client secret back to the client for security
    res.json({
      clientId: process.env.GOOGLE_DRIVE_CLIENT_ID,
      redirectUri: process.env.GOOGLE_DRIVE_REDIRECT_URI,
      tokenPath: process.env.GOOGLE_DRIVE_TOKEN_PATH,
      configuredAt: new Date(),
      isAuthenticated: googleDriveAPI ? googleDriveAPI.isAuthenticated() : false,
      usingEnvVars
    });
  } catch (error) {
    console.error('Error fetching Google Drive configuration:', error);
    res.status(500).json({ message: 'Error fetching Google Drive configuration', error: error.message });
  }
};

/**
 * Get authentication URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAuthUrl = async (req, res) => {
  try {
    ensureApiInitialized();

    const authUrl = googleDriveAPI.getAuthUrl();
    res.json({ authUrl });
  } catch (error) {
    console.error('Error generating Google Drive auth URL:', error);
    res.status(500).json({ message: 'Error generating Google Drive auth URL', error: error.message });
  }
};

/**
 * Handle OAuth2 callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code } = req.query;

    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }

    ensureApiInitialized();

    // Get tokens from Google
    const tokens = await googleDriveAPI.getToken(code);

    // If user is authenticated, store tokens in their record for future refresh
    if (req.user && req.user.id) {
      const user = await User.findById(req.user.id);
      if (user) {
        user.googleAccessToken = tokens.access_token;
        if (tokens.refresh_token) {
          user.googleRefreshToken = tokens.refresh_token;
        }
        await user.save();
        console.log(`Stored Google Drive tokens for user ${req.user.id}`);
      }
    }

    res.json({ message: 'Google Drive authentication successful' });
  } catch (error) {
    console.error('Error handling Google Drive callback:', error);
    res.status(500).json({ message: 'Error handling Google Drive callback', error: error.message });
  }
};

/**
 * List files in Google Drive
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.listFiles = async (req, res) => {
  try {
    console.log('Google Drive Controller: Handling listFiles request');
    
    // Check if we have a user in the request
    if (!req.user) {
      console.warn('Google Drive Controller: No user in request when listing files');
    } else {
      console.log(`Google Drive Controller: Request from user ${req.user.email || req.user.id || 'unknown'}`);
    }
    
    try {
      const api = await getApiInstance(req);
      
      if (!api.isAuthenticated()) {
        console.warn('Google Drive Controller: API not authenticated');
        return res.status(401).json({ 
          message: 'Not authenticated with Google Drive',
          authUrl: api.getAuthUrl(),
          debug: 'The application is not properly authenticated with Google Drive. This could be due to missing or invalid service account credentials.'
        });
      }

      // Get user email from the authenticated user
      const userEmail = req.user ? req.user.email : null;
      
      // If no user email is available, log a warning
      if (!userEmail) {
        console.warn('Google Drive Controller: No user email available for filtering Google Drive files');
      } else {
        console.log(`Google Drive Controller: Filtering files for user ${userEmail}`);
      }

      const options = req.query;
      console.log('Google Drive Controller: Calling API listFiles with options:', JSON.stringify(options));
      
      const files = await api.listFiles(options, userEmail);
      console.log(`Google Drive Controller: Successfully retrieved ${files.length} files`);
      
      res.json(files);
    } catch (apiError) {
      console.error('Google Drive Controller: Error from API when listing files:', apiError);
      
      // Check for specific error types and provide helpful responses
      if (apiError.message.includes('No user email provided')) {
        return res.status(400).json({ 
          message: 'Failed to load files from Google Drive', 
          error: 'User email is required for service account authentication',
          debug: 'The application needs a valid user email to impersonate with the service account. Make sure you are properly logged in.'
        });
      }
      
      if (apiError.message.includes('Service account credentials are missing') || 
          apiError.message.includes('Service account email is missing') ||
          apiError.message.includes('Service account private key is missing')) {
        return res.status(500).json({ 
          message: 'Failed to load files from Google Drive', 
          error: 'Service account credentials are missing or invalid',
          debug: 'Check that GOOGLE_SERVICE_ACCOUNT_EMAIL and GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY environment variables are properly set.'
        });
      }
      
      if (apiError.message.includes('invalid_grant') || apiError.message.includes('Invalid Credentials')) {
        return res.status(401).json({ 
          message: 'Failed to load files from Google Drive', 
          error: 'Invalid credentials when accessing Google Drive',
          debug: 'The service account credentials may be invalid or the impersonated user may not have access to Google Drive.'
        });
      }
      
      // For other errors, provide a generic but informative response
      throw apiError;
    }
  } catch (error) {
    console.error('Google Drive Controller: Unhandled error in listFiles:', error);
    
    // Log detailed error information for debugging
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      code: error.code,
      status: error.status,
      context: error.context || 'No context available'
    };
    
    console.error('Google Drive Controller: Error details:', JSON.stringify(errorDetails, null, 2));

    if (error.message === 'Google Drive configuration not found') {
      return res.status(404).json({ 
        message: error.message,
        debug: 'The Google Drive integration is not properly configured. Check the environment variables.'
      });
    }

    // Provide a more informative error response to the client
    res.status(500).json({ 
      message: 'Failed to load files from Google Drive', 
      error: error.message,
      debug: 'Check the server logs for more detailed error information. The issue might be related to service account authentication or API permissions.'
    });
  }
};

/**
 * Search files in Google Drive
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchFiles = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Drive',
        authUrl: api.getAuthUrl()
      });
    }

    const { query, ...options } = req.query;

    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }

    // Get user email from the authenticated user
    const userEmail = req.user ? req.user.email : null;
    
    // If no user email is available, log a warning
    if (!userEmail) {
      console.warn('No user email available for filtering Google Drive search results');
    }

    const files = await api.searchFiles(query, options, userEmail);
    res.json(files);
  } catch (error) {
    console.error('Error searching files in Google Drive:', error);

    if (error.message === 'Google Drive configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error searching files in Google Drive', error: error.message });
  }
};

/**
 * Get file metadata
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFile = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Drive',
        authUrl: api.getAuthUrl()
      });
    }

    const { fileId } = req.params;

    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    const file = await api.getFile(fileId);
    res.json(file);
  } catch (error) {
    console.error('Error getting file from Google Drive:', error);

    if (error.message === 'Google Drive configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting file from Google Drive', error: error.message });
  }
};

/**
 * Get embedded viewer URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEmbeddedViewerUrl = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    const { fileId } = req.params;

    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    const viewerUrl = api.getEmbeddedViewerUrl(fileId);
    res.json({ viewerUrl });
  } catch (error) {
    console.error('Error getting embedded viewer URL:', error);

    if (error.message === 'Google Drive configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting embedded viewer URL', error: error.message });
  }
};

/**
 * Get embedded editor URL
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEmbeddedEditorUrl = async (req, res) => {
  try {
    const api = await getApiInstance(req);

    if (!api.isAuthenticated()) {
      return res.status(401).json({ 
        message: 'Not authenticated with Google Drive',
        authUrl: api.getAuthUrl()
      });
    }

    const { fileId } = req.params;

    if (!fileId) {
      return res.status(400).json({ message: 'File ID is required' });
    }

    // Get file metadata to determine the MIME type
    const file = await api.getFile(fileId);
    const editorUrl = api.getEmbeddedEditorUrl(fileId, file.mimeType);

    res.json({ editorUrl, file });
  } catch (error) {
    console.error('Error getting embedded editor URL:', error);

    if (error.message === 'Google Drive configuration not found') {
      return res.status(404).json({ message: error.message });
    }

    res.status(500).json({ message: 'Error getting embedded editor URL', error: error.message });
  }
};
