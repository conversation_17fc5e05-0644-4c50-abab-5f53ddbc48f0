const Notification = require('../../models/Notification');
const User = require('../../models/User');

const notificationController = {
  // GET /api/notifications
  getUserNotifications: async (req, res) => {
    try {
      // Respect user system notification preference (default to enabled)
      const user = await User.findById(req.user.id).select('notificationPreferences');
      if (user?.notificationPreferences && user.notificationPreferences.systemEnabled === false) {
        return res.json([]);
      }

      const { onlyUnread, limit } = req.query;
      const query = { userId: req.user.id };
      if (onlyUnread === 'true') query.read = false;
      const lim = Math.min(parseInt(limit, 10) || 10, 100);
      const notifications = await Notification.find(query)
        .sort({ createdAt: -1 })
        .limit(lim);
      res.json(notifications);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      res.status(500).json({ msg: 'Server error' });
    }
  },

  // GET /api/notifications/unread-count
  getUnreadCount: async (req, res) => {
    try {
      const count = await Notification.countDocuments({ userId: req.user.id, read: false });
      res.json({ count });
    } catch (err) {
      console.error('Error counting unread notifications:', err);
      res.status(500).json({ msg: 'Server error' });
    }
  },

  // PATCH /api/notifications/:id/read
  markAsRead: async (req, res) => {
    try {
      const { id } = req.params;
      const updated = await Notification.findOneAndUpdate(
        { _id: id, userId: req.user.id },
        { $set: { read: true } },
        { new: true }
      );
      if (!updated) return res.status(404).json({ msg: 'Notification not found' });
      res.json(updated);
    } catch (err) {
      console.error('Error marking notification as read:', err);
      res.status(500).json({ msg: 'Server error' });
    }
  },

  // PATCH /api/notifications/mark-all-read
  markAllAsRead: async (req, res) => {
    try {
      const result = await Notification.updateMany(
        { userId: req.user.id, read: false },
        { $set: { read: true } }
      );
      res.json({ matched: result.matchedCount ?? result.n, modified: result.modifiedCount ?? result.nModified });
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      res.status(500).json({ msg: 'Server error' });
    }
  },

  // Optional: POST /api/notifications (admin-only) to create a notification for testing
  createNotification: async (req, res) => {
    try {
      const { userId, title, message, type, linkUrl, metadata } = req.body;
      const targetUserId = userId || req.user.id;
      const notif = new Notification({ userId: targetUserId, title, message, type, linkUrl, metadata });
      const saved = await notif.save();
      res.json(saved);
    } catch (err) {
      console.error('Error creating notification:', err);
      res.status(500).json({ msg: 'Server error' });
    }
  }
};

module.exports = notificationController;
