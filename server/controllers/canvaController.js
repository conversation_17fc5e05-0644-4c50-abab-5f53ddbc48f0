const CanvaAPI = require('../integrations/canva/canvaAPI');
const CanvaConfig = require('../../models/CanvaConfig');
const User = require('../../models/User');

// Environment variables for Canva OAuth
const clientId = process.env.CANVA_CLIENT_ID || '';
const clientSecret = process.env.CANVA_CLIENT_SECRET || '';
const redirectUri = process.env.CANVA_REDIRECT_URI || '';
const domain = process.env.CANVA_DOMAIN || 'canva.com';

// Initialize Canva API with OAuth credentials (no API key as it's no longer supported)
let canvaAPI = new CanvaAPI(domain, '', clientId, clientSecret, redirectUri);

// Helper function to get the latest configuration
const getLatestConfig = async () => {
  try {
    // Check if OAuth credentials are configured
    if (!clientId || !clientSecret || !redirectUri) {
      console.warn('Canva OAuth credentials not properly configured. Please set the required environment variables.');
      return null;
    }
    
    // Return OAuth configuration
    return {
      domain,
      clientId,
      clientSecret,
      redirectUri,
      updatedAt: new Date(),
      fromEnv: true,
      oauthOnly: true
    };
  } catch (error) {
    console.error('Error fetching Canva configuration:', error);
    throw error;
  }
};

// Helper function to get an API instance with user tokens
const getApiWithUser = async (userId) => {
  try {
    // Get the user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Check if user has Canva tokens
    if (user.canvaAccessToken) {
      // Create a new API instance with the user's tokens
      const userTokens = {
        accessToken: user.canvaAccessToken,
        refreshToken: user.canvaRefreshToken,
        expiry: user.canvaTokenExpiry
      };

      const api = new CanvaAPI(
        domain,
        '',  // No API key as it's no longer supported
        clientId,
        clientSecret,
        redirectUri,
        userTokens,
        userId
      );

      await api.initialize();
      return api;
    } else {
      // User doesn't have OAuth tokens
      throw new Error('User is not authenticated with Canva. OAuth authentication is required.');
    }
  } catch (error) {
    console.error('Error creating API with user:', error);
    throw error;
  }
};

// Helper function to get the API instance with user OAuth tokens
const getApiInstance = async (req) => {
  // User must be authenticated and have OAuth tokens
  if (req.user && req.user.id) {
    try {
      return await getApiWithUser(req.user.id);
    } catch (error) {
      console.error(`Error getting user API instance: ${error.message}`);
      throw new Error('OAuth authentication is required for Canva. Please authenticate using the OAuth flow.');
    }
  }

  // No user in request
  throw new Error('User authentication is required for Canva integration.');
};

/**
 * Get authentication URL for OAuth2 flow with PKCE
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAuthUrl = async (req, res) => {
  try {
    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Canva integration'
      });
    }
    
    // Check if OAuth credentials are configured
    if (!clientId || !clientSecret || !redirectUri) {
      return res.status(500).json({ 
        message: 'Canva OAuth is not properly configured. Please contact an administrator.'
      });
    }
    
    // Get the auth URL with PKCE
    const api = new CanvaAPI(domain, '', clientId, clientSecret, redirectUri);
    const { authUrl, codeVerifier } = api.getAuthUrl();
    
    // Store the code verifier for this user
    const codeVerifierStore = require('../utils/codeVerifierStore');
    codeVerifierStore.storeCodeVerifier(req.user.id, codeVerifier);
    
    return res.json({ 
      message: 'Please authorize with Canva to use the integration',
      authUrl: authUrl
    });
  } catch (error) {
    console.error('Error generating Canva auth URL:', error);
    res.status(500).json({ message: 'Error generating Canva auth URL', error: error.message });
  }
};

/**
 * Handle OAuth2 callback with PKCE
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleCallback = async (req, res) => {
  try {
    const { code } = req.query;
    
    if (!code) {
      return res.status(400).json({ message: 'Authorization code is required' });
    }
    
    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Canva integration'
      });
    }
    
    // Check if OAuth credentials are configured
    if (!clientId || !clientSecret || !redirectUri) {
      return res.status(500).json({ 
        message: 'Canva OAuth is not properly configured. Please contact an administrator.'
      });
    }
    
    // Retrieve the code verifier for this user
    const codeVerifierStore = require('../utils/codeVerifierStore');
    const codeVerifier = codeVerifierStore.getAndRemoveCodeVerifier(req.user.id);
    
    if (!codeVerifier) {
      return res.status(400).json({ 
        message: 'Code verifier not found or expired. Please try authenticating again.'
      });
    }
    
    // Exchange code for tokens with the code verifier
    const api = new CanvaAPI(domain, '', clientId, clientSecret, redirectUri);
    const tokens = await api.exchangeCode(code, codeVerifier);
    
    // Save tokens to user
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    user.canvaAccessToken = tokens.accessToken;
    user.canvaRefreshToken = tokens.refreshToken;
    user.canvaTokenExpiry = tokens.expiry;
    
    // Add Canva to user's activated integrations if not already there
    if (!user.activatedIntegrations.includes('Canva')) {
      user.activatedIntegrations.push('Canva');
    }
    
    await user.save();
    
    // Redirect to a success page or the Canva dashboard
    res.redirect('/dashboard/canva');
  } catch (error) {
    console.error('Error handling Canva callback:', error);
    res.status(500).json({ message: 'Error handling Canva callback', error: error.message });
  }
};

/**
 * Get designs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesigns = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const designs = await api.getDesigns(req.query);
    res.json(designs);
  } catch (error) {
    console.error('Controller error fetching Canva designs:', error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: 'Error fetching Canva designs', error: error.message });
  }
};

/**
 * Get design details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesign = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const design = await api.getDesign(req.params.id, req.query);
    res.json(design);
  } catch (error) {
    console.error(`Controller error fetching Canva design ${req.params.id}:`, error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: `Error fetching Canva design ${req.params.id}`, error: error.message });
  }
};

/**
 * Get templates
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getTemplates = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const templates = await api.getTemplates(req.query);
    res.json(templates);
  } catch (error) {
    console.error('Controller error fetching Canva templates:', error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: 'Error fetching Canva templates', error: error.message });
  }
};

/**
 * Get design assets
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesignAssets = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const assets = await api.getDesignAssets(req.params.id, req.query);
    res.json(assets);
  } catch (error) {
    console.error(`Controller error fetching Canva design ${req.params.id} assets:`, error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: `Error fetching Canva design ${req.params.id} assets`, error: error.message });
  }
};

/**
 * Get design files
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDesignFiles = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const files = await api.getDesignFiles(req.params.id, req.query);
    res.json(files);
  } catch (error) {
    console.error(`Controller error fetching Canva design ${req.params.id} files:`, error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: `Error fetching Canva design ${req.params.id} files`, error: error.message });
  }
};

/**
 * Get file details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getFile = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const file = await api.getFile(req.params.id, req.query);
    res.json(file);
  } catch (error) {
    console.error(`Controller error fetching Canva file ${req.params.id}:`, error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: `Error fetching Canva file ${req.params.id}`, error: error.message });
  }
};

/**
 * Get users
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUsers = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const users = await api.getUsers(req.query);
    res.json(users);
  } catch (error) {
    console.error('Controller error fetching Canva users:', error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: 'Error fetching Canva users', error: error.message });
  }
};

/**
 * Get user details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUser = async (req, res) => {
  try {
    const api = await getApiInstance(req);
    const user = await api.getUser(req.params.id, req.query);
    res.json(user);
  } catch (error) {
    console.error(`Controller error fetching Canva user ${req.params.id}:`, error);
    
    // Check if this is an authentication error
    if (error.message.includes('authentication') || error.response?.status === 401) {
      return res.status(401).json({ 
        message: 'Authentication required for Canva',
        authUrl: '/api/canva/auth'
      });
    }
    
    res.status(500).json({ message: `Error fetching Canva user ${req.params.id}`, error: error.message });
  }
};

/**
 * Save Canva configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'Global configuration is managed through environment variables by administrators. User-specific configuration is handled through OAuth authentication.',
      authUrl: '/api/canva/auth'
    });
  } catch (error) {
    console.error('Error handling Canva configuration request:', error);
    res.status(500).json({ message: 'Error handling Canva configuration request', error: error.message });
  }
};

/**
 * Get Canva configuration status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Check if OAuth is configured
    const oauthConfigured = !!(clientId && clientSecret && redirectUri);
    
    // Check for user-specific OAuth tokens
    if (req.user && req.user.id) {
      const user = await User.findById(req.user.id);
      if (user && user.canvaAccessToken) {
        // User has Canva OAuth tokens
        return res.json({
          domain,
          configuredAt: user.canvaTokenExpiry ? new Date(user.canvaTokenExpiry) : new Date(),
          userAuth: true,
          oauthConfigured,
          oauthOnly: true
        });
      }
    }
    
    // No user OAuth tokens, check if OAuth is configured
    if (oauthConfigured) {
      return res.json({
        domain,
        configuredAt: new Date(),
        fromEnv: true,
        userAuth: false,
        oauthConfigured: true,
        oauthOnly: true,
        message: 'OAuth authentication is required for Canva. Please authenticate using the OAuth flow.',
        authUrl: '/api/canva/auth'
      });
    }
    
    // OAuth is not configured
    return res.status(404).json({ 
      message: 'Canva OAuth configuration not found. Please set the required environment variables for OAuth.',
      oauthConfigured: false,
      oauthOnly: true
    });
  } catch (error) {
    console.error('Error fetching Canva configuration:', error);
    res.status(500).json({ message: 'Error fetching Canva configuration', error: error.message });
  }
};

/**
 * Check if user is authenticated with Canva
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkAuth = async (req, res) => {
  try {
    // Check if user is logged in
    if (!req.user || !req.user.id) {
      return res.status(401).json({ 
        message: 'You must be logged in to use Canva integration',
        authenticated: false
      });
    }
    
    // Check if user has Canva tokens
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ 
        message: 'User not found',
        authenticated: false
      });
    }
    
    if (user.canvaAccessToken) {
      // Check if token is expired
      if (user.canvaTokenExpiry && new Date(user.canvaTokenExpiry) < new Date()) {
        // Token is expired, but we have a refresh token
        if (user.canvaRefreshToken) {
          try {
            // Try to refresh the token
            const api = new CanvaAPI(
              domain,
              '',
              clientId,
              clientSecret,
              redirectUri,
              {
                accessToken: user.canvaAccessToken,
                refreshToken: user.canvaRefreshToken,
                expiry: user.canvaTokenExpiry
              },
              user.id
            );
            
            const tokens = await api.refreshToken();
            
            // Update user tokens
            user.canvaAccessToken = tokens.accessToken;
            user.canvaRefreshToken = tokens.refreshToken;
            user.canvaTokenExpiry = tokens.expiry;
            await user.save();
            
            return res.json({
              authenticated: true,
              message: 'User is authenticated with Canva (token refreshed)'
            });
          } catch (refreshError) {
            console.error('Error refreshing token:', refreshError);
            return res.status(401).json({
              authenticated: false,
              message: 'Authentication expired and could not be refreshed',
              authUrl: '/api/canva/auth'
            });
          }
        } else {
          return res.status(401).json({
            authenticated: false,
            message: 'Authentication expired',
            authUrl: '/api/canva/auth'
          });
        }
      }
      
      // Token is valid
      return res.json({
        authenticated: true,
        message: 'User is authenticated with Canva'
      });
    }
    
    // User is not authenticated with Canva
    return res.status(401).json({
      authenticated: false,
      message: 'User is not authenticated with Canva',
      authUrl: '/api/canva/auth'
    });
  } catch (error) {
    console.error('Error checking Canva authentication:', error);
    res.status(500).json({ 
      message: 'Error checking Canva authentication', 
      error: error.message,
      authenticated: false
    });
  }
};