const integrationTracker = require('../utils/integrationTracker');

/**
 * Get the status of all integrations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllIntegrationStatuses = async (req, res) => {
  try {
    const statuses = integrationTracker?.getAllStatuses();
    res.json(statuses);
  } catch (error) {
    console.error('Error fetching integration statuses:', error);
    res.status(500).json({ message: 'Error fetching integration statuses', error: error.message });
  }
};

/**
 * Get the status of a specific integration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getIntegrationStatus = async (req, res) => {
  try {
    const { integration } = req.params;

    if (!integration) {
      return res.status(400).json({ message: 'Integration name is required' });
    }

    const status = integrationTracker?.getStatus(integration);

    if (!status) {
      return res.status(404).json({ message: `Integration '${integration}' not found` });
    }

    res.json(status);
  } catch (error) {
    console.error('Error fetching integration status:', error);
    res.status(500).json({ message: 'Error fetching integration status', error: error.message });
  }
};

/**
 * Force a refresh of the integration status file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.refreshStatusFile = async (req, res) => {
  try {
    // This will force the tracker to re-read the status file
    integrationTracker?.initialize();
    res.json({ message: 'Integration status file refreshed successfully' });
  } catch (error) {
    console.error('Error refreshing integration status file:', error);
    res.status(500).json({ message: 'Error refreshing integration status file', error: error.message });
  }
};

/**
 * Get the raw content of the integration status markdown file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getStatusMarkdown = async (req, res) => {
  try {
    const fs = require('fs');
    const path = require('path');

    const filePath = path.resolve(process.cwd(), 'integration-status.md');

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'Integration status file not found' });
    }

    const content = fs.readFileSync(filePath, 'utf8');
    res.send(content);
  } catch (error) {
    console.error('Error fetching integration status markdown:', error);
    res.status(500).json({ message: 'Error fetching integration status markdown', error: error.message });
  }
};
