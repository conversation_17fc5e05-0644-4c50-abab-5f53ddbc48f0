const LenelS2NetBoxAPI = require('../integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Environment variables for Lenel S2 NetBox API
const host = process.env.LENEL_S2_NETBOX_HOST || '';
const username = process.env.LENEL_S2_NETBOX_USERNAME || '';
const password = process.env.LENEL_S2_NETBOX_PASSWORD || '';
const port = process.env.LENEL_S2_NETBOX_PORT || 443;
const localNetwork = process.env.LENEL_S2_NETBOX_LOCAL_NETWORK === 'true';

// Use lazy initialization for the API
let lenelS2NetBoxAPI = null;

// Helper function to ensure API is initialized
const ensureApiInitialized = () => {
  if (!host || !username || !password) {
    throw new Error('Lenel S2 NetBox configuration is missing. Please check your environment variables.');
  }
  
  // Initialize the API if it hasn't been initialized yet
  if (!lenelS2NetBoxAPI) {
    console.log('Initializing Lenel S2 NetBox API on demand');
    lenelS2NetBoxAPI = new LenelS2NetBoxAPI(host, username, password, port);
  }
  
  return true;
};

/**
 * Get all Lenel S2 NetBox portals
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPortals = async (req, res) => {
  try {
    ensureApiInitialized();
    const portals = await lenelS2NetBoxAPI.getPortals();
    res.json(portals);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox portals:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox portals', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox portal details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPortalDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const portalId = req.params.id;
    const portalDetails = await lenelS2NetBoxAPI.getPortalDetails(portalId);
    res.json(portalDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox portal details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox portal details', error: error.message });
  }
};

/**
 * Get all Lenel S2 NetBox cardholders
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCardholders = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholders = await lenelS2NetBoxAPI.getCardholders();
    res.json(cardholders);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox cardholders:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox cardholders', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox cardholder details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCardholderDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const cardholderDetails = await lenelS2NetBoxAPI.getCardholderDetails(cardholderId);
    res.json(cardholderDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox cardholder details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox cardholder details', error: error.message });
  }
};

/**
 * Get all Lenel S2 NetBox alarms
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAlarms = async (req, res) => {
  try {
    ensureApiInitialized();
    const alarms = await lenelS2NetBoxAPI.getAlarms(req.query);
    res.json(alarms);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox alarms:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox alarms', error: error.message });
  }
};

/**
 * Acknowledge a Lenel S2 NetBox alarm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.acknowledgeAlarm = async (req, res) => {
  try {
    ensureApiInitialized();
    const alarmId = req.params.id;
    const result = await lenelS2NetBoxAPI.acknowledgeAlarm(alarmId);
    res.json(result);
  } catch (error) {
    console.error('Controller error acknowledging Lenel S2 NetBox alarm:', error);
    res.status(500).json({ message: 'Error acknowledging Lenel S2 NetBox alarm', error: error.message });
  }
};

/**
 * Get all Lenel S2 NetBox events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvents = async (req, res) => {
  try {
    ensureApiInitialized();
    const events = await lenelS2NetBoxAPI.getEvents(req.query);
    res.json(events);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox events:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox events', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getSystemStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const status = await lenelS2NetBoxAPI.getSystemStatus();
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox system status:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox system status', error: error.message });
  }
};

/**
 * Unlock a Lenel S2 NetBox door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.unlockDoor = async (req, res) => {
  try {
    ensureApiInitialized();
    const doorId = req.params.id;
    const result = await lenelS2NetBoxAPI.unlockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error unlocking Lenel S2 NetBox door:', error);
    res.status(500).json({ message: 'Error unlocking Lenel S2 NetBox door', error: error.message });
  }
};

/**
 * Lock a Lenel S2 NetBox door
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.lockDoor = async (req, res) => {
  try {
    ensureApiInitialized();
    const doorId = req.params.id;
    const result = await lenelS2NetBoxAPI.lockDoor(doorId);
    res.json(result);
  } catch (error) {
    console.error('Controller error locking Lenel S2 NetBox door:', error);
    res.status(500).json({ message: 'Error locking Lenel S2 NetBox door', error: error.message });
  }
};

/**
 * Save Lenel S2 NetBox configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveConfig = async (req, res) => {
  try {
    res.status(403).json({ message: 'Configuration is now managed through environment variables by administrators' });
  } catch (error) {
    console.error('Error handling Lenel S2 NetBox configuration request:', error);
    res.status(500).json({ message: 'Error handling Lenel S2 NetBox configuration request', error: error.message });
  }
};

/**
 * Get Lenel S2 NetBox configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    ensureApiInitialized();

    // Don't send the actual password back to the client for security
    res.json({
      host: process.env.LENEL_S2_NETBOX_HOST,
      username: process.env.LENEL_S2_NETBOX_USERNAME,
      port: process.env.LENEL_S2_NETBOX_PORT || 443,
      localNetwork: process.env.LENEL_S2_NETBOX_LOCAL_NETWORK === 'true',
      configuredAt: new Date()
    });
  } catch (error) {
    console.error('Error fetching Lenel S2 NetBox configuration:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox configuration', error: error.message });
  }
};

/**
 * One-click setup for Lenel S2 NetBox
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.oneClickSetup = async (req, res) => {
  try {
    res.status(403).json({ 
      message: 'One-click setup is no longer available. Configuration is now managed through environment variables by administrators.' 
    });
  } catch (error) {
    console.error('Error handling Lenel S2 NetBox one-click setup request:', error);
    res.status(500).json({ 
      message: 'Error handling Lenel S2 NetBox one-click setup request', 
      error: error.message 
    });
  }
};

/**
 * Control a portal
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlPortal = async (req, res) => {
  try {
    ensureApiInitialized();
    const portalId = req.params.id;
    const command = req.body;

    if (!command || !command.action) {
      return res.status(400).json({ message: 'Command action is required' });
    }

    // Make a custom API call based on the command
    // This is a generic implementation since there's no specific API method
    // for controlling portals in the lenelS2NetBoxAPI
    const response = await lenelS2NetBoxAPI.axios.post(
      `/access-points/${portalId}/control`, // Keep the endpoint the same for now
      command
    );

    res.json(response.data || { message: `Portal ${portalId} control command sent successfully` });
  } catch (error) {
    console.error(`Controller error controlling Lenel S2 NetBox portal ${req.params.id}:`, error);
    res.status(500).json({ 
      message: 'Error controlling Lenel S2 NetBox portal', 
      error: error.message 
    });
  }
};

/**
 * Get all access levels
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessLevels = async (req, res) => {
  try {
    ensureApiInitialized();
    const accessLevels = await lenelS2NetBoxAPI.getAccessLevels();
    res.json(accessLevels);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox access levels:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox access levels', error: error.message });
  }
};

/**
 * Get access level details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessLevelDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const accessLevelId = req.params.id;
    const accessLevelDetails = await lenelS2NetBoxAPI.getAccessLevelDetails(accessLevelId);
    res.json(accessLevelDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox access level details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox access level details', error: error.message });
  }
};

/**
 * Get all access groups
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessGroups = async (req, res) => {
  try {
    ensureApiInitialized();
    const accessGroups = await lenelS2NetBoxAPI.getAccessGroups();
    res.json(accessGroups);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox access groups:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox access groups', error: error.message });
  }
};

/**
 * Get access group details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAccessGroupDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const accessGroupId = req.params.id;
    const accessGroupDetails = await lenelS2NetBoxAPI.getAccessGroupDetails(accessGroupId);
    res.json(accessGroupDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox access group details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox access group details', error: error.message });
  }
};

/**
 * Get all badges
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getBadges = async (req, res) => {
  try {
    ensureApiInitialized();
    const badges = await lenelS2NetBoxAPI.getBadges();
    res.json(badges);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox badges:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox badges', error: error.message });
  }
};

/**
 * Get badge details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getBadgeDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const badgeId = req.params.id;
    const badgeDetails = await lenelS2NetBoxAPI.getBadgeDetails(badgeId);
    res.json(badgeDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox badge details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox badge details', error: error.message });
  }
};

/**
 * Get all door schedules
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoorSchedules = async (req, res) => {
  try {
    ensureApiInitialized();
    const doorSchedules = await lenelS2NetBoxAPI.getDoorSchedules();
    res.json(doorSchedules);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox door schedules:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox door schedules', error: error.message });
  }
};

/**
 * Get door schedule details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoorScheduleDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const scheduleId = req.params.id;
    const doorScheduleDetails = await lenelS2NetBoxAPI.getDoorScheduleDetails(scheduleId);
    res.json(doorScheduleDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox door schedule details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox door schedule details', error: error.message });
  }
};

/**
 * Get all doors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoors = async (req, res) => {
  try {
    ensureApiInitialized();
    const doors = await lenelS2NetBoxAPI.getDoors();
    res.json(doors);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox doors:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox doors', error: error.message });
  }
};

/**
 * Get door status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDoorStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const doorId = req.params.id;
    const doorStatus = await lenelS2NetBoxAPI.getDoorStatus(doorId);
    res.json(doorStatus);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox door status:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox door status', error: error.message });
  }
};

/**
 * Assign access level to cardholder
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.assignAccessLevelToCardholder = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.cardholderId;
    const accessLevelId = req.params.accessLevelId;
    const result = await lenelS2NetBoxAPI.assignAccessLevelToCardholder(cardholderId, accessLevelId);
    res.json(result);
  } catch (error) {
    console.error('Controller error assigning Lenel S2 NetBox access level to cardholder:', error);
    res.status(500).json({ message: 'Error assigning Lenel S2 NetBox access level to cardholder', error: error.message });
  }
};

/**
 * Remove access level from cardholder
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.removeAccessLevelFromCardholder = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.cardholderId;
    const accessLevelId = req.params.accessLevelId;
    const result = await lenelS2NetBoxAPI.removeAccessLevelFromCardholder(cardholderId, accessLevelId);
    res.json(result);
  } catch (error) {
    console.error('Controller error removing Lenel S2 NetBox access level from cardholder:', error);
    res.status(500).json({ message: 'Error removing Lenel S2 NetBox access level from cardholder', error: error.message });
  }
};

/**
 * Create a new user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createUser = async (req, res) => {
  try {
    ensureApiInitialized();
    const userData = req.body;
    const result = await lenelS2NetBoxAPI.createUser(userData);
    res.status(201).json(result);
  } catch (error) {
    console.error('Controller error creating Lenel S2 NetBox user:', error);
    res.status(500).json({ message: 'Error creating Lenel S2 NetBox user', error: error.message });
  }
};

/**
 * Update an existing user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateUser = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const userData = req.body;
    const result = await lenelS2NetBoxAPI.updateUser(cardholderId, userData);
    res.json(result);
  } catch (error) {
    console.error('Controller error updating Lenel S2 NetBox user:', error);
    res.status(500).json({ message: 'Error updating Lenel S2 NetBox user', error: error.message });
  }
};

/**
 * Delete a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteUser = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const result = await lenelS2NetBoxAPI.deleteUser(cardholderId);
    res.json(result);
  } catch (error) {
    console.error('Controller error deleting Lenel S2 NetBox user:', error);
    res.status(500).json({ message: 'Error deleting Lenel S2 NetBox user', error: error.message });
  }
};

/**
 * Create a new user credential
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createUserCredential = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const credentialData = req.body;
    const result = await lenelS2NetBoxAPI.createUserCredential(cardholderId, credentialData);
    res.status(201).json(result);
  } catch (error) {
    console.error('Controller error creating Lenel S2 NetBox user credential:', error);
    res.status(500).json({ message: 'Error creating Lenel S2 NetBox user credential', error: error.message });
  }
};

/**
 * Update a user credential
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateUserCredential = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const credentialId = req.params.credentialId;
    const credentialData = req.body;
    const result = await lenelS2NetBoxAPI.updateUserCredential(cardholderId, credentialId, credentialData);
    res.json(result);
  } catch (error) {
    console.error('Controller error updating Lenel S2 NetBox user credential:', error);
    res.status(500).json({ message: 'Error updating Lenel S2 NetBox user credential', error: error.message });
  }
};

/**
 * Delete a user credential
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteUserCredential = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const credentialId = req.params.credentialId;
    const result = await lenelS2NetBoxAPI.deleteUserCredential(cardholderId, credentialId);
    res.json(result);
  } catch (error) {
    console.error('Controller error deleting Lenel S2 NetBox user credential:', error);
    res.status(500).json({ message: 'Error deleting Lenel S2 NetBox user credential', error: error.message });
  }
};

/**
 * Get user credentials
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserCredentials = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const credentials = await lenelS2NetBoxAPI.getUserCredentials(cardholderId);
    res.json(credentials);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox user credentials:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox user credentials', error: error.message });
  }
};

/**
 * Get user activity logs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUserLogs = async (req, res) => {
  try {
    ensureApiInitialized();
    const cardholderId = req.params.id;
    const logs = await lenelS2NetBoxAPI.getUserLogs(cardholderId, req.query);
    res.json(logs);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox user logs:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox user logs', error: error.message });
  }
};

// ===================================================================
// LIVE ACTIVITY LOG AND CARD MANAGEMENT CONTROLLERS
// ===================================================================

/**
 * Get live activity log
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getLiveActivityLog = async (req, res) => {
  try {
    ensureApiInitialized();
    const activityLog = await lenelS2NetBoxAPI.getLiveActivityLog(req.query);
    res.json(activityLog);
  } catch (error) {
    console.error('Controller error fetching live activity log:', error);
    res.status(500).json({ message: 'Error fetching live activity log', error: error.message });
  }
};

/**
 * Mark a card as lost
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.markCardAsLost = async (req, res) => {
  try {
    ensureApiInitialized();
    const credentialId = req.params.credentialId;
    const { reason } = req.body;
    const result = await lenelS2NetBoxAPI.markCardAsLost(credentialId, reason);
    res.json(result);
  } catch (error) {
    console.error('Controller error marking card as lost:', error);
    res.status(500).json({ message: 'Error marking card as lost', error: error.message });
  }
};

/**
 * Restore a lost card
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.restoreCard = async (req, res) => {
  try {
    ensureApiInitialized();
    const credentialId = req.params.credentialId;
    const result = await lenelS2NetBoxAPI.restoreCard(credentialId);
    res.json(result);
  } catch (error) {
    console.error('Controller error restoring card:', error);
    res.status(500).json({ message: 'Error restoring card', error: error.message });
  }
};

// ===================================================================
// EVACUATION MANAGEMENT CONTROLLERS
// ===================================================================

/**
 * Initiate an evacuation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.initiateEvacuation = async (req, res) => {
  try {
    ensureApiInitialized();
    const evacuationData = req.body;
    const result = await lenelS2NetBoxAPI.initiateEvacuation(evacuationData);
    res.json(result);
  } catch (error) {
    console.error('Controller error initiating evacuation:', error);
    res.status(500).json({ message: 'Error initiating evacuation', error: error.message });
  }
};

/**
 * End an evacuation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.endEvacuation = async (req, res) => {
  try {
    ensureApiInitialized();
    const evacuationId = req.params.id;
    const result = await lenelS2NetBoxAPI.endEvacuation(evacuationId);
    res.json(result);
  } catch (error) {
    console.error('Controller error ending evacuation:', error);
    res.status(500).json({ message: 'Error ending evacuation', error: error.message });
  }
};

/**
 * Get evacuation status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEvacuationStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const evacuationId = req.params.id || null;
    const status = await lenelS2NetBoxAPI.getEvacuationStatus(evacuationId);
    res.json(status);
  } catch (error) {
    console.error('Controller error fetching evacuation status:', error);
    res.status(500).json({ message: 'Error fetching evacuation status', error: error.message });
  }
};

/**
 * Get occupancy report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getOccupancyReport = async (req, res) => {
  try {
    ensureApiInitialized();
    const occupancyReport = await lenelS2NetBoxAPI.getOccupancyReport();
    res.json(occupancyReport);
  } catch (error) {
    console.error('Controller error fetching occupancy report:', error);
    res.status(500).json({ message: 'Error fetching occupancy report', error: error.message });
  }
};

// ===================================================================
// READER MANAGEMENT CONTROLLERS
// ===================================================================

/**
 * Get all readers
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getReaders = async (req, res) => {
  try {
    ensureApiInitialized();
    const readers = await lenelS2NetBoxAPI.getReaders();
    res.json(readers);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox readers:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox readers', error: error.message });
  }
};

/**
 * Get reader details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getReaderDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const readerId = req.params.id;
    const readerDetails = await lenelS2NetBoxAPI.getReaderDetails(readerId);
    res.json(readerDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox reader details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox reader details', error: error.message });
  }
};

/**
 * Get all reader groups
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getReaderGroups = async (req, res) => {
  try {
    ensureApiInitialized();
    const readerGroups = await lenelS2NetBoxAPI.getReaderGroups();
    res.json(readerGroups);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox reader groups:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox reader groups', error: error.message });
  }
};

/**
 * Get reader group details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getReaderGroupDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const readerGroupId = req.params.id;
    const readerGroupDetails = await lenelS2NetBoxAPI.getReaderGroupDetails(readerGroupId);
    res.json(readerGroupDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox reader group details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox reader group details', error: error.message });
  }
};

// ===================================================================
// PORTAL GROUP MANAGEMENT CONTROLLERS
// ===================================================================

/**
 * Get all portal groups
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPortalGroups = async (req, res) => {
  try {
    ensureApiInitialized();
    const portalGroups = await lenelS2NetBoxAPI.getPortalGroups();
    res.json(portalGroups);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox portal groups:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox portal groups', error: error.message });
  }
};

/**
 * Get portal group details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPortalGroupDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const portalGroupId = req.params.id;
    const portalGroupDetails = await lenelS2NetBoxAPI.getPortalGroupDetails(portalGroupId);
    res.json(portalGroupDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox portal group details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox portal group details', error: error.message });
  }
};

// ===================================================================
// ELEVATOR CONTROL CONTROLLERS
// ===================================================================

/**
 * Get all elevators
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getElevators = async (req, res) => {
  try {
    ensureApiInitialized();
    const elevators = await lenelS2NetBoxAPI.getElevators();
    res.json(elevators);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox elevators:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox elevators', error: error.message });
  }
};

/**
 * Get elevator details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getElevatorDetails = async (req, res) => {
  try {
    ensureApiInitialized();
    const elevatorId = req.params.id;
    const elevatorDetails = await lenelS2NetBoxAPI.getElevatorDetails(elevatorId);
    res.json(elevatorDetails);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox elevator details:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox elevator details', error: error.message });
  }
};

/**
 * Control elevator
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlElevator = async (req, res) => {
  try {
    ensureApiInitialized();
    const elevatorId = req.params.id;
    const controlData = req.body;
    const result = await lenelS2NetBoxAPI.controlElevator(elevatorId, controlData);
    res.json(result);
  } catch (error) {
    console.error('Controller error controlling Lenel S2 NetBox elevator:', error);
    res.status(500).json({ message: 'Error controlling Lenel S2 NetBox elevator', error: error.message });
  }
};

/**
 * Get elevator status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getElevatorStatus = async (req, res) => {
  try {
    ensureApiInitialized();
    const elevatorId = req.params.id;
    const elevatorStatus = await lenelS2NetBoxAPI.getElevatorStatus(elevatorId);
    res.json(elevatorStatus);
  } catch (error) {
    console.error('Controller error fetching Lenel S2 NetBox elevator status:', error);
    res.status(500).json({ message: 'Error fetching Lenel S2 NetBox elevator status', error: error.message });
  }
};
