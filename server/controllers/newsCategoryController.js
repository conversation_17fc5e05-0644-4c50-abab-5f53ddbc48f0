const NewsCategory = require('../../models/NewsCategory');
const NewsPost = require('../../models/NewsPost');
const { validationResult } = require('express-validator');

/**
 * News Category Controller
 * Handles API operations for news categories
 */
const newsCategoryController = {
  /**
   * Get all news categories
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with categories
   */
  getAllCategories: async (req, res) => {
    try {
      const categories = await NewsCategory.find()
        .sort({ name: 1 });
      
      res.json(categories);
    } catch (err) {
      console.error('Error fetching news categories:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get news categories accessible by the current user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with accessible categories
   */
  getAccessibleCategories: async (req, res) => {
    try {
      // If user is admin, return all categories
      if (req.user.roles.includes('admin')) {
        const categories = await NewsCategory.find()
          .sort({ name: 1 });
        return res.json(categories);
      }

      // Otherwise, return categories accessible by user's roles
      const categories = await NewsCategory.find({
        $or: [
          { accessRoles: { $in: req.user.roles } },
          { accessRoles: { $size: 0 } } // Categories with empty accessRoles are accessible to all
        ]
      }).sort({ name: 1 });
      
      res.json(categories);
    } catch (err) {
      console.error('Error fetching accessible news categories:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Get category by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with category
   */
  getCategoryById: async (req, res) => {
    try {
      const category = await NewsCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ msg: 'News category not found' });
      }
      
      res.json(category);
    } catch (err) {
      console.error('Error fetching news category:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News category not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Create a new news category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with created category
   */
  createCategory: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { name, description, accessRoles } = req.body;

      // Check if category with the same name already exists
      const existingCategory = await NewsCategory.findOne({ name });
      if (existingCategory) {
        return res.status(400).json({ msg: 'A category with this name already exists' });
      }

      // Create new category
      const newCategory = new NewsCategory({
        name,
        description,
        createdBy: req.user.id,
        accessRoles: accessRoles || []
      });

      const category = await newCategory.save();
      res.json(category);
    } catch (err) {
      console.error('Error creating news category:', err.message);
      res.status(500).send('Server Error');
    }
  },

  /**
   * Update a news category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with updated category
   */
  updateCategory: async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { name, description, accessRoles } = req.body;

      // Find category by ID
      let category = await NewsCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ msg: 'News category not found' });
      }
      
      // Check if another category with the same name already exists
      if (name && name !== category.name) {
        const existingCategory = await NewsCategory.findOne({ name });
        if (existingCategory) {
          return res.status(400).json({ msg: 'A category with this name already exists' });
        }
      }
      
      // Update category fields
      if (name) category.name = name;
      if (description !== undefined) category.description = description;
      if (accessRoles) category.accessRoles = accessRoles;
      
      await category.save();
      res.json(category);
    } catch (err) {
      console.error('Error updating news category:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News category not found' });
      }
      
      res.status(500).send('Server Error');
    }
  },

  /**
   * Delete a news category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response
   */
  deleteCategory: async (req, res) => {
    try {
      // Find category by ID
      const category = await NewsCategory.findById(req.params.id);
      
      if (!category) {
        return res.status(404).json({ msg: 'News category not found' });
      }
      
      // Check if there are any news posts in this category
      const postsCount = await NewsPost.countDocuments({ category: req.params.id });
      if (postsCount > 0) {
        return res.status(400).json({ 
          msg: 'Cannot delete category that contains news posts. Please move or delete the posts first.' 
        });
      }
      
      await category.remove();
      res.json({ msg: 'News category removed' });
    } catch (err) {
      console.error('Error deleting news category:', err.message);
      
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: 'News category not found' });
      }
      
      res.status(500).send('Server Error');
    }
  }
};

module.exports = newsCategoryController;