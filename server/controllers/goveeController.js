const GoveeAPI = require('../integrations/govee/goveeAPI');
const cacheUtil = require('../utils/cacheUtil');

// Initialize Govee API with environment variables
let goveeAPI = new GoveeAPI();

/**
 * Get the latest Govee configuration
 * @returns {Promise<Object|null>} The latest configuration or null if not found
 */
const getLatestConfig = async () => {
  try {
    // Check if environment variables are set
    const apiKey = process.env.GOVEE_API_KEY || '';
    
    // If environment variables are set, use them directly
    if (apiKey) {
      return {
        apiKey,
        updatedAt: new Date(),
        fromEnv: true
      };
    }
    
    // If no environment variables are set, log a warning
    console.warn('Govee API key not set. Please configure GOVEE_API_KEY in your environment.');
    
    return null;
  } catch (error) {
    console.error('Error fetching Govee configuration:', error);
    throw error;
  }
};

// Export the getLatestConfig function for use by other modules
exports.getLatestConfig = getLatestConfig;

/**
 * Initialize the Govee API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.initialize = async (req, res) => {
  try {
    await goveeAPI.initialize();
    res.json({ message: 'Govee API initialized successfully' });
  } catch (error) {
    console.error('Error initializing Govee API:', error);
    res.status(500).json({ message: 'Error initializing Govee API', error: error.message });
  }
};

/**
 * Get all Govee devices
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDevices = async (req, res) => {
  try {
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('govee-devices');
    
    // Try to get the devices from cache first
    let devices = cacheUtil.get(cacheKey);
    
    if (!devices) {
      console.log('Govee devices cache miss, fetching from API');
      // If not in cache, fetch from API
      devices = await goveeAPI.getDevices();
      
      if (!devices) {
        return res.status(500).json({ message: 'Error fetching Govee devices' });
      }
      
      // Cache the result for 5 minutes (devices list doesn't change often)
      cacheUtil.set(cacheKey, devices, 5 * 60 * 1000);
    } else {
      console.log('Govee devices served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=300'); // 5 minutes
    
    res.json(devices);
  } catch (error) {
    console.error('Controller error fetching Govee devices:', error);
    res.status(500).json({ message: 'Error fetching Govee devices', error: error.message });
  }
};

/**
 * Get device state
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDeviceState = async (req, res) => {
  try {
    const { device, model } = req.query;
    
    if (!device || !model) {
      return res.status(400).json({ message: 'Device ID and model are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey(`govee-device-state-${device}`);
    
    // Try to get the device state from cache first
    let deviceState = cacheUtil.get(cacheKey);
    
    if (!deviceState) {
      console.log(`Govee device state cache miss for ${device}, fetching from API`);
      // If not in cache, fetch from API
      deviceState = await goveeAPI.getDeviceState(device, model);
      
      if (!deviceState) {
        return res.status(500).json({ message: 'Error fetching Govee device state' });
      }
      
      // Cache the result for 30 seconds (device state can change frequently)
      cacheUtil.set(cacheKey, deviceState, 30 * 1000);
    } else {
      console.log(`Govee device state for ${device} served from cache`);
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=30'); // 30 seconds
    
    res.json(deviceState);
  } catch (error) {
    console.error('Controller error fetching Govee device state:', error);
    res.status(500).json({ message: 'Error fetching Govee device state', error: error.message });
  }
};

/**
 * Control device
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.controlDevice = async (req, res) => {
  try {
    const { device, model, cmd, value } = req.body;
    
    if (!device || !model || !cmd) {
      return res.status(400).json({ message: 'Device ID, model, and command are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    const success = await goveeAPI.controlDevice({ device, model, cmd, value });
    
    if (!success) {
      return res.status(500).json({ message: 'Error controlling Govee device' });
    }
    
    // Invalidate the device state cache for this device
    const cacheKey = cacheUtil.createKey(`govee-device-state-${device}`);
    cacheUtil.del(cacheKey);
    
    res.json({ message: 'Device controlled successfully' });
  } catch (error) {
    console.error('Controller error controlling Govee device:', error);
    res.status(500).json({ message: 'Error controlling Govee device', error: error.message });
  }
};

/**
 * Turn device on or off
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.turnDevice = async (req, res) => {
  try {
    const { device, model, on } = req.body;
    
    if (!device || !model || on === undefined) {
      return res.status(400).json({ message: 'Device ID, model, and on/off state are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    const success = await goveeAPI.turnDevice(device, model, on);
    
    if (!success) {
      return res.status(500).json({ message: 'Error turning Govee device on/off' });
    }
    
    // Invalidate the device state cache for this device
    const cacheKey = cacheUtil.createKey(`govee-device-state-${device}`);
    cacheUtil.del(cacheKey);
    
    res.json({ message: `Device turned ${on ? 'on' : 'off'} successfully` });
  } catch (error) {
    console.error('Controller error turning Govee device on/off:', error);
    res.status(500).json({ message: 'Error turning Govee device on/off', error: error.message });
  }
};

/**
 * Set device brightness
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setBrightness = async (req, res) => {
  try {
    const { device, model, brightness } = req.body;
    
    if (!device || !model || brightness === undefined) {
      return res.status(400).json({ message: 'Device ID, model, and brightness are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    const success = await goveeAPI.setBrightness(device, model, brightness);
    
    if (!success) {
      return res.status(500).json({ message: 'Error setting Govee device brightness' });
    }
    
    // Invalidate the device state cache for this device
    const cacheKey = cacheUtil.createKey(`govee-device-state-${device}`);
    cacheUtil.del(cacheKey);
    
    res.json({ message: 'Device brightness set successfully' });
  } catch (error) {
    console.error('Controller error setting Govee device brightness:', error);
    res.status(500).json({ message: 'Error setting Govee device brightness', error: error.message });
  }
};

/**
 * Set device color
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setColor = async (req, res) => {
  try {
    const { device, model, color } = req.body;
    
    if (!device || !model || !color) {
      return res.status(400).json({ message: 'Device ID, model, and color are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    const success = await goveeAPI.setColor(device, model, color);
    
    if (!success) {
      return res.status(500).json({ message: 'Error setting Govee device color' });
    }
    
    // Invalidate the device state cache for this device
    const cacheKey = cacheUtil.createKey(`govee-device-state-${device}`);
    cacheUtil.del(cacheKey);
    
    res.json({ message: 'Device color set successfully' });
  } catch (error) {
    console.error('Controller error setting Govee device color:', error);
    res.status(500).json({ message: 'Error setting Govee device color', error: error.message });
  }
};

/**
 * Set device color temperature
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.setColorTemperature = async (req, res) => {
  try {
    const { device, model, temperature } = req.body;
    
    if (!device || !model || !temperature) {
      return res.status(400).json({ message: 'Device ID, model, and temperature are required' });
    }
    
    const config = await getLatestConfig();
    
    if (!config) {
      return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
    }
    
    const success = await goveeAPI.setColorTemperature(device, model, temperature);
    
    if (!success) {
      return res.status(500).json({ message: 'Error setting Govee device color temperature' });
    }
    
    // Invalidate the device state cache for this device
    const cacheKey = cacheUtil.createKey(`govee-device-state-${device}`);
    cacheUtil.del(cacheKey);
    
    res.json({ message: 'Device color temperature set successfully' });
  } catch (error) {
    console.error('Controller error setting Govee device color temperature:', error);
    res.status(500).json({ message: 'Error setting Govee device color temperature', error: error.message });
  }
};

/**
 * Get configuration status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConfig = async (req, res) => {
  try {
    // Create a cache key for this request
    const cacheKey = cacheUtil.createKey('govee-config');
    
    // Try to get the config from cache first
    let safeConfig = cacheUtil.get(cacheKey);
    
    if (!safeConfig) {
      console.log('Govee config cache miss, fetching from sources');
      
      const config = await getLatestConfig();
      
      if (!config) {
        return res.status(404).json({ message: 'Govee API not configured. Please set the required environment variables.' });
      }
      
      // Remove sensitive information
      safeConfig = {
        ...config,
        apiKey: undefined
      };
      
      // Cache the result for 15 minutes (configuration doesn't change often)
      cacheUtil.set(cacheKey, safeConfig, 15 * 60 * 1000);
    } else {
      console.log('Govee config served from cache');
    }
    
    // Set headers to indicate the response can be cached by the client
    res.setHeader('Cache-Control', 'private, max-age=900'); // 15 minutes
    
    res.json(safeConfig);
  } catch (error) {
    console.error('Controller error fetching configuration:', error);
    res.status(500).json({ message: 'Error fetching configuration', error: error.message });
  }
};