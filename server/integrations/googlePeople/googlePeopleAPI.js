const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);
const createGoogleAuthClient = require('../../utils/googleServiceAuth');

/**
 * Google People API Wrapper
 * Handles integration with Google People API for contacts synchronization
 */
class GooglePeopleAPI {
  /**
   * Create a new GooglePeopleAPI instance
   * @param {Object} options - Configuration options
   * @param {string} options.clientId - OAuth client ID
   * @param {string} options.clientSecret - OAuth client secret
   * @param {string} options.redirectUri - OAuth redirect URI
   * @param {string} options.refreshToken - OAuth refresh token (optional)
   */
  constructor(options = {}) {
    this.clientId = options.clientId || process.env.GOOGLE_CLIENT_ID;
    this.clientSecret = options.clientSecret || process.env.GOOGLE_CLIENT_SECRET;
    this.redirectUri = options.redirectUri || process.env.GOOGLE_REDIRECT_URI;
    this.refreshToken = options.refreshToken;
    this.integrationName = 'Google People API';
    this.initialized = false;
    this.auth = null;
    this.peopleApi = null;
  }

  /**
   * Initialize the Google People API client
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // Create OAuth2 client
      this.auth = new google.auth.OAuth2(
        this.clientId,
        this.clientSecret,
        this.redirectUri
      );

      // Set refresh token if provided
      if (this.refreshToken) {
        this.auth.setCredentials({
          refresh_token: this.refreshToken
        });
      }

      // Create People API client
      this.peopleApi = google.people({ version: 'v1', auth: this.auth });
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing Google People API:', error);
      throw error;
    }
  }

  /**
   * Generate OAuth URL for authentication
   * @param {string[]} scopes - OAuth scopes to request
   * @returns {string} - Authentication URL
   */
  generateAuthUrl(scopes = ['https://www.googleapis.com/auth/contacts']) {
    if (!this.auth) {
      this.auth = new google.auth.OAuth2(
        this.clientId,
        this.clientSecret,
        this.redirectUri
      );
    }

    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent'
    });
  }

  /**
   * Get tokens from authorization code
   * @param {string} code - Authorization code
   * @returns {Promise<Object>} - Tokens
   */
  async getTokensFromCode(code) {
    if (!this.auth) {
      this.auth = new google.auth.OAuth2(
        this.clientId,
        this.clientSecret,
        this.redirectUri
      );
    }

    const { tokens } = await this.auth.getToken(code);
    this.auth.setCredentials(tokens);
    this.refreshToken = tokens.refresh_token;
    return tokens;
  }

  /**
   * Create a contact group
   * @param {string} groupName - Name of the contact group
   * @returns {Promise<Object>} - Created contact group
   */
  async createContactGroup(groupName) {
    await this.initialize();

    try {
      const response = await this.peopleApi.contactGroups.create({
        requestBody: {
          contactGroup: {
            name: groupName
          }
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error creating contact group:', error);
      throw error;
    }
  }

  /**
   * Get contact groups
   * @returns {Promise<Array>} - List of contact groups
   */
  async getContactGroups() {
    await this.initialize();

    try {
      const response = await this.peopleApi.contactGroups.list();
      return response.data.contactGroups || [];
    } catch (error) {
      console.error('Error getting contact groups:', error);
      throw error;
    }
  }

  /**
   * Get or create a contact group
   * @param {string} groupName - Name of the contact group
   * @returns {Promise<Object>} - Contact group
   */
  async getOrCreateContactGroup(groupName) {
    const groups = await this.getContactGroups();
    const existingGroup = groups.find(group => group.name === groupName);

    if (existingGroup) {
      return existingGroup;
    }

    return this.createContactGroup(groupName);
  }

  /**
   * Create a contact
   * @param {Object} contactData - Contact data
   * @param {string} groupResourceName - Resource name of the contact group
   * @returns {Promise<Object>} - Created contact
   */
  async createContact(contactData, groupResourceName) {
    await this.initialize();

    try {
      // Format contact data for Google People API
      const formattedContact = this._formatContactForGoogle(contactData);

      // Add to specified group if provided
      if (groupResourceName) {
        formattedContact.membershipInfo = [{
          contactGroupMembership: {
            contactGroupResourceName: groupResourceName
          }
        }];
      }

      const response = await this.peopleApi.people.createContact({
        requestBody: formattedContact
      });

      return response.data;
    } catch (error) {
      console.error('Error creating contact:', error);
      throw error;
    }
  }

  /**
   * Update a contact
   * @param {string} resourceName - Resource name of the contact
   * @param {Object} contactData - Updated contact data
   * @returns {Promise<Object>} - Updated contact
   */
  async updateContact(resourceName, contactData) {
    await this.initialize();

    try {
      // Format contact data for Google People API
      const formattedContact = this._formatContactForGoogle(contactData);

      // Get the person's etag
      const person = await this.peopleApi.people.get({
        resourceName: resourceName,
        personFields: 'names,emailAddresses,phoneNumbers,organizations,addresses'
      });

      const response = await this.peopleApi.people.updateContact({
        resourceName: resourceName,
        updatePersonFields: 'names,emailAddresses,phoneNumbers,organizations,addresses',
        requestBody: {
          ...formattedContact,
          etag: person.data.etag
        }
      });

      return response.data;
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  }

  /**
   * Delete a contact
   * @param {string} resourceName - Resource name of the contact
   * @returns {Promise<void>}
   */
  async deleteContact(resourceName) {
    await this.initialize();

    try {
      await this.peopleApi.people.deleteContact({
        resourceName: resourceName
      });
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  }

  /**
   * Get contacts
   * @param {number} pageSize - Number of contacts to return
   * @param {string} pageToken - Token for pagination
   * @returns {Promise<Object>} - Contacts and next page token
   */
  async getContacts(pageSize = 100, pageToken = null) {
    await this.initialize();

    try {
      const response = await this.peopleApi.people.connections.list({
        resourceName: 'people/me',
        pageSize: pageSize,
        pageToken: pageToken,
        personFields: 'names,emailAddresses,phoneNumbers,organizations,addresses,metadata'
      });

      return {
        contacts: response.data.connections || [],
        nextPageToken: response.data.nextPageToken
      };
    } catch (error) {
      console.error('Error getting contacts:', error);
      throw error;
    }
  }

  /**
   * Get all contacts
   * @returns {Promise<Array>} - All contacts
   */
  async getAllContacts() {
    let allContacts = [];
    let nextPageToken = null;

    do {
      const { contacts, nextPageToken: token } = await this.getContacts(100, nextPageToken);
      allContacts = [...allContacts, ...contacts];
      nextPageToken = token;
    } while (nextPageToken);

    return allContacts;
  }

  /**
   * Format contact data for Google People API
   * @param {Object} contactData - Contact data from CSF Portal
   * @returns {Object} - Formatted contact data for Google
   * @private
   */
  _formatContactForGoogle(contactData) {
    const formattedContact = {
      names: [{
        givenName: contactData.name
      }],
      phoneNumbers: [{
        value: contactData.phone,
        type: 'work'
      }]
    };

    // Add email if available
    if (contactData.email) {
      formattedContact.emailAddresses = [{
        value: contactData.email,
        type: 'work'
      }];
    }

    // Add company if available
    if (contactData.company) {
      formattedContact.organizations = [{
        name: contactData.company,
        type: 'work'
      }];
    }

    // Add address if available
    if (contactData.address && (contactData.address.street || contactData.address.city)) {
      formattedContact.addresses = [{
        streetAddress: contactData.address.street || '',
        city: contactData.address.city || '',
        region: contactData.address.state || '',
        postalCode: contactData.address.zipCode || '',
        country: contactData.address.country || 'USA',
        type: 'work'
      }];
    }

    return formattedContact;
  }

  /**
   * Format Google contact data for CSF Portal
   * @param {Object} googleContact - Contact data from Google
   * @returns {Object} - Formatted contact data for CSF Portal
   */
  formatGoogleContactForPortal(googleContact) {
    const name = googleContact.names && googleContact.names.length > 0
      ? googleContact.names[0].displayName || googleContact.names[0].givenName
      : 'Unknown';

    const phone = googleContact.phoneNumbers && googleContact.phoneNumbers.length > 0
      ? googleContact.phoneNumbers[0].value
      : '';

    const email = googleContact.emailAddresses && googleContact.emailAddresses.length > 0
      ? googleContact.emailAddresses[0].value
      : '';

    const company = googleContact.organizations && googleContact.organizations.length > 0
      ? googleContact.organizations[0].name
      : '';

    let address = {
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA'
    };

    if (googleContact.addresses && googleContact.addresses.length > 0) {
      const googleAddress = googleContact.addresses[0];
      address = {
        street: googleAddress.streetAddress || '',
        city: googleAddress.city || '',
        state: googleAddress.region || '',
        zipCode: googleAddress.postalCode || '',
        country: googleAddress.country || 'USA'
      };
    }

    return {
      name,
      phone,
      email,
      company,
      address,
      category: 'business', // Default category
      resourceName: googleContact.resourceName
    };
  }
}

module.exports = GooglePeopleAPI;