// Rain Bird API Wrapper
const axios = require('axios');
const https = require('https');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Rain Bird API Wrapper
 * Documentation: 
 * - https://github.com/drakej/rainbird-api
 * - https://github.com/allenporter/pyrainbird
 */
class RainBirdAPI {
  constructor(host, username = '', password, port = 443, localNetwork = false, cloudHost = 'rdz-rbcloud.rainbird.com', controllerMac = '') {
    this.host = host;
    // Username is not actually used by the Rain Bird API, but kept for backward compatibility
    this.username = username;
    this.password = password;
    this.port = port;
    this.localNetwork = localNetwork;
    this.cloudHost = cloudHost;
    this.controllerMac = controllerMac;
    this.token = null;
    this.integrationName = 'Rain Bird';
    this.controllerKey = this.password; // The password is used as the encryption key for local control

    // Headers for Rain Bird API requests
    this.headers = {
      'Accept-Language': 'en',
      'Accept-Encoding': 'gzip, deflate',
      'User-Agent': 'RainBird/2.0 CFNetwork/811.5.4 Darwin/16.7.0',
      'Accept': '*/*',
      'Connection': 'keep-alive',
      'Content-Type': 'application/octet-stream'
    };

    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });

    // Load SIP commands if available
    this.sipCommands = null;
    this.loadSipCommands();
  }

  /**
   * Load SIP commands from the JSON file
   */
  loadSipCommands() {
    try {
      const commandsPath = path.join(__dirname, 'sipCommands.json');
      if (fs.existsSync(commandsPath)) {
        this.sipCommands = JSON.parse(fs.readFileSync(commandsPath, 'utf8'));
        console.log('SIP commands loaded successfully');
      } else {
        console.warn('SIP commands file not found. Some functionality may be limited.');
      }
    } catch (error) {
      console.error('Error loading SIP commands:', error);
    }
  }

  /**
   * Encrypt data for local Rain Bird communication
   * @param {string} data - The data to encrypt
   * @returns {string} - The encrypted data
   */
  encrypt(data) {
    try {
      // Rain Bird uses a modified version of AES-256-CBC
      // The key is the password, and the IV is derived from the password
      const key = Buffer.from(this.controllerKey.padEnd(32, '\0').slice(0, 32));
      const iv = Buffer.from(this.controllerKey.padEnd(16, '\0').slice(0, 16));
      
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
      let encrypted = cipher.update(data, 'utf8', 'binary');
      encrypted += cipher.final('binary');
      
      return Buffer.from(encrypted, 'binary').toString('base64');
    } catch (error) {
      console.error('Encryption error:', error);
      throw error;
    }
  }

  /**
   * Decrypt data from local Rain Bird communication
   * @param {string} data - The encrypted data
   * @returns {string} - The decrypted data
   */
  decrypt(data) {
    try {
      // Rain Bird uses a modified version of AES-256-CBC
      // The key is the password, and the IV is derived from the password
      const key = Buffer.from(this.controllerKey.padEnd(32, '\0').slice(0, 32));
      const iv = Buffer.from(this.controllerKey.padEnd(16, '\0').slice(0, 16));
      
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
      let decrypted = decipher.update(Buffer.from(data, 'base64'), 'binary', 'utf8');
      decrypted += decipher.final('utf8');
      
      // Remove null characters and newlines that might be present in the decrypted data
      return decrypted.replace(/\x00|\n/g, '');
    } catch (error) {
      console.error('Decryption error:', error);
      throw error;
    }
  }

  /**
   * Send a JSON-RPC command to the local Rain Bird controller
   * @param {string} method - The RPC method to call
   * @param {Object} params - The parameters for the RPC method
   * @returns {Promise<Object>} - The RPC response
   */
  async localRpcCommand(method, params = {}) {
    try {
      // Create the JSON-RPC request
      const request = {
        id: Math.floor(Date.now() / 1000),
        method: method,
        params: params,
        jsonrpc: '2.0'
      };

      // Encrypt the request
      const jsonPayload = JSON.stringify(request);
      const encryptedPayload = this.encrypt(jsonPayload);

      // Send the request to the controller
      const response = await this.axios({
        method: 'post',
        url: `http://${this.host}/stick`,
        headers: this.headers,
        data: encryptedPayload
      });

      // Decrypt and parse the response
      if (response.status === 200) {
        const decryptedResponse = this.decrypt(response.data);
        return JSON.parse(decryptedResponse);
      } else if (response.status === 503) {
        throw new Error('Controller is busy or locked. Try again later.');
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error sending local RPC command:', error);
      throw error;
    }
  }

  /**
   * Send a JSON-RPC command to the Rain Bird cloud
   * @param {string} method - The RPC method to call
   * @param {Object} params - The parameters for the RPC method
   * @returns {Promise<Object>} - The RPC response
   */
  async cloudRpcCommand(method, params = {}) {
    try {
      // Create the JSON-RPC request
      const request = {
        id: Math.floor(Date.now() / 1000),
        method: method,
        params: params,
        jsonrpc: '2.0'
      };

      // Send the request to the cloud
      const response = await this.axios({
        method: 'post',
        url: `http://${this.cloudHost}/phone-api`,
        headers: {
          'Content-Type': 'application/json'
        },
        data: request
      });

      // Parse and return the response
      if (response.status === 200) {
        return response.data;
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error sending cloud RPC command:', error);
      throw error;
    }
  }

  /**
   * Send a SIP command to the local Rain Bird controller
   * @param {string} command - The SIP command to send
   * @param {...string} args - Additional arguments for the command
   * @returns {Promise<Object>} - The command response
   */
  async sipCommand(command, ...args) {
    try {
      // Check if SIP commands are loaded
      if (!this.sipCommands) {
        throw new Error('SIP commands not loaded. Cannot execute SIP command.');
      }

      // Get the command data
      const commandData = this.sipCommands.ControllerCommands[command];
      if (!commandData) {
        throw new Error(`Unknown SIP command: ${command}`);
      }

      // Build the command string
      let data = commandData.command;
      for (const arg of args) {
        data += arg;
      }

      // Send the command via tunnelSip RPC
      const response = await this.localRpcCommand('tunnelSip', {
        data: data,
        length: commandData.length
      });

      // Parse the response
      const responseData = {};
      if (response.result && response.result.data) {
        const data = response.result.data;
        const responseCode = data.substring(0, 2);
        const responseType = this.sipCommands.ControllerResponses[responseCode];
        
        if (responseType && responseType.params) {
          for (const [name, param] of Object.entries(responseType.params)) {
            responseData[name] = data.substring(param.position, param.position + param.length);
          }
        }
        
        responseData.code = responseCode;
        responseData.raw = data;
      }

      return responseData;
    } catch (error) {
      console.error('Error sending SIP command:', error);
      throw error;
    }
  }

  /**
   * Get the local IP address of the controller from the cloud
   * @returns {Promise<string>} - The local IP address
   */
  async getControllerIpFromCloud() {
    try {
      if (!this.controllerMac) {
        throw new Error('Controller MAC address is required for cloud lookup');
      }

      const response = await this.cloudRpcCommand('getStickIpAddress', {
        StickId: this.controllerMac
      });

      if (response.result && response.result.IpAddress) {
        const ipAddress = response.result.IpAddress;
        this.host = ipAddress; // Update the host with the discovered IP
        return ipAddress;
      } else {
        throw new Error('Could not retrieve controller IP address from cloud');
      }
    } catch (error) {
      console.error('Error getting controller IP from cloud:', error);
      throw error;
    }
  }

  /**
   * Initialize the Rain Bird API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // If we're using cloud control and don't have a local IP, try to get it from the cloud
      if (!this.localNetwork && this.controllerMac && (!this.host || this.host === 'your_rain_bird_host_here')) {
        try {
          await this.getControllerIpFromCloud();
          console.log(`Retrieved controller IP from cloud: ${this.host}`);
        } catch (error) {
          console.error('Failed to get controller IP from cloud:', error);
        }
      }

      // Check if we can authenticate
      const isAuthenticated = await this.isAuthenticated();

      if (isAuthenticated) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated and ready to use.'
        );
      } else {
        // Update the integration status to indicate authentication is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Authentication failed. Check credentials and try again.'
        );
      }
    } catch (error) {
      console.error('Error initializing Rain Bird API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {Promise<boolean>} Authentication status
   */
  async isAuthenticated() {
    try {
      // For local control, we'll try to get the model and version
      // For cloud control, we'll try to get the controller IP
      if (this.localNetwork) {
        const modelInfo = await this.getModelAndVersion();
        return !!modelInfo;
      } else {
        // Try to get the controller IP from the cloud if we have a MAC address
        if (this.controllerMac) {
          const ipAddress = await this.getControllerIpFromCloud();
          return !!ipAddress;
        } else {
          // If we don't have a MAC address, try to get the model and version
          const modelInfo = await this.getModelAndVersion();
          return !!modelInfo;
        }
      }
    } catch (error) {
      console.error('Authentication check failed:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Authentication error: ${error.message}`
      );

      return false;
    }
  }

  /**
   * Get model and version information
   * @returns {Promise<Object>} Model and version information
   */
  async getModelAndVersion() {
    try {
      const response = await this.sipCommand('ModelAndVersion');
      return {
        model: response.model,
        version: response.version,
        raw: response.raw
      };
    } catch (error) {
      console.error('Error fetching Rain Bird model and version:', error);
      throw error;
    }
  }

  /**
   * Get available stations (zones)
   * @returns {Promise<Object>} Available stations
   */
  async getAvailableStations() {
    try {
      const response = await this.sipCommand('AvailableStations');
      
      // Parse the station mask to determine which stations are available
      const stationMask = response.setStations || '';
      const stations = [];
      
      for (let i = 0; i < stationMask.length; i += 2) {
        const byte = parseInt(stationMask.substring(i, i + 2), 16);
        for (let bit = 0; bit < 8; bit++) {
          if ((byte & (1 << bit)) !== 0) {
            stations.push(i * 4 + bit + 1);
          }
        }
      }
      
      return {
        stations: stations,
        raw: response.raw
      };
    } catch (error) {
      console.error('Error fetching Rain Bird available stations:', error);
      throw error;
    }
  }

  /**
   * Get all zones (stations)
   * @returns {Promise<Array>} List of zones
   */
  async getZones() {
    try {
      const availableStations = await this.getAvailableStations();
      const zoneStates = await this.getZoneStates();
      
      // Combine the information
      const zones = availableStations.stations.map(stationId => {
        return {
          id: stationId.toString(),
          name: `Zone ${stationId}`,
          active: zoneStates.active.includes(stationId),
          enabled: true
        };
      });
      
      return zones;
    } catch (error) {
      console.error('Error fetching Rain Bird zones:', error);
      throw error;
    }
  }

  /**
   * Get zone states
   * @returns {Promise<Object>} Zone states
   */
  async getZoneStates() {
    try {
      const response = await this.sipCommand('CurrentStationsActive');
      
      // Parse the station mask to determine which stations are active
      const stationMask = response.activeStations || '';
      const activeStations = [];
      
      for (let i = 0; i < stationMask.length; i += 2) {
        const byte = parseInt(stationMask.substring(i, i + 2), 16);
        for (let bit = 0; bit < 8; bit++) {
          if ((byte & (1 << bit)) !== 0) {
            activeStations.push(i * 4 + bit + 1);
          }
        }
      }
      
      return {
        active: activeStations,
        raw: response.raw
      };
    } catch (error) {
      console.error('Error fetching Rain Bird zone states:', error);
      throw error;
    }
  }

  /**
   * Get zone details
   * @param {string|number} zoneId Zone ID
   * @returns {Promise<Object>} Zone details
   */
  async getZoneDetails(zoneId) {
    try {
      const zoneIdNum = parseInt(zoneId, 10);
      const zones = await this.getZones();
      const zone = zones.find(z => parseInt(z.id, 10) === zoneIdNum);
      
      if (!zone) {
        throw new Error(`Zone ${zoneId} not found`);
      }
      
      return zone;
    } catch (error) {
      console.error(`Error fetching Rain Bird zone details for ${zoneId}:`, error);
      throw error;
    }
  }

  /**
   * Start watering a zone
   * @param {string|number} zoneId Zone ID
   * @param {number} duration Duration in minutes
   * @returns {Promise<Object>} Operation result
   */
  async startZone(zoneId, duration = 5) {
    try {
      const zoneIdNum = parseInt(zoneId, 10);
      
      // Convert duration from minutes to seconds (Rain Bird API uses seconds)
      const durationSec = duration * 60;
      
      // Format the duration as a 4-character hex string
      const durationHex = durationSec.toString(16).padStart(4, '0');
      
      // Format the zone ID as a 2-character hex string
      const zoneHex = zoneIdNum.toString(16).padStart(2, '0');
      
      const response = await this.sipCommand('ManuallyRunStation', zoneHex, durationHex);
      
      if (response.code === '01') {
        return {
          success: true,
          message: `Zone ${zoneId} started for ${duration} minutes`,
          raw: response.raw
        };
      } else {
        throw new Error(`Failed to start zone ${zoneId}: ${response.raw}`);
      }
    } catch (error) {
      console.error(`Error starting Rain Bird zone ${zoneId}:`, error);
      throw error;
    }
  }

  /**
   * Stop watering a zone
   * @param {string|number} zoneId Zone ID
   * @returns {Promise<Object>} Operation result
   */
  async stopZone(zoneId) {
    try {
      // To stop a specific zone, we need to stop all zones and then restart any that were active except the one we want to stop
      const zoneIdNum = parseInt(zoneId, 10);
      const zoneStates = await this.getZoneStates();
      
      // Stop all zones
      const stopResponse = await this.stopAllZones();
      
      // Restart any zones that were active except the one we want to stop
      const restartPromises = zoneStates.active
        .filter(id => id !== zoneIdNum)
        .map(id => this.startZone(id, 5)); // Default to 5 minutes
      
      await Promise.all(restartPromises);
      
      return {
        success: true,
        message: `Zone ${zoneId} stopped`,
        raw: stopResponse.raw
      };
    } catch (error) {
      console.error(`Error stopping Rain Bird zone ${zoneId}:`, error);
      throw error;
    }
  }

  /**
   * Stop all zones
   * @returns {Promise<Object>} Operation result
   */
  async stopAllZones() {
    try {
      const response = await this.sipCommand('StopIrrigation');
      
      if (response.code === '01') {
        return {
          success: true,
          message: 'All zones stopped',
          raw: response.raw
        };
      } else {
        throw new Error(`Failed to stop all zones: ${response.raw}`);
      }
    } catch (error) {
      console.error('Error stopping all Rain Bird zones:', error);
      throw error;
    }
  }

  /**
   * Get all programs (schedules)
   * @returns {Promise<Array>} List of programs
   */
  async getPrograms() {
    try {
      // Get the list of available programs
      const programsResponse = await this.sipCommand('ListProgramsUsed');
      
      if (!programsResponse || !programsResponse.raw) {
        throw new Error('Failed to get programs list');
      }
      
      // Parse the program mask to determine which programs are available
      const programMask = programsResponse.programsUsed || '';
      const programIds = [];
      
      for (let i = 0; i < programMask.length; i += 2) {
        const byte = parseInt(programMask.substring(i, i + 2), 16);
        for (let bit = 0; bit < 8; bit++) {
          if ((byte & (1 << bit)) !== 0) {
            programIds.push(i * 4 + bit + 1);
          }
        }
      }
      
      // Get details for each program
      const programs = [];
      for (const programId of programIds) {
        try {
          // Format the program ID as a 2-character hex string
          const programHex = programId.toString(16).padStart(2, '0');
          
          // Get program details
          const detailsResponse = await this.sipCommand('GetProgramDetails', programHex);
          
          programs.push({
            id: programId.toString(),
            name: `Program ${programId}`,
            enabled: true,
            raw: detailsResponse.raw
          });
        } catch (error) {
          console.error(`Error fetching details for program ${programId}:`, error);
        }
      }
      
      return programs;
    } catch (error) {
      console.error('Error fetching Rain Bird programs:', error);
      throw error;
    }
  }

  /**
   * Start a program
   * @param {string|number} programId Program ID
   * @returns {Promise<Object>} Operation result
   */
  async startProgram(programId) {
    try {
      const programIdNum = parseInt(programId, 10);
      
      // Format the program ID as a 2-character hex string
      const programHex = programIdNum.toString(16).padStart(2, '0');
      
      const response = await this.sipCommand('ManuallyRunProgram', programHex);
      
      if (response.code === '01') {
        return {
          success: true,
          message: `Program ${programId} started`,
          raw: response.raw
        };
      } else {
        throw new Error(`Failed to start program ${programId}: ${response.raw}`);
      }
    } catch (error) {
      console.error(`Error starting Rain Bird program ${programId}:`, error);
      throw error;
    }
  }

  /**
   * Stop a program
   * @param {string|number} programId Program ID
   * @returns {Promise<Object>} Operation result
   */
  async stopProgram(programId) {
    try {
      // To stop a program, we need to stop all irrigation
      const response = await this.stopAllZones();
      
      return {
        success: true,
        message: `Program ${programId} stopped`,
        raw: response.raw
      };
    } catch (error) {
      console.error(`Error stopping Rain Bird program ${programId}:`, error);
      throw error;
    }
  }

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      // Get various status information
      const modelInfo = await this.getModelAndVersion();
      const zoneStates = await this.getZoneStates();
      const rainSensorState = await this.getRainSensorState();
      const rainDelay = await this.getRainDelay();
      
      return {
        model: modelInfo.model,
        version: modelInfo.version,
        activeZones: zoneStates.active,
        rainSensor: rainSensorState.active,
        rainDelay: rainDelay.active ? rainDelay.hours : 0,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching Rain Bird system status:', error);
      throw error;
    }
  }

  /**
   * Get rain sensor state
   * @returns {Promise<Object>} Rain sensor state
   */
  async getRainSensorState() {
    try {
      const response = await this.sipCommand('CurrentRainSensorState');
      
      return {
        active: response.sensorState === '01',
        raw: response.raw
      };
    } catch (error) {
      console.error('Error fetching Rain Bird rain sensor state:', error);
      throw error;
    }
  }

  /**
   * Get rain delay
   * @returns {Promise<Object>} Rain delay
   */
  async getRainDelay() {
    try {
      const response = await this.sipCommand('CurrentRainDelay');
      
      // Convert the hex string to a number
      const hours = parseInt(response.delaySetting || '0', 16);
      
      return {
        active: hours > 0,
        hours: hours,
        raw: response.raw
      };
    } catch (error) {
      console.error('Error fetching Rain Bird rain delay:', error);
      throw error;
    }
  }

  /**
   * Set rain delay
   * @param {number} hours Number of hours to delay
   * @returns {Promise<Object>} Operation result
   */
  async setRainDelay(hours) {
    try {
      // Format the hours as a 2-character hex string
      const hoursHex = Math.min(hours, 255).toString(16).padStart(2, '0');
      
      const response = await this.sipCommand('SetRainDelay', hoursHex);
      
      if (response.code === '01') {
        return {
          success: true,
          message: `Rain delay set to ${hours} hours`,
          raw: response.raw
        };
      } else {
        throw new Error(`Failed to set rain delay: ${response.raw}`);
      }
    } catch (error) {
      console.error(`Error setting Rain Bird rain delay to ${hours} hours:`, error);
      throw error;
    }
  }

  /**
   * Get irrigation state
   * @returns {Promise<Object>} Irrigation state
   */
  async getIrrigationState() {
    try {
      const response = await this.sipCommand('CurrentIrrigationState');
      
      return {
        active: response.irrigationState === '01',
        raw: response.raw
      };
    } catch (error) {
      console.error('Error fetching Rain Bird irrigation state:', error);
      throw error;
    }
  }

  /**
   * Get controller state
   * @returns {Promise<Object>} Controller state
   */
  async getControllerState() {
    try {
      const irrigationState = await this.getIrrigationState();
      const rainSensorState = await this.getRainSensorState();
      const rainDelay = await this.getRainDelay();
      
      return {
        irrigationActive: irrigationState.active,
        rainSensorActive: rainSensorState.active,
        rainDelayActive: rainDelay.active,
        rainDelayHours: rainDelay.hours
      };
    } catch (error) {
      console.error('Error fetching Rain Bird controller state:', error);
      throw error;
    }
  }
}

module.exports = RainBirdAPI;