// Canva API Wrapper
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');
const querystring = require('querystring');

/**
 * Canva API Wrapper
 * Documentation: https://www.canva.dev/docs/connect/
 */
class CanvaAPI {
  constructor(domain, apiKey, clientId, clientSecret, redirectUri, userTokens, userId) {
    this.domain = domain;
    // api<PERSON>ey is deprecated and no longer used by Canva
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.userTokens = userTokens;
    this.userId = userId;
    this.baseURL = `https://api.${domain}/rest/v1`;
    this.authURL = `https://www.${domain}/api/oauth`;
    this.integrationName = 'Canva';
    
    // Create axios instance with appropriate headers
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {}
    });
    
    // Set authorization header based on OAuth tokens
    if (userTokens && userTokens.accessToken) {
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${userTokens.accessToken}`;
    }
  }

  /**
   * Initialize the Canva API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // For user-specific authentication
      if (this.userTokens && this.userTokens.accessToken) {
        try {
          // Check if token needs refresh
          if (this.userTokens.expiry && new Date(this.userTokens.expiry) < new Date()) {
            await this.refreshToken();
          }
          
          // Make a test API call
          await this.getDesigns({ limit: 1 });
          
          // Update the integration status to active for this user
          integrationTracker.updateStatus(
            `${this.integrationName}-user-${this.userId}`, 
            'active', 
            new Date(), 
            'User authentication is valid and ready to use.'
          );
          return;
        } catch (userAuthError) {
          // If refresh token is available, try to refresh the token
          if (this.userTokens.refreshToken && !userAuthError.message.includes('tried to refresh')) {
            try {
              await this.refreshToken();
              await this.getDesigns({ limit: 1 });
              
              // Update the integration status to active for this user
              integrationTracker.updateStatus(
                `${this.integrationName}-user-${this.userId}`, 
                'active', 
                new Date(), 
                'User authentication refreshed and ready to use.'
              );
              return;
            } catch (refreshError) {
              console.error('Error refreshing user token:', refreshError);
              // OAuth authentication failed, update status
              integrationTracker.updateStatus(
                `${this.integrationName}-user-${this.userId}`, 
                'error', 
                null, 
                `OAuth authentication failed: ${refreshError.message}`
              );
              throw refreshError;
            }
          } else {
            // OAuth authentication failed, update status
            integrationTracker.updateStatus(
              `${this.integrationName}-user-${this.userId}`, 
              'error', 
              null, 
              `OAuth authentication failed: ${userAuthError.message}`
            );
            throw userAuthError;
          }
        }
      }

      // Check if we have valid OAuth credentials
      if (!this.domain || !this.clientId || !this.clientSecret || !this.redirectUri) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Domain, client ID, client secret, and redirect URI are required for Canva OAuth integration.'
        );
        return;
      }

      // No user tokens available and API key authentication is no longer supported
      integrationTracker.updateStatus(
        this.integrationName, 
        'not_authenticated', 
        null, 
        'OAuth authentication is required for Canva. Please authenticate using the OAuth flow.'
      );
      
      throw new Error('OAuth authentication is required for Canva. API key authentication is no longer supported.');
    } catch (error) {
      console.error('Error initializing Canva API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }
  
  /**
   * Check if the API is authenticated
   * @returns {boolean} True if authenticated with OAuth
   */
  isAuthenticated() {
    return !!(this.userTokens && this.userTokens.accessToken);
  }
  
  /**
   * Generate authentication URL for OAuth2 flow with PKCE
   * @param {string[]} scopes - OAuth scopes to request
   * @param {string} codeVerifier - PKCE code verifier (if provided)
   * @returns {Object} Object containing authentication URL and code verifier
   */
  getAuthUrl(scopes = ['designs:read', 'templates:read', 'users:read', 'files:read'], codeVerifier = null) {
    if (!this.clientId || !this.redirectUri) {
      throw new Error('Client ID and redirect URI are required for OAuth authentication');
    }
  
    // Import PKCE utilities
    const pkceUtils = require('../../utils/pkceUtils');
  
    // Generate code verifier if not provided
    const verifier = codeVerifier || pkceUtils.generateCodeVerifier();
  
    // Generate code challenge from verifier
    const codeChallenge = pkceUtils.generateCodeChallenge(verifier);
  
    const params = {
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      response_type: 'code',
      scope: scopes.join(' '),
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    };
  
    return {
      authUrl: `${this.authURL}/authorize?${querystring.stringify(params)}`,
      codeVerifier: verifier
    };
  }
  
  /**
   * Exchange authorization code for tokens
   * @param {string} code - Authorization code from OAuth callback
   * @param {string} codeVerifier - PKCE code verifier used when generating the authorization URL
   * @returns {Promise<Object>} OAuth tokens
   */
  async exchangeCode(code, codeVerifier) {
    if (!this.clientId || !this.clientSecret || !this.redirectUri) {
      throw new Error('Client ID, client secret, and redirect URI are required for OAuth authentication');
    }
  
    if (!codeVerifier) {
      throw new Error('Code verifier is required for PKCE OAuth authentication');
    }
  
    try {
      const response = await axios.post(`${this.authURL}/token`, querystring.stringify({
        client_id: this.clientId,
        client_secret: this.clientSecret,
        redirect_uri: this.redirectUri,
        grant_type: 'authorization_code',
        code: code,
        code_verifier: codeVerifier
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
    
      const tokens = response.data;
    
      // Update the userTokens property
      this.userTokens = {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiry: new Date(Date.now() + tokens.expires_in * 1000)
      };
    
      // Update the axios instance to use the new access token
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access_token}`;
    
      return {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token,
        expiry: new Date(Date.now() + tokens.expires_in * 1000)
      };
    } catch (error) {
      console.error('Error exchanging code for tokens:', error);
      throw error;
    }
  }
  
  /**
   * Refresh the access token using the refresh token
   * @returns {Promise<Object>} New OAuth tokens
   */
  async refreshToken() {
    if (!this.clientId || !this.clientSecret) {
      throw new Error('Client ID and client secret are required for token refresh');
    }
    
    if (!this.userTokens || !this.userTokens.refreshToken) {
      throw new Error('Refresh token is required for token refresh');
    }
    
    try {
      const response = await axios.post(`${this.authURL}/token`, querystring.stringify({
        client_id: this.clientId,
        client_secret: this.clientSecret,
        grant_type: 'refresh_token',
        refresh_token: this.userTokens.refreshToken
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      const tokens = response.data;
      
      // Update the userTokens property
      this.userTokens = {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token || this.userTokens.refreshToken, // Use new refresh token if provided, otherwise keep the old one
        expiry: new Date(Date.now() + tokens.expires_in * 1000)
      };
      
      // Update the axios instance to use the new access token
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access_token}`;
      
      return {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token || this.userTokens.refreshToken,
        expiry: new Date(Date.now() + tokens.expires_in * 1000)
      };
    } catch (error) {
      console.error('Error refreshing token:', error);
      
      // Mark that we've tried to refresh to avoid infinite loops
      error.message = `${error.message} (tried to refresh)`;
      throw error;
    }
  }

  /**
   * Get designs
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of designs
   */
  async getDesigns(params = {}) {
    try {
      const response = await this.axios.get('/designs', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva designs:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error fetching designs: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get design details
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} Design details
   */
  async getDesign(designId, params = {}) {
    try {
      const response = await this.axios.get(`/designs/${designId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId}:`, error);
      throw error;
    }
  }

  /**
   * Get templates
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of templates
   */
  async getTemplates(params = {}) {
    try {
      const response = await this.axios.get('/templates', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva templates:', error);
      throw error;
    }
  }

  /**
   * Get design assets
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of assets
   */
  async getDesignAssets(designId, params = {}) {
    try {
      const response = await this.axios.get(`/designs/${designId}/assets`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId} assets:`, error);
      throw error;
    }
  }

  /**
   * Get design files
   * @param {string} designId Design ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of files
   */
  async getDesignFiles(designId, params = {}) {
    try {
      const response = await this.axios.get(`/designs/${designId}/files`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva design ${designId} files:`, error);
      throw error;
    }
  }

  /**
   * Get file details
   * @param {string} fileId File ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} File details
   */
  async getFile(fileId, params = {}) {
    try {
      const response = await this.axios.get(`/files/${fileId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva file ${fileId}:`, error);
      throw error;
    }
  }

  /**
   * Get users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  async getUsers(params = {}) {
    try {
      const response = await this.axios.get('/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Canva users:', error);
      throw error;
    }
  }

  /**
   * Get user details
   * @param {string} userId User ID
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} User details
   */
  async getUser(userId, params = {}) {
    try {
      const response = await this.axios.get(`/users/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching Canva user ${userId}:`, error);
      throw error;
    }
  }
}

module.exports = CanvaAPI;
