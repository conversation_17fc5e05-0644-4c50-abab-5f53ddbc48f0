// Constant Contact API Wrapper
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { tokenDir } = require('../../../config');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Constant Contact API Wrapper
 * Provides methods to interact with Constant Contact email marketing platform
 */
class ConstantContactAPI {
  constructor() {
    this.integrationName = 'Constant Contact';
    this.clientId = process.env.CONSTANT_CONTACT_CLIENT_ID || '';
    this.clientSecret = process.env.CONSTANT_CONTACT_CLIENT_SECRET || '';
    this.tokenFile = path.join(tokenDir, 'constant_contact_tokens.json');

    // Load tokens from file if present; fallback to env
    const fileTokens = this.loadTokensFromFile();
    this.accessToken = (fileTokens && fileTokens.access_token) || process.env.CONSTANT_CONTACT_ACCESS_TOKEN || '';
    this.refreshToken = (fileTokens && fileTokens.refresh_token) || process.env.CONSTANT_CONTACT_REFRESH_TOKEN || '';

    this.baseURL = 'https://api.cc.email/v3';
    
    // Create axios instance with default headers
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': this.accessToken ? `Bearer ${this.accessToken}` : undefined
      }
    });

    // Add a response interceptor to handle token expiration globally
    this.axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const { response, config } = error || {};
        if (!response) return Promise.reject(error);
        // Avoid infinite loop
        if ((response.status === 401 || response.status === 403) && !config?._retry) {
          const refreshed = await this.refreshAccessToken();
          if (refreshed) {
            config._retry = true;
            config.headers = config.headers || {};
            config.headers['Authorization'] = `Bearer ${this.accessToken}`;
            return this.axios(config);
          }
        }
        return Promise.reject(error);
      }
    );
  }

  loadTokensFromFile() {
    try {
      if (fs.existsSync(this.tokenFile)) {
        const raw = fs.readFileSync(this.tokenFile, 'utf-8');
        return JSON.parse(raw);
      }
    } catch (e) {
      console.warn('Failed to load Constant Contact tokens file:', e.message);
    }
    return null;
  }

  saveTokensToFile(tokens) {
    try {
      fs.writeFileSync(this.tokenFile, JSON.stringify(tokens, null, 2));
    } catch (e) {
      console.error('Failed to save Constant Contact tokens file:', e.message);
    }
  }

  /**
   * Initialize the Constant Contact API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if credentials are provided
      if (!this.clientId || !this.clientSecret || !this.accessToken) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Client ID, Client Secret, and Access Token are required for Constant Contact integration.'
        );
        return;
      }

      // Test connection by getting account info
      const accountInfo = await this.getAccountInfo();
      
      if (accountInfo) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName,
          'active',
          { accountInfo },
          'Constant Contact integration is active.'
        );
      } else {
        // Update the integration status to error
        integrationTracker.updateStatus(
          this.integrationName,
          'error',
          null,
          'Failed to connect to Constant Contact API.'
        );
      }
    } catch (error) {
      console.error('Error initializing Constant Contact API:', error);
      
      // Update the integration status to error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error initializing Constant Contact API: ${error.message}`
      );
    }
  }

  /**
   * Refresh the access token
   * @returns {Promise<boolean>} Success or failure
   */
  async refreshAccessToken() {
    try {
      if (!this.clientId || !this.clientSecret || !this.refreshToken) {
        console.error('Missing credentials for token refresh');
        return false;
      }

      const response = await axios.post('https://authz.constantcontact.com/oauth2/default/v1/token', 
        `grant_type=refresh_token&refresh_token=${encodeURIComponent(this.refreshToken)}`,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${Buffer.from(`${this.clientId}:${this.clientSecret}`).toString('base64')}`
          }
        }
      );

      if (response.data && response.data.access_token) {
        this.accessToken = response.data.access_token;
        // Rotating refresh tokens: if a new refresh_token is returned, replace and persist it
        if (response.data.refresh_token) {
          this.refreshToken = response.data.refresh_token;
        }
        // Persist tokens
        this.saveTokensToFile({
          access_token: this.accessToken,
          refresh_token: this.refreshToken,
          token_type: response.data.token_type,
          expires_in: response.data.expires_in,
          updated_at: new Date().toISOString()
        });
        
        // Update the axios instance with the new token
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.accessToken}`;
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error refreshing access token:', error.response?.data || error.message);
      return false;
    }
  }

  /**
   * Get account information
   * @returns {Promise<Object|null>} Account information or null if error
   */
  async getAccountInfo() {
    try {
      const response = await this.axios.get('/account/summary');
      return response.data;
    } catch (error) {
      // If unauthorized, try refreshing the token
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // Retry the request
          try {
            const retryResponse = await this.axios.get('/account/summary');
            return retryResponse.data;
          } catch (retryError) {
            console.error('Error getting account info after token refresh:', retryError);
            return null;
          }
        }
      }
      
      console.error('Error getting account info:', error);
      return null;
    }
  }

  /**
   * Get contact lists
   * @returns {Promise<Array|null>} Contact lists or null if error
   */
  async getContactLists() {
    try {
      const response = await this.axios.get('/contact_lists');
      const raw = Array.isArray(response.data) ? response.data : (response.data?.lists || []);
      const lists = raw.map((l) => ({
        list_id: l.list_id || l.id || l.listId || null,
        name: l.name || l.label || '',
        contact_count: l.contact_count ?? l.membership_count ?? l.member_count ?? l.active_contact_count ?? 0,
        status: l.status || l.list_status || 'active',
        created_at: l.created_at || l.created_time || l.createdDate || l.created || null
      }));
      return lists;
    } catch (error) {
      console.error('Error getting contact lists:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Get contacts
   * @param {Object} params - Query parameters
   * @param {number} params.limit - Number of contacts to return (default: 50)
   * @param {string} params.status - Contact status (all, active, removed, unconfirmed)
   * @returns {Promise<Object|null>} Contacts response or null if error
   */
  async getContacts(params = {}) {
    try {
      const response = await this.axios.get('/contacts', { params });
      const data = response.data || {};
      const raw = Array.isArray(data) ? data : (Array.isArray(data.contacts) ? data.contacts : []);
      const contacts = raw.map((c) => {
        const emailStr = typeof c.email_address === 'string'
          ? c.email_address
          : (c.email_address?.address || (Array.isArray(c.email_addresses) ? c.email_addresses[0]?.address : undefined) || c.email || '');
        return {
          contact_id: c.contact_id || c.id || c.contactId || null,
          email_address: emailStr,
          first_name: c.first_name || c.given_name || '',
          last_name: c.last_name || c.family_name || '',
          status: c.status || c.permission_to_send || '',
          created_at: c.created_at || c.created_time || c.created || null,
          updated_at: c.updated_at || c.modified_at || c.last_updated || null,
          list_memberships: c.list_memberships || c.lists || []
        };
      });
      return { contacts };
    } catch (error) {
      console.error('Error getting contacts:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Get contact by email address
   * @param {string} email - Email address
   * @returns {Promise<Object|null>} Contact or null if error or not found
   */
  async getContactByEmail(email) {
    try {
      const response = await this.axios.get(`/contacts`, {
        params: {
          email: email
        }
      });
      const list = Array.isArray(response.data) ? response.data : (response.data?.contacts || []);
      if (list.length > 0) {
        const c = list[0];
        const emailStr = typeof c.email_address === 'string'
          ? c.email_address
          : (c.email_address?.address || (Array.isArray(c.email_addresses) ? c.email_addresses[0]?.address : undefined) || c.email || '');
        return {
          contact_id: c.contact_id || c.id || c.contactId || null,
          email_address: emailStr,
          first_name: c.first_name || c.given_name || '',
          last_name: c.last_name || c.family_name || '',
          status: c.status || c.permission_to_send || '',
          created_at: c.created_at || c.created_time || c.created || null,
          updated_at: c.updated_at || c.modified_at || c.last_updated || null,
          list_memberships: c.list_memberships || c.lists || []
        };
      }
      return null;
    } catch (error) {
      console.error('Error getting contact by email:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Create a new contact
   * @param {Object} contact - Contact data
   * @param {string} contact.email_address - Email address
   * @param {string} contact.first_name - First name
   * @param {string} contact.last_name - Last name
   * @param {Array} contact.list_memberships - List IDs to add the contact to
   * @returns {Promise<Object|null>} Created contact or null if error
   */
  async createContact(contact) {
    try {
      const response = await this.axios.post('/contacts', contact);
      return response.data;
    } catch (error) {
      console.error('Error creating contact:', error);
      return null;
    }
  }

  /**
   * Update an existing contact
   * @param {string} contactId - Contact ID
   * @param {Object} contact - Updated contact data
   * @returns {Promise<Object|null>} Updated contact or null if error
   */
  async updateContact(contactId, contact) {
    try {
      const response = await this.axios.put(`/contacts/${contactId}`, contact);
      return response.data;
    } catch (error) {
      console.error('Error updating contact:', error);
      return null;
    }
  }

  /**
   * Delete a contact
   * @param {string} contactId - Contact ID
   * @returns {Promise<boolean>} Success or failure
   */
  async deleteContact(contactId) {
    try {
      await this.axios.delete(`/contacts/${contactId}`);
      return true;
    } catch (error) {
      console.error('Error deleting contact:', error);
      return false;
    }
  }

  /**
   * Get campaigns
   * @param {Object} params - Query parameters
   * @param {number} params.limit - Number of campaigns to return (default: 50)
   * @param {string} params.status - Campaign status (all, draft, scheduled, sent)
   * @returns {Promise<Object|null>} Campaigns response or null if error
   */
  async getCampaigns(params = {}) {
    try {
      const response = await this.axios.get('/emails', { params });
      const data = response.data || {};
      const raw = Array.isArray(data)
        ? data
        : (Array.isArray(data.campaigns) ? data.campaigns : (Array.isArray(data.emails) ? data.emails : []));
      const campaigns = raw.map((c) => ({
        campaign_id: c.campaign_id || c.id || c.campaignId || null,
        name: c.name || c.subject || c.title || '',
        status: c.status || c.current_status || c.state || '',
        created_at: c.created_at || c.created_time || c.created || c.createdDate || null,
        updated_at: c.updated_at || c.modified_at || c.last_updated || c.updatedDate || null
      }));
      return { campaigns };
    } catch (error) {
      console.error('Error getting campaigns:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * Get campaign by ID
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<Object|null>} Campaign or null if error
   */
  async getCampaign(campaignId) {
    try {
      const response = await this.axios.get(`/emails/${campaignId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting campaign:', error);
      return null;
    }
  }

  /**
   * Get campaign tracking data
   * @param {string} campaignId - Campaign ID
   * @returns {Promise<Object|null>} Campaign tracking data or null if error
   */
  async getCampaignTracking(campaignId) {
    try {
      const response = await this.axios.get(`/emails/${campaignId}/tracking_summary`);
      const d = response.data || {};
      const sends = d.sends ?? d.sends_count ?? d.sent ?? d.total_sends ?? 0;
      const opens = d.opens ?? d.opens_count ?? d.unique_opens ?? d.unique_opens_count ?? 0;
      const clicks = d.clicks ?? d.clicks_count ?? d.unique_clicks ?? d.unique_clicks_count ?? 0;
      const bounces = d.bounces ?? d.bounces_count ?? d.bounce_count ?? 0;
      const unsubscribes = d.unsubscribes ?? d.unsubscribes_count ?? d.opt_outs ?? d.opt_outs_count ?? 0;
      const pct = (count) => (sends > 0 ? `${((count / sends) * 100).toFixed(1)}%` : '0%');
      return {
        sends,
        opens,
        opens_rate: pct(opens),
        clicks,
        clicks_rate: pct(clicks),
        bounces,
        bounces_rate: pct(bounces),
        unsubscribes,
        unsubscribes_rate: pct(unsubscribes)
      };
    } catch (error) {
      console.error('Error getting campaign tracking:', error.response?.data || error.message);
      return null;
    }
  }
}

module.exports = ConstantContactAPI;