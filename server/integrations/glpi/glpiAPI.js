// GLPI API Wrapper
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * GLPI API Wrapper
 * Documentation: https://github.com/glpi-project/glpi/blob/master/apirest.md
 */
class GLPIAPI {
  constructor(url, appToken, userToken = null) {
    this.url = url;
    this.appToken = appToken;
    this.userToken = userToken;
    this.sessionToken = null;
    this.integrationName = 'GLPI';
    this.axios = axios.create({
      baseURL: url,
      headers: {
        'Content-Type': 'application/json',
        'App-Token': appToken
      }
    });
  }

  /**
   * Initialize the GLPI API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we have valid credentials
      if (!this.url || !this.appToken) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'URL and App Token are required for GLPI integration.'
        );
        return;
      }

      // Check if we can authenticate
      try {
        if (this.userToken) {
          await this.initSessionByUserToken();
        } else {
          // We can't authenticate without credentials, so mark as needs_auth
          integrationTracker.updateStatus(
            this.integrationName, 
            'needs_auth', 
            null, 
            'User token or credentials are required for authentication.'
          );
          return;
        }

        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated and ready to use.'
        );
      } catch (authError) {
        // Update the integration status to indicate authentication failed
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `Authentication failed: ${authError.message}`
        );
        throw authError;
      }
    } catch (error) {
      console.error('Error initializing GLPI API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Initialize the session with GLPI
   * @param {string} username - GLPI username
   * @param {string} password - GLPI password
   * @returns {Promise<string>} Session token
   */
  async initSessionByCredentials(username, password) {
    try {
      const response = await this.axios.get('/initSession', {
        auth: {
          username,
          password
        }
      });

      if (response.data && response.data.session_token) {
        this.sessionToken = response.data.session_token;
        this.axios.defaults.headers.common['Session-Token'] = this.sessionToken;
        return this.sessionToken;
      } else {
        throw new Error('Failed to initialize session: No session token received');
      }
    } catch (error) {
      console.error('Error initializing session by credentials:', error);
      throw error;
    }
  }

  /**
   * Initialize the session with GLPI using user token
   * @returns {Promise<string>} Session token
   */
  async initSessionByUserToken() {
    try {
      if (!this.userToken) {
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'User token is required for authentication.'
        );

        throw new Error('User token is required');
      }

      const response = await this.axios.get('/initSession', {
        headers: {
          'Authorization': `user_token ${this.userToken}`
        }
      });

      if (response.data && response.data.session_token) {
        this.sessionToken = response.data.session_token;
        this.axios.defaults.headers.common['Session-Token'] = this.sessionToken;

        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Successfully authenticated with GLPI.'
        );

        return this.sessionToken;
      } else {
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          'Failed to initialize session: No session token received'
        );

        throw new Error('Failed to initialize session: No session token received');
      }
    } catch (error) {
      console.error('Error initializing session by user token:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Authentication error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Kill the current session
   * @returns {Promise<boolean>} Success status
   */
  async killSession() {
    try {
      if (!this.sessionToken) {
        return true;
      }

      await this.axios.get('/killSession');
      this.sessionToken = null;
      delete this.axios.defaults.headers.common['Session-Token'];
      return true;
    } catch (error) {
      console.error('Error killing session:', error);
      throw error;
    }
  }

  /**
   * Get GLPI profile information
   * @returns {Promise<Object>} Profile information
   */
  async getMyProfiles() {
    try {
      this.checkSession();
      const response = await this.axios.get('/getMyProfiles');
      return response.data;
    } catch (error) {
      console.error('Error getting profiles:', error);
      throw error;
    }
  }

  /**
   * Get all assets
   * @param {Object} options - Query options
   * @returns {Promise<Array>} List of assets
   */
  async getAssets(options = {}) {
    try {
      this.checkSession();
      
      // Default to Computer if no itemtype is specified
      const itemtype = options.itemtype || 'Computer';
      
      // Remove itemtype from options to avoid duplicate params
      const params = { ...options };
      delete params.itemtype;
      
      const response = await this.axios.get(`/${itemtype}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error getting assets:', error);
      throw error;
    }
  }

  /**
   * Get asset by ID
   * @param {number} id - Asset ID
   * @returns {Promise<Object>} Asset details
   */
  async getAsset(id) {
    try {
      this.checkSession();
      const response = await this.axios.get(`/Computer/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error getting asset with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Search assets
   * @param {Object} criteria - Search criteria
   * @returns {Promise<Array>} Search results
   */
  async searchAssets(criteria) {
    try {
      this.checkSession();
      
      // Default to Computer if no itemtype is specified
      const itemtype = criteria.itemtype || 'Computer';
      
      // Create a copy of the criteria to avoid modifying the original
      const params = { ...criteria };
      delete params.itemtype;
      
      // Handle asset tag search if specified
      if (params.asset_tag) {
        // Add specific criteria for asset tag search
        params.criteria = [
          {
            field: 'otherserial', // GLPI field for asset tag/inventory number
            searchtype: 'contains',
            value: params.asset_tag
          }
        ];
        delete params.asset_tag;
      }
      
      const response = await this.axios.get(`/search/${itemtype}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error searching assets:', error);
      throw error;
    }
  }

  /**
   * Get all asset types
   * @returns {Promise<Array>} List of asset types
   */
  async getAssetTypes() {
    try {
      this.checkSession();
      const response = await this.axios.get('/ComputerType');
      return response.data;
    } catch (error) {
      console.error('Error getting asset types:', error);
      throw error;
    }
  }

  /**
   * Get all asset models
   * @returns {Promise<Array>} List of asset models
   */
  async getAssetModels() {
    try {
      this.checkSession();
      const response = await this.axios.get('/ComputerModel');
      return response.data;
    } catch (error) {
      console.error('Error getting asset models:', error);
      throw error;
    }
  }

  /**
   * Get all asset manufacturers
   * @returns {Promise<Array>} List of asset manufacturers
   */
  async getAssetManufacturers() {
    try {
      this.checkSession();
      const response = await this.axios.get('/Manufacturer');
      return response.data;
    } catch (error) {
      console.error('Error getting asset manufacturers:', error);
      throw error;
    }
  }

  /**
   * Get all asset categories
   * @returns {Promise<Array>} List of asset categories
   */
  async getAssetCategories() {
    try {
      this.checkSession();
      const response = await this.axios.get('/ITILCategory');
      return response.data;
    } catch (error) {
      console.error('Error getting asset categories:', error);
      throw error;
    }
  }

  /**
   * Create a new asset
   * @param {Object} assetData - Asset data
   * @returns {Promise<Object>} Created asset
   */
  async createAsset(assetData) {
    try {
      this.checkSession();
      const response = await this.axios.post('/Computer', assetData);
      return response.data;
    } catch (error) {
      console.error('Error creating asset:', error);
      throw error;
    }
  }

  /**
   * Update an asset
   * @param {number} id - Asset ID
   * @param {Object} assetData - Asset data
   * @returns {Promise<Object>} Updated asset
   */
  async updateAsset(id, assetData) {
    try {
      this.checkSession();
      const response = await this.axios.put(`/Computer/${id}`, assetData);
      return response.data;
    } catch (error) {
      console.error(`Error updating asset with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete an asset
   * @param {number} id - Asset ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteAsset(id) {
    try {
      this.checkSession();
      await this.axios.delete(`/Computer/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting asset with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Check if session is active
   * @throws {Error} If session is not active
   */
  checkSession() {
    if (!this.sessionToken) {
      throw new Error('No active session. Please initialize session first.');
    }
  }
}

module.exports = GLPIAPI;
