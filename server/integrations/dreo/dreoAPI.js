// Dreo API Wrapper
const axios = require('axios');
const crypto = require('crypto');
const WebSocket = require('ws');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Dreo API Wrapper
 * Based on homebridge-dreo implementation: https://github.com/zyonse/homebridge-dreo
 */
class DreoAPI {
  constructor(email, password) {
    this.email = email;
    this.password = password;
    this.server = 'us'; // Default to US server
    this.token = null;
    this.ws = null;
    this.wsConnected = false;
    this.integrationName = 'Dreo';
    this.userAgent = 'dreo/2.8.1 (iPhone; iOS 18.0.0; Scale/3.00)';
    this.deviceListeners = new Map(); // Map to store device listeners for WebSocket events
    
    // Create axios instance with common headers
    this.axios = axios.create({
      headers: {
        'ua': this.userAgent,
        'lang': 'en',
        'content-type': 'application/json; charset=UTF-8',
        'accept-encoding': 'gzip',
        'user-agent': 'okhttp/4.9.1',
      }
    });
  }

  /**
   * Initialize the Dreo API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we can authenticate
      const isAuthenticated = await this.isAuthenticated();

      if (isAuthenticated) {
        // Initialize WebSocket connection for real-time control
        await this.initializeWebSocket();
        
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated and ready to use.'
        );
      } else {
        // Update the integration status to indicate authentication is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Authentication failed. Check credentials and try again.'
        );
      }
    } catch (error) {
      console.error('Error initializing Dreo API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {Promise<boolean>} Authentication status
   */
  async isAuthenticated() {
    try {
      if (this.token) {
        return true;
      }

      await this.authenticate();
      return !!this.token;
    } catch (error) {
      console.error('Authentication check failed:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Authentication error: ${error.message}`
      );

      return false;
    }
  }

  /**
   * Authenticate with Dreo API
   * @returns {Promise<object>} Authentication data including token
   */
  async authenticate() {
    try {
      // Hash the password with MD5 as per the Dreo API requirements
      const hashedPassword = crypto.createHash('md5').update(this.password).digest('hex');
      
      const response = await axios.post(`https://app-api-${this.server}.dreo-cloud.com/api/oauth/login`, {
        'client_id': 'd8a56a73d93b427cad801116dc4d3188',
        'client_secret': '2ac9b179f7e84be58bb901d6ed8bf374',
        'email': this.email,
        'encrypt': 'ciphertext',
        'grant_type': 'email-password',
        'himei': '463299817f794e52a228868167df3f34',
        'password': hashedPassword,
        'scope': 'all',
      }, {
        params: {
          'timestamp': Date.now(),
        },
        headers: {
          'ua': this.userAgent,
          'lang': 'en',
          'content-type': 'application/json; charset=UTF-8',
          'accept-encoding': 'gzip',
          'user-agent': 'okhttp/4.9.1',
        },
      });
      
      const payload = response.data;
      
      if (payload.data && payload.data.access_token) {
        // Auth success
        const auth = payload.data;
        this.token = auth.access_token;
        
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Successfully authenticated with Dreo API.'
        );
        
        return auth;
      } else {
        console.error('Error retrieving token:', payload.msg);
        
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `Authentication error: ${payload.msg}`
        );
        
        throw new Error(payload.msg || 'Unknown authentication error');
      }
    } catch (error) {
      console.error('Error authenticating with Dreo API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Authentication error: ${error.message}`
      );

      throw error;
    }
  }
  
  /**
   * Initialize WebSocket connection for real-time control
   * @returns {Promise<void>}
   */
  async initializeWebSocket() {
    try {
      if (!this.token) {
        throw new Error('Authentication token is required for WebSocket connection');
      }
      
      // Close existing connection if any
      if (this.ws) {
        this.ws.terminate();
        this.ws = null;
      }
      
      // Create WebSocket URL with authentication token
      const url = `wss://wsb-${this.server}.dreo-cloud.com/websocket?accessToken=${this.token}&timestamp=${Date.now()}`;
      
      // Create new WebSocket connection
      this.ws = new WebSocket(url);
      
      // Set up event handlers
      this.ws.on('open', () => {
        console.log('Dreo WebSocket connection established');
        this.wsConnected = true;
        
        // Set up keepalive interval (send '2' every 15 seconds)
        this.keepAliveInterval = setInterval(() => {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send('2');
          }
        }, 15000);
      });
      
      this.ws.on('message', (data) => {
        try {
          // Ignore keepalive responses
          if (data.toString() === '2') {
            return;
          }
          
          // Parse message data
          const message = JSON.parse(data);
          
          // If this is a device update, notify listeners
          if (message.deviceSn && message.params) {
            const deviceId = message.deviceSn;
            const listener = this.deviceListeners.get(deviceId);
            
            if (listener) {
              listener(message.params);
            }
          }
        } catch (err) {
          console.error('Error processing WebSocket message:', err);
        }
      });
      
      this.ws.on('error', (error) => {
        console.error('Dreo WebSocket error:', error);
        this.wsConnected = false;
      });
      
      this.ws.on('close', () => {
        console.log('Dreo WebSocket connection closed');
        this.wsConnected = false;
        
        // Clear keepalive interval
        if (this.keepAliveInterval) {
          clearInterval(this.keepAliveInterval);
          this.keepAliveInterval = null;
        }
        
        // Attempt to reconnect after a delay
        setTimeout(() => {
          if (!this.wsConnected) {
            this.initializeWebSocket().catch(err => {
              console.error('Error reconnecting to Dreo WebSocket:', err);
            });
          }
        }, 5000);
      });
      
      // Wait for connection to be established
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);
        
        this.ws.once('open', () => {
          clearTimeout(timeout);
          resolve();
        });
        
        this.ws.once('error', (err) => {
          clearTimeout(timeout);
          reject(err);
        });
      });
    } catch (error) {
      console.error('Error initializing Dreo WebSocket:', error);
      throw error;
    }
  }

  /**
   * Get all devices
   * @returns {Promise<Array>} List of devices
   */
  async getDevices() {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      const response = await axios.get(`https://app-api-${this.server}.dreo-cloud.com/api/app/index/family/room/devices`, {
        params: {
          'timestamp': Date.now(),
        },
        headers: {
          'authorization': 'Bearer ' + this.token,
          'ua': this.userAgent,
          'lang': 'en',
          'accept-encoding': 'gzip',
          'user-agent': 'okhttp/4.9.1',
        },
      });
      
      if (response.data && response.data.data && response.data.data.list) {
        return response.data.data.list;
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching Dreo devices:', error);
      throw error;
    }
  }

  /**
   * Get device state/details
   * @param {string} deviceId Device ID (serial number)
   * @returns {Promise<Object>} Device state
   */
  async getDeviceDetails(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
    
      // First, get the device state
      const stateResponse = await axios.get(`https://app-api-${this.server}.dreo-cloud.com/api/user-device/device/state`, {
        params: {
          'deviceSn': deviceId,
          'timestamp': Date.now(),
        },
        headers: {
          'authorization': 'Bearer ' + this.token,
          'ua': this.userAgent,
          'lang': 'en',
          'accept-encoding': 'gzip',
          'user-agent': 'okhttp/4.9.1',
        },
      });
    
      if (stateResponse.data && stateResponse.data.data && stateResponse.data.data.mixed) {
        const deviceState = stateResponse.data.data.mixed;
        
        // Process power status - ensure it's a boolean value
        // Different devices might use different properties for power status
        if (deviceState.power !== undefined) {
          // Convert numeric power value to boolean
          deviceState.power = Boolean(deviceState.power);
        } else if (deviceState.poweron !== undefined) {
          // Some devices use 'poweron' instead of 'power'
          deviceState.power = Boolean(deviceState.poweron);
        } else if (deviceState.on !== undefined) {
          // Some devices might use 'on'
          deviceState.power = Boolean(deviceState.on);
        }
        
        // Get the full device details from the devices list
        try {
          const devices = await this.getDevices();
          const device = devices.find(d => d.id === deviceId || d.sn === deviceId);
          
          if (device) {
            // Create a comprehensive device details object
            const deviceDetails = {
              deviceId: device.id || deviceId,
              sn: device.sn || deviceId,
              brand: device.brand || 'Dreo',
              model: device.model || '',
              productId: device.productId || '',
              productName: device.productName || '',
              deviceName: device.deviceName || device.name || '',
              shared: device.shared || false,
              series: device.series || null,
              seriesName: device.seriesName || '',
              type: device.type || 0,
              owner: device.owner || true,
              familyId: device.familyId || null,
              familyName: device.familyName || '',
              roomId: device.roomId || '',
              roomName: device.roomName || '',
              roomNameI18Key: device.roomNameI18Key || '',
              color: device.color || '',
              widgetOnImage: device.widgetOnImage || '',
              widgetOffImage: device.widgetOffImage || '',
              widgetAbnormalImage: device.widgetAbnormalImage || null,
              variantIconMd5: device.variantIconMd5 || null,
              
              // Include the full controlsConf from the device
              controlsConf: device.controlsConf || {},
              
              // Include mainConf, resourcesConf, servicesConf if available
              mainConf: device.mainConf || {
                isSmart: true,
                isWifi: true,
                isBluetooth: true,
                isVoiceControl: true
              },
              
              resourcesConf: device.resourcesConf || {
                imageSmallSrc: device.imageSmallSrc || '',
                imageFullSrc: device.imageFullSrc || '',
                imageSmallDarkSrc: device.imageSmallDarkSrc || null,
                imageFullDarkSrc: device.imageFullDarkSrc || null
              },
              
              servicesConf: device.servicesConf || [],
              
              // Include user manuals if available
              userManuals: device.userManuals || [],
              
              // Add device state and capabilities
              state: deviceState,
              capabilities: this.extractDeviceCapabilities(device, deviceState)
            };
            
            // Ensure power status is available at the top level
            if (deviceState.power !== undefined) {
              deviceDetails.power = deviceState.power;
            }
            
            // Cache device capabilities for future use
            if (!this.deviceCapabilities) {
              this.deviceCapabilities = {};
            }
            
            this.deviceCapabilities[deviceId] = {
              type: device.deviceType,
              model: device.model,
              brand: device.brand,
              name: device.deviceName,
              controlsConf: device.controlsConf || {},
              capabilities: deviceDetails.capabilities
            };
            
            // Add device capabilities to the state for backward compatibility
            deviceState.deviceInfo = this.deviceCapabilities[deviceId];
            
            return deviceDetails;
          }
        } catch (err) {
          console.error(`Error fetching comprehensive device details for ${deviceId}:`, err);
          // Continue with basic device state if we can't get the full details
        }
        
        // Add device capabilities to the response if available
        if (this.deviceCapabilities && this.deviceCapabilities[deviceId]) {
          deviceState.deviceInfo = this.deviceCapabilities[deviceId];
        }
        
        // Ensure power status is available at the top level for basic device state
        if (deviceState.power === undefined && deviceState.poweron !== undefined) {
          deviceState.power = Boolean(deviceState.poweron);
        } else if (deviceState.power === undefined && deviceState.on !== undefined) {
          deviceState.power = Boolean(deviceState.on);
        } else if (deviceState.power !== undefined) {
          deviceState.power = Boolean(deviceState.power);
        }
        
        return deviceState;
      }
    
      throw new Error('Invalid device state response');
    } catch (error) {
      console.error(`Error fetching Dreo device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Extract device capabilities from device information and state
   * @param {Object} device Device information
   * @param {Object} state Device state
   * @returns {Object} Device capabilities
   */
  extractDeviceCapabilities(device, state) {
    const capabilities = {
      power: true, // All devices support power control
      temperature: false,
      fanSpeed: false,
      mode: false,
      oscillation: false,
      oscillationAngle: false,
      childLock: false,
      light: false,
      temperatureUnit: false,
      temperatureSensor: false
    };
  
    // Check for capabilities based on device controls configuration
    if (device.controlsConf && device.controlsConf.control) {
      // Check for fan speed control
      const speedControl = device.controlsConf.control.find(
        (params) => params.type === 'Speed'
      );
      if (speedControl) {
        capabilities.fanSpeed = true;
        capabilities.maxSpeed = speedControl.items[1].text;
      }
    
      // Check for oscillation control
      const oscillationControl = device.controlsConf.control.find(
        (params) => params.type === 'Oscillation'
      );
      if (oscillationControl) {
        capabilities.oscillation = true;
        capabilities.oscillationCmd = oscillationControl.cmd;
      }
    
      // Check for mode control
      const modeControl = device.controlsConf.control.find(
        (params) => params.type === 'Mode'
      );
      if (modeControl) {
        capabilities.mode = true;
        capabilities.availableModes = modeControl.items.map(item => item.value);
      }
    }
  
    // Check for capabilities based on device state
    if (state) {
      // Check for temperature control
      if (state.temperature !== undefined) {
        capabilities.temperatureSensor = true;
      }
    
      // Check for eco temperature control (for heaters)
      if (state.ecolevel !== undefined) {
        capabilities.temperature = true;
      }
    
      // Check for child lock
      if (state.childlockon !== undefined) {
        capabilities.childLock = true;
      }
    
      // Check for light control
      if (state.lighton !== undefined && state.brightness !== undefined) {
        capabilities.light = true;
      }
    
      // Check for oscillation angle control
      if (state.oscangle !== undefined) {
        capabilities.oscillationAngle = true;
      } else if (state.oscon !== undefined) {
        capabilities.oscillation = true;
      } else if (state.shakehorizon !== undefined) {
        capabilities.oscillation = true;
        capabilities.oscillationCmd = 'shakehorizon';
      } else if (state.hoscon !== undefined) {
        capabilities.oscillation = true;
        capabilities.oscillationCmd = 'hoscon';
      } else if (state.oscmode !== undefined) {
        capabilities.oscillation = true;
        capabilities.oscillationCmd = 'oscmode';
      }
    
      // Check for temperature unit control
      if (state.tempunit !== undefined) {
        capabilities.temperatureUnit = true;
      }
    }
  
    return capabilities;
  }
  
  /**
   * Register a listener for device state changes
   * @param {string} deviceId Device ID
   * @param {Function} listener Callback function to be called when device state changes
   */
  registerDeviceListener(deviceId, listener) {
    if (typeof listener === 'function') {
      this.deviceListeners.set(deviceId, listener);
    }
  }
  
  /**
   * Unregister a device listener
   * @param {string} deviceId Device ID
   */
  unregisterDeviceListener(deviceId) {
    this.deviceListeners.delete(deviceId);
  }

  /**
   * Control device with custom commands
   * @param {string} deviceId Device ID (serial number)
   * @param {Object} command Command object with parameters
   * @returns {Promise<boolean>} Success status
   */
  async controlDevice(deviceId, command) {
    try {
      if (!this.token) {
        await this.authenticate();
      }
      
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        await this.initializeWebSocket();
      }
      
      // Send command through WebSocket
      this.ws.send(JSON.stringify({
        'deviceSn': deviceId,
        'method': 'control',
        'params': command,
        'timestamp': Date.now(),
      }));
      
      // Return success (WebSocket doesn't provide immediate response)
      return true;
    } catch (error) {
      console.error(`Error controlling Dreo device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device power state
   * @param {string} deviceId Device ID
   * @param {boolean} power Power state (true = on, false = off)
   * @returns {Promise<boolean>} Success status
   */
  async setPower(deviceId, power) {
    try {
      return await this.controlDevice(deviceId, {
        power: power ? 1 : 0
      });
    } catch (error) {
      console.error(`Error setting Dreo device power for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device temperature
   * @param {string} deviceId Device ID
   * @param {number} temperature Temperature in Celsius
   * @returns {Promise<boolean>} Success status
   */
  async setTemperature(deviceId, temperature) {
    try {
      return await this.controlDevice(deviceId, {
        temperature: temperature
      });
    } catch (error) {
      console.error(`Error setting Dreo device temperature for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device fan speed
   * @param {string} deviceId Device ID
   * @param {number} speed Fan speed (1-5)
   * @returns {Promise<boolean>} Success status
   */
  async setFanSpeed(deviceId, speed) {
    try {
      return await this.controlDevice(deviceId, {
        fanSpeed: speed
      });
    } catch (error) {
      console.error(`Error setting Dreo device fan speed for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device mode
   * @param {string} deviceId Device ID
   * @param {string} mode Device mode (cool, fan, dry)
   * @returns {Promise<boolean>} Success status
   */
  async setMode(deviceId, mode) {
    try {
      return await this.controlDevice(deviceId, {
        mode: mode
      });
    } catch (error) {
      console.error(`Error setting Dreo device mode for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device oscillation/swing state
   * @param {string} deviceId Device ID
   * @param {boolean|number} state Oscillation state (true/false or 0/1)
   * @returns {Promise<boolean>} Success status
   */
  async setOscillation(deviceId, state) {
    try {
      // Get device capabilities to determine the correct oscillation command
      const deviceDetails = await this.getDeviceDetails(deviceId);
      const deviceInfo = deviceDetails.deviceInfo;
    
      if (!deviceInfo || !deviceInfo.capabilities || !deviceInfo.capabilities.oscillation) {
        throw new Error('Device does not support oscillation');
      }
    
      const command = {};
      const oscillationCmd = deviceInfo.capabilities.oscillationCmd || 'oscon';
    
      // Different devices use different commands for oscillation
      if (oscillationCmd === 'oscmode') {
        // Some devices use numeric values (0/1) for oscillation
        command[oscillationCmd] = typeof state === 'boolean' ? (state ? 1 : 0) : state;
      } else {
        // Most devices use boolean values for oscillation
        command[oscillationCmd] = typeof state === 'number' ? Boolean(state) : state;
      }
    
      return await this.controlDevice(deviceId, command);
    } catch (error) {
      console.error(`Error setting Dreo device oscillation for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device oscillation angle
   * @param {string} deviceId Device ID
   * @param {number} angle Oscillation angle (60, 90, 120)
   * @returns {Promise<boolean>} Success status
   */
  async setOscillationAngle(deviceId, angle) {
    try {
      // Get device capabilities to check if oscillation angle is supported
      const deviceDetails = await this.getDeviceDetails(deviceId);
      const deviceInfo = deviceDetails.deviceInfo;
    
      if (!deviceInfo || !deviceInfo.capabilities || !deviceInfo.capabilities.oscillationAngle) {
        throw new Error('Device does not support oscillation angle control');
      }
    
      // Validate angle (common values are 60, 90, 120)
      if (![60, 90, 120].includes(angle)) {
        throw new Error('Invalid oscillation angle. Supported values are 60, 90, 120');
      }
    
      return await this.controlDevice(deviceId, {
        oscangle: angle
      });
    } catch (error) {
      console.error(`Error setting Dreo device oscillation angle for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device child lock state
   * @param {string} deviceId Device ID
   * @param {boolean|number} state Child lock state (true/false or 0/1)
   * @returns {Promise<boolean>} Success status
   */
  async setChildLock(deviceId, state) {
    try {
      // Get device capabilities to check if child lock is supported
      const deviceDetails = await this.getDeviceDetails(deviceId);
      const deviceInfo = deviceDetails.deviceInfo;
    
      if (!deviceInfo || !deviceInfo.capabilities || !deviceInfo.capabilities.childLock) {
        throw new Error('Device does not support child lock');
      }
    
      // Some devices use numeric values (0/1) for child lock
      const lockState = typeof state === 'boolean' ? (state ? 1 : 0) : state;
    
      return await this.controlDevice(deviceId, {
        childlockon: lockState
      });
    } catch (error) {
      console.error(`Error setting Dreo device child lock for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device light state
   * @param {string} deviceId Device ID
   * @param {boolean} state Light state (true = on, false = off)
   * @returns {Promise<boolean>} Success status
   */
  async setLight(deviceId, state) {
    try {
      // Get device capabilities to check if light is supported
      const deviceDetails = await this.getDeviceDetails(deviceId);
      const deviceInfo = deviceDetails.deviceInfo;
    
      if (!deviceInfo || !deviceInfo.capabilities || !deviceInfo.capabilities.light) {
        throw new Error('Device does not support light control');
      }
    
      return await this.controlDevice(deviceId, {
        lighton: state
      });
    } catch (error) {
      console.error(`Error setting Dreo device light for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device light brightness
   * @param {string} deviceId Device ID
   * @param {number} brightness Brightness level (0-100)
   * @returns {Promise<boolean>} Success status
   */
  async setLightBrightness(deviceId, brightness) {
    try {
      // Get device capabilities to check if light is supported
      const deviceDetails = await this.getDeviceDetails(deviceId);
      const deviceInfo = deviceDetails.deviceInfo;
    
      if (!deviceInfo || !deviceInfo.capabilities || !deviceInfo.capabilities.light) {
        throw new Error('Device does not support light control');
      }
    
      // Validate brightness
      if (brightness < 0 || brightness > 100) {
        throw new Error('Brightness must be between 0 and 100');
      }
    
      return await this.controlDevice(deviceId, {
        brightness: brightness
      });
    } catch (error) {
      console.error(`Error setting Dreo device light brightness for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device temperature unit
   * @param {string} deviceId Device ID
   * @param {string|number} unit Temperature unit ('C'/'F' or 1/2, where 1=F, 2=C)
   * @returns {Promise<boolean>} Success status
   */
  async setTemperatureUnit(deviceId, unit) {
    try {
      // Get device capabilities to check if temperature unit selection is supported
      const deviceDetails = await this.getDeviceDetails(deviceId);
      const deviceInfo = deviceDetails.deviceInfo;
    
      if (!deviceInfo || !deviceInfo.capabilities || !deviceInfo.capabilities.temperatureUnit) {
        throw new Error('Device does not support temperature unit selection');
      }
    
      // Convert string unit to number (Dreo API uses 1 for F, 2 for C)
      let unitValue;
      if (typeof unit === 'string') {
        unitValue = unit.toUpperCase() === 'F' ? 1 : 2;
      } else {
        unitValue = unit === 1 ? 1 : 2;
      }
    
      return await this.controlDevice(deviceId, {
        tempunit: unitValue
      });
    } catch (error) {
      console.error(`Error setting Dreo device temperature unit for ${deviceId}:`, error);
      throw error;
    }
  }
}

module.exports = DreoAPI;
