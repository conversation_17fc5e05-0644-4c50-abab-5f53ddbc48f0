{"title": "Advanced Settings", "description": "Configure advanced settings for the Dreo integration", "fields": [{"id": "debugMode", "label": "Debug Mode", "type": "boolean", "defaultValue": false, "helperText": "Enable debug logging for troubleshooting"}, {"id": "customEndpoint", "label": "Custom API Endpoint", "type": "text", "defaultValue": "", "helperText": "Override the default API endpoint (leave blank to use default)", "fullWidth": true}, {"id": "deviceFilter", "label": "<PERSON><PERSON>", "type": "text", "multiline": true, "rows": 3, "defaultValue": "", "helperText": "Enter device IDs to filter (one per line, leave blank for all devices)", "fullWidth": true}]}