{"title": "General Settings", "description": "Configure general settings for the Dreo integration", "fields": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "label": "API Key", "type": "text", "defaultValue": "", "helperText": "Enter your Dreo API key", "required": true, "fullWidth": true}, {"id": "refreshInterval", "label": "Refresh Interval", "type": "select", "defaultValue": "15", "helperText": "How often to refresh data from Dreo (in minutes)", "options": [{"value": "5", "label": "5 minutes"}, {"value": "15", "label": "15 minutes"}, {"value": "30", "label": "30 minutes"}, {"value": "60", "label": "1 hour"}]}, {"id": "enableNotifications", "label": "Enable Notifications", "type": "boolean", "defaultValue": true, "helperText": "Receive notifications for Dreo events"}]}