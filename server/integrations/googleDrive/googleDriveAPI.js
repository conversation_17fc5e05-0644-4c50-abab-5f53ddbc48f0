// Google Drive API Wrapper
const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { refreshAndSaveTokens } = require('../../utils/tokenRefresh');
const integrationTracker = require('../../utils/integrationTracker');
const { getAuthenticatedClient } = require('../../utils/googleServiceAuth');

/**
 * Google Drive API Wrapper
 * Documentation: https://developers.google.com/drive/api/v3/reference
 */
class GoogleDriveAPI {
  constructor(clientId, clientSecret, redirectUri, tokenPath, userTokens = null, userId = null, userEmail = null) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.tokenPath = tokenPath;
    this.userTokens = userTokens;
    this.userId = userId;
    this.userEmail = userEmail;
    this.scopes = [
      'https://www.googleapis.com/auth/drive',
      'https://www.googleapis.com/auth/drive.file',
      'https://www.googleapis.com/auth/drive.metadata'
    ];
    this.auth = null;
    this.drive = null;
    this.usingServiceAccount = false;
  }

  /**
   * Initialize the Google Drive API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const integrationName = 'Google Drive';
      
      // Always use service account authentication
      try {
        console.log('Using service account authentication for Google Drive API');
        
        // If no user email is provided, throw an error
        if (!this.userEmail) {
          console.error('Google Drive API initialization failed: No user email provided for service account authentication');
          throw new Error('No user email provided for service account authentication');
        }
        
        // Check service account credentials with detailed logging
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL) {
          console.error('Google Drive API initialization failed: GOOGLE_SERVICE_ACCOUNT_EMAIL environment variable is missing');
          throw new Error('Service account email is missing. Please check your environment variables.');
        }
        
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY) {
          console.error('Google Drive API initialization failed: GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY environment variable is missing');
          throw new Error('Service account private key is missing. Please check your environment variables.');
        }
        
        // Log the service account email being used (but not the private key for security)
        console.log(`Attempting to authenticate with service account: ${process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL}`);
        console.log(`Impersonating user: ${this.userEmail}`);
        
        try {
          this.auth = await getAuthenticatedClient('Drive', this.scopes, this.userEmail);
          this.drive = google.drive({ version: 'v3', auth: this.auth });
          this.usingServiceAccount = true;
          
          console.log('Google Drive API successfully authenticated with service account');
          
          // Update the integration status to active
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Integration is properly authenticated using service account.'
          );
          
          return;
        } catch (authError) {
          console.error('Google Drive API authentication failed with service account:', authError);
          console.error('Authentication error details:', JSON.stringify({
            message: authError.message,
            code: authError.code,
            status: authError.status,
            response: authError.response ? {
              status: authError.response.status,
              statusText: authError.response.statusText,
              data: authError.response.data
            } : 'No response data'
          }, null, 2));
          throw authError;
        }
      } catch (serviceAccountError) {
        console.error('Error initializing with service account:', serviceAccountError);
        
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          integrationName, 
          'error', 
          null, 
          `Error with service account: ${serviceAccountError.message}`
        );
        
        throw serviceAccountError;
      }
    } catch (error) {
      console.error('Error initializing Google Drive API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        'Google Drive', 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Generate authentication URL for OAuth2 flow
   * @returns {string} Authentication URL
   */
  getAuthUrl() {
    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: this.scopes,
      prompt: 'consent'
    });
  }

  /**
   * Get access token from authorization code
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Token object
   */
  async getToken(code) {
    try {
      const integrationName = 'Google Drive';
      const { tokens } = await this.auth.getToken(code);
      this.auth.setCredentials(tokens);

      // Save token to file for future use
      fs.writeFileSync(this.tokenPath, JSON.stringify(tokens));

      // Update the integration status to active
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'active', 
          new Date(), 
          'Successfully obtained new authentication token.'
        );
      }

      return tokens;
    } catch (error) {
      console.error('Error getting token:', error);

      // Update the integration status to indicate an error
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          'Google Drive', 
          'error', 
          null, 
          `Error getting token: ${error.message}`
        );
      }

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    const integrationName = 'Google Drive';

    // If using service account, check if auth and drive are initialized
    if (this.usingServiceAccount) {
      const isAuthenticated = !!(this.auth && this.drive);
      
      // Update the tracker based on the authentication status
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        if (isAuthenticated) {
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Integration is properly authenticated with service account and ready to use.'
          );
        } else {
          integrationTracker.updateStatus(
            integrationName, 
            'error', 
            null, 
            'Service account authentication is not properly configured.'
          );
        }
      }
      
      return isAuthenticated;
    }
    
    // For OAuth authentication, check if we have a token
    if (!this.auth || !this.auth.credentials || !this.auth.credentials.access_token) {
      // Update the tracker if we're not authenticated
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'not_configured', 
          null, 
          'No valid authentication token found.'
        );
      }
      return false;
    }

    // Then check if the token has all the required scopes
    const currentScopes = this.getScopes();
    const hasAllScopes = this.scopes.every(scope => 
      currentScopes.includes(scope) || 
      // Handle case where token has broader scope that includes our required scope
      currentScopes.some(currentScope => scope.startsWith(currentScope))
    );

    // Update the tracker based on the authentication status
    if (!this.userTokens && hasAllScopes) { // Only update for global tokens, not user-specific ones
      integrationTracker.updateStatus(
        integrationName, 
        'active', 
        new Date(), 
        'Integration is properly authenticated and ready to use.'
      );
    } else if (!this.userTokens && !hasAllScopes) {
      integrationTracker.updateStatus(
        integrationName, 
        'needs_auth', 
        null, 
        'Token does not have all required scopes. Need to re-authenticate.'
      );
    }

    return hasAllScopes;
  }

  /**
   * Get the current scopes from the token
   * @returns {Array} List of scopes
   */
  getScopes() {
    if (this.auth && this.auth.credentials && this.auth.credentials.scope) {
      return this.auth.credentials.scope.split(' ');
    }
    return [];
  }

  /**
   * List files in Google Drive
   * @param {Object} options Query options
   * @param {string} userEmail User's email address to filter files by
   * @returns {Promise<Array>} List of files
   */
  async listFiles(options = {}, userEmail = null) {
    try {
      console.log('Google Drive API: Attempting to list files');
      
      if (!this.isAuthenticated()) {
        console.error('Google Drive API: Authentication check failed in listFiles');
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        pageSize: 30,
        fields: 'nextPageToken, files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime, size, parents, owners)'
      };

      // If userEmail is provided, add a query to filter files by user's access
      if (userEmail) {
        // Build the query to filter files the user has access to
        const userQuery = `'${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers`;
        console.log(`Google Drive API: Filtering files for user: ${userEmail}`);
        
        // Combine with existing query if present
        if (options.q) {
          options.q = `(${options.q}) and (${userQuery})`;
        } else {
          options.q = userQuery;
        }
      } else {
        console.warn('Google Drive API: No user email provided for filtering files');
      }

      console.log(`Google Drive API: Listing files with options: ${JSON.stringify({
        ...defaultOptions,
        ...options,
        // Don't log the full query as it might contain sensitive information
        q: options.q ? 'Query present (filtered)' : 'No query'
      })}`);

      try {
        const result = await this.executeWithTokenRefresh(async () => {
          const response = await this.drive.files.list({
            ...defaultOptions,
            ...options
          });
          
          console.log(`Google Drive API: Successfully retrieved ${response.data.files.length} files`);
          return response.data.files;
        });
        
        return result;
      } catch (apiError) {
        console.error('Google Drive API: Error executing files.list API call:', apiError);
        console.error('Google Drive API: Error details:', JSON.stringify({
          message: apiError.message,
          code: apiError.code,
          status: apiError.status,
          response: apiError.response ? {
            status: apiError.response.status,
            statusText: apiError.response.statusText,
            data: apiError.response.data
          } : 'No response data'
        }, null, 2));
        
        // Add more context to the error
        const enhancedError = new Error(`Failed to list files from Google Drive: ${apiError.message}`);
        enhancedError.originalError = apiError;
        enhancedError.context = {
          usingServiceAccount: this.usingServiceAccount,
          userEmail: this.userEmail,
          isAuthenticated: this.isAuthenticated()
        };
        throw enhancedError;
      }
    } catch (error) {
      console.error('Google Drive API: Error in listFiles method:', error);
      throw error;
    }
  }

  /**
   * Get file metadata
   * @param {string} fileId File ID
   * @returns {Promise<Object>} File metadata
   */
  async getFile(fileId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.drive.files.get({
          fileId,
          fields: 'id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime, size, parents, owners'
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting file:', error);
      throw error;
    }
  }

  /**
   * Search for files in Google Drive
   * @param {string} query Search query
   * @param {Object} options Additional options
   * @param {string} userEmail User's email address to filter files by
   * @returns {Promise<Array>} List of files
   */
  async searchFiles(query, options = {}, userEmail = null) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        pageSize: 30,
        fields: 'nextPageToken, files(id, name, mimeType, webViewLink, iconLink, thumbnailLink, createdTime, modifiedTime, size, parents, owners)',
        q: query
      };

      // If userEmail is provided, add a query to filter files by user's access
      if (userEmail) {
        // Build the query to filter files the user has access to
        const userQuery = `'${userEmail}' in owners or '${userEmail}' in writers or '${userEmail}' in readers`;
        
        // Combine with existing query
        defaultOptions.q = `(${defaultOptions.q}) and (${userQuery})`;
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.drive.files.list({
          ...defaultOptions,
          ...options
        });
        return response.data.files;
      });
    } catch (error) {
      console.error('Error searching files:', error);
      throw error;
    }
  }

  /**
   * Get a shareable link for a file
   * @param {string} fileId File ID
   * @returns {Promise<string>} Shareable link
   */
  async getShareableLink(fileId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      // This already uses executeWithTokenRefresh via getFile
      const file = await this.getFile(fileId);
      return file.webViewLink;
    } catch (error) {
      console.error('Error getting shareable link:', error);
      throw error;
    }
  }

  /**
   * Get an embedded viewer URL for Google Docs, Sheets, Slides
   * @param {string} fileId File ID
   * @returns {string} Embedded viewer URL
   */
  getEmbeddedViewerUrl(fileId) {
    return `https://docs.google.com/viewer?srcid=${fileId}&pid=explorer&efh=false&a=v&chrome=false&embedded=true`;
  }

  /**
   * Get an embedded editor URL for Google Docs, Sheets, Slides
   * @param {string} fileId File ID
   * @param {string} mimeType File MIME type
   * @returns {string} Embedded editor URL
   */
  getEmbeddedEditorUrl(fileId, mimeType) {
    // Default to document editor if mime type is not provided
    if (!mimeType) {
      return `https://docs.google.com/document/d/${fileId}/edit?usp=drivesdk&embedded=true`;
    }

    // Determine the appropriate editor URL based on mime type
    if (mimeType === 'application/vnd.google-apps.document') {
      return `https://docs.google.com/document/d/${fileId}/edit?usp=drivesdk&embedded=true`;
    } else if (mimeType === 'application/vnd.google-apps.spreadsheet') {
      return `https://docs.google.com/spreadsheets/d/${fileId}/edit?usp=drivesdk&embedded=true`;
    } else if (mimeType === 'application/vnd.google-apps.presentation') {
      return `https://docs.google.com/presentation/d/${fileId}/edit?usp=drivesdk&embedded=true`;
    } else if (mimeType === 'application/vnd.google-apps.drawing') {
      return `https://docs.google.com/drawings/d/${fileId}/edit?usp=drivesdk&embedded=true`;
    } else if (mimeType === 'application/vnd.google-apps.form') {
      return `https://docs.google.com/forms/d/${fileId}/edit?usp=drivesdk&embedded=true`;
    } else {
      // For other file types, return the viewer URL as they may not be editable
      return this.getEmbeddedViewerUrl(fileId);
    }
  }

  /**
   * Execute an API request with automatic token refresh if needed
   * @param {Function} apiCall - Function that makes the API call
   * @returns {Promise<any>} API response
   */
  async executeWithTokenRefresh(apiCall) {
    try {
      // First attempt to execute the API call
      return await apiCall();
    } catch (error) {
      const integrationName = 'Google Drive';

      // Check if the error is due to an expired token
      const isAuthError = 
        error.code === 401 || 
        error.code === 403 || 
        (error.response && (error.response.status === 401 || error.response.status === 403)) ||
        (error.message && (
          error.message.includes('invalid_grant') || 
          error.message.includes('Invalid Credentials') || 
          error.message.includes('token expired')
        ));

      // If it's an auth error and we have a refresh token, try to refresh
      if (isAuthError && this.auth && this.userId) {
        try {
          console.log('Token expired, attempting to refresh...');

          // Refresh the token
          await refreshAndSaveTokens(this.auth, this.userId);

          // Update the integration status to indicate token was refreshed
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'active', 
              new Date(), 
              'Token was successfully refreshed.'
            );
          }

          // Retry the API call with the new token
          return await apiCall();
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // Update the integration status to indicate an error with token refresh
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'error', 
              null, 
              `Error refreshing token: ${refreshError.message}`
            );
          }

          throw refreshError;
        }
      } else if (isAuthError) {
        // It's an auth error but we can't refresh the token
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'needs_auth', 
            null, 
            'Authentication error occurred and token could not be refreshed. Need to re-authenticate.'
          );
        }
        throw error;
      } else {
        // If it's not an auth error, rethrow the original error
        throw error;
      }
    }
  }
}

module.exports = GoogleDriveAPI;
