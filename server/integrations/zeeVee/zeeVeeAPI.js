// ZeeVee HDbridge 2920-NA API Wrapper
const axios = require('axios');
const https = require('https');
const http = require('http');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Helper function to implement retry logic with exponential backoff
 * @param {Function} apiCall - Async function that makes the API call
 * @param {Object} options - Options for retry behavior
 * @param {number} options.maxRetries - Maximum number of retry attempts (default: 3)
 * @param {number} options.initialDelay - Initial delay in ms before first retry (default: 1000)
 * @param {number} options.maxDelay - Maximum delay in ms between retries (default: 10000)
 * @param {Function} options.shouldRetry - Function to determine if retry should be attempted (default: retry on 503)
 * @returns {Promise<any>} - Result of the API call
 */
async function withRetry(apiCall, options = {}) {
  const maxRetries = options.maxRetries || 3;
  const initialDelay = options.initialDelay || 1000;
  const maxDelay = options.maxDelay || 10000;
  const shouldRetry = options.shouldRetry || ((error) => error.response && error.response.status === 503);
  
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      // Check if we should retry
      if (attempt < maxRetries && shouldRetry(error)) {
        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          maxDelay,
          initialDelay * Math.pow(2, attempt) * (0.9 + Math.random() * 0.2)
        );
        
        console.log(`ZeeVee API request failed with ${error.response?.status || 'unknown'} status. Retrying in ${Math.round(delay)}ms (attempt ${attempt + 1}/${maxRetries})...`);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // We've exhausted retries or shouldn't retry this error
        throw error;
      }
    }
  }
  
  // This should never be reached due to the throw in the loop, but just in case
  throw lastError;
}

/**
 * ZeeVee HDbridge 2920-NA API Wrapper
 */
class ZeeVeeAPI {
  constructor(host, port = 80, username = '', password = '') {
    this.host = host;
    this.port = port;
    this.username = username;
    this.password = password;

    // Set base URL
    this.baseURL = `http://${host}:${port}`;
    
    this.integrationName = 'ZeeVee HDbridge';
    
    // Create HTTP agent with improved configuration for better connection handling
    const httpAgent = new http.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000 // Free socket timeout
    });
    
    // Create HTTPS agent with configuration to allow self-signed certificates
    const httpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000, // Free socket timeout
      rejectUnauthorized: false // Allow self-signed SSL certificates
    });

    // Create axios instance for HTTP communication with ZeeVee device
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive',
        'User-Agent': 'CSFPortal-ZeeVee-Client/1.0'
      },
      // Add timeout configuration to prevent socket hang up errors
      timeout: 10000, // 10 seconds
      // Use the improved HTTP agent for HTTP requests
      httpAgent: httpAgent,
      // Use the HTTPS agent with self-signed certificate support for HTTPS requests
      httpsAgent: httpsAgent,
      // Disable automatic retries (we'll handle them manually)
      maxRedirects: 0,
      // Validate status codes
      validateStatus: (status) => status >= 200 && status < 300,
      // Include auth credentials if provided
      auth: (this.username && this.password) ? {
        username: this.username,
        password: this.password
      } : undefined
    });

    // Add request interceptor for logging and retry logic
    this.axios.interceptors.request.use(
      (config) => {
        console.log(`ZeeVee API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('ZeeVee API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.axios.interceptors.response.use(
      (response) => {
        console.log(`ZeeVee API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const config = error.config;

        // Log detailed error information
        console.error('ZeeVee API Response Error:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          url: config?.url,
          method: config?.method,
          timeout: config?.timeout,
          retryCount: config.__retryCount || 0
        });

        // Implement retry logic for specific errors
        if (this.shouldRetry(error) && (!config.__retryCount || config.__retryCount < 3)) {
          config.__retryCount = (config.__retryCount || 0) + 1;
          
          // Calculate delay with exponential backoff and jitter
          const delay = Math.min(
            10000, // Max delay of 10 seconds
            1000 * Math.pow(2, config.__retryCount - 1) * (0.9 + Math.random() * 0.2)
          );
          
          console.log(`Retrying ZeeVee API request in ${Math.round(delay)}ms (attempt ${config.__retryCount}/3)...`);
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Retry the request
          return this.axios(config);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Determine if a request should be retried based on the error
   * @param {Error} error - The error that occurred
   * @returns {boolean} - Whether the request should be retried
   */
  shouldRetry(error) {
    // Retry on connection errors, timeouts, and 5xx server errors
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNABORTED' ||
      error.code === 'ETIMEDOUT' ||
      (error.response && error.response.status >= 500 && error.response.status < 600)
    );
  }

  /**
   * Initialize the ZeeVee API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we can make a test API call
      const isConfigured = await this.isConfigured();

      if (isConfigured) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly configured and ready to use.'
        );
      } else {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Configuration validation failed. Check host, port, username, and password settings.'
        );
      }
    } catch (error) {
      console.error('Error initializing ZeeVee API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Check if the client is properly configured
   * @returns {Promise<boolean>} Configuration status
   */
  async isConfigured() {
    try {
      if (!this.host) {
        return false;
      }

      // Make a test API call to verify the connection works
      await this.getDeviceInfo();
      return true;
    } catch (error) {
      console.error('ZeeVee configuration validation failed:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Configuration validation error: ${error.message}`
      );

      return false;
    }
  }

  /**
   * Get device information
   * @returns {Promise<Object>} Device information
   */
  async getDeviceInfo() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get('/api/device/info');
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching ZeeVee device information:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee device information: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get('/api/system/status');
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching ZeeVee system status:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee system status: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get all encoders
   * @returns {Promise<Array>} List of encoders
   */
  async getEncoders() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get('/api/encoders');
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching ZeeVee encoders:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee encoders: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get encoder details
   * @param {string} encoderId Encoder ID
   * @returns {Promise<Object>} Encoder details
   */
  async getEncoderDetails(encoderId) {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get(`/api/encoders/${encoderId}`);
        return response.data;
      });
    } catch (error) {
      console.error(`Error fetching ZeeVee encoder details for ${encoderId}:`, error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee encoder details for ${encoderId}: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get all decoders
   * @returns {Promise<Array>} List of decoders
   */
  async getDecoders() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get('/api/decoders');
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching ZeeVee decoders:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee decoders: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get decoder details
   * @param {string} decoderId Decoder ID
   * @returns {Promise<Object>} Decoder details
   */
  async getDecoderDetails(decoderId) {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get(`/api/decoders/${decoderId}`);
        return response.data;
      });
    } catch (error) {
      console.error(`Error fetching ZeeVee decoder details for ${decoderId}:`, error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee decoder details for ${decoderId}: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get all video channels
   * @returns {Promise<Array>} List of video channels
   */
  async getChannels() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get('/api/channels');
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching ZeeVee channels:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee channels: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get channel details
   * @param {string} channelId Channel ID
   * @returns {Promise<Object>} Channel details
   */
  async getChannelDetails(channelId) {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get(`/api/channels/${channelId}`);
        return response.data;
      });
    } catch (error) {
      console.error(`Error fetching ZeeVee channel details for ${channelId}:`, error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee channel details for ${channelId}: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Set encoder input
   * @param {string} encoderId Encoder ID
   * @param {string} input Input source
   * @returns {Promise<Object>} Operation result
   */
  async setEncoderInput(encoderId, input) {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.put(`/api/encoders/${encoderId}/input`, { input });
        return response.data;
      });
    } catch (error) {
      console.error(`Error setting ZeeVee encoder input for ${encoderId}:`, error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error setting ZeeVee encoder input for ${encoderId}: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Set decoder output
   * @param {string} decoderId Decoder ID
   * @param {string} channelId Channel ID to display
   * @returns {Promise<Object>} Operation result
   */
  async setDecoderOutput(decoderId, channelId) {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.put(`/api/decoders/${decoderId}/channel`, { channelId });
        return response.data;
      });
    } catch (error) {
      console.error(`Error setting ZeeVee decoder output for ${decoderId}:`, error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error setting ZeeVee decoder output for ${decoderId}: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Reboot device
   * @returns {Promise<Object>} Operation result
   */
  async rebootDevice() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.post('/api/system/reboot');
        return response.data;
      });
    } catch (error) {
      console.error('Error rebooting ZeeVee device:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error rebooting ZeeVee device: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get network settings
   * @returns {Promise<Object>} Network settings
   */
  async getNetworkSettings() {
    try {
      // Use withRetry to handle errors with exponential backoff
      return await withRetry(async () => {
        const response = await this.axios.get('/api/network/settings');
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching ZeeVee network settings:', error);
      
      // Provide more specific error message for errors after retries
      if (error.response) {
        const enhancedError = new Error(`Error fetching ZeeVee network settings: ${error.response.status} ${error.response.statusText}. Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get health status
   * @returns {Promise<Object>} Health status
   */
  async getHealthStatus() {
    try {
      // Get device info to check health
      const deviceInfo = await this.getDeviceInfo();
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        details: {
          deviceInfo
        }
      };
    } catch (error) {
      console.error('Error checking ZeeVee health status:', error);
      
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }
}

module.exports = ZeeVeeAPI;