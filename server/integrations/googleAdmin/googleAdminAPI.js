// Google Admin API Wrapper
const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { refreshAndSaveTokens } = require('../../utils/tokenRefresh');
const integrationTracker = require('../../utils/integrationTracker');
const { getAuthenticatedClient } = require('../../utils/googleServiceAuth');

/**
 * Google Admin API Wrapper
 * Documentation: https://developers.google.com/admin-sdk/directory/reference/rest
 * 
 * This wrapper supports two authentication methods:
 * 
 * 1. OAuth Authentication:
 *    - Requires user interaction to complete the authentication flow
 *    - Uses client ID, client secret, and redirect URI from environment variables
 *    - Stores the authentication token in a file for future use
 *    - Suitable for development environments and when user interaction is acceptable
 * 
 * 2. Service Account Authentication:
 *    - Uses a service account with domain-wide delegation
 *    - Requires service account email, private key, and impersonation email from environment variables
 *    - No token file or user interaction required
 *    - Suitable for production environments and automated systems
 * 
 * Environment variables:
 * - For OAuth: GOOGLE_ADMIN_CLIENT_ID, GOOGLE_ADMIN_CLIENT_SECRET, GOOGLE_ADMIN_REDIRECT_URI, GOOGLE_ADMIN_TOKEN_PATH
 * - For Service Account: GOOGLE_ADMIN_SERVICE_ACCOUNT_EMAIL, GOOGLE_ADMIN_SERVICE_ACCOUNT_PRIVATE_KEY, GOOGLE_ADMIN_IMPERSONATION_EMAIL
 * 
 * For detailed setup instructions, see GOOGLE_ADMIN_AUTHENTICATION.md
 */
class GoogleAdminAPI {
  /**
   * Constructor for GoogleAdminAPI
   * @param {string} clientId - OAuth client ID (for OAuth authentication)
   * @param {string} clientSecret - OAuth client secret (for OAuth authentication)
   * @param {string} redirectUri - OAuth redirect URI (for OAuth authentication)
   * @param {string} tokenPath - Path to store OAuth token (for OAuth authentication)
   * @param {Object} userTokens - User-specific OAuth tokens (optional)
   * @param {string} userId - User ID for token refresh (optional)
   * @param {string} userEmail - Email of the user to impersonate with service account (optional)
   */
  constructor(clientId, clientSecret, redirectUri, tokenPath, userTokens = null, userId = null, userEmail = null) {
    // OAuth authentication properties
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.tokenPath = tokenPath;
    this.userTokens = userTokens;
    this.userId = userId;
    this.userEmail = userEmail;
    
    // Service account authentication properties (from environment variables)
    // Use standardized common service account variables
    this.serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || null;
    this.serviceAccountKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY || null;
    
    // For impersonation, prefer the user email passed to the constructor
    this.impersonationEmail = this.userEmail || process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL || null;
    
    // Determine if we're using service account authentication
    // We need service account email, key, and an impersonation email
    this.usingServiceAccount = !!(this.serviceAccountEmail && this.serviceAccountKey && this.impersonationEmail);
    
    // API scopes required for both authentication methods
    this.scopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group',
      'https://www.googleapis.com/auth/admin.directory.user.security'
    ];
    
    // These will be initialized in the initialize() method
    this.auth = null;
    this.admin = null;
  }

  /**
   * Initialize the Google Admin API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const integrationName = 'Google Admin';
      
      // Try service account authentication first
      try {
        console.log('Using service account authentication for Google Admin API');
        
        // If no user email is provided, use the impersonation email from environment variables
        const emailToUse = this.userEmail || this.impersonationEmail;
        
        if (!emailToUse) {
          throw new Error('No user email or impersonation email provided for service account authentication');
        }
        
        // Check if service account credentials are available
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY) {
          throw new Error('Service account credentials are missing. Please check GOOGLE_SERVICE_ACCOUNT_EMAIL and GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY environment variables.');
        }
        
        this.auth = await getAuthenticatedClient('Admin', this.scopes, emailToUse);
        
        // Update the integration status to active
        integrationTracker.updateStatus(
          integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated using service account.'
        );
        
        this.admin = google.admin({version: 'directory_v1', auth: this.auth});
        this.usingServiceAccount = true;
        return;
      } catch (serviceAccountError) {
        console.error('Error initializing with service account:', serviceAccountError);
        
        // Update the integration status to indicate an error with service account
        integrationTracker.updateStatus(
          integrationName, 
          'warning', 
          null, 
          `Service account authentication failed: ${serviceAccountError.message}. Trying OAuth fallback.`
        );
        
        // Fall back to OAuth authentication if service account fails
        try {
          console.log('Falling back to OAuth authentication for Google Admin API');
          
          // Check if OAuth credentials are available
          if (!this.clientId || !this.clientSecret || !this.redirectUri) {
            throw new Error('OAuth credentials are missing. Please check GOOGLE_ADMIN_CLIENT_ID, GOOGLE_ADMIN_CLIENT_SECRET, and GOOGLE_ADMIN_REDIRECT_URI environment variables.');
          }
          
          // Create OAuth2 client
          this.auth = new google.auth.OAuth2(
            this.clientId,
            this.clientSecret,
            this.redirectUri
          );
          
          // Check if we have user tokens or if token file exists
          if (this.userTokens) {
            // Use user-specific tokens
            this.auth.setCredentials(this.userTokens);
          } else if (fs.existsSync(this.tokenPath)) {
            // Read token from file
            const token = JSON.parse(fs.readFileSync(this.tokenPath));
            this.auth.setCredentials(token);
          } else {
            // No tokens available, will need to authenticate
            integrationTracker.updateStatus(
              integrationName, 
              'needs_auth', 
              null, 
              'OAuth authentication is required. No valid token found.'
            );
            
            // Still create the admin client, but it won't work until authenticated
            this.admin = google.admin({version: 'directory_v1', auth: this.auth});
            this.usingServiceAccount = false;
            return;
          }
          
          // Update the integration status to active
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Integration is properly authenticated using OAuth.'
          );
          
          this.admin = google.admin({version: 'directory_v1', auth: this.auth});
          this.usingServiceAccount = false;
          return;
        } catch (oauthError) {
          console.error('Error initializing with OAuth:', oauthError);
          
          // Update the integration status to indicate an error with OAuth
          integrationTracker.updateStatus(
            integrationName, 
            'error', 
            null, 
            `OAuth authentication failed: ${oauthError.message}`
          );
          
          // Throw a combined error message
          throw new Error(`Service account authentication failed: ${serviceAccountError.message}. OAuth fallback also failed: ${oauthError.message}`);
        }
      }
    } catch (error) {
      console.error('Error initializing Google Admin API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        'Google Admin', 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Generate authentication URL for OAuth2 flow
   * @returns {string} Authentication URL
   */
  getAuthUrl() {
    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: this.scopes,
      prompt: 'consent'
    });
  }

  /**
   * Get access token from authorization code
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Token object
   */
  async getToken(code) {
    try {
      const integrationName = 'Google Admin';
      const { tokens } = await this.auth.getToken(code);
      this.auth.setCredentials(tokens);

      // Save token to file for future use
      fs.writeFileSync(this.tokenPath, JSON.stringify(tokens));

      // Update the integration status to active
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'active', 
          new Date(), 
          'Successfully obtained new authentication token.'
        );
      }

      return tokens;
    } catch (error) {
      console.error('Error getting token:', error);

      // Update the integration status to indicate an error
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          'Google Admin', 
          'error', 
          null, 
          `Error getting token: ${error.message}`
        );
      }

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    const integrationName = 'Google Admin';

    // If using service account and auth is initialized, we're authenticated
    if (this.usingServiceAccount && this.auth) {
      // For JWT auth, we don't need to check scopes as they're specified during creation
      return true;
    }

    // For OAuth authentication, check if we have a token
    if (!this.auth || !this.auth.credentials || !this.auth.credentials.access_token) {
      // Update the tracker if we're not authenticated
      if (!this.userTokens && !this.usingServiceAccount) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'not_configured', 
          null, 
          'No valid authentication token found.'
        );
      }
      return false;
    }

    // Then check if the token has all the required scopes
    const currentScopes = this.getScopes();
    const hasAllScopes = this.scopes.every(scope => 
      currentScopes.includes(scope) || 
      // Handle case where token has broader scope that includes our required scope
      currentScopes.some(currentScope => scope.startsWith(currentScope))
    );

    // Update the tracker based on the authentication status
    if (!this.userTokens && hasAllScopes) { // Only update for global tokens, not user-specific ones
      integrationTracker.updateStatus(
        integrationName, 
        'active', 
        new Date(), 
        'Integration is properly authenticated and ready to use.'
      );
    } else if (!this.userTokens && !hasAllScopes) {
      integrationTracker.updateStatus(
        integrationName, 
        'needs_auth', 
        null, 
        'Token does not have all required scopes. Need to re-authenticate.'
      );
    }

    return hasAllScopes;
  }

  /**
   * Get the current scopes from the token
   * @returns {Array} List of scopes
   */
  getScopes() {
    if (this.auth && this.auth.credentials && this.auth.credentials.scope) {
      return this.auth.credentials.scope.split(' ');
    }
    return [];
  }

  /**
   * List users in the domain
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of users
   */
  async listUsers(options = {}) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        maxResults: 100,
        orderBy: 'email',
        customer: 'my_customer' // Required parameter for listing users in the authenticated user's domain
      };

      // Log the request parameters for debugging
      console.log('Listing users with parameters:', {
        ...defaultOptions,
        ...options
      });

      return await this.executeWithTokenRefresh(async () => {
        const allUsers = [];
        let pageToken = undefined;
        let safetyCounter = 0;
        const MAX_PAGES = 1000; // safety to avoid infinite loops

        do {
          const response = await this.admin.users.list({
            ...defaultOptions,
            ...options,
            pageToken
          });

          const users = response.data && response.data.users ? response.data.users : [];
          if (users.length) {
            allUsers.push(...users);
          }

          pageToken = response.data ? response.data.nextPageToken : undefined;
          safetyCounter += 1;
        } while (pageToken && safetyCounter < MAX_PAGES);

        if (safetyCounter >= MAX_PAGES) {
          console.warn('GoogleAdminAPI.listUsers: Reached safety page limit; possible pagination loop.');
        }

        return allUsers;
      });
    } catch (error) {
      console.error('Error listing users:', error);
      throw error;
    }
  }

  /**
   * Get user information
   * @param {string} userKey User email or ID
   * @returns {Promise<Object>} User information
   */
  async getUser(userKey) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.users.get({
          userKey
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting user:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param {Object} userData User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.users.insert({
          requestBody: userData
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update user information
   * @param {string} userKey User email or ID
   * @param {Object} userData User data to update
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userKey, userData) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.users.update({
          userKey,
          requestBody: userData
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete a user
   * @param {string} userKey User email or ID
   * @returns {Promise<void>}
   */
  async deleteUser(userKey) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      await this.executeWithTokenRefresh(async () => {
        await this.admin.users.delete({
          userKey
        });
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * List groups in the domain
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of groups
   */
  async listGroups(options = {}) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        maxResults: 100,
        customer: 'my_customer' // Required parameter for listing groups in the authenticated user's domain
      };

      // Log the request parameters for debugging
      console.log('Listing groups with parameters:', {
        ...defaultOptions,
        ...options
      });

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.groups.list({
          ...defaultOptions,
          ...options
        });
        // Add null check to handle case when no groups are returned
        return response.data.groups || [];
      });
    } catch (error) {
      console.error('Error listing groups:', error);
      throw error;
    }
  }

  /**
   * Get group information
   * @param {string} groupKey Group email or ID
   * @returns {Promise<Object>} Group information
   */
  async getGroup(groupKey) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.groups.get({
          groupKey
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting group:', error);
      throw error;
    }
  }

  /**
   * Add user to a group
   * @param {string} groupKey Group email or ID
   * @param {string} userEmail User email
   * @returns {Promise<Object>} Member information
   */
  async addUserToGroup(groupKey, userEmail) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.members.insert({
          groupKey,
          requestBody: {
            email: userEmail,
            role: 'MEMBER'
          }
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error adding user to group:', error);
      throw error;
    }
  }

  /**
   * Remove user from a group
   * @param {string} groupKey Group email or ID
   * @param {string} memberKey Member email or ID
   * @returns {Promise<void>}
   */
  async removeUserFromGroup(groupKey, memberKey) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      await this.executeWithTokenRefresh(async () => {
        await this.admin.members.delete({
          groupKey,
          memberKey
        });
      });
    } catch (error) {
      console.error('Error removing user from group:', error);
      throw error;
    }
  }

  /**
   * List groups a user belongs to
   * @param {string} userKey User email or ID
   * @returns {Promise<Array>} List of groups the user belongs to
   */
  async listUserGroups(userKey) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.groups.list({
          userKey
        });
        return response.data.groups || [];
      });
    } catch (error) {
      console.error('Error listing user groups:', error);
      throw error;
    }
  }

  /**
   * Check if a user is a member of a specific group
   * @param {string} userEmail User email
   * @param {string} groupEmail Group email
   * @returns {Promise<boolean>} True if the user is a member of the group, false otherwise
   */
  async isUserMemberOfGroup(userEmail, groupEmail) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        try {
          // First, try to use the members.hasMember method if available
          const response = await this.admin.members.hasMember({
            groupKey: groupEmail,
            memberKey: userEmail
          });
          return response.data.isMember || false;
        } catch (error) {
          // If the hasMember method is not available or fails, fall back to listing the user's groups
          console.log(`Falling back to listing user groups for ${userEmail} to check membership in ${groupEmail}`);
          const groups = await this.listUserGroups(userEmail);
          return groups.some(group => group.email.toLowerCase() === groupEmail.toLowerCase());
        }
      });
    } catch (error) {
      console.error(`Error checking if user ${userEmail} is a member of group ${groupEmail}:`, error);
      // Return false instead of throwing to avoid breaking the role assignment process
      return false;
    }
  }

  /**
   * List members of a specific group
   * @param {string} groupKey Group email or ID
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of group members
   */
  async listGroupMembers(groupKey, options = {}) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        maxResults: 200
      };

      // Log the request parameters for debugging
      console.log('Listing group members with parameters:', {
        groupKey,
        ...defaultOptions,
        ...options
      });

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.admin.members.list({
          groupKey,
          ...defaultOptions,
          ...options
        });
        // Add null check to handle case when no members are returned
        return response.data.members || [];
      });
    } catch (error) {
      console.error('Error listing group members:', error);
      throw error;
    }
  }

  /**
   * Execute an API request with automatic token refresh if needed
   * @param {Function} apiCall - Function that makes the API call
   * @returns {Promise<any>} API response
   */
  async executeWithTokenRefresh(apiCall) {
    try {
      // First attempt to execute the API call
      return await apiCall();
    } catch (error) {
      const integrationName = 'Google Admin';

      // Check if the error is due to an expired token
      const isAuthError = 
        error.code === 401 || 
        error.code === 403 || 
        (error.response && (error.response.status === 401 || error.response.status === 403)) ||
        (error.message && (
          error.message.includes('invalid_grant') || 
          error.message.includes('Invalid Credentials') || 
          error.message.includes('token expired')
        ));

      // If using service account and we get an auth error, try to re-authorize
      if (isAuthError && this.usingServiceAccount && this.auth) {
        try {
          console.log('Service account token expired, attempting to re-authorize...');
          
          // Re-authorize the JWT client
          await this.auth.authorize();
          
          // Update the integration status
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Service account was successfully re-authorized.'
          );
          
          // Retry the API call with the new token
          return await apiCall();
        } catch (authError) {
          console.error('Error re-authorizing service account:', authError);
          
          // Update the integration status
          integrationTracker.updateStatus(
            integrationName, 
            'error', 
            null, 
            `Error re-authorizing service account: ${authError.message}`
          );
          
          throw authError;
        }
      }
      // If it's an auth error and we have a refresh token, try to refresh
      else if (isAuthError && this.auth && this.userId) {
        try {
          console.log('Token expired, attempting to refresh...');

          // Refresh the token
          await refreshAndSaveTokens(this.auth, this.userId);

          // Update the integration status to indicate token was refreshed
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'active', 
              new Date(), 
              'Token was successfully refreshed.'
            );
          }

          // Retry the API call with the new token
          return await apiCall();
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // Update the integration status to indicate an error with token refresh
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'error', 
              null, 
              `Error refreshing token: ${refreshError.message}`
            );
          }

          throw refreshError;
        }
      } else if (isAuthError) {
        // It's an auth error but we can't refresh the token
        if (!this.userTokens && !this.usingServiceAccount) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'needs_auth', 
            null, 
            'Authentication error occurred and token could not be refreshed. Need to re-authenticate.'
          );
        }
        throw error;
      } else {
        // If it's not an auth error, rethrow the original error
        throw error;
      }
    }
  }
}

module.exports = GoogleAdminAPI;
