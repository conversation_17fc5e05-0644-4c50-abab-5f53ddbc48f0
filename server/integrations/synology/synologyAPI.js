// Synology API Wrapper
const axios = require('axios');
const querystring = require('querystring');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Synology API Wrapper
 * Documentation: 
 * - File Station API: https://global.download.synology.com/download/Document/Software/DeveloperGuide/Package/FileStation/All/enu/Synology_File_Station_API_Guide.pdf
 * - DSM Login Web API: https://kb.synology.com/en-us/DG/DSM_Login_Web_API_Guide/2#x_anchor_iddbcc293edb
 */
class SynologyAPI {
  constructor(host, port, username, password, secure = true) {
    this.host = host;
    this.port = port;
    this.username = username;
    this.password = password;
    this.secure = secure;
    
    // Check if host already includes protocol
    let baseHost = host;
    if (host.startsWith('http://') || host.startsWith('https://')) {
      // Extract the host without protocol
      baseHost = host.replace(/^https?:\/\//, '');
    }
    
    this.baseURL = `${secure ? 'https' : 'http'}://${baseHost}:${port}/webapi`;
    this.sid = null;
    this.integrationName = 'Synology';
    this.axios = axios.create({
      baseURL: this.baseURL,
      timeout: 15000, // 15 seconds timeout - reduced to prevent frontend timeouts
      maxContentLength: 50 * 1024 * 1024, // 50MB max content length
      validateStatus: (status) => status < 500, // Accept all non-server error status codes
      maxRedirects: 3 // Limit redirects to prevent infinite loops
    });
  }

  /**
   * Initialize the Synology API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we have valid credentials
      if (!this.host || !this.port || !this.username || !this.password) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Host, port, username, and password are required for Synology integration.'
        );
        return;
      }

      // Check if we can authenticate
      try {
        await this.login();

        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated and ready to use.'
        );
      } catch (authError) {
        // Update the integration status to indicate authentication failed
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `Authentication failed: ${authError.message}`
        );
        throw authError;
      }
    } catch (error) {
      console.error('Error initializing Synology API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get API information for authentication
   * @returns {Promise<Object>} API information
   */
  async getApiInfo() {
    try {
      const params = {
        api: 'SYNO.API.Info',
        version: '1',
        method: 'query',
        query: 'SYNO.API.Auth,SYNO.FileStation.List'
      };

      const response = await this.axios.get('/query.cgi', { params });

      if (response.data.success) {
        return {
          auth: response.data.data['SYNO.API.Auth'],
          fileStation: response.data.data['SYNO.FileStation.List']
        };
      } else {
        throw new Error(`Failed to get API info: ${response.data.error.code}`);
      }
    } catch (error) {
      console.error('Error getting API info:', error);
      throw error;
    }
  }

  /**
   * Login to Synology DiskStation
   * @returns {Promise<string>} Session ID
   */
  async login() {
    try {
      // First, get the API info to determine the correct path and version
      let authApiInfo;
      try {
        const apiInfo = await this.getApiInfo();
        authApiInfo = apiInfo.auth;
        console.log('Auth API info:', authApiInfo);
      } catch (apiInfoError) {
        console.error('Error getting API info:', apiInfoError);
        // Fall back to default values if we can't get the API info
        authApiInfo = {
          path: 'auth.cgi',
          minVersion: 1,
          maxVersion: 7
        };
      }

      // Use the path from API info or fall back to auth.cgi
      const path = authApiInfo.path || 'auth.cgi';
      
      // Try different API versions, starting with the most recent ones
      // Version 6+ supports 2FA but we'll need to handle it specially
      const versionsToTry = [3, 2, 1, 6];
      let sid = null;
      let lastError = null;
      
      for (const version of versionsToTry) {
        try {
          console.log(`Trying authentication with version ${version}...`);
          
          // Include all required parameters
          const params = {
            api: 'SYNO.API.Auth',
            version: version.toString(),
            method: 'login',
            account: this.username,
            passwd: this.password,
            session: 'FileStation'
          };
          
          // For version 6+, add enable_syno_token parameter
          if (version >= 6) {
            params.enable_syno_token = 'yes';
          }
          
          const response = await this.axios.get(`/${path}`, { params });
          
          if (response.data.success) {
            sid = response.data.data.sid;
            this.sid = sid;
            
            // Update the integration status to active
            integrationTracker.updateStatus(
              this.integrationName, 
              'active', 
              new Date(), 
              `Successfully authenticated with Synology using API version ${version}.`
            );
            
            console.log(`Authentication successful with version ${version}`);
            return sid;
          } else {
            // If we get a 406 error with version 6+, it means 2FA is required
            if (response.data.error && 
                response.data.error.code === 406 && 
                response.data.error.errors && 
                response.data.error.errors.token) {
              
              const token = response.data.error.errors.token;
              const errorMessage = `Two-factor authentication (2FA) is required for this account. The Synology integration does not support 2FA. Please create a new account without 2FA specifically for API access, or disable 2FA on the current account.`;
              
              console.error(errorMessage);
              
              // Update the integration status to indicate 2FA is required
              integrationTracker.updateStatus(
                this.integrationName, 
                'error', 
                null, 
                errorMessage
              );
              
              throw new Error(errorMessage);
            }
            
            // Store the error for later if we need it
            lastError = response.data.error;
            console.log(`Authentication failed with version ${version}: ${JSON.stringify(lastError)}`);
          }
        } catch (versionError) {
          // If this is a network error or other non-API error, rethrow it
          if (!versionError.response || !versionError.response.data) {
            throw versionError;
          }
          
          // Otherwise, store the error and continue trying other versions
          lastError = versionError.response.data.error || versionError;
          console.log(`Error with version ${version}: ${versionError.message}`);
        }
      }
      
      // If we've tried all versions and none worked, throw an error with the last error we got
      if (lastError) {
        let errorMessage = `Authentication failed with all API versions. Last error: ${JSON.stringify(lastError)}`;
        
        // Provide more detailed error information based on the error code
        if (lastError.code === 403) {
          errorMessage = `Authentication error: 403 - Forbidden. This could indicate that the credentials are incorrect, the account doesn't have permission to access the API, or 2FA is enabled on the account. Please check your credentials and permissions.`;
        } else if (lastError.code === 400) {
          errorMessage = `Authentication error: 400 - Bad Request. This could indicate that the request is malformed or missing required parameters.`;
        } else if (lastError.code === 401) {
          errorMessage = `Authentication error: 401 - Unauthorized. This could indicate that the credentials are incorrect.`;
        } else if (lastError.code === 404) {
          errorMessage = `Authentication error: 404 - Not Found. This could indicate that the API endpoint doesn't exist.`;
        } else if (lastError.code === 406) {
          errorMessage = `Authentication error: 406 - Not Acceptable. This likely indicates that 2FA is enabled on the account. Please create a new account without 2FA specifically for API access, or disable 2FA on the current account.`;
        } else if (lastError.code === 103) {
          errorMessage = `Authentication error: 103 - The requested parameter does not exist. This could indicate that a required parameter is missing or has an incorrect name.`;
        }
        
        console.error(errorMessage);
        
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          errorMessage
        );
        
        throw new Error(errorMessage);
      }
      
      // If we get here, something unexpected happened
      throw new Error('Authentication failed with all API versions for unknown reasons');
    } catch (error) {
      // Check if this is an axios error with a 2FA response (code 406)
      if (error.response && 
          error.response.data && 
          error.response.data.error && 
          error.response.data.error.code === 406 && 
          error.response.data.error.errors && 
          error.response.data.error.errors.token) {
        
        const errorMessage = `Two-factor authentication (2FA) is required for this account. The Synology integration does not support 2FA. Please create a new account without 2FA specifically for API access, or disable 2FA on the current account.`;
        
        console.error('Error logging in to Synology:', errorMessage);
        
        // Update the integration status to indicate 2FA is required
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          errorMessage
        );
        
        throw new Error(errorMessage);
      } 
      // Check if this is an axios error with a 2FA response (old format with types)
      else if (error.response && 
          error.response.data && 
          error.response.data.error && 
          error.response.data.error.code === 403 && 
          error.response.data.error.errors && 
          error.response.data.error.errors.types) {
        
        const types = error.response.data.error.errors.types.map(t => t.type).join(', ');
        const errorMessage = `Two-factor authentication required (${types}). The Synology integration does not support 2FA. Please create a new account without 2FA specifically for API access, or disable 2FA on the current account.`;
        
        console.error('Error logging in to Synology:', errorMessage);
        
        // Update the integration status to indicate 2FA is required
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          errorMessage
        );
        
        throw new Error(errorMessage);
      } else {
        console.error('Error logging in to Synology:', error);

        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `Authentication error: ${error.message}`
        );

        throw error;
      }
    }
  }

  /**
   * Logout from Synology DiskStation
   * @returns {Promise<boolean>} Success status
   */
  async logout() {
    try {
      if (!this.sid) {
        return true;
      }

      const params = {
        api: 'SYNO.API.Auth',
        version: '3',  // Use the same version as login
        method: 'logout',
        session: 'FileStation',
        _sid: this.sid
      };

      const response = await this.axios.get('/auth.cgi', { params });

      if (response.data.success) {
        this.sid = null;
        return true;
      } else {
        throw new Error(response.data.error.code);
      }
    } catch (error) {
      console.error('Error logging out from Synology:', error);
      throw error;
    }
  }

  /**
   * Get file list with proper API endpoints and improved error handling
   * @param {string} folderPath Path to the folder
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Object containing files array and pagination metadata
   */
  async listFiles(folderPath, options = {}) {
    try {
      if (!this.sid) {
        await this.login();
      }

      // First, determine if we're listing shares or files in a specific folder
      const isRootOrShareLevel = folderPath === '/' || folderPath === '' || !folderPath.includes('/') || folderPath.startsWith('/volume');
      
      // For root level or share listing, use list_share method
      if (isRootOrShareLevel && (folderPath === '/' || folderPath === '')) {
        return await this.listShares(options);
      }
      
      // For specific folders, use the list method with proper parameters
      return await this.listFolderContents(folderPath, options);
    } catch (error) {
      console.error('Error listing files from Synology:', error);
      throw error;
    }
  }

  /**
   * List shared folders using the correct API endpoint
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Object containing shares array and pagination metadata
   */
  async listShares(options = {}) {
    try {
      const paginatedOptions = {
        limit: options.limit || 50,  // Shares are typically fewer, so higher limit is OK
        offset: options.offset || 0,
        sort_by: options.sort_by || 'name',
        sort_direction: options.sort_direction || 'ASC',
        ...options
      };

      const params = {
        api: 'SYNO.FileStation.List',
        version: '1',  // Use version 1 for list_share as per documentation
        method: 'list_share',
        _sid: this.sid,
        additional: '["real_path","owner","time","perm"]',
        ...paginatedOptions
      };

      console.log('Listing shared folders...');
      const response = await this.axios.get('/FileStation/file_share.cgi', { params });

      if (response.data.success) {
        const shares = response.data.data.shares || [];
        console.log(`Successfully listed ${shares.length} shared folders`);
        
        return {
          files: shares.map(share => ({
            ...share,
            isdir: true,  // Shares are always directories
            path: share.path || `/${share.name}`
          })),
          pagination: {
            offset: paginatedOptions.offset,
            limit: paginatedOptions.limit,
            sort: {
              by: paginatedOptions.sort_by,
              direction: paginatedOptions.sort_direction
            }
          },
          originalPath: '/',
          adjustedPath: '/'
        };
      } else {
        const errorCode = response.data.error.code;
        console.error(`Error listing shares: ${errorCode}`);
        throw new Error(`Error listing shared folders: ${errorCode}`);
      }
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('Connection timeout when listing shared folders. Please try again.');
      }
      throw error;
    }
  }

  /**
   * List contents of a specific folder using the correct API endpoint
   * @param {string} folderPath Path to the folder
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Object containing files array and pagination metadata
   */
  async listFolderContents(folderPath, options = {}) {
    try {
      const paginatedOptions = {
        limit: options.limit || 100,  // Reasonable default for folder contents
        offset: options.offset || 0,
        sort_by: options.sort_by || 'name',
        sort_direction: options.sort_direction || 'ASC',
        ...options
      };

      // Set up retry mechanism with reduced retries for better performance
      const maxRetries = 2;
      let retryCount = 0;
      let adjustedFolderPath = folderPath;

      while (retryCount <= maxRetries) {
        try {
          // If this is a retry, add a small delay
          if (retryCount > 0) {
            const delayMs = retryCount * 500;  // 500ms, 1000ms
            console.log(`Retry ${retryCount}/${maxRetries} after ${delayMs}ms delay...`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
          }

          const params = {
            api: 'SYNO.FileStation.List',
            version: '2',
            method: 'list',
            folder_path: adjustedFolderPath,
            _sid: this.sid,
            additional: '["real_path","size","owner","time","perm"]',
            ...paginatedOptions
          };

          console.log(`Listing files in ${adjustedFolderPath} (limit: ${paginatedOptions.limit}, offset: ${paginatedOptions.offset})...`);
          
          // Use FileStation/file_share.cgi for file operations as per documentation
          const response = await this.axios.get('/FileStation/file_share.cgi', { params });

          if (response.data.success) {
            const files = response.data.data.files || [];
            console.log(`Successfully listed ${files.length} files in ${adjustedFolderPath}`);
            
            return {
              files,
              pagination: {
                offset: paginatedOptions.offset,
                limit: paginatedOptions.limit,
                sort: {
                  by: paginatedOptions.sort_by,
                  direction: paginatedOptions.sort_direction
                }
              },
              originalPath: folderPath,
              adjustedPath: adjustedFolderPath
            };
          } else {
            // Handle specific error codes with reduced retry logic
            const errorCode = response.data.error.code;
            
            if (errorCode === 408 && retryCount < maxRetries) {
              // For timeouts, reduce limit and retry once
              paginatedOptions.limit = Math.max(50, Math.floor(paginatedOptions.limit / 2));
              console.log(`Request timeout, reducing limit to ${paginatedOptions.limit} and retrying...`);
              retryCount++;
              continue;
            } else if ((errorCode === 401 || errorCode === 403) && retryCount < maxRetries) {
              // For authentication errors, try to re-authenticate once
              console.log('Session expired, re-authenticating...');
              await this.login();
              retryCount++;
              continue;
            } else if (errorCode === 404) {
              throw new Error(`Directory not found: ${adjustedFolderPath}`);
            }
            
            throw new Error(`Error listing files (${errorCode}): ${adjustedFolderPath}`);
          }
        } catch (requestError) {
          if (requestError.code === 'ECONNABORTED' && retryCount < maxRetries) {
            // For connection timeouts, reduce limit and retry once
            paginatedOptions.limit = Math.max(50, Math.floor(paginatedOptions.limit / 2));
            console.log(`Connection timeout, reducing limit to ${paginatedOptions.limit} and retrying...`);
            retryCount++;
            continue;
          } else if (requestError.response?.status === 403 && retryCount < maxRetries) {
            // Session expired, try to re-authenticate once
            console.log('Session expired, re-authenticating...');
            await this.login();
            retryCount++;
            continue;
          }
          
          // Re-throw the error if we can't handle it
          throw requestError;
        }
      }
      
      throw new Error(`Failed to list files in ${adjustedFolderPath} after ${maxRetries} retries`);
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        throw new Error(`Connection timeout when listing files in ${folderPath}. The directory might be too large.`);
      }
      throw error;
    }
  }

  /**
   * Download a file
   * @param {string} path Path to the file
   * @returns {Promise<Buffer>} File data
   */
  async downloadFile(path) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const params = {
        api: 'SYNO.FileStation.Download',
        version: '2',
        method: 'download',
        path,
        _sid: this.sid
      };

      try {
        const response = await this.axios.get('/entry.cgi', { 
          params,
          responseType: 'arraybuffer'
        });

        return response.data;
      } catch (requestError) {
        // If we get a 403 error, the session might have expired
        if (requestError.response && requestError.response.status === 403) {
          // Try to re-authenticate and retry the request
          await this.login();

          // Update the session ID in the params
          params._sid = this.sid;

          // Retry the request
          const retryResponse = await this.axios.get('/entry.cgi', { 
            params,
            responseType: 'arraybuffer'
          });

          return retryResponse.data;
        } else {
          // If it's not a 403 error, rethrow it
          throw requestError;
        }
      }
    } catch (error) {
      console.error('Error downloading file from Synology:', error);
      throw error;
    }
  }

  /**
   * Upload a file
   * @param {string} folderPath Destination folder path
   * @param {Buffer} fileData File data
   * @param {string} fileName File name
   * @returns {Promise<Object>} Upload result
   */
  async uploadFile(folderPath, fileData, fileName) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const createFormData = () => {
        const formData = new FormData();
        formData.append('api', 'SYNO.FileStation.Upload');
        formData.append('version', '2');
        formData.append('method', 'upload');
        formData.append('path', folderPath);
        formData.append('create_parents', 'true');
        formData.append('_sid', this.sid);
        formData.append('file', new Blob([fileData]), fileName);
        return formData;
      };

      try {
        const response = await this.axios.post('/entry.cgi', createFormData(), {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });

        if (response.data.success) {
          return response.data.data;
        } else {
          throw new Error(response.data.error.code);
        }
      } catch (requestError) {
        // If we get a 403 error, the session might have expired
        if (requestError.response && requestError.response.status === 403) {
          // Try to re-authenticate and retry the request
          await this.login();

          // Retry the request with a new form data that includes the updated session ID
          const retryResponse = await this.axios.post('/entry.cgi', createFormData(), {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });

          if (retryResponse.data.success) {
            return retryResponse.data.data;
          } else {
            throw new Error(retryResponse.data.error.code);
          }
        } else {
          // If it's not a 403 error, rethrow it
          throw requestError;
        }
      }
    } catch (error) {
      console.error('Error uploading file to Synology:', error);
      throw error;
    }
  }

  /**
   * Create a sharing link
   * @param {string} path Path to the file or folder
   * @param {Object} options Additional options
   * @returns {Promise<Object>} Sharing link info
   */
  async createSharingLink(path, options = {}) {
    try {
      if (!this.sid) {
        await this.login();
      }

      const params = {
        api: 'SYNO.FileStation.Sharing',
        version: '3',
        method: 'create',
        path,
        _sid: this.sid,
        ...options
      };

      try {
        const response = await this.axios.get('/entry.cgi', { params });

        if (response.data.success) {
          return response.data.data;
        } else {
          throw new Error(response.data.error.code);
        }
      } catch (requestError) {
        // If we get a 403 error, the session might have expired
        if (requestError.response && requestError.response.status === 403) {
          // Try to re-authenticate and retry the request
          await this.login();

          // Update the session ID in the params
          params._sid = this.sid;

          // Retry the request
          const retryResponse = await this.axios.get('/entry.cgi', { params });

          if (retryResponse.data.success) {
            return retryResponse.data.data;
          } else {
            throw new Error(retryResponse.data.error.code);
          }
        } else {
          // If it's not a 403 error, rethrow it
          throw requestError;
        }
      }
    } catch (error) {
      console.error('Error creating sharing link on Synology:', error);
      throw error;
    }
  }
}

module.exports = SynologyAPI;
