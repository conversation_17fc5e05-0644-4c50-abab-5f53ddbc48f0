// Using dynamic import for ES Module
const path = require('path');
const fs = require('fs');
const integrationTracker = require('../../utils/integrationTracker');
const GoogleWorkspaceAuth = require('./googleWorkspaceAuth');

/**
 * RADIUS Server Implementation
 * This module initializes and manages a RADIUS server instance
 */
class RadiusServerManager {
  constructor(config = {}) {
    this.config = {
      // Default configuration
      port: process.env.RADIUS_PORT || 1812,
      secret: process.env.RADIUS_SECRET || 'testing123',
      authorizationPort: process.env.RADIUS_AUTHORIZATION_PORT || 1813,
      ...config
    };

    this.server = null;
    this.integrationName = 'RADIUS';
    this.RadiusServer = null; // Will be set after dynamic import
    this.googleWorkspaceAuth = null; // Google Workspace authentication handler
  }

  /**
   * Initialize and start the RADIUS server
   * @returns {Promise<void>}
   */
  async start() {
    try {
      if (this.server) {
        console.log('RADIUS server is already running');
        return;
      }

      // Dynamically import the radius-server module if not already imported
      if (!this.RadiusServer) {
        const radiusModule = await import('radius-server');
        this.RadiusServer = radiusModule.RadiusServer;
      }

      // Get the address to bind to, defaulting to '0.0.0.0'
      const bindAddress = process.env.RADIUS_ADDRESS || '0.0.0.0';
      
      // Get authentication type
      let authType = process.env.RADIUS_AUTHENTICATION || 'GoogleWorkspaceAuth';
      
      // Setup authentication options based on type
      let authenticationOptions = {};
      
      if (authType === 'GoogleWorkspaceAuth') {
        // Initialize Google Workspace authentication
        this.googleWorkspaceAuth = new GoogleWorkspaceAuth();
        
        // Check if Google Workspace auth is configured
        if (!this.googleWorkspaceAuth.isConfigured()) {
          console.warn('RADIUS: Google Workspace authentication not configured, falling back to static auth');
          authType = 'StaticAuth';
          authenticationOptions = {
            users: {
              'test': 'test'
            }
          };
        } else {
          // Initialize Google Workspace auth
          await this.googleWorkspaceAuth.initialize();
          
          // Create custom authentication function
          authenticationOptions = {
            authenticate: async (username, password) => {
              return await this.googleWorkspaceAuth.authenticate(username, password);
            }
          };
        }
      } else {
        // Fallback to static authentication for testing
        authenticationOptions = {
          users: {
            'test': 'test'
          }
        };
      }

      try {
        // Create a new RADIUS server instance
        this.server = new this.RadiusServer({
          port: this.config.port,
          secret: this.config.secret,
          address: bindAddress,
          authentication: authType === 'GoogleWorkspaceAuth' ? 'CustomAuth' : authType,
          authenticationOptions,
          // Optional TLS options if needed
          tlsOptions: process.env.RADIUS_TLS_ENABLED === 'true' ? {
            key: process.env.RADIUS_TLS_KEY_PATH,
            cert: process.env.RADIUS_TLS_CERT_PATH
          } : undefined
        });

        // Start the server
        await this.server.start();
      } catch (bindError) {
        // If we get an EADDRNOTAVAIL error, try again with '0.0.0.0'
        if (bindError.code === 'EADDRNOTAVAIL' && bindAddress !== '0.0.0.0') {
          console.warn(`Failed to bind to ${bindAddress}, falling back to '0.0.0.0'`);

          this.server = new this.RadiusServer({
            port: this.config.port,
            secret: this.config.secret,
            address: '0.0.0.0',
            authentication: authType === 'GoogleWorkspaceAuth' ? 'CustomAuth' : authType,
            authenticationOptions,
            tlsOptions: process.env.RADIUS_TLS_ENABLED === 'true' ? {
              key: process.env.RADIUS_TLS_KEY_PATH,
              cert: process.env.RADIUS_TLS_CERT_PATH
            } : undefined
          });

          await this.server.start();
        } else {
          // If it's not an EADDRNOTAVAIL error or we're already using '0.0.0.0', rethrow
          throw bindError;
        }
      }

      console.log(`RADIUS server started on port ${this.config.port}`);
      console.log(`RADIUS authorization server started on port ${this.config.authorizationPort}`);
      
      // Log authentication method
      if (this.googleWorkspaceAuth) {
        const authStatus = this.googleWorkspaceAuth.getConfigStatus();
        console.log(`RADIUS authentication: Google Workspace (configured: ${authStatus.configured}, initialized: ${authStatus.initialized})`);
      } else {
        console.log(`RADIUS authentication: Static (fallback mode)`);
      }

      // Update the integration status to active
      integrationTracker.updateStatus(
        this.integrationName, 
        'active', 
        new Date(), 
        `RADIUS server is running with ${this.googleWorkspaceAuth ? 'Google Workspace' : 'static'} authentication.`
      );
    } catch (error) {
      console.error('Error starting RADIUS server:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error starting RADIUS server: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Stop the RADIUS server
   * @returns {Promise<void>}
   */
  async stop() {
    try {
      if (!this.server) {
        console.log('RADIUS server is not running');
        return;
      }

      await this.server.stop();
      this.server = null;

      console.log('RADIUS server stopped');

      // Update the integration status
      integrationTracker.updateStatus(
        this.integrationName, 
        'inactive', 
        null, 
        'RADIUS server has been stopped.'
      );
    } catch (error) {
      console.error('Error stopping RADIUS server:', error);
      throw error;
    }
  }

  /**
   * Check if the RADIUS server is running
   * @returns {boolean}
   */
  isRunning() {
    return !!this.server;
  }

  /**
   * Get the current server configuration
   * @returns {Object}
   */
  getConfig() {
    const config = { ...this.config };
    
    // Add Google Workspace authentication status if available
    if (this.googleWorkspaceAuth) {
      config.googleWorkspaceAuth = this.googleWorkspaceAuth.getConfigStatus();
    }
    
    return config;
  }

  /**
   * Update the server configuration
   * @param {Object} newConfig New configuration
   * @returns {Promise<void>}
   */
  async updateConfig(newConfig) {
    try {
      // Stop the server if it's running
      if (this.isRunning()) {
        await this.stop();
      }

      // Update the configuration
      this.config = {
        ...this.config,
        ...newConfig
      };

      // Restart the server with the new configuration
      await this.start();

      return this.getConfig();
    } catch (error) {
      console.error('Error updating RADIUS server configuration:', error);
      throw error;
    }
  }
}

// Create a singleton instance
const radiusServerManager = new RadiusServerManager();

module.exports = radiusServerManager;
