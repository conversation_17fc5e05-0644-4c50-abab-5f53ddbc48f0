// RADIUS API Wrapper
const axios = require('axios');
const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { refreshAndSaveTokens } = require('../../utils/tokenRefresh');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * RADIUS Server API Wrapper
 * Supports authentication through Google SAML or Google API
 */
class RadiusAPI {
  constructor(config) {
    this.config = config;
    this.integrationName = 'RADIUS';
    this.googleAuth = null;
    this.googleScopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group'
    ];

    // Get the local RADIUS server if available
    try {
      this.radiusServer = require('./radiusServer');
    } catch (error) {
      console.warn('Local RADIUS server module not found, will use external server');
      this.radiusServer = null;
    }

    // Create axios instance for RADIUS server communication
    this.axios = axios.create({
      baseURL: config.radiusServerUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (config.authType === 'google_api') {
      this.googleAuth = new google.auth.OAuth2(
        config.googleClientId,
        config.googleClientSecret,
        config.googleRedirectUri
      );
    }
  }

  /**
   * Initialize the RADIUS API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we can authenticate
      const isAuthenticated = await this.isAuthenticated();

      if (isAuthenticated) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated and ready to use.'
        );
      } else {
        // Update the integration status to indicate authentication is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Authentication failed. Check configuration and try again.'
        );
      }
    } catch (error) {
      console.error('Error initializing RADIUS API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {Promise<boolean>} Authentication status
   */
  async isAuthenticated() {
    try {
      // If we have a local RADIUS server and it's running, we're authenticated
      if (this.radiusServer && this.radiusServer.isRunning()) {
        return true;
      }

      // Otherwise, check authentication based on the configured auth type
      if (this.config.authType === 'google_api') {
        return await this.isGoogleApiAuthenticated();
      } else if (this.config.authType === 'google_saml') {
        return await this.isGoogleSamlAuthenticated();
      } else {
        throw new Error('Unsupported authentication type');
      }
    } catch (error) {
      console.error('Authentication check failed:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Authentication error: ${error.message}`
      );

      return false;
    }
  }

  /**
   * Check if Google API authentication is configured and working
   * @returns {Promise<boolean>} Authentication status
   */
  async isGoogleApiAuthenticated() {
    if (!this.googleAuth) {
      return false;
    }

    // Check if we have a token
    if (!this.googleAuth.credentials || !this.googleAuth.credentials.access_token) {
      // Check if we have a token file
      const tokenPath = this.config.googleTokenPath;
      if (fs.existsSync(tokenPath)) {
        const token = JSON.parse(fs.readFileSync(tokenPath, 'utf8'));
        this.googleAuth.setCredentials(token);
      } else {
        return false;
      }
    }

    try {
      // Test the token by making a simple API call
      const admin = google.admin({version: 'directory_v1', auth: this.googleAuth});
      await admin.users.list({ maxResults: 1 });
      return true;
    } catch (error) {
      console.error('Google API authentication test failed:', error);
      return false;
    }
  }

  /**
   * Check if Google SAML authentication is configured and working
   * @returns {Promise<boolean>} Authentication status
   */
  async isGoogleSamlAuthenticated() {
    try {
      // Check if SAML configuration is valid
      const samlConfig = this.config.samlConfig;
      if (!samlConfig || !samlConfig.entryPoint || !samlConfig.issuer) {
        console.error('SAML configuration is incomplete');
        return false;
      }

      // Test RADIUS server connection by checking server status
      // This is a more reliable way than using a specific endpoint that might not exist
      const response = await this.axios.get('/status');
      return response.status === 200;
    } catch (error) {
      console.error('Google SAML authentication test failed:', error);
      return false;
    }
  }

  /**
   * Generate authentication URL for OAuth2 flow (Google API auth)
   * @returns {string} Authentication URL
   */
  getGoogleAuthUrl() {
    if (!this.googleAuth) {
      throw new Error('Google API authentication not configured');
    }

    return this.googleAuth.generateAuthUrl({
      access_type: 'offline',
      scope: this.googleScopes,
      prompt: 'consent'
    });
  }

  /**
   * Get access token from authorization code (Google API auth)
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Token object
   */
  async getGoogleToken(code) {
    try {
      if (!this.googleAuth) {
        throw new Error('Google API authentication not configured');
      }

      const { tokens } = await this.googleAuth.getToken(code);
      this.googleAuth.setCredentials(tokens);

      // Save token to file for future use
      fs.writeFileSync(this.config.googleTokenPath, JSON.stringify(tokens));

      // Update the integration status to active
      integrationTracker.updateStatus(
        this.integrationName, 
        'active', 
        new Date(), 
        'Successfully obtained new Google authentication token.'
      );

      return tokens;
    } catch (error) {
      console.error('Error getting Google token:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error getting Google token: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Configure RADIUS server with Google SAML
   * @param {Object} samlConfig SAML configuration
   * @returns {Promise<Object>} Configuration result
   */
  async configureSaml(samlConfig) {
    try {
      const response = await this.axios.post('/configure-saml', {
        samlConfig
      });

      if (response.data.success) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Successfully configured RADIUS with Google SAML.'
        );
      }

      return response.data;
    } catch (error) {
      console.error('Error configuring RADIUS with SAML:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error configuring SAML: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get RADIUS server status
   * @returns {Promise<Object>} Server status
   */
  async getServerStatus() {
    try {
      // If we have a local RADIUS server, use it to get status
      if (this.radiusServer) {
        const isRunning = this.radiusServer.isRunning();
        const config = this.radiusServer.getConfig();

        return {
          status: isRunning ? 'running' : 'stopped',
          port: config.port,
          authorizationPort: config.authorizationPort,
          uptime: isRunning ? 'since server start' : null,
          local: true
        };
      }

      // Otherwise, try to get status from external server
      const response = await this.axios.get('/status');
      return {
        ...response.data,
        local: false
      };
    } catch (error) {
      console.error('Error getting RADIUS server status:', error);

      // If we have a local RADIUS server but the request failed,
      // still return basic status information
      if (this.radiusServer) {
        const isRunning = this.radiusServer.isRunning();
        const config = this.radiusServer.getConfig();

        return {
          status: isRunning ? 'running' : 'stopped',
          port: config.port,
          authorizationPort: config.authorizationPort,
          uptime: isRunning ? 'since server start' : null,
          local: true,
          error: error.message
        };
      }

      throw error;
    }
  }

  /**
   * Get RADIUS server configuration
   * @returns {Promise<Object>} Server configuration
   */
  async getServerConfig() {
    try {
      // Check if environment variables are being used for Google API configuration
      const usingEnvVars = {
        googleClientId: !!process.env.GOOGLE_CLIENT_ID,
        googleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
        googleRedirectUri: !!process.env.GOOGLE_REDIRECT_URI
      };

      // If we have a local RADIUS server, use it to get config
      if (this.radiusServer) {
        const config = this.radiusServer.getConfig();
        return {
          ...config,
          local: true,
          usingEnvVars
        };
      }

      // Otherwise, try to get config from external server
      const response = await this.axios.get('/config');
      return {
        ...response.data,
        local: false,
        usingEnvVars
      };
    } catch (error) {
      console.error('Error getting RADIUS server configuration:', error);

      // If we have a local RADIUS server but the request failed,
      // still return basic configuration information
      if (this.radiusServer) {
        const config = this.radiusServer.getConfig();
        return {
          ...config,
          local: true,
          error: error.message,
          usingEnvVars: {
            googleClientId: !!process.env.GOOGLE_CLIENT_ID,
            googleClientSecret: !!process.env.GOOGLE_CLIENT_SECRET,
            googleRedirectUri: !!process.env.GOOGLE_REDIRECT_URI
          }
        };
      }

      throw error;
    }
  }

  /**
   * Update RADIUS server configuration
   * @param {Object} config Configuration object
   * @returns {Promise<Object>} Updated configuration
   */
  async updateServerConfig(config) {
    try {
      // If we have a local RADIUS server, use it to update config
      if (this.radiusServer) {
        const updatedConfig = await this.radiusServer.updateConfig(config);
        return {
          ...updatedConfig,
          local: true
        };
      }

      // Otherwise, try to update config on external server
      const response = await this.axios.post('/config', config);
      return {
        ...response.data,
        local: false
      };
    } catch (error) {
      console.error('Error updating RADIUS server configuration:', error);
      throw error;
    }
  }

  /**
   * Get RADIUS server clients
   * @returns {Promise<Array>} List of RADIUS clients
   */
  async getClients() {
    try {
      const response = await this.axios.get('/clients');
      return response.data;
    } catch (error) {
      console.error('Error getting RADIUS clients:', error);
      throw error;
    }
  }

  /**
   * Add a new RADIUS client
   * @param {Object} client Client configuration
   * @returns {Promise<Object>} Created client
   */
  async addClient(client) {
    try {
      const response = await this.axios.post('/clients', client);
      return response.data;
    } catch (error) {
      console.error('Error adding RADIUS client:', error);
      throw error;
    }
  }

  /**
   * Update a RADIUS client
   * @param {string} clientId Client ID
   * @param {Object} client Client configuration
   * @returns {Promise<Object>} Updated client
   */
  async updateClient(clientId, client) {
    try {
      const response = await this.axios.put(`/clients/${clientId}`, client);
      return response.data;
    } catch (error) {
      console.error('Error updating RADIUS client:', error);
      throw error;
    }
  }

  /**
   * Delete a RADIUS client
   * @param {string} clientId Client ID
   * @returns {Promise<Object>} Response message
   */
  async deleteClient(clientId) {
    try {
      const response = await this.axios.delete(`/clients/${clientId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting RADIUS client:', error);
      throw error;
    }
  }

  /**
   * Get authentication logs
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of authentication logs
   */
  async getAuthLogs(params = {}) {
    try {
      const response = await this.axios.get('/logs/auth', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting RADIUS authentication logs:', error);
      throw error;
    }
  }

  /**
   * Get VLAN configurations
   * @returns {Promise<Array>} List of VLAN configurations
   */
  async getVlanConfigs() {
    try {
      const response = await this.axios.get('/vlans');
      return response.data;
    } catch (error) {
      console.error('Error getting VLAN configurations:', error);
      throw error;
    }
  }

  /**
   * Add a new VLAN configuration
   * @param {Object} vlanConfig VLAN configuration
   * @returns {Promise<Object>} Created VLAN configuration
   */
  async addVlanConfig(vlanConfig) {
    try {
      const response = await this.axios.post('/vlans', vlanConfig);
      return response.data;
    } catch (error) {
      console.error('Error adding VLAN configuration:', error);
      throw error;
    }
  }

  /**
   * Update a VLAN configuration
   * @param {string} vlanId VLAN ID
   * @param {Object} vlanConfig VLAN configuration
   * @returns {Promise<Object>} Updated VLAN configuration
   */
  async updateVlanConfig(vlanId, vlanConfig) {
    try {
      const response = await this.axios.put(`/vlans/${vlanId}`, vlanConfig);
      return response.data;
    } catch (error) {
      console.error('Error updating VLAN configuration:', error);
      throw error;
    }
  }

  /**
   * Delete a VLAN configuration
   * @param {string} vlanId VLAN ID
   * @returns {Promise<Object>} Response message
   */
  async deleteVlanConfig(vlanId) {
    try {
      const response = await this.axios.delete(`/vlans/${vlanId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting VLAN configuration:', error);
      throw error;
    }
  }

  /**
   * Get group-to-VLAN mappings
   * @returns {Promise<Array>} List of group-to-VLAN mappings
   */
  async getGroupVlanMappings() {
    try {
      const response = await this.axios.get('/group-vlan-mappings');
      return response.data;
    } catch (error) {
      console.error('Error getting group-to-VLAN mappings:', error);
      throw error;
    }
  }

  /**
   * Add a new group-to-VLAN mapping
   * @param {Object} mapping Group-to-VLAN mapping
   * @returns {Promise<Object>} Created mapping
   */
  async addGroupVlanMapping(mapping) {
    try {
      const response = await this.axios.post('/group-vlan-mappings', mapping);
      return response.data;
    } catch (error) {
      console.error('Error adding group-to-VLAN mapping:', error);
      throw error;
    }
  }

  /**
   * Update a group-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @param {Object} mapping Group-to-VLAN mapping
   * @returns {Promise<Object>} Updated mapping
   */
  async updateGroupVlanMapping(mappingId, mapping) {
    try {
      const response = await this.axios.put(`/group-vlan-mappings/${mappingId}`, mapping);
      return response.data;
    } catch (error) {
      console.error('Error updating group-to-VLAN mapping:', error);
      throw error;
    }
  }

  /**
   * Delete a group-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @returns {Promise<Object>} Response message
   */
  async deleteGroupVlanMapping(mappingId) {
    try {
      const response = await this.axios.delete(`/group-vlan-mappings/${mappingId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting group-to-VLAN mapping:', error);
      throw error;
    }
  }

  /**
   * Get user-to-VLAN mappings
   * @returns {Promise<Array>} List of user-to-VLAN mappings
   */
  async getUserVlanMappings() {
    try {
      const response = await this.axios.get('/user-vlan-mappings');
      return response.data;
    } catch (error) {
      console.error('Error getting user-to-VLAN mappings:', error);
      throw error;
    }
  }

  /**
   * Add a new user-to-VLAN mapping
   * @param {Object} mapping User-to-VLAN mapping
   * @returns {Promise<Object>} Created mapping
   */
  async addUserVlanMapping(mapping) {
    try {
      const response = await this.axios.post('/user-vlan-mappings', mapping);
      return response.data;
    } catch (error) {
      console.error('Error adding user-to-VLAN mapping:', error);
      throw error;
    }
  }

  /**
   * Update a user-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @param {Object} mapping User-to-VLAN mapping
   * @returns {Promise<Object>} Updated mapping
   */
  async updateUserVlanMapping(mappingId, mapping) {
    try {
      const response = await this.axios.put(`/user-vlan-mappings/${mappingId}`, mapping);
      return response.data;
    } catch (error) {
      console.error('Error updating user-to-VLAN mapping:', error);
      throw error;
    }
  }

  /**
   * Delete a user-to-VLAN mapping
   * @param {string} mappingId Mapping ID
   * @returns {Promise<Object>} Response message
   */
  async deleteUserVlanMapping(mappingId) {
    try {
      const response = await this.axios.delete(`/user-vlan-mappings/${mappingId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting user-to-VLAN mapping:', error);
      throw error;
    }
  }

  /**
   * Get VLAN for a user
   * @param {string} username User's username
   * @returns {Promise<Object>} User's VLAN information
   */
  async getUserVlan(username) {
    try {
      const response = await this.axios.get(`/users/${username}/vlan`);
      return response.data;
    } catch (error) {
      console.error('Error getting user VLAN:', error);
      throw error;
    }
  }
}

module.exports = RadiusAPI;
