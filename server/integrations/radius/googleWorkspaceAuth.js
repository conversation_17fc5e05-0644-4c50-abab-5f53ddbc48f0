const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { getAuthenticatedClient } = require('../../utils/googleServiceAuth');

/**
 * Google Workspace Authentication Handler for RADIUS Server
 * This class provides authentication against Google Workspace using the Admin SDK
 */
class GoogleWorkspaceAuth {
  constructor(options = {}) {
    this.googleAuth = null;
    this.admin = null;
    this.config = {
      clientId: options.clientId || process.env.GOOGLE_CLIENT_ID,
      clientSecret: options.clientSecret || process.env.GOOGLE_CLIENT_SECRET,
      redirectUri: options.redirectUri || process.env.GOOGLE_REDIRECT_URI,
      tokenPath: options.tokenPath || path.join(process.cwd(), 'google-admin-token.json'),
      allowedDomains: options.allowedDomains || (process.env.ALLOWED_DOMAINS ? process.env.ALLOWED_DOMAINS.split(',') : []),
      serviceAccountEmail: options.serviceAccountEmail || process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
      serviceAccountKey: options.serviceAccountKey || process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY,
      impersonationEmail: options.impersonationEmail || process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL
    };

    this.scopes = [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group'
    ];

    this.isInitialized = false;
  }

  /**
   * Initialize the Google Workspace authentication
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        return;
      }

      // Check if using service account authentication
      if (this.config.serviceAccountEmail && this.config.serviceAccountKey) {
        console.log('RADIUS: Using service account authentication for Google Workspace');
        
        // Use the common service account authentication utility
        try {
          // Create a JWT client directly to ensure proper configuration
          const jwtClient = new google.auth.JWT({
            email: this.config.serviceAccountEmail,
            key: this.config.serviceAccountKey,
            scopes: this.scopes,
            subject: this.config.impersonationEmail // This is the user to impersonate
          });
          
          // Authorize the client
          await jwtClient.authorize();
          
          this.googleAuth = jwtClient;
          console.log('RADIUS: Successfully created JWT client for Google Workspace');
        } catch (serviceAuthError) {
          console.error('RADIUS: Error creating JWT client:', serviceAuthError);
          throw serviceAuthError;
        }
      } else if (this.config.clientId && this.config.clientSecret) {
        // Use OAuth2 authentication
        console.log('RADIUS: Using OAuth2 authentication for Google Workspace');
        
        this.googleAuth = new google.auth.OAuth2(
          this.config.clientId,
          this.config.clientSecret,
          this.config.redirectUri
        );

        // Load existing token if available
        if (fs.existsSync(this.config.tokenPath)) {
          const token = JSON.parse(fs.readFileSync(this.config.tokenPath, 'utf8'));
          this.googleAuth.setCredentials(token);
        } else {
          throw new Error('Google OAuth token not found. Please authenticate first.');
        }
      } else {
        throw new Error('Google Workspace authentication not configured. Missing required credentials.');
      }

      this.admin = google.admin({ version: 'directory_v1', auth: this.googleAuth });
      this.isInitialized = true;

      // Test authentication with a more specific query to avoid permission issues
      try {
        await this.admin.users.get({ userKey: this.config.impersonationEmail });
        console.log('RADIUS: Google Workspace authentication initialized successfully');
      } catch (apiError) {
        console.error('RADIUS: Error testing Google Workspace API:', apiError);
        // Still mark as initialized if it's just a permission issue
        if (apiError.code === 403) {
          console.log('RADIUS: Authentication succeeded but permission issues detected');
        } else {
          throw apiError;
        }
      }

    } catch (error) {
      console.error('RADIUS: Error initializing Google Workspace authentication:', error);
      throw error;
    }
  }

  /**
   * Authenticate a user against Google Workspace
   * This is called by the RADIUS server for each authentication request
   * @param {string} username - The username (email) to authenticate
   * @param {string} password - The password (not used for Google validation)
   * @returns {Promise<boolean>} - True if authentication succeeds
   */
  async authenticate(username, password) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log(`RADIUS: Authenticating user: ${username}`);

      // Validate email format
      if (!this.isValidEmail(username)) {
        console.log(`RADIUS: Invalid email format: ${username}`);
        return false;
      }

      // Check domain restrictions
      if (!this.isAllowedDomain(username)) {
        console.log(`RADIUS: Email domain not allowed: ${username}`);
        return false;
      }

      // Get user from Google Workspace
      const user = await this.getUser(username);
      if (!user) {
        console.log(`RADIUS: User not found in Google Workspace: ${username}`);
        return false;
      }

      // Check if user is active
      if (user.suspended) {
        console.log(`RADIUS: User is suspended: ${username}`);
        return false;
      }

      if (user.archived) {
        console.log(`RADIUS: User is archived: ${username}`);
        return false;
      }

      // Additional validation: check if user has valid organizational info
      if (!user.orgUnitPath) {
        console.log(`RADIUS: User has no organizational unit: ${username}`);
        return false;
      }

      console.log(`RADIUS: User authenticated successfully: ${username}`);
      return true;

    } catch (error) {
      console.error(`RADIUS: Authentication error for user ${username}:`, error);
      return false;
    }
  }

  /**
   * Get user information from Google Workspace
   * @param {string} email - User email
   * @returns {Promise<Object|null>} - User object or null if not found
   */
  async getUser(email) {
    try {
      const response = await this.admin.users.get({ userKey: email });
      return response.data;
    } catch (error) {
      if (error.code === 404) {
        return null; // User not found
      }
      throw error;
    }
  }

  /**
   * Get user groups from Google Workspace
   * @param {string} email - User email
   * @returns {Promise<Array>} - Array of group objects
   */
  async getUserGroups(email) {
    try {
      const response = await this.admin.groups.list({
        userKey: email,
        maxResults: 200
      });
      return response.data.groups || [];
    } catch (error) {
      console.error(`RADIUS: Error getting groups for user ${email}:`, error);
      return [];
    }
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} - True if valid email format
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if email domain is allowed
   * @param {string} email - Email to check
   * @returns {boolean} - True if domain is allowed
   */
  isAllowedDomain(email) {
    if (!this.config.allowedDomains || this.config.allowedDomains.length === 0) {
      return true; // No domain restrictions
    }

    const domain = email.split('@')[1];
    return this.config.allowedDomains.includes(domain);
  }

  /**
   * Check if the authentication is properly configured
   * @returns {boolean} - True if properly configured
   */
  isConfigured() {
    return !!(
      (this.config.serviceAccountEmail && this.config.serviceAccountKey) ||
      (this.config.clientId && this.config.clientSecret)
    );
  }

  /**
   * Get authentication status
   * @returns {Promise<boolean>} - True if authenticated
   */
  async isAuthenticated() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Test authentication by making a simple API call
      // Use users.get with the impersonation email instead of users.list
      // This is more likely to succeed even with limited permissions
      try {
        await this.admin.users.get({ userKey: this.config.impersonationEmail });
        return true;
      } catch (apiError) {
        // If it's just a permission issue (403), we're still authenticated
        if (apiError.code === 403) {
          console.log('RADIUS: Authentication succeeded but permission issues detected');
          return true;
        }
        throw apiError;
      }
    } catch (error) {
      console.error('RADIUS: Google Workspace authentication test failed:', error);
      return false;
    }
  }

  /**
   * Get configuration status for debugging
   * @returns {Object} - Configuration status
   */
  getConfigStatus() {
    return {
      configured: this.isConfigured(),
      hasServiceAccount: !!(this.config.serviceAccountEmail && this.config.serviceAccountKey),
      hasOAuth: !!(this.config.clientId && this.config.clientSecret),
      hasTokenFile: fs.existsSync(this.config.tokenPath),
      allowedDomains: this.config.allowedDomains,
      initialized: this.isInitialized
    };
  }
}

module.exports = GoogleWorkspaceAuth;