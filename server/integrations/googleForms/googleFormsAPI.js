// Google Forms API Wrapper
const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { refreshAndSaveTokens } = require('../../utils/tokenRefresh');
const integrationTracker = require('../../utils/integrationTracker');
const { getAuthenticatedClient } = require('../../utils/googleServiceAuth');

/**
 * Google Forms API Wrapper
 * Documentation: https://developers.google.com/forms/api/reference/rest
 */
class GoogleFormsAPI {
  constructor(clientId, clientSecret, redirectUri, tokenPath, userTokens = null, userId = null, userEmail = null) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.tokenPath = tokenPath;
    this.userTokens = userTokens;
    this.userId = userId;
    this.userEmail = userEmail;
    this.scopes = [
      'https://www.googleapis.com/auth/forms.body',
      'https://www.googleapis.com/auth/forms.responses.readonly',
      'https://www.googleapis.com/auth/drive.readonly'
    ];
    this.auth = null;
    this.forms = null;
    this.usingServiceAccount = false;
  }

  /**
   * Initialize the Google Forms API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const integrationName = 'Google Forms';
      
      // Always use service account authentication
      try {
        console.log('Using service account authentication for Google Forms API');
        
        // If no user email is provided, throw an error
        if (!this.userEmail) {
          console.error('Google Forms API initialization failed: No user email provided for service account authentication');
          throw new Error('No user email provided for service account authentication');
        }
        
        // Check service account credentials with detailed logging
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL) {
          console.error('Google Forms API initialization failed: GOOGLE_SERVICE_ACCOUNT_EMAIL environment variable is missing');
          throw new Error('Service account email is missing. Please check your environment variables.');
        }
        
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY) {
          console.error('Google Forms API initialization failed: GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY environment variable is missing');
          throw new Error('Service account private key is missing. Please check your environment variables.');
        }
        
        // Log the service account email being used (but not the private key for security)
        console.log(`Attempting to authenticate with service account: ${process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL}`);
        console.log(`Impersonating user: ${this.userEmail}`);
        
        try {
          this.auth = await getAuthenticatedClient('Forms', this.scopes, this.userEmail);
          this.forms = google.forms({ version: 'v1', auth: this.auth });
          this.usingServiceAccount = true;
          
          console.log('Google Forms API successfully authenticated with service account');
          
          // Update the integration status to active
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Integration is properly authenticated using service account.'
          );
          
          return;
        } catch (authError) {
          console.error('Google Forms API authentication failed with service account:', authError);
          console.error('Authentication error details:', JSON.stringify({
            message: authError.message,
            code: authError.code,
            status: authError.status,
            response: authError.response ? {
              status: authError.response.status,
              statusText: authError.response.statusText,
              data: authError.response.data
            } : 'No response data'
          }, null, 2));
          throw authError;
        }
      } catch (serviceAccountError) {
        console.error('Error initializing with service account:', serviceAccountError);
        
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          integrationName, 
          'error', 
          null, 
          `Error with service account: ${serviceAccountError.message}`
        );
        
        throw serviceAccountError;
      }
    } catch (error) {
      console.error('Error initializing Google Forms API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        'Google Forms', 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Generate authentication URL for OAuth2 flow
   * @returns {string} Authentication URL
   */
  getAuthUrl() {
    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: this.scopes,
      prompt: 'consent'
    });
  }

  /**
   * Get access token from authorization code
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Token object
   */
  async getToken(code) {
    try {
      const integrationName = 'Google Forms';
      const { tokens } = await this.auth.getToken(code);
      this.auth.setCredentials(tokens);

      // Save token to file for future use
      fs.writeFileSync(this.tokenPath, JSON.stringify(tokens));

      // Update the integration status to active
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'active', 
          new Date(), 
          'Successfully obtained new authentication token.'
        );
      }

      return tokens;
    } catch (error) {
      console.error('Error getting token:', error);

      // Update the integration status to indicate an error
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          'Google Forms', 
          'error', 
          null, 
          `Error getting token: ${error.message}`
        );
      }

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    const integrationName = 'Google Forms';

    // If using service account, we're authenticated as long as we have an auth object
    if (this.usingServiceAccount) {
      if (this.auth) {
        // Update the tracker to show we're authenticated with service account
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Integration is properly authenticated using service account.'
          );
        }
        return true;
      } else {
        // Update the tracker if service account auth failed
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'error', 
            null, 
            'Service account authentication failed. Check credentials.'
          );
        }
        return false;
      }
    }

    // For OAuth authentication, check if we have a token
    if (!this.auth || !this.auth.credentials || !this.auth.credentials.access_token) {
      // Update the tracker if we're not authenticated
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'not_configured', 
          null, 
          'No valid authentication token found.'
        );
      }
      return false;
    }

    // Then check if the token has all the required scopes
    const currentScopes = this.getScopes();
    const hasAllScopes = this.scopes.every(scope => 
      currentScopes.includes(scope) || 
      // Handle case where token has broader scope that includes our required scope
      currentScopes.some(currentScope => scope.startsWith(currentScope))
    );

    // Update the tracker based on the authentication status
    if (!this.userTokens && hasAllScopes) { // Only update for global tokens, not user-specific ones
      integrationTracker.updateStatus(
        integrationName, 
        'active', 
        new Date(), 
        'Integration is properly authenticated and ready to use.'
      );
    } else if (!this.userTokens && !hasAllScopes) {
      integrationTracker.updateStatus(
        integrationName, 
        'needs_auth', 
        null, 
        'Token does not have all required scopes. Need to re-authenticate.'
      );
    }

    return hasAllScopes;
  }

  /**
   * Get the current scopes from the token
   * @returns {Array} List of scopes
   */
  getScopes() {
    if (this.auth && this.auth.credentials && this.auth.credentials.scope) {
      return this.auth.credentials.scope.split(' ');
    }
    return [];
  }

  /**
   * List forms
   * @param {string} [userEmail] - Optional user email to filter forms by permission
   * @returns {Promise<Array>} List of forms
   */
  async listForms(userEmail) {
    try {
      console.log('Google Forms API: Attempting to list forms');
      
      if (!this.isAuthenticated()) {
        console.error('Google Forms API: Authentication check failed in listForms');
        throw new Error('Not authenticated');
      }

      // Log whether we're filtering by user email
      if (userEmail) {
        console.log(`Google Forms API: Filtering forms for user: ${userEmail}`);
      } else {
        console.warn('Google Forms API: No user email provided for filtering forms');
      }

      try {
        return await this.executeWithTokenRefresh(async () => {
          // Google Forms API doesn't have a direct method to list forms
          // We need to use the Drive API to list files of type 'application/vnd.google-apps.form'
          const drive = google.drive({ version: 'v3', auth: this.auth });
          
          // If userEmail is provided, filter forms by permission
          if (userEmail) {
            return this.listUserForms(userEmail, drive);
          }
          
          // Otherwise, return all forms the authenticated user can access
          console.log('Google Forms API: Listing all forms accessible to the authenticated service');
          const response = await drive.files.list({
            q: "mimeType='application/vnd.google-apps.form'",
            fields: 'files(id, name, description, createdTime, modifiedTime, webViewLink)'
          });
          
          console.log(`Google Forms API: Successfully retrieved ${response.data.files.length} forms`);
          return response.data.files;
        });
      } catch (apiError) {
        console.error('Google Forms API: Error executing files.list API call:', apiError);
        console.error('Google Forms API: Error details:', JSON.stringify({
          message: apiError.message,
          code: apiError.code,
          status: apiError.status,
          response: apiError.response ? {
            status: apiError.response.status,
            statusText: apiError.response.statusText,
            data: apiError.response.data
          } : 'No response data'
        }, null, 2));
        
        // Add more context to the error
        const enhancedError = new Error(`Failed to list forms from Google Forms: ${apiError.message}`);
        enhancedError.originalError = apiError;
        enhancedError.context = {
          usingServiceAccount: this.usingServiceAccount,
          userEmail: this.userEmail,
          isAuthenticated: this.isAuthenticated()
        };
        throw enhancedError;
      }
    } catch (error) {
      console.error('Google Forms API: Error in listForms method:', error);
      throw error;
    }
  }
  
  /**
   * List forms that a specific user has access to
   * @param {string} userEmail - User email to filter forms by permission
   * @param {Object} [driveClient] - Optional Drive API client (to avoid creating a new one)
   * @returns {Promise<Array>} List of forms the user has access to
   */
  async listUserForms(userEmail, driveClient) {
    try {
      console.log(`Google Forms API: Attempting to list forms for user: ${userEmail}`);
      
      if (!this.isAuthenticated()) {
        console.error('Google Forms API: Authentication check failed in listUserForms');
        throw new Error('Not authenticated');
      }
      
      if (!userEmail) {
        console.error('Google Forms API: No user email provided for filtering forms');
        throw new Error('User email is required');
      }

      try {
        return await this.executeWithTokenRefresh(async () => {
          // Use provided Drive client or create a new one
          const drive = driveClient || google.drive({ version: 'v3', auth: this.auth });
          
          // Query for forms that the user has access to
          // This uses the 'owners' or 'writers' or 'readers' parameter to filter by permission
          console.log(`Google Forms API: Querying for forms accessible to user: ${userEmail}`);
          
          const query = `mimeType='application/vnd.google-apps.form' and (
                         '${userEmail}' in owners or 
                         '${userEmail}' in writers or 
                         '${userEmail}' in readers
                       )`;
          console.log(`Google Forms API: Using query: ${query}`);
          
          const response = await drive.files.list({
            q: query,
            fields: 'files(id, name, description, createdTime, modifiedTime, webViewLink, permissions)'
          });
          
          console.log(`Google Forms API: Successfully retrieved ${response.data.files.length} forms for user: ${userEmail}`);
          
          // Return the filtered forms
          return response.data.files;
        });
      } catch (apiError) {
        console.error('Google Forms API: Error executing files.list API call in listUserForms:', apiError);
        console.error('Google Forms API: Error details:', JSON.stringify({
          message: apiError.message,
          code: apiError.code,
          status: apiError.status,
          response: apiError.response ? {
            status: apiError.response.status,
            statusText: apiError.response.statusText,
            data: apiError.response.data
          } : 'No response data'
        }, null, 2));
        
        // Add more context to the error
        const enhancedError = new Error(`Failed to list forms for user ${userEmail}: ${apiError.message}`);
        enhancedError.originalError = apiError;
        enhancedError.context = {
          usingServiceAccount: this.usingServiceAccount,
          userEmail: this.userEmail,
          filterUserEmail: userEmail,
          isAuthenticated: this.isAuthenticated()
        };
        throw enhancedError;
      }
    } catch (error) {
      console.error(`Google Forms API: Error in listUserForms method for user ${userEmail}:`, error);
      throw error;
    }
  }

  /**
   * Get form details
   * @param {string} formId Form ID
   * @returns {Promise<Object>} Form details
   */
  async getForm(formId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.forms.forms.get({
          formId
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting form:', error);
      throw error;
    }
  }

  /**
   * Get form responses
   * @param {string} formId Form ID
   * @returns {Promise<Object>} Form responses
   */
  async getFormResponses(formId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.forms.forms.responses.list({
          formId
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting form responses:', error);
      throw error;
    }
  }

  /**
   * Create a new form
   * @param {Object} formData Form data
   * @returns {Promise<Object>} Created form
   */
  async createForm(formData) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.forms.forms.create({
          requestBody: formData
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error creating form:', error);
      throw error;
    }
  }

  /**
   * Update an existing form
   * @param {string} formId Form ID
   * @param {Object} formData Form data
   * @returns {Promise<Object>} Updated form
   */
  async updateForm(formId, formData) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.forms.forms.batchUpdate({
          formId,
          requestBody: {
            requests: formData
          }
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error updating form:', error);
      throw error;
    }
  }

  /**
   * Execute an API request with automatic token refresh if needed
   * @param {Function} apiCall - Function that makes the API call
   * @returns {Promise<any>} API response
   */
  async executeWithTokenRefresh(apiCall) {
    try {
      // First attempt to execute the API call
      return await apiCall();
    } catch (error) {
      const integrationName = 'Google Forms';

      // Check if the error is due to an expired token
      const isAuthError = 
        error.code === 401 || 
        error.code === 403 || 
        (error.response && (error.response.status === 401 || error.response.status === 403)) ||
        (error.message && (
          error.message.includes('invalid_grant') || 
          error.message.includes('Invalid Credentials') || 
          error.message.includes('token expired')
        ));

      // If it's an auth error and we have a refresh token, try to refresh
      if (isAuthError && this.auth && this.userId) {
        try {
          console.log('Token expired, attempting to refresh...');

          // Refresh the token
          await refreshAndSaveTokens(this.auth, this.userId);

          // Update the integration status to indicate token was refreshed
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'active', 
              new Date(), 
              'Token was successfully refreshed.'
            );
          }

          // Retry the API call with the new token
          return await apiCall();
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // Update the integration status to indicate an error with token refresh
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'error', 
              null, 
              `Error refreshing token: ${refreshError.message}`
            );
          }

          throw refreshError;
        }
      } else if (isAuthError) {
        // It's an auth error but we can't refresh the token
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'needs_auth', 
            null, 
            'Authentication error occurred and token could not be refreshed. Need to re-authenticate.'
          );
        }
        throw error;
      } else {
        // If it's not an auth error, rethrow the original error
        throw error;
      }
    }
  }
}

module.exports = GoogleFormsAPI;