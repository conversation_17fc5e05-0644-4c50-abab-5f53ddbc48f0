// SkyportCloud API Wrapper for HVAC Controls
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * SkyportCloud API Wrapper for HVAC Controls
 * 
 * Note: This implementation is based on assumptions about the SkyportCloud API.
 * It should be updated once the actual API documentation is available.
 */
class SkyportCloudAPI {
  constructor(options = {}, baseUrl = 'https://api.skyportcloud.com') {
    // Support both authentication methods: API key or username/password
    this.apiKey = options.apiKey;
    this.username = options.username;
    this.password = options.password;
    this.baseUrl = baseUrl;
    this.integrationName = 'SkyportCloud';
    this.authToken = null;

    // Create axios instance with default headers
    this.axios = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // If API key is provided, set the Authorization header
    if (this.apiKey) {
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
    }
  }

  /**
   * Login with username and password
   * @returns {Promise<string>} Authentication token
   */
  async login() {
    try {
      if (!this.username || !this.password) {
        throw new Error('Username and password are required for login');
      }

      // Make a login request to get an authentication token
      const response = await axios.post(`${this.baseUrl}/v1/auth/login`, {
        username: this.username,
        password: this.password
      });

      // Store the authentication token
      this.authToken = response.data.token;
      
      // Update the axios instance with the new token
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
      
      return this.authToken;
    } catch (error) {
      console.error('Error logging in to SkyportCloud:', error);
      throw error;
    }
  }

  /**
   * Initialize the SkyportCloud API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we have authentication credentials
      if (!this.apiKey && (!this.username || !this.password)) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Either API key or username/password is required for SkyportCloud integration.'
        );
        return;
      }

      // If we have username/password but no API key, login to get a token
      if (!this.apiKey && this.username && this.password) {
        await this.login();
      }

      // Test the connection by getting user info
      await this.getUserInfo();

      // If we get here, the connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly configured and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing SkyportCloud API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get user information
   * @returns {Promise<Object>} User information
   */
  async getUserInfo() {
    try {
      const response = await this.axios.get('/v1/user');
      return response.data;
    } catch (error) {
      console.error('Error fetching SkyportCloud user information:', error);
      throw error;
    }
  }

  /**
   * Get list of devices
   * @returns {Promise<Array>} List of devices
   */
  async getDevices() {
    try {
      const response = await this.axios.get('/v1/devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching SkyportCloud devices:', error);
      throw error;
    }
  }

  /**
   * Get device information
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device information
   */
  async getDeviceInfo(deviceId) {
    try {
      const response = await this.axios.get(`/v1/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching SkyportCloud device ${deviceId} information:`, error);
      throw error;
    }
  }

  /**
   * Get device status
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device status
   */
  async getDeviceStatus(deviceId) {
    try {
      const response = await this.axios.get(`/v1/devices/${deviceId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching SkyportCloud device ${deviceId} status:`, error);
      throw error;
    }
  }

  /**
   * Set device temperature
   * @param {string} deviceId Device ID
   * @param {number} temperature Temperature in degrees (unit depends on device settings)
   * @returns {Promise<Object>} Response data
   */
  async setTemperature(deviceId, temperature) {
    try {
      const response = await this.axios.post(`/v1/devices/${deviceId}/temperature`, {
        temperature
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting temperature for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device mode
   * @param {string} deviceId Device ID
   * @param {string} mode Mode ('heat', 'cool', 'auto', 'off')
   * @returns {Promise<Object>} Response data
   */
  async setMode(deviceId, mode) {
    try {
      const response = await this.axios.post(`/v1/devices/${deviceId}/mode`, {
        mode
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting mode for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set fan mode
   * @param {string} deviceId Device ID
   * @param {string} fanMode Fan mode ('auto', 'on', 'circulate')
   * @returns {Promise<Object>} Response data
   */
  async setFanMode(deviceId, fanMode) {
    try {
      const response = await this.axios.post(`/v1/devices/${deviceId}/fan`, {
        mode: fanMode
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting fan mode for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get device schedule
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Schedule data
   */
  async getSchedule(deviceId) {
    try {
      const response = await this.axios.get(`/v1/devices/${deviceId}/schedule`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching schedule for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device schedule
   * @param {string} deviceId Device ID
   * @param {Object} schedule Schedule object
   * @returns {Promise<Object>} Response data
   */
  async setSchedule(deviceId, schedule) {
    try {
      const response = await this.axios.post(`/v1/devices/${deviceId}/schedule`, schedule);
      return response.data;
    } catch (error) {
      console.error(`Error setting schedule for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get device energy usage
   * @param {string} deviceId Device ID
   * @param {string} period Period ('day', 'week', 'month', 'year')
   * @returns {Promise<Object>} Energy usage data
   */
  async getEnergyUsage(deviceId, period = 'month') {
    try {
      const response = await this.axios.get(`/v1/devices/${deviceId}/energy`, {
        params: { period }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching energy usage for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set hold status
   * @param {string} deviceId Device ID
   * @param {boolean} holdEnabled Whether hold is enabled
   * @param {number} holdUntil Timestamp until when the hold should be active (optional)
   * @returns {Promise<Object>} Response data
   */
  async setHold(deviceId, holdEnabled, holdUntil = null) {
    try {
      const data = { enabled: holdEnabled };
      if (holdUntil) {
        data.until = holdUntil;
      }
      const response = await this.axios.post(`/v1/devices/${deviceId}/hold`, data);
      return response.data;
    } catch (error) {
      console.error(`Error setting hold for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set away mode
   * @param {string} deviceId Device ID
   * @param {boolean} awayEnabled Whether away mode is enabled
   * @returns {Promise<Object>} Response data
   */
  async setAwayMode(deviceId, awayEnabled) {
    try {
      const response = await this.axios.post(`/v1/devices/${deviceId}/away`, {
        enabled: awayEnabled
      });
      return response.data;
    } catch (error) {
      console.error(`Error setting away mode for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get device zones (for multi-zone systems)
   * @param {string} deviceId Device ID
   * @returns {Promise<Array>} List of zones
   */
  async getZones(deviceId) {
    try {
      const response = await this.axios.get(`/v1/devices/${deviceId}/zones`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching zones for SkyportCloud device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set zone settings
   * @param {string} deviceId Device ID
   * @param {string} zoneId Zone ID
   * @param {Object} settings Zone settings
   * @returns {Promise<Object>} Response data
   */
  async setZoneSettings(deviceId, zoneId, settings) {
    try {
      const response = await this.axios.post(`/v1/devices/${deviceId}/zones/${zoneId}`, settings);
      return response.data;
    } catch (error) {
      console.error(`Error setting zone settings for SkyportCloud device ${deviceId}, zone ${zoneId}:`, error);
      throw error;
    }
  }
}

module.exports = SkyportCloudAPI;