// Apple Business Manager API Wrapper
const axios = require('axios');
const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const integrationTracker = require('../../utils/integrationTracker');
const cacheUtil = require('../../utils/cacheUtil');

/**
 * Apple Business Manager API Wrapper
 * Documentation: https://developer.apple.com/documentation/applebusinessmanagerapi/
 * OAuth Setup: https://support.apple.com/guide/apple-business-manager/create-an-api-account-axm33189f66a/web
 */
class AppleBusinessManagerAPI {
  constructor(clientId, keyId, privateKeyPathOrContent, tokenExpiry = 10 * 60) { // 20 minutes default
    this.clientId = clientId;
    this.keyId = keyId;
    this.privateKeyPathOrContent = privateKeyPathOrContent;
    // Ensure tokenExpiry is a number
    this.tokenExpiry = parseInt(tokenExpiry, 10) || 20 * 60;
    this.baseURL = 'https://api-business.apple.com';
    this.token = null;
    this.tokenExpiration = null;
    this.integrationName = 'Apple Business Manager';
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    // Determine if privateKeyPathOrContent is a file path or the actual key content
    // If it starts with '-----BEGIN PRIVATE KEY-----', it's the key content
    this.isPrivateKeyContent = typeof privateKeyPathOrContent === 'string' && 
                              privateKeyPathOrContent.trim().startsWith('-----BEGIN EC PRIVATE KEY-----');
  }

  /**
   * Initialize the Apple Business Manager API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if all required credentials are provided
      if (!this.clientId ||
          !this.keyId || !this.privateKeyPathOrContent) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Client ID, Key ID, Private Key, and Issuer ID are required for Apple Business Manager integration.'
        );
        return;
      }

      // Check if privateKeyPathOrContent is defined and not empty
      if (typeof this.privateKeyPathOrContent !== 'string' || this.privateKeyPathOrContent.trim() === '') {
        integrationTracker.updateStatus(
          this.integrationName,
          'error',
          null,
          'Private key path or content is undefined, empty, or not a string'
        );
        throw new Error('Private key path or content is undefined, empty, or not a string');
      }

      // If it's not the key content directly, check if the file exists
      if (!this.isPrivateKeyContent) {
        // Check if private key file exists
        if (!fs.existsSync(this.privateKeyPathOrContent)) {
          integrationTracker.updateStatus(
            this.integrationName,
            'error',
            null,
            `Private key file not found at path: ${this.privateKeyPathOrContent}`
          );
          throw new Error(`Private key file not found at path: ${this.privateKeyPathOrContent}`);
        }
      }

      // Authenticate with the API
      await this.authenticate();

      // Test the connection by making a simple API call
      await this.getDevices({ limit: 1 });

      // If we get here, the connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly authenticated and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing Apple Business Manager API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Generate a JWT token for Apple Business Manager API authentication
   * Following the official Apple Business Manager API documentation format
   * @returns {string} JWT token
   * @private
   */
  _generateJWT() {
    try {
      // Check if privateKeyPathOrContent is defined and not empty
      if (typeof this.privateKeyPathOrContent !== 'string' || this.privateKeyPathOrContent.trim() === '') {
        throw new Error('Private key path or content is undefined, empty, or not a string');
      }

      // Get the private key - either directly from the content or by reading the file
      let privateKey;
      if (this.isPrivateKeyContent) {
        // Use the content directly
        privateKey = this.privateKeyPathOrContent;
      } else {
        // Read from file
        privateKey = fs.readFileSync(this.privateKeyPathOrContent, 'utf8');
      }

      // Current time in seconds
      const now = Math.floor(Date.now() / 1000);

      // JWT payload following official Apple Business Manager API documentation
      // The subject and issuer should be prefixed with "BUSINESSAPI."
      const payload = {
        iat: now,
        exp: now + this.tokenExpiry,
        aud: 'https://account.apple.com/auth/oauth2/v2/token',
        iss: this.clientId,
        sub: this.clientId,
        jti: crypto.randomUUID()
      };

      // JWT header
      const header = {
        alg: 'ES256',
        kid: this.keyId
      };

      // Sign the JWT
      return jwt.sign(payload, privateKey, {
        algorithm: 'ES256',
        header: header
      });
    } catch (error) {
      console.error('Error generating JWT token:', error);
      throw error;
    }
  }

  /**
   * Authenticate with Apple Business Manager API
   * This method implements token caching to reduce authentication calls to Apple's servers.
   * It follows this process:
   * 1. Check if a valid token exists in the shared cache
   * 2. If not, check if the instance has a valid token
   * 3. If not, request a new token from Apple
   * 4. Cache the new token for future use by all instances
   * 
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      // Create a cache key for this client based on unique identifiers
      // This ensures different API clients get different cached tokens
      const cacheKey = cacheUtil.createKey('apple-business-manager-token', {
        clientId: this.clientId,
        keyId: this.keyId
      });
      
      // Check if we have a valid token in the shared cache
      // This allows token sharing across different instances of the API client
      const cachedToken = cacheUtil.get(cacheKey);
      if (cachedToken) {
        console.log('Using cached token for Apple Business Manager API');
        this.token = cachedToken.token;
        this.tokenExpiration = cachedToken.expiration;
        // Set the token for all future requests
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
        return this.token;
      }
      
      // Fallback: Check if instance token is still valid
      // This is a secondary check in case the token wasn't in the cache
      if (this.token && this.tokenExpiration && Date.now() < this.tokenExpiration) {
        return this.token;
      }

      // Using JWT bearer flow for authentication
      console.log('Using JWT bearer flow for authentication');
      
      // Generate JWT token
      const jwtToken = this._generateJWT();
      console.log('Generated JWT token for authentication');

      // Prepare request parameters for JWT bearer flow
        
      const requestParams = {
        grant_type: 'client_credentials',
        client_id: this.clientId,
        scope: 'business.api',
        client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',
        client_assertion: jwtToken
      };
      
      console.log('Authentication request parameters:', JSON.stringify({
        ...requestParams,
        client_assertion: '[REDACTED]' // Don't log the actual JWT token
      }, null, 2));

      // Exchange JWT token for access token
      try {
        // Use the specific OAuth v2 endpoint for Apple Business Manager
        const oauthEndpoint = 'https://account.apple.com/auth/oauth2/v2/token';
        
        // Use this.axios but with the full URL to override the baseURL for this specific request
        const requestBody = new URLSearchParams(requestParams).toString();
        const response = await this.axios.post(oauthEndpoint, requestBody, {
          baseURL: '',
          headers: {
            'User-Agent': 'csfportal/1.0',
            'Content-Type': 'application/x-www-form-urlencoded'
          }

        });
        
        console.log('Authentication response status:', response.status);
        console.log('Authentication response headers:', JSON.stringify(response.headers, null, 2));
        console.log('Authentication response data:', JSON.stringify(response.data, null, 2));
        
        // Store the access token from the response
        this.token = response.data.access_token;
        
        // Calculate token expiration time in milliseconds
        // We subtract a 5-minute safety buffer to ensure we refresh before the token actually expires
        const expiresIn = response.data.expires_in * 1000; // Convert seconds to milliseconds
        this.tokenExpiration = Date.now() + expiresIn - (5 * 60 * 1000);
        
        // Store the token in the shared cache for use by all API instances
        // The cache entry includes both the token and its expiration timestamp
        // The cache duration is set to match the token's expiration (minus safety buffer)
        // This ensures the cache entry will be automatically invalidated when the token expires
        cacheUtil.set(cacheKey, {
          token: this.token,
          expiration: this.tokenExpiration
        }, expiresIn - (5 * 60 * 1000));
        
        console.log(`Cached Apple Business Manager token with expiration ${new Date(this.tokenExpiration).toISOString()}`);
        
        // Set the token for all future requests made by this instance
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;

        return this.token;
      } catch (error) {
        console.error('Error in authentication request:');
        if (error.response) {
          console.error('Error response status:', error.response.status);
          console.error('Error response headers:', JSON.stringify(error.response.headers, null, 2));
          console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
          console.error('No response received. Request:', error.request);
        } else {
          console.error('Error setting up request:', error.message);
        }
        throw error;
      }
    } catch (error) {
      console.error('Error authenticating with Apple Business Manager API:', error.message);
      throw error;
    }
  }

  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  async getDevices(params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Rate limiting
      await this._rateLimitDelay();

      // Using the correct endpoint for devices from the Apple Business Manager API
      const response = await this.axios.get(`/v1/orgDevices`, {
        params: {
          ...params,
          limit: params.limit || 100
        }
      });

      return response.data.data || [];
    } catch (error) {
      this._handleApiError(error, 'fetching devices');
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device details from the Apple Business Manager API
      const response = await this.axios.get(`/v1/orgDevices/${deviceId}`);

      return response.data.data || {};
    } catch (error) {
      console.error(`Error fetching Apple Business Manager device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get device MDM assignment information
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} MDM assignment information
   */
  async getDeviceMdmAssignment(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for device MDM assignment from the Apple Business Manager API
      const response = await this.axios.get(`/v1/orgDevices/${deviceId}/assignedServer`);

      const assignedServerData = response.data.data || {};

      // Transform the response to match frontend expectations
      if (assignedServerData && assignedServerData.id) {
        return {
          mdmServerId: assignedServerData.id,
          serverName: assignedServerData.attributes?.serverName || 'Unknown Server',
          serverType: assignedServerData.attributes?.serverType || 'Unknown',
          assignmentDate: assignedServerData.attributes?.createdDateTime || null,
          lastUpdated: assignedServerData.attributes?.updatedDateTime || null,
          // Keep the original data for reference
          originalData: assignedServerData
        };
      }

      return null; // No assignment found
    } catch (error) {
      console.error(`Error fetching MDM assignment for device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Assign device to MDM service using orgDeviceActivities endpoint
   * @param {string} deviceId Device ID
   * @param {string} mdmServerId MDM service ID
   * @returns {Promise<Object>} Assignment activity result
   */
  async assignDeviceToMdm(deviceId, mdmServerId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Use the orgDeviceActivities endpoint for device assignment
      return await this.createDeviceActivity('ASSIGN_DEVICES', [deviceId], mdmServerId);
    } catch (error) {
      console.error(`Error assigning device ${deviceId} to MDM service ${mdmServerId}:`, error);
      throw error;
    }
  }

  /**
   * Reassign device to a different MDM service using orgDeviceActivities endpoint
   * @param {string} deviceId Device ID
   * @param {string} mdmServerId New MDM service ID
   * @returns {Promise<Object>} Reassignment activity result
   */
  async reassignDeviceToMdm(deviceId, mdmServerId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Use the orgDeviceActivities endpoint for device reassignment
      // Note: Reassignment is the same as assignment in the API
      return await this.createDeviceActivity('ASSIGN_DEVICES', [deviceId], mdmServerId);
    } catch (error) {
      console.error(`Error reassigning device ${deviceId} to MDM service ${mdmServerId}:`, error);
      throw error;
    }
  }

  /**
   * Unassign device from MDM service using orgDeviceActivities endpoint
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Unassignment activity result
   */
  async unassignDeviceFromMdm(deviceId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Use the orgDeviceActivities endpoint for device unassignment
      return await this.createDeviceActivity('UNASSIGN_DEVICES', [deviceId]);
    } catch (error) {
      console.error(`Error unassigning device ${deviceId} from MDM service:`, error);
      throw error;
    }
  }



  /**
   * Get all MDM servers
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of MDM servers
   */
  async getMdmServers(params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Using the correct endpoint for MDM servers from the Apple Business Manager API
      const response = await this.axios.get(`/v1/mdmServers`, {
        params: {
          ...params,
          limit: params.limit || 100
        }
      });

      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching Apple Business Manager MDM servers:', error);
      throw error;
    }
  }

  /**
   * Get devices associated with a specific MDM server
   * Note: Apple Business Manager API doesn't provide a direct endpoint for this.
   * This method fetches all devices and filters by MDM assignment.
   * @param {string} mdmServerId MDM server ID
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices assigned to the MDM server
   */
  async getMdmServerDevices(mdmServerId, params = {}) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Since Apple doesn't provide a direct endpoint to get devices by MDM server,
      // we need to fetch all devices and check their MDM assignments
      const allDevices = await this.getAllDevices(params, 5); // Limit to 5 pages for performance
      const assignedDevices = [];

      // Check MDM assignment for each device
      for (const device of allDevices) {
        try {
          await this._rateLimitDelay(); // Respect rate limits
          const assignment = await this.getDeviceMdmAssignment(device.id);

          // Check if device is assigned to the specified MDM server
          if (assignment && assignment.id === mdmServerId) {
            assignedDevices.push(device);
          }
        } catch (error) {
          // Log but don't fail the entire operation for individual device errors
          console.warn(`Could not check MDM assignment for device ${device.id}:`, error.message);
        }
      }

      return assignedDevices;
    } catch (error) {
      console.error(`Error fetching devices for MDM server ${mdmServerId}:`, error);
      throw error;
    }
  }

  /**
   * Create a bulk device activity (assign/unassign devices)
   * @param {string} activityType - 'ASSIGN_DEVICES' or 'UNASSIGN_DEVICES'
   * @param {Array<string>} deviceIds - Array of device IDs
   * @param {string|null} mdmServerId - MDM server ID (null for unassign)
   * @returns {Promise<Object>} Activity response with activity ID
   */
  async createDeviceActivity(activityType, deviceIds, mdmServerId = null) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Validate activity type
      if (!['ASSIGN_DEVICES', 'UNASSIGN_DEVICES'].includes(activityType)) {
        throw new Error('Invalid activity type. Must be ASSIGN_DEVICES or UNASSIGN_DEVICES');
      }

      // Validate device IDs
      if (!Array.isArray(deviceIds) || deviceIds.length === 0) {
        throw new Error('Device IDs must be a non-empty array');
      }

      // For assign operations, MDM server ID is required
      if (activityType === 'ASSIGN_DEVICES' && !mdmServerId) {
        throw new Error('MDM server ID is required for ASSIGN_DEVICES activity');
      }

      // Build the activity payload according to the official API documentation
      const activityData = {
        data: {
          type: 'orgDeviceActivities',
          attributes: {
            activityType: activityType
          },
          relationships: {
            devices: {
              data: deviceIds.map(id => ({
                type: 'orgDevices',
                id: id
              }))
            }
          }
        }
      };

      // Add MDM server relationship for assign operations
      if (activityType === 'ASSIGN_DEVICES' && mdmServerId) {
        activityData.data.relationships.mdmServer = {
          data: {
            type: 'mdmServers',
            id: mdmServerId
          }
        };
      }

      // Execute bulk assignment or unassignment
      const response = await this.axios.post('/v1/orgDeviceActivities', activityData);

      return response.data.data || {};
    } catch (error) {
      console.error(`Error creating device activity ${activityType}:`, error);
      throw error;
    }
  }

  /**
   * Get the status of a device activity
   * @param {string} activityId Activity ID
   * @returns {Promise<Object>} Activity status
   */
  async getDeviceActivityStatus(activityId) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      // Get activity status using the official endpoint
      const response = await this.axios.get(`/v1/orgDeviceActivities/${activityId}`);

      return response.data.data || {};
    } catch (error) {
      console.error(`Error fetching activity status for ${activityId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk assign devices to MDM server
   * @param {Array<string>} deviceIds Array of device IDs
   * @param {string} mdmServerId MDM server ID
   * @returns {Promise<Object>} Activity response
   */
  async bulkAssignDevicesToMdm(deviceIds, mdmServerId) {
    return this.createDeviceActivity('ASSIGN_DEVICES', deviceIds, mdmServerId);
  }

  /**
   * Bulk unassign devices from MDM server
   * @param {Array<string>} deviceIds Array of device IDs
   * @returns {Promise<Object>} Activity response
   */
  async bulkUnassignDevicesFromMdm(deviceIds) {
    return this.createDeviceActivity('UNASSIGN_DEVICES', deviceIds);
  }

  /**
   * Get all devices with automatic pagination
   * @param {Object} params Query parameters
   * @param {number} maxPages Maximum number of pages to fetch (default: 10)
   * @returns {Promise<Array>} Complete list of devices
   */
  async getAllDevices(params = {}, maxPages = 10) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      let allDevices = [];
      let currentPage = 1;
      let hasNextPage = true;
      let nextUrl = null;

      while (hasNextPage && currentPage <= maxPages) {
        const requestParams = {
          ...params,
          limit: params.limit || 100
        };

        // Use next URL if available, otherwise use base endpoint
        const endpoint = nextUrl || '/v1/orgDevices';
        const response = await this.axios.get(endpoint, {
          params: nextUrl ? {} : requestParams // Don't add params if using next URL
        });

        const data = response.data;
        if (data.data && Array.isArray(data.data)) {
          allDevices = allDevices.concat(data.data);
        }

        // Check for next page
        hasNextPage = data.links && data.links.next;
        if (hasNextPage) {
          // Extract the path from the full URL
          const nextFullUrl = data.links.next;
          nextUrl = nextFullUrl.replace(this.baseURL, '');
        }

        currentPage++;
      }

      return allDevices;
    } catch (error) {
      console.error('Error fetching all Apple Business Manager devices:', error);
      throw error;
    }
  }

  /**
   * Get all MDM servers with automatic pagination
   * @param {Object} params Query parameters
   * @param {number} maxPages Maximum number of pages to fetch (default: 10)
   * @returns {Promise<Array>} Complete list of MDM servers
   */
  async getAllMdmServers(params = {}, maxPages = 10) {
    try {
      if (!this.token) {
        await this.authenticate();
      }

      let allServers = [];
      let currentPage = 1;
      let hasNextPage = true;
      let nextUrl = null;

      while (hasNextPage && currentPage <= maxPages) {
        const requestParams = {
          ...params,
          limit: params.limit || 100
        };

        // Use next URL if available, otherwise use base endpoint
        const endpoint = nextUrl || '/v1/mdmServers';
        const response = await this.axios.get(endpoint, {
          params: nextUrl ? {} : requestParams // Don't add params if using next URL
        });

        const data = response.data;
        if (data.data && Array.isArray(data.data)) {
          allServers = allServers.concat(data.data);
        }

        // Check for next page
        hasNextPage = data.links && data.links.next;
        if (hasNextPage) {
          // Extract the path from the full URL
          const nextFullUrl = data.links.next;
          nextUrl = nextFullUrl.replace(this.baseURL, '');
        }

        currentPage++;
      }

      return allServers;
    } catch (error) {
      console.error('Error fetching all Apple Business Manager MDM servers:', error);
      throw error;
    }
  }

  /**
   * Rate limiting helper - ensures we don't exceed 100 requests per second
   * @private
   */
  async _rateLimitDelay() {
    // Apple Business Manager API allows 100 requests per second
    // Add a small delay to prevent hitting rate limits
    const delayMs = 10; // 10ms delay allows for ~100 requests per second
    return new Promise(resolve => setTimeout(resolve, delayMs));
  }

  /**
   * Enhanced error handling for API responses
   * @param {Error} error The error object
   * @param {string} operation The operation being performed
   * @private
   */
  _handleApiError(error, operation) {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 401:
          throw new Error(`Authentication failed for ${operation}. Please check your credentials.`);
        case 403:
          throw new Error(`Access forbidden for ${operation}. Please check your permissions.`);
        case 404:
          throw new Error(`Resource not found for ${operation}.`);
        case 429:
          throw new Error(`Rate limit exceeded for ${operation}. Please try again later.`);
        case 500:
          throw new Error(`Apple Business Manager API server error for ${operation}.`);
        default:
          throw new Error(`API error ${status} for ${operation}: ${data?.message || error.message}`);
      }
    } else if (error.request) {
      throw new Error(`Network error for ${operation}. Please check your connection.`);
    } else {
      throw new Error(`Unexpected error for ${operation}: ${error.message}`);
    }
  }
}

module.exports = AppleBusinessManagerAPI;
