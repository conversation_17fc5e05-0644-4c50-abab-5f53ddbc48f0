// Colorlit Z4 Pro LED Controller API Wrapper
const axios = require('axios');
const https = require('https');
const http = require('http');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Colorlit Z4 Pro LED Controller API Wrapper
 * This class provides methods to interact with the Colorlit Z4 Pro LED Controller
 */
class ColoritAPI {
  /**
   * Create a new ColoritAPI instance
   * @param {string} host - The hostname or IP address of the Colorlit controller
   * @param {number} port - The port number (default: 80)
   * @param {string} apiKey - The API key for authentication (if required)
   * @param {string} username - The username for authentication (if required)
   * @param {string} password - The password for authentication (if required)
   */
  constructor(host, port = 80, apiKey = '', username = '', password = '') {
    this.host = host;
    this.port = port;
    this.apiKey = apiKey;
    this.username = username;
    this.password = password;

    // Set the base URL based on the port (80 for HTTP, 443 for HTTPS)
    const protocol = port === 443 ? 'https' : 'http';
    this.baseURL = `${protocol}://${host}:${port}`;

    this.integrationName = 'Colorlit';
    
    // Create HTTP agent with improved configuration for better connection handling
    const httpAgent = new http.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000 // Free socket timeout
    });
    
    // Create HTTPS agent with configuration to allow self-signed certificates
    const httpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000, // Free socket timeout
      rejectUnauthorized: false // Allow self-signed SSL certificates
    });

    // Create axios instance for HTTP communication with Colorlit device
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive',
        'User-Agent': 'CSFPortal-Colorlit-Client/1.0'
      },
      // Add timeout configuration to prevent socket hang up errors
      timeout: 10000, // 10 seconds
      // Use the improved HTTP agent for HTTP requests
      httpAgent: httpAgent,
      // Use the HTTPS agent with self-signed certificate support for HTTPS requests
      httpsAgent: httpsAgent,
      // Disable automatic retries (we'll handle them manually)
      maxRedirects: 0,
      // Validate status codes
      validateStatus: (status) => status >= 200 && status < 300
    });

    // Add request interceptor for logging and retry logic
    this.axios.interceptors.request.use(
      (config) => {
        console.log(`Colorlit API Request: ${config.method?.toUpperCase()} ${config.url}`);
        
        // Add authentication if provided
        if (this.apiKey) {
          config.headers['X-API-Key'] = this.apiKey;
        } else if (this.username && this.password) {
          config.auth = {
            username: this.username,
            password: this.password
          };
        }
        
        return config;
      },
      (error) => {
        console.error('Colorlit API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.axios.interceptors.response.use(
      (response) => {
        console.log(`Colorlit API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const config = error.config;

        // Log detailed error information
        console.error('Colorlit API Response Error:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          url: config?.url,
          method: config?.method,
          timeout: config?.timeout,
          retryCount: config.__retryCount || 0
        });

        // Implement retry logic for specific errors
        if (this.shouldRetry(error) && (!config.__retryCount || config.__retryCount < 3)) {
          config.__retryCount = (config.__retryCount || 0) + 1;

          // Calculate exponential backoff delay
          const delay = Math.min(1000 * Math.pow(2, config.__retryCount - 1), 5000);

          console.log(`Retrying Colorlit API request (attempt ${config.__retryCount}/3) after ${delay}ms delay`);

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));

          // Retry the request
          return this.axios(config);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Determine if a request should be retried based on the error
   * @param {Error} error - The error object
   * @returns {boolean} - Whether the request should be retried
   */
  shouldRetry(error) {
    // Retry on network errors, timeouts, and certain HTTP status codes
    return (
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNABORTED' ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ENOTFOUND' ||
      (error.response && (error.response.status === 429 || error.response.status >= 500))
    );
  }

  /**
   * Initialize the integration and update its status
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      if (!this.host) {
        await integrationTracker.updateIntegrationStatus(this.integrationName, 'not_configured');
        return;
      }

      // Try to get device information to check if the integration is working
      await this.getDeviceInfo();
      await integrationTracker.updateIntegrationStatus(this.integrationName, 'online');
    } catch (error) {
      console.error('Error initializing Colorlit integration:', error);
      
      // Update integration status based on the error
      if (!this.host) {
        await integrationTracker.updateIntegrationStatus(this.integrationName, 'not_configured');
      } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        await integrationTracker.updateIntegrationStatus(this.integrationName, 'offline');
      } else {
        await integrationTracker.updateIntegrationStatus(this.integrationName, 'error', error.message);
      }
    }
  }

  /**
   * Get device information
   * @returns {Promise<Object>} - Device information
   */
  async getDeviceInfo() {
    try {
      const response = await this.axios.get('/api/device');
      return response.data;
    } catch (error) {
      console.error('Error getting Colorlit device information:', error);
      throw error;
    }
  }

  /**
   * Get the current status of the LED controller
   * @returns {Promise<Object>} - Current status
   */
  async getStatus() {
    try {
      const response = await this.axios.get('/api/status');
      return response.data;
    } catch (error) {
      console.error('Error getting Colorlit status:', error);
      throw error;
    }
  }

  /**
   * Turn the LED controller on or off
   * @param {boolean} power - Whether to turn the device on (true) or off (false)
   * @returns {Promise<Object>} - Response data
   */
  async setPower(power) {
    try {
      const response = await this.axios.post('/api/power', { power });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit power state:', error);
      throw error;
    }
  }

  /**
   * Set the color of the LEDs
   * @param {number} r - Red value (0-255)
   * @param {number} g - Green value (0-255)
   * @param {number} b - Blue value (0-255)
   * @param {number} w - White value (0-255, optional)
   * @returns {Promise<Object>} - Response data
   */
  async setColor(r, g, b, w = 0) {
    try {
      const response = await this.axios.post('/api/color', { r, g, b, w });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit color:', error);
      throw error;
    }
  }

  /**
   * Set the brightness of the LEDs
   * @param {number} brightness - Brightness value (0-100)
   * @returns {Promise<Object>} - Response data
   */
  async setBrightness(brightness) {
    try {
      const response = await this.axios.post('/api/brightness', { brightness });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit brightness:', error);
      throw error;
    }
  }

  /**
   * Set the mode of the LED controller
   * @param {string} mode - Mode name
   * @returns {Promise<Object>} - Response data
   */
  async setMode(mode) {
    try {
      const response = await this.axios.post('/api/mode', { mode });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit mode:', error);
      throw error;
    }
  }

  /**
   * Get available modes
   * @returns {Promise<Array>} - List of available modes
   */
  async getModes() {
    try {
      const response = await this.axios.get('/api/modes');
      return response.data;
    } catch (error) {
      console.error('Error getting Colorlit modes:', error);
      throw error;
    }
  }

  /**
   * Set the speed of the current effect
   * @param {number} speed - Speed value (0-100)
   * @returns {Promise<Object>} - Response data
   */
  async setSpeed(speed) {
    try {
      const response = await this.axios.post('/api/speed', { speed });
      return response.data;
    } catch (error) {
      console.error('Error setting Colorlit speed:', error);
      throw error;
    }
  }

  /**
   * Get the zones/groups configured on the controller
   * @returns {Promise<Array>} - List of zones/groups
   */
  async getZones() {
    try {
      const response = await this.axios.get('/api/zones');
      return response.data;
    } catch (error) {
      console.error('Error getting Colorlit zones:', error);
      throw error;
    }
  }

  /**
   * Select a zone/group to control
   * @param {string} zoneId - Zone/group ID
   * @returns {Promise<Object>} - Response data
   */
  async selectZone(zoneId) {
    try {
      const response = await this.axios.post('/api/zone/select', { zoneId });
      return response.data;
    } catch (error) {
      console.error('Error selecting Colorlit zone:', error);
      throw error;
    }
  }
}

module.exports = ColoritAPI;