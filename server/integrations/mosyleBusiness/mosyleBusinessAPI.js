// Mosyle Business API Wrapper
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Mosyle Business API Wrapper
 * Documentation: https://business.mosyle.com/api
 */
class MosyleBusinessAPI {
  constructor(apiKey, domain) {
    this.apiKey = apiKey;
    this.domain = domain;
    this.baseURL = `https://${domain}.mosyle.com/api/v2`;
    this.integrationName = 'Mosyle Business';
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
  }

  /**
   * Initialize the Mosyle Business API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if credentials are provided
      if (!this.apiKey || !this.domain) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'API Key and domain are required for Mosyle Business integration.'
        );
        return;
      }

      // Test the connection
      await this.getDevices({ limit: 1 });

      // If we get here, the connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly authenticated and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing Mosyle Business API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get all devices
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of devices
   */
  async getDevices(params = {}) {
    try {
      const response = await this.axios.get('/devices', { params });
      // Ensure we always return an array, even if the API returns something else
      return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
      console.error('Error fetching Mosyle Business devices:', error);
      throw error;
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      const response = await this.axios.get(`/devices/${deviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching Mosyle Business device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get all users
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of users
   */
  async getUsers(params = {}) {
    try {
      const response = await this.axios.get('/users', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle Business users:', error);
      throw error;
    }
  }

  /**
   * Get all groups
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of groups
   */
  async getGroups(params = {}) {
    try {
      const response = await this.axios.get('/groups', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching Mosyle Business groups:', error);
      throw error;
    }
  }
}

module.exports = MosyleBusinessAPI;
