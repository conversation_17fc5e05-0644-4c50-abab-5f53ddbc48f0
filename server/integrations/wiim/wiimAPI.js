// WiiM API Wrapper
const axios = require('axios');
const https = require('https');
const http = require('http');
const integrationTracker = require('../../utils/integrationTracker');
const querystring = require('querystring');

/**
 * WiiM API Wrapper
 * Based on:
 * - https://github.com/DanBrezeanu/wiim-extended-http-api
 * - https://www.wiimhome.com/pdf/HTTP%20API%20for%20WiiM%20Products.pdf
 */
class WiimAPI {
  constructor(host, port = 80) {
    this.host = host;
    this.port = port;

    this.baseURL = `https://${host}`;

    this.integrationName = 'WiiM';
    
    // WiiM Spotify device ID from environment variable
    this.wiimSpotifyDeviceId = process.env.WIIM_SPOTIFY_DEVICE_ID;

    // Create HTTP agent with improved configuration for better connection handling
    const httpAgent = new http.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000 // Free socket timeout
    });
    
    // Create HTTPS agent with configuration to allow self-signed certificates
    const httpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000, // Free socket timeout
      rejectUnauthorized: false // Allow self-signed SSL certificates
    });

    // Create axios instance for HTTP communication with WiiM device
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive',
        'User-Agent': 'CSFPortal-WiiM-Client/1.0'
      },
      // Add timeout configuration to prevent socket hang up errors
      timeout: 10000, // 10 seconds
      // Use the improved HTTP agent for HTTP requests
      httpAgent: httpAgent,
      // Use the HTTPS agent with self-signed certificate support for HTTPS requests
      httpsAgent: httpsAgent,
      // Disable automatic retries (we'll handle them manually)
      maxRedirects: 0,
      // Validate status codes
      validateStatus: (status) => status >= 200 && status < 300
    });

    // Add request interceptor for logging and retry logic
    this.axios.interceptors.request.use(
      (config) => {
        console.log(`WiiM API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('WiiM API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.axios.interceptors.response.use(
      (response) => {
        console.log(`WiiM API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const config = error.config;

        // Log detailed error information
        console.error('WiiM API Response Error:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          url: config?.url,
          method: config?.method,
          timeout: config?.timeout,
          retryCount: config.__retryCount || 0
        });

        // Implement retry logic for specific errors
        if (this.shouldRetry(error) && (!config.__retryCount || config.__retryCount < 3)) {
          config.__retryCount = (config.__retryCount || 0) + 1;

          // Calculate exponential backoff delay
          const delay = Math.min(1000 * Math.pow(2, config.__retryCount - 1), 5000);

          console.log(`Retrying WiiM API request (attempt ${config.__retryCount}/3) after ${delay}ms delay`);

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));

          // Retry the request
          return this.axios(config);
        }

        return Promise.reject(error);
      }
    );
    
    // Add request interceptor for retry logic
    this.axios.interceptors.response.use(null, async (error) => {
      const config = error.config;
      
      // If we don't have a config or we've already retried the maximum number of times, throw the error
      if (!config || !config.maxRetries || config._retryCount >= config.maxRetries) {
        return Promise.reject(error);
      }
      
      // Increment the retry count
      config._retryCount = config._retryCount || 0;
      config._retryCount++;
      
      // Create a new promise to handle the retry
      const retryDelay = config.retryDelay || 1000;
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      
      // Log the retry attempt
      console.log(`Retrying WiiM API request (${config._retryCount}/${config.maxRetries}): ${config.url}`);
      
      // Return the axios instance to retry the request
      return this.axios(config);
    });

    // Spotify authentication properties
    this.spotifyClientId = process.env.SPOTIFY_CLIENT_ID;
    this.spotifyClientSecret = process.env.SPOTIFY_CLIENT_SECRET;
    this.spotifyRefreshToken = process.env.SPOTIFY_REFRESH_TOKEN;
    this.spotifyAccessToken = null;
    this.spotifyTokenExpiry = null;

    // Create HTTPS agent with improved configuration for Spotify API
    const spotifyHttpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 10, // Allow more concurrent connections for Spotify
      maxFreeSockets: 5, // Keep more connections in pool
      timeout: 15000, // Socket timeout
      freeSocketTimeout: 20000 // Free socket timeout
    });

    // Create a separate axios instance for Spotify API calls
    this.spotifyAxios = axios.create({
      baseURL: 'https://api.spotify.com/v1',
      headers: {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive',
        'User-Agent': 'CSFPortal-Spotify-Client/1.0',
      },
      // Add timeout configuration to prevent socket hang up errors
      timeout: 15000, // 15 seconds for Spotify API (might be slower than local WiiM device)
      // Use the improved HTTPS agent
      httpsAgent: spotifyHttpsAgent,
      // Disable automatic retries (we'll handle them manually)
      maxRedirects: 5,
      // Validate status codes
      validateStatus: (status) => status >= 200 && status < 300
    });

    // Add request interceptor for Spotify API
    this.spotifyAxios.interceptors.request.use(
      (config) => {
        console.log(`Spotify API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Spotify API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for Spotify API
    this.spotifyAxios.interceptors.response.use(
      (response) => {
        console.log(`Spotify API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const config = error.config;

        // Log detailed error information
        console.error('Spotify API Response Error:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          url: config?.url,
          method: config?.method,
          timeout: config?.timeout,
          retryCount: config.__retryCount || 0
        });

        // Implement retry logic for specific errors
        if (this.shouldRetry(error) && (!config.__retryCount || config.__retryCount < 3)) {
          config.__retryCount = (config.__retryCount || 0) + 1;

          // Calculate exponential backoff delay
          const delay = Math.min(1000 * Math.pow(2, config.__retryCount - 1), 5000);

          console.log(`Retrying Spotify API request (attempt ${config.__retryCount}/3) after ${delay}ms delay`);

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));

          // Retry the request
          return this.spotifyAxios(config);
        }

        return Promise.reject(error);
      }
    );
    
    // Add request interceptor for retry logic
    this.spotifyAxios.interceptors.response.use(null, async (error) => {
      const config = error.config;
      
      // If we don't have a config or we've already retried the maximum number of times, throw the error
      if (!config || !config.maxRetries || config._retryCount >= config.maxRetries) {
        return Promise.reject(error);
      }
      
      // Increment the retry count
      config._retryCount = config._retryCount || 0;
      config._retryCount++;
      
      // Create a new promise to handle the retry
      const retryDelay = config.retryDelay || 1000;
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      
      // Log the retry attempt
      console.log(`Retrying Spotify API request (${config._retryCount}/${config.maxRetries}): ${config.url}`);
      
      // Return the axios instance to retry the request
      return this.spotifyAxios(config);
    });

    // Initialize Spotify token properties
    this.spotifyAccessToken = null;
    this.spotifyTokenExpiry = null;

    // Circuit breaker state for failing endpoints
    this.circuitBreaker = {
      getPlaylists: { failures: 0, lastFailure: null, isOpen: false },
      getSpotifyPlaylists: { failures: 0, lastFailure: null, isOpen: false }
    };
  }

  /**
   * Determine if a request should be retried based on the error
   * @param {Error} error - The error that occurred
   * @returns {boolean} - Whether the request should be retried
   */
  shouldRetry(error) {
    // Don't retry if it's a client error (4xx)
    if (error.response && error.response.status >= 400 && error.response.status < 500) {
      return false;
    }

    // Retry for network errors, timeouts, and server errors
    const retryableErrors = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ENETDOWN',
      'ENETUNREACH',
      'EHOSTDOWN',
      'EHOSTUNREACH',
      'EPIPE',
      'ECONNABORTED',
      'ETIMEDOUT',
      'socket hang up'
    ];

    return retryableErrors.some(code =>
      error.code === code ||
      error.message?.includes(code) ||
      error.message?.toLowerCase().includes('socket hang up') ||
      error.message?.toLowerCase().includes('timeout')
    );
  }

  /**
   * Check if circuit breaker is open for a specific endpoint
   * @param {string} endpoint - The endpoint name
   * @returns {boolean} - Whether the circuit breaker is open
   */
  isCircuitBreakerOpen(endpoint) {
    const breaker = this.circuitBreaker[endpoint];
    if (!breaker) return false;

    // If we have 3 or more failures in the last 5 minutes, open the circuit
    if (breaker.failures >= 3 && breaker.lastFailure) {
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
      if (breaker.lastFailure > fiveMinutesAgo) {
        breaker.isOpen = true;
        return true;
      } else {
        // Reset after 5 minutes
        breaker.failures = 0;
        breaker.lastFailure = null;
        breaker.isOpen = false;
      }
    }

    return false;
  }

  /**
   * Record a failure for circuit breaker
   * @param {string} endpoint - The endpoint name
   */
  recordFailure(endpoint) {
    if (this.circuitBreaker[endpoint]) {
      this.circuitBreaker[endpoint].failures++;
      this.circuitBreaker[endpoint].lastFailure = Date.now();
    }
  }

  /**
   * Record a success for circuit breaker
   * @param {string} endpoint - The endpoint name
   */
  recordSuccess(endpoint) {
    if (this.circuitBreaker[endpoint]) {
      this.circuitBreaker[endpoint].failures = 0;
      this.circuitBreaker[endpoint].lastFailure = null;
      this.circuitBreaker[endpoint].isOpen = false;
    }
  }

  /**
   * Get health status of the WiiM API connection
   * @returns {Promise<Object>} Health status information
   */
  async getHealthStatus() {
    const health = {
      status: 'unknown',
      timestamp: new Date().toISOString(),
      host: this.host,
      port: this.port,
      circuitBreakers: this.circuitBreaker,
      tests: {}
    };

    try {
      // Test basic connectivity
      const startTime = Date.now();
      await this.getDeviceInfo();
      const responseTime = Date.now() - startTime;

      health.tests.connectivity = {
        status: 'pass',
        responseTime: `${responseTime}ms`
      };

      // Test playlist endpoint if circuit breaker is not open
      if (!this.isCircuitBreakerOpen('getPlaylists')) {
        try {
          const playlistStartTime = Date.now();
          await this.getPlaylists();
          const playlistResponseTime = Date.now() - playlistStartTime;

          health.tests.playlists = {
            status: 'pass',
            responseTime: `${playlistResponseTime}ms`
          };
        } catch (error) {
          health.tests.playlists = {
            status: 'fail',
            error: error.message
          };
        }
      } else {
        health.tests.playlists = {
          status: 'circuit_breaker_open',
          error: 'Circuit breaker is open due to repeated failures'
        };
      }

      // Overall status
      const allTestsPassed = Object.values(health.tests).every(test => test.status === 'pass');
      health.status = allTestsPassed ? 'healthy' : 'degraded';

    } catch (error) {
      health.status = 'unhealthy';
      health.tests.connectivity = {
        status: 'fail',
        error: error.message
      };
    }

    return health;
  }

  /**
   * Initialize the WiiM API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if host is provided
      if (!this.host) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Host is required for WiiM integration.'
        );
        return;
      }

      // Test the connection by getting device info
      await this.getDeviceInfo();

      // If we get here, the connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly configured and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing WiiM API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Get device information
   * @returns {Promise<Object>} Device information
   */
  async getDeviceInfo() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=getPlayerStatus');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM device information:', error);
      throw error;
    }
  }

  /**
   * Utility function to decode hex-encoded strings to UTF-8 text
   * @param {string} hexString - Hex-encoded string
   * @returns {string} Decoded UTF-8 text
   */
  hexToUtf8(hexString) {
    try {
      if (!hexString || typeof hexString !== 'string') {
        return '';
      }
      
      // Convert hex string to bytes
      let bytes = [];
      for (let i = 0; i < hexString.length; i += 2) {
        bytes.push(parseInt(hexString.substr(i, 2), 16));
      }
      
      // Convert bytes to UTF-8 string
      return new TextDecoder('utf-8').decode(new Uint8Array(bytes));
    } catch (error) {
      console.error('Error decoding hex string:', error);
      return hexString; // Return original string if decoding fails
    }
  }

  /**
   * Get current playback status
   * @returns {Promise<Object>} Playback status
   */
  async getPlaybackStatus() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=getPlayerStatus');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM playback status:', error);
      throw error;
    }
  }
  
  /**
   * Get enriched player status with Spotify data
   * @returns {Promise<Object>} Enriched playback status with Spotify data
   */
  async getEnrichedPlayerStatus() {
    try {
      // Get the raw player status from WiiM
      const playerStatus = await this.getPlaybackStatus();
      
      // Create a copy of the player status to enrich
      const enrichedStatus = { ...playerStatus };
      
      // Decode hex-encoded fields
      if (playerStatus.Title) {
        enrichedStatus.decodedTitle = this.hexToUtf8(playerStatus.Title);
      }
      
      if (playerStatus.Artist) {
        enrichedStatus.decodedArtist = this.hexToUtf8(playerStatus.Artist);
      }
      
      if (playerStatus.Album) {
        enrichedStatus.decodedAlbum = this.hexToUtf8(playerStatus.Album);
      }
      
      // Check if the current source is Spotify
      if (playerStatus.vendor && playerStatus.vendor.startsWith('spotify:')) {
        // Extract Spotify URI components
        const spotifyUri = playerStatus.vendor;
        const uriParts = spotifyUri.split(':');
        
        // Store the Spotify URI type and ID
        enrichedStatus.spotifyType = uriParts[1] || '';
        enrichedStatus.spotifyId = uriParts[2] || '';
        
        try {
          // Ensure we have a valid Spotify access token
          await this.ensureSpotifyAuthenticated();
          
          // Fetch additional data from Spotify API based on the URI type
          if (enrichedStatus.spotifyType === 'track' && enrichedStatus.spotifyId) {
            // Fetch track details
            const trackResponse = await this.spotifyAxios.get(`/tracks/${enrichedStatus.spotifyId}`);
            enrichedStatus.spotifyData = trackResponse.data;
            
            // Update the title, artist, and album with data from Spotify
            enrichedStatus.title = trackResponse.data.name;
            enrichedStatus.artist = trackResponse.data.artists.map(artist => artist.name).join(', ');
            enrichedStatus.album = trackResponse.data.album.name;
            enrichedStatus.albumart = trackResponse.data.album.images[0]?.url;
            
          } else if (enrichedStatus.spotifyType === 'album' && enrichedStatus.spotifyId) {
            // Fetch album details
            const albumResponse = await this.spotifyAxios.get(`/albums/${enrichedStatus.spotifyId}`);
            enrichedStatus.spotifyData = albumResponse.data;
            
            // Update the title, artist, and album with data from Spotify
            enrichedStatus.title = albumResponse.data.name;
            enrichedStatus.artist = albumResponse.data.artists.map(artist => artist.name).join(', ');
            enrichedStatus.album = albumResponse.data.name;
            enrichedStatus.albumart = albumResponse.data.images[0]?.url;
            
          } else if (enrichedStatus.spotifyType === 'playlist' && enrichedStatus.spotifyId) {
            // Fetch playlist details
            const playlistResponse = await this.spotifyAxios.get(`/playlists/${enrichedStatus.spotifyId}`);
            enrichedStatus.spotifyData = playlistResponse.data;
            
            // Get the current track from the playlist if available
            if (playlistResponse.data.tracks && playlistResponse.data.tracks.items.length > 0) {
              // Try to find the current track based on position if available
              let currentTrack = null;
              
              // First attempt: Use plicurr (playlist current index) if available
              if (playerStatus.plicurr && !isNaN(parseInt(playerStatus.plicurr))) {
                const trackIndex = parseInt(playerStatus.plicurr);
                if (trackIndex >= 0 && trackIndex < playlistResponse.data.tracks.items.length) {
                  currentTrack = playlistResponse.data.tracks.items[trackIndex].track;
                }
              }
              
              // Second attempt: Try to match by title and artist if available
              if (!currentTrack && playerStatus.Title && playerStatus.Artist) {
                const decodedTitle = this.hexToUtf8(playerStatus.Title);
                const decodedArtist = this.hexToUtf8(playerStatus.Artist);
                
                // Find the track in the playlist that matches the title and artist
                const matchingTrackItem = playlistResponse.data.tracks.items.find(item => {
                  const track = item.track;
                  if (!track) return false;
                  
                  // Check if title matches (case insensitive)
                  const titleMatches = track.name.toLowerCase() === decodedTitle.toLowerCase();
                  
                  // Check if any artist matches (case insensitive)
                  const artistMatches = track.artists.some(artist => 
                    artist.name.toLowerCase() === decodedArtist.toLowerCase() ||
                    decodedArtist.toLowerCase().includes(artist.name.toLowerCase()) ||
                    artist.name.toLowerCase().includes(decodedArtist.toLowerCase())
                  );
                  
                  return titleMatches && artistMatches;
                });
                
                if (matchingTrackItem) {
                  currentTrack = matchingTrackItem.track;
                }
              }
              
              // Third attempt: If we still couldn't find the track, use the first track
              if (!currentTrack && playlistResponse.data.tracks.items[0].track) {
                console.warn('Could not determine current track in Spotify playlist, using first track as fallback');
                currentTrack = playlistResponse.data.tracks.items[0].track;
              }
              
              if (currentTrack) {
                // Update the title, artist, and album with data from the current track
                enrichedStatus.title = currentTrack.name;
                enrichedStatus.artist = currentTrack.artists.map(artist => artist.name).join(', ');
                enrichedStatus.album = currentTrack.album.name;
                enrichedStatus.albumart = currentTrack.album.images[0]?.url;
              } else {
                // If we couldn't get track details, use playlist info
                enrichedStatus.title = playlistResponse.data.name;
                enrichedStatus.artist = 'Spotify Playlist';
                enrichedStatus.albumart = playlistResponse.data.images[0]?.url;
              }
            } else {
              // If the playlist is empty or we couldn't get track details, use playlist info
              enrichedStatus.title = playlistResponse.data.name;
              enrichedStatus.artist = 'Spotify Playlist';
              enrichedStatus.albumart = playlistResponse.data.images[0]?.url;
            }
          } else if (enrichedStatus.spotifyType === 'artist' && enrichedStatus.spotifyId) {
            // Fetch artist details
            const artistResponse = await this.spotifyAxios.get(`/artists/${enrichedStatus.spotifyId}`);
            enrichedStatus.spotifyData = artistResponse.data;
            
            // Update the title, artist, and album with data from Spotify
            enrichedStatus.title = `Top tracks from ${artistResponse.data.name}`;
            enrichedStatus.artist = artistResponse.data.name;
            enrichedStatus.albumart = artistResponse.data.images[0]?.url;
          }
        } catch (spotifyError) {
          console.error('Error fetching Spotify data:', spotifyError);
          // If we couldn't get Spotify data, use the decoded fields
          enrichedStatus.title = enrichedStatus.decodedTitle || 'Unknown Track';
          enrichedStatus.artist = enrichedStatus.decodedArtist || 'Unknown Artist';
          enrichedStatus.album = enrichedStatus.decodedAlbum || 'Unknown Album';
        }
      } else {
        // If not playing from Spotify, use the decoded fields
        enrichedStatus.title = enrichedStatus.decodedTitle || 'Unknown Track';
        enrichedStatus.artist = enrichedStatus.decodedArtist || 'Unknown Artist';
        enrichedStatus.album = enrichedStatus.decodedAlbum || 'Unknown Album';
      }
      
      return enrichedStatus;
    } catch (error) {
      console.error('Error getting enriched player status:', error);
      throw error;
    }
  }

  /**
   * Play
   * @returns {Promise<Object>} Response data
   */
  async play() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=setPlayerCmd:play');
      return response.data;
    } catch (error) {
      console.error('Error sending play command to WiiM:', error);
      throw error;
    }
  }

  /**
   * Pause
   * @returns {Promise<Object>} Response data
   */
  async pause() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=setPlayerCmd:pause');
      return response.data;
    } catch (error) {
      console.error('Error sending pause command to WiiM:', error);
      throw error;
    }
  }

  /**
   * Next track
   * @returns {Promise<Object>} Response data
   */
  async next() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=setPlayerCmd:next');
      return response.data;
    } catch (error) {
      console.error('Error sending next command to WiiM:', error);
      throw error;
    }
  }

  /**
   * Previous track
   * @returns {Promise<Object>} Response data
   */
  async previous() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=setPlayerCmd:prev');
      return response.data;
    } catch (error) {
      console.error('Error sending previous command to WiiM:', error);
      throw error;
    }
  }

  /**
   * Set volume
   * @param {number} volume Volume level (0-100)
   * @returns {Promise<Object>} Response data
   */
  async setVolume(volume) {
    try {
      // Note: Volume control via HTTP API is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('Volume control via HTTP API is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:vol:${volume}`);
      return response.data;
    } catch (error) {
      console.error(`Error setting WiiM volume to ${volume}:`, error);
      throw error;
    }
  }

  /**
   * Get current playlist
   * @returns {Promise<Object>} Playlist data
   */
  async getPlaylist() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=getPlaylist', {
        timeout: 15000, // 15 seconds timeout
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache'
        }
      });
      return response.data;
    } catch (error) {
      // Enhanced error logging
      console.error('Error fetching WiiM playlist:', {
        message: error.message,
        code: error.code,
        url: error.config?.url,
        timeout: error.config?.timeout
      });

      // Create enhanced error with context
      let errorMessage = `Error fetching WiiM playlist: ${error.message}`;
      if (error.message?.toLowerCase().includes('socket hang up')) {
        errorMessage += ' (Network connection was unexpectedly closed)';
      }

      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.isWiimError = true;

      throw enhancedError;
    }
  }
  
  /**
   * Get enriched playlist with Spotify data
   * @returns {Promise<Object>} Enriched playlist data
   */
  async getEnrichedPlaylist() {
    try {
      // First try to get the playlist from the WiiM device
      let playlist = null;
      try {
        playlist = await this.getPlaylist();
      } catch (error) {
        console.error('Error fetching playlist from WiiM device, will try Spotify:', error);
      }
      
      // Get the current player status to check if we're playing from Spotify
      const playerStatus = await this.getPlaybackStatus();
      
      // If we're playing from Spotify and it's a playlist, get the playlist from Spotify
      if (playerStatus.vendor && playerStatus.vendor.startsWith('spotify:playlist:')) {
        try {
          // Extract the playlist ID from the vendor field
          const playlistId = playerStatus.vendor.split(':')[2];
          
          // Ensure we have a valid Spotify access token
          await this.ensureSpotifyAuthenticated();
          
          // Fetch the playlist from Spotify
          const playlistResponse = await this.spotifyAxios.get(`/playlists/${playlistId}`, {
            timeout: 25000, // 25 seconds timeout
            headers: {
              'Authorization': `Bearer ${this.spotifyAccessToken}`,
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          });
          
          // Format the playlist tracks for the WiiM API
          const tracks = playlistResponse.data.tracks.items.map((item, index) => {
            const track = item.track;
            return {
              id: track.id,
              title: track.name,
              artist: track.artists.map(artist => artist.name).join(', '),
              album: track.album.name,
              albumart: track.album.images[0]?.url,
              duration: Math.floor(track.duration_ms / 1000),
              position: index
            };
          });
          
          // Create a playlist object in the format expected by the client
          const spotifyPlaylist = {
            songs: tracks,
            name: playlistResponse.data.name,
            id: playlistResponse.data.id,
            source: 'spotify',
            total: tracks.length
          };
          
          // If we have a current position from the player status, add it
          if (playerStatus.plicurr && !isNaN(parseInt(playerStatus.plicurr))) {
            spotifyPlaylist.position = parseInt(playerStatus.plicurr);
          }
          
          return spotifyPlaylist;
        } catch (spotifyError) {
          console.error('Error fetching playlist from Spotify:', spotifyError);
          // If we couldn't get the playlist from Spotify but have one from WiiM, return that
          if (playlist) {
            return playlist;
          }
          // Otherwise, rethrow the error
          throw spotifyError;
        }
      }
      
      // If we're not playing from Spotify or couldn't get Spotify data, return the WiiM playlist
      return playlist;
    } catch (error) {
      console.error('Error getting enriched playlist:', error);
      throw error;
    }
  }

  /**
   * Play specific track from playlist
   * @param {number} index Track index in playlist
   * @returns {Promise<Object>} Response data
   */
  async playTrack(index) {
    try {
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:playIndex:${index}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing track at index ${index} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Set repeat mode
   * @param {string} mode Repeat mode ('all', 'one', 'off')
   * @returns {Promise<Object>} Response data
   */
  async setRepeat(mode) {
    try {
      // Note: Repeat/loop mode control via HTTP API is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('Repeat mode control via HTTP API is not officially documented and may not work');
      let modeValue;
      switch (mode) {
        case 'all':
          modeValue = 2;
          break;
        case 'one':
          modeValue = 1;
          break;
        case 'off':
        default:
          modeValue = 0;
          break;
      }
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:loopmode:${modeValue}`);
      return response.data;
    } catch (error) {
      console.error(`Error setting WiiM repeat mode to ${mode}:`, error);
      throw error;
    }
  }

  /**
   * Set shuffle mode
   * @param {boolean} enabled Whether shuffle is enabled
   * @returns {Promise<Object>} Response data
   */
  async setShuffle(enabled) {
    try {
      // Note: Shuffle mode control via HTTP API is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('Shuffle mode control via HTTP API is not officially documented and may not work');
      const modeValue = enabled ? 1 : 0;
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:shuffle:${modeValue}`);
      return response.data;
    } catch (error) {
      console.error(`Error setting WiiM shuffle mode to ${enabled}:`, error);
      throw error;
    }
  }

  /**
   * Get list of available playlists
   * @returns {Promise<Object>} Playlists data
   */
  async getPlaylists() {
    const endpoint = 'getPlaylists';

    try {
      // Check circuit breaker
      if (this.isCircuitBreakerOpen(endpoint)) {
        const error = new Error('Circuit breaker is open for getPlaylists endpoint due to repeated failures');
        error.isCircuitBreakerError = true;
        error.isWiimError = true;
        throw error;
      }

      // Note: getPlaylists command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('getPlaylists command is not officially documented and may not work');

      // Use a longer timeout for playlist fetching specifically with additional configuration
      const response = await this.axios.get('/httpapi.asp?command=getPlaylists', {
        timeout: 20000, // Increased to 20 seconds timeout for playlist fetching
        // Add additional headers for better compatibility
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        // Disable response transformation to handle any response format
        transformResponse: [(data) => {
          try {
            return JSON.parse(data);
          } catch (e) {
            // If it's not JSON, return as is
            return data;
          }
        }]
      });

      // Record success for circuit breaker
      this.recordSuccess(endpoint);

      return response.data;
    } catch (error) {
      // Record failure for circuit breaker
      this.recordFailure(endpoint);

      // Enhanced error logging with more details
      console.error('Error fetching WiiM playlists:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        url: error.config?.url,
        method: error.config?.method,
        timeout: error.config?.timeout,
        circuitBreakerState: this.circuitBreaker[endpoint]
      });

      // Create a more descriptive error with additional context
      let errorMessage = `Error fetching WiiM playlists: ${error.message}`;

      if (error.message?.toLowerCase().includes('socket hang up')) {
        errorMessage += ' (Network connection was unexpectedly closed. This may be due to device timeout or network issues.)';
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage += ' (Connection refused. Please check if the WiiM device is powered on and accessible.)';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage += ' (Request timed out. The WiiM device may be busy or unresponsive.)';
      }

      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.isWiimError = true;
      enhancedError.endpoint = endpoint;
      enhancedError.circuitBreakerFailures = this.circuitBreaker[endpoint]?.failures || 0;

      throw enhancedError;
    }
  }

  /**
   * Play a specific playlist
   * @param {string} playlistId Playlist ID
   * @returns {Promise<Object>} Response data
   */
  async playPlaylist(playlistId) {
    try {
      // Note: playPlaylist command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('playPlaylist command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=playPlaylist:${playlistId}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing playlist ${playlistId} on WiiM:`, error);
      throw error;
    }
  }
  
  /**
   * Check if the current playlist has ended and advance to the next playlist if needed
   * @returns {Promise<Object>} Result of the operation
   */
  async checkAndAdvancePlaylist() {
    try {
      // Get the current playback status
      const playerStatus = await this.getPlaybackStatus();
      
      // Get the list of available playlists
      const playlistsData = await this.getPlaylists();
      const playlists = playlistsData?.playlists || [];
      
      // If there are no playlists or repeat is enabled, do nothing
      if (playlists.length === 0 || playerStatus.repeat !== 0) {
        return { advanced: false, reason: 'No playlists available or repeat is enabled' };
      }
      
      // Check if we're at the end of the current playlist
      const isAtEnd = this.isPlaylistAtEnd(playerStatus);
      
      if (isAtEnd) {
        // Find the current playlist in the list of playlists
        const currentPlaylistId = playerStatus.playlistId || playerStatus.id;
        if (!currentPlaylistId) {
          return { advanced: false, reason: 'Could not determine current playlist ID' };
        }
        
        // Find the index of the current playlist
        const currentPlaylistIndex = playlists.findIndex(p => p.id === currentPlaylistId);
        if (currentPlaylistIndex === -1) {
          return { advanced: false, reason: 'Current playlist not found in playlists list' };
        }
        
        // Find the next playlist (wrap around to the first if at the end)
        const nextPlaylistIndex = (currentPlaylistIndex + 1) % playlists.length;
        const nextPlaylist = playlists[nextPlaylistIndex];
        
        // Play the next playlist
        await this.playPlaylist(nextPlaylist.id);
        
        return { 
          advanced: true, 
          from: currentPlaylistId, 
          to: nextPlaylist.id,
          fromIndex: currentPlaylistIndex,
          toIndex: nextPlaylistIndex
        };
      }
      
      return { advanced: false, reason: 'Current playlist has not ended' };
    } catch (error) {
      console.error('Error checking and advancing playlist:', error);
      return { advanced: false, error: error.message };
    }
  }
  
  /**
   * Check if the current playlist is at its end
   * @param {Object} playerStatus The current player status
   * @returns {boolean} True if the playlist is at its end
   */
  isPlaylistAtEnd(playerStatus) {
    // If the player is stopped, it might be at the end of a playlist
    if (playerStatus.status === 'stop') {
      return true;
    }
    
    // If we have playlist position information
    if (playerStatus.plicurr !== undefined && playerStatus.plitotal !== undefined) {
      const currentPosition = parseInt(playerStatus.plicurr);
      const totalTracks = parseInt(playerStatus.plitotal);
      
      // If we're at the last track
      if (!isNaN(currentPosition) && !isNaN(totalTracks) && currentPosition >= totalTracks - 1) {
        // If the track is near its end (over 95% complete)
        if (playerStatus.position !== undefined && playerStatus.duration !== undefined) {
          const position = parseInt(playerStatus.position);
          const duration = parseInt(playerStatus.duration);
          
          if (!isNaN(position) && !isNaN(duration) && duration > 0) {
            const percentComplete = (position / duration) * 100;
            return percentComplete > 95;
          }
        }
      }
    }
    
    return false;
  }

  /**
   * Get songs in a specific playlist without playing it
   * @param {string} playlistId Playlist ID
   * @returns {Promise<Object>} Playlist songs data
   */
  async getPlaylistSongs(playlistId) {
    try {
      // Since there's no direct API to get songs in a playlist without playing it,
      // we'll implement a workaround:
      // 1. Get the current playback status to save it
      // 2. Play the requested playlist
      // 3. Get the playlist songs
      // 4. Restore the previous playback state if needed
      
      // Step 1: Get current playback status
      const originalStatus = await this.getPlaybackStatus();
      const wasPlaying = originalStatus.status === 'play';
      const originalPosition = originalStatus.position;
      const originalPlaylistId = originalStatus.playlistId;
      
      // Step 2: Play the requested playlist
      await this.playPlaylist(playlistId);
      
      // Small delay to ensure the playlist is loaded
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Step 3: Get the playlist songs
      const playlistData = await this.getPlaylist();
      
      // Step 4: Restore previous playback state if needed
      if (originalPlaylistId && originalPlaylistId !== playlistId) {
        // If we were playing a different playlist, restore it
        await this.playPlaylist(originalPlaylistId);
        
        // If we were at a specific position, try to restore it
        if (originalPosition !== undefined && originalPosition !== null) {
          await this.playTrack(originalPosition);
        }
        
        // If we were paused, pause again
        if (!wasPlaying) {
          await this.pause();
        }
      } else if (wasPlaying) {
        // If we were playing the same playlist, just ensure we're still playing
        await this.play();
      } else {
        // If we were paused, pause again
        await this.pause();
      }
      
      return playlistData;
    } catch (error) {
      console.error(`Error fetching songs for playlist ${playlistId} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Search for music
   * @param {string} query Search query
   * @returns {Promise<Object>} Search results
   */
  async search(query) {
    try {
      // Note: search command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('search command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=search:${encodeURIComponent(query)}`);
      return response.data;
    } catch (error) {
      console.error(`Error searching for "${query}" on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Get Spotify playlists (requires Spotify integration)
   * @returns {Promise<Object>} Spotify playlists
   */
  async getSpotifyList() {
    try {
      // Note: getSpotifyList command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('getSpotifyList command is not officially documented and may not work');
      const response = await this.axios.get('/httpapi.asp?command=getSpotifyList');
      return response.data;
    } catch (error) {
      console.error('Error fetching Spotify playlists from WiiM:', error);
      throw error;
    }
  }

  /**
   * Play Spotify playlist
   * @param {string} playlistId Spotify playlist ID
   * @returns {Promise<Object>} Response data
   * @deprecated Use the enhanced version with Spotify authentication below
   */
  async _legacyPlaySpotifyPlaylist(playlistId) {
    try {
      // This method is deprecated and uses an incorrect command format
      // Use the enhanced version with Spotify authentication below
      console.warn('This method is deprecated. Use the enhanced version with Spotify authentication instead.');
      throw new Error('Deprecated method. Use the enhanced version with Spotify authentication instead.');
    } catch (error) {
      console.error(`Error playing Spotify playlist ${playlistId} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Get list of available inputs
   * @returns {Promise<Object>} Inputs data
   */
  async getInputs() {
    try {
      // Note: getInputs command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('getInputs command is not officially documented and may not work');
      const response = await this.axios.get('/httpapi.asp?command=getInputs');
      return response.data;
    } catch (error) {
      console.error('Error fetching WiiM inputs:', error);
      throw error;
    }
  }

  /**
   * Switch to a specific input
   * @param {string} inputId Input ID
   * @returns {Promise<Object>} Response data
   */
  async switchInput(inputId) {
    try {
      // Note: switchInput command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('switchInput command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=switchInput:${inputId}`);
      return response.data;
    } catch (error) {
      console.error(`Error switching to input ${inputId} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Seek to a specific position in the current track
   * @param {number} position Position in seconds
   * @returns {Promise<Object>} Response data
   */
  async seek(position) {
    try {
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:seek:${position}`);
      return response.data;
    } catch (error) {
      console.error(`Error seeking to position ${position} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Get detailed information about the current track
   * @returns {Promise<Object>} Track information
   */
  async getTrackInfo() {
    try {
      // Note: getTrackInfo command is not documented in official WiiM API
      // Use getMetaInfo as documented alternative if this doesn't work
      console.warn('getTrackInfo command is not officially documented. Consider using getMetaInfo instead');
      const response = await this.axios.get('/httpapi.asp?command=getTrackInfo');
      return response.data;
    } catch (error) {
      console.error('Error fetching track information from WiiM:', error);
      throw error;
    }
  }

  /**
   * Get equalizer settings
   * @returns {Promise<Object>} Equalizer settings
   */
  async getEqualizer() {
    try {
      // Note: getEqualizer command is not documented in official WiiM API
      // Use EQGetStat or EQGetList as documented alternatives
      console.warn('getEqualizer command is not officially documented. Consider using EQGetStat or EQGetList instead');
      const response = await this.axios.get('/httpapi.asp?command=getEqualizer');
      return response.data;
    } catch (error) {
      console.error('Error fetching equalizer settings from WiiM:', error);
      throw error;
    }
  }

  /**
   * Set equalizer settings
   * @param {string} preset Equalizer preset name or 'custom'
   * @param {Array<number>} bands Array of band values (for custom preset)
   * @returns {Promise<Object>} Response data
   */
  async setEqualizer(preset, bands = []) {
    try {
      // Note: setEqualizer command format is not documented in official WiiM API
      // Use EQLoad:xxx as documented alternative
      console.warn('setEqualizer command format is not officially documented. Consider using EQLoad instead');
      let command = `/httpapi.asp?command=setEqualizer:${preset}`;
      
      // If custom preset and bands are provided, add them to the command
      if (preset === 'custom' && bands.length > 0) {
        command += `:${bands.join(',')}`;
      }
      
      const response = await this.axios.get(command);
      return response.data;
    } catch (error) {
      console.error(`Error setting equalizer on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Get extended status information about the device
   * @returns {Promise<Object>} Extended status information including device name, firmware version, etc.
   */
  async getStatusEx() {
    try {
      // Note: getStatusEx command is not documented in official WiiM API
      // This command may not work on all WiiM devices
      console.warn('getStatusEx command is not officially documented and may not work');
      const response = await this.axios.get('/httpapi.asp?command=getStatusEx');
      return response.data;
    } catch (error) {
      console.error('Error fetching extended status information from WiiM:', error);
      throw error;
    }
  }

  /**
   * Authenticate with Spotify using client credentials and refresh token
   * @returns {Promise<string>} Spotify access token
   */
  async authenticateSpotify() {
    try {
      // Check if we have the required Spotify credentials
      if (!this.spotifyClientId || !this.spotifyClientSecret || !this.spotifyRefreshToken) {
        throw new Error('Spotify credentials are missing. Please set SPOTIFY_CLIENT_ID, SPOTIFY_CLIENT_SECRET, and SPOTIFY_REFRESH_TOKEN in your environment variables.');
      }

      // Get a new access token using the refresh token
      const tokenResponse = await axios.post('https://accounts.spotify.com/api/token', 
        querystring.stringify({
          grant_type: 'refresh_token',
          refresh_token: this.spotifyRefreshToken
        }), 
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': 'Basic ' + Buffer.from(this.spotifyClientId + ':' + this.spotifyClientSecret).toString('base64')
          }
        }
      );

      // Store the access token and calculate expiry time
      this.spotifyAccessToken = tokenResponse.data.access_token;
      // Set expiry time to 5 minutes before actual expiry to be safe
      this.spotifyTokenExpiry = Date.now() + (tokenResponse.data.expires_in - 300) * 1000;

      // If we received a new refresh token (some providers send a new one with each request), update it
      if (tokenResponse.data.refresh_token) {
        this.spotifyRefreshToken = tokenResponse.data.refresh_token;
        console.log('Received and updated Spotify refresh token');
      }

      // Update the Spotify axios instance with the new token
      this.spotifyAxios.defaults.headers.common['Authorization'] = `Bearer ${this.spotifyAccessToken}`;

      console.log('Successfully authenticated with Spotify');
      return this.spotifyAccessToken;
    } catch (error) {
      // Enhanced error logging with more details
      const errorDetails = {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      };
      
      console.error('Error authenticating with Spotify:', errorDetails);
      
      // Handle specific error cases
      if (error.response) {
        if (error.response.status === 400 && error.response.data?.error === 'invalid_grant') {
          throw new Error('Spotify refresh token is invalid or has been revoked. Please generate a new refresh token following the instructions in WIIM_SPOTIFY_INTEGRATION_CHANGES.md.');
        } else if (error.response.status === 401) {
          throw new Error('Spotify client credentials (client ID or client secret) are invalid. Please check your environment variables.');
        }
      }
      
      throw error;
    }
  }

  /**
   * Ensure that we have a valid Spotify access token
   * @param {number} retryCount - Number of retry attempts (default: 0)
   * @param {number} maxRetries - Maximum number of retry attempts (default: 2)
   * @returns {Promise<string>} Spotify access token
   */
  async ensureSpotifyAuthenticated(retryCount = 0, maxRetries = 2) {
    try {
      // If we don't have an access token or it's expired, get a new one
      if (!this.spotifyAccessToken || !this.spotifyTokenExpiry || Date.now() >= this.spotifyTokenExpiry) {
        return await this.authenticateSpotify();
      }
      return this.spotifyAccessToken;
    } catch (error) {
      console.error(`Error ensuring Spotify authentication (attempt ${retryCount + 1}/${maxRetries + 1}):`, error);
      
      // If we haven't reached the maximum number of retries and the error is potentially recoverable, retry
      if (retryCount < maxRetries && this.isRetryableError(error)) {
        console.log(`Retrying Spotify authentication (attempt ${retryCount + 2}/${maxRetries + 1})...`);
        // Wait for a short time before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.ensureSpotifyAuthenticated(retryCount + 1, maxRetries);
      }
      
      // If we've reached the maximum number of retries or the error is not retryable, throw the error
      throw error;
    }
  }
  
  /**
   * Check if an error is retryable
   * @param {Error} error - The error to check
   * @returns {boolean} Whether the error is retryable
   */
  isRetryableError(error) {
    // Network errors, timeouts, and 5xx server errors are generally retryable
    if (!error.response) {
      // Network error or timeout
      return true;
    }
    
    // 5xx server errors are retryable
    if (error.response.status >= 500 && error.response.status < 600) {
      return true;
    }
    
    // 429 Too Many Requests is retryable
    if (error.response.status === 429) {
      return true;
    }
    
    // 401/403 errors are generally not retryable unless they're due to token expiration,
    // which is already handled in authenticateSpotify
    return false;
  }

  /**
   * Get Spotify playlists (requires Spotify integration)
   * This method first ensures Spotify authentication using environment variables
   * @returns {Promise<Object>} Spotify playlists
   */
  async getSpotifyPlaylists() {
    const endpoint = 'getSpotifyPlaylists';

    try {
      // Check circuit breaker
      if (this.isCircuitBreakerOpen(endpoint)) {
        const error = new Error('Circuit breaker is open for getSpotifyPlaylists endpoint due to repeated failures');
        error.isCircuitBreakerError = true;
        error.isWiimError = true;
        error.isSpotifyError = true;
        throw error;
      }

      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Get the user's Spotify playlists with a specific timeout and enhanced configuration
      const userPlaylistsResponse = await this.spotifyAxios.get('/me/playlists', {
        timeout: 25000, // Increased to 25 seconds timeout for Spotify API
        headers: {
          'Authorization': `Bearer ${this.spotifyAccessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      const userPlaylists = userPlaylistsResponse.data.items;

      // Format the playlists for the WiiM API
      const formattedPlaylists = userPlaylists.map(playlist => ({
        id: playlist.id,
        name: playlist.name,
        trackCount: playlist.tracks.total,
        description: playlist.description,
        images: playlist.images || []
      }));

      // Try to get playlists from the WiiM device (this command is undocumented)
      try {
        console.warn('getSpotifyPlaylists command is not officially documented and may not work');
        const response = await this.axios.get('/httpapi.asp?command=getSpotifyPlaylists', {
          timeout: 20000, // Increased to 20 seconds timeout for WiiM device
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Cache-Control': 'no-cache'
          }
        });

        // If the WiiM device returns playlists, use those (they might have additional info)
        if (response.data && response.data.playlists && response.data.playlists.length > 0) {
          // Record success for circuit breaker
          this.recordSuccess(endpoint);
          return response.data;
        }
      } catch (error) {
        // Enhanced error logging for WiiM device error
        console.warn('WiiM device getSpotifyPlaylists failed, using Spotify API directly:', {
          message: error.message,
          code: error.code,
          url: error.config?.url,
          timeout: error.config?.timeout
        });
      }

      // Record success for circuit breaker (we got Spotify data successfully)
      this.recordSuccess(endpoint);

      // Fall back to using playlists from Spotify API directly
      return { playlists: formattedPlaylists };
    } catch (error) {
      // Record failure for circuit breaker
      this.recordFailure(endpoint);

      // Enhanced error logging with more details
      console.error('Error fetching Spotify playlists:', {
        message: error.message,
        code: error.code,
        stack: error.stack,
        url: error.config?.url,
        method: error.config?.method,
        timeout: error.config?.timeout,
        circuitBreakerState: this.circuitBreaker[endpoint]
      });

      // Create a more descriptive error with additional context
      let errorMessage = `Error fetching Spotify playlists: ${error.message}`;

      if (error.message?.toLowerCase().includes('socket hang up')) {
        errorMessage += ' (Network connection was unexpectedly closed. This may be due to Spotify API timeout or network issues.)';
      } else if (error.code === 'ECONNREFUSED') {
        errorMessage += ' (Connection refused. Please check your internet connection.)';
      } else if (error.code === 'ETIMEDOUT') {
        errorMessage += ' (Request timed out. The Spotify API may be slow or unresponsive.)';
      } else if (error.response?.status === 401) {
        errorMessage += ' (Authentication failed. Please check your Spotify credentials.)';
      }

      const enhancedError = new Error(errorMessage);
      enhancedError.originalError = error;
      enhancedError.isWiimError = true;
      enhancedError.isSpotifyError = true;
      enhancedError.endpoint = endpoint;
      enhancedError.circuitBreakerFailures = this.circuitBreaker[endpoint]?.failures || 0;

      throw enhancedError;
    }
  }

  /**
   * Play Spotify playlist
   * This method first ensures Spotify authentication using environment variables
   * @param {string} playlistId Spotify playlist ID
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyPlaylist(playlistId) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Play the playlist on the WiiM device (this command is undocumented)
      console.warn('playSpotifyPlaylist command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=playSpotifyPlaylist:${playlistId}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify playlist ${playlistId} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Search Spotify for tracks, albums, artists, or playlists
   * This method first ensures Spotify authentication using environment variables
   * @param {string} query Search query
   * @param {string} type Type of search (track, album, artist, playlist)
   * @returns {Promise<Object>} Search results
   */
  async searchSpotify(query, type = 'track,album,artist,playlist') {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Search Spotify
      const searchResponse = await this.spotifyAxios.get('/search', {
        params: {
          q: query,
          type: type,
          limit: 20
        }
      });

      // Process the response to ensure consistent structure
      const processedData = { ...searchResponse.data };
      
      // Initialize empty structures if they don't exist to prevent "undefined is not an object" errors
      if (!processedData.tracks) {
        processedData.tracks = { items: [], total: 0 };
      } else if (!processedData.tracks.items) {
        processedData.tracks.items = [];
      }
      
      if (!processedData.albums) {
        processedData.albums = { items: [], total: 0 };
      } else if (!processedData.albums.items) {
        processedData.albums.items = [];
      }
      
      if (!processedData.artists) {
        processedData.artists = { items: [], total: 0 };
      } else if (!processedData.artists.items) {
        processedData.artists.items = [];
      }
      
      if (!processedData.playlists) {
        processedData.playlists = { items: [], total: 0 };
      } else if (!processedData.playlists.items) {
        processedData.playlists.items = [];
      }
      
      // Ensure tracks have consistent structure
      if (processedData.tracks && processedData.tracks.items) {
        processedData.tracks.items = processedData.tracks.items.map(track => {
          if (!track) return { album: { images: [] } };
          return {
            ...track,
            album: track.album ? {
              ...track.album,
              images: track.album.images || []
            } : { images: [] }
          };
        });
      }
      
      // Ensure albums have consistent structure
      if (processedData.albums && processedData.albums.items) {
        processedData.albums.items = processedData.albums.items.map(album => {
          if (!album) return { images: [] };
          return {
            ...album,
            images: album.images || []
          };
        });
      }
      
      // Ensure artists have consistent structure
      if (processedData.artists && processedData.artists.items) {
        processedData.artists.items = processedData.artists.items.map(artist => {
          if (!artist) return { images: [] };
          return {
            ...artist,
            images: artist.images || []
          };
        });
      }
      
      // Ensure playlists have consistent structure
      if (processedData.playlists && processedData.playlists.items) {
        processedData.playlists.items = processedData.playlists.items.map(playlist => {
          if (!playlist) return { images: [] };
          return {
            ...playlist,
            images: playlist.images || []
          };
        });
      }

      return processedData;
    } catch (error) {
      console.error(`Error searching Spotify for "${query}":`, error);
      throw error;
    }
  }

  /**
   * Play a Spotify track
   * This method first ensures Spotify authentication using environment variables
   * @param {string} trackId Spotify track ID
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyTrack(trackId) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Play the track on the WiiM device (this command is undocumented)
      console.warn('playSpotifyTrack command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=playSpotifyTrack:${trackId}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify track ${trackId} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Play a Spotify album
   * This method first ensures Spotify authentication using environment variables
   * @param {string} albumId Spotify album ID
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyAlbum(albumId) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Play the album on the WiiM device (this command is undocumented)
      console.warn('playSpotifyAlbum command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=playSpotifyAlbum:${albumId}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify album ${albumId} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Play a Spotify artist's top tracks
   * This method first ensures Spotify authentication using environment variables
   * @param {string} artistId Spotify artist ID
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyArtist(artistId) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Play the artist's top tracks on the WiiM device (this command is undocumented)
      console.warn('playSpotifyArtist command is not officially documented and may not work');
      const response = await this.axios.get(`/httpapi.asp?command=playSpotifyArtist:${artistId}`);
      return response.data;
    } catch (error) {
      console.error(`Error playing Spotify artist ${artistId} on WiiM:`, error);
      throw error;
    }
  }

  // ============================================================================
  // SPOTIFY CONNECT API METHODS
  // ============================================================================

  /**
   * Get available Spotify devices for playback
   * This method first ensures Spotify authentication using environment variables
   * @returns {Promise<Array>} List of available Spotify devices
   */
  async getSpotifyDevices() {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Get available devices from Spotify Connect API
      const response = await this.spotifyAxios.get('/me/player/devices', {
        timeout: 25000, // Increased to 25 seconds timeout for Spotify API
        headers: {
          'Authorization': `Bearer ${this.spotifyAccessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      return response.data.devices;
    } catch (error) {
      // Enhanced error logging with more details
      const errorDetails = {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      };
      
      console.error('Error getting Spotify devices:', errorDetails);
      
      // Create a more descriptive error based on the response
      if (error.response) {
        if (error.response.status === 401) {
          // Create a custom error for authentication failures
          const authError = new Error('Spotify authentication failed. Please check your Spotify credentials in the environment variables.');
          authError.status = 401;
          authError.isAuthError = true;
          throw authError;
        } else if (error.response.status === 403) {
          // Create a custom error for permission issues
          const permissionError = new Error('Spotify permission denied. Your Spotify account may not have the required permissions.');
          permissionError.status = 403;
          permissionError.isPermissionError = true;
          throw permissionError;
        }
      }
      
      throw error;
    }
  }

  /**
   * Get current Spotify playback state
   * This method first ensures Spotify authentication using environment variables
   * @returns {Promise<Object>} Current playback state
   */
  async getSpotifyPlaybackState() {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Get current playback state from Spotify Connect API
      const response = await this.spotifyAxios.get('/me/player', {
        timeout: 25000, // Increased to 25 seconds timeout for Spotify API
        headers: {
          'Authorization': `Bearer ${this.spotifyAccessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      
      // If WiiM device ID is set, check if playback is on that device
      if (this.wiimSpotifyDeviceId && response.data && response.data.device) {
        if (response.data.device.id === this.wiimSpotifyDeviceId) {
          response.data.is_playing_on_wiim = true;
        } else {
          response.data.is_playing_on_wiim = false;
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('Error getting Spotify playback state:', error);
      throw error;
    }
  }
  
  /**
   * Transfer playback to the WiiM device
   * This method uses the Spotify API to transfer playback to the WiiM device
   * @param {boolean} play Whether to ensure playback happens on the new device
   * @returns {Promise<Object>} Response data
   */
  async transferPlaybackToWiim(play = true) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();
      
      // Check if WiiM device ID is set
      if (!this.wiimSpotifyDeviceId) {
        throw new Error('WiiM Spotify device ID is not set. Please set the WIIM_SPOTIFY_DEVICE_ID environment variable.');
      }
      
      // Transfer playback to WiiM device
      const body = {
        device_ids: [this.wiimSpotifyDeviceId],
        play: play
      };
      
      await this.spotifyAxios.put('/me/player', body);
      return { success: true, message: 'Playback transferred to WiiM device' };
    } catch (error) {
      console.error('Error transferring playback to WiiM device:', error);
      throw error;
    }
  }
  
  /**
   * Get the current queue from Spotify
   * This method first ensures Spotify authentication using environment variables
   * @returns {Promise<Object>} Current queue
   */
  async getSpotifyQueue() {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();
      
      // Get current queue from Spotify API
      const response = await this.spotifyAxios.get('/me/player/queue', {
        timeout: 25000, // Increased to 25 seconds timeout for Spotify API
        headers: {
          'Authorization': `Bearer ${this.spotifyAccessToken}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting Spotify queue:', error);
      throw error;
    }
  }
  
  /**
   * Add an item to the Spotify queue
   * This method first ensures Spotify authentication using environment variables
   * @param {string} uri Spotify URI of the item to add (track, episode)
   * @param {string} deviceId (Optional) Spotify device ID to add the item to
   * @returns {Promise<Object>} Response data
   */
  async addToSpotifyQueue(uri, deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();
      
      // Prepare query parameters
      const params = { uri };
      
      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        deviceId = this.wiimSpotifyDeviceId;
      }
      
      if (deviceId) {
        params.device_id = deviceId;
      }
      
      // Add item to queue
      await this.spotifyAxios.post('/me/player/queue', null, { params });
      return { success: true, message: 'Item added to queue' };
    } catch (error) {
      console.error('Error adding item to Spotify queue:', error);
      throw error;
    }
  }
  
  /**
   * Skip to the next track in the Spotify queue
   * This method first ensures Spotify authentication using environment variables
   * @param {string} deviceId (Optional) Spotify device ID
   * @returns {Promise<Object>} Response data
   */
  async skipToNextTrack(deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();
      
      // Prepare query parameters
      const params = {};
      
      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        deviceId = this.wiimSpotifyDeviceId;
      }
      
      if (deviceId) {
        params.device_id = deviceId;
      }
      
      // Skip to next track
      await this.spotifyAxios.post('/me/player/next', null, { params });
      return { success: true, message: 'Skipped to next track' };
    } catch (error) {
      console.error('Error skipping to next track:', error);
      throw error;
    }
  }
  
  /**
   * Skip to the previous track in the Spotify queue
   * This method first ensures Spotify authentication using environment variables
   * @param {string} deviceId (Optional) Spotify device ID
   * @returns {Promise<Object>} Response data
   */
  async skipToPreviousTrack(deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();
      
      // Prepare query parameters
      const params = {};
      
      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        deviceId = this.wiimSpotifyDeviceId;
      }
      
      if (deviceId) {
        params.device_id = deviceId;
      }
      
      // Skip to previous track
      await this.spotifyAxios.post('/me/player/previous', null, { params });
      return { success: true, message: 'Skipped to previous track' };
    } catch (error) {
      console.error('Error skipping to previous track:', error);
      throw error;
    }
  }

  /**
   * Play a Spotify track using Spotify Connect API
   * This method first ensures Spotify authentication using environment variables
   * @param {string} trackId Spotify track ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyTrackConnect(trackId, deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        // Transfer playback to WiiM device first
        await this.transferPlaybackToWiim(false);
        deviceId = this.wiimSpotifyDeviceId;
      }

      // Prepare request body and query parameters
      const body = {
        uris: [`spotify:track:${trackId}`]
      };
      
      const params = {};
      if (deviceId) {
        params.device_id = deviceId;
      }

      // Play the track using Spotify Connect API
      await this.spotifyAxios.put('/me/player/play', body, { params });
      return { success: true, message: 'Track playback started' };
    } catch (error) {
      console.error(`Error playing Spotify track ${trackId} using Connect API:`, error);
      throw error;
    }
  }

  /**
   * Play a Spotify album using Spotify Connect API
   * This method first ensures Spotify authentication using environment variables
   * @param {string} albumId Spotify album ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyAlbumConnect(albumId, deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        // Transfer playback to WiiM device first
        await this.transferPlaybackToWiim(false);
        deviceId = this.wiimSpotifyDeviceId;
      }

      // Prepare request body and query parameters
      const body = {
        context_uri: `spotify:album:${albumId}`
      };
      
      const params = {};
      if (deviceId) {
        params.device_id = deviceId;
      }

      // Play the album using Spotify Connect API
      await this.spotifyAxios.put('/me/player/play', body, { params });
      return { success: true, message: 'Album playback started' };
    } catch (error) {
      console.error(`Error playing Spotify album ${albumId} using Connect API:`, error);
      throw error;
    }
  }

  /**
   * Play a Spotify artist using Spotify Connect API
   * This method first ensures Spotify authentication using environment variables
   * @param {string} artistId Spotify artist ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyArtistConnect(artistId, deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        // Transfer playback to WiiM device first
        await this.transferPlaybackToWiim(false);
        deviceId = this.wiimSpotifyDeviceId;
      }

      // Prepare request body and query parameters
      const body = {
        context_uri: `spotify:artist:${artistId}`
      };
      
      const params = {};
      if (deviceId) {
        params.device_id = deviceId;
      }

      // Play the artist using Spotify Connect API
      await this.spotifyAxios.put('/me/player/play', body, { params });
      return { success: true, message: 'Artist playback started' };
    } catch (error) {
      console.error(`Error playing Spotify artist ${artistId} using Connect API:`, error);
      throw error;
    }
  }

  /**
   * Play a Spotify playlist using Spotify Connect API
   * This method first ensures Spotify authentication using environment variables
   * @param {string} playlistId Spotify playlist ID
   * @param {string} deviceId (Optional) Spotify device ID to play on
   * @returns {Promise<Object>} Response data
   */
  async playSpotifyPlaylistConnect(playlistId, deviceId = null) {
    try {
      // Ensure we have a valid Spotify access token
      await this.ensureSpotifyAuthenticated();

      // Use WiiM device ID from environment variable if no device ID is provided
      if (!deviceId && this.wiimSpotifyDeviceId) {
        // Transfer playback to WiiM device first
        await this.transferPlaybackToWiim(false);
        deviceId = this.wiimSpotifyDeviceId;
      }

      // Prepare request body and query parameters
      const body = {
        context_uri: `spotify:playlist:${playlistId}`
      };
      
      const params = {};
      if (deviceId) {
        params.device_id = deviceId;
      }

      // Play the playlist using Spotify Connect API
      await this.spotifyAxios.put('/me/player/play', body, { params });
      return { success: true, message: 'Playlist playback started' };
    } catch (error) {
      console.error(`Error playing Spotify playlist ${playlistId} using Connect API:`, error);
      throw error;
    }
  }

  // ============================================================================
  // DOCUMENTED API METHODS (Based on official WiiM HTTP API documentation)
  // ============================================================================

  /**
   * Get WLAN connection state (DOCUMENTED)
   * @returns {Promise<Object>} WLAN connection state
   */
  async getWlanConnectionState() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=wlanGetConnectState');
      return response.data;
    } catch (error) {
      console.error('Error fetching WLAN connection state from WiiM:', error);
      throw error;
    }
  }

  /**
   * Resume playback (DOCUMENTED)
   * @returns {Promise<Object>} Response data
   */
  async resume() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=setPlayerCmd:resume');
      return response.data;
    } catch (error) {
      console.error('Error sending resume command to WiiM:', error);
      throw error;
    }
  }

  /**
   * One pause (specific pause mode) (DOCUMENTED)
   * @returns {Promise<Object>} Response data
   */
  async onePause() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=setPlayerCmd:onepause');
      return response.data;
    } catch (error) {
      console.error('Error sending onepause command to WiiM:', error);
      throw error;
    }
  }

  /**
   * Mute/unmute audio (DOCUMENTED)
   * @param {boolean} mute Whether to mute (true) or unmute (false)
   * @returns {Promise<Object>} Response data
   */
  async setMute(mute) {
    try {
      const muteValue = mute ? 1 : 0;
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:mute:${muteValue}`);
      return response.data;
    } catch (error) {
      console.error(`Error setting mute to ${mute} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Switch playback mode (DOCUMENTED)
   * @param {string} mode Playback mode
   * @returns {Promise<Object>} Response data
   */
  async switchPlaybackMode(mode) {
    try {
      const response = await this.axios.get(`/httpapi.asp?command=setPlayerCmd:switchmode:${encodeURIComponent(mode)}`);
      return response.data;
    } catch (error) {
      console.error(`Error switching playback mode to ${mode} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Enable equalizer (DOCUMENTED)
   * @returns {Promise<Object>} Response data
   */
  async enableEqualizer() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=EQOn');
      return response.data;
    } catch (error) {
      console.error('Error enabling equalizer on WiiM:', error);
      throw error;
    }
  }

  /**
   * Disable equalizer (DOCUMENTED)
   * @returns {Promise<Object>} Response data
   */
  async disableEqualizer() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=EQOff');
      return response.data;
    } catch (error) {
      console.error('Error disabling equalizer on WiiM:', error);
      throw error;
    }
  }

  /**
   * Get equalizer status (DOCUMENTED)
   * @returns {Promise<Object>} Equalizer status
   */
  async getEqualizerStatus() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=EQGetStat');
      return response.data;
    } catch (error) {
      console.error('Error fetching equalizer status from WiiM:', error);
      throw error;
    }
  }

  /**
   * Get equalizer preset list (DOCUMENTED)
   * @returns {Promise<Object>} Available equalizer presets
   */
  async getEqualizerPresets() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=EQGetList');
      return response.data;
    } catch (error) {
      console.error('Error fetching equalizer presets from WiiM:', error);
      throw error;
    }
  }

  /**
   * Load equalizer preset (DOCUMENTED)
   * @param {string} presetName Name of the equalizer preset
   * @returns {Promise<Object>} Response data
   */
  async loadEqualizerPreset(presetName) {
    try {
      const response = await this.axios.get(`/httpapi.asp?command=EQLoad:${encodeURIComponent(presetName)}`);
      return response.data;
    } catch (error) {
      console.error(`Error loading equalizer preset ${presetName} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Get metadata information (DOCUMENTED)
   * @returns {Promise<Object>} Metadata information
   */
  async getMetaInfo() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=getMetaInfo');
      return response.data;
    } catch (error) {
      console.error('Error fetching metadata from WiiM:', error);
      throw error;
    }
  }

  /**
   * Get preset information (DOCUMENTED)
   * @returns {Promise<Object>} Preset information
   */
  async getPresetInfo() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=getPresetInfo');
      return response.data;
    } catch (error) {
      console.error('Error fetching preset information from WiiM:', error);
      throw error;
    }
  }

  /**
   * Sync time (DOCUMENTED)
   * @param {string} timeString Time in format YYYYMMDDHHMMSS
   * @returns {Promise<Object>} Response data
   */
  async syncTime(timeString) {
    try {
      const response = await this.axios.get(`/httpapi.asp?command=timeSync:${timeString}`);
      return response.data;
    } catch (error) {
      console.error(`Error syncing time ${timeString} on WiiM:`, error);
      throw error;
    }
  }

  /**
   * Get alarm clock settings (DOCUMENTED)
   * @param {number} alarmNumber Alarm number
   * @returns {Promise<Object>} Alarm settings
   */
  async getAlarmClock(alarmNumber) {
    try {
      const response = await this.axios.get(`/httpapi.asp?command=getAlarmClock:${alarmNumber}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching alarm clock ${alarmNumber} from WiiM:`, error);
      throw error;
    }
  }

  /**
   * Stop alarm (DOCUMENTED)
   * @returns {Promise<Object>} Response data
   */
  async stopAlarm() {
    try {
      const response = await this.axios.get('/httpapi.asp?command=alarmStop');
      return response.data;
    } catch (error) {
      console.error('Error stopping alarm on WiiM:', error);
      throw error;
    }
  }
}

module.exports = WiimAPI;