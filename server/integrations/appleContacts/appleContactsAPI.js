const vCard = require('vcards-js');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

/**
 * Apple Contacts API Wrapper
 * Handles generation of vCard files for iOS devices
 */
class AppleContactsAPI {
  /**
   * Create a new AppleContactsAPI instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.tempDir = options.tempDir || path.join(__dirname, '../../../temp');
    this.integrationName = 'Apple Contacts';
    this.initialized = false;
  }

  /**
   * Initialize the Apple Contacts API
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // Ensure temp directory exists
      await mkdir(this.tempDir, { recursive: true });
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing Apple Contacts API:', error);
      throw error;
    }
  }

  /**
   * Generate vCard for a single contact
   * @param {Object} contactData - Contact data
   * @returns {string} - vCard data as string
   */
  generateVCard(contactData) {
    const card = vCard();

    // Set basic contact information
    card.firstName = contactData.name;
    card.organization = contactData.company || '';
    
    // Set phone number
    if (contactData.phone) {
      card.workPhone = contactData.phone;
    }
    
    // Set email
    if (contactData.email) {
      card.workEmail = contactData.email;
    }
    
    // Set address
    if (contactData.address && (contactData.address.street || contactData.address.city)) {
      card.workAddress.street = contactData.address.street || '';
      card.workAddress.city = contactData.address.city || '';
      card.workAddress.stateProvince = contactData.address.state || '';
      card.workAddress.postalCode = contactData.address.zipCode || '';
      card.workAddress.countryRegion = contactData.address.country || 'USA';
    }
    
    // Set notes
    if (contactData.notes) {
      card.note = contactData.notes;
    }
    
    // Add category as a custom field
    if (contactData.category) {
      // Use direct property assignment instead of addProperty method
      card.socialUrls = card.socialUrls || {};
      card.socialUrls['category'] = contactData.category;
    }
    
    return card.getFormattedString();
  }

  /**
   * Generate vCard file for multiple contacts
   * @param {Array} contacts - Array of contact data
   * @param {string} filename - Output filename (without extension)
   * @returns {Promise<string>} - Path to generated vCard file
   */
  async generateVCardFile(contacts, filename = 'contacts') {
    await this.initialize();
    
    // Generate vCard data for each contact
    const vCardData = contacts.map(contact => this.generateVCard(contact)).join('\n');
    
    // Create output file path
    const outputPath = path.join(this.tempDir, `${filename}.vcf`);
    
    // Write vCard data to file
    await writeFile(outputPath, vCardData, 'utf8');
    
    return outputPath;
  }

  /**
   * Generate vCard data for multiple contacts (as string)
   * @param {Array} contacts - Array of contact data
   * @returns {string} - Combined vCard data as string
   */
  generateVCardData(contacts) {
    return contacts.map(contact => this.generateVCard(contact)).join('\n');
  }

  /**
   * Filter contacts by category
   * @param {Array} contacts - Array of contact data
   * @param {Array} categories - Array of categories to include
   * @returns {Array} - Filtered contacts
   */
  filterContactsByCategory(contacts, categories) {
    if (!categories || categories.length === 0) {
      return contacts;
    }
    
    return contacts.filter(contact => categories.includes(contact.category));
  }

  /**
   * Generate a unique filename for vCard export
   * @param {string} userId - User ID
   * @returns {string} - Unique filename
   */
  generateUniqueFilename(userId) {
    const timestamp = new Date().getTime();
    return `contacts_${userId}_${timestamp}`;
  }

  /**
   * Clean up temporary files
   * @param {string} filePath - Path to file to delete
   * @returns {Promise<void>}
   */
  async cleanupTempFile(filePath) {
    try {
      await promisify(fs.unlink)(filePath);
    } catch (error) {
      console.error('Error cleaning up temporary file:', error);
    }
  }

  /**
   * Schedule cleanup of temporary files
   * @param {string} filePath - Path to file to delete
   * @param {number} delay - Delay in milliseconds before deletion
   */
  scheduleCleanup(filePath, delay = 3600000) { // Default: 1 hour
    setTimeout(() => {
      this.cleanupTempFile(filePath).catch(err => {
        console.error('Error in scheduled cleanup:', err);
      });
    }, delay);
  }
}

module.exports = AppleContactsAPI;