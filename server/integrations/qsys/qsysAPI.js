// Q-sys Core Manager API Wrapper
const axios = require('axios');
const net = require('net');
const https = require('https');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Q-sys Core Manager API Wrapper
 * This class provides methods to interact with the Q-sys Core Manager API
 */
class QsysAPI {
  /**
   * Constructor for QsysAPI
   * @param {string} host - The hostname or IP address of the Q-sys Core Manager
   * @param {number} port - The port number (default: 1710 for TCP, 443 for HTTPS)
   * @param {string} username - The username for authentication
   * @param {string} password - The password for authentication
   * @param {string} protocol - The protocol to use (tcp or https)
   */
  constructor(host, port, username, password, protocol = 'tcp') {
    this.host = host;
    this.port = port || (protocol === 'tcp' ? 1710 : 443);
    this.username = username;
    this.password = password;
    this.protocol = protocol;
    this.integrationName = 'Q-sys Core Manager';
    this.connected = false;
    this.socket = null;
    this.messageId = 1;
    this.pendingRequests = new Map();

    // Create HTTPS agent with configuration to allow self-signed certificates
    const httpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 30000, // Keep connections alive for 30 seconds
      maxSockets: 5, // Limit concurrent connections
      maxFreeSockets: 2, // Keep some connections in pool
      timeout: 10000, // Socket timeout
      freeSocketTimeout: 15000, // Free socket timeout
      rejectUnauthorized: false // Allow self-signed SSL certificates
    });

    // Create axios instance for HTTPS communication
    this.axios = axios.create({
      baseURL: `https://${host}:${port}`,
      headers: {
        'Content-Type': 'application/json',
        'Connection': 'keep-alive',
        'User-Agent': 'CSFPortal-Qsys-Client/1.0'
      },
      // Add timeout configuration to prevent socket hang up errors
      timeout: 10000, // 10 seconds
      // Use the HTTPS agent with self-signed certificate support for HTTPS requests
      httpsAgent: httpsAgent,
      // Disable automatic retries (we'll handle them manually)
      maxRedirects: 0,
      // Validate status codes
      validateStatus: (status) => status >= 200 && status < 300
    });

    // Add request interceptor for logging
    this.axios.interceptors.request.use(
      (config) => {
        console.log(`Q-sys API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Q-sys API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.axios.interceptors.response.use(
      (response) => {
        console.log(`Q-sys API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const config = error.config;

        // Log detailed error information
        console.error('Q-sys API Response Error:', {
          message: error.message,
          code: error.code,
          status: error.response?.status,
          url: config?.url,
          method: config?.method,
          timeout: config?.timeout,
          retryCount: config.__retryCount || 0
        });

        // Implement retry logic for specific errors
        if (this.shouldRetry(error) && (!config.__retryCount || config.__retryCount < 3)) {
          config.__retryCount = (config.__retryCount || 0) + 1;

          // Calculate exponential backoff delay
          const delay = Math.min(1000 * Math.pow(2, config.__retryCount - 1), 5000);

          console.log(`Retrying Q-sys API request (attempt ${config.__retryCount}/3) after ${delay}ms delay`);

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));

          // Retry the request
          return this.axios(config);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Initialize the Q-sys API and update integration status
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Try to connect to the Q-sys Core Manager
      if (this.protocol === 'tcp') {
        await this.connectTcp();
      } else {
        await this.getStatus();
      }

      // Update integration status to configured
      await integrationTracker.updateIntegrationStatus(this.integrationName, 'configured', {
        host: this.host,
        port: this.port,
        protocol: this.protocol
      });

      console.log(`${this.integrationName} integration initialized successfully`);
    } catch (error) {
      console.error(`Error initializing ${this.integrationName} integration:`, error);

      // Update integration status to error
      await integrationTracker.updateIntegrationStatus(this.integrationName, 'error', {
        error: error.message,
        host: this.host,
        port: this.port,
        protocol: this.protocol
      });

      throw error;
    }
  }

  /**
   * Determine if a request should be retried based on the error
   * @param {Error} error - The error that occurred
   * @returns {boolean} - Whether the request should be retried
   */
  shouldRetry(error) {
    // Retry on network errors, timeouts, and 5xx server errors
    return (
      error.code === 'ECONNABORTED' ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'EHOSTUNREACH' ||
      error.code === 'ENETUNREACH' ||
      (error.response && error.response.status >= 500 && error.response.status < 600)
    );
  }

  /**
   * Connect to the Q-sys Core Manager using TCP
   * @returns {Promise<void>}
   */
  connectTcp() {
    return new Promise((resolve, reject) => {
      if (this.connected && this.socket) {
        resolve();
        return;
      }

      // Create a new socket connection
      this.socket = new net.Socket();

      // Set up event handlers
      this.socket.on('connect', () => {
        console.log(`Connected to Q-sys Core Manager at ${this.host}:${this.port}`);
        this.connected = true;
        
        // Authenticate if credentials are provided
        if (this.username && this.password) {
          this.authenticate()
            .then(() => resolve())
            .catch(reject);
        } else {
          resolve();
        }
      });

      this.socket.on('data', (data) => {
        const response = data.toString();
        console.log(`Received data from Q-sys Core Manager: ${response}`);
        
        try {
          // Parse the response as JSON
          const jsonResponse = JSON.parse(response);
          
          // Check if this is a response to a pending request
          if (jsonResponse.id && this.pendingRequests.has(jsonResponse.id)) {
            const { resolve, reject } = this.pendingRequests.get(jsonResponse.id);
            
            // Remove the pending request
            this.pendingRequests.delete(jsonResponse.id);
            
            // Check for errors
            if (jsonResponse.error) {
              reject(new Error(jsonResponse.error.message || 'Unknown error'));
            } else {
              resolve(jsonResponse.result);
            }
          }
        } catch (error) {
          console.error('Error parsing response from Q-sys Core Manager:', error);
        }
      });

      this.socket.on('error', (error) => {
        console.error(`Socket error: ${error.message}`);
        this.connected = false;
        reject(error);
      });

      this.socket.on('close', () => {
        console.log('Connection to Q-sys Core Manager closed');
        this.connected = false;
      });

      // Connect to the Q-sys Core Manager
      this.socket.connect(this.port, this.host);
    });
  }

  /**
   * Authenticate with the Q-sys Core Manager
   * @returns {Promise<void>}
   */
  async authenticate() {
    if (this.protocol === 'tcp') {
      return this.sendTcpRequest('Logon', [this.username, this.password]);
    } else {
      // For HTTPS, authentication is typically done via Basic Auth
      this.axios.defaults.auth = {
        username: this.username,
        password: this.password
      };
      
      // Test authentication by getting status
      return this.getStatus();
    }
  }

  /**
   * Send a request to the Q-sys Core Manager using TCP
   * @param {string} method - The method to call
   * @param {Array} params - The parameters for the method
   * @returns {Promise<any>} - The response from the Q-sys Core Manager
   */
  sendTcpRequest(method, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.connected) {
        this.connectTcp()
          .then(() => this.sendTcpRequest(method, params))
          .then(resolve)
          .catch(reject);
        return;
      }

      // Create a unique ID for this request
      const id = this.messageId++;

      // Create the request object
      const request = {
        jsonrpc: '2.0',
        method,
        params,
        id
      };

      // Store the promise callbacks
      this.pendingRequests.set(id, { resolve, reject });

      // Send the request
      const requestString = JSON.stringify(request);
      console.log(`Sending request to Q-sys Core Manager: ${requestString}`);
      this.socket.write(requestString);
    });
  }

  /**
   * Send a request to the Q-sys Core Manager using HTTPS
   * @param {string} method - The method to call
   * @param {Array} params - The parameters for the method
   * @returns {Promise<any>} - The response from the Q-sys Core Manager
   */
  async sendHttpsRequest(method, params = []) {
    try {
      // Create the request object
      const request = {
        jsonrpc: '2.0',
        method,
        params,
        id: this.messageId++
      };

      // Send the request
      const response = await this.axios.post('/', request);

      // Check for errors
      if (response.data.error) {
        throw new Error(response.data.error.message || 'Unknown error');
      }

      return response.data.result;
    } catch (error) {
      console.error(`Error sending HTTPS request to Q-sys Core Manager: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send a request to the Q-sys Core Manager
   * @param {string} method - The method to call
   * @param {Array} params - The parameters for the method
   * @returns {Promise<any>} - The response from the Q-sys Core Manager
   */
  async sendRequest(method, params = []) {
    if (this.protocol === 'tcp') {
      return this.sendTcpRequest(method, params);
    } else {
      return this.sendHttpsRequest(method, params);
    }
  }

  /**
   * Get the status of the Q-sys Core Manager
   * @returns {Promise<any>} - The status of the Q-sys Core Manager
   */
  async getStatus() {
    return this.sendRequest('StatusGet');
  }

  /**
   * Get the health status of the Q-sys Core Manager
   * @returns {Promise<any>} - The health status of the Q-sys Core Manager
   */
  async getHealthStatus() {
    try {
      const status = await this.getStatus();
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        details: status
      };
    } catch (error) {
      console.error('Error getting Q-sys Core Manager health status:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Get a list of components in the Q-sys Core Manager
   * @returns {Promise<any>} - The list of components
   */
  async getComponents() {
    return this.sendRequest('Component.GetComponents');
  }

  /**
   * Get the controls for a component
   * @param {string} componentName - The name of the component
   * @returns {Promise<any>} - The controls for the component
   */
  async getControls(componentName) {
    return this.sendRequest('Component.GetControls', [componentName]);
  }

  /**
   * Get the value of a control
   * @param {string} componentName - The name of the component
   * @param {string} controlName - The name of the control
   * @returns {Promise<any>} - The value of the control
   */
  async getControlValue(componentName, controlName) {
    return this.sendRequest('Component.GetControlValue', [componentName, controlName]);
  }

  /**
   * Set the value of a control
   * @param {string} componentName - The name of the component
   * @param {string} controlName - The name of the control
   * @param {any} value - The value to set
   * @returns {Promise<any>} - The result of setting the value
   */
  async setControlValue(componentName, controlName, value) {
    return this.sendRequest('Component.SetControlValue', [componentName, controlName, value]);
  }

  /**
   * Get the configuration of the Q-sys Core Manager
   * @returns {Promise<any>} - The configuration
   */
  async getConfig() {
    try {
      // Set headers to prevent caching
      const headers = {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      };

      // Always prioritize environment variables for configuration
      const envHost = process.env.QSYS_HOST || '';
      const envPort = process.env.QSYS_PORT || (this.protocol === 'tcp' ? 1710 : 443);
      const envUsername = process.env.QSYS_USERNAME || '';
      const envPassword = process.env.QSYS_PASSWORD || '';
      const envProtocol = process.env.QSYS_PROTOCOL || 'tcp';

      let configData = {};
      let deviceInfo = null;

      // If environment variables are set, use them directly
      if (envHost) {
        configData = {
          host: envHost,
          port: envPort,
          username: envUsername,
          password: '********', // Mask password for security
          protocol: envProtocol,
          configuredAt: new Date(),
          fromEnv: true
        };

        // Try to get extended device information using environment variables
        try {
          // Ensure the API is using the correct configuration
          this.host = envHost;
          this.port = envPort;
          this.username = envUsername;
          this.password = envPassword;
          this.protocol = envProtocol;

          // Get status information
          const statusResponse = await this.getStatus();

          // Create deviceInfo object
          deviceInfo = {
            ...statusResponse,
            name: statusResponse?.Name || 'Unknown',
            model: statusResponse?.Model || 'Unknown'
          };
        } catch (deviceError) {
          console.error('Error fetching Q-sys Core Manager device information:', deviceError);
          // Continue without device info if there's an error
        }
      } else {
        return {
          message: 'Q-sys Core Manager configuration not found. Please set the required environment variables.'
        };
      }

      // Combine configuration data with device information
      const responseData = {
        ...configData,
        deviceInfo: deviceInfo
      };

      return responseData;
    } catch (error) {
      console.error('Error fetching Q-sys Core Manager configuration:', error);
      throw error;
    }
  }

  /**
   * Close the connection to the Q-sys Core Manager
   * @returns {Promise<void>}
   */
  async close() {
    if (this.protocol === 'tcp' && this.socket) {
      this.socket.end();
      this.connected = false;
    }
  }
}

module.exports = QsysAPI;