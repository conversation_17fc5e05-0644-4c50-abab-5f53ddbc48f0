// Google Calendar API Wrapper
const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { refreshAndSaveTokens } = require('../../utils/tokenRefresh');
const integrationTracker = require('../../utils/integrationTracker');
const { getAuthenticatedClient } = require('../../utils/googleServiceAuth');

/**
 * Google Calendar API Wrapper
 * Documentation: https://developers.google.com/calendar/api/v3/reference
 */
class GoogleCalendarAPI {
  constructor(clientId, clientSecret, redirectUri, tokenPath, userTokens = null, userId = null, userEmail = null) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.redirectUri = redirectUri;
    this.tokenPath = tokenPath;
    this.userTokens = userTokens;
    this.userId = userId;
    this.userEmail = userEmail;
    this.scopes = [
      'https://www.googleapis.com/auth/calendar',
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar.settings.readonly'
    ];
    this.auth = null;
    this.calendar = null;
    this.usingServiceAccount = false;
  }

  /**
   * Initialize the Google Calendar API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const integrationName = 'Google Calendar';
      
      // Always use service account authentication
      try {
        console.log('Using service account authentication for Google Calendar API');
        
        // If no user email is provided, throw an error
        if (!this.userEmail) {
          throw new Error('No user email provided for service account authentication');
        }
        
        // Use the common service account authentication module
        if (!process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY) {
          throw new Error('Service account credentials are missing. Please check your environment variables.');
        }
        
        this.auth = await getAuthenticatedClient('Calendar', this.scopes, this.userEmail);
        this.calendar = google.calendar({ version: 'v3', auth: this.auth });
        this.usingServiceAccount = true;
        
        // Update the integration status to active
        integrationTracker.updateStatus(
          integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated using service account.'
        );
        
        return;
      } catch (serviceAccountError) {
        console.error('Error initializing with service account:', serviceAccountError);
        
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          integrationName, 
          'error', 
          null, 
          `Error with service account: ${serviceAccountError.message}`
        );
        
        throw serviceAccountError;
      }
    } catch (error) {
      console.error('Error initializing Google Calendar API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        'Google Calendar', 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Generate authentication URL for OAuth2 flow
   * @returns {string} Authentication URL
   */
  getAuthUrl() {
    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: this.scopes,
      prompt: 'consent'
    });
  }

  /**
   * Get access token from authorization code
   * @param {string} code Authorization code
   * @returns {Promise<Object>} Token object
   */
  async getToken(code) {
    try {
      const integrationName = 'Google Calendar';
      const { tokens } = await this.auth.getToken(code);
      this.auth.setCredentials(tokens);

      // Save token to file for future use
      fs.writeFileSync(this.tokenPath, JSON.stringify(tokens));

      // Update the integration status to active
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'active', 
          new Date(), 
          'Successfully obtained new authentication token.'
        );
      }

      return tokens;
    } catch (error) {
      console.error('Error getting token:', error);

      // Update the integration status to indicate an error
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          'Google Calendar', 
          'error', 
          null, 
          `Error getting token: ${error.message}`
        );
      }

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    const integrationName = 'Google Calendar';

    // If using service account, we're authenticated as long as we have an auth object
    if (this.usingServiceAccount) {
      if (this.auth) {
        // Update the tracker to show we're authenticated with service account
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'active', 
            new Date(), 
            'Integration is properly authenticated using service account.'
          );
        }
        return true;
      } else {
        // Update the tracker if service account auth failed
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'error', 
            null, 
            'Service account authentication failed. Check credentials.'
          );
        }
        return false;
      }
    }

    // For OAuth authentication, check if we have a token
    if (!this.auth || !this.auth.credentials || !this.auth.credentials.access_token) {
      // Update the tracker if we're not authenticated
      if (!this.userTokens) { // Only update for global tokens, not user-specific ones
        integrationTracker.updateStatus(
          integrationName, 
          'not_configured', 
          null, 
          'No valid authentication token found.'
        );
      }
      return false;
    }

    // Then check if the token has all the required scopes
    const currentScopes = this.getScopes();
    const hasAllScopes = this.scopes.every(scope => 
      currentScopes.includes(scope) || 
      // Handle case where token has broader scope that includes our required scope
      currentScopes.some(currentScope => scope.startsWith(currentScope))
    );

    // Update the tracker based on the authentication status
    if (!this.userTokens && hasAllScopes) { // Only update for global tokens, not user-specific ones
      integrationTracker.updateStatus(
        integrationName, 
        'active', 
        new Date(), 
        'Integration is properly authenticated and ready to use.'
      );
    } else if (!this.userTokens && !hasAllScopes) {
      integrationTracker.updateStatus(
        integrationName, 
        'needs_auth', 
        null, 
        'Token does not have all required scopes. Need to re-authenticate.'
      );
    }

    return hasAllScopes;
  }

  /**
   * Get the current scopes from the token
   * @returns {Array} List of scopes
   */
  getScopes() {
    if (this.auth && this.auth.credentials && this.auth.credentials.scope) {
      return this.auth.credentials.scope.split(' ');
    }
    return [];
  }

  /**
   * List calendars
   * @returns {Promise<Array>} List of calendars
   */
  async listCalendars() {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.calendar.calendarList.list();
        return response.data.items;
      });
    } catch (error) {
      console.error('Error listing calendars:', error);
      throw error;
    }
  }

  /**
   * Get calendar details
   * @param {string} calendarId Calendar ID
   * @returns {Promise<Object>} Calendar details
   */
  async getCalendar(calendarId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.calendar.calendarList.get({
          calendarId
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting calendar:', error);
      throw error;
    }
  }

  /**
   * List events for a calendar
   * @param {string} calendarId Calendar ID
   * @param {Object} options Query options
   * @returns {Promise<Array>} List of events
   */
  async listEvents(calendarId, options = {}) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      const defaultOptions = {
        calendarId,
        timeMin: (new Date()).toISOString(),
        maxResults: 10,
        singleEvents: true,
        orderBy: 'startTime'
      };

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.calendar.events.list({
          ...defaultOptions,
          ...options
        });
        return response.data.items;
      });
    } catch (error) {
      console.error('Error listing events:', error);
      throw error;
    }
  }

  /**
   * Get event details
   * @param {string} calendarId Calendar ID
   * @param {string} eventId Event ID
   * @returns {Promise<Object>} Event details
   */
  async getEvent(calendarId, eventId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.calendar.events.get({
          calendarId,
          eventId
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error getting event:', error);
      throw error;
    }
  }

  /**
   * Create a new event
   * @param {string} calendarId Calendar ID
   * @param {Object} eventData Event data
   * @returns {Promise<Object>} Created event
   */
  async createEvent(calendarId, eventData) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.calendar.events.insert({
          calendarId,
          resource: eventData
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error creating event:', error);
      throw error;
    }
  }

  /**
   * Update an existing event
   * @param {string} calendarId Calendar ID
   * @param {string} eventId Event ID
   * @param {Object} eventData Event data
   * @returns {Promise<Object>} Updated event
   */
  async updateEvent(calendarId, eventId, eventData) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      return await this.executeWithTokenRefresh(async () => {
        const response = await this.calendar.events.update({
          calendarId,
          eventId,
          resource: eventData
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error updating event:', error);
      throw error;
    }
  }

  /**
   * Delete an event
   * @param {string} calendarId Calendar ID
   * @param {string} eventId Event ID
   * @returns {Promise<void>}
   */
  async deleteEvent(calendarId, eventId) {
    try {
      if (!this.isAuthenticated()) {
        throw new Error('Not authenticated');
      }

      await this.executeWithTokenRefresh(async () => {
        await this.calendar.events.delete({
          calendarId,
          eventId
        });
      });
    } catch (error) {
      console.error('Error deleting event:', error);
      throw error;
    }
  }

  /**
   * Execute an API request with automatic token refresh if needed
   * @param {Function} apiCall - Function that makes the API call
   * @returns {Promise<any>} API response
   */
  async executeWithTokenRefresh(apiCall) {
    try {
      // First attempt to execute the API call
      return await apiCall();
    } catch (error) {
      const integrationName = 'Google Calendar';

      // Check if the error is due to an expired token
      const isAuthError = 
        error.code === 401 || 
        error.code === 403 || 
        (error.response && (error.response.status === 401 || error.response.status === 403)) ||
        (error.message && (
          error.message.includes('invalid_grant') || 
          error.message.includes('Invalid Credentials') || 
          error.message.includes('token expired')
        ));

      // If it's an auth error and we have a refresh token, try to refresh
      if (isAuthError && this.auth && this.userId) {
        try {
          console.log('Token expired, attempting to refresh...');

          // Refresh the token
          await refreshAndSaveTokens(this.auth, this.userId);

          // Update the integration status to indicate token was refreshed
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'active', 
              new Date(), 
              'Token was successfully refreshed.'
            );
          }

          // Retry the API call with the new token
          return await apiCall();
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // Update the integration status to indicate an error with token refresh
          if (!this.userTokens) { // Only update for global tokens, not user-specific ones
            integrationTracker.updateStatus(
              integrationName, 
              'error', 
              null, 
              `Error refreshing token: ${refreshError.message}`
            );
          }

          throw refreshError;
        }
      } else if (isAuthError) {
        // It's an auth error but we can't refresh the token
        if (!this.userTokens) { // Only update for global tokens, not user-specific ones
          integrationTracker.updateStatus(
            integrationName, 
            'needs_auth', 
            null, 
            'Authentication error occurred and token could not be refreshed. Need to re-authenticate.'
          );
        }
        throw error;
      } else {
        // If it's not an auth error, rethrow the original error
        throw error;
      }
    }
  }
}

module.exports = GoogleCalendarAPI;
