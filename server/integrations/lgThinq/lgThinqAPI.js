// LG ThinQ API Wrapper
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');
const { v4: uuidv4 } = require('uuid');

/**
 * Helper function to implement retry logic with exponential backoff
 * @param {Function} apiCall - Async function that makes the API call
 * @param {Object} options - Options for retry behavior
 * @param {number} options.maxRetries - Maximum number of retry attempts (default: 3)
 * @param {number} options.initialDelay - Initial delay in ms before first retry (default: 1000)
 * @param {number} options.maxDelay - Maximum delay in ms between retries (default: 10000)
 * @param {Function} options.shouldRetry - Function to determine if retry should be attempted (default: retry on 503)
 * @returns {Promise<any>} - Result of the API call
 */
async function withRetry(apiCall, options = {}) {
  const maxRetries = options.maxRetries || 3;
  const initialDelay = options.initialDelay || 1000;
  const maxDelay = options.maxDelay || 10000;
  const shouldRetry = options.shouldRetry || ((error) => error.response && error.response.status === 503);
  
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      // Check if we should retry
      if (attempt < maxRetries && shouldRetry(error)) {
        // Calculate delay with exponential backoff and jitter
        const delay = Math.min(
          maxDelay,
          initialDelay * Math.pow(2, attempt) * (0.9 + Math.random() * 0.2)
        );
        
        console.log(`LG ThinQ API request failed with ${error.response?.status || 'unknown'} status. Retrying in ${Math.round(delay)}ms (attempt ${attempt + 1}/${maxRetries})...`);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // We've exhausted retries or shouldn't retry this error
        throw error;
      }
    }
  }
  
  // This should never be reached due to the throw in the loop, but just in case
  throw lastError;
}

/**
 * LG ThinQ API Wrapper
 * Documentation based on lg-openapi.json
 */
class LGThinqAPI {
  constructor(patToken, region = 'america', country = 'US', clientId = null) {
    this.patToken = patToken;
    this.country = country;
    this.region = region.toLowerCase();
    this.clientId = clientId || `csfportal-${uuidv4().replace(/-/g, '')}`;
    
    // Set base URL based on region
    switch (this.region) {
      case 'asia':
      case 'pacific':
        this.baseURL = 'https://api-kic.lgthinq.com';
        break;
      case 'america':
        this.baseURL = 'https://api-aic.lgthinq.com';
        break;
      case 'europe':
      case 'middle_east':
      case 'africa':
        this.baseURL = 'https://api-eic.lgthinq.com';
        break;
      default:
        this.baseURL = 'https://api-aic.lgthinq.com'; // Default to America
    }
    
    this.integrationName = 'LG ThinQ';
    // Fixed API key as per documentation
    this.fixedApiKey = 'v6GFvkweNo7DK7yD3ylIZ9w52aKBU0eJ7wLXkSR3';
    
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.fixedApiKey,
        'x-country': this.country,
        'x-service-phase': 'OP', // Operation phase
        'x-client-id': this.clientId
      }
    });
  }

  /**
   * Initialize the LG ThinQ API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we can make a test API call
      const isConfigured = await this.isConfigured();

      if (isConfigured) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly configured and ready to use.'
        );
      } else {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'API key validation failed. Check API key and region settings.'
        );
      }
    } catch (error) {
      console.error('Error initializing LG ThinQ API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Check if the client is properly configured
   * @returns {Promise<boolean>} Configuration status
   */
  async isConfigured() {
    try {
      if (!this.patToken) {
        return false;
      }

      // Make a test API call to verify the PAT token works
      // Using the route endpoint as it's a simple GET request
      await this.getRouteInfo();
      return true;
    } catch (error) {
      console.error('PAT token validation failed:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `PAT token validation error: ${error.message}`
      );

      return false;
    }
  }

  /**
   * Get route information (used to validate API key)
   * @returns {Promise<Object>} Route information
   */
  async getRouteInfo() {
    try {
      // Use withRetry to handle 503 errors with exponential backoff
      const result = await withRetry(async () => {
        // Generate a unique message ID for this request (base64url format)
        const messageId = Buffer.from(uuidv4().replace(/-/g, '')).toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '')
          .substring(0, 22);
        
        const response = await this.axios.get('/route', {
          headers: {
            'x-message-id': messageId
          }
        });
        
        return response.data;
      });
      
      // Update the integration status to active
      integrationTracker.updateStatus(
        this.integrationName, 
        'active', 
        new Date(), 
        'Successfully connected to LG ThinQ API.'
      );

      return result;
    } catch (error) {
      console.error('Error getting route information from LG ThinQ API:', error);

      // Provide more specific error message for 503 errors after retries
      if (error.response && error.response.status === 503) {
        const errorMessage = 'Error connecting to LG ThinQ API: Service temporarily unavailable (503). Retry attempts exhausted.';
        
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          errorMessage
        );
        
        const enhancedError = new Error(errorMessage);
        enhancedError.originalError = error;
        throw enhancedError;
      } else {
        // Update the integration status to indicate an error
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `API error: ${error.message}`
        );
        
        throw error;
      }
    }
  }

  /**
   * Get all devices
   * @returns {Promise<Array>} List of devices
   */
  async getDevices() {
    try {
      // Use withRetry to handle 503 errors with exponential backoff
      return await withRetry(async () => {
        // Generate a unique message ID for this request (base64url format)
        const messageId = Buffer.from(uuidv4().replace(/-/g, '')).toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '')
          .substring(0, 22);
        
        const response = await this.axios.get('/devices', {
          headers: {
            'Authorization': `Bearer ${this.patToken}`,
            'x-message-id': messageId
          }
        });
        return response.data;
      });
    } catch (error) {
      console.error('Error fetching LG ThinQ devices:', error);
      
      // Provide more specific error message for 503 errors after retries
      if (error.response && error.response.status === 503) {
        const enhancedError = new Error('Error fetching LG ThinQ devices: Service temporarily unavailable (503). Retry attempts exhausted.');
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get device profile (detailed information)
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device profile
   */
  async getDeviceDetails(deviceId) {
    try {
      // Use withRetry to handle 503 errors with exponential backoff
      return await withRetry(async () => {
        // Generate a unique message ID for this request (base64url format)
        const messageId = Buffer.from(uuidv4().replace(/-/g, '')).toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '')
          .substring(0, 22);
        
        const response = await this.axios.get(`/devices/${deviceId}/profile`, {
          headers: {
            'Authorization': `Bearer ${this.patToken}`,
            'x-message-id': messageId
          }
        });
        return response.data;
      });
    } catch (error) {
      console.error(`Error fetching LG ThinQ device profile for ${deviceId}:`, error);
      
      // Provide more specific error message for 503 errors after retries
      if (error.response && error.response.status === 503) {
        const enhancedError = new Error(`Error fetching LG ThinQ device profile for ${deviceId}: Service temporarily unavailable (503). Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Get device state (current status)
   * @param {string} deviceId Device ID
   * @returns {Promise<Object>} Device state
   */
  async getDeviceStatus(deviceId) {
    try {
      // Use withRetry to handle 503 errors with exponential backoff
      return await withRetry(async () => {
        // Generate a unique message ID for this request (base64url format)
        const messageId = Buffer.from(uuidv4().replace(/-/g, '')).toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '')
          .substring(0, 22);
        
        const response = await this.axios.get(`/devices/${deviceId}/state`, {
          headers: {
            'Authorization': `Bearer ${this.patToken}`,
            'x-message-id': messageId
          }
        });
        return response.data;
      }, {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 10000,
        shouldRetry: (error) => error.response && error.response.status === 503
      });
    } catch (error) {
      console.error(`Error fetching LG ThinQ device state for ${deviceId}:`, error);
      
      // Provide more specific error message for 503 errors after retries
      if (error.response && error.response.status === 503) {
        const enhancedError = new Error(`Error fetching LG ThinQ device state for ${deviceId}: Service temporarily unavailable (503). Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Control device with a command
   * @param {string} deviceId Device ID
   * @param {Object} command Command object with control parameters
   * @returns {Promise<Object>} Operation result
   */
  async controlDevice(deviceId, command) {
    try {
      // Use withRetry to handle 503 errors with exponential backoff
      return await withRetry(async () => {
        // Generate a unique message ID for this request (base64url format)
        const messageId = Buffer.from(uuidv4().replace(/-/g, '')).toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=/g, '')
          .substring(0, 22);
        
        const response = await this.axios.post(`/devices/${deviceId}/control`, command, {
          headers: {
            'Authorization': `Bearer ${this.patToken}`,
            'x-message-id': messageId
          }
        });
        return response.data;
      });
    } catch (error) {
      console.error(`Error controlling LG ThinQ device ${deviceId}:`, error);
      
      // Provide more specific error message for 503 errors after retries
      if (error.response && error.response.status === 503) {
        const enhancedError = new Error(`Error controlling LG ThinQ device ${deviceId}: Service temporarily unavailable (503). Retry attempts exhausted.`);
        enhancedError.originalError = error;
        throw enhancedError;
      }
      
      throw error;
    }
  }

  /**
   * Set device power state
   * @param {string} deviceId Device ID
   * @param {boolean} power Power state (true = on, false = off)
   * @returns {Promise<Object>} Operation result
   */
  async setPower(deviceId, power) {
    return this.controlDevice(deviceId, {
      command: 'power',
      value: power
    });
  }

  /**
   * Set device temperature
   * @param {string} deviceId Device ID
   * @param {number} temperature Temperature in Celsius
   * @returns {Promise<Object>} Operation result
   */
  async setTemperature(deviceId, temperature) {
    return this.controlDevice(deviceId, {
      command: 'temperature',
      value: temperature
    });
  }

  /**
   * Set device fan speed
   * @param {string} deviceId Device ID
   * @param {number} speed Fan speed (1-5)
   * @returns {Promise<Object>} Operation result
   */
  async setFanSpeed(deviceId, speed) {
    return this.controlDevice(deviceId, {
      command: 'fan-speed',
      value: speed
    });
  }

  /**
   * Set device mode
   * @param {string} deviceId Device ID
   * @param {string} mode Device mode (cool, heat, fan, dry, auto)
   * @returns {Promise<Object>} Operation result
   */
  async setMode(deviceId, mode) {
    return this.controlDevice(deviceId, {
      command: 'mode',
      value: mode
    });
  }

  /**
   * Set device swing mode
   * @param {string} deviceId Device ID
   * @param {string} swingMode Swing mode (horizontal, vertical, both, off)
   * @returns {Promise<Object>} Operation result
   */
  async setSwingMode(deviceId, swingMode) {
    return this.controlDevice(deviceId, {
      command: 'swing-mode',
      value: swingMode
    });
  }
}

module.exports = LGThinqAPI;