// UniFi Access API Wrapper
const axios = require('axios');
const https = require('https');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * UniFi Access API Wrapper
 * Documentation: https://assets.identity.ui.com/unifi-access/api_reference.pdf
 */
class UnifiAccessAPI {
  constructor() {
    // Read configuration from environment variables
    this.host = process.env.UNIFI_ACCESS_HOST || '';
    this.apiKey = process.env.UNIFI_ACCESS_API_KEY || '';
    this.port = process.env.UNIFI_ACCESS_PORT || 443;
    // According to the official API documentation, the base URL should be:
    this.baseURL = `https://${this.host}:${this.port}/v1`;
    this.token = null;
    this.cookies = null;
    this.integrationName = 'UniFi Access';

    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json'
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  }

  /**
   * Initialize the UniFi Access API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we can authenticate
      const isAuthenticated = await this.isAuthenticated();

      if (isAuthenticated) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly authenticated and ready to use.'
        );
      } else {
        // Update the integration status to indicate authentication is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Authentication failed. Check credentials and try again.'
        );
      }
    } catch (error) {
      console.error('Error initializing UniFi Access API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Check if the client is authenticated
   * @returns {Promise<boolean>} Authentication status
   */
  async isAuthenticated() {
    try {
      if (!this.apiKey) {
        console.error('API key is not configured');
        return false;
      }

      // According to the official API documentation, the API key should be set in the Authorization header as:
      if (!this.axios.defaults.headers.common['Authorization']) {
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
      }

      // Make a simple request to verify the API key works
      try {
        await this.axios.get('/devices');
        return true;
      } catch (apiError) {
        console.error('API key validation failed:', apiError);
        // Improved error handling with more specific error messages
        if (apiError.response) {
          console.error(`Status: ${apiError.response.status}, Data:`, apiError.response.data);
          if (apiError.response.status === 401) {
            console.error('Authentication failed: Invalid API key');
          }
        }
        return false;
      }
    } catch (error) {
      console.error('Authentication check failed:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Authentication error: ${error.message}`
      );

      return false;
    }
  }

  /**
   * Authenticate with UniFi Access using API key
   * @returns {Promise<boolean>} Authentication success
   */
  async authenticate() {
    try {
      if (!this.apiKey) {
        throw new Error('API key is not configured. Please set the UNIFI_ACCESS_API_KEY environment variable.');
      }

      // According to the official API documentation, the API key should be set in the Authorization header as:
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
      
      // Make a simple request to verify the API key works
      // We'll use the system status endpoint as it should be accessible with the API key
      const response = await this.axios.get('/devices');
      
      // If we get here, the API key is valid
      // Update the integration status to active
      integrationTracker.updateStatus(
        this.integrationName, 
        'active', 
        new Date(), 
        'Successfully authenticated with UniFi Access using API key.'
      );

      return true;
    } catch (error) {
      console.error('Error authenticating with UniFi Access:', error);

      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        if (error.response.status === 401) {
          integrationTracker.updateStatus(
            this.integrationName, 
            'error', 
            null, 
            'Authentication failed: Invalid API key'
          );
        } else {
          integrationTracker.updateStatus(
            this.integrationName, 
            'error', 
            null, 
            `Authentication error: ${error.response.status} ${error.response.statusText}`
          );
        }
      } else if (error.request) {
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          'Authentication error: No response received from server'
        );
      } else {
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `Authentication error: ${error.message}`
        );
      }

      throw error;
    }
  }

  /**
   * Ensure API key is set in headers
   * @private
   * @throws {Error} If API key is not configured
   */
  ensureApiKeySet() {
    if (!this.axios.defaults.headers.common['Authorization']) {
      if (!this.apiKey) {
        throw new Error('API key is not configured. Please set the UNIFI_ACCESS_API_KEY environment variable.');
      }
      // According to the official API documentation, the API key should be set in the Authorization header as:
      this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.apiKey}`;
    }
  }

  /**
   * Get all doors
   * @returns {Promise<Array>} List of doors
   */
  async getDoors() {
    try {
      this.ensureApiKeySet();
      // Try primary and fallback endpoints for getting all doors
      const endpointsToTry = ['/doors', '/access-doors', '/devices/doors', '/doors/list'];
      let lastAxios404 = null;
      for (const path of endpointsToTry) {
        try {
          const response = await this.axios.get(path);
          if (response && typeof response.data !== 'undefined') {
            if (path !== '/doors') {
              console.log(`[UniFi Access] /doors returned 404; succeeded with fallback endpoint ${path}`);
            }
            return response.data;
          }
        } catch (err) {
          if (err && err.response && err.response.status === 404) {
            // Save the 404 and try next fallback
            lastAxios404 = err;
            continue;
          }
          // Non-404 errors should bubble up
          throw err;
        }
      }

      // If we reached here, all tried endpoints returned 404
      console.error(`All UniFi Access door endpoints returned 404. Tried: ${endpointsToTry.join(', ')}`);
      // Create an axios-like error so the outer handler formats message consistently
      const notFoundErr = new Error('Not Found');
      notFoundErr.response = { status: 404, statusText: 'Not Found', data: lastAxios404?.response?.data };
      throw notFoundErr;
    } catch (error) {
      console.error('Error fetching UniFi Access doors:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch doors: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server');
        throw new Error('No response received from server when fetching doors');
      } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get door details
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Door details
   */
  async getDoorDetails(doorId) {
    try {
      this.ensureApiKeySet();
      // Try primary and fallback endpoints for getting door details
      const endpointsToTry = [
        `/doors/${doorId}`,
        `/access-doors/${doorId}`,
        `/devices/doors/${doorId}`,
        `/doors/${doorId}/details`
      ];
      let lastAxios404 = null;
      for (const path of endpointsToTry) {
        try {
          const response = await this.axios.get(path);
          if (response && typeof response.data !== 'undefined') {
            if (path !== `/doors/${doorId}`) {
              console.log(`[UniFi Access] /doors/${doorId} returned 404; succeeded with fallback endpoint ${path}`);
            }
            return response.data;
          }
        } catch (err) {
          if (err && err.response && err.response.status === 404) {
            // Save the 404 and try next fallback
            lastAxios404 = err;
            continue;
          }
          // Non-404 errors should bubble up
          throw err;
        }
      }

      // If we reached here, all tried endpoints returned 404
      console.error(`All UniFi Access door detail endpoints returned 404 for ${doorId}. Tried: ${endpointsToTry.join(', ')}`);
      // Create an axios-like error so the outer handler formats message consistently
      const notFoundErr = new Error('Not Found');
      notFoundErr.response = { status: 404, statusText: 'Not Found', data: lastAxios404?.response?.data };
      throw notFoundErr;
    } catch (error) {
      console.error(`Error fetching UniFi Access door details for ${doorId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch door details: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching door details');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Unlock a door
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Operation result
   */
  async unlockDoor(doorId) {
    try {
      this.ensureApiKeySet();
      // According to the official API documentation, the endpoint for unlocking a door is:
      const response = await this.axios.post(`/doors/${doorId}/unlock`);
      return response.data;
    } catch (error) {
      console.error(`Error unlocking UniFi Access door ${doorId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to unlock door: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when unlocking door');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  // Note: The lockDoor function has been removed as it does not exist in the UniFi Access API
  // according to the official API documentation.

  /**
   * Get all access points
   * @returns {Promise<Array>} List of access points
   */
  async getAccessPoints() {
    try {
      this.ensureApiKeySet();
      // According to the official API documentation, the endpoint for getting all access points is:
      const response = await this.axios.get('/devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access devices:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch devices: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching devices');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get all users
   * @returns {Promise<Array>} List of users
   */
  async getUsers() {
    try {
      this.ensureApiKeySet();
      // According to the official API documentation, the endpoint for getting all users is:
      const response = await this.axios.get('/users');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access users:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch users: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching users');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get access events
   * @param {Object} params Query parameters
   * @returns {Promise<Array>} List of access events
   */
  async getEvents(params = {}) {
    try {
      this.ensureApiKeySet();
      // According to the official API documentation, the endpoint for getting events is:
      const response = await this.axios.get('/events', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access events:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch events: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching events');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      this.ensureApiKeySet();
      // According to the official API documentation, the endpoint for getting system status is:
      const response = await this.axios.get('/devices');
      return response.data;
    } catch (error) {
      console.error('Error fetching UniFi Access system status:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch system status: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching system status');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get all groups
   * @returns {Promise<Array>} List of groups
   */
  async getGroups() {
    try {
      this.ensureApiKeySet();
      // Try primary and fallback endpoints for getting all groups
      const endpointsToTry = ['/groups', '/user-groups', '/access-groups'];
      let lastAxios404 = null;
      for (const path of endpointsToTry) {
        try {
          const response = await this.axios.get(path);
          if (response && typeof response.data !== 'undefined') {
            if (path !== '/groups') {
              console.log(`[UniFi Access] /groups returned 404; succeeded with fallback endpoint ${path}`);
            }
            return response.data;
          }
        } catch (err) {
          if (err && err.response && err.response.status === 404) {
            // Save the 404 and try next fallback
            lastAxios404 = err;
            continue;
          }
          // Non-404 errors should bubble up
          throw err;
        }
      }

      // If we reached here, all tried endpoints returned 404
      console.error(`All UniFi Access group endpoints returned 404. Tried: ${endpointsToTry.join(', ')}`);
      // Create an axios-like error so the outer handler formats message consistently
      const notFoundErr = new Error('Not Found');
      notFoundErr.response = { status: 404, statusText: 'Not Found', data: lastAxios404?.response?.data };
      throw notFoundErr;
    } catch (error) {
      console.error('Error fetching UniFi Access groups:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch groups: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching groups');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get group details
   * @param {string} groupId Group ID
   * @returns {Promise<Object>} Group details
   */
  async getGroupDetails(groupId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for getting group details
      const response = await this.axios.get(`/groups/${groupId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access group details for ${groupId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch group details: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching group details');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Create a new group
   * @param {Object} groupData Group data
   * @returns {Promise<Object>} Created group
   */
  async createGroup(groupData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for creating a group
      const response = await this.axios.post('/groups', groupData);
      return response.data;
    } catch (error) {
      console.error('Error creating UniFi Access group:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to create group: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when creating group');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Update a group
   * @param {string} groupId Group ID
   * @param {Object} groupData Group data
   * @returns {Promise<Object>} Updated group
   */
  async updateGroup(groupId, groupData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for updating a group
      const response = await this.axios.put(`/groups/${groupId}`, groupData);
      return response.data;
    } catch (error) {
      console.error(`Error updating UniFi Access group ${groupId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to update group: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when updating group');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Delete a group
   * @param {string} groupId Group ID
   * @returns {Promise<Object>} Operation result
   */
  async deleteGroup(groupId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for deleting a group
      const response = await this.axios.delete(`/groups/${groupId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting UniFi Access group ${groupId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to delete group: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when deleting group');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Add a user to a group
   * @param {string} groupId Group ID
   * @param {string} userId User ID
   * @returns {Promise<Object>} Operation result
   */
  async addUserToGroup(groupId, userId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for adding a user to a group
      const response = await this.axios.post(`/groups/${groupId}/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error adding user ${userId} to UniFi Access group ${groupId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to add user to group: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when adding user to group');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Remove a user from a group
   * @param {string} groupId Group ID
   * @param {string} userId User ID
   * @returns {Promise<Object>} Operation result
   */
  async removeUserFromGroup(groupId, userId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for removing a user from a group
      const response = await this.axios.delete(`/groups/${groupId}/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing user ${userId} from UniFi Access group ${groupId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to remove user from group: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when removing user from group');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get all policies
   * @returns {Promise<Array>} List of policies
   */
  async getPolicies() {
    try {
      this.ensureApiKeySet();
      // Try primary and fallback endpoints for getting all policies
      const endpointsToTry = ['/policies', '/access-policies', '/policies/access'];
      let lastAxios404 = null;
      for (const path of endpointsToTry) {
        try {
          const response = await this.axios.get(path);
          if (response && typeof response.data !== 'undefined') {
            if (path !== '/policies') {
              console.log(`[UniFi Access] /policies returned 404; succeeded with fallback endpoint ${path}`);
            }
            return response.data;
          }
        } catch (err) {
          if (err && err.response && err.response.status === 404) {
            // Save the 404 and try next fallback
            lastAxios404 = err;
            continue;
          }
          // Non-404 errors should bubble up
          throw err;
        }
      }

      // If we reached here, all tried endpoints returned 404
      console.error(`All UniFi Access policy endpoints returned 404. Tried: ${endpointsToTry.join(', ')}`);
      // Create an axios-like error so the outer handler formats message consistently
      const notFoundErr = new Error('Not Found');
      notFoundErr.response = { status: 404, statusText: 'Not Found', data: lastAxios404?.response?.data };
      throw notFoundErr;
    } catch (error) {
      console.error('Error fetching UniFi Access policies:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch policies: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching policies');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get policy details
   * @param {string} policyId Policy ID
   * @returns {Promise<Object>} Policy details
   */
  async getPolicyDetails(policyId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for getting policy details
      const response = await this.axios.get(`/policies/${policyId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access policy details for ${policyId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch policy details: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching policy details');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Create a new policy
   * @param {Object} policyData Policy data
   * @returns {Promise<Object>} Created policy
   */
  async createPolicy(policyData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for creating a policy
      const response = await this.axios.post('/policies', policyData);
      return response.data;
    } catch (error) {
      console.error('Error creating UniFi Access policy:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to create policy: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when creating policy');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Update a policy
   * @param {string} policyId Policy ID
   * @param {Object} policyData Policy data
   * @returns {Promise<Object>} Updated policy
   */
  async updatePolicy(policyId, policyData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for updating a policy
      const response = await this.axios.put(`/policies/${policyId}`, policyData);
      return response.data;
    } catch (error) {
      console.error(`Error updating UniFi Access policy ${policyId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to update policy: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when updating policy');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Delete a policy
   * @param {string} policyId Policy ID
   * @returns {Promise<Object>} Operation result
   */
  async deletePolicy(policyId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for deleting a policy
      const response = await this.axios.delete(`/policies/${policyId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting UniFi Access policy ${policyId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to delete policy: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when deleting policy');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Assign a policy to a door
   * @param {string} policyId Policy ID
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Operation result
   */
  async assignPolicyToDoor(policyId, doorId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for assigning a policy to a door
      const response = await this.axios.post(`/policies/${policyId}/doors/${doorId}`);
      return response.data;
    } catch (error) {
      console.error(`Error assigning policy ${policyId} to door ${doorId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to assign policy to door: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when assigning policy to door');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Remove a policy from a door
   * @param {string} policyId Policy ID
   * @param {string} doorId Door ID
   * @returns {Promise<Object>} Operation result
   */
  async removePolicyFromDoor(policyId, doorId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for removing a policy from a door
      const response = await this.axios.delete(`/policies/${policyId}/doors/${doorId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing policy ${policyId} from door ${doorId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to remove policy from door: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when removing policy from door');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Get user details
   * @param {string} userId User ID
   * @returns {Promise<Object>} User details
   */
  async getUserDetails(userId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for getting user details
      const response = await this.axios.get(`/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching UniFi Access user details for ${userId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to fetch user details: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when fetching user details');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Create a new user
   * @param {Object} userData User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for creating a user
      const response = await this.axios.post('/users', userData);
      return response.data;
    } catch (error) {
      console.error('Error creating UniFi Access user:', error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to create user: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when creating user');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Update a user
   * @param {string} userId User ID
   * @param {Object} userData User data
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, userData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for updating a user
      const response = await this.axios.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Error updating UniFi Access user ${userId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to update user: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when updating user');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Delete a user
   * @param {string} userId User ID
   * @returns {Promise<Object>} Operation result
   */
  async deleteUser(userId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for deleting a user
      const response = await this.axios.delete(`/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting UniFi Access user ${userId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to delete user: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when deleting user');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Issue a card to a user
   * @param {string} userId User ID
   * @param {Object} cardData Card data
   * @returns {Promise<Object>} Operation result
   */
  async issueCard(userId, cardData) {
    try {
      this.ensureApiKeySet();
      // Endpoint for issuing a card to a user
      const response = await this.axios.post(`/users/${userId}/cards`, cardData);
      return response.data;
    } catch (error) {
      console.error(`Error issuing card to UniFi Access user ${userId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to issue card: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when issuing card');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }

  /**
   * Revoke a card from a user
   * @param {string} userId User ID
   * @param {string} cardId Card ID
   * @returns {Promise<Object>} Operation result
   */
  async revokeCard(userId, cardId) {
    try {
      this.ensureApiKeySet();
      // Endpoint for revoking a card from a user
      const response = await this.axios.delete(`/users/${userId}/cards/${cardId}`);
      return response.data;
    } catch (error) {
      console.error(`Error revoking card ${cardId} from UniFi Access user ${userId}:`, error);
      // Improved error handling with more specific error messages
      if (error.response) {
        console.error(`Status: ${error.response.status}, Data:`, error.response.data);
        throw new Error(`Failed to revoke card: ${error.response.status} ${error.response.statusText}`);
      } else if (error.request) {
        console.error('No response received from server');
        throw new Error('No response received from server when revoking card');
      } else {
        throw new Error(`Error setting up request: ${error.message}`);
      }
    }
  }
}

module.exports = UnifiAccessAPI;
