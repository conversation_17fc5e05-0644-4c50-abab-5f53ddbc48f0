// Planning Center API Wrapper
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');
require('dotenv').config();

/**
 * Planning Center API Wrapper
 * Documentation: https://developer.planning.center/docs/#/
 * 
 * Note: The Planning Center API uses 'v2' versioning in the actual API endpoints (e.g., '/people/v2/people'),
 * while the documentation URL may use a date-based versioning scheme (e.g., '2025-07-02' for People API).
 * This implementation follows the API specifications by using the correct endpoint versioning.
 * 
 * People API Documentation: https://developer.planning.center/docs/#/apps/people/2025-07-02/vertices/person
 * Calendar API Documentation: https://developer.planning.center/docs/#/apps/calendar/2022-07-07/vertices/resource
 */
class PlanningCenterAPI {
  constructor() {
    this.username = process.env.PLANNING_CENTER_APPID || '';
    this.password = process.env.PLANNING_CENTER_TOKEN || '';
    this.baseURL = 'https://api.planningcenteronline.com';
    this.integrationName = 'Planning Center';

    // Initialize axios with basic auth credentials
    this.initializeAxios();
  }

  /**
   * Initialize or update the axios instance with basic authentication
   */
  initializeAxios() {
    this.axios = axios.create({
      baseURL: this.baseURL,
      auth: {
        username: this.username,
        password: this.password
      }
    });
  }

  /**
   * Update the authentication credentials and reinitialize the axios instance
   * @param {string} username - The username for basic auth
   * @param {string} password - The password for basic auth
   */
  updateCredentials(username, password) {
    this.username = username;
    this.password = password;
    this.initializeAxios();
  }

  /**
   * Initialize the Planning Center API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we have valid credentials
      if (!this.username || !this.password) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName, 
          'not_configured', 
          null, 
          'Username and password are required for Planning Center integration.'
        );
        return;
      }

      // Actually verify the credentials by making a test API call
      try {
        // Make a simple API call to verify the credentials work
        await this.getApplications();

        // If we get here, the credentials are valid
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName, 
          'active', 
          new Date(), 
          'Integration is properly configured with valid credentials.'
        );
      } catch (apiError) {
        // If the API call fails, the credentials are invalid
        console.error('Error verifying Planning Center credentials:', apiError);

        // Update the integration status to indicate an error with the credentials
        integrationTracker.updateStatus(
          this.integrationName, 
          'error', 
          null, 
          `Invalid credentials: ${apiError.message}`
        );

        throw apiError;
      }
    } catch (error) {
      console.error('Error initializing Planning Center API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName, 
        'error', 
        null, 
        `Error: ${error.message}`
      );

      throw error;
    }
  }


  /**
   * Get all available applications
   * @returns {Promise<Array>} List of applications
   */
  async getApplications() {
    try {
      const response = await this.axios.get('/');
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center applications:', error);
      throw error;
    }
  }

  /**
   * Get calendar events
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} List of events with processed included data
   */
  async getEvents(params = {}) {
    try {
      // Validate and prepare query parameters
      const queryParams = { ...params };
      
      // If no where[starts_at] parameter is provided, default to events starting from today
      if (!queryParams['where[starts_at]'] && !queryParams['where[ends_at]']) {
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        queryParams['where[starts_at]'] = `>=${formattedDate}`;
      }
      
      // Validate date parameters if provided
      if (queryParams['where[starts_at]']) {
        // Ensure the date format is valid
        // The API expects dates in YYYY-MM-DD format or with operators like >=YYYY-MM-DD
        const datePattern = /^(>=|<=|>|<)?(\d{4}-\d{2}-\d{2})$/;
        const dateValue = queryParams['where[starts_at]'];
        
        if (!datePattern.test(dateValue)) {
          console.warn(`Invalid date format for where[starts_at]: ${dateValue}. Using default.`);
          const today = new Date();
          const formattedDate = today.toISOString().split('T')[0];
          queryParams['where[starts_at]'] = `>=${formattedDate}`;
        }
      }
      
      // Make the API request
      const response = await this.axios.get('/calendar/v2/events', { params: queryParams });
      
      // Validate the response
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response from Planning Center API');
      }
      
      // Process included data if it exists
      if (response.data.included && response.data.included.length > 0) {
        return this.processIncludedData(response.data);
      }
      
      return response.data;
    } catch (error) {
      // Log the error with more details
      console.error('Error fetching Planning Center events:', {
        message: error.message,
        params: JSON.stringify(params),
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      
      // Rethrow the error with a more descriptive message
      throw new Error(`Failed to fetch Planning Center events: ${error.message}`);
    }
  }

  /**
   * Get people
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} List of people with processed included data
   */
  async getPeople(params = {}) {
    try {
      // Ensure pagination parameters are properly set
      const queryParams = { ...params };
    
      // Set default pagination if not provided
      if (!queryParams.per_page) {
        queryParams.per_page = 25; // Default page size
      }
    
      if (!queryParams.page) {
        queryParams.page = 1; // Default to first page
      }
    
      // Make the API request with the prepared parameters
      const response = await this.axios.get('/people/v2/people', { params: queryParams });
    
      // Log pagination information for debugging
      console.log('Planning Center API pagination info:', {
        total_count: response.data.meta?.total_count,
        total_pages: response.data.meta?.total_pages,
        current_page: response.data.meta?.current_page,
        per_page: queryParams.per_page,
        page: queryParams.page
      });
    
      // Process included data if it exists
      if (response.data.included && response.data.included.length > 0) {
        return this.processIncludedData(response.data);
      }
    
      // If no included data, still extract pagination information
      const pagination = this.extractPaginationInfo(response.data.links, response.data.meta);
    
      return {
        ...response.data,
        pagination
      };
    } catch (error) {
      console.error('Error fetching Planning Center people:', error);
      throw error;
    }
  }
  
  /**
   * Process included data and link it to the main data objects
   * @param {Object} responseData - The raw API response data
   * @returns {Object} - The processed response with linked included data
   */
  processIncludedData(responseData) {
    const { data, included, meta, links } = responseData;
  
    // Create a map of included items for quick lookup
    const includedMap = {};
    if (included && included.length > 0) {
      included.forEach(item => {
        const key = `${item.type}:${item.id}`;
        includedMap[key] = item;
      });
    }
  
    // Check if data is an array or a single object
    const isDataArray = Array.isArray(data);
  
    // If data is a single object, wrap it in an array for processing
    const dataArray = isDataArray ? data : [data];
  
    // Process each item to link their relationships with included data
    const processedData = dataArray.map(item => {
      // Create a copy of the item object
      const processedItem = { ...item };
    
      // Ensure attributes exist
      if (!processedItem.attributes) {
        processedItem.attributes = {};
      }
    
      // Ensure first_name and last_name are properly set
      // Handle all falsy values (undefined, null, empty string, etc.), not just undefined
      if (!processedItem.attributes.first_name) {
        processedItem.attributes.first_name = '';
      }

      if (!processedItem.attributes.last_name) {
        processedItem.attributes.last_name = '';
      }

      // Add a full_name attribute for convenience
      // If both first_name and last_name are empty, use "Unknown" as the default name
      const fullName = `${processedItem.attributes.first_name} ${processedItem.attributes.last_name}`.trim();
      processedItem.attributes.full_name = fullName || 'Unknown';
    
      // Process relationships if they exist
      if (item.relationships) {
        // Create a linked_data object to store the processed relationships
        processedItem.linked_data = {};
      
        // Process each relationship
        Object.entries(item.relationships).forEach(([relationshipName, relationship]) => {
          // Handle single relationships
          if (relationship.data && !Array.isArray(relationship.data)) {
            const relatedItemKey = `${relationship.data.type}:${relationship.data.id}`;
            const relatedItem = includedMap[relatedItemKey];
            if (relatedItem) {
              processedItem.linked_data[relationshipName] = relatedItem;
            }
          } 
          // Handle relationship collections
          else if (relationship.data && Array.isArray(relationship.data)) {
            processedItem.linked_data[relationshipName] = relationship.data
              .map(ref => {
                const relatedItemKey = `${ref.type}:${ref.id}`;
                return includedMap[relatedItemKey];
              })
              .filter(item => item !== undefined);
          }
        });
      }
    
      return processedItem;
    });
  
    // Process pagination information if it exists
    const pagination = this.extractPaginationInfo(links, meta);
  
    // Log the first few processed items for debugging
    if (processedData.length > 0) {
      console.log('Sample processed people data:');
      processedData.slice(0, 3).forEach((person, index) => {
        console.log(`Person ${index + 1}: ID=${person.id}, Name=${person.attributes.full_name || 'N/A'}`);
      });
    }
  
    // Return the processed data with the original meta and links, plus pagination info
    // If the original data was a single object, return the first item of the processed array
    return {
      data: isDataArray ? processedData : processedData[0],
      included,
      meta,
      links,
      pagination
    };
  }
  
  /**
   * Extract pagination information from links and meta data
   * @param {Object} links - The links object from the API response
   * @param {Object} meta - The meta object from the API response
   * @returns {Object} - Pagination information
   */
  extractPaginationInfo(links, meta) {
    // Initialize pagination object
    const pagination = {
      currentPage: 1,
      totalPages: 1,
      totalCount: 0,
      hasNextPage: false,
      hasPreviousPage: false,
      nextPageUrl: null,
      previousPageUrl: null,
      firstPageUrl: null,
      lastPageUrl: null
    };
    
    // Extract information from meta if available
    if (meta) {
      if (meta.total_count !== undefined) {
        pagination.totalCount = meta.total_count;
      }
      if (meta.total_pages !== undefined) {
        pagination.totalPages = meta.total_pages;
      }
      if (meta.current_page !== undefined) {
        pagination.currentPage = meta.current_page;
      }
    }
    
    // Extract links if available
    if (links) {
      if (links.self) {
        pagination.currentPageUrl = links.self;
      }
      if (links.next) {
        pagination.nextPageUrl = links.next;
        pagination.hasNextPage = true;
      }
      if (links.prev) {
        pagination.previousPageUrl = links.prev;
        pagination.hasPreviousPage = true;
      }
      if (links.first) {
        pagination.firstPageUrl = links.first;
      }
      if (links.last) {
        pagination.lastPageUrl = links.last;
      }
    }
    
    return pagination;
  }

  /**
   * Get resources
   * @param {Object} params Query parameters
   * @returns {Promise<Object>} List of resources with processed included data
   */
  async getResources(params = {}) {
    try {
      const response = await this.axios.get('/calendar/v2/resources', { params });
      
      // Process included data if it exists
      if (response.data.included && response.data.included.length > 0) {
        return this.processIncludedData(response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching Planning Center resources:', error);
      throw error;
    }
  }
  
  /**
   * Get a single resource by ID
   * @param {string} resourceId - The ID of the resource to retrieve
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Resource data with processed included data
   */
  async getResourceById(resourceId, params = {}) {
    try {
      if (!resourceId) {
        throw new Error('Resource ID is required');
      }
      
      const response = await this.axios.get(`/calendar/v2/resources/${resourceId}`, { params });
      
      // Process included data if it exists
      if (response.data.included && response.data.included.length > 0) {
        return this.processIncludedData(response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching Planning Center resource with ID ${resourceId}:`, error);
      throw error;
    }
  }

  /**
   * Get a single person by ID
   * @param {string} personId - The ID of the person to retrieve
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Person data with processed included data
   */
  async getPersonById(personId, params = {}) {
    try {
      if (!personId) {
        throw new Error('Person ID is required');
      }
      
      // If no include parameter is specified, add default includes for common person data
      const queryParams = { ...params };
      if (!queryParams.include) {
        queryParams.include = 'emails,phone_numbers,addresses,households';
      }
      
      const response = await this.axios.get(`/people/v2/people/${personId}`, { params: queryParams });
      
      // Process included data if it exists
      if (response.data.included && response.data.included.length > 0) {
        return this.processIncludedData(response.data);
      }
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching Planning Center person with ID ${personId}:`, error);
      throw error;
    }
  }
}

module.exports = PlanningCenterAPI;
