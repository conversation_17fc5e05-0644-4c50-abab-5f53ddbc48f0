// Govee API Wrapper
const axios = require('axios');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Govee API Wrapper
 * Provides methods to interact with Govee smart devices
 */
class GoveeAPI {
  constructor() {
    this.integrationName = 'Govee';
    this.apiKey = process.env.GOVEE_API_KEY || '';
    this.baseURL = 'https://developer-api.govee.com/v1';
    
    // Create axios instance with default headers
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Govee-API-Key': this.apiKey,
        'Content-Type': 'application/json'
      }
    });
  }

  /**
   * Initialize the Govee API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if API key is provided
      if (!this.apiKey) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'API key is required for Govee integration.'
        );
        return;
      }

      // Test connection by getting devices
      const devices = await this.getDevices();
      
      if (devices) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName,
          'active',
          { deviceCount: devices.length },
          'Govee integration is active.'
        );
      } else {
        // Update the integration status to error
        integrationTracker.updateStatus(
          this.integrationName,
          'error',
          null,
          'Failed to connect to Govee API.'
        );
      }
    } catch (error) {
      console.error('Error initializing Govee API:', error);
      
      // Update the integration status to error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error initializing Govee API: ${error.message}`
      );
    }
  }

  /**
   * Get all Govee devices
   * @returns {Promise<Array|null>} List of devices or null if error
   */
  async getDevices() {
    try {
      const response = await this.axios.get('/devices');
      return response.data.data.devices;
    } catch (error) {
      console.error('Error getting Govee devices:', error);
      return null;
    }
  }

  /**
   * Get device state
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @returns {Promise<Object|null>} Device state or null if error
   */
  async getDeviceState(device, model) {
    try {
      const response = await this.axios.get(`/devices/state`, {
        params: {
          device,
          model
        }
      });
      return response.data.data;
    } catch (error) {
      console.error('Error getting Govee device state:', error);
      return null;
    }
  }

  /**
   * Control device
   * @param {Object} params - Control parameters
   * @param {string} params.device - Device ID
   * @param {string} params.model - Device model
   * @param {string} params.cmd - Command (turn, brightness, color, colorTem)
   * @param {*} params.value - Command value
   * @returns {Promise<boolean>} Success or failure
   */
  async controlDevice(params) {
    try {
      const response = await this.axios.put('/devices/control', {
        device: params.device,
        model: params.model,
        cmd: {
          name: params.cmd,
          value: params.value
        }
      });
      return response.data.code === 200;
    } catch (error) {
      console.error('Error controlling Govee device:', error);
      return false;
    }
  }

  /**
   * Turn device on or off
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {boolean} on - True to turn on, false to turn off
   * @returns {Promise<boolean>} Success or failure
   */
  async turnDevice(device, model, on) {
    return this.controlDevice({
      device,
      model,
      cmd: 'turn',
      value: on ? 'on' : 'off'
    });
  }

  /**
   * Set device brightness
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {number} brightness - Brightness value (0-100)
   * @returns {Promise<boolean>} Success or failure
   */
  async setBrightness(device, model, brightness) {
    return this.controlDevice({
      device,
      model,
      cmd: 'brightness',
      value: brightness
    });
  }

  /**
   * Set device color
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {Object} color - Color in RGB format
   * @param {number} color.r - Red (0-255)
   * @param {number} color.g - Green (0-255)
   * @param {number} color.b - Blue (0-255)
   * @returns {Promise<boolean>} Success or failure
   */
  async setColor(device, model, color) {
    return this.controlDevice({
      device,
      model,
      cmd: 'color',
      value: color
    });
  }

  /**
   * Set device color temperature
   * @param {string} device - Device ID
   * @param {string} model - Device model
   * @param {number} temperature - Color temperature in Kelvin (2000-9000)
   * @returns {Promise<boolean>} Success or failure
   */
  async setColorTemperature(device, model, temperature) {
    return this.controlDevice({
      device,
      model,
      cmd: 'colorTem',
      value: temperature
    });
  }
}

module.exports = GoveeAPI;