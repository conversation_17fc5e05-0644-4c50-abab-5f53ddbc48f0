// UniFi Protect API Wrapper
const axios = require('axios');
const https = require('https');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * UniFi Protect API Wrapper
 * Documentation: https://github.com/hjdhjd/unifi-protect
 */
class UnifiProtectAPI {
  constructor(instanceId = null) {
    this.instances = {};
    this.integrationName = 'UniFi Protect';
    
    // Initialize instances if environment variables are set
    if (process.env.UNIFI_PROTECT_HOST_A && process.env.UNIFI_PROTECT_API_KEY_A) {
      this.addInstance('A', process.env.UNIFI_PROTECT_HOST_A, process.env.UNIFI_PROTECT_API_KEY_A);
    }
    
    if (process.env.UNIFI_PROTECT_HOST_B && process.env.UNIFI_PROTECT_API_KEY_B) {
      this.addInstance('B', process.env.UNIFI_PROTECT_HOST_B, process.env.UNIFI_PROTECT_API_KEY_B);
    }
    
    // For backward compatibility
    if (process.env.UNIFI_PROTECT_HOST && !instanceId) {
      this.host = process.env.UNIFI_PROTECT_HOST || '';
      this.username = process.env.UNIFI_PROTECT_USERNAME || '';
      this.password = process.env.UNIFI_PROTECT_PASSWORD || '';
      this.port = process.env.UNIFI_PROTECT_PORT || 443;
      this.baseURL = `https://${this.host}:${this.port}/api`;
      this.token = null;
      this.cookies = null;
      
      // Create axios instance with SSL verification disabled (for self-signed certs)
      this.axios = axios.create({
        baseURL: this.baseURL,
        headers: {
          'Content-Type': 'application/json'
        },
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      });
    }
  }
  
  /**
   * Add a new UniFi Protect instance
   * @param {string} id - Instance identifier (e.g., 'A', 'B')
   * @param {string} host - Host or IP address
   * @param {string} apiKey - API key for authentication
   * @param {number} port - Port number (default: 443)
   */
  addInstance(id, host, apiKey, port = 443) {
    const baseURL = `https://${host}:${port}/proxy/protect`;
    
    // Create axios instance for this specific Protect console
    const axiosInstance = axios.create({
      baseURL: baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
    
    this.instances[id] = {
      id,
      host,
      apiKey,
      port,
      baseURL,
      axios: axiosInstance
    };
  }

  /**
   * Initialize the UniFi Protect API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if we have any instances configured
      const instanceIds = Object.keys(this.instances);
      
      // For backward compatibility
      if (instanceIds.length === 0 && this.host) {
        // Check if credentials are provided
        if (!this.host || !this.username || !this.password) {
          // Update the integration status to indicate configuration is needed
          integrationTracker.updateStatus(
            this.integrationName,
            'not_configured',
            null,
            'Host, username, and password are required for UniFi Protect integration.'
          );
          return;
        }

        // Test the connection using legacy authentication
        await this.authenticate();
      } else if (instanceIds.length === 0) {
        // No instances configured
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'No UniFi Protect instances configured. Please set UNIFI_PROTECT_HOST_A/B and UNIFI_PROTECT_API_KEY_A/B environment variables.'
        );
        return;
      } else {
        // Test connection to all instances
        let successCount = 0;
        let errorMessages = [];
        
        for (const id of instanceIds) {
          try {
            // Test the connection by making a simple API call
            await this.getSystemStatus(id);
            successCount++;
          } catch (error) {
            errorMessages.push(`Instance ${id}: ${error.message}`);
          }
        }
        
        if (successCount === 0) {
          // All instances failed
          integrationTracker.updateStatus(
            this.integrationName,
            'error',
            null,
            `Failed to connect to all UniFi Protect instances: ${errorMessages.join('; ')}`
          );
          throw new Error(`Failed to connect to all UniFi Protect instances: ${errorMessages.join('; ')}`);
        }
      }

      // If we get here, at least one connection was successful
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly authenticated and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing UniFi Protect API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Authenticate with UniFi Protect (legacy method for backward compatibility)
   * @returns {Promise<string>} Authentication token
   */
  async authenticate() {
    try {
      const response = await this.axios.post('/auth/login', {
        username: this.username,
        password: this.password
      });

      // Store cookies for future requests
      if (response.headers['set-cookie']) {
        this.cookies = response.headers['set-cookie'];
        this.axios.defaults.headers.common['Cookie'] = this.cookies.join('; ');
      }

      // Store token if available
      if (response.data && response.data.token) {
        this.token = response.data.token;
        this.axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
      }

      return this.token;
    } catch (error) {
      console.error('Error authenticating with UniFi Protect:', error);
      throw error;
    }
  }

  /**
   * Get the appropriate axios instance for the given instance ID
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Object} Axios instance
   * @private
   */
  _getAxiosInstance(instanceId) {
    // If no instanceId is provided or no instances are configured, use the legacy instance
    if (!instanceId || Object.keys(this.instances).length === 0) {
      if (!this.axios) {
        throw new Error('No UniFi Protect instances configured');
      }
      return this.axios;
    }
    
    // Check if the requested instance exists
    if (!this.instances[instanceId]) {
      throw new Error(`UniFi Protect instance '${instanceId}' not found`);
    }
    
    return this.instances[instanceId].axios;
  }

  /**
   * Get all cameras
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Array>} List of cameras
   */
  async getCameras(instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API endpoints
      const endpoints = [
        '/proxy/protect/v1/cameras',
        '/v1/cameras',
        '/cameras'  // Fallback for older versions
      ];
      
      let cameras = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying cameras endpoint: ${endpoint}`);
          const response = await axiosInstance.get(endpoint);
          cameras = response.data;
          console.log(`[UniFi Protect Debug] Successfully fetched cameras from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!cameras) {
        throw new Error('All camera endpoints failed');
      }
      
      // Add instance information to each camera
      if (instanceId && cameras) {
        cameras = cameras.map(camera => ({
          ...camera,
          instanceId
        }));
      }
      
      return cameras;
    } catch (error) {
      console.error(`Error fetching UniFi Protect cameras from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
  
  /**
   * Get all cameras from all instances
   * @returns {Promise<Array>} List of cameras from all instances
   */
  async getAllCameras() {
    const instanceIds = Object.keys(this.instances);
    
    // If no instances are configured, fall back to legacy method
    if (instanceIds.length === 0) {
      return this.getCameras();
    }
    
    // Get cameras from all instances
    const results = await Promise.allSettled(
      instanceIds.map(id => this.getCameras(id))
    );
    
    // Combine results from successful calls
    const cameras = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        cameras.push(...result.value);
      } else {
        console.error(`Error fetching cameras from instance ${instanceIds[index]}:`, result.reason);
      }
    });
    
    return cameras;
  }

  /**
   * Get camera details
   * @param {string} cameraId - Camera ID
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} Camera details
   */
  async getCameraDetails(cameraId, instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API endpoints
      const endpoints = [
        `/proxy/protect/v1/cameras/${cameraId}`,
        `/v1/cameras/${cameraId}`,
        `/cameras/${cameraId}`  // Fallback for older versions
      ];
      
      let cameraDetails = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying camera details endpoint: ${endpoint}`);
          const response = await axiosInstance.get(endpoint);
          cameraDetails = response.data;
          console.log(`[UniFi Protect Debug] Successfully fetched camera details from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!cameraDetails) {
        throw new Error(`Camera ${cameraId} not found in any endpoint`);
      }
      
      // Add instance information
      if (instanceId && cameraDetails) {
        cameraDetails.instanceId = instanceId;
      }
      
      return cameraDetails;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera details for ${cameraId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Get camera snapshot
   * @param {string} cameraId - Camera ID
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Buffer>} Camera snapshot image
   */
  async getCameraSnapshot(cameraId, instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API thumbnail endpoints
      const endpoints = [
        `/proxy/protect/v1/cameras/${cameraId}/snapshot`,
        `/v1/cameras/${cameraId}/snapshot`,
        `/cameras/${cameraId}/snapshot`  // Fallback for older versions
      ];
      
      let snapshot = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying snapshot endpoint: ${endpoint}`);
          const response = await axiosInstance.get(endpoint, {
            responseType: 'arraybuffer'
          });
          snapshot = response.data;
          console.log(`[UniFi Protect Debug] Successfully fetched snapshot from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!snapshot) {
        throw new Error(`Snapshot for camera ${cameraId} not available from any endpoint`);
      }
      
      return snapshot;
    } catch (error) {
      console.error(`Error fetching UniFi Protect camera snapshot for ${cameraId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Get all events
   * @param {Object} params - Query parameters
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Array>} List of events
   */
  async getEvents(params = {}, instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API endpoints for events
      const endpoints = [
        '/proxy/protect/v1/events',
        '/v1/events',
        '/events'  // Fallback for older versions
      ];
      
      let events = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying events endpoint: ${endpoint}`);
          const response = await axiosInstance.get(endpoint, { params });
          events = response.data;
          console.log(`[UniFi Protect Debug] Successfully fetched events from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!events) {
        throw new Error('All event endpoints failed');
      }
      
      // Add instance information to each event
      if (instanceId && events) {
        events = events.map(event => ({
          ...event,
          instanceId
        }));
      }
      
      return events;
    } catch (error) {
      console.error(`Error fetching UniFi Protect events from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
  
  /**
   * Get events from all instances
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} List of events from all instances
   */
  async getAllEvents(params = {}) {
    const instanceIds = Object.keys(this.instances);
    
    // If no instances are configured, fall back to legacy method
    if (instanceIds.length === 0) {
      return this.getEvents(params);
    }
    
    // Get events from all instances
    const results = await Promise.allSettled(
      instanceIds.map(id => this.getEvents(params, id))
    );
    
    // Combine results from successful calls
    const events = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        events.push(...result.value);
      } else {
        console.error(`Error fetching events from instance ${instanceIds[index]}:`, result.reason);
      }
    });
    
    return events;
  }

  /**
   * Get system status
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus(instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API endpoints for system status
      const endpoints = [
        '/proxy/protect/v1/bootstrap',
        '/proxy/protect/v1/meta/info',
        '/v1/bootstrap',
        '/v1/meta/info',
        '/system'  // Fallback for older versions
      ];
      
      let systemStatus = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying system status endpoint: ${endpoint}`);
          const response = await axiosInstance.get(endpoint);
          systemStatus = response.data;
          console.log(`[UniFi Protect Debug] Successfully fetched system status from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!systemStatus) {
        throw new Error('All system status endpoints failed');
      }
      
      // Add instance information
      if (instanceId && systemStatus) {
        systemStatus.instanceId = instanceId;
      }
      
      return systemStatus;
    } catch (error) {
      console.error(`Error fetching UniFi Protect system status from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
  
  /**
   * Get system status from all instances
   * @returns {Promise<Array>} System status from all instances
   */
  async getAllSystemStatus() {
    const instanceIds = Object.keys(this.instances);
    
    // If no instances are configured, fall back to legacy method
    if (instanceIds.length === 0) {
      return [await this.getSystemStatus()];
    }
    
    // Get system status from all instances
    const results = await Promise.allSettled(
      instanceIds.map(id => this.getSystemStatus(id))
    );
    
    // Combine results from successful calls
    const statuses = [];
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        statuses.push({
          ...result.value,
          instanceId: instanceIds[index]
        });
      } else {
        console.error(`Error fetching system status from instance ${instanceIds[index]}:`, result.reason);
      }
    });
    
    return statuses;
  }

  /**
   * Control PTZ camera
   * @param {string} cameraId - Camera ID
   * @param {string} action - PTZ action (goto, patrol_start, patrol_stop, zoom, move)
   * @param {Object} params - Action parameters
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} Response data
   */
  async controlPTZ(cameraId, action, params = {}, instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      let endpoint;
      let method = 'POST';
      
      // Determine the correct endpoint based on action
      switch (action) {
        case 'goto':
          endpoint = `/proxy/protect/v1/cameras/${cameraId}/ptz/goto/${params.slot || 0}`;
          break;
        case 'patrol_start':
          endpoint = `/proxy/protect/v1/cameras/${cameraId}/ptz/patrol/start/${params.slot || 0}`;
          break;
        case 'patrol_stop':
          endpoint = `/proxy/protect/v1/cameras/${cameraId}/ptz/patrol/stop`;
          break;
        case 'move':
          endpoint = `/proxy/protect/v1/cameras/${cameraId}/ptz/move`;
          break;
        case 'zoom':
          endpoint = `/proxy/protect/v1/cameras/${cameraId}/ptz/zoom`;
          break;
        default:
          throw new Error(`Unsupported PTZ action: ${action}`);
      }
      
      console.log(`[UniFi Protect Debug] Controlling PTZ camera ${cameraId} with action ${action}`);
      
      // Try multiple endpoint formats
      const endpoints = [
        endpoint,
        endpoint.replace('/proxy/protect', ''),
        endpoint.replace('/proxy/protect/v1', '/cameras')
      ];
      
      let result = null;
      for (const ep of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying PTZ endpoint: ${ep}`);
          const response = await axiosInstance[method.toLowerCase()](ep, params.data || {});
          result = response.data;
          console.log(`[UniFi Protect Debug] Successfully controlled PTZ from endpoint: ${ep}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] PTZ endpoint ${ep} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (result === null && endpoints.length > 0) {
        // For endpoints that return 204 No Content, this is success
        result = { success: true };
      }
      
      return result;
    } catch (error) {
      console.error(`Error controlling UniFi Protect PTZ camera ${cameraId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Get all viewers
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Array>} List of viewers
   */
  async getViewers(instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API endpoints for viewers
      const endpoints = [
        '/proxy/protect/v1/viewers',
        '/v1/viewers',
        '/viewers'  // Fallback for older versions
      ];
      
      let viewers = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying viewers endpoint: ${endpoint}`);
          const response = await axiosInstance.get(endpoint);
          viewers = response.data;
          console.log(`[UniFi Protect Debug] Successfully fetched viewers from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!viewers) {
        throw new Error('All viewer endpoints failed');
      }
      
      // Add instance information to each viewer
      if (instanceId && viewers) {
        viewers = viewers.map(viewer => ({
          ...viewer,
          instanceId
        }));
      }
      
      return viewers;
    } catch (error) {
      console.error(`Error fetching UniFi Protect viewers from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }

  /**
   * Update viewer settings
   * @param {string} viewerId - Viewer ID
   * @param {Object} settings - Viewer settings to update
   * @param {string} instanceId - Instance identifier (e.g., 'A', 'B')
   * @returns {Promise<Object>} Updated viewer data
   */
  async updateViewer(viewerId, settings, instanceId = null) {
    try {
      // For backward compatibility
      if (!instanceId && !Object.keys(this.instances).length && (!this.cookies && !this.token)) {
        await this.authenticate();
      }

      const axiosInstance = this._getAxiosInstance(instanceId);
      
      // Try official UniFi Protect API endpoints for viewer updates
      const endpoints = [
        `/proxy/protect/v1/viewers/${viewerId}`,
        `/v1/viewers/${viewerId}`,
        `/viewers/${viewerId}`  // Fallback for older versions
      ];
      
      let updatedViewer = null;
      for (const endpoint of endpoints) {
        try {
          console.log(`[UniFi Protect Debug] Trying viewer update endpoint: ${endpoint}`);
          const response = await axiosInstance.patch(endpoint, settings);
          updatedViewer = response.data;
          console.log(`[UniFi Protect Debug] Successfully updated viewer from endpoint: ${endpoint}`);
          break;
        } catch (err) {
          console.log(`[UniFi Protect Debug] Endpoint ${endpoint} failed: ${err.message}`);
          if (err.response && err.response.status !== 404) {
            throw err;
          }
        }
      }
      
      if (!updatedViewer) {
        throw new Error(`Viewer ${viewerId} update failed on all endpoints`);
      }
      
      // Add instance information
      if (instanceId && updatedViewer) {
        updatedViewer.instanceId = instanceId;
      }
      
      return updatedViewer;
    } catch (error) {
      console.error(`Error updating UniFi Protect viewer ${viewerId} from instance ${instanceId || 'default'}:`, error);
      throw error;
    }
  }
}

module.exports = UnifiProtectAPI;
