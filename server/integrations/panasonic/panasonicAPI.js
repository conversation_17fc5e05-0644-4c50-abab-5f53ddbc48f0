// Panasonic Pro AV Camera API Wrapper
const axios = require('axios');
const https = require('https');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * Panasonic Pro AV Camera API Wrapper
 * Specifically designed for the AW-HE40 model
 */
class PanasonicAPI {
  constructor() {
    this.integrationName = 'Panasonic Pro AV Camera';
    this.host = process.env.PANASONIC_HOST || '';
    this.username = process.env.PANASONIC_USERNAME || '';
    this.password = process.env.PANASONIC_PASSWORD || '';
    this.port = process.env.PANASONIC_PORT || 80;
    this.model = process.env.PANASONIC_MODEL || 'AW-HE40';
    this.protocol = process.env.PANASONIC_PROTOCOL || 'http';
    this.baseURL = `${this.protocol}://${this.host}:${this.port}/cgi-bin`;
    
    // Create axios instance with SSL verification disabled (for self-signed certs)
    this.axios = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      auth: {
        username: this.username,
        password: this.password
      },
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    });
  }

  /**
   * Initialize the Panasonic Camera API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if credentials are provided
      if (!this.host || !this.username || !this.password) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Host, username, and password are required for Panasonic Pro AV Camera integration.'
        );
        return;
      }

      // Test connection by getting camera information
      const cameraInfo = await this.getCameraInfo();
      
      if (cameraInfo) {
        // Update the integration status to active
        integrationTracker.updateStatus(
          this.integrationName,
          'active',
          cameraInfo,
          'Panasonic Pro AV Camera integration is active.'
        );
      } else {
        // Update the integration status to error
        integrationTracker.updateStatus(
          this.integrationName,
          'error',
          null,
          'Failed to connect to Panasonic Pro AV Camera.'
        );
      }
    } catch (error) {
      console.error('Error initializing Panasonic Pro AV Camera API:', error);
      
      // Update the integration status to error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error initializing Panasonic Pro AV Camera API: ${error.message}`
      );
    }
  }

  /**
   * Get camera information
   * @returns {Promise<Object|null>} Camera information or null if error
   */
  async getCameraInfo() {
    try {
      const response = await this.axios.get('/get_information.cgi');
      return this.parseResponse(response.data);
    } catch (error) {
      console.error('Error getting camera information:', error);
      return null;
    }
  }

  /**
   * Get camera status
   * @returns {Promise<Object|null>} Camera status or null if error
   */
  async getCameraStatus() {
    try {
      const response = await this.axios.get('/get_status.cgi');
      return this.parseResponse(response.data);
    } catch (error) {
      console.error('Error getting camera status:', error);
      return null;
    }
  }

  /**
   * Get camera preset list
   * @returns {Promise<Array|null>} Camera presets or null if error
   */
  async getCameraPresets() {
    try {
      const response = await this.axios.get('/preset.cgi?cmd=get');
      return this.parseResponse(response.data);
    } catch (error) {
      console.error('Error getting camera presets:', error);
      return null;
    }
  }

  /**
   * Move camera to preset position
   * @param {number} presetId - Preset ID to move to
   * @returns {Promise<boolean>} Success or failure
   */
  async moveToPreset(presetId) {
    try {
      const response = await this.axios.get(`/preset.cgi?cmd=move&id=${presetId}`);
      return response.status === 200;
    } catch (error) {
      console.error('Error moving to preset:', error);
      return false;
    }
  }

  /**
   * Control camera PTZ (Pan, Tilt, Zoom)
   * @param {string} direction - Direction to move (up, down, left, right, upleft, upright, downleft, downright)
   * @param {number} speed - Speed of movement (1-100)
   * @returns {Promise<boolean>} Success or failure
   */
  async controlPTZ(direction, speed = 50) {
    try {
      const directionMap = {
        up: 'up',
        down: 'down',
        left: 'left',
        right: 'right',
        upleft: 'upleft',
        upright: 'upright',
        downleft: 'downleft',
        downright: 'downright',
        stop: 'stop'
      };

      const dir = directionMap[direction.toLowerCase()] || 'stop';
      const response = await this.axios.get(`/ptz.cgi?cmd=${dir}&speed=${speed}`);
      return response.status === 200;
    } catch (error) {
      console.error('Error controlling PTZ:', error);
      return false;
    }
  }

  /**
   * Control camera zoom
   * @param {string} direction - Direction to zoom (tele, wide, stop)
   * @param {number} speed - Speed of zoom (1-100)
   * @returns {Promise<boolean>} Success or failure
   */
  async controlZoom(direction, speed = 50) {
    try {
      const directionMap = {
        tele: 'tele',
        wide: 'wide',
        stop: 'stop'
      };

      const dir = directionMap[direction.toLowerCase()] || 'stop';
      const response = await this.axios.get(`/zoom.cgi?cmd=${dir}&speed=${speed}`);
      return response.status === 200;
    } catch (error) {
      console.error('Error controlling zoom:', error);
      return false;
    }
  }

  /**
   * Get camera snapshot
   * @returns {Promise<Buffer|null>} Image buffer or null if error
   */
  async getSnapshot() {
    try {
      const response = await this.axios.get('/snapshot.cgi', {
        responseType: 'arraybuffer'
      });
      return response.data;
    } catch (error) {
      console.error('Error getting snapshot:', error);
      return null;
    }
  }

  /**
   * Parse response from camera
   * @param {string} data - Response data
   * @returns {Object} Parsed response
   */
  parseResponse(data) {
    if (typeof data !== 'string') {
      return data;
    }

    try {
      // Some Panasonic cameras return data in a custom format
      // Convert to a more usable object
      const lines = data.split('\n');
      const result = {};

      lines.forEach(line => {
        const parts = line.split('=');
        if (parts.length === 2) {
          const key = parts[0].trim();
          const value = parts[1].trim();
          result[key] = value;
        }
      });

      return result;
    } catch (error) {
      console.error('Error parsing response:', error);
      return data;
    }
  }
}

module.exports = PanasonicAPI;