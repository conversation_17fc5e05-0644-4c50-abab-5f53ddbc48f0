// UniFi Network API Wrapper using @panoptic-it-solutions/unifi-api-client
const { UniFiClient } = require('@panoptic-it-solutions/unifi-api-client');
const integrationTracker = require('../../utils/integrationTracker');

/**
 * UniFi Network API Wrapper
 * Uses @panoptic-it-solutions/unifi-api-client for comprehensive UniFi Network controller integration
 */
class UnifiNetworkAPI {
  constructor(host, apiKey, port = 443, site = 'default') {
    this.host = host;
    this.apiKey = apiKey; // Kept for backward compatibility
    this.username = process.env.UNIFI_NETWORK_USERNAME || 'admin'; // Default username
    this.password = process.env.UNIFI_NETWORK_PASSWORD || apiKey; // Use apiKey as password if no password is provided
    this.port = port;
    // Prefer site ID from environment if provided
    this.site = process.env.UNIFI_NETWORK_SITEID || site;
    this.integrationName = 'UniFi Network';
    this.client = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the UniFi Network API client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Check if we need to initialize the integration based on the tracker
      const needsCheck = integrationTracker.needsCheck(this.integrationName);

      // If we don't need to check this integration again, log and return early
      if (!needsCheck) {
        console.log(`Skipping ${this.integrationName} initialization - last checked recently`);
        const status = integrationTracker.getStatus(this.integrationName);
        if (status && status.status === 'active') {
          return;
        }
      }

      // Check if credentials are provided
      if (!this.host || (!this.apiKey && !this.password)) {
        // Update the integration status to indicate configuration is needed
        integrationTracker.updateStatus(
          this.integrationName,
          'not_configured',
          null,
          'Host and either API key or username/password are required for UniFi Network integration.'
        );
        return;
      }

      // Initialize the UniFi client
      const controllerUrl = `https://${this.host}:${this.port}`;
      console.log(`[UniFi Network Debug] Initializing client with URL: ${controllerUrl}`);
      
      this.client = new UniFiClient(controllerUrl, {
        username: this.username,
        password: this.password,
        site: this.site,
        strictSSL: false, // Disable SSL verification for self-signed certs
        timeout: 10000 // 10 second timeout
      });

      // Test the connection by logging in
      await this.testConnection();

      // If we get here, the connection was successful
      this.isInitialized = true;
      integrationTracker.updateStatus(
        this.integrationName,
        'active',
        new Date(),
        'Integration is properly authenticated and ready to use.'
      );
    } catch (error) {
      console.error('Error initializing UniFi Network API:', error);

      // Update the integration status to indicate an error
      integrationTracker.updateStatus(
        this.integrationName,
        'error',
        null,
        `Error: ${error.message}`
      );

      throw error;
    }
  }

  /**
   * Test connection with UniFi Network Controller by logging in
   * @returns {Promise<boolean>} True if connection is successful
   */
  async testConnection() {
    console.log(`[UniFi Network Debug] Testing connection to UniFi Network Controller at ${this.host}:${this.port}`);
    
    try {
      // Attempt to login
      const loginResult = await this.client.login();
      console.log(`[UniFi Network Debug] Login successful: ${loginResult}`);
      
      // Get sites to verify connection
      const sites = await this.client.sites.listSites();
      console.log(`[UniFi Network Debug] Connection test successful. Found ${sites.length} sites.`);
      
      return true;
    } catch (error) {
      this._logDetailedError('Connection test', 'login', 'POST', error);
      console.error('[UniFi Network Debug] Connection test failed');
      console.error('Error testing connection with UniFi Network Controller:', error);
      throw new Error(`Failed to connect to UniFi Network Controller at ${this.host}:${this.port}. Error: ${error.message}`);
    }
  }
  
  /**
   * Helper method to log detailed error information
   * @param {string} operation - The operation being performed
   * @param {string} endpoint - The API endpoint that was called
   * @param {string} method - The HTTP method used
   * @param {Error} error - The error object
   * @private
   */
  _logDetailedError(operation, endpoint, method, error) {
    console.error(`[UniFi Network Debug] Error during ${operation}:`);
    console.error(`[UniFi Network Debug] - Request: ${method} ${endpoint}`);
    
    if (error.response) {
      console.error(`[UniFi Network Debug] - Status: ${error.response.status} ${error.response.statusText}`);
      
      // Special handling for 404 errors
      if (error.response.status === 404) {
        console.error(`[UniFi Network Debug] - 404 Not Found: The endpoint ${endpoint} does not exist or is not accessible`);
        console.error(`[UniFi Network Debug] - This may indicate an incompatible UniFi Network controller version or incorrect site configuration`);
      }
      
      if (error.response.data) {
        console.error(`[UniFi Network Debug] - Response data:`, error.response.data);
      }
    } else {
      console.error(`[UniFi Network Debug] - Error: ${error.message}`);
    }
    
    // Log the error stack for debugging
    console.error(`[UniFi Network Debug] - Error stack:`, error.stack);
  }
  
  /**
   * Ensure the client is initialized before making API calls
   * @private
   */
  async _ensureInitialized() {
    if (!this.client || !this.isInitialized) {
      console.log('[UniFi Network Debug] Client not initialized, initializing now');
      await this.initialize();
    }
    
    // Ensure we're logged in
    if (!this.client.isLoggedIn()) {
      console.log('[UniFi Network Debug] Not logged in, logging in now');
      await this.client.login();
    }
  }

  /**
   * Get all devices
   * @param {Object} options - Filter and pagination options
   * @param {string} options.filter - Filter expression following the format described in the API documentation
   * @param {number} options.offset - Number of items to skip (for pagination)
   * @param {number} options.limit - Maximum number of items to return (for pagination)
   * @returns {Promise<Object>} Object containing devices list and pagination information
   */
  async getDevices(options = {}) {
    try {
      console.log(`[UniFi Network Debug] Fetching all devices for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get all devices using the client
      const devices = await this.client.devices.listDevices();
      console.log(`[UniFi Network Debug] Successfully fetched ${devices.length} devices`);
      
      // Apply filtering if needed
      let filteredDevices = devices;
      if (options.filter) {
        console.log(`[UniFi Network Debug] Applying filter: ${options.filter}`);
        // Simple filtering implementation - can be enhanced based on filter syntax
        try {
          const filterParts = options.filter.split('=');
          if (filterParts.length === 2) {
            const [key, value] = filterParts;
            filteredDevices = devices.filter(device => {
              return device[key.trim()] && device[key.trim()].toString() === value.trim();
            });
          }
        } catch (filterError) {
          console.error(`[UniFi Network Debug] Error applying filter: ${filterError.message}`);
        }
      }
      
      // Apply pagination
      const offset = options.offset !== undefined ? options.offset : 0;
      const limit = options.limit !== undefined ? options.limit : 25;
      
      const paginatedDevices = filteredDevices.slice(offset, offset + limit);
      
      // Return devices with pagination information
      return {
        data: paginatedDevices,
        offset: offset,
        limit: limit,
        count: paginatedDevices.length,
        totalCount: filteredDevices.length
      };
    } catch (error) {
      this._logDetailedError('Get devices', 'devices.listDevices', 'GET', error);
      console.error('Error fetching UniFi Network devices:', error);
      throw error;
    }
  }

  /**
   * Get device details
   * @param {string} deviceId Device ID (MAC address)
   * @returns {Promise<Object>} Device details
   */
  async getDeviceDetails(deviceId) {
    try {
      console.log(`[UniFi Network Debug] Fetching device details for device ID: ${deviceId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get device details using the client
      const device = await this.client.devices.getDevice(deviceId);
      
      if (!device) {
        console.error(`[UniFi Network Debug] Device with ID ${deviceId} not found`);
        throw new Error(`Device with ID ${deviceId} not found`);
      }
      
      console.log(`[UniFi Network Debug] Successfully fetched details for device ID: ${deviceId}`);
      return device;
    } catch (error) {
      this._logDetailedError('Get device details', `devices.getDevice(${deviceId})`, 'GET', error);
      console.error(`Error fetching UniFi Network device details for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get all clients
   * @param {Object} options - Filter and pagination options
   * @param {string} options.filter - Filter expression following the format described in the API documentation
   * @param {number} options.offset - Number of items to skip (for pagination)
   * @param {number} options.limit - Maximum number of items to return (for pagination)
   * @returns {Promise<Object>} Object containing clients list and pagination information
   */
  async getClients(options = {}) {
    try {
      console.log(`[UniFi Network Debug] Fetching all clients for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get all clients using the client
      // By default, get all clients (active and historical)
      const clients = await this.client.getAllClients({
        // The package may support pagination directly, but we'll handle it ourselves for consistency
      });
      
      console.log(`[UniFi Network Debug] Successfully fetched ${clients.length} clients`);
      
      // Apply filtering if needed
      let filteredClients = clients;
      if (options.filter) {
        console.log(`[UniFi Network Debug] Applying filter: ${options.filter}`);
        // Simple filtering implementation - can be enhanced based on filter syntax
        try {
          const filterParts = options.filter.split('=');
          if (filterParts.length === 2) {
            const [key, value] = filterParts;
            filteredClients = clients.filter(client => {
              return client[key.trim()] && client[key.trim()].toString() === value.trim();
            });
          }
        } catch (filterError) {
          console.error(`[UniFi Network Debug] Error applying filter: ${filterError.message}`);
        }
      }
      
      // Apply pagination
      const offset = options.offset !== undefined ? options.offset : 0;
      const limit = options.limit !== undefined ? options.limit : 25;
      
      const paginatedClients = filteredClients.slice(offset, offset + limit);
      
      // Return clients with pagination information
      return {
        data: paginatedClients,
        offset: offset,
        limit: limit,
        count: paginatedClients.length,
        totalCount: filteredClients.length
      };
    } catch (error) {
      this._logDetailedError('Get clients', 'getAllClients', 'GET', error);
      console.error('Error fetching UniFi Network clients:', error);
      throw error;
    }
  }

  /**
   * Get client details
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Client details
   */
  async getClientDetails(clientId) {
    try {
      console.log(`[UniFi Network Debug] Fetching client details for client ID: ${clientId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get client details using the client
      const client = await this.client.getClientDetails({ mac: clientId });
      
      if (!client) {
        console.error(`[UniFi Network Debug] Client with ID ${clientId} not found`);
        throw new Error(`Client with ID ${clientId} not found`);
      }
      
      console.log(`[UniFi Network Debug] Successfully fetched details for client ID: ${clientId}`);
      return client;
    } catch (error) {
      this._logDetailedError('Get client details', `getClientDetails({mac: ${clientId}})`, 'GET', error);
      console.error(`Error fetching UniFi Network client details for ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Block a client
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Response data
   */
  async blockClient(clientId) {
    try {
      console.log(`[UniFi Network Debug] Blocking client with ID: ${clientId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Block client using the client
      const result = await this.client.blockClient(clientId);
      
      console.log(`[UniFi Network Debug] Successfully blocked client with ID: ${clientId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Block client', `blockClient(${clientId})`, 'POST', error);
      console.error(`Error blocking UniFi Network client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Unblock a client
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Response data
   */
  async unblockClient(clientId) {
    try {
      console.log(`[UniFi Network Debug] Unblocking client with ID: ${clientId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Unblock client using the client
      const result = await this.client.unblockClient(clientId);
      
      console.log(`[UniFi Network Debug] Successfully unblocked client with ID: ${clientId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Unblock client', `unblockClient(${clientId})`, 'POST', error);
      console.error(`Error unblocking UniFi Network client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Get device statistics
   * @param {string} deviceId Device ID (MAC address)
   * @returns {Promise<Object>} Device statistics
   */
  async getDeviceStatistics(deviceId) {
    try {
      console.log(`[UniFi Network Debug] Fetching device statistics for device ID: ${deviceId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get device details first
      const device = await this.getDeviceDetails(deviceId);
      
      try {
        // Try to get device stats using the client
        // The package doesn't have a direct method for device statistics, so we'll use the device details
        // and enhance it with any available stats
        
        // Get site stats to see if we can find device-specific stats
        const siteStats = await this.client.getSiteStats();
        const deviceStats = await this.client.getDeviceStats();
        
        // Find stats for this specific device
        const deviceStat = deviceStats.find(stat => stat.mac === deviceId);
        
        // Combine device details with stats
        return {
          ...device,
          uptime: device.uptime,
          status: device.state,
          last_seen: device.last_seen,
          tx_bytes: device.tx_bytes,
          rx_bytes: device.rx_bytes,
          stats: deviceStat || device.stat
        };
      } catch (error) {
        // If stats endpoint fails, return basic stats from device details
        console.log(`[UniFi Network Debug] Error fetching device statistics, falling back to basic stats from device details: ${error.message}`);
        return {
          uptime: device.uptime,
          status: device.state,
          last_seen: device.last_seen,
          tx_bytes: device.tx_bytes,
          rx_bytes: device.rx_bytes,
          stats: device.stat
        };
      }
    } catch (error) {
      this._logDetailedError('Get device statistics', `getDeviceStats()`, 'GET', error);
      console.error(`Error fetching UniFi Network device statistics for ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Reconnect (kick) a client
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Response data
   */
  async reconnectClient(clientId) {
    try {
      console.log(`[UniFi Network Debug] Reconnecting client with ID: ${clientId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Reconnect client using the client
      const result = await this.client.reconnectClient(clientId);
      
      console.log(`[UniFi Network Debug] Successfully reconnected client with ID: ${clientId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Reconnect client', `reconnectClient(${clientId})`, 'POST', error);
      console.error(`Error reconnecting UniFi Network client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Authorize a guest client
   * @param {string} clientId Client ID (MAC address)
   * @param {number} minutes Number of minutes to authorize (default: 60)
   * @returns {Promise<Object>} Response data
   */
  async authorizeGuest(clientId, minutes = 60) {
    try {
      console.log(`[UniFi Network Debug] Authorizing guest client with ID: ${clientId} for ${minutes} minutes, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Authorize guest using the client
      const result = await this.client.authorizeGuest(clientId, minutes);
      
      console.log(`[UniFi Network Debug] Successfully authorized guest client with ID: ${clientId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Authorize guest', `authorizeGuest(${clientId}, ${minutes})`, 'POST', error);
      console.error(`Error authorizing UniFi Network guest client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Unauthorize a guest client
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Response data
   */
  async unauthorizeGuest(clientId) {
    try {
      console.log(`[UniFi Network Debug] Unauthorizing guest client with ID: ${clientId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Unauthorize guest using the client
      const result = await this.client.unauthorizeGuest(clientId);
      
      console.log(`[UniFi Network Debug] Successfully unauthorized guest client with ID: ${clientId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Unauthorize guest', `unauthorizeGuest(${clientId})`, 'POST', error);
      console.error(`Error unauthorizing UniFi Network guest client ${clientId}:`, error);
      throw error;
    }
  }

  /**
   * Get client statistics
   * @param {string} clientId Client ID (MAC address)
   * @returns {Promise<Object>} Client statistics
   */
  async getClientStatistics(clientId) {
    try {
      console.log(`[UniFi Network Debug] Fetching client statistics for client ID: ${clientId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get client stats using the client
      const stats = await this.client.getClientStats(clientId);
      
      console.log(`[UniFi Network Debug] Successfully fetched statistics for client ID: ${clientId}`);
      return stats || {};
    } catch (error) {
      this._logDetailedError('Get client statistics', `getClientStats(${clientId})`, 'GET', error);
      console.error(`Error fetching UniFi Network client statistics for ${clientId}:`, error);
      throw error;
    }
  }

  // ===== Network Management Methods =====

  /**
   * Get all networks
   * @returns {Promise<Array>} Array of networks
   */
  async getNetworks() {
    try {
      console.log(`[UniFi Network Debug] Fetching all networks for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get networks using the client
      const networks = await this.client.getNetworks();
      
      console.log(`[UniFi Network Debug] Successfully fetched ${networks.length} networks`);
      return networks;
    } catch (error) {
      this._logDetailedError('Get networks', 'getNetworks()', 'GET', error);
      console.error('Error fetching UniFi Network networks:', error);
      throw error;
    }
  }

  /**
   * Create a new network
   * @param {Object} networkData Network configuration data
   * @returns {Promise<Object>} Created network
   */
  async createNetwork(networkData) {
    try {
      console.log(`[UniFi Network Debug] Creating new network with name: ${networkData.name}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Create network using the client
      const network = await this.client.createNetwork(networkData);
      
      console.log(`[UniFi Network Debug] Successfully created network with name: ${networkData.name}`);
      return network;
    } catch (error) {
      this._logDetailedError('Create network', 'createNetwork()', 'POST', error);
      console.error('Error creating UniFi Network network:', error);
      throw error;
    }
  }

  /**
   * Update an existing network
   * @param {string} networkId Network ID
   * @param {Object} networkData Network configuration data
   * @returns {Promise<Object>} Updated network
   */
  async updateNetwork(networkId, networkData) {
    try {
      console.log(`[UniFi Network Debug] Updating network with ID: ${networkId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Update network using the client
      const network = await this.client.updateNetwork(networkId, networkData);
      
      console.log(`[UniFi Network Debug] Successfully updated network with ID: ${networkId}`);
      return network;
    } catch (error) {
      this._logDetailedError('Update network', `updateNetwork(${networkId})`, 'PUT', error);
      console.error(`Error updating UniFi Network network ${networkId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a network
   * @param {string} networkId Network ID
   * @returns {Promise<boolean>} True if successful
   */
  async deleteNetwork(networkId) {
    try {
      console.log(`[UniFi Network Debug] Deleting network with ID: ${networkId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Delete network using the client
      const result = await this.client.deleteNetwork(networkId);
      
      console.log(`[UniFi Network Debug] Successfully deleted network with ID: ${networkId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Delete network', `deleteNetwork(${networkId})`, 'DELETE', error);
      console.error(`Error deleting UniFi Network network ${networkId}:`, error);
      throw error;
    }
  }

  // ===== Wireless Management Methods =====

  /**
   * Get all wireless networks (WLANs)
   * @returns {Promise<Array>} Array of WLANs
   */
  async getWirelessNetworks() {
    try {
      console.log(`[UniFi Network Debug] Fetching all wireless networks for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get WLANs using the client
      const wlans = await this.client.getWlanConfigs();
      
      console.log(`[UniFi Network Debug] Successfully fetched ${wlans.length} wireless networks`);
      return wlans;
    } catch (error) {
      this._logDetailedError('Get wireless networks', 'getWlanConfigs()', 'GET', error);
      console.error('Error fetching UniFi Network wireless networks:', error);
      throw error;
    }
  }

  /**
   * Create a new wireless network (WLAN)
   * @param {Object} wlanData WLAN configuration data
   * @returns {Promise<Object>} Created WLAN
   */
  async createWirelessNetwork(wlanData) {
    try {
      console.log(`[UniFi Network Debug] Creating new wireless network with name: ${wlanData.name}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Create WLAN using the client
      const wlan = await this.client.createWlanConfig(wlanData);
      
      console.log(`[UniFi Network Debug] Successfully created wireless network with name: ${wlanData.name}`);
      return wlan;
    } catch (error) {
      this._logDetailedError('Create wireless network', 'createWlanConfig()', 'POST', error);
      console.error('Error creating UniFi Network wireless network:', error);
      throw error;
    }
  }

  /**
   * Update an existing wireless network (WLAN)
   * @param {string} wlanId WLAN ID
   * @param {Object} wlanData WLAN configuration data
   * @returns {Promise<Object>} Updated WLAN
   */
  async updateWirelessNetwork(wlanId, wlanData) {
    try {
      console.log(`[UniFi Network Debug] Updating wireless network with ID: ${wlanId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Update WLAN using the client
      const wlan = await this.client.updateWlanConfig(wlanId, wlanData);
      
      console.log(`[UniFi Network Debug] Successfully updated wireless network with ID: ${wlanId}`);
      return wlan;
    } catch (error) {
      this._logDetailedError('Update wireless network', `updateWlanConfig(${wlanId})`, 'PUT', error);
      console.error(`Error updating UniFi Network wireless network ${wlanId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a wireless network (WLAN)
   * @param {string} wlanId WLAN ID
   * @returns {Promise<boolean>} True if successful
   */
  async deleteWirelessNetwork(wlanId) {
    try {
      console.log(`[UniFi Network Debug] Deleting wireless network with ID: ${wlanId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Delete WLAN using the client
      const result = await this.client.deleteWlanConfig(wlanId);
      
      console.log(`[UniFi Network Debug] Successfully deleted wireless network with ID: ${wlanId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Delete wireless network', `deleteWlanConfig(${wlanId})`, 'DELETE', error);
      console.error(`Error deleting UniFi Network wireless network ${wlanId}:`, error);
      throw error;
    }
  }

  // ===== System Management Methods =====

  /**
   * Get system status
   * @returns {Promise<Object>} System status
   */
  async getSystemStatus() {
    try {
      console.log(`[UniFi Network Debug] Fetching system status for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get system status using the client
      const status = await this.client.getSystemStatus();
      
      console.log(`[UniFi Network Debug] Successfully fetched system status`);
      return status;
    } catch (error) {
      this._logDetailedError('Get system status', 'getSystemStatus()', 'GET', error);
      console.error('Error fetching UniFi Network system status:', error);
      throw error;
    }
  }

  /**
   * Get system settings
   * @returns {Promise<Object>} System settings
   */
  async getSystemSettings() {
    try {
      console.log(`[UniFi Network Debug] Fetching system settings for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get system settings using the client
      const settings = await this.client.getSystemSettings();
      
      console.log(`[UniFi Network Debug] Successfully fetched system settings`);
      return settings;
    } catch (error) {
      this._logDetailedError('Get system settings', 'getSystemSettings()', 'GET', error);
      console.error('Error fetching UniFi Network system settings:', error);
      throw error;
    }
  }

  /**
   * Update system settings
   * @param {Object} settingsData System settings data
   * @returns {Promise<Object>} Updated system settings
   */
  async updateSystemSettings(settingsData) {
    try {
      console.log(`[UniFi Network Debug] Updating system settings for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Update system settings using the client
      const settings = await this.client.updateSystemSettings(settingsData);
      
      console.log(`[UniFi Network Debug] Successfully updated system settings`);
      return settings;
    } catch (error) {
      this._logDetailedError('Update system settings', 'updateSystemSettings()', 'PUT', error);
      console.error('Error updating UniFi Network system settings:', error);
      throw error;
    }
  }

  /**
   * Get system logs
   * @param {number} limit Maximum number of logs to return
   * @returns {Promise<Array>} Array of log entries
   */
  async getSystemLogs(limit = 100) {
    try {
      console.log(`[UniFi Network Debug] Fetching system logs for site: ${this.site}, limit: ${limit}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get system logs using the client
      const logs = await this.client.getSystemLogs(limit);
      
      console.log(`[UniFi Network Debug] Successfully fetched ${logs.length} system logs`);
      return logs;
    } catch (error) {
      this._logDetailedError('Get system logs', `getSystemLogs(${limit})`, 'GET', error);
      console.error('Error fetching UniFi Network system logs:', error);
      throw error;
    }
  }

  /**
   * Get system alerts
   * @returns {Promise<Array>} Array of alerts
   */
  async getSystemAlerts() {
    try {
      console.log(`[UniFi Network Debug] Fetching system alerts for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get system alerts using the client
      const alerts = await this.client.getSystemAlerts();
      
      console.log(`[UniFi Network Debug] Successfully fetched ${alerts.length} system alerts`);
      return alerts;
    } catch (error) {
      this._logDetailedError('Get system alerts', 'getSystemAlerts()', 'GET', error);
      console.error('Error fetching UniFi Network system alerts:', error);
      throw error;
    }
  }

  /**
   * Reboot a device
   * @param {string} deviceId Device ID (MAC address)
   * @returns {Promise<boolean>} True if successful
   */
  async rebootDevice(deviceId) {
    try {
      console.log(`[UniFi Network Debug] Rebooting device with ID: ${deviceId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Reboot device using the client
      const result = await this.client.devices.restartDevice(deviceId);
      
      console.log(`[UniFi Network Debug] Successfully rebooted device with ID: ${deviceId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Reboot device', `devices.restartDevice(${deviceId})`, 'POST', error);
      console.error(`Error rebooting UniFi Network device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Adopt a device
   * @param {string} deviceId Device ID (MAC address)
   * @returns {Promise<boolean>} True if successful
   */
  async adoptDevice(deviceId) {
    try {
      console.log(`[UniFi Network Debug] Adopting device with ID: ${deviceId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Adopt device using the client
      const result = await this.client.devices.adoptDevice(deviceId);
      
      console.log(`[UniFi Network Debug] Successfully adopted device with ID: ${deviceId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Adopt device', `devices.adoptDevice(${deviceId})`, 'POST', error);
      console.error(`Error adopting UniFi Network device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Set device name
   * @param {string} deviceId Device ID (MAC address)
   * @param {string} name New device name
   * @returns {Promise<boolean>} True if successful
   */
  async setDeviceName(deviceId, name) {
    try {
      console.log(`[UniFi Network Debug] Setting name for device with ID: ${deviceId} to ${name}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Set device name using the client
      const result = await this.client.devices.setDeviceName(deviceId, name);
      
      console.log(`[UniFi Network Debug] Successfully set name for device with ID: ${deviceId} to ${name}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Set device name', `devices.setDeviceName(${deviceId}, ${name})`, 'POST', error);
      console.error(`Error setting name for UniFi Network device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Upgrade device firmware
   * @param {string} deviceId Device ID (MAC address)
   * @returns {Promise<boolean>} True if successful
   */
  async upgradeDeviceFirmware(deviceId) {
    try {
      console.log(`[UniFi Network Debug] Upgrading firmware for device with ID: ${deviceId}, site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Upgrade device firmware using the client
      const result = await this.client.devices.upgradeDevice(deviceId);
      
      console.log(`[UniFi Network Debug] Successfully initiated firmware upgrade for device with ID: ${deviceId}`);
      return { success: result };
    } catch (error) {
      this._logDetailedError('Upgrade device firmware', `devices.upgradeDevice(${deviceId})`, 'POST', error);
      console.error(`Error upgrading firmware for UniFi Network device ${deviceId}:`, error);
      throw error;
    }
  }

  /**
   * Get site statistics
   * @returns {Promise<Object>} Site statistics
   */
  async getSiteStatistics() {
    try {
      console.log(`[UniFi Network Debug] Fetching site statistics for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get site stats using the client
      const stats = await this.client.getSiteStats();
      
      console.log(`[UniFi Network Debug] Successfully fetched site statistics`);
      return stats;
    } catch (error) {
      this._logDetailedError('Get site statistics', 'getSiteStats()', 'GET', error);
      console.error('Error fetching UniFi Network site statistics:', error);
      throw error;
    }
  }

  /**
   * Get traffic statistics
   * @returns {Promise<Object>} Traffic statistics
   */
  async getTrafficStatistics() {
    try {
      console.log(`[UniFi Network Debug] Fetching traffic statistics for site: ${this.site}`);
      
      // Ensure client is initialized
      await this._ensureInitialized();
      
      // Get traffic stats using the client
      const stats = await this.client.getTrafficStats();
      
      console.log(`[UniFi Network Debug] Successfully fetched traffic statistics`);
      return stats;
    } catch (error) {
      this._logDetailedError('Get traffic statistics', 'getTrafficStats()', 'GET', error);
      console.error('Error fetching UniFi Network traffic statistics:', error);
      throw error;
    }
  }




}

module.exports = UnifiNetworkAPI;
