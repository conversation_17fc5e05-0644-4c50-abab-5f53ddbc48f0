const debug = require('debug')('api-debug');

const debugStore = {
  requests: [],
  maxSize: 1000,
  
  addRequest(requestData) {
    this.requests.unshift(requestData);
    if (this.requests.length > this.maxSize) {
      this.requests = this.requests.slice(0, this.maxSize);
    }
  },
  
  getRequests() {
    return this.requests;
  },
  
  clear() {
    this.requests = [];
  }
};

// Global axios interceptor setup
let axiosInterceptorsSetup = false;
let websocketServerRef = null;

const createDebugLogger = (websocketServer) => {
  return (req, res, next) => {
    const startTime = Date.now();
    const requestId = `${startTime}_${Math.random().toString(36).substr(2, 9)}`;
    
    const originalSend = res.send;
    const originalJson = res.json;
    
    let responseBody = null;
    let responseHeaders = null;
    
    res.send = function(body) {
      responseBody = body;
      responseHeaders = res.getHeaders();
      return originalSend.call(this, body);
    };
    
    res.json = function(obj) {
      responseBody = obj;
      responseHeaders = res.getHeaders();
      return originalJson.call(this, obj);
    };
    
    res.on('finish', () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const requestData = {
        id: requestId,
        timestamp: new Date(startTime).toISOString(),
        method: req.method,
        url: req.originalUrl,
        path: req.path,
        query: req.query,
        params: req.params,
        headers: req.headers,
        body: req.body,
        statusCode: res.statusCode,
        statusMessage: res.statusMessage,
        responseHeaders,
        responseBody: responseBody && typeof responseBody === 'string' 
          ? (responseBody.length > 5000 ? responseBody.substring(0, 5000) + '...[truncated]' : responseBody)
          : responseBody,
        duration,
        userAgent: req.get('User-Agent'),
        ip: req.ip || req.connection.remoteAddress,
        userId: req.user ? req.user._id : null,
        userEmail: req.user ? req.user.email : null
      };
      
      debugStore.addRequest(requestData);
      
      if (websocketServer) {
        websocketServer.broadcastDebugData('api_request', requestData);
      }
      
      debug(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`);
    });
    
    next();
  };
};

// Function to set websocket server reference
const setWebsocketServer = (websocketServer) => {
  websocketServerRef = websocketServer;
  debug('WebSocket server reference updated');
};

// Function to setup axios interceptors globally
const setupAxiosInterceptors = (websocketServer) => {
  if (axiosInterceptorsSetup) return;
  
  if (websocketServer) {
    websocketServerRef = websocketServer;
  }
  
  // Clear and patch the axios module cache to ensure we get the same instance everywhere
  const axios = require('axios');
  
  // Store original methods
  const originalCreate = axios.create;
  const originalRequest = axios.request;
  const originalGet = axios.get;
  const originalPost = axios.post;
  const originalPut = axios.put;
  const originalDelete = axios.delete;
  const originalPatch = axios.patch;
  
  // Override axios.create to add interceptors to all new instances
  axios.create = function(config = {}) {
    const instance = originalCreate.call(axios, config);
    addInterceptorsToInstance(instance);
    return instance;
  };
  
  // Add interceptors to the default axios instance
  addInterceptorsToInstance(axios);
  
  // Also patch direct method calls on the main axios instance
  ['request', 'get', 'post', 'put', 'delete', 'patch', 'head', 'options'].forEach(method => {
    if (axios[method] && typeof axios[method] === 'function') {
      addInterceptorsToInstance(axios);
    }
  });
  
  // Patch require cache to ensure all future requires get the patched version
  const Module = require('module');
  const originalRequire = Module.prototype.require;
  
  Module.prototype.require = function(id) {
    const result = originalRequire.apply(this, arguments);
    
    // If someone requires axios, make sure they get our patched version
    if (id === 'axios' && result && typeof result.create === 'function') {
      if (!result._debugPatched) {
        addInterceptorsToInstance(result);
        result._debugPatched = true;
      }
    }
    
    return result;
  };
  
  // Mark axios as patched
  axios._debugPatched = true;
  
  axiosInterceptorsSetup = true;
  debug('Global axios interceptors setup complete with require patching');
};

// Function to add interceptors to an axios instance
const addInterceptorsToInstance = (axiosInstance) => {
  // Request interceptor
  axiosInstance.interceptors.request.use(
    (config) => {
      config.metadata = { startTime: Date.now() };
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  // Response interceptor for successful responses
  axiosInstance.interceptors.response.use(
    (response) => {
      if (!response.config.metadata) return response;
      
      const endTime = Date.now();
      const duration = endTime - response.config.metadata.startTime;
      
      // Create full URL
      let fullUrl = response.config.url;
      if (response.config.baseURL && !response.config.url.startsWith('http')) {
        fullUrl = response.config.baseURL + response.config.url;
      }
      
      const externalRequestData = {
        id: `ext_${response.config.metadata.startTime}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(response.config.metadata.startTime).toISOString(),
        type: 'external_api',
        method: response.config.method ? response.config.method.toUpperCase() : 'GET',
        url: fullUrl,
        requestHeaders: response.config.headers,
        requestBody: response.config.data,
        statusCode: response.status,
        statusText: response.statusText,
        responseHeaders: response.headers,
        responseBody: response.data && typeof response.data === 'object' 
          ? JSON.stringify(response.data).length > 5000 
            ? JSON.stringify(response.data).substring(0, 5000) + '...[truncated]'
            : response.data
          : response.data,
        duration
      };
      
      debugStore.addRequest(externalRequestData);
      
      if (websocketServerRef) {
        websocketServerRef.broadcastDebugData('external_api_request', externalRequestData);
      }
      
      debug(`External API: ${externalRequestData.method} ${fullUrl} - ${response.status} - ${duration}ms`);
      
      return response;
    },
    (error) => {
      if (!error.config || !error.config.metadata) return Promise.reject(error);
      
      const endTime = Date.now();
      const duration = endTime - error.config.metadata.startTime;
      
      // Create full URL
      let fullUrl = error.config.url;
      if (error.config.baseURL && !error.config.url.startsWith('http')) {
        fullUrl = error.config.baseURL + error.config.url;
      }
      
      const externalRequestData = {
        id: `ext_${error.config.metadata.startTime}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(error.config.metadata.startTime).toISOString(),
        type: 'external_api',
        method: error.config.method ? error.config.method.toUpperCase() : 'GET',
        url: fullUrl,
        requestHeaders: error.config.headers,
        requestBody: error.config.data,
        statusCode: error.response ? error.response.status : null,
        statusText: error.response ? error.response.statusText : 'Network Error',
        responseHeaders: error.response ? error.response.headers : null,
        responseBody: error.response ? error.response.data : error.message,
        duration,
        error: true
      };
      
      debugStore.addRequest(externalRequestData);
      
      if (websocketServerRef) {
        websocketServerRef.broadcastDebugData('external_api_request', externalRequestData);
      }
      
      debug(`External API Error: ${externalRequestData.method} ${fullUrl} - ${error.response?.status || 'Network Error'} - ${duration}ms`);
      
      return Promise.reject(error);
    }
  );
};

module.exports = {
  createDebugLogger,
  setupAxiosInterceptors,
  setWebsocketServer,
  debugStore
};