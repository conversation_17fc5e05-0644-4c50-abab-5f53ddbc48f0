/**
 * Utility functions for handling Google API token refresh
 */
const User = require('../../models/User');

/**
 * Refreshes the Google API tokens for a user and updates them in the database
 * @param {Object} auth - Google OAuth2 client
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Updated tokens
 */
const refreshAndSaveTokens = async (auth, userId) => {
  try {
    // Request token refresh from Google
    const { credentials } = await auth.refreshAccessToken();
    
    // Update user in database with new tokens
    if (userId) {
      const user = await User.findById(userId);
      if (user) {
        user.googleAccessToken = credentials.access_token;
        
        // Only update refresh token if a new one was provided
        if (credentials.refresh_token) {
          user.googleRefreshToken = credentials.refresh_token;
        }
        
        await user.save();
        console.log(`Updated Google tokens for user ${userId}`);
      }
    }
    
    return credentials;
  } catch (error) {
    console.error('Error refreshing Google tokens:', error);
    throw error;
  }
};

module.exports = {
  refreshAndSaveTokens
};