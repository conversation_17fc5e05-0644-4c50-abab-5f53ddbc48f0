/**
 * Code Verifier Store
 * 
 * This module provides a simple in-memory store for PKCE code verifiers.
 * In a production environment, this should be replaced with a more robust
 * storage solution like Redis or a database.
 */

// In-memory store for code verifiers
const codeVerifiers = new Map();

/**
 * Store a code verifier for a user
 * 
 * @param {string} userId - The user ID
 * @param {string} codeVerifier - The code verifier to store
 * @param {number} expiryMs - Expiry time in milliseconds (default: 10 minutes)
 */
function storeCodeVerifier(userId, codeVerifier, expiryMs = 10 * 60 * 1000) {
  // Store the code verifier with an expiry timestamp
  codeVerifiers.set(userId, {
    codeVerifier,
    expiry: Date.now() + expiryMs
  });
  
  // Set up automatic cleanup after expiry
  setTimeout(() => {
    const entry = codeVerifiers.get(userId);
    if (entry && entry.codeVerifier === codeVerifier) {
      codeVerifiers.delete(userId);
    }
  }, expiryMs);
}

/**
 * Get and remove a code verifier for a user
 * 
 * @param {string} userId - The user ID
 * @returns {string|null} The code verifier if found and not expired, null otherwise
 */
function getAndRemoveCodeVerifier(userId) {
  const entry = codeVerifiers.get(userId);
  
  // Check if entry exists and is not expired
  if (entry && entry.expiry > Date.now()) {
    codeVerifiers.delete(userId);
    return entry.codeVerifier;
  }
  
  // Entry doesn't exist or is expired
  codeVerifiers.delete(userId);
  return null;
}

module.exports = {
  storeCodeVerifier,
  getAndRemoveCodeVerifier
};