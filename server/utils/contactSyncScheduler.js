const ContactSyncConfig = require('../../models/ContactSyncConfig');
const ContactSyncLog = require('../../models/ContactSyncLog');
const GooglePeopleAPI = require('../integrations/googlePeople/googlePeopleAPI');
const AppleContactsAPI = require('../integrations/appleContacts/appleContactsAPI');
const Contact = require('../../models/Contact');

/**
 * Contact Sync Scheduler
 * Handles periodic synchronization of contacts with Google and Apple
 */
class ContactSyncScheduler {
  constructor() {
    this.initialized = false;
    this.syncJobs = new Map(); // Map of user+provider to timeout IDs
  }

  /**
   * Initialize the scheduler
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) return;

    try {
      console.log('Initializing Contact Sync Scheduler...');
      
      // Load all active sync configurations
      const syncConfigs = await ContactSyncConfig.find({ enabled: true });
      console.log(`Found ${syncConfigs.length} active sync configurations`);
      
      // Schedule sync for each configuration
      for (const config of syncConfigs) {
        this.scheduleSync(config);
      }
      
      this.initialized = true;
      console.log('Contact Sync Scheduler initialized');
    } catch (error) {
      console.error('Error initializing Contact Sync Scheduler:', error);
      throw error;
    }
  }

  /**
   * Schedule sync for a configuration
   * @param {Object} syncConfig - Sync configuration
   */
  scheduleSync(syncConfig) {
    // Cancel existing job if any
    const jobKey = `${syncConfig.user}_${syncConfig.provider}`;
    if (this.syncJobs.has(jobKey)) {
      clearTimeout(this.syncJobs.get(jobKey));
      this.syncJobs.delete(jobKey);
    }
    
    // Skip if disabled
    if (!syncConfig.enabled) {
      return;
    }
    
    // Calculate next sync time
    const lastSyncTime = syncConfig.lastSyncTime || new Date(0);
    const syncFrequency = syncConfig.syncFrequency || 60; // Default: 60 minutes
    const nextSyncTime = new Date(lastSyncTime.getTime() + syncFrequency * 60 * 1000);
    const now = new Date();
    
    // Calculate delay until next sync
    let delay = nextSyncTime.getTime() - now.getTime();
    if (delay < 0) {
      delay = 0; // Sync immediately if overdue
    }
    
    // Schedule sync
    const timeoutId = setTimeout(() => {
      this.performSync(syncConfig)
        .catch(error => {
          console.error(`Error performing scheduled sync for ${syncConfig.provider}:`, error);
        })
        .finally(() => {
          // Reschedule after completion
          ContactSyncConfig.findById(syncConfig._id)
            .then(updatedConfig => {
              if (updatedConfig && updatedConfig.enabled) {
                this.scheduleSync(updatedConfig);
              }
            })
            .catch(error => {
              console.error('Error rescheduling sync:', error);
            });
        });
    }, delay);
    
    this.syncJobs.set(jobKey, timeoutId);
    
    console.log(`Scheduled ${syncConfig.provider} sync for user ${syncConfig.user} in ${Math.round(delay / 60000)} minutes`);
  }

  /**
   * Perform sync for a configuration
   * @param {Object} syncConfig - Sync configuration
   * @returns {Promise<void>}
   */
  async performSync(syncConfig) {
    console.log(`Starting ${syncConfig.provider} sync for user ${syncConfig.user}`);
    
    // Create sync log entry
    const syncLog = new ContactSyncLog({
      user: syncConfig.user,
      provider: syncConfig.provider,
      status: 'in_progress'
    });
    
    await syncLog.save();
    
    // Update sync status
    syncConfig.syncStatus = 'in_progress';
    syncConfig.lastSyncTime = new Date();
    await syncConfig.save();
    
    try {
      if (syncConfig.provider === 'google') {
        await this.performGoogleSync(syncConfig, syncLog);
      } else if (syncConfig.provider === 'apple') {
        await this.performAppleSync(syncConfig, syncLog);
      }
    } catch (error) {
      console.error(`Error performing ${syncConfig.provider} sync:`, error);
      
      // Update sync log
      syncLog.status = 'error';
      syncLog.error = error.message;
      syncLog.endTime = new Date();
      await syncLog.save();
      
      // Update sync config
      syncConfig.syncStatus = 'error';
      syncConfig.syncError = error.message;
      await syncConfig.save();
    }
  }

  /**
   * Perform Google sync
   * @param {Object} syncConfig - Sync configuration
   * @param {Object} syncLog - Sync log
   * @returns {Promise<void>}
   */
  async performGoogleSync(syncConfig, syncLog) {
    // Initialize Google People API
    const googlePeopleAPI = new GooglePeopleAPI({
      refreshToken: syncConfig.googleRefreshToken
    });
    
    await googlePeopleAPI.initialize();
    
    // Get or create contact group
    const groupName = 'CSF Portal Contacts';
    const contactGroup = await googlePeopleAPI.getOrCreateContactGroup(groupName);
    
    // Update sync config with group resource name
    syncConfig.googleResourceName = contactGroup.resourceName;
    await syncConfig.save();
    
    // Get contacts from database
    let contacts = await Contact.find({});
    
    // Filter by categories if specified
    if (syncConfig.syncCategories && syncConfig.syncCategories.length > 0) {
      contacts = contacts.filter(contact => 
        syncConfig.syncCategories.includes(contact.category)
      );
    }
    
    // Get existing contacts from Google
    const googleContacts = await googlePeopleAPI.getAllContacts();
    
    // Initialize counters
    let added = 0;
    let updated = 0;
    let removed = 0;
    
    // Process each contact
    for (const contact of contacts) {
      // Check if contact already exists in Google
      const contactId = contact._id.toString();
      const resourceName = syncConfig.contactMappings && syncConfig.contactMappings.get(contactId);
      
      if (resourceName) {
        // Contact exists, update it
        const existingContact = googleContacts.find(c => c.resourceName === resourceName);
        
        if (existingContact) {
          await googlePeopleAPI.updateContact(resourceName, contact);
          updated++;
        } else {
          // Contact was deleted in Google, recreate it
          const newContact = await googlePeopleAPI.createContact(contact, contactGroup.resourceName);
          
          // Update mapping
          if (!syncConfig.contactMappings) {
            syncConfig.contactMappings = new Map();
          }
          syncConfig.contactMappings.set(contactId, newContact.resourceName);
          added++;
        }
      } else {
        // Contact doesn't exist, create it
        const newContact = await googlePeopleAPI.createContact(contact, contactGroup.resourceName);
        
        // Update mapping
        if (!syncConfig.contactMappings) {
          syncConfig.contactMappings = new Map();
        }
        syncConfig.contactMappings.set(contactId, newContact.resourceName);
        added++;
      }
    }
    
    // Update sync log
    syncLog.status = 'success';
    syncLog.contactsAdded = added;
    syncLog.contactsUpdated = updated;
    syncLog.contactsRemoved = removed;
    syncLog.endTime = new Date();
    syncLog.details = `Synced ${contacts.length} contacts with Google`;
    await syncLog.save();
    
    // Update sync config
    syncConfig.syncStatus = 'success';
    syncConfig.syncError = null;
    syncConfig.lastSyncTime = new Date();
    await syncConfig.save();
    
    console.log(`Completed Google sync for user ${syncConfig.user}: ${added} added, ${updated} updated, ${removed} removed`);
  }

  /**
   * Perform Apple sync (currently just updates the sync status)
   * @param {Object} syncConfig - Sync configuration
   * @param {Object} syncLog - Sync log
   * @returns {Promise<void>}
   */
  async performAppleSync(syncConfig, syncLog) {
    // For Apple, we don't have automatic sync, just update the status
    syncLog.status = 'success';
    syncLog.endTime = new Date();
    syncLog.details = 'Apple contacts sync is manual via vCard download';
    await syncLog.save();
    
    syncConfig.syncStatus = 'success';
    syncConfig.syncError = null;
    syncConfig.lastSyncTime = new Date();
    await syncConfig.save();
    
    console.log(`Updated Apple sync status for user ${syncConfig.user}`);
  }

  /**
   * Update sync configuration
   * @param {Object} syncConfig - Updated sync configuration
   * @returns {Promise<void>}
   */
  async updateSyncConfig(syncConfig) {
    // Reschedule sync if enabled
    if (syncConfig.enabled) {
      this.scheduleSync(syncConfig);
    } else {
      // Cancel existing job if disabled
      const jobKey = `${syncConfig.user}_${syncConfig.provider}`;
      if (this.syncJobs.has(jobKey)) {
        clearTimeout(this.syncJobs.get(jobKey));
        this.syncJobs.delete(jobKey);
      }
    }
  }
}

// Create singleton instance
const contactSyncScheduler = new ContactSyncScheduler();

module.exports = contactSyncScheduler;