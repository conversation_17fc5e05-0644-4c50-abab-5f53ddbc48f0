/**
 * PKCE (Proof Key for Code Exchange) Utilities
 * 
 * This module provides utility functions for implementing PKCE in OAuth 2.0 flows.
 * PKCE is an extension to the Authorization Code flow to prevent CSRF and authorization
 * code injection attacks.
 * 
 * References:
 * - RFC 7636: https://datatracker.ietf.org/doc/html/rfc7636
 * - Canva Authentication: https://www.canva.dev/docs/connect/authentication/
 */

const crypto = require('crypto');

/**
 * Generates a code verifier for PKCE.
 * 
 * The code verifier is a high-entropy cryptographically random string
 * between 43 and 128 characters long, using only ASCII letters, numbers,
 * and the characters "-", ".", "_", and "~".
 * 
 * @returns {string} A random code verifier string
 */
function generateCodeVerifier() {
  // Generate a random string of 96 bytes and encode it as base64url
  return crypto.randomBytes(96).toString('base64url');
}

/**
 * Generates a code challenge from a code verifier using SHA-256 hashing.
 * 
 * @param {string} codeVerifier - The code verifier to hash
 * @returns {string} The code challenge derived from the code verifier
 */
function generateCodeChallenge(codeVerifier) {
  return crypto
    .createHash('sha256')
    .update(codeVerifier)
    .digest('base64url');
}

module.exports = {
  generateCodeVerifier,
  generateCodeChallenge
};