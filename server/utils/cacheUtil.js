/**
 * Simple in-memory cache utility for caching external API responses
 * Designed for data that doesn't change frequently, like device details
 */

class CacheUtil {
  constructor() {
    // Initialize the cache storage
    this.cache = new Map();
    
    // Default cache duration in milliseconds (5 minutes)
    this.defaultDuration = 5 * 60 * 1000;
    
    // Log cache initialization
    console.log('Cache utility initialized');
  }

  /**
   * Get a value from the cache
   * @param {string} key - The cache key
   * @returns {any|null} - The cached value or null if not found or expired
   */
  get(key) {
    // Check if the key exists in the cache
    if (!this.cache.has(key)) {
      return null;
    }

    const cacheItem = this.cache.get(key);
    const now = Date.now();

    // Check if the cache item has expired
    if (now > cacheItem.expiry) {
      // Remove the expired item from the cache
      this.cache.delete(key);
      return null;
    }

    // Return the cached value
    return cacheItem.value;
  }

  /**
   * Set a value in the cache
   * @param {string} key - The cache key
   * @param {any} value - The value to cache
   * @param {number} [duration] - Cache duration in milliseconds (optional, defaults to 5 minutes)
   */
  set(key, value, duration = this.defaultDuration) {
    const expiry = Date.now() + duration;
    this.cache.set(key, { value, expiry });
  }

  /**
   * Remove a specific item from the cache
   * @param {string} key - The cache key to remove
   */
  remove(key) {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Get the current size of the cache
   * @returns {number} - The number of items in the cache
   */
  size() {
    return this.cache.size;
  }

  /**
   * Helper method to create a cache key from a prefix and parameters
   * @param {string} prefix - The prefix for the cache key (usually the API endpoint)
   * @param {Object} params - Parameters that affect the response (optional)
   * @returns {string} - The generated cache key
   */
  createKey(prefix, params = {}) {
    if (Object.keys(params).length === 0) {
      return prefix;
    }
    
    // Sort the parameters to ensure consistent key generation
    const sortedParams = Object.keys(params).sort().map(key => {
      return `${key}=${JSON.stringify(params[key])}`;
    }).join('&');
    
    return `${prefix}?${sortedParams}`;
  }
}

// Create a singleton instance
const cacheUtil = new CacheUtil();

module.exports = cacheUtil;