const googleAdminController = require('../controllers/googleAdminController');

/**
 * Google User Sync Scheduler
 * Periodically syncs Google Workspace users to the platform using the
 * service-account authenticated Google Admin integration.
 */
let initialized = false;
let inProgress = false;
let intervalId = null;
let startupTimeoutId = null;

function getIntervalMinutes() {
  const val = parseInt(process.env.GOOGLE_USER_AUTO_SYNC_INTERVAL_MINUTES, 10);
  if (Number.isFinite(val) && val > 0) return val;
  return 360; // default 6 hours
}

function isEnabled() {
  // Default enabled unless explicitly set to 'false'
  return process.env.GOOGLE_USER_AUTO_SYNC_ENABLED !== 'false';
}

async function runOnce() {
  if (inProgress) {
    console.log('[GoogleUserSync] Previous sync still running, skipping this tick.');
    return;
  }
  inProgress = true;
  const startedAt = new Date();
  console.log(`[GoogleUserSync] Starting users sync at ${startedAt.toISOString()}`);
  try {
    const result = await googleAdminController.runBackgroundUsersSync();
    if (result && result.ok) {
      const { results } = result;
      console.log('[GoogleUserSync] Sync completed:', {
        total: results.total,
        created: results.created,
        existing: results.existing,
        errors: results.errors,
        groupsProcessed: results.groupsProcessed,
        teamsProcessed: results.teamsProcessed,
      });
      if (results.errorDetails && results.errorDetails.length > 0) {
        console.warn(`[GoogleUserSync] ${results.errorDetails.length} user(s) had errors. See logs for details.`);
      }
    } else {
      console.error('[GoogleUserSync] Sync failed:', result && result.error ? result.error : 'Unknown error');
    }
  } catch (err) {
    console.error('[GoogleUserSync] Unexpected error during sync:', err);
  } finally {
    inProgress = false;
    const finishedAt = new Date();
    console.log(`[GoogleUserSync] Users sync finished at ${finishedAt.toISOString()} (duration ${(finishedAt - startedAt) / 1000}s)`);
  }
}

async function initialize() {
  if (initialized) return;

  if (!isEnabled()) {
    console.log('[GoogleUserSync] Auto-sync disabled (GOOGLE_USER_AUTO_SYNC_ENABLED=false).');
    initialized = true;
    return;
  }

  const minutes = getIntervalMinutes();
  const intervalMs = minutes * 60 * 1000;

  // Add a small jitter (0-120s) to avoid thundering herd on multi-instance deployments
  const jitterMs = Math.floor(Math.random() * 120000);

  // Schedule first run after jitter, then set interval
  startupTimeoutId = setTimeout(() => {
    runOnce();
    intervalId = setInterval(runOnce, intervalMs);
  }, jitterMs);

  initialized = true;
  console.log(`[GoogleUserSync] Scheduler initialized. Interval: ${minutes} minutes. First run in ${Math.round(jitterMs / 1000)} seconds.`);
}

function stop() {
  if (startupTimeoutId) clearTimeout(startupTimeoutId);
  if (intervalId) clearInterval(intervalId);
  initialized = false;
  console.log('[GoogleUserSync] Scheduler stopped.');
}

module.exports = {
  initialize,
  runOnce,
  stop,
};
