const fs = require('fs');
const path = require('path');
const nodemailer = require('nodemailer');

/**
 * Email template utility functions
 */
class EmailUtils {
  constructor() {
    // Initialize the email transporter
    this.initializeTransporter();

    // Cache for templates
    this.templateCache = {};
    
    // Base layout path
    this.baseLayoutPath = path.join(__dirname, 'baseLayout.html');
    
    // Templates directory
    this.templatesDir = path.join(__dirname, 'templates');
  }

  /**
   * Initialize the email transporter with Gmail OAuth2
   * Falls back to SMTP if OAuth2 is not configured or fails
   */
  initializeTransporter() {
    // Check if Gmail OAuth2 credentials are available
    const hasOAuth2Credentials = !!(
      process.env.GMAIL_OAUTH_CLIENT_ID &&
      process.env.GMAIL_OAUTH_CLIENT_SECRET &&
      process.env.GMAIL_OAUTH_REFRESH_TOKEN
    );

    if (hasOAuth2Credentials) {
      // Initialize with Gmail OAuth2
      const oauth2Config = {
        type: 'OAuth2',
        user: process.env.EMAIL_FROM || '<EMAIL>',
        clientId: process.env.GMAIL_OAUTH_CLIENT_ID,
        clientSecret: process.env.GMAIL_OAUTH_CLIENT_SECRET,
        refreshToken: process.env.GMAIL_OAUTH_REFRESH_TOKEN
      };

      // Log OAuth2 configuration for debugging (without sensitive data)
      console.log('Initializing Gmail OAuth2 with user:', oauth2Config.user);
      console.log('OAuth2 client ID available:', !!oauth2Config.clientId);
      console.log('OAuth2 client secret available:', !!oauth2Config.clientSecret);
      console.log('OAuth2 refresh token available:', !!oauth2Config.refreshToken);

      try {
        this.transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: oauth2Config,
          debug: process.env.ENABLE_DEBUG_LOGGING === 'true'
        });

        // Handle token refresh events
        this.transporter.on('token', token => {
          console.log('New access token generated for:', token.user);
        });

        console.log('Gmail OAuth2 transporter initialized successfully');
      } catch (error) {
        console.error('Error initializing Gmail OAuth2 transporter:', error);
        this.initializeSMTPTransporter();
      }
    } else {
      console.log('Gmail OAuth2 credentials not found, falling back to SMTP');
      this.initializeSMTPTransporter();
    }
  }

  /**
   * Initialize the email transporter with SMTP (fallback)
   */
  initializeSMTPTransporter() {
    console.log('Initializing SMTP transporter');
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      }
    });
  }

  /**
   * Read a template file from disk or cache
   * @param {string} templateName - Name of the template file without extension
   * @returns {string} Template content
   */
  getTemplate(templateName) {
    // Check if template is in cache
    if (this.templateCache[templateName]) {
      return this.templateCache[templateName];
    }

    // Read template from disk
    const templatePath = path.join(this.templatesDir, `${templateName}.html`);
    try {
      const template = fs.readFileSync(templatePath, 'utf8');
      // Cache the template
      this.templateCache[templateName] = template;
      return template;
    } catch (error) {
      console.error(`Error reading template ${templateName}:`, error);
      throw new Error(`Template ${templateName} not found`);
    }
  }

  /**
   * Read the base layout template
   * @returns {string} Base layout template content
   */
  getBaseLayout() {
    // Check if base layout is in cache
    if (this.templateCache['baseLayout']) {
      return this.templateCache['baseLayout'];
    }

    try {
      const baseLayout = fs.readFileSync(this.baseLayoutPath, 'utf8');
      // Cache the base layout
      this.templateCache['baseLayout'] = baseLayout;
      return baseLayout;
    } catch (error) {
      console.error('Error reading base layout:', error);
      throw new Error('Base layout template not found');
    }
  }

  /**
   * Render a template with provided data
   * @param {string} template - Template content
   * @param {Object} data - Data to inject into the template
   * @returns {string} Rendered template
   */
  renderTemplate(template, data) {
    // Replace placeholders with data
    let rendered = template;
    for (const key in data) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      rendered = rendered.replace(regex, data[key]);
    }
    return rendered;
  }

  /**
   * Render a complete email using the base layout and a specific template
   * @param {string} templateName - Name of the template file without extension
   * @param {Object} data - Data to inject into the template
   * @param {string} title - Email title
   * @returns {string} Rendered email HTML
   */
  renderEmail(templateName, data, title) {
    // Get the template content
    const templateContent = this.getTemplate(templateName);
    
    // Render the template with data
    const renderedContent = this.renderTemplate(templateContent, data);
    
    // Get the base layout
    const baseLayout = this.getBaseLayout();
    
    // Add current year for the footer
    const currentYear = new Date().getFullYear();
    
    // Render the base layout with the rendered content
    return this.renderTemplate(baseLayout, {
      content: renderedContent,
      title: title,
      year: currentYear
    });
  }

  /**
   * Send an email using a template
   * @param {Object} options - Email options
   * @param {string} options.to - Recipient email address
   * @param {string} options.subject - Email subject
   * @param {string} options.templateName - Name of the template file without extension
   * @param {Object} options.data - Data to inject into the template
   * @param {string} [options.from] - Sender email address (defaults to EMAIL_FROM env var)
   * @returns {Promise} Promise that resolves when the email is sent
   */
  async sendTemplatedEmail(options) {
    const { to, subject, templateName, data, from = process.env.EMAIL_FROM } = options;
    
    // Render the email
    const html = this.renderEmail(templateName, data, subject);
    
    // Setup email data
    const mailOptions = {
        from: `"CSF Portal" <${from}>`,
        to,
        subject,
        html
    };
    
    try {
      // Try to send the email with the current transporter
      const info = await this.transporter.sendMail(mailOptions);
      console.log(`Email sent: ${info.messageId}`);
      return info;
    } catch (error) {
      console.error('Error sending email with primary method:', error);
      
      // If the error is an authentication error and we're using OAuth2, try SMTP as fallback
      if (error.code === 'EAUTH' && this.transporter?.options?.auth?.type === 'OAuth2') {
        console.log('OAuth2 authentication failed, falling back to SMTP...');
        
        // Initialize SMTP transporter as fallback
        this.initializeSMTPTransporter();
        
        try {
          // Try again with SMTP
          const info = await this.transporter.sendMail(mailOptions);
          console.log(`Email sent with SMTP fallback: ${info.messageId}`);
          return info;
        } catch (smtpError) {
          console.error('Error sending email with SMTP fallback:', smtpError);
          throw smtpError;
        }
      } else {
        // If it's not an authentication error or we're not using OAuth2, just throw the original error
        throw error;
      }
    }
  }
}

// Create and export a singleton instance
const emailUtils = new EmailUtils();
module.exports = emailUtils;