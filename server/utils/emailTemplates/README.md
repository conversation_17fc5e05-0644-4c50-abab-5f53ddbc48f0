# Email Templating System

This directory contains the email templating system for the CSF Portal application. The system provides a framework for creating and sending emails with consistent layouts and styling.

## Directory Structure

- `baseLayout.html` - The base layout template used by all emails
- `templates/` - Directory containing specific email templates
  - `welcome.html` - Template for welcome emails sent to new users
  - `passwordReset.html` - Template for password reset emails
- `emailUtils.js` - Utility functions for rendering templates and sending emails

## How It Works

The email templating system uses a simple template engine that replaces placeholders in the form of `{{variable}}` with actual values. The system follows these steps:

1. A specific email template (e.g., welcome.html) is loaded
2. Template variables are replaced with actual data
3. The rendered content is inserted into the base layout
4. The complete HTML email is sent using nodemailer

## Using the Email Templating System

To send an email using the templating system, import the `emailUtils` module and call the `sendTemplatedEmail` function:

```javascript
const emailUtils = require('../../server/utils/emailTemplates/emailUtils');

// Send an email using a template
await emailUtils.sendTemplatedEmail({
  to: '<EMAIL>',
  subject: 'Email Subject',
  templateName: 'templateName', // without .html extension
  data: {
    // Data to inject into the template
    key1: 'value1',
    key2: 'value2'
  }
});
```

## Creating New Email Templates

To create a new email template:

1. Create a new HTML file in the `templates/` directory
2. Use placeholders in the form of `{{variable}}` for dynamic content
3. Use the template in your code by specifying its name (without the .html extension)

Example template:

```html
<h1>Hello {{name}}!</h1>
<p>This is a sample email template.</p>
<p><a href="{{link}}" class="button">Click Here</a></p>
```

## Styling Emails

The base layout includes CSS styles that can be used in email templates:

- `.button` - Style for buttons
- `.content` - Main content area
- `.header` - Email header
- `.footer` - Email footer

## Available Templates

### Welcome Email

**Template Name:** `welcome`

**Variables:**
- `{{name}}` - User's name
- `{{resetLink}}` - Link for setting the initial password

### Password Reset Email

**Template Name:** `passwordReset`

**Variables:**
- `{{resetLink}}` - Link for resetting the password

## Environment Variables

The email templating system uses the following environment variables:

- `EMAIL_HOST` - SMTP server host
- `EMAIL_PORT` - SMTP server port
- `EMAIL_SECURE` - Whether to use TLS (true/false)
- `EMAIL_USER` - SMTP username
- `EMAIL_PASSWORD` - SMTP password
- `EMAIL_FROM` - Default sender email address
- `FRONTEND_URL` - URL of the frontend application (used for links)