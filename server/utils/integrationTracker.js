const fs = require('fs');
const path = require('path');

/**
 * Utility for tracking integration status in a markdown file
 */
class IntegrationTracker {
  constructor(filePath = 'integration-status.md') {
    this.filePath = path.resolve(process.cwd(), filePath);
    this.integrationStatus = {};
    this.initialize();
  }

  /**
   * Initialize the tracker by reading the existing status file or creating a new one
   */
  initialize() {
    try {
      if (fs.existsSync(this.filePath)) {
        this.parseStatusFile();
      } else {
        this.createStatusFile();
      }
    } catch (error) {
      console.error('Error initializing integration tracker:', error);
      // Create a new file if there was an error parsing the existing one
      this.createStatusFile();
    }
  }

  /**
   * Create a new status file with headers
   */
  createStatusFile() {
    const header = `# Integration Status Tracker\n\nLast Updated: ${new Date().toISOString()}\n\n`;
    const tableHeader = `| Integration | Status | Last Checked | Last Updated | Notes |\n| ----------- | ------ | ------------ | ------------ | ----- |\n`;

    fs.writeFileSync(this.filePath, header + tableHeader);
    this.integrationStatus = {};
  }

  /**
   * Parse the status file and load the integration status
   */
  parseStatusFile() {
    const content = fs.readFileSync(this.filePath, 'utf8');
    const lines = content.split('\n');

    // Skip header lines and table header
    const dataLines = lines.filter(line => line.startsWith('|') && !line.startsWith('| Integration'));

    this.integrationStatus = {};

    dataLines.forEach(line => {
      const parts = line.split('|').map(part => part.trim());
      if (parts.length >= 6) {
        const [_, integration, status, lastChecked, lastUpdated, notes] = parts;

        // Safely parse dates with fallback to current date if invalid
        let parsedLastChecked;
        try {
          parsedLastChecked = lastChecked ? new Date(lastChecked) : new Date();
          // Validate the date is valid and not in the future
          if (isNaN(parsedLastChecked) || parsedLastChecked > new Date()) {
            parsedLastChecked = new Date();
          }
        } catch (error) {
          parsedLastChecked = new Date();
        }

        let parsedLastUpdated = null;
        if (lastUpdated && lastUpdated !== 'N/A') {
          try {
            parsedLastUpdated = new Date(lastUpdated);
            // Validate the date is valid and not in the future
            if (isNaN(parsedLastUpdated) || parsedLastUpdated > new Date()) {
              parsedLastUpdated = null;
            }
          } catch (error) {
            parsedLastUpdated = null;
          }
        }

        this.integrationStatus[integration] = {
          status,
          lastChecked: parsedLastChecked,
          lastUpdated: parsedLastUpdated,
          notes
        };
      }
    });
  }

  /**
   * Update the status of an integration
   * @param {string} integration - Name of the integration
   * @param {string} status - Status of the integration (e.g., 'active', 'inactive', 'error')
   * @param {Date} lastUpdated - When the integration was last updated (null if not updated)
   * @param {string} notes - Additional notes about the integration status
   */
  updateStatus(integration, status, lastUpdated = null, notes = '') {
    const now = new Date();

    // Validate lastUpdated is a valid date and not in the future
    let validLastUpdated = null;
    if (lastUpdated) {
      try {
        // If it's already a Date object
        if (lastUpdated instanceof Date && !isNaN(lastUpdated)) {
          validLastUpdated = lastUpdated > now ? now : lastUpdated;
        } 
        // If it's a string or something else
        else {
          const parsedDate = new Date(lastUpdated);
          if (!isNaN(parsedDate)) {
            validLastUpdated = parsedDate > now ? now : parsedDate;
          }
        }
      } catch (error) {
        console.error('Invalid date provided to updateStatus:', error);
        validLastUpdated = null;
      }
    }

    // Use existing lastUpdated from status if available and valid
    if (!validLastUpdated && this.integrationStatus[integration]?.lastUpdated) {
      const existingDate = this.integrationStatus[integration]?.lastUpdated;
      if (existingDate instanceof Date && !isNaN(existingDate) && existingDate <= now) {
        validLastUpdated = existingDate;
      }
    }

    this.integrationStatus[integration] = {
      status,
      lastChecked: now,
      lastUpdated: validLastUpdated,
      notes
    };

    this.saveStatusFile();
  }

  /**
   * Save the current status to the markdown file
   */
  saveStatusFile() {
    // Ensure we're using a valid current date
    const now = new Date();
    const header = `# Integration Status Tracker\n\nLast Updated: ${now.toISOString()}\n\n`;
    const tableHeader = `| Integration | Status | Last Checked | Last Updated | Notes |\n| ----------- | ------ | ------------ | ------------ | ----- |\n`;

    let tableContent = '';

    Object.entries(this.integrationStatus).forEach(([integration, status]) => {
      // Ensure dates are valid before calling toISOString()
      const lastCheckedStr = status.lastChecked && !isNaN(status.lastChecked) 
        ? status.lastChecked.toISOString() 
        : now.toISOString();

      const lastUpdatedStr = status.lastUpdated && !isNaN(status.lastUpdated) 
        ? status.lastUpdated.toISOString() 
        : 'N/A';

      tableContent += `| ${integration} | ${status.status} | ${lastCheckedStr} | ${lastUpdatedStr} | ${status.notes} |\n`;
    });

    fs.writeFileSync(this.filePath, header + tableHeader + tableContent);
  }

  /**
   * Check if an integration needs to be checked again based on a time threshold
   * @param {string} integration - Name of the integration
   * @param {number} thresholdHours - Number of hours before checking again (default: 24)
   * @returns {boolean} - Whether the integration needs to be checked
   */
  needsCheck(integration, thresholdHours = 24) {
    if (!this.integrationStatus[integration]) {
      return true; // If we have no record, it needs to be checked
    }

    const lastChecked = this.integrationStatus[integration]?.lastChecked;
    const now = new Date();
    const hoursSinceLastCheck = (now - lastChecked) / (1000 * 60 * 60);

    return hoursSinceLastCheck >= thresholdHours;
  }

  /**
   * Get the status of an integration
   * @param {string} integration - Name of the integration
   * @returns {Object|null} - Status object or null if not found
   */
  getStatus(integration) {
    return this.integrationStatus[integration] || null;
  }

  /**
   * Get all integration statuses
   * @returns {Object} - Object with all integration statuses
   */
  getAllStatuses() {
    return this.integrationStatus;
  }
}

module.exports = new IntegrationTracker();
