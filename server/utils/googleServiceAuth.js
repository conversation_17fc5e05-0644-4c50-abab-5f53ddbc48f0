/**
 * Google Service Account Authentication Utility
 * 
 * This module provides a reusable function to authenticate with Google APIs
 * using a service account and impersonating a user.
 */
const { google } = require('googleapis');
const integrationTracker = require('./integrationTracker');

/**
 * Creates a Google API client authenticated with a service account
 * that impersonates the specified user.
 * 
 * @param {string} serviceName - The name of the Google service (e.g., 'Calendar', 'Drive')
 * @param {string[]} scopes - Array of OAuth scopes required for the API
 * @param {string} userEmail - Email of the user to impersonate (typically the logged-in user)
 * @returns {Object} - The authenticated Google API client
 */
const getAuthenticatedClient = async (serviceName, scopes, userEmail) => {
  try {
    // Check if we need to initialize the integration based on the tracker
    const integrationName = `Google ${serviceName}`;
    console.log(`Initializing ${integrationName} service with service account for user: ${userEmail}`);
    
    const needsCheck = integrationTracker.needsCheck(integrationName);

    // If we don't need to check this integration again, log and return early
    if (!needsCheck) {
      console.log(`Skipping ${integrationName} initialization - last checked recently`);
      const status = integrationTracker.getStatus(integrationName);
      if (status && status.status === 'active') {
        // Still create the auth object but skip the token validation
        console.log(`Creating ${integrationName} auth object with cached validation`);
        try {
          const auth = createServiceAccountAuth(scopes, userEmail);
          return auth;
        } catch (authError) {
          console.error(`Failed to create ${integrationName} auth object with cached validation:`, authError);
          throw authError;
        }
      }
    }

    // Validate inputs
    if (!userEmail) {
      console.error(`${integrationName} authentication failed: No user email provided for impersonation`);
      throw new Error(`User email is required for ${serviceName} service account authentication`);
    }

    if (!scopes || !Array.isArray(scopes) || scopes.length === 0) {
      console.error(`${integrationName} authentication failed: Invalid scopes provided`, scopes);
      throw new Error(`Valid scopes are required for ${serviceName} service account authentication`);
    }

    console.log(`Creating ${integrationName} auth object with scopes:`, scopes);
    
    // Create service account auth with detailed error handling
    try {
      const auth = createServiceAccountAuth(scopes, userEmail);
      
      // Update the integration status to active
      integrationTracker.updateStatus(
        integrationName, 
        'active', 
        new Date(), 
        'Service account authentication is properly configured and ready to use.'
      );
      
      console.log(`Successfully created authenticated client for ${integrationName}`);
      return auth;
    } catch (authError) {
      console.error(`Failed to create service account auth for ${integrationName}:`, authError);
      throw authError;
    }
  } catch (error) {
    console.error(`Error initializing Google ${serviceName} API with service account:`, error);
    
    // Log detailed error information
    console.error(`${serviceName} Authentication Error Details:`, JSON.stringify({
      message: error.message,
      code: error.code,
      stack: error.stack,
      serviceName: serviceName,
      userEmail: userEmail,
      scopes: scopes
    }, null, 2));

    // Update the integration status to indicate an error
    integrationTracker.updateStatus(
      `Google ${serviceName}`, 
      'error', 
      null, 
      `Error: ${error.message}`
    );

    throw error;
  }
};

/**
 * Creates a Google API client authenticated with a service account
 * that impersonates the specified user.
 * 
 * @param {string[]} scopes - Array of OAuth scopes required for the API
 * @param {string} userEmail - Email of the user to impersonate
 * @returns {Object} - The authenticated Google API client
 */
const createServiceAccountAuth = (scopes, userEmail) => {
  // Get service account credentials from environment variables
  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;

  if (!serviceAccountEmail || !serviceAccountPrivateKey) {
    throw new Error('Service account credentials are missing. Please check your environment variables.');
  }

  // Create a JWT client with the service account credentials
  const auth = new google.auth.JWT({
    email: serviceAccountEmail,
    key: serviceAccountPrivateKey,
    scopes: scopes,
    subject: userEmail // This is the user to impersonate
  });

  return auth;
};

module.exports = {
  getAuthenticatedClient
};