const express = require('express');
const router = express.Router();
const BuildingTrackingItem = require('../../models/BuildingTrackingItem');
const Building = require('../../models/Building');
const Contact = require('../../models/Contact');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Helper to safely create regex
function escapeRegex(text) {
  return text ? text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') : '';
}

async function findExistingContactByProvider(provider) {
  if (!provider) return null;
  const or = [];
  if (provider.email) {
    or.push({ email: provider.email });
  }
  if (provider.phone) {
    or.push({ phone: provider.phone });
  }
  if (provider.name) {
    or.push({ name: new RegExp('^' + escapeRegex(provider.name) + '$', 'i') });
  }
  if (provider.contactPerson) {
    or.push({ name: new RegExp('^' + escapeRegex(provider.contactPerson) + '$', 'i') });
  }
  if (or.length === 0) return null;
  return await Contact.findOne({ $or: or });
}

/**
 * @route   GET /api/building-tracking
 * @desc    Get all tracking items (with optional filters)
 * @access  Private
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const { 
      buildingId, 
      type, 
      status, 
      expiringBefore, 
      expiringAfter,
      renewalBefore,
      renewalAfter
    } = req.query;
    
    // Build query object based on provided filters
    const query = {};
    
    if (buildingId) query.buildingId = buildingId;
    if (type) query.type = type;
    if (status) query.status = status;
    
    // Date range filters
    if (expiringBefore || expiringAfter) {
      query.expirationDate = {};
      if (expiringBefore) query.expirationDate.$lte = new Date(expiringBefore);
      if (expiringAfter) query.expirationDate.$gte = new Date(expiringAfter);
    }
    
    if (renewalBefore || renewalAfter) {
      query.renewalDate = {};
      if (renewalBefore) query.renewalDate.$lte = new Date(renewalBefore);
      if (renewalAfter) query.renewalDate.$gte = new Date(renewalAfter);
    }
    
    // Get tracking items with populated building info
    const trackingItems = await BuildingTrackingItem.find(query)
      .populate('buildingId', 'name address')
      .populate('providerContactId', 'name phone email company')
      .sort({ expirationDate: 1 });
    
    res.json(trackingItems);
  } catch (err) {
    console.error('Error fetching tracking items:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   GET /api/building-tracking/expiring
 * @desc    Get tracking items expiring within a specified number of days
 * @access  Private
 */
router.get('/expiring', isAuthenticated, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    // Calculate the date range (today to X days from now)
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + parseInt(days));
    
    // Find items expiring within the date range
    const expiringItems = await BuildingTrackingItem.find({
      expirationDate: { $gte: today, $lte: futureDate },
      status: { $in: ['active', 'pending_renewal'] }
    })
      .populate('buildingId', 'name address')
      .populate('providerContactId', 'name phone email company')
      .sort({ expirationDate: 1 });
    
    res.json(expiringItems);
  } catch (err) {
    console.error('Error fetching expiring items:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   GET /api/building-tracking/building/:buildingId
 * @desc    Get all tracking items for a specific building
 * @access  Private
 */
router.get('/building/:buildingId', isAuthenticated, async (req, res) => {
  try {
    const { buildingId } = req.params;
    
    // Verify building exists
    const building = await Building.findById(buildingId);
    if (!building) {
      return res.status(404).json({ message: 'Building not found' });
    }
    
    // Get all tracking items for the building
    const trackingItems = await BuildingTrackingItem.find({ buildingId })
      .populate('providerContactId', 'name phone email company')
      .sort({ expirationDate: 1 });
    
    res.json(trackingItems);
  } catch (err) {
    console.error('Error fetching building tracking items:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   GET /api/building-tracking/:id
 * @desc    Get a tracking item by ID
 * @access  Private
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const trackingItem = await BuildingTrackingItem.findById(req.params.id)
      .populate('buildingId', 'name address')
      .populate('providerContactId', 'name phone email company');
    
    if (!trackingItem) {
      return res.status(404).json({ message: 'Tracking item not found' });
    }
    
    res.json(trackingItem);
  } catch (err) {
    console.error('Error fetching tracking item:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   POST /api/building-tracking
 * @desc    Create a new tracking item
 * @access  Private
 */
router.post('/', isAuthenticated, async (req, res) => {
  try {
    const {
      buildingId,
      name,
      type,
      description,
      provider,
      providerContactId,
      createProviderContact,
      startDate,
      expirationDate,
      renewalDate,
      notificationDays,
      status,
      cost,
      documents,
      notes
    } = req.body;
    
    // Verify building exists
    const building = await Building.findById(buildingId);
    if (!building) {
      return res.status(404).json({ message: 'Building not found' });
    }

    // Determine providerContactId (auto-lookup / create if needed)
    let linkedContactId = providerContactId || null;
    const shouldCreate = createProviderContact !== false; // default true

    if (!linkedContactId) {
      const existing = await findExistingContactByProvider(provider);
      if (existing) {
        linkedContactId = existing._id;
      } else if (shouldCreate && provider && (provider.name || provider.contactPerson || provider.phone || provider.email)) {
        try {
          const contact = new Contact({
            name: provider.contactPerson || provider.name || name,
            company: provider.name || undefined,
            category: 'business',
            phone: provider.phone || undefined,
            email: provider.email || undefined,
            notes: `Auto-created from Facility Services item: ${name}`,
            createdBy: req.user.id
          });
          const saved = await contact.save();
          linkedContactId = saved._id;
        } catch (e) {
          console.warn('Failed to auto-create provider contact:', e.message);
        }
      }
    }

    // If provider fields are missing but we linked a contact, hydrate snapshot from contact
    let providerSnapshot = provider;
    if ((!providerSnapshot || Object.keys(providerSnapshot || {}).length === 0) && linkedContactId) {
      const contact = await Contact.findById(linkedContactId);
      if (contact) {
        providerSnapshot = {
          name: contact.company || contact.name || '',
          contactPerson: contact.name || '',
          phone: contact.phone || '',
          email: contact.email || '',
          website: ''
        };
      }
    }
    
    // Create new tracking item
    const newTrackingItem = new BuildingTrackingItem({
      buildingId,
      name,
      type,
      description,
      provider: providerSnapshot,
      providerContactId: linkedContactId,
      startDate,
      expirationDate,
      renewalDate,
      notificationDays,
      status,
      cost,
      documents,
      notes,
      createdBy: req.user.id
    });
    
    const trackingItem = await newTrackingItem.save();
    res.status(201).json(trackingItem);
  } catch (err) {
    console.error('Error creating tracking item:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   PUT /api/building-tracking/:id
 * @desc    Update a tracking item
 * @access  Private
 */
router.put('/:id', isAuthenticated, async (req, res) => {
  try {
    const {
      buildingId,
      name,
      type,
      description,
      provider,
      providerContactId,
      createProviderContact,
      startDate,
      expirationDate,
      renewalDate,
      notificationDays,
      status,
      cost,
      documents,
      notes
    } = req.body;
    
    // Find tracking item by ID
    let trackingItem = await BuildingTrackingItem.findById(req.params.id);
    
    if (!trackingItem) {
      return res.status(404).json({ message: 'Tracking item not found' });
    }
    
    // If buildingId is changing, verify the new building exists
    if (buildingId && buildingId !== trackingItem.buildingId.toString()) {
      const building = await Building.findById(buildingId);
      if (!building) {
        return res.status(404).json({ message: 'Building not found' });
      }
    }

    // Determine providerContactId (auto-lookup / create if needed)
    let linkedContactId = providerContactId || trackingItem.providerContactId || null;
    const shouldCreate = createProviderContact !== false; // default true

    // If providerContactId changed or not set and provider provided, try match or create
    if (!linkedContactId || (provider && provider !== trackingItem.provider)) {
      const existing = await findExistingContactByProvider(provider);
      if (existing) {
        linkedContactId = existing._id;
      } else if (shouldCreate && provider && (provider.name || provider.contactPerson || provider.phone || provider.email)) {
        try {
          const contact = new Contact({
            name: provider.contactPerson || provider.name || name || trackingItem.name,
            company: provider.name || undefined,
            category: 'business',
            phone: provider.phone || undefined,
            email: provider.email || undefined,
            notes: `Auto-created from Facility Services item: ${name || trackingItem.name}`,
            createdBy: req.user.id
          });
          const saved = await contact.save();
          linkedContactId = saved._id;
        } catch (e) {
          console.warn('Failed to auto-create provider contact:', e.message);
        }
      }
    }
    
    // If provider not provided but linked contact exists, hydrate provider snapshot
    let providerSnapshot = provider !== undefined ? provider : trackingItem.provider;
    if ((!providerSnapshot || Object.keys(providerSnapshot || {}).length === 0) && linkedContactId) {
      const contact = await Contact.findById(linkedContactId);
      if (contact) {
        providerSnapshot = {
          name: contact.company || contact.name || '',
          contactPerson: contact.name || '',
          phone: contact.phone || '',
          email: contact.email || '',
          website: ''
        };
      }
    }
    
    // Update tracking item fields
    trackingItem.buildingId = buildingId || trackingItem.buildingId;
    trackingItem.name = name || trackingItem.name;
    trackingItem.type = type || trackingItem.type;
    trackingItem.description = description !== undefined ? description : trackingItem.description;
    trackingItem.provider = providerSnapshot;
    trackingItem.providerContactId = linkedContactId;
    trackingItem.startDate = startDate || trackingItem.startDate;
    trackingItem.expirationDate = expirationDate || trackingItem.expirationDate;
    trackingItem.renewalDate = renewalDate !== undefined ? renewalDate : trackingItem.renewalDate;
    trackingItem.notificationDays = notificationDays !== undefined ? notificationDays : trackingItem.notificationDays;
    trackingItem.status = status || trackingItem.status;
    trackingItem.cost = cost || trackingItem.cost;
    trackingItem.documents = documents || trackingItem.documents;
    trackingItem.notes = notes !== undefined ? notes : trackingItem.notes;
    trackingItem.updatedAt = Date.now();
    
    // Save updated tracking item
    trackingItem = await trackingItem.save();
    // Populate providerContactId for response consistency
    trackingItem = await trackingItem.populate('providerContactId', 'name phone email company');
    res.json(trackingItem);
  } catch (err) {
    console.error('Error updating tracking item:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

/**
 * @route   DELETE /api/building-tracking/:id
 * @desc    Delete a tracking item
 * @access  Private
 */
router.delete('/:id', isAuthenticated, async (req, res) => {
  try {
    // Find tracking item by ID
    const trackingItem = await BuildingTrackingItem.findById(req.params.id);
    
    if (!trackingItem) {
      return res.status(404).json({ message: 'Tracking item not found' });
    }
    
    // Delete the tracking item
    await BuildingTrackingItem.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Tracking item deleted successfully' });
  } catch (err) {
    console.error('Error deleting tracking item:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;