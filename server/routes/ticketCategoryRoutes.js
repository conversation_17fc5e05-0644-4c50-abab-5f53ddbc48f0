const express = require('express');
const router = express.Router();
const ticketCategoryController = require('../controllers/ticketCategoryController');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const { body } = require('express-validator');

/**
 * Ticket Category Routes
 * Base path: /api/ticket-categories
 */

// Validation middleware
const validateCategoryCreation = [
  body('name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ max: 100 })
    .withMessage('Category name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('parent')
    .optional()
    .isMongoId()
    .withMessage('Invalid parent category ID'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color code'),
  body('defaultAssignee')
    .optional()
    .isMongoId()
    .withMessage('Invalid default assignee user ID'),
  body('defaultGroup')
    .optional()
    .isMongoId()
    .withMessage('Invalid default group ID'),
  body('slaSettings.firstResponseTime')
    .optional()
    .isInt({ min: 1 })
    .withMessage('First response time must be a positive integer (minutes)'),
  body('slaSettings.resolutionTime')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Resolution time must be a positive integer (minutes)'),
  body('autoRules')
    .optional()
    .isArray()
    .withMessage('Auto rules must be an array'),
  body('autoRules.*.field')
    .optional()
    .isIn(['subject', 'description', 'requesterEmail', 'senderDomain'])
    .withMessage('Invalid auto rule field'),
  body('autoRules.*.operator')
    .optional()
    .isIn(['contains', 'starts_with', 'ends_with', 'equals', 'regex'])
    .withMessage('Invalid auto rule operator'),
  body('autoRules.*.value')
    .optional()
    .notEmpty()
    .withMessage('Auto rule value is required'),
  body('autoRules.*.priority')
    .optional()
    .isInt()
    .withMessage('Auto rule priority must be an integer'),
  body('sortOrder')
    .optional()
    .isInt()
    .withMessage('Sort order must be an integer')
];

const validateCategoryUpdate = [
  body('name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Category name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('parent')
    .optional()
    .isMongoId()
    .withMessage('Invalid parent category ID'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color code'),
  body('defaultAssignee')
    .optional()
    .isMongoId()
    .withMessage('Invalid default assignee user ID'),
  body('defaultGroup')
    .optional()
    .isMongoId()
    .withMessage('Invalid default group ID'),
  body('slaSettings.firstResponseTime')
    .optional()
    .isInt({ min: 1 })
    .withMessage('First response time must be a positive integer (minutes)'),
  body('slaSettings.resolutionTime')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Resolution time must be a positive integer (minutes)'),
  body('autoRules')
    .optional()
    .isArray()
    .withMessage('Auto rules must be an array'),
  body('autoRules.*.field')
    .optional()
    .isIn(['subject', 'description', 'requesterEmail', 'senderDomain'])
    .withMessage('Invalid auto rule field'),
  body('autoRules.*.operator')
    .optional()
    .isIn(['contains', 'starts_with', 'ends_with', 'equals', 'regex'])
    .withMessage('Invalid auto rule operator'),
  body('autoRules.*.value')
    .optional()
    .notEmpty()
    .withMessage('Auto rule value is required'),
  body('autoRules.*.priority')
    .optional()
    .isInt()
    .withMessage('Auto rule priority must be an integer'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('sortOrder')
    .optional()
    .isInt()
    .withMessage('Sort order must be an integer')
];

// Middleware to check ticket manager permissions
const requireTicketManagerOrAdmin = (req, res, next) => {
  if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
    return res.status(403).json({ message: 'Requires admin or ticket_manager role' });
  }
  next();
};

// Get all categories
// GET /api/ticket-categories
router.get('/', isAuthenticated, ticketCategoryController.getAllCategories);

// Get category hierarchy
// GET /api/ticket-categories/hierarchy
router.get('/hierarchy', isAuthenticated, ticketCategoryController.getCategoryHierarchy);

// Get category by ID
// GET /api/ticket-categories/:id
router.get('/:id', isAuthenticated, ticketCategoryController.getCategoryById);

// Create a new category (admin or ticket manager only)
// POST /api/ticket-categories
router.post('/', isAuthenticated, requireTicketManagerOrAdmin, validateCategoryCreation, ticketCategoryController.createCategory);

// Update a category (admin or ticket manager only)
// PUT /api/ticket-categories/:id
router.put('/:id', isAuthenticated, requireTicketManagerOrAdmin, validateCategoryUpdate, ticketCategoryController.updateCategory);

// Delete a category (admin only)
// DELETE /api/ticket-categories/:id
router.delete('/:id', isAuthenticated, isAdmin, ticketCategoryController.deleteCategory);

module.exports = router;