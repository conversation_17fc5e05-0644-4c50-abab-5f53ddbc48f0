const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Mock audit log storage - in production this would use a dedicated audit database
let auditLogs = [];

/**
 * Audit Log Routes
 * Implements security audit logging for BMS actions
 * Tracks critical actions, user permissions, and system access
 */

// @route   POST /api/audit/actions
// @desc    Log a critical BMS action
// @access  Authenticated
router.post('/actions', isAuthenticated, async (req, res) => {
  try {
    const {
      action,
      entityType,
      entityId,
      userId,
      details,
      timestamp,
      ipAddress,
      userAgent,
      sessionId,
      emergencyMode,
      building,
      floor
    } = req.body;

    // Validate required fields
    if (!action || !entityType || !userId) {
      return res.status(400).json({ 
        message: 'Missing required fields: action, entityType, userId' 
      });
    }

    // Create audit log entry
    const auditEntry = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      action,
      entityType,
      entityId,
      userId: userId || req.user.id,
      userEmail: req.user.email,
      userName: req.user.name,
      details: details || {},
      timestamp: timestamp || new Date().toISOString(),
      ipAddress: ipAddress || req.ip,
      userAgent: userAgent || req.get('User-Agent'),
      sessionId: sessionId || req.sessionID,
      emergencyMode: emergencyMode || false,
      building: building || null,
      floor: floor || null,
      severity: determineSeverity(action, emergencyMode),
      createdAt: new Date()
    };

    // Store audit log (in production, this would go to a dedicated audit database)
    auditLogs.push(auditEntry);

    // Keep only last 10000 entries in memory (production would use proper persistence)
    if (auditLogs.length > 10000) {
      auditLogs = auditLogs.slice(-10000);
    }

    // Log to console for immediate monitoring
    console.log(`AUDIT LOG: ${action} by ${req.user.email} on ${entityType}:${entityId}`, {
      emergency: emergencyMode,
      details: details
    });

    res.status(201).json({
      id: auditEntry.id,
      message: 'Action logged successfully',
      timestamp: auditEntry.timestamp
    });
  } catch (error) {
    console.error('Error logging audit action:', error);
    res.status(500).json({ message: 'Failed to log audit action' });
  }
});

// @route   GET /api/audit/entities/:entityType/:entityId
// @desc    Get audit logs for a specific entity
// @access  Authenticated
router.get('/entities/:entityType/:entityId', isAuthenticated, async (req, res) => {
  try {
    const { entityType, entityId } = req.params;
    const { 
      page = 1, 
      limit = 50, 
      startDate, 
      endDate, 
      action, 
      userId 
    } = req.query;

    let filteredLogs = auditLogs.filter(log => 
      log.entityType === entityType && log.entityId === entityId
    );

    // Apply filters
    if (startDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) <= new Date(endDate)
      );
    }

    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action.includes(action));
    }

    if (userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === userId);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    res.json({
      logs: paginatedLogs,
      total: filteredLogs.length,
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: endIndex < filteredLogs.length
    });
  } catch (error) {
    console.error('Error fetching entity audit logs:', error);
    res.status(500).json({ message: 'Failed to fetch audit logs' });
  }
});

// @route   GET /api/audit/buildings/:buildingId
// @desc    Get audit logs for a building
// @access  Authenticated
router.get('/buildings/:buildingId', isAuthenticated, async (req, res) => {
  try {
    const { buildingId } = req.params;
    const { 
      page = 1, 
      limit = 100, 
      startDate, 
      endDate, 
      action, 
      userId, 
      emergencyOnly 
    } = req.query;

    let filteredLogs = auditLogs.filter(log => log.building === buildingId);

    // Apply filters
    if (emergencyOnly === 'true') {
      filteredLogs = filteredLogs.filter(log => log.emergencyMode === true);
    }

    if (startDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) <= new Date(endDate)
      );
    }

    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action.includes(action));
    }

    if (userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === userId);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    res.json({
      logs: paginatedLogs,
      total: filteredLogs.length,
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: endIndex < filteredLogs.length
    });
  } catch (error) {
    console.error('Error fetching building audit logs:', error);
    res.status(500).json({ message: 'Failed to fetch building audit logs' });
  }
});

// @route   GET /api/audit/emergency
// @desc    Get emergency action logs
// @access  Authenticated
router.get('/emergency', isAuthenticated, async (req, res) => {
  try {
    const { 
      buildingId, 
      page = 1, 
      limit = 50, 
      startDate, 
      endDate 
    } = req.query;

    let filteredLogs = auditLogs.filter(log => log.emergencyMode === true);

    if (buildingId) {
      filteredLogs = filteredLogs.filter(log => log.building === buildingId);
    }

    if (startDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) <= new Date(endDate)
      );
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    res.json({
      logs: paginatedLogs,
      total: filteredLogs.length,
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: endIndex < filteredLogs.length
    });
  } catch (error) {
    console.error('Error fetching emergency logs:', error);
    res.status(500).json({ message: 'Failed to fetch emergency logs' });
  }
});

// @route   GET /api/audit/users/:userId
// @desc    Get user activity summary
// @access  Admin or self
router.get('/users/:userId', isAuthenticated, async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Users can only view their own logs unless they're admin
    if (req.user.id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { 
      page = 1, 
      limit = 50, 
      startDate, 
      endDate, 
      buildingId 
    } = req.query;

    let filteredLogs = auditLogs.filter(log => log.userId === userId);

    if (buildingId) {
      filteredLogs = filteredLogs.filter(log => log.building === buildingId);
    }

    if (startDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) <= new Date(endDate)
      );
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Generate activity summary
    const summary = generateActivitySummary(filteredLogs);

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    res.json({
      logs: paginatedLogs,
      summary,
      total: filteredLogs.length,
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: endIndex < filteredLogs.length
    });
  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({ message: 'Failed to fetch user activity' });
  }
});

// @route   GET /api/audit/export
// @desc    Export audit logs
// @access  Admin
router.get('/export', isAdmin, async (req, res) => {
  try {
    const { 
      format = 'csv', 
      buildingId, 
      startDate, 
      endDate, 
      action, 
      emergencyOnly 
    } = req.query;

    let filteredLogs = [...auditLogs];

    // Apply filters
    if (buildingId) {
      filteredLogs = filteredLogs.filter(log => log.building === buildingId);
    }

    if (emergencyOnly === 'true') {
      filteredLogs = filteredLogs.filter(log => log.emergencyMode === true);
    }

    if (startDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) >= new Date(startDate)
      );
    }

    if (endDate) {
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp) <= new Date(endDate)
      );
    }

    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action.includes(action));
    }

    // Sort by timestamp
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    if (format === 'csv') {
      const csv = generateCSV(filteredLogs);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=audit_log_${new Date().toISOString().split('T')[0]}.csv`);
      res.send(csv);
    } else if (format === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename=audit_log_${new Date().toISOString().split('T')[0]}.json`);
      res.json(filteredLogs);
    } else {
      res.status(400).json({ message: 'Unsupported export format' });
    }
  } catch (error) {
    console.error('Error exporting audit logs:', error);
    res.status(500).json({ message: 'Failed to export audit logs' });
  }
});

// @route   GET /api/audit/stats
// @desc    Get real-time audit log statistics
// @access  Authenticated
router.get('/stats', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, timeRange = '24h' } = req.query;

    // Calculate time range
    const now = new Date();
    const timeRangeMs = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000
    };

    const startTime = new Date(now.getTime() - (timeRangeMs[timeRange] || timeRangeMs['24h']));

    let filteredLogs = auditLogs.filter(log => 
      new Date(log.timestamp) >= startTime
    );

    if (buildingId) {
      filteredLogs = filteredLogs.filter(log => log.building === buildingId);
    }

    const stats = {
      totalActions: filteredLogs.length,
      emergencyActions: filteredLogs.filter(log => log.emergencyMode).length,
      criticalActions: filteredLogs.filter(log => log.severity === 'critical').length,
      uniqueUsers: new Set(filteredLogs.map(log => log.userId)).size,
      actionsByType: getActionsByType(filteredLogs),
      actionsByHour: getActionsByHour(filteredLogs),
      topUsers: getTopUsers(filteredLogs),
      recentCritical: filteredLogs
        .filter(log => log.severity === 'critical')
        .slice(0, 5)
        .map(log => ({
          action: log.action,
          user: log.userName,
          timestamp: log.timestamp,
          entityType: log.entityType
        }))
    };

    res.json(stats);
  } catch (error) {
    console.error('Error generating audit stats:', error);
    res.status(500).json({ message: 'Failed to generate audit statistics' });
  }
});

// @route   POST /api/audit/sync
// @desc    Sync local audit logs to server
// @access  Authenticated
router.post('/sync', isAuthenticated, async (req, res) => {
  try {
    const { logs } = req.body;

    if (!Array.isArray(logs)) {
      return res.status(400).json({ message: 'Logs must be an array' });
    }

    let syncedCount = 0;
    const errors = [];

    for (const log of logs) {
      try {
        // Validate and add to audit logs
        const auditEntry = {
          ...log,
          id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userId: log.userId || req.user.id,
          userEmail: req.user.email,
          userName: req.user.name,
          synced: true,
          syncedAt: new Date().toISOString()
        };

        auditLogs.push(auditEntry);
        syncedCount++;
      } catch (error) {
        errors.push({ log, error: error.message });
      }
    }

    res.json({
      synced: syncedCount,
      errors: errors.length,
      errorDetails: errors
    });
  } catch (error) {
    console.error('Error syncing audit logs:', error);
    res.status(500).json({ message: 'Failed to sync audit logs' });
  }
});

// Helper functions
function determineSeverity(action, emergencyMode) {
  if (emergencyMode) return 'critical';
  
  const criticalActions = [
    'door_unlock', 'door_lock', 'emergency_activate', 'panel_edit', 
    'safety_override', 'utility_shutoff'
  ];
  
  const warningActions = [
    'camera_access', 'electrical_trace', 'safety_inspect'
  ];

  if (criticalActions.some(a => action.includes(a))) return 'critical';
  if (warningActions.some(a => action.includes(a))) return 'warning';
  return 'info';
}

function generateActivitySummary(logs) {
  const totalActions = logs.length;
  const emergencyActions = logs.filter(log => log.emergencyMode).length;
  const actionTypes = {};
  
  logs.forEach(log => {
    actionTypes[log.action] = (actionTypes[log.action] || 0) + 1;
  });

  return {
    totalActions,
    emergencyActions,
    mostCommonAction: Object.keys(actionTypes).reduce((a, b) => 
      actionTypes[a] > actionTypes[b] ? a : b, ''
    ),
    actionTypes
  };
}

function generateCSV(logs) {
  const headers = [
    'Timestamp', 'Action', 'Entity Type', 'Entity ID', 'User', 'Emergency', 
    'Building', 'Floor', 'IP Address', 'Details'
  ];

  const rows = logs.map(log => [
    log.timestamp,
    log.action,
    log.entityType,
    log.entityId,
    log.userEmail,
    log.emergencyMode,
    log.building,
    log.floor,
    log.ipAddress,
    JSON.stringify(log.details)
  ]);

  return [headers, ...rows]
    .map(row => row.map(field => `"${field || ''}"`).join(','))
    .join('\n');
}

function getActionsByType(logs) {
  const actionTypes = {};
  logs.forEach(log => {
    const type = log.action.split('_')[0];
    actionTypes[type] = (actionTypes[type] || 0) + 1;
  });
  return actionTypes;
}

function getActionsByHour(logs) {
  const hourlyActions = {};
  logs.forEach(log => {
    const hour = new Date(log.timestamp).getHours();
    hourlyActions[hour] = (hourlyActions[hour] || 0) + 1;
  });
  return hourlyActions;
}

function getTopUsers(logs) {
  const userActions = {};
  logs.forEach(log => {
    userActions[log.userEmail] = (userActions[log.userEmail] || 0) + 1;
  });
  
  return Object.entries(userActions)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([email, count]) => ({ email, count }));
}

module.exports = router;