const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../../middleware/auth');
const buildingManagementController = require('../../controllers/buildingManagementController');

// @desc    Get building management system status
// @route   GET /api/building-management/status
// @access  Private
router.get('/status', isAuthenticated, buildingManagementController.getSystemStatus);

// @desc    Get all doors
// @route   GET /api/building-management/doors
// @access  Private
router.get('/doors', isAuthenticated, buildingManagementController.getDoors);

// @desc    Control a door (lock, unlock, passage mode)
// @route   POST /api/building-management/doors/:doorId/control
// @access  Private/Admin
router.post('/doors/:doorId/control', isAuthenticated, isAdmin, buildingManagementController.controlDoor);

// @desc    Get all climate devices
// @route   GET /api/building-management/climate-devices
// @access  Private
router.get('/climate-devices', isAuthenticated, buildingManagementController.getClimateDevices);

// @desc    Get all cameras
// @route   GET /api/building-management/cameras
// @access  Private
router.get('/cameras', isAuthenticated, buildingManagementController.getCameras);

// @desc    Get all network devices
// @route   GET /api/building-management/network-devices
// @access  Private
router.get('/network-devices', isAuthenticated, buildingManagementController.getNetworkDevices);

// @desc    Get building management settings
// @route   GET /api/building-management/settings
// @access  Private
router.get('/settings', isAuthenticated, buildingManagementController.getSettings);

// @desc    Save building management settings
// @route   POST /api/building-management/settings
// @access  Private/Admin
router.post('/settings', isAuthenticated, isAdmin, buildingManagementController.saveSettings);

// @desc    Get automation rules
// @route   GET /api/building-management/automation/rules
// @access  Private
router.get('/automation/rules', isAuthenticated, buildingManagementController.getAutomationRules);

// @desc    Save an automation rule
// @route   POST /api/building-management/automation/rules
// @access  Private/Admin
router.post('/automation/rules', isAuthenticated, isAdmin, buildingManagementController.saveAutomationRule);

// @desc    Update an automation rule
// @route   PUT /api/building-management/automation/rules/:ruleId
// @access  Private/Admin
router.put('/automation/rules/:ruleId', isAuthenticated, isAdmin, buildingManagementController.updateAutomationRule);

// @desc    Delete an automation rule
// @route   DELETE /api/building-management/automation/rules/:ruleId
// @access  Private/Admin
router.delete('/automation/rules/:ruleId', isAuthenticated, isAdmin, buildingManagementController.deleteAutomationRule);

// @desc    Get historical data
// @route   GET /api/building-management/historical-data
// @access  Private
router.get('/historical-data', isAuthenticated, buildingManagementController.getHistoricalData);

// @desc    Get detailed integration status
// @route   GET /api/building-management/integrations/status
// @access  Private
router.get('/integrations/status', isAuthenticated, buildingManagementController.getIntegrationStatus);

module.exports = router;