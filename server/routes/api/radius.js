const express = require('express');
const router = express.Router();
const RadiusAPI = require('../../integrations/radius/radiusAPI');
const { isAuthenticated, isAdmin } = require('../../../middleware/auth');
const config = require('../../../config');
const path = require('path');

// Create a config object for the RADIUS API
const getRadiusConfig = (req) => {
  // Get user-specific tokens if available
  const userTokens = req.user && req.user.tokens ? req.user.tokens.radius : null;
  const userId = req.user ? req.user.id : null;

  return {
    radiusServerUrl: process.env.RADIUS_SERVER_URL || 'http://localhost:1812',
    authType: process.env.RADIUS_AUTH_TYPE || 'google_api', // 'google_saml' or 'google_api'

    // Google API config
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    googleClientSecret: process.env.GOOGLE_CLIENT_SECRET,
    googleRedirectUri: process.env.GOOGLE_REDIRECT_URI,
    googleTokenPath: path.join(config.tokenDir, 'radius_token.json'),

    // Google SAML config
    samlConfig: {
      entryPoint: process.env.RADIUS_SAML_ENTRY_POINT,
      issuer: process.env.RADIUS_SAML_ISSUER,
      cert: process.env.RADIUS_SAML_CERT,
      privateKey: process.env.RADIUS_SAML_PRIVATE_KEY
    },

    // User-specific tokens if available
    userTokens,
    userId
  };
};

// Initialize RADIUS API instance
const getRadiusAPI = (req) => {
  const radiusConfig = getRadiusConfig(req);
  return new RadiusAPI(radiusConfig);
};

// Get RADIUS server status
router.get('/status', async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const status = await radiusAPI.getServerStatus();
    res.json(status);
  } catch (error) {
    console.error('Error getting RADIUS server status:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get RADIUS server configuration
router.get('/config', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const config = await radiusAPI.getServerConfig();
    res.json(config);
  } catch (error) {
    console.error('Error getting RADIUS server configuration:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update RADIUS server configuration
router.post('/config', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const updatedConfig = await radiusAPI.updateServerConfig(req.body);
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error updating RADIUS server configuration:', error);
    res.status(500).json({ error: error.message });
  }
});

// Configure RADIUS with Google SAML
router.post('/configure-saml', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const result = await radiusAPI.configureSaml(req.body.samlConfig);
    res.json(result);
  } catch (error) {
    console.error('Error configuring RADIUS with SAML:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get Google auth URL (for Google API auth)
router.get('/google-auth-url', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    const authUrl = radiusAPI.getGoogleAuthUrl();
    res.json({ authUrl });
  } catch (error) {
    console.error('Error getting Google auth URL:', error);
    res.status(500).json({ error: error.message });
  }
});

// Handle Google auth callback (for Google API auth)
router.post('/google-auth-callback', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { code } = req.body;
    if (!code) {
      return res.status(400).json({ error: 'Authorization code is required' });
    }

    const radiusAPI = getRadiusAPI(req);
    const tokens = await radiusAPI.getGoogleToken(code);

    res.json({ success: true, message: 'Successfully authenticated with Google' });
  } catch (error) {
    console.error('Error handling Google auth callback:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get RADIUS clients
router.get('/clients', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const clients = await radiusAPI.getClients();
    res.json(clients);
  } catch (error) {
    console.error('Error getting RADIUS clients:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add a new RADIUS client
router.post('/clients', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const client = await radiusAPI.addClient(req.body);
    res.json(client);
  } catch (error) {
    console.error('Error adding RADIUS client:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update a RADIUS client
router.put('/clients/:clientId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { clientId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const client = await radiusAPI.updateClient(clientId, req.body);
    res.json(client);
  } catch (error) {
    console.error('Error updating RADIUS client:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a RADIUS client
router.delete('/clients/:clientId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { clientId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const result = await radiusAPI.deleteClient(clientId);
    res.json(result);
  } catch (error) {
    console.error('Error deleting RADIUS client:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get authentication logs
router.get('/logs/auth', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const logs = await radiusAPI.getAuthLogs(req.query);
    res.json(logs);
  } catch (error) {
    console.error('Error getting RADIUS authentication logs:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get VLAN configurations
router.get('/vlans', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const vlans = await radiusAPI.getVlanConfigs();
    res.json(vlans);
  } catch (error) {
    console.error('Error getting VLAN configurations:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add a new VLAN configuration
router.post('/vlans', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const vlan = await radiusAPI.addVlanConfig(req.body);
    res.json(vlan);
  } catch (error) {
    console.error('Error adding VLAN configuration:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update a VLAN configuration
router.put('/vlans/:vlanId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { vlanId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const vlan = await radiusAPI.updateVlanConfig(vlanId, req.body);
    res.json(vlan);
  } catch (error) {
    console.error('Error updating VLAN configuration:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a VLAN configuration
router.delete('/vlans/:vlanId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { vlanId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const result = await radiusAPI.deleteVlanConfig(vlanId);
    res.json(result);
  } catch (error) {
    console.error('Error deleting VLAN configuration:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get group-to-VLAN mappings
router.get('/group-vlan-mappings', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const mappings = await radiusAPI.getGroupVlanMappings();
    res.json(mappings);
  } catch (error) {
    console.error('Error getting group-to-VLAN mappings:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add a new group-to-VLAN mapping
router.post('/group-vlan-mappings', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const mapping = await radiusAPI.addGroupVlanMapping(req.body);
    res.json(mapping);
  } catch (error) {
    console.error('Error adding group-to-VLAN mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update a group-to-VLAN mapping
router.put('/group-vlan-mappings/:mappingId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { mappingId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const mapping = await radiusAPI.updateGroupVlanMapping(mappingId, req.body);
    res.json(mapping);
  } catch (error) {
    console.error('Error updating group-to-VLAN mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a group-to-VLAN mapping
router.delete('/group-vlan-mappings/:mappingId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { mappingId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const result = await radiusAPI.deleteGroupVlanMapping(mappingId);
    res.json(result);
  } catch (error) {
    console.error('Error deleting group-to-VLAN mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user-to-VLAN mappings
router.get('/user-vlan-mappings', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const mappings = await radiusAPI.getUserVlanMappings();
    res.json(mappings);
  } catch (error) {
    console.error('Error getting user-to-VLAN mappings:', error);
    res.status(500).json({ error: error.message });
  }
});

// Add a new user-to-VLAN mapping
router.post('/user-vlan-mappings', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const mapping = await radiusAPI.addUserVlanMapping(req.body);
    res.json(mapping);
  } catch (error) {
    console.error('Error adding user-to-VLAN mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update a user-to-VLAN mapping
router.put('/user-vlan-mappings/:mappingId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { mappingId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const mapping = await radiusAPI.updateUserVlanMapping(mappingId, req.body);
    res.json(mapping);
  } catch (error) {
    console.error('Error updating user-to-VLAN mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Delete a user-to-VLAN mapping
router.delete('/user-vlan-mappings/:mappingId', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { mappingId } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const result = await radiusAPI.deleteUserVlanMapping(mappingId);
    res.json(result);
  } catch (error) {
    console.error('Error deleting user-to-VLAN mapping:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get VLAN for a user
router.get('/users/:username/vlan', isAuthenticated, async (req, res) => {
  try {
    const { username } = req.params;
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const vlan = await radiusAPI.getUserVlan(username);
    res.json(vlan);
  } catch (error) {
    console.error('Error getting user VLAN:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test RADIUS server connection
router.post('/test-connection', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    await radiusAPI.initialize();

    const isAuthenticated = await radiusAPI.isAuthenticated();

    if (isAuthenticated) {
      res.json({ success: true, message: 'Successfully connected to RADIUS server' });
    } else {
      res.status(401).json({ success: false, message: 'Failed to authenticate with RADIUS server' });
    }
  } catch (error) {
    console.error('Error testing RADIUS server connection:', error);
    res.status(500).json({ error: error.message });
  }
});

// Check if RADIUS server is running locally
router.get('/is-local', async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);

    // Check if the radiusAPI has a reference to a local RADIUS server
    const isLocal = !!radiusAPI.radiusServer;
    const isRunning = isLocal && radiusAPI.radiusServer.isRunning();
    
    // Get Google Workspace authentication status if available
    let googleWorkspaceStatus = null;
    if (isLocal && radiusAPI.radiusServer.googleWorkspaceAuth) {
      googleWorkspaceStatus = radiusAPI.radiusServer.googleWorkspaceAuth.getConfigStatus();
    }

    res.json({
      isLocal,
      isRunning,
      serverUrl: radiusAPI.config.radiusServerUrl,
      googleWorkspaceAuth: googleWorkspaceStatus
    });
  } catch (error) {
    console.error('Error checking if RADIUS server is local:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get Google Workspace authentication status
router.get('/google-workspace-status', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const radiusAPI = getRadiusAPI(req);
    
    // Check if we have a local RADIUS server with Google Workspace auth
    if (!radiusAPI.radiusServer || !radiusAPI.radiusServer.googleWorkspaceAuth) {
      return res.json({
        configured: false,
        message: 'Google Workspace authentication not available'
      });
    }
    
    const status = radiusAPI.radiusServer.googleWorkspaceAuth.getConfigStatus();
    const isAuthenticated = await radiusAPI.radiusServer.googleWorkspaceAuth.isAuthenticated();
    
    res.json({
      ...status,
      isAuthenticated,
      message: isAuthenticated ? 'Google Workspace authentication is working' : 'Google Workspace authentication needs setup'
    });
  } catch (error) {
    console.error('Error getting Google Workspace status:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
