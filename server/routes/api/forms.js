const express = require('express');
const router = express.Router();
const formController = require('../../controllers/formController');
const formSubmissionController = require('../../controllers/formSubmissionController');
const { isAuthenticated, hasPermission } = require('../../../middleware/auth');

/**
 * Form Routes
 * All routes in this file require authentication
 */

// Form CRUD operations
router.post('/', 
  isAuthenticated, 
  hasPermission('forms_create'), 
  formController.createForm
);

router.get('/', 
  isAuthenticated, 
  formController.getForms
);

router.get('/:id', 
  isAuthenticated, 
  formController.getForm
);

router.put('/:id', 
  isAuthenticated, 
  formController.updateForm
);

router.delete('/:id', 
  isAuthenticated, 
  formController.deleteForm
);

// Form duplication
router.post('/:id/duplicate', 
  isAuthenticated, 
  hasPermission('forms_create'), 
  formController.duplicateForm
);

// Form statistics
router.get('/:id/stats', 
  isAuthenticated, 
  formController.getFormStats
);

// Form submissions
router.post('/:formId/submissions', 
  isAuthenticated, 
  formSubmissionController.uploadFiles(),
  formSubmissionController.submitForm
);

router.get('/:formId/submissions', 
  isAuthenticated, 
  formSubmissionController.getFormSubmissions
);

router.get('/submissions/:id', 
  isAuthenticated, 
  formSubmissionController.getSubmission
);

router.put('/submissions/:id', 
  isAuthenticated, 
  formSubmissionController.updateSubmission
);

router.delete('/submissions/:id', 
  isAuthenticated, 
  formSubmissionController.deleteSubmission
);

// Form submission files
router.get('/submissions/files/:filename', 
  isAuthenticated, 
  formSubmissionController.getSubmissionFile
);

module.exports = router;