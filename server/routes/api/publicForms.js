const express = require('express');
const router = express.Router();
const formController = require('../../controllers/formController');
const formSubmissionController = require('../../controllers/formSubmissionController');

/**
 * Public Form Routes
 * These routes do not require authentication
 * Only published forms with public access are available
 */

// Get a public form by slug
router.get('/:slug', formController.getPublicForm);

// Submit a public form
router.post('/:slug/submit', 
  formSubmissionController.uploadFiles(),
  formSubmissionController.submitPublicForm
);

// Get a public submission file
router.get('/submissions/files/:filename', 
  formSubmissionController.getPublicSubmissionFile
);

module.exports = router;