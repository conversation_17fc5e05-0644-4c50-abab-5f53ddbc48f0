const express = require('express');
const router = express.Router();
const UtilityShutoff = require('../../models/UtilityShutoff');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

/**
 * Phase 6 - Water & Gas Shutoffs API Routes
 * Provides comprehensive CRUD operations and emergency management
 */

// ===== Utility Shutoff Management =====

/**
 * GET /api/utility-shutoffs
 * Get all utility shutoffs with filtering and pagination
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const {
      buildingId,
      floorId,
      shutoffType,
      utilityType,
      priority,
      status,
      search,
      includeInactive = 'false',
      page = 1,
      limit = 50
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (buildingId) filter.buildingId = buildingId;
    if (floorId) filter.floorId = floorId;
    if (shutoffType) filter.shutoffType = shutoffType;
    if (priority) filter['coverage.priority'] = priority;
    if (status) filter['status.currentState'] = status;
    if (includeInactive === 'false') filter.isActive = true;

    // Handle utility type filter (water, gas, steam, etc.)
    if (utilityType) {
      filter.shutoffType = { $regex: `^${utilityType}`, $options: 'i' };
    }

    // Handle search
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { shutoffId: { $regex: search, $options: 'i' } },
        { room: { $regex: search, $options: 'i' } },
        { 'coverage.description': { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [shutoffs, total] = await Promise.all([
      UtilityShutoff.find(filter)
        .populate('buildingId', 'name code')
        .populate('floorId', 'name level')
        .populate('integration.iconId')
        .sort({ 'coverage.priority': -1, shutoffType: 1, name: 1 })
        .skip(skip)
        .limit(parseInt(limit)),
      UtilityShutoff.countDocuments(filter)
    ]);

    res.json({
      shutoffs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching utility shutoffs:', error);
    res.status(500).json({ error: 'Failed to fetch utility shutoffs' });
  }
});

/**
 * GET /api/utility-shutoffs/:id
 * Get specific utility shutoff details
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const shutoff = await UtilityShutoff.findById(req.params.id)
      .populate('buildingId', 'name code address')
      .populate('floorId', 'name level')
      .populate('integration.iconId')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    if (!shutoff) {
      return res.status(404).json({ error: 'Utility shutoff not found' });
    }

    res.json(shutoff);
  } catch (error) {
    console.error('Error fetching utility shutoff:', error);
    res.status(500).json({ error: 'Failed to fetch utility shutoff' });
  }
});

/**
 * POST /api/utility-shutoffs
 * Create new utility shutoff
 */
router.post('/', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const shutoffData = {
      ...req.body,
      createdBy: req.user.id,
      updatedBy: req.user.id
    };

    // Validate required fields
    const required = ['shutoffId', 'name', 'buildingId', 'floorId', 'shutoffType'];
    for (const field of required) {
      if (!shutoffData[field]) {
        return res.status(400).json({ error: `${field} is required` });
      }
    }

    // Check for duplicate shutoffId
    const existingShutoff = await UtilityShutoff.findOne({ 
      shutoffId: shutoffData.shutoffId 
    });
    if (existingShutoff) {
      return res.status(409).json({ error: 'Shutoff ID already exists' });
    }

    const shutoff = new UtilityShutoff(shutoffData);
    await shutoff.save();

    const populatedShutoff = await UtilityShutoff.findById(shutoff._id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name level');

    res.status(201).json(populatedShutoff);
  } catch (error) {
    console.error('Error creating utility shutoff:', error);
    if (error.code === 11000) {
      res.status(409).json({ error: 'Shutoff ID must be unique' });
    } else {
      res.status(400).json({ error: error.message });
    }
  }
});

/**
 * PUT /api/utility-shutoffs/:id
 * Update utility shutoff
 */
router.put('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user.id
    };

    // Remove fields that shouldn't be updated
    delete updateData._id;
    delete updateData.createdBy;
    delete updateData.createdAt;

    const shutoff = await UtilityShutoff.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('buildingId', 'name code')
     .populate('floorId', 'name level');

    if (!shutoff) {
      return res.status(404).json({ error: 'Utility shutoff not found' });
    }

    res.json(shutoff);
  } catch (error) {
    console.error('Error updating utility shutoff:', error);
    res.status(400).json({ error: error.message });
  }
});

/**
 * DELETE /api/utility-shutoffs/:id
 * Delete utility shutoff (soft delete by default)
 */
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { force = 'false' } = req.query;

    if (force === 'true') {
      // Hard delete
      const shutoff = await UtilityShutoff.findByIdAndDelete(req.params.id);
      if (!shutoff) {
        return res.status(404).json({ error: 'Utility shutoff not found' });
      }
    } else {
      // Soft delete
      const shutoff = await UtilityShutoff.findByIdAndUpdate(
        req.params.id,
        { isActive: false, updatedBy: req.user.id },
        { new: true }
      );
      if (!shutoff) {
        return res.status(404).json({ error: 'Utility shutoff not found' });
      }
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting utility shutoff:', error);
    res.status(500).json({ error: 'Failed to delete utility shutoff' });
  }
});

// ===== Location-based Queries =====

/**
 * GET /api/utility-shutoffs/building/:buildingId/floor/:floorId
 * Get shutoffs for specific building and floor
 */
router.get('/building/:buildingId/floor/:floorId', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;
    const { includePositioned = 'false' } = req.query;

    let filter = { buildingId, floorId, isActive: true };
    
    // Only include shutoffs with position data if requested
    if (includePositioned === 'true') {
      filter['position.x'] = { $exists: true, $ne: null };
      filter['position.y'] = { $exists: true, $ne: null };
    }

    const shutoffs = await UtilityShutoff.findByLocation(buildingId, floorId);
    
    res.json(shutoffs);
  } catch (error) {
    console.error('Error fetching shutoffs by location:', error);
    res.status(500).json({ error: 'Failed to fetch shutoffs by location' });
  }
});

/**
 * GET /api/utility-shutoffs/type/:shutoffType
 * Get shutoffs by type (water_main, gas_main, etc.)
 */
router.get('/type/:shutoffType', isAuthenticated, async (req, res) => {
  try {
    const { shutoffType } = req.params;
    const shutoffs = await UtilityShutoff.findByType(shutoffType);
    res.json(shutoffs);
  } catch (error) {
    console.error('Error fetching shutoffs by type:', error);
    res.status(500).json({ error: 'Failed to fetch shutoffs by type' });
  }
});

/**
 * GET /api/utility-shutoffs/emergency/critical
 * Get critical emergency shutoffs
 */
router.get('/emergency/critical', isAuthenticated, async (req, res) => {
  try {
    const criticalShutoffs = await UtilityShutoff.findCriticalShutoffs();
    res.json(criticalShutoffs);
  } catch (error) {
    console.error('Error fetching critical shutoffs:', error);
    res.status(500).json({ error: 'Failed to fetch critical shutoffs' });
  }
});

// ===== Maintenance and Inspection =====

/**
 * GET /api/utility-shutoffs/inspections/due
 * Get shutoffs with overdue or upcoming inspections
 */
router.get('/inspections/due', isAuthenticated, async (req, res) => {
  try {
    const { daysAhead = 30 } = req.query;
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + parseInt(daysAhead));

    const [overdue, upcoming] = await Promise.all([
      UtilityShutoff.findOverdueInspections(),
      UtilityShutoff.find({
        'maintenance.nextInspection': { 
          $gte: today, 
          $lte: futureDate 
        },
        isActive: true
      }).populate('buildingId floorId')
    ]);

    res.json({
      overdue,
      upcoming,
      summary: {
        overdueCount: overdue.length,
        upcomingCount: upcoming.length,
        totalRequiringAttention: overdue.length + upcoming.length
      }
    });
  } catch (error) {
    console.error('Error fetching inspection schedule:', error);
    res.status(500).json({ error: 'Failed to fetch inspection schedule' });
  }
});

/**
 * POST /api/utility-shutoffs/:id/inspection
 * Record inspection completion
 */
router.post('/:id/inspection', isAuthenticated, async (req, res) => {
  try {
    const { notes, condition } = req.body;
    const inspector = req.user.name || req.user.email;

    const shutoff = await UtilityShutoff.findById(req.params.id);
    if (!shutoff) {
      return res.status(404).json({ error: 'Utility shutoff not found' });
    }

    // Update condition if provided
    if (condition) {
      shutoff.status.condition = condition;
    }

    await shutoff.recordInspection(inspector, notes);

    const updatedShutoff = await UtilityShutoff.findById(req.params.id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name level');

    res.json(updatedShutoff);
  } catch (error) {
    console.error('Error recording inspection:', error);
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/utility-shutoffs/:id/emergency-use
 * Record emergency shutoff usage
 */
router.post('/:id/emergency-use', isAuthenticated, async (req, res) => {
  try {
    const { reason, newState = 'closed' } = req.body;
    const usedBy = req.user.name || req.user.email;

    if (!reason) {
      return res.status(400).json({ error: 'Reason for emergency use is required' });
    }

    const shutoff = await UtilityShutoff.findById(req.params.id);
    if (!shutoff) {
      return res.status(404).json({ error: 'Utility shutoff not found' });
    }

    await shutoff.recordEmergencyUse(usedBy, reason);

    const updatedShutoff = await UtilityShutoff.findById(req.params.id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name level');

    res.json(updatedShutoff);
  } catch (error) {
    console.error('Error recording emergency use:', error);
    res.status(400).json({ error: error.message });
  }
});

// ===== Emergency Response =====

/**
 * GET /api/utility-shutoffs/emergency/nearest
 * Find nearest shutoffs to a location for emergency response
 */
router.get('/emergency/nearest', isAuthenticated, async (req, res) => {
  try {
    const { 
      buildingId, 
      floorId, 
      x, 
      y, 
      utilityType,
      maxDistance = 50 
    } = req.query;

    if (!buildingId || !floorId) {
      return res.status(400).json({ error: 'buildingId and floorId are required' });
    }

    let filter = { 
      buildingId, 
      floorId, 
      isActive: true,
      'position.x': { $exists: true },
      'position.y': { $exists: true }
    };

    if (utilityType) {
      filter.shutoffType = { $regex: `^${utilityType}`, $options: 'i' };
    }

    const shutoffs = await UtilityShutoff.find(filter)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name level');

    // Calculate distances if x,y coordinates provided
    let shutoffsWithDistance = shutoffs;
    if (x !== undefined && y !== undefined) {
      const targetX = parseFloat(x);
      const targetY = parseFloat(y);

      shutoffsWithDistance = shutoffs.map(shutoff => {
        const dx = shutoff.position.x - targetX;
        const dy = shutoff.position.y - targetY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        return {
          ...shutoff.toObject(),
          distance: parseFloat(distance.toFixed(2))
        };
      }).filter(shutoff => shutoff.distance <= parseFloat(maxDistance))
        .sort((a, b) => a.distance - b.distance);
    }

    // Prioritize critical shutoffs
    const sortedShutoffs = shutoffsWithDistance.sort((a, b) => {
      // First by emergency priority
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const priorityA = priorityOrder[a.coverage?.priority] || 1;
      const priorityB = priorityOrder[b.coverage?.priority] || 1;
      
      if (priorityA !== priorityB) return priorityB - priorityA;
      
      // Then by distance if available
      if (a.distance !== undefined && b.distance !== undefined) {
        return a.distance - b.distance;
      }
      
      return 0;
    });

    res.json({
      shutoffs: sortedShutoffs.slice(0, 10), // Limit to 10 nearest
      searchArea: {
        buildingId,
        floorId,
        center: x !== undefined ? { x: parseFloat(x), y: parseFloat(y) } : null,
        maxDistance: parseFloat(maxDistance),
        utilityType
      }
    });
  } catch (error) {
    console.error('Error finding nearest shutoffs:', error);
    res.status(500).json({ error: 'Failed to find nearest shutoffs' });
  }
});

/**
 * GET /api/utility-shutoffs/emergency/procedures/:id
 * Get emergency procedure information for specific shutoff
 */
router.get('/emergency/procedures/:id', isAuthenticated, async (req, res) => {
  try {
    const shutoff = await UtilityShutoff.findById(req.params.id)
      .select('name shutoffType procedures coverage status position')
      .populate('buildingId', 'name')
      .populate('floorId', 'name');

    if (!shutoff) {
      return res.status(404).json({ error: 'Utility shutoff not found' });
    }

    // Format for emergency response
    const emergencyInfo = {
      shutoff: {
        id: shutoff._id,
        name: shutoff.name,
        type: shutoff.shutoffType,
        location: {
          building: shutoff.buildingId?.name,
          floor: shutoff.floorId?.name,
          room: shutoff.room,
          position: shutoff.position
        },
        priority: shutoff.coverage?.priority,
        currentState: shutoff.status?.currentState
      },
      procedures: shutoff.procedures || {},
      emergencyContacts: shutoff.procedures?.emergencyContact ? [shutoff.procedures.emergencyContact] : [],
      warnings: shutoff.procedures?.warnings || [],
      requiredTools: shutoff.procedures?.specialTools || []
    };

    res.json(emergencyInfo);
  } catch (error) {
    console.error('Error fetching emergency procedures:', error);
    res.status(500).json({ error: 'Failed to fetch emergency procedures' });
  }
});

// ===== Analytics and Reporting =====

/**
 * GET /api/utility-shutoffs/analytics/summary
 * Get analytics summary for shutoffs
 */
router.get('/analytics/summary', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, timeframe = '30' } = req.query;

    let filter = { isActive: true };
    if (buildingId) filter.buildingId = buildingId;

    const [
      totalShutoffs,
      byType,
      byPriority,
      byCondition,
      overdueInspections,
      recentActivity
    ] = await Promise.all([
      // Total count
      UtilityShutoff.countDocuments(filter),
      
      // Group by shutoff type
      UtilityShutoff.aggregate([
        { $match: filter },
        { $group: { _id: '$shutoffType', count: { $sum: 1 } } }
      ]),
      
      // Group by priority
      UtilityShutoff.aggregate([
        { $match: filter },
        { $group: { _id: '$coverage.priority', count: { $sum: 1 } } }
      ]),
      
      // Group by condition
      UtilityShutoff.aggregate([
        { $match: filter },
        { $group: { _id: '$status.condition', count: { $sum: 1 } } }
      ]),
      
      // Overdue inspections
      UtilityShutoff.findOverdueInspections(),
      
      // Recent service activity
      UtilityShutoff.aggregate([
        { $match: filter },
        { $unwind: '$serviceHistory' },
        { 
          $match: { 
            'serviceHistory.date': { 
              $gte: new Date(Date.now() - parseInt(timeframe) * 24 * 60 * 60 * 1000) 
            } 
          } 
        },
        { $group: { _id: '$serviceHistory.type', count: { $sum: 1 } } }
      ])
    ]);

    res.json({
      summary: {
        totalShutoffs,
        overdueInspections: overdueInspections.length,
        needsAttention: overdueInspections.length // Could add other criteria
      },
      distribution: {
        byType: byType.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byPriority: byPriority.reduce((acc, item) => {
          acc[item._id || 'unspecified'] = item.count;
          return acc;
        }, {}),
        byCondition: byCondition.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      },
      activity: {
        recent: recentActivity.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        timeframe: `${timeframe} days`
      }
    });
  } catch (error) {
    console.error('Error generating analytics:', error);
    res.status(500).json({ error: 'Failed to generate analytics' });
  }
});

/**
 * POST /api/utility-shutoffs/seed-demo-data
 * Seed demo utility shutoff data for testing
 */
router.post('/seed-demo-data', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { buildingId, floorId, clearExisting = false } = req.body;

    if (!buildingId || !floorId) {
      return res.status(400).json({ error: 'buildingId and floorId are required' });
    }

    // Clear existing demo data if requested
    if (clearExisting) {
      await UtilityShutoff.deleteMany({ 
        buildingId, 
        floorId,
        shutoffId: { $regex: '^DEMO-' }
      });
    }

    // Demo shutoff data
    const demoShutoffs = [
      {
        shutoffId: 'DEMO-WM-01',
        name: 'Main Water Shutoff',
        buildingId,
        floorId,
        room: 'Mechanical Room',
        position: { x: 15, y: 25 },
        shutoffType: 'water_main',
        specifications: {
          valveType: 'ball_valve',
          size: '2 inch',
          material: 'brass'
        },
        coverage: {
          priority: 'critical',
          affectedAreas: ['Entire Building'],
          description: 'Controls water supply to entire building'
        },
        procedures: {
          shutoffInstructions: '1. Locate valve near water meter\n2. Turn clockwise to close\n3. Verify water is off at fixtures',
          emergencyContact: 'Facilities: (555) 123-4567',
          specialTools: ['Water meter key'],
          warnings: ['Will shut off water to entire building', 'Notify all occupants before shutting off']
        },
        status: {
          currentState: 'open',
          condition: 'good',
          isAccessible: true
        },
        createdBy: req.user.id,
        updatedBy: req.user.id
      },
      {
        shutoffId: 'DEMO-WZ-01',
        name: 'Kitchen Water Zone',
        buildingId,
        floorId,
        room: 'Kitchen',
        position: { x: 45, y: 60 },
        shutoffType: 'water_zone',
        specifications: {
          valveType: 'gate_valve',
          size: '3/4 inch',
          material: 'brass'
        },
        coverage: {
          priority: 'high',
          affectedAreas: ['Kitchen', 'Dishwashing Area'],
          description: 'Controls water to kitchen facilities'
        },
        procedures: {
          shutoffInstructions: '1. Open cabinet under sink\n2. Turn valve clockwise\n3. Check that water stops flowing',
          specialTools: ['None'],
          warnings: ['May affect dishwasher and ice machine']
        },
        status: {
          currentState: 'open',
          condition: 'good',
          isAccessible: true
        },
        createdBy: req.user.id,
        updatedBy: req.user.id
      },
      {
        shutoffId: 'DEMO-GM-01',
        name: 'Main Gas Shutoff',
        buildingId,
        floorId,
        room: 'Mechanical Room',
        position: { x: 20, y: 30 },
        shutoffType: 'gas_main',
        specifications: {
          valveType: 'ball_valve',
          size: '1 inch',
          material: 'steel'
        },
        coverage: {
          priority: 'critical',
          affectedAreas: ['Entire Building'],
          description: 'Emergency gas shutoff for entire building'
        },
        procedures: {
          shutoffInstructions: '1. Use gas meter key\n2. Turn 1/4 turn clockwise\n3. Valve should be perpendicular to pipe',
          emergencyContact: 'Gas Company: (555) 911-GAS1',
          specialTools: ['Gas meter key', 'Pipe wrench'],
          warnings: ['EMERGENCY USE ONLY', 'Call gas company before restoring', 'Ensure no gas leaks before operating']
        },
        status: {
          currentState: 'open',
          condition: 'excellent',
          isAccessible: true
        },
        createdBy: req.user.id,
        updatedBy: req.user.id
      }
    ];

    const createdShutoffs = [];
    for (const shutoffData of demoShutoffs) {
      try {
        const shutoff = new UtilityShutoff(shutoffData);
        await shutoff.save();
        createdShutoffs.push(shutoff);
      } catch (error) {
        console.warn(`Failed to create demo shutoff ${shutoffData.shutoffId}:`, error.message);
      }
    }

    res.json({
      message: `Created ${createdShutoffs.length} demo utility shutoffs`,
      shutoffs: createdShutoffs,
      building: buildingId,
      floor: floorId
    });
  } catch (error) {
    console.error('Error seeding demo data:', error);
    res.status(500).json({ error: 'Failed to seed demo data' });
  }
});

module.exports = router;