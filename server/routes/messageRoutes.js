const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');
const { isAuthenticated } = require('../../middleware/auth');
const checkPermission = require('../../middleware/checkPermission');

// All messaging routes require authentication
router.use(isAuthenticated);

// Conversations
router.get('/conversations', messageController.getConversations);
router.post('/conversations', checkPermission('messages:create'), messageController.getOrCreateConversation);
router.get('/conversations/:conversationId', checkPermission('messages:read'), messageController.getConversation);

// Messages
router.get('/conversations/:conversationId/messages', checkPermission('messages:read'), messageController.getMessages);
router.post('/messages', checkPermission('messages:create'), messageController.sendMessage);
router.post('/conversations/:conversationId/read', checkPermission('messages:read'), messageController.markAsRead);
router.delete('/messages/:messageId', checkPermission('messages:delete'), messageController.deleteMessage);

// Reactions
router.post('/messages/:messageId/reactions', checkPermission('messages:create'), messageController.addReaction);

// Typing indicator
router.post('/conversations/:conversationId/typing', checkPermission('messages:create'), messageController.setTypingStatus);

// User search
router.get('/users/search', checkPermission('messages:read'), messageController.searchUsers);

module.exports = router;