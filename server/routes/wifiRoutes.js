const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const WiFiAccessPoint = require('../../models/WiFiAccessPoint');
const WiFiCoverage = require('../../models/WiFiCoverage');
const Building = require('../../models/Building');
const Floor = require('../../models/Floor');
const unifiNetworkController = require('../controllers/unifiNetworkController');

// Minimal helpers to bridge controller handlers (which expect req/res) to plain function calls
const callUnifiController = async (handler, { params = {}, query = {}, body = {} } = {}) => {
  const req = { params, query, body };
  const captured = { payload: undefined };
  const res = {
    statusCode: 200,
    status(code) { this.statusCode = code; return this; },
    json(payload) { captured.payload = payload; return this; }
  };
  await handler(req, res);
  return { status: res.statusCode || 200, data: captured.payload };
};

const getUnifiDevices = async () => {
  const { status, data } = await callUnifiController(unifiNetworkController.getDevices);
  if (status >= 400) throw new Error((data && (data.error || data.message)) || 'Failed to fetch UniFi devices');
  // Normalize shape to match previous service return
  return { data };
};

const getUnifiDeviceById = async (deviceId) => {
  const { status, data } = await callUnifiController(unifiNetworkController.getDeviceDetails, { params: { id: deviceId } });
  if (status >= 400) throw new Error((data && (data.error || data.message)) || 'Failed to fetch UniFi device');
  return data;
};

/**
 * WiFi Management Routes
 * Part of Phase 5 - Wi-Fi coverage & APs
 * Builds on existing UniFi Network integration
 */

// ===== Access Point Management =====

/**
 * GET /api/wifi/access-points
 * Get all WiFi access points with optional filtering
 */
router.get('/access-points', isAuthenticated, async (req, res) => {
  try {
    const {
      buildingId,
      floorId,
      room,
      ssid,
      status,
      includeOffline = 'false',
      page = 1,
      limit = 50
    } = req.query;

    // Build query
    const query = { isEnabled: true };
    
    if (buildingId) query.buildingId = buildingId;
    if (floorId) query.floorId = floorId;
    if (room) query.room = new RegExp(room, 'i');
    if (ssid) query['wireless.ssids.name'] = ssid;
    if (status) query['status.state'] = status;
    if (includeOffline !== 'true') {
      query['status.state'] = 'connected';
    }

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const accessPoints = await WiFiAccessPoint.find(query)
      .populate('buildingId', 'name')
      .populate('floorId', 'name')
      .populate('floorplanIcon.iconId')
      .sort({ 'deviceInfo.name': 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await WiFiAccessPoint.countDocuments(query);

    res.json({
      accessPoints,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching WiFi access points:', error);
    res.status(500).json({ error: 'Failed to fetch WiFi access points' });
  }
});

/**
 * GET /api/wifi/access-points/:id
 * Get specific access point details
 */
router.get('/access-points/:id', isAuthenticated, async (req, res) => {
  try {
    const accessPoint = await WiFiAccessPoint.findById(req.params.id)
      .populate('buildingId', 'name')
      .populate('floorId', 'name')
      .populate('coverage.coverageAreas.coverageId')
      .populate('floorplanIcon.iconId');

    if (!accessPoint) {
      return res.status(404).json({ error: 'Access point not found' });
    }

    res.json(accessPoint);
  } catch (error) {
    console.error('Error fetching access point:', error);
    res.status(500).json({ error: 'Failed to fetch access point' });
  }
});

/**
 * POST /api/wifi/access-points
 * Create new access point (import from UniFi)
 */
router.post('/access-points', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      deviceId,
      buildingId,
      floorId,
      position,
      room,
      mounting,
      syncFromUnifi = true
    } = req.body;

    // Validate required fields
    if (!deviceId || !buildingId || !floorId || !position) {
      return res.status(400).json({ 
        error: 'Device ID, building ID, floor ID, and position are required' 
      });
    }

    // Check if access point already exists
    const existingAP = await WiFiAccessPoint.findOne({ deviceId });
    if (existingAP) {
      return res.status(409).json({ error: 'Access point already exists' });
    }

    // Validate building and floor exist
    const building = await Building.findById(buildingId);
    const floor = await Floor.findById(floorId);
    
    if (!building || !floor) {
      return res.status(404).json({ error: 'Building or floor not found' });
    }

    // Create access point
    const accessPointData = {
      deviceId: deviceId.toUpperCase(),
      buildingId,
      floorId,
      position,
      room,
      mounting: mounting || {},
      unifiSiteId: process.env.UNIFI_NETWORK_SITEID || 'default'
    };

    // Sync from UniFi if requested
    if (syncFromUnifi) {
      try {
        const unifiData = await getUnifiDeviceById(deviceId);
        accessPointData.deviceInfo = {
          name: unifiData.name || `AP-${deviceId.slice(-6)}`,
          model: unifiData.model,
          firmwareVersion: unifiData.version,
          serialNumber: unifiData.serial,
          lastSeen: unifiData.last_seen ? new Date(unifiData.last_seen * 1000) : new Date()
        };
        
        accessPointData.status = {
          state: unifiData.state === 1 ? 'connected' : 'disconnected',
          uptime: unifiData.uptime || 0,
          ipAddress: unifiData.ip
        };

        // Extract wireless configuration
        if (unifiData.radio_table) {
          accessPointData.wireless = {
            radios: unifiData.radio_table.map(radio => ({
              band: radio.radio === 'ng' ? '2.4GHz' : '5GHz',
              channel: radio.channel,
              txPower: radio.tx_power,
              enabled: radio.radio !== 'na'
            }))
          };
        }
      } catch (unifiError) {
        console.warn('Failed to sync from UniFi, creating with basic data:', unifiError.message);
        accessPointData.deviceInfo = {
          name: `AP-${deviceId.slice(-6)}`
        };
      }
    }

    const accessPoint = new WiFiAccessPoint(accessPointData);
    await accessPoint.save();

    res.status(201).json(accessPoint);
  } catch (error) {
    console.error('Error creating access point:', error);
    res.status(500).json({ error: 'Failed to create access point' });
  }
});

/**
 * PUT /api/wifi/access-points/:id
 * Update access point
 */
router.put('/access-points/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const accessPoint = await WiFiAccessPoint.findById(req.params.id);
    if (!accessPoint) {
      return res.status(404).json({ error: 'Access point not found' });
    }

    // Update allowed fields
    const allowedUpdates = [
      'position', 'room', 'mounting', 'eventOptimization', 
      'maintenance', 'floorplanIcon', 'notes', 'tags'
    ];

    allowedUpdates.forEach(field => {
      if (req.body[field] !== undefined) {
        accessPoint[field] = req.body[field];
      }
    });

    await accessPoint.save();
    res.json(accessPoint);
  } catch (error) {
    console.error('Error updating access point:', error);
    res.status(500).json({ error: 'Failed to update access point' });
  }
});

/**
 * DELETE /api/wifi/access-points/:id
 * Delete access point
 */
router.delete('/access-points/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const accessPoint = await WiFiAccessPoint.findById(req.params.id);
    if (!accessPoint) {
      return res.status(404).json({ error: 'Access point not found' });
    }

    await accessPoint.deleteOne();
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting access point:', error);
    res.status(500).json({ error: 'Failed to delete access point' });
  }
});

// ===== Coverage Management =====

/**
 * GET /api/wifi/coverage
 * Get WiFi coverage areas
 */
router.get('/coverage', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId, ssid, status } = req.query;

    const query = { isEnabled: true };
    if (buildingId) query.buildingId = buildingId;
    if (floorId) query.floorId = floorId;
    if (ssid) query.ssid = ssid;
    if (status) query.status = status;

    const coverageAreas = await WiFiCoverage.find(query)
      .populate('buildingId', 'name')
      .populate('floorId', 'name')
      .sort({ name: 1 });

    res.json(coverageAreas);
  } catch (error) {
    console.error('Error fetching WiFi coverage:', error);
    res.status(500).json({ error: 'Failed to fetch WiFi coverage' });
  }
});

/**
 * POST /api/wifi/coverage
 * Create new coverage area
 */
router.post('/coverage', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const coverageData = req.body;

    // Validate required fields
    if (!coverageData.name || !coverageData.buildingId || !coverageData.floorId || 
        !coverageData.coverageArea) {
      return res.status(400).json({ 
        error: 'Name, building ID, floor ID, and coverage area are required' 
      });
    }

    const coverage = new WiFiCoverage(coverageData);
    await coverage.save();

    res.status(201).json(coverage);
  } catch (error) {
    console.error('Error creating WiFi coverage:', error);
    res.status(500).json({ error: 'Failed to create WiFi coverage' });
  }
});

/**
 * GET /api/wifi/coverage/:id
 * Get specific coverage area
 */
router.get('/coverage/:id', isAuthenticated, async (req, res) => {
  try {
    const coverage = await WiFiCoverage.findById(req.params.id)
      .populate('buildingId', 'name')
      .populate('floorId', 'name');

    if (!coverage) {
      return res.status(404).json({ error: 'Coverage area not found' });
    }

    res.json(coverage);
  } catch (error) {
    console.error('Error fetching WiFi coverage:', error);
    res.status(500).json({ error: 'Failed to fetch WiFi coverage' });
  }
});

/**
 * PUT /api/wifi/coverage/:id
 * Update coverage area
 */
router.put('/coverage/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const coverage = await WiFiCoverage.findById(req.params.id);
    if (!coverage) {
      return res.status(404).json({ error: 'Coverage area not found' });
    }

    Object.assign(coverage, req.body);
    await coverage.save();

    res.json(coverage);
  } catch (error) {
    console.error('Error updating WiFi coverage:', error);
    res.status(500).json({ error: 'Failed to update WiFi coverage' });
  }
});

/**
 * DELETE /api/wifi/coverage/:id
 * Delete coverage area
 */
router.delete('/coverage/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const coverage = await WiFiCoverage.findById(req.params.id);
    if (!coverage) {
      return res.status(404).json({ error: 'Coverage area not found' });
    }

    await coverage.deleteOne();
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting WiFi coverage:', error);
    res.status(500).json({ error: 'Failed to delete WiFi coverage' });
  }
});

// ===== Location-based Queries =====

/**
 * GET /api/wifi/building/:buildingId/floor/:floorId/access-points
 * Get access points for specific floor
 */
router.get('/building/:buildingId/floor/:floorId/access-points', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;
    const { includeOffline, ssid } = req.query;

    const accessPoints = await WiFiAccessPoint.findByLocation(buildingId, floorId, {
      includeOffline: includeOffline === 'true',
      ssid
    });

    res.json(accessPoints);
  } catch (error) {
    console.error('Error fetching floor access points:', error);
    res.status(500).json({ error: 'Failed to fetch floor access points' });
  }
});

/**
 * GET /api/wifi/building/:buildingId/floor/:floorId/coverage
 * Get coverage areas for specific floor
 */
router.get('/building/:buildingId/floor/:floorId/coverage', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;

    const coverage = await WiFiCoverage.find({
      buildingId,
      floorId,
      status: 'active',
      isEnabled: true
    }).sort({ name: 1 });

    res.json(coverage);
  } catch (error) {
    console.error('Error fetching floor coverage:', error);
    res.status(500).json({ error: 'Failed to fetch floor coverage' });
  }
});

/**
 * GET /api/wifi/building/:buildingId/floor/:floorId/summary
 * Get WiFi summary for floor
 */
router.get('/building/:buildingId/floor/:floorId/summary', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;

    const [apSummary, coverageSummary] = await Promise.all([
      WiFiAccessPoint.getFloorSummary(buildingId, floorId),
      WiFiCoverage.getFloorCoverageSummary(buildingId, floorId)
    ]);

    res.json({
      accessPoints: apSummary,
      coverage: coverageSummary,
      floorId,
      buildingId
    });
  } catch (error) {
    console.error('Error fetching WiFi floor summary:', error);
    res.status(500).json({ error: 'Failed to fetch WiFi floor summary' });
  }
});

// ===== UniFi Integration & Sync =====

/**
 * POST /api/wifi/sync-unifi
 * Sync access points with UniFi Network controller
 */
router.post('/sync-unifi', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { deviceIds, updateExisting = true } = req.body;

    let syncResults = {
      synced: 0,
      created: 0,
      updated: 0,
      errors: []
    };

    // Get devices from UniFi
    const unifiDevices = await getUnifiDevices();
    let devicesToSync = unifiDevices.data;

    // Filter to specific device IDs if provided
    if (deviceIds && Array.isArray(deviceIds)) {
      devicesToSync = devicesToSync.filter(device => 
        deviceIds.includes(device.mac.toUpperCase())
      );
    }

    // Only process access points (not switches, etc.)
    devicesToSync = devicesToSync.filter(device => 
      device.type === 'uap' || device.model?.includes('U6') || device.model?.includes('AC')
    );

    for (const unifiDevice of devicesToSync) {
      try {
        const deviceId = unifiDevice.mac.toUpperCase();
        let accessPoint = await WiFiAccessPoint.findOne({ deviceId });

        if (accessPoint && updateExisting) {
          // Update existing
          accessPoint.updateFromUnifiData(unifiDevice);
          await accessPoint.save();
          syncResults.updated++;
        } else if (!accessPoint) {
          // Create new (but only if positioned on a floor)
          console.log(`UniFi device ${deviceId} found but not mapped to floor plan. Skipping auto-creation.`);
          continue;
        }

        syncResults.synced++;
      } catch (deviceError) {
        console.error(`Error syncing device ${unifiDevice.mac}:`, deviceError);
        syncResults.errors.push({
          deviceId: unifiDevice.mac,
          error: deviceError.message
        });
      }
    }

    res.json({
      message: 'UniFi sync completed',
      results: syncResults
    });
  } catch (error) {
    console.error('Error syncing with UniFi:', error);
    res.status(500).json({ error: 'Failed to sync with UniFi Network controller' });
  }
});

/**
 * GET /api/wifi/unifi-devices
 * Get available UniFi devices for import
 */
router.get('/unifi-devices', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const unifiDevices = await getUnifiDevices();
    
    // Filter to access points only
    const accessPoints = unifiDevices.data.filter(device => 
      device.type === 'uap' || device.model?.includes('U6') || device.model?.includes('AC')
    );

    // Check which ones are already imported
    const existingDeviceIds = await WiFiAccessPoint.find(
      { deviceId: { $in: accessPoints.map(d => d.mac.toUpperCase()) } },
      { deviceId: 1 }
    );
    const existingIds = new Set(existingDeviceIds.map(ap => ap.deviceId));

    const enrichedDevices = accessPoints.map(device => ({
      deviceId: device.mac.toUpperCase(),
      name: device.name || `AP-${device.mac.slice(-6)}`,
      model: device.model,
      state: device.state === 1 ? 'connected' : 'disconnected',
      ipAddress: device.ip,
      firmwareVersion: device.version,
      uptime: device.uptime,
      isImported: existingIds.has(device.mac.toUpperCase()),
      lastSeen: device.last_seen ? new Date(device.last_seen * 1000) : null
    }));

    res.json(enrichedDevices);
  } catch (error) {
    console.error('Error fetching UniFi devices:', error);
    res.status(500).json({ error: 'Failed to fetch UniFi devices' });
  }
});

// ===== Analytics and Optimization =====

/**
 * GET /api/wifi/analytics/coverage-quality
 * Get coverage quality analytics
 */
router.get('/analytics/coverage-quality', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.query;

    const query = { status: 'active', isEnabled: true };
    if (buildingId) query.buildingId = buildingId;
    if (floorId) query.floorId = floorId;

    const coverageAreas = await WiFiCoverage.find(query);
    
    if (coverageAreas.length === 0) {
      return res.json({
        message: 'No coverage data available',
        analytics: null
      });
    }

    // Calculate analytics
    const analytics = {
      totalAreas: coverageAreas.length,
      qualityDistribution: {
        excellent: coverageAreas.filter(area => area.quality.overallScore >= 80).length,
        good: coverageAreas.filter(area => area.quality.overallScore >= 60 && area.quality.overallScore < 80).length,
        fair: coverageAreas.filter(area => area.quality.overallScore >= 40 && area.quality.overallScore < 60).length,
        poor: coverageAreas.filter(area => area.quality.overallScore < 40).length
      },
      averageSignalStrength: coverageAreas.reduce((sum, area) => 
        sum + (area.signalStrength.average || -70), 0) / coverageAreas.length,
      totalCapacity: coverageAreas.reduce((sum, area) => 
        sum + area.eventSuitability.recommendedMaxDevices, 0),
      frequencyDistribution: {
        '2.4GHz': coverageAreas.filter(area => area.frequency === '2.4GHz').length,
        '5GHz': coverageAreas.filter(area => area.frequency === '5GHz').length,
        '6GHz': coverageAreas.filter(area => area.frequency === '6GHz').length,
        'Mixed': coverageAreas.filter(area => area.frequency === 'Mixed').length
      }
    };

    res.json({ analytics });
  } catch (error) {
    console.error('Error generating coverage analytics:', error);
    res.status(500).json({ error: 'Failed to generate coverage analytics' });
  }
});

/**
 * GET /api/wifi/suggest-ap-placement
 * Suggest optimal AP placement for event location
 */
router.get('/suggest-ap-placement', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId, x, y, eventType, expectedDevices = 50 } = req.query;

    if (!buildingId || !floorId || !x || !y) {
      return res.status(400).json({ 
        error: 'Building ID, floor ID, and coordinates (x, y) are required' 
      });
    }

    const posX = parseFloat(x);
    const posY = parseFloat(y);
    const deviceCount = parseInt(expectedDevices);

    // Find nearby access points
    const nearbyAPs = await WiFiAccessPoint.findNearPoint(buildingId, floorId, posX, posY, 100);
    
    // Find coverage at this location
    const coverageAreas = await WiFiCoverage.findByPoint(buildingId, floorId, posX, posY);

    // Calculate recommendations
    const recommendations = {
      location: { x: posX, y: posY },
      eventType,
      expectedDevices: deviceCount,
      nearbyAccessPoints: nearbyAPs.length,
      currentCoverage: coverageAreas.length > 0,
      recommendations: []
    };

    if (coverageAreas.length === 0) {
      recommendations.recommendations.push({
        type: 'add_coverage',
        priority: 'high',
        message: 'No Wi-Fi coverage detected at this location. Consider adding an access point within 50 meters.',
        suggestedActions: ['Add new access point', 'Extend existing coverage area']
      });
    } else {
      const bestCoverage = coverageAreas.sort((a, b) => b.quality.overallScore - a.quality.overallScore)[0];
      
      if (bestCoverage.eventSuitability.recommendedMaxDevices < deviceCount) {
        recommendations.recommendations.push({
          type: 'insufficient_capacity',
          priority: 'medium',
          message: `Current capacity (${bestCoverage.eventSuitability.recommendedMaxDevices} devices) may be insufficient for expected load (${deviceCount} devices).`,
          suggestedActions: ['Add additional access point', 'Upgrade existing equipment', 'Enable load balancing']
        });
      }

      if (bestCoverage.quality.overallScore < 60) {
        recommendations.recommendations.push({
          type: 'poor_quality',
          priority: 'medium',
          message: 'Wi-Fi quality at this location is below optimal for events.',
          suggestedActions: ['Optimize nearby access point settings', 'Reduce interference', 'Adjust antenna positioning']
        });
      }
    }

    if (nearbyAPs.length > 3) {
      recommendations.recommendations.push({
        type: 'potential_interference',
        priority: 'low',
        message: 'High density of access points detected. Monitor for potential interference.',
        suggestedActions: ['Review channel assignments', 'Optimize power levels', 'Enable band steering']
      });
    }

    if (recommendations.recommendations.length === 0) {
      recommendations.recommendations.push({
        type: 'optimal',
        priority: 'info',
        message: 'Current Wi-Fi setup appears suitable for the planned event.',
        suggestedActions: ['Monitor during event', 'Have backup plan ready']
      });
    }

    res.json(recommendations);
  } catch (error) {
    console.error('Error generating AP placement suggestions:', error);
    res.status(500).json({ error: 'Failed to generate AP placement suggestions' });
  }
});

// ===== Demo Data =====

/**
 * POST /api/wifi/seed-demo-data
 * Create demo WiFi data for testing
 */
router.post('/seed-demo-data', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { buildingId, floorId, clearExisting = false } = req.body;

    if (!buildingId || !floorId) {
      return res.status(400).json({ error: 'Building ID and floor ID are required' });
    }

    // Clear existing data if requested
    if (clearExisting) {
      await WiFiAccessPoint.deleteMany({ buildingId, floorId });
      await WiFiCoverage.deleteMany({ buildingId, floorId });
    }

    // Create demo access points
    const demoAPs = [
      {
        deviceId: '24:5E:BE:12:34:56',
        deviceInfo: { name: 'AP-Sanctuary-Main' },
        position: { x: 200, y: 300 },
        room: 'Sanctuary',
        wireless: { 
          clientStats: { total: 45 },
          radios: [
            { band: '2.4GHz', channel: 6, txPower: 22, enabled: true },
            { band: '5GHz', channel: 36, txPower: 20, enabled: true }
          ]
        }
      },
      {
        deviceId: '24:5E:BE:78:90:AB',
        deviceInfo: { name: 'AP-Lobby-Welcome' },
        position: { x: 150, y: 150 },
        room: 'Main Lobby',
        wireless: { 
          clientStats: { total: 23 },
          radios: [
            { band: '2.4GHz', channel: 1, txPower: 18, enabled: true },
            { band: '5GHz', channel: 44, txPower: 17, enabled: true }
          ]
        }
      },
      {
        deviceId: '24:5E:BE:CD:EF:12',
        deviceInfo: { name: 'AP-Fellowship-Hall' },
        position: { x: 400, y: 200 },
        room: 'Fellowship Hall',
        wireless: { 
          clientStats: { total: 67 },
          radios: [
            { band: '2.4GHz', channel: 11, txPower: 25, enabled: true },
            { band: '5GHz', channel: 149, txPower: 22, enabled: true }
          ]
        }
      }
    ];

    const createdAPs = [];
    for (const apData of demoAPs) {
      const ap = new WiFiAccessPoint({
        ...apData,
        buildingId,
        floorId,
        unifiSiteId: 'default',
        status: { state: 'connected', uptime: 86400 }
      });
      await ap.save();
      createdAPs.push(ap);
    }

    // Create demo coverage areas
    const demoCoverage = [
      {
        name: 'Sanctuary Coverage Zone',
        buildingId,
        floorId,
        coverageArea: {
          type: 'Polygon',
          coordinates: [[[150, 250], [250, 250], [250, 350], [150, 350], [150, 250]]]
        },
        signalStrength: { average: -55, minimum: -68, maximum: -42 },
        quality: { excellent: 70, good: 25, fair: 5, poor: 0, overallScore: 85 },
        ssid: 'ChurchGuest',
        frequency: '5GHz',
        eventSuitability: { recommendedMaxDevices: 150 }
      },
      {
        name: 'Fellowship Hall Coverage',
        buildingId,
        floorId,
        coverageArea: {
          type: 'Polygon',
          coordinates: [[[350, 150], [450, 150], [450, 250], [350, 250], [350, 150]]]
        },
        signalStrength: { average: -48, minimum: -62, maximum: -35 },
        quality: { excellent: 85, good: 12, fair: 3, poor: 0, overallScore: 92 },
        ssid: 'ChurchGuest',
        frequency: 'Mixed',
        eventSuitability: { recommendedMaxDevices: 200 }
      }
    ];

    const createdCoverage = [];
    for (const coverageData of demoCoverage) {
      const coverage = new WiFiCoverage(coverageData);
      await coverage.save();
      createdCoverage.push(coverage);
    }

    res.json({
      message: 'Demo WiFi data created successfully',
      created: {
        accessPoints: createdAPs.length,
        coverageAreas: createdCoverage.length
      }
    });
  } catch (error) {
    console.error('Error seeding demo WiFi data:', error);
    res.status(500).json({ error: 'Failed to seed demo WiFi data' });
  }
});

module.exports = router;