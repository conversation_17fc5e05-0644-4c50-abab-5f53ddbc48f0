const express = require('express');
const router = express.Router();
const ticketFormsWebhookController = require('../controllers/ticketFormsWebhookController');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const { body } = require('express-validator');

/**
 * Ticket Forms Webhook Routes
 * Base path: /api/ticket-forms-webhooks
 */

// Validation middleware
const validateWebhookCreation = [
  body('formId')
    .notEmpty()
    .withMessage('Form ID is required'),
  body('formName')
    .notEmpty()
    .withMessage('Form name is required'),
  body('fieldMappings.subjectField')
    .notEmpty()
    .withMessage('Subject field mapping is required'),
  body('fieldMappings.descriptionField')
    .optional()
    .isString()
    .withMessage('Description field must be a string'),
  body('fieldMappings.priorityField')
    .optional()
    .isString()
    .withMessage('Priority field must be a string'),
  body('fieldMappings.typeField')
    .optional()
    .isString()
    .withMessage('Type field must be a string'),
  body('fieldMappings.dueDateField')
    .optional()
    .isString()
    .withMessage('Due date field must be a string'),
  body('fieldMappings.categoryField')
    .optional()
    .isString()
    .withMessage('Category field must be a string'),
  body('fieldMappings.requesterEmailField')
    .optional()
    .isString()
    .withMessage('Requester email field must be a string'),
  body('fieldMappings.requesterNameField')
    .optional()
    .isString()
    .withMessage('Requester name field must be a string'),
  body('fieldMappings.tagsField')
    .optional()
    .isString()
    .withMessage('Tags field must be a string'),
  body('fieldMappings.customFields')
    .optional()
    .isArray()
    .withMessage('Custom fields must be an array'),
  body('fieldMappings.customFields.*.formField')
    .optional()
    .notEmpty()
    .withMessage('Custom field form field is required'),
  body('fieldMappings.customFields.*.ticketField')
    .optional()
    .notEmpty()
    .withMessage('Custom field ticket field is required'),
  body('fieldMappings.customFields.*.transform')
    .optional()
    .isIn(['none', 'email_to_user', 'text_to_tags', 'date_parse', 'priority_map'])
    .withMessage('Invalid custom field transform type'),
  body('tokenMappings')
    .optional()
    .isArray()
    .withMessage('Token mappings must be an array'),
  body('tokenMappings.*.token')
    .optional()
    .notEmpty()
    .withMessage('Token name is required'),
  body('tokenMappings.*.value')
    .optional()
    .notEmpty()
    .withMessage('Token value is required'),
  body('tokenMappings.*.type')
    .optional()
    .isIn(['static', 'form_field', 'user_lookup', 'date_calc'])
    .withMessage('Invalid token type'),
  body('assignmentRules')
    .optional()
    .isArray()
    .withMessage('Assignment rules must be an array'),
  body('assignmentRules.*.field')
    .optional()
    .notEmpty()
    .withMessage('Assignment rule field is required'),
  body('assignmentRules.*.operator')
    .optional()
    .isIn(['equals', 'contains', 'starts_with', 'ends_with', 'regex'])
    .withMessage('Invalid assignment rule operator'),
  body('assignmentRules.*.value')
    .optional()
    .notEmpty()
    .withMessage('Assignment rule value is required'),
  body('assignmentRules.*.assignTo')
    .optional()
    .isMongoId()
    .withMessage('Invalid assignTo user ID'),
  body('assignmentRules.*.assignToGroup')
    .optional()
    .isMongoId()
    .withMessage('Invalid assignToGroup ID'),
  body('assignmentRules.*.priority')
    .optional()
    .isInt()
    .withMessage('Assignment rule priority must be an integer'),
  body('defaults.assignee')
    .optional()
    .isMongoId()
    .withMessage('Invalid default assignee user ID'),
  body('defaults.group')
    .optional()
    .isMongoId()
    .withMessage('Invalid default group ID'),
  body('defaults.priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid default priority'),
  body('defaults.type')
    .optional()
    .isIn(['incident', 'request', 'problem', 'change'])
    .withMessage('Invalid default type'),
  body('defaults.tags')
    .optional()
    .isArray()
    .withMessage('Default tags must be an array'),
  body('notifications.notifyAssignee')
    .optional()
    .isBoolean()
    .withMessage('Notify assignee must be a boolean'),
  body('notifications.notifyGroup')
    .optional()
    .isBoolean()
    .withMessage('Notify group must be a boolean'),
  body('notifications.notifyFollowers')
    .optional()
    .isArray()
    .withMessage('Notify followers must be an array'),
  body('notifications.notifyFollowers.*')
    .optional()
    .isMongoId()
    .withMessage('Invalid follower user ID')
];

const validateWebhookUpdate = [
  body('formName')
    .optional()
    .notEmpty()
    .withMessage('Form name is required if provided'),
  body('active')
    .optional()
    .isBoolean()
    .withMessage('Active must be a boolean'),
  // Include the same validation as creation but all optional
  ...validateWebhookCreation.map(validator => {
    // Make all validators optional by adding .optional() if not already present
    if (validator.optional) return validator;
    const chain = validator.builder ? validator.builder : validator;
    return chain.optional();
  })
];

// Middleware to check ticket manager permissions
const requireTicketManagerOrAdmin = (req, res, next) => {
  if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
    return res.status(403).json({ message: 'Requires admin or ticket_manager role' });
  }
  next();
};

// Get all ticket form webhooks
// GET /api/ticket-forms-webhooks
router.get('/', isAuthenticated, requireTicketManagerOrAdmin, ticketFormsWebhookController.getAllWebhooks);

// Get webhook by ID
// GET /api/ticket-forms-webhooks/:id
router.get('/:id', isAuthenticated, requireTicketManagerOrAdmin, ticketFormsWebhookController.getWebhookById);

// Create a new ticket form webhook (admin or ticket manager only)
// POST /api/ticket-forms-webhooks
router.post('/', isAuthenticated, requireTicketManagerOrAdmin, validateWebhookCreation, ticketFormsWebhookController.createWebhook);

// Update a webhook (admin or ticket manager only)
// PUT /api/ticket-forms-webhooks/:id
router.put('/:id', isAuthenticated, requireTicketManagerOrAdmin, validateWebhookUpdate, ticketFormsWebhookController.updateWebhook);

// Delete a webhook (admin only)
// DELETE /api/ticket-forms-webhooks/:id
router.delete('/:id', isAuthenticated, isAdmin, ticketFormsWebhookController.deleteWebhook);

// Process a specific webhook manually
// POST /api/ticket-forms-webhooks/:id/process
router.post('/:id/process', isAuthenticated, requireTicketManagerOrAdmin, ticketFormsWebhookController.processWebhook);

// Process all active webhooks (admin only)
// POST /api/ticket-forms-webhooks/process-all
router.post('/process-all', isAuthenticated, isAdmin, ticketFormsWebhookController.processAllWebhooks);

module.exports = router;