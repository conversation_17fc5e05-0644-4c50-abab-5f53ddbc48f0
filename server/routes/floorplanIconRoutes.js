const express = require('express');
const router = express.Router();
const FloorplanIcon = require('../../models/FloorplanIcon');
const Floor = require('../../models/Floor');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Get all floorplan icons
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Support filtering by type, status, or floorId
    const filter = {};
    if (req.query.type) filter.type = req.query.type;
    if (req.query.status) filter.status = req.query.status;
    if (req.query.floorId) filter.floorId = req.query.floorId;

    const icons = await FloorplanIcon.find(filter);
    res.json(icons);
  } catch (err) {
    console.error('Error fetching floorplan icons:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get floorplan icon by ID
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const icon = await FloorplanIcon.findById(req.params.id);

    if (!icon) {
      return res.status(404).json({ message: 'Floorplan icon not found' });
    }

    res.json(icon);
  } catch (err) {
    console.error('Error fetching floorplan icon:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create a new floorplan icon
router.post('/', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      floorId,
      name,
      type,
      customType,
      position,
      size,
      color,
      status,
      data,
      deviceId,
      integrationSource,
      metadata
    } = req.body;

    // Verify floor exists
    const floor = await Floor.findById(floorId);
    if (!floor) {
      return res.status(404).json({ message: 'Floor not found' });
    }

    // Parse position and ensure it has x and y properties
    let parsedPosition;
    if (position) {
      try {
        parsedPosition = JSON.parse(position);
        // Ensure position has x and y properties
        if (parsedPosition.x === undefined || parsedPosition.y === undefined) {
          return res.status(400).json({ 
            message: 'Invalid position data', 
            error: 'Position must include x and y coordinates' 
          });
        }
      } catch (error) {
        return res.status(400).json({ 
          message: 'Invalid position data', 
          error: 'Position must be a valid JSON object' 
        });
      }
    } else {
      // Default position if not provided
      parsedPosition = { x: 0, y: 0, rotation: 0 };
    }

    // Create new floorplan icon
    const newIcon = new FloorplanIcon({
      floorId,
      name,
      type,
      customType,
      position: parsedPosition,
      size: size ? JSON.parse(size) : { width: 32, height: 32 },
      color,
      status,
      data: data ? JSON.parse(data) : undefined,
      deviceId,
      integrationSource,
      metadata: metadata ? JSON.parse(metadata) : undefined,
      createdBy: req.user.id
    });

    const icon = await newIcon.save();
    res.status(201).json(icon);
  } catch (err) {
    console.error('Error creating floorplan icon:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Update a floorplan icon
router.put('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      name,
      type,
      customType,
      position,
      size,
      color,
      status,
      data,
      deviceId,
      integrationSource,
      metadata
    } = req.body;

    // Find icon by ID
    let icon = await FloorplanIcon.findById(req.params.id);

    if (!icon) {
      return res.status(404).json({ message: 'Floorplan icon not found' });
    }

    // Parse and validate position if provided
    if (position) {
      try {
        const parsedPosition = JSON.parse(position);
        // Ensure position has x and y properties
        if (parsedPosition.x === undefined || parsedPosition.y === undefined) {
          return res.status(400).json({ 
            message: 'Invalid position data', 
            error: 'Position must include x and y coordinates' 
          });
        }
        icon.position = parsedPosition;
      } catch (error) {
        return res.status(400).json({ 
          message: 'Invalid position data', 
          error: 'Position must be a valid JSON object' 
        });
      }
    }

    // Update other icon fields
    if (name) icon.name = name;
    if (type) icon.type = type;
    if (customType !== undefined) icon.customType = customType;
    if (size) {
      try {
        icon.size = JSON.parse(size);
      } catch (error) {
        return res.status(400).json({ 
          message: 'Invalid size data', 
          error: 'Size must be a valid JSON object' 
        });
      }
    }
    if (color) icon.color = color;
    if (status) icon.status = status;
    if (data) {
      try {
        icon.data = JSON.parse(data);
      } catch (error) {
        return res.status(400).json({ 
          message: 'Invalid data', 
          error: 'Data must be a valid JSON object' 
        });
      }
    }
    if (deviceId !== undefined) icon.deviceId = deviceId;
    if (integrationSource !== undefined) icon.integrationSource = integrationSource;
    if (metadata) {
      try {
        icon.metadata = JSON.parse(metadata);
      } catch (error) {
        return res.status(400).json({ 
          message: 'Invalid metadata', 
          error: 'Metadata must be a valid JSON object' 
        });
      }
    }
    icon.updatedAt = Date.now();

    // Save updated icon
    icon = await icon.save();
    res.json(icon);
  } catch (err) {
    console.error('Error updating floorplan icon:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Delete a floorplan icon
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    // Find icon by ID
    const icon = await FloorplanIcon.findById(req.params.id);

    if (!icon) {
      return res.status(404).json({ message: 'Floorplan icon not found' });
    }

    // Delete the icon
    await FloorplanIcon.findByIdAndDelete(req.params.id);

    res.json({ message: 'Floorplan icon deleted successfully' });
  } catch (err) {
    console.error('Error deleting floorplan icon:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update icon data (for real-time updates from integrations)
router.patch('/:id/data', isAuthenticated, async (req, res) => {
  try {
    const { value, unit, min, max } = req.body;

    // Find icon by ID
    let icon = await FloorplanIcon.findById(req.params.id);

    if (!icon) {
      return res.status(404).json({ message: 'Floorplan icon not found' });
    }

    // Update data fields
    icon.data = {
      ...icon.data,
      value: value !== undefined ? value : icon.data?.value,
      unit: unit !== undefined ? unit : icon.data?.unit,
      min: min !== undefined ? min : icon.data?.min,
      max: max !== undefined ? max : icon.data?.max,
      lastUpdated: Date.now()
    };

    // Save updated icon
    icon = await icon.save();
    res.json(icon);
  } catch (err) {
    console.error('Error updating floorplan icon data:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Bulk create icons
router.post('/bulk', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { icons } = req.body;

    if (!Array.isArray(icons) || icons.length === 0) {
      return res.status(400).json({ message: 'Invalid icons array' });
    }

    // Add createdBy to each icon
    const iconsWithCreator = icons.map(icon => ({
      ...icon,
      createdBy: req.user.id
    }));

    // Create all icons
    const createdIcons = await FloorplanIcon.insertMany(iconsWithCreator);

    res.status(201).json(createdIcons);
  } catch (err) {
    console.error('Error bulk creating floorplan icons:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Bulk update icon positions (for drag and drop UI)
router.put('/bulk/positions', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { icons } = req.body;

    if (!Array.isArray(icons) || icons.length === 0) {
      return res.status(400).json({ message: 'Invalid icons array' });
    }

    // Validate each icon's position
    for (const icon of icons) {
      if (!icon.position || typeof icon.position !== 'object') {
        return res.status(400).json({ 
          message: 'Invalid position data', 
          error: `Icon ${icon._id} has invalid position data` 
        });
      }

      // Ensure position has x and y properties
      if (icon.position.x === undefined || icon.position.y === undefined) {
        return res.status(400).json({ 
          message: 'Invalid position data', 
          error: `Icon ${icon._id} is missing position.x or position.y` 
        });
      }
    }

    // Update each icon's position
    const updatePromises = icons.map(icon => 
      FloorplanIcon.findByIdAndUpdate(
        icon._id, 
        { 
          position: icon.position,
          updatedAt: Date.now()
        },
        { new: true }
      )
    );

    const updatedIcons = await Promise.all(updatePromises);

    res.json(updatedIcons);
  } catch (err) {
    console.error('Error updating floorplan icon positions:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

module.exports = router;
