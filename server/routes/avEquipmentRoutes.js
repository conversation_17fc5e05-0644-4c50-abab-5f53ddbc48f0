const express = require('express');
const router = express.Router();
const AVEquipment = require('../../models/AVEquipment');
const { isAuthenticated: auth, isAdmin: admin } = require('../../middleware/auth');

/**
 * AV Equipment Routes for Phase 8 - AV Equipment Management & Technical Support
 * Provides comprehensive CRUD operations for audio/visual equipment management
 */

// @route   GET /api/av-equipment
// @desc    Get all AV equipment with filtering options
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      buildingId,
      floorId,
      category,
      status,
      health,
      search,
      maintenanceDue,
      hasAlerts,
      page = 1,
      limit = 50
    } = req.query;

    let query = { isActive: true };

    // Location filtering
    if (buildingId) {
      query['location.buildingId'] = buildingId;
    }
    if (floorId) {
      query['location.floorId'] = floorId;
    }

    // Category filtering
    if (category) {
      query.category = category;
    }

    // Status filtering
    if (status) {
      query['status.operational'] = status;
    }
    if (health) {
      query['status.health'] = health;
    }

    // Search filtering
    if (search) {
      query.$text = { $search: search };
    }

    // Maintenance filtering
    if (maintenanceDue === 'true') {
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      query['maintenance.preventiveMaintenance.nextService'] = { $lte: thirtyDaysFromNow };
    }

    // Alerts filtering
    if (hasAlerts === 'true') {
      query['maintenance.alerts'] = { 
        $elemMatch: { acknowledged: false } 
      };
    }

    const equipment = await AVEquipment.find(query)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name')
      .sort({ name: 1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    const total = await AVEquipment.countDocuments(query);

    res.json({
      equipment,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit))
    });
  } catch (err) {
    console.error('Error fetching AV equipment:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET /api/av-equipment/:id
// @desc    Get specific AV equipment by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const equipment = await AVEquipment.findById(req.params.id)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    res.json(equipment);
  } catch (err) {
    console.error('Error fetching AV equipment:', err);
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   POST /api/av-equipment
// @desc    Create new AV equipment
// @access  Private (Admin)
router.post('/', [auth, admin], async (req, res) => {
  try {
    const equipmentData = {
      ...req.body,
      createdBy: req.user.id,
      updatedBy: req.user.id
    };

    const equipment = new AVEquipment(equipmentData);
    await equipment.save();

    const populatedEquipment = await AVEquipment.findById(equipment._id)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name');

    res.status(201).json(populatedEquipment);
  } catch (err) {
    console.error('Error creating AV equipment:', err);
    if (err.code === 11000) {
      return res.status(400).json({ msg: 'Equipment ID already exists' });
    }
    res.status(400).json({ msg: 'Invalid equipment data' });
  }
});

// @route   PUT /api/av-equipment/:id
// @desc    Update AV equipment
// @access  Private (Admin)
router.put('/:id', [auth, admin], async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user.id
    };

    const equipment = await AVEquipment.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('location.buildingId', 'name')
     .populate('location.floorId', 'name')
     .populate('location.roomId', 'name');

    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    res.json(equipment);
  } catch (err) {
    console.error('Error updating AV equipment:', err);
    res.status(400).json({ msg: 'Invalid update data' });
  }
});

// @route   DELETE /api/av-equipment/:id
// @desc    Delete AV equipment (soft delete)
// @access  Private (Admin)
router.delete('/:id', [auth, admin], async (req, res) => {
  try {
    const equipment = await AVEquipment.findByIdAndUpdate(
      req.params.id,
      { 
        isActive: false,
        updatedBy: req.user.id 
      },
      { new: true }
    );

    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    res.json({ msg: 'AV equipment deleted successfully' });
  } catch (err) {
    console.error('Error deleting AV equipment:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET /api/av-equipment/location/:buildingId/:floorId
// @desc    Get AV equipment by location
// @access  Private
router.get('/location/:buildingId/:floorId', auth, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;
    const { category, includePortable } = req.query;

    const equipment = await AVEquipment.findByLocation(buildingId, floorId, {
      category: category || undefined,
      includePortable: includePortable === 'true'
    }).populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name');

    res.json(equipment);
  } catch (err) {
    console.error('Error fetching equipment by location:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET /api/av-equipment/category/:category
// @desc    Get AV equipment by category
// @access  Private
router.get('/category/:category', auth, async (req, res) => {
  try {
    const { category } = req.params;
    const { buildingId } = req.query;

    const equipment = await AVEquipment.findByCategory(category, {
      buildingId: buildingId || undefined
    }).populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name');

    res.json(equipment);
  } catch (err) {
    console.error('Error fetching equipment by category:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET /api/av-equipment/maintenance/due
// @desc    Get equipment requiring maintenance
// @access  Private
router.get('/maintenance/due', auth, async (req, res) => {
  try {
    const { daysAhead = 30 } = req.query;

    const equipment = await AVEquipment.findRequiringMaintenance(parseInt(daysAhead))
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name');

    res.json(equipment);
  } catch (err) {
    console.error('Error fetching maintenance due equipment:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET /api/av-equipment/alerts
// @desc    Get equipment with active alerts
// @access  Private
router.get('/alerts/active', auth, async (req, res) => {
  try {
    const { severity } = req.query;

    const equipment = await AVEquipment.findWithAlerts(severity || null)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name')
      .populate('location.roomId', 'name');

    res.json(equipment);
  } catch (err) {
    console.error('Error fetching equipment with alerts:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   POST /api/av-equipment/:id/service
// @desc    Add service record to equipment
// @access  Private (Admin)
router.post('/:id/service', [auth, admin], async (req, res) => {
  try {
    const equipment = await AVEquipment.findById(req.params.id);
    
    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    const serviceData = {
      ...req.body,
      technician: req.body.technician || req.user.name
    };

    await equipment.addServiceRecord(serviceData);

    res.json({ msg: 'Service record added successfully', equipment });
  } catch (err) {
    console.error('Error adding service record:', err);
    res.status(400).json({ msg: 'Invalid service data' });
  }
});

// @route   POST /api/av-equipment/:id/alert
// @desc    Add alert to equipment
// @access  Private
router.post('/:id/alert', auth, async (req, res) => {
  try {
    const equipment = await AVEquipment.findById(req.params.id);
    
    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    await equipment.addAlert(req.body);

    res.json({ msg: 'Alert added successfully', equipment });
  } catch (err) {
    console.error('Error adding alert:', err);
    res.status(400).json({ msg: 'Invalid alert data' });
  }
});

// @route   PUT /api/av-equipment/:id/alert/:alertId/acknowledge
// @desc    Acknowledge equipment alert
// @access  Private
router.put('/:id/alert/:alertId/acknowledge', auth, async (req, res) => {
  try {
    const equipment = await AVEquipment.findById(req.params.id);
    
    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    await equipment.acknowledgeAlert(req.params.alertId, req.user.name);

    res.json({ msg: 'Alert acknowledged successfully', equipment });
  } catch (err) {
    console.error('Error acknowledging alert:', err);
    res.status(400).json({ msg: 'Error acknowledging alert' });
  }
});

// @route   PUT /api/av-equipment/:id/status
// @desc    Update equipment status
// @access  Private
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { status, notes } = req.body;
    const equipment = await AVEquipment.findById(req.params.id);
    
    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    await equipment.updateStatus(status, notes);

    res.json({ msg: 'Status updated successfully', equipment });
  } catch (err) {
    console.error('Error updating status:', err);
    res.status(400).json({ msg: 'Error updating status' });
  }
});

// @route   POST /api/av-equipment/:id/session/start
// @desc    Start equipment usage session
// @access  Private
router.post('/:id/session/start', auth, async (req, res) => {
  try {
    const { service } = req.body;
    const equipment = await AVEquipment.findById(req.params.id);
    
    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    await equipment.startSession(req.user.name, service);

    res.json({ msg: 'Session started successfully', equipment });
  } catch (err) {
    console.error('Error starting session:', err);
    res.status(400).json({ msg: 'Error starting session' });
  }
});

// @route   POST /api/av-equipment/:id/session/end
// @desc    End equipment usage session
// @access  Private
router.post('/:id/session/end', auth, async (req, res) => {
  try {
    const equipment = await AVEquipment.findById(req.params.id);
    
    if (!equipment) {
      return res.status(404).json({ msg: 'AV equipment not found' });
    }

    await equipment.endSession();

    res.json({ msg: 'Session ended successfully', equipment });
  } catch (err) {
    console.error('Error ending session:', err);
    res.status(400).json({ msg: 'Error ending session' });
  }
});

// @route   GET /api/av-equipment/analytics/summary
// @desc    Get analytics summary for AV equipment
// @access  Private
router.get('/analytics/summary', auth, async (req, res) => {
  try {
    const { buildingId, startDate, endDate } = req.query;

    let matchQuery = { isActive: true };
    if (buildingId) {
      matchQuery['location.buildingId'] = buildingId;
    }

    const summary = await AVEquipment.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          totalEquipment: { $sum: 1 },
          activeEquipment: {
            $sum: { $cond: [{ $eq: ['$status.operational', 'active'] }, 1, 0] }
          },
          maintenanceEquipment: {
            $sum: { $cond: [{ $eq: ['$status.operational', 'maintenance'] }, 1, 0] }
          },
          failedEquipment: {
            $sum: { $cond: [{ $eq: ['$status.operational', 'failed'] }, 1, 0] }
          },
          categoryCounts: { $push: '$category' },
          totalAlerts: { $sum: { $size: { $ifNull: ['$maintenance.alerts', []] } } },
          maintenanceDue: {
            $sum: {
              $cond: [
                { $lte: ['$maintenance.preventiveMaintenance.nextService', new Date()] },
                1,
                0
              ]
            }
          }
        }
      }
    ]);

    const categoryBreakdown = await AVEquipment.aggregate([
      { $match: matchQuery },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    res.json({
      summary: summary[0] || {},
      categoryBreakdown
    });
  } catch (err) {
    console.error('Error fetching analytics summary:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   POST /api/av-equipment/seed-demo-data
// @desc    Seed demo AV equipment data
// @access  Private (Admin)
router.post('/seed-demo-data', [auth, admin], async (req, res) => {
  try {
    const { buildingId, floorId, clearExisting = false } = req.body;

    if (clearExisting) {
      await AVEquipment.deleteMany({ 'location.buildingId': buildingId });
    }

    const demoEquipment = [
      {
        equipmentId: 'AV-MIC-001',
        name: 'Pastor Wireless Microphone',
        description: 'Primary wireless microphone for pastor during services',
        category: 'microphone_wireless',
        subcategory: 'handheld',
        location: {
          buildingId,
          floorId,
          roomName: 'Main Sanctuary',
          position: {
            zone: 'stage',
            mounting: 'handheld'
          }
        },
        specifications: {
          manufacturer: 'Shure',
          model: 'SLXD24/SM58',
          serialNumber: 'SLX001234',
          connectivity: {
            wireless: {
              frequency: 'UHF 470-698 MHz',
              channel: 'Channel 1'
            }
          }
        },
        status: {
          operational: 'active',
          health: 'good'
        },
        maintenance: {
          preventiveMaintenance: {
            interval: 90,
            lastService: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)
          }
        }
      },
      {
        equipmentId: 'AV-PROJ-001',
        name: 'Main Sanctuary Projector',
        description: 'Primary projector for sanctuary video display',
        category: 'projector',
        location: {
          buildingId,
          floorId,
          roomName: 'Main Sanctuary',
          position: {
            zone: 'sanctuary',
            mounting: 'ceiling'
          }
        },
        specifications: {
          manufacturer: 'Epson',
          model: 'Pro L1070U',
          serialNumber: 'EPSON7890',
          performance: {
            resolution: '1920x1200 WUXGA',
            brightness: '7000 lumens'
          }
        },
        status: {
          operational: 'active',
          health: 'excellent'
        },
        maintenance: {
          preventiveMaintenance: {
            interval: 180,
            lastService: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      },
      {
        equipmentId: 'AV-CAM-001',
        name: 'Stage PTZ Camera',
        description: 'Pan-tilt-zoom camera for stage coverage',
        category: 'camera_ptz',
        location: {
          buildingId,
          floorId,
          roomName: 'Main Sanctuary',
          position: {
            zone: 'sanctuary',
            mounting: 'ceiling'
          }
        },
        specifications: {
          manufacturer: 'Sony',
          model: 'EVI-D100',
          serialNumber: 'SONY5678',
          performance: {
            resolution: '1080p',
            frameRate: '30fps'
          },
          connectivity: {
            network: {
              protocol: 'RTSP',
              ipAddress: '*************'
            }
          }
        },
        status: {
          operational: 'active',
          health: 'good'
        }
      },
      {
        equipmentId: 'AV-MIX-001',
        name: 'Digital Audio Mixer',
        description: 'Main audio mixing console',
        category: 'audio_mixer',
        location: {
          buildingId,
          floorId,
          roomName: 'Audio Booth',
          position: {
            zone: 'booth',
            mounting: 'rack'
          }
        },
        specifications: {
          manufacturer: 'Behringer',
          model: 'X32',
          serialNumber: 'X32001',
          connectivity: {
            inputs: ['XLR', 'TRS'],
            outputs: ['XLR', 'TRS'],
            network: {
              protocol: 'X32 Protocol',
              ipAddress: '*************'
            }
          }
        },
        status: {
          operational: 'active',
          health: 'excellent'
        },
        maintenance: {
          preventiveMaintenance: {
            interval: 120,
            lastService: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
          }
        }
      },
      {
        equipmentId: 'AV-SPKR-001',
        name: 'Main Left Speaker',
        description: 'Left main speaker for sanctuary audio',
        category: 'speakers',
        subcategory: 'main',
        location: {
          buildingId,
          floorId,
          roomName: 'Main Sanctuary',
          position: {
            zone: 'sanctuary',
            mounting: 'wall'
          }
        },
        specifications: {
          manufacturer: 'QSC',
          model: 'K12.2',
          serialNumber: 'QSC2345',
          performance: {
            maxSPL: '131 dB',
            frequencyResponse: '45Hz-20kHz'
          }
        },
        status: {
          operational: 'active',
          health: 'good'
        }
      }
    ];

    const created = await AVEquipment.insertMany(
      demoEquipment.map(eq => ({
        ...eq,
        createdBy: req.user.id,
        updatedBy: req.user.id
      }))
    );

    res.json({
      msg: 'Demo AV equipment data seeded successfully',
      created: created.length,
      equipment: created
    });
  } catch (err) {
    console.error('Error seeding demo data:', err);
    res.status(500).json({ msg: 'Error seeding demo data' });
  }
});

module.exports = router;