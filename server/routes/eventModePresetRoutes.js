const express = require('express');
const router = express.Router();
const { isAuthenticated: auth, isAdmin: admin } = require('../../middleware/auth');
const EventModePreset = require('../../models/EventModePreset');

// @route   GET /api/event-mode-presets
// @desc    Get all event mode presets
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { 
      eventType, 
      isPublic,
      createdBy,
      search,
      sortBy = 'name',
      sortOrder = 'asc',
      page = 1,
      limit = 50
    } = req.query;

    // Build filter
    const filter = {};
    
    if (eventType) {
      filter.eventType = eventType;
    }
    
    if (isPublic !== undefined) {
      filter.isPublic = isPublic === 'true';
    }
    
    if (createdBy) {
      filter.createdBy = createdBy;
    }
    
    if (search) {
      filter.$or = [
        { name: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') }
      ];
    }

    // Check access permissions
    if (!req.user.role === 'admin') {
      // Non-admin users can only see public presets or their own
      filter.$or = [
        { isPublic: true },
        { createdBy: req.user.id },
        { allowedRoles: { $in: [req.user.role] } }
      ];
    }

    // Build sort
    const sortOptions = {};
    if (sortBy === 'popularity') {
      sortOptions['usage.timesUsed'] = sortOrder === 'desc' ? -1 : 1;
    } else if (sortBy === 'rating') {
      sortOptions['usage.averageRating'] = sortOrder === 'desc' ? -1 : 1;
    } else if (sortBy === 'recent') {
      sortOptions['usage.lastUsed'] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [presets, total] = await Promise.all([
      EventModePreset.find(filter)
        .populate('createdBy', 'name email')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      EventModePreset.countDocuments(filter)
    ]);

    res.json({
      presets,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching event mode presets:', error);
    res.status(500).json({ 
      message: 'Error fetching event mode presets',
      error: error.message 
    });
  }
});

// @route   GET /api/event-mode-presets/defaults
// @desc    Get default presets for event types
// @access  Private
router.get('/defaults', auth, async (req, res) => {
  try {
    const defaults = EventModePreset.getDefaults();
    res.json(defaults);
  } catch (error) {
    console.error('Error getting default presets:', error);
    res.status(500).json({ 
      message: 'Error getting default presets',
      error: error.message 
    });
  }
});

// @route   GET /api/event-mode-presets/types
// @desc    Get available event types
// @access  Private
router.get('/types', auth, async (req, res) => {
  try {
    const types = [
      { value: 'sunday_service', label: 'Sunday Service', icon: 'church', color: '#8BC34A' },
      { value: 'youth_night', label: 'Youth Night', icon: 'groups', color: '#E91E63' },
      { value: 'conference', label: 'Conference', icon: 'event', color: '#673AB7' },
      { value: 'wedding', label: 'Wedding', icon: 'favorite', color: '#E91E63' },
      { value: 'funeral', label: 'Funeral', icon: 'local_florist', color: '#607D8B' },
      { value: 'community_event', label: 'Community Event', icon: 'group', color: '#FF9800' },
      { value: 'maintenance', label: 'Maintenance', icon: 'build', color: '#795548' },
      { value: 'emergency', label: 'Emergency', icon: 'emergency', color: '#F44336' },
      { value: 'custom', label: 'Custom', icon: 'settings', color: '#2196F3' }
    ];
    
    res.json(types);
  } catch (error) {
    console.error('Error getting event types:', error);
    res.status(500).json({ 
      message: 'Error getting event types',
      error: error.message 
    });
  }
});

// @route   GET /api/event-mode-presets/:id
// @desc    Get specific event mode preset
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const preset = await EventModePreset.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('usage.feedback.userId', 'name');

    if (!preset) {
      return res.status(404).json({ message: 'Event mode preset not found' });
    }

    // Check access permissions
    if (!preset.isPublic && 
        preset.createdBy._id.toString() !== req.user.id && 
        req.user.role !== 'admin' &&
        !preset.allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied to this preset' });
    }

    res.json(preset);
  } catch (error) {
    console.error('Error fetching event mode preset:', error);
    res.status(500).json({ 
      message: 'Error fetching event mode preset',
      error: error.message 
    });
  }
});

// @route   POST /api/event-mode-presets
// @desc    Create new event mode preset
// @access  Private (Admin or allowed users)
router.post('/', auth, async (req, res) => {
  try {
    const presetData = {
      ...req.body,
      createdBy: req.user.id
    };

    // Validate required fields
    const { name, eventType, layerConfig } = presetData;
    if (!name || !eventType || !layerConfig) {
      return res.status(400).json({ 
        message: 'Name, event type, and layer configuration are required' 
      });
    }

    const preset = new EventModePreset(presetData);
    await preset.save();
    
    await preset.populate('createdBy', 'name email');
    
    res.status(201).json(preset);
  } catch (error) {
    console.error('Error creating event mode preset:', error);
    
    if (error.code === 11000) {
      return res.status(409).json({ 
        message: 'A preset with this name already exists' 
      });
    }
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error creating event mode preset',
      error: error.message 
    });
  }
});

// @route   PUT /api/event-mode-presets/:id
// @desc    Update event mode preset
// @access  Private (Creator or Admin)
router.put('/:id', auth, async (req, res) => {
  try {
    const preset = await EventModePreset.findById(req.params.id);
    
    if (!preset) {
      return res.status(404).json({ message: 'Event mode preset not found' });
    }

    // Check permissions
    if (preset.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to update this preset' });
    }

    // Update preset
    Object.assign(preset, req.body);
    await preset.save();
    
    await preset.populate('createdBy', 'name email');
    
    res.json(preset);
  } catch (error) {
    console.error('Error updating event mode preset:', error);
    
    if (error.code === 11000) {
      return res.status(409).json({ 
        message: 'A preset with this name already exists' 
      });
    }
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error updating event mode preset',
      error: error.message 
    });
  }
});

// @route   DELETE /api/event-mode-presets/:id
// @desc    Delete event mode preset
// @access  Private (Creator or Admin)
router.delete('/:id', auth, async (req, res) => {
  try {
    const preset = await EventModePreset.findById(req.params.id);
    
    if (!preset) {
      return res.status(404).json({ message: 'Event mode preset not found' });
    }

    // Check permissions
    if (preset.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this preset' });
    }

    await EventModePreset.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Event mode preset deleted successfully' });
  } catch (error) {
    console.error('Error deleting event mode preset:', error);
    res.status(500).json({ 
      message: 'Error deleting event mode preset',
      error: error.message 
    });
  }
});

// @route   POST /api/event-mode-presets/:id/apply
// @desc    Apply event mode preset and track usage
// @access  Private
router.post('/:id/apply', auth, async (req, res) => {
  try {
    const { currentConfig = {} } = req.body;
    
    const preset = await EventModePreset.findById(req.params.id);
    
    if (!preset) {
      return res.status(404).json({ message: 'Event mode preset not found' });
    }

    // Check access permissions
    if (!preset.isPublic && 
        preset.createdBy.toString() !== req.user.id && 
        req.user.role !== 'admin' &&
        !preset.allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied to this preset' });
    }

    // Apply preset configuration
    const appliedConfig = preset.apply(currentConfig);
    
    // Save usage tracking
    await preset.save();
    
    res.json({
      message: 'Event mode preset applied successfully',
      config: appliedConfig,
      preset: {
        id: preset._id,
        name: preset.name,
        eventType: preset.eventType,
        appliedAt: new Date()
      }
    });
  } catch (error) {
    console.error('Error applying event mode preset:', error);
    res.status(500).json({ 
      message: 'Error applying event mode preset',
      error: error.message 
    });
  }
});

// @route   POST /api/event-mode-presets/:id/feedback
// @desc    Add feedback/rating for preset
// @access  Private
router.post('/:id/feedback', auth, async (req, res) => {
  try {
    const { rating, comment } = req.body;
    
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({ message: 'Rating must be between 1 and 5' });
    }
    
    const preset = await EventModePreset.findById(req.params.id);
    
    if (!preset) {
      return res.status(404).json({ message: 'Event mode preset not found' });
    }

    // Check if user already provided feedback
    const existingFeedback = preset.usage.feedback.find(
      fb => fb.userId.toString() === req.user.id
    );
    
    if (existingFeedback) {
      // Update existing feedback
      existingFeedback.rating = rating;
      existingFeedback.comment = comment || existingFeedback.comment;
    } else {
      // Add new feedback
      preset.usage.feedback.push({
        userId: req.user.id,
        rating,
        comment
      });
    }
    
    // Recalculate average rating
    const totalRating = preset.usage.feedback.reduce((sum, fb) => sum + fb.rating, 0);
    preset.usage.averageRating = totalRating / preset.usage.feedback.length;
    
    await preset.save();
    
    res.json({
      message: 'Feedback added successfully',
      averageRating: preset.usage.averageRating,
      totalFeedback: preset.usage.feedback.length
    });
  } catch (error) {
    console.error('Error adding feedback:', error);
    res.status(500).json({ 
      message: 'Error adding feedback',
      error: error.message 
    });
  }
});

// @route   POST /api/event-mode-presets/initialize-defaults
// @desc    Initialize default presets (Admin only)
// @access  Private (Admin)
router.post('/initialize-defaults', [auth, admin], async (req, res) => {
  try {
    await EventModePreset.initializeDefaults();
    
    const count = await EventModePreset.countDocuments();
    
    res.json({
      message: 'Default presets initialized successfully',
      totalPresets: count
    });
  } catch (error) {
    console.error('Error initializing default presets:', error);
    res.status(500).json({ 
      message: 'Error initializing default presets',
      error: error.message 
    });
  }
});

// @route   GET /api/event-mode-presets/stats/usage
// @desc    Get usage statistics for presets
// @access  Private (Admin)
router.get('/stats/usage', [auth, admin], async (req, res) => {
  try {
    const stats = await EventModePreset.aggregate([
      {
        $group: {
          _id: '$eventType',
          totalPresets: { $sum: 1 },
          totalUsage: { $sum: '$usage.timesUsed' },
          averageRating: { $avg: '$usage.averageRating' },
          mostUsed: { $max: '$usage.timesUsed' }
        }
      },
      { $sort: { totalUsage: -1 } }
    ]);
    
    const totalPresets = await EventModePreset.countDocuments();
    const totalUsage = await EventModePreset.aggregate([
      { $group: { _id: null, total: { $sum: '$usage.timesUsed' } } }
    ]);
    
    res.json({
      byEventType: stats,
      overall: {
        totalPresets,
        totalUsage: totalUsage[0]?.total || 0
      }
    });
  } catch (error) {
    console.error('Error getting usage statistics:', error);
    res.status(500).json({ 
      message: 'Error getting usage statistics',
      error: error.message 
    });
  }
});

module.exports = router;