const express = require('express');
const router = express.Router();
const contactSyncController = require('../controllers/contactSyncController');
const { isAuthenticated } = require('../../middleware/auth');

// Apply authentication middleware to all routes
router.use(isAuthenticated);

// Google Contacts Integration Routes
router.get('/google/auth', contactSyncController.getGoogleAuthUrl);
router.get('/google/callback', contactSyncController.handleGoogleCallback);
router.post('/google/configure', contactSyncController.configureGoogleSync);
router.post('/google/sync-now', contactSyncController.syncNowGoogle);
router.get('/google/status', contactSyncController.getGoogleSyncStatus);

// Apple Contacts Integration Routes
router.get('/apple/vcard', contactSyncController.generateAppleVCard);
router.post('/apple/configure', contactSyncController.configureAppleSync);
router.get('/apple/status', contactSyncController.getAppleSyncStatus);

// Common Routes
router.get('/logs', contactSyncController.getSyncLogs);

module.exports = router;