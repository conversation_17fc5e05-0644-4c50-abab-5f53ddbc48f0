const express = require('express');
const router = express.Router();
const { isAuthenticated: auth, isAdmin: admin } = require('../../middleware/auth');
const WayfindingRoute = require('../../models/WayfindingRoute');
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');

// @route   GET /api/wayfinding
// @desc    Get all wayfinding routes with filtering
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      routeType,
      buildingId,
      floorId,
      priority,
      difficulty,
      wheelchairAccessible,
      search,
      sortBy = 'name',
      sortOrder = 'asc',
      page = 1,
      limit = 50
    } = req.query;

    // Build filter
    const filter = { status: 'active' };
    
    if (routeType) filter.routeType = routeType;
    if (priority) filter.priority = priority;
    if (difficulty) filter.difficulty = difficulty;
    
    if (buildingId) {
      filter.$or = [
        { 'startPoint.buildingId': buildingId },
        { 'endPoint.buildingId': buildingId }
      ];
    }
    
    if (floorId) {
      filter.$or = [
        { 'startPoint.floorId': floorId },
        { 'endPoint.floorId': floorId }
      ];
    }
    
    if (wheelchairAccessible === 'true') {
      filter['accessibility.wheelchairAccessible'] = true;
    }
    
    if (search) {
      filter.$text = { $search: search };
    }

    // Check access permissions
    if (req.user.role !== 'admin') {
      filter.$or = [
        { isPublic: true },
        { createdBy: req.user.id },
        { allowedRoles: { $in: [req.user.role] } }
      ];
    }

    // Build sort
    let sortOptions = {};
    if (sortBy === 'popularity') {
      sortOptions['usage.timesGenerated'] = sortOrder === 'desc' ? -1 : 1;
    } else if (sortBy === 'distance') {
      // Would need aggregation for accurate sorting
      sortOptions.name = sortOrder === 'desc' ? -1 : 1;
    } else if (search) {
      sortOptions = { score: { $meta: 'textScore' } };
    } else {
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [routes, total] = await Promise.all([
      WayfindingRoute.find(filter, search ? { score: { $meta: 'textScore' } } : {})
        .populate('startPoint.buildingId', 'name code')
        .populate('startPoint.floorId', 'name level')
        .populate('endPoint.buildingId', 'name code')
        .populate('endPoint.floorId', 'name level')
        .populate('createdBy', 'name email')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      WayfindingRoute.countDocuments(filter)
    ]);

    res.json({
      routes,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching wayfinding routes:', error);
    res.status(500).json({ 
      message: 'Error fetching wayfinding routes',
      error: error.message 
    });
  }
});

// @route   GET /api/wayfinding/types
// @desc    Get available route types and options
// @access  Private
router.get('/types', auth, async (req, res) => {
  try {
    const types = {
      routeTypes: [
        { value: 'evacuation', label: 'Evacuation Routes', icon: 'directions_run', color: '#F44336', priority: 'emergency' },
        { value: 'accessibility', label: 'Accessibility Routes', icon: 'accessible', color: '#2196F3', priority: 'high' },
        { value: 'volunteer_guide', label: 'Volunteer Navigation', icon: 'volunteer_activism', color: '#4CAF50', priority: 'medium' },
        { value: 'visitor_directions', label: 'Visitor Directions', icon: 'directions_walk', color: '#FF9800', priority: 'medium' },
        { value: 'utility_access', label: 'Utility Access', icon: 'build', color: '#795548', priority: 'medium' },
        { value: 'event_setup', label: 'Event Setup Routes', icon: 'event', color: '#E91E63', priority: 'medium' },
        { value: 'safety_patrol', label: 'Safety Patrol', icon: 'security', color: '#673AB7', priority: 'high' },
        { value: 'tour_guide', label: 'Building Tours', icon: 'tour', color: '#009688', priority: 'low' },
        { value: 'custom', label: 'Custom Routes', icon: 'route', color: '#607D8B', priority: 'medium' }
      ],
      priorities: [
        { value: 'emergency', label: 'Emergency', color: '#F44336', icon: 'priority_high' },
        { value: 'high', label: 'High', color: '#FF9800', icon: 'keyboard_arrow_up' },
        { value: 'medium', label: 'Medium', color: '#2196F3', icon: 'remove' },
        { value: 'low', label: 'Low', color: '#4CAF50', icon: 'keyboard_arrow_down' }
      ],
      difficulties: [
        { value: 'easy', label: 'Easy', color: '#4CAF50', icon: 'sentiment_satisfied' },
        { value: 'moderate', label: 'Moderate', color: '#FF9800', icon: 'sentiment_neutral' },
        { value: 'difficult', label: 'Difficult', color: '#F44336', icon: 'sentiment_dissatisfied' }
      ],
      directions: [
        { value: 'straight', label: 'Continue Straight', icon: 'arrow_upward' },
        { value: 'left', label: 'Turn Left', icon: 'arrow_back' },
        { value: 'right', label: 'Turn Right', icon: 'arrow_forward' },
        { value: 'up', label: 'Go Up', icon: 'keyboard_arrow_up' },
        { value: 'down', label: 'Go Down', icon: 'keyboard_arrow_down' },
        { value: 'enter', label: 'Enter', icon: 'login' },
        { value: 'exit', label: 'Exit', icon: 'logout' },
        { value: 'stop', label: 'Stop/Destination', icon: 'stop' }
      ]
    };
    
    res.json(types);
  } catch (error) {
    console.error('Error getting wayfinding types:', error);
    res.status(500).json({ 
      message: 'Error getting wayfinding types',
      error: error.message 
    });
  }
});

// @route   GET /api/wayfinding/emergency/:buildingId
// @desc    Get emergency evacuation routes for building
// @access  Private
router.get('/emergency/:buildingId', auth, async (req, res) => {
  try {
    const { floorId } = req.query;
    
    const routes = await WayfindingRoute.findEmergencyRoutes(
      req.params.buildingId,
      floorId
    );
    
    res.json({ routes });
  } catch (error) {
    console.error('Error fetching emergency routes:', error);
    res.status(500).json({ 
      message: 'Error fetching emergency routes',
      error: error.message 
    });
  }
});

// @route   GET /api/wayfinding/search
// @desc    Search wayfinding routes
// @access  Private
router.get('/search', auth, async (req, res) => {
  try {
    const { q, ...filters } = req.query;
    
    if (!q || q.trim().length === 0) {
      return res.status(400).json({ message: 'Search query is required' });
    }
    
    const routes = await WayfindingRoute.searchRoutes(q, filters);
    
    res.json({ routes, query: q });
  } catch (error) {
    console.error('Error searching wayfinding routes:', error);
    res.status(500).json({ 
      message: 'Error searching wayfinding routes',
      error: error.message 
    });
  }
});

// @route   GET /api/wayfinding/:id
// @desc    Get specific wayfinding route
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const route = await WayfindingRoute.findById(req.params.id)
      .populate('startPoint.buildingId', 'name code')
      .populate('startPoint.floorId', 'name level')
      .populate('endPoint.buildingId', 'name code')
      .populate('endPoint.floorId', 'name level')
      .populate('createdBy', 'name email')
      .populate('verifiedBy', 'name email')
      .populate('usage.feedback.userId', 'name');

    if (!route) {
      return res.status(404).json({ message: 'Wayfinding route not found' });
    }

    // Check access permissions
    if (!route.isPublic && 
        route.createdBy._id.toString() !== req.user.id && 
        req.user.role !== 'admin' &&
        !route.allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied to this route' });
    }

    res.json(route);
  } catch (error) {
    console.error('Error fetching wayfinding route:', error);
    res.status(500).json({ 
      message: 'Error fetching wayfinding route',
      error: error.message 
    });
  }
});

// @route   POST /api/wayfinding
// @desc    Create new wayfinding route
// @access  Private (Admin/Facilities)
router.post('/', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'facilities', 'security'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const routeData = {
      ...req.body,
      createdBy: req.user.id
    };

    const route = new WayfindingRoute(routeData);
    
    // Validate route
    const validation = route.validateRoute();
    if (!validation.isValid) {
      return res.status(400).json({ 
        message: 'Route validation failed',
        errors: validation.issues 
      });
    }
    
    await route.save();
    
    await route.populate([
      { path: 'startPoint.buildingId', select: 'name code' },
      { path: 'endPoint.buildingId', select: 'name code' },
      { path: 'createdBy', select: 'name email' }
    ]);
    
    res.status(201).json(route);
  } catch (error) {
    console.error('Error creating wayfinding route:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error creating wayfinding route',
      error: error.message 
    });
  }
});

// @route   PUT /api/wayfinding/:id
// @desc    Update wayfinding route
// @access  Private (Creator or Admin)
router.put('/:id', auth, async (req, res) => {
  try {
    const route = await WayfindingRoute.findById(req.params.id);
    
    if (!route) {
      return res.status(404).json({ message: 'Wayfinding route not found' });
    }

    // Check permissions
    if (route.createdBy.toString() !== req.user.id && 
        req.user.role !== 'admin' &&
        !['facilities', 'security'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Not authorized to update this route' });
    }

    // Update route
    Object.assign(route, req.body);
    
    // Validate updated route
    const validation = route.validateRoute();
    if (!validation.isValid) {
      return res.status(400).json({ 
        message: 'Route validation failed',
        errors: validation.issues 
      });
    }
    
    await route.save();
    
    await route.populate([
      { path: 'startPoint.buildingId', select: 'name code' },
      { path: 'endPoint.buildingId', select: 'name code' },
      { path: 'createdBy', select: 'name email' }
    ]);
    
    res.json(route);
  } catch (error) {
    console.error('Error updating wayfinding route:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error updating wayfinding route',
      error: error.message 
    });
  }
});

// @route   DELETE /api/wayfinding/:id
// @desc    Delete wayfinding route
// @access  Private (Creator or Admin)
router.delete('/:id', auth, async (req, res) => {
  try {
    const route = await WayfindingRoute.findById(req.params.id);
    
    if (!route) {
      return res.status(404).json({ message: 'Wayfinding route not found' });
    }

    // Check permissions
    if (route.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this route' });
    }

    await WayfindingRoute.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Wayfinding route deleted successfully' });
  } catch (error) {
    console.error('Error deleting wayfinding route:', error);
    res.status(500).json({ 
      message: 'Error deleting wayfinding route',
      error: error.message 
    });
  }
});

// @route   POST /api/wayfinding/:id/generate-pdf
// @desc    Generate PDF for wayfinding route
// @access  Private
router.post('/:id/generate-pdf', auth, async (req, res) => {
  try {
    const route = await WayfindingRoute.findById(req.params.id)
      .populate('startPoint.buildingId', 'name code')
      .populate('startPoint.floorId', 'name level')
      .populate('endPoint.buildingId', 'name code')
      .populate('endPoint.floorId', 'name level');

    if (!route) {
      return res.status(404).json({ message: 'Wayfinding route not found' });
    }

    // Check access permissions
    if (!route.isPublic && 
        route.createdBy.toString() !== req.user.id && 
        req.user.role !== 'admin' &&
        !route.allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied to this route' });
    }

    // Record usage
    route.recordUsage('generated');
    await route.save();

    // Generate PDF
    const pdf = await generateRoutePDF(route, req.user);
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${route.name.replace(/[^a-z0-9]/gi, '_')}_wayfinding.pdf"`);
    
    pdf.pipe(res);
    pdf.end();
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ 
      message: 'Error generating PDF',
      error: error.message 
    });
  }
});

// @route   POST /api/wayfinding/:id/feedback
// @desc    Add feedback for route
// @access  Private
router.post('/:id/feedback', auth, async (req, res) => {
  try {
    const { rating, comment, improvements } = req.body;
    
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({ message: 'Rating must be between 1 and 5' });
    }
    
    const route = await WayfindingRoute.findById(req.params.id);
    
    if (!route) {
      return res.status(404).json({ message: 'Wayfinding route not found' });
    }

    route.addFeedback(req.user.id, rating, comment, improvements);
    await route.save();
    
    res.json({
      message: 'Feedback added successfully',
      averageRating: route.averageRating
    });
  } catch (error) {
    console.error('Error adding feedback:', error);
    res.status(500).json({ 
      message: 'Error adding feedback',
      error: error.message 
    });
  }
});

// @route   PUT /api/wayfinding/:id/verify
// @desc    Verify route accuracy (Facilities/Admin only)
// @access  Private
router.put('/:id/verify', auth, async (req, res) => {
  try {
    if (!['admin', 'facilities', 'security'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const route = await WayfindingRoute.findById(req.params.id);
    
    if (!route) {
      return res.status(404).json({ message: 'Wayfinding route not found' });
    }

    route.lastVerified = new Date();
    route.verifiedBy = req.user.id;
    route.needsUpdate = false;
    route.status = 'active';
    
    await route.save();
    
    res.json({
      message: 'Route verified successfully',
      verifiedAt: route.lastVerified
    });
  } catch (error) {
    console.error('Error verifying route:', error);
    res.status(500).json({ 
      message: 'Error verifying route',
      error: error.message 
    });
  }
});

// @route   GET /api/wayfinding/stats/overview
// @desc    Get wayfinding statistics overview
// @access  Private (Admin/Facilities)
router.get('/stats/overview', auth, async (req, res) => {
  try {
    if (!['admin', 'facilities', 'security'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const [
      totalRoutes,
      emergencyRoutes,
      needsVerification,
      recentUsage,
      popularRoutes
    ] = await Promise.all([
      WayfindingRoute.countDocuments({ status: 'active' }),
      WayfindingRoute.countDocuments({ routeType: 'evacuation', status: 'active' }),
      WayfindingRoute.countDocuments({ needsUpdate: true }),
      WayfindingRoute.aggregate([
        {
          $match: {
            'usage.lastGenerated': {
              $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        },
        {
          $group: {
            _id: null,
            totalGenerated: { $sum: '$usage.timesGenerated' }
          }
        }
      ]),
      WayfindingRoute.find({ status: 'active' })
        .sort({ 'usage.timesGenerated': -1 })
        .limit(5)
        .select('name routeType usage.timesGenerated')
    ]);
    
    res.json({
      overview: {
        totalRoutes,
        emergencyRoutes,
        needsVerification,
        recentUsage: recentUsage[0]?.totalGenerated || 0
      },
      popularRoutes
    });
  } catch (error) {
    console.error('Error fetching wayfinding statistics:', error);
    res.status(500).json({ 
      message: 'Error fetching wayfinding statistics',
      error: error.message 
    });
  }
});

// Helper function to generate PDF
async function generateRoutePDF(route, user) {
  const doc = new PDFDocument({
    size: route.pdfSettings.paperSize.toUpperCase(),
    layout: route.pdfSettings.orientation
  });
  
  const fontSize = route.pdfSettings.fontSize === 'small' ? 10 : 
                  route.pdfSettings.fontSize === 'large' ? 14 : 12;
  
  // Header
  doc.fontSize(18).text(route.name, { align: 'center' });
  doc.fontSize(fontSize).text(`Route Type: ${route.routeType.replace(/_/g, ' ').toUpperCase()}`, { align: 'center' });
  doc.moveDown();
  
  // Route overview
  doc.fontSize(14).text('Route Overview:', { underline: true });
  doc.fontSize(fontSize);
  doc.text(`From: ${route.startPoint.description}`);
  doc.text(`To: ${route.endPoint.description}`);
  doc.text(`Estimated Time: ${route.estimatedTime?.walking || 'N/A'} minutes`);
  doc.text(`Difficulty: ${route.difficulty.charAt(0).toUpperCase() + route.difficulty.slice(1)}`);
  doc.text(`Wheelchair Accessible: ${route.accessibility.wheelchairAccessible ? 'Yes' : 'No'}`);
  doc.moveDown();
  
  // Steps
  doc.fontSize(14).text('Directions:', { underline: true });
  doc.fontSize(fontSize);
  
  route.steps.forEach((step, index) => {
    doc.text(`${step.stepNumber}. ${step.instruction}`);
    if (step.warning) {
      doc.fillColor('red').text(`   ⚠️ Warning: ${step.warning}`);
      doc.fillColor('black');
    }
    if (step.distance?.description) {
      doc.text(`   Distance: ${step.distance.description}`);
    }
    doc.moveDown(0.5);
  });
  
  // Safety information
  if (route.pdfSettings.includeSafetyInfo && route.safetyInfo) {
    doc.addPage();
    doc.fontSize(14).text('Safety Information:', { underline: true });
    doc.fontSize(fontSize);
    
    if (route.safetyInfo.emergencyExits?.length > 0) {
      doc.text('Emergency Exits:');
      route.safetyInfo.emergencyExits.forEach(exit => {
        doc.text(`• ${exit.description}`);
      });
      doc.moveDown();
    }
    
    if (route.safetyInfo.aedLocations?.length > 0) {
      doc.text('AED Locations:');
      route.safetyInfo.aedLocations.forEach(aed => {
        doc.text(`• ${aed.description}`);
      });
      doc.moveDown();
    }
    
    if (route.safetyInfo.hazards?.length > 0) {
      doc.text('Safety Hazards:');
      route.safetyInfo.hazards.forEach(hazard => {
        doc.fillColor('red').text(`• ${hazard.description} (${hazard.severity.toUpperCase()})`);
        if (hazard.mitigation) {
          doc.fillColor('black').text(`  Mitigation: ${hazard.mitigation}`);
        }
      });
      doc.fillColor('black');
    }
  }
  
  // QR Code
  if (route.pdfSettings.includeQRCode) {
    const qrUrl = `${process.env.BASE_URL || 'http://localhost:6000'}/wayfinding/${route._id}`;
    try {
      const qrCode = await QRCode.toDataURL(qrUrl);
      doc.addPage();
      doc.fontSize(12).text('Scan for digital route:', { align: 'center' });
      doc.image(qrCode, {
        fit: [100, 100],
        align: 'center',
        valign: 'center'
      });
    } catch (qrError) {
      console.error('Error generating QR code:', qrError);
    }
  }
  
  // Footer
  doc.fontSize(8).text(`Generated by ${user.name} on ${new Date().toLocaleDateString()}`, {
    align: 'center'
  });
  
  return doc;
}

module.exports = router;