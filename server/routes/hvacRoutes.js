const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const HVACUnit = require('../../models/HVACUnit');
const Building = require('../../models/Building');
const Floor = require('../../models/Floor');
const FloorplanIcon = require('../../models/FloorplanIcon');

/**
 * HVAC Routes - Phase 4 Climate Heatmap + HVAC Service
 * Provides comprehensive HVAC unit management with filter tracking and service history
 */

// @route   GET /api/hvac/units
// @desc    Get all HVAC units with optional filtering
// @access  Private
router.get('/units', isAuthenticated, async (req, res) => {
  try {
    const {
      buildingId,
      floorId,
      unitType,
      status,
      zone,
      filtersDue,
      maintenanceDue,
      limit = 50,
      offset = 0,
      search
    } = req.query;

    // Build query
    let query = {};
    
    if (buildingId) query.buildingId = buildingId;
    if (floorId) query.floorId = floorId;
    if (unitType) query.unitType = unitType;
    if (status) query.status = status;
    if (zone) query.zone = zone;
    
    // Search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { unitId: { $regex: search, $options: 'i' } },
        { room: { $regex: search, $options: 'i' } },
        { 'specifications.manufacturer': { $regex: search, $options: 'i' } },
        { 'specifications.model': { $regex: search, $options: 'i' } }
      ];
    }

    // Filters due query
    if (filtersDue === 'true') {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      query['filters.nextChangeDate'] = { $lte: futureDate };
    }

    // Maintenance due query
    if (maintenanceDue === 'true') {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      query['maintenanceSchedule.nextInspection'] = { $lte: futureDate };
    }

    const units = await HVACUnit.find(query)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name code')
      .populate('iconId')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ room: 1, name: 1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset));

    const total = await HVACUnit.countDocuments(query);

    res.json({
      units,
      total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    console.error('Error fetching HVAC units:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   GET /api/hvac/units/:id
// @desc    Get specific HVAC unit by ID
// @access  Private
router.get('/units/:id', isAuthenticated, async (req, res) => {
  try {
    const unit = await HVACUnit.findById(req.params.id)
      .populate('buildingId', 'name code address')
      .populate('floorId', 'name code')
      .populate('iconId')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .populate('serviceHistory.performedBy', 'name email');

    if (!unit) {
      return res.status(404).json({ message: 'HVAC unit not found' });
    }

    res.json(unit);
  } catch (error) {
    console.error('Error fetching HVAC unit:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/hvac/units
// @desc    Create new HVAC unit
// @access  Private (Admin)
router.post('/units', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      unitId,
      name,
      unitType,
      buildingId,
      floorId,
      room,
      zone,
      specifications,
      filters,
      integrationSource,
      deviceId,
      x,
      y
    } = req.body;

    // Validate required fields
    if (!unitId || !name || !unitType || !buildingId || !room) {
      return res.status(400).json({ 
        message: 'Missing required fields: unitId, name, unitType, buildingId, room' 
      });
    }

    // Check if unitId already exists
    const existingUnit = await HVACUnit.findOne({ unitId });
    if (existingUnit) {
      return res.status(400).json({ message: 'Unit ID already exists' });
    }

    // Validate building exists
    const building = await Building.findById(buildingId);
    if (!building) {
      return res.status(400).json({ message: 'Building not found' });
    }

    // Validate floor if provided
    if (floorId) {
      const floor = await Floor.findById(floorId);
      if (!floor) {
        return res.status(400).json({ message: 'Floor not found' });
      }
    }

    // Create HVAC unit
    const hvacUnit = new HVACUnit({
      unitId,
      name,
      unitType,
      buildingId,
      floorId,
      room,
      zone,
      specifications,
      filters: filters || [],
      integrationSource,
      deviceId,
      x,
      y,
      createdBy: req.user._id
    });

    // Auto-calculate next change dates for filters
    hvacUnit.filters.forEach(filter => {
      if (!filter.nextChangeDate && filter.installDate && filter.changeFrequency) {
        filter.nextChangeDate = new Date(
          filter.installDate.getTime() + (filter.changeFrequency * 24 * 60 * 60 * 1000)
        );
      }
    });

    await hvacUnit.save();

    // Populate and return
    const populatedUnit = await HVACUnit.findById(hvacUnit._id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name code')
      .populate('createdBy', 'name email');

    res.status(201).json(populatedUnit);
  } catch (error) {
    console.error('Error creating HVAC unit:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   PUT /api/hvac/units/:id
// @desc    Update HVAC unit
// @access  Private (Admin)
router.put('/units/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const updates = { ...req.body };
    updates.updatedBy = req.user._id;

    // Auto-calculate next change dates for updated filters
    if (updates.filters) {
      updates.filters.forEach(filter => {
        if (!filter.nextChangeDate && filter.installDate && filter.changeFrequency) {
          filter.nextChangeDate = new Date(
            filter.installDate.getTime() + (filter.changeFrequency * 24 * 60 * 60 * 1000)
          );
        }
      });
    }

    const unit = await HVACUnit.findByIdAndUpdate(
      req.params.id,
      updates,
      { new: true, runValidators: true }
    )
    .populate('buildingId', 'name code')
    .populate('floorId', 'name code')
    .populate('updatedBy', 'name email');

    if (!unit) {
      return res.status(404).json({ message: 'HVAC unit not found' });
    }

    res.json(unit);
  } catch (error) {
    console.error('Error updating HVAC unit:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   DELETE /api/hvac/units/:id
// @desc    Delete HVAC unit
// @access  Private (Admin)
router.delete('/units/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const unit = await HVACUnit.findByIdAndDelete(req.params.id);

    if (!unit) {
      return res.status(404).json({ message: 'HVAC unit not found' });
    }

    res.json({ message: 'HVAC unit deleted successfully' });
  } catch (error) {
    console.error('Error deleting HVAC unit:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   GET /api/hvac/units/building/:buildingId/floor/:floorId
// @desc    Get HVAC units by location
// @access  Private
router.get('/units/building/:buildingId/floor/:floorId', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;
    
    const units = await HVACUnit.findByLocation(buildingId, floorId);
    res.json(units);
  } catch (error) {
    console.error('Error fetching HVAC units by location:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   GET /api/hvac/maintenance/due
// @desc    Get units requiring maintenance
// @access  Private
router.get('/maintenance/due', isAuthenticated, async (req, res) => {
  try {
    const { daysAhead = 30 } = req.query;
    
    const units = await HVACUnit.findMaintenanceDue(parseInt(daysAhead));
    res.json(units);
  } catch (error) {
    console.error('Error fetching maintenance due:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/hvac/units/:id/filters/:position/change
// @desc    Record filter change
// @access  Private (Admin)
router.post('/units/:id/filters/:position/change', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { id, position } = req.params;
    const changeDetails = req.body;

    const unit = await HVACUnit.findById(id);
    if (!unit) {
      return res.status(404).json({ message: 'HVAC unit not found' });
    }

    await unit.recordFilterChange(position, changeDetails, req.user._id);

    const updatedUnit = await HVACUnit.findById(id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name code');

    res.json(updatedUnit);
  } catch (error) {
    console.error('Error recording filter change:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/hvac/units/:id/service
// @desc    Add service record
// @access  Private (Admin)
router.post('/units/:id/service', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const serviceRecord = {
      ...req.body,
      performedBy: req.user._id,
      createdAt: new Date()
    };

    const unit = await HVACUnit.findByIdAndUpdate(
      id,
      { 
        $push: { serviceHistory: serviceRecord },
        updatedBy: req.user._id
      },
      { new: true, runValidators: true }
    )
    .populate('buildingId', 'name code')
    .populate('floorId', 'name code')
    .populate('serviceHistory.performedBy', 'name email');

    if (!unit) {
      return res.status(404).json({ message: 'HVAC unit not found' });
    }

    res.json(unit);
  } catch (error) {
    console.error('Error adding service record:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/hvac/units/:id/schedule-inspection
// @desc    Schedule inspection
// @access  Private (Admin)
router.post('/units/:id/schedule-inspection', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { inspectionDate } = req.body;

    if (!inspectionDate) {
      return res.status(400).json({ message: 'Inspection date is required' });
    }

    const unit = await HVACUnit.findById(id);
    if (!unit) {
      return res.status(404).json({ message: 'HVAC unit not found' });
    }

    await unit.scheduleInspection(new Date(inspectionDate), req.user._id);

    const updatedUnit = await HVACUnit.findById(id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name code');

    res.json(updatedUnit);
  } catch (error) {
    console.error('Error scheduling inspection:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   GET /api/hvac/alerts
// @desc    Get HVAC system alerts (overdue filters, maintenance)
// @access  Private
router.get('/alerts', isAuthenticated, async (req, res) => {
  try {
    const alerts = await HVACUnit.aggregate([
      {
        $match: {
          status: { $in: ['active', 'maintenance'] }
        }
      },
      {
        $project: {
          unitId: 1,
          name: 1,
          room: 1,
          unitType: 1,
          buildingId: 1,
          floorId: 1,
          alerts: {
            $concatArrays: [
              // Filter alerts
              {
                $map: {
                  input: {
                    $filter: {
                      input: '$filters',
                      cond: {
                        $or: [
                          { $lt: ['$$this.nextChangeDate', new Date()] }, // Overdue
                          { 
                            $lte: [
                              '$$this.nextChangeDate',
                              new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // Due within 14 days
                            ]
                          }
                        ]
                      }
                    }
                  },
                  as: 'filter',
                  in: {
                    type: 'filter',
                    severity: {
                      $cond: [
                        { $lt: ['$$filter.nextChangeDate', new Date()] },
                        'critical',
                        'warning'
                      ]
                    },
                    message: {
                      $concat: [
                        'Filter due for position: ',
                        '$$filter.position',
                        ' (Size: ',
                        '$$filter.size',
                        ')'
                      ]
                    },
                    dueDate: '$$filter.nextChangeDate',
                    filterPosition: '$$filter.position'
                  }
                }
              },
              // Maintenance alerts
              {
                $cond: [
                  {
                    $and: [
                      { $ne: ['$maintenanceSchedule.nextInspection', null] },
                      {
                        $lte: [
                          '$maintenanceSchedule.nextInspection',
                          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Due within 30 days
                        ]
                      }
                    ]
                  },
                  [{
                    type: 'maintenance',
                    severity: {
                      $cond: [
                        { $lt: ['$maintenanceSchedule.nextInspection', new Date()] },
                        'critical',
                        'warning'
                      ]
                    },
                    message: 'Routine inspection due',
                    dueDate: '$maintenanceSchedule.nextInspection'
                  }],
                  []
                ]
              }
            ]
          }
        }
      },
      {
        $match: {
          'alerts.0': { $exists: true } // Only return units with alerts
        }
      },
      {
        $lookup: {
          from: 'buildings',
          localField: 'buildingId',
          foreignField: '_id',
          as: 'building'
        }
      },
      {
        $lookup: {
          from: 'floors',
          localField: 'floorId',
          foreignField: '_id',
          as: 'floor'
        }
      },
      {
        $project: {
          unitId: 1,
          name: 1,
          room: 1,
          unitType: 1,
          building: { $arrayElemAt: ['$building.name', 0] },
          floor: { $arrayElemAt: ['$floor.name', 0] },
          alerts: 1
        }
      },
      {
        $sort: { 'alerts.dueDate': 1 }
      }
    ]);

    res.json(alerts);
  } catch (error) {
    console.error('Error fetching HVAC alerts:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @route   POST /api/hvac/seed-demo-data
// @desc    Create demo HVAC units for testing
// @access  Private (Admin)
router.post('/seed-demo-data', isAuthenticated, isAdmin, async (req, res) => {
  try {
    // Check if demo data already exists
    const existingUnits = await HVACUnit.countDocuments();
    if (existingUnits > 0) {
      return res.status(400).json({ message: 'HVAC units already exist. Clear existing data first.' });
    }

    // Get first building and floor for demo data
    const building = await Building.findOne();
    const floor = await Floor.findOne();

    if (!building) {
      return res.status(400).json({ message: 'No buildings found. Create a building first.' });
    }

    const demoUnits = [
      {
        unitId: 'HVAC-001',
        name: 'Main Sanctuary HVAC',
        unitType: 'air_handler',
        buildingId: building._id,
        floorId: floor?._id,
        room: 'Mechanical Room A',
        zone: 'Sanctuary',
        specifications: {
          manufacturer: 'Carrier',
          model: 'AHU-50-TC',
          capacity: {
            btu: 240000,
            tonnage: 20,
            cfm: 8000
          },
          efficiency: {
            seer: 14.5
          }
        },
        filters: [
          {
            filterType: 'pleated',
            size: '20x25x4',
            merv: 13,
            position: 'return_air',
            installDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
            changeFrequency: 90,
            brand: 'FilterBuy',
            partNumber: 'AFB-20x25x4-MERV13'
          }
        ],
        maintenanceSchedule: {
          routineInspectionFrequency: 180,
          nextInspection: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        },
        createdBy: req.user._id
      },
      {
        unitId: 'HVAC-002',
        name: 'Fellowship Hall HVAC',
        unitType: 'packaged_unit',
        buildingId: building._id,
        floorId: floor?._id,
        room: 'Roof Unit 2',
        zone: 'Fellowship',
        specifications: {
          manufacturer: 'Trane',
          model: 'PKG-25-RT',
          capacity: {
            btu: 300000,
            tonnage: 25,
            cfm: 10000
          }
        },
        filters: [
          {
            filterType: 'pleated',
            size: '16x20x2',
            merv: 11,
            position: 'return_air',
            installDate: new Date(Date.now() - 80 * 24 * 60 * 60 * 1000), // 80 days ago (due soon)
            changeFrequency: 90,
            brand: 'Honeywell'
          }
        ],
        createdBy: req.user._id
      },
      {
        unitId: 'HVAC-003',
        name: 'Office Area Mini-Split',
        unitType: 'mini_split',
        buildingId: building._id,
        floorId: floor?._id,
        room: 'Office Area',
        zone: 'Administrative',
        specifications: {
          manufacturer: 'Mitsubishi',
          model: 'MSZ-FH18NA',
          capacity: {
            btu: 18000,
            tonnage: 1.5
          },
          efficiency: {
            seer: 22
          }
        },
        filters: [
          {
            filterType: 'washable',
            size: 'Custom',
            position: 'indoor_unit',
            installDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // 6 months ago (overdue)
            changeFrequency: 120,
            brand: 'Mitsubishi OEM'
          }
        ],
        createdBy: req.user._id
      }
    ];

    const createdUnits = await HVACUnit.insertMany(demoUnits);

    res.json({
      message: 'Demo HVAC units created successfully',
      created: createdUnits.length,
      units: createdUnits.map(unit => ({
        id: unit._id,
        unitId: unit.unitId,
        name: unit.name,
        unitType: unit.unitType
      }))
    });
  } catch (error) {
    console.error('Error creating demo HVAC units:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;