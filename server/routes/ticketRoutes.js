const express = require('express');
const router = express.Router();
const ticketController = require('../controllers/ticketController');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const { body } = require('express-validator');

/**
 * Ticket Routes
 * Base path: /api/tickets
 */

// Validation middleware
const validateTicketCreation = [
  body('subject')
    .notEmpty()
    .withMessage('Subject is required')
    .isLength({ max: 255 })
    .withMessage('Subject must be less than 255 characters'),
  body('description')
    .notEmpty()
    .withMessage('Description is required'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority value'),
  body('type')
    .optional()
    .isIn(['incident', 'request', 'problem', 'change'])
    .withMessage('Invalid type value'),
  body('status')
    .optional()
    .isIn(['open', 'pending', 'on_hold', 'resolved', 'closed'])
    .withMessage('Invalid status value'),
  body('assignedTo')
    .optional()
    .customSanitizer(value => value === '' ? undefined : value)
    .isMongoId()
    .withMessage('Invalid assignedTo user ID'),
  body('assignedGroup')
    .optional()
    .customSanitizer(value => value === '' ? undefined : value)
    .isMongoId()
    .withMessage('Invalid assignedGroup ID'),
  body('requester')
    .optional()
    .isMongoId()
    .withMessage('Invalid requester user ID'),
  body('dueDate')
    .optional()
    .customSanitizer(value => value === null ? undefined : value)
    .isISO8601()
    .withMessage('Invalid due date format'),
  body('followers')
    .optional()
    .isArray()
    .withMessage('Followers must be an array'),
  body('followers.*')
    .optional()
    .isMongoId()
    .withMessage('Invalid follower user ID'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
];

const validateTicketUpdate = [
  body('subject')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Subject must be less than 255 characters'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority value'),
  body('status')
    .optional()
    .isIn(['open', 'pending', 'on_hold', 'resolved', 'closed'])
    .withMessage('Invalid status value'),
  body('type')
    .optional()
    .isIn(['incident', 'request', 'problem', 'change'])
    .withMessage('Invalid type value'),
  body('assignedTo')
    .optional()
    .customSanitizer(value => value === '' ? undefined : value)
    .isMongoId()
    .withMessage('Invalid assignedTo user ID'),
  body('assignedGroup')
    .optional()
    .customSanitizer(value => value === '' ? undefined : value)
    .isMongoId()
    .withMessage('Invalid assignedGroup ID'),
  body('requester')
    .optional()
    .isMongoId()
    .withMessage('Invalid requester user ID'),
  body('dueDate')
    .optional()
    .customSanitizer(value => value === null ? undefined : value)
    .isISO8601()
    .withMessage('Invalid due date format'),
  body('followers')
    .optional()
    .isArray()
    .withMessage('Followers must be an array'),
  body('followers.*')
    .optional()
    .isMongoId()
    .withMessage('Invalid follower user ID'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
];

const validateComment = [
  body('content')
    .notEmpty()
    .withMessage('Comment content is required'),
  body('type')
    .optional()
    .isIn(['comment', 'internal_note', 'system', 'email_inbound', 'email_outbound'])
    .withMessage('Invalid comment type'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean'),
  body('attachments')
    .optional()
    .isArray()
    .withMessage('Attachments must be an array')
];

// Get all tickets with filtering and pagination
// GET /api/tickets
router.get('/', isAuthenticated, ticketController.getAllTickets);

// Get ticket statistics
// GET /api/tickets/stats
router.get('/stats', isAuthenticated, ticketController.getTicketStats);

// Get ticket by ID
// GET /api/tickets/:id
router.get('/:id', isAuthenticated, ticketController.getTicketById);

// Create a new ticket
// POST /api/tickets
router.post('/', isAuthenticated, validateTicketCreation, ticketController.createTicket);

// Update a ticket
// PUT /api/tickets/:id
router.put('/:id', isAuthenticated, validateTicketUpdate, ticketController.updateTicket);

// Delete a ticket (admin only)
// DELETE /api/tickets/:id
router.delete('/:id', isAuthenticated, isAdmin, ticketController.deleteTicket);

// Add a comment to a ticket
// POST /api/tickets/:id/comments
router.post('/:id/comments', isAuthenticated, validateComment, ticketController.addComment);

module.exports = router;