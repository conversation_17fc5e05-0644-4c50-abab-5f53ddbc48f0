const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const Building = require('../../models/Building');
const Floor = require('../../models/Floor');

// Import existing HVAC and WiFi services for data sources
const dreoAPI = require('../integrations/dreo/dreoAPI');
const goveeAPI = require('../integrations/govee/goveeAPI');
const skyportCloudAPI = require('../integrations/skyportcloud/skyportcloudAPI');
const lgThinqAPI = require('../integrations/lgThinq/lgThinqAPI');
const unifiNetworkAPI = require('../integrations/unifiNetwork/unifiNetworkAPI');

/**
 * Coverage Zones API - Temperature Heatmap and WiFi Coverage
 * Leverages existing smart device integrations to minimize code duplication
 */

// @route   GET /api/coverage/temperature-zones
// @desc    Get temperature coverage zones for heatmap visualization
// @access  Private
router.get('/temperature-zones', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId, type = 'interpolated', resolution = 'medium' } = req.query;

    if (!buildingId || !floorId) {
      return res.status(400).json({ message: 'Building ID and Floor ID are required' });
    }

    // Get temperature data from all available sources
    const temperatureData = await getTemperatureDataFromSources(buildingId, floorId);
    
    // Generate coverage zones based on sensor data
    const coverageZones = generateTemperatureCoverageZones(temperatureData, type, resolution);
    
    // Get floor dimensions for proper scaling
    const floor = await Floor.findById(floorId);
    const floorDimensions = {
      width: floor?.floorplanImage?.width || 1000,
      height: floor?.floorplanImage?.height || 800
    };

    res.json({
      zones: coverageZones,
      metadata: {
        buildingId,
        floorId,
        type,
        resolution,
        sensorCount: temperatureData.length,
        floorDimensions,
        generatedAt: new Date(),
        sources: getTemperatureSourceCounts(temperatureData)
      }
    });
  } catch (error) {
    console.error('Error generating temperature coverage zones:', error);
    res.status(500).json({ message: 'Server error generating temperature zones' });
  }
});

// @route   GET /api/coverage/wifi-zones
// @desc    Get WiFi coverage zones for signal strength visualization
// @access  Private
router.get('/wifi-zones', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId, type = 'signal_strength', band = 'all' } = req.query;

    if (!buildingId || !floorId) {
      return res.status(400).json({ message: 'Building ID and Floor ID are required' });
    }

    // Get WiFi access point data from UniFi Network
    const wifiData = await getWiFiDataFromUniFi(buildingId, floorId, band);
    
    // Generate WiFi coverage zones
    const coverageZones = generateWiFiCoverageZones(wifiData, type);
    
    // Get floor dimensions
    const floor = await Floor.findById(floorId);
    const floorDimensions = {
      width: floor?.floorplanImage?.width || 1000,
      height: floor?.floorplanImage?.height || 800
    };

    res.json({
      zones: coverageZones,
      metadata: {
        buildingId,
        floorId,
        type,
        band,
        accessPointCount: wifiData.length,
        floorDimensions,
        generatedAt: new Date(),
        coverage: calculateWiFiCoverageStats(coverageZones)
      }
    });
  } catch (error) {
    console.error('Error generating WiFi coverage zones:', error);
    res.status(500).json({ message: 'Server error generating WiFi zones' });
  }
});

// @route   GET /api/coverage/composite
// @desc    Get composite coverage data combining temperature and WiFi
// @access  Private
router.get('/composite', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.query;

    if (!buildingId || !floorId) {
      return res.status(400).json({ message: 'Building ID and Floor ID are required' });
    }

    // Get both temperature and WiFi data in parallel
    const [temperatureData, wifiData] = await Promise.all([
      getTemperatureDataFromSources(buildingId, floorId),
      getWiFiDataFromUniFi(buildingId, floorId)
    ]);

    // Generate both types of zones
    const temperatureZones = generateTemperatureCoverageZones(temperatureData, 'interpolated', 'medium');
    const wifiZones = generateWiFiCoverageZones(wifiData, 'signal_strength');

    // Get floor dimensions
    const floor = await Floor.findById(floorId);
    const floorDimensions = {
      width: floor?.floorplanImage?.width || 1000,
      height: floor?.floorplanImage?.height || 800
    };

    res.json({
      temperature: {
        zones: temperatureZones,
        sensorCount: temperatureData.length,
        sources: getTemperatureSourceCounts(temperatureData)
      },
      wifi: {
        zones: wifiZones,
        accessPointCount: wifiData.length,
        coverage: calculateWiFiCoverageStats(wifiZones)
      },
      metadata: {
        buildingId,
        floorId,
        floorDimensions,
        generatedAt: new Date()
      }
    });
  } catch (error) {
    console.error('Error generating composite coverage data:', error);
    res.status(500).json({ message: 'Server error generating composite coverage' });
  }
});

// @route   POST /api/coverage/manual-zone
// @desc    Create manual coverage zone for areas without sensors
// @access  Admin
router.post('/manual-zone', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { buildingId, floorId, type, polygon, value, metadata } = req.body;

    if (!buildingId || !floorId || !type || !polygon || value === undefined) {
      return res.status(400).json({ 
        message: 'Building ID, Floor ID, type, polygon, and value are required' 
      });
    }

    // Create manual zone (stored in database or returned as immediate zone)
    const manualZone = {
      id: `manual_${Date.now()}`,
      type: 'manual',
      coverageType: type, // 'temperature' or 'wifi'
      polygon: polygon,
      value: value,
      metadata: {
        ...metadata,
        createdBy: req.user.id,
        createdAt: new Date(),
        buildingId,
        floorId
      }
    };

    // For now, return the manual zone. In production, you might store it in a ManualCoverageZone model
    res.status(201).json({
      zone: manualZone,
      message: 'Manual coverage zone created successfully'
    });
  } catch (error) {
    console.error('Error creating manual coverage zone:', error);
    res.status(500).json({ message: 'Server error creating manual zone' });
  }
});

// Helper function to get temperature data from all available sources
async function getTemperatureDataFromSources(buildingId, floorId) {
  const temperatureData = [];

  try {
    // Get Dreo device temperatures
    const dreoDevices = await dreoAPI.getDevices();
    for (const device of dreoDevices) {
      if (device.buildingId === buildingId && device.floorId === floorId && device.status?.temperature) {
        temperatureData.push({
          id: `dreo_${device.deviceId}`,
          source: 'dreo',
          x: device.location?.x || 0,
          y: device.location?.y || 0,
          temperature: device.status.temperature,
          timestamp: new Date(device.status.lastUpdate || Date.now())
        });
      }
    }
  } catch (error) {
    console.warn('Failed to get Dreo temperature data:', error.message);
  }

  try {
    // Get Govee sensor temperatures
    const goveeDevices = await goveeAPI.getDevices();
    for (const device of goveeDevices) {
      if (device.buildingId === buildingId && device.floorId === floorId) {
        const deviceState = await goveeAPI.getDeviceState(device.device, device.model);
        const tempProperty = deviceState.properties?.find(p => p.name === 'temperature');
        
        if (tempProperty) {
          temperatureData.push({
            id: `govee_${device.device}`,
            source: 'govee',
            x: device.location?.x || 0,
            y: device.location?.y || 0,
            temperature: tempProperty.value,
            timestamp: new Date()
          });
        }
      }
    }
  } catch (error) {
    console.warn('Failed to get Govee temperature data:', error.message);
  }

  try {
    // Get Skyport Cloud temperatures
    const skyportDevices = await skyportCloudAPI.getDevices();
    for (const device of skyportDevices) {
      if (device.buildingId === buildingId && device.floorId === floorId && device.sensors) {
        for (const sensor of device.sensors) {
          if (sensor.type === 'temperature' && sensor.temperature) {
            temperatureData.push({
              id: `skyport_${device.id}_${sensor.id}`,
              source: 'skyport',
              x: device.location?.x || 0,
              y: device.location?.y || 0,
              temperature: sensor.temperature,
              timestamp: new Date(sensor.lastUpdate || Date.now())
            });
          }
        }
      }
    }
  } catch (error) {
    console.warn('Failed to get Skyport temperature data:', error.message);
  }

  try {
    // Get LG ThinQ temperatures
    const lgDevices = await lgThinqAPI.getDevices();
    for (const device of lgDevices) {
      if (device.buildingId === buildingId && device.floorId === floorId) {
        const deviceStatus = await lgThinqAPI.getDeviceStatus(device.deviceId);
        if (deviceStatus.temperature !== undefined) {
          temperatureData.push({
            id: `lg_${device.deviceId}`,
            source: 'lgThinq',
            x: device.location?.x || 0,
            y: device.location?.y || 0,
            temperature: deviceStatus.temperature,
            timestamp: new Date(deviceStatus.lastUpdate || Date.now())
          });
        }
      }
    }
  } catch (error) {
    console.warn('Failed to get LG ThinQ temperature data:', error.message);
  }

  return temperatureData;
}

// Helper function to get WiFi data from UniFi Network
async function getWiFiDataFromUniFi(buildingId, floorId, band = 'all') {
  try {
    const accessPoints = await unifiNetworkAPI.getAccessPoints();
    
    return accessPoints
      .filter(ap => ap.buildingId === buildingId && ap.floorId === floorId)
      .map(ap => ({
        id: ap._id,
        name: ap.name,
        x: ap.x || 0,
        y: ap.y || 0,
        signalStrength: calculateSignalStrength(ap),
        channel: getChannelForBand(ap, band),
        clientCount: ap.num_clients || 0,
        status: ap.state === 1 ? 'online' : 'offline'
      }));
  } catch (error) {
    console.warn('Failed to get UniFi WiFi data:', error.message);
    return [];
  }
}

// Helper function to generate temperature coverage zones using interpolation
function generateTemperatureCoverageZones(temperatureData, type, resolution) {
  if (temperatureData.length === 0) return [];

  const zones = [];
  const gridSize = resolution === 'high' ? 20 : resolution === 'medium' ? 40 : 80;

  // Simple grid-based interpolation
  for (let x = 0; x < 1000; x += gridSize) {
    for (let y = 0; y < 800; y += gridSize) {
      const interpolatedTemp = interpolateTemperatureAtPoint(x, y, temperatureData);
      
      if (interpolatedTemp !== null) {
        zones.push({
          type: 'temperature',
          polygon: [
            [x, y],
            [x + gridSize, y],
            [x + gridSize, y + gridSize],
            [x, y + gridSize]
          ],
          value: interpolatedTemp,
          intensity: normalizeTemperature(interpolatedTemp),
          metadata: {
            gridSize,
            nearestSensors: findNearestSensors(x + gridSize/2, y + gridSize/2, temperatureData, 3)
          }
        });
      }
    }
  }

  return zones;
}

// Helper function to generate WiFi coverage zones
function generateWiFiCoverageZones(wifiData, type) {
  if (wifiData.length === 0) return [];

  const zones = [];
  const gridSize = 60; // Larger grid for WiFi coverage

  for (let x = 0; x < 1000; x += gridSize) {
    for (let y = 0; y < 800; y += gridSize) {
      const signalStrength = calculateWiFiSignalAtPoint(x + gridSize/2, y + gridSize/2, wifiData);
      
      if (signalStrength > 0) {
        zones.push({
          type: 'wifi',
          polygon: [
            [x, y],
            [x + gridSize, y],
            [x + gridSize, y + gridSize],
            [x, y + gridSize]
          ],
          value: signalStrength,
          intensity: signalStrength / 100, // Normalize to 0-1
          metadata: {
            gridSize,
            nearestAPs: findNearestAPs(x + gridSize/2, y + gridSize/2, wifiData, 2)
          }
        });
      }
    }
  }

  return zones;
}

// Helper functions
function interpolateTemperatureAtPoint(x, y, sensors) {
  if (sensors.length === 0) return null;
  
  const distances = sensors.map(sensor => ({
    sensor,
    distance: Math.sqrt(Math.pow(x - sensor.x, 2) + Math.pow(y - sensor.y, 2))
  }));
  
  // Inverse distance weighting
  const maxDistance = 200; // Max influence distance
  const influencingSensors = distances.filter(d => d.distance <= maxDistance);
  
  if (influencingSensors.length === 0) return null;
  
  let weightedTemp = 0;
  let totalWeight = 0;
  
  influencingSensors.forEach(({ sensor, distance }) => {
    const weight = distance === 0 ? 1000 : 1 / Math.pow(distance, 2);
    weightedTemp += sensor.temperature * weight;
    totalWeight += weight;
  });
  
  return totalWeight > 0 ? weightedTemp / totalWeight : null;
}

function calculateWiFiSignalAtPoint(x, y, accessPoints) {
  let maxSignal = 0;
  
  accessPoints.forEach(ap => {
    if (ap.status === 'offline') return;
    
    const distance = Math.sqrt(Math.pow(x - ap.x, 2) + Math.pow(y - ap.y, 2));
    const maxRange = 150; // Approximate WiFi range
    
    if (distance <= maxRange) {
      // Simple signal strength calculation
      const signal = Math.max(0, 100 - (distance / maxRange) * 100);
      maxSignal = Math.max(maxSignal, signal);
    }
  });
  
  return maxSignal;
}

function normalizeTemperature(temp) {
  // Normalize temperature for color mapping (60-85°F range)
  const minTemp = 60;
  const maxTemp = 85;
  return Math.max(0, Math.min(1, (temp - minTemp) / (maxTemp - minTemp)));
}

function findNearestSensors(x, y, sensors, count) {
  return sensors
    .map(sensor => ({
      ...sensor,
      distance: Math.sqrt(Math.pow(x - sensor.x, 2) + Math.pow(y - sensor.y, 2))
    }))
    .sort((a, b) => a.distance - b.distance)
    .slice(0, count);
}

function findNearestAPs(x, y, accessPoints, count) {
  return accessPoints
    .map(ap => ({
      ...ap,
      distance: Math.sqrt(Math.pow(x - ap.x, 2) + Math.pow(y - ap.y, 2))
    }))
    .sort((a, b) => a.distance - b.distance)
    .slice(0, count);
}

function calculateSignalStrength(ap) {
  // Calculate signal strength from UniFi AP data
  if (!ap.radio_table || ap.radio_table.length === 0) return 50;
  
  const avgTxPower = ap.radio_table.reduce((sum, radio) => sum + (radio.tx_power || 0), 0) / ap.radio_table.length;
  return Math.min(100, Math.max(0, avgTxPower * 4)); // Scale to 0-100
}

function getChannelForBand(ap, band) {
  if (!ap.radio_table) return null;
  
  const radio = ap.radio_table.find(r => {
    if (band === '2.4ghz') return r.channel <= 14;
    if (band === '5ghz') return r.channel >= 36 && r.channel <= 165;
    return true; // 'all'
  });
  
  return radio?.channel || null;
}

function getTemperatureSourceCounts(data) {
  return data.reduce((counts, sensor) => {
    counts[sensor.source] = (counts[sensor.source] || 0) + 1;
    return counts;
  }, {});
}

function calculateWiFiCoverageStats(zones) {
  if (zones.length === 0) return { coverage: 0, averageSignal: 0 };
  
  const totalArea = zones.length;
  const coveredArea = zones.filter(zone => zone.value > 30).length; // Minimum usable signal
  const averageSignal = zones.reduce((sum, zone) => sum + zone.value, 0) / zones.length;
  
  return {
    coverage: (coveredArea / totalArea) * 100,
    averageSignal: Math.round(averageSignal),
    totalZones: totalArea,
    coveredZones: coveredArea
  };
}

module.exports = router;