const express = require('express');
const router = express.Router();
const AppleBusinessManagerAPI = require('../integrations/appleBusinessManager/appleBusinessManagerAPI');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Helper function to get API client
const getApiClient = () => {
  try {
    // Check if required environment variables are set
    if (!process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID || 
        !process.env.APPLE_BUSINESS_MANAGER_KEY_ID || 
        !process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY) {
      console.error('Apple Business Manager environment variables are not properly configured');
      return null;
    }

    return new AppleBusinessManagerAPI(
      process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID,
      process.env.APPLE_BUSINESS_MANAGER_KEY_ID,
      process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY,
      process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY
    );
  } catch (error) {
    console.error('Error creating Apple Business Manager API client:', error);
    return null;
  }
};

// Configuration is now managed through environment variables

// Get all devices
router.get('/devices', isAuthenticated, async (req, res) => {
  try {
    const api = getApiClient();
    if (!api) {
      return res.status(404).json({ message: 'Apple Business Manager not configured' });
    }

    const devices = await api.getDevices(req.query);
    res.json(devices);
  } catch (error) {
    console.error('Error fetching Apple Business Manager devices:', error);
    res.status(500).json({ message: 'Error fetching devices', error: error.message });
  }
});

// Get device details
router.get('/devices/:deviceId', isAuthenticated, async (req, res) => {
  try {
    const api = getApiClient();
    if (!api) {
      return res.status(404).json({ message: 'Apple Business Manager not configured' });
    }

    const device = await api.getDeviceDetails(req.params.deviceId);
    res.json(device);
  } catch (error) {
    console.error(`Error fetching Apple Business Manager device ${req.params.deviceId}:`, error);
    res.status(500).json({ message: 'Error fetching device details', error: error.message });
  }
});

// Get all users
router.get('/users', isAuthenticated, async (req, res) => {
  try {
    const api = getApiClient();
    if (!api) {
      return res.status(404).json({ message: 'Apple Business Manager not configured' });
    }

    const users = await api.getUsers(req.query);
    res.json(users);
  } catch (error) {
    console.error('Error fetching Apple Business Manager users:', error);
    res.status(500).json({ message: 'Error fetching users', error: error.message });
  }
});

// Get all locations
router.get('/locations', isAuthenticated, async (req, res) => {
  try {
    const api = getApiClient();
    if (!api) {
      return res.status(404).json({ message: 'Apple Business Manager not configured' });
    }

    const locations = await api.getLocations(req.query);
    res.json(locations);
  } catch (error) {
    console.error('Error fetching Apple Business Manager locations:', error);
    res.status(500).json({ message: 'Error fetching locations', error: error.message });
  }
});

// Get all apps
router.get('/apps', isAuthenticated, async (req, res) => {
  try {
    const api = getApiClient();
    if (!api) {
      return res.status(404).json({ message: 'Apple Business Manager not configured' });
    }

    const apps = await api.getApps(req.query);
    res.json(apps);
  } catch (error) {
    console.error('Error fetching Apple Business Manager apps:', error);
    res.status(500).json({ message: 'Error fetching apps', error: error.message });
  }
});

// Assign app to device
router.post('/apps/:appId/assign', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const api = getApiClient();
    if (!api) {
      return res.status(404).json({ message: 'Apple Business Manager not configured' });
    }

    const { deviceId } = req.body;
    if (!deviceId) {
      return res.status(400).json({ message: 'Device ID is required' });
    }

    const result = await api.assignAppToDevice(req.params.appId, deviceId);
    res.json(result);
  } catch (error) {
    console.error(`Error assigning app ${req.params.appId} to device:`, error);
    res.status(500).json({ message: 'Error assigning app to device', error: error.message });
  }
});

// One-click setup has been removed as configuration is now managed through environment variables

module.exports = router;
