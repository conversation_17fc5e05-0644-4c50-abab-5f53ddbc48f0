const express = require('express');
const router = express.Router();
const roomController = require('../controllers/roomController');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

/**
 * Room Routes
 * Base path: /api/rooms
 */

// Get all rooms
// GET /api/rooms
router.get('/', isAuthenticated, roomController.getAllRooms);

// Find available rooms
// GET /api/rooms/available
// Query params: startTime, endTime, capacity, features, buildingId, floorId
router.get('/available', isAuthenticated, roomController.findAvailableRooms);

// Get room by ID
// GET /api/rooms/:id
router.get('/:id', isAuthenticated, roomController.getRoomById);

// Create a new room (admin only)
// POST /api/rooms
router.post('/', isAuthenticated, isAdmin, roomController.createRoom);

// Update a room (admin only)
// PUT /api/rooms/:id
router.put('/:id', isAuthenticated, isAdmin, roomController.updateRoom);

// Delete a room (admin only)
// DELETE /api/rooms/:id
router.delete('/:id', isAuthenticated, isAdmin, roomController.deleteRoom);

module.exports = router;
