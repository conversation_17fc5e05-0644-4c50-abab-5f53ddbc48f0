const express = require('express');
const router = express.Router();
const Building = require('../../models/Building');
const Floor = require('../../models/Floor');
const FloorplanIcon = require('../../models/FloorplanIcon');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Get all buildings
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const buildings = await Building.find().sort({ name: 1 });
    res.json(buildings);
  } catch (err) {
    console.error('Error fetching buildings:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get building by ID
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const building = await Building.findById(req.params.id);

    if (!building) {
      return res.status(404).json({ message: 'Building not found' });
    }

    res.json(building);
  } catch (err) {
    console.error('Error fetching building:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create a new building
router.post('/', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      name,
      address,
      description,
      buildingType,
      status,
      contactInfo,
      metadata
    } = req.body;

    // Create new building
    const newBuilding = new Building({
      name,
      address,
      description,
      buildingType,
      status,
      contactInfo,
      metadata,
      createdBy: req.user.id
    });

    const building = await newBuilding.save();
    res.status(201).json(building);
  } catch (err) {
    console.error('Error creating building:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update a building
router.put('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      name,
      address,
      description,
      buildingType,
      status,
      contactInfo,
      metadata
    } = req.body;

    // Find building by ID
    let building = await Building.findById(req.params.id);

    if (!building) {
      return res.status(404).json({ message: 'Building not found' });
    }

    // Update building fields
    building.name = name || building.name;
    building.address = address || building.address;
    building.description = description || building.description;
    building.buildingType = buildingType || building.buildingType;
    building.status = status || building.status;
    building.contactInfo = contactInfo || building.contactInfo;
    building.metadata = metadata || building.metadata;
    building.updatedAt = Date.now();

    // Save updated building
    building = await building.save();
    res.json(building);
  } catch (err) {
    console.error('Error updating building:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete a building
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    // Find building by ID
    const building = await Building.findById(req.params.id);

    if (!building) {
      return res.status(404).json({ message: 'Building not found' });
    }

    // Delete all floors associated with this building
    const floors = await Floor.find({ buildingId: req.params.id });

    // Delete all floorplan icons associated with these floors
    for (const floor of floors) {
      await FloorplanIcon.deleteMany({ floorId: floor._id });
    }

    // Delete all floors
    await Floor.deleteMany({ buildingId: req.params.id });

    // Delete the building
    await Building.findByIdAndDelete(req.params.id);

    res.json({ message: 'Building deleted successfully' });
  } catch (err) {
    console.error('Error deleting building:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get all floors for a building
router.get('/:id/floors', isAuthenticated, async (req, res) => {
  try {
    const floors = await Floor.find({ buildingId: req.params.id }).sort({ level: 1 });
    res.json(floors);
  } catch (err) {
    console.error('Error fetching floors:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
