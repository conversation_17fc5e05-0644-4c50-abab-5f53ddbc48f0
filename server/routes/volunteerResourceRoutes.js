const express = require('express');
const router = express.Router();
const { isAuthenticated: auth, isAdmin: admin } = require('../../middleware/auth');
const VolunteerResource = require('../../models/VolunteerResource');

// @route   GET /api/volunteer-resources
// @desc    Get all volunteer resources with filtering
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      category,
      subcategory,
      buildingId,
      floorId,
      room,
      status,
      resourceType,
      search,
      tags,
      sortBy = 'name',
      sortOrder = 'asc',
      page = 1,
      limit = 50,
      includeUnavailable = false
    } = req.query;

    // Build filter
    const filter = {};
    
    if (category) filter.category = category;
    if (subcategory) filter.subcategory = subcategory;
    if (buildingId) filter.buildingId = buildingId;
    if (floorId) filter.floorId = floorId;
    if (room) filter.room = new RegExp(room, 'i');
    if (resourceType) filter.resourceType = resourceType;
    
    if (status) {
      filter.status = status;
    } else if (!includeUnavailable) {
      filter.status = { $in: ['available', 'reserved'] };
    }
    
    if (tags) {
      const tagArray = tags.split(',');
      filter.tags = { $in: tagArray };
    }
    
    if (search) {
      filter.$text = { $search: search };
    }

    // Build sort
    let sortOptions = {};
    if (sortBy === 'popularity') {
      sortOptions['usage.popularityScore'] = sortOrder === 'desc' ? -1 : 1;
    } else if (sortBy === 'usage') {
      sortOptions['usage.timesUsed'] = sortOrder === 'desc' ? -1 : 1;
    } else if (sortBy === 'lastUsed') {
      sortOptions['usage.lastUsed'] = sortOrder === 'desc' ? -1 : 1;
    } else if (sortBy === 'condition') {
      sortOptions['specifications.condition'] = sortOrder === 'desc' ? -1 : 1;
    } else if (search) {
      sortOptions = { score: { $meta: 'textScore' } };
    } else {
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [resources, total] = await Promise.all([
      VolunteerResource.find(filter, search ? { score: { $meta: 'textScore' } } : {})
        .populate('buildingId', 'name code')
        .populate('floorId', 'name level')
        .populate('createdBy', 'name email')
        .populate('lastUpdatedBy', 'name email')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      VolunteerResource.countDocuments(filter)
    ]);

    res.json({
      resources,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching volunteer resources:', error);
    res.status(500).json({ 
      message: 'Error fetching volunteer resources',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/categories
// @desc    Get resource categories with counts
// @access  Private
router.get('/categories', auth, async (req, res) => {
  try {
    const { buildingId, floorId } = req.query;
    
    const matchFilter = { status: { $ne: 'missing' } };
    if (buildingId) matchFilter.buildingId = buildingId;
    if (floorId) matchFilter.floorId = floorId;
    
    const categories = await VolunteerResource.aggregate([
      { $match: matchFilter },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          availableCount: {
            $sum: { $cond: [{ $eq: ['$status', 'available'] }, 1, 0] }
          },
          subcategories: { $addToSet: '$subcategory' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    const categoryInfo = {
      cleaning_supplies: { label: 'Cleaning Supplies', icon: 'cleaning_services' },
      event_supplies: { label: 'Event Supplies', icon: 'event' },
      av_equipment: { label: 'AV Equipment', icon: 'mic' },
      signage: { label: 'Signage', icon: 'signpost' },
      kitchen_supplies: { label: 'Kitchen Supplies', icon: 'kitchen' },
      office_supplies: { label: 'Office Supplies', icon: 'inventory' },
      safety_equipment: { label: 'Safety Equipment', icon: 'safety_check' },
      maintenance_tools: { label: 'Maintenance Tools', icon: 'handyman' },
      setup_equipment: { label: 'Setup Equipment', icon: 'moving' },
      storage_area: { label: 'Storage Areas', icon: 'warehouse' },
      other: { label: 'Other', icon: 'category' }
    };

    const enrichedCategories = categories.map(cat => ({
      ...cat,
      ...categoryInfo[cat._id],
      subcategories: cat.subcategories.filter(sub => sub) // Remove null/undefined
    }));

    res.json(enrichedCategories);
  } catch (error) {
    console.error('Error fetching resource categories:', error);
    res.status(500).json({ 
      message: 'Error fetching resource categories',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/search
// @desc    Search resources with advanced filters
// @access  Private
router.get('/search', auth, async (req, res) => {
  try {
    const { q, ...filters } = req.query;
    
    if (!q || q.trim().length === 0) {
      return res.status(400).json({ message: 'Search query is required' });
    }
    
    const resources = await VolunteerResource.searchResources(q, filters);
    
    res.json({ resources, query: q });
  } catch (error) {
    console.error('Error searching volunteer resources:', error);
    res.status(500).json({ 
      message: 'Error searching volunteer resources',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/nearest
// @desc    Find nearest resources to a location
// @access  Private
router.get('/nearest', auth, async (req, res) => {
  try {
    const { floorId, category, limit = 5 } = req.query;
    
    if (!floorId) {
      return res.status(400).json({ message: 'Floor ID is required' });
    }
    
    const resources = await VolunteerResource.findNearest(
      floorId, 
      category, 
      parseInt(limit)
    );
    
    res.json({ resources, floorId, category });
  } catch (error) {
    console.error('Error finding nearest resources:', error);
    res.status(500).json({ 
      message: 'Error finding nearest resources',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/popular
// @desc    Get most popular resources
// @access  Private
router.get('/popular', auth, async (req, res) => {
  try {
    const { category, limit = 10 } = req.query;
    
    const filter = { status: 'available' };
    if (category) filter.category = category;
    
    const resources = await VolunteerResource.find(filter)
      .sort({ 'usage.popularityScore': -1, 'usage.timesUsed': -1 })
      .limit(parseInt(limit))
      .populate('buildingId', 'name')
      .populate('floorId', 'name level');
    
    res.json({ resources });
  } catch (error) {
    console.error('Error fetching popular resources:', error);
    res.status(500).json({ 
      message: 'Error fetching popular resources',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/maintenance-alerts
// @desc    Get resources needing maintenance
// @access  Private (Admin/Facilities)
router.get('/maintenance-alerts', auth, async (req, res) => {
  try {
    // Check if user has permission to view maintenance info
    if (!['admin', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const resources = await VolunteerResource.getMaintenanceAlerts();
    
    res.json({ resources });
  } catch (error) {
    console.error('Error fetching maintenance alerts:', error);
    res.status(500).json({ 
      message: 'Error fetching maintenance alerts',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/:id
// @desc    Get specific volunteer resource
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const resource = await VolunteerResource.findById(req.params.id)
      .populate('buildingId', 'name code')
      .populate('floorId', 'name level')
      .populate('createdBy', 'name email')
      .populate('lastUpdatedBy', 'name email')
      .populate('usage.usageHistory.userId', 'name')
      .populate('usage.usageHistory.eventId', 'name eventDate');

    if (!resource) {
      return res.status(404).json({ message: 'Volunteer resource not found' });
    }

    res.json(resource);
  } catch (error) {
    console.error('Error fetching volunteer resource:', error);
    res.status(500).json({ 
      message: 'Error fetching volunteer resource',
      error: error.message 
    });
  }
});

// @route   POST /api/volunteer-resources
// @desc    Create new volunteer resource
// @access  Private (Admin/Facilities)
router.post('/', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const resourceData = {
      ...req.body,
      createdBy: req.user.id,
      lastUpdatedBy: req.user.id
    };

    const resource = new VolunteerResource(resourceData);
    await resource.save();
    
    await resource.populate([
      { path: 'buildingId', select: 'name code' },
      { path: 'floorId', select: 'name level' },
      { path: 'createdBy', select: 'name email' }
    ]);
    
    res.status(201).json(resource);
  } catch (error) {
    console.error('Error creating volunteer resource:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error creating volunteer resource',
      error: error.message 
    });
  }
});

// @route   PUT /api/volunteer-resources/:id
// @desc    Update volunteer resource
// @access  Private (Admin/Facilities)
router.put('/:id', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const resource = await VolunteerResource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ message: 'Volunteer resource not found' });
    }

    // Update resource
    Object.assign(resource, req.body);
    resource.lastUpdatedBy = req.user.id;
    
    await resource.save();
    
    await resource.populate([
      { path: 'buildingId', select: 'name code' },
      { path: 'floorId', select: 'name level' },
      { path: 'lastUpdatedBy', select: 'name email' }
    ]);
    
    res.json(resource);
  } catch (error) {
    console.error('Error updating volunteer resource:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error updating volunteer resource',
      error: error.message 
    });
  }
});

// @route   DELETE /api/volunteer-resources/:id
// @desc    Delete volunteer resource
// @access  Private (Admin only)
router.delete('/:id', [auth, admin], async (req, res) => {
  try {
    const resource = await VolunteerResource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ message: 'Volunteer resource not found' });
    }

    await VolunteerResource.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'Volunteer resource deleted successfully' });
  } catch (error) {
    console.error('Error deleting volunteer resource:', error);
    res.status(500).json({ 
      message: 'Error deleting volunteer resource',
      error: error.message 
    });
  }
});

// @route   POST /api/volunteer-resources/:id/use
// @desc    Record resource usage
// @access  Private
router.post('/:id/use', auth, async (req, res) => {
  try {
    const { quantity = 1, eventId, notes = '' } = req.body;
    
    const resource = await VolunteerResource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ message: 'Volunteer resource not found' });
    }

    if (resource.status !== 'available') {
      return res.status(400).json({ 
        message: `Resource is currently ${resource.status} and cannot be used` 
      });
    }

    if (resource.resourceType === 'consumable' && resource.quantity.available < quantity) {
      return res.status(400).json({ 
        message: `Insufficient quantity available. Requested: ${quantity}, Available: ${resource.quantity.available}` 
      });
    }

    // Record usage
    resource.recordUsage(req.user.id, quantity, eventId, notes);
    await resource.save();
    
    res.json({
      message: 'Resource usage recorded successfully',
      resource: {
        id: resource._id,
        name: resource.name,
        remainingQuantity: resource.quantity.available,
        status: resource.status
      }
    });
  } catch (error) {
    console.error('Error recording resource usage:', error);
    res.status(500).json({ 
      message: 'Error recording resource usage',
      error: error.message 
    });
  }
});

// @route   POST /api/volunteer-resources/:id/maintenance
// @desc    Update maintenance information
// @access  Private (Admin/Facilities)
router.post('/:id/maintenance', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const { maintenanceNotes, condition, nextCheck } = req.body;
    
    const resource = await VolunteerResource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ message: 'Volunteer resource not found' });
    }

    // Update maintenance info
    resource.maintenance.lastChecked = new Date();
    resource.maintenance.checkedBy = req.user.name || req.user.email;
    
    if (maintenanceNotes) {
      resource.maintenance.maintenanceNotes = maintenanceNotes;
    }
    
    if (condition) {
      resource.specifications.condition = condition;
    }
    
    if (nextCheck) {
      resource.maintenance.nextCheck = new Date(nextCheck);
    }
    
    // Clear maintenance alerts
    resource.alerts = resource.alerts.filter(alert => 
      alert.type !== 'maintenance_due' || alert.acknowledged
    );
    
    await resource.save();
    
    res.json({
      message: 'Maintenance information updated successfully',
      maintenance: resource.maintenance,
      condition: resource.specifications.condition
    });
  } catch (error) {
    console.error('Error updating maintenance information:', error);
    res.status(500).json({ 
      message: 'Error updating maintenance information',
      error: error.message 
    });
  }
});

// @route   POST /api/volunteer-resources/:id/alerts/:alertId/acknowledge
// @desc    Acknowledge an alert
// @access  Private
router.post('/:id/alerts/:alertId/acknowledge', auth, async (req, res) => {
  try {
    const resource = await VolunteerResource.findById(req.params.id);
    
    if (!resource) {
      return res.status(404).json({ message: 'Volunteer resource not found' });
    }

    const alert = resource.alerts.id(req.params.alertId);
    
    if (!alert) {
      return res.status(404).json({ message: 'Alert not found' });
    }

    alert.acknowledged = true;
    alert.acknowledgedBy = req.user.id;
    alert.acknowledgedAt = new Date();
    
    await resource.save();
    
    res.json({
      message: 'Alert acknowledged successfully',
      alert
    });
  } catch (error) {
    console.error('Error acknowledging alert:', error);
    res.status(500).json({ 
      message: 'Error acknowledging alert',
      error: error.message 
    });
  }
});

// @route   GET /api/volunteer-resources/stats/overview
// @desc    Get resource statistics overview
// @access  Private (Admin/Facilities)
router.get('/stats/overview', auth, async (req, res) => {
  try {
    if (!['admin', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const [
      totalResources,
      availableResources,
      maintenanceNeeded,
      categoryStats,
      recentUsage
    ] = await Promise.all([
      VolunteerResource.countDocuments(),
      VolunteerResource.countDocuments({ status: 'available' }),
      VolunteerResource.countDocuments({
        $or: [
          { 'maintenance.nextCheck': { $lt: new Date() } },
          { 'alerts.type': 'maintenance_due', 'alerts.acknowledged': false }
        ]
      }),
      VolunteerResource.aggregate([
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            available: { $sum: { $cond: [{ $eq: ['$status', 'available'] }, 1, 0] } }
          }
        }
      ]),
      VolunteerResource.aggregate([
        { $unwind: '$usage.usageHistory' },
        {
          $match: {
            'usage.usageHistory.usedAt': {
              $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        },
        {
          $group: {
            _id: null,
            totalUsage: { $sum: 1 }
          }
        }
      ])
    ]);
    
    res.json({
      overview: {
        totalResources,
        availableResources,
        maintenanceNeeded,
        utilizationRate: totalResources > 0 ? 
          Math.round((recentUsage[0]?.totalUsage || 0) / totalResources * 100) : 0
      },
      categoryStats,
      recentUsage: recentUsage[0]?.totalUsage || 0
    });
  } catch (error) {
    console.error('Error fetching resource statistics:', error);
    res.status(500).json({ 
      message: 'Error fetching resource statistics',
      error: error.message 
    });
  }
});

module.exports = router;