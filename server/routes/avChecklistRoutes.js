const express = require('express');
const router = express.Router();
const { isAuthenticated: auth, isAdmin: admin } = require('../../middleware/auth');
const AVChecklist = require('../../models/AVChecklist');

// @route   GET /api/av-checklists
// @desc    Get all AV checklists with filtering
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      checklistType,
      serviceType,
      isTemplate,
      assignedTo,
      search,
      sortBy = 'name',
      sortOrder = 'asc',
      page = 1,
      limit = 50
    } = req.query;

    // Build filter
    const filter = {};
    
    if (checklistType) filter.checklistType = checklistType;
    if (serviceType) filter.serviceType = serviceType;
    if (isTemplate !== undefined) filter.isTemplate = isTemplate === 'true';
    if (assignedTo) filter['assignedTeams.members'] = assignedTo;
    
    if (search) {
      filter.$or = [
        { name: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') }
      ];
    }

    // Build sort
    const sortOptions = {};
    if (sortBy === 'completionRate') {
      // This would need aggregation for accurate sorting
      sortOptions.name = sortOrder === 'desc' ? -1 : 1;
    } else {
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [checklists, total] = await Promise.all([
      AVChecklist.find(filter)
        .populate('createdBy', 'name email')
        .populate('assignedTeams.members', 'name email role')
        .populate('parentTemplate', 'name')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      AVChecklist.countDocuments(filter)
    ]);

    res.json({
      checklists,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / parseInt(limit)),
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error fetching AV checklists:', error);
    res.status(500).json({ 
      message: 'Error fetching AV checklists',
      error: error.message 
    });
  }
});

// @route   GET /api/av-checklists/templates
// @desc    Get template checklists
// @access  Private
router.get('/templates', auth, async (req, res) => {
  try {
    const templates = await AVChecklist.getTemplates();
    res.json(templates);
  } catch (error) {
    console.error('Error fetching checklist templates:', error);
    res.status(500).json({ 
      message: 'Error fetching checklist templates',
      error: error.message 
    });
  }
});

// @route   GET /api/av-checklists/due
// @desc    Get due checklists for current user
// @access  Private
router.get('/due', auth, async (req, res) => {
  try {
    const { includeAll } = req.query;
    const userId = includeAll === 'true' ? null : req.user.id;
    
    const checklists = await AVChecklist.getDueChecklists(userId);
    res.json(checklists);
  } catch (error) {
    console.error('Error fetching due checklists:', error);
    res.status(500).json({ 
      message: 'Error fetching due checklists',
      error: error.message 
    });
  }
});

// @route   GET /api/av-checklists/types
// @desc    Get available checklist and service types
// @access  Private
router.get('/types', auth, async (req, res) => {
  try {
    const types = {
      checklistTypes: [
        { value: 'pre_service', label: 'Pre-Service Check', icon: 'play_arrow', color: '#4CAF50' },
        { value: 'post_service', label: 'Post-Service Check', icon: 'stop', color: '#FF9800' },
        { value: 'weekly_maintenance', label: 'Weekly Maintenance', icon: 'schedule', color: '#2196F3' },
        { value: 'monthly_maintenance', label: 'Monthly Maintenance', icon: 'event_repeat', color: '#673AB7' },
        { value: 'event_setup', label: 'Event Setup', icon: 'event', color: '#E91E63' },
        { value: 'emergency_check', label: 'Emergency Check', icon: 'warning', color: '#F44336' },
        { value: 'custom', label: 'Custom', icon: 'settings', color: '#607D8B' }
      ],
      serviceTypes: [
        { value: 'sunday_service', label: 'Sunday Service', icon: 'church', color: '#8BC34A' },
        { value: 'youth_service', label: 'Youth Service', icon: 'groups', color: '#E91E63' },
        { value: 'special_event', label: 'Special Event', icon: 'celebration', color: '#FF9800' },
        { value: 'conference', label: 'Conference', icon: 'event', color: '#673AB7' },
        { value: 'wedding', label: 'Wedding', icon: 'favorite', color: '#E91E63' },
        { value: 'funeral', label: 'Funeral', icon: 'local_florist', color: '#607D8B' },
        { value: 'general', label: 'General', icon: 'category', color: '#757575' }
      ],
      priorities: [
        { value: 'critical', label: 'Critical', color: '#F44336', icon: 'priority_high' },
        { value: 'high', label: 'High', color: '#FF9800', icon: 'keyboard_arrow_up' },
        { value: 'medium', label: 'Medium', color: '#2196F3', icon: 'remove' },
        { value: 'low', label: 'Low', color: '#4CAF50', icon: 'keyboard_arrow_down' }
      ],
      categories: [
        { value: 'audio', label: 'Audio', icon: 'volume_up', color: '#FF9800' },
        { value: 'video', label: 'Video', icon: 'videocam', color: '#2196F3' },
        { value: 'lighting', label: 'Lighting', icon: 'lightbulb', color: '#FFEB3B' },
        { value: 'recording', label: 'Recording', icon: 'fiber_manual_record', color: '#F44336' },
        { value: 'streaming', label: 'Streaming', icon: 'cast', color: '#9C27B0' },
        { value: 'presentation', label: 'Presentation', icon: 'slideshow', color: '#4CAF50' },
        { value: 'microphones', label: 'Microphones', icon: 'mic', color: '#795548' },
        { value: 'monitors', label: 'Monitors', icon: 'monitor', color: '#607D8B' },
        { value: 'general', label: 'General', icon: 'settings', color: '#757575' }
      ]
    };
    
    res.json(types);
  } catch (error) {
    console.error('Error getting checklist types:', error);
    res.status(500).json({ 
      message: 'Error getting checklist types',
      error: error.message 
    });
  }
});

// @route   GET /api/av-checklists/:id
// @desc    Get specific AV checklist
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const checklist = await AVChecklist.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('assignedTeams.members', 'name email role')
      .populate('parentTemplate', 'name')
      .populate('completions.completedBy', 'name email')
      .populate('completions.eventId', 'name eventDate');

    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    res.json(checklist);
  } catch (error) {
    console.error('Error fetching AV checklist:', error);
    res.status(500).json({ 
      message: 'Error fetching AV checklist',
      error: error.message 
    });
  }
});

// @route   POST /api/av-checklists
// @desc    Create new AV checklist
// @access  Private (Admin/AV Tech)
router.post('/', auth, async (req, res) => {
  try {
    // Check permissions
    if (!['admin', 'av_tech', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const checklistData = {
      ...req.body,
      createdBy: req.user.id
    };

    const checklist = new AVChecklist(checklistData);
    await checklist.save();
    
    await checklist.populate([
      { path: 'createdBy', select: 'name email' },
      { path: 'assignedTeams.members', select: 'name email role' }
    ]);
    
    res.status(201).json(checklist);
  } catch (error) {
    console.error('Error creating AV checklist:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error creating AV checklist',
      error: error.message 
    });
  }
});

// @route   POST /api/av-checklists/from-template/:templateId
// @desc    Create checklist from template
// @access  Private
router.post('/from-template/:templateId', auth, async (req, res) => {
  try {
    const customizations = {
      ...req.body,
      createdBy: req.user.id
    };
    
    const checklist = await AVChecklist.createFromTemplate(req.params.templateId, customizations);
    await checklist.save();
    
    await checklist.populate([
      { path: 'createdBy', select: 'name email' },
      { path: 'parentTemplate', select: 'name' }
    ]);
    
    res.status(201).json(checklist);
  } catch (error) {
    console.error('Error creating checklist from template:', error);
    
    if (error.message === 'Template not found') {
      return res.status(404).json({ message: 'Template not found' });
    }
    
    res.status(500).json({ 
      message: 'Error creating checklist from template',
      error: error.message 
    });
  }
});

// @route   PUT /api/av-checklists/:id
// @desc    Update AV checklist
// @access  Private (Creator or Admin)
router.put('/:id', auth, async (req, res) => {
  try {
    const checklist = await AVChecklist.findById(req.params.id);
    
    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    // Check permissions
    const isAssignedMember = checklist.assignedTeams.some(team => 
      team.members.some(member => member.toString() === req.user.id)
    );
    
    if (checklist.createdBy.toString() !== req.user.id && 
        req.user.role !== 'admin' && 
        !isAssignedMember) {
      return res.status(403).json({ message: 'Not authorized to update this checklist' });
    }

    // Update checklist
    Object.assign(checklist, req.body);
    await checklist.save();
    
    await checklist.populate([
      { path: 'createdBy', select: 'name email' },
      { path: 'assignedTeams.members', select: 'name email role' }
    ]);
    
    res.json(checklist);
  } catch (error) {
    console.error('Error updating AV checklist:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error',
        errors 
      });
    }
    
    res.status(500).json({ 
      message: 'Error updating AV checklist',
      error: error.message 
    });
  }
});

// @route   DELETE /api/av-checklists/:id
// @desc    Delete AV checklist
// @access  Private (Creator or Admin)
router.delete('/:id', auth, async (req, res) => {
  try {
    const checklist = await AVChecklist.findById(req.params.id);
    
    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    // Check permissions
    if (checklist.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this checklist' });
    }

    await AVChecklist.findByIdAndDelete(req.params.id);
    
    res.json({ message: 'AV checklist deleted successfully' });
  } catch (error) {
    console.error('Error deleting AV checklist:', error);
    res.status(500).json({ 
      message: 'Error deleting AV checklist',
      error: error.message 
    });
  }
});

// @route   POST /api/av-checklists/:id/start
// @desc    Start checklist execution
// @access  Private
router.post('/:id/start', auth, async (req, res) => {
  try {
    const { eventId } = req.body;
    
    const checklist = await AVChecklist.findById(req.params.id);
    
    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    // Check if user is assigned to this checklist
    const isAssigned = checklist.assignedTeams.some(team => 
      team.members.some(member => member.toString() === req.user.id)
    ) || req.user.role === 'admin';

    if (!isAssigned) {
      return res.status(403).json({ message: 'Not authorized to execute this checklist' });
    }

    const execution = checklist.startExecution(req.user.id, eventId);
    await checklist.save();
    
    res.json({
      message: 'Checklist execution started successfully',
      execution: {
        id: execution._id,
        startedAt: execution.startedAt,
        status: execution.status,
        itemCount: execution.itemResults.length
      }
    });
  } catch (error) {
    console.error('Error starting checklist execution:', error);
    res.status(500).json({ 
      message: 'Error starting checklist execution',
      error: error.message 
    });
  }
});

// @route   PUT /api/av-checklists/:id/executions/:executionId/items/:itemId
// @desc    Complete checklist item
// @access  Private
router.put('/:id/executions/:executionId/items/:itemId', auth, async (req, res) => {
  try {
    const { status, notes, issues, verificationData, timeSpent } = req.body;
    
    const checklist = await AVChecklist.findById(req.params.id);
    
    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    const execution = checklist.completions.id(req.params.executionId);
    if (!execution) {
      return res.status(404).json({ message: 'Execution not found' });
    }

    // Check if user owns this execution
    if (execution.completedBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to update this execution' });
    }

    const result = {
      status,
      notes,
      issues,
      verificationData,
      timeSpent
    };

    const itemResult = checklist.completeItem(req.params.executionId, req.params.itemId, result);
    await checklist.save();
    
    res.json({
      message: 'Item completed successfully',
      itemResult,
      executionStatus: execution.status
    });
  } catch (error) {
    console.error('Error completing checklist item:', error);
    res.status(500).json({ 
      message: 'Error completing checklist item',
      error: error.message 
    });
  }
});

// @route   PUT /api/av-checklists/:id/executions/:executionId/complete
// @desc    Complete entire checklist execution
// @access  Private
router.put('/:id/executions/:executionId/complete', auth, async (req, res) => {
  try {
    const { overallNotes, recommendations, followUpActions } = req.body;
    
    const checklist = await AVChecklist.findById(req.params.id);
    
    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    const execution = checklist.completions.id(req.params.executionId);
    if (!execution) {
      return res.status(404).json({ message: 'Execution not found' });
    }

    // Check if user owns this execution
    if (execution.completedBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to complete this execution' });
    }

    const completedExecution = checklist.completeExecution(
      req.params.executionId, 
      overallNotes, 
      recommendations
    );
    
    // Add follow-up actions if provided
    if (followUpActions && followUpActions.length > 0) {
      completedExecution.followUpActions = followUpActions;
      completedExecution.followUpRequired = true;
    }
    
    await checklist.save();
    
    res.json({
      message: 'Checklist execution completed successfully',
      execution: completedExecution,
      analytics: checklist.analytics
    });
  } catch (error) {
    console.error('Error completing checklist execution:', error);
    res.status(500).json({ 
      message: 'Error completing checklist execution',
      error: error.message 
    });
  }
});

// @route   GET /api/av-checklists/:id/analytics
// @desc    Get checklist analytics
// @access  Private
router.get('/:id/analytics', auth, async (req, res) => {
  try {
    const checklist = await AVChecklist.findById(req.params.id)
      .populate('completions.completedBy', 'name email');

    if (!checklist) {
      return res.status(404).json({ message: 'AV checklist not found' });
    }

    // Update analytics before returning
    checklist.updateAnalytics();
    await checklist.save();

    const analytics = {
      ...checklist.analytics.toObject(),
      totalExecutions: checklist.completions.length,
      completedExecutions: checklist.completions.filter(c => c.status === 'completed').length,
      failedExecutions: checklist.completions.filter(c => c.status === 'failed').length,
      recentExecutions: checklist.completions
        .filter(c => c.completedAt && c.completedAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
        .length
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching checklist analytics:', error);
    res.status(500).json({ 
      message: 'Error fetching checklist analytics',
      error: error.message 
    });
  }
});

// @route   GET /api/av-checklists/stats/overview
// @desc    Get overall checklist statistics
// @access  Private (Admin/AV Tech)
router.get('/stats/overview', auth, async (req, res) => {
  try {
    if (!['admin', 'av_tech', 'facilities'].includes(req.user.role)) {
      return res.status(403).json({ message: 'Access denied' });
    }
    
    const [
      totalChecklists,
      templates,
      recentExecutions,
      avgCompletionTime,
      commonIssues
    ] = await Promise.all([
      AVChecklist.countDocuments({ isTemplate: false }),
      AVChecklist.countDocuments({ isTemplate: true }),
      AVChecklist.aggregate([
        { $unwind: '$completions' },
        {
          $match: {
            'completions.completedAt': {
              $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
          }
        },
        { $count: 'total' }
      ]),
      AVChecklist.aggregate([
        { $unwind: '$completions' },
        { $match: { 'completions.status': 'completed' } },
        {
          $group: {
            _id: null,
            avgTime: { $avg: '$completions.totalTimeSpent' }
          }
        }
      ]),
      AVChecklist.aggregate([
        { $unwind: '$analytics.commonIssues' },
        {
          $group: {
            _id: '$analytics.commonIssues.issueDescription',
            frequency: { $sum: '$analytics.commonIssues.frequency' }
          }
        },
        { $sort: { frequency: -1 } },
        { $limit: 5 }
      ])
    ]);
    
    res.json({
      overview: {
        totalChecklists,
        templates,
        recentExecutions: recentExecutions[0]?.total || 0,
        averageCompletionTime: Math.round(avgCompletionTime[0]?.avgTime || 0)
      },
      commonIssues: commonIssues.map(issue => ({
        description: issue._id,
        frequency: issue.frequency
      }))
    });
  } catch (error) {
    console.error('Error fetching checklist statistics:', error);
    res.status(500).json({ 
      message: 'Error fetching checklist statistics',
      error: error.message 
    });
  }
});

module.exports = router;