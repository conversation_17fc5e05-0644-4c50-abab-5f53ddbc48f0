const express = require('express');
const mongoose = require('mongoose');
const ElectricalPanel = require('../../models/ElectricalPanel');
const ElectricalCircuit = require('../../models/ElectricalCircuit');
const ElectricalOutlet = require('../../models/ElectricalOutlet');

const router = express.Router();

// Utility: parse pagination (not strictly used, return arrays for client simplicity)
function buildRegex(str) {
  try {
    return new RegExp(str, 'i');
  } catch (e) {
    return null;
  }
}

// Panels
router.get('/panels', async (req, res) => {
  try {
    const { buildingId, floorId, search } = req.query;
    const filter = {};
    if (buildingId) filter.buildingId = buildingId;
    if (floorId) filter.floorId = floorId;
    if (search) {
      const rx = buildRegex(search);
      if (rx) filter.$or = [{ name: rx }, { code: rx }, { room: rx }];
    }
    const panels = await ElectricalPanel.find(filter).sort({ name: 1 }).lean();
    res.json(panels);
  } catch (err) {
    console.error('GET /panels error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.get('/panels/:id', async (req, res) => {
  try {
    const panel = await ElectricalPanel.findById(req.params.id).lean();
    if (!panel) return res.status(404).json({ message: 'Panel not found' });
    res.json(panel);
  } catch (err) {
    console.error('GET /panels/:id error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.post('/panels', async (req, res) => {
  try {
    const { buildingId, floorId, name } = req.body;
    if (!buildingId || !floorId || !name) {
      return res.status(400).json({ message: 'buildingId, floorId, and name are required' });
    }
    const panel = await ElectricalPanel.create(req.body);
    res.status(201).json(panel);
  } catch (err) {
    console.error('POST /panels error', err);
    if (err.code === 11000) return res.status(409).json({ message: 'Duplicate panel' });
    res.status(500).json({ message: 'Server error' });
  }
});

router.put('/panels/:id', async (req, res) => {
  try {
    const panel = await ElectricalPanel.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!panel) return res.status(404).json({ message: 'Panel not found' });
    res.json(panel);
  } catch (err) {
    console.error('PUT /panels/:id error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.delete('/panels/:id', async (req, res) => {
  try {
    const { force } = req.query;
    const panelId = req.params.id;

    const circuitsCount = await ElectricalCircuit.countDocuments({ panelId });
    const outletsCount = await ElectricalOutlet.countDocuments({ panelId });
    if ((circuitsCount > 0 || outletsCount > 0) && force !== 'true') {
      return res.status(409).json({ message: 'Panel has related circuits or outlets. Use force=true to delete.' });
    }

    await ElectricalCircuit.deleteMany({ panelId });
    await ElectricalOutlet.updateMany({ panelId }, { $unset: { panelId: 1, circuitId: 1 } });
    await ElectricalPanel.findByIdAndDelete(panelId);
    res.status(204).send();
  } catch (err) {
    console.error('DELETE /panels/:id error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Circuits
router.get('/panels/:id/circuits', async (req, res) => {
  try {
    const panelId = req.params.id;
    const circuits = await ElectricalCircuit.find({ panelId }).sort({ number: 1 }).lean();
    res.json(circuits);
  } catch (err) {
    console.error('GET /panels/:id/circuits error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.get('/circuits', async (req, res) => {
  try {
    const { panelId, number } = req.query;
    const filter = {};
    if (panelId) filter.panelId = panelId;
    if (number) filter.number = Number(number);
    const circuits = await ElectricalCircuit.find(filter).sort({ number: 1 }).lean();
    res.json(circuits);
  } catch (err) {
    console.error('GET /circuits error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.get('/circuits/:circuitId', async (req, res) => {
  try {
    const c = await ElectricalCircuit.findById(req.params.circuitId).lean();
    if (!c) return res.status(404).json({ message: 'Circuit not found' });
    res.json(c);
  } catch (err) {
    console.error('GET /circuits/:circuitId error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.post('/panels/:id/circuits', async (req, res) => {
  try {
    const panelId = req.params.id;
    const { number } = req.body;
    if (!number) return res.status(400).json({ message: 'number is required' });

    const circuit = await ElectricalCircuit.create({ ...req.body, panelId });
    res.status(201).json(circuit);
  } catch (err) {
    console.error('POST /panels/:id/circuits error', err);
    if (err.code === 11000) return res.status(409).json({ message: 'Duplicate circuit number for panel' });
    res.status(500).json({ message: 'Server error' });
  }
});

router.put('/circuits/:circuitId', async (req, res) => {
  try {
    const c = await ElectricalCircuit.findByIdAndUpdate(req.params.circuitId, req.body, { new: true });
    if (!c) return res.status(404).json({ message: 'Circuit not found' });
    res.json(c);
  } catch (err) {
    console.error('PUT /circuits/:circuitId error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.delete('/circuits/:circuitId', async (req, res) => {
  try {
    const circuitId = req.params.circuitId;
    await ElectricalOutlet.updateMany({ circuitId }, { $unset: { circuitId: 1 } });
    await ElectricalCircuit.findByIdAndDelete(circuitId);
    res.status(204).send();
  } catch (err) {
    console.error('DELETE /circuits/:circuitId error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Outlets
router.get('/outlets', async (req, res) => {
  try {
    const { floorId, panelId, circuitId, label, room, buildingId } = req.query;
    const filter = {};
    if (buildingId) filter.buildingId = buildingId;
    if (floorId) filter.floorId = floorId;
    if (panelId) filter.panelId = panelId;
    if (circuitId) filter.circuitId = circuitId;
    if (room) filter.room = buildRegex(room) || room;
    if (label) {
      const rx = buildRegex(label);
      if (rx) filter.label = rx; else filter.label = label;
    }
    const outlets = await ElectricalOutlet.find(filter).lean();
    res.json(outlets);
  } catch (err) {
    console.error('GET /outlets error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.get('/outlets/:id', async (req, res) => {
  try {
    const outlet = await ElectricalOutlet.findById(req.params.id).lean();
    if (!outlet) return res.status(404).json({ message: 'Outlet not found' });
    res.json(outlet);
  } catch (err) {
    console.error('GET /outlets/:id error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.post('/outlets', async (req, res) => {
  try {
    const { label } = req.body;
    if (!label) return res.status(400).json({ message: 'label is required' });

    // validate panel/circuit consistency if provided
    if (req.body.circuitId && req.body.panelId) {
      const circuit = await ElectricalCircuit.findById(req.body.circuitId);
      if (!circuit) return res.status(422).json({ message: 'circuitId not found' });
      if (String(circuit.panelId) !== String(req.body.panelId)) {
        return res.status(422).json({ message: 'circuitId does not belong to panelId' });
      }
    }

    const outlet = await ElectricalOutlet.create(req.body);
    res.status(201).json(outlet);
  } catch (err) {
    console.error('POST /outlets error', err);
    if (err.code === 11000) return res.status(409).json({ message: 'Duplicate outlet' });
    res.status(500).json({ message: 'Server error' });
  }
});

router.put('/outlets/:id', async (req, res) => {
  try {
    if (req.body.circuitId && req.body.panelId) {
      const circuit = await ElectricalCircuit.findById(req.body.circuitId);
      if (!circuit) return res.status(422).json({ message: 'circuitId not found' });
      if (String(circuit.panelId) !== String(req.body.panelId)) {
        return res.status(422).json({ message: 'circuitId does not belong to panelId' });
      }
    }
    const outlet = await ElectricalOutlet.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!outlet) return res.status(404).json({ message: 'Outlet not found' });
    res.json(outlet);
  } catch (err) {
    console.error('PUT /outlets/:id error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

router.delete('/outlets/:id', async (req, res) => {
  try {
    await ElectricalOutlet.findByIdAndDelete(req.params.id);
    res.status(204).send();
  } catch (err) {
    console.error('DELETE /outlets/:id error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Trace
router.get('/trace', async (req, res) => {
  try {
    const { outletId, label } = req.query;
    let outlet;
    if (outletId) {
      outlet = await ElectricalOutlet.findById(outletId).lean();
    } else if (label) {
      outlet = await ElectricalOutlet.findOne({ label }).lean();
    } else {
      return res.status(400).json({ message: 'outletId or label is required' });
    }

    if (!outlet) return res.status(404).json({ message: 'Outlet not found' });
    if (!outlet.panelId || !outlet.circuitId) {
      return res.status(404).json({ message: 'Outlet not linked to panel/circuit' });
    }

    const [circuit, panel] = await Promise.all([
      ElectricalCircuit.findById(outlet.circuitId).lean(),
      ElectricalPanel.findById(outlet.panelId).lean()
    ]);

    if (!circuit || !panel) {
      return res.status(422).json({ message: 'Inconsistent links for outlet' });
    }

    const path = [];
    if (outlet.iconId && panel.locationIconId) {
      path.push({ fromIconId: outlet.iconId, toIconId: panel.locationIconId, type: 'electrical-trace' });
    }

    res.json({ outlet, circuit, panel, path });
  } catch (err) {
    console.error('GET /trace error', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// QR Code generation endpoints
router.post('/generate-qr-codes', async (req, res) => {
  try {
    const { buildingId, floorId, itemType } = req.body;

    if (!buildingId || !floorId || !itemType) {
      return res.status(400).json({ message: 'buildingId, floorId, and itemType are required' });
    }

    let items = [];
    let updatedCount = 0;

    switch (itemType) {
      case 'outlets':
        items = await ElectricalOutlet.find({ buildingId, floorId });
        for (const outlet of items) {
          const qrCode = `OUTLET:${outlet.label}:${buildingId}:${floorId}`;
          await ElectricalOutlet.findByIdAndUpdate(outlet._id, { qrCode });
          updatedCount++;
        }
        break;

      case 'panels':
        items = await ElectricalPanel.find({ buildingId, floorId });
        for (const panel of items) {
          const qrCode = `PANEL:${panel.code || panel.name}:${buildingId}:${floorId}`;
          await ElectricalPanel.findByIdAndUpdate(panel._id, { qrCode });
          updatedCount++;
        }
        break;

      default:
        return res.status(400).json({ message: 'Invalid itemType. Use "outlets" or "panels"' });
    }

    res.json({
      message: `QR codes generated for ${updatedCount} ${itemType}`,
      itemsProcessed: updatedCount,
      totalItems: items.length
    });

  } catch (error) {
    console.error('Error generating QR codes:', error);
    res.status(500).json({ message: 'Server error generating QR codes' });
  }
});

router.get('/qr-codes/:buildingId/:floorId', async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;
    const { itemType } = req.query;

    let results = {};

    if (!itemType || itemType === 'outlets') {
      const outlets = await ElectricalOutlet.find({ 
        buildingId, 
        floorId, 
        qrCode: { $exists: true, $ne: null } 
      }).select('_id label qrCode room').lean();
      results.outlets = outlets;
    }

    if (!itemType || itemType === 'panels') {
      const panels = await ElectricalPanel.find({ 
        buildingId, 
        floorId, 
        qrCode: { $exists: true, $ne: null } 
      }).select('_id name code qrCode room').lean();
      results.panels = panels;
    }

    res.json(results);

  } catch (error) {
    console.error('Error fetching QR codes:', error);
    res.status(500).json({ message: 'Server error fetching QR codes' });
  }
});

router.delete('/qr-codes/:itemType/:itemId', async (req, res) => {
  try {
    const { itemType, itemId } = req.params;

    let result;
    switch (itemType) {
      case 'outlet':
        result = await ElectricalOutlet.findByIdAndUpdate(
          itemId, 
          { $unset: { qrCode: 1 } },
          { new: true }
        );
        break;
      case 'panel':
        result = await ElectricalPanel.findByIdAndUpdate(
          itemId, 
          { $unset: { qrCode: 1 } },
          { new: true }
        );
        break;
      default:
        return res.status(400).json({ message: 'Invalid itemType' });
    }

    if (!result) {
      return res.status(404).json({ message: 'Item not found' });
    }

    res.json({ message: 'QR code removed successfully', item: result });

  } catch (error) {
    console.error('Error removing QR code:', error);
    res.status(500).json({ message: 'Server error removing QR code' });
  }
});

module.exports = router;
