const express = require('express');
const router = express.Router();
const ChurchEvent = require('../../models/ChurchEvent');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

/**
 * Phase 7 - Church Event Management & Room Usage API Routes
 * Provides comprehensive event management with Google Calendar integration
 */

// ===== Church Event Management =====

/**
 * GET /api/church-events
 * Get church events with filtering and pagination
 */
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const {
      buildingId,
      floorId,
      startDate,
      endDate,
      eventType,
      ministry,
      status,
      organizer,
      search,
      upcoming = 'false',
      limit = 50,
      page = 1
    } = req.query;

    let query = { isActive: true };
    
    // Location filters
    if (buildingId) query['location.buildingId'] = buildingId;
    if (floorId) query['location.floorId'] = floorId;
    
    // Event filters
    if (eventType) query.eventType = eventType;
    if (ministry) query.ministry = ministry;
    if (status) query.status = status;
    if (organizer) query['organizer.userId'] = organizer;
    
    // Date range filter
    if (startDate || endDate || upcoming === 'true') {
      const start = startDate ? new Date(startDate) : new Date();
      const end = endDate ? new Date(endDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      
      query.$or = [
        { startTime: { $gte: start, $lte: end } },
        { endTime: { $gte: start, $lte: end } },
        { startTime: { $lte: start }, endTime: { $gte: end } }
      ];
    }
    
    // Search filter
    if (search) {
      query.$text = { $search: search };
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [events, total] = await Promise.all([
      ChurchEvent.find(query)
        .populate('location.buildingId', 'name')
        .populate('location.floorId', 'name level')
        .populate('organizer.userId', 'name email')
        .sort({ startTime: 1 })
        .skip(skip)
        .limit(parseInt(limit)),
      ChurchEvent.countDocuments(query)
    ]);

    res.json({
      events,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching church events:', error);
    res.status(500).json({ error: 'Failed to fetch church events' });
  }
});

/**
 * GET /api/church-events/:id
 * Get specific church event details
 */
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const event = await ChurchEvent.findById(req.params.id)
      .populate('location.buildingId', 'name address')
      .populate('location.floorId', 'name level')
      .populate('organizer.userId', 'name email phone')
      .populate('staff.userId', 'name email phone')
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    if (!event) {
      return res.status(404).json({ error: 'Church event not found' });
    }

    res.json(event);
  } catch (error) {
    console.error('Error fetching church event:', error);
    res.status(500).json({ error: 'Failed to fetch church event' });
  }
});

/**
 * POST /api/church-events
 * Create new church event
 */
router.post('/', isAuthenticated, async (req, res) => {
  try {
    const eventData = {
      ...req.body,
      createdBy: req.user.id,
      updatedBy: req.user.id
    };

    // Validate required fields
    const required = ['eventId', 'title', 'startTime', 'endTime', 'eventType'];
    for (const field of required) {
      if (!eventData[field]) {
        return res.status(400).json({ error: `${field} is required` });
      }
    }

    // Validate date logic
    if (new Date(eventData.startTime) >= new Date(eventData.endTime)) {
      return res.status(400).json({ error: 'End time must be after start time' });
    }

    // Check for duplicate eventId
    const existingEvent = await ChurchEvent.findOne({ 
      eventId: eventData.eventId,
      isActive: true 
    });
    if (existingEvent) {
      return res.status(409).json({ error: 'Event ID already exists' });
    }

    const event = new ChurchEvent(eventData);
    await event.save();

    const populatedEvent = await ChurchEvent.findById(event._id)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name level')
      .populate('organizer.userId', 'name email');

    res.status(201).json(populatedEvent);
  } catch (error) {
    console.error('Error creating church event:', error);
    if (error.code === 11000) {
      res.status(409).json({ error: 'Event ID must be unique' });
    } else {
      res.status(400).json({ error: error.message });
    }
  }
});

/**
 * PUT /api/church-events/:id
 * Update church event
 */
router.put('/:id', isAuthenticated, async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      updatedBy: req.user.id
    };

    // Remove fields that shouldn't be updated directly
    delete updateData._id;
    delete updateData.createdBy;
    delete updateData.createdAt;

    // Validate date logic if dates are being updated
    if (updateData.startTime && updateData.endTime) {
      if (new Date(updateData.startTime) >= new Date(updateData.endTime)) {
        return res.status(400).json({ error: 'End time must be after start time' });
      }
    }

    const event = await ChurchEvent.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate('location.buildingId', 'name')
     .populate('location.floorId', 'name level')
     .populate('organizer.userId', 'name email');

    if (!event) {
      return res.status(404).json({ error: 'Church event not found' });
    }

    res.json(event);
  } catch (error) {
    console.error('Error updating church event:', error);
    res.status(400).json({ error: error.message });
  }
});

/**
 * DELETE /api/church-events/:id
 * Delete church event (soft delete by default)
 */
router.delete('/:id', isAuthenticated, async (req, res) => {
  try {
    const { force = 'false' } = req.query;

    if (force === 'true') {
      // Hard delete
      const event = await ChurchEvent.findByIdAndDelete(req.params.id);
      if (!event) {
        return res.status(404).json({ error: 'Church event not found' });
      }
    } else {
      // Soft delete
      const event = await ChurchEvent.findByIdAndUpdate(
        req.params.id,
        { isActive: false, updatedBy: req.user.id },
        { new: true }
      );
      if (!event) {
        return res.status(404).json({ error: 'Church event not found' });
      }
    }

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting church event:', error);
    res.status(500).json({ error: 'Failed to delete church event' });
  }
});

// ===== Event Management Operations =====

/**
 * POST /api/church-events/:id/checklist
 * Add item to event preparation checklist
 */
router.post('/:id/checklist', isAuthenticated, async (req, res) => {
  try {
    const { task, responsible, dueTime, systems = [] } = req.body;

    if (!task || !responsible) {
      return res.status(400).json({ error: 'Task and responsible person are required' });
    }

    const event = await ChurchEvent.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ error: 'Church event not found' });
    }

    await event.addChecklistItem(task, responsible, dueTime, systems);

    const updatedEvent = await ChurchEvent.findById(req.params.id)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name level');

    res.json(updatedEvent);
  } catch (error) {
    console.error('Error adding checklist item:', error);
    res.status(400).json({ error: error.message });
  }
});

/**
 * PUT /api/church-events/:id/checklist/:taskId/complete
 * Mark checklist item as completed
 */
router.put('/:id/checklist/:taskId/complete', isAuthenticated, async (req, res) => {
  try {
    const { notes = '' } = req.body;
    const completedBy = req.user.name || req.user.email;

    const event = await ChurchEvent.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ error: 'Church event not found' });
    }

    await event.completeChecklistItem(req.params.taskId, completedBy, notes);

    const updatedEvent = await ChurchEvent.findById(req.params.id)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name level');

    res.json(updatedEvent);
  } catch (error) {
    console.error('Error completing checklist item:', error);
    res.status(400).json({ error: error.message });
  }
});

/**
 * PUT /api/church-events/:id/status
 * Update event status
 */
router.put('/:id/status', isAuthenticated, async (req, res) => {
  try {
    const { status, notes = '' } = req.body;

    const validStatuses = [
      'draft', 'planning', 'approved', 'preparing', 'setup',
      'in_progress', 'concluded', 'cleanup', 'completed', 'cancelled'
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    const event = await ChurchEvent.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ error: 'Church event not found' });
    }

    // Add to status history
    if (!event.workflow.statusHistory) {
      event.workflow.statusHistory = [];
    }

    event.workflow.statusHistory.push({
      status,
      changedBy: req.user.name || req.user.email,
      changedAt: new Date(),
      notes
    });

    event.status = status;
    event.updatedBy = req.user.id;

    await event.save();

    const updatedEvent = await ChurchEvent.findById(req.params.id)
      .populate('location.buildingId', 'name')
      .populate('location.floorId', 'name level');

    res.json(updatedEvent);
  } catch (error) {
    console.error('Error updating event status:', error);
    res.status(400).json({ error: error.message });
  }
});

// ===== Location and Calendar Queries =====

/**
 * GET /api/church-events/building/:buildingId/floor/:floorId
 * Get events for specific building and floor
 */
router.get('/building/:buildingId/floor/:floorId', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, floorId } = req.params;
    const { 
      startDate = new Date().toISOString(),
      endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      includeSetup = 'true'
    } = req.query;

    const events = await ChurchEvent.findByLocation(
      buildingId, 
      floorId, 
      { 
        start: new Date(startDate), 
        end: new Date(endDate) 
      }
    ).populate('organizer.userId', 'name email');

    // Include setup/cleanup time if requested
    let eventsWithSetup = events;
    if (includeSetup === 'true') {
      eventsWithSetup = events.map(event => {
        const setupTime = event.preparation?.setupTime?.duration || 30; // Default 30 minutes
        const cleanupTime = 15; // Default 15 minutes cleanup
        
        return {
          ...event.toObject(),
          effectiveStartTime: new Date(event.startTime.getTime() - setupTime * 60 * 1000),
          effectiveEndTime: new Date(event.endTime.getTime() + cleanupTime * 60 * 1000),
          setupDuration: setupTime,
          cleanupDuration: cleanupTime
        };
      });
    }

    res.json(eventsWithSetup);
  } catch (error) {
    console.error('Error fetching events by location:', error);
    res.status(500).json({ error: 'Failed to fetch events by location' });
  }
});

/**
 * GET /api/church-events/upcoming
 * Get upcoming events requiring attention
 */
router.get('/upcoming', isAuthenticated, async (req, res) => {
  try {
    const { 
      limit = 10, 
      eventTypes = '',
      hoursAhead = 24 
    } = req.query;

    const eventTypesArray = eventTypes ? eventTypes.split(',') : [];
    const upcomingEvents = await ChurchEvent.findUpcoming(
      parseInt(limit), 
      eventTypesArray
    );

    // Also get events requiring preparation
    const needsPreparation = await ChurchEvent.findRequiringPreparation();

    res.json({
      upcoming: upcomingEvents,
      needsPreparation,
      summary: {
        upcomingCount: upcomingEvents.length,
        needsPreparationCount: needsPreparation.length
      }
    });
  } catch (error) {
    console.error('Error fetching upcoming events:', error);
    res.status(500).json({ error: 'Failed to fetch upcoming events' });
  }
});

/**
 * GET /api/church-events/calendar/:buildingId
 * Get calendar view for building
 */
router.get('/calendar/:buildingId', isAuthenticated, async (req, res) => {
  try {
    const { buildingId } = req.params;
    const { 
      startDate = new Date().toISOString(),
      endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      view = 'week' 
    } = req.query;

    const events = await ChurchEvent.findByDateRange(
      new Date(startDate),
      new Date(endDate),
      { 'location.buildingId': buildingId }
    ).populate('location.floorId', 'name level')
     .populate('organizer.userId', 'name');

    // Group events by date for calendar view
    const calendar = {};
    events.forEach(event => {
      const dateKey = event.startTime.toISOString().split('T')[0];
      if (!calendar[dateKey]) {
        calendar[dateKey] = [];
      }
      calendar[dateKey].push(event);
    });

    res.json({
      calendar,
      events,
      view,
      dateRange: {
        start: startDate,
        end: endDate
      }
    });
  } catch (error) {
    console.error('Error fetching calendar events:', error);
    res.status(500).json({ error: 'Failed to fetch calendar events' });
  }
});

// ===== Analytics and Reporting =====

/**
 * GET /api/church-events/analytics/summary
 * Get analytics summary for events
 */
router.get('/analytics/summary', isAuthenticated, async (req, res) => {
  try {
    const { buildingId, timeframe = '30' } = req.query;

    let filter = { isActive: true };
    if (buildingId) filter['location.buildingId'] = buildingId;

    const startDate = new Date(Date.now() - parseInt(timeframe) * 24 * 60 * 60 * 1000);

    const [
      totalEvents,
      upcomingEvents,
      eventsByType,
      eventsByMinistry,
      eventsByStatus,
      attendanceStats
    ] = await Promise.all([
      // Total events
      ChurchEvent.countDocuments(filter),
      
      // Upcoming events
      ChurchEvent.countDocuments({
        ...filter,
        startTime: { $gte: new Date() },
        status: { $nin: ['cancelled', 'completed'] }
      }),
      
      // Group by event type
      ChurchEvent.aggregate([
        { $match: { ...filter, startTime: { $gte: startDate } } },
        { $group: { _id: '$eventType', count: { $sum: 1 } } }
      ]),
      
      // Group by ministry
      ChurchEvent.aggregate([
        { $match: { ...filter, startTime: { $gte: startDate } } },
        { $group: { _id: '$ministry', count: { $sum: 1 } } }
      ]),
      
      // Group by status
      ChurchEvent.aggregate([
        { $match: filter },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      
      // Attendance statistics
      ChurchEvent.aggregate([
        { $match: { ...filter, 'attendance.actual': { $exists: true } } },
        { 
          $group: { 
            _id: null, 
            totalAttendance: { $sum: '$attendance.actual' },
            averageAttendance: { $avg: '$attendance.actual' },
            maxAttendance: { $max: '$attendance.actual' },
            eventsWithAttendance: { $sum: 1 }
          } 
        }
      ])
    ]);

    res.json({
      summary: {
        totalEvents,
        upcomingEvents,
        eventsWithAttendance: attendanceStats[0]?.eventsWithAttendance || 0
      },
      distribution: {
        byType: eventsByType.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {}),
        byMinistry: eventsByMinistry.reduce((acc, item) => {
          acc[item._id || 'unspecified'] = item.count;
          return acc;
        }, {}),
        byStatus: eventsByStatus.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      },
      attendance: attendanceStats[0] || {
        totalAttendance: 0,
        averageAttendance: 0,
        maxAttendance: 0,
        eventsWithAttendance: 0
      },
      timeframe: `${timeframe} days`
    });
  } catch (error) {
    console.error('Error generating analytics:', error);
    res.status(500).json({ error: 'Failed to generate analytics' });
  }
});

/**
 * POST /api/church-events/seed-demo-data
 * Seed demo church event data for testing
 */
router.post('/seed-demo-data', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { buildingId, floorId, clearExisting = false } = req.body;

    if (!buildingId) {
      return res.status(400).json({ error: 'buildingId is required' });
    }

    // Clear existing demo data if requested
    if (clearExisting) {
      await ChurchEvent.deleteMany({ 
        'location.buildingId': buildingId,
        eventId: { $regex: '^DEMO-' }
      });
    }

    // Demo event data
    const now = new Date();
    const demoEvents = [
      {
        eventId: 'DEMO-WORSHIP-001',
        title: 'Sunday Morning Worship Service',
        description: 'Traditional worship service with communion',
        startTime: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        endTime: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 + 90 * 60 * 1000), // 90 minutes later
        eventType: 'worship_service',
        ministry: 'worship',
        priority: 'high',
        location: {
          buildingId,
          floorId,
          roomName: 'Main Sanctuary',
          capacity: 300,
          expectedAttendance: 250
        },
        organizer: {
          name: 'Pastor Smith',
          email: '<EMAIL>'
        },
        resources: {
          audioVisual: {
            required: true,
            equipment: ['microphones', 'projector', 'sound_system', 'live_stream'],
            technicianRequired: true,
            setupTime: 60
          },
          facilities: {
            heating: true,
            lighting: [{ zone: 'sanctuary', level: 'medium' }],
            security: { required: true }
          }
        },
        preparation: {
          checklist: [
            {
              task: 'Test microphones and sound system',
              responsible: 'Tech Team',
              dueTime: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 - 60 * 60 * 1000),
              systems: ['audio'],
              completed: false
            },
            {
              task: 'Set up communion table',
              responsible: 'Deacons',
              dueTime: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000 - 30 * 60 * 1000),
              systems: [],
              completed: false
            }
          ],
          systemsChecks: [
            { system: 'audio', status: 'not_checked' },
            { system: 'lighting', status: 'not_checked' },
            { system: 'hvac', status: 'not_checked' }
          ]
        },
        status: 'approved',
        createdBy: req.user.id,
        updatedBy: req.user.id
      },
      {
        eventId: 'DEMO-YOUTH-001',
        title: 'Youth Group Meeting',
        description: 'Weekly youth group meeting with games and Bible study',
        startTime: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        endTime: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000 + 120 * 60 * 1000), // 2 hours later
        eventType: 'youth_group',
        ministry: 'youth',
        priority: 'medium',
        location: {
          buildingId,
          floorId,
          roomName: 'Youth Room',
          capacity: 50,
          expectedAttendance: 30
        },
        organizer: {
          name: 'Youth Pastor Jones',
          email: '<EMAIL>'
        },
        resources: {
          audioVisual: {
            required: true,
            equipment: ['sound_system', 'projector'],
            setupTime: 30
          },
          catering: {
            required: true,
            mealType: 'snacks',
            expectedCount: 30
          }
        },
        status: 'planning',
        createdBy: req.user.id,
        updatedBy: req.user.id
      },
      {
        eventId: 'DEMO-WEDDING-001',
        title: 'Smith-Johnson Wedding',
        description: 'Wedding ceremony for Sarah Smith and John Johnson',
        startTime: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        endTime: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000 + 180 * 60 * 1000), // 3 hours later
        eventType: 'wedding',
        ministry: 'worship',
        priority: 'high',
        location: {
          buildingId,
          floorId,
          roomName: 'Main Sanctuary',
          capacity: 300,
          expectedAttendance: 150
        },
        organizer: {
          name: 'Wedding Coordinator',
          email: '<EMAIL>'
        },
        resources: {
          audioVisual: {
            required: true,
            equipment: ['microphones', 'sound_system', 'recording'],
            technicianRequired: true,
            setupTime: 120
          },
          facilities: {
            lighting: [
              { zone: 'sanctuary', level: 'high' },
              { zone: 'altar', level: 'custom' }
            ],
            security: { required: true }
          }
        },
        status: 'approved',
        createdBy: req.user.id,
        updatedBy: req.user.id
      }
    ];

    const createdEvents = [];
    for (const eventData of demoEvents) {
      try {
        const event = new ChurchEvent(eventData);
        await event.save();
        createdEvents.push(event);
      } catch (error) {
        console.warn(`Failed to create demo event ${eventData.eventId}:`, error.message);
      }
    }

    res.json({
      message: `Created ${createdEvents.length} demo church events`,
      events: createdEvents,
      building: buildingId,
      floor: floorId
    });
  } catch (error) {
    console.error('Error seeding demo data:', error);
    res.status(500).json({ error: 'Failed to seed demo data' });
  }
});

module.exports = router;