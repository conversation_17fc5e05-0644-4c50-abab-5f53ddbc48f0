const express = require('express');
const router = express.Router();
const reservationController = require('../controllers/reservationController');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

/**
 * Reservation Routes
 * Base path: /api/reservations
 */

// Get all reservations
// GET /api/reservations
router.get('/', isAuthenticated, reservationController.getAllReservations);

// Get reservation by ID
// GET /api/reservations/:id
router.get('/:id', isAuthenticated, reservationController.getReservationById);

// Create a new reservation
// POST /api/reservations
router.post('/', isAuthenticated, reservationController.createReservation);

// Update a reservation
// PUT /api/reservations/:id
router.put('/:id', isAuthenticated, reservationController.updateReservation);

// Delete a reservation
// DELETE /api/reservations/:id
router.delete('/:id', isAuthenticated, reservationController.deleteReservation);

// Approve a reservation
// POST /api/reservations/:id/approve
router.post('/:id/approve', isAuthenticated, reservationController.approveReservation);

// Reject a reservation
// POST /api/reservations/:id/reject
router.post('/:id/reject', isAuthenticated, reservationController.rejectReservation);

// Confirm a reservation
// POST /api/reservations/:id/confirm
router.post('/:id/confirm', isAuthenticated, reservationController.confirmReservation);

// Cancel a reservation
// POST /api/reservations/:id/cancel
router.post('/:id/cancel', isAuthenticated, reservationController.cancelReservation);

module.exports = router;