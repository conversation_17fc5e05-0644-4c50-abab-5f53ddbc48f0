const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const mongoose = require('mongoose');
const Floor = require('../../models/Floor');
const FloorplanIcon = require('../../models/FloorplanIcon');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/floorplans');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename: timestamp-originalname
    const uniqueFilename = `${Date.now()}-${file.originalname.replace(/\s+/g, '-')}`;
    cb(null, uniqueFilename);
  }
});

// File filter to only allow image files
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'application/pdf'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, SVG, and PDF files are allowed.'), false);
  }
};

const upload = multer({ 
  storage, 
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB max file size
  }
});

// Get all floors
router.get('/', isAuthenticated, async (req, res) => {
  try {
    const floors = await Floor.find().sort({ buildingId: 1, level: 1 });
    res.json(floors);
  } catch (err) {
    console.error('Error fetching floors:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get floor by ID
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const floor = await Floor.findById(req.params.id);

    if (!floor) {
      return res.status(404).json({ message: 'Floor not found' });
    }

    res.json(floor);
  } catch (err) {
    console.error('Error fetching floor:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create a new floor
router.post('/', isAuthenticated, isAdmin, upload.single('floorplan'), async (req, res) => {
  try {
    const {
      buildingId,
      name,
      level,
      description,
      dimensions,
      status,
      metadata
    } = req.body;

    // Create new floor
    const newFloor = new Floor({
      buildingId,
      name,
      level: parseInt(level, 10),
      description,
      dimensions: dimensions ? JSON.parse(dimensions) : undefined,
      status,
      metadata: metadata ? JSON.parse(metadata) : undefined,
      createdBy: req.user.id
    });

    // Add floorplan if uploaded
    if (req.file) {
      newFloor.floorplan = {
        filename: req.file.filename,
        originalFilename: req.file.originalname,
        path: req.file.path,
        mimetype: req.file.mimetype,
        size: req.file.size,
        uploadDate: Date.now()
      };
    }

    const floor = await newFloor.save();
    res.status(201).json(floor);
  } catch (err) {
    console.error('Error creating floor:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Update a floor
router.put('/:id', isAuthenticated, isAdmin, upload.single('floorplan'), async (req, res) => {
  try {
    const {
      name,
      level,
      description,
      dimensions,
      status,
      metadata
    } = req.body;

    // Find floor by ID
    let floor = await Floor.findById(req.params.id);

    if (!floor) {
      return res.status(404).json({ message: 'Floor not found' });
    }

    // Update floor fields
    if (name) floor.name = name;
    if (level) floor.level = parseInt(level, 10);
    if (description) floor.description = description;
    if (dimensions) floor.dimensions = JSON.parse(dimensions);
    if (status) floor.status = status;
    if (metadata) floor.metadata = JSON.parse(metadata);
    floor.updatedAt = Date.now();

    // Update floorplan if uploaded
    if (req.file) {
      // Delete old floorplan file if it exists
      if (floor.floorplan && floor.floorplan.path && fs.existsSync(floor.floorplan.path)) {
        fs.unlinkSync(floor.floorplan.path);
      }

      floor.floorplan = {
        filename: req.file.filename,
        originalFilename: req.file.originalname,
        path: req.file.path,
        mimetype: req.file.mimetype,
        size: req.file.size,
        uploadDate: Date.now()
      };
    }

    // Save updated floor
    floor = await floor.save();
    res.json(floor);
  } catch (err) {
    console.error('Error updating floor:', err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Delete a floor
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    // Find floor by ID
    const floor = await Floor.findById(req.params.id);

    if (!floor) {
      return res.status(404).json({ message: 'Floor not found' });
    }

    // Delete floorplan file if it exists
    if (floor.floorplan && floor.floorplan.path && fs.existsSync(floor.floorplan.path)) {
      fs.unlinkSync(floor.floorplan.path);
    }

    // Delete all floorplan icons associated with this floor
    await FloorplanIcon.deleteMany({ floorId: floor._id });

    // Delete the floor
    await Floor.findByIdAndDelete(req.params.id);

    res.json({ message: 'Floor deleted successfully' });
  } catch (err) {
    console.error('Error deleting floor:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get all icons for a floor
router.get('/:id/icons', isAuthenticated, async (req, res) => {
  try {
    const icons = await FloorplanIcon.find({ floorId: req.params.id });
    res.json(icons);
  } catch (err) {
    console.error('Error fetching floor icons:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Serve floorplan image
router.get('/:id/floorplan', isAuthenticated, async (req, res) => {
  try {
    const floorId = req.params.id;
    console.log(`Attempting to retrieve floorplan for floor ID: ${floorId}`);
    
    // Check if the ID is a valid MongoDB ObjectID
    if (!mongoose.Types.ObjectId.isValid(floorId)) {
      console.error(`Invalid floor ID format: ${floorId}`);
      return res.status(400).json({ message: 'Invalid floor ID format' });
    }

    const floor = await Floor.findById(floorId);

    if (!floor) {
      console.error(`Floor not found with ID: ${floorId}`);
      return res.status(404).json({ message: 'Floor not found' });
    }

    if (!floor.floorplan) {
      console.error(`Floor ${floorId} does not have a floorplan`);
      return res.status(404).json({ message: 'Floor does not have a floorplan' });
    }

    if (!floor.floorplan.path) {
      console.error(`Floor ${floorId} has a floorplan but no path`);
      return res.status(404).json({ message: 'Floorplan path not found' });
    }

    // Check if the file exists
    if (!fs.existsSync(floor.floorplan.path)) {
      console.error(`Floorplan file does not exist at path: ${floor.floorplan.path}`);
      return res.status(404).json({ message: 'Floorplan file not found on server' });
    }

    console.log(`Serving floorplan for floor ${floorId} from path: ${floor.floorplan.path}`);
    res.sendFile(floor.floorplan.path);
  } catch (err) {
    console.error(`Error serving floorplan for floor ${req.params.id}:`, err);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

module.exports = router;
