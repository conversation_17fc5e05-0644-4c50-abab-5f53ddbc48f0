const express = require('express');
const router = express.Router();
const ticketTagController = require('../controllers/ticketTagController');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');
const { body } = require('express-validator');

/**
 * Ticket Tag Routes
 * Base path: /api/ticket-tags
 */

// Validation middleware
const validateTagCreation = [
  body('name')
    .notEmpty()
    .withMessage('Tag name is required')
    .isLength({ max: 50 })
    .withMessage('Tag name must be less than 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Tag name can only contain letters, numbers, underscores, and hyphens'),
  body('displayName')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Display name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color code'),
  body('backgroundColor')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Background color must be a valid hex color code'),
  body('autoRules')
    .optional()
    .isArray()
    .withMessage('Auto rules must be an array'),
  body('autoRules.*.field')
    .optional()
    .isIn(['subject', 'description', 'requesterEmail', 'senderDomain', 'category'])
    .withMessage('Invalid auto rule field'),
  body('autoRules.*.operator')
    .optional()
    .isIn(['contains', 'starts_with', 'ends_with', 'equals', 'regex'])
    .withMessage('Invalid auto rule operator'),
  body('autoRules.*.value')
    .optional()
    .notEmpty()
    .withMessage('Auto rule value is required'),
  body('autoRules.*.priority')
    .optional()
    .isInt()
    .withMessage('Auto rule priority must be an integer'),
  body('relatedTags')
    .optional()
    .isArray()
    .withMessage('Related tags must be an array'),
  body('relatedTags.*.tag')
    .optional()
    .isMongoId()
    .withMessage('Invalid related tag ID'),
  body('relatedTags.*.relationship')
    .optional()
    .isIn(['similar', 'opposite', 'parent', 'child'])
    .withMessage('Invalid relationship type'),
  body('isSystemTag')
    .optional()
    .isBoolean()
    .withMessage('isSystemTag must be a boolean')
];

const validateTagUpdate = [
  body('name')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Tag name must be less than 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Tag name can only contain letters, numbers, underscores, and hyphens'),
  body('displayName')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Display name must be less than 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color code'),
  body('backgroundColor')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Background color must be a valid hex color code'),
  body('autoRules')
    .optional()
    .isArray()
    .withMessage('Auto rules must be an array'),
  body('autoRules.*.field')
    .optional()
    .isIn(['subject', 'description', 'requesterEmail', 'senderDomain', 'category'])
    .withMessage('Invalid auto rule field'),
  body('autoRules.*.operator')
    .optional()
    .isIn(['contains', 'starts_with', 'ends_with', 'equals', 'regex'])
    .withMessage('Invalid auto rule operator'),
  body('autoRules.*.value')
    .optional()
    .notEmpty()
    .withMessage('Auto rule value is required'),
  body('autoRules.*.priority')
    .optional()
    .isInt()
    .withMessage('Auto rule priority must be an integer'),
  body('relatedTags')
    .optional()
    .isArray()
    .withMessage('Related tags must be an array'),
  body('relatedTags.*.tag')
    .optional()
    .isMongoId()
    .withMessage('Invalid related tag ID'),
  body('relatedTags.*.relationship')
    .optional()
    .isIn(['similar', 'opposite', 'parent', 'child'])
    .withMessage('Invalid relationship type'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
];

// Middleware to check ticket manager permissions
const requireTicketManagerOrAdmin = (req, res, next) => {
  if (!req.user.roles.includes('admin') && !req.user.roles.includes('ticket_manager')) {
    return res.status(403).json({ message: 'Requires admin or ticket_manager role' });
  }
  next();
};

// Get all tags
// GET /api/ticket-tags
router.get('/', isAuthenticated, ticketTagController.getAllTags);

// Search tags by name
// GET /api/ticket-tags/search
router.get('/search', isAuthenticated, ticketTagController.searchTags);

// Get popular tags
// GET /api/ticket-tags/popular
router.get('/popular', isAuthenticated, ticketTagController.getPopularTags);

// Update all tag usage counts (admin only)
// POST /api/ticket-tags/update-counts
router.post('/update-counts', isAuthenticated, isAdmin, ticketTagController.updateTagCounts);

// Get tag by ID
// GET /api/ticket-tags/:id
router.get('/:id', isAuthenticated, ticketTagController.getTagById);

// Create a new tag (admin or ticket manager only)
// POST /api/ticket-tags
router.post('/', isAuthenticated, requireTicketManagerOrAdmin, validateTagCreation, ticketTagController.createTag);

// Update a tag (admin or ticket manager only)
// PUT /api/ticket-tags/:id
router.put('/:id', isAuthenticated, requireTicketManagerOrAdmin, validateTagUpdate, ticketTagController.updateTag);

// Delete a tag (admin only)
// DELETE /api/ticket-tags/:id
router.delete('/:id', isAuthenticated, isAdmin, ticketTagController.deleteTag);

module.exports = router;