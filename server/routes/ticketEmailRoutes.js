const express = require('express');
const router = express.Router();
const ticketEmailController = require('../controllers/ticketEmailController');

/**
 * Ticket Email Routes
 * Base path: /api/ticket-email
 */

// Webhook endpoint for incoming emails
// POST /api/ticket-email/webhook
// This endpoint should be configured in your email service (SendGrid, Mailgun, etc.)
// No authentication required as this comes from external email service
router.post('/webhook', ticketEmailController.processIncomingEmail);

// Alternative webhook endpoints for different email services
// POST /api/ticket-email/webhook/sendgrid
router.post('/webhook/sendgrid', ticketEmailController.processIncomingEmail);

// POST /api/ticket-email/webhook/mailgun
router.post('/webhook/mailgun', ticketEmailController.processIncomingEmail);

// POST /api/ticket-email/webhook/generic
router.post('/webhook/generic', ticketEmailController.processIncomingEmail);

module.exports = router;