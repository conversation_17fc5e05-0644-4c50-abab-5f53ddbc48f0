const express = require('express');
const router = express.Router();
const Contact = require('../../models/Contact');
const { isAuthenticated, isAdmin } = require('../../middleware/auth');

// Get all contacts
router.get('/', isAuthenticated, async (req, res) => {
  try {
    // Support filtering by category and group
    const filter = {};
    if (req.query.category) {
      filter.category = req.query.category;
    }
    if (req.query.group) {
      filter.groups = req.query.group;
    }

    // Support search by name or company
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      filter.$or = [
        { name: searchRegex },
        { company: searchRegex }
      ];
    }

    const contacts = await Contact.find(filter).sort({ name: 1 });
    res.json(contacts);
  } catch (err) {
    console.error('Error fetching contacts:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get contact by ID
router.get('/:id', isAuthenticated, async (req, res) => {
  try {
    const contact = await Contact.findById(req.params.id);

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    res.json(contact);
  } catch (err) {
    console.error('Error fetching contact:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create a new contact
router.post('/', isAuthenticated, async (req, res) => {
  try {
    const {
      name,
      category,
      phone,
      email,
      address,
      company,
      notes
    } = req.body;

    // Create new contact
    const newContact = new Contact({
      name,
      category,
      phone,
      email,
      address,
      company,
      notes,
      createdBy: req.user.id
    });

    const contact = await newContact.save();
    res.status(201).json(contact);
  } catch (err) {
    console.error('Error creating contact:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update a contact
router.put('/:id', isAuthenticated, async (req, res) => {
  try {
    const {
      name,
      category,
      phone,
      email,
      address,
      company,
      notes
    } = req.body;

    // Find contact by ID
    let contact = await Contact.findById(req.params.id);

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    // Update contact fields
    contact.name = name || contact.name;
    contact.category = category || contact.category;
    contact.phone = phone || contact.phone;
    contact.email = email || contact.email;
    contact.address = address || contact.address;
    contact.company = company || contact.company;
    contact.notes = notes || contact.notes;
    contact.updatedAt = Date.now();

    // Save updated contact
    contact = await contact.save();
    res.json(contact);
  } catch (err) {
    console.error('Error updating contact:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete a contact
router.delete('/:id', isAuthenticated, isAdmin, async (req, res) => {
  try {
    // Find contact by ID
    const contact = await Contact.findById(req.params.id);

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    // Delete the contact
    await Contact.findByIdAndDelete(req.params.id);

    res.json({ message: 'Contact deleted successfully' });
  } catch (err) {
    console.error('Error deleting contact:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;