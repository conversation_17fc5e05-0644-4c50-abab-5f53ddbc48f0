# Sidebar Navigation Redesign

Date: 2025-08-16

Summary
- Goal: Make the sidebar more user friendly, organized, and reduce reliance on the hamburger menu on desktop.
- Outcome: Implemented a persistent left sidebar on desktop and a temporary overlay drawer on mobile. This improves discoverability and reduces navigation friction for staff users.

Rationale (Best Practice)
- Admin/operations portals benefit from persistent left navigation on desktop: it improves information scent and reduces the cost of navigation (common in MUI Admin, Microsoft/Fluent, Carbon, and many enterprise UX patterns).
- On smaller screens, a temporary drawer (toggled by a hamburger) remains appropriate to preserve screen real estate (Material Design responsive layout guidance).

Key Changes
- File: client/src/components/Layout.js
  - Introduced responsive behavior using MUI useTheme/useMediaQuery.
    - isDesktop = theme.breakpoints.up('md')
    - Drawer switches between variant="permanent" (desktop) and variant="temporary" (mobile).
  - Auto-open the drawer on desktop and hide the hamburger icon there; keep the hamburger on mobile only.
  - The drawer is rendered only for authenticated users (unchanged behavior for unauthenticated users).
  - Prevent drawer from auto-closing on click when desktop (keeps the nav stable and always visible).
  - Layout: Drawer and main content are placed side-by-side in a flex row for desktop; content uses Container and grows to fill space.
  - Accessibility: Drawer container now uses role="navigation" and aria-label="Primary".

Notable Constants
- drawerWidth = 250 (easy to adjust in Layout.js).
- Breakpoint for desktop behavior: 'md'. To widen or narrow persistent behavior, adjust the breakpoint (e.g., up('lg')).

Organization
- Existing grouping (e.g., Google, Assets) and permissions remain intact to minimize scope while improving usability.
- Collapsible groups auto-open based on the current route (existing behavior retained).

Edge Cases & Behavior
- Desktop: Sidebar is always visible; clicking items does not close it.
- Mobile: Sidebar opens/closes via the hamburger icon; clicking items closes it (onClick applied only in mobile mode).
- No-permission items remain hidden as before.

Future Enhancements (Optional)
- Add favorites/pinned items at the top of the sidebar.
- Add a compact/collapsed sidebar mode for wide desktop screens.
- Add a quick filter field within the sidebar for fast navigation.

How to Adjust
- Change the breakpoint: in Layout.js, update useMediaQuery(theme.breakpoints.up('md')).
- Change sidebar width: update drawerWidth.

Testing Guidance
- Verify on desktop width (>= md) that the sidebar is persistent and the hamburger icon is hidden.
- Verify on mobile width (< md) that the hamburger toggles the drawer and that clicking a menu item closes it.
- Verify grouped/collapsible sections (Google, Assets) still function and auto-open based on route.

This redesign adheres to widely adopted enterprise UX standards and should improve usability without disrupting existing permissions, routing, or menu data sources.