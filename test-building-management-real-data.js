/**
 * Test script to verify the building management API endpoints with real data
 * This script makes requests to the API endpoints and logs the responses
 */

const axios = require('axios');

// Base URL for API requests
const baseUrl = 'http://localhost:5000'; // Adjust if your server runs on a different port

// Test function to check if the API endpoints are working with real data
async function testBuildingManagementRealData() {
  console.log('Testing building management API endpoints with real data...');
  
  try {
    // Test the system status endpoint
    console.log('\n1. Testing GET /api/building-management/status');
    try {
      const statusResponse = await axios.get(`${baseUrl}/api/building-management/status`);
      console.log('Status code:', statusResponse.status);
      console.log('Response data:', JSON.stringify(statusResponse.data, null, 2));
      
      // Check if the response contains real data
      const hasRealData = statusResponse.data && 
        typeof statusResponse.data === 'object' &&
        Object.keys(statusResponse.data).length > 0;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
    } catch (error) {
      console.error('Error testing status endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the doors endpoint
    console.log('\n2. Testing GET /api/building-management/doors');
    try {
      const doorsResponse = await axios.get(`${baseUrl}/api/building-management/doors`);
      console.log('Status code:', doorsResponse.status);
      console.log('Response data (first 2 items):', JSON.stringify(doorsResponse.data.slice(0, 2), null, 2));
      console.log(`Total doors: ${doorsResponse.data.length}`);
      
      // Check if the response contains real data
      const hasRealData = doorsResponse.data && 
        Array.isArray(doorsResponse.data) &&
        doorsResponse.data.length > 0 &&
        doorsResponse.data[0].id &&
        doorsResponse.data[0].source;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
      
      // Check sources of doors
      const sources = {};
      doorsResponse.data.forEach(door => {
        sources[door.source] = (sources[door.source] || 0) + 1;
      });
      console.log('Door sources:', sources);
    } catch (error) {
      console.error('Error testing doors endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the climate devices endpoint
    console.log('\n3. Testing GET /api/building-management/climate-devices');
    try {
      const climateResponse = await axios.get(`${baseUrl}/api/building-management/climate-devices`);
      console.log('Status code:', climateResponse.status);
      console.log('Response data (first 2 items):', JSON.stringify(climateResponse.data.slice(0, 2), null, 2));
      console.log(`Total climate devices: ${climateResponse.data.length}`);
      
      // Check if the response contains real data
      const hasRealData = climateResponse.data && 
        Array.isArray(climateResponse.data) &&
        climateResponse.data.length > 0 &&
        climateResponse.data[0].id &&
        climateResponse.data[0].source;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
      
      // Check sources of climate devices
      const sources = {};
      climateResponse.data.forEach(device => {
        sources[device.source] = (sources[device.source] || 0) + 1;
      });
      console.log('Climate device sources:', sources);
    } catch (error) {
      console.error('Error testing climate devices endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the cameras endpoint
    console.log('\n4. Testing GET /api/building-management/cameras');
    try {
      const camerasResponse = await axios.get(`${baseUrl}/api/building-management/cameras`);
      console.log('Status code:', camerasResponse.status);
      console.log('Response data (first 2 items):', JSON.stringify(camerasResponse.data.slice(0, 2), null, 2));
      console.log(`Total cameras: ${camerasResponse.data.length}`);
      
      // Check if the response contains real data
      const hasRealData = camerasResponse.data && 
        Array.isArray(camerasResponse.data) &&
        camerasResponse.data.length > 0 &&
        camerasResponse.data[0].id &&
        camerasResponse.data[0].source;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
      
      // Check sources of cameras
      const sources = {};
      camerasResponse.data.forEach(camera => {
        sources[camera.source] = (sources[camera.source] || 0) + 1;
      });
      console.log('Camera sources:', sources);
    } catch (error) {
      console.error('Error testing cameras endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the network devices endpoint
    console.log('\n5. Testing GET /api/building-management/network-devices');
    try {
      const networkResponse = await axios.get(`${baseUrl}/api/building-management/network-devices`);
      console.log('Status code:', networkResponse.status);
      console.log('Response data (first 2 items):', JSON.stringify(networkResponse.data.slice(0, 2), null, 2));
      console.log(`Total network devices: ${networkResponse.data.length}`);
      
      // Check if the response contains real data
      const hasRealData = networkResponse.data && 
        Array.isArray(networkResponse.data) &&
        networkResponse.data.length > 0 &&
        networkResponse.data[0].id &&
        networkResponse.data[0].source;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
      
      // Check sources of network devices
      const sources = {};
      networkResponse.data.forEach(device => {
        sources[device.source] = (sources[device.source] || 0) + 1;
      });
      console.log('Network device sources:', sources);
    } catch (error) {
      console.error('Error testing network devices endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the settings endpoint
    console.log('\n6. Testing GET /api/building-management/settings');
    try {
      const settingsResponse = await axios.get(`${baseUrl}/api/building-management/settings`);
      console.log('Status code:', settingsResponse.status);
      console.log('Response data:', JSON.stringify(settingsResponse.data, null, 2));
      
      // Check if the response contains real data
      const hasRealData = settingsResponse.data && 
        typeof settingsResponse.data === 'object' &&
        settingsResponse.data.integrations &&
        settingsResponse.data.integrationStatus;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
    } catch (error) {
      console.error('Error testing settings endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the automation rules endpoint
    console.log('\n7. Testing GET /api/building-management/automation/rules');
    try {
      const rulesResponse = await axios.get(`${baseUrl}/api/building-management/automation/rules`);
      console.log('Status code:', rulesResponse.status);
      console.log('Response data (first 2 items):', JSON.stringify(rulesResponse.data.slice(0, 2), null, 2));
      console.log(`Total automation rules: ${rulesResponse.data.length}`);
      
      // Check if the response contains real data
      const hasRealData = rulesResponse.data && 
        Array.isArray(rulesResponse.data);
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
    } catch (error) {
      console.error('Error testing automation rules endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    // Test the historical data endpoint
    console.log('\n8. Testing GET /api/building-management/historical-data');
    try {
      const historicalResponse = await axios.get(`${baseUrl}/api/building-management/historical-data`);
      console.log('Status code:', historicalResponse.status);
      console.log('Response data (first 2 data points):', JSON.stringify(historicalResponse.data.dataPoints.slice(0, 2), null, 2));
      console.log(`Total data points: ${historicalResponse.data.dataPoints.length}`);
      
      // Check if the response contains real data
      const hasRealData = historicalResponse.data && 
        historicalResponse.data.dataPoints &&
        Array.isArray(historicalResponse.data.dataPoints) &&
        historicalResponse.data.dataPoints.length > 0;
      
      console.log('Has real data:', hasRealData ? '✅' : '❌');
      
      // Check which systems have data
      const systems = new Set();
      historicalResponse.data.dataPoints.forEach(dataPoint => {
        Object.keys(dataPoint).forEach(key => {
          if (key !== 'timestamp') {
            systems.add(key);
          }
        });
      });
      console.log('Systems with historical data:', Array.from(systems));
    } catch (error) {
      console.error('Error testing historical data endpoint:', error.message);
      if (error.response) {
        console.log('Status code:', error.response.status);
        console.log('Response data:', error.response.data);
      }
    }

    console.log('\nAPI testing completed.');
    console.log('Note: 401 Unauthorized errors are expected if you are not authenticated.');
    console.log('To test with authentication, you need to include a valid session cookie.');

  } catch (error) {
    console.error('Error testing building management API:', error);
  }
}

// Run the test
testBuildingManagementRealData();