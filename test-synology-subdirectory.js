// Test script to verify Synology API works with subdirectories
require('dotenv').config();
const SynologyAPI = require('./server/integrations/synology/synologyAPI');

// Create a custom version of SynologyAPI that bypasses the integration tracker
class TestSynologyAPI extends SynologyAP<PERSON> {
  async initialize() {
    try {
      // Skip the integration tracker checks
      if (!this.host || !this.port || !this.username || !this.password) {
        console.error('Host, port, username, and password are required for Synology integration.');
        return;
      }

      // Check if we can authenticate
      try {
        await this.login();
        console.log('Authentication successful!');
      } catch (authError) {
        console.error('Authentication failed:', authError.message);
        throw authError;
      }
    } catch (error) {
      console.error('Error initializing Synology API:', error);
      throw error;
    }
  }
}

async function testSynologySubdirectory() {
  try {
    console.log('Testing Synology API with environment variables:');
    console.log(`SYNOLOGY_HOST: ${process.env.SYNOLOGY_HOST}`);
    console.log(`SYNOLOGY_PORT: ${process.env.SYNOLOGY_PORT}`);
    console.log(`SYNOLOGY_USERNAME: ${process.env.SYNOLOGY_USERNAME}`);
    console.log(`SYNOLOGY_SECURE: ${process.env.SYNOLOGY_SECURE}`);
    
    // Extract host without protocol
    let host = process.env.SYNOLOGY_HOST;
    if (host.startsWith('http://') || host.startsWith('https://')) {
      host = host.replace(/^https?:\/\//, '');
    }
    
    // Create a new TestSynologyAPI instance with environment variables
    const synologyAPI = new TestSynologyAPI(
      host,
      parseInt(process.env.SYNOLOGY_PORT, 10),
      process.env.SYNOLOGY_USERNAME,
      process.env.SYNOLOGY_PASSWORD,
      process.env.SYNOLOGY_SECURE !== 'false'
    );
    
    // Log the baseURL to verify it's formed correctly
    console.log(`Generated baseURL: ${synologyAPI.baseURL}`);
    
    // Initialize the API (this will attempt to authenticate)
    console.log('Initializing API...');
    await synologyAPI.initialize();
    
    // Try to list files in various subdirectories
    const subdirectories = [
      '/volume1/homes',
      '/volume1/web',
      '/volume1/public',
      '/volume1/music',
      '/volume1/photo',
      '/volume1/video'
    ];
    
    for (const subdirectory of subdirectories) {
      try {
        console.log(`\nTrying to list files in subdirectory: ${subdirectory}`);
        const files = await synologyAPI.listFiles(subdirectory, { limit: 10 });
        
        console.log(`Successfully listed ${files.length} files in ${subdirectory}:`);
        if (files.length > 0) {
          // Print the first 5 files (or fewer if there are less than 5)
          const filesToShow = Math.min(files.length, 5);
          console.log(`Showing ${filesToShow} of ${files.length} files:`);
          for (let i = 0; i < filesToShow; i++) {
            const file = files[i];
            console.log(`  ${i+1}. ${file.name} (${file.isdir ? 'Directory' : 'File'})`);
          }
          
          // If we successfully listed files in a subdirectory, we can stop
          console.log('\nSuccessfully verified that subdirectory listing works!');
          break;
        } else {
          console.log('No files found in this subdirectory.');
        }
      } catch (error) {
        console.error(`Error listing files in ${subdirectory}:`, error.message);
        // Continue to the next subdirectory
      }
    }
    
    console.log('\nTest completed!');
  } catch (error) {
    console.error('Error testing Synology API:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testSynologySubdirectory();