/**
 * Test script to verify that user creation uses the database settings for default roles
 * 
 * This script demonstrates how to:
 * 1. Set up default role settings in the database
 * 2. Create a local user and verify the assigned role
 * 3. Simulate Google user creation and verify the assigned role
 * 4. Test Google Groups role mapping
 * 
 * To run this test:
 * 1. Make sure the server is running
 * 2. Run: node test-user-creation-with-settings.js
 */

require('dotenv').config();
const mongoose = require('mongoose');
const RoleSettings = require('./models/RoleSettings');
const Role = require('./models/Role');
const User = require('./models/User');
const bcrypt = require('bcryptjs');

// Mock GoogleAdminAPI for testing
class MockGoogleAdminAPI {
  constructor() {
    this.initialized = false;
    this.groups = [
      { id: '1', email: '<EMAIL>', name: 'Admins' },
      { id: '2', email: '<EMAIL>', name: 'Editors' },
      { id: '3', email: '<EMAIL>', name: 'Viewers' }
    ];
    
    // Mock user group memberships
    this.memberships = {
      '<EMAIL>': ['<EMAIL>'],
      '<EMAIL>': ['<EMAIL>'],
      '<EMAIL>': ['<EMAIL>'],
      '<EMAIL>': ['<EMAIL>', '<EMAIL>'],
      '<EMAIL>': []
    };
  }
  
  async initialize() {
    this.initialized = true;
    return true;
  }
  
  async listGroups() {
    return this.groups;
  }
  
  async isUserMemberOfGroup(userEmail, groupEmail) {
    const userGroups = this.memberships[userEmail] || [];
    return userGroups.includes(groupEmail);
  }
}

// Test settings
const TEST_SETTINGS = {
  defaultRoleLocalUsers: 'editor',
  defaultRoleGoogleUsers: 'viewer',
  googleGroupsRoleMapping: [
    { groupEmail: '<EMAIL>', roleName: 'admin' },
    { groupEmail: '<EMAIL>', roleName: 'editor' }
  ]
};

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (err) {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  }
}

// Create test roles if they don't exist
async function createTestRoles() {
  try {
    // Check if roles exist
    const roles = ['admin', 'editor', 'viewer'];
    
    for (const roleName of roles) {
      const role = await Role.findOne({ name: roleName });
      
      if (!role) {
        console.log(`Creating role: ${roleName}`);
        const newRole = new Role({
          name: roleName,
          description: `Test role: ${roleName}`,
          permissions: roleName === 'admin' ? ['*'] : [`${roleName}:read`],
          isDefault: roleName === 'admin'
        });
        
        await newRole.save();
      }
    }
    
    console.log('Test roles created or verified');
  } catch (err) {
    console.error('Error creating test roles:', err);
  }
}

// Set up test settings
async function setupTestSettings() {
  try {
    // Get or create settings
    let settings = await RoleSettings.findOne();
    
    if (!settings) {
      settings = new RoleSettings(TEST_SETTINGS);
    } else {
      settings.defaultRoleLocalUsers = TEST_SETTINGS.defaultRoleLocalUsers;
      settings.defaultRoleGoogleUsers = TEST_SETTINGS.defaultRoleGoogleUsers;
      settings.googleGroupsRoleMapping = TEST_SETTINGS.googleGroupsRoleMapping;
    }
    
    await settings.save();
    console.log('Test settings saved:', settings);
    return settings;
  } catch (err) {
    console.error('Error setting up test settings:', err);
    throw err;
  }
}

// Create a local user
async function createLocalUser(name, email) {
  try {
    // Delete user if exists
    await User.deleteOne({ email });
    
    // Create avatar URL
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    const avatarColor = Math.floor(Math.random() * 16777215).toString(16);
    const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${avatarColor}&color=fff`;
    
    // Create password hash
    const salt = await bcrypt.genSalt(10);
    const password = await bcrypt.hash('password123', salt);
    
    // Create user without specifying roles (should use default)
    const user = new User({
      authType: 'local',
      name,
      email,
      password,
      avatar: avatarUrl,
      isActive: true
    });
    
    await user.save();
    console.log(`Local user created: ${name} (${email}) with roles: ${user.roles.join(', ')}`);
    return user;
  } catch (err) {
    console.error('Error creating local user:', err);
    throw err;
  }
}

// Simulate Google user creation
async function simulateGoogleUserCreation(profile) {
  try {
    // Delete user if exists
    await User.deleteOne({ email: profile.email });
    
    // Get default role settings
    const settings = await RoleSettings.getCurrentSettings();
    let defaultRole = settings.defaultRoleGoogleUsers;
    let roles = [defaultRole];
    
    // Convert googleGroupsRoleMapping to the format expected by the code
    let googleGroupsRoleMapping = {};
    if (settings.googleGroupsRoleMapping && settings.googleGroupsRoleMapping.length > 0) {
      settings.googleGroupsRoleMapping.forEach(mapping => {
        googleGroupsRoleMapping[mapping.groupEmail] = mapping.roleName;
      });
    }
    
    // Check Google Groups membership
    if (Object.keys(googleGroupsRoleMapping).length > 0) {
      const googleAdminAPI = new MockGoogleAdminAPI();
      await googleAdminAPI.initialize();
      
      const userEmail = profile.email;
      const groups = await googleAdminAPI.listGroups();
      
      for (const [groupEmail, roleName] of Object.entries(googleGroupsRoleMapping)) {
        const group = groups.find(g => g.email.toLowerCase() === groupEmail.toLowerCase());
        
        if (group) {
          const isMember = await googleAdminAPI.isUserMemberOfGroup(userEmail, group.email);
          
          if (isMember) {
            if (!roles.includes(roleName)) {
              roles.push(roleName);
            }
          }
        }
      }
    }
    
    // Create user
    const user = new User({
      googleId: profile.id || 'test-google-id',
      name: profile.name,
      email: profile.email,
      avatar: profile.avatar || 'https://example.com/avatar.jpg',
      roles: roles
    });
    
    await user.save();
    console.log(`Google user created: ${profile.name} (${profile.email}) with roles: ${user.roles.join(', ')}`);
    return user;
  } catch (err) {
    console.error('Error simulating Google user creation:', err);
    throw err;
  }
}

// Verify user roles
async function verifyUserRoles(email, expectedRoles) {
  try {
    const user = await User.findOne({ email });
    
    if (!user) {
      console.error(`User not found: ${email}`);
      return false;
    }
    
    // Check if all expected roles are assigned
    const hasAllRoles = expectedRoles.every(role => user.roles.includes(role));
    // Check if no unexpected roles are assigned
    const hasOnlyExpectedRoles = user.roles.every(role => expectedRoles.includes(role));
    
    const isValid = hasAllRoles && hasOnlyExpectedRoles;
    
    if (isValid) {
      console.log(`✅ User ${email} has the expected roles: ${expectedRoles.join(', ')}`);
      return true;
    } else {
      console.error(`❌ User ${email} roles do not match expected roles`);
      console.log(`Expected: ${expectedRoles.join(', ')}`);
      console.log(`Actual: ${user.roles.join(', ')}`);
      return false;
    }
  } catch (err) {
    console.error(`Error verifying user roles for ${email}:`, err);
    return false;
  }
}

// Main test function
async function runTest() {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Create test roles
    await createTestRoles();
    
    // Set up test settings
    await setupTestSettings();
    
    // Test local user creation
    console.log('\n=== Testing Local User Creation ===');
    const localUser = await createLocalUser('Test Local User', '<EMAIL>');
    await verifyUserRoles('<EMAIL>', ['editor']);
    
    // Test Google user creation with default role
    console.log('\n=== Testing Google User Creation with Default Role ===');
    const googleUser = await simulateGoogleUserCreation({
      name: 'Test Google User',
      email: '<EMAIL>'
    });
    await verifyUserRoles('<EMAIL>', ['viewer']);
    
    // Test Google user creation with group role
    console.log('\n=== Testing Google User Creation with Group Role ===');
    const adminUser = await simulateGoogleUserCreation({
      name: 'Test Admin User',
      email: '<EMAIL>'
    });
    await verifyUserRoles('<EMAIL>', ['viewer', 'admin']);
    
    // Test Google user creation with multiple group roles
    console.log('\n=== Testing Google User Creation with Multiple Group Roles ===');
    const multiRoleUser = await simulateGoogleUserCreation({
      name: 'Test Multi-Role User',
      email: '<EMAIL>'
    });
    await verifyUserRoles('<EMAIL>', ['viewer', 'admin', 'editor']);
    
    console.log('\n✅ All tests completed successfully');
    
  } catch (err) {
    console.error('Test failed:', err);
  } finally {
    // Disconnect from database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
runTest()
  .then(() => {
    console.log('Test completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error running test:', err);
    process.exit(1);
  });