/**
 * MongoDB Sessions Collection Setup Script
 * 
 * This script creates the sessions collection and sets up the TTL index
 * for automatic session expiration.
 * 
 * Run this script with: node sessions_setup.js
 */

require('dotenv').config();
const mongoose = require('mongoose');

async function setupSessionsCollection() {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');

    // Check if sessions collection exists
    const collections = await mongoose.connection.db.listCollections({ name: 'sessions' }).toArray();
    
    if (collections.length === 0) {
      // Create sessions collection
      await mongoose.connection.db.createCollection('sessions');
      console.log('Sessions collection created');
    } else {
      console.log('Sessions collection already exists');
    }
    
    // Ensure TTL index exists
    await mongoose.connection.db.collection('sessions').createIndex(
      { expires: 1 },
      { expireAfterSeconds: 0 }
    );
    console.log('Sessions TTL index created/updated');

    // Print the raw MongoDB commands for manual execution
    console.log('\nRaw MongoDB commands for manual execution:');
    console.log('----------------------------------------');
    console.log('use csfportal');
    console.log('db.createCollection("sessions")');
    console.log('db.sessions.createIndex({ expires: 1 }, { expireAfterSeconds: 0 })');
    console.log('----------------------------------------');

  } catch (error) {
    console.error('Error setting up sessions collection:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Run the setup function
setupSessionsCollection();