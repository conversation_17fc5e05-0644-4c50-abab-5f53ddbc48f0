/**
 * Test script for contacts synchronization feature
 * 
 * This script tests the functionality of the contacts synchronization feature,
 * including Google and Apple contacts integration and auto-sync functionality.
 */

const mongoose = require('mongoose');
const Contact = require('./models/Contact');
const ContactSyncConfig = require('./models/ContactSyncConfig');
const ContactSyncLog = require('./models/ContactSyncLog');
const GooglePeopleAPI = require('./server/integrations/googlePeople/googlePeopleAPI');
const AppleContactsAPI = require('./server/integrations/appleContacts/appleContactsAPI');
const contactSyncScheduler = require('./server/utils/contactSyncScheduler');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('MongoDB connected');
  runTests();
}).catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

/**
 * Run all tests
 */
async function runTests() {
  try {
    console.log('\n=== Testing Contacts Synchronization Feature ===\n');
    
    // Test Google People API
    await testGooglePeopleAPI();
    
    // Test Apple Contacts API
    await testAppleContactsAPI();
    
    // Test sync configuration
    await testSyncConfiguration();
    
    // Test scheduler
    await testSyncScheduler();
    
    console.log('\n=== All tests completed successfully ===\n');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
    process.exit(0);
  }
}

/**
 * Test Google People API
 */
async function testGooglePeopleAPI() {
  console.log('\n--- Testing Google People API ---\n');
  
  try {
    // Check if environment variables are set
    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      console.log('Google API credentials not found in environment variables. Skipping test.');
      return;
    }
    
    // Create API instance
    const googlePeopleAPI = new GooglePeopleAPI();
    
    // Generate auth URL
    const authUrl = googlePeopleAPI.generateAuthUrl();
    console.log('Auth URL generated:', authUrl ? 'Yes' : 'No');
    
    // If refresh token is available, test API calls
    if (process.env.GOOGLE_TEST_REFRESH_TOKEN) {
      console.log('Testing with refresh token...');
      
      // Initialize API with refresh token
      const apiWithToken = new GooglePeopleAPI({
        refreshToken: process.env.GOOGLE_TEST_REFRESH_TOKEN
      });
      
      await apiWithToken.initialize();
      
      // Test contact group operations
      const groupName = 'CSF Portal Test Group';
      const group = await apiWithToken.getOrCreateContactGroup(groupName);
      console.log('Contact group created/found:', group.resourceName);
      
      // Create a test contact
      const testContact = {
        name: 'Test Contact ' + Date.now(),
        phone: '************',
        email: '<EMAIL>',
        company: 'Test Company',
        category: 'business'
      };
      
      const createdContact = await apiWithToken.createContact(testContact, group.resourceName);
      console.log('Contact created:', createdContact.resourceName);
      
      // Update the contact
      testContact.company = 'Updated Company';
      const updatedContact = await apiWithToken.updateContact(createdContact.resourceName, testContact);
      console.log('Contact updated:', updatedContact.resourceName);
      
      // Delete the contact
      await apiWithToken.deleteContact(createdContact.resourceName);
      console.log('Contact deleted');
      
      console.log('Google People API test completed successfully');
    } else {
      console.log('No refresh token available for testing API calls. Skipping.');
    }
  } catch (error) {
    console.error('Google People API test failed:', error);
    throw error;
  }
}

/**
 * Test Apple Contacts API
 */
async function testAppleContactsAPI() {
  console.log('\n--- Testing Apple Contacts API ---\n');
  
  try {
    // Create API instance
    const appleContactsAPI = new AppleContactsAPI();
    
    // Initialize API
    await appleContactsAPI.initialize();
    
    // Create test contacts
    const testContacts = [
      {
        name: 'Apple Test Contact 1',
        phone: '************',
        email: '<EMAIL>',
        company: 'Test Company 1',
        category: 'business',
        address: {
          street: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          zipCode: '12345',
          country: 'USA'
        }
      },
      {
        name: 'Apple Test Contact 2',
        phone: '************',
        email: '<EMAIL>',
        company: 'Test Company 2',
        category: 'support',
        address: {
          street: '456 Oak Ave',
          city: 'Somewhere',
          state: 'NY',
          zipCode: '67890',
          country: 'USA'
        }
      }
    ];
    
    // Generate vCard data
    const vCardData = appleContactsAPI.generateVCardData(testContacts);
    console.log('vCard data generated:', vCardData.length > 0 ? 'Yes' : 'No');
    
    // Generate vCard file
    const filename = appleContactsAPI.generateUniqueFilename('test-user');
    const filePath = await appleContactsAPI.generateVCardFile(testContacts, filename);
    console.log('vCard file generated:', filePath);
    
    // Clean up
    await appleContactsAPI.cleanupTempFile(filePath);
    console.log('Temporary file cleaned up');
    
    console.log('Apple Contacts API test completed successfully');
  } catch (error) {
    console.error('Apple Contacts API test failed:', error);
    throw error;
  }
}

/**
 * Test sync configuration
 */
async function testSyncConfiguration() {
  console.log('\n--- Testing Sync Configuration ---\n');
  
  try {
    // Create a test user ID
    const testUserId = new mongoose.Types.ObjectId();
    
    // Create Google sync config
    const googleConfig = new ContactSyncConfig({
      user: testUserId,
      provider: 'google',
      enabled: true,
      syncFrequency: 60,
      syncCategories: ['business', 'support'],
      googleRefreshToken: 'test-refresh-token',
      googleResourceName: 'test-resource-name'
    });
    
    await googleConfig.save();
    console.log('Google sync config created:', googleConfig._id);
    
    // Create Apple sync config
    const appleConfig = new ContactSyncConfig({
      user: testUserId,
      provider: 'apple',
      enabled: true,
      syncCategories: ['business', 'emergency']
    });
    
    await appleConfig.save();
    console.log('Apple sync config created:', appleConfig._id);
    
    // Create sync log
    const syncLog = new ContactSyncLog({
      user: testUserId,
      provider: 'google',
      status: 'success',
      contactsAdded: 5,
      contactsUpdated: 2,
      contactsRemoved: 1,
      startTime: new Date(Date.now() - 3600000), // 1 hour ago
      endTime: new Date(Date.now() - 3590000), // 1 hour ago + 10 seconds
      details: 'Test sync operation'
    });
    
    await syncLog.save();
    console.log('Sync log created:', syncLog._id);
    
    // Clean up
    await ContactSyncConfig.deleteMany({ user: testUserId });
    await ContactSyncLog.deleteMany({ user: testUserId });
    console.log('Test data cleaned up');
    
    console.log('Sync configuration test completed successfully');
  } catch (error) {
    console.error('Sync configuration test failed:', error);
    throw error;
  }
}

/**
 * Test sync scheduler
 */
async function testSyncScheduler() {
  console.log('\n--- Testing Sync Scheduler ---\n');
  
  try {
    // Create a test user ID
    const testUserId = new mongoose.Types.ObjectId();
    
    // Create test sync config
    const syncConfig = new ContactSyncConfig({
      user: testUserId,
      provider: 'google',
      enabled: true,
      syncFrequency: 60,
      syncCategories: ['business', 'support'],
      googleRefreshToken: 'test-refresh-token',
      googleResourceName: 'test-resource-name'
    });
    
    await syncConfig.save();
    console.log('Test sync config created:', syncConfig._id);
    
    // Test scheduling
    contactSyncScheduler.scheduleSync(syncConfig);
    console.log('Sync scheduled');
    
    // Wait a moment to ensure scheduling completes
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Update config
    syncConfig.enabled = false;
    await syncConfig.save();
    
    // Test updating scheduler
    await contactSyncScheduler.updateSyncConfig(syncConfig);
    console.log('Sync config updated');
    
    // Clean up
    await ContactSyncConfig.deleteMany({ user: testUserId });
    console.log('Test data cleaned up');
    
    console.log('Sync scheduler test completed successfully');
  } catch (error) {
    console.error('Sync scheduler test failed:', error);
    throw error;
  }
}