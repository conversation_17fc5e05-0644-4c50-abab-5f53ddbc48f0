const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Set up axios with credentials
axios.defaults.withCredentials = true;
axios.defaults.baseURL = 'http://localhost:6000';

// Test user credentials
const user1 = {
  email: '<EMAIL>',
  password: 'password123'
};

const user2 = {
  email: '<EMAIL>',
  password: 'password123'
};

// Test note data
const testNote = {
  title: 'User Isolation Test Note',
  content: 'This note should only be visible to the user who created it',
  type: 'note',
  color: '#ffffff',
  pinned: false
};

// Test functions
const login = async (user) => {
  try {
    const response = await axios.post('/api/auth/login', user);
    console.log(`Login successful for ${user.email}:`, response.data.user.name);
    return response.data.user;
  } catch (error) {
    console.error(`<PERSON><PERSON> failed for ${user.email}:`, error.response?.data || error.message);
    throw error;
  }
};

const createNote = async () => {
  try {
    const response = await axios.post('/api/notes', testNote);
    console.log('Note created:', response.data);
    return response.data;
  } catch (error) {
    console.error('Create note failed:', error.response?.data || error.message);
    throw error;
  }
};

const getAllNotes = async (user) => {
  try {
    const response = await axios.get('/api/notes');
    console.log(`Notes for ${user.email}:`, response.data);
    return response.data;
  } catch (error) {
    console.error('Get all notes failed:', error.response?.data || error.message);
    throw error;
  }
};

const getNoteById = async (id, expectedStatus = 200) => {
  try {
    const response = await axios.get(`/api/notes/${id}`);
    console.log('Note by ID:', response.data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === expectedStatus) {
      console.log(`Expected error (${expectedStatus}) when accessing note:`, error.response.data);
      return error.response.data;
    }
    console.error('Get note by ID failed:', error.response?.data || error.message);
    throw error;
  }
};

const updateNote = async (id, data, expectedStatus = 200) => {
  try {
    const response = await axios.put(`/api/notes/${id}`, data);
    console.log('Note updated:', response.data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === expectedStatus) {
      console.log(`Expected error (${expectedStatus}) when updating note:`, error.response.data);
      return error.response.data;
    }
    console.error('Update note failed:', error.response?.data || error.message);
    throw error;
  }
};

const deleteNote = async (id, expectedStatus = 200) => {
  try {
    const response = await axios.delete(`/api/notes/${id}`);
    console.log('Note deleted:', response.data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.status === expectedStatus) {
      console.log(`Expected error (${expectedStatus}) when deleting note:`, error.response.data);
      return error.response.data;
    }
    console.error('Delete note failed:', error.response?.data || error.message);
    throw error;
  }
};

// Run tests
const runTests = async () => {
  try {
    // Login as user1
    const user1Data = await login(user1);
    
    // Create a note as user1
    const user1Note = await createNote();
    console.log('User1 created note with ID:', user1Note._id);
    
    // Get all notes for user1
    await getAllNotes(user1);
    
    // Login as user2
    const user2Data = await login(user2);
    
    // Get all notes for user2 (should not include user1's note)
    const user2Notes = await getAllNotes(user2);
    
    // Verify user2 cannot see user1's note
    if (user2Notes.some(note => note._id === user1Note._id)) {
      throw new Error('User isolation failed: User2 can see User1\'s note');
    } else {
      console.log('User isolation test passed: User2 cannot see User1\'s note');
    }
    
    // Try to access user1's note directly (should return 403 Forbidden)
    await getNoteById(user1Note._id, 403);
    
    // Try to update user1's note (should return 403 Forbidden)
    await updateNote(user1Note._id, { title: 'Updated by User2' }, 403);
    
    // Try to delete user1's note (should return 403 Forbidden)
    await deleteNote(user1Note._id, 403);
    
    // Login back as user1
    await login(user1);
    
    // Verify user1 can still access their note
    await getNoteById(user1Note._id);
    
    // Clean up - delete the test note
    await deleteNote(user1Note._id);
    
    console.log('All user isolation tests completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the tests
runTests();