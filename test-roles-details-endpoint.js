const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Set the base URL for API requests
const baseURL = process.env.NODE_ENV === 'production' 
  ? 'https://portal.ukcsf.org' 
  : `http://localhost:${process.env.PORT || 8080}`;

// Function to test the /api/roles/details endpoint
async function testRolesDetailsEndpoint() {
  try {
    console.log('Testing /api/roles/details endpoint...');
    
    // You'll need to get a valid session cookie or token for authentication
    // This is a simplified example - in a real test, you would need to authenticate first
    
    // Make a request to the endpoint
    const response = await axios.get(`${baseURL}/api/roles/details`, {
      // Include authentication headers or cookies as needed
      // headers: { 'Authorization': 'Bearer your_token_here' }
    });
    
    // Check if the response is successful
    if (response.status === 200) {
      console.log('✅ Success! /api/roles/details endpoint is working.');
      console.log('Response data:', JSON.stringify(response.data, null, 2));
    } else {
      console.error('❌ Unexpected response status:', response.status);
    }
  } catch (error) {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('❌ Error response:', error.response.status, error.response.statusText);
      console.error('Error data:', error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('❌ No response received:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('❌ Error setting up request:', error.message);
    }
  }
}

// Run the test
testRolesDetailsEndpoint();

console.log('\nNote: This test requires authentication. If you see a 401 Unauthorized error,');
console.log('you need to modify the script to include valid authentication credentials.');
console.log('You can also test the endpoint manually using a tool like Postman or curl');
console.log('with proper authentication headers.');