/**
 * Enhanced test script to verify that ALL Mosyle Business endpoints
 * no longer return 304 errors after adding cache-control headers.
 * 
 * This script tests the following endpoints:
 * - /api/mosyle-business/config
 * - /api/mosyle-business/devices
 * - /api/mosyle-business/devices/:id
 * - /api/mosyle-business/users
 * - /api/mosyle-business/groups
 */
const axios = require('axios');

// Create an axios instance that will follow redirects but won't use browser cache
const client = axios.create({
  baseURL: 'http://localhost:8080',
  maxRedirects: 5,
  headers: {
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
});

/**
 * Test a specific Mosyle Business endpoint
 * @param {string} endpoint - The endpoint to test
 * @param {string} description - Description of the endpoint
 */
async function testEndpoint(endpoint, description) {
  console.log(`\nTesting Mosyle Business ${description} endpoint (${endpoint})...`);
  
  try {
    // Make first request
    const response1 = await client.get(endpoint);
    console.log(`First request status: ${response1.status}`);
    
    // Make second request immediately after
    const response2 = await client.get(endpoint);
    console.log(`Second request status: ${response2.status}`);
    
    // Verify that both requests returned 200 OK
    if (response1.status === 200 && response2.status === 200) {
      console.log(`✅ Mosyle Business ${description} endpoint is returning 200 OK for both requests`);
    } else {
      console.log(`❌ Mosyle Business ${description} endpoint is not returning 200 OK for both requests`);
    }
    
    // Check for cache-control headers
    const cacheControl = response2.headers['cache-control'];
    if (cacheControl && cacheControl.includes('no-store') && cacheControl.includes('no-cache')) {
      console.log('✅ Cache-control headers are set correctly');
    } else {
      console.log('❌ Cache-control headers are not set correctly');
      console.log('Cache-Control:', cacheControl);
    }

    return true;
  } catch (error) {
    console.error(`Error testing Mosyle Business ${description} endpoint:`, error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    }
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('Starting Enhanced Mosyle Business 304 Fix Tests...');
  console.log('=================================================');
  
  // Test config endpoint
  await testEndpoint('/api/mosyle-business/config', 'config');
  
  // Test devices endpoint
  const devicesSuccess = await testEndpoint('/api/mosyle-business/devices', 'devices');
  
  // Test device details endpoint (only if devices endpoint succeeded)
  if (devicesSuccess) {
    try {
      // Get the first device ID from the devices endpoint
      const devicesResponse = await client.get('/api/mosyle-business/devices');
      if (devicesResponse.data && devicesResponse.data.length > 0) {
        const deviceId = devicesResponse.data[0].id || devicesResponse.data[0].device_id || Object.keys(devicesResponse.data[0])[0];
        await testEndpoint(`/api/mosyle-business/devices/${deviceId}`, 'device details');
      } else {
        console.log('\n⚠️ No devices found to test device details endpoint');
      }
    } catch (error) {
      console.error('Error getting device ID for testing:', error.message);
    }
  }
  
  // Test users endpoint
  await testEndpoint('/api/mosyle-business/users', 'users');
  
  // Test groups endpoint
  await testEndpoint('/api/mosyle-business/groups', 'groups');
  
  console.log('\nTests completed.');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});