// Test script to verify token caching for Apple Business Manager API
const AppleBusinessManagerAPI = require('./server/integrations/appleBusinessManager/appleBusinessManagerAPI');
const cacheUtil = require('./server/utils/cacheUtil');
require('dotenv').config();

// Get configuration from environment variables
const clientId = process.env.APPLE_BUSINESS_MANAGER_CLIENT_ID;
const keyId = process.env.APPLE_BUSINESS_MANAGER_KEY_ID;
const privateKey = process.env.APPLE_BUSINESS_MANAGER_PRIVATE_KEY;
const tokenExpiry = process.env.APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY;

// Store original console.log
const originalConsoleLog = console.log;

// Array to capture logs for analysis
const logCapture = [];

// Override console.log to capture logs
console.log = function(...args) {
  const logMessage = args.join(' ');
  logCapture.push(logMessage);
  originalConsoleLog.apply(console, args);
};

// Test the token caching functionality
async function testTokenCaching() {
  console.log('\n=== Testing Apple Business Manager Token Caching ===\n');
  
  // Check if required environment variables are set
  if (!clientId || !keyId || !privateKey) {
    console.error('Error: Required environment variables are not set.');
    console.log('Required variables: APPLE_BUSINESS_MANAGER_CLIENT_ID, APPLE_BUSINESS_MANAGER_KEY_ID, APPLE_BUSINESS_MANAGER_PRIVATE_KEY');
    return;
  }

  // Clear the cache before testing
  cacheUtil.clear();
  console.log('Cache cleared for testing');
  
  try {
    // Clear log capture
    logCapture.length = 0;
    
    // Create API instance
    const api = new AppleBusinessManagerAPI(
      clientId,
      keyId,
      privateKey,
      tokenExpiry
    );
    
    console.log('API instance created successfully.');
    
    // Test 1: First authentication should make an API call
    console.log('\nTest 1: First authentication should make an API call');
    
    // Clear log capture before authentication
    logCapture.length = 0;
    
    const token1 = await api.authenticate();
    console.log(`Token received: ${token1.substring(0, 10)}...`);
    
    // Check logs for evidence of API call
    const madeApiCall = logCapture.some(log => log.includes('Authentication response status:'));
    const usedCache = logCapture.some(log => log.includes('Using cached token'));
    
    if (madeApiCall && !usedCache) {
      console.log('✅ Test 1 passed: API call was made for first authentication');
    } else {
      console.log('❌ Test 1 failed: No API call was made for first authentication');
    }
    
    // Test 2: Second authentication should use cached token
    console.log('\nTest 2: Second authentication should use cached token');
    
    // Clear log capture before second authentication
    logCapture.length = 0;
    
    const token2 = await api.authenticate();
    console.log(`Token received: ${token2.substring(0, 10)}...`);
    
    // Check logs for evidence of cache usage
    const madeApiCall2 = logCapture.some(log => log.includes('Authentication response status:'));
    const usedCache2 = logCapture.some(log => log.includes('Using cached token'));
    
    if (!madeApiCall2 && usedCache2) {
      console.log('✅ Test 2 passed: Cached token was used for second authentication');
    } else {
      console.log('❌ Test 2 failed: Cached token was not used for second authentication');
    }
    
    // Test 3: Authentication after cache expiration should make a new API call
    console.log('\nTest 3: Authentication after cache expiration should make a new API call');
    
    // Get the cache key
    const cacheKey = cacheUtil.createKey('apple-business-manager-token', {
      clientId: clientId,
      keyId: keyId
    });
    
    // Get the cached token
    const cachedToken = cacheUtil.get(cacheKey);
    if (cachedToken) {
      console.log(`Current cached token expiration: ${new Date(cachedToken.expiration).toISOString()}`);
    }
    
    // Remove the token from the cache entirely
    cacheUtil.remove(cacheKey);
    
    // Also clear the instance token to force a new authentication
    api.token = null;
    api.tokenExpiration = null;
    
    console.log('Removed token from cache and cleared instance token');
    
    // Clear log capture before third authentication
    logCapture.length = 0;
    
    const token3 = await api.authenticate();
    console.log(`Token received: ${token3.substring(0, 10)}...`);
    
    // Check logs for evidence of API call
    const madeApiCall3 = logCapture.some(log => log.includes('Authentication response status:'));
    const usedCache3 = logCapture.some(log => log.includes('Using cached token'));
    
    if (madeApiCall3 && !usedCache3) {
      console.log('✅ Test 3 passed: API call was made after token expiration');
    } else {
      console.log('❌ Test 3 failed: No API call was made after token expiration');
    }
    
    // Test 4: Create a new instance and verify it uses the cached token
    console.log('\nTest 4: New instance should use the cached token');
    
    // Ensure we have a valid token in the cache
    // Get the cache key again
    const cacheKey2 = cacheUtil.createKey('apple-business-manager-token', {
      clientId: clientId,
      keyId: keyId
    });
    
    // Check if we have a valid token in the cache
    const cachedToken2 = cacheUtil.get(cacheKey2);
    if (!cachedToken2) {
      console.log('No valid token in cache, caching the current token');
      // Cache the current token
      cacheUtil.set(cacheKey2, {
        token: token3,
        expiration: Date.now() + 3600 * 1000 - (5 * 60 * 1000) // 1 hour minus 5 minutes
      }, 3600 * 1000 - (5 * 60 * 1000));
    }
    
    // Create a new API instance
    const api2 = new AppleBusinessManagerAPI(
      clientId,
      keyId,
      privateKey,
      tokenExpiry
    );
    
    // Clear log capture before fourth authentication
    logCapture.length = 0;
    
    const token4 = await api2.authenticate();
    console.log(`Token received: ${token4.substring(0, 10)}...`);
    
    // Check logs for evidence of cache usage
    const madeApiCall4 = logCapture.some(log => log.includes('Authentication response status:'));
    const usedCache4 = logCapture.some(log => log.includes('Using cached token'));
    
    if (!madeApiCall4 && usedCache4) {
      console.log('✅ Test 4 passed: New instance used the cached token');
    } else {
      console.log('❌ Test 4 failed: New instance did not use the cached token');
    }
    
    console.log('\n=== Token Caching Test Completed Successfully ===\n');
  } catch (error) {
    console.error('Error during token caching test:', error.message);
    
    // Log more detailed error information
    if (error.response) {
      console.error('Error response data:', JSON.stringify(error.response.data, null, 2));
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
    } else if (error.request) {
      console.error('Error request:', error.request);
    }
    
    console.log('\n=== Token Caching Test Failed ===\n');
  } finally {
    // Clean up
    console.log = originalConsoleLog;
  }
}

// Run the test
testTokenCaching().catch(error => {
  console.error('Test failed with error:', error);
});