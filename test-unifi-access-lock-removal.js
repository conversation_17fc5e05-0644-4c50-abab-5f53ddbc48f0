/**
 * Test script for verifying the removal of the lockDoor function from the UniFi Access API
 * 
 * This script tests:
 * 1. That the UniFi Access API still works correctly without the lockDoor function
 * 2. That attempting to lock a door returns the appropriate error message
 * 3. That other functions like unlockDoor still work correctly
 * 
 * Usage:
 * node test-unifi-access-lock-removal.js
 */

// Set environment variables for testing
process.env.UNIFI_ACCESS_HOST = process.env.UNIFI_ACCESS_HOST || 'your-unifi-access-host';
process.env.UNIFI_ACCESS_API_KEY = process.env.UNIFI_ACCESS_API_KEY || 'your-unifi-access-api-key';
process.env.UNIFI_ACCESS_PORT = process.env.UNIFI_ACCESS_PORT || '443';

// Import required modules
const UnifiAccessAPI = require('./server/integrations/unifiAccess/unifiAccessAPI');
const unifiAccessController = require('./server/controllers/unifiAccessController');
const accessControlController = require('./server/controllers/accessControlController');

// Create a test function
async function testUnifiAccessLockRemoval() {
  console.log('Testing UniFi Access API after lockDoor function removal...');
  
  try {
    // Initialize the API
    const unifiAccessAPI = new UnifiAccessAPI();
    
    // Test 1: Verify that the API initializes correctly
    console.log('\nTest 1: Verify API initialization');
    try {
      await unifiAccessAPI.initialize();
      console.log('✅ API initialized successfully');
    } catch (error) {
      console.error('❌ API initialization failed:', error.message);
    }
    
    // Test 2: Verify that the unlockDoor function still works
    console.log('\nTest 2: Verify unlockDoor function');
    try {
      // Create a mock door ID - replace with a real one if available
      const doorId = 'test-door-id';
      
      // Create mock request and response objects
      const req = { params: { id: doorId } };
      const res = {
        json: (data) => {
          console.log('Response data:', data);
          return data;
        },
        status: (code) => ({
          json: (data) => {
            console.log(`Response status: ${code}, data:`, data);
            return { code, data };
          }
        })
      };
      
      // Try to unlock the door
      try {
        await unifiAccessController.unlockDoor(req, res);
        console.log('✅ unlockDoor function called successfully');
      } catch (error) {
        // This might fail with a real error if the door ID is invalid or the API is not accessible
        // That's okay for this test - we just want to make sure the function exists and is called
        console.log('⚠️ unlockDoor function called but returned an error (this might be expected):', error.message);
      }
    } catch (error) {
      console.error('❌ Error testing unlockDoor function:', error.message);
    }
    
    // Test 3: Verify that attempting to lock a door returns an error
    console.log('\nTest 3: Verify lockDoor function returns an error');
    try {
      // Create a mock door ID - replace with a real one if available
      const doorId = 'test-door-id';
      
      // Create mock request and response objects
      const req = { params: { id: doorId } };
      const res = {
        json: (data) => {
          console.log('Response data:', data);
          return data;
        },
        status: (code) => ({
          json: (data) => {
            console.log(`Response status: ${code}, data:`, data);
            return { code, data };
          }
        })
      };
      
      // Try to lock the door
      await unifiAccessController.lockDoor(req, res);
      console.log('✅ lockDoor function correctly returned an error');
    } catch (error) {
      console.error('❌ Error testing lockDoor function:', error.message);
    }
    
    // Test 4: Verify that the access control controller handles the lock action correctly
    console.log('\nTest 4: Verify access control controller handles lock action');
    try {
      // Create a mock door ID - replace with a real one if available
      const doorId = 'test-door-id';
      
      // Create mock request and response objects
      const req = { 
        body: { 
          doorId,
          system: 'unifi-access',
          action: 'lock'
        } 
      };
      const res = {
        json: (data) => {
          console.log('Response data:', data);
          return data;
        },
        status: (code) => ({
          json: (data) => {
            console.log(`Response status: ${code}, data:`, data);
            return { code, data };
          }
        })
      };
      
      // Try to control the door with the lock action
      await accessControlController.controlDoor(req, res);
      console.log('✅ controlDoor function correctly handled the lock action');
    } catch (error) {
      console.error('❌ Error testing controlDoor function:', error.message);
    }
    
    console.log('\nTest completed successfully.');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }
}

// Run the test
testUnifiAccessLockRemoval().then(() => {
  console.log('Test script completed');
  // Exit after a short delay to allow any pending operations to complete
  setTimeout(() => process.exit(0), 1000);
}).catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});