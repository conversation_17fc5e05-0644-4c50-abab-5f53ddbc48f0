const axios = require('axios');

// Test the RADIUS server endpoints
async function testRadiusServer() {
  try {
    console.log('Testing RADIUS server endpoints...');
    console.log('=====================================');
    
    // Test if the RADIUS server is running locally
    console.log('\n1. Testing local server status...');
    const isLocalResponse = await axios.get('http://localhost:8080/api/radius/is-local');
    console.log('Local server status:', JSON.stringify(isLocalResponse.data, null, 2));
    
    // Test the RADIUS server status
    console.log('\n2. Testing RADIUS server status...');
    const statusResponse = await axios.get('http://localhost:8080/api/radius/status');
    console.log('RADIUS server status:', JSON.stringify(statusResponse.data, null, 2));
    
    // Test Google Workspace authentication status (requires auth - will likely fail)
    console.log('\n3. Testing Google Workspace authentication status...');
    try {
      const googleStatusResponse = await axios.get('http://localhost:8080/api/radius/google-workspace-status');
      console.log('Google Workspace status:', JSON.stringify(googleStatusResponse.data, null, 2));
    } catch (authError) {
      console.log('Google Workspace status test failed (expected - requires authentication):', authError.response?.status);
    }
    
    console.log('\n=====================================');
    console.log('Basic tests completed!');
    
    // Show configuration recommendations
    console.log('\nConfiguration Status:');
    const config = isLocalResponse.data;
    if (config.googleWorkspaceAuth) {
      console.log('- Google Workspace Auth Configured:', config.googleWorkspaceAuth.configured);
      console.log('- Has Service Account:', config.googleWorkspaceAuth.hasServiceAccount);
      console.log('- Has OAuth Config:', config.googleWorkspaceAuth.hasOAuth);
      console.log('- Has Token File:', config.googleWorkspaceAuth.hasTokenFile);
      console.log('- Allowed Domains:', config.googleWorkspaceAuth.allowedDomains);
      
      if (!config.googleWorkspaceAuth.configured) {
        console.log('\n⚠️  Google Workspace authentication is not configured!');
        console.log('   Please set up the required environment variables:');
        console.log('   - GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI');
        console.log('   - OR GOOGLE_ADMIN_SERVICE_ACCOUNT_EMAIL, GOOGLE_ADMIN_SERVICE_ACCOUNT_PRIVATE_KEY');
        console.log('   - ALLOWED_DOMAINS (optional)');
      }
    } else {
      console.log('- Google Workspace authentication not available');
    }
    
  } catch (error) {
    console.error('Error testing RADIUS server:', error.response ? error.response.data : error.message);
  }
}

// Run the test
testRadiusServer();