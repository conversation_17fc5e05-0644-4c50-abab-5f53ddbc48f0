/**
 * Test script to verify that the Google Forms service account integration is working correctly
 * 
 * This script tests the following:
 * 1. Authentication with service account
 * 2. Listing forms with user email filtering
 * 3. Getting form details
 * 
 * Usage: 
 *   node test-google-forms-service-account.js
 * 
 * Environment variables:
 *   TEST_USER_EMAIL - Email of the test user (default: from .env)
 *   TEST_USER_PASSWORD - Password of the test user (default: from .env)
 *   TEST_FORM_ID - ID of a test form in Google Forms (optional)
 *   SERVER_PORT - Port of the local server (default: 6000 or from .env)
 *   DEBUG - Set to 'true' for more detailed logging
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Debug mode
const DEBUG = process.env.DEBUG === 'true';

// Helper function for debug logging
function debugLog(...args) {
  if (DEBUG) {
    console.log('[DEBUG]', ...args);
  }
}

// Print test environment information
console.log('=== Google Forms Service Account Test ===');
console.log(`Date/Time: ${new Date().toISOString()}`);
console.log(`Debug Mode: ${DEBUG ? 'Enabled' : 'Disabled'}`);
console.log('=======================================');

// Set up axios with base URL and credentials
const serverPort = process.env.SERVER_PORT || process.env.PORT || 6000;
const baseURL = `http://localhost:${serverPort}`;
console.log(`Server URL: ${baseURL}`);

const api = axios.create({
  baseURL,
  withCredentials: true
});

// Add request interceptor for debugging
api.interceptors.request.use(request => {
  debugLog('Request:', {
    method: request.method,
    url: request.url,
    headers: request.headers,
    data: request.data
  });
  return request;
});

// Add response interceptor for debugging
api.interceptors.response.use(
  response => {
    debugLog('Response:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    });
    return response;
  },
  error => {
    debugLog('Error Response:', {
      message: error.message,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data
      } : 'No response'
    });
    return Promise.reject(error);
  }
);

// Test user credentials - use environment variables or defaults
const testUser = {
  email: process.env.TEST_USER_EMAIL || '<EMAIL>',
  password: process.env.TEST_USER_PASSWORD || 'password123'
};
console.log(`Test User: ${testUser.email}`);

// Test form ID - use environment variable or will be determined from listing forms
const testFormId = process.env.TEST_FORM_ID || null;
if (testFormId) {
  console.log(`Test Form ID: ${testFormId}`);
} else {
  console.log('Test Form ID: Will be determined from listing forms');
}

// Helper function to format error messages
function formatError(error) {
  if (error.response && error.response.data) {
    const { message, error: errorMsg, debug } = error.response.data;
    return {
      status: error.response.status,
      message: message || 'Unknown error',
      error: errorMsg || error.message,
      debug: debug || 'No debug information available'
    };
  }
  return {
    message: error.message,
    stack: DEBUG ? error.stack : undefined
  };
}

// Helper function to print test results
function printTestResult(testName, success, data = null, error = null) {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`\n[${status}] ${testName}`);
  
  if (success && data) {
    console.log('  Result:', typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
  }
  
  if (!success && error) {
    console.error('  Error:', typeof error === 'object' ? JSON.stringify(error, null, 2) : error);
    if (error.debug) {
      console.error('  Debug:', error.debug);
    }
  }
}

// Login function
async function login() {
  console.log('\n📋 TEST: Authentication');
  try {
    console.log(`Attempting to login as ${testUser.email}...`);
    const response = await api.post('/api/auth/login', testUser);
    printTestResult('Authentication', true, { token: 'Token received (hidden)' });
    return response.data.token;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Authentication', false, null, formattedError);
    throw new Error('Authentication failed - cannot continue tests');
  }
}

// Test listing forms
async function testListForms(token) {
  console.log('\n📋 TEST: List Forms');
  try {
    console.log('Fetching forms from Google Forms...');
    const response = await api.get('/api/google/forms', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const result = {
      count: response.data.forms.length,
      message: response.data.message,
      sample: response.data.forms.slice(0, 3).map(form => ({
        id: form.id,
        name: form.name,
        webViewLink: form.webViewLink
      }))
    };
    
    printTestResult('List Forms', true, result);
    return response.data.forms;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('List Forms', false, null, formattedError);
    throw error;
  }
}

// Test getting a form
async function testGetForm(token, formId) {
  console.log('\n📋 TEST: Get Form');
  try {
    console.log(`Fetching form with ID: ${formId}...`);
    const response = await api.get(`/api/google/forms/${formId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const result = {
      id: response.data.formId,
      title: response.data.info.title,
      description: response.data.info.description
    };
    
    printTestResult('Get Form', true, result);
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Get Form', false, null, formattedError);
    throw error;
  }
}

// Main test function
async function runTests() {
  console.log('\n🚀 Starting Google Forms service account integration tests...');
  console.log('=======================================================');
  
  let token;
  let forms;
  let formId;
  let testResults = {
    total: 0,
    passed: 0,
    failed: 0
  };
  
  try {
    // Step 1: Login
    token = await login();
    testResults.total++;
    testResults.passed++;
    
    // Step 2: List forms
    try {
      forms = await testListForms(token);
      testResults.total++;
      testResults.passed++;
      
      // If we have forms, use the first one for further tests
      if (forms.length > 0) {
        formId = forms[0].id;
        console.log(`\nUsing form "${forms[0].name}" (ID: ${formId}) for remaining tests`);
      } else if (testFormId) {
        formId = testFormId;
        console.log(`\nNo forms found. Using provided test form ID: ${formId}`);
      } else {
        throw new Error('No forms found and no test form ID provided. Cannot continue tests.');
      }
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      throw error;
    }
    
    // Step 3: Get form
    try {
      await testGetForm(token, formId);
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.error('\n⚠️ Get form test failed');
    }
    
    // Print test summary
    console.log('\n=======================================================');
    console.log(`📊 TEST SUMMARY: ${testResults.passed}/${testResults.total} tests passed`);
    if (testResults.failed > 0) {
      console.log(`❌ ${testResults.failed} tests failed`);
    } else {
      console.log('✅ All tests completed successfully!');
    }
    console.log('=======================================================');
  } catch (error) {
    console.error('\n❌ Tests aborted due to critical error:', error.message);
    console.log('\n=======================================================');
    console.log(`📊 TEST SUMMARY: ${testResults.passed}/${testResults.total} tests passed`);
    console.log(`❌ ${testResults.failed} tests failed, and some tests were not run`);
    console.log('=======================================================');
    process.exit(1);
  }
}

// Run the tests
runTests();