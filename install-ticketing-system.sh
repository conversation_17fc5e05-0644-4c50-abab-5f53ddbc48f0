#!/bin/bash

# Gmail Ticketing System Installation Script
# This script installs dependencies and runs the Gmail ticketing setup

echo "🎫 Installing Gmail Ticketing System"
echo "===================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"
echo ""

# Install required dependencies
echo "📦 Installing required dependencies..."
npm install @google-cloud/pubsub googleapis imap mailparser

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo ""

# Make the setup script executable
chmod +x setup-gmail-ticketing.js

# Ask user which setup method to use
echo "🚀 Choose Gmail ticketing setup method:"
echo "1. Simple setup (recommended) - Uses service account only"
echo "2. Advanced setup - Uses OAuth2 flow (more complex)"
echo ""
read -p "Enter choice (1 or 2): " choice

if [ "$choice" = "2" ]; then
    echo "🚀 Starting advanced Gmail ticketing setup..."
    node setup-gmail-ticketing.js
else
    echo "🚀 Starting simple Gmail ticketing setup..."
    node setup-gmail-simple.js
fi

echo ""
echo "📋 Next Steps:"
echo "1. Update your .env file with the app password for SMTP"
echo "2. Set your actual PORTAL_URL in the .env file"
echo "3. Start your application with: npm run dev"
echo "4. Send a test email to verify the ticketing system works"
echo ""
echo "For detailed instructions, see GMAIL_TICKETING_SETUP.md"