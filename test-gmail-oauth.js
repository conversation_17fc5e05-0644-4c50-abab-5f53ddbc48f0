require('dotenv').config();
const emailUtils = require('./server/utils/emailTemplates/emailUtils');

async function testEmailSending() {
  try {
    console.log('Testing Gmail OAuth2 email sending...');
    
    // Send a test email
    const result = await emailUtils.sendTemplatedEmail({
      to: process.env.EMAIL_FROM, // Send to self for testing
      subject: 'Test Email - Gmail OAuth2',
      templateName: 'loginLink', // Using an existing template
      data: {
        name: 'Test User',
        loginLink: 'https://portal.ukcsf.org',
        message: 'This is a test email to verify Gmail OAuth2 configuration is working correctly.'
      }
    });
    
    console.log('Email sent successfully!');
    console.log('Message ID:', result.messageId);
  } catch (error) {
    console.error('Error sending email:', error);
  }
}

testEmailSending();