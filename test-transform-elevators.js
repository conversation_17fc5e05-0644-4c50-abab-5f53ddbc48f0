/**
 * Test script to verify the transformElevatorsData method handles missing fields correctly
 */

// Import the LenelS2NetBoxAPI class
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');

// Create a test instance
const api = new LenelS2NetBoxAPI('test-host', 'test-user', 'test-pass');

// Create a mock XML response with an elevator missing required fields
const mockResponseWithMissingFields = {
  NETBOX: {
    RESPONSE: [
      {
        CODE: ['SUCCESS'],
        DETAILS: [
          {
            ELEVATORS: [
              {
                ELEVATOR: [
                  {
                    // Missing ELEVATORKEY and NAME
                    LOCATION: ['Building A'],
                    STATUS: ['Online'],
                    CURRENTFLOOR: ['2'],
                    TOTALFLOORS: ['10'],
                    FLOORS: [
                      {
                        FLOOR: [
                          {
                            NUMBER: ['1'],
                            NAME: ['Lobby'],
                            ACCESSIBLE: ['true']
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

// Create a mock XML response with an elevator that has all required fields
const mockResponseWithAllFields = {
  NETBOX: {
    RESPONSE: [
      {
        CODE: ['SUCCESS'],
        DETAILS: [
          {
            ELEVATORS: [
              {
                ELEVATOR: [
                  {
                    ELEVATORKEY: ['123'],
                    NAME: ['Main Elevator'],
                    LOCATION: ['Building A'],
                    STATUS: ['Online'],
                    CURRENTFLOOR: ['2'],
                    TOTALFLOORS: ['10'],
                    FLOORS: [
                      {
                        FLOOR: [
                          {
                            NUMBER: ['1'],
                            NAME: ['Lobby'],
                            ACCESSIBLE: ['true']
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

// Test the transformElevatorsData method with missing fields
console.log('Testing transformElevatorsData with missing fields...');
const elevatorsWithMissingFields = api.transformElevatorsData(mockResponseWithMissingFields);
console.log('Result:');
console.log(JSON.stringify(elevatorsWithMissingFields, null, 2));

// Verify that the required fields are present
if (elevatorsWithMissingFields.length > 0) {
  const elevator = elevatorsWithMissingFields[0];
  console.log('\nVerifying required fields:');
  console.log(`KEY/id: ${elevator.key || elevator.id || 'MISSING'}`);
  console.log(`NAME: ${elevator.name || 'MISSING'}`);
  
  if (elevator.key && elevator.name) {
    console.log('\nSUCCESS: All required fields are present');
    console.log(`KEY value: ${elevator.key}`);
    console.log(`NAME value: ${elevator.name}`);
    
    if (elevator.key === '1' && elevator.name === 'Elevator 00') {
      console.log('Default values were correctly applied');
    } else {
      console.log('WARNING: Values are present but not the expected defaults');
    }
  } else {
    console.log('\nFAILURE: Some required fields are still missing');
  }
} else {
  console.log('\nFAILURE: No elevators were returned');
}

// Test the transformElevatorsData method with all fields present
console.log('\n\nTesting transformElevatorsData with all fields present...');
const elevatorsWithAllFields = api.transformElevatorsData(mockResponseWithAllFields);
console.log('Result:');
console.log(JSON.stringify(elevatorsWithAllFields, null, 2));

// Verify that the original fields are preserved
if (elevatorsWithAllFields.length > 0) {
  const elevator = elevatorsWithAllFields[0];
  console.log('\nVerifying original fields are preserved:');
  console.log(`KEY/id: ${elevator.key || elevator.id || 'MISSING'}`);
  console.log(`NAME: ${elevator.name || 'MISSING'}`);
  
  if (elevator.key === '123' && elevator.name === 'Main Elevator') {
    console.log('\nSUCCESS: Original field values are preserved');
  } else {
    console.log('\nFAILURE: Original field values were not preserved');
  }
} else {
  console.log('\nFAILURE: No elevators were returned');
}