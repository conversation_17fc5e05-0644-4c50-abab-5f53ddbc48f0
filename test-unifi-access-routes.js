/**
 * Test script for UniFi Access routes
 * 
 * This script tests both the original and new UniFi Access config routes by:
 * 1. Creating a simple Express server that mounts the UniFi Access routes
 * 2. Making direct requests to the routes
 * 3. Checking the response status and data
 * 
 * Usage:
 * node test-unifi-access-routes.js
 */

const express = require('express');
const unifiAccessController = require('./server/controllers/unifiAccessController');

// Create a mock Express request and response
function createMockReqRes() {
  const req = {};
  const res = {
    status: function(statusCode) {
      this.statusCode = statusCode;
      return this;
    },
    json: function(data) {
      this.data = data;
      return this;
    }
  };
  return { req, res };
}

// Create a test function
async function testUnifiAccessRoutes() {
  console.log('Testing UniFi Access routes...');
  
  // Set environment variables for testing
  process.env.UNIFI_ACCESS_HOST = 'test-host';
  process.env.UNIFI_ACCESS_API_KEY = 'test-api-key';
  process.env.UNIFI_ACCESS_PORT = '443';
  
  // Test the original route (/config)
  console.log('\nTesting original route: /api/unifi-access/config');
  try {
    const { req, res } = createMockReqRes();
    await unifiAccessController.getConfig(req, res);
    
    if (res.statusCode) {
      console.log(`Response status: ${res.statusCode}`);
      console.log('Response data:', res.data);
      console.log('❌ Original route returned an error status code');
    } else {
      console.log('Response data:', res.data);
      console.log('✅ Original route works correctly');
    }
  } catch (error) {
    console.error('❌ Error with original route:', error);
  }
  
  // Test with missing environment variables
  console.log('\nTesting with missing environment variables');
  try {
    // Save original values
    const originalHost = process.env.UNIFI_ACCESS_HOST;
    const originalApiKey = process.env.UNIFI_ACCESS_API_KEY;
    
    // Delete environment variables
    delete process.env.UNIFI_ACCESS_HOST;
    delete process.env.UNIFI_ACCESS_API_KEY;
    
    const { req, res } = createMockReqRes();
    await unifiAccessController.getConfig(req, res);
    
    if (res.statusCode === 404) {
      console.log(`Response status: ${res.statusCode}`);
      console.log('Response data:', res.data);
      console.log('✅ Route correctly returns 404 when environment variables are missing');
    } else {
      console.log(`Response status: ${res.statusCode || 'none'}`);
      console.log('Response data:', res.data);
      console.log('❌ Route did not return expected 404 status code');
    }
    
    // Restore environment variables
    process.env.UNIFI_ACCESS_HOST = originalHost;
    process.env.UNIFI_ACCESS_API_KEY = originalApiKey;
  } catch (error) {
    console.error('❌ Error testing with missing environment variables:', error);
  }
  
  console.log('\nTest completed.');
}

// Run the test
testUnifiAccessRoutes();