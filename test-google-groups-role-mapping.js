/**
 * Test script to verify the Google Groups role mapping functionality
 * 
 * This script tests:
 * 1. The isUserMemberOfGroup method in the GoogleAdminAPI class
 * 2. The role assignment based on Google Groups membership
 * 
 * To run this test:
 * 1. Make sure you have the necessary environment variables set:
 *    - GOOGLE_SERVICE_ACCOUNT_EMAIL
 *    - GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY
 *    - GOOGLE_ADMIN_IMPERSONATION_EMAIL
 * 2. Run: node test-google-groups-role-mapping.js
 */

require('dotenv').config();
const GoogleAdminAPI = require('./server/integrations/googleAdmin/googleAdminAPI');
const RoleSettings = require('./models/RoleSettings');
const mongoose = require('mongoose');

// Connect to MongoDB
async function connectToDatabase() {
  try {
    const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal';
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Test the isUserMemberOfGroup method
async function testIsUserMemberOfGroup() {
  console.log('\n=== Testing isUserMemberOfGroup method ===');
  
  try {
    // Create a new GoogleAdminAPI instance
    const api = new GoogleAdminAPI(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_CALLBACK_URL,
      null, // No token path needed
      null, // No user tokens
      null, // No user ID
      process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL // Impersonation email
    );
    
    // Initialize the API
    console.log('Initializing GoogleAdminAPI...');
    await api.initialize();
    
    // Test user and group
    const testUserEmail = process.env.TEST_USER_EMAIL || process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;
    
    // List all groups
    console.log('Listing all groups...');
    const groups = await api.listGroups();
    console.log(`Found ${groups.length} groups`);
    
    if (groups.length === 0) {
      console.log('No groups found. Skipping membership test.');
      return;
    }
    
    // Test with the first group
    const testGroup = groups[0];
    console.log(`Testing if user ${testUserEmail} is a member of group ${testGroup.email}...`);
    
    const isMember = await api.isUserMemberOfGroup(testUserEmail, testGroup.email);
    console.log(`Result: ${isMember ? 'User is a member' : 'User is not a member'}`);
    
    // Test with a non-existent group
    console.log(`Testing with a non-existent group...`);
    const nonExistentGroup = '<EMAIL>';
    const isMemberNonExistent = await api.isUserMemberOfGroup(testUserEmail, nonExistentGroup);
    console.log(`Result: ${isMemberNonExistent ? 'User is a member' : 'User is not a member'}`);
    
  } catch (error) {
    console.error('Error testing isUserMemberOfGroup method:', error);
  }
}

// Test role assignment based on Google Groups membership
async function testRoleAssignment() {
  console.log('\n=== Testing role assignment based on Google Groups membership ===');
  
  try {
    // Get current role settings
    const settings = await RoleSettings.getCurrentSettings();
    console.log('Current role settings:');
    console.log(`- Default role for local users: ${settings.defaultRoleLocalUsers}`);
    console.log(`- Default role for Google users: ${settings.defaultRoleGoogleUsers}`);
    console.log('- Google Groups role mapping:');
    
    if (settings.googleGroupsRoleMapping && settings.googleGroupsRoleMapping.length > 0) {
      settings.googleGroupsRoleMapping.forEach(mapping => {
        console.log(`  - ${mapping.groupEmail} => ${mapping.roleName}`);
      });
    } else {
      console.log('  No mappings configured');
    }
    
    // Create a new GoogleAdminAPI instance
    const api = new GoogleAdminAPI(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_CALLBACK_URL,
      null, // No token path needed
      null, // No user tokens
      null, // No user ID
      process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL // Impersonation email
    );
    
    // Initialize the API
    console.log('\nInitializing GoogleAdminAPI...');
    await api.initialize();
    
    // Test user
    const testUserEmail = process.env.TEST_USER_EMAIL || process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;
    
    // List all groups
    console.log('Listing all groups...');
    const groups = await api.listGroups();
    console.log(`Found ${groups.length} groups`);
    
    // Convert googleGroupsRoleMapping to the format expected by the code
    let googleGroupsRoleMapping = {};
    if (settings.googleGroupsRoleMapping && settings.googleGroupsRoleMapping.length > 0) {
      settings.googleGroupsRoleMapping.forEach(mapping => {
        googleGroupsRoleMapping[mapping.groupEmail] = mapping.roleName;
      });
    }
    
    // Simulate role assignment
    let roles = [settings.defaultRoleGoogleUsers];
    
    if (Object.keys(googleGroupsRoleMapping).length > 0) {
      console.log('\nChecking group memberships for role assignment...');
      
      // For each group in the mapping
      for (const [groupEmail, roleName] of Object.entries(googleGroupsRoleMapping)) {
        console.log(`Checking if user ${testUserEmail} is a member of group ${groupEmail}...`);
        
        // Check if user is a member of the group
        const isMember = await api.isUserMemberOfGroup(testUserEmail, groupEmail);
        
        if (isMember) {
          console.log(`User is a member of group ${groupEmail}, adding role: ${roleName}`);
          // Add the role if not already in the roles array
          if (!roles.includes(roleName)) {
            roles.push(roleName);
          }
        } else {
          console.log(`User is not a member of group ${groupEmail}`);
        }
      }
    }
    
    console.log('\nFinal assigned roles:');
    console.log(roles);
    
  } catch (error) {
    console.error('Error testing role assignment:', error);
  }
}

// Main function
async function main() {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Run tests
    await testIsUserMemberOfGroup();
    await testRoleAssignment();
    
    console.log('\n=== All tests completed ===');
    
    // Disconnect from the database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the main function
main().catch(console.error);