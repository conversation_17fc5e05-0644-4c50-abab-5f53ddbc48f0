const axios = require('axios');
const dotenv = require('dotenv');
dotenv.config();

// Set up axios defaults
const api = axios.create({
  baseURL: process.env.API_URL || 'http://localhost:5000',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Login function to get authentication token
async function login() {
  try {
    const response = await api.post('/api/auth/login', {
      email: process.env.TEST_USER_EMAIL,
      password: process.env.TEST_USER_PASSWORD
    });
    
    return response.data.token;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    throw error;
  }
}

// Test ticket creation with empty assignedTo, assignedGroup, and null dueDate
async function testTicketCreation() {
  try {
    // Login to get token
    const token = await login();
    api.defaults.headers.common['x-auth-token'] = token;
    
    console.log('Testing ticket creation with problematic values...');
    
    // Create a ticket with empty assignedTo, assignedGroup, and null dueDate
    const ticketData = {
      subject: 'Test Ticket for Validation Fix',
      description: 'This is a test ticket to verify the validation fix',
      priority: 'normal',
      type: 'incident',
      status: 'open',
      assignedTo: '',
      assignedGroup: '',
      dueDate: null
    };
    
    const response = await api.post('/api/tickets', ticketData);
    
    console.log('Ticket created successfully!');
    console.log('Ticket ID:', response.data._id);
    console.log('Validation fix is working correctly.');
    
    return response.data;
  } catch (error) {
    console.error('Test failed:');
    if (error.response && error.response.data) {
      console.error('Error data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error(error.message);
    }
    throw error;
  }
}

// Run the test
testTicketCreation()
  .then(() => {
    console.log('Test completed successfully.');
    process.exit(0);
  })
  .catch(() => {
    console.log('Test failed.');
    process.exit(1);
  });