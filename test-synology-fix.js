// Test script to verify the Synology API fix
require('dotenv').config();
const SynologyAPI = require('./server/integrations/synology/synologyAPI');
const axios = require('axios');

// Create a custom version of SynologyAPI that bypasses the integration tracker
class TestSynologyAPI extends SynologyAPI {
  async initialize() {
    try {
      // Skip the integration tracker checks
      if (!this.host || !this.port || !this.username || !this.password) {
        console.error('Host, port, username, and password are required for Synology integration.');
        return;
      }

      // Check if we can authenticate
      try {
        await this.login();
        console.log('Authentication successful!');
      } catch (authError) {
        console.error('Authentication failed:', authError.message);
        throw authError;
      }
    } catch (error) {
      console.error('Error initializing Synology API:', error);
      throw error;
    }
  }
}

async function testSynologyAPI() {
  try {
    console.log('Testing Synology API with environment variables:');
    console.log(`SYNOLOGY_HOST: ${process.env.SYNOLOGY_HOST}`);
    console.log(`SYNOLOGY_PORT: ${process.env.SYNOLOGY_PORT}`);
    console.log(`SYNOLOGY_USERNAME: ${process.env.SYNOLOGY_USERNAME}`);
    console.log(`SYNOLOGY_SECURE: ${process.env.SYNOLOGY_SECURE}`);
    
    // Extract host without protocol
    let host = process.env.SYNOLOGY_HOST;
    if (host.startsWith('http://') || host.startsWith('https://')) {
      host = host.replace(/^https?:\/\//, '');
    }
    
    // Create a new TestSynologyAPI instance with environment variables
    const synologyAPI = new TestSynologyAPI(
      host,
      parseInt(process.env.SYNOLOGY_PORT, 10),
      process.env.SYNOLOGY_USERNAME,
      process.env.SYNOLOGY_PASSWORD,
      process.env.SYNOLOGY_SECURE !== 'false'
    );
    
    // Log the baseURL to verify it's formed correctly
    console.log(`Generated baseURL: ${synologyAPI.baseURL}`);
    
    // Try direct authentication to debug the issue
    console.log('Attempting direct authentication...');
    try {
      // First, get the API info to determine the correct path and version
      console.log('Getting API info...');
      const apiInfoUrl = `${synologyAPI.baseURL}/entry.cgi?api=SYNO.API.Info&version=1&method=query&query=SYNO.API.Auth`;
      const apiInfoResponse = await axios.get(apiInfoUrl);
      console.log('API info response:', JSON.stringify(apiInfoResponse.data, null, 2));
      
      // Use the path returned by the API info query, but with version 3
      const authApiInfo = apiInfoResponse.data.data['SYNO.API.Auth'];
      const version = 3;
      const path = 'auth.cgi';  // Always use auth.cgi for login
      
      console.log(`Using auth path: ${path}, version: ${version}`);
      
      // Include all required parameters
      const authUrl = `${synologyAPI.baseURL}/${path}?api=SYNO.API.Auth&version=${version}&method=login&account=${process.env.SYNOLOGY_USERNAME}&passwd=${process.env.SYNOLOGY_PASSWORD}&session=FileStation&format=cookie`;
      console.log(`Auth URL: ${authUrl}`);
      
      const response = await axios.get(authUrl);
      console.log('Direct auth response:', JSON.stringify(response.data, null, 2));
    } catch (authError) {
      console.error('Direct auth error:', authError.message);
      if (authError.response) {
        console.error('Response status:', authError.response.status);
        console.error('Response data:', JSON.stringify(authError.response.data, null, 2));
      }
    }
    
    // Initialize the API (this will attempt to authenticate)
    console.log('Initializing API...');
    await synologyAPI.initialize();
    
    // Try to list files in the root directory
    console.log('Listing files in root directory...');
    const files = await synologyAPI.listFiles('/');
    
    console.log('Files retrieved successfully:');
    console.log(JSON.stringify(files, null, 2));
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error testing Synology API:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testSynologyAPI();