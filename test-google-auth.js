/**
 * Test script to verify Google authentication configuration
 * 
 * This script checks if the Google OAuth environment variables are properly set
 * and if the Google authentication strategy would be initialized.
 */

// Load environment variables
require('dotenv').config();

// Check if required environment variables are set
const googleClientId = process.env.GOOGLE_CLIENT_ID;
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET;
const googleCallbackUrl = process.env.GOOGLE_CALLBACK_URL;
const allowedDomains = process.env.ALLOWED_DOMAINS;

console.log('\n=== Google Authentication Configuration Test ===\n');

// Check Google OAuth credentials
console.log('Checking Google OAuth credentials:');
if (googleClientId && googleClientId !== 'your_google_client_id') {
  console.log('✅ GOOGLE_CLIENT_ID is set');
} else {
  console.log('❌ GOOGLE_CLIENT_ID is missing or has placeholder value');
  console.log('   Current value:', googleClientId);
}

if (googleClientSecret && googleClientSecret !== 'your_google_client_secret') {
  console.log('✅ GOOGLE_CLIENT_SECRET is set');
} else {
  console.log('❌ GOOGLE_CLIENT_SECRET is missing or has placeholder value');
  console.log('   Current value:', googleClientSecret);
}

if (googleCallbackUrl && googleCallbackUrl !== 'https://your-app-url.ondigitalocean.app/api/auth/google/callback') {
  console.log('✅ GOOGLE_CALLBACK_URL is set');
  console.log('   Current value:', googleCallbackUrl);
} else {
  console.log('❌ GOOGLE_CALLBACK_URL is missing or has placeholder value');
  console.log('   Current value:', googleCallbackUrl);
}

// Check allowed domains
console.log('\nChecking allowed domains:');
if (allowedDomains) {
  console.log('✅ ALLOWED_DOMAINS is set to:', allowedDomains);
  const domains = allowedDomains.split(',');
  console.log('   Allowed domains:', domains);
} else {
  console.log('❌ ALLOWED_DOMAINS is missing');
}

// Check if Google strategy would be initialized
console.log('\nChecking if Google strategy would be initialized:');
if (
  googleClientId && 
  googleClientId !== 'your_google_client_id' && 
  googleClientSecret && 
  googleClientSecret !== 'your_google_client_secret' && 
  googleCallbackUrl
) {
  console.log('✅ Google strategy would be initialized');
} else {
  console.log('❌ Google strategy would NOT be initialized');
  console.log('   The following conditions must be met:');
  console.log('   1. GOOGLE_CLIENT_ID must be set and not equal to "your_google_client_id"');
  console.log('   2. GOOGLE_CLIENT_SECRET must be set and not equal to "your_google_client_secret"');
  console.log('   3. GOOGLE_CALLBACK_URL must be set');
}

console.log('\n=== Test Complete ===');
console.log('\nTo fix the Google authentication issue:');
console.log('1. Update the .env file with actual Google OAuth credentials');
console.log('2. Ensure GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are set to valid values');
console.log('3. Ensure GOOGLE_CALLBACK_URL is set to https://portal.ukcsf.org/api/auth/google/callback');
console.log('4. Restart the application after making these changes');