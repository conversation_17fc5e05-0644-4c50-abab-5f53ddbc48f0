/**
 * UI Tests for Access Control System
 * 
 * This file contains UI tests for critical workflows in the access control system
 * using Jest and React Testing Library.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { AuthProvider } from '../client/src/context/AuthContext';
import theme from '../client/src/theme';

// Import components to test
import AccessControlPage from '../client/src/pages/AccessControl/AccessControlPage';
import UserManagement from '../client/src/components/AccessControl/UserManagement';
import DoorManagement from '../client/src/components/AccessControl/DoorManagement';
import AccessLevelManagement from '../client/src/components/AccessControl/AccessLevelManagement';
import ScheduleManagement from '../client/src/components/AccessControl/ScheduleManagement';
import PolicyManagement from '../client/src/components/AccessControl/PolicyManagement';

// Mock the services
jest.mock('../client/src/services/accessControlService', () => ({
  getConfigStatus: jest.fn().mockResolvedValue({
    unifiAccess: true,
    lenelS2NetBox: true
  }),
  getAllDoors: jest.fn().mockResolvedValue([
    { id: 'door-1', name: 'Front Door', status: 'locked', system: 'unifi-access' },
    { id: 'door-2', name: 'Back Door', status: 'locked', system: 'lenel-s2-netbox' }
  ]),
  controlDoor: jest.fn().mockResolvedValue({
    success: true,
    message: 'Door unlocked successfully'
  }),
  getAllUsers: jest.fn().mockResolvedValue([
    { id: 'user-1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>', systems: ['unifi-access'] },
    { id: 'user-2', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', systems: ['lenel-s2-netbox'] }
  ]),
  createUser: jest.fn().mockResolvedValue({
    success: true,
    userId: 'user-3',
    message: 'User created successfully'
  }),
  getAllAccessLevels: jest.fn().mockResolvedValue([
    { id: 'level-1', name: 'Admin', system: 'unifi-access' },
    { id: 'level-2', name: 'Employee', system: 'lenel-s2-netbox' }
  ]),
  getAllSchedules: jest.fn().mockResolvedValue([
    { id: 'schedule-1', name: 'Business Hours', system: 'unifi-access' },
    { id: 'schedule-2', name: 'Weekend Hours', system: 'lenel-s2-netbox' }
  ]),
  createSchedule: jest.fn().mockResolvedValue({
    success: true,
    scheduleId: 'schedule-3',
    message: 'Schedule created successfully'
  }),
  getAllPolicies: jest.fn().mockResolvedValue([
    { id: 'policy-1', name: 'Standard Access', system: 'unifi-access' },
    { id: 'policy-2', name: 'Restricted Access', system: 'lenel-s2-netbox' }
  ]),
  createPolicy: jest.fn().mockResolvedValue({
    success: true,
    policyId: 'policy-3',
    message: 'Policy created successfully'
  })
}));

// Mock the websocket service
jest.mock('../client/src/services/websocketService', () => ({
  connect: jest.fn().mockResolvedValue(true),
  subscribe: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  isConnected: jest.fn().mockReturnValue(true)
}));

// Wrapper component for providing context
const TestWrapper = ({ children, initialRoute = '/access-control' }) => (
  <MemoryRouter initialEntries={[initialRoute]}>
    <ThemeProvider theme={theme}>
      <AuthProvider>
        <Routes>
          <Route path="/access-control/*" element={children} />
        </Routes>
      </AuthProvider>
    </ThemeProvider>
  </MemoryRouter>
);

describe('Access Control UI Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AccessControlPage', () => {
    it('should render the access control dashboard', async () => {
      render(
        <TestWrapper>
          <AccessControlPage />
        </TestWrapper>
      );

      // Wait for the page to load
      await waitFor(() => {
        expect(screen.getByText('Access Control Management')).toBeInTheDocument();
      });

      // Check that tabs are rendered
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Users')).toBeInTheDocument();
      expect(screen.getByText('Doors')).toBeInTheDocument();
      expect(screen.getByText('Access Levels')).toBeInTheDocument();
    });

    it('should navigate between tabs', async () => {
      render(
        <TestWrapper>
          <AccessControlPage />
        </TestWrapper>
      );

      // Wait for the page to load
      await waitFor(() => {
        expect(screen.getByText('Access Control Management')).toBeInTheDocument();
      });

      // Click on the Users tab
      fireEvent.click(screen.getByText('Users'));
      
      // Check that the Users tab content is displayed
      await waitFor(() => {
        expect(screen.getByText('User Management')).toBeInTheDocument();
      });

      // Click on the Doors tab
      fireEvent.click(screen.getByText('Doors'));
      
      // Check that the Doors tab content is displayed
      await waitFor(() => {
        expect(screen.getByText('Door Management')).toBeInTheDocument();
      });
    });
  });

  describe('UserManagement', () => {
    it('should render the user list', async () => {
      render(
        <TestWrapper>
          <UserManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the user list to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('should open the create user dialog when clicking the add button', async () => {
      render(
        <TestWrapper>
          <UserManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByText('User Management')).toBeInTheDocument();
      });

      // Click the add user button
      fireEvent.click(screen.getByText('Add User'));
      
      // Check that the create user dialog is displayed
      await waitFor(() => {
        expect(screen.getByText('Create New User')).toBeInTheDocument();
      });
    });

    it('should create a new user', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UserManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByText('User Management')).toBeInTheDocument();
      });

      // Click the add user button
      await user.click(screen.getByText('Add User'));
      
      // Fill out the form
      await waitFor(() => {
        expect(screen.getByText('Create New User')).toBeInTheDocument();
      });
      
      await user.type(screen.getByLabelText(/First Name/i), 'Test');
      await user.type(screen.getByLabelText(/Last Name/i), 'User');
      await user.type(screen.getByLabelText(/Email/i), '<EMAIL>');
      
      // Select systems
      const unifiCheckbox = screen.getByLabelText(/Unifi Access/i);
      await user.click(unifiCheckbox);
      
      // Go to next step
      await user.click(screen.getByText('Next'));
      
      // Select access levels (assuming we're now on the access level step)
      await waitFor(() => {
        expect(screen.getByText('Select Access Levels')).toBeInTheDocument();
      });
      
      // Go to next step
      await user.click(screen.getByText('Next'));
      
      // Review and create
      await waitFor(() => {
        expect(screen.getByText('Review User Details')).toBeInTheDocument();
      });
      
      await user.click(screen.getByText('Create User'));
      
      // Check that the service was called
      await waitFor(() => {
        expect(screen.getByText('User created successfully')).toBeInTheDocument();
      });
    });
  });

  describe('DoorManagement', () => {
    it('should render the door list', async () => {
      render(
        <TestWrapper>
          <DoorManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the door list to load
      await waitFor(() => {
        expect(screen.getByText('Front Door')).toBeInTheDocument();
        expect(screen.getByText('Back Door')).toBeInTheDocument();
      });
    });

    it('should control a door', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <DoorManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the door list to load
      await waitFor(() => {
        expect(screen.getByText('Front Door')).toBeInTheDocument();
      });

      // Find the door card
      const doorCard = screen.getByText('Front Door').closest('.MuiCard-root');
      
      // Click the unlock button
      const unlockButton = within(doorCard).getByLabelText('Unlock');
      await user.click(unlockButton);
      
      // Check that the confirmation dialog is displayed
      await waitFor(() => {
        expect(screen.getByText('Confirm Door Control')).toBeInTheDocument();
      });
      
      // Confirm the action
      await user.click(screen.getByText('Unlock'));
      
      // Check that the service was called
      await waitFor(() => {
        expect(screen.getByText('Door unlocked successfully')).toBeInTheDocument();
      });
    });
  });

  describe('AccessLevelManagement', () => {
    it('should render the access level list', async () => {
      render(
        <TestWrapper>
          <AccessLevelManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the access level list to load
      await waitFor(() => {
        expect(screen.getByText('Admin')).toBeInTheDocument();
        expect(screen.getByText('Employee')).toBeInTheDocument();
      });
    });
  });

  describe('ScheduleManagement', () => {
    it('should render the schedule list', async () => {
      render(
        <TestWrapper>
          <ScheduleManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the schedule list to load
      await waitFor(() => {
        expect(screen.getByText('Business Hours')).toBeInTheDocument();
        expect(screen.getByText('Weekend Hours')).toBeInTheDocument();
      });
    });

    it('should open the create schedule dialog when clicking the add button', async () => {
      render(
        <TestWrapper>
          <ScheduleManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByText('Schedule Management')).toBeInTheDocument();
      });

      // Click the add schedule button
      fireEvent.click(screen.getByText('Add Schedule'));
      
      // Check that the create schedule dialog is displayed
      await waitFor(() => {
        expect(screen.getByText('Create New Schedule')).toBeInTheDocument();
      });
    });
  });

  describe('PolicyManagement', () => {
    it('should render the policy list', async () => {
      render(
        <TestWrapper>
          <PolicyManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the policy list to load
      await waitFor(() => {
        expect(screen.getByText('Standard Access')).toBeInTheDocument();
        expect(screen.getByText('Restricted Access')).toBeInTheDocument();
      });
    });
  });

  describe('Time Zone Management', () => {
    it('should render the time zone manager in schedule creation', async () => {
      render(
        <TestWrapper>
          <ScheduleManagement configStatus={{ unifiAccess: true, lenelS2NetBox: true }} />
        </TestWrapper>
      );

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByText('Schedule Management')).toBeInTheDocument();
      });

      // Click the add schedule button
      fireEvent.click(screen.getByText('Add Schedule'));
      
      // Check that the create schedule dialog is displayed with time zone settings
      await waitFor(() => {
        expect(screen.getByText('Create New Schedule')).toBeInTheDocument();
        expect(screen.getByText('Time Zone Settings')).toBeInTheDocument();
      });
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should trigger actions when keyboard shortcuts are pressed', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AccessControlPage />
        </TestWrapper>
      );

      // Wait for the page to load
      await waitFor(() => {
        expect(screen.getByText('Access Control Management')).toBeInTheDocument();
      });

      // Simulate pressing Ctrl+H to open help dialog
      await user.keyboard('{Control>}h{/Control}');
      
      // Check that the keyboard shortcuts help dialog is displayed
      await waitFor(() => {
        expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument();
      });
    });
  });

  describe('Dashboard Layout Customization', () => {
    it('should allow customizing the dashboard layout', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <AccessControlPage />
        </TestWrapper>
      );

      // Wait for the page to load
      await waitFor(() => {
        expect(screen.getByText('Access Control Management')).toBeInTheDocument();
      });

      // Find and click the dashboard layout settings button
      const settingsButton = screen.getByLabelText('Configure Layout');
      await user.click(settingsButton);
      
      // Check that the layout configuration dialog is displayed
      await waitFor(() => {
        expect(screen.getByText('Configure Dashboard Layout')).toBeInTheDocument();
      });
      
      // Change layout type to list
      const listLayoutOption = screen.getByText('List Layout');
      await user.click(listLayoutOption);
      
      // Apply changes
      await user.click(screen.getByText('Apply'));
      
      // Check that the layout has been updated
      // This would require more specific assertions based on the actual implementation
    });
  });
});

// Export the test suite
export default {};