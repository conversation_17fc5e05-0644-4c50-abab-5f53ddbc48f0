/**
 * Test script to verify that the Mosyle Business config and devices endpoints
 * no longer return 304 errors after adding cache-control headers.
 */
const axios = require('axios');

// Create an axios instance that will follow redirects but won't use browser cache
const client = axios.create({
  baseURL: 'http://localhost:8080',
  maxRedirects: 5,
  headers: {
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  }
});

/**
 * Test the Mosyle Business config endpoint
 */
async function testMosyleConfig() {
  console.log('Testing Mosyle Business config endpoint...');
  
  try {
    // Make first request
    const response1 = await client.get('/api/mosyle-business/config');
    console.log(`First request status: ${response1.status}`);
    
    // Make second request immediately after
    const response2 = await client.get('/api/mosyle-business/config');
    console.log(`Second request status: ${response2.status}`);
    
    // Verify that both requests returned 200 OK
    if (response1.status === 200 && response2.status === 200) {
      console.log('✅ Mosyle Business config endpoint is returning 200 OK for both requests');
    } else {
      console.log('❌ Mosyle Business config endpoint is not returning 200 OK for both requests');
    }
    
    // Check for cache-control headers
    const cacheControl = response2.headers['cache-control'];
    if (cacheControl && cacheControl.includes('no-store') && cacheControl.includes('no-cache')) {
      console.log('✅ Cache-control headers are set correctly');
    } else {
      console.log('❌ Cache-control headers are not set correctly');
      console.log('Cache-Control:', cacheControl);
    }
  } catch (error) {
    console.error('Error testing Mosyle Business config endpoint:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    }
  }
}

/**
 * Test the Mosyle Business devices endpoint
 */
async function testMosyleDevices() {
  console.log('\nTesting Mosyle Business devices endpoint...');
  
  try {
    // Make first request
    const response1 = await client.get('/api/mosyle-business/devices');
    console.log(`First request status: ${response1.status}`);
    
    // Make second request immediately after
    const response2 = await client.get('/api/mosyle-business/devices');
    console.log(`Second request status: ${response2.status}`);
    
    // Verify that both requests returned 200 OK
    if (response1.status === 200 && response2.status === 200) {
      console.log('✅ Mosyle Business devices endpoint is returning 200 OK for both requests');
    } else {
      console.log('❌ Mosyle Business devices endpoint is not returning 200 OK for both requests');
    }
    
    // Check for cache-control headers
    const cacheControl = response2.headers['cache-control'];
    if (cacheControl && cacheControl.includes('no-store') && cacheControl.includes('no-cache')) {
      console.log('✅ Cache-control headers are set correctly');
    } else {
      console.log('❌ Cache-control headers are not set correctly');
      console.log('Cache-Control:', cacheControl);
    }
  } catch (error) {
    console.error('Error testing Mosyle Business devices endpoint:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    }
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('Starting Mosyle Business 304 fix tests...');
  console.log('===========================================');
  
  await testMosyleConfig();
  await testMosyleDevices();
  
  console.log('\nTests completed.');
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});