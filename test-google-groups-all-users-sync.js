/**
 * Test script to verify that teams and groups are being pulled from Google for all users
 * 
 * This script:
 * 1. Authenticates an admin user
 * 2. Calls the sync-all-users endpoint
 * 3. Logs the results of the sync operation
 * 
 * Usage: node test-google-groups-all-users-sync.js
 */

const axios = require('axios');
const dotenv = require('dotenv');
const mongoose = require('mongoose');
const User = require('./models/User');
const Group = require('./models/Group');
const Team = require('./models/Team');
const { getAuthenticatedClient } = require('./server/utils/googleServiceAuth');
const { google } = require('googleapis');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

/**
 * Test the sync-all-users endpoint
 */
async function testSyncAllUsers() {
  try {
    // Get an admin user from the database
    const adminUser = await User.findOne({ 
      roles: { $in: ['admin'] },
      isActive: true 
    });
    
    if (!adminUser) {
      console.error('No active admin users found in the database');
      process.exit(1);
    }
    
    console.log(`Testing with admin user: ${adminUser.name} (${adminUser.email})`);
    
    // Get current stats before sync
    const userCountBefore = await User.countDocuments();
    const groupCountBefore = await Group.countDocuments();
    const teamCountBefore = await Team.countDocuments();
    
    console.log('\nCurrent stats before sync:');
    console.log(`- Users: ${userCountBefore}`);
    console.log(`- Groups: ${groupCountBefore}`);
    console.log(`- Teams: ${teamCountBefore}`);
    
    // Create a server instance and make a request to the endpoint
    const express = require('express');
    const app = express();
    const googleAdminRoutes = require('./routes/api/googleAdmin');
    
    // Mock authentication middleware
    app.use((req, res, next) => {
      req.user = {
        id: adminUser._id,
        email: adminUser.email,
        roles: adminUser.roles
      };
      next();
    });
    
    app.use('/api/google-admin', googleAdminRoutes);
    
    // Start the server
    const server = app.listen(0, async () => {
      const port = server.address().port;
      console.log(`Test server running on port ${port}`);
      
      try {
        // Make a request to the sync-all-users endpoint
        console.log('\nCalling sync-all-users endpoint...');
        const response = await axios.post(`http://localhost:${port}/api/google-admin/sync-all-users`);
        
        console.log('\nSync all users response:');
        console.log(JSON.stringify(response.data, null, 2));
        
        // Get updated stats after sync
        const userCountAfter = await User.countDocuments();
        const groupCountAfter = await Group.countDocuments();
        const teamCountAfter = await Team.countDocuments();
        
        console.log('\nCurrent stats after sync:');
        console.log(`- Users: ${userCountAfter} (${userCountAfter - userCountBefore} new)`);
        console.log(`- Groups: ${groupCountAfter} (${groupCountAfter - groupCountBefore} new)`);
        console.log(`- Teams: ${teamCountAfter} (${teamCountAfter - teamCountBefore} new)`);
        
        // Get a sample of users with their groups and teams
        const sampleUsers = await User.find()
          .limit(5)
          .populate('groups', 'name description')
          .populate('teams', 'name description');
        
        console.log('\nSample users with their groups and teams:');
        for (const user of sampleUsers) {
          console.log(`\nUser: ${user.name} (${user.email})`);
          console.log('Groups:', user.groups.map(g => g.name).join(', ') || 'None');
          console.log('Teams:', user.teams.map(t => t.name).join(', ') || 'None');
        }
        
        console.log('\nTest completed successfully!');
      } catch (error) {
        console.error('Error calling sync-all-users endpoint:', error.response?.data || error.message);
      } finally {
        // Close the server and database connection
        server.close();
        mongoose.disconnect();
        console.log('Test completed');
      }
    });
  } catch (error) {
    console.error('Test error:', error);
    mongoose.disconnect();
  }
}

// Run the test
testSyncAllUsers();