/**
 * Test script for Apple Business Manager MDM servers lookup functionality
 * 
 * This script tests the new API endpoint for fetching MDM servers from Apple Business Manager.
 * It verifies that the backend implementation is working correctly.
 */

require('dotenv').config();
const axios = require('axios');

// Set up authentication
const authenticate = async () => {
  try {
    console.log('Authenticating...');
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      password: process.env.TEST_USER_PASSWORD || 'password123'
    });
    
    const token = response.data.token;
    console.log('Authentication successful');
    
    return token;
  } catch (error) {
    console.error('Authentication failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
};

// Test MDM servers lookup
const testMdmServersLookup = async (token) => {
  try {
    console.log('\nTesting MDM servers lookup...');
    
    const response = await axios.get('http://localhost:5000/api/apple-business-manager/mdm-servers', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('MDM servers lookup successful');
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.error('MDM servers lookup failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
};

// Main function
const main = async () => {
  try {
    // Authenticate
    const token = await authenticate();
    
    // Test MDM servers lookup
    const mdmServers = await testMdmServersLookup(token);
    
    if (mdmServers) {
      console.log('\nTest completed successfully');
    } else {
      console.error('\nTest failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('Test failed:', error.message);
    process.exit(1);
  }
};

// Run the main function
main();