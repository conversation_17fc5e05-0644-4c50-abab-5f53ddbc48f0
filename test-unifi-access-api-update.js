/**
 * Test script for the updated UniFi Access API implementation
 * 
 * This script tests the updated UniFi Access API by:
 * 1. Setting environment variables for UniFi Access including API key
 * 2. Initializing the UniFi Access API
 * 3. Testing various API endpoints to ensure they work correctly
 * 
 * Usage:
 * node test-unifi-access-api-update.js
 */

// Set environment variables for testing
// Replace these with your actual UniFi Access credentials
process.env.UNIFI_ACCESS_HOST = process.env.UNIFI_ACCESS_HOST || 'your-unifi-access-host';
process.env.UNIFI_ACCESS_API_KEY = process.env.UNIFI_ACCESS_API_KEY || 'your-unifi-access-api-key';
process.env.UNIFI_ACCESS_PORT = process.env.UNIFI_ACCESS_PORT || '443';

// Import the UniFi Access API
const UnifiAccessAPI = require('./server/integrations/unifiAccess/unifiAccessAPI');

// Create a test function
async function testUnifiAccessAPI() {
  console.log('Testing updated UniFi Access API implementation...');
  console.log('Environment variables:');
  console.log(`  UNIFI_ACCESS_HOST: ${process.env.UNIFI_ACCESS_HOST}`);
  console.log(`  UNIFI_ACCESS_PORT: ${process.env.UNIFI_ACCESS_PORT}`);
  console.log(`  UNIFI_ACCESS_API_KEY: [hidden]`);
  
  try {
    // Initialize the API
    const unifiAccessAPI = new UnifiAccessAPI();
    
    // Check if the API was initialized with the correct values
    console.log('\nAPI initialized with:');
    console.log(`  Host: ${unifiAccessAPI.host}`);
    console.log(`  Port: ${unifiAccessAPI.port}`);
    console.log(`  Base URL: ${unifiAccessAPI.baseURL}`);
    console.log(`  API Key: [hidden]`);
    
    // Test authentication
    console.log('\nTesting authentication...');
    try {
      const isAuthenticated = await unifiAccessAPI.authenticate();
      if (isAuthenticated) {
        console.log('✅ Authentication successful');
      } else {
        console.log('❌ Authentication failed');
      }
    } catch (error) {
      console.error('❌ Authentication error:', error.message);
    }
    
    // Test getting doors
    console.log('\nTesting getDoors()...');
    try {
      const doors = await unifiAccessAPI.getDoors();
      console.log(`✅ Successfully retrieved ${doors.length || 0} doors`);
      if (doors && doors.length > 0) {
        console.log('First door:', doors[0]);
      }
    } catch (error) {
      console.error('❌ Error getting doors:', error.message);
    }
    
    // Test getting devices (access points)
    console.log('\nTesting getAccessPoints()...');
    try {
      const devices = await unifiAccessAPI.getAccessPoints();
      console.log(`✅ Successfully retrieved ${devices.length || 0} devices`);
      if (devices && devices.length > 0) {
        console.log('First device:', devices[0]);
      }
    } catch (error) {
      console.error('❌ Error getting devices:', error.message);
    }
    
    // Test getting users
    console.log('\nTesting getUsers()...');
    try {
      const users = await unifiAccessAPI.getUsers();
      console.log(`✅ Successfully retrieved ${users.length || 0} users`);
      if (users && users.length > 0) {
        console.log('First user:', users[0]);
      }
    } catch (error) {
      console.error('❌ Error getting users:', error.message);
    }
    
    // Test getting events
    console.log('\nTesting getEvents()...');
    try {
      const events = await unifiAccessAPI.getEvents();
      console.log(`✅ Successfully retrieved ${events.length || 0} events`);
      if (events && events.length > 0) {
        console.log('First event:', events[0]);
      }
    } catch (error) {
      console.error('❌ Error getting events:', error.message);
    }
    
    // Test getting system status
    console.log('\nTesting getSystemStatus()...');
    try {
      const status = await unifiAccessAPI.getSystemStatus();
      console.log('✅ Successfully retrieved system status');
      console.log('System status:', status);
    } catch (error) {
      console.error('❌ Error getting system status:', error.message);
    }
    
    console.log('\nTest completed successfully.');
  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  }
}

// Run the test
testUnifiAccessAPI().then(() => {
  console.log('Test script completed');
  // Exit after a short delay to allow any pending operations to complete
  setTimeout(() => process.exit(0), 1000);
}).catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});