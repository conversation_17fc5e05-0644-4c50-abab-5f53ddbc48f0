#!/usr/bin/env node

const { google } = require('googleapis');
const { PubSub } = require('@google-cloud/pubsub');
const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');
const crypto = require('crypto');
const { execSync } = require('child_process');

/**
 * Gmail Ticketing Setup Script
 * Automatically configures Gmail API, Pub/Sub, and service accounts for ticket email integration
 */
class GmailTicketingSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.config = {
      projectId: null,
      serviceAccountEmail: null,
      serviceAccountKeyPath: null,
      topicName: 'gmail-tickets',
      subscriptionName: 'gmail-tickets-sub',
      monitoredEmail: null,
      webhookSecret: crypto.randomBytes(32).toString('hex')
    };
    
    this.credentials = null;
    this.oauth2Client = null;
  }

  /**
   * Main setup process
   */
  async run() {
    try {
      console.log('\n🎫 Gmail Ticketing System Setup');
      console.log('=====================================\n');
      
      await this.checkPrerequisites();
      await this.authenticate();
      await this.setupProject();
      await this.enableAPIs();
      await this.createServiceAccount();
      await this.setupPubSub();
      await this.configureGmailWatch();
      await this.updateEnvironmentFile();
      await this.testConfiguration();
      
      console.log('\n✅ Setup completed successfully!');
      console.log('\nYour Gmail ticketing system is now ready to use.');
      console.log('Send a test email to your monitored address to verify it works.\n');
      
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      console.log('\nPlease check the error above and run the script again.\n');
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  /**
   * Check prerequisites
   */
  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...\n');
    
    // Check if gcloud CLI is installed
    try {
      execSync('gcloud --version', { stdio: 'pipe' });
      console.log('✅ Google Cloud CLI found');
    } catch (error) {
      console.log('❌ Google Cloud CLI not found');
      console.log('\nPlease install Google Cloud CLI:');
      console.log('https://cloud.google.com/sdk/docs/install\n');
      throw new Error('Google Cloud CLI required');
    }

    // Check if user is authenticated with gcloud
    try {
      const account = execSync('gcloud auth list --filter=status:ACTIVE --format="value(account)"', { 
        encoding: 'utf8' 
      }).trim();
      
      if (!account) {
        console.log('❌ Not authenticated with Google Cloud');
        console.log('\nPlease run: gcloud auth login');
        throw new Error('Google Cloud authentication required');
      }
      
      console.log(`✅ Authenticated as: ${account}`);
    } catch (error) {
      console.log('❌ Google Cloud authentication check failed');
      console.log('\nPlease run: gcloud auth login');
      throw new Error('Google Cloud authentication required');
    }

    // Check Node.js version
    const nodeVersion = process.version;
    console.log(`✅ Node.js version: ${nodeVersion}`);
    
    console.log('✅ All prerequisites met\n');
  }

  /**
   * Authenticate with Google APIs
   */
  async authenticate() {
    console.log('🔐 Setting up Google API authentication...\n');
    
    // Get or create OAuth2 credentials for the application
    const credentialsPath = path.join(__dirname, 'gmail-setup-credentials.json');
    
    try {
      const credentialsContent = await fs.readFile(credentialsPath, 'utf8');
      this.credentials = JSON.parse(credentialsContent);
      
      // Validate credentials structure
      if (!this.credentials.installed && !this.credentials.web) {
        throw new Error('Invalid credentials file format');
      }
      
      console.log('✅ Using existing OAuth2 credentials');
    } catch (error) {
      console.log('❌ OAuth2 credentials not found or invalid');
      console.log('\n📋 To create OAuth2 credentials:');
      console.log('1. Go to: https://console.cloud.google.com/apis/credentials');
      console.log('2. Click "Create Credentials" → "OAuth 2.0 Client IDs"');
      console.log('3. Application type: Choose "Desktop application"');
      console.log('4. Name: Enter "Gmail Ticketing Setup"');
      console.log('5. Click "Create"');
      console.log('6. Click "Download JSON" button');
      console.log(`7. Save the downloaded file as: ${path.basename(credentialsPath)}`);
      console.log(`   in this directory: ${path.dirname(credentialsPath)}`);
      console.log('\n⚠️  Important: The file should contain "installed" or "web" client configuration');
      
      await this.question('Press Enter after you\'ve saved the credentials file...');
      
      try {
        const credentialsContent = await fs.readFile(credentialsPath, 'utf8');
        this.credentials = JSON.parse(credentialsContent);
        
        // Validate credentials structure again
        if (!this.credentials.installed && !this.credentials.web) {
          console.log('\n❌ The credentials file format is incorrect.');
          console.log('Expected structure: {"installed": {"client_id": "...", "client_secret": "...", ...}}');
          console.log('or: {"web": {"client_id": "...", "client_secret": "...", ...}}');
          console.log('\nMake sure you downloaded the OAuth2 client credentials, not service account credentials.');
          throw new Error('Invalid credentials file format');
        }
        
        console.log('✅ OAuth2 credentials loaded and validated');
      } catch (retryError) {
        if (retryError.code === 'ENOENT') {
          throw new Error(`Credentials file not found: ${credentialsPath}`);
        } else if (retryError instanceof SyntaxError) {
          throw new Error('Credentials file is not valid JSON');
        } else {
          throw new Error(`Failed to load OAuth2 credentials: ${retryError.message}`);
        }
      }
    }

    // Set up OAuth2 client
    const { client_secret, client_id, redirect_uris } = this.credentials.installed || this.credentials.web;
    this.oauth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);

    // Get access token
    const tokenPath = path.join(__dirname, 'gmail-setup-token.json');
    
    try {
      const token = await fs.readFile(tokenPath, 'utf8');
      this.oauth2Client.setCredentials(JSON.parse(token));
      console.log('✅ Using existing access token');
    } catch (error) {
      console.log('🔑 Generating new access token...');
      
      const authUrl = this.oauth2Client.generateAuthUrl({
        access_type: 'offline',
        scope: [
          'https://www.googleapis.com/auth/gmail.readonly',
          'https://www.googleapis.com/auth/cloud-platform'
        ],
      });

      console.log('\n📋 Please visit this URL to authorize the application:');
      console.log(authUrl);
      
      const code = await this.question('\nEnter the authorization code: ');
      
      if (!code || code.trim().length === 0) {
        throw new Error('Authorization code is required');
      }
      
      console.log('🔄 Exchanging authorization code for tokens...');
      
      try {
        const tokenResponse = await this.oauth2Client.getToken(code.trim());
        
        if (!tokenResponse || !tokenResponse.tokens) {
          throw new Error('Failed to get tokens from authorization code');
        }
        
        const { tokens } = tokenResponse;
        this.oauth2Client.setCredentials(tokens);
        
        await fs.writeFile(tokenPath, JSON.stringify(tokens, null, 2));
        console.log('✅ Access token saved');
      } catch (tokenError) {
        console.error('❌ Token exchange failed:', tokenError.message);
        console.log('\nTroubleshooting steps:');
        console.log('1. Make sure you copied the entire authorization code');
        console.log('2. The authorization code should be used immediately (it expires quickly)');
        console.log('3. Make sure you\'re using the correct OAuth2 credentials file');
        console.log('4. Try generating a new authorization code if this one has expired');
        
        const retry = await this.question('\nWould you like to try with a new authorization code? (y/n): ');
        if (retry.toLowerCase() === 'y') {
          console.log('\n📋 Please visit this URL again to get a new authorization code:');
          console.log(authUrl);
          const newCode = await this.question('\nEnter the new authorization code: ');
          
          const retryResponse = await this.oauth2Client.getToken(newCode.trim());
          if (!retryResponse || !retryResponse.tokens) {
            throw new Error('Failed to get tokens on retry');
          }
          
          const { tokens } = retryResponse;
          this.oauth2Client.setCredentials(tokens);
          
          await fs.writeFile(tokenPath, JSON.stringify(tokens, null, 2));
          console.log('✅ Access token saved on retry');
        } else {
          throw tokenError;
        }
      }
    }
    
    console.log('✅ Google API authentication completed\n');
  }

  /**
   * Set up Google Cloud project
   */
  async setupProject() {
    console.log('🏗️  Setting up Google Cloud project...\n');
    
    // Get current project
    try {
      const currentProject = execSync('gcloud config get-value project', { 
        encoding: 'utf8' 
      }).trim();
      
      if (currentProject && currentProject !== '(unset)') {
        const useExisting = await this.question(
          `Use existing project "${currentProject}"? (y/n): `
        );
        
        if (useExisting.toLowerCase() === 'y') {
          this.config.projectId = currentProject;
        }
      }
    } catch (error) {
      // No current project set
    }

    if (!this.config.projectId) {
      this.config.projectId = await this.question('Enter Google Cloud Project ID: ');
      
      // Set the project
      try {
        execSync(`gcloud config set project ${this.config.projectId}`, { stdio: 'pipe' });
        console.log(`✅ Project set to: ${this.config.projectId}`);
      } catch (error) {
        throw new Error(`Failed to set project: ${error.message}`);
      }
    }
    
    console.log(`✅ Using project: ${this.config.projectId}\n`);
  }

  /**
   * Enable required APIs
   */
  async enableAPIs() {
    console.log('🔌 Enabling required APIs...\n');
    
    const apis = [
      'gmail.googleapis.com',
      'pubsub.googleapis.com',
      'iam.googleapis.com',
      'cloudresourcemanager.googleapis.com'
    ];

    for (const api of apis) {
      try {
        console.log(`Enabling ${api}...`);
        execSync(`gcloud services enable ${api}`, { stdio: 'pipe' });
        console.log(`✅ ${api} enabled`);
      } catch (error) {
        console.log(`⚠️  ${api} may already be enabled`);
      }
    }
    
    console.log('✅ All APIs enabled\n');
  }

  /**
   * Create service account
   */
  async createServiceAccount() {
    console.log('👤 Creating service account...\n');
    
    const serviceAccountName = 'gmail-ticketing-sa';
    const serviceAccountDisplayName = 'Gmail Ticketing Service Account';
    this.config.serviceAccountEmail = `${serviceAccountName}@${this.config.projectId}.iam.gserviceaccount.com`;
    
    try {
      // Create service account
      console.log('Creating service account...');
      execSync(`gcloud iam service-accounts create ${serviceAccountName} \
        --display-name="${serviceAccountDisplayName}" \
        --description="Service account for Gmail ticketing system"`, { stdio: 'pipe' });
      console.log('✅ Service account created');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('✅ Service account already exists');
      } else {
        throw new Error(`Failed to create service account: ${error.message}`);
      }
    }

    // Grant necessary roles
    const roles = [
      'roles/pubsub.admin',
      'roles/gmail.readonly'
    ];

    for (const role of roles) {
      try {
        console.log(`Granting role ${role}...`);
        execSync(`gcloud projects add-iam-policy-binding ${this.config.projectId} \
          --member="serviceAccount:${this.config.serviceAccountEmail}" \
          --role="${role}"`, { stdio: 'pipe' });
        console.log(`✅ Role ${role} granted`);
      } catch (error) {
        console.log(`⚠️  Role ${role} may already be granted`);
      }
    }

    // Create and download service account key
    const keyFileName = `gmail-ticketing-service-account-key.json`;
    this.config.serviceAccountKeyPath = path.join(__dirname, keyFileName);
    
    try {
      console.log('Creating service account key...');
      execSync(`gcloud iam service-accounts keys create "${this.config.serviceAccountKeyPath}" \
        --iam-account="${this.config.serviceAccountEmail}"`, { stdio: 'pipe' });
      console.log(`✅ Service account key saved to: ${keyFileName}`);
    } catch (error) {
      // Key might already exist, that's okay
      console.log('✅ Service account key already exists or created');
    }
    
    console.log('✅ Service account setup completed\n');
  }

  /**
   * Set up Pub/Sub topic and subscription
   */
  async setupPubSub() {
    console.log('📡 Setting up Pub/Sub...\n');
    
    try {
      // Create topic
      console.log(`Creating topic: ${this.config.topicName}`);
      execSync(`gcloud pubsub topics create ${this.config.topicName}`, { stdio: 'pipe' });
      console.log(`✅ Topic "${this.config.topicName}" created`);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log(`✅ Topic "${this.config.topicName}" already exists`);
      } else {
        throw new Error(`Failed to create topic: ${error.message}`);
      }
    }

    try {
      // Create subscription
      console.log(`Creating subscription: ${this.config.subscriptionName}`);
      execSync(`gcloud pubsub subscriptions create ${this.config.subscriptionName} \
        --topic=${this.config.topicName}`, { stdio: 'pipe' });
      console.log(`✅ Subscription "${this.config.subscriptionName}" created`);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log(`✅ Subscription "${this.config.subscriptionName}" already exists`);
      } else {
        throw new Error(`Failed to create subscription: ${error.message}`);
      }
    }

    // Grant Gmail service permission to publish to the topic
    const gmailServiceAccount = '<EMAIL>';
    
    try {
      console.log('Granting Gmail API permissions...');
      execSync(`gcloud pubsub topics add-iam-policy-binding ${this.config.topicName} \
        --member="serviceAccount:${gmailServiceAccount}" \
        --role="roles/pubsub.publisher"`, { stdio: 'pipe' });
      console.log('✅ Gmail API permissions granted');
    } catch (error) {
      console.log('⚠️  Gmail API permissions may already be granted');
    }
    
    console.log('✅ Pub/Sub setup completed\n');
  }

  /**
   * Configure Gmail watch
   */
  async configureGmailWatch() {
    console.log('👁️  Configuring Gmail watch...\n');
    
    this.config.monitoredEmail = await this.question(
      'Enter the email address to monitor for tickets (e.g., <EMAIL>): '
    );

    // Note: The actual Gmail watch will be set up when the application starts
    // This is because it requires domain-wide delegation for service accounts
    console.log('📋 Gmail watch configuration notes:');
    console.log('1. The Gmail watch will be automatically configured when your application starts');
    console.log('2. Make sure the monitored email address has Gmail API access');
    console.log('3. For Google Workspace, you may need to enable domain-wide delegation\n');
    
    console.log('✅ Gmail watch configuration prepared\n');
  }

  /**
   * Update environment file
   */
  async updateEnvironmentFile() {
    console.log('📝 Updating environment configuration...\n');
    
    const envPath = path.join(__dirname, '.env');
    const envExamplePath = path.join(__dirname, '.env.example');
    
    // Read existing .env file
    let existingEnv = '';
    try {
      existingEnv = await fs.readFile(envPath, 'utf8');
    } catch (error) {
      // .env doesn't exist, that's okay
    }

    // Gmail ticketing configuration
    const gmailConfig = `
# Gmail Ticketing System Configuration (Auto-generated)
GOOGLE_GMAIL_PROJECT_ID=${this.config.projectId}
GOOGLE_GMAIL_TOPIC_NAME=${this.config.topicName}
GOOGLE_GMAIL_SUBSCRIPTION_NAME=${this.config.subscriptionName}
GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY=${this.config.serviceAccountKeyPath}
GMAIL_MONITORED_EMAIL=${this.config.monitoredEmail}

# SMTP Configuration for outgoing emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=${this.config.monitoredEmail}
SMTP_PASS=your-app-password-here

# Ticket system configuration
TICKET_EMAIL_FROM=${this.config.monitoredEmail}
PORTAL_URL=https://yourportal.com
WEBHOOK_SECRET=${this.config.webhookSecret}
`;

    // Remove existing Gmail configuration if present
    const cleanedEnv = existingEnv.replace(
      /# Gmail Ticketing System Configuration[\s\S]*?(?=\n#|\n[A-Z]|$)/g, 
      ''
    ).trim();

    // Combine configurations
    const newEnvContent = cleanedEnv + gmailConfig;
    
    // Write updated .env file
    await fs.writeFile(envPath, newEnvContent);
    console.log('✅ Environment file updated');

    // Create backup example file
    await fs.writeFile(envExamplePath, newEnvContent.replace(/=.+$/gm, '='));
    console.log('✅ Environment example file created');

    console.log('\n📋 Important: Update these environment variables:');
    console.log(`   SMTP_PASS: Create an app password for ${this.config.monitoredEmail}`);
    console.log('   PORTAL_URL: Set to your actual portal URL');
    console.log('\n✅ Environment configuration completed\n');
  }

  /**
   * Test configuration
   */
  async testConfiguration() {
    console.log('🧪 Testing configuration...\n');
    
    try {
      // Test service account key
      const keyContent = await fs.readFile(this.config.serviceAccountKeyPath, 'utf8');
      const keyData = JSON.parse(keyContent);
      console.log(`✅ Service account key valid for: ${keyData.client_email}`);
      
      // Test Pub/Sub access
      const pubsub = new PubSub({
        projectId: this.config.projectId,
        keyFilename: this.config.serviceAccountKeyPath
      });
      
      const [topics] = await pubsub.getTopics();
      const topicExists = topics.some(topic => topic.name.includes(this.config.topicName));
      
      if (topicExists) {
        console.log('✅ Pub/Sub topic accessible');
      } else {
        console.log('⚠️  Pub/Sub topic not found in accessible topics');
      }
      
      console.log('✅ Configuration test completed\n');
      
    } catch (error) {
      console.log(`⚠️  Configuration test failed: ${error.message}`);
      console.log('The setup has completed, but there may be permission issues.\n');
    }
  }

  /**
   * Utility function for user input
   */
  question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  const setup = new GmailTicketingSetup();
  setup.run().catch(console.error);
}

module.exports = GmailTicketingSetup;