const axios = require('axios');

/**
 * <PERSON><PERSON><PERSON> to remove the menu item with path "/admin/integrations"
 */
async function removeAdminIntegrationsMenuItem() {
  try {
    // Use the base URL of your API server
    const API_BASE_URL = process.env.API_URL || 'http://localhost:3001';
    
    console.log('Fetching all menu items...');
    const response = await axios.get(`${API_BASE_URL}/api/menu-items`);
    const menuItems = response.data;
    
    console.log(`Found ${menuItems.length} menu items.`);
    
    // Find the menu item with path "/admin/integrations"
    const adminIntegrationsMenuItem = menuItems.find(item => item.path === '/admin/integrations');
    
    if (!adminIntegrationsMenuItem) {
      console.log('No menu item with path "/admin/integrations" found. Nothing to remove.');
      return;
    }
    
    console.log(`Found menu item with path "/admin/integrations":`, adminIntegrationsMenuItem);
    
    // Delete the menu item
    console.log(`Deleting menu item with ID ${adminIntegrationsMenuItem._id}...`);
    await axios.delete(`${API_BASE_URL}/api/menu-items/${adminIntegrationsMenuItem._id}`);
    
    console.log('Menu item deleted successfully.');
  } catch (error) {
    console.error('Error removing menu item:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
  }
}

// Execute the function
removeAdminIntegrationsMenuItem();