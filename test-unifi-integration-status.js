/**
 * Test script to verify UniFi integration status fixes
 * 
 * This script tests:
 * 1. The integration status endpoint
 * 2. The initialization of UniFi Access API
 * 3. The initialization of UniFi Protect API
 * 4. The initialization of UniFi Network API
 */

// Load environment variables
require('dotenv').config();

// Import required modules
const axios = require('axios');
const mongoose = require('mongoose');
const UnifiAccessAPI = require('./server/integrations/unifiAccess/unifiAccessAPI');
const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');
const unifiProtectController = require('./server/controllers/unifiProtectController');
const integrationTracker = require('./server/utils/integrationTracker');

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Test UniFi Access API initialization
async function testUnifiAccessInitialization() {
  console.log('\n--- Testing UniFi Access API Initialization ---');
  
  try {
    // Check if environment variables are set
    if (process.env.UNIFI_ACCESS_HOST && process.env.UNIFI_ACCESS_USERNAME && process.env.UNIFI_ACCESS_PASSWORD) {
      console.log('UniFi Access environment variables are set:');
      console.log(`  Host: ${process.env.UNIFI_ACCESS_HOST}`);
      console.log(`  Username: ${process.env.UNIFI_ACCESS_USERNAME}`);
      console.log(`  Port: ${process.env.UNIFI_ACCESS_PORT || 443}`);
      
      // Initialize UniFi Access API
      const unifiAccessAPI = new UnifiAccessAPI();
      await unifiAccessAPI.initialize();
      
      // Check integration status
      const status = integrationTracker.getStatus('UniFi Access');
      console.log('UniFi Access integration status:', status);
      
      if (status && status.status === 'active') {
        console.log('✅ UniFi Access API initialized successfully');
        return true;
      } else {
        console.log('❌ UniFi Access API initialization failed');
        return false;
      }
    } else {
      console.log('❌ UniFi Access environment variables are not set');
      return false;
    }
  } catch (error) {
    console.error('Error testing UniFi Access API initialization:', error);
    return false;
  }
}

// Test UniFi Protect API initialization
async function testUnifiProtectInitialization() {
  console.log('\n--- Testing UniFi Protect API Initialization ---');
  
  try {
    // Get the latest configuration from the database
    const config = await unifiProtectController.getLatestConfig();
    
    if (config) {
      console.log('UniFi Protect configuration found:');
      console.log(`  Host: ${config.host}`);
      console.log(`  Username: ${config.username}`);
      console.log(`  Port: ${config.port || 443}`);
      
      // Initialize UniFi Protect API
      const unifiProtectAPI = new UnifiProtectAPI(config.host, config.username, config.password, config.port);
      await unifiProtectAPI.initialize();
      
      // Check integration status
      const status = integrationTracker.getStatus('UniFi Protect');
      console.log('UniFi Protect integration status:', status);
      
      if (status && status.status === 'active') {
        console.log('✅ UniFi Protect API initialized successfully');
        return true;
      } else {
        console.log('❌ UniFi Protect API initialization failed');
        return false;
      }
    } else {
      console.log('❌ UniFi Protect configuration not found');
      return false;
    }
  } catch (error) {
    console.error('Error testing UniFi Protect API initialization:', error);
    return false;
  }
}

// Test UniFi Network API initialization
async function testUnifiNetworkInitialization() {
  console.log('\n--- Testing UniFi Network API Initialization ---');
  
  try {
    // Check if environment variables are set
    if (process.env.UNIFI_NETWORK_HOST && process.env.UNIFI_NETWORK_API_KEY) {
      console.log('UniFi Network environment variables are set:');
      console.log(`  Host: ${process.env.UNIFI_NETWORK_HOST}`);
      console.log(`  API Key: [hidden]`);
      console.log(`  Port: ${process.env.UNIFI_NETWORK_PORT || 443}`);
      console.log(`  Site: ${process.env.UNIFI_NETWORK_SITE || 'default'}`);
      
      // Check integration status
      const status = integrationTracker.getStatus('UniFi Network');
      console.log('UniFi Network integration status:', status);
      
      if (status && status.status === 'active') {
        console.log('✅ UniFi Network API initialized successfully');
        return true;
      } else {
        console.log('❌ UniFi Network API initialization failed');
        return false;
      }
    } else {
      console.log('❌ UniFi Network environment variables are not set');
      return false;
    }
  } catch (error) {
    console.error('Error testing UniFi Network API initialization:', error);
    return false;
  }
}

// Test integration status endpoint
async function testIntegrationStatusEndpoint() {
  console.log('\n--- Testing Integration Status Endpoint ---');
  
  try {
    // Make a request to the integration status endpoint
    const response = await axios.get('http://localhost:6000/api/integration-status', {
      headers: {
        Cookie: 'connect.sid=YOUR_SESSION_COOKIE' // Replace with a valid session cookie
      }
    });
    
    console.log('Integration status endpoint response:', response.data);
    
    // Check if UniFi integrations are included in the response
    const hasUnifiAccess = response.data.hasOwnProperty('UniFi Access');
    const hasUnifiProtect = response.data.hasOwnProperty('UniFi Protect');
    const hasUnifiNetwork = response.data.hasOwnProperty('UniFi Network');
    
    console.log(`UniFi Access included: ${hasUnifiAccess ? '✅' : '❌'}`);
    console.log(`UniFi Protect included: ${hasUnifiProtect ? '✅' : '❌'}`);
    console.log(`UniFi Network included: ${hasUnifiNetwork ? '✅' : '❌'}`);
    
    return hasUnifiAccess && hasUnifiProtect && hasUnifiNetwork;
  } catch (error) {
    console.error('Error testing integration status endpoint:', error);
    console.log('Note: This test requires the server to be running and a valid session cookie.');
    return false;
  }
}

// Main function
async function main() {
  console.log('=== UniFi Integration Status Test ===');
  
  try {
    // Connect to MongoDB
    await connectToMongoDB();
    
    // Run tests
    const accessResult = await testUnifiAccessInitialization();
    const protectResult = await testUnifiProtectInitialization();
    const networkResult = await testUnifiNetworkInitialization();
    const endpointResult = await testIntegrationStatusEndpoint();
    
    // Print summary
    console.log('\n=== Test Summary ===');
    console.log(`UniFi Access API Initialization: ${accessResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`UniFi Protect API Initialization: ${protectResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`UniFi Network API Initialization: ${networkResult ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Integration Status Endpoint: ${endpointResult ? '✅ PASS' : '❌ FAIL'}`);
    
    // Overall result
    const overallResult = accessResult && protectResult && networkResult && endpointResult;
    console.log(`\nOverall Result: ${overallResult ? '✅ PASS' : '❌ FAIL'}`);
    
    // Cleanup
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
    
    process.exit(overallResult ? 0 : 1);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the main function
main();