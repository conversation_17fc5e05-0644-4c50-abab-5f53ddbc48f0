/**
 * Test script to reinitialize menu items and verify that the Forms menu item is included
 */
const mongoose = require('mongoose');
const MenuItem = require('./models/MenuItem');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/csfportal')
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // First, check if the Forms menu item already exists
      const existingFormsItem = await MenuItem.findOne({ path: '/forms' });
      
      if (existingFormsItem) {
        console.log('Forms menu item already exists:', existingFormsItem);
      } else {
        console.log('Forms menu item does not exist yet');
      }
      
      // Reinitialize default menu items
      console.log('Reinitializing default menu items...');
      await MenuItem.createDefaultMenuItems();
      
      // Check if the Forms menu item exists after reinitialization
      const formsItem = await MenuItem.findOne({ path: '/forms' });
      
      if (formsItem) {
        console.log('Forms menu item created successfully:', formsItem);
      } else {
        console.error('Failed to create Forms menu item');
      }
      
      // List all menu items to verify
      const allItems = await MenuItem.find({}).sort({ type: 1, order: 1 });
      console.log(`Total menu items: ${allItems.length}`);
      
      // Group items by type for better readability
      const regularItems = allItems.filter(item => item.type === 'regular');
      const integrationItems = allItems.filter(item => item.type === 'integration');
      const adminItems = allItems.filter(item => item.type === 'admin');
      
      console.log('\nRegular menu items:');
      regularItems.forEach(item => {
        console.log(`- ${item.title} (${item.path})`);
      });
      
      console.log('\nIntegration menu items:');
      integrationItems.forEach(item => {
        console.log(`- ${item.title} (${item.path})`);
      });
      
      console.log('\nAdmin menu items:');
      adminItems.forEach(item => {
        console.log(`- ${item.title} (${item.path})`);
      });
      
    } catch (error) {
      console.error('Error:', error);
    } finally {
      // Close the MongoDB connection
      await mongoose.connection.close();
      console.log('Disconnected from MongoDB');
    }
  })
  .catch(err => {
    console.error('Failed to connect to MongoDB:', err);
    process.exit(1);
  });