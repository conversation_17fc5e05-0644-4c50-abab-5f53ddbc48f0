/**
 * Test script for Canva OAuth integration
 * 
 * This script tests the Canva OAuth integration by:
 * 1. Testing the auth URL generation
 * 2. Testing the API functionality with mock tokens
 * 
 * Note: The actual OAuth flow requires user interaction and cannot be fully automated.
 * This script provides a way to verify that the backend implementation is working correctly.
 */

require('dotenv').config();
const axios = require('axios');
const mongoose = require('mongoose');
const User = require('./models/User');
const CanvaAPI = require('./server/integrations/canva/canvaAPI');

// Mock tokens for testing
const mockTokens = {
  accessToken: 'mock_access_token',
  refreshToken: 'mock_refresh_token',
  expiry: new Date(Date.now() + 3600 * 1000) // 1 hour from now
};

// Test user ID - replace with a valid user ID from your database
const TEST_USER_ID = '60a1b2c3d4e5f6g7h8i9j0k1';

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Test auth URL generation
async function testAuthUrlGeneration() {
  try {
    console.log('\n--- Testing Auth URL Generation ---');
    
    // Check if OAuth credentials are configured
    if (!process.env.CANVA_CLIENT_ID || !process.env.CANVA_CLIENT_SECRET || !process.env.CANVA_REDIRECT_URI) {
      console.error('Canva OAuth credentials not configured. Please set the required environment variables.');
      return false;
    }
    
    // Create a new CanvaAPI instance
    const canvaAPI = new CanvaAPI(
      process.env.CANVA_DOMAIN || 'canva.com',
      '',
      process.env.CANVA_CLIENT_ID,
      process.env.CANVA_CLIENT_SECRET,
      process.env.CANVA_REDIRECT_URI
    );
    
    // Generate auth URL
    const authUrl = canvaAPI.getAuthUrl();
    
    // Verify the auth URL
    if (!authUrl) {
      console.error('Failed to generate auth URL');
      return false;
    }
    
    if (!authUrl.includes(process.env.CANVA_CLIENT_ID)) {
      console.error('Auth URL does not contain client ID');
      return false;
    }
    
    if (!authUrl.includes(encodeURIComponent(process.env.CANVA_REDIRECT_URI))) {
      console.error('Auth URL does not contain redirect URI');
      return false;
    }
    
    console.log('Auth URL generated successfully:', authUrl);
    return true;
  } catch (error) {
    console.error('Error testing auth URL generation:', error);
    return false;
  }
}

// Test API functionality with mock tokens
async function testApiWithMockTokens() {
  try {
    console.log('\n--- Testing API with Mock Tokens ---');
    
    // Create a new CanvaAPI instance with mock tokens
    const canvaAPI = new CanvaAPI(
      process.env.CANVA_DOMAIN || 'canva.com',
      '',
      process.env.CANVA_CLIENT_ID,
      process.env.CANVA_CLIENT_SECRET,
      process.env.CANVA_REDIRECT_URI,
      mockTokens,
      'mock_user_id'
    );
    
    // Mock the axios instance to return test data
    canvaAPI.axios = {
      get: async (url, options) => {
        console.log(`Mock API call to ${url}`);
        
        // Return mock data based on the endpoint
        if (url === '/designs') {
          return {
            data: [
              {
                id: 'design1',
                title: 'Test Design 1',
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                thumbnail: 'https://example.com/thumbnail1.jpg'
              },
              {
                id: 'design2',
                title: 'Test Design 2',
                created: new Date().toISOString(),
                modified: new Date().toISOString(),
                thumbnail: 'https://example.com/thumbnail2.jpg'
              }
            ]
          };
        } else if (url === '/templates') {
          return {
            data: [
              {
                id: 'template1',
                title: 'Test Template 1',
                category: 'Marketing',
                thumbnail: 'https://example.com/template1.jpg'
              },
              {
                id: 'template2',
                title: 'Test Template 2',
                category: 'Social Media',
                thumbnail: 'https://example.com/template2.jpg'
              }
            ]
          };
        } else if (url === '/users') {
          return {
            data: [
              {
                id: 'user1',
                name: 'Test User 1',
                email: '<EMAIL>'
              },
              {
                id: 'user2',
                name: 'Test User 2',
                email: '<EMAIL>'
              }
            ]
          };
        } else {
          return { data: {} };
        }
      }
    };
    
    // Test getDesigns
    console.log('Testing getDesigns...');
    const designs = await canvaAPI.getDesigns();
    if (!designs || !Array.isArray(designs) || designs.length === 0) {
      console.error('Failed to get designs');
      return false;
    }
    console.log(`Successfully retrieved ${designs.length} designs`);
    
    // Test getTemplates
    console.log('Testing getTemplates...');
    const templates = await canvaAPI.getTemplates();
    if (!templates || !Array.isArray(templates) || templates.length === 0) {
      console.error('Failed to get templates');
      return false;
    }
    console.log(`Successfully retrieved ${templates.length} templates`);
    
    // Test getUsers
    console.log('Testing getUsers...');
    const users = await canvaAPI.getUsers();
    if (!users || !Array.isArray(users) || users.length === 0) {
      console.error('Failed to get users');
      return false;
    }
    console.log(`Successfully retrieved ${users.length} users`);
    
    console.log('All API tests passed with mock tokens');
    return true;
  } catch (error) {
    console.error('Error testing API with mock tokens:', error);
    return false;
  }
}

// Test updating user with mock tokens
async function testUserTokenUpdate() {
  try {
    console.log('\n--- Testing User Token Update ---');
    
    // Find the test user
    const user = await User.findById(TEST_USER_ID);
    if (!user) {
      console.error(`User with ID ${TEST_USER_ID} not found. Please update the TEST_USER_ID in the script.`);
      return false;
    }
    
    // Save the original tokens
    const originalAccessToken = user.canvaAccessToken;
    const originalRefreshToken = user.canvaRefreshToken;
    const originalTokenExpiry = user.canvaTokenExpiry;
    
    // Update the user with mock tokens
    user.canvaAccessToken = mockTokens.accessToken;
    user.canvaRefreshToken = mockTokens.refreshToken;
    user.canvaTokenExpiry = mockTokens.expiry;
    
    // Save the user
    await user.save();
    console.log('User updated with mock tokens');
    
    // Verify the tokens were saved
    const updatedUser = await User.findById(TEST_USER_ID);
    if (
      updatedUser.canvaAccessToken !== mockTokens.accessToken ||
      updatedUser.canvaRefreshToken !== mockTokens.refreshToken
    ) {
      console.error('Failed to update user with mock tokens');
      
      // Restore the original tokens
      user.canvaAccessToken = originalAccessToken;
      user.canvaRefreshToken = originalRefreshToken;
      user.canvaTokenExpiry = originalTokenExpiry;
      await user.save();
      
      return false;
    }
    
    console.log('User token update verified');
    
    // Restore the original tokens
    user.canvaAccessToken = originalAccessToken;
    user.canvaRefreshToken = originalRefreshToken;
    user.canvaTokenExpiry = originalTokenExpiry;
    await user.save();
    console.log('Original tokens restored');
    
    return true;
  } catch (error) {
    console.error('Error testing user token update:', error);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Run the tests
    const authUrlTest = await testAuthUrlGeneration();
    const apiTest = await testApiWithMockTokens();
    
    // Only run the user token update test if the database is connected
    let userTest = false;
    if (mongoose.connection.readyState === 1) {
      userTest = await testUserTokenUpdate();
    } else {
      console.log('Skipping user token update test - not connected to database');
    }
    
    // Print the results
    console.log('\n--- Test Results ---');
    console.log(`Auth URL Generation: ${authUrlTest ? 'PASSED' : 'FAILED'}`);
    console.log(`API with Mock Tokens: ${apiTest ? 'PASSED' : 'FAILED'}`);
    console.log(`User Token Update: ${userTest ? 'PASSED' : 'SKIPPED'}`);
    
    // Disconnect from the database
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    // Exit with appropriate code
    process.exit(authUrlTest && apiTest ? 0 : 1);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run the main function
main();