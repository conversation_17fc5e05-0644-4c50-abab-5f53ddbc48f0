#!/usr/bin/env node

/**
 * Extract the exact Client ID from the service account for domain-wide delegation
 */

require('dotenv').config();
const { google } = require('googleapis');
const jwt = require('jsonwebtoken');

async function extractClientId() {
  console.log('🔑 Extracting Service Account Client ID for Domain-Wide Delegation\n');

  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;

  if (!serviceAccountEmail || !serviceAccountPrivateKey) {
    console.error('❌ Missing service account credentials');
    process.exit(1);
  }

  try {
    // Method 1: Extract from JWT token
    console.log('📋 Method 1: JWT Token Analysis');
    console.log('-'.repeat(40));
    
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    });

    // Get an access token and analyze it
    const tokenResponse = await auth.getAccessToken();
    console.log(`✅ Access token obtained: ${tokenResponse.token.substring(0, 30)}...`);

    // Try to get service account info using IAM API
    console.log('\n📋 Method 2: IAM API Query');
    console.log('-'.repeat(40));
    
    try {
      const iam = google.iam({ version: 'v1', auth });
      const projectId = serviceAccountEmail.split('@')[1].split('.')[0];
      const resourceName = `projects/${projectId}/serviceAccounts/${serviceAccountEmail}`;
      
      console.log(`Querying: ${resourceName}`);
      
      const serviceAccount = await iam.projects.serviceAccounts.get({
        name: resourceName
      });
      
      if (serviceAccount.data.uniqueId) {
        console.log(`✅ Found Client ID: ${serviceAccount.data.uniqueId}`);
        console.log(`📧 Email: ${serviceAccount.data.email}`);
        console.log(`📛 Display Name: ${serviceAccount.data.displayName || 'N/A'}`);
        console.log(`🔢 Unique ID (Client ID): ${serviceAccount.data.uniqueId}`);
        
        console.log('\n🎯 USE THIS CLIENT ID FOR DOMAIN-WIDE DELEGATION:');
        console.log(`   ${serviceAccount.data.uniqueId}`);
        
      } else {
        console.log('⚠️  Could not extract Client ID from service account info');
      }
      
    } catch (iamError) {
      console.log(`❌ IAM API failed: ${iamError.message}`);
      console.log('This might be due to missing IAM permissions, but that\'s okay.');
    }

    // Method 3: Manual extraction guide
    console.log('\n📋 Method 3: Manual Extraction Guide');
    console.log('-'.repeat(40));
    console.log('If the above methods didn\'t work, manually get the Client ID:');
    console.log('1. Go to https://console.cloud.google.com/');
    console.log('2. Navigate to "IAM & Admin" → "Service Accounts"');
    console.log(`3. Find service account: ${serviceAccountEmail}`);
    console.log('4. Click on the service account name');
    console.log('5. Look for "Unique ID" - this is your Client ID');
    console.log('6. It should be a long numeric string (21 digits)');

    // Method 4: Check current delegation status
    console.log('\n📋 Method 4: Current Delegation Status Check');  
    console.log('-'.repeat(40));
    console.log('To verify current domain-wide delegation:');
    console.log('1. Go to https://admin.google.com/');
    console.log('2. Security → Access and data control → API controls');
    console.log('3. Click "Manage Domain Wide Delegation"');
    console.log('4. Look for an entry with your Client ID');
    console.log('5. Verify the scopes match exactly what we need');

    console.log('\n🔍 Verification Steps:');
    console.log('1. Ensure the Client ID in delegation matches the service account');
    console.log('2. <NAME_EMAIL> exists in Google Workspace');
    console.log('3. Check that all required APIs are enabled in the project');
    console.log('4. Confirm the delegation was saved properly');

    // Check what APIs are enabled
    console.log('\n📋 Method 5: Check API Enablement');
    console.log('-'.repeat(40));
    
    try {
      const serviceUsage = google.serviceusage({ version: 'v1', auth });
      const projectId = serviceAccountEmail.split('@')[1].split('.')[0];
      
      const requiredAPIs = [
        'drive.googleapis.com',
        'admin.googleapis.com', 
        'calendar-json.googleapis.com',
        'forms.googleapis.com'
      ];
      
      console.log('Checking if required APIs are enabled...');
      for (const api of requiredAPIs) {
        try {
          const service = await serviceUsage.services.get({
            name: `projects/${projectId}/services/${api}`
          });
          const state = service.data.state;
          console.log(`   ${api}: ${state === 'ENABLED' ? '✅ Enabled' : '❌ Disabled'}`);
        } catch (apiError) {
          console.log(`   ${api}: ❓ Unknown (${apiError.message})`);
        }
      }
      
    } catch (serviceUsageError) {
      console.log(`Could not check API status: ${serviceUsageError.message}`);
    }

  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
  }
}

extractClientId();