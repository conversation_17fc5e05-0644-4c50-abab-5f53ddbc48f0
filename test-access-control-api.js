/**
 * Unit tests for Access Control API endpoints
 * 
 * This file contains tests for the unified access control API endpoints
 * that combine Unifi Access and Lenel S2 NetBox systems.
 */

const axios = require('axios');
const chai = require('chai');
const expect = chai.expect;
const sinon = require('sinon');
const MockAdapter = require('axios-mock-adapter');

// Import the controllers to test
const accessControlController = require('./server/controllers/accessControlController');

// Create a mock for axios
const mock = new MockAdapter(axios);

describe('Access Control API Tests', () => {
  // Setup and teardown
  beforeEach(() => {
    // Reset axios mock
    mock.reset();
  });

  afterEach(() => {
    // Restore all sinon mocks
    sinon.restore();
  });

  describe('Configuration Status Endpoint', () => {
    it('should return configuration status for all systems', async () => {
      // Mock the request object
      const req = {};
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getConfigStatus').resolves({
        unifiAccess: true,
        lenelS2NetBox: true
      });

      // Call the controller method
      await accessControlController.getConfigStatus(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal({
        unifiAccess: true,
        lenelS2NetBox: true
      });
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {};
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'getConfigStatus').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.getConfigStatus(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });

  describe('Doors Endpoint', () => {
    it('should return doors from all systems', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample door data
      const mockDoors = [
        { id: 'unifi-1', name: 'Front Door', system: 'unifi-access' },
        { id: 'lenel-1', name: 'Back Door', system: 'lenel-s2-netbox' }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllDoors').resolves(mockDoors);

      // Call the controller method
      await accessControlController.getAllDoors(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockDoors);
    });

    it('should filter doors by system when query parameter is provided', async () => {
      // Mock the request object with system filter
      const req = {
        query: { system: 'unifi-access' }
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample door data
      const mockDoors = [
        { id: 'unifi-1', name: 'Front Door', system: 'unifi-access' }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllDoors').resolves(mockDoors);

      // Call the controller method
      await accessControlController.getAllDoors(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockDoors);
      expect(accessControlController.getAllDoors.calledWith(req.query)).to.be.true;
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'getAllDoors').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.getAllDoors(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });

  describe('Door Control Endpoint', () => {
    it('should control a door and return success message', async () => {
      // Mock the request object
      const req = {
        body: {
          doorId: 'door-1',
          system: 'unifi-access',
          action: 'unlock'
        }
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'controlDoor').resolves({
        success: true,
        message: 'Door unlocked successfully'
      });

      // Call the controller method
      await accessControlController.controlDoor(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal({
        success: true,
        message: 'Door unlocked successfully'
      });
    });

    it('should validate required parameters', async () => {
      // Mock the request object with missing parameters
      const req = {
        body: {
          doorId: 'door-1',
          // Missing system and action
        }
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Call the controller method
      await accessControlController.controlDoor(req, res);

      // Verify the response
      expect(res.status.calledWith(400)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {
        body: {
          doorId: 'door-1',
          system: 'unifi-access',
          action: 'unlock'
        }
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'controlDoor').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.controlDoor(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });

  describe('Users Endpoint', () => {
    it('should return users from all systems', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample user data
      const mockUsers = [
        { id: 'user-1', name: 'John Doe', email: '<EMAIL>', systems: ['unifi-access'] },
        { id: 'user-2', name: 'Jane Smith', email: '<EMAIL>', systems: ['lenel-s2-netbox'] }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllUsers').resolves(mockUsers);

      // Call the controller method
      await accessControlController.getAllUsers(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockUsers);
    });

    it('should filter users by system when query parameter is provided', async () => {
      // Mock the request object with system filter
      const req = {
        query: { system: 'unifi-access' }
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample user data
      const mockUsers = [
        { id: 'user-1', name: 'John Doe', email: '<EMAIL>', systems: ['unifi-access'] }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllUsers').resolves(mockUsers);

      // Call the controller method
      await accessControlController.getAllUsers(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockUsers);
      expect(accessControlController.getAllUsers.calledWith(req.query)).to.be.true;
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'getAllUsers').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.getAllUsers(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });

  describe('Access Levels Endpoint', () => {
    it('should return access levels from all systems', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample access level data
      const mockAccessLevels = [
        { id: 'level-1', name: 'Admin', system: 'unifi-access' },
        { id: 'level-2', name: 'Employee', system: 'lenel-s2-netbox' }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllAccessLevels').resolves(mockAccessLevels);

      // Call the controller method
      await accessControlController.getAllAccessLevels(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockAccessLevels);
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'getAllAccessLevels').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.getAllAccessLevels(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });

  describe('Schedules Endpoint', () => {
    it('should return schedules from all systems', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample schedule data
      const mockSchedules = [
        { id: 'schedule-1', name: 'Business Hours', system: 'unifi-access' },
        { id: 'schedule-2', name: 'Weekend Hours', system: 'lenel-s2-netbox' }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllSchedules').resolves(mockSchedules);

      // Call the controller method
      await accessControlController.getAllSchedules(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockSchedules);
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'getAllSchedules').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.getAllSchedules(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });

  describe('Holidays Endpoint', () => {
    it('should return holidays from all systems', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Sample holiday data
      const mockHolidays = [
        { id: 'holiday-1', name: 'Christmas', date: '2025-12-25', system: 'unifi-access' },
        { id: 'holiday-2', name: 'New Year', date: '2026-01-01', system: 'lenel-s2-netbox' }
      ];

      // Mock the controller's dependencies
      sinon.stub(accessControlController, 'getAllHolidays').resolves(mockHolidays);

      // Call the controller method
      await accessControlController.getAllHolidays(req, res);

      // Verify the response
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.deep.equal(mockHolidays);
    });

    it('should handle errors and return 500 status', async () => {
      // Mock the request object
      const req = {
        query: {}
      };
      
      // Mock the response object
      const res = {
        json: sinon.spy(),
        status: sinon.stub().returnsThis()
      };

      // Mock the controller's dependencies to throw an error
      sinon.stub(accessControlController, 'getAllHolidays').rejects(new Error('Test error'));

      // Call the controller method
      await accessControlController.getAllHolidays(req, res);

      // Verify the response
      expect(res.status.calledWith(500)).to.be.true;
      expect(res.json.calledOnce).to.be.true;
      expect(res.json.firstCall.args[0]).to.have.property('error');
    });
  });
});

// Run the tests
if (require.main === module) {
  describe('Running Access Control API Tests', () => {
    // Add any setup code here
    
    // Run the tests
    require('mocha').run();
  });
}