/**
 * Test script to verify that the Spotify play links work correctly
 * 
 * This script tests the following functionality:
 * 1. Playing a Spotify playlist
 * 2. Playing a Spotify track
 * 3. Playing a Spotify album
 * 4. Playing a Spotify artist
 */

const axios = require('axios');

// Sample IDs for testing
// Replace these with actual IDs if needed
const PLAYLIST_ID = '37i9dQZF1DXcBWIGoYBM5M'; // Today's Top Hits
const TRACK_ID = '4cOdK2wGLETKBW3PvgPWqT';    // Random example
const ALBUM_ID = '0JGOiO34nwfUdDrD612dOp';    // Random example
const ARTIST_ID = '06HL4z0CvFAxyc27GXpf02';   // Taylor Swift

// Base URL for API calls
const BASE_URL = 'http://localhost:3000/api/wiim';

// Test playing a Spotify playlist
async function testPlaySpotifyPlaylist() {
  try {
    console.log(`Testing play Spotify playlist: ${PLAYLIST_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/playlists/${PLAYLIST_ID}/play`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify playlist:', error.response?.data || error.message);
    return false;
  }
}

// Test playing a Spotify track
async function testPlaySpotifyTrack() {
  try {
    console.log(`Testing play Spotify track: ${TRACK_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/tracks/${TRACK_ID}/play`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify track:', error.response?.data || error.message);
    return false;
  }
}

// Test playing a Spotify album
async function testPlaySpotifyAlbum() {
  try {
    console.log(`Testing play Spotify album: ${ALBUM_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/albums/${ALBUM_ID}/play`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify album:', error.response?.data || error.message);
    return false;
  }
}

// Test playing a Spotify artist
async function testPlaySpotifyArtist() {
  try {
    console.log(`Testing play Spotify artist: ${ARTIST_ID}`);
    const response = await axios.post(`${BASE_URL}/spotify/artists/${ARTIST_ID}/play`);
    console.log('Success:', response.data);
    return true;
  } catch (error) {
    console.error('Error playing Spotify artist:', error.response?.data || error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting Spotify play links tests...');
  
  // Test playing a Spotify playlist
  const playlistResult = await testPlaySpotifyPlaylist();
  
  // Test playing a Spotify track
  const trackResult = await testPlaySpotifyTrack();
  
  // Test playing a Spotify album
  const albumResult = await testPlaySpotifyAlbum();
  
  // Test playing a Spotify artist
  const artistResult = await testPlaySpotifyArtist();
  
  // Summary
  console.log('\nTest Results:');
  console.log(`Play Spotify Playlist: ${playlistResult ? 'PASS' : 'FAIL'}`);
  console.log(`Play Spotify Track: ${trackResult ? 'PASS' : 'FAIL'}`);
  console.log(`Play Spotify Album: ${albumResult ? 'PASS' : 'FAIL'}`);
  console.log(`Play Spotify Artist: ${artistResult ? 'PASS' : 'FAIL'}`);
  
  const allPassed = playlistResult && trackResult && albumResult && artistResult;
  console.log(`\nOverall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
});