// Test script for Spotify search
const WiimAPI = require('./server/integrations/wiim/wiimAPI');
require('dotenv').config();

// Create a WiimAPI instance with dummy values
const wiimAPI = new WiimAPI('dummy-host', 80);

// Test function to search Spotify
async function testSpotifySearch() {
  try {
    console.log('Testing Spotify search...');
    
    // Test with a simple query
    const results = await wiimAPI.searchSpotify('test query');
    console.log('Search successful!');
    
    // Check if the results have the expected structure
    if (results.tracks && results.tracks.items) {
      console.log(`Found ${results.tracks.items.length} tracks`);
      
      // Check if we can access the images property without errors
      const firstTrack = results.tracks.items[0];
      if (firstTrack && firstTrack.album && firstTrack.album.images) {
        console.log('Track album images property is accessible');
      }
    }
    
    if (results.albums && results.albums.items) {
      console.log(`Found ${results.albums.items.length} albums`);
      
      // Check if we can access the images property without errors
      const firstAlbum = results.albums.items[0];
      if (firstAlbum && firstAlbum.images) {
        console.log('Album images property is accessible');
      }
    }
    
    if (results.artists && results.artists.items) {
      console.log(`Found ${results.artists.items.length} artists`);
      
      // Check if we can access the images property without errors
      const firstArtist = results.artists.items[0];
      if (firstArtist && firstArtist.images) {
        console.log('Artist images property is accessible');
      }
    }
    
    if (results.playlists && results.playlists.items) {
      console.log(`Found ${results.playlists.items.length} playlists`);
      
      // Check if we can access the images property without errors
      const firstPlaylist = results.playlists.items[0];
      if (firstPlaylist && firstPlaylist.images) {
        console.log('Playlist images property is accessible');
      }
    }
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Error during test:', error);
  }
}

// Run the test
testSpotifySearch();