/**
 * Test script to verify the Phone Book menu item
 * 
 * This script tests that the Phone Book menu item has been added correctly
 * and points to the correct path.
 */

const axios = require('axios');
const mongoose = require('mongoose');
const MenuItem = require('./models/MenuItem'); // Import the MenuItem model
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
  useNewUrlParser: true,
  useUnifiedTopology: true
}).then(() => {
  console.log('MongoDB connected');
  runTests();
}).catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

/**
 * Run all tests
 */
async function runTests() {
  try {
    console.log('\n=== Testing Phone Book Menu Item ===\n');
    
    // Test direct database query
    await testDatabaseQuery();
    
    // Test API endpoint
    await testApiEndpoint();
    
    console.log('\n=== All tests completed successfully ===\n');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
    process.exit(0);
  }
}

/**
 * Test that the Phone Book menu item exists in the database
 */
async function testDatabaseQuery() {
  console.log('\n--- Testing database query ---\n');
  
  // Use the imported MenuItem model directly
  
  // Initialize default menu items first
  console.log('Initializing default menu items...');
  await MenuItem.createDefaultMenuItems();
  console.log('Default menu items initialized');
  
  // Find the Phone Book menu item
  const phoneBookMenuItem = await MenuItem.findOne({ path: '/phone-book' });
  
  if (!phoneBookMenuItem) {
    throw new Error('Phone Book menu item not found in database');
  }
  
  console.log('Phone Book menu item found in database:');
  console.log(`- Title: ${phoneBookMenuItem.title}`);
  console.log(`- Path: ${phoneBookMenuItem.path}`);
  console.log(`- Icon: ${phoneBookMenuItem.icon}`);
  console.log(`- Categories: ${phoneBookMenuItem.categories.join(', ')}`);
  console.log(`- Required Permission: ${phoneBookMenuItem.requiredPermission}`);
  console.log(`- Type: ${phoneBookMenuItem.type}`);
  
  // Verify properties
  if (phoneBookMenuItem.title !== 'Phone Book') {
    throw new Error(`Incorrect title: ${phoneBookMenuItem.title}`);
  }
  
  if (phoneBookMenuItem.path !== '/phone-book') {
    throw new Error(`Incorrect path: ${phoneBookMenuItem.path}`);
  }
  
  if (phoneBookMenuItem.icon !== 'contact_phone') {
    throw new Error(`Incorrect icon: ${phoneBookMenuItem.icon}`);
  }
  
  if (!phoneBookMenuItem.categories.includes('Building Management')) {
    throw new Error(`Incorrect categories: ${phoneBookMenuItem.categories.join(', ')}`);
  }
  
  if (phoneBookMenuItem.requiredPermission !== 'contacts:read') {
    throw new Error(`Incorrect required permission: ${phoneBookMenuItem.requiredPermission}`);
  }
  
  if (phoneBookMenuItem.type !== 'regular') {
    throw new Error(`Incorrect type: ${phoneBookMenuItem.type}`);
  }
  
  console.log('All properties verified successfully');
}

/**
 * Test that the Phone Book menu item is returned by the API
 */
async function testApiEndpoint() {
  console.log('\n--- Testing API endpoint ---\n');
  
  try {
    // Get all menu items
    const response = await axios.get('http://localhost:6000/api/menu-items');
    const menuItems = response.data;
    
    // Find the Phone Book menu item
    const phoneBookMenuItem = menuItems.find(item => item.path === '/phone-book');
    
    if (!phoneBookMenuItem) {
      throw new Error('Phone Book menu item not found in API response');
    }
    
    console.log('Phone Book menu item found in API response:');
    console.log(`- Title: ${phoneBookMenuItem.title}`);
    console.log(`- Path: ${phoneBookMenuItem.path}`);
    console.log(`- Icon: ${phoneBookMenuItem.icon}`);
    console.log(`- Categories: ${phoneBookMenuItem.categories.join(', ')}`);
    
    console.log('API endpoint test successful');
  } catch (error) {
    console.error('API endpoint test failed:', error.message);
    console.log('Note: This test requires the server to be running. If the server is not running, this test will fail.');
  }
}