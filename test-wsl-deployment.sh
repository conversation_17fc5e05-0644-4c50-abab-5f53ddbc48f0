#!/usr/bin/env bash

# Test script for WSL deployment
# This script tests the deployment environment without making actual changes

# Set error handling
set -e  # Exit immediately if a command exits with a non-zero status

echo "=== CSF Portal WSL Deployment Test ==="
echo "This script will test your environment for WSL deployment compatibility."
echo "No actual deployment will be performed."
echo ""

# Check if bash is available
if [ -z "$BASH_VERSION" ]; then
    echo "❌ Error: This script requires bash to run."
    echo "   Please ensure bash is installed and try again."
    exit 1
else
    echo "✅ Bash is available: $BASH_VERSION"
fi

# Detect if running from Windows PowerShell
IS_POWERSHELL=false
if [ -n "$ComSpec" ] || [ -n "$POWERSHELL_DISTRIBUTION_CHANNEL" ]; then
    IS_POWERSHELL=true
    echo "✅ Detected Windows PowerShell environment"
else
    echo "ℹ️ Not running from PowerShell"
fi

# Check for WSL-specific environment
if [ -f /proc/version ] && grep -qi microsoft /proc/version; then
    # Running in WSL
    IS_WSL=true
    echo "✅ Detected Windows Subsystem for Linux (WSL) environment"
    
    # Check WSL version
    if grep -qi "wsl2" /proc/version; then
        echo "✅ WSL 2 detected (recommended for Docker integration)"
    else
        echo "⚠️ WSL 1 detected. WSL 2 is recommended for Docker integration"
    fi
else
    IS_WSL=false
    echo "ℹ️ Not running in WSL environment"
fi

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    echo "   Please install Docker and ensure it's in your PATH before deployment"
    if [ "$IS_WSL" = true ]; then
        echo ""
        echo "   For WSL environments:"
        echo "   1. Install Docker Desktop for Windows with WSL 2 integration"
        echo "   2. Enable the WSL 2 integration for your Linux distribution in Docker Desktop settings"
        echo "   3. Restart Docker Desktop and WSL"
        echo ""
    fi
    HAS_DOCKER=false
else
    echo "✅ Docker is installed: $(docker --version)"
    HAS_DOCKER=true
fi

# Check if Docker is running
if [ "$HAS_DOCKER" = true ]; then
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running"
        echo "   Please start Docker before deployment"
        if [ "$IS_WSL" = true ]; then
            echo ""
            echo "   For WSL environments:"
            echo "   1. Start Docker Desktop for Windows"
            echo "   2. Ensure WSL 2 integration is enabled for your distribution"
            echo "   3. If issues persist, try restarting your computer"
            echo ""
        fi
        DOCKER_RUNNING=false
    else
        echo "✅ Docker daemon is running"
        DOCKER_RUNNING=true
    fi
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose is not installed or not in PATH"
    echo "   Please install docker-compose and ensure it's in your PATH before deployment"
    if [ "$IS_WSL" = true ]; then
        echo ""
        echo "   For WSL environments:"
        echo "   Docker Compose should be included with Docker Desktop with WSL 2 integration"
        echo "   If it's missing, please reinstall Docker Desktop and enable WSL 2 integration"
        echo ""
    fi
    HAS_DOCKER_COMPOSE=false
else
    echo "✅ docker-compose is installed: $(docker-compose --version)"
    HAS_DOCKER_COMPOSE=true
fi

# Check if git is available
if ! command -v git &> /dev/null; then
    echo "❌ git is not installed or not in PATH"
    echo "   Please install git and ensure it's in your PATH before deployment"
    HAS_GIT=false
else
    echo "✅ git is installed: $(git --version)"
    HAS_GIT=true
fi

# Check if current directory is a git repository
if [ "$HAS_GIT" = true ]; then
    if ! git rev-parse --is-inside-work-tree &> /dev/null; then
        echo "❌ Current directory is not a git repository"
        echo "   Please run this script from the CSF Portal repository root"
    else
        echo "✅ Current directory is a git repository"
    fi
fi

# Check for deploy.sh and deploy.ps1
if [ -f "./deploy.sh" ]; then
    echo "✅ deploy.sh script found"
else
    echo "❌ deploy.sh script not found in current directory"
    echo "   Please run this script from the CSF Portal repository root"
fi

if [ -f "./deploy.ps1" ]; then
    echo "✅ deploy.ps1 script found"
else
    echo "❌ deploy.ps1 script not found in current directory"
    echo "   Please run this script from the CSF Portal repository root"
fi

echo ""
echo "=== Environment Test Summary ==="

# Check if all requirements are met
if [ "$IS_WSL" = true ] && [ "$HAS_DOCKER" = true ] && [ "$DOCKER_RUNNING" = true ] && [ "$HAS_DOCKER_COMPOSE" = true ] && [ "$HAS_GIT" = true ]; then
    echo "✅ Your environment meets all requirements for WSL deployment"
    echo "   You can proceed with deployment using:"
    echo "   - WSL: ./deploy.sh"
    echo ""
    echo "   Note: Windows users can also use the native PowerShell script:"
    echo "   - PowerShell: .\\deploy.ps1 (does not require WSL)"
else
    echo "❌ Your environment does not meet all requirements for deployment"
    echo "   Please address the issues above before attempting deployment"
    echo "   For more information, see the WSL_DEPLOYMENT_GUIDE.md file"
fi

echo ""
echo "For detailed deployment instructions, please refer to WSL_DEPLOYMENT_GUIDE.md"