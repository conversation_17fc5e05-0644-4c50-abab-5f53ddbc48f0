// Test script for Lenel S2 NetBox access levels
const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');
require('dotenv').config();

// Get environment variables
const host = process.env.LENEL_S2_NETBOX_HOST || '';
const username = process.env.LENEL_S2_NETBOX_USERNAME || '';
const password = process.env.LENEL_S2_NETBOX_PASSWORD || '';
const port = process.env.LENEL_S2_NETBOX_PORT || 443;

// Create an instance of the API
const lenelS2NetBoxAPI = new LenelS2NetBoxAPI(host, username, password, port);

// Test function to get access levels
async function testGetAccessLevels() {
  console.log('Testing Lenel S2 NetBox getAccessLevels method...');
  console.log(`Using host: ${host}, port: ${port}`);

  try {
    // Authenticate first
    await lenelS2NetBoxAPI.authenticate();
    console.log('Authentication successful');
    
    // Get access levels
    console.log('Getting access levels...');
    const accessLevels = await lenelS2NetBoxAPI.getAccessLevels();
    
    console.log('Access levels:', accessLevels);
    console.log('Test successful!');
  } catch (error) {
    console.error('Error testing getAccessLevels:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testGetAccessLevels().catch(error => {
  console.error('Test error:', error);
});