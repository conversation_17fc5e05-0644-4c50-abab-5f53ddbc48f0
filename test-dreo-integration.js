// Test script to verify Dreo integration implementation
require('dotenv').config();
const WebSocket = require('ws');
const axios = require('axios');
const DreoAPI = require('./server/integrations/dreo/dreoAPI');

// Configuration
const API_BASE_URL = 'http://localhost:5000/api';
const WS_URL = 'ws://localhost:5000/api/dreo/ws';

// Test authentication
async function testAuthentication() {
  console.log('\n=== Testing Dreo Authentication ===');
  
  try {
    // Check if environment variables are set
    const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
    const password = process.env.DREO_PASSWORD;
    
    if (!email || !password) {
      console.error('Error: DREO_EMAIL/DREO_USERNAME and DREO_PASSWORD environment variables must be set');
      return false;
    }
    
    console.log(`Using email: ${email}`);
    console.log('Password is set');
    
    // Create Dreo API instance
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    console.log('Authenticating with Dreo API...');
    await dreoAPI.authenticate();
    
    console.log('Authentication successful!');
    return true;
  } catch (error) {
    console.error('Authentication failed:', error.message);
    return false;
  }
}

// Test device discovery
async function testDeviceDiscovery(authenticated) {
  console.log('\n=== Testing Dreo Device Discovery ===');
  
  if (!authenticated) {
    console.log('Skipping device discovery test due to authentication failure');
    return [];
  }
  
  try {
    // Create Dreo API instance
    const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
    const password = process.env.DREO_PASSWORD;
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    await dreoAPI.authenticate();
    
    // Get devices
    console.log('Fetching devices...');
    const devices = await dreoAPI.getDevices();
    
    if (devices.length === 0) {
      console.log('No devices found. Make sure your Dreo devices are set up in the Dreo app.');
      return [];
    }
    
    console.log(`Found ${devices.length} devices:`);
    devices.forEach((device, index) => {
      console.log(`${index + 1}. ${device.name} (ID: ${device.id})`);
    });
    
    return devices;
  } catch (error) {
    console.error('Device discovery failed:', error.message);
    return [];
  }
}

// Test device status
async function testDeviceStatus(devices) {
  console.log('\n=== Testing Dreo Device Status ===');
  
  if (devices.length === 0) {
    console.log('Skipping device status test due to no devices found');
    return null;
  }
  
  try {
    // Create Dreo API instance
    const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
    const password = process.env.DREO_PASSWORD;
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    await dreoAPI.authenticate();
    
    // Get status of the first device
    const device = devices[0];
    console.log(`Fetching status for device: ${device.name} (ID: ${device.id})...`);
    
    const status = await dreoAPI.getDeviceDetails(device.id);
    
    console.log('Device status:');
    console.log(JSON.stringify(status, null, 2));
    
    return { device, status };
  } catch (error) {
    console.error('Device status check failed:', error.message);
    return null;
  }
}

// Test device control
async function testDeviceControl(deviceInfo) {
  console.log('\n=== Testing Dreo Device Control ===');
  
  if (!deviceInfo) {
    console.log('Skipping device control test due to no device status');
    return false;
  }
  
  try {
    // Create Dreo API instance
    const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
    const password = process.env.DREO_PASSWORD;
    const dreoAPI = new DreoAPI(email, password);
    
    // Authenticate
    await dreoAPI.authenticate();
    
    const { device, status } = deviceInfo;
    
    // Get current power state
    const currentPower = status.power;
    console.log(`Current power state: ${currentPower ? 'ON' : 'OFF'}`);
    
    // Toggle power
    const newPower = !currentPower;
    console.log(`Setting power to: ${newPower ? 'ON' : 'OFF'}...`);
    
    await dreoAPI.setPower(device.id, newPower);
    console.log('Power state changed successfully!');
    
    // Wait a moment for the change to take effect
    console.log('Waiting for 2 seconds...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Get updated status
    console.log('Fetching updated status...');
    const updatedStatus = await dreoAPI.getDeviceDetails(device.id);
    
    console.log(`Updated power state: ${updatedStatus.power ? 'ON' : 'OFF'}`);
    
    // Verify the change
    if (updatedStatus.power === newPower) {
      console.log('Device control test passed!');
    } else {
      console.log('Device control test failed: Power state did not change as expected');
    }
    
    // Restore original power state
    console.log(`Restoring original power state: ${currentPower ? 'ON' : 'OFF'}...`);
    await dreoAPI.setPower(device.id, currentPower);
    
    return true;
  } catch (error) {
    console.error('Device control test failed:', error.message);
    return false;
  }
}

// Test WebSocket connection
async function testWebSocket(deviceInfo) {
  console.log('\n=== Testing Dreo WebSocket Connection ===');
  
  if (!deviceInfo) {
    console.log('Skipping WebSocket test due to no device status');
    return false;
  }
  
  return new Promise((resolve) => {
    try {
      const { device } = deviceInfo;
      
      console.log('Connecting to WebSocket...');
      const ws = new WebSocket(WS_URL);
      
      ws.on('open', () => {
        console.log('WebSocket connection established');
        
        // Register for device updates
        console.log(`Registering for updates on device: ${device.name} (ID: ${device.id})...`);
        ws.send(JSON.stringify({
          type: 'register-device',
          deviceId: device.id
        }));
      });
      
      ws.on('message', async (data) => {
        try {
          const message = JSON.parse(data);
          console.log('Received message:', message);
          
          if (message.type === 'welcome') {
            console.log('Received welcome message');
          } else if (message.type === 'register-success') {
            console.log('Device registration successful');
            
            // Create Dreo API instance for control
            const email = process.env.DREO_EMAIL || process.env.DREO_USERNAME;
            const password = process.env.DREO_PASSWORD;
            const dreoAPI = new DreoAPI(email, password);
            
            // Authenticate
            await dreoAPI.authenticate();
            
            // Get current power state
            const status = await dreoAPI.getDeviceDetails(device.id);
            const currentPower = status.power;
            
            // Toggle power to trigger an update
            console.log(`Toggling power from ${currentPower ? 'ON' : 'OFF'} to ${!currentPower ? 'ON' : 'OFF'}...`);
            await dreoAPI.setPower(device.id, !currentPower);
            
            // Wait for the update to come through WebSocket
            console.log('Waiting for WebSocket update...');
            
            // Set a timeout to restore the original state after 5 seconds
            setTimeout(async () => {
              console.log('Restoring original power state...');
              await dreoAPI.setPower(device.id, currentPower);
              
              // Close the WebSocket after another 2 seconds
              setTimeout(() => {
                console.log('Closing WebSocket connection...');
                ws.close();
                resolve(true);
              }, 2000);
            }, 5000);
          } else if (message.type === 'device-update') {
            console.log('Received device update:');
            console.log(JSON.stringify(message.status, null, 2));
            console.log('WebSocket test passed!');
          }
        } catch (err) {
          console.error('Error processing WebSocket message:', err);
        }
      });
      
      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        resolve(false);
      });
      
      ws.on('close', () => {
        console.log('WebSocket connection closed');
      });
      
      // Set a timeout to close the connection if no updates are received
      setTimeout(() => {
        if (ws.readyState === WebSocket.OPEN) {
          console.log('No updates received within timeout period');
          ws.close();
          resolve(false);
        }
      }, 30000);
    } catch (error) {
      console.error('WebSocket test failed:', error.message);
      resolve(false);
    }
  });
}

// Run all tests
async function runTests() {
  console.log('=== Dreo Integration Test ===');
  console.log('Testing Dreo integration with WebSocket-based real-time updates');
  
  try {
    // Test authentication
    const authenticated = await testAuthentication();
    
    // Test device discovery
    const devices = await testDeviceDiscovery(authenticated);
    
    // Test device status
    const deviceInfo = await testDeviceStatus(devices);
    
    // Test device control
    await testDeviceControl(deviceInfo);
    
    // Test WebSocket connection
    await testWebSocket(deviceInfo);
    
    console.log('\n=== Test Summary ===');
    console.log('Authentication:', authenticated ? 'PASSED' : 'FAILED');
    console.log('Device Discovery:', devices.length > 0 ? 'PASSED' : 'FAILED');
    console.log('Device Status:', deviceInfo ? 'PASSED' : 'FAILED');
    console.log('Device Control:', deviceInfo ? 'PASSED' : 'FAILED');
    console.log('WebSocket Updates:', deviceInfo ? 'PASSED' : 'FAILED');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the tests
runTests();