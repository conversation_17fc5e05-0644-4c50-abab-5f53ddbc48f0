/**
 * Test script for Google Calendar service account authentication
 * 
 * This script tests the Google Calendar API with service account credentials
 * to ensure it's properly authenticated.
 */
require('dotenv').config();
const GoogleCalendarAPI = require('./server/integrations/googleCalendar/googleCalendarAPI');

async function testGoogleCalendarServiceAccount() {
  try {
    console.log('Testing Google Calendar service account authentication...');
    
    // Use the service account email as the default impersonation email
    const defaultServiceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
    
    console.log(`Using service account email: ${defaultServiceAccountEmail}`);
    
    // Initialize the Google Calendar API with service account credentials
    const googleCalendar = new GoogleCalendarAPI(
      process.env.GOOGLE_CALENDAR_CLIENT_ID,
      process.env.GOOGLE_CALENDAR_CLIENT_SECRET,
      process.env.GOOGLE_CALENDAR_REDIRECT_URI,
      process.env.GOOGLE_CALENDAR_TOKEN_PATH,
      null, // No user tokens
      null, // No user ID
      defaultServiceAccountEmail // Use the service account email itself as the default impersonation email
    );
    
    // Initialize the API
    await googleCalendar.initialize();
    
    // Check if the API is authenticated
    const isAuthenticated = googleCalendar.isAuthenticated();
    
    console.log(`Google Calendar API authenticated: ${isAuthenticated}`);
    
    if (isAuthenticated) {
      console.log('Service account authentication is working correctly!');
      
      // Try to list calendars to further verify authentication
      try {
        console.log('Attempting to list calendars...');
        const calendars = await googleCalendar.listCalendars();
        console.log(`Successfully retrieved ${calendars.length} calendars.`);
        console.log('First few calendars:');
        calendars.slice(0, 3).forEach(calendar => {
          console.log(`- ${calendar.summary} (${calendar.id})`);
        });
      } catch (calendarError) {
        console.error('Error listing calendars:', calendarError.message);
      }
    } else {
      console.error('Service account authentication failed.');
      console.error('Please check your environment variables and service account credentials.');
    }
  } catch (error) {
    console.error('Error testing Google Calendar service account authentication:', error);
  }
}

// Run the test
testGoogleCalendarServiceAccount();