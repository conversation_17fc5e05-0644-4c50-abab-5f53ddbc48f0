// Test script to fetch a Planning Center person by ID
const PlanningCenterAPI = require('./server/integrations/planningCenter/planningCenterAPI');
require('dotenv').config();

// The ID of the person that was causing the error
const PERSON_ID = '4232862';

async function testFetchPerson() {
  try {
    console.log(`Testing fetch of Planning Center person with ID ${PERSON_ID}...`);
    
    // Initialize the API
    const planningCenterAPI = new PlanningCenterAPI();
    
    // Fetch the person
    const personResponse = await planningCenterAPI.getPersonById(PERSON_ID);
    
    // If we get here without an error, the fix worked
    console.log('Success! Person data fetched without errors.');
    
    // Log some basic info about the person to verify the data
    if (personResponse && personResponse.data) {
      const person = personResponse.data;
      console.log('Person details:');
      console.log(`- ID: ${person.id}`);
      
      if (person.attributes) {
        const attrs = person.attributes;
        console.log(`- Name: ${attrs.first_name} ${attrs.last_name}`);
        console.log(`- Email: ${attrs.email || 'N/A'}`);
      }
      
      // Check if linked data was processed correctly
      if (person.linked_data) {
        console.log('Linked data was processed successfully:');
        Object.keys(person.linked_data).forEach(key => {
          const data = person.linked_data[key];
          if (Array.isArray(data)) {
            console.log(`- ${key}: ${data.length} items`);
          } else {
            console.log(`- ${key}: 1 item`);
          }
        });
      } else {
        console.log('No linked data was found or processed.');
      }
    } else {
      console.log('Warning: Person data structure is not as expected.');
      console.log('Response:', JSON.stringify(personResponse, null, 2));
    }
  } catch (error) {
    console.error('Error fetching Planning Center person:', error);
    console.error('Error message:', error.message);
    if (error.response) {
      console.error('API response status:', error.response.status);
      console.error('API response data:', error.response.data);
    }
  }
}

// Run the test
testFetchPerson();