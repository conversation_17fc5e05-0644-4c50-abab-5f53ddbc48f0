NODE_ENV=production
PORT=8080
MONGO_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
SESSION_SECRET=your_secure_session_secret

# Google OAuth credentials
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=https://your-app-url.ondigitalocean.app/api/auth/google/callback

# Google API credentials for Drive and Admin SDK
GOOGLE_API_KEY=your_google_api_key

# Allowed domains for Google login (comma-separated)
ALLOWED_DOMAINS=yourchurch.org

# User role configuration
# NOTE: These environment variables are now considered legacy.
# It is recommended to configure default roles through the frontend interface
# in the Role Management page under the "Default Role Settings" tab.
# These variables will only be used if no settings are found in the database.
#
# Default role for new local users (optional, defaults to 'user')
DEFAULT_ROLE_LOCAL_USERS=user
# Default role for new Google users (optional, defaults to 'user')
DEFAULT_ROLE_GOOGLE_USERS=user
# Google Groups to roles mapping (optional, JSON format)
# Example: {"<EMAIL>":"admin","<EMAIL>":"finance"}

# LG ThinQ configuration
LG_THINQ_API_KEY=your_lg_thinq_api_key
LG_THINQ_REGION=america
LG_THINQ_COUNTRY=US

# Canva API configuration
CANVA_DOMAIN=canva.com
CANVA_API_KEY=your_canva_api_key

# Canva OAuth configuration
CANVA_CLIENT_ID=your_canva_client_id
CANVA_CLIENT_SECRET=your_canva_client_secret
CANVA_REDIRECT_URI=https://your-app-url.ondigitalocean.app/api/canva/callback

# Q-sys Core Manager configuration
# QSYS_HOST: The hostname or IP address of the Q-sys Core Manager
# QSYS_PORT: The port number (optional, defaults to 1710 for TCP, 443 for HTTPS)
# QSYS_USERNAME: The username for authentication (optional)
# QSYS_PASSWORD: The password for authentication (optional)
# QSYS_PROTOCOL: The protocol to use (tcp or https, defaults to tcp)
QSYS_HOST=your_qsys_host
QSYS_PORT=1710
QSYS_USERNAME=your_qsys_username
QSYS_PASSWORD=your_qsys_password
QSYS_PROTOCOL=tcp

# Colorlit Z4 Pro LED Controller configuration
# COLORLIT_HOST: The hostname or IP address of the Colorlit controller
# COLORLIT_PORT: The port number (optional, defaults to 80 for HTTP, 443 for HTTPS)
# COLORLIT_API_KEY: The API key for authentication (optional)
# COLORLIT_USERNAME: The username for authentication (optional)
# COLORLIT_PASSWORD: The password for authentication (optional)
COLORLIT_HOST=your_colorlit_host
COLORLIT_PORT=80
COLORLIT_API_KEY=your_colorlit_api_key
COLORLIT_USERNAME=your_colorlit_username
COLORLIT_PASSWORD=your_colorlit_password

# ZeeVee HDbridge 2920-NA configuration
# ZEEVEE_HOST: The hostname or IP address of the ZeeVee HDbridge 2920-NA
# ZEEVEE_PORT: The port number (optional, defaults to 80)
# ZEEVEE_USERNAME: The username for authentication (optional)
# ZEEVEE_PASSWORD: The password for authentication (optional)
ZEEVEE_HOST=your_zeevee_host
ZEEVEE_PORT=80
ZEEVEE_USERNAME=your_zeevee_username
ZEEVEE_PASSWORD=your_zeevee_password

# Panasonic Pro AV Camera configuration
# PANASONIC_HOST: The hostname or IP address of the Panasonic camera
# PANASONIC_PORT: The port number (optional, defaults to 80 for HTTP, 443 for HTTPS)
# PANASONIC_USERNAME: The username for authentication
# PANASONIC_PASSWORD: The password for authentication
# PANASONIC_MODEL: The camera model (e.g., AW-HE40)
# PANASONIC_PROTOCOL: The protocol to use (http or https, defaults to http)
PANASONIC_HOST=your_panasonic_host
PANASONIC_PORT=80
PANASONIC_USERNAME=your_panasonic_username
PANASONIC_PASSWORD=your_panasonic_password
PANASONIC_MODEL=AW-HE40
PANASONIC_PROTOCOL=http