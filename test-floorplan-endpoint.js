const axios = require('axios');

// Test the floorplan endpoint with the problematic ID
async function testFloorplanEndpoint() {
  const floorId = '686dd139c7f14736e4e21bd2';
  const baseUrl = 'https://portal.ukcsf.org';
  
  try {
    console.log(`Testing floorplan endpoint for floor ID: ${floorId}`);
    const response = await axios.get(`${baseUrl}/api/floors/${floorId}/floorplan`, {
      validateStatus: false // Don't throw error on non-2xx responses
    });
    
    console.log(`Status code: ${response.status}`);
    console.log('Response data:', response.data);
    
    if (response.status === 400) {
      console.log('The floor ID format is invalid. It should be a valid MongoDB ObjectID.');
    } else if (response.status === 404) {
      console.log('The floorplan was not found. This could be because:');
      console.log('1. The floor with this ID does not exist');
      console.log('2. The floor exists but does not have a floorplan');
      console.log('3. The floorplan file does not exist on the server');
    }
  } catch (error) {
    console.error('Error making request:', error.message);
  }
}

testFloorplanEndpoint();