ENABLE_DEBUG_LOGGING=
NODE_ENV=
PORT=
MONGO_URI=
SESSION_SECRET=

# Google API credentials for Drive and Admin SDK
GOOGLE_API_KEY=

# Google OAuth credentials
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=

# Google Service Account credentials for all Google integrations
GOOGLE_SERVICE_ACCOUNT_EMAIL=
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY=
GOOGLE_ADMIN_IMPERSONATION_EMAIL=

# Allowed domains for Google login (comma-separated)
ALLOWED_DOMAINS=

# GLPI API configuration (if used)
GLPI_API_URL=
GLPI_APP_TOKEN=
GLPI_USER_TOKEN=

# UniFi Network configuration
UNIFI_NETWORK_HOST=
UNIFI_NETWORK_API_KEY=
UNIFI_NETWORK_PORT=
UNIFI_NETWORK_SITE=

# UniFi Access configuration
UNIFI_ACCESS_HOST=
UNIFI_ACCESS_PORT=
UNIFI_ACCESS_API_KEY=

# UniFi Protect configuration
UNIFI_PROTECT_HOST_A=
UNIFI_PROTECT_HOST_B=
UNIFI_PROTECT_PORT=
UNIFI_PROTECT_API_KEY_A=
UNIFI_PROTECT_API_KEY_B=

# Lenel S2 NetBox configuration
LENEL_S2_NETBOX_HOST=
LENEL_S2_NETBOX_USERNAME=
LENEL_S2_NETBOX_PASSWORD=
LENEL_S2_NETBOX_PORT=
LENEL_S2_NETBOX_LOCAL_NETWORK=

# RADIUS server configuration (if used)
RADIUS_PORT=
RADIUS_AUTHORIZATION_PORT=
RADIUS_ADDRESS=
RADIUS_AUTHENTICATION=
RADIUS_SECRET=

# WiiM configuration
WIIM_HOST=
WIIM_PORT=

# Planning Center configuration
PLANNING_CENTER_APPID=
PLANNING_CENTER_TOKEN=

# Spotify configuration for WiiM integration
SPOTIFY_CLIENT_ID=
SPOTIFY_CLIENT_SECRET=
SPOTIFY_REFRESH_TOKEN=
WIIM_SPOTIFY_DEVICE_ID=

# LG ThinQ configuration
LG_THINQ_PAT_TOKEN=
LG_THINQ_REGION=
LG_THINQ_COUNTRY=

# Synology Configuration
SYNOLOGY_HOST=
SYNOLOGY_PORT=
SYNOLOGY_USERNAME=
SYNOLOGY_PASSWORD=
SYNOLOGY_SECURE=

# SkyportCloud HVAC Configuration
# Either API key (preferred) or username/password is required
# SKYPORTCLOUD_API_KEY=
SKYPORTCLOUD_USERNAME=
SKYPORTCLOUD_PASSWORD=
# Optional: Override the default base URL if needed
SKYPORTCLOUD_BASE_URL=

# Canva API configuration
CANVA_DOMAIN=

# Canva OAuth configuration
CANVA_CLIENT_ID=
CANVA_CLIENT_SECRET=
CANVA_REDIRECT_URI=

# Mosyle Business configuration
# For MOSYLE_BUSINESS_DOMAIN, use your Mosyle subdomain (e.g., if your URL is https://example.mosyle.com, use "example")
# For MOSYLE_BUSINESS_API_KEY, use your Mosyle Business API key from your account settings
MOSYLE_BUSINESS_API_KEY=
MOSYLE_BUSINESS_DOMAIN=

# Dreo API configuration
# DREO_USERNAME: The username for Dreo API authentication
# DREO_PASSWORD: The password for Dreo API authentication
DREO_USERNAME=
DREO_PASSWORD=

# Rain Bird API configuration
# RAIN_BIRD_HOST: The hostname or IP address of the Rain Bird controller
# RAIN_BIRD_USERNAME: The username for authentication
# RAIN_BIRD_PASSWORD: The password for authentication (also used as encryption key for local control)
# RAIN_BIRD_PORT: The port number (optional, defaults to 443)
# RAIN_BIRD_LOCAL_NETWORK: Whether the Rain Bird controller is on a local network (true/false, optional)
# RAIN_BIRD_CLOUD_HOST: The hostname for the Rain Bird cloud service (optional, defaults to rdz-rbcloud.rainbird.com)
# RAIN_BIRD_CONTROLLER_MAC: The MAC address of the controller (required for cloud-based control)
RAIN_BIRD_HOST=
RAIN_BIRD_PASSWORD=
RAIN_BIRD_PORT=
RAIN_BIRD_LOCAL_NETWORK=
RAIN_BIRD_CLOUD_HOST=
RAIN_BIRD_CONTROLLER_MAC=

# Email configuration for welcome emails and password resets
EMAIL_HOST=
EMAIL_PORT=
EMAIL_SECURE=
EMAIL_USER=
EMAIL_PASSWORD=
EMAIL_FROM=
FRONTEND_URL=

# Gmail OAuth2 Configuration (for Gmail SMTP)
# Only needed if using Gmail as SMTP provider with OAuth2
GMAIL_OAUTH_CLIENT_ID=
GMAIL_OAUTH_CLIENT_SECRET=
GMAIL_OAUTH_REFRESH_TOKEN=
GMAIL_OAUTH_ACCESS_TOKEN=

# Apple Business Manager configuration
# These environment variables are required for the Apple Business Manager integration
APPLE_BUSINESS_MANAGER_CLIENT_ID=
APPLE_BUSINESS_MANAGER_CLIENT_SECRET=
APPLE_BUSINESS_MANAGER_ORGANIZATION_ID=
APPLE_BUSINESS_MANAGER_KEY_ID=
APPLE_BUSINESS_MANAGER_PRIVATE_KEY_PATH=
APPLE_BUSINESS_MANAGER_ISSUER_ID=
APPLE_BUSINESS_MANAGER_TOKEN_EXPIRY=

# Q-sys Core Manager configuration
# QSYS_HOST: The hostname or IP address of the Q-sys Core Manager
# QSYS_PORT: The port number (optional, defaults to 1710 for TCP, 443 for HTTPS)
# QSYS_USERNAME: The username for authentication (optional)
# QSYS_PASSWORD: The password for authentication (optional)
# QSYS_PROTOCOL: The protocol to use (tcp or https, defaults to tcp)
QSYS_HOST=
QSYS_PORT=
QSYS_USERNAME=
QSYS_PASSWORD=
QSYS_PROTOCOL=

# Colorlit Z4 Pro LED Controller configuration
# COLORLIT_HOST: The hostname or IP address of the Colorlit controller
# COLORLIT_PORT: The port number (optional, defaults to 80 for HTTP, 443 for HTTPS)
# COLORLIT_API_KEY: The API key for authentication (optional)
# COLORLIT_USERNAME: The username for authentication (optional)
# COLORLIT_PASSWORD: The password for authentication (optional)
COLORLIT_HOST=
COLORLIT_PORT=
COLORLIT_API_KEY=
COLORLIT_USERNAME=
COLORLIT_PASSWORD=

# ZeeVee HDbridge 2920-NA configuration
# ZEEVEE_HOST: The hostname or IP address of the ZeeVee HDbridge 2920-NA
# ZEEVEE_PORT: The port number (optional, defaults to 80)
# ZEEVEE_USERNAME: The username for authentication (optional)
# ZEEVEE_PASSWORD: The password for authentication (optional)
ZEEVEE_HOST=
ZEEVEE_PORT=
ZEEVEE_USERNAME=
ZEEVEE_PASSWORD=

# Panasonic Pro AV Camera configuration
# PANASONIC_HOST: The hostname or IP address of the Panasonic camera
# PANASONIC_PORT: The port number (optional, defaults to 80 for HTTP, 443 for HTTPS)
# PANASONIC_USERNAME: The username for authentication
# PANASONIC_PASSWORD: The password for authentication
# PANASONIC_MODEL: The camera model (e.g., AW-HE40)
# PANASONIC_PROTOCOL: The protocol to use (http or https, defaults to http)
PANASONIC_HOST=
PANASONIC_PORT=
PANASONIC_USERNAME=
PANASONIC_PASSWORD=
PANASONIC_MODEL=
PANASONIC_PROTOCOL=

# SMTP Configuration for outgoing emails
SMTP_HOST=
SMTP_PORT=
SMTP_SECURE=
SMTP_USER=
SMTP_PASS=

# Ticket system configuration
TICKET_EMAIL_FROM=
PORTAL_URL=

# Optional: Email webhook authentication (recommended)
WEBHOOK_SECRET=
# Gmail Ticketing System Configuration (Auto-generated)
GOOGLE_GMAIL_PROJECT_ID=
GOOGLE_GMAIL_TOPIC_NAME=
GOOGLE_GMAIL_SUBSCRIPTION_NAME=
GOOGLE_GMAIL_SERVICE_ACCOUNT_KEY=
GMAIL_MONITORED_EMAIL=

# SMTP Configuration for outgoing emails
SMTP_HOST=
SMTP_PORT=
SMTP_SECURE=
SMTP_USER=
SMTP_PASS=

# Ticket system configuration
TICKET_EMAIL_FROM=
PORTAL_URL=
API_URL=
WEBHOOK_SECRET=
