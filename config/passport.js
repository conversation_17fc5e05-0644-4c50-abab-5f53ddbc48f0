const GoogleStrategy = require('passport-google-oauth20').Strategy;
const LocalStrategy = require('passport-local').Strategy;
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const RoleSettings = require('../models/RoleSettings');

module.exports = function(passport) {
  // Configure Local Strategy for local authentication
  passport.use(
    new LocalStrategy(
      {
        usernameField: 'email',
        passwordField: 'password'
      },
      async (email, password, done) => {
        try {
          // Find user by email
          const user = await User.findOne({ email });
          
          // Check if user exists
          if (!user) {
            return done(null, false, { message: 'Invalid credentials' });
          }
          
          // Check if user is a local user
          if (user.authType !== 'local') {
            return done(null, false, { 
              message: 'This account uses Google authentication. Please sign in with Google.' 
            });
          }
          
          // Check if user is active
          if (!user.isActive) {
            return done(null, false, { message: 'Account is inactive' });
          }
          
          // Verify password
          const isMatch = await bcrypt.compare(password, user.password);
          if (!isMatch) {
            return done(null, false, { message: 'Invalid credentials' });
          }
          
          // Check if 2FA is required
          if (user.twoFactorEnabled) {
            // Return user with 2FA flag to indicate 2FA verification is needed
            return done(null, user, { requires2FA: true });
          }
          
          // If 2FA is not set up but required for local users, redirect to setup
          if (!user.twoFactorSetupComplete) {
            return done(null, user, { requiresSetup2FA: true });
          }
          
          // Authentication successful
          return done(null, user);
        } catch (err) {
          console.error('Error in local authentication:', err);
          return done(err);
        }
      }
    )
  );

  // Only initialize Google Strategy if all required environment variables are set
  if (process.env.GOOGLE_CLIENT_ID && 
      process.env.GOOGLE_CLIENT_ID !== 'your_google_client_id' && 
      process.env.GOOGLE_CLIENT_SECRET && 
      process.env.GOOGLE_CLIENT_SECRET !== 'your_google_client_secret' && 
      process.env.GOOGLE_CALLBACK_URL) {


    passport.use(
      new GoogleStrategy(
        {
          clientID: process.env.GOOGLE_CLIENT_ID,
          clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          callbackURL: process.env.GOOGLE_CALLBACK_URL,
          proxy: true
        },
        async (accessToken, refreshToken, profile, done) => {
          // Extract email domain to check if it's allowed
          const email = profile.emails[0].value;
          const domain = email.split('@')[1];
          const allowedDomains = process.env.ALLOWED_DOMAINS.split(',');

          if (!allowedDomains.includes(domain)) {
            return done(null, false, { message: 'Domain not allowed' });
          }

          try {
            // Check if user already exists
            let user = await User.findOne({ googleId: profile.id });

            if (user) {
              // Update user's tokens
              user.googleAccessToken = accessToken;
              // Only update refresh token if a new one was provided
              if (refreshToken) {
                user.googleRefreshToken = refreshToken;
              }
              await user.save();
              return done(null, user);
            }

            // If not, create new user
            // Get default role settings from database or use 'user' as fallback
            let defaultRole = 'user';
            let googleGroupsRoleMapping = null;
            
            try {
              // Get settings from database
              const settings = await RoleSettings.getCurrentSettings();
              defaultRole = settings.defaultRoleGoogleUsers;
              
              // Convert array of objects to object with groupEmail as key and roleName as value
              if (settings.googleGroupsRoleMapping && settings.googleGroupsRoleMapping.length > 0) {
                googleGroupsRoleMapping = {};
                settings.googleGroupsRoleMapping.forEach(mapping => {
                  googleGroupsRoleMapping[mapping.groupEmail] = mapping.roleName;
                });
              }
            } catch (error) {
              console.error('Error getting role settings, using defaults:', error);
              // Continue with default role if there's an error
            }
            
            let roles = [defaultRole];

            // If Google Groups role mapping is configured, check user's groups and assign roles
            if (googleGroupsRoleMapping) {
              try {
                // Import the GoogleAdminAPI
                const GoogleAdminAPI = require('../server/integrations/googleAdmin/googleAdminAPI');
                
                // Create a new API instance for checking groups
                const googleAdminAPI = new GoogleAdminAPI(
                  process.env.GOOGLE_CLIENT_ID,
                  process.env.GOOGLE_CLIENT_SECRET,
                  process.env.GOOGLE_CALLBACK_URL,
                  null, // No token path needed
                  null, // No user tokens
                  null, // No user ID
                  process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL // Impersonation email
                );
                
                // Initialize the API
                await googleAdminAPI.initialize();
                
                // Get user's email
                const userEmail = profile.emails[0].value;
                
                // Get all groups
                const groups = await googleAdminAPI.listGroups();
                
                // For each group in the mapping
                for (const [groupEmail, role] of Object.entries(googleGroupsRoleMapping)) {
                  // Find the group
                  const group = groups.find(g => g.email.toLowerCase() === groupEmail.toLowerCase());
                  
                  if (group) {
                    try {
                      // Check if user is a member of the group
                      const isMember = await googleAdminAPI.isUserMemberOfGroup(userEmail, group.email);
                      
                      if (isMember) {
                        // Add the role if not already in the roles array
                        if (!roles.includes(role)) {
                          roles.push(role);
                        }
                      }
                    } catch (groupCheckError) {
                      console.error(`Error checking if user is member of group ${group.email}:`, groupCheckError);
                    }
                  }
                }
              } catch (error) {
                console.error('Error checking Google Groups for role assignment:', error);
                // Continue with default role if there's an error
              }
            }

            const newUser = new User({
              googleId: profile.id,
              name: profile.displayName,
              email: profile.emails[0].value,
              avatar: profile.photos[0].value,
              googleAccessToken: accessToken,
              // Only set refresh token if it was provided
              ...(refreshToken && { googleRefreshToken: refreshToken }),
              roles: roles
            });

            await newUser.save();
            return done(null, newUser);
          } catch (err) {
            console.error(err);
            return done(err);
          }
        }
      )
    );
  } else {
    console.log('Google OAuth credentials not properly configured. Google authentication is disabled.');
  }

  // Serialize user for the session
  passport.serializeUser((user, done) => {
    done(null, user.id);
  });

  // Deserialize user from the session
  passport.deserializeUser(async (id, done) => {
    try {
      const user = await User.findById(id).select('-googleAccessToken -googleRefreshToken');
      done(null, user);
    } catch (err) {
      done(err);
    }
  });
};
