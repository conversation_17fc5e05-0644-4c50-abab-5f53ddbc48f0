const path = require('path');
const fs = require('fs');

// Define the token directory path
const tokenDir = path.join(__dirname, '..', 'tokens');

// Create the tokens directory if it doesn't exist
if (!fs.existsSync(tokenDir)) {
  try {
    fs.mkdirSync(tokenDir, { recursive: true });
    console.log(`Created token directory: ${tokenDir}`);
  } catch (err) {
    console.error(`Error creating token directory: ${err.message}`);
  }
}

module.exports = {
  // Directory for storing token files
  tokenDir
};
