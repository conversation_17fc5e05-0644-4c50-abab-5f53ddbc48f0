// Central permissions registry
// This module scans the codebase for permission strings used with
// hasPermission('...') and checkPermission('...') and exports a
// deduplicated, sorted list along with grouped categories.
//
// Rationale: Keeps the registry in sync with code while avoiding
// hardcoding an incomplete list. This file performs lightweight
// scanning at require-time; it is primarily intended for tooling/tests.

const fs = require('fs');
const path = require('path');

// Directories likely to contain permission usage
const DEFAULT_SCAN_DIRS = [
  path.join(__dirname, '..', 'routes'),
  path.join(__dirname, '..', 'server', 'routes'),
  path.join(__dirname, '..', 'server'),
];

function isJsFile(filePath) {
  return filePath.endsWith('.js') || filePath.endsWith('.jsx');
}

function walk(dir, out = []) {
  if (!fs.existsSync(dir)) return out;
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    // Skip node_modules, .git, build artifacts
    if (entry.name === 'node_modules' || entry.name === '.git' || entry.name === 'build' || entry.name === 'coverage') {
      continue;
    }
    const full = path.join(dir, entry.name);
    try {
      if (entry.isDirectory()) {
        walk(full, out);
      } else if (entry.isFile() && isJsFile(full)) {
        out.push(full);
      }
    } catch (_) {
      // ignore filesystem race conditions
    }
  }
  return out;
}

function extractPermissionsFromSource(src) {
  const perms = [];
  const regexes = [
    /hasPermission\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
    /checkPermission\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
  ];
  for (const rx of regexes) {
    let m;
    while ((m = rx.exec(src)) !== null) {
      perms.push(m[1]);
    }
  }
  return perms;
}

function scanPermissions(dirs = DEFAULT_SCAN_DIRS) {
  const files = dirs.flatMap((d) => walk(d));
  const set = new Set();
  for (const f of files) {
    try {
      const src = fs.readFileSync(f, 'utf8');
      const found = extractPermissionsFromSource(src);
      for (const p of found) set.add(p);
    } catch (_) {
      // ignore unreadable files
    }
  }
  // Return a sorted array for stable output
  return Array.from(set).sort();
}

const allPermissions = scanPermissions();

function groupByEntity(perms) {
  const groups = {};
  for (const p of perms) {
    const [entity] = p.split(':');
    if (!groups[entity]) groups[entity] = [];
    groups[entity].push(p);
  }
  // sort each group
  for (const k of Object.keys(groups)) {
    groups[k] = groups[k].sort();
  }
  return groups;
}

const grouped = groupByEntity(allPermissions);

module.exports = {
  list: allPermissions,
  grouped,
  has: (perm) => allPermissions.includes(perm),
  // Expose helpers for tests/tooling
  _scanPermissions: scanPermissions,
  _extract: extractPermissionsFromSource,
};
