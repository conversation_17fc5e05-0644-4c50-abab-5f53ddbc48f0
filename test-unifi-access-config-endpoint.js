/**
 * Test script for UniFi Access config endpoint
 * 
 * This script tests the UniFi Access config endpoint by:
 * 1. Making a direct HTTP request to the endpoint
 * 2. Checking the response status and data
 * 
 * Usage:
 * node test-unifi-access-config-endpoint.js
 */

const axios = require('axios');

// Create a test function
async function testUnifiAccessConfigEndpoint() {
  console.log('Testing UniFi Access config endpoint...');
  
  try {
    // Make a request to the endpoint
    console.log('Making request to /api/unifi-access/config...');
    const response = await axios.get('http://localhost:8080/api/unifi-access/config');
    
    // Log the response
    console.log(`Response status: ${response.status}`);
    console.log('Response data:', response.data);
    
    console.log('\n✅ Test completed successfully.');
  } catch (error) {
    console.error('\n❌ Test failed with error:');
    
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error(`Status: ${error.response.status}`);
      console.error('Response data:', error.response.data);
      console.error('Response headers:', error.response.headers);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from server');
      console.error('Request:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
    }
  }
}

// Run the test
testUnifiAccessConfigEndpoint();