// Test script to verify pagination with large directories in the Synology API
require('dotenv').config();
const SynologyAPI = require('./server/integrations/synology/synologyAPI');

// Create a custom version of SynologyAPI that bypasses the integration tracker
class TestSynologyAPI extends SynologyAPI {
  async initialize() {
    try {
      // Skip the integration tracker checks
      if (!this.host || !this.port || !this.username || !this.password) {
        console.error('Host, port, username, and password are required for Synology integration.');
        return;
      }

      // Check if we can authenticate
      try {
        await this.login();
        console.log('Authentication successful!');
      } catch (authError) {
        console.error('Authentication failed:', authError.message);
        throw authError;
      }
    } catch (error) {
      console.error('Error initializing Synology API:', error);
      throw error;
    }
  }
}

async function testPagination() {
  try {
    console.log('Testing Synology API pagination with environment variables:');
    console.log(`SYNOLOGY_HOST: ${process.env.SYNOLOGY_HOST}`);
    console.log(`SYNOLOGY_PORT: ${process.env.SYNOLOGY_PORT}`);
    console.log(`SYNOLOGY_USERNAME: ${process.env.SYNOLOGY_USERNAME}`);
    console.log(`SYNOLOGY_SECURE: ${process.env.SYNOLOGY_SECURE}`);
    
    // Extract host without protocol
    let host = process.env.SYNOLOGY_HOST;
    if (host.startsWith('http://') || host.startsWith('https://')) {
      host = host.replace(/^https?:\/\//, '');
    }
    
    // Create a new TestSynologyAPI instance with environment variables
    const synologyAPI = new TestSynologyAPI(
      host,
      parseInt(process.env.SYNOLOGY_PORT, 10),
      process.env.SYNOLOGY_USERNAME,
      process.env.SYNOLOGY_PASSWORD,
      process.env.SYNOLOGY_SECURE !== 'false'
    );
    
    // Log the baseURL to verify it's formed correctly
    console.log(`Generated baseURL: ${synologyAPI.baseURL}`);
    
    // Initialize the API (this will attempt to authenticate)
    console.log('Initializing API...');
    await synologyAPI.initialize();
    
    // Test directories to check
    const testDirectories = [
      '/volume1',
      '/volume1/homes',
      '/volume1/web',
      '/volume1/public'
    ];
    
    // Page sizes to test
    const pageSizes = [10, 20, 50, 100];
    
    // Test each directory with different page sizes
    for (const directory of testDirectories) {
      console.log(`\n=== Testing directory: ${directory} ===`);
      
      try {
        // First, try to get all files without pagination to see total count
        console.log(`Fetching all files in ${directory} to get total count...`);
        const allFilesResult = await synologyAPI.listFiles(directory, { limit: 1000 });
        const totalFiles = allFilesResult.files.length;
        console.log(`Total files in directory: ${totalFiles}`);
        
        if (totalFiles === 0) {
          console.log('Directory is empty, skipping pagination tests.');
          continue;
        }
        
        // Test each page size
        for (const pageSize of pageSizes) {
          console.log(`\nTesting with page size: ${pageSize}`);
          
          // Calculate expected number of pages
          const expectedPages = Math.ceil(totalFiles / pageSize);
          console.log(`Expected pages: ${expectedPages}`);
          
          // Test first page
          console.log(`Fetching page 1 with ${pageSize} items per page...`);
          const firstPageResult = await synologyAPI.listFiles(directory, { 
            limit: pageSize, 
            offset: 0 
          });
          
          console.log(`Page 1: Retrieved ${firstPageResult.files.length} files`);
          console.log(`Pagination info:`, firstPageResult.pagination);
          
          // If there are multiple pages, test the second page
          if (expectedPages > 1) {
            console.log(`Fetching page 2 with ${pageSize} items per page...`);
            const secondPageResult = await synologyAPI.listFiles(directory, { 
              limit: pageSize, 
              offset: pageSize 
            });
            
            console.log(`Page 2: Retrieved ${secondPageResult.files.length} files`);
            console.log(`Pagination info:`, secondPageResult.pagination);
            
            // Verify that we got different files on different pages
            if (secondPageResult.files.length > 0 && firstPageResult.files.length > 0) {
              const firstPageFirstFile = firstPageResult.files[0].name;
              const secondPageFirstFile = secondPageResult.files[0].name;
              
              if (firstPageFirstFile !== secondPageFirstFile) {
                console.log('✅ Pagination working correctly: Different files on different pages');
              } else {
                console.log('❌ Pagination issue: Same files on different pages');
              }
            }
          }
          
          // Test sorting
          console.log('\nTesting sorting...');
          
          // Test ascending sort by name
          console.log('Fetching files sorted by name (ascending)...');
          const ascResult = await synologyAPI.listFiles(directory, { 
            limit: pageSize, 
            offset: 0,
            sort_by: 'name',
            sort_direction: 'ASC'
          });
          
          if (ascResult.files.length > 1) {
            console.log(`First file (ASC): ${ascResult.files[0].name}`);
            console.log(`Last file (ASC): ${ascResult.files[ascResult.files.length - 1].name}`);
          }
          
          // Test descending sort by name
          console.log('Fetching files sorted by name (descending)...');
          const descResult = await synologyAPI.listFiles(directory, { 
            limit: pageSize, 
            offset: 0,
            sort_by: 'name',
            sort_direction: 'DESC'
          });
          
          if (descResult.files.length > 1) {
            console.log(`First file (DESC): ${descResult.files[0].name}`);
            console.log(`Last file (DESC): ${descResult.files[descResult.files.length - 1].name}`);
            
            // Verify that sorting works
            if (ascResult.files.length > 0 && descResult.files.length > 0) {
              const ascFirstFile = ascResult.files[0].name;
              const descFirstFile = descResult.files[0].name;
              
              if (ascFirstFile !== descFirstFile) {
                console.log('✅ Sorting working correctly: Different order with different sort directions');
              } else {
                console.log('❌ Sorting issue: Same order with different sort directions');
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error testing directory ${directory}:`, error.message);
      }
    }
    
    console.log('\nPagination testing completed!');
  } catch (error) {
    console.error('Error in test script:', error);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testPagination();