#!/usr/bin/env node

/**
 * Test script for Google Service Account authentication
 * This script tests if the service account credentials are valid
 */

require('dotenv').config();
const { google } = require('googleapis');

async function testGoogleServiceAuth() {
  console.log('🧪 Testing Google Service Account Authentication...\n');

  // Check environment variables
  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
  const testUserEmail = '<EMAIL>'; // Use a known user for testing

  console.log('📋 Configuration:');
  console.log(`   Service Account Email: ${serviceAccountEmail || '❌ Missing'}`);
  console.log(`   Service Account Key: ${serviceAccountPrivateKey ? '✅ Set' : '❌ Missing'}`);
  console.log(`   Test User Email: ${testUserEmail}\n`);

  if (!serviceAccountEmail || !serviceAccountPrivateKey) {
    console.error('❌ Error: Missing service account credentials');
    process.exit(1);
  }

  try {
    // Test 1: Create JWT client
    console.log('🔑 Test 1: Creating JWT client...');
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: serviceAccountPrivateKey,
      scopes: ['https://www.googleapis.com/auth/drive.readonly'],
      subject: testUserEmail
    });
    console.log('   ✅ JWT client created successfully\n');

    // Test 2: Get access token
    console.log('🎫 Test 2: Getting access token...');
    try {
      const accessToken = await auth.getAccessToken();
      console.log('   ✅ Access token obtained successfully');
      console.log(`   📝 Token type: ${accessToken.token ? 'Valid' : 'Invalid'}\n`);
    } catch (tokenError) {
      console.error('   ❌ Failed to get access token:', tokenError.message);
      throw tokenError;
    }

    // Test 3: Test Drive API call
    console.log('📂 Test 3: Testing Drive API call...');
    try {
      const drive = google.drive({ version: 'v3', auth });
      const response = await drive.files.list({
        pageSize: 1,
        fields: 'files(id, name)'
      });
      console.log('   ✅ Drive API call successful');
      console.log(`   📊 Files found: ${response.data.files ? response.data.files.length : 0}\n`);
    } catch (driveError) {
      console.error('   ❌ Drive API call failed:', driveError.message);
      if (driveError.code === 403) {
        console.log('   🔐 This might be a permissions issue. Check:');
        console.log('      - Service account has domain-wide delegation enabled');
        console.log('      - The correct scopes are authorized in Google Admin Console');
        console.log('      - The test user email exists in the domain');
      }
      throw driveError;
    }

    // Test 4: Test Admin API call
    console.log('👤 Test 4: Testing Admin API call...');
    try {
      const adminAuth = new google.auth.JWT({
        email: serviceAccountEmail,
        key: serviceAccountPrivateKey,
        scopes: ['https://www.googleapis.com/auth/admin.directory.user.readonly'],
        subject: testUserEmail
      });

      const admin = google.admin({ version: 'directory_v1', auth: adminAuth });
      const response = await admin.users.list({
        domain: 'ukcsf.org',
        maxResults: 1
      });
      console.log('   ✅ Admin API call successful');
      console.log(`   👥 Users found: ${response.data.users ? response.data.users.length : 0}\n`);
    } catch (adminError) {
      console.error('   ❌ Admin API call failed:', adminError.message);
      if (adminError.code === 403) {
        console.log('   🔐 This might be a permissions issue. Check:');
        console.log('      - Service account has Admin SDK scopes authorized');
        console.log('      - Domain-wide delegation is properly configured');
      }
      throw adminError;
    }

    console.log('🎉 All tests passed! Google Service Account authentication is working correctly.\n');

  } catch (error) {
    console.error('\n💥 Service account authentication test failed:', error.message);
    
    if (error.message.includes('invalid_grant')) {
      console.log('\n🔧 Troubleshooting invalid_grant error:');
      console.log('   - Check that the service account email is correct');
      console.log('   - Verify the private key is properly formatted');
      console.log('   - Ensure domain-wide delegation is enabled');
      console.log('   - Check that the subject email exists in the domain');
    } else if (error.message.includes('private_key')) {
      console.log('\n🔧 Private key issue:');
      console.log('   - Ensure the private key includes -----BEGIN PRIVATE KEY----- and -----END PRIVATE KEY-----');
      console.log('   - Check for proper newline characters (\\n)');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testGoogleServiceAuth();
}

module.exports = testGoogleServiceAuth;