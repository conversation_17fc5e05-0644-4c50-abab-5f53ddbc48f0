/**
 * Test script to verify that the Google Drive service account integration is working correctly
 * 
 * This script tests the following:
 * 1. Listing files with user email filtering
 * 2. Searching files with user email filtering
 * 3. Getting a file with user access verification
 * 4. Getting a viewer URL with user access verification
 * 5. Getting an editor URL with user access verification
 * 6. Adding a file to favorites with user access verification
 * 
 * Usage: 
 *   node test-google-drive-service-account.js
 * 
 * Environment variables:
 *   TEST_USER_EMAIL - Email of the test user (default: from .env)
 *   TEST_USER_PASSWORD - Password of the test user (default: from .env)
 *   TEST_FILE_ID - ID of a test file in Google Drive (optional)
 *   SERVER_PORT - Port of the local server (default: 6000 or from .env)
 *   DEBUG - Set to 'true' for more detailed logging
 */

const axios = require('axios');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Debug mode
const DEBUG = process.env.DEBUG === 'true';

// Helper function for debug logging
function debugLog(...args) {
  if (DEBUG) {
    console.log('[DEBUG]', ...args);
  }
}

// Print test environment information
console.log('=== Google Drive Service Account Test ===');
console.log(`Date/Time: ${new Date().toISOString()}`);
console.log(`Debug Mode: ${DEBUG ? 'Enabled' : 'Disabled'}`);
console.log('=======================================');

// Set up axios with base URL and credentials
const serverPort = process.env.SERVER_PORT || process.env.PORT || 6000;
const baseURL = `http://localhost:${serverPort}`;
console.log(`Server URL: ${baseURL}`);

const api = axios.create({
  baseURL,
  withCredentials: true
});

// Add request interceptor for debugging
api.interceptors.request.use(request => {
  debugLog('Request:', {
    method: request.method,
    url: request.url,
    headers: request.headers,
    data: request.data
  });
  return request;
});

// Add response interceptor for debugging
api.interceptors.response.use(
  response => {
    debugLog('Response:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    });
    return response;
  },
  error => {
    debugLog('Error Response:', {
      message: error.message,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data
      } : 'No response'
    });
    return Promise.reject(error);
  }
);

// Test user credentials - use environment variables or defaults
const testUser = {
  email: process.env.TEST_USER_EMAIL || '<EMAIL>',
  password: process.env.TEST_USER_PASSWORD || 'password123'
};
console.log(`Test User: ${testUser.email}`);

// Test file ID - use environment variable or will be determined from listing files
const testFileId = process.env.TEST_FILE_ID || null;
if (testFileId) {
  console.log(`Test File ID: ${testFileId}`);
} else {
  console.log('Test File ID: Will be determined from listing files');
}

// Helper function to format error messages
function formatError(error) {
  if (error.response && error.response.data) {
    const { message, error: errorMsg, debug } = error.response.data;
    return {
      status: error.response.status,
      message: message || 'Unknown error',
      error: errorMsg || error.message,
      debug: debug || 'No debug information available'
    };
  }
  return {
    message: error.message,
    stack: DEBUG ? error.stack : undefined
  };
}

// Helper function to print test results
function printTestResult(testName, success, data = null, error = null) {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`\n[${status}] ${testName}`);
  
  if (success && data) {
    console.log('  Result:', typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
  }
  
  if (!success && error) {
    console.error('  Error:', typeof error === 'object' ? JSON.stringify(error, null, 2) : error);
    if (error.debug) {
      console.error('  Debug:', error.debug);
    }
  }
}

// Login function
async function login() {
  console.log('\n📋 TEST: Authentication');
  try {
    console.log(`Attempting to login as ${testUser.email}...`);
    const response = await api.post('/api/auth/login', testUser);
    printTestResult('Authentication', true, { token: 'Token received (hidden)' });
    return response.data.token;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Authentication', false, null, formattedError);
    throw new Error('Authentication failed - cannot continue tests');
  }
}

// Test listing files
async function testListFiles(token) {
  console.log('\n📋 TEST: List Files');
  try {
    console.log('Fetching files from Google Drive...');
    const response = await api.get('/api/google/drive/files', {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const result = {
      count: response.data.length,
      sample: response.data.slice(0, 3).map(file => ({
        id: file.id,
        name: file.name,
        mimeType: file.mimeType
      }))
    };
    
    printTestResult('List Files', true, result);
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('List Files', false, null, formattedError);
    throw error;
  }
}

// Test searching files
async function testSearchFiles(token, query) {
  console.log('\n📋 TEST: Search Files');
  try {
    console.log(`Searching for files matching "${query}"...`);
    const response = await api.get(`/api/google/drive/search`, {
      params: { q: query },
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const result = {
      query,
      count: response.data.length,
      sample: response.data.slice(0, 3).map(file => ({
        id: file.id,
        name: file.name,
        mimeType: file.mimeType
      }))
    };
    
    printTestResult('Search Files', true, result);
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Search Files', false, null, formattedError);
    throw error;
  }
}

// Test getting a file
async function testGetFile(token, fileId) {
  console.log('\n📋 TEST: Get File');
  try {
    console.log(`Fetching file with ID: ${fileId}...`);
    const response = await api.get(`/api/google/drive/file/${fileId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    const result = {
      id: response.data.id,
      name: response.data.name,
      mimeType: response.data.mimeType,
      webViewLink: response.data.webViewLink
    };
    
    printTestResult('Get File', true, result);
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Get File', false, null, formattedError);
    throw error;
  }
}

// Test getting a viewer URL
async function testGetViewerUrl(token, fileId) {
  console.log('\n📋 TEST: Get Viewer URL');
  try {
    console.log(`Getting viewer URL for file ID: ${fileId}...`);
    const response = await api.get(`/api/google/drive/viewer/${fileId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    printTestResult('Get Viewer URL', true, { viewerUrl: response.data.viewerUrl });
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Get Viewer URL', false, null, formattedError);
    throw error;
  }
}

// Test getting an editor URL
async function testGetEditorUrl(token, fileId) {
  console.log('\n📋 TEST: Get Editor URL');
  try {
    console.log(`Getting editor URL for file ID: ${fileId}...`);
    const response = await api.get(`/api/google/drive/editor/${fileId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    printTestResult('Get Editor URL', true, { 
      editorUrl: response.data.editorUrl,
      mimeType: response.data.file.mimeType 
    });
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Get Editor URL', false, null, formattedError);
    throw error;
  }
}

// Test adding a file to favorites
async function testAddFavorite(token, fileId) {
  console.log('\n📋 TEST: Add to Favorites');
  try {
    console.log(`Adding file ID: ${fileId} to favorites...`);
    const response = await api.post('/api/google/drive/favorites', { fileId }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    printTestResult('Add to Favorites', true, {
      id: response.data.favorite.id,
      name: response.data.favorite.name
    });
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Add to Favorites', false, null, formattedError);
    throw error;
  }
}

// Test removing a file from favorites
async function testRemoveFavorite(token, fileId) {
  console.log('\n📋 TEST: Remove from Favorites');
  try {
    console.log(`Removing file ID: ${fileId} from favorites...`);
    const response = await api.delete(`/api/google/drive/favorites/${fileId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    printTestResult('Remove from Favorites', true, { message: response.data.msg });
    return response.data;
  } catch (error) {
    const formattedError = formatError(error);
    printTestResult('Remove from Favorites', false, null, formattedError);
    throw error;
  }
}

// Main test function
async function runTests() {
  console.log('\n🚀 Starting Google Drive service account integration tests...');
  console.log('=======================================================');
  
  let token;
  let files;
  let fileId;
  let testResults = {
    total: 0,
    passed: 0,
    failed: 0
  };
  
  try {
    // Step 1: Login
    token = await login();
    testResults.total++;
    testResults.passed++;
    
    // Step 2: List files
    try {
      files = await testListFiles(token);
      testResults.total++;
      testResults.passed++;
      
      // If we have files, use the first one for further tests
      if (files.length > 0) {
        fileId = files[0].id;
        console.log(`\nUsing file "${files[0].name}" (ID: ${fileId}) for remaining tests`);
      } else if (testFileId) {
        fileId = testFileId;
        console.log(`\nNo files found. Using provided test file ID: ${fileId}`);
      } else {
        throw new Error('No files found and no test file ID provided. Cannot continue tests.');
      }
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      throw error;
    }
    
    // Step 3: Search files
    try {
      if (files && files.length > 0) {
        await testSearchFiles(token, files[0].name);
      } else {
        console.log('\n⚠️ Skipping search test - no files available');
      }
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.error('\n⚠️ Search test failed but continuing with other tests');
    }
    
    // Step 4: Get file
    try {
      await testGetFile(token, fileId);
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.error('\n⚠️ Get file test failed but continuing with other tests');
    }
    
    // Step 5: Get viewer URL
    try {
      await testGetViewerUrl(token, fileId);
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.error('\n⚠️ Get viewer URL test failed but continuing with other tests');
    }
    
    // Step 6: Get editor URL
    try {
      await testGetEditorUrl(token, fileId);
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.log('\n⚠️ Editor URL test failed (file might not support editing) but continuing with other tests');
    }
    
    // Step 7: Add to favorites
    try {
      await testAddFavorite(token, fileId);
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.error('\n⚠️ Add to favorites test failed but continuing with other tests');
    }
    
    // Step 8: Remove from favorites
    try {
      await testRemoveFavorite(token, fileId);
      testResults.total++;
      testResults.passed++;
    } catch (error) {
      testResults.total++;
      testResults.failed++;
      console.error('\n⚠️ Remove from favorites test failed');
    }
    
    // Print test summary
    console.log('\n=======================================================');
    console.log(`📊 TEST SUMMARY: ${testResults.passed}/${testResults.total} tests passed`);
    if (testResults.failed > 0) {
      console.log(`❌ ${testResults.failed} tests failed`);
    } else {
      console.log('✅ All tests completed successfully!');
    }
    console.log('=======================================================');
  } catch (error) {
    console.error('\n❌ Tests aborted due to critical error:', error.message);
    console.log('\n=======================================================');
    console.log(`📊 TEST SUMMARY: ${testResults.passed}/${testResults.total} tests passed`);
    console.log(`❌ ${testResults.failed} tests failed, and some tests were not run`);
    console.log('=======================================================');
    process.exit(1);
  }
}

// Run the tests
runTests();