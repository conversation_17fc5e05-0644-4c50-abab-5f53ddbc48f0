/**
 * Simple test script to directly test the GoogleFormsAPI with service account authentication
 * 
 * This script tests:
 * 1. Creating a GoogleFormsAPI instance with service account authentication
 * 2. Initializing the API
 * 3. Checking if the API is authenticated
 * 4. Listing forms
 * 
 * Usage: node test-google-forms-direct.js
 */

// Load environment variables
require('dotenv').config();

// Import the GoogleFormsAPI class
const GoogleFormsAPI = require('./server/integrations/googleForms/googleFormsAPI');

// Get the admin impersonation email from environment variables
const adminEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;

console.log('=== Google Forms Service Account Direct Test ===');
console.log(`Date/Time: ${new Date().toISOString()}`);
console.log(`Admin Email: ${adminEmail}`);
console.log('=======================================');

// Main test function
async function testGoogleFormsAPI() {
  try {
    console.log('Creating GoogleFormsAPI instance with service account authentication...');
    
    // Create a new GoogleFormsAPI instance with the admin email for service account impersonation
    const api = new GoogleFormsAPI(
      '', // clientId (not needed for service account)
      '', // clientSecret (not needed for service account)
      '', // redirectUri (not needed for service account)
      '', // tokenPath (not needed for service account)
      null, // userTokens (not needed for service account)
      null, // userId (not needed for service account)
      adminEmail // userEmail for service account impersonation
    );
    
    console.log('Initializing the API...');
    await api.initialize();
    
    console.log('Checking if the API is authenticated...');
    const isAuthenticated = api.isAuthenticated();
    console.log(`API authenticated: ${isAuthenticated}`);
    
    if (isAuthenticated) {
      console.log('Listing forms...');
      const forms = await api.listForms();
      console.log(`Found ${forms.length} forms:`);
      
      // Print the first 3 forms (or all if less than 3)
      const formsToPrint = forms.slice(0, 3);
      formsToPrint.forEach((form, index) => {
        console.log(`${index + 1}. ${form.name} (ID: ${form.id})`);
      });
      
      if (forms.length > 3) {
        console.log(`... and ${forms.length - 3} more`);
      }
      
      console.log('\n✅ TEST PASSED: Successfully authenticated and listed forms using service account');
    } else {
      console.error('\n❌ TEST FAILED: API is not authenticated');
    }
  } catch (error) {
    console.error('\n❌ TEST FAILED: Error during test:', error.message);
    console.error('Error details:', error);
  }
}

// Run the test
testGoogleFormsAPI();