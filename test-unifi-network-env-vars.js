/**
 * Test script to verify the UniFi Network configuration is recognized when environment variables are set
 * 
 * This script tests the updated UniFi Network controller to ensure
 * it correctly recognizes configuration from environment variables.
 * 
 * Usage: node test-unifi-network-env-vars.js
 */

// Set environment variables for testing
process.env.UNIFI_NETWORK_HOST = process.env.UNIFI_NETWORK_HOST || 'test-host';
process.env.UNIFI_NETWORK_API_KEY = process.env.UNIFI_NETWORK_API_KEY || 'test-api-key';
process.env.UNIFI_NETWORK_PORT = process.env.UNIFI_NETWORK_PORT || '443';
process.env.UNIFI_NETWORK_SITE = process.env.UNIFI_NETWORK_SITE || 'default';

// Import the UniFi Network controller
const unifiNetworkController = require('./server/controllers/unifiNetworkController');

// Mock Express request and response objects
const req = {};
const res = {
  status: function(statusCode) {
    console.log(`Response status: ${statusCode}`);
    return this;
  },
  json: function(data) {
    console.log('Response data:', data);
    return this;
  }
};

// Test the getConfig method
async function testGetConfig() {
  console.log('Testing getConfig method...');
  
  try {
    await unifiNetworkController.getConfig(req, res);
    console.log('Test passed: getConfig method executed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testGetConfig();