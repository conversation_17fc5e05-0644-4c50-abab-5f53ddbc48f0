/**
 * Test script for door status monitoring
 * 
 * This script tests the door status monitoring functionality in the realtimeService
 * by initializing the service and triggering a door status check.
 */

// Set environment variables for testing
process.env.UNIFI_ACCESS_HOST = process.env.UNIFI_ACCESS_HOST || 'your-unifi-access-host';
process.env.UNIFI_ACCESS_API_KEY = process.env.UNIFI_ACCESS_API_KEY || 'your-unifi-access-api-key';
process.env.UNIFI_ACCESS_PORT = process.env.UNIFI_ACCESS_PORT || '443';

// Import required modules
const realtimeService = require('./server/services/realtimeService');
const websocketServer = require('./server/websocket/websocketServer');

// Mock the websocket broadcast function to log messages instead of sending them
websocketServer.broadcast = (event, data) => {
  console.log(`[WebSocket Broadcast] Event: ${event}`);
  console.log(`[WebSocket Broadcast] Data:`, JSON.stringify(data, null, 2));
  return true;
};

// Function to test door status monitoring
async function testDoorStatusMonitoring() {
  console.log('Starting door status monitoring test...');
  
  try {
    // Initialize the realtime service
    console.log('Initializing realtime service...');
    await realtimeService.initializeUnifiIntegrations();
    
    // Manually trigger a door status check
    console.log('Triggering door status check...');
    
    // Access the private method using a workaround
    const checkInterval = realtimeService.intervals.get('door-status');
    if (checkInterval) {
      console.log('Door status monitoring is already running, clearing interval to restart it');
      clearInterval(checkInterval);
    }
    
    // Start door status monitoring
    realtimeService.startDoorStatusMonitoring();
    
    // Wait for 15 seconds to allow the check to complete
    console.log('Waiting for door status check to complete...');
    await new Promise(resolve => setTimeout(resolve, 15000));
    
    // Get the status of the realtime service
    const status = realtimeService.getStatus();
    console.log('Realtime service status:', status);
    
    // Stop the realtime service
    realtimeService.stop();
    
    console.log('Door status monitoring test completed successfully');
  } catch (error) {
    console.error('Error testing door status monitoring:', error);
  }
}

// Run the test
testDoorStatusMonitoring().then(() => {
  console.log('Test script completed');
  // Exit after a short delay to allow any pending operations to complete
  setTimeout(() => process.exit(0), 1000);
}).catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});