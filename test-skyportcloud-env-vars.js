/**
 * Test script to verify that the SkyportCloud integration works correctly with environment variables
 * 
 * This script tests:
 * 1. Loading configuration from environment variables
 * 2. Initializing the SkyportCloud API with environment variables
 * 3. Making a test API call to verify the connection
 * 
 * Usage:
 * node test-skyportcloud-env-vars.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Import the SkyportCloud API and controller
const SkyportCloudAPI = require('./server/integrations/skyportcloud/skyportcloudAPI');
const skyportCloudController = require('./server/controllers/skyportCloudController');

// Test function
async function testSkyportCloudEnvVars() {
  console.log('Testing SkyportCloud integration with environment variables...');
  
  // Check if environment variables are set
  const envApiKey = process.env.SKYPORTCLOUD_API_KEY || '';
  const envUsername = process.env.SKYPORTCLOUD_USERNAME || '';
  const envPassword = process.env.SKYPORTCLOUD_PASSWORD || '';
  const envBaseUrl = process.env.SKYPORTCLOUD_BASE_URL || 'https://api.skyportcloud.com';
  
  console.log('\nEnvironment variables:');
  console.log(`SKYPORTCLOUD_API_KEY: ${envApiKey ? '✓ Set' : '✗ Not set'}`);
  console.log(`SKYPORTCLOUD_USERNAME: ${envUsername ? '✓ Set' : '✗ Not set'}`);
  console.log(`SKYPORTCLOUD_PASSWORD: ${envPassword ? '✓ Set' : '✗ Not set'}`);
  console.log(`SKYPORTCLOUD_BASE_URL: ${envBaseUrl}`);
  
  // Check if we have enough credentials to proceed
  if (!envApiKey && (!envUsername || !envPassword)) {
    console.error('\n❌ Error: No authentication credentials found in environment variables.');
    console.log('Please set either SKYPORTCLOUD_API_KEY or both SKYPORTCLOUD_USERNAME and SKYPORTCLOUD_PASSWORD in your .env file.');
    return;
  }
  
  try {
    console.log('\nInitializing SkyportCloud API with environment variables...');
    
    // Create options object for API initialization
    const options = {};
    
    // Add authentication credentials based on what's available in environment variables
    if (envApiKey) {
      options.apiKey = envApiKey;
      console.log('Using API key authentication');
    } else if (envUsername && envPassword) {
      options.username = envUsername;
      options.password = envPassword;
      console.log('Using username/password authentication');
    }
    
    // Initialize the API
    const skyportCloudAPI = new SkyportCloudAPI(options, envBaseUrl);
    
    // Test the API by getting user info
    console.log('\nTesting API connection by getting user info...');
    try {
      await skyportCloudAPI.initialize();
      const userInfo = await skyportCloudAPI.getUserInfo();
      console.log('✅ Success! Connected to SkyportCloud API using environment variables.');
      console.log('\nUser info:');
      console.log(JSON.stringify(userInfo, null, 2));
    } catch (error) {
      console.error('❌ Error connecting to SkyportCloud API:', error.message);
    }
    
    // Test the controller's getConfig method
    console.log('\nTesting controller getConfig method...');
    try {
      // Mock Express request and response objects
      const req = {};
      const res = {
        json: (data) => {
          console.log('✅ getConfig returned:');
          console.log(JSON.stringify(data, null, 2));
        },
        status: (code) => ({
          json: (data) => {
            console.error(`❌ getConfig returned status ${code}:`, data);
          }
        })
      };
      
      // Call the getConfig method
      await skyportCloudController.getConfig(req, res);
    } catch (error) {
      console.error('❌ Error testing getConfig method:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

// Run the test
testSkyportCloudEnvVars().catch(console.error);