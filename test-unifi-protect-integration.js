/**
 * Test script for the UniFi Protect integration
 * 
 * This script tests the UniFi Protect API implementation using the unifi-protect npm package.
 * It initializes the API and tests the various methods.
 * 
 * Usage:
 * 1. Set the required environment variables:
 *    - UNIFI_PROTECT_HOST_A and UNIFI_PROTECT_API_KEY_A for instance A
 *    - UNIFI_PROTECT_HOST_B and UNIFI_PROTECT_API_KEY_B for instance B (optional)
 *    - Or legacy variables: UNIFI_PROTECT_HOST, UNIFI_PROTECT_USERNAME, UNIFI_PROTECT_PASSWORD
 * 
 * 2. Run the script:
 *    node test-unifi-protect-integration.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Import the UniFi Protect API
const UnifiProtectAPI = require('./server/integrations/unifiProtect/unifiProtectAPI');

// Create a new instance of the API
const unifiProtectAPI = new UnifiProtectAPI();

// Test function to run all tests
async function runTests() {
  try {
    console.log('Testing UniFi Protect API implementation...');
    
    // Initialize the API
    console.log('\n1. Initializing API...');
    await unifiProtectAPI.initialize();
    console.log('✓ API initialized successfully');
    
    // Get all cameras
    console.log('\n2. Getting all cameras...');
    const cameras = await unifiProtectAPI.getAllCameras();
    console.log(`✓ Found ${cameras.length} cameras`);
    
    // If we have cameras, test camera-specific methods
    if (cameras.length > 0) {
      const camera = cameras[0];
      console.log(`\n3. Testing camera-specific methods for camera: ${camera.name} (${camera.id})`);
      
      // Get camera details
      console.log('\n3.1. Getting camera details...');
      const cameraDetails = await unifiProtectAPI.getCameraDetails(camera.id, camera.instanceId);
      console.log(`✓ Got details for camera: ${cameraDetails.name}`);
      
      // Get camera snapshot
      console.log('\n3.2. Getting camera snapshot...');
      try {
        const snapshot = await unifiProtectAPI.getCameraSnapshot(camera.id, camera.instanceId);
        console.log(`✓ Got snapshot for camera: ${camera.name} (${snapshot.length} bytes)`);
      } catch (error) {
        console.error(`✗ Error getting snapshot: ${error.message}`);
      }
      
      // Test PTZ if the camera supports it
      if (camera.featureFlags?.hasPtz) {
        console.log('\n3.3. Testing PTZ control...');
        try {
          const ptzResult = await unifiProtectAPI.controlPTZ(camera.id, 'goto', { slot: 0 }, camera.instanceId);
          console.log(`✓ PTZ control successful: ${JSON.stringify(ptzResult)}`);
        } catch (error) {
          console.error(`✗ Error controlling PTZ: ${error.message}`);
        }
      } else {
        console.log('\n3.3. Skipping PTZ test - camera does not support PTZ');
      }
    }
    
    // Get events
    console.log('\n4. Getting events...');
    try {
      const events = await unifiProtectAPI.getAllEvents({ limit: 10 });
      console.log(`✓ Found ${events.length} events`);
    } catch (error) {
      console.error(`✗ Error getting events: ${error.message}`);
    }
    
    // Get system status
    console.log('\n5. Getting system status...');
    const systemStatus = await unifiProtectAPI.getAllSystemStatus();
    console.log(`✓ Got system status for ${systemStatus.length} instances`);
    
    // Get viewers
    console.log('\n6. Getting viewers...');
    try {
      const viewers = await unifiProtectAPI.getViewers(cameras[0]?.instanceId);
      console.log(`✓ Found ${viewers.length} viewers`);
      
      // If we have viewers, test viewer-specific methods
      if (viewers.length > 0) {
        const viewer = viewers[0];
        console.log(`\n6.1. Testing viewer update for viewer: ${viewer.name} (${viewer.id})`);
        
        // We're not actually updating the viewer to avoid making changes
        // Just log that we would update it
        console.log(`✓ Would update viewer: ${viewer.name} (${viewer.id})`);
      }
    } catch (error) {
      console.error(`✗ Error getting viewers: ${error.message}`);
    }
    
    console.log('\nAll tests completed!');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
runTests();