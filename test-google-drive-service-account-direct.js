/**
 * Direct test for Google Drive API with service account
 * 
 * This script tests the GoogleDriveAPI class directly without making HTTP requests
 * to ensure that service account authentication is working properly.
 */

require('dotenv').config();
const GoogleDriveAPI = require('./server/integrations/googleDrive/googleDriveAPI');

// Get service account credentials from environment variables
const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
const serviceAccountPrivateKey = process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY;
const adminImpersonationEmail = process.env.GOOGLE_ADMIN_IMPERSONATION_EMAIL;

console.log('=== Testing Google Drive API with Service Account ===');
console.log(`Service Account Email: ${serviceAccountEmail}`);
console.log(`Admin Impersonation Email: ${adminImpersonationEmail}`);

// Create an instance of the GoogleDriveAPI with service account credentials
const api = new GoogleDriveAPI(
  '', // clientId (not needed for service account)
  '', // clientSecret (not needed for service account)
  '', // redirectUri (not needed for service account)
  '', // tokenPath (not needed for service account)
  null, // userTokens (not needed for service account)
  null, // userId (not needed for service account)
  adminImpersonationEmail // Use admin email for service account impersonation
);

// Test the API
const testApi = async () => {
  try {
    console.log('Initializing API...');
    await api.initialize();
    
    console.log('Checking authentication status...');
    const isAuthenticated = api.isAuthenticated();
    console.log(`Authentication status: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
    
    if (!isAuthenticated) {
      console.log('API is not authenticated. Check service account credentials and permissions.');
      return;
    }
    
    console.log('Listing files...');
    const options = { 
      q: "'root' in parents",
      orderBy: "modifiedTime desc",
      pageSize: 10
    };
    
    const files = await api.listFiles(options, adminImpersonationEmail);
    
    console.log(`Retrieved ${files.length} files`);
    
    // Display the files
    if (files.length > 0) {
      console.log('Recent files:');
      files.forEach((file, index) => {
        console.log(`${index + 1}. ${file.name} (${file.mimeType}) - Modified: ${new Date(file.modifiedTime).toLocaleString()}`);
      });
    } else {
      console.log('No files found');
    }
    
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error testing Google Drive API:', error);
    console.error('Error details:', JSON.stringify({
      message: error.message,
      stack: error.stack,
      code: error.code,
      status: error.status,
      context: error.context || 'No context available'
    }, null, 2));
  }
};

// Run the test
testApi().catch(error => {
  console.error('Unhandled error during test:', error);
});