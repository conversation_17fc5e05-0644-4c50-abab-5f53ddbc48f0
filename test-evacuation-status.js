#!/usr/bin/env node

/**
 * Test script for Lenel S2 NetBox API getEvacuationStatus method
 * This script tests the simplified implementation of getEvacuationStatus
 */

const LenelS2NetBoxAPI = require('./server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');
require('dotenv').config();

// Get configuration from environment variables
const config = {
  host: process.env.LENEL_S2_NETBOX_HOST,
  username: process.env.LENEL_S2_NETBOX_USERNAME,
  password: process.env.LENEL_S2_NETBOX_PASSWORD,
  port: process.env.LENEL_S2_NETBOX_PORT || 443
};

async function testEvacuationStatus() {
  console.log('🔧 Testing Lenel S2 NetBox getEvacuationStatus method...\n');

  try {
    // Initialize the API client
    console.log('📡 Initializing API client with:');
    console.log(`   Host: ${config.host}`);
    console.log(`   Username: ${config.username}`);
    console.log(`   Port: ${config.port}`);
    
    const api = new LenelS2NetBoxAPI(config.host, config.username, config.password, config.port);
    
    console.log('📡 Initializing API client...');
    await api.initialize();
    console.log('✅ API client initialized successfully\n');

    // Test getEvacuationStatus method
    console.log('🚨 Testing getEvacuationStatus() method...');
    try {
      const evacuationStatus = await api.getEvacuationStatus();
      console.log('✅ Successfully retrieved evacuation status:');
      console.log(JSON.stringify(evacuationStatus, null, 2));
    } catch (error) {
      console.log(`❌ getEvacuationStatus() failed: ${error.message}`);
      console.error('Stack trace:', error.stack);
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testEvacuationStatus().catch(console.error);
}

module.exports = { testEvacuationStatus };